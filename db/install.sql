CREATE DATABASE IF NOT EXISTS TRANSACTION DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_general_ci;
USE TRANSACTION;


-- ----------------------------
-- 1、存储每一个已配置的 jobDetail 的详细信息
-- ----------------------------
DROP TABLE IF EXISTS QRTZ_JOB_DETAILS;
CREATE TABLE QRTZ_JOB_DETAILS (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    job_name             VARCHAR(200)    NOT NULL            COMMENT '任务名称',
    job_group            VARCHAR(200)    NOT NULL            COMMENT '任务组名',
    description          VARCHAR(250)    NULL                COMMENT '相关介绍',
    job_class_name       VARCHAR(250)    NOT NULL            COMMENT '执行任务类名称',
    is_durable           VARCHAR(1)      NOT NULL            COMMENT '是否持久化',
    is_nonconcurrent     VARCHAR(1)      NOT NULL            COMMENT '是否并发',
    is_update_data       VARCHAR(1)      NOT NULL            COMMENT '是否更新数据',
    requests_recovery    VARCHAR(1)      NOT NULL            COMMENT '是否接受恢复执行',
    job_data             BLOB            NULL                COMMENT '存放持久化job对象',
    PRIMARY KEY (sched_name, job_name, job_group)
) ENGINE=INNODB COMMENT = '任务详细信息表';

-- ----------------------------
-- 2、 存储已配置的 Trigger 的信息
-- ----------------------------
DROP TABLE IF EXISTS QRTZ_TRIGGERS;
CREATE TABLE QRTZ_TRIGGERS (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    trigger_name         VARCHAR(200)    NOT NULL            COMMENT '触发器的名字',
    trigger_group        VARCHAR(200)    NOT NULL            COMMENT '触发器所属组的名字',
    job_name             VARCHAR(200)    NOT NULL            COMMENT 'qrtz_job_details表job_name的外键',
    job_group            VARCHAR(200)    NOT NULL            COMMENT 'qrtz_job_details表job_group的外键',
    description          VARCHAR(250)    NULL                COMMENT '相关介绍',
    next_fire_time       BIGINT(13)      NULL                COMMENT '上一次触发时间（毫秒）',
    prev_fire_time       BIGINT(13)      NULL                COMMENT '下一次触发时间（默认为-1表示不触发）',
    priority             INTEGER         NULL                COMMENT '优先级',
    trigger_state        VARCHAR(16)     NOT NULL            COMMENT '触发器状态',
    trigger_type         VARCHAR(8)      NOT NULL            COMMENT '触发器的类型',
    start_time           BIGINT(13)      NOT NULL            COMMENT '开始时间',
    end_time             BIGINT(13)      NULL                COMMENT '结束时间',
    calendar_name        VARCHAR(200)    NULL                COMMENT '日程表名称',
    misfire_instr        SMALLINT(2)     NULL                COMMENT '补偿执行的策略',
    job_data             BLOB            NULL                COMMENT '存放持久化job对象',
    PRIMARY KEY (sched_name, trigger_name, trigger_group)
) ENGINE=INNODB COMMENT = '触发器详细信息表';

-- ----------------------------
-- 3、 存储简单的 Trigger，包括重复次数，间隔，以及已触发的次数
-- ----------------------------
DROP TABLE IF EXISTS QRTZ_SIMPLE_TRIGGERS;
CREATE TABLE QRTZ_SIMPLE_TRIGGERS (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    trigger_name         VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_name的外键',
    trigger_group        VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_group的外键',
    repeat_count         BIGINT(7)       NOT NULL            COMMENT '重复的次数统计',
    repeat_interval      BIGINT(12)      NOT NULL            COMMENT '重复的间隔时间',
    times_triggered      BIGINT(10)      NOT NULL            COMMENT '已经触发的次数',
    PRIMARY KEY (sched_name, trigger_name, trigger_group)
) ENGINE=INNODB COMMENT = '简单触发器的信息表';

-- ----------------------------
-- 4、 存储 Cron Trigger，包括 Cron 表达式和时区信息
-- ---------------------------- 
DROP TABLE IF EXISTS QRTZ_CRON_TRIGGERS;
CREATE TABLE QRTZ_CRON_TRIGGERS (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    trigger_name         VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_name的外键',
    trigger_group        VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_group的外键',
    cron_expression      VARCHAR(200)    NOT NULL            COMMENT 'cron表达式',
    time_zone_id         VARCHAR(80)                         COMMENT '时区',
    PRIMARY KEY (sched_name, trigger_name, trigger_group)
) ENGINE=INNODB COMMENT = 'Cron类型的触发器表';

-- ----------------------------
-- 5、 Trigger 作为 Blob 类型存储(用于 Quartz 用户用 JDBC 创建他们自己定制的 Trigger 类型，JobStore 并不知道如何存储实例的时候)
-- ---------------------------- 
DROP TABLE IF EXISTS QRTZ_BLOB_TRIGGERS;
CREATE TABLE QRTZ_BLOB_TRIGGERS (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    trigger_name         VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_name的外键',
    trigger_group        VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_group的外键',
    blob_data            BLOB            NULL                COMMENT '存放持久化Trigger对象',
    PRIMARY KEY (sched_name, trigger_name, trigger_group)
) ENGINE=INNODB COMMENT = 'Blob类型的触发器表';

-- ----------------------------
-- 6、 以 Blob 类型存储存放日历信息， quartz可配置一个日历来指定一个时间范围
-- ---------------------------- 
DROP TABLE IF EXISTS QRTZ_CALENDARS;
CREATE TABLE QRTZ_CALENDARS (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    calendar_name        VARCHAR(200)    NOT NULL            COMMENT '日历名称',
    calendar             BLOB            NOT NULL            COMMENT '存放持久化calendar对象',
    PRIMARY KEY (sched_name, calendar_name)
) ENGINE=INNODB COMMENT = '日历信息表';

-- ----------------------------
-- 7、 存储已暂停的 Trigger 组的信息
-- ---------------------------- 
DROP TABLE IF EXISTS QRTZ_PAUSED_TRIGGER_GRPS;
CREATE TABLE QRTZ_PAUSED_TRIGGER_GRPS (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    trigger_group        VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_group的外键',
    PRIMARY KEY (sched_name, trigger_group)
) ENGINE=INNODB COMMENT = '暂停的触发器表';

-- ----------------------------
-- 8、 存储与已触发的 Trigger 相关的状态信息，以及相联 Job 的执行信息
-- ---------------------------- 
DROP TABLE IF EXISTS QRTZ_FIRED_TRIGGERS;
CREATE TABLE QRTZ_FIRED_TRIGGERS (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    entry_id             VARCHAR(95)     NOT NULL            COMMENT '调度器实例id',
    trigger_name         VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_name的外键',
    trigger_group        VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_group的外键',
    instance_name        VARCHAR(200)    NOT NULL            COMMENT '调度器实例名',
    fired_time           BIGINT(13)      NOT NULL            COMMENT '触发的时间',
    sched_time           BIGINT(13)      NOT NULL            COMMENT '定时器制定的时间',
    priority             INTEGER         NOT NULL            COMMENT '优先级',
    state                VARCHAR(16)     NOT NULL            COMMENT '状态',
    job_name             VARCHAR(200)    NULL                COMMENT '任务名称',
    job_group            VARCHAR(200)    NULL                COMMENT '任务组名',
    is_nonconcurrent     VARCHAR(1)      NULL                COMMENT '是否并发',
    requests_recovery    VARCHAR(1)      NULL                COMMENT '是否接受恢复执行',
    PRIMARY KEY (sched_name, entry_id)
) ENGINE=INNODB COMMENT = '已触发的触发器表';

-- ----------------------------
-- 9、 存储少量的有关 Scheduler 的状态信息，假如是用于集群中，可以看到其他的 Scheduler 实例
-- ---------------------------- 
DROP TABLE IF EXISTS QRTZ_SCHEDULER_STATE;
CREATE TABLE QRTZ_SCHEDULER_STATE (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    instance_name        VARCHAR(200)    NOT NULL            COMMENT '实例名称',
    last_checkin_time    BIGINT(13)      NOT NULL            COMMENT '上次检查时间',
    checkin_interval     BIGINT(13)      NOT NULL            COMMENT '检查间隔时间',
    PRIMARY KEY (sched_name, instance_name)
) ENGINE=INNODB COMMENT = '调度器状态表';

-- ----------------------------
-- 10、 存储程序的悲观锁的信息(假如使用了悲观锁)
-- ---------------------------- 
DROP TABLE IF EXISTS QRTZ_LOCKS;
CREATE TABLE QRTZ_LOCKS (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    lock_name            VARCHAR(40)     NOT NULL            COMMENT '悲观锁名称',
    PRIMARY KEY (sched_name, lock_name)
) ENGINE=INNODB COMMENT = '存储的悲观锁信息表';

-- ----------------------------
-- 11、 Quartz集群实现同步机制的行锁表
-- ---------------------------- 
DROP TABLE IF EXISTS QRTZ_SIMPROP_TRIGGERS;
CREATE TABLE QRTZ_SIMPROP_TRIGGERS (
    sched_name           VARCHAR(120)    NOT NULL            COMMENT '调度名称',
    trigger_name         VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_name的外键',
    trigger_group        VARCHAR(200)    NOT NULL            COMMENT 'qrtz_triggers表trigger_group的外键',
    str_prop_1           VARCHAR(512)    NULL                COMMENT 'String类型的trigger的第一个参数',
    str_prop_2           VARCHAR(512)    NULL                COMMENT 'String类型的trigger的第二个参数',
    str_prop_3           VARCHAR(512)    NULL                COMMENT 'String类型的trigger的第三个参数',
    int_prop_1           INT             NULL                COMMENT 'int类型的trigger的第一个参数',
    int_prop_2           INT             NULL                COMMENT 'int类型的trigger的第二个参数',
    long_prop_1          BIGINT          NULL                COMMENT 'long类型的trigger的第一个参数',
    long_prop_2          BIGINT          NULL                COMMENT 'long类型的trigger的第二个参数',
    dec_prop_1           NUMERIC(13,4)   NULL                COMMENT 'decimal类型的trigger的第一个参数',
    dec_prop_2           NUMERIC(13,4)   NULL                COMMENT 'decimal类型的trigger的第二个参数',
    bool_prop_1          VARCHAR(1)      NULL                COMMENT 'Boolean类型的trigger的第一个参数',
    bool_prop_2          VARCHAR(1)      NULL                COMMENT 'Boolean类型的trigger的第二个参数',
    PRIMARY KEY (sched_name, trigger_name, trigger_group)
) ENGINE=INNODB COMMENT = '同步机制的行锁表';

DROP TABLE IF EXISTS t_ryytn_job;
CREATE TABLE t_ryytn_job (
  jobId BIGINT NOT NULL COMMENT '任务ID',
  jobName VARCHAR(256) NOT NULL DEFAULT '' COMMENT '任务名称',
  jobType INT NOT NULL DEFAULT '1' COMMENT '调度类型，1：定时调度，2：循环调度，3：延迟调度',
  startDate DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间，默认当前时间',
  endDate DATETIME DEFAULT NULL COMMENT '结束时间',
  jobConf VARCHAR(32) DEFAULT NULL COMMENT '调度配置，根据调度类型赋值，jobType值，1：cron表达式，2：循环间隔时间，3：空',
  className VARCHAR(256) DEFAULT NULL COMMENT 'bean名称',
  param TEXT DEFAULT NULL COMMENT '调用参数',
  serviceId BIGINT DEFAULT NULL COMMENT '业务编号',
  misfirePolicy VARCHAR(2) DEFAULT NULL COMMENT '计划执行错误策略，暂时预留，后续实现',
  CONCURRENT TINYINT NOT NULL DEFAULT 0 COMMENT '是否并发执行，0：禁止并发 1：允许并发，默认值：0',
  STATUS INT NOT NULL DEFAULT '1' COMMENT '状态，1：正常，2：禁用，3：已结束，默认值：1',
  description VARCHAR(255) DEFAULT '' COMMENT '描述',
  createdBy VARCHAR(64) DEFAULT NULL COMMENT '创建者',
  createdTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedBy VARCHAR(64) DEFAULT NULL COMMENT '更新者',
  updatedTime DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (jobId)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定时任务调度表';


COMMIT;



DROP TABLE IF EXISTS t_ryytn_file;
CREATE TABLE t_ryytn_file (
  fileId VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件编号，在文件服务器上存储的唯一编号(路径)',
  fileType INT NOT NULL COMMENT '文件类型，0：图片，1：视频，2：音频，3：文本，4：文档，5：压缩文件，6：脚本文件，99：其他',
  suffix VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件后缀',
  oFileName VARCHAR(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '原文件名',
  createdBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  createdTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (fileId)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='文件信息表';

DROP TABLE IF EXISTS t_ryytn_file_ref;
CREATE TABLE t_ryytn_file_ref (
  fileId VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件编号，在文件服务器上存储的唯一编号(路径)',
  serviceId VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务编号',
  serviceType VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务表名',
  PRIMARY KEY (fileId,serviceId)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='业务文件关联表';

DROP TABLE IF EXISTS t_ryytn_configcategory;
CREATE TABLE t_ryytn_configcategory (
  categoryId VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则编号，业务唯一约束',
  parentId BIGINT DEFAULT NULL COMMENT '父类别编号',
  parentIds BIGINT DEFAULT NULL COMMENT '所有有父类别编号',
  NAME VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类别名称',
  STATUS INT DEFAULT '1' COMMENT '配置状态，1：正常，2：停用',
  sortNo INT DEFAULT NULL COMMENT '类别序号',
  PRIMARY KEY (categoryId)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='配置类别表';

DROP TABLE IF EXISTS t_ryytn_config;
CREATE TABLE t_ryytn_config (
  categoryId VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属类别',
  configId VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置编码',
  configName VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置名称',
  configType INT DEFAULT NULL COMMENT '配置值类型（1：密码类型；2：文本类型；3：文件类型；4：枚举类型；5：数字文本；6：文本域）',
  configValue VARCHAR(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置值',
  STATUS INT DEFAULT '1' COMMENT '配置状态，1：正常，2：停用',
  isDisplay INT DEFAULT NULL COMMENT '0:不展示, 1:展示',
  validator VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '校验规则',
  description VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置描述',
  sortNo INT DEFAULT NULL COMMENT '排序字段',
  createdBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'admin' COMMENT '创建人',
  createdTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  updatedTime DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (configId)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='配置表';

DROP TABLE IF EXISTS t_ryytn_dict_type;
CREATE TABLE t_ryytn_dict_type (
  dictTypeId BIGINT NOT NULL AUTO_INCREMENT COMMENT '字典类型编号',
  dictType VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典类型',
  dictName VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称',
  STATUS INT DEFAULT '1' COMMENT '状态，1：正常，2：停用，默认值：1',
  deleteFlag TINYINT(1) DEFAULT '0' COMMENT '删除状态，0：未删除，1：已删除，默认0',
  description VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  dataType INT NOT NULL DEFAULT '2' COMMENT '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2',
  createdBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人(登录账号)',
  createdTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人(登录账号)',
  updatedTime DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (dictTypeId)
) ENGINE=INNODB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典类型表';

DROP TABLE IF EXISTS t_ryytn_dict_data;
CREATE TABLE t_ryytn_dict_data (
  dictId BIGINT NOT NULL AUTO_INCREMENT COMMENT '字典编号',
  dictType VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典类型',
  NAME VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称',
  CODE VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典编码',
  parentId VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父字典编号',
  parentIds VARCHAR(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所有父字典编号',
  LEVEL INT NOT NULL COMMENT '字典层级',
  leafFlag TINYINT(1) NOT NULL DEFAULT '1' COMMENT '是否叶子，0：否，1：是，首次新增默认为1',
  cssClass VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  listClass VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '表格回显样式',
  itemCheck INT DEFAULT '0' COMMENT '是否默认选中，1：是，0：否，默认为0',
  sortNo INT DEFAULT NULL COMMENT '排序',
  STATUS INT DEFAULT '1' COMMENT '状态，1：正常，2：停用，默认值：1',
  deleteFlag TINYINT(1) DEFAULT '0' COMMENT '删除状态，0：未删除，1：已删除，默认0',
  description VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  dataType INT NOT NULL DEFAULT '2' COMMENT '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2',
  createdBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人(登录账号)',
  createdTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人(登录账号)',
  updatedTime DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (dictId)
) ENGINE=INNODB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典数据表';

DROP TABLE IF EXISTS t_ryytn_thirdparty_system;
CREATE TABLE t_ryytn_thirdparty_system (
  id BIGINT NOT NULL COMMENT '编号',
  NAME VARCHAR(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  authCode VARCHAR(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '授权码',
  url VARCHAR(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入口页面跳转地址',
  STATUS INT NOT NULL DEFAULT '1' COMMENT '状态，1：正常，2：不正常（扩展字段）',
  description VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (id),
  UNIQUE KEY (NAME)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='第三方系统表';
INSERT INTO t_ryytn_thirdparty_system (id,NAME,authCode,url,STATUS,description) VALUES (1,'oa','SUkZiB',NULL,1,'OA系统');

DROP TABLE IF EXISTS t_ryytn_oa_subcompany;
CREATE TABLE t_ryytn_oa_subcompany (
  id VARCHAR (32) NOT NULL COMMENT '编号',
  subCompanyName VARCHAR (32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分部简称',
  subcompanyDesc VARCHAR (64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分部全称',
  subCompanyCode VARCHAR (32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分部编码',
  supSubComId VARCHAR (32) DEFAULT NULL COMMENT '上级分部id,0或者空为表示没有上级分部',
  supSubComIds VARCHAR (2048) DEFAULT NULL COMMENT '所有上级分部id,英文逗号分隔',
  LEVEL INT DEFAULT 0 COMMENT '分部层级，根据supSubComIds包含的英文逗号数量计算',
  canceled VARCHAR (10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '封存标志，1 封存，其他为未封存',
  sortNo DOUBLE DEFAULT NULL COMMENT '排序',
  createdTime DATETIME DEFAULT NULL COMMENT '创建时间',
  updatedTime DATETIME DEFAULT NULL COMMENT '修改时间',
  syncTime BIGINT DEFAULT NULL COMMENT '同步时间，精确到毫秒',
  PRIMARY KEY (id)
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OA同步分部（公司）表，从OA系统同步' ;

DROP TABLE IF EXISTS t_ryytn_oa_subcompany_extend;
CREATE TABLE t_ryytn_oa_subcompany_extend
(
  id VARCHAR (32) NOT NULL COMMENT '编号',
  fieldName VARCHAR(32) NOT NULL COMMENT '字段名',
  fieldValue VARCHAR(64) NOT NULL COMMENT '字段值',
  INDEX (id)
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OA同步分部（公司）扩展字段表' ;

DROP TABLE IF EXISTS t_ryytn_oa_department;
CREATE TABLE t_ryytn_oa_department
(
  id VARCHAR (32) NOT NULL COMMENT '编号',
  departmentMark VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门简称',
  departmentName VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门全称',
  departmentCode VARCHAR (32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门编码',
  subCompanyId VARCHAR (32) DEFAULT NULL COMMENT '分部编号，对应t_ryytn_subcompany表id字段',
  supDepId VARCHAR (32) DEFAULT NULL COMMENT '上级部门编号，0或者空为表示没有上级分部',
  supDepIds VARCHAR (2048) DEFAULT NULL COMMENT '所有上级部门编号,英文逗号分隔',
  LEVEL INT DEFAULT 0 COMMENT '部门层级，根据supDepIds包含的英文逗号数量计算',
  canceled VARCHAR (10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '封存标志，1 封存，其他为未封存',
  sortNo DOUBLE DEFAULT NULL COMMENT '排序',
  createdTime DATETIME DEFAULT NULL COMMENT '创建时间',
  updatedTime DATETIME DEFAULT NULL COMMENT '修改时间',
  syncTime BIGINT DEFAULT NULL COMMENT '同步时间，精确到毫秒',
  PRIMARY KEY (id)
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OA同步部门表，从OA系统同步' ;

DROP TABLE IF EXISTS t_ryytn_oa_department_extend;
CREATE TABLE t_ryytn_oa_department_extend
(
  id VARCHAR (32) NOT NULL COMMENT '编号',
  fieldName VARCHAR(32) NOT NULL COMMENT '字段名',
  fieldValue VARCHAR(64) NOT NULL COMMENT '字段值',
  INDEX (id)
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OA同步部门扩展字段表' ;

DROP TABLE IF EXISTS t_ryytn_oa_jobtitle;
CREATE TABLE t_ryytn_oa_jobtitle
(
  id VARCHAR (32) NOT NULL COMMENT '编号',
  jobTitleMark VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '简称',
  jobTitleName VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '全称',
  jobDoc VARCHAR(32) DEFAULT NULL COMMENT '相关文档id',
  jobDepartmentId VARCHAR (32) DEFAULT NULL COMMENT '部门编号，OA接口废弃字段，以人员表departmentId字段为准',
  jobResponsibility VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职责',
  jobCompetency VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任职资格',
  jobTitleRemark VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  createdTime DATETIME DEFAULT NULL COMMENT '创建时间',
  updatedTime DATETIME DEFAULT NULL COMMENT '修改时间',
  syncTime BIGINT DEFAULT NULL COMMENT '同步时间，精确到毫秒',
  PRIMARY KEY (id)
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OA同步岗位表，从OA系统同步' ;

DROP TABLE IF EXISTS t_ryytn_oa_person;
CREATE TABLE t_ryytn_oa_person
(
  id VARCHAR (32) NOT NULL COMMENT '编号',
  workCode VARCHAR(32) DEFAULT NULL COMMENT '编号',
  lastName VARCHAR(32) DEFAULT NULL COMMENT '人员名称',
  loginId VARCHAR(32) DEFAULT NULL COMMENT '登录名',
  accountType INT(4) DEFAULT NULL COMMENT '主次账号标志：1：次账号,其他：主账号',
  beLongTo VARCHAR (32) DEFAULT NULL COMMENT '主账号id （当accounttype 为 1 有效）',
  departmentId VARCHAR (32) DEFAULT NULL COMMENT '部门编号',
  jobTitleId VARCHAR (32) DEFAULT NULL COMMENT '岗位编号',
  locationId VARCHAR (32) DEFAULT NULL COMMENT '办公地点',
  STATUS INT(2) DEFAULT NULL COMMENT '状态: 0 试用 1 正式 2 临时 3 试用延期 4 解聘 5 离职 6 退休 7 无效', 
  LANGUAGE VARCHAR(32) DEFAULT NULL COMMENT '系统语言',
  jobActivityDesc VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职责描述',
  jobLevel VARCHAR(32) DEFAULT NULL COMMENT '职级',
  jobCall VARCHAR(32) DEFAULT NULL COMMENT '职称',
  managerId VARCHAR (32) DEFAULT NULL COMMENT '上级人员编号',
  assistantId VARCHAR (32) DEFAULT NULL COMMENT '助理人员编号',
  sex VARCHAR(10) DEFAULT NULL COMMENT '性别',
  telephone VARCHAR(32) DEFAULT NULL COMMENT '办公电话',
  mobile VARCHAR(32) DEFAULT NULL COMMENT '移动电话',
  mobileCall VARCHAR(32) DEFAULT NULL COMMENT '其他电话',
  email VARCHAR(32) DEFAULT NULL COMMENT '邮箱',
  startDate VARCHAR(20) DEFAULT NULL COMMENT '合同开始日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR',
  endDate VARCHAR(20) DEFAULT NULL COMMENT '合同结束日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR',
  secLevel VARCHAR(10) DEFAULT NULL COMMENT '安全级别',
  PASSWORD VARCHAR(64) DEFAULT NULL COMMENT '密码，密文',
  certificateNum VARCHAR(32) DEFAULT NULL COMMENT '身份证',
  birthday VARCHAR(20) DEFAULT NULL COMMENT '生日，OA系统同步过来数据可能不满足Date格式，使用VARCHAR',
  height VARCHAR(10) DEFAULT NULL COMMENT '身高',
  weight VARCHAR(10) DEFAULT NULL COMMENT '体重',
  folk VARCHAR(32) DEFAULT NULL COMMENT '民族',
  nativePlace VARCHAR(32) DEFAULT NULL COMMENT '籍贯',
  healthInfo VARCHAR(32) DEFAULT NULL COMMENT '健康状况',
  maritalStatus VARCHAR(32) DEFAULT NULL COMMENT '婚姻状况',
  tempResidentNumber VARCHAR(32) DEFAULT NULL COMMENT '暂住证号码',
  residentPlace VARCHAR(64) DEFAULT NULL COMMENT '户口',
  regresidentPlace VARCHAR(64) DEFAULT NULL COMMENT '户口所在地',
  homeAddress VARCHAR(32) DEFAULT NULL COMMENT '家庭联系方式',
  policy VARCHAR(32) DEFAULT NULL COMMENT '政治面貌',
  beMemberDate VARCHAR(20) DEFAULT NULL COMMENT '入团日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR',
  bePartyDate VARCHAR(20) DEFAULT NULL COMMENT '入党日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR',
  degree VARCHAR(32) DEFAULT NULL COMMENT '学位',
  educationLevel VARCHAR(10) DEFAULT NULL COMMENT '学历',
  isLabouunion TINYINT DEFAULT 0 COMMENT '是否公会会员',
  lastModDate VARCHAR(20) DEFAULT NULL COMMENT '最后修改日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR',
  sortNo DOUBLE DEFAULT NULL COMMENT '排序',
  createdTime DATETIME DEFAULT NULL COMMENT '创建时间',
  updatedTime DATETIME DEFAULT NULL COMMENT '修改时间',
  syncTime BIGINT DEFAULT NULL COMMENT '同步时间，精确到毫秒',
  PRIMARY KEY (id)
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OA同步员工表，从OA系统同步' ;

DROP TABLE IF EXISTS t_ryytn_oa_person_extend;
CREATE TABLE t_ryytn_oa_person_extend
(
  id VARCHAR (32) NOT NULL COMMENT '编号',
  fieldName VARCHAR(32) NOT NULL COMMENT '字段名',
  fieldValue VARCHAR(64) NOT NULL COMMENT '字段值',
  INDEX (id)
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OA同步员工扩展字段表' ;

DROP TABLE IF EXISTS t_ryytn_account;
CREATE TABLE t_ryytn_account
(
  id BIGINT NOT NULL COMMENT '编号',
  NAME VARCHAR(64) DEFAULT NULL COMMENT '账号名称，OA同步lastName可能为空，此处默认为空，业务代码校验本地账号不能为空',
  nickName VARCHAR(64) DEFAULT NULL COMMENT '账号昵称',
  workCode VARCHAR(32) DEFAULT NULL COMMENT '工号',
  loginId VARCHAR(32) DEFAULT NULL COMMENT '登录账号，本地账号必填，OA账号对应loginId，可能为空，此处默认为空，业务代码校验本地账号不能为空',
  PASSWORD VARCHAR(64) DEFAULT NULL COMMENT '密码，本地账号必填，此处默认为空，业务代码校验本地账号不能为空',
  oaId VARCHAR(32) DEFAULT NULL COMMENT 'OA人员编号',
  STATUS INT(2) DEFAULT 1 COMMENT '状态，1：正常，2：停用',
  description VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  dataType INT DEFAULT '2' COMMENT '数据类型，1:表示初始化数据不允许删除；2:表示管理端创建数据；3:OA系统同步，默认为2',
  createdTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedTime DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (id)
)ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '账号信息表，包含预置管理账号、本地管理账号和OA同步人员';
-- 预置超级管理员
INSERT INTO t_ryytn_account (id,NAME,nickName,workCode,loginId,PASSWORD,oaId,STATUS,description,dataType) VALUES (*********,'超级管理员','超级管理员',NULL,'sysAdmin','628c709b5d084dc6b22a6dbe87665419',NULL,1,'预置超级管理员',1);

DROP TABLE IF EXISTS t_ryytn_role;
CREATE TABLE t_ryytn_role (
  id BIGINT NOT NULL COMMENT '角色编号，业务唯一约束',
  NAME VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  defaultFlag TINYINT DEFAULT '0' COMMENT '是否默认角色，0：不是默认角色，1：是默认角色',
  STATUS INT NOT NULL DEFAULT '1' COMMENT '状态，1：正常，2：禁用，默认值：1',
  description VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  sortNo INT DEFAULT NULL COMMENT '排序',
  dataType INT NOT NULL DEFAULT '2' COMMENT '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2',
  createdBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人(登录账号)',
  createdTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人(登录账号)',
  updatedTime DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (id)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色表';

-- 预置超级管理员角色
INSERT INTO t_ryytn_role (id,NAME,defaultFlag,STATUS,description,sortNo,dataType) VALUES (*********,'超级管理员角色',0,1,'超级管理员角色',0,1);

DROP TABLE IF EXISTS t_ryytn_account_role;
CREATE TABLE t_ryytn_account_role (
  accountId BIGINT NOT NULL COMMENT '账号编号',
  roleId BIGINT NOT NULL COMMENT '角色编号',
  UNIQUE KEY `UK_ACCOUNTID_ROLEID` (`accountId`,`roleId`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='账号角色关联关系表';

-- 预置超级管理员
INSERT INTO t_ryytn_account_role (accountId,roleId) VALUES (*********,*********);

DROP TABLE IF EXISTS t_ryytn_moudel;
CREATE TABLE t_ryytn_moudel (
  id BIGINT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块编号，业务唯一约束',
  NAME VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块名称',
  TYPE INT NOT NULL DEFAULT 1 COMMENT '系统类型，1:web，2：app',
  path VARCHAR(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '首页路由',
  STATUS INT NOT NULL DEFAULT '1' COMMENT '状态，1：正常，2：禁用，默认值：1',
  description VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (id)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模块表';

DROP TABLE IF EXISTS t_ryytn_page;
CREATE TABLE t_ryytn_page (
  id BIGINT NOT NULL COMMENT '页面编号',
  NAME VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '页面名称',
  alias VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面别名',
  permission VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面权限码，用于后端校验权限，英文逗号分隔',
  parentId BIGINT NOT NULL COMMENT '页面父编号，根页面为-1',
  parentIds VARCHAR(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '页面所有父编号，英文逗号分隔',
  dependencyIds VARCHAR(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面依赖编号，英文逗号分隔',
  TYPE VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '页面类型，1：菜单',
  path VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面路由地址',
  configPath VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面配置页路由地址',
  component VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件路径',
  icon VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面图标静态资源目录',
  moudelId BIGINT NOT NULL COMMENT '模块编号',
  sortNo INT DEFAULT NULL COMMENT '页面排序',
  sumFlag TINYINT(1) DEFAULT '0' COMMENT '是否开启合计，0：否，1：是，默认0',
  description VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (id)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='页面表';

DROP TABLE IF EXISTS t_ryytn_role_page;
CREATE TABLE t_ryytn_role_page (
  roleId BIGINT NOT NULL COMMENT '角色编号',
  pageId BIGINT NOT NULL COMMENT '菜单编号',
  UNIQUE KEY `UK_ROLEID_PAGEID` (`roleId`,`pageId`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色菜单关联关系表';

DROP TABLE IF EXISTS t_ryytn_button;
CREATE TABLE t_ryytn_button(
  id BIGINT NOT NULL COMMENT '按钮编号',
  NAME VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '按钮名称',
  alias VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '按钮别名',
  permission VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '按钮权限码，用于后端校验权限，英文逗号分隔',
  dependencyIds VARCHAR(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '按钮依赖编号，英文逗号分隔',
  pageId BIGINT NOT NULL COMMENT '所属页面编号',
  sortNo INT DEFAULT NULL COMMENT '页面排序',
  PRIMARY KEY (id)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='按钮表';

DROP TABLE IF EXISTS t_ryytn_role_button;
CREATE TABLE t_ryytn_role_button (
  roleId BIGINT NOT NULL COMMENT '角色编号',
  buttonId BIGINT NOT NULL COMMENT '菜单编号',
  UNIQUE KEY `UK_ROLEID_BUTTONID` (`roleId`,`buttonId`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色按钮关联关系表';

DROP TABLE IF EXISTS t_ryytn_role_channel;
CREATE TABLE t_ryytn_role_channel(
  roleId BIGINT NOT NULL COMMENT '角色编号',
  channelId VARCHAR(64) NOT NULL COMMENT '渠道编号',
  UNIQUE KEY `UK_ROLEID_CHANNELID` (`roleId`,`channelId`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色渠道关联关系表';

DROP TABLE IF EXISTS t_ryytn_role_productcategory;
CREATE TABLE t_ryytn_role_productcategory(
  roleId BIGINT NOT NULL COMMENT '角色编号',
  categoryId VARCHAR(64) NOT NULL COMMENT '产品品类编号',
  UNIQUE KEY `UK_ROLEID_CATEGORYID` (`roleId`,`categoryId`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色产品品类关联关系表';

DROP TABLE IF EXISTS t_ryytn_role_factory;
CREATE TABLE t_ryytn_role_factory(
  roleId BIGINT NOT NULL COMMENT '角色编号',
  factoryId VARCHAR(255) NOT NULL COMMENT '工厂编号，字段长度与阿里一致',
  UNIQUE KEY `UK_ROLEID_FACTORYID` (`roleId`,`factoryId`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色工厂关联关系表';

DROP TABLE IF EXISTS t_ryytn_role_depository;
CREATE TABLE t_ryytn_role_depository(
  roleId BIGINT NOT NULL COMMENT '角色编号',
  depositoryId VARCHAR(255) NOT NULL COMMENT '仓库编号，字段长度与阿里一致',
  UNIQUE KEY `UK_ROLEID_DEPOSITORYID` (`roleId`,`depositoryId`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色仓库关联关系表';

DROP TABLE IF EXISTS t_ryytn_page_config;
CREATE TABLE t_ryytn_page_config (
  id BIGINT NOT NULL COMMENT '配置编号',
  pageId BIGINT NOT NULL COMMENT '页面编号',
  rowName VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列名称',
  rowField VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列字段名',
  width INT DEFAULT NULL COMMENT  '列宽度',
  sortNo INT DEFAULT NULL COMMENT '列顺序',
  freezeFlag TINYINT(1) DEFAULT '0' COMMENT '列冻结/解冻，0：解冻，1：解冻，默认0',
  showFlag TINYINT(1) DEFAULT '0' COMMENT '列展示/隐藏，0：展示，1：隐藏，默认0',
  gatherFlag TINYINT(1) DEFAULT '0' COMMENT   '列聚合，0：不聚合，1：聚合，默认0',
  createdBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人(登录账号)',
  createdTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedBy VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人(登录账号)',
  updatedTime DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (id)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='页面配置表';

