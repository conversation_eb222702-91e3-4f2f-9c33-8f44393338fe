
with 
plandate as (
select 
min(plan_date) as startDate,
max(plan_date) as endDate
from tdm_xqyc_txn_demand_plan_warehouse_di
where demand_plan_code='427406169552678912'
and version_id='DP202404W3-2'
and deleted=false
and is_modify=false
),
rate1 as (
-- 仓+TOBC粒度历史出库比
select
				sku_code,
				lv1_category_code,
				lv2_category_code,
				lv3_category_code,
				channel_type,
				warehouse_code,
				warehouse_name,
				biz_date_value,
				outbound_rate
			from
				cdop_biz.tdm_xqyc_txn_delivery_order_rate_df
			where
				biz_date_type = '16WEEK'
				and dim_comb = 'SKU_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK'
				and biz_date_value =(
				select
					MAX(biz_date_value)
				from
					cdop_biz.tdm_xqyc_txn_delivery_order_rate_df
				where
					biz_date_type = '16WEEK'
					and dim_comb = 'SKU_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK')
),
-- 渠道+仓+TOBC粒度历史出库比
rate2 as (
		select
				sku_code,
				lv1_category_code,
				lv2_category_code,
				lv3_category_code,
				channel_type,
				warehouse_code,
				biz_date_value,
				sum(outbound_rate::numeric)/count(1) as outbound_rate
			from
				cdop_biz.tdm_xqyc_txn_delivery_order_rate_df
			where
				biz_date_type = '16WEEK'
				and dim_comb = 'SKU_CODE+LV3_CHANNEL_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK'
				and biz_date_value =(
				select
					MAX(biz_date_value)
				from
					cdop_biz.tdm_xqyc_txn_delivery_order_rate_df
				where
					biz_date_type = '16WEEK'
					and dim_comb = 'SKU_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK')
			group by sku_code,lv1_category_code,lv2_category_code,lv3_category_code,warehouse_code,channel_type,biz_date_value
),
forecast as (
select 
sku_code,
lv1_category_code,
lv2_category_code,
lv3_category_code,
warehouse_code,
receiver_type,
target_biz_date,
prediction_result
from cdop_biz.tdm_xqyc_txn_forcast_warehouse_di
where prediction_version='V-20240408'
)
select 
plan.lv1_category_name as "产品分类",
plan.lv2_category_name as "产品大类",
plan.lv3_category_name as "产品小类",
plan.sku_code as "产品编号",
plan.sku_name as "产品名称",
rate1.warehouse_name as "仓",
rate1.channel_type as "BC",
plan.plan_date as "周",
plan.plan_value as "渠道需求计划共识数据",
rate1.outbound_rate as "仓+BC出库比",
round(plan.plan_value*rate1.outbound_rate::numeric,0) as "渠道需求计划共识数据*仓+BC出库比",
warehouse_plan.plan_value as "当前拆分分仓需求计划数量结果",
forecast.prediction_result as "分仓预测结果比例",
round(plan.plan_value*forecast.prediction_result::numeric,0) as "渠道需求计划共识数据*分仓预测结果比例",
rate2.outbound_rate as "渠道+仓+BC出库比",
warehouse_report.order_num as "分仓需求提报数量结果"
from 
-- 渠道需求计划共识数据
(select
	lv1_category_code,
	max(lv1_category_name) as lv1_category_name,
	lv2_category_code,
	max(lv2_category_name) as lv2_category_name,
	lv3_category_code,
	max(lv3_category_name) as lv3_category_name,
	sku_code,
	max(sku_name) as sku_name,
	plan_date,
	sum(plan_value) as plan_value
from cdop_biz.tdm_xqyc_txn_demand_plan_order_di
where demand_plan_code='427406169552678912'
  and version_id='DP202404W3-2'
  and is_modify = false
  and deleted = false
group by demand_plan_code,version_id,lv1_category_code,lv2_category_code,lv3_category_code,sku_code,plan_date
) plan
left join
rate1
on plan.sku_code=rate1.sku_code
and plan.lv1_category_code=rate1.lv1_category_code
and plan.lv2_category_code=rate1.lv2_category_code
and plan.lv3_category_code=rate1.lv3_category_code
left join 
rate2
on rate1.sku_code=rate2.sku_code
and rate1.lv1_category_code=rate2.lv1_category_code
and rate1.lv2_category_code=rate2.lv2_category_code
and rate1.lv3_category_code=rate2.lv3_category_code
and rate1.warehouse_code=rate2.warehouse_code
and rate1.channel_type=rate2.channel_type
left join 
-- 分仓需求计划拆分结果
(
select 
lv1_category_code,
lv2_category_code,
lv3_category_code,
sku_code,
sku_name,
warehouse_code,
warehouse_name,
receiver_type,
plan_date,
plan_value
from cdop_biz.tdm_xqyc_txn_demand_plan_warehouse_di 
where demand_plan_code='427406169552678912'
and version_id='DP202404W3-2'
and deleted=false
and is_modify=false
) warehouse_plan
on plan.sku_code=warehouse_plan.sku_code
and plan.lv1_category_code=warehouse_plan.lv1_category_code
and plan.lv2_category_code=warehouse_plan.lv2_category_code
and plan.lv3_category_code=warehouse_plan.lv3_category_code
and plan.plan_date=warehouse_plan.plan_date
and lower(rate1.channel_type)=lower(warehouse_plan.receiver_type)
and rate1.warehouse_code=warehouse_plan.warehouse_code
left join forecast
on plan.sku_code=forecast.sku_code
and plan.lv1_category_code=forecast.lv1_category_code
and plan.lv2_category_code=forecast.lv2_category_code
and plan.lv3_category_code=forecast.lv3_category_code
and to_char(plan.plan_date,'yyyyMMdd')=forecast.target_biz_date
and lower(rate1.channel_type)=lower(forecast.receiver_type)
and rate1.warehouse_code=forecast.warehouse_code
left join 
(
-- 分仓需求提报
select 
lv1_category_code,
lv2_category_code,
lv3_category_code,
sku_code,
warehouse_code,
receiver_type,
biz_date_value,
sum(order_num) as order_num
from cdop_sys.t_ryytn_warehouse_demand_report
where demand_plan_code='427406169552678912'
and rolling_version='DP202404W3-2'
group by lv1_category_code,lv2_category_code,lv3_category_code,sku_code,warehouse_code,receiver_type,biz_date_value
) warehouse_report
on plan.sku_code=warehouse_report.sku_code
and plan.lv1_category_code=warehouse_report.lv1_category_code
and plan.lv2_category_code=warehouse_report.lv2_category_code
and plan.lv3_category_code=warehouse_report.lv3_category_code
and rate1.warehouse_code=warehouse_report.warehouse_code
and lower(rate1.channel_type)=lower(warehouse_report.receiver_type)
and to_char(plan.plan_date,'yyyyMMdd')=warehouse_report.biz_date_value
where plan.plan_date>=(select startDate from plandate)
and plan.plan_date<=(select endDate from plandate)
order by plan.sku_code,rate1.warehouse_code,rate1.channel_type,plan.plan_date;