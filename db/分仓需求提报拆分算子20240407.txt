SELECT get_sequence_uid(ROW_NUMBER() OVER (ORDER BY a.plan_date)) AS id
	,PLAN.demand_plan_name AS name
	,a.demand_plan_code AS demand_plan_code
	,a.version_id AS rolling_version
	,a.sku_code AS sku_code
	,a.sku_name AS sku_name
	,a.lv1_category_code AS lv1_category_code
	,a.lv1_category_name AS lv1_category_name
	,a.lv2_category_code AS lv2_category_code
	,a.lv2_category_name AS lv2_category_name
	,a.lv3_category_code AS lv3_category_code
	,a.lv3_category_name AS lv3_category_name
	,a.lv1_channel_code AS lv1_channel_code
	,a.lv1_channel_name AS lv1_channel_name
	,a.lv2_channel_code AS lv2_channel_code
	,a.lv2_channel_name AS lv2_channel_name
	,b.lv3_channel_code AS lv3_channel_code
	,b.lv3_channel_name AS lv3_channel_name
	,b.channel_type AS receiver_type
	,d.biz_warehouse_code AS warehouse_code
	,d.biz_warehouse_name AS warehouse_name
	,'WEEK' AS biz_date_type
	,to_char(a.plan_date, 'YYYYMMDD')AS biz_date_value
	,CASE 
		WHEN c.channel_ratio IS NULL
			OR e.outbound_rate IS NULL
			OR a.plan_value IS NULL
			THEN 0
		ELSE round(a.plan_value * c.channel_ratio * e.outbound_rate::NUMERIC,0)
		END AS order_num
	,NULL AS unit
	,c."lv2ChannelSum"
	,0 AS deviation_radio
	,NULL AS remark
	,NULL AS extend
	,0 AS is_modify
	,'${creator}' AS creator
	,'${creator}' AS last_modifier
	,to_char(CURRENT_TIMESTAMP,'YYYY-MM-DD HH24:MI:SS') AS gmt_create
	,to_char(CURRENT_TIMESTAMP,'YYYY-MM-DD HH24:MI:SS') AS gmt_modify
	,0 AS is_delete
FROM tdm_xqyc_txn_demand_plan_order_di a
LEFT JOIN tdm_xqyc_txn_demand_config_info_di PLAN ON a.demand_plan_code = PLAN.demand_plan_code
	AND PLAN.subject_type = 'order'
LEFT JOIN (select distinct lv1_channel_code,lv2_channel_code,lv3_channel_code,lv3_channel_name,channel_type from tdm_rltn_org_reseller_df) b ON a.lv1_channel_code = b.lv1_channel_code
	AND a.lv2_channel_code = b.lv2_channel_code
LEFT JOIN (
	SELECT lv3ChannelReport.rolling_version
		,lv3ChannelReport.biz_date_value
		,lv3ChannelReport.sku_code
		,lv3ChannelReport.lv1_category_code
		,lv3ChannelReport.lv2_category_code
		,lv3ChannelReport.lv3_category_code
		,lv3ChannelReport.lv1_channel_code
		,lv3ChannelReport.lv2_channel_code
		,lv3ChannelReport.lv3_channel_code
		,lv3ChannelReport.lv3_channel_name
		,lv2ChannelReport.lv2ChannelSum as "lv2ChannelSum"
		,CASE 
			WHEN lv2ChannelReport.lv2ChannelSum = 0
				THEN 0
			ELSE round(lv3ChannelReport.lv3ChannelSum / lv2ChannelReport.lv2ChannelSum, 8)
			END AS channel_ratio
	FROM (
		SELECT rolling_version
			,biz_date_value
			,sku_code
			,lv1_category_code
			,lv2_category_code
			,lv3_category_code
			,lv1_channel_code
			,lv2_channel_code
			,'' AS lv3_channel_code
			,sum(order_num) lv2ChannelSum
		FROM tdm_xqyc_txn_sku_reporting_di
		WHERE biz_date_type = 'WEEK'
			AND is_modify = 0
		GROUP BY rolling_version
			,biz_date_value
			,sku_code
			,lv1_category_code
			,lv2_category_code
			,lv3_category_code
			,lv1_channel_code
			,lv2_channel_code
		) lv2ChannelReport
	LEFT JOIN (
		SELECT rolling_version
			,biz_date_value
			,sku_code
			,lv1_category_code
			,lv2_category_code
			,lv3_category_code
			,lv1_channel_code
			,lv2_channel_code
			,lv3_channel_code
			,lv3_channel_name
			,sum(order_num) lv3ChannelSum
		FROM tdm_xqyc_txn_sku_reporting_di
		WHERE biz_date_type = 'WEEK'
			AND is_modify = 0
		GROUP BY rolling_version
			,biz_date_value
			,sku_code
			,lv1_category_code
			,lv2_category_code
			,lv3_category_code
			,lv1_channel_code
			,lv2_channel_code
			,lv3_channel_code
			,lv3_channel_name
		) lv3ChannelReport ON lv2ChannelReport.rolling_version = lv3ChannelReport.rolling_version
		AND lv2ChannelReport.biz_date_value = lv3ChannelReport.biz_date_value
		AND lv2ChannelReport.sku_code = lv3ChannelReport.sku_code
		AND lv2ChannelReport.lv1_category_code = lv3ChannelReport.lv1_category_code
		AND lv2ChannelReport.lv2_category_code = lv3ChannelReport.lv2_category_code
		AND lv2ChannelReport.lv3_category_code = lv3ChannelReport.lv3_category_code
		AND lv2ChannelReport.lv1_channel_code = lv3ChannelReport.lv1_channel_code
		AND lv2ChannelReport.lv2_channel_code = lv3ChannelReport.lv2_channel_code
		WHERE lv3ChannelReport.rolling_version = (select max(rolling_version) from tdm_xqyc_txn_sku_reporting_di where replace(rolling_version,'CDP','DP') <= '${versionId}')
	) c ON replace(a.plan_date::VARCHAR, '-', '') = c.biz_date_value
	AND a.sku_code = c.sku_code
	AND a.lv1_category_code = c.lv1_category_code
	AND a.lv2_category_code = c.lv2_category_code
	AND a.lv3_category_code = c.lv3_category_code
	AND a.lv1_channel_code = c.lv1_channel_code
	AND a.lv2_channel_code = c.lv2_channel_code
	AND b.lv3_channel_code = c.lv3_channel_code
CROSS JOIN (
	SELECT DISTINCT biz_warehouse_code
		,biz_warehouse_name
	FROM dim_bas_warehouse_info_df
	WHERE STATUS = '1'
		AND biz_warehouse_code IS NOT NULL
		AND lv1_type_code = '1607108726'
		AND warehouse_type_code != '644734982'
	) d
--LEFT JOIN tdm_xqyc_txn_delivery_order_rate_df
LEFT JOIN (
select distinct delivery.sku_code,
delivery.lv1_category_code,
delivery.lv2_category_code,
delivery.lv3_category_code,
delivery.lv1_channel_code,
delivery.lv2_channel_code,
delivery.lv3_channel_code,
delivery.channel_type,
delivery.warehouse_code,
case when sumDelivery.outbound_rate_sum=0 then 0 else delivery.outbound_rate::numeric/sumDelivery.outbound_rate_sum end as outbound_rate
from (
select * from tdm_xqyc_txn_delivery_order_rate_df
WHERE biz_date_type = '16WEEK'
and dim_comb='SKU_CODE+LV3_CHANNEL_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK'
) delivery
left join (
select sku_code,lv1_category_code,lv2_category_code,lv3_category_code,lv1_channel_code,lv2_channel_code,lv3_channel_code,channel_type,biz_date_value,sum(outbound_rate::numeric) as outbound_rate_sum
from tdm_xqyc_txn_delivery_order_rate_df
WHERE biz_date_type = '16WEEK'
and dim_comb='SKU_CODE+LV3_CHANNEL_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK'
GROUP BY sku_code,lv1_category_code,lv2_category_code,lv3_category_code,lv1_channel_code,lv2_channel_code,lv3_channel_code,channel_type,biz_date_value
) sumDelivery
on  delivery.sku_code=sumDelivery.sku_code
and delivery.lv1_category_code=sumDelivery.lv1_category_code
and delivery.lv2_category_code=sumDelivery.lv2_category_code
and delivery.lv3_category_code=sumDelivery.lv3_category_code
and delivery.lv1_channel_code=sumDelivery.lv1_channel_code
and delivery.lv2_channel_code=sumDelivery.lv2_channel_code
and delivery.lv3_channel_code=sumDelivery.lv3_channel_code
and delivery.channel_type=sumDelivery.channel_type
and delivery.biz_date_value=sumDelivery.biz_date_value
where delivery.biz_date_value=(select MAX(biz_date_value) from tdm_xqyc_txn_delivery_order_rate_df where biz_date_type = '16WEEK' and dim_comb ='SKU_CODE+LV3_CHANNEL_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK')
) e 
ON d.biz_warehouse_code = e.warehouse_code
--	AND c.biz_date_value = e.biz_date_value
	AND a.sku_code = e.sku_code
	AND a.lv1_category_code = e.lv1_category_code
	AND a.lv2_category_code = e.lv2_category_code
	AND a.lv3_category_code = e.lv3_category_code
	AND a.lv1_channel_code = e.lv1_channel_code
	AND a.lv2_channel_code = e.lv2_channel_code
	AND c.lv3_channel_code = e.lv3_channel_code
	AND b.channel_type = e.channel_type
WHERE 1 = 1
	AND a.demand_plan_code = '${demandPlanCode}'
	AND a.version_id = '${versionId}'
	AND a.plan_date >= cast('${planDateStart}' AS DATE)
	AND a.plan_date <= cast('${planDateEnd}' AS DATE)
	AND a.is_modify = false
	AND a.deleted = false