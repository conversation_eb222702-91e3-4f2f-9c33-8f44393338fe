insert  into "t_ryytn_account"("id","name","nick_name","work_code","login_id","password","oa_id","status","description","data_type","created_time","updated_time") values (*********,'超级管理员','超级管理员',NULL,'sysAdmin','628c709b5d084dc6b22a6dbe87665419',NULL,1,'预置超级管理员',1,'2023-10-08 10:58:34',NULL);
insert  into "t_ryytn_account_role"("account_id","role_id") values (*********,*********);
insert  into "t_ryytn_button"("id","name","alias","permission","dependency_ids","page_id","sort_no") values (*********,'查询','','system:account:query','',999001,1),
(*********,'新增','','system:account:add','*********',999001,2),
(*********,'修改','','system:account:update','*********',999001,3),
(*********,'删除','','system:account:delete','*********',999001,4),
(*********,'查询','','system:role:query','',999002,1),
(*********,'新增','','system:role:add','*********',999002,2),
(*********,'修改','','system:role:update','*********',999002,3),
(*********,'删除','','system:role:delete','*********',999002,4),
(*********,'查询','','system:page:query','',999003,1),
(*********,'修改','','system:page:update','*********',999003,2),
(*********,'查询','','system:dict:query','',999004,1),
(*********,'新增','','system:dict:add','*********',999004,2),
(*********,'修改','','system:dict:update','*********',999004,3),
(*********,'删除','','system:dict:delete','*********',999004,4),
(*********,'查询','','system:config:query','',999005,1),
(*********,'修改','','system:config:update','*********',999005,2),
(************,'查询','','demand:config:saleTarget:query','',*********,1),
(*********001,'查询','','demand:config:promotion:query','',*********,1),
(************,'新增','','demand:config:promotion:add','*********001',*********,2),
(*********003,'修改','','demand:config:promotion:update','*********001',*********,3),
(*********004,'删除','','demand:config:promotion:delete','*********001',*********,4),
(101001003001,'查询','','demand:config:productLock:query','',101001003,1),
(101001003002,'新增','','demand:config:productLock:add','101001003001',101001003,2),
(101001003003,'删除','','demand:config:productLock:delete','101001003001',101001003,3),
(101001004001,'查询','','demand:config:channelDemandReport:query','',101001004,1),
(101001004002,'新增','','demand:config:channelDemandReport:add','101001004001',101001004,2),
(101001004003,'修改','','demand:config:channelDemandReport:update','101001004001',101001004,3),
(101001004004,'导出','','demand:config:channelDemandReport:export','101001004001',101001004,4),
(101001005001,'查询','','demand:config:coldDemandReport:query','',101001005,1),
(101001005002,'新增','','demand:config:coldDemandReport:add','101001005001',101001005,2),
(101001005003,'修改','','demand:config:coldDemandReport:update','101001005001',101001005,3),
(101001005004,'导出','','demand:config:coldDemandReport:export','101001005001',101001005,4),
(101001006001,'查询','','demand:config:dispositoryDemandReport:query','',101001006,1),
(101001006002,'新增','','demand:config:dispositoryDemandReport:add','101001006001',101001006,2),
(101001006003,'修改','','demand:config:dispositoryDemandReport:update','101001006001',101001006,3),
(101001006004,'导出','','demand:config:dispositoryDemandReport:export','101001006001',101001006,4),
(101002001001,'查询','','demand:algorithm:channel:query','',101002001,1),
(*********001,'查询','','demand:algorithm:warehouse:query','',*********,1),
(*********001,'查询','','demand:forecastResult:channel:query','',*********,1),
(*********002,'导出','','demand:forecastResult:channel:export','',*********,2),
(*********001,'查询','','demand:forecastResult:warehouse:query','',*********,1);
insert  into "t_ryytn_config"("category_id","config_id","config_name","config_type","config_value","status","is_display","validator","description","sort_no","created_by","created_time","updated_by","updated_time") values ('1','ACCOUNT_DEFAULT_PASSWORD','账号重置密码默认密码',1,'Password123',1,1,NULL,'账号重置密码默认密码',999,'admin','2023-09-28 18:09:16',NULL,'2023-10-08 15:36:15'),
('1','DATAQ_API_BASEBUS_CHANNEL_LIST','DATAQ接口地址：查询渠道列表',2,'/elastic-25-432/ry_retail/ryytn_dev/base-bus/channel-list',1,1,NULL,'DATAQ接口地址：查询渠道列表',999,NULL,'2023-10-24 10:51:25',NULL,'2023-10-24 17:08:52'),
('1','DATAQ_API_BASEBUS_FACTORY_LIST','DATAQ接口地址：查询工厂列表',2,'/elastic-25-432/ry_retail/ryytn_dev/base-bus/factory-list',1,1,NULL,'DATAQ接口地址：查询工厂列表',999,NULL,'2023-10-26 16:47:40',NULL,NULL),
('1','DATAQ_API_BASEBUS_SKULIST','DATAQ接口地址：查询产品列表',2,'/elastic-25-432/ry_retail/ryytn_dev/base-bus/sku-list',1,1,NULL,'DATAQ接口地址：查询产品列表',999,NULL,'2023-10-24 10:57:13',NULL,'2023-10-25 14:29:29'),
('2','DATAQ_API_BASEBUS_WAREHOUSE_LIST','DATAQ接口地址：查询仓库列表',2,'/elastic-25-432/ry_retail/ryytn_dev/base-bus/warehouse-list',1,1,NULL,'DATAQ接口地址：查询仓库列表',999,NULL,'2023-11-08 09:26:31',NULL,NULL),
('2','DATAQ_API_BUSPARAM_CONSTANT_LIST','DATAQ接口地址：查询活动常量配置列表',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/constant-list',1,1,NULL,'DATAQ接口地址：查询活动常量配置列表',999,NULL,'2023-10-26 11:29:37',NULL,NULL),
('1','DATAQ_API_BUSPARAM_MARKET_ACTIVITY_ADD_OR_UPDATE','DATAQ接口地址：保存修改活动',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/market-activity/add-or-update',1,1,NULL,'DATAQ接口地址：保存修改活动',999,NULL,'2023-10-30 11:19:37',NULL,NULL),
('1','DATAQ_API_BUSPARAM_MARKET_ACTIVITY_DELETED','DATAQ接口地址：删除活动',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/market-activity/deleted',1,1,NULL,'DATAQ接口地址：删除活动',999,NULL,'2023-10-30 14:18:56',NULL,NULL),
('1','DATAQ_API_BUSPARAM_MARKET_ACTIVITY_RESELLER_ADD_OR_UPDATE','DATAQ接口地址：保存修改活动渠道列表',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/market-activity/reseller/add-or-update',1,1,NULL,'DATAQ接口地址：保存修改活动渠道列表',999,NULL,'2023-10-30 11:21:51',NULL,NULL),
('1','DATAQ_API_BUSPARAM_MARKET_ACTIVITY_SKU_ADD_OR_UPDATE','DATAQ接口地址：保存修改活动产品列表',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/market-activity/sku/add-or-update',1,1,NULL,'DATAQ接口地址：保存修改活动产品列表',999,NULL,'2023-10-30 11:20:43',NULL,NULL),
('2','DATAQ_API_BUSPARAM_MARKET_CHANNEL_LIST','DATAQ接口地址：查询促销活动渠道列表',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/market-channel-list',1,1,NULL,'DATAQ接口地址：查询促销活动渠道列表',999,NULL,'2023-10-26 09:12:21',NULL,NULL),
('2','DATAQ_API_BUSPARAM_MARKET_LIST','DATAQ接口地址：查询促销活动列表',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/market-list',1,1,NULL,'DATAQ接口地址：查询促销活动列表',999,NULL,'2023-10-24 10:42:09',NULL,'2023-10-25 14:28:45'),
('2','DATAQ_API_BUSPARAM_MARKET_SKU_LIST','DATAQ接口地址：查询活动产品列表',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/market-sku-list',1,1,NULL,'DATAQ接口地址：查询活动产品列表',999,NULL,'2023-10-26 17:32:08',NULL,NULL),
('1','DATAQ_API_CALENDAR_FSCL_LIST','DATAQ接口地址：查询财年列表',2,'/elastic-25-432/ry_retail/ryytn_dev/calendar/fscl-list',1,1,NULL,'DATAQ接口地址：业务参数-销售财年列表',999,NULL,'2023-10-24 10:46:19',NULL,'2023-10-25 14:29:01'),
('2','DATAQ_API_CALENDAR_WEEK_LIST','DATAQ接口地址：查询周数据列表',2,'/elastic-25-432/ry_retail/ryytn_dev/calendar/week-list',1,1,NULL,'DATAQ接口地址：查询周数据列表',999,NULL,'2023-10-24 10:37:12',NULL,NULL),
('2','DATAQ_API_CHANNEL_DEMAND_REPORT_COLUMN','DATAQ接口地址：查询渠道需求提报列表字段',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/demand-report-column',1,1,NULL,'DATAQ接口地址：查询渠道需求提报列表字段',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-26 17:05:24'),
('2','DATAQ_API_CHANNEL_DEMAND_REPORT_IMPORT','DATAQ接口地址：批量导入渠道需求提报数据',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/demand-report/save-batch',1,1,NULL,'DATAQ接口地址：批量导入渠道需求提报数据',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-27 10:47:34'),
('2','DATAQ_API_CHANNEL_DEMAND_REPORT_LIST','DATAQ接口地址：查询渠道需求提报列表',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/demand-report-list',1,1,NULL,'DATAQ接口地址：查询渠道需求提报列表',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-27 10:47:34'),
('2','DATAQ_API_CHANNEL_DEMAND_REPORT_TABLE','DATAQ接口地址：查询渠道需求提报列表（行转列）包含时间数据',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/demand-report-table',1,1,NULL,'DATAQ接口地址：查询渠道需求提报列表（行转列）包含时间数据',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-26 17:05:40'),
('2','DATAQ_API_CHANNEL_DEMAND_REPORT_TEMPLATE_COLUMN','DATAQ接口地址：查询渠道需求提报导入模板字段',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/channel-demand-report-template-column',1,1,NULL,'DATAQ接口地址：查询渠道需求提报导入模板字段',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-26 17:05:24'),
('2','DATAQ_API_CHANNEL_DEMAND_REPORT_TEMPLATE_DATA','DATAQ接口地址：查询渠道需求提报导入模板数据',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/demand-report-format-table',1,1,NULL,'DATAQ接口地址：查询渠道需求提报导入模板数据',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-27 11:26:11'),
('2','DATAQ_API_CHANNEL_DEMAND_REPORT_UPDATE','DATAQ接口地址：修改渠道需求提报数据',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/demand-report/update',1,1,NULL,'DATAQ接口地址：修改渠道需求提报数据',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-31 17:54:11'),
('2','DATAQ_API_CHANNEL_DEMAND_REPORT_VERSION_LIST','DATAQ接口地址：查询渠道需求提报版本列表',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/demand-report-version-list',1,1,NULL,'DATAQ接口地址：查询渠道需求提报版本列表',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-26 15:43:50'),
('2','DATAQ_API_CHANNEL_DEMAND_REPORT_VERSION_LOCK','DATAQ接口地址：锁定渠道需求提报版本',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/demand-report/update-lock',1,1,NULL,'DATAQ接口地址：锁定渠道需求提报版本',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-26 15:43:49'),
('2','DATAQ_API_DEMAND_CHANNEL_PLAN_LIST','DATAQ接口地址：查询渠道需求计划列表详情',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/order/list',1,1,NULL,'DATAQ接口地址：查询渠道需求计划列表详情',999,NULL,'2023-10-24 16:35:30',NULL,'2023-10-24 16:37:14'),
('2','DATAQ_API_DEMAND_FORECAST_ALGORITHM_LIST','DATAQ接口地址：查询渠道_分仓预测算法列表列表',2,'/elastic-25-432/ry_retail/ryytn_dev/algorithm/list',1,1,NULL,'DATAQ接口地址：查询渠道_分仓预测算法列表列表',999,NULL,'2023-10-23 11:17:53',NULL,'2023-10-24 16:08:34'),
('2','DATAQ_API_DEMAND_FORECAST_CHANNEL','DATAQ接口地址：查询渠道预测结果',2,'/elastic-25-432/ry_retail/ryytn_dev/forecast/channel-table',1,1,NULL,'DATAQ接口地址：查询渠道预测结果',999,'admin','2023-11-01 15:39:26',NULL,'2023-11-08 10:26:04'),
('2','DATAQ_API_DEMAND_FORECAST_CHANNEL_COLUMN','DATAQ接口地址：查询渠道预测结果列表字段',2,'/elastic-25-432/ry_retail/ryytn_dev/forecast/channel-column',1,1,NULL,'DATAQ接口地址：查询渠道预测结果列表字段',999,'admin','2023-11-01 15:43:48',NULL,'2023-11-08 10:26:07'),
('2','DATAQ_API_DEMAND_FORECAST_CHANNEL_DETAIL','DATAQ接口地址：查询渠道预测结果详情',2,'/elastic-25-432/ry_retail/ryytn_dev/forecast/channel-detail',1,1,NULL,'DATAQ接口地址：查询渠道预测结果详情',999,'admin','2023-11-08 10:27:20',NULL,'2023-11-08 10:28:24'),
('2','DATAQ_API_DEMAND_FORECAST_CHANNEL_FILTER_LIST','DATAQ接口地址：查询渠道预测结果查询条件',2,'/elastic-25-432/ry_retail/ryytn_dev/forecast/channel-filter-list',1,1,NULL,'DATAQ接口地址：查询渠道预测结果查询条件',999,NULL,'2023-10-23 18:14:30',NULL,'2023-10-23 18:20:34'),
('2','DATAQ_API_DEMAND_FORECAST_CHANNEL_LIST','DATAQ接口地址：查询渠道预测结果',2,'/elastic-25-432/ry_retail/ryytn_dev/forecast/channel-list',1,1,NULL,'DATAQ接口地址：查询渠道预测结果',999,NULL,'2023-10-23 18:14:30',NULL,'2023-10-23 18:20:34'),
('2','DATAQ_API_DEMAND_FORECAST_WAREHOUSE_FILTER_LIST','DATAQ接口地址：查询分仓预测结果查询条件',2,'/elastic-25-432/ry_retail/ryytn_dev/forecast/warehouse-filter-list',1,1,NULL,'DATAQ接口地址：查询分仓预测结果查询条件',999,NULL,'2023-10-24 09:12:21',NULL,'2023-10-24 09:13:14'),
('2','DATAQ_API_DEMAND_FORECAST_WAREHOUSE_LIST','DATAQ接口地址：查询分仓预测结果',2,'/elastic-25-432/ry_retail/ryytn_dev/forecast/warehouse-list',1,1,NULL,'DATAQ接口地址：查询分仓预测结果',999,NULL,'2023-10-24 09:12:21',NULL,'2023-10-24 09:13:20'),
('2','DATAQ_API_DEMAND_PLAN_CONFIG_CREATE','DATAQ接口地址：新增需求计划参数',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/plan/conf/create',1,1,NULL,'DATAQ接口地址：新增需求计划参数',999,NULL,'2023-10-25 10:51:08',NULL,'2023-10-25 10:51:59'),
('2','DATAQ_API_DEMAND_PLAN_CREATE','DATAQ接口地址：新增需求计划',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/plan/create',1,1,NULL,'DATAQ接口地址：新增需求计划',999,NULL,'2023-10-24 17:11:44',NULL,'2023-10-24 17:13:49'),
('2','DATAQ_API_DEMAND_PLAN_DELETE','DATAQ接口地址：删除需求计划',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/plan/delete',1,1,NULL,'DATAQ接口地址：删除需求计划',999,'admin','2023-10-27 15:56:27',NULL,'2023-10-27 15:57:23'),
('2','DATAQ_API_DEMAND_PLAN_LIST','DATAQ接口地址：查询需求计划列表',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/list',1,1,NULL,'DATAQ接口地址：查询需求计划列表',999,NULL,'2023-10-24 15:56:46',NULL,'2023-10-24 16:08:39'),
('2','DATAQ_API_DEMAND_PLAN_OFFLINE','DATAQ接口地址：下线需求计划',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/plan/offline',1,1,NULL,'DATAQ接口地址：下线需求计划',999,'admin','2023-10-27 15:45:54',NULL,'2023-10-27 15:47:55'),
('2','DATAQ_API_DEMAND_PLAN_PARAM_UPDATE','DATAQ接口地址：需求计划参数更新',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/plan/param-update',1,1,NULL,'DATAQ接口地址：需求计划参数更新',999,'admin','2023-10-27 14:31:54',NULL,'2023-10-27 15:19:24'),
('2','DATAQ_API_DEMAND_PLAN_SUBMIT','DATAQ接口地址：提交需求计划',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/paln/submit',1,1,NULL,'DATAQ接口地址：提交需求计划',999,NULL,'2023-10-25 10:37:53',NULL,'2023-10-25 10:39:02'),
('2','DATAQ_API_DEMAND_SALE_TARGET','DATAQ接口地址：业务参数-销售目标列表',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/sale-target',1,1,NULL,'DATAQ接口地址：业务参数-销售目标列表',999,NULL,'2023-10-23 11:17:53',NULL,'2023-11-06 12:58:01'),
('2','DATAQ_API_DEMAND_SALE_TARGET_COLUMN','DATAQ接口地址：业务参数-销售目标列表字段',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/sale-target-column',1,1,NULL,'DATAQ接口地址：业务参数-销售目标列表字段',999,NULL,'2023-10-23 11:17:53',NULL,'2023-10-25 14:29:12'),
('2','DATAQ_API_DEMAND_SALE_TARGET_TABLE','DATAQ接口地址：业务参数-销售目标列表（行转列）包含时间数据',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/sale-target-table',1,1,NULL,'DATAQ接口地址：业务参数-销售目标列表（行转列）包含时间数据',999,NULL,'2023-10-23 11:17:53',NULL,'2023-10-25 14:29:17'),
('2','DATAQ_API_DEMAND_SKULOCK_SKU_QUERY','DATAQ接口地址：业务参数-产品锁定查询产品详情',2,'/elastic-25-432/ry_retail/ryytn_dev/base-bus/sku-list',1,1,NULL,'DATAQ接口地址：业务参数-产品锁定查询产品详情',999,'admin','2023-10-26 16:57:20',NULL,'2023-10-26 16:58:41'),
('2','DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST','DATAQ接口地址：查询分仓需求计划列表详情',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/warehouse/list',1,1,NULL,'DATAQ接口地址：查询分仓需求计划列表详情',999,NULL,'2023-10-24 16:53:51',NULL,'2023-10-24 16:55:09'),
('2','DATAQ_API_DEVIATION_CHANNEL_DEF_LIST','DATAQ接口地址：查询偏差比对-渠道比对-列表数据提报、需求、实际',2,'/elastic-25-432/ry_retail/ryytn_dev/deviatio/channel-def-list',1,1,NULL,'DATAQ接口地址：查询偏差比对-渠道比对-列表数据提报、需求、实际',999,NULL,'2023-11-02 10:16:23',NULL,'2023-11-09 14:53:35'),
('2','DATAQ_API_DEVIATION_CHANNEL_FORCAST_LIST','DATAQ接口地址：查询偏差比对预测结果数据',NULL,'/elastic-25-432/ry_retail/ryytn_dev/deviatio/channel-forcast-list',1,1,NULL,'DATAQ接口地址：查询偏差比对预测结果数据',999,NULL,'2023-11-09 14:33:27',NULL,NULL),
('2','DATAQ_API_SYSTEM_DEVIATION_RADIO_QUERY','DATAQ接口地址：查询偏差率配置（废弃）',2,'/elastic-25-432/ry_retail/ryytn_dev//system/deviation-ratio',1,1,NULL,'DATAQ接口地址：查询偏差率配置（废弃）',999,NULL,'2023-10-26 17:32:08',NULL,'2023-11-03 16:54:44'),
('2','DATAQ_API_SYSTEM_DEVIATION_RADIO_UPDATE','DATAQ接口地址：修改偏差率配置（废弃）',2,'/elastic-25-432/ry_retail/ryytn_dev/deviation-ratio/save-or-update',1,1,NULL,'DATAQ接口地址：修改偏差率配置（废弃）',999,NULL,'2023-10-26 17:32:08',NULL,'2023-11-03 16:54:50'),
('1','OA_DEPARTMENT_URL','OA系统查询部门接口URL',2,'http://116.62.50.60:8086/api/hrm/resful/getHrmdepartmentWithPage',1,1,NULL,'OA系统查询部门接口URL',999,'admin','2023-09-27 11:03:07',NULL,'2023-09-27 11:04:19'),
('1','OA_JOBTITLE_URL','OA系统查询岗位接口URL',2,'http://116.62.50.60:8086/api/hrm/resful/getJobtitleInfoWithPage',1,1,NULL,'OA系统查询岗位接口URL',999,'admin','2023-09-27 11:04:28',NULL,'2023-09-27 11:04:31'),
('1','OA_PERSON_URL','OA系统查询人员接口URL',2,'http://116.62.50.60:8086/api/hrm/resful/getHrmUserInfoWithPage',1,1,NULL,'OA系统查询人员接口URL',999,'admin','2023-09-27 11:06:20',NULL,'2023-09-27 15:46:35'),
('1','OA_SUBCOMPANY_URL','OA系统查询分部（公司）接口URL',2,'http://116.62.50.60:8086/api/hrm/resful/getHrmsubcompanyWithPage',1,1,NULL,'OA系统查询分部（公司）接口URL',999,'admin','2023-09-27 11:01:32',NULL,'2023-09-27 11:02:22'),
('1','OA_SYNC_CLEAR_TIME','OA系统数据清理时间',2,'30',1,1,'^[1-9]\\d*$','OA系统数据清理时间，同步数据会清理上次同步时间早于此时间的数据，单位时间：天',999,'admin','2023-09-27 15:28:56',NULL,'2023-09-27 15:28:59'),
('1','OA_SYNC_LOCK_EXPIRE_TIME','OA系统数据同步锁失效时间',2,'3600',1,1,'^[1-9]\\d*$','OA系统数据同步锁失效时间，单位时间：秒',999,'admin','2023-09-27 11:11:30',NULL,'2023-09-27 11:11:34'),
('1','OA_SYNC_PAGESIZE','OA系统数据同步页长',5,'1000',1,1,'^[1-9]\\d*$','OA系统数据同步页长',999,'admin','2023-09-27 11:10:10',NULL,'2023-09-28 10:08:09'),
('1','OPERATELOG_ENABLE','操作日志开关',4,'true',1,1,'^(true|false)$','true：记录操作日志，false：不记录操作日志',999,'admin','2023-06-12 05:12:34',NULL,'2023-09-27 11:00:35'),
('1','SSO_CHECK_TIME','单点登录时间戳校验偏差',2,'3600',1,1,'^[1-9]\\d*$','单点登录时间戳校验偏差，单位时间：秒',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
('2','DATAQ_API_DEMAND_PLAN_LABEL_SET','DATAQ接口地址：设置需求计划标签',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/plan/label',1,1,NULL,'DATAQ接口地址：设置需求计划标签',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
('2','DATAQ_API_DEMAND_PLAN_CONFIRM','DATAQ接口地址：确认需求计划子计划清单',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/plan/confirm',1,1,NULL,'DATAQ接口地址：确认需求计划子计划清单',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
('2','DATAQ_API_DEMAND_PLAN_CONFIG_UPDATE','DATAQ接口地址：修改需求计划参数',2,'/elastic-25-432/ry_retail/ryytn_dev/demand/plan/param-update',1,1,NULL,'DATAQ接口地址：修改需求计划参数',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
('2','DATAQ_API_SKU_DELIVERY_LIST','DATAQ接口地址：查询产品历史销售量',2,'/elastic-25-432/ry_retail/ryytn_dev/base-bus/sku-delivery-list',1,1,NULL,'DATAQ接口地址：查询产品历史销售量',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
('2','DATAQ_API_BASEBUS_SKU_WAREHOUSE_LIST','DATAQ接口地址：查询仓库_SKU_映射列表',2,'/elastic-25-432/ry_retail/ryytn_dev/base-bus/sku_warehouse-list',1,1,NULL,'DATAQ接口地址：查询仓库_SKU_映射列表',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_DEVIATIO_SALES_PLAN_DETAIL_COLUMN','DATAQ接口地址：查询偏差比对_渠道比对_详情_列表字段',2,'/elastic-25-432/ry_retail/ryytn_dev/deviatio/sales-plan-detail-column',1,1,NULL,'DATAQ接口地址：查询偏差比对_渠道比对_详情_列表字段',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_DEVIATION_CHANNEL_DEMAND_PLAN_LIST','DATAQ接口地址：查询偏差比对_渠道比对_列表数据需求计划',2,'/elastic-25-432/ry_retail/ryytn_dev/deviatio/channel-demand-plan-list',1,1,NULL,'DATAQ接口地址：查询偏差比对_渠道比对_列表数据需求计划',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_DEVIATIO_SALES_PLAN_DETAIL','DATAQ接口地址：查询偏差比对_渠道比对_销售目标详情_行转列',2,'/elastic-25-432/ry_retail/ryytn_dev/deviatio/sales-plan-detail',1,1,NULL,'DATAQ接口地址：查询偏差比对_渠道比对_销售目标详情_行转列',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_DEVIATIO_FORCAST_DETAIL','DATAQ接口地址：查询偏差比对_渠道比对_需求预测详情_行转列',2,'/elastic-25-432/ry_retail/ryytn_dev/deviatio/forcast-detail',1,1,NULL,'DATAQ接口地址：查询偏差比对_渠道比对_需求预测详情_行转列',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_DEVIATIO_DEMAND_PLAN_DETAIL','DATAQ接口地址：查询偏差比对_渠道比对_需求预测详情_行转列',2,'/elastic-25-432/ry_retail/ryytn_dev/deviatio/demand-plan-detail',1,1,NULL,'DATAQ接口地址：查询偏差比对_渠道比对_需求预测详情_行转列',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_BASEBUS_DEMAND_REPORT_RATE','DATAQ接口地址：查询分仓需求提报比例',2,'/elastic-25-432/ry_retail/ryytn_dev/bus-param/demand-report-rate',1,1,NULL,'DATAQ接口地址：查询分仓需求提报比例',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('1','SYNC_DEMAND_PLAN_DATA_SPECIAL_LV2CHANNELCODE','同步需求计划数据特殊二级渠道编号，英文逗号分隔',2,'',1,1,NULL,'同步需求计划数据特殊二级渠道编号，英文逗号分隔，配置在此的二级渠道编号，同步时时间为周第一天，其他渠道为周最后一天',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_INVSTRAT_WAREHOUSE_SALES_VOLUME','DATAQ接口地址：查询库存策略_仓库_日均销量',2,'/elastic-25-432/ry_retail/ryytn_dev/inv-strat/warehouse-sales-volume',1,1,NULL,'DATAQ接口地址：查询库存策略_仓库_日均销量',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_INVSTRAT_FORECAST_WAREHOUSE_DAY_LIST','DATAQ接口地址：查询库存策略_预测结果_分仓预测_拆分日',2,'/elastic-25-432/ry_retail/ryytn_dev/inv-strat/forecast-warehouse-day-list',1,1,NULL,'DATAQ接口地址：查询库存策略_预测结果_分仓预测_拆分日',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_INVSTRAT_SAVE_OR_UPDATE','DATAQ接口地址：库存策略_库存配置_新增或修改配置',2,'/elastic-25-432/ry_retail/ryytn_dev/inv-strat/save-or-update',1,1,NULL,'DATAQ接口地址：库存策略_库存配置_新增或修改配置',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_INVSTRAT_CONFIG_DETAIL','DATAQ接口地址：查询库存策略_库存配置_配置详情',2,'/elastic-25-432/ry_retail/ryytn_dev/inv-strat/config-detail',1,1,NULL,'DATAQ接口地址：库存策略_库存配置_配置详情',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_BASEBUS_SKU_PRODUCT_TABLE','DATAQ接口地址：查询基础业务_生产编码_SKU_映射列表',2,'/elastic-25-432/ry_retail/ryytn_dev/base-bus/sku-product-table',1,1,NULL,'DATAQ接口地址：查询基础业务_生产编码_SKU_映射列表',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_INVSTRAT_SAVE_OR_UPDATE_BATCH','DATAQ接口地址：库存策略_库存配置_批量新增或修改配置',2,'/elastic-25-432/ry_retail/ryytn_dev/inv-strat/save-or-update-batch',1,1,NULL,'DATAQ接口地址：库存策略_库存配置_批量新增或修改配置',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_INVSTRAT_PAGE','DATAQ接口地址：查询库存策略_库存配置_列表接口',2,'/elastic-25-432/ry_retail/ryytn_dev/inv-strat/page',1,1,NULL,'DATAQ接口地址：查询库存策略_库存配置_列表接口',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_INVSTRAT_DAILY_SALES_SAVE_BATCH','DATAQ接口地址：库存策略_库存配置_批量插入日均销量',2,'/elastic-15561-551/znyy_test/ryytn_dev/inv-strat/daily-sales/save-batch',1,1,NULL,'DATAQ接口地址：库存策略_库存配置_批量插入日均销量',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
('2','DATAQ_API_INVSTRAT_DAILY_SALES_CLEAN','DATAQ接口地址：库存策略_库存配置_清除日均销量',2,'/elastic-15561-551/znyy_test/ryytn_dev/inv-strat/daily-sales/clean',1,1,NULL,'DATAQ接口地址：库存策略_库存配置_清除日均销量',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14');

insert  into "t_ryytn_configcategory"("category_id","parent_id","parent_ids","name","status","sort_no") values ('1',-1,-1,'通用设置',1,1),
('2',-1,-1,'DATAQ接口',1,2);
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('999','系统管理','','system','-1','-1','','1','','','','','1','999','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('999001','用户管理','','system:account','999','-1,999','','1','/user-manage','','','','1','1','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('999002','角色管理','','system:role','999','-1,999','','1','/role-manage','','','','1','2','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('999003','菜单管理','','system:page','999','-1,999','','1','/page-manage','','','','1','3','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('999004','字典管理','','system:dict','999','-1,999','','1','/dict-manage','','','','1','4','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('999005','配置管理','','system:config','999','-1,999','','1','/config-manage','','','','1','5','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101','需求计划','','demandPlan','-1','-1','','1','','','','','2','101','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101001','参数设置','','demandPlan:config','101','-1,101','','1','','','','','2','1','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('*********','销售目标','','demandPlan:config:sale','101001','-1,101,101001','','1','/sales-target','','','','2','1','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('*********','促销活动','','demandPlan:config:promotion','101001','-1,101,101001','','1','/activity','','','','2','2','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101001003','产品锁定期','','demandPlan:config:productLock','101001','-1,101,101001','','1','/lockup-period','','','','2','3','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101001004','渠道需求提报','','demandPlan:config:channelDemand','101001','-1,101,101001','','1','/channel-reporting','','','','2','4','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101001005','低温需求提报','','demandPlan:config:','101001','-1,101,101001','','1','/hypothermy-reporting','','','','2','5','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101001006','分仓需求提报','','demandPlan:config:dispositoryDemand','101001','-1,101,101001','','1','/swarehouse-reporting','','','','2','6','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101002','预测算法','','','101','-1,101','','1','','','','','2','2','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101002001','渠道预测算法','','','101002','-1,101,101002','','1','/channel-algorithm','','','','2','1','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('*********','分仓预测算法','','','101002','-1,101,101002','','1','/stash-algorithm','','','','2','2','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101003','预测结果','','','101','-1,101','','1','','','','','2','3','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('*********','渠道预测结果','','','101003','-1,101,101003','','1','/channel-forecast','','','','2','1','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('*********','分仓预测结果','','','101003','-1,101,101003','','1','/stash-forecast','','','','2','2','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101004','需求计划','','','101','-1,101,101004','','1','','','','','2','4','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101004001','渠道需求计划','','','101004','-1,101,101004','','1','/channel-order-plan','','','','2','1','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101004002','分仓需求计划','','','101004','-1,101,101004','','1','/stash-order-plan','','','','2','2','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101005','偏差对比','','','101','-1,101','','1','','','','','2','5','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101005001','渠道偏差对比','','','101005','-1,101,101005','','1','/deviation-comparison','','','','2','1','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('101005002','多版本比对分析','','','101005','-1,101,101005','','1','/version-comparison','','','','2','2','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('102','分销计划','','demandPlan','-1','-1','','1','','','','','2','102','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('102003','业务参数','','','102','-1,102','','1','','','','','2','3','0','');
INSERT INTO t_ryytn_page(id,name,alias,permission,parent_id,parent_ids,dependency_ids,type,path,config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES('102003001','效期分档规则','','','102003','-1,102,102003','','1','/period-lass','','','','2','1','0','');

insert  into "t_ryytn_page_config"("id","page_id","row_name","row_field","width","sort_no","freeze_flag","show_flag","gather_flag","created_by","created_time","updated_by","updated_time") values (1,12,'页面名称','pageName',15,1,0,1,0,'hh','2023-10-11 15:34:35',NULL,NULL),
(2,12,'页面编码','pageNum',15,2,0,0,0,'hh','2023-10-11 15:37:00',NULL,'2023-10-11 15:39:01'),
(3,*********,'产品分类','lv1CategoryName',150,1,0,1,0,NULL,'2023-10-30 10:54:35',NULL,NULL),
(4,*********,'产品大类','lv2CategoryName',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(5,*********,'产品小类','lv3CategoryName',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(6,*********,'一级渠道','lv1ChannelName',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(7,*********,'二级渠道','lv2ChannelName',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(8,*********,'三级渠道','lv3ChannelName',150,6,0,1,0,NULL,'2023-10-30 10:55:34',NULL,NULL),
(9,*********,'产品编码','skuCode',150,7,0,1,0,NULL,'2023-10-30 10:55:34',NULL,NULL),
(11,101001004,'产品分类','lv1CategoryName',150,1,0,1,0,NULL,'2023-10-30 10:54:35',NULL,NULL),
(12,101001004,'产品大类','lv2CategoryName',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(13,101001004,'产品小类','lv3CategoryName',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(14,101001004,'一级渠道','lv1ChannelName',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(15,101001004,'二级渠道','lv2ChannelName',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(16,101001004,'三级渠道','lv3ChannelName',150,6,0,1,0,NULL,'2023-10-30 10:55:34',NULL,NULL),
(17,101001004,'产品编码','skuCode',150,7,0,1,0,NULL,'2023-10-30 10:55:34',NULL,NULL),
(18,*********,'产品名称','skuName',150,9,0,1,0,NULL,'2023-11-21 18:47:36.71084',NULL,NULL),
(19,101001004,'产品名称','skuName',150,8,0,1,0,NULL,'2023-11-21 18:47:36.742665',NULL,NULL),
(21,101001007,'产品分类','lv1CategoryName',150,1,0,1,0,NULL,'2023-10-30 10:54:35',NULL,NULL),
(22,101001007,'产品大类','lv2CategoryName',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(23,101001007,'产品小类','lv3CategoryName',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(24,101001007,'一级渠道','lv1ChannelName',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(25,101001007,'二级渠道','lv2ChannelName',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(26,101001007,'三级渠道','lv3ChannelName',150,6,0,1,0,NULL,'2023-10-30 10:55:34',NULL,NULL),
(27,101001007,'产品编码','skuCode',150,7,0,1,0,NULL,'2023-10-30 10:55:34',NULL,NULL),
(28,101001007,'产品名称','skuName',150,8,0,1,0,NULL,'2023-11-21 18:47:36.71084',NULL,NULL),
(367977127552380928,999,'角色编码','id',99,1,1,1,1,'','2023-10-13 10:09:43',NULL,NULL),
(367977127552380929,999,'角色名称','name',20,2,0,0,0,'','2023-10-13 10:09:43',NULL,NULL),
(33,*********,'活动名称','actiName',150,1,0,1,0,NULL,'2023-10-30 10:54:35',NULL,NULL),
(34,*********,'活动类型','actiType',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(35,*********,'活动等级','actiLevel',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(36,*********,'开始时间','startDate',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(37,*********,'结束时间','endDate',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(38,*********,'活动区域(省份)','actiArea',150,6,0,1,0,NULL,'2023-10-30 10:55:34',NULL,NULL),
(39,*********,'活动状态','activityStatus',150,7,0,1,0,NULL,'2023-10-30 10:55:34',NULL,NULL),
(43,101001003,'产品分类','lv1CategoryName',150,1,0,1,0,NULL,'2023-10-30 10:54:35',NULL,NULL),
(44,101001003,'产品大类','lv2CategoryName',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(45,101001003,'产品小类','lv3CategoryName',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(46,101001003,'产品简称','skuName',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(47,101001003,'锁定开始时间','lockStartDate',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(48,101001003,'锁定结束时间','lockEndDate',150,6,0,1,0,NULL,'2023-10-30 10:55:34',NULL,NULL),
(49,101001003,'锁定渠道','channelNameList',150,7,0,1,0,NULL,'2023-10-30 10:55:34',NULL,NULL),
(53,*********,'产品分类','lv1CategoryName',150,1,0,1,0,NULL,'2023-10-30 10:54:35',NULL,NULL),
(54,*********,'产品大类','lv2CategoryName',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(55,*********,'产品小类','lv3CategoryName',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(56,*********,'一级渠道','lv1ChannelName',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(57,*********,'二级渠道','lv2ChannelName',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(61,101001006,'一级渠道','lv1ChannelName',150,1,0,1,0,NULL,'2023-10-30 10:54:35',NULL,NULL),
(62,101001006,'二级渠道','lv2ChannelName',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(63,101001006,'三级渠道','lv3ChannelName',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(64,101001006,'渠道类型','lv3ChannelType',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(65,101001006,'产品分类','lv1CategoryName',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(66,101001006,'产品大类','lv2CategoryName',150,6,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(67,101001006,'产品小类','lv3CategoryName',150,7,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(68,101001006,'产品编码','skuCode',150,8,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(69,101001006,'产品简称','skuName',150,9,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(70,101001006,'仓库','warehouseName',150,10,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(71,*********,'产品分类','lv1CategoryName',150,1,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(72,*********,'产品大类','lv2CategoryName',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(73,*********,'产品小类','lv3CategoryName',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(74,*********,'产品编码','skuCode',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(75,*********,'产品简称','skuName',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(76,*********,'仓库','dcType',150,6,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(77,*********,'渠道类型','receiverType',150,7,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(81,102006001,'生产编码','skuCode',150,1,0,1,0,NULL,'2023-10-30 10:55:33', NULL,NULL),
(82,102006001,'产品简称','skuName',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(83,102006001,'调出仓库','warehouseNameOut',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(84,102006001,'调入仓库','warehouseNameIn',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(85,102006001,'效期规则','validType',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(86,102006001,'运输方式','transType',150,6,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(91,101004003,'产品分类','lv1CategoryName',150,1,0,1,0,NULL,'2023-10-30 10:55:33', NULL,NULL),
(92,101004003,'产品大类','lv2CategoryName',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(93,101004003,'产品小类','lv3CategoryName',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(94,101004003,'一级渠道','lv1ChannelName',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(95,101004003,'二级渠道','lv2ChannelName',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(96,101004003,'产品编码','skuCode',150,6,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(97,101004003,'产品简称','skuName',150,7,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(101,101001005,'产品分类','lv1CategoryName',150,1,0,1,0,NULL,'2023-10-30 10:55:33', NULL,NULL),
(102,101001005,'产品大类','lv2CategoryName',150,2,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(103,101001005,'产品小类','lv3CategoryName',150,3,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(104,101001005,'一级渠道','lv1ChannelName',150,4,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(105,101001005,'二级渠道','lv2ChannelName',150,5,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(106,101001005,'三级渠道','lv3ChannelName',150,6,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(107,101001005,'产品编码','skuCode',150,7,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL),
(108,101001005,'产品简称','skuName',150,8,0,1,0,NULL,'2023-10-30 10:55:33',NULL,NULL);
insert  into "t_ryytn_role"("id","name","default_flag","status","description","sort_no","data_type","created_by","created_time","updated_by","updated_time") values (*********,'超级管理员角色',0,1,NULL,1,1,NULL,'2023-10-08 11:00:37',NULL,'2023-10-16 15:45:52');
insert  into "t_ryytn_thirdparty_system"("id","name","auth_code","url","status","description") values (1,'oa','SuiTZM',NULL,1,'OA系统');
INSERT INTO cdop_sys.t_ryytn_job
(job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time)
VALUES(*********, 'OA系统子公司数据同步', 1, NULL, NULL, '0 30 0 * * ?', 'cn.aliyun.ryytn.modules.system.task.SyncOASubCompanyTaskServiceImpl', NULL, NULL, NULL, 0, 1, '', '', now()),
(*********, 'OA系统部门数据同步', 1, NULL, NULL, '0 0 1 * * ?', 'cn.aliyun.ryytn.modules.system.task.SyncOADepartmentTaskServiceImpl', NULL, NULL, NULL, 0, 1, '', '', now()),
(*********, 'OA系统岗位数据同步', 1, NULL, NULL, '0 30 1 * * ?', 'cn.aliyun.ryytn.modules.system.task.SyncOAJobTitleTaskServiceImpl', NULL, NULL, NULL, 0, 1, '', '', now()),
(*********, 'OA系统员工数据同步', 1, NULL, NULL, '0 0 2 * * ?', 'cn.aliyun.ryytn.modules.system.task.SyncOAPersonTaskServiceImpl', NULL, NULL, NULL, 0, 1, '', '', now()),
(*********, '刷新日历周数据缓存', 1, NULL, NULL, '0 0 2 * * ?', 'cn.aliyun.ryytn.modules.system.task.RefreshCalendarWeekCacheTaskServiceImpl', NULL, NULL, NULL, 0, 1, '', '', now()),
(*********, '渠道需求提报滚动版本生成', 1, NULL, NULL, '0 0 6 ? * 3', 'cn.aliyun.ryytn.modules.demand.task.AddChannelDemandReportVersionTaskServiceImpl', NULL, NULL, NULL, 0, 1, '', '', now());
INSERT INTO t_ryytn_dict_type
(dict_type, dict_name, status, delete_flag, description, data_type, created_by, created_time)
VALUES('DEMAND_PLAN_LABEL', '需求计划标签', 1, 0, '需求计划标签', 1, 'admin', now());
INSERT INTO t_ryytn_dict_data
(dict_type, "name", code, parent_id, parent_ids, "level", leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time)
VALUES('DEMAND_PLAN_LABEL', '共识版', '1', '-1', '-1', 0, 1, NULL, NULL, 0, 0, 1, 0, '', 2, 'admin', now()),
('DEMAND_PLAN_LABEL', '考核版', '2', '-1', '-1', 0, 1, NULL, NULL, 0, 0, 1, 0, '', 2, 'admin', now());

-- 预置库存策略配置表数据
insert into t_ryytn_distribute_plan_inventory_strategy_conf (id, config_name, config_value,  remark, created_by)VALUES (1,'SERVICE_LEVEL','12','服务水平 可以有小数', '预置');
insert into t_ryytn_distribute_plan_inventory_strategy_conf (id, config_name, config_value,  remark, created_by)VALUES (2,'INVENTORY_SAFE_DAY_ADV_START','2023-10-15','建议安全库存参考周期起','预置');
insert into t_ryytn_distribute_plan_inventory_strategy_conf (id, config_name, config_value,  remark, created_by)VALUES (3,'INVENTORY_SAFE_DAY_ADV_END','2023-12-16','建议安全库存参考周期止','预置');
insert into t_ryytn_distribute_plan_inventory_strategy_conf (id, config_name, config_value,  remark, created_by)VALUES (4,'OUTBOUND_RATIO_3','50','M-3月出库量占比','预置');
insert into t_ryytn_distribute_plan_inventory_strategy_conf (id, config_name, config_value,  remark, created_by)VALUES (5,'OUTBOUND_RATIO_2','20','M-2月出库量占比','预置');
insert into t_ryytn_distribute_plan_inventory_strategy_conf (id, config_name, config_value,  remark, created_by)VALUES (6,'OUTBOUND_RATIO_1','10','M-1月出库量占比','预置');
insert into t_ryytn_distribute_plan_inventory_strategy_conf (id, config_name, config_value,  remark, created_by)VALUES (7,'PLAN_DEMAND_RATIO','20', '分仓需求计划量占比','预置');



