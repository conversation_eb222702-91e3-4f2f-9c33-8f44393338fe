
        // 导出模板表格头二维数组
        List<List<String>> headList = new ArrayList<List<String>>();
        // 通过反射获取固定字段表头
        Class clazz = FreightPlanDto.class;
        // 遍历所有字段，获取所有ExcelProperty注解
        List<ExcelProperty> excelPropertyList = new ArrayList<>(clazz.getDeclaredFields().length);
        List<Integer> hideCols = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields())
        {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (Objects.isNull(excelProperty))
            {
                continue;
            }
            HideProperty hideProperty = field.getAnnotation(HideProperty.class);
            if (Objects.nonNull(hideProperty) && hideProperty.isHide())
            {
                hideCols.add(excelProperty.index());
            }
            excelPropertyList.add(excelProperty);
        }
        // ExcelProperty注解集合根据index正序排序，取value值转换为List
        List<List<String>> fieldHeadList =
            excelPropertyList.stream().sorted(Comparator.comparing(ExcelProperty::index)).map(item -> {
                return Arrays.asList(item.value());
            }).collect(Collectors.toList());
        headList.addAll(fieldHeadList);

        List<String> bizDateValueList = dataqFreightPlanDao.queryFreightPlanHeadList(freightPlanDto);
        List<List<String>> trendsHeadList = new ArrayList<>();
        trendsHeadList.add(bizDateValueList);

        headList.addAll(trendsHeadList);

        // 由于存在动态字段，无法通过Java类定义电子表格数据，通过Object二维数组实现，字段顺序写死
        List<List<Object>> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(freightPlanList))
        {
            for (FreightPlanDto item : freightPlanList)
            {
                List<Object> rowDataList = new ArrayList<>();
                rowDataList.add(item.getItemId());
                rowDataList.add(item.getItemName());
                rowDataList.add(item.getStartPhysicalPointName());
                rowDataList.add(item.getEndPhysicalPointName());
                rowDataList.add(item.getValidRuleLabel());
                rowDataList.add(item.getShippingTypeGroupName());
                rowDataList.add(item.getPlanDate());
                rowDataList.add(item.getActQty());
                rowDataList.add(item.getPlanUnitCnt());
                rowDataList.add(item.getActUnitQty());
                rowDataList.add(item.getDays());
                List<PlanValue> planValueList = JSON.parseArray(item.getData(), PlanValue.class);
                if (CollectionUtils.isNotEmpty(planValueList))
                {
                    item.setDataMap(planValueList.stream().collect(Collectors.toMap(PlanValue::getPlanDate, Function.identity(), (ke1, key2) -> key2)));
                }
                for (List<String> trendsKeys : trendsHeadList)
                {
                    String key = trendsKeys.get(0);
                    Double num = 0d;
                    if (Objects.nonNull(item.getDataMap().get(key)))
                    {
                        num = item.getDataMap().get(key).getPlanValue();
                    }
                    rowDataList.add(num);
                }
                dataList.add(rowDataList);
            }
        }