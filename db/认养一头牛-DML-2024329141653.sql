INSERT INTO t_ryytn_account (id,"name",nick_name,work_code,login_id,"password",oa_id,status,description,data_type,created_time,updated_time,department_id) VALUES
	 (*********,'超级管理员','超级管理员',NULL,'sysAdmin','628c709b5d084dc6b22a6dbe87665419',NULL,1,'预置超级管理员',1,'2023-10-08 10:58:34',NULL,'-1');
INSERT INTO t_ryytn_account_role (account_id,role_id) VALUES
	 (380679300006441146,*********);
INSERT INTO t_ryytn_adjustable_days_rule (id,"name",range_type,start_time,end_time,forever_flag,created_by,created_time,updated_by,updated_time,is_default) VALUES
	 (*********,'通用规则',2,NULL,NULL,1,'sysAdmin','2024-01-02 20:56:00.575449','sysAdmin','2024-02-22 17:33:01.17',0);
INSERT INTO t_ryytn_adjustable_days_rule_range (id,rule_id,warehouse_code,warehouse_name,adjustable_days) VALUES
	 (**********,*********,'**********','山东工厂',0),
	 (*********,*********,'*********','故城工厂',9),
	 (*********,*********,'**********','杭州货权',0),
	 (*********,*********,'**********','山东行政',0);
INSERT INTO t_ryytn_button (id,"name",alias,"permission",dependency_ids,page_id,sort_no) VALUES
	 (*********,'查询','','system:account:query','',999001,1),
	 (*********,'新增','','system:account:add','',999001,2),
	 (*********,'修改','','system:role:update','',999002,3),
	 (*********,'删除','','system:role:delete','',999002,4),
	 (*********,'查询','','system:page:query','',999003,1),
	 (*********,'查询','','system:dict:query','',999004,1),
	 (*********,'删除','','system:dict:delete','',999004,4),
	 (*********,'修改','','system:config:update','',999005,2),
	 (************,'新增活动','','demandPlan:config:promotion:add','',*********,1),
	 (************,'新增锁定','','demandPlan:config:productLock:add','',*********,1),
	 (*********002,'删除','','demandPlan:config:productLock:delete','',*********,2),
	 (************,'偏差设置','','demandPlan:config:channelReport:deviation','',*********,1),
	 (*********004,'提报锁定','','demandPlan:config:channelReport:lock','',*********,4),
	 (************,'编辑','','demandPlan:config:channelReportDetail:update','',*********,3),
	 (************,'提报导入','','demandPlan:config:coldReport:import','',*********,2),
	 (*********005,'编辑','','demandPlan:config:coldReport:update','',*********,5),
	 (************,'提报导入','','demandPlan:config:warehouseReport:import','',*********,2),
	 (*********003,'提报导出','','demandPlan:config:warehouseReport:export','',*********,3),
	 (*********,'修改','','system:account:update','',999001,3),
	 (*********,'删除','','system:account:delete','',999001,4),
	 (*********,'查询','','system:role:query','',999002,1),
	 (*********,'新增','','system:role:add','',999002,2),
	 (*********,'修改','','system:page:update','',999003,2),
	 (*********,'新增','','system:dict:add','',999004,2),
	 (*********,'修改','','system:dict:update','',999004,3),
	 (*********,'查询','','system:config:query','',999005,1),
	 (*********002,'详情','','demandPlan:config:promotion:detail','',*********,2),
	 (*********003,'编辑','','demandPlan:config:promotion:update','',*********,3),
	 (*********004,'删除','','demandPlan:config:promotion:delete','',*********,4),
	 (*********002,'导出','','demandPlan:config:channelReport:export','',*********,2),
	 (*********003,'需求提报','','demandPlan:config:channelReport:detail','',*********,3),
	 (*********001,'偏差设置','','demandPlan:config:channelReportDetail:deviation','',*********,1),
	 (************,'提报导入','','demandPlan:config:channelReportDetail:import','',*********,2),
	 (*********004,'明细','','demandPlan:config:channelReportDetail:history','',*********,4),
	 (*********001,'下载模板','','demandPlan:config:coldReport:template','',*********,1),
	 (*********003,'提报导出','','demandPlan:config:coldReport:export','',*********,3),
	 (*********005,'发布提报','','demandPlan:config:warehouseReport:publish','',*********,5),
	 (101002002001,'查看','','demandPlan:forecast:algo:warehouse:result','',101002002,1),
	 (101003002001,'预测结果导出','','demandPlan:forecast:result:warehouse:export','',101003002,1),
	 (101004001003,'删除','','demandPlan:plan:channel:delete','',101004001,3),
	 (101004001008,'调整','','demandPlan:plan:channel:update','',101004001,8),
	 (101004003001,'需求计划导出','','demandPlan:plan:channel:detail:export','',101004003,1),
	 (101004002001,'试算','','demandPlan:plan:warehouse:trail','',101004002,1),
	 (101004002002,'查看','','demandPlan:plan:warehouse:detail','',101004002,2),
	 (101004002004,'调整','','demandPlan:plan:warehouse:update','',101004002,4),
	 (101004002005,'确认','','demandPlan:plan:warehouse:confirm','',101004002,5),
	 (**********01,'偏差设置','','demandPlan:deviation:channel:config','',101005001,1),
	 (*********003,'删除','','distributionPlan:config:validateRule:delete','',*********,3),
	 (*********001,'设置服务水平','','distributionPlan:config:inventoryStrategy:setup','',*********,1),
	 (*********004,'偏差设置','','demandPlan:config:coldReport:deviation','',*********,4),
	 (*********001,'下载模板','','demandPlan:config:warehouseReport:template','',*********,1),
	 (*********004,'编辑','','demandPlan:config:warehouseReport:update','',*********,4),
	 (*********001,'查看','','demandPlan:forecast:algo:channel:result','',*********,1),
	 (101003001001,'预测结果导出','','demandPlan:forecast:result:channel:export','',101003001,1),
	 (101004001001,'新增需求计划','','demandPlan:plan:channel:add','',101004001,1),
	 (101004001002,'下线','','demandPlan:plan:channel:offline','',101004001,2),
	 (101004001004,'试算','','demandPlan:plan:channel:trail','',101004001,4),
	 (101004001005,'查看','','demandPlan:plan:channel:detail','',101004001,5),
	 (101004001006,'复制','','demandPlan:plan:channel:duplicate','',101004001,6),
	 (101004001007,'设置标签','','demandPlan:plan:channel:label','',101004001,7),
	 (101004001009,'确认','','demandPlan:plan:channel:confirm','',101004001,9),
	 (101004002003,'设置标签','','demandPlan:plan:warehouse:label','',101004002,3),
	 (101004004001,'需求计划导出','','demandPlan:plan:warehouse:detail:export','',101004004,1),
	 (*********002,'新增规则','','distributionPlan:config:validateRule:add','',*********,2),
	 (*********003,'编辑','','distributionPlan:config:validateRule:update','',*********,3),
	 (*********004,'删除','','distributionPlan:config:validateRule:delete','',*********,4),
	 (*********001,'新增规则','','distributionPlan:config:adjustableDaysRule:add','',*********,1),
	 (*********002,'编辑','','distributionPlan:config:adjustableDaysRule:update','',*********,2),
	 (*********003,'删除','','distributionPlan:config:adjustableDaysRule:delete','',*********,3),
	 (*********001,'新增规则','','distributionPlan:config:validateRule:add','',*********,1),
	 (*********002,'编辑','','distributionPlan:config:validateRule:update','',*********,2),
	 (*********002,'刷新库存策略','','distributionPlan:config:inventoryStrategy:refresh','',*********,2),
	 (*********003,'编辑','','distributionPlan:config:inventoryStrategy:update','',*********,3),
	 (*********004,'详情','','distributionPlan:config:inventoryStrategy:detail','',*********,4),
	 (102004001001,'生成日分仓调拨需求（确认生成）','','distributionPlan:dailyWarehouse:modify:confirm','',102004001,1),
	 (102004001002,'日分仓需求导出','','distributionPlan:dailyWarehouse:modify:export','',102004001,2),
	 (102004002001,'日分仓调拨需求导出','','distributionPlan:dailyWarehouse:freight:export','',102004002,1),
	 (**********01,'查看','','distributionPlan:algo:config:result','',*********,1),
	 (102006001002,'重新生成调拨计划','','distributionPlan:freightPlan:freightPlan:generate','',102006001,2),
	 (102006001003,'调拨计划导出','','distributionPlan:freightPlan:freightPlan:export','',102006001,3),
	 (102006001004,'编辑','','distributionPlan:freightPlan:freightPlan:update','',102006001,4),
	 (102006001005,'查看','','distributionPlan:freightPlan:freightPlan:detail','',102006001,5);
INSERT INTO t_ryytn_config (category_id,config_id,config_name,config_type,config_value,status,is_display,"validator",description,sort_no,created_by,created_time,updated_by,updated_time) VALUES
	 ('2','DATAQ_DAILY_WAREHOUSE_AIPLAN_ALGO_VERSION','DATAQ日分仓调拨需求触发调拨计划算法版本号',2,'algo_warehouse_AIPlan_day_forecast-V0.0',1,1,NULL,'DATAQ日分仓调拨需求触发调拨计划算法版本号',999,'admin','2023-12-07 14:12:04.28364',NULL,NULL),
	 ('1','OA_SYNC_LOCK_EXPIRE_TIME','OA系统数据同步锁失效时间，单位时间：秒',2,'3600',1,1,'^[1-9]\\d*$','OA系统数据同步锁失效时间，单位时间：秒',999,'admin','2023-09-27 11:11:30',NULL,'2023-09-27 11:11:34'),
	 ('2','DATAQ_API_DISTRIBUTION_ALGO_DB_LIST','DATAQ接口地址：算法调度数据库配置密文获取',2,'/elastic-15895-624/znyy_prod/ryytn_prod/system/algo-db-list',1,1,NULL,'DATAQ接口地址：算法调度数据库配置密文获取',999,'admin','2024-01-02 17:48:04.943151',NULL,NULL),
	 ('2','DATAQ_API_DISTRIBUTION_ALGORITHM_LIST','DATAQ接口地址：分销计划算法管理列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/algorithm/distribution/list',1,1,NULL,'DATAQ接口地址：分销计划算法管理列表',999,'admin','2023-11-22 09:19:54.864998',NULL,NULL),
	 ('2','DATAQ_API_BASEBUS_SKU_WAREHOUSE_LIST','DATAQ接口地址：查询仓库_SKU_映射列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/base-bus/sku_warehouse-list',1,1,NULL,'DATAQ接口地址：查询仓库_SKU_映射列表',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
	 ('1','ACCOUNT_DEFAULT_PASSWORD','账号重置密码默认密码',1,'Password123',1,1,NULL,'账号重置密码默认密码',999,'admin','2023-09-28 18:09:16',NULL,'2023-10-08 15:36:15'),
	 ('1','OA_SYNC_CLEAR_TIME','OA系统数据清理时间，同步数据会清理上次同步时间早于此时间的数据，单位时间：天',2,'30',1,1,'^[1-9]\\d*$','OA系统数据清理时间，同步数据会清理上次同步时间早于此时间的数据，单位时间：天',999,'admin','2023-09-27 15:28:56',NULL,'2023-09-27 15:28:59'),
	 ('1','OA_SYNC_PAGESIZE','OA系统数据同步页长',5,'1000',1,1,'^[1-9]\\d*$','OA系统数据同步页长',999,'admin','2023-09-27 11:10:10',NULL,'2023-09-28 10:08:09'),
	 ('1','OPERATELOG_ENABLE','true：记录操作日志，false：不记录操作日志',4,'true',1,1,'^(true|false)$','true：记录操作日志，false：不记录操作日志',999,'admin','2023-06-12 05:12:34',NULL,'2023-09-27 11:00:35'),
	 ('1','SSO_CHECK_TIME','单点登录时间戳校验偏差，单位时间：秒',2,'3600',1,1,'^[1-9]\\d*$','单点登录时间戳校验偏差，单位时间：秒',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_VERSION_LOCK','DATAQ接口地址：锁定渠道需求提报版本',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report/update-lock',1,1,NULL,'DATAQ接口地址：锁定渠道需求提报版本',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-26 15:43:49'),
	 ('2','DATAQ_API_DEVIATIO_SALES_PLAN_DETAIL_COLUMN','DATAQ接口地址：查询偏差比对_渠道比对_详情_列表字段',2,'/elastic-15895-624/znyy_prod/ryytn_prod/deviatio/sales-plan-detail-column',1,1,NULL,'DATAQ接口地址：查询偏差比对_渠道比对_详情_列表字段',999,'admin','2023-11-22 16:55:34.446799',NULL,NULL),
	 ('2','DATAQ_API_DEVIATIO_FORCAST_DETAIL','DATAQ接口地址：查询偏差比对_渠道比对_需求预测详情_行转列',2,'/elastic-15895-624/znyy_prod/ryytn_prod/deviatio/forcast-detail',1,1,NULL,'DATAQ接口地址：查询偏差比对_渠道比对_需求预测详情_行转列',999,'admin','2023-11-09 14:33:27',NULL,NULL),
	 ('2','DATAQ_API_INVSTRAT_WAREHOUSE_SALES_VOLUME','DATAQ接口地址：查询库存策略_仓库_日均销量',2,'/elastic-15895-624/znyy_prod/ryytn_prod/inv-strat/warehouse-sales-volume',1,1,NULL,'DATAQ接口地址：查询库存策略_仓库_日均销量',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
	 ('2','DATAQ_API_INVSTRAT_FORECAST_WAREHOUSE_LIST','DATAQ接口地址：查询库存策略_预测结果_分仓预测，获取指定时间范围的最新数据',2,'/elastic-15895-624/znyy_prod/ryytn_prod/inv-strat/forecast-warehouse-list',1,1,NULL,'DATAQ接口地址：查询库存策略_预测结果_分仓预测，获取指定时间范围的最新数据',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
	 ('2','DATAQ_API_INVSTRAT_PAGE','DATAQ接口地址：查询库存策略_库存配置_列表接口',2,'/elastic-15895-624/znyy_prod/ryytn_prod/inv-strat/page',1,1,NULL,'DATAQ接口地址：查询库存策略_库存配置_列表接口',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
	 ('2','DATAQ_API_BASEBUS_SKU_PRODUCT_TABLE','DATAQ接口地址：查询基础业务_生产编码_SKU_映射列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/base-bus/sku-product-table',1,1,NULL,'DATAQ接口地址：查询基础业务_生产编码_SKU_映射列表',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
	 ('2','DATAQ_API_INVSTRAT_SAVE_OR_UPDATE_BATCH','DATAQ接口地址：库存策略_库存配置_批量新增或修改配置',2,'/elastic-15895-624/znyy_prod/ryytn_prod/inv-strat/save-or-update-batch',1,1,NULL,'DATAQ接口地址：库存策略_库存配置_批量新增或修改配置',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
	 ('2','DATAQ_API_BASEBUS_CHANNEL_LEVEL_LIST','DATAQ接口地址：查询渠道级别列表（组织与渠道关联关系）',2,'/elastic-15895-624/znyy_prod/ryytn_prod/base-bus/channel-level-list',1,1,NULL,'DATAQ接口地址：查询渠道级别列表（组织与渠道关联关系）',999,'admin','2023-12-01 15:24:18.541606',NULL,NULL),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_VERSION_LIST','DATAQ接口地址：查询渠道需求提报版本列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report-version-list',1,1,NULL,'DATAQ接口地址：查询渠道需求提报版本列表',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-26 15:43:50'),
	 ('2','DATAQ_API_DEMAND_FORECAST_ALGORITHM_LIST','DATAQ接口地址：查询渠道_分仓预测算法列表列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/algorithm/list',1,1,NULL,'DATAQ接口地址：查询渠道_分仓预测算法列表列表',999,NULL,'2023-10-23 11:17:53',NULL,'2023-10-24 16:08:34'),
	 ('2','DATAQ_API_DEMAND_FORECAST_CHANNEL','DATAQ接口地址：查询渠道预测结果',2,'/elastic-15895-624/znyy_prod/ryytn_prod/forecast/channel-table',1,1,NULL,'DATAQ接口地址：查询渠道预测结果',999,'admin','2023-11-01 15:39:26',NULL,'2023-11-08 10:26:04'),
	 ('2','DATAQ_API_DEMAND_FORECAST_CHANNEL_COLUMN','DATAQ接口地址：查询渠道预测结果列表字段',2,'/elastic-15895-624/znyy_prod/ryytn_prod/forecast/channel-column',1,1,NULL,'DATAQ接口地址：查询渠道预测结果列表字段',999,'admin','2023-11-01 15:43:48',NULL,'2023-11-08 10:26:07'),
	 ('2','DATAQ_API_DEMAND_FORECAST_WAREHOUSE','DATAQ接口地址：查询分仓数据行转列',2,'/elastic-15895-624/znyy_prod/ryytn_prod/forecast/warehouse-table',1,1,NULL,'DATAQ接口地址：查询分仓数据行转列',999,'admin','2023-12-04 11:14:10.985032',NULL,NULL),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_TEMPLATE_COLUMN','DATAQ接口地址：查询渠道需求提报导入模板字段',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/channel-demand-report-template-column',1,1,NULL,'DATAQ接口地址：查询渠道需求提报导入模板字段',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-26 17:05:24'),
	 ('2','DATAQ_API_BASEBUS_CHANNEL_LIST','DATAQ接口地址：查询渠道列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/base-bus/channel-list',1,1,NULL,'DATAQ接口地址：查询渠道列表',999,NULL,'2023-10-24 10:51:25',NULL,'2023-10-24 17:08:52'),
	 ('2','DATAQ_API_BASEBUS_SKULIST','DATAQ接口地址：查询产品列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/base-bus/sku-list',1,1,NULL,'DATAQ接口地址：查询产品列表',999,NULL,'2023-10-24 10:57:13',NULL,'2023-10-25 14:29:29'),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_RATIO','DATAQ接口地址：查询渠道需求提报数据比例',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report-ratio-list',1,1,NULL,'DATAQ接口地址：查询渠道需求提报数据比例',999,'admin','2023-10-24 10:46:19',NULL,'2023-10-25 14:29:01'),
	 ('2','DATAQ_API_DEMAND_FORECAST_CHANNEL_FILTER_LIST','DATAQ接口地址：查询渠道预测结果查询条件',2,'/elastic-15895-624/znyy_prod/ryytn_prod/forecast/channel-filter-list',1,1,NULL,'DATAQ接口地址：查询渠道预测结果查询条件',999,NULL,'2023-10-23 18:14:30',NULL,'2023-10-23 18:20:34'),
	 ('2','DATAQ_API_DEMAND_FORECAST_CHANNEL_LIST','DATAQ接口地址：查询渠道预测结果',2,'/elastic-15895-624/znyy_prod/ryytn_prod/forecast/channel-list',1,1,NULL,'DATAQ接口地址：查询渠道预测结果',999,NULL,'2023-10-23 18:14:30',NULL,'2023-10-23 18:20:34'),
	 ('2','DATAQ_API_DEMAND_FORECAST_WAREHOUSE_FILTER_LIST','DATAQ接口地址：查询分仓预测结果查询条件',2,'/elastic-15895-624/znyy_prod/ryytn_prod/forecast/warehouse-filter-list',1,1,NULL,'DATAQ接口地址：查询分仓预测结果查询条件',999,NULL,'2023-10-24 09:12:21',NULL,'2023-10-24 09:13:14'),
	 ('2','DATAQ_API_DEMAND_FORECAST_WAREHOUSE_LIST','DATAQ接口地址：查询分仓预测结果',2,'/elastic-15895-624/znyy_prod/ryytn_prod/forecast/warehouse-list',1,1,NULL,'DATAQ接口地址：查询分仓预测结果',999,NULL,'2023-10-24 09:12:21',NULL,'2023-10-24 09:13:20'),
	 ('2','DATAQ_API_DEMAND_SALE_TARGET','DATAQ接口地址：业务参数-销售目标列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/sale-target',1,1,NULL,'DATAQ接口地址：业务参数-销售目标列表',999,NULL,'2023-10-23 11:17:53',NULL,'2023-11-06 12:58:01'),
	 ('2','DATAQ_API_BUSPARAM_CONSTANT_LIST','DATAQ接口地址：查询活动常量配置列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/constant-list',1,1,NULL,'DATAQ接口地址：查询活动常量配置列表',999,NULL,'2023-10-26 11:29:37',NULL,NULL),
	 ('2','DATAQ_API_BUSPARAM_MARKET_CHANNEL_LIST','DATAQ接口地址：查询促销活动渠道列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/market-channel-list',1,1,NULL,'DATAQ接口地址：查询促销活动渠道列表',999,NULL,'2023-10-26 09:12:21',NULL,NULL),
	 ('2','DATAQ_API_BUSPARAM_MARKET_SKU_LIST','DATAQ接口地址：查询活动产品列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/market-sku-list',1,1,NULL,'DATAQ接口地址：查询活动产品列表',999,NULL,'2023-10-26 17:32:08',NULL,NULL),
	 ('2','DATAQ_API_CALENDAR_WEEK_LIST','DATAQ接口地址：查询周数据列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/calendar/week-list',1,1,NULL,'DATAQ接口地址：查询周数据列表',999,NULL,'2023-10-24 10:37:12',NULL,NULL),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_IMPORT','DATAQ接口地址：批量导入渠道需求提报数据',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report/save-batch',1,1,NULL,'DATAQ接口地址：批量导入渠道需求提报数据',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-27 10:47:34'),
	 ('2','DATAQ_API_DEMAND_SALE_TARGET_TABLE','DATAQ接口地址：业务参数-销售目标列表（行转列）包含时间数据',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/sale-target-table',1,1,NULL,'DATAQ接口地址：业务参数-销售目标列表（行转列）包含时间数据',999,NULL,'2023-10-23 11:17:53',NULL,'2023-10-25 14:29:17'),
	 ('2','DATAQ_API_DEMAND_SKULOCK_SKU_QUERY','DATAQ接口地址：业务参数-产品锁定查询产品详情',2,'/elastic-15895-624/znyy_prod/ryytn_prod/base-bus/sku-list',1,1,NULL,'DATAQ接口地址：业务参数-产品锁定查询产品详情',999,'admin','2023-10-26 16:57:20',NULL,'2023-10-26 16:58:41'),
	 ('2','DATAQ_API_BASEBUS_WAREHOUSE_LIST','DATAQ接口地址：查询仓库列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/base-bus/warehouse-list',1,1,NULL,'DATAQ接口地址：查询仓库列表',999,NULL,'2023-11-08 09:26:31',NULL,NULL),
	 ('2','DATAQ_API_BUSPARAM_MARKET_LIST','DATAQ接口地址：查询促销活动列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/market-list',1,1,NULL,'DATAQ接口地址：查询促销活动列表',999,NULL,'2023-10-24 10:42:09',NULL,'2023-10-25 14:28:45'),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_COLUMN','DATAQ接口地址：查询渠道需求提报列表字段',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report-column',1,1,NULL,'DATAQ接口地址：查询渠道需求提报列表字段',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-26 17:05:24'),
	 ('1','OA_JOBTITLE_URL','OA系统查询岗位接口URL',2,'http://oa.ryytngroup.com:8088/api/hrm/resful/getJobtitleInfoWithPage',1,1,NULL,'OA系统查询岗位接口URL',999,'admin','2023-09-27 11:04:28',NULL,'2023-09-27 11:04:31'),
	 ('2','DATAQ_APPCODE_DAILY_WAREHOUSE_AIPLAN_TASK','DATAQ探索应用编号：日分仓调拨需求触发调拨计划算法任务',2,'MICROAPP_d05dc09b0e1d4e92aef8b512bf6bfb19',1,1,NULL,'DATAQ探索应用编号：日分仓调拨需求触发调拨计划算法任务',999,'admin','2023-12-07 14:12:04.28364',NULL,NULL),
	 ('1','SYNC_DEMAND_PLAN_DATA_SPECIAL_LV2CHANNELCODE','同步需求计划数据特殊二级渠道编号，配置在此的二级渠道编号，英文逗号分隔，同步时时间为周第一天，其他渠道为周最后一天',2,'FH156',1,1,NULL,'同步需求计划数据特殊二级渠道编号，配置在此的二级渠道编号，英文逗号分隔，同步时时间为周第一天，其他渠道为周最后一天',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
	 ('2','DATAQ_APPCODE_ADD_CHANNEL_DEMAND_REPORT_VERSION','DATAQ探索应用编号：生成渠道需求提报版本',2,'MICROAPP_373a047081c540acaf7ffdb6577c0e63',1,1,NULL,'DATAQ探索应用编号：生成渠道需求提报版本',999,'admin','2023-12-07 14:12:04.28364',NULL,NULL),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_TEMPLATE_DATA','DATAQ接口地址：查询渠道需求提报导入模板数据',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report-format-table',1,1,NULL,'DATAQ接口地址：查询渠道需求提报导入模板数据',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-27 11:26:11'),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_UPDATE','DATAQ接口地址：修改渠道需求提报数据',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report/update',1,1,NULL,'DATAQ接口地址：修改渠道需求提报数据',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-31 17:54:11'),
	 ('2','DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST','DATAQ接口地址：查询分仓需求计划列表详情',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/warehouse/list',1,1,NULL,'DATAQ接口地址：查询分仓需求计划列表详情',999,NULL,'2023-10-24 16:53:51',NULL,'2023-10-24 16:55:09'),
	 ('2','DATAQ_API_SYSTEM_DEVIATION_RADIO_QUERY','DATAQ接口地址：查询偏差率配置（废弃）',2,'/elastic-15895-624/znyy_prod/ryytn_prod//system/deviation-ratio',1,1,NULL,'DATAQ接口地址：查询偏差率配置（废弃）',999,NULL,'2023-10-26 17:32:08',NULL,'2023-11-03 16:54:44'),
	 ('2','DATAQ_API_SYSTEM_DEVIATION_RADIO_UPDATE','DATAQ接口地址：修改偏差率配置（废弃）',2,'/elastic-15895-624/znyy_prod/ryytn_prod/deviation-ratio/save-or-update',1,1,NULL,'DATAQ接口地址：修改偏差率配置（废弃）',999,NULL,'2023-10-26 17:32:08',NULL,'2023-11-03 16:54:50'),
	 ('2','DATAQ_APPCODE_CHANNEL_FORECAST_TASK','DATAQ探索应用编号：渠道预测算法任务',2,'MICROAPP_70b3cd1e3fe545bba63ed1887b8b7177',1,1,NULL,'DATAQ探索应用编号：渠道预测算法任务',999,'admin','2023-12-07 14:12:04.28364',NULL,NULL),
	 ('2','DATAQ_APPCODE_GRAPH_KCCL_TASK','DATAQ探索应用编号：库存策略算法任务',2,'MICROAPP_028e00950aff41f381cc1b27bb5c507f',1,1,NULL,'DATAQ探索应用编号：库存策略算法任务',999,'admin','2023-12-07 14:12:04.28364',NULL,NULL),
	 ('2','DATAQ_APPCODE_WAREHOUSE_FORECAST_TASK','DATAQ探索应用编号：分仓预测算法任务',2,'MICROAPP_a9bc06f05e544ab9bb4c39429fb9db7e',1,1,NULL,'DATAQ探索应用编号：分仓预测算法任务',999,'admin','2023-12-07 14:12:04.28364',NULL,NULL),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_LIST','DATAQ接口地址：查询渠道需求提报列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report-list',1,1,NULL,'DATAQ接口地址：查询渠道需求提报列表',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-27 10:47:34'),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_TABLE','DATAQ接口地址：查询渠道需求提报列表（行转列）包含时间数据',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report-table',1,1,NULL,'DATAQ接口地址：查询渠道需求提报列表（行转列）包含时间数据',999,NULL,'2023-10-24 09:54:31',NULL,'2023-10-26 17:05:40'),
	 ('2','DATAQ_API_DEMAND_PLAN_CONFIG_UPDATE','DATAQ接口地址：修改需求计划参数',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/plan/param-update',1,1,NULL,'DATAQ接口地址：修改需求计划参数',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
	 ('1','OA_DEPARTMENT_URL','OA系统查询部门接口URL',2,'http://oa.ryytngroup.com:8088/api/hrm/resful/getHrmdepartmentWithPage',1,1,NULL,'OA系统查询部门接口URL',999,'admin','2023-09-27 11:03:07',NULL,'2023-09-27 11:04:19'),
	 ('1','OA_PERSON_URL','OA系统查询人员接口URL',2,'http://oa.ryytngroup.com:8088/api/hrm/resful/getHrmUserInfoWithPage',1,1,NULL,'OA系统查询人员接口URL',999,'admin','2023-09-27 11:06:20',NULL,'2023-09-27 15:46:35'),
	 ('1','OA_SUBCOMPANY_URL','OA系统查询分部（公司）接口URL',2,'http://oa.ryytngroup.com:8088/api/hrm/resful/getHrmsubcompanyWithPage',1,1,NULL,'OA系统查询分部（公司）接口URL',999,'admin','2023-09-27 11:01:32',NULL,'2023-09-27 11:02:22'),
	 ('2','DATAQ_APPCODE_ADD_WAREHOUSE_DEMAND_REPORT_VERSION','DATAQ探索应用编号：生成分仓需求提报版本',2,'MICROAPP_e516ab9c9aa64fe8b8ff0e3069b7b9cc',1,1,NULL,'DATAQ探索应用编号：生成分仓需求提报版本',999,'admin','2023-12-07 14:12:04.28364',NULL,NULL),
	 ('2','DATAQ_API_DEMAND_CHANNEL_PLAN_LIST','DATAQ接口地址：查询渠道需求计划列表详情',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/order/list',1,1,NULL,'DATAQ接口地址：查询渠道需求计划列表详情',999,NULL,'2023-10-24 16:35:30',NULL,'2023-10-24 16:37:14'),
	 ('2','DATAQ_API_DEMAND_FORECAST_CHANNEL_DETAIL','DATAQ接口地址：查询渠道预测结果详情',2,'/elastic-15895-624/znyy_prod/ryytn_prod/forecast/channel-detail',1,1,NULL,'DATAQ接口地址：查询渠道预测结果详情',999,'admin','2023-11-08 10:27:20',NULL,'2023-11-08 10:28:24'),
	 ('2','DATAQ_API_DEMAND_PLAN_CONFIG_CREATE','DATAQ接口地址：新增需求计划参数',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/plan/conf/create',1,1,NULL,'DATAQ接口地址：新增需求计划参数',999,NULL,'2023-10-25 10:51:08',NULL,'2023-10-25 10:51:59'),
	 ('2','DATAQ_API_DEMAND_PLAN_CREATE','DATAQ接口地址：新增需求计划',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/plan/create',1,1,NULL,'DATAQ接口地址：新增需求计划',999,NULL,'2023-10-24 17:11:44',NULL,'2023-10-24 17:13:49'),
	 ('2','DATAQ_API_DEMAND_PLAN_DELETE','DATAQ接口地址：删除需求计划',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/plan/delete',1,1,NULL,'DATAQ接口地址：删除需求计划',999,'admin','2023-10-27 15:56:27',NULL,'2023-10-27 15:57:23'),
	 ('2','DATAQ_APPCODE_DUPLICATE_CHANNEL_DEMAND_PLAN_VERSION','DATAQ探索应用编号：复制渠道需求提报版本',2,'MICROAPP_6abd9d6e83114c25965380cec63fa62b',1,1,NULL,'DATAQ探索应用编号：复制渠道需求提报版本',999,'admin','2023-12-07 14:12:04.28364',NULL,NULL),
	 ('2','DATAQ_API_SKU_DELIVERY_LIST','DATAQ接口地址：查询产品历史销售量',2,'/elastic-15895-624/znyy_prod/ryytn_prod/base-bus/sku-delivery-list',1,1,NULL,'DATAQ接口地址：查询产品历史销售量',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
	 ('2','DATAQ_API_DEVIATION_CHANNEL_DEMAND_PLAN_LIST','DATAQ接口地址：查询偏差比对_渠道比对_列表数据需求计划',2,'/elastic-15895-624/znyy_prod/ryytn_prod/deviatio/channel-demand-plan-list',1,1,NULL,'DATAQ接口地址：查询偏差比对_渠道比对_列表数据需求计划',999,'admin','2023-11-22 16:55:34.446799',NULL,NULL),
	 ('2','DATAQ_API_DEVIATION_CHANNEL_FORCAST_LIST','DATAQ接口地址：查询偏差比对预测结果数据',2,'/elastic-15895-624/znyy_prod/ryytn_prod/deviatio/channel-forcast-list',1,1,NULL,'DATAQ接口地址：查询偏差比对预测结果数据',999,NULL,'2023-11-09 14:33:27',NULL,NULL),
	 ('2','DATAQ_API_DEMAND_PLAN_LIST','DATAQ接口地址：查询需求计划列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/list',1,1,NULL,'DATAQ接口地址：查询需求计划列表',999,NULL,'2023-10-24 15:56:46',NULL,'2023-10-24 16:08:39'),
	 ('2','DATAQ_API_DEMAND_PLAN_OFFLINE','DATAQ接口地址：下线需求计划',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/plan/offline',1,1,NULL,'DATAQ接口地址：下线需求计划',999,'admin','2023-10-27 15:45:54',NULL,'2023-10-27 15:47:55'),
	 ('2','DATAQ_API_DEMAND_PLAN_PARAM_UPDATE','DATAQ接口地址：需求计划参数更新',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/plan/param-update',1,1,NULL,'DATAQ接口地址：需求计划参数更新',999,'admin','2023-10-27 14:31:54',NULL,'2023-10-27 15:19:24'),
	 ('2','DATAQ_API_DEMAND_PLAN_SUBMIT','DATAQ接口地址：提交需求计划',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/paln/submit',1,1,NULL,'DATAQ接口地址：提交需求计划',999,NULL,'2023-10-25 10:37:53',NULL,'2023-10-25 10:39:02'),
	 ('2','DATAQ_API_DEMAND_SALE_TARGET_COLUMN','DATAQ接口地址：业务参数-销售目标列表字段',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/sale-target-column',1,1,NULL,'DATAQ接口地址：业务参数-销售目标列表字段',999,NULL,'2023-10-23 11:17:53',NULL,'2023-10-25 14:29:12'),
	 ('2','DATAQ_API_DEVIATIO_SALES_PLAN_DETAIL','DATAQ接口地址：查询偏差比对_渠道比对_销售目标详情_行转列',2,'/elastic-15895-624/znyy_prod/ryytn_prod/deviatio/sales-plan-detail',1,1,NULL,'DATAQ接口地址：查询偏差比对_渠道比对_销售目标详情_行转列',999,'admin','2023-11-22 16:55:34.446799',NULL,NULL),
	 ('2','DATAQ_API_DEVIATIO_DEMAND_PLAN_DETAIL','DATAQ接口地址：查询偏差比对_渠道比对_需求计划详情_行转列',2,'/elastic-15895-624/znyy_prod/ryytn_prod/deviatio/demand-plan-detail',1,1,NULL,'DATAQ接口地址：查询偏差比对_渠道比对_需求计划详情_行转列',999,'admin','2023-11-09 14:33:27',NULL,NULL),
	 ('2','DATAQ_API_INVSTRAT_FORECAST_WAREHOUSE_DAY_LIST','DATAQ接口地址：查询库存策略_预测结果_分仓预测_拆分日',2,'/elastic-15895-624/znyy_prod/ryytn_prod/inv-strat/forecast-warehouse-day-list',1,1,NULL,'DATAQ接口地址：查询库存策略_预测结果_分仓预测_拆分日',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
	 ('2','DATAQ_API_BASEBUS_DEMAND_REPORT_RATE','DATAQ接口地址：查询分仓需求提报比例',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report-rate',1,1,NULL,'DATAQ接口地址：查询分仓需求提报比例',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
	 ('2','DATAQ_API_INVSTRAT_SAVE_OR_UPDATE','DATAQ接口地址：库存策略_库存配置_新增或修改配置',2,'/elastic-15895-624/znyy_prod/ryytn_prod/inv-strat/save-or-update',1,1,NULL,'DATAQ接口地址：库存策略_库存配置_新增或修改配置',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
	 ('2','DATAQ_API_INVSTRAT_CONFIG_DETAIL','DATAQ接口地址：查询库存策略_库存配置_配置详情',2,'/elastic-15895-624/znyy_prod/ryytn_prod/inv-strat/config-detail',1,1,NULL,'DATAQ接口地址：查询库存策略_库存配置_配置详情',999,'admin','2023-10-22 14:30:16',NULL,'2023-10-22 15:06:14'),
	 ('2','DATAQ_API_DEMAND_FORECAST_WAREHOUSE_COLUMN','DATAQ接口地址：查询分仓数据列表字段',2,'/elastic-15895-624/znyy_prod/ryytn_prod/forecast/warehouse-column',1,1,NULL,'DATAQ接口地址：查询分仓数据列表字段',999,'admin','2023-12-04 11:12:22.355559',NULL,NULL),
	 ('2','DATAQ_API_BASEBUS_FACTORY_LIST','DATAQ接口地址：查询工厂列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/base-bus/factory-list',1,1,NULL,'DATAQ接口地址：查询工厂列表',999,NULL,'2023-10-26 16:47:40',NULL,NULL),
	 ('2','DATAQ_API_BUSPARAM_MARKET_ACTIVITY_ADD_OR_UPDATE','DATAQ接口地址：保存修改活动',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/market-activity/add-or-update',1,1,NULL,'DATAQ接口地址：保存修改活动',999,NULL,'2023-10-30 11:19:37',NULL,NULL),
	 ('2','DATAQ_API_DEVIATION_CHANNEL_DEF_LIST','DATAQ接口地址：查询偏差比对-渠道比对-列表数据提报、需求、实际',2,'/elastic-15895-624/znyy_prod/ryytn_prod/deviatio/channel-def-list',1,1,NULL,'DATAQ接口地址：查询偏差比对-渠道比对-列表数据提报、需求、实际',999,NULL,'2023-11-02 10:16:23',NULL,'2023-11-09 14:53:35'),
	 ('2','DATAQ_API_DEMAND_PLAN_LABEL_SET','DATAQ接口地址：设置需求计划标签',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/plan/label',1,1,NULL,'DATAQ接口地址：设置需求计划标签',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
	 ('2','DATAQ_API_DEMAND_PLAN_CONFIRM','DATAQ接口地址：确认需求计划子计划清单',2,'/elastic-15895-624/znyy_prod/ryytn_prod/demand/plan/confirm',1,1,NULL,'DATAQ接口地址：确认需求计划子计划清单',999,'admin','2023-10-20 14:30:16',NULL,'2023-10-20 15:06:14'),
	 ('2','DATAQ_API_INVSTRAT_DAILY_SALES_CLEAN','DATAQ接口地址：库存策略_库存配置_清除日均销量',2,'/elastic-15895-624/znyy_prod/ryytn_prod/inv-strat/daily-sales/clean',1,1,NULL,'DATAQ接口地址：库存策略_库存配置_清除日均销量',999,'admin','2023-10-24 09:54:31',NULL,'2023-10-26 17:05:40'),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_GROUP','DATAQ接口地址：查询渠道需求提报列表分组聚合数据',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report-group',1,1,NULL,'DATAQ接口地址：查询渠道需求提报列表分组聚合数据',999,'admin','2023-10-24 09:54:31',NULL,'2023-10-26 17:05:40'),
	 ('2','DATAQ_API_BUSPARAM_MARKET_ACTIVITY_DELETED','DATAQ接口地址：删除活动',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/market-activity/deleted',1,1,NULL,'DATAQ接口地址：删除活动',999,NULL,'2023-10-30 14:18:56',NULL,NULL),
	 ('2','DATAQ_API_BUSPARAM_MARKET_ACTIVITY_RESELLER_ADD_OR_UPDATE','DATAQ接口地址：保存修改活动渠道列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/market-activity/reseller/add-or-update',1,1,NULL,'DATAQ接口地址：保存修改活动渠道列表',999,NULL,'2023-10-30 11:21:51',NULL,NULL),
	 ('2','DATAQ_API_BUSPARAM_MARKET_ACTIVITY_SKU_ADD_OR_UPDATE','DATAQ接口地址：保存修改活动产品列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/market-activity/sku/add-or-update',1,1,NULL,'DATAQ接口地址：保存修改活动产品列表',999,NULL,'2023-10-30 11:20:43',NULL,NULL),
	 ('2','DATAQ_API_CALENDAR_FSCL_LIST','DATAQ接口地址：业务参数-销售财年列表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/calendar/fscl-list',1,1,NULL,'DATAQ接口地址：业务参数-销售财年列表',999,NULL,'2023-10-24 10:46:19',NULL,'2023-10-25 14:29:01'),
	 ('2','DATAQ_API_CHANNEL_DEMAND_REPORT_JSON','DATAQ接口地址：查询渠道需求提报列表JSON格式时间数据',2,'/elastic-15895-624/znyy_prod/ryytn_prod/bus-param/demand-report-json',1,1,NULL,'DATAQ接口地址：查询渠道需求提报列表JSON格式时间数据',999,'admin','2023-10-24 09:54:31',NULL,'2023-10-26 17:05:40'),
	 ('2','DATAQ_API_INVSTRAT_DAILY_SALES_SAVE_BATCH','DATAQ接口地址：库存策略_库存配置_批量插入日均销量',2,'/elastic-15895-624/znyy_prod/ryytn_prod/inv-strat/daily-sales/save-batch',1,1,NULL,'DATAQ接口地址：库存策略_库存配置_批量插入日均销量',999,'admin','2023-10-24 09:54:31',NULL,'2023-10-26 17:05:40'),
	 ('2','DATAQ_API_GROUPING_DEMAND_FORECAST_CHANNEL_DETAIL','DATAQ接口地址：查询聚合渠道预测结果报表',2,'/elastic-15895-624/znyy_prod/ryytn_prod/forecast/channel-detail-group',1,1,NULL,'DATAQ接口地址：查询聚合渠道预测结果报表',999,'admin','2024-01-05 16:27:03.465243',NULL,NULL),
	 ('2','DATAQ_APPCODE_ADD_WAREHOUSE_DEMAND_PLAN_VERSION','DATAQ探索应用编号：渠道需求计划拆分分仓需求计划',2,'MICROAPP_e0ed46bf7f734d199c9bb6c19682097a',1,1,NULL,'DATAQ探索应用编号：渠道需求计划拆分分仓需求计划',999,'admin','2023-12-07 14:12:04.28364',NULL,NULL);
INSERT INTO t_ryytn_configcategory (category_id,parent_id,parent_ids,"name",status,sort_no) VALUES
	 ('1',-1,-1,'通用设置',1,1),
	 ('2',-1,-1,'DATAQ接口',1,2);
INSERT INTO t_ryytn_dict_data (dict_type,"name",code,parent_id,parent_ids,"level",leaf_flag,css_class,list_class,item_check,sort_no,status,delete_flag,description,data_type,created_by,created_time,updated_by,updated_time) VALUES
	 ('DEMAND_PLAN_LABEL','共识版','1','-1','-1',0,1,NULL,NULL,0,0,1,0,'',2,'admin','2023-11-17 15:39:44.956181',NULL,NULL),
	 ('VALID_RULE_LABEL','大日期','3','-1','-1',0,1,NULL,NULL,0,0,1,0,'',2,'admin','2023-11-17 15:39:44.956181',NULL,NULL),
	 ('DEMAND_PLAN_LABEL','考核版','2','-1','-1',0,1,NULL,NULL,0,0,1,0,'',2,'admin','2023-11-17 15:39:44.956181',NULL,NULL),
	 ('VALID_RULE_LABEL','新鲜效期','1','-1','-1',0,1,NULL,NULL,0,0,1,0,'',2,'admin','2023-11-17 15:39:44.956181',NULL,NULL),
	 ('VALID_RULE_LABEL','常规效期','2','-1','-1',0,1,NULL,NULL,0,0,1,0,'',2,'admin','2023-11-17 15:39:44.956181',NULL,NULL);
INSERT INTO t_ryytn_dict_type (dict_type,dict_name,status,delete_flag,description,data_type,created_by,created_time,updated_by,updated_time) VALUES
	 ('DEMAND_PLAN_LABEL','需求计划标签',1,0,'需求计划标签',1,'admin','2023-11-17 15:39:44.956181',NULL,NULL),
	 ('VALID_RULE_LABEL','效期规则标签',1,0,'效期规则标签',1,'admin','2023-11-17 15:39:44.956181',NULL,NULL);
INSERT INTO t_ryytn_distribute_plan_inventory_strategy_conf (id,config_name,config_value,remark,created_by,created_time,updated_by,updated_time) VALUES
	 (5,'SALES_DAILY_DAY','30','日均销量天数','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.55'),
	 (12,'DAILY_WAREHOUSE_DEMAND_RATIO','0','日均日分仓调拨需求占比','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.552'),
	 (1,'SERVICE_LEVEL','95','服务水平 可以有小数','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.554'),
	 (11,'PLAN_DEMAND_RATIO','1','分仓需求计划量占比','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.556'),
	 (4,'SALES_DAILY_TYPE','过去','日均销量类型 过去/未来','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.533'),
	 (8,'OUTBOUND_RATIO_3','19','M-3月出库量占比','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.536'),
	 (10,'OUTBOUND_RATIO_1','69','M-1月出库量占比','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.538'),
	 (9,'OUTBOUND_RATIO_2','30','M-2月出库量占比','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.54'),
	 (3,'INVENTORY_SAFE_DAY_ADV_END','2023-12-31','建议安全库存参考周期止','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.543'),
	 (6,'INVENTORY_TURNOVER_DAY_ADV_TYPE','未来','建议周转库存计算策略类型 过去/未来 ','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.545'),
	 (7,'INVENTORY_TURNOVER_DAY_ADV_DAY','7','建议周转库存计算策略天数','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.547'),
	 (2,'INVENTORY_SAFE_DAY_ADV_START','2023-12-11','建议安全库存参考周期起','预置','2023-11-21 11:11:25.877716','update','2024-03-26 13:43:46.549');
INSERT INTO t_ryytn_distribute_plan_valid_rule (id,"name",range_type,start_time,end_time,forever_flag,distribute_type,created_by,created_time,updated_by,updated_time,is_default) VALUES
	 (*********,'通用规则',1,'2024-01-01','2024-01-01',0,1,'新增','2024-01-21 13:31:01.083',NULL,NULL,0),
	 (100000002,'通用规则',1,'2024-01-01','2024-01-01',0,0,'新增','2024-01-21 13:23:59.59','修改','2024-03-22 15:53:33.759',0);
INSERT INTO t_ryytn_distribute_plan_valid_rule_range (id,rule_id,"name",start_day,end_day,ratio) VALUES
	 (2062298203,100000002,'常规效期',16,9999999,50.0),
	 (1795811865,*********,'常规效期',16,99999999,50.0),
	 (687275382,*********,'新鲜效期',0,15,50.0),
	 (1569602452,100000002,'大日期',0,15,50.0);
INSERT INTO t_ryytn_distribute_plan_warehouse_rule (id,"name",range_type,start_time,end_time,forever_flag,distribute_type,created_by,created_time,updated_by,updated_time,is_default) VALUES
	 (*********,'通用规则',2,NULL,NULL,1,NULL,'新增','2024-01-02 21:08:56.883','修改','2024-01-02 22:44:39.698',0);
INSERT INTO t_ryytn_job (job_id,job_name,job_type,start_date,end_date,job_conf,class_name,param,service_id,misfire_policy,concurrent,status,description,created_by,created_time,updated_by,updated_time) VALUES
	 (*********,'OA系统子公司数据同步',1,NULL,NULL,'0 30 0 * * ?','cn.aliyun.ryytn.modules.system.task.SyncOASubCompanyTaskServiceImpl','',NULL,NULL,0,1,'','','2023-11-17 10:50:11.055517',NULL,NULL),
	 (*********,'渠道需求提报滚动版本生成',1,NULL,NULL,'0 0 6 ? * 3','cn.aliyun.ryytn.modules.demand.task.AddChannelDemandReportVersionTaskServiceImpl',NULL,NULL,NULL,0,1,'','','2023-11-28 10:55:51.750224',NULL,NULL),
	 (*********,'刷新日历周数据缓存',1,NULL,NULL,'0 0 2 * * ?','cn.aliyun.ryytn.modules.system.task.RefreshCalendarWeekCacheTaskServiceImpl','',NULL,NULL,0,1,'','','2023-11-17 10:50:11.055517',NULL,NULL),
	 (*********,'刷新SKU产品销售生产映射关系数据缓存',1,NULL,NULL,'0 0 2 * * ?','cn.aliyun.ryytn.modules.system.task.RefreshSkuCacheTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-01 19:55:42.124613',NULL,NULL),
	 (*********,'定时刷新阿里dataq任务执行状态并执行补偿机制',1,NULL,NULL,'0 */30 * * * ?','cn.aliyun.ryytn.modules.scheduler.task.ReloadDataqTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-07 17:33:39.1586',NULL,NULL),
	 (100000010,'定时创建分仓需求提报分区表',1,NULL,NULL,'0 0 0 1 1 ? *','cn.aliyun.ryytn.modules.demand.task.CreateWarehouseDemandReportPartitionTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-07 17:33:39.1586',NULL,NULL),
	 (100000014,'定时执行渠道预测算法',1,NULL,NULL,'0 0 0 1 * ?','cn.aliyun.ryytn.modules.distribution.task.ChannelScheduledTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-13 15:41:42.062056',NULL,NULL),
	 (100000004,'OA系统员工数据同步',1,NULL,NULL,'0 0 2 * * ?','cn.aliyun.ryytn.modules.system.task.SyncOAPersonTaskServiceImpl','',NULL,NULL,0,1,'','','2023-11-17 10:50:11.055517',NULL,NULL),
	 (100000002,'OA系统部门数据同步',1,NULL,NULL,'0 0 1 * * ?','cn.aliyun.ryytn.modules.system.task.SyncOADepartmentTaskServiceImpl','',NULL,NULL,0,1,'','','2023-11-17 10:50:11.055517',NULL,NULL),
	 (100000003,'OA系统岗位数据同步',1,NULL,NULL,'0 30 1 * * ?','cn.aliyun.ryytn.modules.system.task.SyncOAJobTitleTaskServiceImpl','',NULL,NULL,0,1,'','','2023-11-17 10:50:11.055517',NULL,NULL),
	 (100000012,'定时执行库存策略预测算法',1,NULL,NULL,'0 0 0 2 * ?','cn.aliyun.ryytn.modules.distribution.task.LogisticsScheduledTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-12 17:13:44.30672',NULL,NULL),
	 (100000015,'定时创建日分仓调拨需求分区表',1,NULL,NULL,'0 0 0 1 1 ? *','cn.aliyun.ryytn.modules.distribution.task.CreateDailyWarehouseAiplanPartitionTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-15 10:34:24.105208',NULL,NULL),
	 (100000016,'定时创建低温需求提报分区表',1,NULL,NULL,'0 0 0 1 1 ? *','cn.aliyun.ryytn.modules.demand.task.CreateColdDemandReportPartitionTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-07 17:33:39.1586',NULL,NULL),
	 (100000013,'定时执行分仓预测算法',1,NULL,NULL,'0 0 0 ? * 2','cn.aliyun.ryytn.modules.distribution.task.WarehouseScheduledTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-13 10:30:13.209104',NULL,NULL),
	 (100000017,'低温需求提报滚动版本生成',1,NULL,NULL,'0 0 16 ? * 6','cn.aliyun.ryytn.modules.demand.task.AddColdDemandReportVersionTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-07 17:33:39.1586',NULL,NULL),
	 (100000011,'定时创建日分仓需求分区表',1,NULL,NULL,'0 0 0 1 1 ? *','cn.aliyun.ryytn.modules.distribution.task.CreateDailyWarehousePartitionTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-11 10:56:20.724123',NULL,NULL),
	 (100000007,'定时执行调拨计划算法调度',1,NULL,NULL,'0 30 13 * * ?','cn.aliyun.ryytn.modules.distribution.task.AiplanScheduledTaskServiceImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-11-30 10:07:30.943625',NULL,NULL),
	 (100000018,'定时同步仓能力规则中间表',1,NULL,NULL,'0 0 0 * * ?','cn.aliyun.ryytn.modules.distribution.task.WarehouseRuleScheduledTaskImpl',NULL,NULL,NULL,0,1,NULL,NULL,'2023-12-27 10:13:06.271226',NULL,NULL),
	 (100000019,'刷新物理仓逻辑仓映射关系',1,NULL,NULL,'0 0 2 * * ?','cn.aliyun.ryytn.modules.system.task.RefreshWarehouseMappingCacheTaskServiceImpl','',NULL,NULL,0,1,'','','2023-11-17 10:50:11.055517',NULL,NULL);
INSERT INTO t_ryytn_page (id,"name",alias,"permission",parent_id,parent_ids,dependency_ids,"type","path",config_path,component,icon,moudel_id,sort_no,sum_flag,description) VALUES
	 (999003,'菜单管理','','system:page',999,'-1,999','','1','/page-manage','','','',1,9990030000,0,''),
	 (101,'需求计划','','demandPlan',-1,'-1','','1','','','','',1,1010000000,0,''),
	 (101002,'预测算法','','',101,'-1,101','','1','','','','',1,1010020000,0,''),
	 (101002002,'分仓预测算法','','',101002,'-1,101,101002','','1','/stash-algorithm','','','',1,1010020020,0,''),
	 (101004,'需求计划','','',101,'-1,101,101004','','1','','','','',1,1010040000,0,''),
	 (101004001,'渠道需求计划','','',101004,'-1,101,101004','','1','/channel-order-plan','','','',1,1010040010,0,''),
	 (101004002,'分仓需求计划','','',101004,'-1,101,101004','','1','/stash-order-plan','','','',1,1010040020,0,''),
	 (101005,'偏差对比','','',101,'-1,101','','1','','','','',1,1010050000,0,''),
	 (101005001,'渠道偏差对比','','',101005,'-1,101,101005','','1','/deviation-comparison','','','',1,**********,0,''),
	 (102,'分销计划','','demandPlan',-1,'-1','','1','','','','',1,**********,0,''),
	 (102003,'业务参数','','',102,'-1,102','','1','','','','',1,**********,0,''),
	 (*********,'可调天数规则','','',102003,'-1,102,102003','','1','/adjustable-days','','','',1,*********0,0,''),
	 (*********,'仓能力规则','','',102003,'-1,102,102003','','1','/warehouse-capacity','','','',1,*********0,0,''),
	 (999001,'用户管理','','system:account',999,'-1,999','','1','/user-manage','','','',1,**********,0,''),
	 (999002,'角色管理','','system:role',999,'-1,999','','1','/role-manage','','','',1,**********,0,''),
	 (101001,'参数设置','','demandPlan:config',101,'-1,101','','1','','','','',1,**********,0,''),
	 (*********,'渠道预测算法','','',101002,'-1,101,101002','','1','/channel-algorithm','','','',1,*********0,0,''),
	 (101003,'预测结果','','',101,'-1,101','','1','','','','',1,**********,0,''),
	 (*********,'效期分档规则','','',102003,'-1,102,102003','','1','/period-lass','','','',1,*********0,0,''),
	 (*********,'库存策略','','',102003,'-1,102,102003','','1','/inventory-strategy','','','',1,*********0,0,''),
	 (102004,'日分仓需求','','',102,'-1,102','','1','','','','',1,**********,0,''),
	 (102005,'算法管理','','',102,'-1,102','','1','','','','',1,**********,0,''),
	 (*********,'算法管理','','',102005,'-1,102,102005','','1','/algorithm-management','','','',1,**********,0,''),
	 (*********,'产品锁定期','','demandPlan:config:productLock',101001,'-1,101,101001','','1','/lockup-period','','','',1,*********0,0,''),
	 (101004004,'分仓需求计划-查看详情','','',101004,'-1,101,101004','','99999','/stash-view-list','','','',1,1010040040,0,''),
	 (102006002,'装车建议','','',102006,'-1,102,102006','','99999','','','','',1,1020060020,0,''),
	 (102006003,'调拨单','','',102006,'-1,102,102006','','99999','/transfer-plan','','','',1,1020060030,0,''),
	 (101001001,'销售目标','','demandPlan:config:sale',101001,'-1,101,101001','','1','/sales-target','','','',1,1010010010,0,''),
	 (102006001,'调拨计划','','',102006,'-1,102,102006','','1','/transfer-plan','','','',1,1020060010,0,''),
	 (102004003,'生成日分仓调拨求','','distributionPlan:algo',102004,'-1,102,102004','','99999','/daily-warehouse-generation','','','',1,1020040030,0,''),
	 (101004008,'分仓需求计划-需求计划调整','','',101003,'-1,101,101003','','99999','/stash-modify-plan','','','',1,1010040080,0,''),
	 (101004007,'分仓需求计划-子计划清单','','',101003,'-1,101,101003','','99999','/stash-sub-plan','','','',1,1010040070,0,''),
	 (102006006,'调拨计划多仓比对','','',102006,'-1,102,102006','','99999','/transfer-plan-detail','','','',1,1020060060,0,''),
	 (102006008,'调拨计划RDC推演','','',102006,'-1,102,102006','','99999','/inventory-inferenceRDC','','','',1,1020060080,0,''),
	 (102004001,'日分仓需求编辑','','',102004,'-1,102,102004','','1','/day-warehouse-edit','','','',1,1020040010,0,''),
	 (102006,'调拨计划','','',102,'-1,102','','1','','','','',1,1020060000,0,''),
	 (102004002,'日分仓调拨需求','','',102004,'-1,102,102004','','1','/day-warehouse-allocation','','','',1,1020040020,0,''),
	 (101003001,'渠道预测结果','','',101003,'-1,101,101003','','1','/channel-forecast','','','',1,1010030010,0,''),
	 (101003002,'分仓预测结果','','',101003,'-1,101,101003','','1','/stash-forecast','','','',1,1010030020,0,''),
	 (*********,'促销活动','','demandPlan:config:promotion',101001,'-1,101,101001','','1','/activity','','','',1,*********0,0,''),
	 (101004003,'渠道需求计划-查看详情','','',101004,'-1,101,101004','','99999','/channel-view-list','','','',1,1010040030,0,''),
	 (*********,'渠道需求提报','','demandPlan:config:channelDemand',101001,'-1,101,101001','','1','/channel-reporting','','','',1,*********0,0,''),
	 (*********,'低温需求提报','','demandPlan:config:',101001,'-1,101,101001','','1','/lowTemperature-reporting','','','',1,*********0,0,''),
	 (*********,'分仓需求提报','','demandPlan:config:dispositoryDemand',101001,'-1,101,101001','','1','/swarehouse-reporting','','','',1,*********0,0,''),
	 (*********,'渠道需求提报-详情','','demandPlan:config:channelDemandDetail',101001,'-1,101,101001','','99999','/channel-reporting-detail','','','',1,*********0,0,''),
	 (999,'系统管理','','system',-1,'-1','','1','','','','',1,9990000000,0,''),
	 (102003005,'仓能力编辑','','',102003,'-1,102,102003','','99999','/editWarehouse','','','',1,1020030050,0,''),
	 (102006004,'调拨计划编辑','','',102006,'-1,102,102006','','99999','/transfer-plan-edit','','','',1,1020060040,0,''),
	 (102006005,'调拨计划查看','','',102006,'-1,102,102006','','99999','','','','',1,1020060050,0,''),
	 (101001008,'分仓需求提报-编辑','','',101001,'-1,101,101001','','99999','/swarehouse-reporting-detail','','','',1,1010010080,0,''),
	 (101005002,'多版本比对',NULL,NULL,101005,'-1,101,101005',NULL,'99999','/version-comparison',NULL,NULL,NULL,1,1010050020,0,NULL),
	 (101004005,'渠道需求计划-子计划清单','','',101004,'-1,101,101004','','99999','/channel-sub-plan','','','',1,1010040050,0,''),
	 (101004006,'渠道需求计划-需求计划调整','','',101004,'-1,101,101004','','99999','/channel-modify-plan','','','',1,1010040060,0,''),
	 (102006007,'调拨计划CDC推演','','',102006,'-1,102,102006','','99999','/inventory-inferenceCDC','','','',1,1020060070,0,'');
INSERT INTO t_ryytn_page_config (page_id,row_name,row_field,width,sort_no,freeze_flag,show_flag,gather_flag,created_by,created_time,updated_by,updated_time) VALUES
	 (12,'页面名称','pageName',15,1,0,1,0,'hh','2023-10-11 15:34:35',NULL,NULL),
	 (101003001,'产品小类','lv3CategoryName',120,3,0,1,0,'sysAdmin','2023-12-29 09:48:44.830471',NULL,NULL),
	 (101003002,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2023-12-29 09:49:35.450107',NULL,NULL),
	 (101003002,'产品小类','lv3CategoryName',120,3,0,1,0,'sysAdmin','2023-12-29 09:49:35.450107',NULL,NULL),
	 (101003002,'产品编码','skuCode',120,4,0,1,0,'sysAdmin','2023-12-29 09:49:35.450107',NULL,NULL),
	 (101003002,'产品简称','skuName',120,5,0,1,0,'sysAdmin','2023-12-29 09:49:35.450107',NULL,NULL),
	 (101003002,'仓库','warehouseName',120,6,0,1,0,'sysAdmin','2023-12-29 09:49:35.450107',NULL,NULL),
	 (101003002,'渠道类型','receiverType',120,7,0,1,0,'sysAdmin','2023-12-29 09:49:35.450107',NULL,NULL),
	 (*********,'活动区域(省份)','actiArea',120,6,0,1,0,'sysAdmin','2023-12-29 09:49:46.89933',NULL,NULL),
	 (*********,'产品小类','lv3CategoryName',120,3,0,1,0,'sysAdmin','2023-12-29 09:50:03.631084',NULL,NULL),
	 (*********,'产品简称','skuName',120,4,0,1,0,'sysAdmin','2023-12-29 09:50:03.631084',NULL,NULL),
	 (*********,'锁定开始时间','lockStartDate',120,5,0,1,0,'sysAdmin','2023-12-29 09:50:03.631084',NULL,NULL),
	 (*********,'锁定渠道','channelNameList',120,7,0,1,0,'sysAdmin','2023-12-29 09:50:03.631084',NULL,NULL),
	 (101004003,'产品大类','lv2CategoryName',120,2,0,1,0,'sysAdmin','2023-12-29 09:50:15.992954',NULL,NULL),
	 (101004003,'产品小类','lv3CategoryName',120,3,0,1,0,'sysAdmin','2023-12-29 09:50:15.992954',NULL,NULL),
	 (12,'页面编码','pageNum',15,2,0,1,0,'hh','2023-10-11 15:37:00',NULL,'2023-10-11 15:39:01'),
	 (101003001,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2023-12-29 09:48:44.830471',NULL,NULL),
	 (101003001,'产品大类','lv2CategoryName',120,2,0,1,0,'sysAdmin','2023-12-29 09:48:44.830471',NULL,NULL),
	 (101003001,'一级渠道','lv1ChannelName',120,4,0,1,0,'sysAdmin','2023-12-29 09:48:44.830471',NULL,NULL),
	 (101003001,'二级渠道','lv2ChannelName',120,5,0,1,0,'sysAdmin','2023-12-29 09:48:44.830471',NULL,NULL),
	 (101003002,'产品大类','lv2CategoryName',120,2,0,1,0,'sysAdmin','2023-12-29 09:49:35.450107',NULL,NULL),
	 (*********,'活动名称','actiName',120,1,0,1,0,'sysAdmin','2023-12-29 09:49:46.89933',NULL,NULL),
	 (*********,'活动类型','actiType',120,2,0,1,0,'sysAdmin','2023-12-29 09:49:46.89933',NULL,NULL),
	 (*********,'活动等级','actiLevel',120,3,0,1,0,'sysAdmin','2023-12-29 09:49:46.89933',NULL,NULL),
	 (*********,'开始时间','startDate',120,4,0,1,0,'sysAdmin','2023-12-29 09:49:46.89933',NULL,NULL),
	 (*********,'结束时间','endDate',120,5,0,1,0,'sysAdmin','2023-12-29 09:49:46.89933',NULL,NULL),
	 (*********,'活动状态','activityStatus',120,7,0,1,0,'sysAdmin','2023-12-29 09:49:46.89933',NULL,NULL),
	 (*********,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2023-12-29 09:50:03.631084',NULL,NULL),
	 (*********,'产品大类','lv2CategoryName',120,2,0,1,0,'sysAdmin','2023-12-29 09:50:03.631084',NULL,NULL),
	 (*********,'锁定结束时间','lockEndDate',120,6,0,1,0,'sysAdmin','2023-12-29 09:50:03.631084',NULL,NULL),
	 (101004003,'二级渠道','lv2ChannelName',120,5,0,1,0,'sysAdmin','2023-12-29 09:50:15.992954',NULL,NULL),
	 (101004003,'产品编码','skuCode',120,6,0,1,0,'sysAdmin','2023-12-29 09:50:15.992954',NULL,NULL),
	 (101004003,'成品名称','skuName',120,7,0,1,0,'sysAdmin','2023-12-29 09:50:15.992954',NULL,NULL),
	 (*********,'一级渠道','lv1ChannelName',120,4,0,1,0,'sysAdmin','2023-12-29 09:50:36.240724',NULL,NULL),
	 (*********,'二级渠道','lv2ChannelName',120,5,0,1,0,'sysAdmin','2023-12-29 09:50:36.240724',NULL,NULL),
	 (*********,'三级渠道','lv3ChannelName',120,6,0,1,0,'sysAdmin','2023-12-29 09:50:36.240724',NULL,NULL),
	 (*********,'产品编码','skuCode',120,7,0,1,0,'sysAdmin','2023-12-29 09:50:36.240724',NULL,NULL),
	 (*********,'产品简称','skuName',120,8,0,1,0,'sysAdmin','2023-12-29 09:50:36.240724',NULL,NULL),
	 (101004004,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2023-12-29 09:51:18.708782',NULL,NULL),
	 (101004004,'仓库','warehouseName',120,1,0,1,0,'sysAdmin','2023-12-29 09:51:18.708782',NULL,NULL),
	 (101004004,'产品简称','skuName',120,1,0,1,0,'sysAdmin','2023-12-29 09:51:18.708782',NULL,NULL),
	 (*********,'产品大类','lv2CategoryName',120,2,0,1,0,'sysAdmin','2023-12-29 10:02:11.030756',NULL,NULL),
	 (*********,'产品小类','lv3CategoryName',120,3,0,1,0,'sysAdmin','2023-12-29 10:02:11.030756',NULL,NULL),
	 (*********,'一级渠道','lv1ChannelName',120,4,0,1,0,'sysAdmin','2023-12-29 10:02:11.030756',NULL,NULL),
	 (*********,'二级渠道','lv2ChannelName',120,5,0,1,0,'sysAdmin','2023-12-29 10:02:11.030756',NULL,NULL),
	 (101004003,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2023-12-29 09:50:15.992954',NULL,NULL),
	 (101004003,'一级渠道','lv1ChannelName',120,4,0,1,0,'sysAdmin','2023-12-29 09:50:15.992954',NULL,NULL),
	 (*********,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2023-12-29 09:50:36.240724',NULL,NULL),
	 (*********,'产品大类','lv2CategoryName',120,2,0,1,0,'sysAdmin','2023-12-29 09:50:36.240724',NULL,NULL),
	 (*********,'产品小类','lv3CategoryName',120,3,0,1,0,'sysAdmin','2023-12-29 09:50:36.240724',NULL,NULL),
	 (101004004,'产品大类','lv2CategoryName',120,1,0,1,0,'sysAdmin','2023-12-29 09:51:18.708782',NULL,NULL),
	 (101004004,'产品小类','lv3CategoryName',120,1,0,1,0,'sysAdmin','2023-12-29 09:51:18.708782',NULL,NULL),
	 (101004004,'产品编码','skuCode',120,1,0,1,0,'sysAdmin','2023-12-29 09:51:18.708782',NULL,NULL),
	 (*********,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2023-12-29 10:02:11.030756',NULL,NULL),
	 (*********,'三级渠道','lv3ChannelName',120,6,0,1,0,'sysAdmin','2023-12-29 10:02:11.030756',NULL,NULL),
	 (*********,'产品简称','skuName',120,8,0,1,0,'sysAdmin','2023-12-29 10:02:11.030756',NULL,NULL),
	 (*********,'二级渠道','lv2ChannelName',120,2,0,1,0,'sysAdmin','2023-12-29 10:02:24.315519',NULL,NULL),
	 (*********,'渠道类型','receiverType',120,4,0,1,0,'sysAdmin','2023-12-29 10:02:24.315519',NULL,NULL),
	 (*********,'产品大类','lv2CategoryName',120,6,0,1,0,'sysAdmin','2023-12-29 10:02:24.315519',NULL,NULL),
	 (*********,'仓库','warehouseName',120,10,0,1,0,'sysAdmin','2023-12-29 10:02:24.315519',NULL,NULL),
	 (*********,'产品编码','skuCode',120,7,0,1,0,'sysAdmin','2023-12-29 10:02:11.030756',NULL,NULL),
	 (*********,'一级渠道','lv1ChannelName',120,1,0,1,0,'sysAdmin','2023-12-29 10:02:24.315519',NULL,NULL),
	 (*********,'三级渠道','lv3ChannelName',120,3,0,1,0,'sysAdmin','2023-12-29 10:02:24.315519',NULL,NULL),
	 (*********,'产品分类','lv1CategoryName',120,5,0,1,0,'sysAdmin','2023-12-29 10:02:24.315519',NULL,NULL),
	 (*********,'产品小类','lv3CategoryName',120,7,0,1,0,'sysAdmin','2023-12-29 10:02:24.315519',NULL,NULL),
	 (*********,'产品编码','skuCode',120,8,0,1,0,'sysAdmin','2023-12-29 10:02:24.315519',NULL,NULL),
	 (*********,'产品简称','skuName',120,9,0,1,0,'sysAdmin','2023-12-29 10:02:24.315519',NULL,NULL),
	 (*********,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2023-12-29 10:02:39.185408',NULL,NULL),
	 (*********,'产品大类','lv2CategoryName',120,2,0,1,0,'sysAdmin','2023-12-29 10:02:39.185408',NULL,NULL),
	 (*********,'二级渠道','lv2ChannelName',120,5,0,1,0,'sysAdmin','2023-12-29 10:02:39.185408',NULL,NULL),
	 (*********,'三级渠道','lv3ChannelName',120,6,0,1,0,'sysAdmin','2023-12-29 10:02:39.185408',NULL,NULL),
	 (*********,'产品编码','skuCode',120,7,0,1,0,'sysAdmin','2023-12-29 10:02:39.185408',NULL,NULL),
	 (999,'角色编码','id',120,1,0,1,1,'sysAdmin','2023-12-29 10:02:53.390062',NULL,NULL),
	 (999,'角色名称','name',120,2,0,1,0,'sysAdmin','2023-12-29 10:02:53.390062',NULL,NULL),
	 (102004002,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2024-01-03 09:42:06.245487',NULL,NULL),
	 (*********,'产品小类','lv3CategoryName',120,3,0,1,0,'sysAdmin','2023-12-29 10:02:39.185408',NULL,NULL),
	 (*********,'一级渠道','lv1ChannelName',120,4,0,1,0,'sysAdmin','2023-12-29 10:02:39.185408',NULL,NULL),
	 (*********,'产品名称','skuName',120,9,0,1,0,'sysAdmin','2023-12-29 10:02:39.185408',NULL,NULL),
	 (102004002,'产品大类','lv2CategoryName',120,2,0,1,0,'sysAdmin','2024-01-03 09:45:04.040869',NULL,NULL),
	 (102004002,'产品小类','lv3CategoryName',150,3,0,1,0,'sysAdmin','2024-01-03 09:45:54.646838',NULL,NULL),
	 (102004002,'仓库','warehouseName',150,6,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102004001,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2024-01-03 09:42:06.245487',NULL,NULL),
	 (102004001,'产品大类','lv2CategoryName',120,2,0,1,0,'sysAdmin','2024-01-03 09:45:04.040869',NULL,NULL),
	 (102004001,'产品编码','skuCode',150,4,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102004001,'仓库','warehouseName',150,6,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (101001001,'产品分类','lv1CategoryName',120,1,0,1,0,'liguangyao','2024-02-06 08:43:22.180716',NULL,NULL),
	 (101001001,'产品大类','lv2CategoryName',120,2,0,1,0,'liguangyao','2024-02-06 08:43:22.180716',NULL,NULL),
	 (101001001,'二级渠道','lv2ChannelName',120,5,0,1,0,'liguangyao','2024-02-06 08:43:22.180716',NULL,NULL),
	 (101001001,'产品简称','skuName',120,9,0,1,0,'liguangyao','2024-02-06 08:43:22.180716',NULL,NULL),
	 (102006001,'产品简称','itemName',120,2,0,1,0,'sysAdmin','2024-02-29 11:22:03.577482',NULL,NULL),
	 (102004002,'产品编码','skuCode',150,4,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102004002,'产品简称','skuName',150,5,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102004002,'渠道类型','distributeType',120,7,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102004002,'效期规则','validityPeriod',150,8,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102004001,'产品小类','lv3CategoryName',150,3,0,1,0,'sysAdmin','2024-01-03 09:45:54.646838',NULL,NULL),
	 (102004001,'产品简称','skuName',150,5,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102004001,'渠道类型','distributeType',120,7,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (101004004,'渠道类型','receiverType',150,1,0,1,0,'sysAdmin','2023-12-29 09:51:18.708782',NULL,NULL),
	 (*********,'产品编号','skuCode',120,4,0,1,0,'sysAdmin','2023-12-29 09:50:03.631084',NULL,NULL),
	 (101001001,'产品小类','lv3CategoryName',120,3,0,1,0,'liguangyao','2024-02-06 08:43:22.180716',NULL,NULL),
	 (101001001,'一级渠道','lv1ChannelName',120,4,0,1,0,'liguangyao','2024-02-06 08:43:22.180716',NULL,NULL),
	 (101001001,'三级渠道','lv3ChannelName',120,6,0,1,0,'liguangyao','2024-02-06 08:43:22.180716',NULL,NULL),
	 (101001001,'产品编码','skuCode',120,7,0,1,0,'liguangyao','2024-02-06 08:43:22.180716',NULL,NULL),
	 (101001001,'单位','unit',120,10,0,1,0,'liguangyao','2024-02-06 08:43:22.180716',NULL,NULL),
	 (102006001,'生产编码','itemId',120,1,0,1,0,'sysAdmin','2024-02-29 11:22:03.577482',NULL,NULL),
	 (102006001,'调出物理仓','startPhysicalPointName',120,3,0,1,0,'sysAdmin','2024-02-29 11:22:03.577482',NULL,NULL),
	 (102006001,'调出逻辑仓','startPointName',120,4,0,1,0,'sysAdmin','2024-02-29 11:22:03.577482',NULL,NULL),
	 (102006001,'调入逻辑仓','endPointName',120,6,0,1,0,'sysAdmin','2024-02-29 11:22:03.577482',NULL,NULL),
	 (102006001,'运输方式','shippingTypeGroupName',120,8,0,1,0,'sysAdmin','2024-02-29 11:22:03.577482',NULL,NULL),
	 (102004003,'产品大类','lv2CategoryName',120,2,0,1,0,'sysAdmin','2024-01-03 09:45:04.040869',NULL,NULL),
	 (102004003,'仓库','warehouseName',150,6,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102004003,'产品小类','lv3CategoryName',150,3,0,1,0,'sysAdmin','2024-01-03 09:45:54.646838',NULL,NULL),
	 (102004003,'产品简称','skuName',150,5,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102006001,'调入物理仓','endPhysicalPointName',120,5,0,1,0,'sysAdmin','2024-02-29 11:22:03.577482',NULL,NULL),
	 (102004003,'产品分类','lv1CategoryName',120,1,0,1,0,'sysAdmin','2024-01-03 09:42:06.245487',NULL,NULL),
	 (102004003,'产品编码','skuCode',150,4,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102004003,'渠道类型','distributeType',120,7,0,1,0,'sysAdmin','2024-01-03 09:47:19.861853',NULL,NULL),
	 (102006001,'效期规则','departureValid',120,7,0,1,0,'sysAdmin','2024-02-29 11:22:03.577482',NULL,NULL);
INSERT INTO t_ryytn_role (id,"name",default_flag,status,description,sort_no,data_type,created_by,created_time,updated_by,updated_time) VALUES
	 (*********,'超级管理员角色',0,1,NULL,1,1,NULL,'2023-10-08 11:00:37','c010198','2023-10-16 15:45:52');
INSERT INTO t_ryytn_thirdparty_system (id,"name",auth_code,url,status,description) VALUES
	 (1,'oa','SuiTZM',NULL,1,'OA系统');
