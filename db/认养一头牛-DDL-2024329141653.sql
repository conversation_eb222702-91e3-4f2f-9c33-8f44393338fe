DROP TABLE IF EXISTS qrtz_blob_triggers;
CREATE TABLE qrtz_blob_triggers(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_name` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_name的外键' ,
    `trigger_group` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_group的外键' ,
    `blob_data` bytea(2147483647)    COMMENT '存放持久化Trigger对象' ,
    PRIMARY KEY (sched_name,trigger_name,trigger_group)
)  COMMENT = 'Blob类型的触发器表';


CREATE UNIQUE INDEX qrtz_blob_triggers_pkey ON qrtz_blob_triggers(sched_name,trigger_name,trigger_group);

DROP TABLE IF EXISTS qrtz_calendars;
CREATE TABLE qrtz_calendars(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `calendar_name` varchar(200) NOT NULL   COMMENT '日历名称' ,
    `calendar` bytea(2147483647) NOT NULL   COMMENT '存放持久化calendar对象' ,
    PRIMARY KEY (sched_name,calendar_name)
)  COMMENT = '日历信息表';


CREATE UNIQUE INDEX qrtz_calendars_pkey ON qrtz_calendars(sched_name,calendar_name);

DROP TABLE IF EXISTS qrtz_cron_triggers;
CREATE TABLE qrtz_cron_triggers(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_name` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_name的外键' ,
    `trigger_group` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_group的外键' ,
    `cron_expression` varchar(200) NOT NULL   COMMENT 'cron表达式' ,
    `time_zone_id` varchar(80)    COMMENT '时区' ,
    PRIMARY KEY (sched_name,trigger_name,trigger_group)
)  COMMENT = 'Cron类型的触发器表';


CREATE UNIQUE INDEX qrtz_cron_triggers_pkey ON qrtz_cron_triggers(sched_name,trigger_name,trigger_group);

DROP TABLE IF EXISTS qrtz_fired_triggers;
CREATE TABLE qrtz_fired_triggers(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `entry_id` varchar(95) NOT NULL   COMMENT '调度器实例id' ,
    `trigger_name` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_name的外键' ,
    `trigger_group` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_group的外键' ,
    `instance_name` varchar(200) NOT NULL   COMMENT '调度器实例名' ,
    `fired_time` int8(19) NOT NULL   COMMENT '触发的时间' ,
    `sched_time` int8(19) NOT NULL   COMMENT '定时器制定的时间' ,
    `priority` int4 NOT NULL   COMMENT '优先级' ,
    `state` varchar(16) NOT NULL   COMMENT '状态' ,
    `job_name` varchar(200)    COMMENT '任务名称' ,
    `job_group` varchar(200)    COMMENT '任务组名' ,
    `is_nonconcurrent` bool(1) NOT NULL   COMMENT '是否并发' ,
    `requests_recovery` bool(1) NOT NULL   COMMENT '是否接受恢复执行' ,
    PRIMARY KEY (sched_name,entry_id)
)  COMMENT = '已触发的触发器表';


CREATE UNIQUE INDEX qrtz_fired_triggers_pkey ON qrtz_fired_triggers(sched_name,entry_id);

DROP TABLE IF EXISTS qrtz_job_details;
CREATE TABLE qrtz_job_details(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `job_name` varchar(200) NOT NULL   COMMENT '任务名称' ,
    `job_group` varchar(200) NOT NULL   COMMENT '任务组名' ,
    `description` varchar(250)    COMMENT '相关介绍' ,
    `job_class_name` varchar(250) NOT NULL   COMMENT '执行任务类名称' ,
    `is_durable` bool(1) NOT NULL   COMMENT '是否持久化' ,
    `is_nonconcurrent` bool(1) NOT NULL   COMMENT '是否并发' ,
    `is_update_data` bool(1) NOT NULL   COMMENT '是否更新数据' ,
    `requests_recovery` bool(1) NOT NULL   COMMENT '是否接受恢复执行' ,
    `job_data` bytea(2147483647)    COMMENT '存放持久化job对象' ,
    PRIMARY KEY (sched_name,job_name,job_group)
)  COMMENT = '任务详细信息表';


CREATE UNIQUE INDEX qrtz_job_details_pkey ON qrtz_job_details(sched_name,job_name,job_group);

DROP TABLE IF EXISTS qrtz_locks;
CREATE TABLE qrtz_locks(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `lock_name` varchar(40) NOT NULL   COMMENT '悲观锁名称' ,
    PRIMARY KEY (sched_name,lock_name)
)  COMMENT = '存储的悲观锁信息表';


CREATE UNIQUE INDEX qrtz_locks_pkey ON qrtz_locks(sched_name,lock_name);

DROP TABLE IF EXISTS qrtz_paused_trigger_grps;
CREATE TABLE qrtz_paused_trigger_grps(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_group` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_group的外键' ,
    PRIMARY KEY (sched_name,trigger_group)
)  COMMENT = '暂停的触发器表';


CREATE UNIQUE INDEX qrtz_paused_trigger_grps_pkey ON qrtz_paused_trigger_grps(sched_name,trigger_group);

DROP TABLE IF EXISTS qrtz_scheduler_state;
CREATE TABLE qrtz_scheduler_state(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `instance_name` varchar(200) NOT NULL   COMMENT '实例名称' ,
    `last_checkin_time` int8(19) NOT NULL   COMMENT '上次检查时间' ,
    `checkin_interval` int8(19) NOT NULL   COMMENT '检查间隔时间' ,
    PRIMARY KEY (sched_name,instance_name)
)  COMMENT = '调度器状态表';


CREATE UNIQUE INDEX qrtz_scheduler_state_pkey ON qrtz_scheduler_state(sched_name,instance_name);

DROP TABLE IF EXISTS qrtz_simple_triggers;
CREATE TABLE qrtz_simple_triggers(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_name` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_name的外键' ,
    `trigger_group` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_group的外键' ,
    `repeat_count` int8(19) NOT NULL   COMMENT '重复的次数统计' ,
    `repeat_interval` int8(19) NOT NULL   COMMENT '重复的间隔时间' ,
    `times_triggered` int8(19) NOT NULL   COMMENT '已经触发的次数' ,
    PRIMARY KEY (sched_name,trigger_name,trigger_group)
)  COMMENT = '简单触发器的信息表';


CREATE UNIQUE INDEX qrtz_simple_triggers_pkey ON qrtz_simple_triggers(sched_name,trigger_name,trigger_group);

DROP TABLE IF EXISTS qrtz_simprop_triggers;
CREATE TABLE qrtz_simprop_triggers(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_name` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_name的外键' ,
    `trigger_group` varchar(200) NOT NULL   COMMENT 'qrtz_triggers表trigger_group的外键' ,
    `str_prop_1` varchar(512)    COMMENT 'String类型的trigger的第一个参数' ,
    `str_prop_2` varchar(512)    COMMENT 'String类型的trigger的第二个参数' ,
    `str_prop_3` varchar(512)    COMMENT 'String类型的trigger的第三个参数' ,
    `int_prop_1` int4    COMMENT 'int类型的trigger的第一个参数' ,
    `int_prop_2` int4    COMMENT 'int类型的trigger的第二个参数' ,
    `long_prop_1` int8(19)    COMMENT 'long类型的trigger的第一个参数' ,
    `long_prop_2` int8(19)    COMMENT 'long类型的trigger的第二个参数' ,
    `dec_prop_1` numeric(13,4)    COMMENT 'decimal类型的trigger的第一个参数' ,
    `dec_prop_2` numeric(13,4)    COMMENT 'decimal类型的trigger的第二个参数' ,
    `bool_prop_1` bool(1)    COMMENT 'Boolean类型的trigger的第一个参数' ,
    `bool_prop_2` bool(1)    COMMENT 'Boolean类型的trigger的第二个参数' ,
    PRIMARY KEY (sched_name,trigger_name,trigger_group)
)  COMMENT = '同步机制的行锁表';


CREATE UNIQUE INDEX qrtz_simprop_triggers_pkey ON qrtz_simprop_triggers(sched_name,trigger_name,trigger_group);

DROP TABLE IF EXISTS qrtz_triggers;
CREATE TABLE qrtz_triggers(
    `sched_name` varchar(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_name` varchar(200) NOT NULL   COMMENT '触发器的名字' ,
    `trigger_group` varchar(200) NOT NULL   COMMENT '触发器所属组的名字' ,
    `job_name` varchar(200) NOT NULL   COMMENT 'qrtz_job_details表job_name的外键' ,
    `job_group` varchar(200) NOT NULL   COMMENT 'qrtz_job_details表job_group的外键' ,
    `description` varchar(250)    COMMENT '相关介绍' ,
    `next_fire_time` int8(19)    COMMENT '上一次触发时间（毫秒）' ,
    `prev_fire_time` int8(19)    COMMENT '下一次触发时间（默认为-1表示不触发）' ,
    `priority` int4    COMMENT '优先级' ,
    `trigger_state` varchar(16) NOT NULL   COMMENT '触发器状态' ,
    `trigger_type` varchar(8) NOT NULL   COMMENT '触发器的类型' ,
    `start_time` int8(19) NOT NULL   COMMENT '开始时间' ,
    `end_time` int8(19)    COMMENT '结束时间' ,
    `calendar_name` varchar(200)    COMMENT '日程表名称' ,
    `misfire_instr` int2(5)    COMMENT '补偿执行的策略' ,
    `job_data` bytea(2147483647)    COMMENT '存放持久化job对象' ,
    PRIMARY KEY (sched_name,trigger_name,trigger_group)
)  COMMENT = '触发器详细信息表';


CREATE UNIQUE INDEX qrtz_triggers_pkey ON qrtz_triggers(sched_name,trigger_name,trigger_group);

DROP TABLE IF EXISTS so_ss_average_demand;
CREATE TABLE so_ss_average_demand(
    `stock_point_id` VARCHAR(32)    COMMENT '库存点编码' ,
    `stock_point_name` varchar(64)    COMMENT '库存点名称' ,
    `item_id` VARCHAR(32)    COMMENT '物品编码' ,
    `item_name` varchar(64)    COMMENT '物品名称' ,
    `average_qty` numeric(53)    COMMENT '日均量 默认0' ,
    `remark` varchar(64)    COMMENT '备注' ,
    `status` int8(19)    COMMENT '状态' 
)  COMMENT = '日均需求';

DROP TABLE IF EXISTS so_ss_service_level;
CREATE TABLE so_ss_service_level(
    `stock_point_id` VARCHAR(32)    COMMENT '库存点编码' ,
    `stock_point_name` varchar(64)    COMMENT '库存点名称' ,
    `item_id` VARCHAR(32)    COMMENT '物品编码' ,
    `item_name` varchar(64)    COMMENT '物品名称' ,
    `service_level` numeric    COMMENT '服务水平 默认0.95' ,
    `remark` varchar(64)    COMMENT '备注' ,
    `status` int8(19)    COMMENT '状态' 
)  COMMENT = '服务水平';

DROP TABLE IF EXISTS so_wt_demand;
CREATE TABLE so_wt_demand(
    `item_id` varchar(64) NOT NULL   COMMENT '产品编码' ,
    `item_name` varchar(64)    COMMENT '产品简称' ,
    `stock_point_id` varchar(64) NOT NULL   COMMENT '库存点编码' ,
    `stock_point_name` varchar(64)    COMMENT '库存点简称' ,
    `expiry_limit` int8(19) NOT NULL   COMMENT '效期限制，保质期在多少天内，不填写默认无效期限制' ,
    `expected_delivery_date` date NOT NULL   COMMENT '期望交期' ,
    `qty` numeric   DEFAULT 0 COMMENT '量，不填写默认为0' ,
    `remark` varchar(64)    COMMENT '备注' ,
    `status` int8(19) NOT NULL  DEFAULT 1 COMMENT '状态  1.有效  0.无效' ,
    `demand_plan_code` int8(19)    COMMENT '需求计划编号' ,
    `demand_plan_name` varchar(64)    COMMENT '需求计划名称' ,
    `version_id` VARCHAR(32)    COMMENT '版本编号' 
)  COMMENT = '调拨算法输入-需求';

DROP TABLE IF EXISTS so_wt_safety_stock;
CREATE TABLE so_wt_safety_stock(
    `stock_point_id` VARCHAR(32)    COMMENT '库存点编码' ,
    `stock_point_name` varchar(64)    COMMENT '库存点名称' ,
    `item_id` VARCHAR(32)    COMMENT '物品编码' ,
    `item_name` varchar(64)    COMMENT '物品名称' ,
    `safety_stock` numeric(53)    COMMENT '安全库存' ,
    `target_stock` numeric(53)    COMMENT '目标库存' ,
    `remark` varchar(64)    COMMENT '备注' ,
    `status` int8(19)    COMMENT '状态' 
)  COMMENT = '安全库存';

DROP TABLE IF EXISTS so_wt_stock_capacity;
CREATE TABLE so_wt_stock_capacity(
    `rule_id` int8(19)    COMMENT '规则id' ,
    `stock_point_id` VARCHAR(32)    COMMENT '库存点编码' ,
    `stock_point_name` varchar(64)    COMMENT '库存点名称' ,
    `item_id` VARCHAR(32)    COMMENT '物品编码 不填写表示全部物品' ,
    `item_name` varchar(64)    COMMENT '物品名称' ,
    `group_id` VARCHAR(32)    COMMENT '分组编码' ,
    `type` int8(19)    COMMENT '1-库存容量；2-出库能力；3-入库能力' ,
    `capacity` numeric(53)    COMMENT '量' ,
    `remark` varchar(64)    COMMENT '备注' ,
    `status` int8(19)   DEFAULT 1 COMMENT '状态' 
)  COMMENT = '库存能力';

DROP TABLE IF EXISTS t_ryytn_account;
CREATE TABLE t_ryytn_account(
    `id` int8(19) NOT NULL   COMMENT '编号' ,
    `name` varchar(64)    COMMENT '账号名称，OA同步lastName可能为空，此处默认为空，业务代码校验本地账号不能为空' ,
    `nick_name` varchar(64)    COMMENT '账号昵称' ,
    `work_code` VARCHAR(32)    COMMENT '工号' ,
    `login_id` VARCHAR(32)    COMMENT '登录账号，本地账号必填，OA账号对应loginId，可能为空，此处默认为空，业务代码校验本地账号不能为空' ,
    `password` varchar(64)    COMMENT '密码，本地账号必填，此处默认为空，业务代码校验本地账号不能为空' ,
    `oa_id` VARCHAR(32)    COMMENT 'OA人员编号' ,
    `status` int4   DEFAULT 1 COMMENT '状态，1：正常，2：停用' ,
    `description` varchar(256)    COMMENT '描述' ,
    `data_type` int4   DEFAULT 2 COMMENT '数据类型，1:表示初始化数据不允许删除；2:表示管理端创建数据；3:OA系统同步，默认为2' ,
    `created_time` timestamp   DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_time` timestamp    COMMENT '修改时间' ,
    `department_id` VARCHAR(32)    COMMENT '部门编号' ,
    PRIMARY KEY (id)
)  COMMENT = '账号信息表，包含预置管理账号、本地管理账号和OA同步人员';


CREATE UNIQUE INDEX t_ryytn_account_pkey ON t_ryytn_account(id);

DROP TABLE IF EXISTS t_ryytn_account_role;
CREATE TABLE t_ryytn_account_role(
    `account_id` int8(19) NOT NULL   COMMENT '账号编号' ,
    `role_id` int8(19) NOT NULL   COMMENT '角色编号' 
)  COMMENT = '账号角色关联关系表';


CREATE UNIQUE INDEX uk_t_ryytn_account_role ON t_ryytn_account_role(account_id,role_id);

DROP TABLE IF EXISTS t_ryytn_adjustable_days_rule;
CREATE TABLE t_ryytn_adjustable_days_rule(
    `id` int8(19) NOT NULL   COMMENT '主键id' ,
    `name` varchar(200)    COMMENT '规则名称' ,
    `range_type` int2(5)    COMMENT '范围类型(0:产品，1:品类，2：全部)' ,
    `start_time` date    COMMENT '生效时间' ,
    `end_time` date    COMMENT '结束时间' ,
    `forever_flag` int2(5)    COMMENT '规则是否永久生效 0:否，1：是' ,
    `created_by` varchar(64)    COMMENT '创建人' ,
    `created_time` timestamp   DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '更新人' ,
    `updated_time` timestamp    COMMENT '更新时间' ,
    `is_default` int2(5)   DEFAULT 1 COMMENT '0为预置数据 1为手动添加' ,
    PRIMARY KEY (id)
)  COMMENT = '可调天数规则表';


CREATE UNIQUE INDEX t_ryytn_adjustable_days_rule_pk ON t_ryytn_adjustable_days_rule(id);

DROP TABLE IF EXISTS t_ryytn_adjustable_days_rule_range;
CREATE TABLE t_ryytn_adjustable_days_rule_range(
    `id` int8(19) NOT NULL   COMMENT '主键id' ,
    `rule_id` int8(19)    COMMENT '规则id' ,
    `warehouse_code` VARCHAR(32)    COMMENT '库存地点code' ,
    `warehouse_name` varchar(64)    COMMENT '库存地点名称' ,
    `adjustable_days` int4    COMMENT '可调天数' ,
    PRIMARY KEY (id)
)  COMMENT = '可调天数规则范围';


CREATE UNIQUE INDEX 可调天数规则子表_pk ON t_ryytn_adjustable_days_rule_range(id);

DROP TABLE IF EXISTS t_ryytn_adjustable_days_rule_range_category;
CREATE TABLE t_ryytn_adjustable_days_rule_range_category(
    `id` int8(19) NOT NULL   COMMENT '主键id' ,
    `rule_id` int8(19)    COMMENT '规则id' ,
    `category_code` VARCHAR(32)    COMMENT '品类编码' ,
    `category_name` varchar(64)    COMMENT '品类名称' ,
    `level` int2(5)    COMMENT '品类等级' ,
    `sku_codes` text(2147483647)    COMMENT '产品编码集合' ,
    PRIMARY KEY (id)
)  COMMENT = '可调天数规则品类';


CREATE UNIQUE INDEX t_ryytn_adjustable_days_rule_range_category_pk ON t_ryytn_adjustable_days_rule_range_category(id);

DROP TABLE IF EXISTS t_ryytn_adjustable_days_rule_range_product;
CREATE TABLE t_ryytn_adjustable_days_rule_range_product(
    `id` int8(19) NOT NULL   COMMENT '主键id' ,
    `rule_id` int8(19)    COMMENT '规则id' ,
    `sku_code` VARCHAR(32)    COMMENT '产品编码' ,
    `sku_name` varchar(64)    COMMENT '产品简称' ,
    PRIMARY KEY (id)
)  COMMENT = '可调天数规则产品';


CREATE UNIQUE INDEX t_ryytn_adjustable_days_rule_range_product_pk ON t_ryytn_adjustable_days_rule_range_product(id);

DROP TABLE IF EXISTS t_ryytn_algo_scheduling_record;
CREATE TABLE t_ryytn_algo_scheduling_record(
    `id` int8(19) NOT NULL   COMMENT '主键' ,
    `algo_name_and_version` varchar(64)    COMMENT '算法名称和版本' ,
    `start_time` timestamp    COMMENT '开始时间' ,
    `end_time` timestamp    COMMENT '结束时间' ,
    `running_status` int4    COMMENT '运行状态 0.INIT,
;    1.RUNNING,
    2.STOPPING,
    3.STOPPED,
    4.SKIP,
    5.SUCCESS,
    6.FAILED' ,
    `scene` int2(5)    COMMENT '算法类型 0：渠道 1：分仓 2：库存 3：调拨' ,
    PRIMARY KEY (id)
)  COMMENT = '算法任务调用记录';


CREATE UNIQUE INDEX t_ryytn_aiplan_algo_pk ON t_ryytn_algo_scheduling_record(id);

DROP TABLE IF EXISTS t_ryytn_button;
CREATE TABLE t_ryytn_button(
    `id` int8(19) NOT NULL   COMMENT '按钮编号' ,
    `name` varchar(64) NOT NULL   COMMENT '按钮名称' ,
    `alias` varchar(64)    COMMENT '按钮别名' ,
    `permission` varchar(256)    COMMENT '按钮权限码，用于后端校验权限，英文逗号分隔' ,
    `dependency_ids` varchar(1024)    COMMENT '按钮依赖编号，英文逗号分隔' ,
    `page_id` int8(19) NOT NULL   COMMENT '所属页面编号' ,
    `sort_no` int4    COMMENT '页面排序' ,
    PRIMARY KEY (id)
)  COMMENT = '按钮表';


CREATE UNIQUE INDEX t_ryytn_button_pkey ON t_ryytn_button(id);

DROP TABLE IF EXISTS t_ryytn_channel_demand_plan_data_sync;
CREATE TABLE t_ryytn_channel_demand_plan_data_sync(
    `id` serial NOT NULL  DEFAULT nextval('cdop_sys.t_ryytn_channel_demand_plan_data_sync_id_seq' COMMENT '编号' ,
    `demand_plan_code` VARCHAR(32) NOT NULL   COMMENT '计划编号' ,
    `version_id` VARCHAR(32) NOT NULL   COMMENT '滚动版本号' ,
    `version_name` varchar(64)    COMMENT '版本名称，格式：计划编号-滚动版本号' ,
    `lv2_channel_code` varchar(64)    COMMENT '二级渠道编号' ,
    `sku_code` VARCHAR(255)    COMMENT 'SKU产品编号' ,
    `plan_date` varchar(64) NOT NULL   COMMENT '时间，格式yyyyMMdd，二级渠道为生产计划部时放周的第一天，其他二级渠道放周的最后一天' ,
    `plan_value` numeric(10) NOT NULL   COMMENT '需求数据' ,
    `month_week` VARCHAR(32)    COMMENT '月份周数，格式MMWn' ,
    PRIMARY KEY (id)
)  COMMENT = '渠道需求计划数据同步中间表';


CREATE UNIQUE INDEX t_ryytn_channel_demand_plan_data_sync_pkey ON t_ryytn_channel_demand_plan_data_sync(id);
CREATE UNIQUE INDEX t_ryytn_channel_demand_plan_data_sync_demand_plan_code_idx ON t_ryytn_channel_demand_plan_data_sync(demand_plan_code);

DROP TABLE IF EXISTS t_ryytn_channel_demand_report_version;
CREATE TABLE t_ryytn_channel_demand_report_version(
    `id` int8(19) NOT NULL   COMMENT '唯一编号' ,
    `rolling_version` VARCHAR(32) NOT NULL   COMMENT '版本号' ,
    `is_locked` int2(5)   DEFAULT 0 COMMENT '是否锁定，0：未锁定，1：已锁定' ,
    `creator` VARCHAR(32)   DEFAULT 'NULL::character varying' COMMENT '创建人' ,
    `last_modifier` VARCHAR(32)   DEFAULT 'NULL::character varying' COMMENT '修改人' ,
    `gmt_create` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `gmt_modify` timestamp NOT NULL  DEFAULT 'now()' COMMENT '修改时间' ,
	`lv3_channel_codes` text NULL  COMMENT '已编辑的三级渠道编号' ,
    PRIMARY KEY (id)
)  COMMENT = '渠道需求提报版本';


CREATE UNIQUE INDEX t_ryytn_channel_demand_report_version_pkey ON t_ryytn_channel_demand_report_version(id);

DROP TABLE IF EXISTS t_ryytn_cold_demand_report;
CREATE TABLE t_ryytn_cold_demand_report(
    `id` int8(19) NOT NULL   COMMENT '编号' ,
    `rolling_version` VARCHAR(32) NOT NULL   COMMENT '版本号，DDP+提报起始年份+提报起始月份+下周周一所属周数' ,
    `sku_code` VARCHAR(255)    COMMENT 'skuCode' ,
    `sku_name` VARCHAR(255)    COMMENT 'sku名称' ,
    `lv1_category_code` varchar(64)    COMMENT '产品分类编码' ,
    `lv1_category_name` varchar(64)    COMMENT '产品分类名称' ,
    `lv2_category_code` varchar(64)    COMMENT '产品大类编码' ,
    `lv2_category_name` varchar(64)    COMMENT '产品大类名称' ,
    `lv3_category_code` varchar(64)    COMMENT '产品小类编码' ,
    `lv3_category_name` varchar(64)    COMMENT '产品小类名称' ,
    `lv1_channel_code` varchar(64)    COMMENT '一级渠道类型编码' ,
    `lv1_channel_name` varchar(64)    COMMENT '一级渠道类型名称' ,
    `lv2_channel_code` varchar(64)    COMMENT '二级渠道类型编码' ,
    `lv2_channel_name` varchar(64)    COMMENT '二级渠道类型名称' ,
    `lv3_channel_code` varchar(64)    COMMENT '三级渠道类型编码' ,
    `lv3_channel_name` varchar(64)    COMMENT '三级渠道类型名称' ,
    `receiver_type` varchar(64)    COMMENT '渠道类型' ,
    `biz_date_type` VARCHAR(32) NOT NULL   COMMENT '时间类型:DAY,WEEK，MONTH,YEAR' ,
    `biz_date_value` varchar(64) NOT NULL   COMMENT '时间类型值,日：20230101;周:0230103' ,
    `order_num` numeric(10) NOT NULL   COMMENT '订单数量/订单金额' ,
    `unit` varchar(3)    COMMENT '计量单位:件/瓶/吨ml/元' ,
    `last_order_num` numeric(10)   DEFAULT NULL::numeric COMMENT '上一版本订单数量/订单金额' ,
    `deviation_radio` numeric(10,2)   DEFAULT NULL::numeric COMMENT '需求提报二级渠道数据偏差率' ,
    `remark` varchar(256)    COMMENT '备注' ,
    `extend` varchar(1024)    COMMENT '扩展字段' ,
    `is_modify` int2(5)    COMMENT '是否调整：0为否;1为是，默认0' ,
    `is_delete` int2(5)   DEFAULT 0 COMMENT '是否删除：0为否;1为是，默认0' ,
    `creator` VARCHAR(32)    COMMENT '创建人' ,
    `last_modifier` VARCHAR(32)    COMMENT '最后修改人' ,
    `gmt_create` timestamp    COMMENT '创建时间' ,
    `gmt_modify` timestamp    COMMENT '修改时间' ,
    PRIMARY KEY (id)
)  COMMENT = '低温需求提报数据表';


CREATE UNIQUE INDEX t_ryytn_cold_demand_report_pkey ON t_ryytn_cold_demand_report(id);
CREATE UNIQUE INDEX t_ryytn_cold_demand_report_service_column ON t_ryytn_cold_demand_report(rolling_version,sku_code,lv1_category_code,lv2_category_code,lv3_category_code,lv1_channel_code,lv2_channel_code,lv3_channel_code,biz_date_value);

DROP TABLE IF EXISTS t_ryytn_cold_demand_report_history;
CREATE TABLE t_ryytn_cold_demand_report_history(
    `id` bigserial(19) NOT NULL  DEFAULT nextval('cdop_sys.t_ryytn_cold_demand_report_history_id_seq' COMMENT '编号' ,
    `rolling_version` VARCHAR(32) NOT NULL   COMMENT '版本号，DDP+提报起始年份+提报起始月份+下周周一所属周数' ,
    `sku_code` VARCHAR(255)    COMMENT 'skuCode' ,
    `sku_name` VARCHAR(255)    COMMENT 'sku名称' ,
    `lv1_category_code` varchar(64)    COMMENT '产品分类编码' ,
    `lv1_category_name` varchar(64)    COMMENT '产品分类名称' ,
    `lv2_category_code` varchar(64)    COMMENT '产品大类编码' ,
    `lv2_category_name` varchar(64)    COMMENT '产品大类名称' ,
    `lv3_category_code` varchar(64)    COMMENT '产品小类编码' ,
    `lv3_category_name` varchar(64)    COMMENT '产品小类名称' ,
    `lv1_channel_code` varchar(64)    COMMENT '一级渠道类型编码' ,
    `lv1_channel_name` varchar(64)    COMMENT '一级渠道类型名称' ,
    `lv2_channel_code` varchar(64)    COMMENT '二级渠道类型编码' ,
    `lv2_channel_name` varchar(64)    COMMENT '二级渠道类型名称' ,
    `lv3_channel_code` varchar(64)    COMMENT '三级渠道类型编码' ,
    `lv3_channel_name` varchar(64)    COMMENT '三级渠道类型名称' ,
    `lv3_channel_type` varchar(64)    COMMENT '三级渠道类型' ,
    `biz_date_type` VARCHAR(32) NOT NULL   COMMENT '时间类型:DAY,WEEK，MONTH,YEAR' ,
    `biz_date_value` varchar(64) NOT NULL   COMMENT '时间类型值,日：20230101;周:0230103' ,
    `order_num` numeric(10,2) NOT NULL   COMMENT '订单数量/订单金额' ,
    `old_order_num` numeric(10,2) NOT NULL   COMMENT '旧订单数量/旧订单金额' ,
    `unit` varchar(3)    COMMENT '计量单位:件/瓶/吨ml/元' ,
    `deviation_radio` numeric(10)   DEFAULT 0 COMMENT '渠道需求提报二级渠道数据偏差率' ,
    `remark` varchar(256)    COMMENT '备注' ,
    `extend` varchar(1024)    COMMENT '扩展字段' ,
    `last_modifier` VARCHAR(32)    COMMENT '最后修改人' ,
    `gmt_modify` timestamp    COMMENT '修改时间' ,
    PRIMARY KEY (id)
)  COMMENT = '低温需求提报数据历史记录表';


CREATE UNIQUE INDEX t_ryytn_cold_demand_report_history_pkey ON t_ryytn_cold_demand_report_history(id);

DROP TABLE IF EXISTS t_ryytn_cold_demand_report_version;
CREATE TABLE t_ryytn_cold_demand_report_version(
    `id` int8(19) NOT NULL   COMMENT '编号' ,
    `rolling_version` VARCHAR(32) NOT NULL   COMMENT '版本编号' ,
    `creator` VARCHAR(32)   DEFAULT 'NULL::character varying' COMMENT '创建人' ,
    `last_modifier` VARCHAR(32)   DEFAULT 'NULL::character varying' COMMENT '修改人' ,
    `gmt_create` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `gmt_modify` timestamp NOT NULL  DEFAULT 'now()' COMMENT '修改时间' ,
    PRIMARY KEY (id)
)  COMMENT = '低温提报版本';


CREATE UNIQUE INDEX t_ryytn_warehouse_demand_report_version_pkey ON t_ryytn_cold_demand_report_version(id);

DROP TABLE IF EXISTS t_ryytn_config;
CREATE TABLE t_ryytn_config(
    `category_id` VARCHAR(32) NOT NULL   COMMENT '所属类别' ,
    `config_id` varchar(64) NOT NULL   COMMENT '配置编码' ,
    `config_name` varchar(64)    COMMENT '配置名称' ,
    `config_type` int4    COMMENT '配置值类型（1：密码类型；2：文本类型；3：文件类型；4：枚举类型；5：数字文本；6：文本域）' ,
    `config_value` varchar(512)    COMMENT '配置值' ,
    `status` int4   DEFAULT 1 COMMENT '配置状态，1：正常，2：停用' ,
    `is_display` int4    COMMENT '0:不展示, 1:展示' ,
    `validator` varchar(256)    COMMENT '校验规则' ,
    `description` varchar(256)    COMMENT '配置描述' ,
    `sort_no` int4    COMMENT '排序字段' ,
    `created_by` varchar(64)   DEFAULT 'admin' COMMENT '创建人' ,
    `created_time` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '更新人' ,
    `updated_time` timestamp    COMMENT '更新时间' ,
    PRIMARY KEY (config_id)
)  COMMENT = '配置表';


CREATE UNIQUE INDEX t_ryytn_config_pkey ON t_ryytn_config(config_id);

DROP TABLE IF EXISTS t_ryytn_configcategory;
CREATE TABLE t_ryytn_configcategory(
    `category_id` VARCHAR(32) NOT NULL   COMMENT '规则编号，业务唯一约束' ,
    `parent_id` int8(19)    COMMENT '父类别编号' ,
    `parent_ids` int8(19)    COMMENT '所有有父类别编号' ,
    `name` varchar(50)    COMMENT '类别名称' ,
    `status` int4   DEFAULT 1 COMMENT '配置状态，1：正常，2：停用' ,
    `sort_no` int4    COMMENT '类别序号' ,
    PRIMARY KEY (category_id)
)  COMMENT = '配置类别表';


CREATE UNIQUE INDEX t_ryytn_configcategory_pkey ON t_ryytn_configcategory(category_id);

DROP TABLE IF EXISTS t_ryytn_daily_warehouse_aiplan_demand;
CREATE TABLE t_ryytn_daily_warehouse_aiplan_demand(
    `id` varchar(64) NOT NULL   COMMENT '' ,
    `demand_plan_code` varchar(64)    COMMENT '需求计划编码' ,
    `demand_plan_name` varchar(64)    COMMENT '需求计划名称' ,
    `demand_plan_version` varchar(64)    COMMENT '需求计划版本' ,
    `aiplan_demand_version` varchar(64)    COMMENT '调拨需求版本' ,
    `sku_code` varchar(64)    COMMENT '产品编码' ,
    `sku_name` varchar(64)    COMMENT '产品简称' ,
    `distribute_type` int2(5)    COMMENT '渠道类型 0：ToB，1：ToC' ,
    `warehouse_code` varchar(64)    COMMENT '仓库编码' ,
    `warehouse_name` varchar(64)    COMMENT '仓库名称' ,
    `lv1_category_code` varchar(64)    COMMENT '产品分类编码' ,
    `lv1_category_name` varchar(64)    COMMENT '产品分类名称' ,
    `lv2_category_code` varchar(64)    COMMENT '产品大类编码' ,
    `lv2_category_name` varchar(64)    COMMENT '产品大类名称' ,
    `lv3_category_code` varchar(64)    COMMENT '产品小类编码' ,
    `lv3_category_name` varchar(64)    COMMENT '产品小类名称' ,
    `validity_period` varchar(64)    COMMENT '效期名称' ,
    `date_recorded` varchar(64)    COMMENT '日期' ,
    `date_value` numeric    COMMENT '数量' ,
	`status` int4 default 1    COMMENT '数据状态：1：正常，2：生成中',
    PRIMARY KEY (id)
)  COMMENT = '日分仓调拨需求';


CREATE UNIQUE INDEX t_ryytn_daily_warehouse_aiplan_demand_pk ON t_ryytn_daily_warehouse_aiplan_demand(id);
CREATE UNIQUE INDEX t_ryytn_daily_warehouse_aiplan_demand_demand_plan_code_idx ON t_ryytn_daily_warehouse_aiplan_demand(demand_plan_code,demand_plan_version,aiplan_demand_version);

DROP TABLE IF EXISTS t_ryytn_daily_warehouse_demand;
CREATE TABLE t_ryytn_daily_warehouse_demand(
    `id` varchar(2147483647) NOT NULL   COMMENT '' ,
    `demand_plan_code` varchar(64)    COMMENT '需求计划编码' ,
    `demand_plan_name` varchar(64)    COMMENT '需求计划名称' ,
    `demand_plan_version` varchar(64)    COMMENT '需求计划版本' ,
    `sku_code` varchar(64)    COMMENT '产品编码' ,
    `sku_name` varchar(64)    COMMENT '产品名称' ,
    `distribute_type` int2(5)    COMMENT '渠道类型 0：ToB，1：ToC' ,
    `warehouse_code` varchar(64)    COMMENT '仓库编码' ,
    `warehouse_name` varchar(64)    COMMENT '仓库名称' ,
    `lv1_category_code` varchar(64)    COMMENT '产品分类编码' ,
    `lv1_category_name` varchar(64)    COMMENT '产品分类名称' ,
    `lv2_category_code` varchar(64)    COMMENT '产品大类编码' ,
    `lv2_category_name` varchar(64)    COMMENT '产品大类名称' ,
    `lv3_category_code` varchar(64)    COMMENT '产品小类编码' ,
    `lv3_category_name` varchar(64)    COMMENT '产品小类名称' ,
    `date_recorded` varchar(64)    COMMENT '日期' ,
    `date_value` numeric    COMMENT '日数量' ,
    `week_recorded` varchar(64)    COMMENT '周日期' ,
    `week_raw_value` numeric    COMMENT '周原始数量' ,
    `week_actual_value` numeric    COMMENT '周实际数量' ,
	`status` int4 default 1    COMMENT '数据状态：1：正常，2：生成中',
    PRIMARY KEY (id)
)  COMMENT = '日分仓需求';


CREATE UNIQUE INDEX t_ryytn_daily_warehouse_demand_pk ON t_ryytn_daily_warehouse_demand(id);
CREATE UNIQUE INDEX t_ryytn_daily_warehouse_demand_demand_plan_code_idx ON t_ryytn_daily_warehouse_demand(demand_plan_code,demand_plan_version);

DROP TABLE IF EXISTS t_ryytn_dataq_task;
CREATE TABLE t_ryytn_dataq_task(
    `id` bigserial(19) NOT NULL  DEFAULT nextval('cdop_sys.t_ryytn_dataq_task_id_seq' COMMENT '自增主键' ,
    `task_id` int8(19)    COMMENT '阿里dataq任务实例编号' ,
    `job_id` int8(19)    COMMENT '业务应用任务编号' ,
    `app_code` varchar(64)   DEFAULT 'NULL::character varying' COMMENT '阿里dataq任务应用编号' ,
    `param` text(2147483647)    COMMENT '阿里dataq任务请求参数' ,
    `lock_key` varchar(128)   DEFAULT 'NULL::character varying' COMMENT '阿里dataq任务锁的key' ,
    `status` VARCHAR(32)   DEFAULT 'NULL::character varying' COMMENT '阿里dataq任务状态，INIT：初始化，RUNNING：执行中，STOPPING：停止中，STOPPED：已停止，SKIP：跳过，SUCCESS：成功，FAILED：失败' ,
    `reload_id` int8(19)    COMMENT '补偿机制重发请求的阿里dataq任务实例编号' ,
    `start_time` timestamp   DEFAULT 'now()' COMMENT '开始时间' ,
    `end_time` timestamp    COMMENT '完成时间' ,
    PRIMARY KEY (id)
)  COMMENT = '阿里dataq任务执行记录表';


CREATE UNIQUE INDEX t_ryytn_dataq_task_pkey ON t_ryytn_dataq_task(id);

DROP TABLE IF EXISTS t_ryytn_dict_data;
CREATE TABLE t_ryytn_dict_data(
    `dict_id` serial NOT NULL  DEFAULT nextval('cdop_sys.t_ryytn_dict_data_dict_id_seq' COMMENT '字典编号' ,
    `dict_type` varchar(64) NOT NULL   COMMENT '字典类型' ,
    `name` varchar(64) NOT NULL   COMMENT '字典名称' ,
    `code` varchar(64) NOT NULL   COMMENT '字典编码' ,
    `parent_id` varchar(64) NOT NULL   COMMENT '父字典编号' ,
    `parent_ids` varchar(2048) NOT NULL   COMMENT '所有父字典编号' ,
    `level` int4 NOT NULL   COMMENT '字典层级' ,
    `leaf_flag` int2(5) NOT NULL  DEFAULT 1::smallint COMMENT '是否叶子，0：否，1：是，首次新增默认为1' ,
    `css_class` varchar(64)    COMMENT '样式属性（其他样式扩展）' ,
    `list_class` varchar(64)    COMMENT '表格回显样式' ,
    `item_check` int4   DEFAULT 0 COMMENT '是否默认选中，1：是，0：否，默认为0' ,
    `sort_no` int4    COMMENT '排序' ,
    `status` int4   DEFAULT 1 COMMENT '状态，1：正常，2：停用，默认值：1' ,
    `delete_flag` int2(5)   DEFAULT 0::smallint COMMENT '删除状态，0：未删除，1：已删除，默认0' ,
    `description` varchar(256)    COMMENT '描述' ,
    `data_type` int4 NOT NULL  DEFAULT 2 COMMENT '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2' ,
    `created_by` varchar(64)    COMMENT '创建人(登录账号)' ,
    `created_time` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '修改人(登录账号)' ,
    `updated_time` timestamp    COMMENT '修改时间' ,
    PRIMARY KEY (dict_id)
)  COMMENT = '字典数据表';


CREATE UNIQUE INDEX t_ryytn_dict_data_pkey ON t_ryytn_dict_data(dict_id);

DROP TABLE IF EXISTS t_ryytn_dict_type;
CREATE TABLE t_ryytn_dict_type(
    `dict_type_id` serial NOT NULL  DEFAULT nextval('cdop_sys.t_ryytn_dict_type_dict_type_id_seq' COMMENT '字典类型编号' ,
    `dict_type` varchar(64) NOT NULL   COMMENT '字典类型' ,
    `dict_name` varchar(64) NOT NULL   COMMENT '字典名称' ,
    `status` int4   DEFAULT 1 COMMENT '状态，1：正常，2：停用，默认值：1' ,
    `delete_flag` int2(5)   DEFAULT 0::smallint COMMENT '删除状态，0：未删除，1：已删除，默认0' ,
    `description` varchar(256)    COMMENT '描述' ,
    `data_type` int4 NOT NULL  DEFAULT 2 COMMENT '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为1' ,
    `created_by` varchar(64)    COMMENT '创建人(登录账号)' ,
    `created_time` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '修改人(登录账号)' ,
    `updated_time` timestamp    COMMENT '修改时间' ,
    PRIMARY KEY (dict_type_id)
)  COMMENT = '字典类型表';


CREATE UNIQUE INDEX t_ryytn_dict_type_pkey ON t_ryytn_dict_type(dict_type_id);

DROP TABLE IF EXISTS t_ryytn_distribute_plan_inventory_strategy;
CREATE TABLE t_ryytn_distribute_plan_inventory_strategy(
    `id` int8(19) NOT NULL   COMMENT '主键ID' ,
    `warehouse_code` VARCHAR(32)    COMMENT '仓库编码' ,
    `warehouse_name` VARCHAR(32)    COMMENT '仓库名称' ,
    `warehouse_type` VARCHAR(32)    COMMENT '仓库类别' ,
    `sku_code` VARCHAR(32)    COMMENT '产品编码' ,
    `sku_name` varchar(64)    COMMENT '产品名称' ,
    `sku_name_simple` varchar(64)    COMMENT '产品简称' ,
    `inventory_safe_day` int4    COMMENT '安全库存天数' ,
    `inventory_safe_amount` int4    COMMENT '安全库存数量' ,
    `inventory_safe_amount_adv` int4    COMMENT '建议安全库存数量' ,
    `inventory_turnover_day` int4    COMMENT '周转库存天数' ,
    `inventory_turnover_amount` int4    COMMENT '周转库存数量' ,
    `inventory_target_day_adv` int4    COMMENT '建议目标库存天数' ,
    `inventory_target_amount_adv` int4    COMMENT '建议目标库存数量' ,
    `inventory_target_day` int4    COMMENT '目标库存天数' ,
    `inventory_target_amount` int4    COMMENT '目标库存数量' ,
    `sales_daily` int4    COMMENT '日均销量' ,
    `special_strategy_flag` int2(5)    COMMENT '特殊策略开启标识' ,
    `inventory_safe_day_spec` int4    COMMENT '特殊安全库存天数' ,
    `inventory_turnover_day_spec` int4    COMMENT '特殊周转库存天数' ,
    `start_time_strategy` timestamp    COMMENT '策略生效时间起' ,
    `end_time_strategy` timestamp    COMMENT '策略生效时间止' ,
    `created_by` varchar(64)    COMMENT '创建人' ,
    `created_time` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '更新人' ,
    `updated_time` timestamp    COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '库存策略数据表';


CREATE UNIQUE INDEX t_ryytn_distribute_plan_inventory_strategy_pkey ON t_ryytn_distribute_plan_inventory_strategy(id);

DROP TABLE IF EXISTS t_ryytn_distribute_plan_inventory_strategy_conf;
CREATE TABLE t_ryytn_distribute_plan_inventory_strategy_conf(
    `id` int8(19) NOT NULL   COMMENT '主键ID' ,
    `config_name` VARCHAR(32)    COMMENT '配置项' ,
    `config_value` VARCHAR(32)    COMMENT '配置值' ,
    `remark` varchar(64)    COMMENT '备注' ,
    `created_by` varchar(64)    COMMENT '创建人' ,
    `created_time` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '更新人' ,
    `updated_time` timestamp    COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '库存策略-配置';


CREATE UNIQUE INDEX t_ryytn_distribute_plan_inventory_strategy_conf_pkey ON t_ryytn_distribute_plan_inventory_strategy_conf(id);

DROP TABLE IF EXISTS t_ryytn_distribute_plan_valid_rule;
CREATE TABLE t_ryytn_distribute_plan_valid_rule(
    `id` int8(19) NOT NULL   COMMENT '主键ID' ,
    `name` varchar(200)    COMMENT '规则名称' ,
    `range_type` int2(5)    COMMENT '范围类型(0:产品，1:品类，2：全部)' ,
    `start_time` date    COMMENT '生效时间' ,
    `end_time` date    COMMENT '失效时间' ,
    `forever_flag` int2(5)    COMMENT '是否永久生效 0:否，1：是' ,
    `distribute_type` int2(5)    COMMENT '0:TOB业务，1:TOC业务' ,
    `created_by` varchar(64)    COMMENT '创建者' ,
    `created_time` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '更新者' ,
    `updated_time` timestamp    COMMENT '更新时间' ,
    `is_default` int2(5)   DEFAULT 1 COMMENT '0为预置数据 1为人工添加' ,
    PRIMARY KEY (id)
)  COMMENT = '效期分档规则-主表';


CREATE UNIQUE INDEX t_ryytn_distribute_plan_valid_rule_pkey ON t_ryytn_distribute_plan_valid_rule(id);

DROP TABLE IF EXISTS t_ryytn_distribute_plan_valid_rule_range;
CREATE TABLE t_ryytn_distribute_plan_valid_rule_range(
    `id` int8(19) NOT NULL   COMMENT '主键ID' ,
    `rule_id` int8(19)    COMMENT '效期分档规则id' ,
    `name` varchar(64)    COMMENT '标签名称' ,
    `start_day` int4    COMMENT '分档起始天数' ,
    `end_day` int4    COMMENT '效期结束天数' ,
    `ratio` float8(17,17)    COMMENT '效期需求占比' 
)  COMMENT = '效期分档子表';

DROP TABLE IF EXISTS t_ryytn_distribute_plan_valid_rule_range_category;
CREATE TABLE t_ryytn_distribute_plan_valid_rule_range_category(
    `id` int8(19) NOT NULL   COMMENT '主键ID' ,
    `rule_id` int8(19)    COMMENT '效期分档规则id' ,
    `category_code` VARCHAR(32)    COMMENT '品类编码' ,
    `category_name` varchar(64)    COMMENT '品类名称' ,
    `level` int2(5)    COMMENT '品类等级' ,
    `sku_codes` text(2147483647)    COMMENT '产品编码集合' 
)  COMMENT = '效期规则品类';

DROP TABLE IF EXISTS t_ryytn_distribute_plan_valid_rule_range_product;
CREATE TABLE t_ryytn_distribute_plan_valid_rule_range_product(
    `id` int8(19) NOT NULL   COMMENT '主键ID' ,
    `rule_id` int8(19)    COMMENT '仓能力规则id' ,
    `sku_code` VARCHAR(32)    COMMENT '产品编码' ,
    `sku_name` varchar(64)    COMMENT '产品名称' 
)  COMMENT = '效期规则产品';

DROP TABLE IF EXISTS t_ryytn_distribute_plan_warehouse_rule;
CREATE TABLE t_ryytn_distribute_plan_warehouse_rule(
    `id` int8(19) NOT NULL   COMMENT '主键ID' ,
    `name` varchar(200)    COMMENT '规则名称' ,
    `range_type` int2(5)    COMMENT '范围类型(0:产品，1:品类，2：全部)' ,
    `start_time` date    COMMENT '生效时间' ,
    `end_time` date    COMMENT '失效时间' ,
    `forever_flag` int2(5)    COMMENT '是否永久生效 0:否，1：是' ,
    `distribute_type` int2(5)    COMMENT '0:TOB业务，1:TOC业务' ,
    `created_by` varchar(64)    COMMENT '创建者' ,
    `created_time` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '更新者' ,
    `updated_time` timestamp    COMMENT '更新时间' ,
    `is_default` int2(5)   DEFAULT 1 COMMENT '0为预置数据 1为人工添加' ,
    PRIMARY KEY (id)
)  COMMENT = '仓能力规则-主表';


CREATE UNIQUE INDEX t_ryytn_distribute_plan_warehouse_rule_pkey ON t_ryytn_distribute_plan_warehouse_rule(id);

DROP TABLE IF EXISTS t_ryytn_distribute_plan_warehouse_rule_capacity;
CREATE TABLE t_ryytn_distribute_plan_warehouse_rule_capacity(
    `id` int8(19) NOT NULL   COMMENT '主键ID' ,
    `rule_id` int8(19)    COMMENT '仓能力规则id' ,
    `warehouse_code` VARCHAR(32)    COMMENT '仓库编码' ,
    `warehouse_name` varchar(64)    COMMENT '仓库名称' ,
    `capacity` int4    COMMENT '库容（提/罐）' ,
    `capacity_flag` int2(5)   DEFAULT 1 COMMENT '是否开启库容' ,
    `delivery_limit` int4    COMMENT '收货能力上限（提/罐）' ,
    `delivery_limit_flag` int2(5)   DEFAULT 1 COMMENT '是否开启收货能力上限' ,
    `delivery_unlimit_flag` int2(5)   DEFAULT 0 COMMENT '是否设置收货无限能力' ,
    `shipment_limit` int4    COMMENT '出库能力上限（提/罐）' ,
    `shipment_limit_flag` int2(5)   DEFAULT 1 COMMENT '是否开启出库能力上限' ,
    `shipment_unlimit_flag` int2(5)   DEFAULT 0 COMMENT '是否设置出库无限能力' ,
    PRIMARY KEY (id)
)  COMMENT = '仓能力子表';


CREATE UNIQUE INDEX t_ryytn_distribute_plan_warehouse_rule_capacity_pkey ON t_ryytn_distribute_plan_warehouse_rule_capacity(id);

DROP TABLE IF EXISTS t_ryytn_distribute_plan_warehouse_rule_capacity_category;
CREATE TABLE t_ryytn_distribute_plan_warehouse_rule_capacity_category(
    `id` int8(19) NOT NULL   COMMENT '主键ID' ,
    `rule_id` int8(19)    COMMENT '仓能力规则id' ,
    `category_code` VARCHAR(32)    COMMENT '品类编码' ,
    `category_name` varchar(64)    COMMENT '品类名称' ,
    `level` int2(5)    COMMENT '品类等级' ,
    `sku_codes` text(2147483647)    COMMENT '产品编码集合' ,
    PRIMARY KEY (id)
)  COMMENT = '仓能力规则品类';


CREATE UNIQUE INDEX t_ryytn_distribute_plan_warehouse_rule_capacity_category_pkey ON t_ryytn_distribute_plan_warehouse_rule_capacity_category(id);

DROP TABLE IF EXISTS t_ryytn_distribute_plan_warehouse_rule_capacity_product;
CREATE TABLE t_ryytn_distribute_plan_warehouse_rule_capacity_product(
    `id` int8(19) NOT NULL   COMMENT '编号' ,
    `rule_id` int8(19)    COMMENT '仓能力规则编号' ,
    `sku_code` VARCHAR(32)    COMMENT '产品编号' ,
    `sku_name` varchar(64)    COMMENT '产品名称' ,
    PRIMARY KEY (id)
)  COMMENT = '仓能力规则产品表';


CREATE UNIQUE INDEX t_ryytn_distribute_plan_warehouse_rule_capacity_product_pkey ON t_ryytn_distribute_plan_warehouse_rule_capacity_product(id);

DROP TABLE IF EXISTS t_ryytn_file;
CREATE TABLE t_ryytn_file(
    `file_id` varchar(256) NOT NULL   COMMENT '文件编号，在文件服务器上存储的唯一编号(路径)' ,
    `file_type` int4 NOT NULL   COMMENT '文件类型，0：图片，1：视频，2：音频，3：文本，4：文档，5：压缩文件，6：脚本文件，99：其他' ,
    `suffix` varchar(8)    COMMENT '文件后缀' ,
    `o_file_name` varchar(1024)    COMMENT '原文件名' ,
    `created_by` varchar(64)    COMMENT '创建人' ,
    `created_time` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    PRIMARY KEY (file_id)
)  COMMENT = '文件信息表';


CREATE UNIQUE INDEX t_ryytn_file_pkey ON t_ryytn_file(file_id);

DROP TABLE IF EXISTS t_ryytn_file_ref;
CREATE TABLE t_ryytn_file_ref(
    `file_id` varchar(256) NOT NULL   COMMENT '文件编号，在文件服务器上存储的唯一编号(路径)' ,
    `service_id` VARCHAR(32) NOT NULL   COMMENT '业务编号' ,
    `service_type` varchar(64)    COMMENT '业务表名' ,
    PRIMARY KEY (file_id,service_id)
)  COMMENT = '业务文件关联表';


CREATE UNIQUE INDEX t_ryytn_file_ref_pkey ON t_ryytn_file_ref(file_id,service_id);

DROP TABLE IF EXISTS t_ryytn_job;
CREATE TABLE t_ryytn_job(
    `job_id` int8(19) NOT NULL   COMMENT '任务ID' ,
    `job_name` varchar(256) NOT NULL  DEFAULT '' COMMENT '任务名称' ,
    `job_type` int4 NOT NULL  DEFAULT 1 COMMENT '调度类型，1：定时调度，2：循环调度，3：延迟调度' ,
    `start_date` timestamp    COMMENT '开始时间' ,
    `end_date` timestamp    COMMENT '结束时间' ,
    `job_conf` VARCHAR(32)    COMMENT '调度配置，根据调度类型赋值，jobType值，1：cron表达式，2：循环间隔时间，3：空' ,
    `class_name` varchar(256)    COMMENT 'bean名称' ,
    `param` text(2147483647)    COMMENT '调用参数' ,
    `service_id` int8(19)    COMMENT '业务编号' ,
    `misfire_policy` varchar(2)    COMMENT '计划执行错误策略，暂时预留，后续实现' ,
    `concurrent` int2(5) NOT NULL  DEFAULT 0 COMMENT '是否并发执行，0：禁止并发 1：允许并发，默认值：0' ,
    `status` int4 NOT NULL  DEFAULT 1 COMMENT '状态，1：正常，2：禁用，3：已结束，默认值：1' ,
    `description` VARCHAR(255)    COMMENT '描述' ,
    `created_by` varchar(64)    COMMENT '创建者' ,
    `created_time` timestamp   DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '更新者' ,
    `updated_time` timestamp    COMMENT '更新时间' ,
    PRIMARY KEY (job_id)
)  COMMENT = '定时任务调度表';


CREATE UNIQUE INDEX t_ryytn_job_pkey ON t_ryytn_job(job_id);

DROP TABLE IF EXISTS t_ryytn_moudel;
CREATE TABLE t_ryytn_moudel(
    `id` int8(19) NOT NULL   COMMENT '模块编号，业务唯一约束' ,
    `name` varchar(64) NOT NULL   COMMENT '模块名称' ,
    `type` int4 NOT NULL  DEFAULT 1 COMMENT '系统类型，1:web，2：app' ,
    `path` varchar(128)   DEFAULT 'NULL::character varying' COMMENT '首页路由' ,
    `status` int4 NOT NULL  DEFAULT 1 COMMENT '状态，1：正常，2：禁用，默认值：1' ,
    `description` varchar(256)   DEFAULT 'NULL::character varying' COMMENT '描述' ,
    PRIMARY KEY (id)
)  COMMENT = '模块表';


CREATE UNIQUE INDEX t_ryytn_moudel_pkey ON t_ryytn_moudel(id);

DROP TABLE IF EXISTS t_ryytn_oa_department;
CREATE TABLE t_ryytn_oa_department(
    `id` VARCHAR(32) NOT NULL   COMMENT '编号' ,
    `department_mark` VARCHAR(32)    COMMENT '部门简称' ,
    `department_name` varchar(64)    COMMENT '部门全称' ,
    `department_code` VARCHAR(32)    COMMENT '部门编码' ,
    `sub_company_id` VARCHAR(32)    COMMENT '分部编号，对应t_ryytn_subcompany表id字段' ,
    `sup_dep_id` VARCHAR(32)    COMMENT '上级部门编号，0或者空为表示没有上级分部' ,
    `sup_dep_ids` varchar(2048)    COMMENT '所有上级部门编号,英文逗号分隔' ,
    `level` int4   DEFAULT 1 COMMENT '部门层级，根据supDepIds包含的英文逗号数量计算' ,
    `canceled` varchar(10)    COMMENT '封存标志，1 封存，其他为未封存' ,
    `sort_no` float8(17,17)    COMMENT '排序' ,
    `created_time` timestamp    COMMENT '创建时间' ,
    `updated_time` timestamp    COMMENT '修改时间' ,
    `sync_time` int8(19)    COMMENT '同步时间，精确到毫秒' ,
    PRIMARY KEY (id)
)  COMMENT = 'OA同步部门表，从OA系统同步';


CREATE UNIQUE INDEX t_ryytn_oa_department_pkey ON t_ryytn_oa_department(id);

DROP TABLE IF EXISTS t_ryytn_oa_department_extend;
CREATE TABLE t_ryytn_oa_department_extend(
    `id` VARCHAR(32) NOT NULL   COMMENT '编号' ,
    `field_name` VARCHAR(32) NOT NULL   COMMENT '字段名' ,
    `field_value` varchar(64) NOT NULL   COMMENT '字段值' 
)  COMMENT = 'OA同步部门扩展字段表';


CREATE UNIQUE INDEX index_t_ryytn_oa_department_extend_id ON t_ryytn_oa_department_extend(id);

DROP TABLE IF EXISTS t_ryytn_oa_jobtitle;
CREATE TABLE t_ryytn_oa_jobtitle(
    `id` VARCHAR(32) NOT NULL   COMMENT '编号' ,
    `job_title_mark` VARCHAR(32)    COMMENT '简称' ,
    `job_title_name` varchar(64)    COMMENT '全称' ,
    `job_doc` VARCHAR(32)    COMMENT '相关文档id' ,
    `job_department_id` VARCHAR(32)    COMMENT '部门编号，OA接口废弃字段，以人员表departmentId字段为准' ,
    `job_responsibility` varchar(256)    COMMENT '职责' ,
    `job_competency` varchar(256)    COMMENT '任职资格' ,
    `job_title_remark` varchar(256)    COMMENT '备注' ,
    `created_time` timestamp    COMMENT '创建时间' ,
    `updated_time` timestamp    COMMENT '修改时间' ,
    `sync_time` int8(19)    COMMENT '同步时间，精确到毫秒' ,
    PRIMARY KEY (id)
)  COMMENT = 'OA同步岗位表，从OA系统同步';


CREATE UNIQUE INDEX t_ryytn_oa_jobtitle_pkey ON t_ryytn_oa_jobtitle(id);

DROP TABLE IF EXISTS t_ryytn_oa_person;
CREATE TABLE t_ryytn_oa_person(
    `id` VARCHAR(32) NOT NULL   COMMENT '编号' ,
    `work_code` VARCHAR(32)    COMMENT '编号' ,
    `last_name` VARCHAR(32)    COMMENT '人员名称' ,
    `login_id` VARCHAR(32)    COMMENT '登录名' ,
    `account_type` int4    COMMENT '主次账号标志：1：次账号,其他：主账号' ,
    `be_long_to` VARCHAR(32)    COMMENT '主账号id （当accounttype 为 1 有效）' ,
    `department_id` VARCHAR(32)    COMMENT '部门编号' ,
    `job_title_id` VARCHAR(32)    COMMENT '岗位编号' ,
    `location_id` VARCHAR(32)    COMMENT '办公地点' ,
    `status` int4    COMMENT '状态: 0 试用 1 正式 2 临时 3 试用延期 4 解聘 5 离职 6 退休 7 无效' ,
    `language` VARCHAR(32)    COMMENT '系统语言' ,
    `job_activity_desc` varchar(256)    COMMENT '职责描述' ,
    `job_level` VARCHAR(32)    COMMENT '职级' ,
    `job_call` VARCHAR(32)    COMMENT '职称' ,
    `manager_id` VARCHAR(32)    COMMENT '上级人员编号' ,
    `assistant_id` VARCHAR(32)    COMMENT '助理人员编号' ,
    `sex` varchar(10)    COMMENT '性别' ,
    `telephone` VARCHAR(32)    COMMENT '办公电话' ,
    `mobile` VARCHAR(32)    COMMENT '移动电话' ,
    `mobile_call` VARCHAR(32)    COMMENT '其他电话' ,
    `email` VARCHAR(32)    COMMENT '邮箱' ,
    `start_date` varchar(20)    COMMENT '合同开始日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR' ,
    `end_date` varchar(20)    COMMENT '合同结束日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR' ,
    `sec_level` varchar(10)    COMMENT '安全级别' ,
    `password` varchar(64)    COMMENT '密码，密文' ,
    `certificate_num` VARCHAR(32)    COMMENT '身份证' ,
    `birthday` varchar(20)    COMMENT '生日，OA系统同步过来数据可能不满足Date格式，使用VARCHAR' ,
    `height` varchar(10)    COMMENT '身高' ,
    `weight` varchar(10)    COMMENT '体重' ,
    `folk` VARCHAR(32)    COMMENT '民族' ,
    `native_place` VARCHAR(32)    COMMENT '籍贯' ,
    `health_info` VARCHAR(32)    COMMENT '健康状况' ,
    `marital_status` VARCHAR(32)    COMMENT '婚姻状况' ,
    `temp_resident_number` VARCHAR(32)    COMMENT '暂住证号码' ,
    `resident_place` varchar(64)    COMMENT '户口' ,
    `regresident_place` varchar(64)    COMMENT '户口所在地' ,
    `home_address` VARCHAR(32)    COMMENT '家庭联系方式' ,
    `policy` VARCHAR(32)    COMMENT '政治面貌' ,
    `be_member_date` varchar(20)    COMMENT '入团日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR' ,
    `be_party_date` varchar(20)    COMMENT '入党日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR' ,
    `degree` VARCHAR(32)    COMMENT '学位' ,
    `education_level` varchar(10)    COMMENT '学历' ,
    `is_labouunion` int2(5)   DEFAULT 0 COMMENT '是否公会会员' ,
    `last_mod_date` varchar(20)    COMMENT '最后修改日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR' ,
    `sort_no` float8(17,17)    COMMENT '排序' ,
    `created_time` timestamp    COMMENT '创建时间' ,
    `updated_time` timestamp    COMMENT '修改时间' ,
    `sync_time` int8(19)    COMMENT '同步时间，精确到毫秒' ,
    PRIMARY KEY (id)
)  COMMENT = 'OA同步员工表，从OA系统同步';


CREATE UNIQUE INDEX t_ryytn_oa_person_pkey ON t_ryytn_oa_person(id);

DROP TABLE IF EXISTS t_ryytn_oa_person_extend;
CREATE TABLE t_ryytn_oa_person_extend(
    `id` VARCHAR(32) NOT NULL   COMMENT '编号' ,
    `field_name` VARCHAR(32) NOT NULL   COMMENT '字段名' ,
    `field_value` varchar(64) NOT NULL   COMMENT '字段值' 
)  COMMENT = 'OA同步员工扩展字段表';


CREATE UNIQUE INDEX index_t_ryytn_oa_person_extend_id ON t_ryytn_oa_person_extend(id);

DROP TABLE IF EXISTS t_ryytn_oa_subcompany;
CREATE TABLE t_ryytn_oa_subcompany(
    `id` VARCHAR(32) NOT NULL   COMMENT '编号' ,
    `sub_company_name` VARCHAR(32)    COMMENT '分部简称' ,
    `sub_company_desc` varchar(64)    COMMENT '分部全称' ,
    `sub_company_code` VARCHAR(32)    COMMENT '分部编码' ,
    `sup_sub_com_id` VARCHAR(32)    COMMENT '上级分部id,0或者空为表示没有上级分部' ,
    `sup_sub_com_ids` varchar(2048)    COMMENT '所有上级分部id,英文逗号分隔' ,
    `level` int4   DEFAULT 0 COMMENT '分部层级，根据supSubComIds包含的英文逗号数量计算' ,
    `canceled` varchar(10)    COMMENT '封存标志，1 封存，其他为未封存' ,
    `sort_no` float8(17,17)    COMMENT '排序' ,
    `created_time` timestamp    COMMENT '创建时间' ,
    `updated_time` timestamp    COMMENT '修改时间' ,
    `sync_time` int8(19)    COMMENT '同步时间，精确到毫秒' ,
    PRIMARY KEY (id)
)  COMMENT = 'OA同步分部（公司）表，从OA系统同步';


CREATE UNIQUE INDEX t_ryytn_oa_subcompany_pkey ON t_ryytn_oa_subcompany(id);

DROP TABLE IF EXISTS t_ryytn_oa_subcompany_extend;
CREATE TABLE t_ryytn_oa_subcompany_extend(
    `id` VARCHAR(32) NOT NULL   COMMENT '编号' ,
    `field_name` VARCHAR(32) NOT NULL   COMMENT '字段名' ,
    `field_value` varchar(64) NOT NULL   COMMENT '字段值' 
)  COMMENT = 'OA同步分部（公司）扩展字段表';


CREATE UNIQUE INDEX index_t_ryytn_oa_subcompany_extend_id ON t_ryytn_oa_subcompany_extend(id);

DROP TABLE IF EXISTS t_ryytn_page;
CREATE TABLE t_ryytn_page(
    `id` int8(19) NOT NULL   COMMENT '页面编号' ,
    `name` varchar(64) NOT NULL   COMMENT '页面名称' ,
    `alias` varchar(64)    COMMENT '页面别名' ,
    `permission` varchar(256)    COMMENT '页面权限码，用于后端校验权限，英文逗号分隔' ,
    `parent_id` int8(19) NOT NULL   COMMENT '页面父编号，根页面为-1' ,
    `parent_ids` varchar(2048) NOT NULL   COMMENT '页面所有父编号，英文逗号分隔' ,
    `dependency_ids` varchar(1024)    COMMENT '页面依赖编号，英文逗号分隔' ,
    `type` VARCHAR(32) NOT NULL   COMMENT '页面类型，1：菜单' ,
    `path` varchar(256)    COMMENT '页面路由地址' ,
    `config_path` varchar(256)    COMMENT '页面配置页路由地址' ,
    `component` varchar(256)    COMMENT '组件路径' ,
    `icon` varchar(256)    COMMENT '页面图标静态资源目录' ,
    `moudel_id` int8(19) NOT NULL   COMMENT '模块编号' ,
    `sort_no` int8    COMMENT '页面排序' ,
    `sum_flag` int2(5)   DEFAULT 0::smallint COMMENT '是否开启合计，0：否，1：是，默认0' ,
    `description` varchar(256)    COMMENT '描述' ,
    PRIMARY KEY (id)
)  COMMENT = '页面表';


CREATE UNIQUE INDEX t_ryytn_page_pkey ON t_ryytn_page(id);

DROP TABLE IF EXISTS t_ryytn_page_config;
CREATE TABLE t_ryytn_page_config(
    `id` bigserial(19) NOT NULL  DEFAULT nextval('cdop_sys.t_ryytn_page_config_id_seq' COMMENT '配置编号' ,
    `page_id` int8(19) NOT NULL   COMMENT '页面编号' ,
    `row_name` varchar(64)    COMMENT '列名称' ,
    `row_field` varchar(64)    COMMENT '列字段名' ,
    `width` int4    COMMENT '列宽度' ,
    `sort_no` int4    COMMENT '列顺序' ,
    `freeze_flag` int2(5)   DEFAULT 0::smallint COMMENT '列冻结/解冻，0：解冻，1：解冻，默认0' ,
    `show_flag` int2(5)   DEFAULT 0::smallint COMMENT '列展示/隐藏，0：展示，1：隐藏，默认0' ,
    `gather_flag` int2(5)   DEFAULT 0::smallint COMMENT '列聚合，0：不聚合，1：聚合，默认0' ,
    `created_by` varchar(64)    COMMENT '创建人(登录账号)' ,
    `created_time` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '修改人(登录账号)' ,
    `updated_time` timestamp    COMMENT '修改时间' ,
    PRIMARY KEY (id)
)  COMMENT = '页面配置表';


CREATE UNIQUE INDEX t_ryytn_page_config_pkey ON t_ryytn_page_config(id);

DROP TABLE IF EXISTS t_ryytn_role;
CREATE TABLE t_ryytn_role(
    `id` int8(19) NOT NULL   COMMENT '角色编号，业务唯一约束' ,
    `name` varchar(64) NOT NULL   COMMENT '角色名称' ,
    `default_flag` int2(5)   DEFAULT 0::smallint COMMENT '是否默认角色，0：不是默认角色，1：是默认角色' ,
    `status` int4 NOT NULL  DEFAULT 1 COMMENT '状态，1：正常，2：禁用，默认值：1' ,
    `description` varchar(256)    COMMENT '描述' ,
    `sort_no` int4    COMMENT '排序' ,
    `data_type` int4 NOT NULL  DEFAULT 2 COMMENT '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2' ,
    `created_by` varchar(64)    COMMENT '创建人(登录账号)' ,
    `created_time` timestamp   DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_by` varchar(64)    COMMENT '修改人(登录账号)' ,
    `updated_time` timestamp    COMMENT '修改时间' ,
    PRIMARY KEY (id)
)  COMMENT = '角色表';


CREATE UNIQUE INDEX t_ryytn_role_pkey ON t_ryytn_role(id);

DROP TABLE IF EXISTS t_ryytn_role_button;
CREATE TABLE t_ryytn_role_button(
    `role_id` int8(19) NOT NULL   COMMENT '角色编号' ,
    `button_id` int8(19) NOT NULL   COMMENT '菜单编号' 
)  COMMENT = '角色按钮关联关系表';


CREATE UNIQUE INDEX uk_t_ryytn_role_button ON t_ryytn_role_button(role_id,button_id);

DROP TABLE IF EXISTS t_ryytn_role_channel;
CREATE TABLE t_ryytn_role_channel(
    `role_id` int8(19) NOT NULL   COMMENT '角色编号' ,
    `channel_id` VARCHAR(32) NOT NULL   COMMENT '渠道编号' 
)  COMMENT = '角色渠道关联关系表';


CREATE UNIQUE INDEX uk_t_ryytn_role_channel ON t_ryytn_role_channel(role_id,channel_id);

DROP TABLE IF EXISTS t_ryytn_role_depository;
CREATE TABLE t_ryytn_role_depository(
    `role_id` int8(19) NOT NULL   COMMENT '角色编号' ,
    `depository_id` VARCHAR(32) NOT NULL   COMMENT '仓库编号' 
)  COMMENT = '角色仓库关联关系表';


CREATE UNIQUE INDEX uk_t_ryytn_role_depository ON t_ryytn_role_depository(role_id,depository_id);

DROP TABLE IF EXISTS t_ryytn_role_factory;
CREATE TABLE t_ryytn_role_factory(
    `role_id` int8(19) NOT NULL   COMMENT '角色编号' ,
    `factory_id` VARCHAR(32) NOT NULL   COMMENT '工厂编号' 
)  COMMENT = '角色工厂关联关系表';


CREATE UNIQUE INDEX uk_t_ryytn_role_factory ON t_ryytn_role_factory(role_id,factory_id);

DROP TABLE IF EXISTS t_ryytn_role_page;
CREATE TABLE t_ryytn_role_page(
    `role_id` int8(19) NOT NULL   COMMENT '角色编号' ,
    `page_id` int8(19) NOT NULL   COMMENT '菜单编号' 
)  COMMENT = '角色菜单关联关系表';


CREATE UNIQUE INDEX uk_t_ryytn_role_page ON t_ryytn_role_page(role_id,page_id);

DROP TABLE IF EXISTS t_ryytn_role_productcategory;
CREATE TABLE t_ryytn_role_productcategory(
    `role_id` int8(19) NOT NULL   COMMENT '角色编号' ,
    `category_id` VARCHAR(32) NOT NULL   COMMENT '产品品类编号' 
)  COMMENT = '角色产品品类关联关系表';


CREATE UNIQUE INDEX uk_t_ryytn_role_productcategory ON t_ryytn_role_productcategory(role_id,category_id);

DROP TABLE IF EXISTS t_ryytn_sku_lock;
CREATE TABLE t_ryytn_sku_lock(
    `id` int8(19) NOT NULL   COMMENT '主键' ,
    `sku_code` VARCHAR(255)    COMMENT '产品编码' ,
    `sku_name` VARCHAR(255)    COMMENT '产品简称' ,
    `lv1_category_code` varchar(64)    COMMENT '一级分类编码' ,
    `lv1_category_name` varchar(64)    COMMENT '一级分类名称' ,
    `lv2_category_code` varchar(64)    COMMENT '二级分类编码' ,
    `lv2_category_name` varchar(64)    COMMENT '二级分类名称' ,
    `lv3_category_code` varchar(64)    COMMENT '三级分类编码' ,
    `lv3_category_name` varchar(64)    COMMENT '三级分类名称' ,
    `lock_start_date` varchar(10)    COMMENT '锁定期开始时间 yyyyMMdd' ,
    `lock_end_date` varchar(10)    COMMENT '锁定期结束时间 yyyyMMdd' ,
    `lock_start_week` varchar(10)    COMMENT '锁定期开始时间 yyyy/MM/WW' ,
    `lock_end_week` varchar(10)    COMMENT '锁定期结束时间 yyyy/MM/WW' ,
    `created_by` varchar(64)    COMMENT '创建人' ,
    `created_time` timestamp   DEFAULT 'now()' COMMENT '创建时间' ,
    `updated_time` timestamp    COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '产品锁定期表';


CREATE UNIQUE INDEX t_ryytn_sku_lock_pkey ON t_ryytn_sku_lock(id);

DROP TABLE IF EXISTS t_ryytn_sku_lock_channel;
CREATE TABLE t_ryytn_sku_lock_channel(
    `lock_id` int8(19) NOT NULL   COMMENT '主键' ,
    `channel_id` varchar(64) NOT NULL   COMMENT '锁定渠道（二级）' ,
    `channel_name` varchar(64)    COMMENT '锁定渠道名称（二级）' 
)  COMMENT = '产品锁定期渠道';


CREATE UNIQUE INDEX index_t_ryytn_sku_lock_channel_lockId_channelId ON t_ryytn_sku_lock_channel(lock_id,channel_id);

DROP TABLE IF EXISTS t_ryytn_thirdparty_system;
CREATE TABLE t_ryytn_thirdparty_system(
    `id` int8(19) NOT NULL   COMMENT '编号' ,
    `name` varchar(64) NOT NULL   COMMENT '名称' ,
    `auth_code` varchar(64) NOT NULL   COMMENT '授权码' ,
    `url` varchar(1024)    COMMENT '入口页面跳转地址' ,
    `status` int4 NOT NULL  DEFAULT 1 COMMENT '状态，1：正常，2：不正常（扩展字段）' ,
    `description` varchar(256)    COMMENT '描述' ,
    PRIMARY KEY (id)
)  COMMENT = '第三方系统表';


CREATE UNIQUE INDEX t_ryytn_thirdparty_system_pkey ON t_ryytn_thirdparty_system(id);
CREATE UNIQUE INDEX index_t_ryytn_thirdparty_system_name ON t_ryytn_thirdparty_system(name);

DROP TABLE IF EXISTS t_ryytn_warehouse_demand_plan_mark;
CREATE TABLE t_ryytn_warehouse_demand_plan_mark(
    `demand_plan_code` VARCHAR(32) NOT NULL   COMMENT '计划编号' ,
    `version_id` VARCHAR(32) NOT NULL   COMMENT '版本编号' ,
    `receiver_type` varchar(8) NOT NULL   COMMENT '渠道类型' ,
    `lv3_category_code` varchar(64) NOT NULL   COMMENT '三级品类编号' ,
    `biz_date_value` varchar(64) NOT NULL   COMMENT '日期' ,
    `creator` VARCHAR(32)   DEFAULT 'NULL::character varying' COMMENT '创建人' ,
    `last_modifier` VARCHAR(32)   DEFAULT 'NULL::character varying' COMMENT '修改人' ,
    `gmt_create` timestamp NOT NULL  DEFAULT 'now()' COMMENT '创建时间' ,
    `gmt_modify` timestamp NOT NULL  DEFAULT 'now()' COMMENT '修改时间' ,
    PRIMARY KEY (demand_plan_code,version_id,receiver_type,lv3_category_code,biz_date_value)
)  COMMENT = '分仓需求计划标记表';


CREATE UNIQUE INDEX t_ryytn_warehouse_demand_plan_mark_pkey ON t_ryytn_warehouse_demand_plan_mark(demand_plan_code,version_id,receiver_type,lv3_category_code,biz_date_value);

DROP TABLE IF EXISTS t_ryytn_warehouse_demand_report;
CREATE TABLE t_ryytn_warehouse_demand_report(
    `id` int8(19) NOT NULL   COMMENT '编号' ,
    `name` varchar(64) NOT NULL   COMMENT '名称' ,
    `demand_plan_code` VARCHAR(32) NOT NULL   COMMENT '需求计划编号，继承渠道需求计划编号' ,
    `rolling_version` VARCHAR(32) NOT NULL   COMMENT '需求计划版本号，继承渠道需求计划版本号' ,
    `sku_code` VARCHAR(255)    COMMENT 'skuCode' ,
    `sku_name` VARCHAR(255)    COMMENT 'sku名称' ,
    `lv1_category_code` varchar(64)    COMMENT '产品分类编码' ,
    `lv1_category_name` varchar(64)    COMMENT '产品分类名称' ,
    `lv2_category_code` varchar(64)    COMMENT '产品大类编码' ,
    `lv2_category_name` varchar(64)    COMMENT '产品大类名称' ,
    `lv3_category_code` varchar(64)    COMMENT '产品小类编码' ,
    `lv3_category_name` varchar(64)    COMMENT '产品小类名称' ,
    `lv1_channel_code` varchar(64)    COMMENT '一级渠道类型编码' ,
    `lv1_channel_name` varchar(64)    COMMENT '一级渠道类型名称' ,
    `lv2_channel_code` varchar(64)    COMMENT '二级渠道类型编码' ,
    `lv2_channel_name` varchar(64)    COMMENT '二级渠道类型名称' ,
    `lv3_channel_code` varchar(64)    COMMENT '三级渠道类型编码' ,
    `lv3_channel_name` varchar(64)    COMMENT '三级渠道类型名称' ,
    `receiver_type` varchar(64)    COMMENT '渠道类型' ,
    `warehouse_code` varchar(64)    COMMENT '仓库编号' ,
    `warehouse_name` varchar(64)    COMMENT '仓库名称' ,
    `biz_date_type` VARCHAR(32) NOT NULL   COMMENT '时间类型:DAY,WEEK，MONTH,YEAR' ,
    `biz_date_value` varchar(64) NOT NULL   COMMENT '时间类型值,日：20230101;周:0230103' ,
    `order_num` numeric(10,2) NOT NULL   COMMENT '订单数量/订单金额' ,
    `unit` varchar(3)    COMMENT '计量单位:件/瓶/吨ml/元' ,
    `deviation_radio` numeric(10)   DEFAULT 0 COMMENT '渠道需求提报二级渠道数据偏差率' ,
    `remark` varchar(256)    COMMENT '备注' ,
    `extend` varchar(1024)    COMMENT '扩展字段' ,
    `is_modify` int2(5)    COMMENT '是否调整：0为否;1为是，默认0' ,
    `creator` VARCHAR(32)    COMMENT '创建人' ,
    `last_modifier` VARCHAR(32)    COMMENT '最后修改人' ,
    `gmt_create` timestamp    COMMENT '创建时间' ,
    `gmt_modify` timestamp    COMMENT '修改时间' ,
    `is_delete` int2(5)   DEFAULT 0 COMMENT '是否删除：0为否;1为是，默认0' ,
    PRIMARY KEY (id)
)  COMMENT = '分仓需求提报数据表';


CREATE UNIQUE INDEX t_ryytn_warehouse_demand_report_pkey ON t_ryytn_warehouse_demand_report(id);
CREATE UNIQUE INDEX t_ryytn_warehouse_demand_report_service_column ON t_ryytn_warehouse_demand_report(demand_plan_code,rolling_version,sku_code,lv1_category_code,lv2_category_code,lv3_category_code,lv1_channel_code,lv2_channel_code,lv3_channel_code,warehouse_code,biz_date_value);

DROP TABLE IF EXISTS t_ryytn_warehouse_demand_report_history;
CREATE TABLE t_ryytn_warehouse_demand_report_history(
    `id` bigserial(19) NOT NULL  DEFAULT nextval('cdop_sys.t_ryytn_warehouse_demand_report_history_id_seq' COMMENT '编号' ,
    `name` varchar(64) NOT NULL   COMMENT '名称' ,
    `demand_plan_code` VARCHAR(32) NOT NULL   COMMENT '需求计划编号，继承渠道需求计划编号' ,
    `rolling_version` VARCHAR(32) NOT NULL   COMMENT '需求计划版本号，继承渠道需求计划版本号' ,
    `sku_code` VARCHAR(255)    COMMENT 'skuCode' ,
    `sku_name` VARCHAR(255)    COMMENT 'sku名称' ,
    `lv1_category_code` varchar(64)    COMMENT '产品分类编码' ,
    `lv1_category_name` varchar(64)    COMMENT '产品分类名称' ,
    `lv2_category_code` varchar(64)    COMMENT '产品大类编码' ,
    `lv2_category_name` varchar(64)    COMMENT '产品大类名称' ,
    `lv3_category_code` varchar(64)    COMMENT '产品小类编码' ,
    `lv3_category_name` varchar(64)    COMMENT '产品小类名称' ,
    `lv1_channel_code` varchar(64)    COMMENT '一级渠道类型编码' ,
    `lv1_channel_name` varchar(64)    COMMENT '一级渠道类型名称' ,
    `lv2_channel_code` varchar(64)    COMMENT '二级渠道类型编码' ,
    `lv2_channel_name` varchar(64)    COMMENT '二级渠道类型名称' ,
    `lv3_channel_code` varchar(64)    COMMENT '三级渠道类型编码' ,
    `lv3_channel_name` varchar(64)    COMMENT '三级渠道类型名称' ,
    `receiver_type` varchar(64)    COMMENT '渠道类型' ,
    `warehouse_code` varchar(64)    COMMENT '仓库编号' ,
    `warehouse_name` varchar(64)    COMMENT '仓库名称' ,
    `biz_date_type` VARCHAR(32) NOT NULL   COMMENT '时间类型:DAY,WEEK，MONTH,YEAR' ,
    `biz_date_value` varchar(64) NOT NULL   COMMENT '时间类型值,日：20230101;周:0230103' ,
    `order_num` numeric(10,2) NOT NULL   COMMENT '订单数量/订单金额' ,
    `old_order_num` numeric(10,2) NOT NULL   COMMENT '旧订单数量/旧订单金额' ,
    `unit` varchar(3)    COMMENT '计量单位:件/瓶/吨ml/元' ,
    `deviation_radio` numeric(10)   DEFAULT 0 COMMENT '渠道需求提报二级渠道数据偏差率' ,
    `remark` varchar(256)    COMMENT '备注' ,
    `extend` varchar(1024)    COMMENT '扩展字段' ,
    `last_modifier` VARCHAR(32)    COMMENT '最后修改人' ,
    `gmt_modify` timestamp    COMMENT '修改时间' ,
    PRIMARY KEY (id)
)  COMMENT = '分仓需求提报数据历史记录表';


CREATE UNIQUE INDEX t_ryytn_warehouse_demand_report_history_pkey ON t_ryytn_warehouse_demand_report_history(id);

DROP TABLE IF EXISTS t_ryytn_warehouse_demand_report_version;
CREATE TABLE t_ryytn_warehouse_demand_report_version(
    `name` varchar(64) NOT NULL   COMMENT '计划名称' ,
    `demand_plan_code` VARCHAR(32) NOT NULL   COMMENT '计划编号' ,
    `rolling_version` VARCHAR(32) NOT NULL   COMMENT '版本编号' 
)  COMMENT = '分仓需求提报版本';

DROP TABLE IF EXISTS t_ryytn_channel_demand_plan_history;
CREATE TABLE t_ryytn_channel_demand_plan_history(
    `id` bigserial(19) NOT NULL  DEFAULT nextval('cdop_sys.t_ryytn_channel_demand_plan_history_id_seq' COMMENT '编号' ,
    `demand_plan_code` VARCHAR(32) NOT NULL   COMMENT '计划编号' ,
    `version_id` VARCHAR(32) NOT NULL   COMMENT '版本号' ,
    `sku_code` VARCHAR(255)    COMMENT '产品编号' ,
    `sku_name` VARCHAR(255)    COMMENT '产品名称' ,
    `lv1_category_code` varchar(64)    COMMENT '一级品类编号' ,
    `lv1_category_name` varchar(64)    COMMENT '一级品类名称' ,
    `lv2_category_code` varchar(64)    COMMENT '二级品类编号' ,
    `lv2_category_name` varchar(64)    COMMENT '二级品类名称' ,
    `lv3_category_code` varchar(64)    COMMENT '三级品类编号' ,
    `lv3_category_name` varchar(64)    COMMENT '三级品类名称' ,
    `lv1_channel_code` varchar(64)    COMMENT '一级渠道编号' ,
    `lv1_channel_name` varchar(64)    COMMENT '一级渠道名称' ,
    `lv2_channel_code` varchar(64)    COMMENT '二级渠道编号' ,
    `lv2_channel_name` varchar(64)    COMMENT '二级渠道名称' ,
    `lv3_channel_code` varchar(64)    COMMENT '三级渠道编号' ,
    `lv3_channel_name` varchar(64)    COMMENT '三级渠道名称' ,
    `plan_date` varchar(64) NOT NULL   COMMENT '日期' ,
    `plan_value` numeric(10,2) NOT NULL   COMMENT '修改后值' ,
    `old_plan_value` numeric(10,2)   DEFAULT 0 COMMENT '修改前值' ,
    `deviation_radio` numeric(10)   DEFAULT 0 COMMENT '偏差率' ,
    `remark` varchar(256)    COMMENT '备注' ,
    `extend` varchar(1024)    COMMENT '扩展字段' ,
    `last_modifier` VARCHAR(32)    COMMENT '修改人' ,
    `gmt_modify` timestamp    COMMENT '修改时间' ,
    `group_id` int4    COMMENT '分组编号' ,
    PRIMARY KEY (id)
)  COMMENT = '渠道需求计划修改历史明细';


CREATE UNIQUE INDEX t_ryytn_channel_demand_plan_history_pkey ON t_ryytn_channel_demand_plan_history(id);



CREATE OR REPLACE FUNCTION cdop_sys.execute_dynamic_query(query character varying)
 RETURNS SETOF record
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY EXECUTE query;
END;
$function$
;


CREATE OR REPLACE FUNCTION cdop_sys.get_sequence_uid()
 RETURNS bigint
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_last_timestamp BIGINT;
    v_sequence BIGINT;
    v_worker_id BIGINT; -- 假设您的工作机器ID为1
    v_epoch BIGINT := 1609430400000; -- 自定义的起始时间戳
    v_current_timestamp BIGINT;
    v_uid BIGINT;
BEGIN
    -- 获取当前时间戳，单位毫秒
    v_current_timestamp := (EXTRACT(EPOCH FROM clock_timestamp()) * 1000)::BIGINT;
		v_sequence:=FLOOR(random() * 4096)::INT;
		v_worker_id:=FLOOR(random() * 10)::INT;
    -- 返回生成的唯一ID
    v_uid := ((v_current_timestamp - v_epoch) << 22) | (v_worker_id << 12) | v_sequence;
    RETURN v_uid;
END;
$function$
;


