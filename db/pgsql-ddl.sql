DROP TABLE IF EXISTS "qrtz_blob_triggers";
CREATE TABLE "qrtz_blob_triggers" (
  "sched_name" varchar(120) NOT NULL,
  "trigger_name" varchar(200) NOT NULL,
  "trigger_group" varchar(200) NOT NULL,
  "blob_data" bytea,
  PRIMARY KEY ("sched_name", "trigger_name", "trigger_group")
);
COMMENT ON COLUMN "qrtz_blob_triggers"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_blob_triggers"."trigger_name" IS 'qrtz_triggers表trigger_name的外键';
COMMENT ON COLUMN "qrtz_blob_triggers"."trigger_group" IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON COLUMN "qrtz_blob_triggers"."blob_data" IS '存放持久化Trigger对象';
COMMENT ON TABLE "qrtz_blob_triggers" IS 'Blob类型的触发器表';

DROP TABLE IF EXISTS "qrtz_calendars";
CREATE TABLE "qrtz_calendars" (
  "sched_name" varchar(120) NOT NULL,
  "calendar_name" varchar(200) NOT NULL,
  "calendar" bytea NOT NULL,
  PRIMARY KEY ("sched_name", "calendar_name")
);
COMMENT ON COLUMN "qrtz_calendars"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_calendars"."calendar_name" IS '日历名称';
COMMENT ON COLUMN "qrtz_calendars"."calendar" IS '存放持久化calendar对象';
COMMENT ON TABLE "qrtz_calendars" IS '日历信息表';

DROP TABLE IF EXISTS "qrtz_cron_triggers";
CREATE TABLE "qrtz_cron_triggers" (
  "sched_name" varchar(120) NOT NULL,
  "trigger_name" varchar(200) NOT NULL,
  "trigger_group" varchar(200) NOT NULL,
  "cron_expression" varchar(200) NOT NULL,
  "time_zone_id" varchar(80),
  PRIMARY KEY ("sched_name", "trigger_name", "trigger_group")
);
COMMENT ON COLUMN "qrtz_cron_triggers"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_cron_triggers"."trigger_name" IS 'qrtz_triggers表trigger_name的外键';
COMMENT ON COLUMN "qrtz_cron_triggers"."trigger_group" IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON COLUMN "qrtz_cron_triggers"."cron_expression" IS 'cron表达式';
COMMENT ON COLUMN "qrtz_cron_triggers"."time_zone_id" IS '时区';
COMMENT ON TABLE "qrtz_cron_triggers" IS 'Cron类型的触发器表';

DROP TABLE IF EXISTS "qrtz_fired_triggers";
CREATE TABLE "qrtz_fired_triggers" (
  "sched_name" varchar(120) NOT NULL,
  "entry_id" varchar(95) NOT NULL,
  "trigger_name" varchar(200) NOT NULL,
  "trigger_group" varchar(200) NOT NULL,
  "instance_name" varchar(200) NOT NULL,
  "fired_time" int8 NOT NULL,
  "sched_time" int8 NOT NULL,
  "priority" int4 NOT NULL,
  "state" varchar(16) NOT NULL,
  "job_name" varchar(200),
  "job_group" varchar(200),
  "is_nonconcurrent" bool NOT NULL,
  "requests_recovery" bool NOT NULL,
  PRIMARY KEY ("sched_name", "entry_id")
);
COMMENT ON COLUMN "qrtz_fired_triggers"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_fired_triggers"."entry_id" IS '调度器实例id';
COMMENT ON COLUMN "qrtz_fired_triggers"."trigger_name" IS 'qrtz_triggers表trigger_name的外键';
COMMENT ON COLUMN "qrtz_fired_triggers"."trigger_group" IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON COLUMN "qrtz_fired_triggers"."instance_name" IS '调度器实例名';
COMMENT ON COLUMN "qrtz_fired_triggers"."fired_time" IS '触发的时间';
COMMENT ON COLUMN "qrtz_fired_triggers"."sched_time" IS '定时器制定的时间';
COMMENT ON COLUMN "qrtz_fired_triggers"."priority" IS '优先级';
COMMENT ON COLUMN "qrtz_fired_triggers"."state" IS '状态';
COMMENT ON COLUMN "qrtz_fired_triggers"."job_name" IS '任务名称';
COMMENT ON COLUMN "qrtz_fired_triggers"."job_group" IS '任务组名';
COMMENT ON COLUMN "qrtz_fired_triggers"."is_nonconcurrent" IS '是否并发';
COMMENT ON COLUMN "qrtz_fired_triggers"."requests_recovery" IS '是否接受恢复执行';
COMMENT ON TABLE "qrtz_fired_triggers" IS '已触发的触发器表';

DROP TABLE IF EXISTS "qrtz_job_details";
CREATE TABLE "qrtz_job_details" (
  "sched_name" varchar(120) NOT NULL,
  "job_name" varchar(200) NOT NULL,
  "job_group" varchar(200) NOT NULL,
  "description" varchar(250),
  "job_class_name" varchar(250) NOT NULL,
  "is_durable" bool NOT NULL,
  "is_nonconcurrent" bool NOT NULL,
  "is_update_data" bool NOT NULL,
  "requests_recovery" bool NOT NULL,
  "job_data" bytea,
  PRIMARY KEY ("sched_name", "job_name", "job_group")
);
COMMENT ON COLUMN "qrtz_job_details"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_job_details"."job_name" IS '任务名称';
COMMENT ON COLUMN "qrtz_job_details"."job_group" IS '任务组名';
COMMENT ON COLUMN "qrtz_job_details"."description" IS '相关介绍';
COMMENT ON COLUMN "qrtz_job_details"."job_class_name" IS '执行任务类名称';
COMMENT ON COLUMN "qrtz_job_details"."is_durable" IS '是否持久化';
COMMENT ON COLUMN "qrtz_job_details"."is_nonconcurrent" IS '是否并发';
COMMENT ON COLUMN "qrtz_job_details"."is_update_data" IS '是否更新数据';
COMMENT ON COLUMN "qrtz_job_details"."requests_recovery" IS '是否接受恢复执行';
COMMENT ON COLUMN "qrtz_job_details"."job_data" IS '存放持久化job对象';
COMMENT ON TABLE "qrtz_job_details" IS '任务详细信息表';

DROP TABLE IF EXISTS "qrtz_locks";
CREATE TABLE "qrtz_locks" (
  "sched_name" varchar(120) NOT NULL,
  "lock_name" varchar(40) NOT NULL,
  PRIMARY KEY ("sched_name", "lock_name")
);
COMMENT ON COLUMN "qrtz_locks"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_locks"."lock_name" IS '悲观锁名称';
COMMENT ON TABLE "qrtz_locks" IS '存储的悲观锁信息表';

DROP TABLE IF EXISTS "qrtz_paused_trigger_grps";
CREATE TABLE "qrtz_paused_trigger_grps" (
  "sched_name" varchar(120) NOT NULL,
  "trigger_group" varchar(200) NOT NULL,
  PRIMARY KEY ("sched_name", "trigger_group")
);
COMMENT ON COLUMN "qrtz_paused_trigger_grps"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_paused_trigger_grps"."trigger_group" IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON TABLE "qrtz_paused_trigger_grps" IS '暂停的触发器表';

DROP TABLE IF EXISTS "qrtz_scheduler_state";
CREATE TABLE "qrtz_scheduler_state" (
  "sched_name" varchar(120) NOT NULL,
  "instance_name" varchar(200) NOT NULL,
  "last_checkin_time" int8 NOT NULL,
  "checkin_interval" int8 NOT NULL,
  PRIMARY KEY ("sched_name", "instance_name")
);
COMMENT ON COLUMN "qrtz_scheduler_state"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_scheduler_state"."instance_name" IS '实例名称';
COMMENT ON COLUMN "qrtz_scheduler_state"."last_checkin_time" IS '上次检查时间';
COMMENT ON COLUMN "qrtz_scheduler_state"."checkin_interval" IS '检查间隔时间';
COMMENT ON TABLE "qrtz_scheduler_state" IS '调度器状态表';

DROP TABLE IF EXISTS "qrtz_simple_triggers";
CREATE TABLE "qrtz_simple_triggers" (
  "sched_name" varchar(120) NOT NULL,
  "trigger_name" varchar(200) NOT NULL,
  "trigger_group" varchar(200) NOT NULL,
  "repeat_count" int8 NOT NULL,
  "repeat_interval" int8 NOT NULL,
  "times_triggered" int8 NOT NULL,
  PRIMARY KEY ("sched_name", "trigger_name", "trigger_group")
);
COMMENT ON COLUMN "qrtz_simple_triggers"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_simple_triggers"."trigger_name" IS 'qrtz_triggers表trigger_name的外键';
COMMENT ON COLUMN "qrtz_simple_triggers"."trigger_group" IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON COLUMN "qrtz_simple_triggers"."repeat_count" IS '重复的次数统计';
COMMENT ON COLUMN "qrtz_simple_triggers"."repeat_interval" IS '重复的间隔时间';
COMMENT ON COLUMN "qrtz_simple_triggers"."times_triggered" IS '已经触发的次数';
COMMENT ON TABLE "qrtz_simple_triggers" IS '简单触发器的信息表';

DROP TABLE IF EXISTS "qrtz_simprop_triggers";
CREATE TABLE "qrtz_simprop_triggers" (
  "sched_name" varchar(120) NOT NULL,
  "trigger_name" varchar(200) NOT NULL,
  "trigger_group" varchar(200) NOT NULL,
  "str_prop_1" varchar(512),
  "str_prop_2" varchar(512),
  "str_prop_3" varchar(512),
  "int_prop_1" int4,
  "int_prop_2" int4,
  "long_prop_1" int8,
  "long_prop_2" int8,
  "dec_prop_1" numeric(13,4),
  "dec_prop_2" numeric(13,4),
  "bool_prop_1" bool,
  "bool_prop_2" bool,
  PRIMARY KEY ("sched_name", "trigger_name", "trigger_group")
);
COMMENT ON COLUMN "qrtz_simprop_triggers"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_simprop_triggers"."trigger_name" IS 'qrtz_triggers表trigger_name的外键';
COMMENT ON COLUMN "qrtz_simprop_triggers"."trigger_group" IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON COLUMN "qrtz_simprop_triggers"."str_prop_1" IS 'String类型的trigger的第一个参数';
COMMENT ON COLUMN "qrtz_simprop_triggers"."str_prop_2" IS 'String类型的trigger的第二个参数';
COMMENT ON COLUMN "qrtz_simprop_triggers"."str_prop_3" IS 'String类型的trigger的第三个参数';
COMMENT ON COLUMN "qrtz_simprop_triggers"."int_prop_1" IS 'int类型的trigger的第一个参数';
COMMENT ON COLUMN "qrtz_simprop_triggers"."int_prop_2" IS 'int类型的trigger的第二个参数';
COMMENT ON COLUMN "qrtz_simprop_triggers"."long_prop_1" IS 'long类型的trigger的第一个参数';
COMMENT ON COLUMN "qrtz_simprop_triggers"."long_prop_2" IS 'long类型的trigger的第二个参数';
COMMENT ON COLUMN "qrtz_simprop_triggers"."dec_prop_1" IS 'decimal类型的trigger的第一个参数';
COMMENT ON COLUMN "qrtz_simprop_triggers"."dec_prop_2" IS 'decimal类型的trigger的第二个参数';
COMMENT ON COLUMN "qrtz_simprop_triggers"."bool_prop_1" IS 'Boolean类型的trigger的第一个参数';
COMMENT ON COLUMN "qrtz_simprop_triggers"."bool_prop_2" IS 'Boolean类型的trigger的第二个参数';
COMMENT ON TABLE "qrtz_simprop_triggers" IS '同步机制的行锁表';

DROP TABLE IF EXISTS "qrtz_triggers";
CREATE TABLE "qrtz_triggers" (
  "sched_name" varchar(120) NOT NULL,
  "trigger_name" varchar(200) NOT NULL,
  "trigger_group" varchar(200) NOT NULL,
  "job_name" varchar(200) NOT NULL,
  "job_group" varchar(200) NOT NULL,
  "description" varchar(250),
  "next_fire_time" int8,
  "prev_fire_time" int8,
  "priority" int4,
  "trigger_state" varchar(16) NOT NULL,
  "trigger_type" varchar(8) NOT NULL,
  "start_time" int8 NOT NULL,
  "end_time" int8,
  "calendar_name" varchar(200),
  "misfire_instr" int2,
  "job_data" bytea,
  PRIMARY KEY ("sched_name", "trigger_name", "trigger_group")
);
COMMENT ON COLUMN "qrtz_triggers"."sched_name" IS '调度名称';
COMMENT ON COLUMN "qrtz_triggers"."trigger_name" IS '触发器的名字';
COMMENT ON COLUMN "qrtz_triggers"."trigger_group" IS '触发器所属组的名字';
COMMENT ON COLUMN "qrtz_triggers"."job_name" IS 'qrtz_job_details表job_name的外键';
COMMENT ON COLUMN "qrtz_triggers"."job_group" IS 'qrtz_job_details表job_group的外键';
COMMENT ON COLUMN "qrtz_triggers"."description" IS '相关介绍';
COMMENT ON COLUMN "qrtz_triggers"."next_fire_time" IS '上一次触发时间（毫秒）';
COMMENT ON COLUMN "qrtz_triggers"."prev_fire_time" IS '下一次触发时间（默认为-1表示不触发）';
COMMENT ON COLUMN "qrtz_triggers"."priority" IS '优先级';
COMMENT ON COLUMN "qrtz_triggers"."trigger_state" IS '触发器状态';
COMMENT ON COLUMN "qrtz_triggers"."trigger_type" IS '触发器的类型';
COMMENT ON COLUMN "qrtz_triggers"."start_time" IS '开始时间';
COMMENT ON COLUMN "qrtz_triggers"."end_time" IS '结束时间';
COMMENT ON COLUMN "qrtz_triggers"."calendar_name" IS '日程表名称';
COMMENT ON COLUMN "qrtz_triggers"."misfire_instr" IS '补偿执行的策略';
COMMENT ON COLUMN "qrtz_triggers"."job_data" IS '存放持久化job对象';
COMMENT ON TABLE "qrtz_triggers" IS '触发器详细信息表';

DROP TABLE IF EXISTS "t_ryytn_account";
CREATE TABLE "t_ryytn_account" (
  "id" int8 NOT NULL,
  "name" varchar(64),
  "nick_name" varchar(64),
  "work_code" varchar(32),
  "login_id" varchar(32),
  "password" varchar(64),
  "oa_id" varchar(32),
  "status" int4 DEFAULT 1 ,
  "description" varchar(256),
  "data_type" int4 DEFAULT '2' ,
  "created_time" timestamp DEFAULT CURRENT_TIMESTAMP,
  "updated_time" timestamp,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_account"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_account"."name" IS '账号名称，OA同步lastName可能为空，此处默认为空，业务代码校验本地账号不能为空';
COMMENT ON COLUMN "t_ryytn_account"."nick_name" IS '账号昵称';
COMMENT ON COLUMN "t_ryytn_account"."work_code" IS '工号';
COMMENT ON COLUMN "t_ryytn_account"."login_id" IS '登录账号，本地账号必填，OA账号对应loginId，可能为空，此处默认为空，业务代码校验本地账号不能为空';
COMMENT ON COLUMN "t_ryytn_account"."password" IS '密码，本地账号必填，此处默认为空，业务代码校验本地账号不能为空';
COMMENT ON COLUMN "t_ryytn_account"."oa_id" IS 'OA人员编号';
COMMENT ON COLUMN "t_ryytn_account"."status" IS '状态，1：正常，2：停用';
COMMENT ON COLUMN "t_ryytn_account"."description" IS '描述';
COMMENT ON COLUMN "t_ryytn_account"."data_type" IS '数据类型，1:表示初始化数据不允许删除；2:表示管理端创建数据；3:OA系统同步，默认为2';
COMMENT ON COLUMN "t_ryytn_account"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_account"."updated_time" IS '修改时间';
COMMENT ON TABLE "t_ryytn_account" IS '账号信息表，包含预置管理账号、本地管理账号和OA同步人员';

DROP TABLE IF EXISTS "t_ryytn_account_role";
CREATE TABLE "t_ryytn_account_role" (
  "account_id" int8 NOT NULL,
  "role_id" int8 NOT NULL,
  CONSTRAINT "uk_t_ryytn_account_role" UNIQUE ("account_id", "role_id")
);
COMMENT ON COLUMN "t_ryytn_account_role"."account_id" IS '账号编号';
COMMENT ON COLUMN "t_ryytn_account_role"."role_id" IS '角色编号';
COMMENT ON TABLE "t_ryytn_account_role" IS '账号角色关联关系表';

DROP TABLE IF EXISTS "t_ryytn_moudel";
CREATE TABLE "t_ryytn_moudel" (
  "id" INT8 NOT NULL,
  "name" VARCHAR(64) NOT NULL,
  "type" INT4 NOT NULL DEFAULT 1,
  "path" VARCHAR(128) DEFAULT NULL,
  "status" INT4 NOT NULL DEFAULT '1',
  "description" VARCHAR(256) DEFAULT NULL,
  PRIMARY KEY (id)
);
COMMENT ON COLUMN "t_ryytn_moudel"."id" IS '模块编号，业务唯一约束';
COMMENT ON COLUMN "t_ryytn_moudel"."name" IS '模块名称';
COMMENT ON COLUMN "t_ryytn_moudel"."type" IS '系统类型，1:web，2：app';
COMMENT ON COLUMN "t_ryytn_moudel"."path" IS '首页路由';
COMMENT ON COLUMN "t_ryytn_moudel"."status" IS '状态，1：正常，2：禁用，默认值：1';
COMMENT ON COLUMN "t_ryytn_moudel"."description" IS '描述';

DROP TABLE IF EXISTS "t_ryytn_button";
CREATE TABLE "t_ryytn_button" (
  "id" int8 NOT NULL,
  "name" varchar(64) NOT NULL,
  "alias" varchar(64),
  "permission" varchar(256),
  "dependency_ids" varchar(1024),
  "page_id" int8 NOT NULL,
  "sort_no" int4,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_button"."id" IS '按钮编号';
COMMENT ON COLUMN "t_ryytn_button"."name" IS '按钮名称';
COMMENT ON COLUMN "t_ryytn_button"."alias" IS '按钮别名';
COMMENT ON COLUMN "t_ryytn_button"."permission" IS '按钮权限码，用于后端校验权限，英文逗号分隔';
COMMENT ON COLUMN "t_ryytn_button"."dependency_ids" IS '按钮依赖编号，英文逗号分隔';
COMMENT ON COLUMN "t_ryytn_button"."page_id" IS '所属页面编号';
COMMENT ON COLUMN "t_ryytn_button"."sort_no" IS '页面排序';
COMMENT ON TABLE "t_ryytn_button" IS '按钮表';

DROP TABLE IF EXISTS "t_ryytn_config";
CREATE TABLE "t_ryytn_config" (
  "category_id" varchar(32) NOT NULL,
  "config_id" varchar(64) NOT NULL,
  "config_name" varchar(64),
  "config_type" int4,
  "config_value" varchar(512),
  "status" int4 DEFAULT '1' ,
  "is_display" int4,
  "validator" varchar(256),
  "description" varchar(256),
  "sort_no" int4,
  "created_by" varchar(64) DEFAULT 'admin' ,
  "created_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(64),
  "updated_time" timestamp,
  PRIMARY KEY ("config_id")
);
COMMENT ON COLUMN "t_ryytn_config"."category_id" IS '所属类别';
COMMENT ON COLUMN "t_ryytn_config"."config_id" IS '配置编码';
COMMENT ON COLUMN "t_ryytn_config"."config_name" IS '配置名称';
COMMENT ON COLUMN "t_ryytn_config"."config_type" IS '配置值类型（1：密码类型；2：文本类型；3：文件类型；4：枚举类型；5：数字文本；6：文本域）';
COMMENT ON COLUMN "t_ryytn_config"."config_value" IS '配置值';
COMMENT ON COLUMN "t_ryytn_config"."status" IS '配置状态，1：正常，2：停用';
COMMENT ON COLUMN "t_ryytn_config"."is_display" IS '0:不展示, 1:展示';
COMMENT ON COLUMN "t_ryytn_config"."validator" IS '校验规则';
COMMENT ON COLUMN "t_ryytn_config"."description" IS '配置描述';
COMMENT ON COLUMN "t_ryytn_config"."sort_no" IS '排序字段';
COMMENT ON COLUMN "t_ryytn_config"."created_by" IS '创建人';
COMMENT ON COLUMN "t_ryytn_config"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_config"."updated_by" IS '更新人';
COMMENT ON COLUMN "t_ryytn_config"."updated_time" IS '更新时间';
COMMENT ON TABLE "t_ryytn_config" IS '配置表';

DROP TABLE IF EXISTS "t_ryytn_configcategory";
CREATE TABLE "t_ryytn_configcategory" (
  "category_id" varchar(32) NOT NULL,
  "parent_id" int8,
  "parent_ids" int8,
  "name" varchar(50),
  "status" int4 DEFAULT '1' ,
  "sort_no" int4,
  PRIMARY KEY ("category_id")
);
COMMENT ON COLUMN "t_ryytn_configcategory"."category_id" IS '规则编号，业务唯一约束';
COMMENT ON COLUMN "t_ryytn_configcategory"."parent_id" IS '父类别编号';
COMMENT ON COLUMN "t_ryytn_configcategory"."parent_ids" IS '所有有父类别编号';
COMMENT ON COLUMN "t_ryytn_configcategory"."name" IS '类别名称';
COMMENT ON COLUMN "t_ryytn_configcategory"."status" IS '配置状态，1：正常，2：停用';
COMMENT ON COLUMN "t_ryytn_configcategory"."sort_no" IS '类别序号';
COMMENT ON TABLE "t_ryytn_configcategory" IS '配置类别表';

DROP TABLE IF EXISTS "t_ryytn_dict_data";
CREATE TABLE "t_ryytn_dict_data" (
  "dict_id" serial NOT NULL,
  "dict_type" varchar(64) NOT NULL,
  "name" varchar(64) NOT NULL,
  "code" varchar(64) NOT NULL,
  "parent_id" varchar(64) NOT NULL,
  "parent_ids" varchar(2048) NOT NULL,
  "level" int4 NOT NULL,
  "leaf_flag" int2 NOT NULL DEFAULT '1' ,
  "css_class" varchar(64),
  "list_class" varchar(64),
  "item_check" int4 DEFAULT '0' ,
  "sort_no" int4,
  "status" int4 DEFAULT '1' ,
  "delete_flag" int2 DEFAULT '0' ,
  "description" varchar(256),
  "data_type" int4 NOT NULL DEFAULT '2' ,
  "created_by" varchar(64),
  "created_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(64),
  "updated_time" timestamp,
  PRIMARY KEY ("dict_id")
);
COMMENT ON COLUMN "t_ryytn_dict_data"."dict_id" IS '字典编号';
COMMENT ON COLUMN "t_ryytn_dict_data"."dict_type" IS '字典类型';
COMMENT ON COLUMN "t_ryytn_dict_data"."name" IS '字典名称';
COMMENT ON COLUMN "t_ryytn_dict_data"."code" IS '字典编码';
COMMENT ON COLUMN "t_ryytn_dict_data"."parent_id" IS '父字典编号';
COMMENT ON COLUMN "t_ryytn_dict_data"."parent_ids" IS '所有父字典编号';
COMMENT ON COLUMN "t_ryytn_dict_data"."level" IS '字典层级';
COMMENT ON COLUMN "t_ryytn_dict_data"."leaf_flag" IS '是否叶子，0：否，1：是，首次新增默认为1';
COMMENT ON COLUMN "t_ryytn_dict_data"."css_class" IS '样式属性（其他样式扩展）';
COMMENT ON COLUMN "t_ryytn_dict_data"."list_class" IS '表格回显样式';
COMMENT ON COLUMN "t_ryytn_dict_data"."item_check" IS '是否默认选中，1：是，0：否，默认为0';
COMMENT ON COLUMN "t_ryytn_dict_data"."sort_no" IS '排序';
COMMENT ON COLUMN "t_ryytn_dict_data"."status" IS '状态，1：正常，2：停用，默认值：1';
COMMENT ON COLUMN "t_ryytn_dict_data"."delete_flag" IS '删除状态，0：未删除，1：已删除，默认0';
COMMENT ON COLUMN "t_ryytn_dict_data"."description" IS '描述';
COMMENT ON COLUMN "t_ryytn_dict_data"."data_type" IS '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2';
COMMENT ON COLUMN "t_ryytn_dict_data"."created_by" IS '创建人(登录账号)';
COMMENT ON COLUMN "t_ryytn_dict_data"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_dict_data"."updated_by" IS '修改人(登录账号)';
COMMENT ON COLUMN "t_ryytn_dict_data"."updated_time" IS '修改时间';
COMMENT ON TABLE "t_ryytn_dict_data" IS '字典数据表';

DROP TABLE IF EXISTS "t_ryytn_dict_type";
CREATE TABLE "t_ryytn_dict_type" (
  "dict_type_id" serial NOT NULL,
  "dict_type" varchar(64) NOT NULL,
  "dict_name" varchar(64) NOT NULL,
  "status" int4 DEFAULT '1' ,
  "delete_flag" int2 DEFAULT '0',
  "description" varchar(256),
  "data_type" int4 NOT NULL DEFAULT '2',
  "created_by" varchar(64),
  "created_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(64),
  "updated_time" timestamp,
  PRIMARY KEY ("dict_type_id")
);
COMMENT ON COLUMN "t_ryytn_dict_type"."dict_type_id" IS '字典类型编号';
COMMENT ON COLUMN "t_ryytn_dict_type"."dict_type" IS '字典类型';
COMMENT ON COLUMN "t_ryytn_dict_type"."dict_name" IS '字典名称';
COMMENT ON COLUMN "t_ryytn_dict_type"."status" IS '状态，1：正常，2：停用，默认值：1';
COMMENT ON COLUMN "t_ryytn_dict_type"."delete_flag" IS '删除状态，0：未删除，1：已删除，默认0';
COMMENT ON COLUMN "t_ryytn_dict_type"."description" IS '描述';
COMMENT ON COLUMN "t_ryytn_dict_type"."data_type" IS '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为1';
COMMENT ON COLUMN "t_ryytn_dict_type"."created_by" IS '创建人(登录账号)';
COMMENT ON COLUMN "t_ryytn_dict_type"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_dict_type"."updated_by" IS '修改人(登录账号)';
COMMENT ON COLUMN "t_ryytn_dict_type"."updated_time" IS '修改时间';
COMMENT ON TABLE "t_ryytn_dict_type" IS '字典类型表';

DROP TABLE IF EXISTS "t_ryytn_file";
CREATE TABLE "t_ryytn_file" (
  "file_id" varchar(256) NOT NULL,
  "file_type" int4 NOT NULL,
  "suffix" varchar(8),
  "o_file_name" varchar(1024),
  "created_by" varchar(64),
  "created_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("file_id")
);
COMMENT ON COLUMN "t_ryytn_file"."file_id" IS '文件编号，在文件服务器上存储的唯一编号(路径)';
COMMENT ON COLUMN "t_ryytn_file"."file_type" IS '文件类型，0：图片，1：视频，2：音频，3：文本，4：文档，5：压缩文件，6：脚本文件，99：其他';
COMMENT ON COLUMN "t_ryytn_file"."suffix" IS '文件后缀';
COMMENT ON COLUMN "t_ryytn_file"."o_file_name" IS '原文件名';
COMMENT ON COLUMN "t_ryytn_file"."created_by" IS '创建人';
COMMENT ON COLUMN "t_ryytn_file"."created_time" IS '创建时间';
COMMENT ON TABLE "t_ryytn_file" IS '文件信息表';

DROP TABLE IF EXISTS "t_ryytn_file_ref";
CREATE TABLE "t_ryytn_file_ref" (
  "file_id" varchar(256) NOT NULL,
  "service_id" varchar(32) NOT NULL,
  "service_type" varchar(64),
  PRIMARY KEY ("file_id", "service_id")
);
COMMENT ON COLUMN "t_ryytn_file_ref"."file_id" IS '文件编号，在文件服务器上存储的唯一编号(路径)';
COMMENT ON COLUMN "t_ryytn_file_ref"."service_id" IS '业务编号';
COMMENT ON COLUMN "t_ryytn_file_ref"."service_type" IS '业务表名';
COMMENT ON TABLE "t_ryytn_file_ref" IS '业务文件关联表';

DROP TABLE IF EXISTS "t_ryytn_job";
CREATE TABLE "t_ryytn_job" (
  "job_id" int8 NOT NULL,
  "job_name" varchar(256) NOT NULL DEFAULT '',
  "job_type" int4 NOT NULL DEFAULT '1',
  "start_date" timestamp DEFAULT CURRENT_TIMESTAMP,
  "end_date" timestamp DEFAULT NULL,
  "job_conf" varchar(32),
  "class_name" varchar(256),
  "param" text,
  "service_id" int8,
  "misfire_policy" varchar(2),
  "concurrent" int2 NOT NULL DEFAULT 0,
  "status" int4 NOT NULL  DEFAULT '1',
  "description" varchar(255),
  "created_by" varchar(64),
  "created_time" timestamp DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(64),
  "updated_time" timestamp,
  PRIMARY KEY ("job_id")
);
COMMENT ON COLUMN "t_ryytn_job"."job_id" IS '任务ID';
COMMENT ON COLUMN "t_ryytn_job"."job_name" IS '任务名称';
COMMENT ON COLUMN "t_ryytn_job"."job_type" IS '调度类型，1：定时调度，2：循环调度，3：延迟调度';
COMMENT ON COLUMN "t_ryytn_job"."start_date" IS '开始时间，默认当前时间';
COMMENT ON COLUMN "t_ryytn_job"."end_date" IS '结束时间';
COMMENT ON COLUMN "t_ryytn_job"."job_conf" IS '调度配置，根据调度类型赋值，jobType值，1：cron表达式，2：循环间隔时间，3：空';
COMMENT ON COLUMN "t_ryytn_job"."class_name" IS 'bean名称';
COMMENT ON COLUMN "t_ryytn_job"."param" IS '调用参数';
COMMENT ON COLUMN "t_ryytn_job"."service_id" IS '业务编号';
COMMENT ON COLUMN "t_ryytn_job"."misfire_policy" IS '计划执行错误策略，暂时预留，后续实现';
COMMENT ON COLUMN "t_ryytn_job"."concurrent" IS '是否并发执行，0：禁止并发 1：允许并发，默认值：0';
COMMENT ON COLUMN "t_ryytn_job"."status" IS '状态，1：正常，2：禁用，3：已结束，默认值：1';
COMMENT ON COLUMN "t_ryytn_job"."description" IS '描述';
COMMENT ON COLUMN "t_ryytn_job"."created_by" IS '创建者';
COMMENT ON COLUMN "t_ryytn_job"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_job"."updated_by" IS '更新者';
COMMENT ON COLUMN "t_ryytn_job"."updated_time" IS '更新时间';
COMMENT ON TABLE "t_ryytn_job" IS '定时任务调度表';

DROP TABLE IF EXISTS "t_ryytn_oa_department";
CREATE TABLE "t_ryytn_oa_department" (
  "id" varchar(32) NOT NULL,
  "department_mark" varchar(32),
  "department_name" varchar(64),
  "department_code" varchar(32),
  "sub_company_id" varchar(32),
  "sup_dep_id" varchar(32),
  "sup_dep_ids" varchar(2048),
  "level" int4 DEFAULT 0,
  "canceled" varchar(10),
  "sort_no" float8,
  "created_time" timestamp,
  "updated_time" timestamp,
  "sync_time" int8,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_oa_department"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_oa_department"."department_mark" IS '部门简称';
COMMENT ON COLUMN "t_ryytn_oa_department"."department_name" IS '部门全称';
COMMENT ON COLUMN "t_ryytn_oa_department"."department_code" IS '部门编码';
COMMENT ON COLUMN "t_ryytn_oa_department"."sub_company_id" IS '分部编号，对应t_ryytn_subcompany表id字段';
COMMENT ON COLUMN "t_ryytn_oa_department"."sup_dep_id" IS '上级部门编号，0或者空为表示没有上级分部';
COMMENT ON COLUMN "t_ryytn_oa_department"."sup_dep_ids" IS '所有上级部门编号,英文逗号分隔';
COMMENT ON COLUMN "t_ryytn_oa_department"."level" IS '部门层级，根据supDepIds包含的英文逗号数量计算';
COMMENT ON COLUMN "t_ryytn_oa_department"."canceled" IS '封存标志，1 封存，其他为未封存';
COMMENT ON COLUMN "t_ryytn_oa_department"."sort_no" IS '排序';
COMMENT ON COLUMN "t_ryytn_oa_department"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_oa_department"."updated_time" IS '修改时间';
COMMENT ON COLUMN "t_ryytn_oa_department"."sync_time" IS '同步时间，精确到毫秒';
COMMENT ON TABLE "t_ryytn_oa_department" IS 'OA同步部门表，从OA系统同步';

DROP TABLE IF EXISTS "t_ryytn_oa_department_extend";
CREATE TABLE "t_ryytn_oa_department_extend" (
  "id" varchar(32) NOT NULL,
  "field_name" varchar(32) NOT NULL,
  "field_value" varchar(64) NOT NULL
);
CREATE INDEX "index_t_ryytn_oa_department_extend_id" ON "t_ryytn_oa_department_extend" USING btree (
  "id"
);
COMMENT ON COLUMN "t_ryytn_oa_department_extend"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_oa_department_extend"."field_name" IS '字段名';
COMMENT ON COLUMN "t_ryytn_oa_department_extend"."field_value" IS '字段值';
COMMENT ON TABLE "t_ryytn_oa_department_extend" IS 'OA同步部门扩展字段表';

DROP TABLE IF EXISTS "t_ryytn_oa_jobtitle";
CREATE TABLE "t_ryytn_oa_jobtitle" (
  "id" varchar(32) NOT NULL,
  "job_title_mark" varchar(32),
  "job_title_name" varchar(64),
  "job_doc" varchar(32),
  "job_department_id" varchar(32),
  "job_responsibility" varchar(256),
  "job_competency" varchar(256),
  "job_title_remark" varchar(256),
  "created_time" timestamp,
  "updated_time" timestamp,
  "sync_time" int8,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."job_title_mark" IS '简称';
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."job_title_name" IS '全称';
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."job_doc" IS '相关文档id';
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."job_department_id" IS '部门编号，OA接口废弃字段，以人员表departmentId字段为准';
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."job_responsibility" IS '职责';
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."job_competency" IS '任职资格';
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."job_title_remark" IS '备注';
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."updated_time" IS '修改时间';
COMMENT ON COLUMN "t_ryytn_oa_jobtitle"."sync_time" IS '同步时间，精确到毫秒';
COMMENT ON TABLE "t_ryytn_oa_jobtitle" IS 'OA同步岗位表，从OA系统同步';

DROP TABLE IF EXISTS "t_ryytn_oa_person";
CREATE TABLE "t_ryytn_oa_person" (
  "id" varchar(32) NOT NULL,
  "work_code" varchar(32),
  "last_name" varchar(32),
  "login_id" varchar(32),
  "account_type" int4,
  "be_long_to" varchar(32),
  "department_id" varchar(32),
  "job_title_id" varchar(32),
  "location_id" varchar(32),
  "status" int4,
  "language" varchar(32),
  "job_activity_desc" varchar(256),
  "job_level" varchar(32),
  "job_call" varchar(32),
  "manager_id" varchar(32),
  "assistant_id" varchar(32),
  "sex" varchar(10),
  "telephone" varchar(32),
  "mobile" varchar(32),
  "mobile_call" varchar(32),
  "email" varchar(32),
  "start_date" varchar(20),
  "end_date" varchar(20),
  "sec_level" varchar(10),
  "password" varchar(64),
  "certificate_num" varchar(32),
  "birthday" varchar(20),
  "height" varchar(10),
  "weight" varchar(10),
  "folk" varchar(32),
  "native_place" varchar(32),
  "health_info" varchar(32),
  "marital_status" varchar(32),
  "temp_resident_number" varchar(32),
  "resident_place" varchar(64),
  "regresident_place" varchar(64),
  "home_address" varchar(32),
  "policy" varchar(32),
  "be_member_date" varchar(20),
  "be_party_date" varchar(20),
  "degree" varchar(32),
  "education_level" varchar(10),
  "is_labouunion" int2  DEFAULT 0,
  "last_mod_date" varchar(20),
  "sort_no" double precision,
  "created_time" timestamp,
  "updated_time" timestamp,
  "sync_time" int8,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_oa_person"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_oa_person"."work_code" IS '编号';
COMMENT ON COLUMN "t_ryytn_oa_person"."last_name" IS '人员名称';
COMMENT ON COLUMN "t_ryytn_oa_person"."login_id" IS '登录名';
COMMENT ON COLUMN "t_ryytn_oa_person"."account_type" IS '主次账号标志：1：次账号,其他：主账号';
COMMENT ON COLUMN "t_ryytn_oa_person"."be_long_to" IS '主账号id （当accounttype 为 1 有效）';
COMMENT ON COLUMN "t_ryytn_oa_person"."department_id" IS '部门编号';
COMMENT ON COLUMN "t_ryytn_oa_person"."job_title_id" IS '岗位编号';
COMMENT ON COLUMN "t_ryytn_oa_person"."location_id" IS '办公地点';
COMMENT ON COLUMN "t_ryytn_oa_person"."status" IS '状态: 0 试用 1 正式 2 临时 3 试用延期 4 解聘 5 离职 6 退休 7 无效';
COMMENT ON COLUMN "t_ryytn_oa_person"."language" IS '系统语言';
COMMENT ON COLUMN "t_ryytn_oa_person"."job_activity_desc" IS '职责描述';
COMMENT ON COLUMN "t_ryytn_oa_person"."job_level" IS '职级';
COMMENT ON COLUMN "t_ryytn_oa_person"."job_call" IS '职称';
COMMENT ON COLUMN "t_ryytn_oa_person"."manager_id" IS '上级人员编号';
COMMENT ON COLUMN "t_ryytn_oa_person"."assistant_id" IS '助理人员编号';
COMMENT ON COLUMN "t_ryytn_oa_person"."sex" IS '性别';
COMMENT ON COLUMN "t_ryytn_oa_person"."telephone" IS '办公电话';
COMMENT ON COLUMN "t_ryytn_oa_person"."mobile" IS '移动电话';
COMMENT ON COLUMN "t_ryytn_oa_person"."mobile_call" IS '其他电话';
COMMENT ON COLUMN "t_ryytn_oa_person"."email" IS '邮箱';
COMMENT ON COLUMN "t_ryytn_oa_person"."start_date" IS '合同开始日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN "t_ryytn_oa_person"."end_date" IS '合同结束日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN "t_ryytn_oa_person"."sec_level" IS '安全级别';
COMMENT ON COLUMN "t_ryytn_oa_person"."password" IS '密码，密文';
COMMENT ON COLUMN "t_ryytn_oa_person"."certificate_num" IS '身份证';
COMMENT ON COLUMN "t_ryytn_oa_person"."birthday" IS '生日，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN "t_ryytn_oa_person"."height" IS '身高';
COMMENT ON COLUMN "t_ryytn_oa_person"."weight" IS '体重';
COMMENT ON COLUMN "t_ryytn_oa_person"."folk" IS '民族';
COMMENT ON COLUMN "t_ryytn_oa_person"."native_place" IS '籍贯';
COMMENT ON COLUMN "t_ryytn_oa_person"."health_info" IS '健康状况';
COMMENT ON COLUMN "t_ryytn_oa_person"."marital_status" IS '婚姻状况';
COMMENT ON COLUMN "t_ryytn_oa_person"."temp_resident_number" IS '暂住证号码';
COMMENT ON COLUMN "t_ryytn_oa_person"."resident_place" IS '户口';
COMMENT ON COLUMN "t_ryytn_oa_person"."regresident_place" IS '户口所在地';
COMMENT ON COLUMN "t_ryytn_oa_person"."home_address" IS '家庭联系方式';
COMMENT ON COLUMN "t_ryytn_oa_person"."policy" IS '政治面貌';
COMMENT ON COLUMN "t_ryytn_oa_person"."be_member_date" IS '入团日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN "t_ryytn_oa_person"."be_party_date" IS '入党日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN "t_ryytn_oa_person"."degree" IS '学位';
COMMENT ON COLUMN "t_ryytn_oa_person"."education_level" IS '学历';
COMMENT ON COLUMN "t_ryytn_oa_person"."is_labouunion" IS '是否公会会员';
COMMENT ON COLUMN "t_ryytn_oa_person"."last_mod_date" IS '最后修改日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN "t_ryytn_oa_person"."sort_no" IS '排序';
COMMENT ON COLUMN "t_ryytn_oa_person"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_oa_person"."updated_time" IS '修改时间';
COMMENT ON COLUMN "t_ryytn_oa_person"."sync_time" IS '同步时间，精确到毫秒';
COMMENT ON TABLE "t_ryytn_oa_person" IS 'OA同步员工表，从OA系统同步';

DROP TABLE IF EXISTS "t_ryytn_oa_person_extend";
CREATE TABLE "t_ryytn_oa_person_extend" (
  "id" varchar(32) NOT NULL,
  "field_name" varchar(32) NOT NULL,
  "field_value" varchar(64) NOT NULL
);
CREATE INDEX "index_t_ryytn_oa_person_extend_id" ON "t_ryytn_oa_person_extend" USING btree (
  "id"
);
COMMENT ON COLUMN "t_ryytn_oa_person_extend"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_oa_person_extend"."field_name" IS '字段名';
COMMENT ON COLUMN "t_ryytn_oa_person_extend"."field_value" IS '字段值';
COMMENT ON TABLE "t_ryytn_oa_person_extend" IS 'OA同步员工扩展字段表';

DROP TABLE IF EXISTS "t_ryytn_oa_subcompany";
CREATE TABLE "t_ryytn_oa_subcompany" (
  "id" varchar(32) NOT NULL,
  "sub_company_name" varchar(32),
  "sub_company_desc" varchar(64),
  "sub_company_code" varchar(32),
  "sup_sub_com_id" varchar(32),
  "sup_sub_com_ids" varchar(2048),
  "level" int4 DEFAULT 0,
  "canceled" varchar(10),
  "sort_no" double precision,
  "created_time" timestamp,
  "updated_time" timestamp,
  "sync_time" int8,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."sub_company_name" IS '分部简称';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."sub_company_desc" IS '分部全称';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."sub_company_code" IS '分部编码';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."sup_sub_com_id" IS '上级分部id,0或者空为表示没有上级分部';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."sup_sub_com_ids" IS '所有上级分部id,英文逗号分隔';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."level" IS '分部层级，根据supSubComIds包含的英文逗号数量计算';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."canceled" IS '封存标志，1 封存，其他为未封存';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."sort_no" IS '排序';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."updated_time" IS '修改时间';
COMMENT ON COLUMN "t_ryytn_oa_subcompany"."sync_time" IS '同步时间，精确到毫秒';
COMMENT ON TABLE "t_ryytn_oa_subcompany" IS 'OA同步分部（公司）表，从OA系统同步';

DROP TABLE IF EXISTS "t_ryytn_oa_subcompany_extend";
CREATE TABLE "t_ryytn_oa_subcompany_extend" (
  "id" varchar(32) NOT NULL,
  "field_name" varchar(32) NOT NULL,
  "field_value" varchar(64) NOT NULL
);
CREATE INDEX "index_t_ryytn_oa_subcompany_extend_id" ON "t_ryytn_oa_subcompany_extend" USING btree (
  "id"
);
COMMENT ON COLUMN "t_ryytn_oa_subcompany_extend"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_oa_subcompany_extend"."field_name" IS '字段名';
COMMENT ON COLUMN "t_ryytn_oa_subcompany_extend"."field_value" IS '字段值';
COMMENT ON TABLE "t_ryytn_oa_subcompany_extend" IS 'OA同步分部（公司）扩展字段表';

DROP TABLE IF EXISTS "t_ryytn_page";
CREATE TABLE "t_ryytn_page" (
  "id" int8 NOT NULL,
  "name" varchar(64) NOT NULL,
  "alias" varchar(64),
  "permission" varchar(256),
  "parent_id" int8 NOT NULL,
  "parent_ids" varchar(2048) NOT NULL,
  "dependency_ids" varchar(1024),
  "type" varchar(32) NOT NULL,
  "path" varchar(256),
  "config_path" varchar(256),
  "component" varchar(256),
  "icon" varchar(256),
  "moudel_id" int8 NOT NULL,
  "sort_no" int4,
  "sum_flag" int2 DEFAULT '0',
  "description" varchar(256),
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_page"."id" IS '页面编号';
COMMENT ON COLUMN "t_ryytn_page"."name" IS '页面名称';
COMMENT ON COLUMN "t_ryytn_page"."alias" IS '页面别名';
COMMENT ON COLUMN "t_ryytn_page"."permission" IS '页面权限码，用于后端校验权限，英文逗号分隔';
COMMENT ON COLUMN "t_ryytn_page"."parent_id" IS '页面父编号，根页面为-1';
COMMENT ON COLUMN "t_ryytn_page"."parent_ids" IS '页面所有父编号，英文逗号分隔';
COMMENT ON COLUMN "t_ryytn_page"."dependency_ids" IS '页面依赖编号，英文逗号分隔';
COMMENT ON COLUMN "t_ryytn_page"."type" IS '页面类型，1：菜单';
COMMENT ON COLUMN "t_ryytn_page"."path" IS '页面路由地址';
COMMENT ON COLUMN "t_ryytn_page"."config_path" IS '页面配置页路由地址';
COMMENT ON COLUMN "t_ryytn_page"."component" IS '组件路径';
COMMENT ON COLUMN "t_ryytn_page"."icon" IS '页面图标静态资源目录';
COMMENT ON COLUMN "t_ryytn_page"."moudel_id" IS '模块编号';
COMMENT ON COLUMN "t_ryytn_page"."sort_no" IS '页面排序';
COMMENT ON COLUMN "t_ryytn_page"."sum_flag" IS '是否开启合计，0：否，1：是，默认0';
COMMENT ON COLUMN "t_ryytn_page"."description" IS '描述';
COMMENT ON TABLE "t_ryytn_page" IS '页面表';

DROP TABLE IF EXISTS "t_ryytn_page_config";
CREATE TABLE "t_ryytn_page_config" (
  "id" bigserial NOT NULL,
  "page_id" int8 NOT NULL,
  "row_name" varchar(64),
  "row_field" varchar(64),
  "width" int4,
  "sort_no" int4,
  "freeze_flag" int2 DEFAULT '0',
  "show_flag" int2 DEFAULT '0',
  "gather_flag" int2 DEFAULT '0',
  "created_by" varchar(64),
  "created_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(64),
  "updated_time" timestamp,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_page_config"."id" IS '配置编号';
COMMENT ON COLUMN "t_ryytn_page_config"."page_id" IS '页面编号';
COMMENT ON COLUMN "t_ryytn_page_config"."row_name" IS '列名称';
COMMENT ON COLUMN "t_ryytn_page_config"."row_field" IS '列字段名';
COMMENT ON COLUMN "t_ryytn_page_config"."width" IS '列宽度';
COMMENT ON COLUMN "t_ryytn_page_config"."sort_no" IS '列顺序';
COMMENT ON COLUMN "t_ryytn_page_config"."freeze_flag" IS '列冻结/解冻，0：解冻，1：解冻，默认0';
COMMENT ON COLUMN "t_ryytn_page_config"."show_flag" IS '列展示/隐藏，0：展示，1：隐藏，默认0';
COMMENT ON COLUMN "t_ryytn_page_config"."gather_flag" IS '列聚合，0：不聚合，1：聚合，默认0';
COMMENT ON COLUMN "t_ryytn_page_config"."created_by" IS '创建人(登录账号)';
COMMENT ON COLUMN "t_ryytn_page_config"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_page_config"."updated_by" IS '修改人(登录账号)';
COMMENT ON COLUMN "t_ryytn_page_config"."updated_time" IS '修改时间';
COMMENT ON TABLE "t_ryytn_page_config" IS '页面配置表';


DROP TABLE IF EXISTS "t_ryytn_role";
CREATE TABLE "t_ryytn_role" (
  "id" int8 NOT NULL,
  "name" varchar(64) NOT NULL,
  "default_flag" int2 DEFAULT '0',
  "status" int4 NOT NULL DEFAULT '1',
  "description" varchar(256),
  "sort_no" int4,
  "data_type" int4 NOT NULL DEFAULT '2',
  "created_by" varchar(64),
  "created_time" timestamp DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(64),
  "updated_time" timestamp,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_role"."id" IS '角色编号，业务唯一约束';
COMMENT ON COLUMN "t_ryytn_role"."name" IS '角色名称';
COMMENT ON COLUMN "t_ryytn_role"."default_flag" IS '是否默认角色，0：不是默认角色，1：是默认角色';
COMMENT ON COLUMN "t_ryytn_role"."status" IS '状态，1：正常，2：禁用，默认值：1';
COMMENT ON COLUMN "t_ryytn_role"."description" IS '描述';
COMMENT ON COLUMN "t_ryytn_role"."sort_no" IS '排序';
COMMENT ON COLUMN "t_ryytn_role"."data_type" IS '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2';
COMMENT ON COLUMN "t_ryytn_role"."created_by" IS '创建人(登录账号)';
COMMENT ON COLUMN "t_ryytn_role"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_role"."updated_by" IS '修改人(登录账号)';
COMMENT ON COLUMN "t_ryytn_role"."updated_time" IS '修改时间';
COMMENT ON TABLE "t_ryytn_role" IS '角色表';

DROP TABLE IF EXISTS "t_ryytn_role_button";
CREATE TABLE "t_ryytn_role_button" (
  "role_id" int8 NOT NULL,
  "button_id" int8 NOT NULL,
  CONSTRAINT "uk_t_ryytn_role_button" UNIQUE ("role_id", "button_id")
);
COMMENT ON COLUMN "t_ryytn_role_button"."role_id" IS '角色编号';
COMMENT ON COLUMN "t_ryytn_role_button"."button_id" IS '菜单编号';
COMMENT ON TABLE "t_ryytn_role_button" IS '角色按钮关联关系表';

DROP TABLE IF EXISTS "t_ryytn_role_channel";
CREATE TABLE "t_ryytn_role_channel" (
  "role_id" int8 NOT NULL,
  "channel_id" varchar(32) NOT NULL,
  CONSTRAINT "uk_t_ryytn_role_channel" UNIQUE ("role_id", "channel_id")
);
COMMENT ON COLUMN "t_ryytn_role_channel"."role_id" IS '角色编号';
COMMENT ON COLUMN "t_ryytn_role_channel"."channel_id" IS '渠道编号';
COMMENT ON TABLE "t_ryytn_role_channel" IS '角色渠道关联关系表';

DROP TABLE IF EXISTS "t_ryytn_role_depository";
CREATE TABLE "t_ryytn_role_depository" (
  "role_id" int8 NOT NULL,
  "depository_id" varchar(32) NOT NULL,
  CONSTRAINT "uk_t_ryytn_role_depository" UNIQUE ("role_id", "depository_id")
);
COMMENT ON COLUMN "t_ryytn_role_depository"."role_id" IS '角色编号';
COMMENT ON COLUMN "t_ryytn_role_depository"."depository_id" IS '仓库编号';
COMMENT ON TABLE "t_ryytn_role_depository" IS '角色仓库关联关系表';

DROP TABLE IF EXISTS "t_ryytn_role_factory";
CREATE TABLE "t_ryytn_role_factory" (
  "role_id" int8 NOT NULL,
  "factory_id" varchar(32) NOT NULL,
  CONSTRAINT "uk_t_ryytn_role_factory" UNIQUE ("role_id", "factory_id")
);
COMMENT ON COLUMN "t_ryytn_role_factory"."role_id" IS '角色编号';
COMMENT ON COLUMN "t_ryytn_role_factory"."factory_id" IS '工厂编号';
COMMENT ON TABLE "t_ryytn_role_factory" IS '角色工厂关联关系表';

DROP TABLE IF EXISTS "t_ryytn_role_page";
CREATE TABLE "t_ryytn_role_page" (
  "role_id" int8 NOT NULL,
  "page_id" int8 NOT NULL,
  CONSTRAINT "uk_t_ryytn_role_page" UNIQUE ("role_id", "page_id")
);
COMMENT ON COLUMN "t_ryytn_role_page"."role_id" IS '角色编号';
COMMENT ON COLUMN "t_ryytn_role_page"."page_id" IS '菜单编号';
COMMENT ON TABLE "t_ryytn_role_page" IS '角色菜单关联关系表';

DROP TABLE IF EXISTS "t_ryytn_role_productcategory";
CREATE TABLE "t_ryytn_role_productcategory" (
  "role_id" int8 NOT NULL,
  "category_id" varchar(32) NOT NULL,
  CONSTRAINT "uk_t_ryytn_role_productcategory" UNIQUE ("role_id", "category_id")
);
COMMENT ON COLUMN "t_ryytn_role_productcategory"."role_id" IS '角色编号';
COMMENT ON COLUMN "t_ryytn_role_productcategory"."category_id" IS '产品品类编号';
COMMENT ON TABLE "t_ryytn_role_productcategory" IS '角色产品品类关联关系表';

DROP TABLE IF EXISTS "t_ryytn_sku_lock";
CREATE TABLE "t_ryytn_sku_lock" (
  "id" int8 NOT NULL,
  "sku_code" varchar(255),
  "sku_name" varchar(255),
  "lv1_category_code" varchar(64),
  "lv1_category_name" varchar(64),
  "lv2_category_code" varchar(64),
  "lv2_category_name" varchar(64),
  "lv3_category_code" varchar(64),
  "lv3_category_name" varchar(64),
  "lock_start_date" varchar(10),
  "lock_end_date" varchar(10),
  "lock_start_week" varchar(10),
  "lock_end_week" varchar(10),
  "created_by" varchar(64),
  "created_time" timestamp DEFAULT CURRENT_TIMESTAMP,
  "updated_time" timestamp,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_sku_lock"."id" IS '主键';
COMMENT ON COLUMN "t_ryytn_sku_lock"."sku_code" IS '产品编码';
COMMENT ON COLUMN "t_ryytn_sku_lock"."sku_name" IS '产品简称';
COMMENT ON COLUMN "t_ryytn_sku_lock"."lv1_category_code" IS '一级分类编码';
COMMENT ON COLUMN "t_ryytn_sku_lock"."lv1_category_name" IS '一级分类名称';
COMMENT ON COLUMN "t_ryytn_sku_lock"."lv2_category_code" IS '二级分类编码';
COMMENT ON COLUMN "t_ryytn_sku_lock"."lv2_category_name" IS '二级分类名称';
COMMENT ON COLUMN "t_ryytn_sku_lock"."lv3_category_code" IS '三级分类编码';
COMMENT ON COLUMN "t_ryytn_sku_lock"."lv3_category_name" IS '三级分类名称';
COMMENT ON COLUMN "t_ryytn_sku_lock"."lock_start_date" IS '锁定期开始时间 yyyyMMdd';
COMMENT ON COLUMN "t_ryytn_sku_lock"."lock_end_date" IS '锁定期结束时间 yyyyMMdd';
COMMENT ON COLUMN "t_ryytn_sku_lock"."lock_start_week" IS '锁定期开始时间 yyyy/MM/WW';
COMMENT ON COLUMN "t_ryytn_sku_lock"."lock_end_week" IS '锁定期结束时间 yyyy/MM/WW';
COMMENT ON COLUMN "t_ryytn_sku_lock"."created_by" IS '创建人';
COMMENT ON COLUMN "t_ryytn_sku_lock"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_sku_lock"."updated_time" IS '更新时间';

DROP TABLE IF EXISTS "t_ryytn_sku_lock_channel";
CREATE TABLE "t_ryytn_sku_lock_channel" (
  "lock_id" int8 NOT NULL,
  "channel_id" varchar(64) NOT NULL,
  "channel_name" varchar(64)
);
CREATE INDEX "index_t_ryytn_sku_lock_channel_lockId_channelId" ON "t_ryytn_sku_lock_channel" USING btree (
  "lock_id",
  "channel_id"
);
COMMENT ON COLUMN "t_ryytn_sku_lock_channel"."lock_id" IS '主键';
COMMENT ON COLUMN "t_ryytn_sku_lock_channel"."channel_id" IS '锁定渠道（二级）';
COMMENT ON COLUMN "t_ryytn_sku_lock_channel"."channel_name" IS '锁定渠道名称（二级）';

DROP TABLE IF EXISTS "t_ryytn_thirdparty_system";
CREATE TABLE "t_ryytn_thirdparty_system" (
  "id" int8 NOT NULL,
  "name" varchar(64) NOT NULL,
  "auth_code" varchar(64) NOT NULL,
  "url" varchar(1024),
  "status" int4 NOT NULL DEFAULT '1',
  "description" varchar(256),
  PRIMARY KEY ("id")
);
CREATE INDEX "index_t_ryytn_thirdparty_system_name" ON "t_ryytn_thirdparty_system" USING btree (
  "name"
);
COMMENT ON COLUMN "t_ryytn_thirdparty_system"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_thirdparty_system"."name" IS '名称';
COMMENT ON COLUMN "t_ryytn_thirdparty_system"."auth_code" IS '授权码';
COMMENT ON COLUMN "t_ryytn_thirdparty_system"."url" IS '入口页面跳转地址';
COMMENT ON COLUMN "t_ryytn_thirdparty_system"."status" IS '状态，1：正常，2：不正常（扩展字段）';
COMMENT ON COLUMN "t_ryytn_thirdparty_system"."description" IS '描述';
COMMENT ON TABLE "t_ryytn_thirdparty_system" IS '第三方系统表';

CREATE TABLE t_ryytn_distribute_plan_valid_rule
(
    id              int8      NOT NULL,
    name            VARCHAR(200),
    range_type      int2,
    start_time      date,
    end_time        date,
    forever_flag    int2,
    distribute_type int2,
    created_by      varchar(64),
    created_time    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by      varchar(64),
    updated_time    timestamp,
    PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."id" IS '主键ID';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."name" IS '规则名称';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."range_type" IS '范围类型(0:产品，1:品类，2：全部)';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."start_time" IS '生效时间';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."end_time" IS '失效时间';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."forever_flag" IS '是否永久生效 0:否，1：是';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."distribute_type" IS '0:TOB业务，1:TOC业务';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."created_by" IS '创建者';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."updated_by" IS '更新者';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule"."updated_time" IS '更新时间';
COMMENT ON TABLE "t_ryytn_distribute_plan_valid_rule" IS '效期分档规则-主表';

CREATE TABLE t_ryytn_distribute_plan_valid_rule_range
(
    id        int8 NOT NULL,
    rule_id   BIGINT,
    name      VARCHAR(64),
    start_day int4,
    end_day   int4,
    ratio     float8,
    PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range"."id" IS '主键ID';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range"."rule_id" IS '效期分档规则id';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range"."name" IS '标签名称';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range"."start_day" IS '分档起始天数';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range"."end_day" IS '效期结束天数';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range"."ratio" IS '效期需求占比';
COMMENT ON TABLE "t_ryytn_distribute_plan_valid_rule_range" IS '效期分档子表';

CREATE TABLE t_ryytn_distribute_plan_valid_rule_range_product
(
    id       int8 NOT NULL,
    rule_id  int8,
    sku_code VARCHAR(32),
    sku_name VARCHAR(64),
    PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_product"."id" IS '主键ID';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_product"."rule_id" IS '效期分档规则id';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_product"."sku_code" IS '产品编码';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_product"."sku_name" IS '产品名称';
COMMENT ON TABLE "t_ryytn_distribute_plan_valid_rule_range_product" IS '适用范围产品-子表';

CREATE TABLE t_ryytn_distribute_plan_valid_rule_range_category
(
    id            int8 NOT NULL,
    rule_id       int8,
    category_code VARCHAR(32),
    category_name VARCHAR(64),
    level   int2,
    sku_codes VARCHAR(100),
    PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_category"."id" IS '主键ID';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_category"."rule_id" IS '效期分档规则id';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_category"."category_code" IS '品类编码';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_category"."category_name" IS '品类名称';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_category"."category_name" IS '品类名称';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_category"."level" IS '品类等级';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_category"."sku_codes" IS '产品编码集合';
COMMENT ON TABLE "t_ryytn_distribute_plan_valid_rule_range_category" IS '适用范围品类-子表';

CREATE TABLE t_ryytn_distribute_plan_warehouse_rule
(
    id              int8      NOT NULL,
    name            VARCHAR(200),
    range_type      int2,
    start_time      date,
    end_time        date,
    forever_flag    int2,
    created_by      varchar(64),
    created_time    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by      varchar(64),
    updated_time    timestamp,
    PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule"."id" IS '主键ID';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule"."name" IS '规则名称';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule"."range_type" IS '范围类型(0:产品，1:品类，2：全部)';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule"."start_time" IS '生效时间';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule"."end_time" IS '失效时间';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule"."forever_flag" IS '是否永久生效 0:否，1：是';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule"."created_by" IS '创建者';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule"."updated_by" IS '更新者';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule"."updated_time" IS '更新时间';
COMMENT ON TABLE "t_ryytn_distribute_plan_warehouse_rule" IS '仓能力规则-主表';

CREATE TABLE t_ryytn_distribute_plan_warehouse_rule_capacity
(
    id        int8 NOT NULL,
    rule_id   int8,
    warehouse_code      VARCHAR(32),
    warehouse_name VARCHAR(64),
    capacity int4,
    capacity_flag int2,
    delivery_limit int4,
    delivery_limit_flag int2,
    delivery_unlimit_flag int2,
    shipment_limit int4,
    shipment_limit_flag int2,
    shipment_unlimit_flag int2,
    PRIMARY KEY ("id")
);

COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."id" IS '主键ID';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."rule_id" IS '仓能力规则id';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."warehouse_code" IS '仓库编码';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."warehouse_name" IS '仓库名称';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."capacity" IS '库容（提/罐）';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."capacity_flag" IS '是否开启库容';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."delivery_limit" IS '收货能力上限（提/罐）';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."delivery_limit_flag" IS '是否开启收货能力上限';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."delivery_unlimit_flag" IS '是否设置收货无限能力';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."shipment_limit" IS '出库能力上限（提/罐）';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."shipment_limit_flag" IS '是否开启出库能力上限';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity"."shipment_unlimit_flag" IS '是否设置出库无限能力';
COMMENT ON TABLE "t_ryytn_distribute_plan_warehouse_rule_capacity" IS '仓能力子表';
CREATE TABLE t_ryytn_distribute_plan_warehouse_rule_capacity_product
(
    id       int8 NOT NULL,
    rule_id  int8,
    sku_code VARCHAR(32),
    sku_name VARCHAR(64),
    PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_product"."id" IS '主键ID';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_product"."rule_id" IS '仓能力规则id';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_product"."sku_code" IS '产品编码';
COMMENT ON COLUMN "t_ryytn_distribute_plan_valid_rule_range_product"."sku_name" IS '产品名称';
COMMENT ON TABLE "t_ryytn_distribute_plan_valid_rule_range_product" IS '适用范围产品-子表';
CREATE TABLE t_ryytn_distribute_plan_warehouse_rule_capacity_category
(
    id            int8 NOT NULL,
    rule_id       int8,
    category_code VARCHAR(32),
    category_name VARCHAR(64),
    level   int2,
    sku_codes VARCHAR(100),
    PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity_category"."id" IS '主键ID';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity_category"."rule_id" IS '仓能力规则id';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity_category"."category_code" IS '品类编码';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity_category"."category_name" IS '品类名称';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity_category"."level" IS '品类等级';
COMMENT ON COLUMN "t_ryytn_distribute_plan_warehouse_rule_capacity_category"."sku_codes" IS '产品编码集合';
COMMENT ON TABLE "t_ryytn_distribute_plan_warehouse_rule_capacity_category" IS '适用范围品类-子表';
CREATE TABLE t_ryytn_distribute_plan_inventory_strategy_conf
(
    id            int8      NOT NULL,
    config_name   VARCHAR(32),
    config_value  VARCHAR(32),
    remark        VARCHAR(64),
    created_by    varchar(64),
    created_time  timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by    varchar(64),
    updated_time  timestamp,
    PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy_conf"."id" IS '主键ID';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy_conf"."config_name" IS '配置项';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy_conf"."config_value" IS '配置值';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy_conf"."remark" IS '备注';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy_conf"."created_by" IS '创建人';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy_conf"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy_conf"."updated_by" IS '更新人';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy_conf"."updated_time" IS '更新时间';
COMMENT ON TABLE "t_ryytn_distribute_plan_inventory_strategy_conf" IS '库存策略-配置';
CREATE TABLE t_ryytn_distribute_plan_inventory_strategy
(
    id                            int8 NOT NULL,
    warehouse_code                VARCHAR(32),
    warehouse_name                VARCHAR(32),
    warehouse_type                VARCHAR(32),
    sku_code                      VARCHAR(32),
    sku_name                      varchar(64),
    sku_name_simple               varchar(64),
    inventory_safe_day            int4,
    inventory_safe_amount         int4,
    inventory_safe_amount_adv     int4,
    inventory_turnover_day        int4,
    inventory_turnover_amount     int4,
    inventory_target_day_adv      int4,
    inventory_target_amount_adv   int4,
    inventory_target_day          int4,
    inventory_target_amount       int4,
    sales_daily                   int4,
    special_strategy_flag         int2,
    inventory_safe_day_spec       int4,
    inventory_turnover_day_spec   int4,
    start_time_strategy   timestamp,
    end_time_strategy   timestamp,
    created_by   VARCHAR(64),
    created_time  timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by  VARCHAR(64),
    updated_time  timestamp,
    PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."id" IS '主键ID';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."warehouse_code" IS '仓库编码';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."warehouse_name" IS '仓库名称';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."warehouse_type" IS '仓库类别';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."sku_code" IS '产品编码';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."sku_name" IS '产品名称';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."sku_name_simple" IS '产品简称';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_safe_day" IS '安全库存天数';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_safe_amount" IS '安全库存数量';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_safe_amount_adv" IS '建议安全库存数量';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_turnover_day" IS '周转库存天数';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_turnover_amount" IS '周转库存数量';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_target_day_adv" IS '建议目标库存天数';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_target_amount_adv" IS '建议目标库存数量';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_target_day" IS '目标库存天数';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_target_amount" IS '目标库存数量';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."sales_daily" IS '日均销量';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."special_strategy_flag" IS '特殊策略开启标识';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_safe_day_spec" IS '特殊安全库存天数';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."inventory_turnover_day_spec" IS '特殊周转库存天数';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."start_time_strategy" IS '策略生效时间起';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."end_time_strategy" IS '策略生效时间止';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."created_by" IS '创建人';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."created_time" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."updated_by" IS '更新人';
COMMENT ON COLUMN "t_ryytn_distribute_plan_inventory_strategy"."updated_time" IS '更新时间';
COMMENT ON TABLE "t_ryytn_distribute_plan_inventory_strategy" IS '库存策略数据表';
CREATE TABLE so_wt_stock_capacity
(
    rule_id  int8,
    stock_point_id   VARCHAR(32),
    stock_point_name  VARCHAR(64),
    item_id        VARCHAR(32),
    item_name    varchar(64),
    group_id  VARCHAR(32),
    type    int2,
    capacity  double precision,
    remark VARCHAR(64),
    status int2
);
COMMENT ON COLUMN "so_wt_stock_capacity"."rule_id" IS '规则id';
COMMENT ON COLUMN "so_wt_stock_capacity"."stock_point_id" IS '库存点编码';
COMMENT ON COLUMN "so_wt_stock_capacity"."stock_point_name" IS '库存点名称';
COMMENT ON COLUMN "so_wt_stock_capacity"."item_id" IS '物品编码 不填写表示全部物品';
COMMENT ON COLUMN "so_wt_stock_capacity"."item_name" IS '物品名称';
COMMENT ON COLUMN "so_wt_stock_capacity"."group_id" IS '分组编码';
COMMENT ON COLUMN "so_wt_stock_capacity"."type" IS '1-库存容量；2-出库能力；3-入库能力';
COMMENT ON COLUMN "so_wt_stock_capacity"."capacity" IS '量';
COMMENT ON COLUMN "so_wt_stock_capacity"."remark" IS '备注';
COMMENT ON COLUMN "so_wt_stock_capacity"."status" IS '状态';
COMMENT ON TABLE "so_wt_stock_capacity" IS '库存能力';
CREATE TABLE so_ss_service_level
(
    stock_point_id   VARCHAR(32),
    stock_point_name  VARCHAR(64),
    item_id        VARCHAR(32),
    item_name    varchar(64),
    service_level  double precision,
    remark VARCHAR(64),
    status int2
);
COMMENT ON COLUMN "so_ss_service_level"."stock_point_id" IS '库存点编码';
COMMENT ON COLUMN "so_ss_service_level"."stock_point_name" IS '库存点名称';
COMMENT ON COLUMN "so_ss_service_level"."item_id" IS '物品编码';
COMMENT ON COLUMN "so_ss_service_level"."item_name" IS '物品名称';
COMMENT ON COLUMN "so_ss_service_level"."service_level" IS '服务水平 默认0.95';
COMMENT ON COLUMN "so_ss_service_level"."remark" IS '备注';
COMMENT ON COLUMN "so_ss_service_level"."status" IS '状态';
COMMENT ON TABLE "so_ss_service_level" IS '服务水平';
CREATE TABLE so_ss_average_demand
(
    stock_point_id   VARCHAR(32),
    stock_point_name  VARCHAR(64),
    item_id        VARCHAR(32),
    item_name    varchar(64),
    average_qty  double precision,
    remark VARCHAR(64),
    status int2
);
COMMENT ON COLUMN "so_ss_average_demand"."stock_point_id" IS '库存点编码';
COMMENT ON COLUMN "so_ss_average_demand"."stock_point_name" IS '库存点名称';
COMMENT ON COLUMN "so_ss_average_demand"."item_id" IS '物品编码';
COMMENT ON COLUMN "so_ss_average_demand"."item_name" IS '物品名称';
COMMENT ON COLUMN "so_ss_average_demand"."average_qty" IS '日均量 默认0';
COMMENT ON COLUMN "so_ss_average_demand"."remark" IS '备注';
COMMENT ON COLUMN "so_ss_average_demand"."status" IS '状态';
COMMENT ON TABLE "so_ss_average_demand" IS '日均需求';

CREATE TABLE so_wt_safety_stock
(
    stock_point_id   VARCHAR(32),
    stock_point_name  VARCHAR(64),
    item_id        VARCHAR(32),
    item_name    varchar(64),
    safety_stock  double precision,
    target_stock  double precision,
    remark VARCHAR(64),
    status int2
);
COMMENT ON COLUMN "so_wt_safety_stock"."stock_point_id" IS '库存点编码';
COMMENT ON COLUMN "so_wt_safety_stock"."stock_point_name" IS '库存点名称';
COMMENT ON COLUMN "so_wt_safety_stock"."item_id" IS '物品编码';
COMMENT ON COLUMN "so_wt_safety_stock"."item_name" IS '物品名称';
COMMENT ON COLUMN "so_wt_safety_stock"."safety_stock" IS '安全库存';
COMMENT ON COLUMN "so_wt_safety_stock"."target_stock" IS '目标库存';
COMMENT ON COLUMN "so_wt_safety_stock"."remark" IS '备注';
COMMENT ON COLUMN "so_wt_safety_stock"."status" IS '状态';
COMMENT ON TABLE "so_wt_safety_stock" IS '安全库存';


DROP TABLE IF EXISTS "t_ryytn_channel_demand_plan_data_sync";
CREATE TABLE "t_ryytn_channel_demand_plan_data_sync"(
  "id" serial NOT NULL,
  "demand_plan_code" varchar(32) NOT NULL,
  "version_id" varchar(32) NOT NULL,
  "version_name" varchar(64),
  "lv2_channel_code" varchar(64),
  "sku_code" varchar(255),
  "plan_date" varchar(64) NOT NULL,
  "plan_value" numeric(10,0) NOT NULL,
  "month_week" varchar(32),
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_data_sync"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_data_sync"."demand_plan_code" IS '计划编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_data_sync"."version_id" IS '滚动版本号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_data_sync"."version_name" IS '版本名称，格式：计划编号-滚动版本号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_data_sync"."lv2_channel_code" IS '二级渠道编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_data_sync"."sku_code" IS 'SKU产品编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_data_sync"."plan_date" IS '时间，格式yyyyMMdd，二级渠道为生产计划部时放周的第一天，其他二级渠道放周的最后一天';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_data_sync"."plan_value" IS '需求数据';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_data_sync"."month_week" IS '月份周数，格式MMWn';
COMMENT ON TABLE "t_ryytn_channel_demand_plan_data_sync" IS '渠道需求计划数据同步中间表';




DROP TABLE IF EXISTS "t_ryytn_warehouse_demand_report";
CREATE TABLE "t_ryytn_warehouse_demand_report" (
  "id" int8 NOT NULL,
  "name" varchar(64) NOT NULL,
  "demand_plan_code" varchar(32) NOT NULL,
  "rolling_version" varchar(32) NOT NULL,
  "sku_code" varchar(255),
  "sku_name" varchar(255),
  "lv1_category_code" varchar(64),
  "lv1_category_name" varchar(64),
  "lv2_category_code" varchar(64),
  "lv2_category_name" varchar(64),
  "lv3_category_code" varchar(64),
  "lv3_category_name" varchar(64),
  "lv1_channel_code" varchar(64),
  "lv1_channel_name" varchar(64),
  "lv2_channel_code" varchar(64),
  "lv2_channel_name" varchar(64),
  "lv3_channel_code" varchar(64),
  "lv3_channel_name" varchar(64),
  "lv3_channel_type" varchar(64),
  "warehouse_code" varchar(64),
  "warehouse_name" varchar(64),
  "biz_date_type" varchar(32) NOT NULL,
  "biz_date_value" varchar(64) NOT NULL,
  "order_num" numeric(10,0) NOT NULL,
  "unit" varchar(3),
  "deviation_radio" numeric(10,2) default null,
  "remark" varchar(256),
  "extend" varchar(1024),
  "is_modify" int2,
  "is_delete" int2 default 0,
  "creator" varchar(32),
  "last_modifier" varchar(32),
  "gmt_create" timestamp(0),
  "gmt_modify" timestamp(0),
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."name" IS '名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."demand_plan_code" IS '需求计划编号，继承渠道需求计划编号';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."rolling_version" IS '需求计划版本号，继承渠道需求计划版本号';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."sku_code" IS 'skuCode';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."sku_name" IS 'sku名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv1_category_code" IS '产品分类编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv1_category_name" IS '产品分类名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv2_category_code" IS '产品大类编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv2_category_name" IS '产品大类名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv3_category_code" IS '产品小类编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv3_category_name" IS '产品小类名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv1_channel_code" IS '一级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv1_channel_name" IS '一级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv2_channel_code" IS '二级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv2_channel_name" IS '二级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv3_channel_code" IS '三级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv3_channel_name" IS '三级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."lv3_channel_type" IS '三级渠道类型';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."warehouse_code" IS '仓库编号';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."warehouse_name" IS '仓库名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."biz_date_type" IS '时间类型:DAY,WEEK，MONTH,YEAR';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."biz_date_value" IS '时间类型值,日：20230101;周:0230103;月:202301';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."order_num" IS '订单数量/订单金额';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."unit" IS '计量单位:件/瓶/吨ml/元';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."deviation_radio" IS '渠道需求提报二级渠道数据偏差率';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."remark" IS '备注';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."extend" IS '扩展字段';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."is_modify" IS '是否调整：0为否;1为是，默认0';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."is_delete" IS '是否删除：0为否;1为是，默认0';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."creator" IS '创建人';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."last_modifier" IS '最后修改人';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."gmt_create" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report"."gmt_modify" IS '修改时间';
COMMENT ON TABLE "t_ryytn_warehouse_demand_report" IS '分仓需求提报数据表';


DROP TABLE IF EXISTS "t_ryytn_warehouse_demand_report_history" cascade;
CREATE TABLE "t_ryytn_warehouse_demand_report_history" (
  "id" bigserial NOT NULL,
  "name" varchar(64) NOT NULL,
  "demand_plan_code" varchar(32) NOT NULL,
  "rolling_version" varchar(32) NOT NULL,
  "sku_code" varchar(255),
  "sku_name" varchar(255),
  "lv1_category_code" varchar(64),
  "lv1_category_name" varchar(64),
  "lv2_category_code" varchar(64),
  "lv2_category_name" varchar(64),
  "lv3_category_code" varchar(64),
  "lv3_category_name" varchar(64),
  "lv1_channel_code" varchar(64),
  "lv1_channel_name" varchar(64),
  "lv2_channel_code" varchar(64),
  "lv2_channel_name" varchar(64),
  "lv3_channel_code" varchar(64),
  "lv3_channel_name" varchar(64),
  "receiver_type" varchar(64),
  "warehouse_code" varchar(64),
  "warehouse_name" varchar(64),
  "biz_date_type" varchar(32) NOT NULL,
  "biz_date_value" varchar(64) NOT NULL,
  "order_num" numeric(10,2) NOT NULL,
  "old_order_num" numeric(10,2) NOT NULL,
  "unit" varchar(3),
  "deviation_radio" numeric(10,0) default 0,
  "remark" varchar(256),
  "extend" varchar(1024),
  "last_modifier" varchar(32),
  "gmt_modify" timestamp(0),
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."name" IS '名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."demand_plan_code" IS '需求计划编号，继承渠道需求计划编号';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."rolling_version" IS '需求计划版本号，继承渠道需求计划版本号';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."sku_code" IS 'skuCode';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."sku_name" IS 'sku名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv1_category_code" IS '产品分类编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv1_category_name" IS '产品分类名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv2_category_code" IS '产品大类编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv2_category_name" IS '产品大类名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv3_category_code" IS '产品小类编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv3_category_name" IS '产品小类名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv1_channel_code" IS '一级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv1_channel_name" IS '一级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv2_channel_code" IS '二级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv2_channel_name" IS '二级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv3_channel_code" IS '三级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."lv3_channel_name" IS '三级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."receiver_type" IS '三级渠道类型';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."warehouse_code" IS '仓库编号';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."warehouse_name" IS '仓库名称';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."biz_date_type" IS '时间类型:DAY,WEEK，MONTH,YEAR';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."biz_date_value" IS '时间类型值,日：20230101;周:0230103;月:202301';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."order_num" IS '订单数量/订单金额';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."old_order_num" IS '旧订单数量/旧订单金额';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."unit" IS '计量单位:件/瓶/吨ml/元';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."deviation_radio" IS '渠道需求提报二级渠道数据偏差率';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."remark" IS '备注';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."extend" IS '扩展字段';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."last_modifier" IS '最后修改人';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_report_history"."gmt_modify" IS '修改时间';
COMMENT ON TABLE "t_ryytn_warehouse_demand_report_history" IS '分仓需求提报数据历史记录表';

   
drop table if exists "t_ryytn_warehouse_demand_plan_version";
CREATE TABLE "t_ryytn_warehouse_demand_plan_version" (
  "demand_plan_code" varchar(32) NOT NULL,
  "version_id" varchar(32) NOT null,
  CONSTRAINT "uniq_t_ryytn_warehouse_demand_plan_version" UNIQUE ("demand_plan_code", "version_id")
);
COMMENT ON COLUMN "t_ryytn_warehouse_demand_plan_version"."demand_plan_code" IS '需求计划编号，继承渠道需求计划编号';
COMMENT ON COLUMN "t_ryytn_warehouse_demand_plan_version"."version_id" IS '需求计划版本号，继承渠道需求计划版本号';
COMMENT ON TABLE "t_ryytn_warehouse_demand_plan_version" IS '分仓需求计划版本表';


DROP TABLE IF EXISTS "t_ryytn_dataq_task";
CREATE TABLE "t_ryytn_dataq_task" (
  "id" bigserial NOT NULL,
  "task_id" int8 default null,
  "job_id" int8 DEFAULT NULL,
  "app_code" varchar(64) DEFAULT NULL,
  "param" text default null,
  "lock_key" varchar(64) default null,
  "status" varchar(32) DEFAULT NULL,
  "reload_id" int8 default null,
  "start_time" timestamp DEFAULT CURRENT_TIMESTAMP,
  "end_time" timestamp,
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_dataq_task"."id" IS '自增主键';
COMMENT ON COLUMN "t_ryytn_dataq_task"."task_id" IS '阿里dataq任务实例编号';
COMMENT ON COLUMN "t_ryytn_dataq_task"."job_id" IS '业务应用任务编号';
COMMENT ON COLUMN "t_ryytn_dataq_task"."app_code" IS '阿里dataq任务应用编号';
COMMENT ON COLUMN "t_ryytn_dataq_task"."param" IS '阿里dataq任务请求参数';
COMMENT ON COLUMN "t_ryytn_dataq_task"."lock_key" IS '阿里dataq任务锁的key';
COMMENT ON COLUMN "t_ryytn_dataq_task"."status" IS '阿里dataq任务状态，INIT：初始化，RUNNING：执行中，STOPPING：停止中，STOPPED：已停止，SKIP：跳过，SUCCESS：成功，FAILED：失败';
COMMENT ON COLUMN "t_ryytn_dataq_task"."reload_id" IS '补偿机制重发请求的阿里dataq任务实例编号';
COMMENT ON COLUMN "t_ryytn_dataq_task"."start_time" IS '开始时间';
COMMENT ON COLUMN "t_ryytn_dataq_task"."end_time" IS '完成时间';
COMMENT ON TABLE "t_ryytn_dataq_task" IS '阿里dataq任务执行记录表';


DROP TABLE IF EXISTS "t_ryytn_cold_demand_report";
CREATE TABLE "t_ryytn_cold_demand_report" (
  "id" int8 NOT NULL,
  "rolling_version" varchar(32) NOT NULL,
  "sku_code" varchar(255),
  "sku_name" varchar(255),
  "lv1_category_code" varchar(64),
  "lv1_category_name" varchar(64),
  "lv2_category_code" varchar(64),
  "lv2_category_name" varchar(64),
  "lv3_category_code" varchar(64),
  "lv3_category_name" varchar(64),
  "lv1_channel_code" varchar(64),
  "lv1_channel_name" varchar(64),
  "lv2_channel_code" varchar(64),
  "lv2_channel_name" varchar(64),
  "lv3_channel_code" varchar(64),
  "lv3_channel_name" varchar(64),
  "lv3_channel_type" varchar(64),
  "biz_date_type" varchar(32) NOT NULL,
  "biz_date_value" varchar(64) NOT NULL,
  "order_num" numeric(10,0) NOT NULL,
  "unit" varchar(3),
  "last_order_num" numeric(10,0) default null,
  "deviation_radio" numeric(10,2) default null,
  "remark" varchar(256),
  "extend" varchar(1024),
  "is_modify" int2,
  "is_delete" int2 default 0,
  "creator" varchar(32),
  "last_modifier" varchar(32),
  "gmt_create" timestamp(0),
  "gmt_modify" timestamp(0),
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."rolling_version" IS '版本号，DDP+提报起始年份+提报起始月份+下周周一所属周数';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."sku_code" IS 'skuCode';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."sku_name" IS 'sku名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv1_category_code" IS '产品分类编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv1_category_name" IS '产品分类名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv2_category_code" IS '产品大类编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv2_category_name" IS '产品大类名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv3_category_code" IS '产品小类编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv3_category_name" IS '产品小类名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv1_channel_code" IS '一级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv1_channel_name" IS '一级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv2_channel_code" IS '二级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv2_channel_name" IS '二级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv3_channel_code" IS '三级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv3_channel_name" IS '三级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."lv3_channel_type" IS '三级渠道类型';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."biz_date_type" IS '时间类型:DAY,WEEK，MONTH,YEAR';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."biz_date_value" IS '时间类型值,日：20230101;周:0230103;月:202301';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."order_num" IS '订单数量/订单金额';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."unit" IS '计量单位:件/瓶/吨ml/元';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."last_order_num" IS '上一版本订单数量/订单金额';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."deviation_radio" IS '需求提报二级渠道数据偏差率';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."remark" IS '备注';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."extend" IS '扩展字段';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."is_modify" IS '是否调整：0为否;1为是，默认0';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."is_delete" IS '是否删除：0为否;1为是，默认0';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."creator" IS '创建人';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."last_modifier" IS '最后修改人';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."gmt_create" IS '创建时间';
COMMENT ON COLUMN "t_ryytn_cold_demand_report"."gmt_modify" IS '修改时间';
COMMENT ON TABLE "t_ryytn_cold_demand_report" IS '低温需求提报数据表';


DROP TABLE IF EXISTS "t_ryytn_cold_demand_report_history" cascade;
CREATE TABLE "t_ryytn_cold_demand_report_history" (
  "id" bigserial NOT NULL,
  "rolling_version" varchar(32) NOT NULL,
  "sku_code" varchar(255),
  "sku_name" varchar(255),
  "lv1_category_code" varchar(64),
  "lv1_category_name" varchar(64),
  "lv2_category_code" varchar(64),
  "lv2_category_name" varchar(64),
  "lv3_category_code" varchar(64),
  "lv3_category_name" varchar(64),
  "lv1_channel_code" varchar(64),
  "lv1_channel_name" varchar(64),
  "lv2_channel_code" varchar(64),
  "lv2_channel_name" varchar(64),
  "lv3_channel_code" varchar(64),
  "lv3_channel_name" varchar(64),
  "lv3_channel_type" varchar(64),
  "biz_date_type" varchar(32) NOT NULL,
  "biz_date_value" varchar(64) NOT NULL,
  "order_num" numeric(10,2) NOT NULL,
  "old_order_num" numeric(10,2) NOT NULL,
  "unit" varchar(3),
  "deviation_radio" numeric(10,0) default 0,
  "remark" varchar(256),
  "extend" varchar(1024),
  "last_modifier" varchar(32),
  "gmt_modify" timestamp(0),
  PRIMARY KEY ("id")
);
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."rolling_version" IS '版本号，DDP+提报起始年份+提报起始月份+下周周一所属周数';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."sku_code" IS 'skuCode';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."sku_name" IS 'sku名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv1_category_code" IS '产品分类编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv1_category_name" IS '产品分类名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv2_category_code" IS '产品大类编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv2_category_name" IS '产品大类名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv3_category_code" IS '产品小类编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv3_category_name" IS '产品小类名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv1_channel_code" IS '一级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv1_channel_name" IS '一级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv2_channel_code" IS '二级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv2_channel_name" IS '二级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv3_channel_code" IS '三级渠道类型编码';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv3_channel_name" IS '三级渠道类型名称';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."lv3_channel_type" IS '三级渠道类型';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."biz_date_type" IS '时间类型:DAY,WEEK，MONTH,YEAR';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."biz_date_value" IS '时间类型值,日：20230101;周:0230103;月:202301';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."order_num" IS '订单数量/订单金额';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."old_order_num" IS '旧订单数量/旧订单金额';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."unit" IS '计量单位:件/瓶/吨ml/元';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."deviation_radio" IS '渠道需求提报二级渠道数据偏差率';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."remark" IS '备注';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."extend" IS '扩展字段';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."last_modifier" IS '最后修改人';
COMMENT ON COLUMN "t_ryytn_cold_demand_report_history"."gmt_modify" IS '修改时间';
COMMENT ON TABLE "t_ryytn_cold_demand_report_history" IS '低温需求提报数据历史记录表';



	
drop table 	t_ryytn_channel_demand_plan_history ;	
CREATE TABLE cdop_sys.t_ryytn_channel_demand_plan_history (
	id serial NOT NULL,
	demand_plan_code varchar(32) NOT NULL,
	version_id varchar(32) NOT NULL,
	sku_code varchar(255) NULL,
	sku_name varchar(255) NULL,
	lv1_category_code varchar(64) NULL,
	lv1_category_name varchar(64) NULL,
	lv2_category_code varchar(64) NULL,
	lv2_category_name varchar(64) NULL,
	lv3_category_code varchar(64) NULL,
	lv3_category_name varchar(64) NULL,
	lv1_channel_code varchar(64) NULL,
	lv1_channel_name varchar(64) NULL,
	lv2_channel_code varchar(64) NULL,
	lv2_channel_name varchar(64) NULL,
	lv3_channel_code varchar(64) NULL,
	lv3_channel_name varchar(64) NULL,
	plan_date varchar(64) NOT NULL,
	plan_value numeric(10, 2) NOT NULL,
	old_plan_value numeric(10, 2) NULL DEFAULT 0,
	deviation_radio numeric(10) NULL DEFAULT 0,
	remark varchar(256) NULL,
	extend varchar(1024) NULL,
	last_modifier varchar(32) NULL,
	gmt_modify timestamp NULL,
	"group_id" int4 NULL,
	CONSTRAINT t_ryytn_channel_demand_plan_history_pkey PRIMARY KEY (id)
);
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."id" IS '编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."demand_plan_code" IS '计划编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."version_id" IS '版本号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."sku_code" IS '产品编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."sku_name" IS '产品名称';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv1_category_code" IS '一级品类编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv1_category_name" IS '一级品类名称';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv2_category_code" IS '二级品类编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv2_category_name" IS '二级品类名称';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv3_category_code" IS '三级品类编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv3_category_name" IS '三级品类名称';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv1_channel_code" IS '一级渠道编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv1_channel_name" IS '一级渠道名称';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv2_channel_code" IS '二级渠道编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv2_channel_name" IS '二级渠道名称';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv3_channel_code" IS '三级渠道编号';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."lv3_channel_name" IS '三级渠道名称';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."plan_date" IS '日期';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."plan_value" IS '修改后值';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."old_plan_value" IS '修改前值';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."deviation_radio" IS '偏差率';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."remark" IS '备注';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."extend" IS '扩展字段';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."last_modifier" IS '修改人';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."gmt_modify" IS '修改时间';
COMMENT ON COLUMN "t_ryytn_channel_demand_plan_history"."group_id" IS '分组编号';