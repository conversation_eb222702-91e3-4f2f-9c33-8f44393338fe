alter table t_ryytn_daily_warehouse_demand add status int4 default 1;
COMMENT ON COLUMN t_ryytn_daily_warehouse_demand.status is '数据状态：1：正常，2：生成中';



alter table t_ryytn_daily_warehouse_aiplan_demand add status int4 default 1;
COMMENT ON COLUMN t_ryytn_daily_warehouse_aiplan_demand.status is '数据状态：1：正常，2：生成中';



CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202401 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202401'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202401 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202402 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202402'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202402 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202403 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202403'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202403 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202404 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202404'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202404 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202405 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202405'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202405 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202406 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202406'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202406 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202407 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202407'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202407 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202408 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202408'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202408 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202409 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202409'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202409 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202410 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202410'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202410 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202411 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202411'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202411 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202412 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202412'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202412 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);

CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202501 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202501'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202501 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202502 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202502'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202502 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202503 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202503'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202503 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202504 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202504'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202504 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202505 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202505'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202505 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202506 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202506'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202506 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202507 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202507'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202507 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202508 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202508'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202508 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202509 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202509'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202509 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202510 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202510'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202510 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202511 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202511'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202511 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);
CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_202512 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202512'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_demand_202512 (id, demand_plan_code, demand_plan_name, demand_plan_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, date_recorded, date_value, week_recorded, week_raw_value, week_actual_value,status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.date_recorded, new.date_value, new.week_recorded, new.week_raw_value, new.week_actual_value,new.status);

  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202401 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202401'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202401 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202402 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202402'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202402 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202403 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202403'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202403 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202404 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202404'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202404 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202405 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202405'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202405 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202406 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202406'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202406 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202407 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202407'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202407 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202408 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202408'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202408 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202409 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202409'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202409 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202410 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202410'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202410 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202411 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202411'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202411 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202412 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202412'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202412 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202501 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202501'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202501 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202502 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202502'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202502 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202503 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202503'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202503 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202504 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202504'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202504 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202505 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202505'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202505 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202506 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202506'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202506 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202507 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202507'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202507 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202508 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202508'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202508 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202509 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202509'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202509 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202510 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202510'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202510 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202511 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202511'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202511 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  
  CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_202512 AS
    ON INSERT TO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand
   WHERE (substr((new.demand_plan_version)::text, 3, 6) = '202512'::text) DO INSTEAD  INSERT INTO cdop_sys.t_ryytn_daily_warehouse_aiplan_demand_202512 (id, demand_plan_code, demand_plan_name, demand_plan_version, aiplan_demand_version, sku_code, sku_name, distribute_type, warehouse_code, warehouse_name, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, validity_period, date_recorded, date_value, status)
  VALUES (new.id, new.demand_plan_code, new.demand_plan_name, new.demand_plan_version, new.aiplan_demand_version, new.sku_code, new.sku_name, new.distribute_type, new.warehouse_code, new.warehouse_name, new.lv1_category_code, new.lv1_category_name, new.lv2_category_code, new.lv2_category_name, new.lv3_category_code, new.lv3_category_name, new.validity_period, new.date_recorded, new.date_value,new.status);
  