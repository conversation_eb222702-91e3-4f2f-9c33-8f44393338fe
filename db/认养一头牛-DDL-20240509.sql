-- cdop_sys.qrtz_blob_triggers definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_blob_triggers;

CREATE TABLE cdop_sys.qrtz_blob_triggers (
	sched_name varchar(120) NOT NULL, -- 调度名称
	trigger_name varchar(200) NOT NULL, -- qrtz_triggers表trigger_name的外键
	trigger_group varchar(200) NOT NULL, -- qrtz_triggers表trigger_group的外键
	blob_data bytea NULL, -- 存放持久化Trigger对象
	CONSTRAINT qrtz_blob_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group)
);

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN cdop_sys.qrtz_blob_triggers.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_blob_triggers.trigger_name IS 'qrtz_triggers表trigger_name的外键';
COMMENT ON COLUMN cdop_sys.qrtz_blob_triggers.trigger_group IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON COLUMN cdop_sys.qrtz_blob_triggers.blob_data IS '存放持久化Trigger对象';


-- cdop_sys.qrtz_calendars definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_calendars;

CREATE TABLE cdop_sys.qrtz_calendars (
	sched_name varchar(120) NOT NULL, -- 调度名称
	calendar_name varchar(200) NOT NULL, -- 日历名称
	calendar bytea NOT NULL, -- 存放持久化calendar对象
	CONSTRAINT qrtz_calendars_pkey PRIMARY KEY (sched_name, calendar_name)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.qrtz_calendars.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_calendars.calendar_name IS '日历名称';
COMMENT ON COLUMN cdop_sys.qrtz_calendars.calendar IS '存放持久化calendar对象';


-- cdop_sys.qrtz_cron_triggers definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_cron_triggers;

CREATE TABLE cdop_sys.qrtz_cron_triggers (
	sched_name varchar(120) NOT NULL, -- 调度名称
	trigger_name varchar(200) NOT NULL, -- qrtz_triggers表trigger_name的外键
	trigger_group varchar(200) NOT NULL, -- qrtz_triggers表trigger_group的外键
	cron_expression varchar(200) NOT NULL, -- cron表达式
	time_zone_id varchar(80) NULL, -- 时区
	CONSTRAINT qrtz_cron_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.qrtz_cron_triggers.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_cron_triggers.trigger_name IS 'qrtz_triggers表trigger_name的外键';
COMMENT ON COLUMN cdop_sys.qrtz_cron_triggers.trigger_group IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON COLUMN cdop_sys.qrtz_cron_triggers.cron_expression IS 'cron表达式';
COMMENT ON COLUMN cdop_sys.qrtz_cron_triggers.time_zone_id IS '时区';


-- cdop_sys.qrtz_fired_triggers definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_fired_triggers;

CREATE TABLE cdop_sys.qrtz_fired_triggers (
	sched_name varchar(120) NOT NULL, -- 调度名称
	entry_id varchar(95) NOT NULL, -- 调度器实例id
	trigger_name varchar(200) NOT NULL, -- qrtz_triggers表trigger_name的外键
	trigger_group varchar(200) NOT NULL, -- qrtz_triggers表trigger_group的外键
	instance_name varchar(200) NOT NULL, -- 调度器实例名
	fired_time int8 NOT NULL, -- 触发的时间
	sched_time int8 NOT NULL, -- 定时器制定的时间
	priority int4 NOT NULL, -- 优先级
	state varchar(16) NOT NULL, -- 状态
	job_name varchar(200) NULL, -- 任务名称
	job_group varchar(200) NULL, -- 任务组名
	is_nonconcurrent bool NOT NULL, -- 是否并发
	requests_recovery bool NOT NULL, -- 是否接受恢复执行
	CONSTRAINT qrtz_fired_triggers_pkey PRIMARY KEY (sched_name, entry_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.entry_id IS '调度器实例id';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.trigger_name IS 'qrtz_triggers表trigger_name的外键';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.trigger_group IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.instance_name IS '调度器实例名';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.fired_time IS '触发的时间';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.sched_time IS '定时器制定的时间';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.priority IS '优先级';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.state IS '状态';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.job_name IS '任务名称';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.job_group IS '任务组名';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.is_nonconcurrent IS '是否并发';
COMMENT ON COLUMN cdop_sys.qrtz_fired_triggers.requests_recovery IS '是否接受恢复执行';


-- cdop_sys.qrtz_job_details definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_job_details;

CREATE TABLE cdop_sys.qrtz_job_details (
	sched_name varchar(120) NOT NULL, -- 调度名称
	job_name varchar(200) NOT NULL, -- 任务名称
	job_group varchar(200) NOT NULL, -- 任务组名
	description varchar(250) NULL, -- 相关介绍
	job_class_name varchar(250) NOT NULL, -- 执行任务类名称
	is_durable bool NOT NULL, -- 是否持久化
	is_nonconcurrent bool NOT NULL, -- 是否并发
	is_update_data bool NOT NULL, -- 是否更新数据
	requests_recovery bool NOT NULL, -- 是否接受恢复执行
	job_data bytea NULL, -- 存放持久化job对象
	CONSTRAINT qrtz_job_details_pkey PRIMARY KEY (sched_name, job_name, job_group)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.qrtz_job_details.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_job_details.job_name IS '任务名称';
COMMENT ON COLUMN cdop_sys.qrtz_job_details.job_group IS '任务组名';
COMMENT ON COLUMN cdop_sys.qrtz_job_details.description IS '相关介绍';
COMMENT ON COLUMN cdop_sys.qrtz_job_details.job_class_name IS '执行任务类名称';
COMMENT ON COLUMN cdop_sys.qrtz_job_details.is_durable IS '是否持久化';
COMMENT ON COLUMN cdop_sys.qrtz_job_details.is_nonconcurrent IS '是否并发';
COMMENT ON COLUMN cdop_sys.qrtz_job_details.is_update_data IS '是否更新数据';
COMMENT ON COLUMN cdop_sys.qrtz_job_details.requests_recovery IS '是否接受恢复执行';
COMMENT ON COLUMN cdop_sys.qrtz_job_details.job_data IS '存放持久化job对象';


-- cdop_sys.qrtz_locks definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_locks;

CREATE TABLE cdop_sys.qrtz_locks (
	sched_name varchar(120) NOT NULL, -- 调度名称
	lock_name varchar(40) NOT NULL, -- 悲观锁名称
	CONSTRAINT qrtz_locks_pkey PRIMARY KEY (sched_name, lock_name)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.qrtz_locks.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_locks.lock_name IS '悲观锁名称';


-- cdop_sys.qrtz_paused_trigger_grps definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_paused_trigger_grps;

CREATE TABLE cdop_sys.qrtz_paused_trigger_grps (
	sched_name varchar(120) NOT NULL, -- 调度名称
	trigger_group varchar(200) NOT NULL, -- qrtz_triggers表trigger_group的外键
	CONSTRAINT qrtz_paused_trigger_grps_pkey PRIMARY KEY (sched_name, trigger_group)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.qrtz_paused_trigger_grps.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_paused_trigger_grps.trigger_group IS 'qrtz_triggers表trigger_group的外键';


-- cdop_sys.qrtz_scheduler_state definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_scheduler_state;

CREATE TABLE cdop_sys.qrtz_scheduler_state (
	sched_name varchar(120) NOT NULL, -- 调度名称
	instance_name varchar(200) NOT NULL, -- 实例名称
	last_checkin_time int8 NOT NULL, -- 上次检查时间
	checkin_interval int8 NOT NULL, -- 检查间隔时间
	CONSTRAINT qrtz_scheduler_state_pkey PRIMARY KEY (sched_name, instance_name)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.qrtz_scheduler_state.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_scheduler_state.instance_name IS '实例名称';
COMMENT ON COLUMN cdop_sys.qrtz_scheduler_state.last_checkin_time IS '上次检查时间';
COMMENT ON COLUMN cdop_sys.qrtz_scheduler_state.checkin_interval IS '检查间隔时间';


-- cdop_sys.qrtz_simple_triggers definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_simple_triggers;

CREATE TABLE cdop_sys.qrtz_simple_triggers (
	sched_name varchar(120) NOT NULL, -- 调度名称
	trigger_name varchar(200) NOT NULL, -- qrtz_triggers表trigger_name的外键
	trigger_group varchar(200) NOT NULL, -- qrtz_triggers表trigger_group的外键
	repeat_count int8 NOT NULL, -- 重复的次数统计
	repeat_interval int8 NOT NULL, -- 重复的间隔时间
	times_triggered int8 NOT NULL, -- 已经触发的次数
	CONSTRAINT qrtz_simple_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.qrtz_simple_triggers.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_simple_triggers.trigger_name IS 'qrtz_triggers表trigger_name的外键';
COMMENT ON COLUMN cdop_sys.qrtz_simple_triggers.trigger_group IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON COLUMN cdop_sys.qrtz_simple_triggers.repeat_count IS '重复的次数统计';
COMMENT ON COLUMN cdop_sys.qrtz_simple_triggers.repeat_interval IS '重复的间隔时间';
COMMENT ON COLUMN cdop_sys.qrtz_simple_triggers.times_triggered IS '已经触发的次数';


-- cdop_sys.qrtz_simprop_triggers definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_simprop_triggers;

CREATE TABLE cdop_sys.qrtz_simprop_triggers (
	sched_name varchar(120) NOT NULL, -- 调度名称
	trigger_name varchar(200) NOT NULL, -- qrtz_triggers表trigger_name的外键
	trigger_group varchar(200) NOT NULL, -- qrtz_triggers表trigger_group的外键
	str_prop_1 varchar(512) NULL, -- String类型的trigger的第一个参数
	str_prop_2 varchar(512) NULL, -- String类型的trigger的第二个参数
	str_prop_3 varchar(512) NULL, -- String类型的trigger的第三个参数
	int_prop_1 int4 NULL, -- int类型的trigger的第一个参数
	int_prop_2 int4 NULL, -- int类型的trigger的第二个参数
	long_prop_1 int8 NULL, -- long类型的trigger的第一个参数
	long_prop_2 int8 NULL, -- long类型的trigger的第二个参数
	dec_prop_1 numeric(13, 4) NULL, -- decimal类型的trigger的第一个参数
	dec_prop_2 numeric(13, 4) NULL, -- decimal类型的trigger的第二个参数
	bool_prop_1 bool NULL, -- Boolean类型的trigger的第一个参数
	bool_prop_2 bool NULL, -- Boolean类型的trigger的第二个参数
	CONSTRAINT qrtz_simprop_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.trigger_name IS 'qrtz_triggers表trigger_name的外键';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.trigger_group IS 'qrtz_triggers表trigger_group的外键';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.str_prop_1 IS 'String类型的trigger的第一个参数';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.str_prop_2 IS 'String类型的trigger的第二个参数';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.str_prop_3 IS 'String类型的trigger的第三个参数';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.int_prop_1 IS 'int类型的trigger的第一个参数';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.int_prop_2 IS 'int类型的trigger的第二个参数';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.long_prop_1 IS 'long类型的trigger的第一个参数';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.long_prop_2 IS 'long类型的trigger的第二个参数';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.dec_prop_1 IS 'decimal类型的trigger的第一个参数';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.dec_prop_2 IS 'decimal类型的trigger的第二个参数';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.bool_prop_1 IS 'Boolean类型的trigger的第一个参数';
COMMENT ON COLUMN cdop_sys.qrtz_simprop_triggers.bool_prop_2 IS 'Boolean类型的trigger的第二个参数';


-- cdop_sys.qrtz_triggers definition

-- Drop table

-- DROP TABLE cdop_sys.qrtz_triggers;

CREATE TABLE cdop_sys.qrtz_triggers (
	sched_name varchar(120) NOT NULL, -- 调度名称
	trigger_name varchar(200) NOT NULL, -- 触发器的名字
	trigger_group varchar(200) NOT NULL, -- 触发器所属组的名字
	job_name varchar(200) NOT NULL, -- qrtz_job_details表job_name的外键
	job_group varchar(200) NOT NULL, -- qrtz_job_details表job_group的外键
	description varchar(250) NULL, -- 相关介绍
	next_fire_time int8 NULL, -- 上一次触发时间（毫秒）
	prev_fire_time int8 NULL, -- 下一次触发时间（默认为-1表示不触发）
	priority int4 NULL, -- 优先级
	trigger_state varchar(16) NOT NULL, -- 触发器状态
	trigger_type varchar(8) NOT NULL, -- 触发器的类型
	start_time int8 NOT NULL, -- 开始时间
	end_time int8 NULL, -- 结束时间
	calendar_name varchar(200) NULL, -- 日程表名称
	misfire_instr int2 NULL, -- 补偿执行的策略
	job_data bytea NULL, -- 存放持久化job对象
	CONSTRAINT qrtz_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.qrtz_triggers.sched_name IS '调度名称';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.trigger_name IS '触发器的名字';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.trigger_group IS '触发器所属组的名字';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.job_name IS 'qrtz_job_details表job_name的外键';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.job_group IS 'qrtz_job_details表job_group的外键';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.description IS '相关介绍';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.next_fire_time IS '上一次触发时间（毫秒）';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.prev_fire_time IS '下一次触发时间（默认为-1表示不触发）';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.priority IS '优先级';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.trigger_state IS '触发器状态';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.trigger_type IS '触发器的类型';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.start_time IS '开始时间';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.end_time IS '结束时间';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.calendar_name IS '日程表名称';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.misfire_instr IS '补偿执行的策略';
COMMENT ON COLUMN cdop_sys.qrtz_triggers.job_data IS '存放持久化job对象';


-- cdop_sys.so_ss_average_demand definition

-- Drop table

-- DROP TABLE cdop_sys.so_ss_average_demand;

CREATE TABLE cdop_sys.so_ss_average_demand (
	stock_point_id varchar(32) NOT NULL, -- 库存点编码
	stock_point_name varchar(64) NULL, -- 库存点名称
	item_id varchar(32) NOT NULL, -- 物品编码
	item_name varchar(64) NULL, -- 物品名称
	average_qty numeric(53) NULL, -- 日均量 默认0
	remark varchar(64) NULL, -- 备注
	status int8 NULL, -- 状态
	CONSTRAINT so_ss_average_demand_pkey PRIMARY KEY (item_id, stock_point_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.so_ss_average_demand.stock_point_id IS '库存点编码';
COMMENT ON COLUMN cdop_sys.so_ss_average_demand.stock_point_name IS '库存点名称';
COMMENT ON COLUMN cdop_sys.so_ss_average_demand.item_id IS '物品编码';
COMMENT ON COLUMN cdop_sys.so_ss_average_demand.item_name IS '物品名称';
COMMENT ON COLUMN cdop_sys.so_ss_average_demand.average_qty IS '日均量 默认0';
COMMENT ON COLUMN cdop_sys.so_ss_average_demand.remark IS '备注';
COMMENT ON COLUMN cdop_sys.so_ss_average_demand.status IS '状态';


-- cdop_sys.so_ss_service_level definition

-- Drop table

-- DROP TABLE cdop_sys.so_ss_service_level;

CREATE TABLE cdop_sys.so_ss_service_level (
	stock_point_id varchar(32) NOT NULL, -- 库存点编码
	stock_point_name varchar(64) NULL, -- 库存点名称
	item_id varchar(32) NOT NULL, -- 物品编码
	item_name varchar(64) NULL, -- 物品名称
	service_level numeric NULL, -- 服务水平 默认0.95
	remark varchar(64) NULL, -- 备注
	status int8 NULL, -- 状态
	CONSTRAINT so_ss_service_level_pkey PRIMARY KEY (stock_point_id, item_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.so_ss_service_level.stock_point_id IS '库存点编码';
COMMENT ON COLUMN cdop_sys.so_ss_service_level.stock_point_name IS '库存点名称';
COMMENT ON COLUMN cdop_sys.so_ss_service_level.item_id IS '物品编码';
COMMENT ON COLUMN cdop_sys.so_ss_service_level.item_name IS '物品名称';
COMMENT ON COLUMN cdop_sys.so_ss_service_level.service_level IS '服务水平 默认0.95';
COMMENT ON COLUMN cdop_sys.so_ss_service_level.remark IS '备注';
COMMENT ON COLUMN cdop_sys.so_ss_service_level.status IS '状态';


-- cdop_sys.so_wt_demand definition

-- Drop table

-- DROP TABLE cdop_sys.so_wt_demand;

CREATE TABLE cdop_sys.so_wt_demand (
	item_id varchar(64) NOT NULL, -- 产品编码
	item_name varchar(64) NULL, -- 产品简称
	stock_point_id varchar(64) NOT NULL, -- 库存点编码
	stock_point_name varchar(64) NULL, -- 库存点简称
	expiry_limit int8 NOT NULL, -- 效期限制，保质期在多少天内，不填写默认无效期限制
	expected_delivery_date date NOT NULL, -- 期望交期
	qty numeric NULL DEFAULT 0, -- 量，不填写默认为0
	remark varchar(64) NULL, -- 备注
	status int8 NOT NULL DEFAULT 1, -- 状态  1.有效  0.无效
	demand_plan_code int8 NULL, -- 需求计划编号
	demand_plan_name varchar(64) NULL, -- 需求计划名称
	version_id varchar(32) NULL -- 版本编号
);
COMMENT ON TABLE cdop_sys.so_wt_demand IS '调拨算法输入-需求';

-- Column comments

COMMENT ON COLUMN cdop_sys.so_wt_demand.item_id IS '产品编码';
COMMENT ON COLUMN cdop_sys.so_wt_demand.item_name IS '产品简称';
COMMENT ON COLUMN cdop_sys.so_wt_demand.stock_point_id IS '库存点编码';
COMMENT ON COLUMN cdop_sys.so_wt_demand.stock_point_name IS '库存点简称';
COMMENT ON COLUMN cdop_sys.so_wt_demand.expiry_limit IS '效期限制，保质期在多少天内，不填写默认无效期限制';
COMMENT ON COLUMN cdop_sys.so_wt_demand.expected_delivery_date IS '期望交期';
COMMENT ON COLUMN cdop_sys.so_wt_demand.qty IS '量，不填写默认为0';
COMMENT ON COLUMN cdop_sys.so_wt_demand.remark IS '备注';
COMMENT ON COLUMN cdop_sys.so_wt_demand.status IS '状态  1.有效  0.无效';
COMMENT ON COLUMN cdop_sys.so_wt_demand.demand_plan_code IS '需求计划编号';
COMMENT ON COLUMN cdop_sys.so_wt_demand.demand_plan_name IS '需求计划名称';
COMMENT ON COLUMN cdop_sys.so_wt_demand.version_id IS '版本编号';


-- cdop_sys.so_wt_safety_stock definition

-- Drop table

-- DROP TABLE cdop_sys.so_wt_safety_stock;

CREATE TABLE cdop_sys.so_wt_safety_stock (
	stock_point_id varchar(32) NOT NULL, -- 库存点编码
	stock_point_name varchar(64) NULL, -- 库存点名称
	item_id varchar(32) NOT NULL, -- 物品编码
	item_name varchar(64) NULL, -- 物品名称
	safety_stock numeric(53) NULL, -- 安全库存
	target_stock numeric(53) NULL, -- 目标库存
	remark varchar(64) NULL, -- 备注
	status int8 NULL, -- 状态
	CONSTRAINT so_wt_safety_stock_pkey PRIMARY KEY (stock_point_id, item_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.so_wt_safety_stock.stock_point_id IS '库存点编码';
COMMENT ON COLUMN cdop_sys.so_wt_safety_stock.stock_point_name IS '库存点名称';
COMMENT ON COLUMN cdop_sys.so_wt_safety_stock.item_id IS '物品编码';
COMMENT ON COLUMN cdop_sys.so_wt_safety_stock.item_name IS '物品名称';
COMMENT ON COLUMN cdop_sys.so_wt_safety_stock.safety_stock IS '安全库存';
COMMENT ON COLUMN cdop_sys.so_wt_safety_stock.target_stock IS '目标库存';
COMMENT ON COLUMN cdop_sys.so_wt_safety_stock.remark IS '备注';
COMMENT ON COLUMN cdop_sys.so_wt_safety_stock.status IS '状态';


-- cdop_sys.so_wt_stock_capacity definition

-- Drop table

-- DROP TABLE cdop_sys.so_wt_stock_capacity;

CREATE TABLE cdop_sys.so_wt_stock_capacity (
	rule_id int8 NULL,
	stock_point_id varchar(32) NULL,
	stock_point_name varchar(64) NULL,
	item_id varchar(32) NULL,
	item_name varchar(64) NULL,
	"group_id" varchar(32) NULL,
	"type" int4 NULL,
	capacity numeric(53) NULL,
	remark varchar(64) NULL,
	status int8 NULL
);


-- cdop_sys.t_ryytn_account definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_account;

CREATE TABLE cdop_sys.t_ryytn_account (
	id int8 NOT NULL, -- 编号
	"name" varchar(64) NULL, -- 账号名称，OA同步lastName可能为空，此处默认为空，业务代码校验本地账号不能为空
	nick_name varchar(64) NULL, -- 账号昵称
	work_code varchar(32) NULL, -- 工号
	login_id varchar(32) NULL, -- 登录账号，本地账号必填，OA账号对应loginId，可能为空，此处默认为空，业务代码校验本地账号不能为空
	"password" varchar(64) NULL, -- 密码，本地账号必填，此处默认为空，业务代码校验本地账号不能为空
	oa_id varchar(32) NULL, -- OA人员编号
	status int4 NULL DEFAULT 1, -- 状态，1：正常，2：停用
	description varchar(256) NULL, -- 描述
	data_type int4 NULL DEFAULT 2, -- 数据类型，1:表示初始化数据不允许删除；2:表示管理端创建数据；3:OA系统同步，默认为2
	created_time timestamp NULL DEFAULT now(), -- 创建时间
	updated_time timestamp NULL, -- 修改时间
	department_id varchar(32) NULL, -- 部门编号
	CONSTRAINT t_ryytn_account_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_account.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_account."name" IS '账号名称，OA同步lastName可能为空，此处默认为空，业务代码校验本地账号不能为空';
COMMENT ON COLUMN cdop_sys.t_ryytn_account.nick_name IS '账号昵称';
COMMENT ON COLUMN cdop_sys.t_ryytn_account.work_code IS '工号';
COMMENT ON COLUMN cdop_sys.t_ryytn_account.login_id IS '登录账号，本地账号必填，OA账号对应loginId，可能为空，此处默认为空，业务代码校验本地账号不能为空';
COMMENT ON COLUMN cdop_sys.t_ryytn_account."password" IS '密码，本地账号必填，此处默认为空，业务代码校验本地账号不能为空';
COMMENT ON COLUMN cdop_sys.t_ryytn_account.oa_id IS 'OA人员编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_account.status IS '状态，1：正常，2：停用';
COMMENT ON COLUMN cdop_sys.t_ryytn_account.description IS '描述';
COMMENT ON COLUMN cdop_sys.t_ryytn_account.data_type IS '数据类型，1:表示初始化数据不允许删除；2:表示管理端创建数据；3:OA系统同步，默认为2';
COMMENT ON COLUMN cdop_sys.t_ryytn_account.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_account.updated_time IS '修改时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_account.department_id IS '部门编号';


-- cdop_sys.t_ryytn_account_role definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_account_role;

CREATE TABLE cdop_sys.t_ryytn_account_role (
	account_id int8 NOT NULL, -- 账号编号
	role_id int8 NOT NULL, -- 角色编号
	CONSTRAINT t_ryytn_account_role_pkey PRIMARY KEY (account_id, role_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_account_role.account_id IS '账号编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_account_role.role_id IS '角色编号';


-- cdop_sys.t_ryytn_adjustable_days_rule definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_adjustable_days_rule;

CREATE TABLE cdop_sys.t_ryytn_adjustable_days_rule (
	id int8 NOT NULL, -- 主键id
	"name" varchar(200) NULL, -- 规则名称
	range_type int2 NULL, -- 范围类型(0:产品，1:品类，2：全部)
	start_time date NULL, -- 生效时间
	end_time date NULL, -- 结束时间
	forever_flag int2 NULL, -- 规则是否永久生效 0:否，1：是
	created_by varchar(64) NULL, -- 创建人
	created_time timestamp NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 更新人
	updated_time timestamp NULL, -- 更新时间
	is_default int2 NULL DEFAULT 1, -- 0为预置数据 1为手动添加
	CONSTRAINT t_ryytn_adjustable_days_rule_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_adjustable_days_rule IS '可调天数规则表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule.id IS '主键id';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule."name" IS '规则名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule.range_type IS '范围类型(0:产品，1:品类，2：全部)';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule.start_time IS '生效时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule.end_time IS '结束时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule.forever_flag IS '规则是否永久生效 0:否，1：是';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule.created_by IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule.updated_by IS '更新人';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule.updated_time IS '更新时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule.is_default IS '0为预置数据 1为手动添加';


-- cdop_sys.t_ryytn_adjustable_days_rule_range definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_adjustable_days_rule_range;

CREATE TABLE cdop_sys.t_ryytn_adjustable_days_rule_range (
	id int8 NOT NULL, -- 主键id
	rule_id int8 NULL, -- 规则id
	warehouse_code varchar(32) NULL, -- 库存地点code
	warehouse_name varchar(64) NULL, -- 库存地点名称
	adjustable_days int4 NULL, -- 可调天数
	CONSTRAINT t_ryytn_adjustable_days_rule_range_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_adjustable_days_rule_range IS '可调天数规则范围';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range.id IS '主键id';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range.rule_id IS '规则id';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range.warehouse_code IS '库存地点code';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range.warehouse_name IS '库存地点名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range.adjustable_days IS '可调天数';


-- cdop_sys.t_ryytn_adjustable_days_rule_range_category definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_adjustable_days_rule_range_category;

CREATE TABLE cdop_sys.t_ryytn_adjustable_days_rule_range_category (
	id int8 NOT NULL, -- 主键id
	rule_id int8 NULL, -- 规则id
	category_code varchar(32) NULL, -- 品类编码
	category_name varchar(64) NULL, -- 品类名称
	"level" int2 NULL, -- 品类等级
	sku_codes text NULL, -- 产品编码集合
	CONSTRAINT t_ryytn_adjustable_days_rule_range_category_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_adjustable_days_rule_range_category IS '可调天数规则品类';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range_category.id IS '主键id';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range_category.rule_id IS '规则id';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range_category.category_code IS '品类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range_category.category_name IS '品类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range_category."level" IS '品类等级';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range_category.sku_codes IS '产品编码集合';


-- cdop_sys.t_ryytn_adjustable_days_rule_range_product definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_adjustable_days_rule_range_product;

CREATE TABLE cdop_sys.t_ryytn_adjustable_days_rule_range_product (
	id int8 NOT NULL, -- 主键id
	rule_id int8 NULL, -- 规则id
	sku_code varchar(32) NULL, -- 产品编码
	sku_name varchar(64) NULL, -- 产品简称
	CONSTRAINT t_ryytn_adjustable_days_rule_range_product_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_adjustable_days_rule_range_product IS '可调天数规则产品';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range_product.id IS '主键id';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range_product.rule_id IS '规则id';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range_product.sku_code IS '产品编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_adjustable_days_rule_range_product.sku_name IS '产品简称';


-- cdop_sys.t_ryytn_algo_scheduling_record definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_algo_scheduling_record;

CREATE TABLE cdop_sys.t_ryytn_algo_scheduling_record (
	id int8 NOT NULL, -- 主键
	algo_name_and_version varchar(64) NULL, -- 算法名称和版本
	start_time timestamp NULL, -- 开始时间
	end_time timestamp NULL, -- 结束时间
	running_status int4 NULL, -- 运行状态 0.INIT,¶    1.RUNNING,¶    2.STOPPING,¶    3.STOPPED,¶    4.SKIP,¶    5.SUCCESS,¶    6.FAILED
	scene int2 NULL, -- 算法类型 0：渠道 1：分仓 2：库存 3：调拨
	CONSTRAINT t_ryytn_algo_scheduling_record_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_algo_scheduling_record IS '算法任务调用记录';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_algo_scheduling_record.id IS '主键';
COMMENT ON COLUMN cdop_sys.t_ryytn_algo_scheduling_record.algo_name_and_version IS '算法名称和版本';
COMMENT ON COLUMN cdop_sys.t_ryytn_algo_scheduling_record.start_time IS '开始时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_algo_scheduling_record.end_time IS '结束时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_algo_scheduling_record.running_status IS '运行状态 0.INIT,
    1.RUNNING,
    2.STOPPING,
    3.STOPPED,
    4.SKIP,
    5.SUCCESS,
    6.FAILED';
COMMENT ON COLUMN cdop_sys.t_ryytn_algo_scheduling_record.scene IS '算法类型 0：渠道 1：分仓 2：库存 3：调拨';


-- cdop_sys.t_ryytn_button definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_button;

CREATE TABLE cdop_sys.t_ryytn_button (
	id int8 NOT NULL, -- 按钮编号
	"name" varchar(64) NOT NULL, -- 按钮名称
	alias varchar(64) NULL, -- 按钮别名
	"permission" varchar(256) NULL, -- 按钮权限码，用于后端校验权限，英文逗号分隔
	dependency_ids varchar(1024) NULL, -- 按钮依赖编号，英文逗号分隔
	page_id int8 NOT NULL, -- 所属页面编号
	sort_no int4 NULL, -- 页面排序
	CONSTRAINT t_ryytn_button_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_button.id IS '按钮编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_button."name" IS '按钮名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_button.alias IS '按钮别名';
COMMENT ON COLUMN cdop_sys.t_ryytn_button."permission" IS '按钮权限码，用于后端校验权限，英文逗号分隔';
COMMENT ON COLUMN cdop_sys.t_ryytn_button.dependency_ids IS '按钮依赖编号，英文逗号分隔';
COMMENT ON COLUMN cdop_sys.t_ryytn_button.page_id IS '所属页面编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_button.sort_no IS '页面排序';


-- cdop_sys.t_ryytn_channel_demand_plan_data_sync definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_channel_demand_plan_data_sync;

CREATE TABLE cdop_sys.t_ryytn_channel_demand_plan_data_sync (
	id serial4 NOT NULL, -- 编号
	demand_plan_code varchar(32) NOT NULL, -- 计划编号
	version_id varchar(32) NOT NULL, -- 滚动版本号
	version_name varchar(64) NULL, -- 版本名称，格式：计划编号-滚动版本号
	lv2_channel_code varchar(64) NULL, -- 二级渠道编号
	sku_code varchar(255) NULL, -- SKU产品编号
	plan_date varchar(64) NOT NULL, -- 时间，格式yyyyMMdd，二级渠道为生产计划部时放周的第一天，其他二级渠道放周的最后一天
	plan_value numeric(10) NOT NULL, -- 需求数据
	month_week varchar(32) NULL, -- 月份周数，格式MMWn
	CONSTRAINT t_ryytn_channel_demand_plan_data_sync_pkey PRIMARY KEY (id)
);
CREATE INDEX t_ryytn_channel_demand_plan_data_sync_demand_plan_code_idx ON cdop_sys.t_ryytn_channel_demand_plan_data_sync USING btree (demand_plan_code);
COMMENT ON TABLE cdop_sys.t_ryytn_channel_demand_plan_data_sync IS '渠道需求计划数据同步中间表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_data_sync.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_data_sync.demand_plan_code IS '计划编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_data_sync.version_id IS '滚动版本号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_data_sync.version_name IS '版本名称，格式：计划编号-滚动版本号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_data_sync.lv2_channel_code IS '二级渠道编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_data_sync.sku_code IS 'SKU产品编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_data_sync.plan_date IS '时间，格式yyyyMMdd，二级渠道为生产计划部时放周的第一天，其他二级渠道放周的最后一天';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_data_sync.plan_value IS '需求数据';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_data_sync.month_week IS '月份周数，格式MMWn';


-- cdop_sys.t_ryytn_channel_demand_plan_history definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_channel_demand_plan_history;

CREATE TABLE cdop_sys.t_ryytn_channel_demand_plan_history (
	id serial4 NOT NULL, -- 编号
	demand_plan_code varchar(32) NOT NULL, -- 计划编号
	version_id varchar(32) NOT NULL, -- 版本号
	sku_code varchar(255) NULL, -- 产品编号
	sku_name varchar(255) NULL, -- 产品名称
	lv1_category_code varchar(64) NULL, -- 一级品类编号
	lv1_category_name varchar(64) NULL, -- 一级品类名称
	lv2_category_code varchar(64) NULL, -- 二级品类编号
	lv2_category_name varchar(64) NULL, -- 二级品类名称
	lv3_category_code varchar(64) NULL, -- 三级品类编号
	lv3_category_name varchar(64) NULL, -- 三级品类名称
	lv1_channel_code varchar(64) NULL, -- 一级渠道编号
	lv1_channel_name varchar(64) NULL, -- 一级渠道名称
	lv2_channel_code varchar(64) NULL, -- 二级渠道编号
	lv2_channel_name varchar(64) NULL, -- 二级渠道名称
	lv3_channel_code varchar(64) NULL, -- 三级渠道编号
	lv3_channel_name varchar(64) NULL, -- 三级渠道名称
	plan_date varchar(64) NOT NULL, -- 日期
	plan_value numeric(10, 2) NOT NULL, -- 修改后值
	old_plan_value numeric(10, 2) NULL DEFAULT 0, -- 修改前值
	deviation_radio numeric(10) NULL DEFAULT 0, -- 偏差率
	remark varchar(256) NULL, -- 备注
	extend varchar(1024) NULL, -- 扩展字段
	last_modifier varchar(32) NULL, -- 修改人
	gmt_modify timestamp NULL, -- 修改时间
	"group_id" int4 NULL, -- 分组编号
	CONSTRAINT t_ryytn_channel_demand_plan_history_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_channel_demand_plan_history IS '渠道需求计划修改历史明细';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.demand_plan_code IS '计划编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.version_id IS '版本号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.sku_code IS '产品编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.sku_name IS '产品名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv1_category_code IS '一级品类编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv1_category_name IS '一级品类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv2_category_code IS '二级品类编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv2_category_name IS '二级品类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv3_category_code IS '三级品类编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv3_category_name IS '三级品类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv1_channel_code IS '一级渠道编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv1_channel_name IS '一级渠道名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv2_channel_code IS '二级渠道编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv2_channel_name IS '二级渠道名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv3_channel_code IS '三级渠道编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.lv3_channel_name IS '三级渠道名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.plan_date IS '日期';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.plan_value IS '修改后值';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.old_plan_value IS '修改前值';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.deviation_radio IS '偏差率';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.remark IS '备注';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.extend IS '扩展字段';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.last_modifier IS '修改人';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history.gmt_modify IS '修改时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_plan_history."group_id" IS '分组编号';


-- cdop_sys.t_ryytn_channel_demand_report_version definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_channel_demand_report_version;

CREATE TABLE cdop_sys.t_ryytn_channel_demand_report_version (
	id int8 NOT NULL, -- 编号
	rolling_version varchar(32) NOT NULL, -- 版本编号
	is_locked int2 NULL DEFAULT 0, -- 是否锁定，0：未锁定，1：已锁定
	creator varchar(32) NULL DEFAULT NULL::character varying, -- 创建人
	last_modifier varchar(32) NULL DEFAULT NULL::character varying, -- 修改人
	gmt_create timestamp NOT NULL DEFAULT now(), -- 创建时间
	gmt_modify timestamp NOT NULL DEFAULT now(), -- 修改时间
	lv3_channel_codes text NULL, -- 已编辑的三级渠道编号
	CONSTRAINT t_ryytn_channel_demand_report_version_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_channel_demand_report_version IS '渠道需求提报版本';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_report_version.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_report_version.rolling_version IS '版本编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_report_version.is_locked IS '是否锁定，0：未锁定，1：已锁定';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_report_version.creator IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_report_version.last_modifier IS '修改人';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_report_version.gmt_create IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_report_version.gmt_modify IS '修改时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_channel_demand_report_version.lv3_channel_codes IS '已编辑的三级渠道编号';


-- cdop_sys.t_ryytn_cold_demand_report definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_cold_demand_report;

CREATE TABLE cdop_sys.t_ryytn_cold_demand_report (
	id int8 NOT NULL, -- 编号
	rolling_version varchar(32) NOT NULL, -- 版本号，DDP+提报起始年份+提报起始月份+下周周一所属周数
	sku_code varchar(255) NULL, -- skuCode
	sku_name varchar(255) NULL, -- sku名称
	lv1_category_code varchar(64) NULL, -- 产品分类编码
	lv1_category_name varchar(64) NULL, -- 产品分类名称
	lv2_category_code varchar(64) NULL, -- 产品大类编码
	lv2_category_name varchar(64) NULL, -- 产品大类名称
	lv3_category_code varchar(64) NULL, -- 产品小类编码
	lv3_category_name varchar(64) NULL, -- 产品小类名称
	lv1_channel_code varchar(64) NULL, -- 一级渠道类型编码
	lv1_channel_name varchar(64) NULL, -- 一级渠道类型名称
	lv2_channel_code varchar(64) NULL, -- 二级渠道类型编码
	lv2_channel_name varchar(64) NULL, -- 二级渠道类型名称
	lv3_channel_code varchar(64) NULL, -- 三级渠道类型编码
	lv3_channel_name varchar(64) NULL, -- 三级渠道类型名称
	receiver_type varchar(64) NULL, -- 渠道类型
	biz_date_type varchar(32) NOT NULL, -- 时间类型:DAY,WEEK，MONTH,YEAR
	biz_date_value varchar(64) NOT NULL, -- 时间类型值,日：20230101;周:0230103;月:202301
	order_num numeric(10) NOT NULL, -- 订单数量/订单金额
	unit varchar(3) NULL, -- 计量单位:件/瓶/吨ml/元
	last_order_num numeric(10) NULL DEFAULT NULL::numeric, -- 上一版本订单数量/订单金额
	deviation_radio numeric(10, 2) NULL DEFAULT NULL::numeric, -- 需求提报二级渠道数据偏差率
	remark varchar(256) NULL, -- 备注
	extend varchar(1024) NULL, -- 扩展字段
	is_modify int2 NULL, -- 是否调整：0为否;1为是，默认0
	is_delete int2 NULL DEFAULT 0, -- 是否删除：0为否;1为是，默认0
	creator varchar(32) NULL, -- 创建人
	last_modifier varchar(32) NULL, -- 最后修改人
	gmt_create timestamp NULL, -- 创建时间
	gmt_modify timestamp NULL, -- 修改时间
	CONSTRAINT t_ryytn_cold_demand_report_pkey PRIMARY KEY (id)
);
CREATE INDEX t_ryytn_cold_demand_report_rolling_version_idx ON cdop_sys.t_ryytn_cold_demand_report USING btree (rolling_version, sku_code, lv1_category_code, lv2_category_code, lv3_category_code, lv1_channel_code, lv2_channel_code, lv3_channel_code, biz_date_value);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.rolling_version IS '版本号，DDP+提报起始年份+提报起始月份+下周周一所属周数';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.sku_code IS 'skuCode';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.sku_name IS 'sku名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv1_category_code IS '产品分类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv1_category_name IS '产品分类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv2_category_code IS '产品大类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv2_category_name IS '产品大类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv3_category_code IS '产品小类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv3_category_name IS '产品小类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv1_channel_code IS '一级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv1_channel_name IS '一级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv2_channel_code IS '二级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv2_channel_name IS '二级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv3_channel_code IS '三级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.lv3_channel_name IS '三级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.receiver_type IS '渠道类型';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.biz_date_type IS '时间类型:DAY,WEEK，MONTH,YEAR';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.biz_date_value IS '时间类型值,日：20230101;周:0230103;月:202301';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.order_num IS '订单数量/订单金额';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.unit IS '计量单位:件/瓶/吨ml/元';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.last_order_num IS '上一版本订单数量/订单金额';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.deviation_radio IS '需求提报二级渠道数据偏差率';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.remark IS '备注';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.extend IS '扩展字段';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.is_modify IS '是否调整：0为否;1为是，默认0';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.is_delete IS '是否删除：0为否;1为是，默认0';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.creator IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.last_modifier IS '最后修改人';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.gmt_create IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report.gmt_modify IS '修改时间';



-- cdop_sys.t_ryytn_cold_demand_report_history definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_cold_demand_report_history;

CREATE TABLE cdop_sys.t_ryytn_cold_demand_report_history (
	id bigserial NOT NULL, -- 编号
	rolling_version varchar(32) NOT NULL, -- 版本号，DDP+提报起始年份+提报起始月份+下周周一所属周数
	sku_code varchar(255) NULL, -- skuCode
	sku_name varchar(255) NULL, -- sku名称
	lv1_category_code varchar(64) NULL, -- 产品分类编码
	lv1_category_name varchar(64) NULL, -- 产品分类名称
	lv2_category_code varchar(64) NULL, -- 产品大类编码
	lv2_category_name varchar(64) NULL, -- 产品大类名称
	lv3_category_code varchar(64) NULL, -- 产品小类编码
	lv3_category_name varchar(64) NULL, -- 产品小类名称
	lv1_channel_code varchar(64) NULL, -- 一级渠道类型编码
	lv1_channel_name varchar(64) NULL, -- 一级渠道类型名称
	lv2_channel_code varchar(64) NULL, -- 二级渠道类型编码
	lv2_channel_name varchar(64) NULL, -- 二级渠道类型名称
	lv3_channel_code varchar(64) NULL, -- 三级渠道类型编码
	lv3_channel_name varchar(64) NULL, -- 三级渠道类型名称
	lv3_channel_type varchar(64) NULL, -- 三级渠道类型
	biz_date_type varchar(32) NOT NULL, -- 时间类型:DAY,WEEK，MONTH,YEAR
	biz_date_value varchar(64) NOT NULL, -- 时间类型值,日：20230101;周:0230103;月:202301
	order_num numeric(10, 2) NOT NULL, -- 订单数量/订单金额
	old_order_num numeric(10, 2) NOT NULL, -- 旧订单数量/旧订单金额
	unit varchar(3) NULL, -- 计量单位:件/瓶/吨ml/元
	deviation_radio numeric(10) NULL DEFAULT 0, -- 渠道需求提报二级渠道数据偏差率
	remark varchar(256) NULL, -- 备注
	extend varchar(1024) NULL, -- 扩展字段
	last_modifier varchar(32) NULL, -- 最后修改人
	gmt_modify timestamp(0) NULL, -- 修改时间
	CONSTRAINT t_ryytn_cold_demand_report_history_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_cold_demand_report_history IS '低温需求提报数据历史记录表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.rolling_version IS '版本号，DDP+提报起始年份+提报起始月份+下周周一所属周数';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.sku_code IS 'skuCode';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.sku_name IS 'sku名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv1_category_code IS '产品分类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv1_category_name IS '产品分类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv2_category_code IS '产品大类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv2_category_name IS '产品大类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv3_category_code IS '产品小类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv3_category_name IS '产品小类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv1_channel_code IS '一级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv1_channel_name IS '一级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv2_channel_code IS '二级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv2_channel_name IS '二级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv3_channel_code IS '三级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv3_channel_name IS '三级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.lv3_channel_type IS '三级渠道类型';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.biz_date_type IS '时间类型:DAY,WEEK，MONTH,YEAR';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.biz_date_value IS '时间类型值,日：20230101;周:0230103;月:202301';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.order_num IS '订单数量/订单金额';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.old_order_num IS '旧订单数量/旧订单金额';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.unit IS '计量单位:件/瓶/吨ml/元';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.deviation_radio IS '渠道需求提报二级渠道数据偏差率';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.remark IS '备注';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.extend IS '扩展字段';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.last_modifier IS '最后修改人';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_history.gmt_modify IS '修改时间';


-- cdop_sys.t_ryytn_cold_demand_report_version definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_cold_demand_report_version;

CREATE TABLE cdop_sys.t_ryytn_cold_demand_report_version (
	id int8 NOT NULL, -- 编号
	rolling_version varchar(32) NOT NULL, -- 版本编号
	creator varchar(32) NULL DEFAULT NULL::character varying, -- 创建人
	last_modifier varchar(32) NULL DEFAULT NULL::character varying, -- 修改人
	gmt_create timestamp NOT NULL DEFAULT now(), -- 创建时间
	gmt_modify timestamp NOT NULL DEFAULT now(), -- 修改时间
	CONSTRAINT t_ryytn_cold_demand_report_version_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_cold_demand_report_version IS '低温提报版本';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_version.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_version.rolling_version IS '版本编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_version.creator IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_version.last_modifier IS '修改人';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_version.gmt_create IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_cold_demand_report_version.gmt_modify IS '修改时间';


-- cdop_sys.t_ryytn_config definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_config;

CREATE TABLE cdop_sys.t_ryytn_config (
	category_id varchar(32) NOT NULL, -- 所属类别
	config_id varchar(64) NOT NULL, -- 配置编码
	config_name varchar(64) NULL, -- 配置名称
	config_type int4 NULL, -- 配置值类型（1：密码类型；2：文本类型；3：文件类型；4：枚举类型；5：数字文本；6：文本域）
	config_value varchar(512) NULL, -- 配置值
	status int4 NULL DEFAULT 1, -- 配置状态，1：正常，2：停用
	is_display int4 NULL, -- 0:不展示, 1:展示
	"validator" varchar(256) NULL, -- 校验规则
	description varchar(256) NULL, -- 配置描述
	sort_no int4 NULL, -- 排序字段
	created_by varchar(64) NULL DEFAULT 'admin'::character varying, -- 创建人
	created_time timestamp NOT NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 更新人
	updated_time timestamp NULL, -- 更新时间
	CONSTRAINT t_ryytn_config_pkey PRIMARY KEY (config_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_config.category_id IS '所属类别';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.config_id IS '配置编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.config_name IS '配置名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.config_type IS '配置值类型（1：密码类型；2：文本类型；3：文件类型；4：枚举类型；5：数字文本；6：文本域）';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.config_value IS '配置值';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.status IS '配置状态，1：正常，2：停用';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.is_display IS '0:不展示, 1:展示';
COMMENT ON COLUMN cdop_sys.t_ryytn_config."validator" IS '校验规则';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.description IS '配置描述';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.sort_no IS '排序字段';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.created_by IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.updated_by IS '更新人';
COMMENT ON COLUMN cdop_sys.t_ryytn_config.updated_time IS '更新时间';


-- cdop_sys.t_ryytn_configcategory definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_configcategory;

CREATE TABLE cdop_sys.t_ryytn_configcategory (
	category_id varchar(32) NOT NULL, -- 规则编号，业务唯一约束
	parent_id int8 NULL, -- 父类别编号
	parent_ids int8 NULL, -- 所有有父类别编号
	"name" varchar(50) NULL, -- 类别名称
	status int4 NULL DEFAULT 1, -- 配置状态，1：正常，2：停用
	sort_no int4 NULL, -- 类别序号
	CONSTRAINT t_ryytn_configcategory_pkey PRIMARY KEY (category_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_configcategory.category_id IS '规则编号，业务唯一约束';
COMMENT ON COLUMN cdop_sys.t_ryytn_configcategory.parent_id IS '父类别编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_configcategory.parent_ids IS '所有有父类别编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_configcategory."name" IS '类别名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_configcategory.status IS '配置状态，1：正常，2：停用';
COMMENT ON COLUMN cdop_sys.t_ryytn_configcategory.sort_no IS '类别序号';


-- cdop_sys.t_ryytn_daily_warehouse_aiplan_demand definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_daily_warehouse_aiplan_demand;

CREATE TABLE cdop_sys.t_ryytn_daily_warehouse_aiplan_demand (
	id varchar(64) NOT NULL,
	demand_plan_code varchar(64) NULL, -- 需求计划编码
	demand_plan_name varchar(64) NULL, -- 需求计划名称
	demand_plan_version varchar(64) NULL, -- 需求计划版本
	aiplan_demand_version varchar(64) NULL, -- 调拨需求版本
	sku_code varchar(64) NULL, -- 产品编码
	sku_name varchar(64) NULL, -- 产品简称
	distribute_type int2 NULL, -- 渠道类型 0：ToB，1：ToC
	warehouse_code varchar(64) NULL, -- 仓库编码
	warehouse_name varchar(64) NULL, -- 仓库名称
	lv1_category_code varchar(64) NULL, -- 产品分类编码
	lv1_category_name varchar(64) NULL, -- 产品分类名称
	lv2_category_code varchar(64) NULL, -- 产品大类编码
	lv2_category_name varchar(64) NULL, -- 产品大类名称
	lv3_category_code varchar(64) NULL, -- 产品小类编码
	lv3_category_name varchar(64) NULL, -- 产品小类名称
	validity_period varchar(64) NULL, -- 效期名称
	date_recorded varchar(64) NULL, -- 日期
	date_value numeric NULL, -- 数量
	status int4 NULL DEFAULT 1, -- 数据状态：1：正常，2：生成中
	CONSTRAINT t_ryytn_daily_warehouse_aiplan_demand_pkey PRIMARY KEY (id)
);
CREATE INDEX aiplan_demand_version ON cdop_sys.t_ryytn_daily_warehouse_aiplan_demand USING btree (aiplan_demand_version);
CREATE INDEX t_ryytn_daily_warehouse_aiplan_demand_demand_plan_code_idx ON cdop_sys.t_ryytn_daily_warehouse_aiplan_demand USING btree (demand_plan_code, demand_plan_version, aiplan_demand_version, sku_code, warehouse_code, lv1_category_code, lv2_category_code, lv3_category_code, date_recorded, validity_period, distribute_type);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.demand_plan_code IS '需求计划编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.demand_plan_name IS '需求计划名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.demand_plan_version IS '需求计划版本';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.aiplan_demand_version IS '调拨需求版本';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.sku_code IS '产品编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.sku_name IS '产品简称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.distribute_type IS '渠道类型 0：ToB，1：ToC';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.warehouse_code IS '仓库编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.warehouse_name IS '仓库名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.lv1_category_code IS '产品分类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.lv1_category_name IS '产品分类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.lv2_category_code IS '产品大类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.lv2_category_name IS '产品大类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.lv3_category_code IS '产品小类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.lv3_category_name IS '产品小类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.validity_period IS '效期名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.date_recorded IS '日期';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.date_value IS '数量';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_aiplan_demand.status IS '数据状态：1：正常，2：生成中';


-- cdop_sys.t_ryytn_daily_warehouse_demand definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_daily_warehouse_demand;

CREATE TABLE cdop_sys.t_ryytn_daily_warehouse_demand (
	id varchar NOT NULL,
	demand_plan_code varchar(64) NULL, -- 需求计划编码
	demand_plan_name varchar(64) NULL, -- 需求计划名称
	demand_plan_version varchar(64) NULL, -- 需求计划版本
	sku_code varchar(64) NULL, -- 产品编码
	sku_name varchar(64) NULL, -- 产品名称
	distribute_type int2 NULL, -- 渠道类型 0：ToB，1：ToC
	warehouse_code varchar(64) NULL, -- 仓库编码
	warehouse_name varchar(64) NULL, -- 仓库名称
	lv1_category_code varchar(64) NULL, -- 产品分类编码
	lv1_category_name varchar(64) NULL, -- 产品分类名称
	lv2_category_code varchar(64) NULL, -- 产品大类编码
	lv2_category_name varchar(64) NULL, -- 产品大类名称
	lv3_category_code varchar(64) NULL, -- 产品小类编码
	lv3_category_name varchar(64) NULL, -- 产品小类名称
	date_recorded varchar(64) NULL, -- 日期
	date_value numeric NULL, -- 日数量
	week_recorded varchar(64) NULL, -- 周日期
	week_raw_value numeric NULL, -- 周原始数量
	week_actual_value numeric NULL, -- 周实际数量
	status int4 NULL DEFAULT 1, -- 数据状态：1：正常，2：生成中
	CONSTRAINT t_ryytn_daily_warehouse_demand_pkey PRIMARY KEY (id)
);
CREATE INDEX t_ryytn_daily_warehouse_demand_demand_plan_code_idx ON cdop_sys.t_ryytn_daily_warehouse_demand USING btree (demand_plan_code, demand_plan_version, sku_code, distribute_type, warehouse_code, lv1_category_code, lv2_category_code, lv3_category_code, week_recorded, date_recorded);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.demand_plan_code IS '需求计划编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.demand_plan_name IS '需求计划名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.demand_plan_version IS '需求计划版本';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.sku_code IS '产品编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.sku_name IS '产品名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.distribute_type IS '渠道类型 0：ToB，1：ToC';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.warehouse_code IS '仓库编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.warehouse_name IS '仓库名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.lv1_category_code IS '产品分类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.lv1_category_name IS '产品分类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.lv2_category_code IS '产品大类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.lv2_category_name IS '产品大类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.lv3_category_code IS '产品小类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.lv3_category_name IS '产品小类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.date_recorded IS '日期';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.date_value IS '日数量';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.week_recorded IS '周日期';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.week_raw_value IS '周原始数量';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.week_actual_value IS '周实际数量';
COMMENT ON COLUMN cdop_sys.t_ryytn_daily_warehouse_demand.status IS '数据状态：1：正常，2：生成中';


-- cdop_sys.t_ryytn_dataq_task definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_dataq_task;

CREATE TABLE cdop_sys.t_ryytn_dataq_task (
	id bigserial NOT NULL, -- 自增主键
	task_id int8 NULL, -- 阿里dataq任务实例编号
	job_id int8 NULL, -- 业务应用任务编号
	app_code varchar(64) NULL DEFAULT NULL::character varying, -- 阿里dataq任务应用编号
	param text NULL, -- 阿里dataq任务请求参数
	lock_key varchar(128) NULL DEFAULT NULL::character varying, -- 阿里dataq任务锁的key
	status varchar(32) NULL DEFAULT NULL::character varying, -- 阿里dataq任务状态，INIT：初始化，RUNNING：执行中，STOPPING：停止中，STOPPED：已停止，SKIP：跳过，SUCCESS：成功，FAILED：失败
	reload_id int8 NULL, -- 补偿次数
	start_time timestamp NULL DEFAULT now(), -- 开始时间
	end_time timestamp NULL, -- 完成时间
	CONSTRAINT t_ryytn_dataq_task_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_dataq_task IS '阿里dataq任务执行记录表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_dataq_task.id IS '自增主键';
COMMENT ON COLUMN cdop_sys.t_ryytn_dataq_task.task_id IS '阿里dataq任务实例编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_dataq_task.job_id IS '业务应用任务编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_dataq_task.app_code IS '阿里dataq任务应用编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_dataq_task.param IS '阿里dataq任务请求参数';
COMMENT ON COLUMN cdop_sys.t_ryytn_dataq_task.lock_key IS '阿里dataq任务锁的key';
COMMENT ON COLUMN cdop_sys.t_ryytn_dataq_task.status IS '阿里dataq任务状态，INIT：初始化，RUNNING：执行中，STOPPING：停止中，STOPPED：已停止，SKIP：跳过，SUCCESS：成功，FAILED：失败';
COMMENT ON COLUMN cdop_sys.t_ryytn_dataq_task.reload_id IS '补偿次数';
COMMENT ON COLUMN cdop_sys.t_ryytn_dataq_task.start_time IS '开始时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_dataq_task.end_time IS '完成时间';


-- cdop_sys.t_ryytn_dict_data definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_dict_data;

CREATE TABLE cdop_sys.t_ryytn_dict_data (
	dict_id serial4 NOT NULL, -- 字典编号
	dict_type varchar(64) NOT NULL, -- 字典类型
	"name" varchar(64) NOT NULL, -- 字典名称
	code varchar(64) NOT NULL, -- 字典编码
	parent_id varchar(64) NOT NULL, -- 父字典编号
	parent_ids varchar(2048) NOT NULL, -- 所有父字典编号
	"level" int4 NOT NULL, -- 字典层级
	leaf_flag int2 NOT NULL DEFAULT 1::smallint, -- 是否叶子，0：否，1：是，首次新增默认为1
	css_class varchar(64) NULL, -- 样式属性（其他样式扩展）
	list_class varchar(64) NULL, -- 表格回显样式
	item_check int4 NULL DEFAULT 0, -- 是否默认选中，1：是，0：否，默认为0
	sort_no int4 NULL, -- 排序
	status int4 NULL DEFAULT 1, -- 状态，1：正常，2：停用，默认值：1
	delete_flag int2 NULL DEFAULT 0::smallint, -- 删除状态，0：未删除，1：已删除，默认0
	description varchar(256) NULL, -- 描述
	data_type int4 NOT NULL DEFAULT 2, -- 数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2
	created_by varchar(64) NULL, -- 创建人(登录账号)
	created_time timestamp NOT NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 修改人(登录账号)
	updated_time timestamp NULL, -- 修改时间
	CONSTRAINT t_ryytn_dict_data_pkey1 PRIMARY KEY (dict_id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_dict_data IS '字典数据表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.dict_id IS '字典编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.dict_type IS '字典类型';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data."name" IS '字典名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.code IS '字典编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.parent_id IS '父字典编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.parent_ids IS '所有父字典编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data."level" IS '字典层级';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.leaf_flag IS '是否叶子，0：否，1：是，首次新增默认为1';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.css_class IS '样式属性（其他样式扩展）';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.list_class IS '表格回显样式';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.item_check IS '是否默认选中，1：是，0：否，默认为0';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.sort_no IS '排序';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.status IS '状态，1：正常，2：停用，默认值：1';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.delete_flag IS '删除状态，0：未删除，1：已删除，默认0';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.description IS '描述';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.data_type IS '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.created_by IS '创建人(登录账号)';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.updated_by IS '修改人(登录账号)';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_data.updated_time IS '修改时间';


-- cdop_sys.t_ryytn_dict_type definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_dict_type;

CREATE TABLE cdop_sys.t_ryytn_dict_type (
	dict_type_id serial4 NOT NULL, -- 字典类型编号
	dict_type varchar(64) NOT NULL, -- 字典类型
	dict_name varchar(64) NOT NULL, -- 字典名称
	status int4 NULL DEFAULT 1, -- 状态，1：正常，2：停用，默认值：1
	delete_flag int2 NULL DEFAULT 0::smallint, -- 删除状态，0：未删除，1：已删除，默认0
	description varchar(256) NULL, -- 描述
	data_type int4 NOT NULL DEFAULT 2, -- 数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为1
	created_by varchar(64) NULL, -- 创建人(登录账号)
	created_time timestamp NOT NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 修改人(登录账号)
	updated_time timestamp NULL, -- 修改时间
	CONSTRAINT t_ryytn_dict_type_pkey1 PRIMARY KEY (dict_type_id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_dict_type IS '字典类型表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.dict_type_id IS '字典类型编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.dict_type IS '字典类型';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.dict_name IS '字典名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.status IS '状态，1：正常，2：停用，默认值：1';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.delete_flag IS '删除状态，0：未删除，1：已删除，默认0';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.description IS '描述';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.data_type IS '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为1';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.created_by IS '创建人(登录账号)';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.updated_by IS '修改人(登录账号)';
COMMENT ON COLUMN cdop_sys.t_ryytn_dict_type.updated_time IS '修改时间';


-- cdop_sys.t_ryytn_distribute_plan_inventory_strategy definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_distribute_plan_inventory_strategy;

CREATE TABLE cdop_sys.t_ryytn_distribute_plan_inventory_strategy (
	id int8 NOT NULL, -- 主键ID
	warehouse_code varchar(32) NULL, -- 仓库编码
	warehouse_name varchar(32) NULL, -- 仓库名称
	warehouse_type varchar(32) NULL, -- 仓库类别
	sku_code varchar(32) NULL, -- 产品编码
	sku_name varchar(64) NULL, -- 产品名称
	sku_name_simple varchar(64) NULL, -- 产品简称
	inventory_safe_day int4 NULL, -- 安全库存天数
	inventory_safe_amount int4 NULL, -- 安全库存数量
	inventory_safe_amount_adv int4 NULL, -- 建议安全库存数量
	inventory_turnover_day int4 NULL, -- 周转库存天数
	inventory_turnover_amount int4 NULL, -- 周转库存数量
	inventory_target_day_adv int4 NULL, -- 建议目标库存天数
	inventory_target_amount_adv int4 NULL, -- 建议目标库存数量
	inventory_target_day int4 NULL, -- 目标库存天数
	inventory_target_amount int4 NULL, -- 目标库存数量
	sales_daily int4 NULL, -- 日均销量
	special_strategy_flag int2 NULL, -- 特殊策略开启标识
	inventory_safe_day_spec int4 NULL, -- 特殊安全库存天数
	inventory_turnover_day_spec int4 NULL, -- 特殊周转库存天数
	start_time_strategy timestamp NULL, -- 策略生效时间起
	end_time_strategy timestamp NULL, -- 策略生效时间止
	created_by varchar(64) NULL, -- 创建人
	created_time timestamp NOT NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 更新人
	updated_time timestamp NULL, -- 更新时间
	CONSTRAINT t_ryytn_distribute_plan_inventory_strategy_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.id IS '主键ID';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.warehouse_code IS '仓库编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.warehouse_name IS '仓库名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.warehouse_type IS '仓库类别';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.sku_code IS '产品编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.sku_name IS '产品名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.sku_name_simple IS '产品简称';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_safe_day IS '安全库存天数';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_safe_amount IS '安全库存数量';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_safe_amount_adv IS '建议安全库存数量';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_turnover_day IS '周转库存天数';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_turnover_amount IS '周转库存数量';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_target_day_adv IS '建议目标库存天数';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_target_amount_adv IS '建议目标库存数量';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_target_day IS '目标库存天数';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_target_amount IS '目标库存数量';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.sales_daily IS '日均销量';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.special_strategy_flag IS '特殊策略开启标识';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_safe_day_spec IS '特殊安全库存天数';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.inventory_turnover_day_spec IS '特殊周转库存天数';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.start_time_strategy IS '策略生效时间起';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.end_time_strategy IS '策略生效时间止';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.created_by IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.updated_by IS '更新人';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy.updated_time IS '更新时间';


-- cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf;

CREATE TABLE cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf (
	id int8 NOT NULL, -- 主键ID
	config_name varchar(32) NULL, -- 配置项
	config_value varchar(32) NULL, -- 配置值
	remark varchar(64) NULL, -- 备注
	created_by varchar(64) NULL, -- 创建人
	created_time timestamp NOT NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 更新人
	updated_time timestamp NULL, -- 更新时间
	CONSTRAINT t_ryytn_distribute_plan_inventory_strategy_conf_pkey PRIMARY KEY (id)
);
CREATE INDEX t_ryytn_distribute_plan_inventory_strategy_conf_config_name_idx ON cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf USING btree (config_name);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf.id IS '主键ID';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf.config_name IS '配置项';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf.config_value IS '配置值';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf.remark IS '备注';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf.created_by IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf.updated_by IS '更新人';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf.updated_time IS '更新时间';


-- cdop_sys.t_ryytn_distribute_plan_valid_rule definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule;

CREATE TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule (
	id int8 NOT NULL, -- 主键ID
	"name" varchar(200) NULL, -- 规则名称
	range_type int2 NULL, -- 范围类型(0:产品，1:品类，2：全部)
	start_time date NULL, -- 生效时间
	end_time date NULL, -- 失效时间
	forever_flag int2 NULL, -- 是否永久生效 0:否，1：是
	distribute_type int2 NULL, -- 0:TOB业务，1:TOC业务
	created_by varchar(64) NULL, -- 创建者
	created_time timestamp(6) NOT NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 更新者
	updated_time timestamp(6) NULL, -- 更新时间
	is_default int2 NULL DEFAULT 1, -- 0为预置数据 1为人工添加
	CONSTRAINT t_ryytn_distribute_plan_valid_rule_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule IS '效期分档规则-主表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.id IS '主键ID';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule."name" IS '规则名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.range_type IS '范围类型(0:产品，1:品类，2：全部)';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.start_time IS '生效时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.end_time IS '失效时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.forever_flag IS '是否永久生效 0:否，1：是';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.distribute_type IS '0:TOB业务，1:TOC业务';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.created_by IS '创建者';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.updated_by IS '更新者';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.updated_time IS '更新时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule.is_default IS '0为预置数据 1为人工添加';


-- cdop_sys.t_ryytn_distribute_plan_valid_rule_range definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule_range;

CREATE TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule_range (
	id int8 NOT NULL, -- 主键ID
	rule_id int8 NULL, -- 效期分档规则id
	"name" varchar(64) NULL, -- 标签名称
	start_day int4 NULL, -- 分档起始天数
	end_day int4 NULL, -- 效期结束天数
	ratio float8 NULL -- 效期需求占比
);
COMMENT ON TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule_range IS '效期分档子表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range.id IS '主键ID';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range.rule_id IS '效期分档规则id';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range."name" IS '标签名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range.start_day IS '分档起始天数';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range.end_day IS '效期结束天数';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range.ratio IS '效期需求占比';


-- cdop_sys.t_ryytn_distribute_plan_valid_rule_range_category definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule_range_category;

CREATE TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule_range_category (
	id int8 NOT NULL, -- 主键ID
	rule_id int8 NULL, -- 效期分档规则id
	category_code varchar(32) NULL, -- 品类编码
	category_name varchar(64) NULL, -- 品类名称
	"level" int2 NULL, -- 品类等级
	sku_codes text NULL -- 产品编码集合
);
COMMENT ON TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule_range_category IS '适用范围品类-子表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range_category.id IS '主键ID';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range_category.rule_id IS '效期分档规则id';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range_category.category_code IS '品类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range_category.category_name IS '品类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range_category."level" IS '品类等级';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range_category.sku_codes IS '产品编码集合';


-- cdop_sys.t_ryytn_distribute_plan_valid_rule_range_product definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule_range_product;

CREATE TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule_range_product (
	id int8 NOT NULL, -- 主键ID
	rule_id int8 NULL, -- 仓能力规则id
	sku_code varchar(32) NULL, -- 产品编码
	sku_name varchar(64) NULL -- 产品名称
);
COMMENT ON TABLE cdop_sys.t_ryytn_distribute_plan_valid_rule_range_product IS '适用范围产品-子表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range_product.id IS '主键ID';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range_product.rule_id IS '仓能力规则id';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range_product.sku_code IS '产品编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_valid_rule_range_product.sku_name IS '产品名称';


-- cdop_sys.t_ryytn_distribute_plan_warehouse_rule definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_distribute_plan_warehouse_rule;

CREATE TABLE cdop_sys.t_ryytn_distribute_plan_warehouse_rule (
	id int8 NOT NULL, -- 主键ID
	"name" varchar(200) NULL, -- 规则名称
	range_type int2 NULL, -- 范围类型(0:产品，1:品类，2：全部)
	start_time date NULL, -- 生效时间
	end_time date NULL, -- 失效时间
	forever_flag int2 NULL, -- 是否永久生效 0:否，1：是
	distribute_type int2 NULL, -- 0:TOB业务，1:TOC业务
	created_by varchar(64) NULL, -- 创建者
	created_time timestamp NOT NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 更新者
	updated_time timestamp NULL, -- 更新时间
	is_default int2 NULL DEFAULT 1, -- 0为预置数据 1为人工添加
	CONSTRAINT t_ryytn_distribute_plan_warehouse_rule_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.id IS '主键ID';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule."name" IS '规则名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.range_type IS '范围类型(0:产品，1:品类，2：全部)';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.start_time IS '生效时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.end_time IS '失效时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.forever_flag IS '是否永久生效 0:否，1：是';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.distribute_type IS '0:TOB业务，1:TOC业务';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.created_by IS '创建者';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.updated_by IS '更新者';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.updated_time IS '更新时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule.is_default IS '0为预置数据 1为人工添加';


-- cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity;

CREATE TABLE cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity (
	id int8 NOT NULL, -- 主键ID
	rule_id int8 NULL, -- 仓能力规则id
	warehouse_code varchar(32) NULL, -- 仓库编码
	warehouse_name varchar(64) NULL, -- 仓库名称
	capacity int4 NULL, -- 库容（提/罐）
	capacity_flag int2 NULL DEFAULT 1, -- 是否开启库容
	delivery_limit int4 NULL, -- 收货能力上限（提/罐）
	delivery_limit_flag int2 NULL DEFAULT 1, -- 是否开启收货能力上限
	delivery_unlimit_flag int2 NULL DEFAULT 0, -- 是否设置收货无限能力
	shipment_limit int4 NULL, -- 出库能力上限（提/罐）
	shipment_limit_flag int2 NULL DEFAULT 1, -- 是否开启出库能力上限
	shipment_unlimit_flag int2 NULL DEFAULT 0, -- 是否设置出库无限能力
	CONSTRAINT t_ryytn_distribute_plan_warehouse_rule_capacity_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.id IS '主键ID';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.rule_id IS '仓能力规则id';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.warehouse_code IS '仓库编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.warehouse_name IS '仓库名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.capacity IS '库容（提/罐）';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.capacity_flag IS '是否开启库容';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.delivery_limit IS '收货能力上限（提/罐）';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.delivery_limit_flag IS '是否开启收货能力上限';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.delivery_unlimit_flag IS '是否设置收货无限能力';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.shipment_limit IS '出库能力上限（提/罐）';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.shipment_limit_flag IS '是否开启出库能力上限';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity.shipment_unlimit_flag IS '是否设置出库无限能力';


-- cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_category definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_category;

CREATE TABLE cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_category (
	id int8 NOT NULL, -- 主键ID
	rule_id int8 NULL, -- 仓能力规则id
	category_code varchar(32) NULL, -- 品类编码
	category_name varchar(64) NULL, -- 品类名称
	"level" int2 NULL, -- 品类等级
	sku_codes text NULL, -- 产品编码集合
	CONSTRAINT t_ryytn_distribute_plan_warehouse_rule_capacity_category_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_category.id IS '主键ID';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_category.rule_id IS '仓能力规则id';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_category.category_code IS '品类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_category.category_name IS '品类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_category."level" IS '品类等级';
COMMENT ON COLUMN cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_category.sku_codes IS '产品编码集合';


-- cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_product definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_product;

CREATE TABLE cdop_sys.t_ryytn_distribute_plan_warehouse_rule_capacity_product (
	id int8 NOT NULL,
	rule_id int8 NULL,
	sku_code varchar(32) NULL,
	sku_name varchar(64) NULL,
	CONSTRAINT t_ryytn_distribute_plan_warehouse_rule_capacity_product_pkey PRIMARY KEY (id)
);


-- cdop_sys.t_ryytn_file definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_file;

CREATE TABLE cdop_sys.t_ryytn_file (
	file_id varchar(256) NOT NULL, -- 文件编号，在文件服务器上存储的唯一编号(路径)
	file_type int4 NOT NULL, -- 文件类型，0：图片，1：视频，2：音频，3：文本，4：文档，5：压缩文件，6：脚本文件，99：其他
	suffix varchar(8) NULL, -- 文件后缀
	o_file_name varchar(1024) NULL, -- 原文件名
	created_by varchar(64) NULL, -- 创建人
	created_time timestamp NOT NULL DEFAULT now(), -- 创建时间
	CONSTRAINT t_ryytn_file_pkey PRIMARY KEY (file_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_file.file_id IS '文件编号，在文件服务器上存储的唯一编号(路径)';
COMMENT ON COLUMN cdop_sys.t_ryytn_file.file_type IS '文件类型，0：图片，1：视频，2：音频，3：文本，4：文档，5：压缩文件，6：脚本文件，99：其他';
COMMENT ON COLUMN cdop_sys.t_ryytn_file.suffix IS '文件后缀';
COMMENT ON COLUMN cdop_sys.t_ryytn_file.o_file_name IS '原文件名';
COMMENT ON COLUMN cdop_sys.t_ryytn_file.created_by IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_file.created_time IS '创建时间';


-- cdop_sys.t_ryytn_file_ref definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_file_ref;

CREATE TABLE cdop_sys.t_ryytn_file_ref (
	file_id varchar(256) NOT NULL, -- 文件编号，在文件服务器上存储的唯一编号(路径)
	service_id varchar(32) NOT NULL, -- 业务编号
	service_type varchar(64) NULL, -- 业务表名
	CONSTRAINT t_ryytn_file_ref_pkey PRIMARY KEY (file_id, service_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_file_ref.file_id IS '文件编号，在文件服务器上存储的唯一编号(路径)';
COMMENT ON COLUMN cdop_sys.t_ryytn_file_ref.service_id IS '业务编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_file_ref.service_type IS '业务表名';


-- cdop_sys.t_ryytn_job definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_job;

CREATE TABLE cdop_sys.t_ryytn_job (
	job_id int8 NOT NULL, -- 任务ID
	job_name varchar(256) NOT NULL DEFAULT ''::character varying, -- 任务名称
	job_type int4 NOT NULL DEFAULT 1, -- 调度类型，1：定时调度，2：循环调度，3：延迟调度
	start_date timestamp NULL, -- 开始时间
	end_date timestamp NULL, -- 结束时间
	job_conf varchar(32) NULL, -- 调度配置，根据调度类型赋值，jobType值，1：cron表达式，2：循环间隔时间，3：空
	class_name varchar(256) NULL, -- bean名称
	param text NULL, -- 调用参数
	service_id int8 NULL, -- 业务编号
	misfire_policy varchar(2) NULL, -- 计划执行错误策略，暂时预留，后续实现
	concurrent int2 NOT NULL DEFAULT 0, -- 是否并发执行，0：禁止并发 1：允许并发，默认值：0
	status int4 NOT NULL DEFAULT 1, -- 状态，1：正常，2：禁用，3：已结束，默认值：1
	description varchar(255) NULL, -- 描述
	created_by varchar(64) NULL, -- 创建者
	created_time timestamp NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 更新者
	updated_time timestamp NULL, -- 更新时间
	CONSTRAINT t_ryytn_job_pkey PRIMARY KEY (job_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_job.job_id IS '任务ID';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.job_name IS '任务名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.job_type IS '调度类型，1：定时调度，2：循环调度，3：延迟调度';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.start_date IS '开始时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.end_date IS '结束时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.job_conf IS '调度配置，根据调度类型赋值，jobType值，1：cron表达式，2：循环间隔时间，3：空';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.class_name IS 'bean名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.param IS '调用参数';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.service_id IS '业务编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.misfire_policy IS '计划执行错误策略，暂时预留，后续实现';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.concurrent IS '是否并发执行，0：禁止并发 1：允许并发，默认值：0';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.status IS '状态，1：正常，2：禁用，3：已结束，默认值：1';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.description IS '描述';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.created_by IS '创建者';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.updated_by IS '更新者';
COMMENT ON COLUMN cdop_sys.t_ryytn_job.updated_time IS '更新时间';


-- cdop_sys.t_ryytn_moudel definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_moudel;

CREATE TABLE cdop_sys.t_ryytn_moudel (
	id int8 NOT NULL, -- 模块编号，业务唯一约束
	"name" varchar(64) NOT NULL, -- 模块名称
	"type" int4 NOT NULL DEFAULT 1, -- 系统类型，1:web，2：app
	"path" varchar(128) NULL DEFAULT NULL::character varying, -- 首页路由
	status int4 NOT NULL DEFAULT 1, -- 状态，1：正常，2：禁用，默认值：1
	description varchar(256) NULL DEFAULT NULL::character varying, -- 描述
	CONSTRAINT t_ryytn_moudel_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_moudel.id IS '模块编号，业务唯一约束';
COMMENT ON COLUMN cdop_sys.t_ryytn_moudel."name" IS '模块名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_moudel."type" IS '系统类型，1:web，2：app';
COMMENT ON COLUMN cdop_sys.t_ryytn_moudel."path" IS '首页路由';
COMMENT ON COLUMN cdop_sys.t_ryytn_moudel.status IS '状态，1：正常，2：禁用，默认值：1';
COMMENT ON COLUMN cdop_sys.t_ryytn_moudel.description IS '描述';


-- cdop_sys.t_ryytn_oa_department definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_oa_department;

CREATE TABLE cdop_sys.t_ryytn_oa_department (
	id varchar(32) NOT NULL, -- 编号
	department_mark varchar(32) NULL, -- 部门简称
	department_name varchar(64) NULL, -- 部门全称
	department_code varchar(32) NULL, -- 部门编码
	sub_company_id varchar(32) NULL, -- 分部编号，对应t_ryytn_subcompany表id字段
	sup_dep_id varchar(32) NULL, -- 上级部门编号，0或者空为表示没有上级分部
	sup_dep_ids varchar(2048) NULL, -- 所有上级部门编号,英文逗号分隔
	"level" int4 NULL DEFAULT 1, -- 部门层级，根据supDepIds包含的英文逗号数量计算
	canceled varchar(10) NULL, -- 封存标志，1 封存，其他为未封存
	sort_no float8 NULL, -- 排序
	created_time timestamp NULL, -- 创建时间
	updated_time timestamp NULL, -- 修改时间
	sync_time int8 NULL, -- 同步时间，精确到毫秒
	CONSTRAINT t_ryytn_oa_department_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.department_mark IS '部门简称';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.department_name IS '部门全称';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.department_code IS '部门编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.sub_company_id IS '分部编号，对应t_ryytn_subcompany表id字段';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.sup_dep_id IS '上级部门编号，0或者空为表示没有上级分部';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.sup_dep_ids IS '所有上级部门编号,英文逗号分隔';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department."level" IS '部门层级，根据supDepIds包含的英文逗号数量计算';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.canceled IS '封存标志，1 封存，其他为未封存';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.sort_no IS '排序';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.updated_time IS '修改时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department.sync_time IS '同步时间，精确到毫秒';


-- cdop_sys.t_ryytn_oa_department_extend definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_oa_department_extend;

CREATE TABLE cdop_sys.t_ryytn_oa_department_extend (
	id varchar(32) NOT NULL, -- 编号
	field_name varchar(32) NOT NULL, -- 字段名
	field_value varchar(64) NOT NULL, -- 字段值
	CONSTRAINT t_ryytn_oa_department_extend_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department_extend.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department_extend.field_name IS '字段名';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_department_extend.field_value IS '字段值';


-- cdop_sys.t_ryytn_oa_jobtitle definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_oa_jobtitle;

CREATE TABLE cdop_sys.t_ryytn_oa_jobtitle (
	id varchar(32) NOT NULL, -- 编号
	job_title_mark varchar(32) NULL, -- 简称
	job_title_name varchar(64) NULL, -- 全称
	job_doc varchar(32) NULL, -- 相关文档id
	job_department_id varchar(32) NULL, -- 部门编号，OA接口废弃字段，以人员表departmentId字段为准
	job_responsibility varchar(256) NULL, -- 职责
	job_competency varchar(256) NULL, -- 任职资格
	job_title_remark varchar(256) NULL, -- 备注
	created_time timestamp NULL, -- 创建时间
	updated_time timestamp NULL, -- 修改时间
	sync_time int8 NULL, -- 同步时间，精确到毫秒
	CONSTRAINT t_ryytn_oa_jobtitle_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.job_title_mark IS '简称';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.job_title_name IS '全称';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.job_doc IS '相关文档id';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.job_department_id IS '部门编号，OA接口废弃字段，以人员表departmentId字段为准';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.job_responsibility IS '职责';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.job_competency IS '任职资格';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.job_title_remark IS '备注';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.updated_time IS '修改时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_jobtitle.sync_time IS '同步时间，精确到毫秒';


-- cdop_sys.t_ryytn_oa_person definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_oa_person;

CREATE TABLE cdop_sys.t_ryytn_oa_person (
	id varchar(32) NOT NULL, -- 编号
	work_code varchar(32) NULL, -- 编号
	last_name varchar(32) NULL, -- 人员名称
	login_id varchar(32) NULL, -- 登录名
	account_type int4 NULL, -- 主次账号标志：1：次账号,其他：主账号
	be_long_to varchar(32) NULL, -- 主账号id （当accounttype 为 1 有效）
	department_id varchar(32) NULL, -- 部门编号
	job_title_id varchar(32) NULL, -- 岗位编号
	location_id varchar(32) NULL, -- 办公地点
	status int4 NULL, -- 状态: 0 试用 1 正式 2 临时 3 试用延期 4 解聘 5 离职 6 退休 7 无效
	"language" varchar(32) NULL, -- 系统语言
	job_activity_desc varchar(256) NULL, -- 职责描述
	job_level varchar(32) NULL, -- 职级
	job_call varchar(32) NULL, -- 职称
	manager_id varchar(32) NULL, -- 上级人员编号
	assistant_id varchar(32) NULL, -- 助理人员编号
	sex varchar(10) NULL, -- 性别
	telephone varchar(32) NULL, -- 办公电话
	mobile varchar(32) NULL, -- 移动电话
	mobile_call varchar(32) NULL, -- 其他电话
	email varchar(32) NULL, -- 邮箱
	start_date varchar(20) NULL, -- 合同开始日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR
	end_date varchar(20) NULL, -- 合同结束日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR
	sec_level varchar(10) NULL, -- 安全级别
	"password" varchar(64) NULL, -- 密码，密文
	certificate_num varchar(32) NULL, -- 身份证
	birthday varchar(20) NULL, -- 生日，OA系统同步过来数据可能不满足Date格式，使用VARCHAR
	height varchar(10) NULL, -- 身高
	weight varchar(10) NULL, -- 体重
	folk varchar(32) NULL, -- 民族
	native_place varchar(32) NULL, -- 籍贯
	health_info varchar(32) NULL, -- 健康状况
	marital_status varchar(32) NULL, -- 婚姻状况
	temp_resident_number varchar(32) NULL, -- 暂住证号码
	resident_place varchar(64) NULL, -- 户口
	regresident_place varchar(64) NULL, -- 户口所在地
	home_address varchar(32) NULL, -- 家庭联系方式
	policy varchar(32) NULL, -- 政治面貌
	be_member_date varchar(20) NULL, -- 入团日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR
	be_party_date varchar(20) NULL, -- 入党日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR
	"degree" varchar(32) NULL, -- 学位
	education_level varchar(10) NULL, -- 学历
	is_labouunion int2 NULL DEFAULT 0, -- 是否公会会员
	last_mod_date varchar(20) NULL, -- 最后修改日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR
	sort_no float8 NULL, -- 排序
	created_time timestamp NULL, -- 创建时间
	updated_time timestamp NULL, -- 修改时间
	sync_time int8 NULL, -- 同步时间，精确到毫秒
	CONSTRAINT t_ryytn_oa_person_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.work_code IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.last_name IS '人员名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.login_id IS '登录名';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.account_type IS '主次账号标志：1：次账号,其他：主账号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.be_long_to IS '主账号id （当accounttype 为 1 有效）';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.department_id IS '部门编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.job_title_id IS '岗位编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.location_id IS '办公地点';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.status IS '状态: 0 试用 1 正式 2 临时 3 试用延期 4 解聘 5 离职 6 退休 7 无效';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person."language" IS '系统语言';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.job_activity_desc IS '职责描述';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.job_level IS '职级';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.job_call IS '职称';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.manager_id IS '上级人员编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.assistant_id IS '助理人员编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.sex IS '性别';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.telephone IS '办公电话';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.mobile IS '移动电话';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.mobile_call IS '其他电话';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.email IS '邮箱';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.start_date IS '合同开始日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.end_date IS '合同结束日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.sec_level IS '安全级别';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person."password" IS '密码，密文';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.certificate_num IS '身份证';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.birthday IS '生日，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.height IS '身高';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.weight IS '体重';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.folk IS '民族';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.native_place IS '籍贯';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.health_info IS '健康状况';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.marital_status IS '婚姻状况';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.temp_resident_number IS '暂住证号码';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.resident_place IS '户口';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.regresident_place IS '户口所在地';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.home_address IS '家庭联系方式';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.policy IS '政治面貌';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.be_member_date IS '入团日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.be_party_date IS '入党日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person."degree" IS '学位';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.education_level IS '学历';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.is_labouunion IS '是否公会会员';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.last_mod_date IS '最后修改日期，OA系统同步过来数据可能不满足Date格式，使用VARCHAR';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.sort_no IS '排序';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.updated_time IS '修改时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person.sync_time IS '同步时间，精确到毫秒';


-- cdop_sys.t_ryytn_oa_person_extend definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_oa_person_extend;

CREATE TABLE cdop_sys.t_ryytn_oa_person_extend (
	id varchar(32) NOT NULL, -- 编号
	field_name varchar(32) NOT NULL, -- 字段名
	field_value varchar(64) NOT NULL, -- 字段值
	CONSTRAINT t_ryytn_oa_person_extend_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person_extend.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person_extend.field_name IS '字段名';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_person_extend.field_value IS '字段值';


-- cdop_sys.t_ryytn_oa_subcompany definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_oa_subcompany;

CREATE TABLE cdop_sys.t_ryytn_oa_subcompany (
	id varchar(32) NOT NULL, -- 编号
	sub_company_name varchar(32) NULL, -- 分部简称
	sub_company_desc varchar(64) NULL, -- 分部全称
	sub_company_code varchar(32) NULL, -- 分部编码
	sup_sub_com_id varchar(32) NULL, -- 上级分部id,0或者空为表示没有上级分部
	sup_sub_com_ids varchar(2048) NULL, -- 所有上级分部id,英文逗号分隔
	"level" int4 NULL DEFAULT 0, -- 分部层级，根据supSubComIds包含的英文逗号数量计算
	canceled varchar(10) NULL, -- 封存标志，1 封存，其他为未封存
	sort_no float8 NULL, -- 排序
	created_time timestamp NULL, -- 创建时间
	updated_time timestamp NULL, -- 修改时间
	sync_time int8 NULL, -- 同步时间，精确到毫秒
	CONSTRAINT t_ryytn_oa_subcompany_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.sub_company_name IS '分部简称';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.sub_company_desc IS '分部全称';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.sub_company_code IS '分部编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.sup_sub_com_id IS '上级分部id,0或者空为表示没有上级分部';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.sup_sub_com_ids IS '所有上级分部id,英文逗号分隔';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany."level" IS '分部层级，根据supSubComIds包含的英文逗号数量计算';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.canceled IS '封存标志，1 封存，其他为未封存';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.sort_no IS '排序';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.updated_time IS '修改时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany.sync_time IS '同步时间，精确到毫秒';


-- cdop_sys.t_ryytn_oa_subcompany_extend definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_oa_subcompany_extend;

CREATE TABLE cdop_sys.t_ryytn_oa_subcompany_extend (
	id varchar(32) NOT NULL, -- 编号
	field_name varchar(32) NOT NULL, -- 字段名
	field_value varchar(64) NOT NULL, -- 字段值
	CONSTRAINT t_ryytn_oa_subcompany_extend_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany_extend.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany_extend.field_name IS '字段名';
COMMENT ON COLUMN cdop_sys.t_ryytn_oa_subcompany_extend.field_value IS '字段值';


-- cdop_sys.t_ryytn_page definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_page;

CREATE TABLE cdop_sys.t_ryytn_page (
	id int8 NOT NULL, -- 页面编号
	"name" varchar(64) NOT NULL, -- 页面名称
	alias varchar(64) NULL, -- 页面别名
	"permission" varchar(256) NULL, -- 页面权限码，用于后端校验权限，英文逗号分隔
	parent_id int8 NOT NULL, -- 页面父编号，根页面为-1
	parent_ids varchar(2048) NOT NULL, -- 页面所有父编号，英文逗号分隔
	dependency_ids varchar(1024) NULL, -- 页面依赖编号，英文逗号分隔
	"type" varchar(32) NOT NULL, -- 页面类型，1：菜单
	"path" varchar(256) NULL, -- 页面路由地址
	config_path varchar(256) NULL, -- 页面配置页路由地址
	component varchar(256) NULL, -- 组件路径
	icon varchar(256) NULL, -- 页面图标静态资源目录
	moudel_id int8 NOT NULL, -- 模块编号
	sort_no int8 NULL, -- 页面排序
	sum_flag int2 NULL DEFAULT 0::smallint, -- 是否开启合计，0：否，1：是，默认0
	description varchar(256) NULL, -- 描述
	CONSTRAINT t_ryytn_page_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_page.id IS '页面编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_page."name" IS '页面名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.alias IS '页面别名';
COMMENT ON COLUMN cdop_sys.t_ryytn_page."permission" IS '页面权限码，用于后端校验权限，英文逗号分隔';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.parent_id IS '页面父编号，根页面为-1';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.parent_ids IS '页面所有父编号，英文逗号分隔';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.dependency_ids IS '页面依赖编号，英文逗号分隔';
COMMENT ON COLUMN cdop_sys.t_ryytn_page."type" IS '页面类型，1：菜单';
COMMENT ON COLUMN cdop_sys.t_ryytn_page."path" IS '页面路由地址';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.config_path IS '页面配置页路由地址';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.component IS '组件路径';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.icon IS '页面图标静态资源目录';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.moudel_id IS '模块编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.sort_no IS '页面排序';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.sum_flag IS '是否开启合计，0：否，1：是，默认0';
COMMENT ON COLUMN cdop_sys.t_ryytn_page.description IS '描述';


-- cdop_sys.t_ryytn_page_config definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_page_config;

CREATE TABLE cdop_sys.t_ryytn_page_config (
	id bigserial NOT NULL, -- 配置编号
	page_id int8 NOT NULL, -- 页面编号
	row_name varchar(64) NULL, -- 列名称
	row_field varchar(64) NULL, -- 列字段名
	width int4 NULL, -- 列宽度
	sort_no int4 NULL, -- 列顺序
	freeze_flag int2 NULL DEFAULT 0::smallint, -- 列冻结/解冻，0：解冻，1：解冻，默认0
	show_flag int2 NULL DEFAULT 0::smallint, -- 列展示/隐藏，0：展示，1：隐藏，默认0
	gather_flag int2 NULL DEFAULT 0::smallint, -- 列聚合，0：不聚合，1：聚合，默认0
	created_by varchar(64) NULL, -- 创建人(登录账号)
	created_time timestamp NOT NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 修改人(登录账号)
	updated_time timestamp NULL, -- 修改时间
	CONSTRAINT t_ryytn_page_config_pkey1 PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_page_config IS '页面配置表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.id IS '配置编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.page_id IS '页面编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.row_name IS '列名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.row_field IS '列字段名';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.width IS '列宽度';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.sort_no IS '列顺序';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.freeze_flag IS '列冻结/解冻，0：解冻，1：解冻，默认0';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.show_flag IS '列展示/隐藏，0：展示，1：隐藏，默认0';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.gather_flag IS '列聚合，0：不聚合，1：聚合，默认0';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.created_by IS '创建人(登录账号)';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.updated_by IS '修改人(登录账号)';
COMMENT ON COLUMN cdop_sys.t_ryytn_page_config.updated_time IS '修改时间';


-- cdop_sys.t_ryytn_role definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_role;

CREATE TABLE cdop_sys.t_ryytn_role (
	id int8 NOT NULL, -- 角色编号，业务唯一约束
	"name" varchar(64) NOT NULL, -- 角色名称
	default_flag int2 NULL DEFAULT 0::smallint, -- 是否默认角色，0：不是默认角色，1：是默认角色
	status int4 NOT NULL DEFAULT 1, -- 状态，1：正常，2：禁用，默认值：1
	description varchar(256) NULL, -- 描述
	sort_no int4 NULL, -- 排序
	data_type int4 NOT NULL DEFAULT 2, -- 数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2
	created_by varchar(64) NULL, -- 创建人(登录账号)
	created_time timestamp NULL DEFAULT now(), -- 创建时间
	updated_by varchar(64) NULL, -- 修改人(登录账号)
	updated_time timestamp NULL, -- 修改时间
	CONSTRAINT t_ryytn_role_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_role.id IS '角色编号，业务唯一约束';
COMMENT ON COLUMN cdop_sys.t_ryytn_role."name" IS '角色名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_role.default_flag IS '是否默认角色，0：不是默认角色，1：是默认角色';
COMMENT ON COLUMN cdop_sys.t_ryytn_role.status IS '状态，1：正常，2：禁用，默认值：1';
COMMENT ON COLUMN cdop_sys.t_ryytn_role.description IS '描述';
COMMENT ON COLUMN cdop_sys.t_ryytn_role.sort_no IS '排序';
COMMENT ON COLUMN cdop_sys.t_ryytn_role.data_type IS '数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2';
COMMENT ON COLUMN cdop_sys.t_ryytn_role.created_by IS '创建人(登录账号)';
COMMENT ON COLUMN cdop_sys.t_ryytn_role.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_role.updated_by IS '修改人(登录账号)';
COMMENT ON COLUMN cdop_sys.t_ryytn_role.updated_time IS '修改时间';


-- cdop_sys.t_ryytn_role_button definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_role_button;

CREATE TABLE cdop_sys.t_ryytn_role_button (
	role_id int8 NOT NULL, -- 角色编号
	button_id int8 NOT NULL, -- 菜单编号
	CONSTRAINT t_ryytn_role_button_pkey PRIMARY KEY (role_id, button_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_role_button.role_id IS '角色编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_role_button.button_id IS '菜单编号';


-- cdop_sys.t_ryytn_role_channel definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_role_channel;

CREATE TABLE cdop_sys.t_ryytn_role_channel (
	role_id int8 NOT NULL, -- 角色编号
	channel_id varchar(32) NOT NULL, -- 渠道编号
	CONSTRAINT t_ryytn_role_channel_pkey PRIMARY KEY (role_id, channel_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_role_channel.role_id IS '角色编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_role_channel.channel_id IS '渠道编号';


-- cdop_sys.t_ryytn_role_depository definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_role_depository;

CREATE TABLE cdop_sys.t_ryytn_role_depository (
	role_id int8 NOT NULL, -- 角色编号
	depository_id varchar(32) NOT NULL, -- 仓库编号
	CONSTRAINT t_ryytn_role_depository_pkey PRIMARY KEY (role_id, depository_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_role_depository.role_id IS '角色编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_role_depository.depository_id IS '仓库编号';


-- cdop_sys.t_ryytn_role_factory definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_role_factory;

CREATE TABLE cdop_sys.t_ryytn_role_factory (
	role_id int8 NOT NULL, -- 角色编号
	factory_id varchar(32) NOT NULL, -- 工厂编号
	CONSTRAINT t_ryytn_role_factory_pkey PRIMARY KEY (role_id, factory_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_role_factory.role_id IS '角色编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_role_factory.factory_id IS '工厂编号';


-- cdop_sys.t_ryytn_role_page definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_role_page;

CREATE TABLE cdop_sys.t_ryytn_role_page (
	role_id int8 NOT NULL, -- 角色编号
	page_id int8 NOT NULL, -- 菜单编号
	CONSTRAINT t_ryytn_role_page_pkey PRIMARY KEY (role_id, page_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_role_page.role_id IS '角色编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_role_page.page_id IS '菜单编号';


-- cdop_sys.t_ryytn_role_productcategory definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_role_productcategory;

CREATE TABLE cdop_sys.t_ryytn_role_productcategory (
	role_id int8 NOT NULL, -- 角色编号
	category_id varchar(32) NOT NULL, -- 产品品类编号
	CONSTRAINT t_ryytn_role_productcategory_pkey PRIMARY KEY (role_id, category_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_role_productcategory.role_id IS '角色编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_role_productcategory.category_id IS '产品品类编号';


-- cdop_sys.t_ryytn_sku_lock definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_sku_lock;

CREATE TABLE cdop_sys.t_ryytn_sku_lock (
	id int8 NOT NULL, -- 主键
	sku_code varchar(255) NULL, -- 产品编码
	sku_name varchar(255) NULL, -- 产品简称
	lv1_category_code varchar(64) NULL, -- 一级分类编码
	lv1_category_name varchar(64) NULL, -- 一级分类名称
	lv2_category_code varchar(64) NULL, -- 二级分类编码
	lv2_category_name varchar(64) NULL, -- 二级分类名称
	lv3_category_code varchar(64) NULL, -- 三级分类编码
	lv3_category_name varchar(64) NULL, -- 三级分类名称
	lock_start_date varchar(10) NULL, -- 锁定期开始时间 yyyyMMdd
	lock_end_date varchar(10) NULL, -- 锁定期结束时间 yyyyMMdd
	lock_start_week varchar(10) NULL, -- 锁定期开始时间 yyyy/MM/WW
	lock_end_week varchar(10) NULL, -- 锁定期结束时间 yyyy/MM/WW
	created_by varchar(64) NULL, -- 创建人
	created_time timestamp NULL DEFAULT now(), -- 创建时间
	updated_time timestamp NULL, -- 更新时间
	CONSTRAINT t_ryytn_sku_lock_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.id IS '主键';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.sku_code IS '产品编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.sku_name IS '产品简称';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.lv1_category_code IS '一级分类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.lv1_category_name IS '一级分类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.lv2_category_code IS '二级分类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.lv2_category_name IS '二级分类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.lv3_category_code IS '三级分类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.lv3_category_name IS '三级分类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.lock_start_date IS '锁定期开始时间 yyyyMMdd';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.lock_end_date IS '锁定期结束时间 yyyyMMdd';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.lock_start_week IS '锁定期开始时间 yyyy/MM/WW';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.lock_end_week IS '锁定期结束时间 yyyy/MM/WW';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.created_by IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.created_time IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock.updated_time IS '更新时间';


-- cdop_sys.t_ryytn_sku_lock_channel definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_sku_lock_channel;

CREATE TABLE cdop_sys.t_ryytn_sku_lock_channel (
	lock_id int8 NOT NULL, -- 主键
	channel_id varchar(64) NOT NULL, -- 锁定渠道（二级）
	channel_name varchar(64) NULL, -- 锁定渠道名称（二级）
	CONSTRAINT t_ryytn_sku_lock_channel_pkey PRIMARY KEY (lock_id, channel_id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock_channel.lock_id IS '主键';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock_channel.channel_id IS '锁定渠道（二级）';
COMMENT ON COLUMN cdop_sys.t_ryytn_sku_lock_channel.channel_name IS '锁定渠道名称（二级）';


-- cdop_sys.t_ryytn_thirdparty_system definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_thirdparty_system;

CREATE TABLE cdop_sys.t_ryytn_thirdparty_system (
	id int8 NOT NULL, -- 编号
	"name" varchar(64) NOT NULL, -- 名称
	auth_code varchar(64) NOT NULL, -- 授权码
	url varchar(1024) NULL, -- 入口页面跳转地址
	status int4 NOT NULL DEFAULT 1, -- 状态，1：正常，2：不正常（扩展字段）
	description varchar(256) NULL, -- 描述
	CONSTRAINT t_ryytn_thirdparty_system_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_thirdparty_system.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_thirdparty_system."name" IS '名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_thirdparty_system.auth_code IS '授权码';
COMMENT ON COLUMN cdop_sys.t_ryytn_thirdparty_system.url IS '入口页面跳转地址';
COMMENT ON COLUMN cdop_sys.t_ryytn_thirdparty_system.status IS '状态，1：正常，2：不正常（扩展字段）';
COMMENT ON COLUMN cdop_sys.t_ryytn_thirdparty_system.description IS '描述';


-- cdop_sys.t_ryytn_warehouse_demand_plan_mark definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_warehouse_demand_plan_mark;

CREATE TABLE cdop_sys.t_ryytn_warehouse_demand_plan_mark (
	demand_plan_code varchar(32) NOT NULL, -- 计划编号
	version_id varchar(32) NOT NULL, -- 版本编号
	receiver_type varchar(8) NOT NULL, -- 渠道类型
	lv3_category_code varchar(64) NOT NULL, -- 三级品类编号
	biz_date_value varchar(64) NOT NULL, -- 日期
	creator varchar(32) NULL DEFAULT NULL::character varying, -- 创建人
	last_modifier varchar(32) NULL DEFAULT NULL::character varying, -- 修改人
	gmt_create timestamp NOT NULL DEFAULT now(), -- 创建时间
	gmt_modify timestamp NOT NULL DEFAULT now(), -- 修改时间
	CONSTRAINT t_ryytn_warehouse_demand_plan_mark_pkey PRIMARY KEY (demand_plan_code, version_id, receiver_type, lv3_category_code, biz_date_value)
);
COMMENT ON TABLE cdop_sys.t_ryytn_warehouse_demand_plan_mark IS '分仓需求计划标记表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_plan_mark.demand_plan_code IS '计划编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_plan_mark.version_id IS '版本编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_plan_mark.receiver_type IS '渠道类型';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_plan_mark.lv3_category_code IS '三级品类编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_plan_mark.biz_date_value IS '日期';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_plan_mark.creator IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_plan_mark.last_modifier IS '修改人';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_plan_mark.gmt_create IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_plan_mark.gmt_modify IS '修改时间';


-- cdop_sys.t_ryytn_warehouse_demand_report definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_warehouse_demand_report;

CREATE TABLE cdop_sys.t_ryytn_warehouse_demand_report (
	id int8 NOT NULL, -- 编号
	"name" varchar(64) NOT NULL, -- 名称
	demand_plan_code varchar(32) NOT NULL, -- 需求计划编号，继承渠道需求计划编号
	rolling_version varchar(32) NOT NULL, -- 需求计划版本号，继承渠道需求计划版本号
	sku_code varchar(255) NULL, -- skuCode
	sku_name varchar(255) NULL, -- sku名称
	lv1_category_code varchar(64) NULL, -- 产品分类编码
	lv1_category_name varchar(64) NULL, -- 产品分类名称
	lv2_category_code varchar(64) NULL, -- 产品大类编码
	lv2_category_name varchar(64) NULL, -- 产品大类名称
	lv3_category_code varchar(64) NULL, -- 产品小类编码
	lv3_category_name varchar(64) NULL, -- 产品小类名称
	lv1_channel_code varchar(64) NULL, -- 一级渠道类型编码
	lv1_channel_name varchar(64) NULL, -- 一级渠道类型名称
	lv2_channel_code varchar(64) NULL, -- 二级渠道类型编码
	lv2_channel_name varchar(64) NULL, -- 二级渠道类型名称
	lv3_channel_code varchar(64) NULL, -- 三级渠道类型编码
	lv3_channel_name varchar(64) NULL, -- 三级渠道类型名称
	receiver_type varchar(64) NULL, -- 渠道类型
	warehouse_code varchar(64) NULL, -- 仓库编号
	warehouse_name varchar(64) NULL, -- 仓库名称
	biz_date_type varchar(32) NOT NULL, -- 时间类型:DAY,WEEK，MONTH,YEAR
	biz_date_value varchar(64) NOT NULL, -- 时间类型值,日：20230101;周:0230103;月:202301
	order_num numeric(10, 2) NOT NULL, -- 订单数量/订单金额
	unit varchar(3) NULL, -- 计量单位:件/瓶/吨ml/元
	deviation_radio numeric(10) NULL DEFAULT 0, -- 渠道需求提报二级渠道数据偏差率
	remark varchar(256) NULL, -- 备注
	extend varchar(1024) NULL, -- 扩展字段
	is_modify int2 NULL, -- 是否调整：0为否;1为是，默认0
	creator varchar(32) NULL, -- 创建人
	last_modifier varchar(32) NULL, -- 最后修改人
	gmt_create timestamp NULL, -- 创建时间
	gmt_modify timestamp NULL, -- 修改时间
	is_delete int2 NULL DEFAULT 0, -- 是否删除：0为否;1为是，默认0
	CONSTRAINT t_ryytn_warehouse_demand_report_pkey PRIMARY KEY (id)
);
CREATE INDEX t_ryytn_warehouse_demand_report_demand_plan_code_idx ON cdop_sys.t_ryytn_warehouse_demand_report USING btree (demand_plan_code, rolling_version, lv1_category_code, lv2_category_code, lv3_category_code, lv1_channel_code, lv2_channel_code, lv3_channel_code, warehouse_code, biz_date_value, sku_code);

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report."name" IS '名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.demand_plan_code IS '需求计划编号，继承渠道需求计划编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.rolling_version IS '需求计划版本号，继承渠道需求计划版本号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.sku_code IS 'skuCode';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.sku_name IS 'sku名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv1_category_code IS '产品分类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv1_category_name IS '产品分类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv2_category_code IS '产品大类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv2_category_name IS '产品大类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv3_category_code IS '产品小类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv3_category_name IS '产品小类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv1_channel_code IS '一级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv1_channel_name IS '一级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv2_channel_code IS '二级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv2_channel_name IS '二级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv3_channel_code IS '三级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.lv3_channel_name IS '三级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.receiver_type IS '渠道类型';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.warehouse_code IS '仓库编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.warehouse_name IS '仓库名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.biz_date_type IS '时间类型:DAY,WEEK，MONTH,YEAR';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.biz_date_value IS '时间类型值,日：20230101;周:0230103;月:202301';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.order_num IS '订单数量/订单金额';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.unit IS '计量单位:件/瓶/吨ml/元';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.deviation_radio IS '渠道需求提报二级渠道数据偏差率';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.remark IS '备注';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.extend IS '扩展字段';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.is_modify IS '是否调整：0为否;1为是，默认0';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.creator IS '创建人';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.last_modifier IS '最后修改人';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.gmt_create IS '创建时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.gmt_modify IS '修改时间';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report.is_delete IS '是否删除：0为否;1为是，默认0';


-- cdop_sys.t_ryytn_warehouse_demand_report_history definition

-- Drop table

-- DROP TABLE cdop_sys.t_ryytn_warehouse_demand_report_history;

CREATE TABLE cdop_sys.t_ryytn_warehouse_demand_report_history (
	id bigserial NOT NULL, -- 编号
	"name" varchar(64) NOT NULL, -- 名称
	demand_plan_code varchar(32) NOT NULL, -- 需求计划编号，继承渠道需求计划编号
	rolling_version varchar(32) NOT NULL, -- 需求计划版本号，继承渠道需求计划版本号
	sku_code varchar(255) NULL, -- skuCode
	sku_name varchar(255) NULL, -- sku名称
	lv1_category_code varchar(64) NULL, -- 产品分类编码
	lv1_category_name varchar(64) NULL, -- 产品分类名称
	lv2_category_code varchar(64) NULL, -- 产品大类编码
	lv2_category_name varchar(64) NULL, -- 产品大类名称
	lv3_category_code varchar(64) NULL, -- 产品小类编码
	lv3_category_name varchar(64) NULL, -- 产品小类名称
	lv1_channel_code varchar(64) NULL, -- 一级渠道类型编码
	lv1_channel_name varchar(64) NULL, -- 一级渠道类型名称
	lv2_channel_code varchar(64) NULL, -- 二级渠道类型编码
	lv2_channel_name varchar(64) NULL, -- 二级渠道类型名称
	lv3_channel_code varchar(64) NULL, -- 三级渠道类型编码
	lv3_channel_name varchar(64) NULL, -- 三级渠道类型名称
	receiver_type varchar(64) NULL, -- 三级渠道类型
	warehouse_code varchar(64) NULL, -- 仓库编号
	warehouse_name varchar(64) NULL, -- 仓库名称
	biz_date_type varchar(32) NOT NULL, -- 时间类型:DAY,WEEK，MONTH,YEAR
	biz_date_value varchar(64) NOT NULL, -- 时间类型值,日：20230101;周:0230103;月:202301
	order_num numeric(10, 2) NOT NULL, -- 订单数量/订单金额
	old_order_num numeric(10, 2) NOT NULL, -- 旧订单数量/旧订单金额
	unit varchar(3) NULL, -- 计量单位:件/瓶/吨ml/元
	deviation_radio numeric(10) NULL DEFAULT 0, -- 渠道需求提报二级渠道数据偏差率
	remark varchar(256) NULL, -- 备注
	extend varchar(1024) NULL, -- 扩展字段
	last_modifier varchar(32) NULL, -- 最后修改人
	gmt_modify timestamp(0) NULL, -- 修改时间
	CONSTRAINT t_ryytn_warehouse_demand_report_history_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE cdop_sys.t_ryytn_warehouse_demand_report_history IS '分仓需求提报数据历史记录表';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.id IS '编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history."name" IS '名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.demand_plan_code IS '需求计划编号，继承渠道需求计划编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.rolling_version IS '需求计划版本号，继承渠道需求计划版本号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.sku_code IS 'skuCode';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.sku_name IS 'sku名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv1_category_code IS '产品分类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv1_category_name IS '产品分类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv2_category_code IS '产品大类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv2_category_name IS '产品大类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv3_category_code IS '产品小类编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv3_category_name IS '产品小类名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv1_channel_code IS '一级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv1_channel_name IS '一级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv2_channel_code IS '二级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv2_channel_name IS '二级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv3_channel_code IS '三级渠道类型编码';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.lv3_channel_name IS '三级渠道类型名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.receiver_type IS '三级渠道类型';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.warehouse_code IS '仓库编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.warehouse_name IS '仓库名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.biz_date_type IS '时间类型:DAY,WEEK，MONTH,YEAR';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.biz_date_value IS '时间类型值,日：20230101;周:0230103;月:202301';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.order_num IS '订单数量/订单金额';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.old_order_num IS '旧订单数量/旧订单金额';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.unit IS '计量单位:件/瓶/吨ml/元';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.deviation_radio IS '渠道需求提报二级渠道数据偏差率';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.remark IS '备注';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.extend IS '扩展字段';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.last_modifier IS '最后修改人';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_history.gmt_modify IS '修改时间';


-- cdop_sys.t_ryytn_warehouse_demand_report_version definition

-- Drop table

CREATE OR REPLACE FUNCTION cdop_sys.execute_dynamic_query(query character varying)
 RETURNS SETOF record
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY EXECUTE query;
END;
$function$
;



CREATE OR REPLACE FUNCTION cdop_sys.get_sequence_uid()
 RETURNS bigint
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_last_timestamp BIGINT;
    v_sequence BIGINT;
    v_worker_id BIGINT; -- 假设您的工作机器ID为1
    v_epoch BIGINT := 1609430400000; -- 自定义的起始时间戳
    v_current_timestamp BIGINT;
    v_uid BIGINT;
BEGIN
    -- 获取当前时间戳，单位毫秒
    v_current_timestamp := (EXTRACT(EPOCH FROM clock_timestamp()) * 1000)::BIGINT;
		v_sequence:=FLOOR(random() * 4096)::INT;
		v_worker_id:=FLOOR(random() * 10)::INT;
    -- 返回生成的唯一ID
    v_uid := ((v_current_timestamp - v_epoch) << 22) | (v_worker_id << 12) | v_sequence;
    RETURN v_uid;
END;
$function$
;


-- DROP TABLE cdop_sys.t_ryytn_warehouse_demand_report_version;

CREATE TABLE cdop_sys.t_ryytn_warehouse_demand_report_version (
	"name" varchar(64) NOT NULL, -- 计划名称
	demand_plan_code varchar(32) NOT NULL, -- 计划编号
	rolling_version varchar(32) NOT NULL, -- 版本编号
	CONSTRAINT t_ryytn_warehouse_demand_report_version_pkey PRIMARY KEY (rolling_version, demand_plan_code)
);
COMMENT ON TABLE cdop_sys.t_ryytn_warehouse_demand_report_version IS '分仓需求提报版本';

-- Column comments

COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_version."name" IS '计划名称';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_version.demand_plan_code IS '计划编号';
COMMENT ON COLUMN cdop_sys.t_ryytn_warehouse_demand_report_version.rolling_version IS '版本编号';


-- cdop_sys.uid_status definition

-- Drop table

-- DROP TABLE cdop_sys.uid_status;

CREATE TABLE cdop_sys.uid_status (
	worker_id int8 NOT NULL,
	last_timestamp int8 NULL,
	"sequence" int8 NULL,
	CONSTRAINT uid_status_pkey PRIMARY KEY (worker_id)
);