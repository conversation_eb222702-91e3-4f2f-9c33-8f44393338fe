
select 
get_sequence_uid(ROW_NUMBER() OVER (ORDER BY plan.plan_date)) AS id, 
plan.demand_plan_code, 
plan.version_id, 
plan.version_date, 
plan.lv1_category_code, 
plan.lv1_category_name, 
plan.lv2_category_code, 
plan.lv2_category_name, 
plan.lv3_category_code, 
plan.lv3_category_name, 
plan.sku_code, 
plan.sku_name, 
null as lifecycle_code, 
null as lifecycle_name, 
delivery.warehouse_code, 
delivery.warehouse_name, 
null as lv1_type_code, 
null as lv1_type_name, 
null as algo_version, 
null as ref_version, 
null as ref_type, 
plan.plan_date, 
CASE 
	WHEN delivery.outbound_rate IS NULL
	THEN 0
	ELSE round(plan.plan_value * delivery.outbound_rate::NUMERIC,0)
END AS plan_value,
null as plan_remark, 
0 as is_modify, 
-2 as status, 
false as deleted, 
case when delivery.channel_type='toB' then 1 else 2 end as "group_id", 
delivery.channel_type as receiver_type, 
null as "label",
'sysAdmin' AS creator,
'sysAdmin' AS last_modifier,
to_char(CURRENT_TIMESTAMP,'YYYY-MM-DD HH24:MI:SS') AS gmt_create,
to_char(CURRENT_TIMESTAMP,'YYYY-MM-DD HH24:MI:SS') AS gmt_modify
from 
(
select 
	demand_plan_code,
	version_id,
	max(version_date) as version_date,
	lv1_category_code,
	max(lv1_category_name) as lv1_category_name,
	lv2_category_code,
	max(lv2_category_name) as lv2_category_name,
	lv3_category_code,
	max(lv3_category_name) as lv3_category_name,
	sku_code,
	max(sku_name) as sku_name,
	plan_date,
	sum(plan_value) as plan_value
from tdm_xqyc_txn_demand_plan_order_di
where 1 = 1
	AND demand_plan_code = '427406169552678912'
	AND version_id = 'DP202403W5-1'
	AND plan_date >= cast('2024-04-22' AS DATE)
	AND plan_date <= cast('2024-04-22' AS DATE)
	AND is_modify = false
	AND deleted = false 
	group by demand_plan_code,version_id,lv1_category_code,lv2_category_code,lv3_category_code,sku_code,plan_date
) plan
left join 
(
select 
lv1_category_code,
max(lv1_category_name) as lv1_category_name,
lv2_category_code,
max(lv2_category_name) as lv2_category_name,
lv3_category_code,
max(lv3_category_name) as lv3_category_name,
sku_code,
max(sku_name) as sku_name,
warehouse_code,
max(warehouse_name) as warehouse_name,
channel_type,
sum(outbound_rate::numeric) as outbound_rate
from tdm_xqyc_txn_delivery_order_rate_df
group by lv1_category_code,lv2_category_code,lv3_category_code,sku_code,warehouse_code,channel_type
) delivery
on plan.sku_code=delivery.sku_code;