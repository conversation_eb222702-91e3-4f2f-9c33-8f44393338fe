<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper.ScpGeiTaskMapper">
    <sql id="Table_Name">
        cdop_biz.scp_gei_task
    </sql>
    <sql id="Child_Table_Name">
        cdop_biz.scp_gei_child_task
    </sql>
    <sql id="Basic_Where_Clause">
        <where>
            <if test="taskId != null">
                task_id = #{taskId}
            </if>
        </where>
    </sql>
    <sql id="Insert_Column_List">
        task_id, task_name, request_params, task_type , status, status_desc, gmt_process_started, gmt_process_finished
        , data_total_cnt, child_task_total_cnt, gmt_merge_started , merge_try_times,  merge_max_try_times
        , gmt_expired, gmt_clear_expired, file_name, file_address, properties, execute_template_code, template_dict_code
        , creator_code, creator_name, operator_code, operator_name
    </sql>
    <sql id="Insert_Properties">
        #{taskId}
        ,#{taskName}
        ,#{requestParams}
        ,#{taskType}
        ,#{status}
        ,#{statusDesc}
        ,#{gmtProcessStarted}
        ,#{gmtProcessFinished}
        ,#{dataTotalCnt}
        ,#{childTaskTotalCnt}
        ,#{gmtMergeStarted}
        ,#{mergeTryTimes}
        ,#{mergeMaxTryTimes}
        ,#{gmtExpired}
        ,#{gmtClearExpired}
        ,#{fileName}
        ,#{fileAddress}
        ,#{properties}
        ,#{executeTemplateCode}
        ,#{templateDictCode}
        ,#{creatorCode}
        ,#{creatorName}
        ,#{operatorCode}
        ,#{operatorName}
    </sql>
    <select id="selectByCondition" resultType="com.cainiao.cntech.dsct.scp.gei.biz.dao.dto.ScpGeiTaskDTO">
        select
            if(
                t2.task_completed_cnt is null or t2.task_completed_cnt &lt;= 0, 0
                ,t2.task_completed_cnt / t1.child_task_total_cnt
            ) * 0.99 + if(t1.status = 'SUCCESS' or t1.status = 'FAILED', 0.01, 0) as task_progress
            ,t1.*
            ,t2.*
        from <include refid="Table_Name"/> t1
        left join (
            select
                parent_task_id
                ,sum(
                    if(status = 'SUCCESS' or status = 'FAILED', 1, 0)
                ) as task_completed_cnt
                ,sum(
                    if(status = 'SUCCESS' or status = 'FAILED', 0, 1)
                ) as task_uncompleted_cnt
            from <include refid="Child_Table_Name"/>
            group by parent_task_id
        ) t2 on t1.task_id = t2.parent_task_id
        <include refid="Basic_Where_Clause"/>
        <choose>
            <when test="orderColumn != null and orderColumn != ''">
                order by t1.${orderColumn}
                <choose>
                    <when test="orderType != null and orderType == 'desc'"> desc </when>
                    <otherwise> asc </otherwise>
                </choose>
            </when>
            <otherwise>
                order by t1.gmt_process_started desc
            </otherwise>
        </choose>
    </select>
    <select id="selectCount" resultType="long">
        select
            count(1)
        from <include refid="Table_Name" />
        <include refid="Basic_Where_Clause" />
    </select>

    <select id="selectExpireTaskList" resultType="com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiTaskDO">
        select * from <include refid="Table_Name" />
        where gmt_clear_expired is not null and gmt_clear_expired &lt;= now()
    </select>
    <select id="selectProcessExpireTaskList" resultType="com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiTaskDO">
        select * from <include refid="Table_Name" />
        where gmt_expired is not null and gmt_expired &lt;= now() and status not in ('SUCCESS', 'FAILED')
    </select>

    <insert id="insert" parameterType="com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiTaskDO" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="Table_Name"/>
        (<include refid="Insert_Column_List"/>)
        values
        (<include refid="Insert_Properties"/>)
    </insert>

    <delete id="delete" parameterType="long">
        delete from <include refid="Table_Name"/>
        <where>
            <foreach collection="taskIds" item="id" open="task_id in (" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </delete>

    <update id="updateByTaskId" parameterType="com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiTaskDO">
        update <include refid="Table_Name"/>
        <set>
            gmt_modified = now()
            <if test='status != null and status != ""'> ,status = #{status} </if>
            <if test='statusDesc != null and statusDesc != ""'> ,status_desc = #{statusDesc} </if>
            <if test='gmtProcessStarted != null'> ,gmt_process_started = #{gmtProcessStarted} </if>
            <if test='gmtProcessFinished != null'> ,gmt_process_finished = #{gmtProcessFinished} </if>
            <if test='gmtMergeStarted != null'> ,gmt_merge_started = #{gmtMergeStarted} </if>
            <if test='mergeTryTimes != null'> ,merge_try_times = #{mergeTryTimes} </if>
            <if test='fileName != null and fileName != ""'> ,file_name = #{fileName} </if>
            <if test='fileAddress != null and fileAddress != ""'> ,file_address = #{fileAddress} </if>
            <if test='operatorCode != null and operatorCode != ""'> ,operator_code = #{operatorCode} </if>
            <if test='operatorName != null and operatorName != ""'> ,operator_name = #{operatorName} </if>
        </set>
        <where>
            task_id = #{taskId}
        </where>
    </update>

    <update id="closeProcessExpireByTaskId" parameterType="long">
        update <include refid="Table_Name"/>
        <set>
            gmt_modified = now()
            ,gmt_process_finished = now()
            ,status = 'FAILED'
            ,status_desc = '任务执行超时！'
        </set>
        <where>
            <foreach collection="taskIds" item="id" open="task_id in (" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

</mapper>