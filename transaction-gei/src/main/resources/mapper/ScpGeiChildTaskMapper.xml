<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper.ScpGeiChildTaskMapper">
    <sql id="Table_Name">
        cdop_biz.scp_gei_child_task
    </sql>
    <sql id="Basic_Where_Clause">
        <where>
            <if test="parent != null">
                parent_task_id = #{parent}
            </if>
        </where>
    </sql>
    <sql id="Insert_Column_List">
        task_id, parent_task_id, request_params, task_type, status, status_desc, gmt_process_started, gmt_process_finished
        , paging, page, page_size, process_try_times, process_max_try_times
        , gmt_expired, data_file_address, execute_template_code, template_dict_code, properties
        , creator_code, creator_name, operator_code, operator_name
    </sql>
    <sql id="Insert_Properties">
        #{item.taskId}
        ,#{item.parentTaskId}
        ,#{item.requestParams}
        ,#{item.taskType}
        ,#{item.status}
        ,#{item.statusDesc}
        ,#{item.gmtProcessStarted}
        ,#{item.gmtProcessFinished}
        ,#{item.paging}
        ,#{item.page}
        ,#{item.pageSize}
        ,#{item.processTryTimes}
        ,#{item.processMaxTryTimes}
        ,#{item.gmtExpired}
        ,#{item.dataFileAddress}
        ,#{item.executeTemplateCode}
        ,#{item.templateDictCode}
        ,#{item.properties}
        ,#{item.creatorCode}
        ,#{item.creatorName}
        ,#{item.operatorCode}
        ,#{item.operatorName}
    </sql>
    <select id="selectByCondition" resultType="com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiChildTaskDO">
        select * from <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>
    </select>
    <select id="selectCount" resultType="long">
        select
            count(1)
        from <include refid="Table_Name" />
        <include refid="Basic_Where_Clause" />
    </select>

    <insert id="batchInsert" parameterType="com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiChildTaskDO" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="Table_Name"/>
        (<include refid="Insert_Column_List"/>)
        values
        <foreach collection="params" item="item" separator=",">
            (<include refid="Insert_Properties"/>)
        </foreach>
    </insert>
    <delete id="delete" parameterType="long">
        delete from <include refid="Table_Name"/>
        <where>
            <foreach collection="parentIds" item="id" open="parent_task_id in (" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </delete>
    <update id="updateByTaskId" parameterType="com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiChildTaskDO">
        update <include refid="Table_Name"/>
        <set>
            gmt_modified = now()
            <if test='status != null and status != ""'> ,status = #{status} </if>
            <if test='statusDesc != null and statusDesc != ""'> ,status_desc = #{statusDesc} </if>
            <if test='gmtProcessStarted != null'> ,gmt_process_started = #{gmtProcessStarted} </if>
            <if test='gmtProcessFinished != null'> ,gmt_process_finished = #{gmtProcessFinished} </if>
            <if test='processTryTimes != null'> ,process_try_times = #{processTryTimes} </if>
            <if test='dataFileAddress != null and dataFileAddress != ""'> ,data_file_address = #{dataFileAddress} </if>
            <if test='operatorCode != null and operatorCode != ""'> ,operator_code = #{operatorCode} </if>
            <if test='operatorName != null and operatorName != ""'> ,operator_name = #{operatorName} </if>
        </set>
        <where>
            task_id = #{taskId}
            <if test="parentTaskId != null">
                and parent_task_id = #{parentTaskId}
            </if>
        </where>
    </update>
    <update id="closeProcessExpireByParentTaskId"  parameterType="long">
        update <include refid="Table_Name"/>
        <set>
            gmt_modified = now()
            ,gmt_process_finished = now()
            ,status = 'FAILED'
            ,status_desc = '任务执行超时！'
        </set>
        <where>
            <foreach collection="parentIds" item="id" open="parent_task_id in (" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>
    <update id="updateNotIsoTaskStatusFailedByTaskId"  parameterType="long">
        update <include refid="Table_Name"/>
        <set>
            gmt_modified = now()
            ,gmt_process_finished = now()
            ,status = 'FAILED'
            ,status_desc = '「非任务隔离」监测到其他子任务校验失败，当前任务关闭！'
        </set>
        <where>
            parent_task_id = #{parentId} and status = 'RUNNING'
        </where>
    </update>
</mapper>