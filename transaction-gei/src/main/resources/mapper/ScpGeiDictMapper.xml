<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper.ScpGeiDictMapper">
    <sql id="Table_Name">
        cdop_biz.scp_gei_dict
    </sql>
    <sql id="Basic_Where_Clause">
        <where>
        </where>
    </sql>
    <select id="selectByCode" resultType="com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiDictDO">
        select * from <include refid="Table_Name"/>
        where code = #{code} and type = #{type}
    </select>

</mapper>