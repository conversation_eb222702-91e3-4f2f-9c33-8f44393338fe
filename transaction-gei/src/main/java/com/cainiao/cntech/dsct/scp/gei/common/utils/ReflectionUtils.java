package com.cainiao.cntech.dsct.scp.gei.common.utils;


import org.springframework.core.ResolvableType;

import java.lang.annotation.Annotation;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023-07-06 00:22:35
 * @description: 反射工具
 */
public class ReflectionUtils {

    /**
     * 根据传入的字段名称 深度查找Field
     * @param tClass class
     * @param fieldName 属性名称
     * @return Field 如未找到指定属性名的属性 则返回空
     */
    public static Field findField(Class<?> tClass, String fieldName){
        if (tClass == Object.class) {
            return null;
        }
        try {
            return tClass.getDeclaredField(fieldName);
        }catch (NoSuchFieldException ignored){
            return findField(tClass.getSuperclass(), fieldName);
        }
    }

    /**
     * 根据传入的字段名称 深度查找Field，但同时如果annotations参数存在则将判断属性上是否标注了传入的注解
     * @param tClass class
     * @param fieldName 属性名称
     * @param annotations 属性标注的注解条件
     * @return Field 如未找到指定属性名的属性 则返回空
     */
    @SafeVarargs
    public static Field findFieldByCondition(Class<?> tClass, String fieldName, Class<? extends Annotation>... annotations){
        if(annotations == null || annotations.length < 1){
            return findField(tClass, fieldName);
        }

        if (tClass == Object.class) {
            return null;
        }
        try {
            Field declaredField = tClass.getDeclaredField(fieldName);
            for (Class<? extends Annotation> annotation : annotations) {
                if(! declaredField.isAnnotationPresent(annotation)){
                    return findFieldByCondition(tClass.getSuperclass(), fieldName, annotations);
                }
            }
            return declaredField;
        }catch (NoSuchFieldException ignored){
            return findFieldByCondition(tClass.getSuperclass(), fieldName, annotations);
        }
    }
    /**
     * 查找一个类标注指定注解的所有属性集合
     * @param tClass class
     * @param isDeep 是否允许深度查找，true深度，false只查找当前类
     * @param annotations 属性标注的注解条件
     * @return Field 如未找到指定属性名的属性 则返回空集合
     */
    @SafeVarargs
    public static List<Field> findFieldByCondition(Class<?> tClass, boolean isDeep, Class<? extends Annotation>... annotations){
        if(annotations == null || annotations.length < 1){
            return new ArrayList<>(0);
        }
        List<Field> result = new ArrayList<>();
        Class<?> tempClass = tClass;
        a:while (tempClass != Object.class){
            b:for (Field field : tempClass.getDeclaredFields()) {
                boolean searchFlag = true;
                c:for (Class<? extends Annotation> annotation : annotations) {
                    if(! field.isAnnotationPresent(annotation)){
                        searchFlag = false;
                        break c;
                    }
                }
                if(searchFlag){
                    result.add(field);
                }
            }

            if (isDeep){
                tempClass = tempClass.getSuperclass();
            }else {
                break a;
            }
        }
        return result;
    }

    /**
     * 查找一个类标注指定注解的所有属性集合
     * @param tClass class
     * @param isDeep 是否允许深度查找，true深度，false只查找当前类
     * @param filter 在根据属性查找到结果的基础上进行过滤，属性作为参数，返回true 或 false， true则保留结果， false则忽略掉这个属性
     * @param annotations 属性标注的注解条件
     * @return Field 如未找到指定属性名的属性 则返回空集合
     */
    @SafeVarargs
    public static List<Field> findFieldByCondition(Class<?> tClass, boolean isDeep, Function<Field, Boolean> filter, Class<? extends Annotation>... annotations){
        List<Field> fieldByCondition = findFieldByCondition(tClass, isDeep, annotations);
        if(!fieldByCondition.isEmpty()){
            return fieldByCondition.stream().filter(filter::apply).collect(Collectors.toList());
        }
        return fieldByCondition;
    }
    /**
     * 将映射表内容填充到对象中
     * @param t 待填充实体
     * @param fieldMapping key=属性名 v=属性值的映射map
     * @return 填充后实体
     * @param <T> 不限
     */
    public static <T> T populateObject(T t, Map<String, Object> fieldMapping){
        if (t == null) {
            return null;
        }
        if (fieldMapping == null) {
            return t;
        }
        Class<?> tClass = t.getClass();
        fieldMapping.forEach((k,v) -> {
            Field field;
            if ((field = findField(tClass, k)) != null) {
                try {
                    field.setAccessible(true);
                    field.set(t, v);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        });
        return t;
    }

    public static <T> Constructor<T> getConstructor(Class<T> modelClass){
        try {
            return modelClass.getDeclaredConstructor();
        } catch (NoSuchMethodException e) {
            return null;
        }
    }
    public static <T> T createInstance(Class<T> modelClass){
        return createInstance(modelClass, null);
    }

    public static <T> T createInstance(Class<T> modelClass, Object... args){
        try {
            Constructor<T> constructor;
            if(args != null && args.length > 0){
                constructor = modelClass.getDeclaredConstructor(Stream.of(args).map(Object::getClass).toArray(Class[]::new));
            }else {
                constructor = modelClass.getDeclaredConstructor();
            }
            constructor.setAccessible(true);
            return constructor.newInstance(args);
        }catch (NoSuchMethodException | InvocationTargetException | InstantiationException | IllegalAccessException e) {
            return null;
        }
    }
    public static <T> Field getFieldByName(Class<T> clazz, String name){
        return getField(clazz, name);
    }

    public static <T, A extends Annotation> Field getFieldByAnnotation(Class<T> clazz, Class<A> annotationClass){
        List<Field> list = getFieldsByAnnotation(clazz, annotationClass);
        if(list.isEmpty()){
            return null;
        }
        return list.get(0);
    }
    public static <T, A extends Annotation> List<Field> getFieldsByAnnotation(Class<T> clazz, Class<A> annotationClass){
        if (Objects.isNull(clazz) || Objects.isNull(annotationClass)) {
            return Collections.emptyList();
        }
        List<Field> result = new ArrayList<>();
        getFieldsByAnnotation(result, clazz, annotationClass);
        return result;
    }
    private static <T, A extends Annotation> void getFieldsByAnnotation(List<Field> result, Class<T> clazz, Class<A> annotationClass){
        if(Object.class.equals(clazz)){
            return;
        }
        getFieldsByAnnotation(result, clazz.getSuperclass(), annotationClass);
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if(field.isAnnotationPresent(annotationClass)){
                result.add(field);
            }
        }
    }
    /**
     * 从对象里获取字段值
     * @param fieldName 字段名称
     * @param t    对象
     */
    public static <T> Object getFieldValue(String fieldName, T t) {
        try {
            Field declaredField = getField(t.getClass(), fieldName);
            if(declaredField != null){
                declaredField.setAccessible(true);
                return declaredField.get(t);
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }
    /**
     * 从对象里获取字段值
     * @param field 字段
     * @param t    对象
     */
    public static <T> Object getFieldValue(Field field, T t) {
        try {
            field.setAccessible(true);
            return field.get(t);
        } catch (Exception e) {
            return null;
        }
    }
    /**
     * 从对象里获取字段值
     * @param field 字段
     * @param t    对象
     */
    public static <T, R> R getFieldValue(Field field, T t, Class<R> returnType) {
        try {
            field.setAccessible(true);
            return returnType.cast(field.get(t));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 调用方法
     */
    public static Object invokeMethod(Object object, String methodName, Object... args) {
        try {
            Method method = getMethod(object.getClass(), methodName);
            if(method == null){
                return null;
            }
            method.setAccessible(true);
            return method.invoke(object, args);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 调用方法
     */
    public static <T> T invokeMethod(Object object, String methodName, Class<T> returnType, Object... args) {
        try {
            Method method = getMethod(object.getClass(), methodName);
            if(method == null){
                return null;
            }
            method.setAccessible(true);
            return returnType.cast(method.invoke(object, args));
        } catch (Exception e) {
            return null;
        }
    }
    /**
     * 查找方法
     */
    public static <T> Method getMethod(Class<?> clazz, String methodName) {
        for (Method declaredMethod : clazz.getDeclaredMethods()) {
            if (declaredMethod.getName().equals(methodName)) {
                return declaredMethod;
            }
        }

       if(Object.class.equals(clazz)){
           return null;
       }
       return getMethod(clazz.getSuperclass(), methodName);
    }
    /**
     * 从对象里获取字段值
     * @param field 字段
     * @param t    对象
     */
    public static <T> void setFieldValue(T t, Field field, Object value) {
        try {
            field.setAccessible(true);
            field.set(t, value);
        } catch (Exception ignored) {
        }
    }
    /**
     * 从对象里获取字段值
     * @param field 字段
     * @param t    对象
     */
    public static <T> void setFieldValue(T t, String field, Object value) {
        try {
            Class<?> clazz = t.getClass();
            Field declaredField = getField(clazz, field);
            if(declaredField != null){
                declaredField.setAccessible(true);
                declaredField.set(t, value);
            }
        } catch (Exception ignored) {
        }
    }
    /**
     * 获取指定类型上的泛型类型
     * @param source 源类型
     * @param searchClass 查找的类型
     * @param genericIndex 泛型索引
     * @return
     */
    public static Class<?> searchGenericClass(Class<?> source, Class<?> searchClass, int genericIndex){
        ResolvableType resolvableType = ResolvableType.forClass(source);
        return searchGenericClass(resolvableType, searchClass, genericIndex);
    }

    /**
     * 解析数据请求参数体
     * @param queryMap
     * @return
     */
    public static Object resolveMapToGenerics(Object object, Class<?> searchClass, Map<String, Object> queryMap, int index){
        Class<?> queryRequestClass = GenericsSearchUtils.searchClass(object, searchClass, index);
        if(queryMap == null){
            return ReflectionUtils.createInstance(queryRequestClass);
        }
        for (String key : queryMap.keySet()) {
            String value;
            Object tempValue = queryMap.get(key);
            if(tempValue instanceof String && (value = tempValue.toString()).contains(",")){
                String[] split = value.split(",");
                queryMap.put(key, split);
            }
        }
        return JsonUtils.toObject(queryMap, queryRequestClass);
    }
    public static List<Object> resolveListMapToGenerics(Object object, Class<?> searchClass, List<Map<String, Object>> list, int index){
        List<Object> result = new ArrayList<>();
        for (Map<String, Object> queryMap : list) {
            result.add(resolveMapToGenerics(object, searchClass, queryMap, index));
        }
        return result;
    }

    /**
     * 获取包括父类中的所有字段
     * @param clazz
     * @return
     */
    public static List<Field> getFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        getFields(clazz, fields);
        return fields;
    }
    public static void getFields(Class<?> clazz, List<Field> list) {
        if (Object.class.equals(clazz)) {
            return;
        }
        try {
            Field[] fields = clazz.getDeclaredFields();
            list.addAll(Arrays.asList(fields));
            getFields(clazz.getSuperclass(), list);
        } catch (Exception ignored) {
        }
    }

    /**
     * 获取指定类型上的泛型类型
     * @param source 源类型
     * @param searchClass 查找的类型
     * @param genericIndex 泛型索引
     * @return
     */
    private static Class<?> searchGenericClass(ResolvableType source, Class<?> searchClass, int genericIndex){
        if(source == null || Objects.equals(source.getRawClass(), Object.class)){ return null; }
        if(Objects.equals(source.getRawClass(), searchClass)){ return source.getGeneric(genericIndex).resolve(); }
        for (ResolvableType interfaceClass : source.getInterfaces()) {
            Class<?> clientClass = searchGenericClass(interfaceClass, searchClass, genericIndex);
            if (Objects.nonNull(clientClass)) {
                 return clientClass;
            }
        }
        ResolvableType superType = source.getSuperType();
        return searchGenericClass(superType, searchClass, genericIndex);
    }

    private static Field getField(Class<?> clazz, String fieldName){
        if(Object.class.equals(clazz)){
            return null;
        }
        try {
            return clazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            return getField(clazz.getSuperclass(), fieldName);
        }
    }
}
