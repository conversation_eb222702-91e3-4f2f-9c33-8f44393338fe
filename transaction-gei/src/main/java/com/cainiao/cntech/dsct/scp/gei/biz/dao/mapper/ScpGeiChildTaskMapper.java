package com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper;

import com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiChildTaskDO;

import java.util.List;
import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:31
 * @description 通用导入导出子任务Mapper
 */
public interface ScpGeiChildTaskMapper {
    List<ScpGeiChildTaskDO> selectByCondition(Map<String, Object> params);
    Long selectCount(Map<String, Object> params);

    Long batchInsert(List<ScpGeiChildTaskDO> params);
    Long delete(List<Long> parentIds);

    Long updateByTaskId(ScpGeiChildTaskDO params);
    Long closeProcessExpireByParentTaskId(List<Long> parentIds);

    Long updateNotIsoTaskStatusFailedByTaskId(Long parentId);
}
