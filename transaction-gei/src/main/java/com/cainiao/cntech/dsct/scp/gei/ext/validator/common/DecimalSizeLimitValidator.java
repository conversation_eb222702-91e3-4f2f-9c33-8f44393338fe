package com.cainiao.cntech.dsct.scp.gei.ext.validator.common;


import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.ext.validator.ImportValidator;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelValid;

import java.lang.reflect.Field;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-07-05 16:31
 * @description
 */
public class DecimalSizeLimitValidator<T> implements ImportValidator<T> {
    @Override
    public boolean require(T t, Field field, String fieldName) {
        return field.isAnnotationPresent(ExcelValid.class) && field.getAnnotation(ExcelValid.class).decimalSizeLimit() >= 0;
    }

    @Override
    public String validate(ImportDataWrapper<T> importDataWrapper, T t, Field field, Object fieldValue) {
        ExcelValid excelValid = field.getAnnotation(ExcelValid.class);
        if (fieldValue instanceof Number) {
            Number rawValue = (Number) fieldValue;
            String v = String.valueOf(rawValue.doubleValue());
            int decimalSize = v.substring(v.indexOf(".") + 1).length();
            if (decimalSize > excelValid.decimalSizeLimit()) {
                return "%s字段小数位限制为「" + excelValid.decimalSizeLimit() + "」位";
            }
        }
        return null;
    }
}
