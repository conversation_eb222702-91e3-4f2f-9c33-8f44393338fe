package com.cainiao.cntech.dsct.scp.gei.core.model;

import lombok.Data;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 14:33
 * @description 分片数据包装
 */
@Data
public class SliceDataWrapper {
    /**
     * 当前页
     */
    private Boolean paging;

    /**
     * 当前页
     */
    private Long currentPage;
    /**
     * 步长
     */
    private Long pageSize;

    public SliceDataWrapper(Boolean paging, Long currentPage, Long pageSize) {
        this.paging = paging;
        this.currentPage = currentPage;
        this.pageSize = pageSize;
    }

    public static SliceDataWrapper of(Boolean paging, Long currentPage, Long pageSize) {
        return new SliceDataWrapper(paging, currentPage, pageSize);
    }

}
