package com.cainiao.cntech.dsct.scp.gei.support.annotation;

import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-10 10:41
 * @description 导入服务注解
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface ImportService {
    /**
     * 导入模版编码
     *
     * @return
     */
    String value();

    /**
     * 导入模版配置的dictCode
     *
     * @return
     */
    String dict() default "";

    /**
     * 默认的导入模版文件名
     *
     * @return
     */
    String filename();

    /**
     * 导入模版提示
     *
     * @return
     */
    String templateDesc() default "填写规范：\n" +
            "   暂无";

    /**
     * 是否为异步导出
     */
    boolean async() default true;

    /**
     * 拆分阈值
     */
    int sliceSize() default 2000;

    /**
     * 拆分隔离
     */
    boolean sliceIso() default true;

    /**
     * 任务过期清理时间， 单位（秒）
     */
    long expire() default 60 * 60 * 48;

    /**
     * 任务执行过期时间， 单位（秒）
     */
    long processExpire() default 60 * 60 * 5;
}
