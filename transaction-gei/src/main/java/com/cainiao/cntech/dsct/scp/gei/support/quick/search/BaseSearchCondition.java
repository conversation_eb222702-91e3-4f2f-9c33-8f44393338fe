package com.cainiao.cntech.dsct.scp.gei.support.quick.search;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cainiao.cntech.dsct.scp.gei.biz.web.model.OptionVO;
import com.cainiao.cntech.dsct.scp.gei.common.constants.OrderType;
import com.cainiao.cntech.dsct.scp.gei.core.GeiQuickBasicManager;
import com.cainiao.cntech.dsct.scp.gei.support.quick.QuickBasicOperation;

import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-06-12 17:24
 * @description 条件搜索通用接口，枚举需要实现
 */
public interface BaseSearchCondition<T> {
    static void register(QuickBasicOperation<?, ?> quickObject, BaseSearchCondition<?>[] searchConditions) {
        // 注册搜索组件
        GeiQuickBasicManager.registerSearch(quickObject, searchConditions);
    }

    /**
     * 字段编码，同一个子枚举中，该code应保证唯一
     */
    String getFieldCode();

    /**
     * 搜索字段名，需要与数据库表的字段对应
     */
    List<String> getSearchField();

    /**
     * 分组字段名，需要与数据库表的字段对应
     */
    default List<String> getGroupField() {
        return null;
    }

    /**
     * 排序字段名，需要与数据库表的字段对应
     */
    default List<String> getOrderField() {
        return null;
    }
    /**
     * 排序方法
     */
    default String getOrderMethod() {
        return OrderType.ASC;
    }

    /**
     * 查询筛选列表前扩展，可自定义在queryWrapper中填充内容
     */
    default Consumer<QueryWrapper<T>> getBeforeListExt() {
        return null;
    }

    /**
     * 转换器，搜索结果转换为OptionVO
     *
     * @return
     */
    Function<T, OptionVO> getConvert();

    /**
     * 子类无需实现
     */
    default OptionVO convert(T t) {
        if (Objects.isNull(t)) {
            return null;
        }
        Function<T, OptionVO> convert = getConvert();
        if (convert == null) {
            return null;
        }
        return convert.apply(t);
    }
}
