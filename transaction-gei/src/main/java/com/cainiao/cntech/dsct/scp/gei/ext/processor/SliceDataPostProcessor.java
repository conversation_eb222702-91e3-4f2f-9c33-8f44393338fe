package com.cainiao.cntech.dsct.scp.gei.ext.processor;

import com.cainiao.cntech.dsct.scp.gei.core.model.SliceDataWrapper;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-14 19:40
 * @description 数据分片后处理器
 */
public interface SliceDataPostProcessor {
    /**
     * 是否支持数据分片
     *
     * @param request 传入数据导出请求体
     * @return true支持，false不支持
     * 如果返回false，那么在拆分子任务时，仅会分配一个任务，不再进行分片
     */
    boolean supportSliceData(Object request);

    /**
     * 同步导出数据之前回调
     *
     * @param request
     */
    void beforeSyncExportData(Object request);

    /**
     * 异步导出数据之前回调，一般用于分片数据回写分页信息
     *
     * @param request
     * @param sliceDataWrapper
     */
    void beforeAsyncSliceExportData(Object request, SliceDataWrapper sliceDataWrapper);
}
