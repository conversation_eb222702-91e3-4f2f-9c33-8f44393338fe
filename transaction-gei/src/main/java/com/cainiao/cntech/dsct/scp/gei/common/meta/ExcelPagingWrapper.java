package com.cainiao.cntech.dsct.scp.gei.common.meta;

import com.cainiao.cntech.dsct.scp.gei.common.request.GeiPageQueryRequest;
import lombok.Data;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 14:33
 * @description 分页包装
 */
@Data
public class ExcelPagingWrapper {

    /**
     * 当前页
     */
    private Long currentPage;
    /**
     * 步长
     */
    private Long pageSize;
    /**
     * 总量
     */
    private Long totalCount;

    public ExcelPagingWrapper() {
    }

    public ExcelPagingWrapper(Long currentPage, Long pageSize, Long totalCount) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
    }

    public static ExcelPagingWrapper of(Long currentPage, Long pageSize, Long totalCount) {
        return new ExcelPagingWrapper(currentPage, pageSize, totalCount);
    }

    public static ExcelPagingWrapper of(GeiPageQueryRequest request, Long totalCount) {
        return new ExcelPagingWrapper(request.getCurrentPage(), request.getPageSize(), totalCount);
    }

}
