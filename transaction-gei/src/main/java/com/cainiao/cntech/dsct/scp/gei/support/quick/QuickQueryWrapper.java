package com.cainiao.cntech.dsct.scp.gei.support.quick;

import com.cainiao.cntech.dsct.scp.gei.support.quick.annotation.QuickQuery;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-03-05 10:25
 * @description
 */
@Data
public class QuickQueryWrapper {
    /**
     * 模型属性名
     */
    private String modelFieldName;
    /**
     * 是否范围查询
     */
    private Boolean isRange = Boolean.FALSE;
    /**
     * 是否查询非空
     */
    private Boolean isNotNull = Boolean.FALSE;
    /**
     * 是否查询空
     */
    private Boolean isNull = Boolean.FALSE;

    public QuickQueryWrapper reset(QuickQuery quickQuery) {
        if (Objects.isNull(quickQuery)) {
            return this;
        }
        this.modelFieldName = null;
        if (StringUtils.isNotBlank(quickQuery.value())) {
            modelFieldName = quickQuery.value();
        }
        isRange = quickQuery.isRange();
        isNull = quickQuery.isNull();
        isNotNull = quickQuery.isNotNull();
        return this;
    }

    public static QuickQueryWrapper of(QuickQuery quickQuery) {
        QuickQueryWrapper queryWrapper = new QuickQueryWrapper();
        return queryWrapper.reset(quickQuery);
    }

    public String getModelFieldName(Field requestField) {
        return StringUtils.isNotBlank(this.modelFieldName) ? this.modelFieldName : requestField.getName();
    }
}
