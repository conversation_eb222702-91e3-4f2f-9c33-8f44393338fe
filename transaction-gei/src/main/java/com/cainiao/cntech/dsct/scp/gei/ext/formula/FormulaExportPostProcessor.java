package com.cainiao.cntech.dsct.scp.gei.ext.formula;

import com.cainiao.cntech.dsct.scp.gei.common.utils.ExcelCellPositionUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.ReflectionUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.RegexUtils;
import com.cainiao.cntech.dsct.scp.gei.core.model.BeforeWriteCellWrapper;
import com.cainiao.cntech.dsct.scp.gei.ext.formula.annotation.Formula;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ExportPostProcessor;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-02-08 16:20
 * @description
 */
public class FormulaExportPostProcessor implements ExportPostProcessor {
    private static final String REGEX = "\\$\\{([^}]+)\\}";
    private final Class<Formula> formulaClass = Formula.class;

    @Override
    public Object postProcessBeforeWriteCellValue(BeforeWriteCellWrapper beforeWriteCellWrapper) {
        if (beforeWriteCellWrapper.getRowData() instanceof Map) {
            return null;
        }
        String fieldName = beforeWriteCellWrapper.getFieldName();
        Class<?> modelClass = beforeWriteCellWrapper.getModelClass();
        Field field = ReflectionUtils.getFieldByName(modelClass, fieldName);
        if (!field.isAnnotationPresent(formulaClass)) {
            return null;
        }
        Formula formula = field.getAnnotation(formulaClass);
        return getFormulaValue(formula, beforeWriteCellWrapper);
    }

    private String getFormulaValue(Formula formula, BeforeWriteCellWrapper beforeWriteCellWrapper) {
        List<String> columnList = beforeWriteCellWrapper.getColumnList();
        String expr = formula.value();
        int rowIndex = beforeWriteCellWrapper.getRowIndex();
        return String.format("=%s", RegexUtils.findAndReplace(REGEX, expr, fieldName -> {
            int columnIndex = 0;
            for (String column : columnList) {
                if (StringUtils.equals(fieldName, column)) {
                    return ExcelCellPositionUtils.getPosition(2, rowIndex, columnIndex);
                }
                columnIndex++;
            }
            return fieldName;
        }));
    }
}
