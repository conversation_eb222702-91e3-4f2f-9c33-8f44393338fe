package com.cainiao.cntech.dsct.scp.gei.processor;

import com.cainiao.cntech.dsct.scp.gei.common.constants.GeiCustomSqlSessionConstant;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-04-24 19:39
 * @description
 */
public class GeiSqlSessionFactoryBeanFactoryPostProcessor implements BeanFactoryPostProcessor {

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory configurableListableBeanFactory) throws BeansException {
        String[] beanNames = configurableListableBeanFactory.getBeanNamesForType(SqlSessionFactory.class);
        // 本身已经处于多sqlSession的场景下，不进行单独设置
        if (beanNames.length > 2) {
            return;
        }
        for (String beanName : configurableListableBeanFactory.getBeanNamesForType(SqlSessionFactory.class)) {
            if (GeiCustomSqlSessionConstant.SESSION_FACTORY_BEAN_NAME.equals(beanName)) {
                continue;
            }
            BeanDefinition bd = configurableListableBeanFactory.getBeanDefinition(beanName);
            bd.setPrimary(true);
            return;
        }
    }
}
