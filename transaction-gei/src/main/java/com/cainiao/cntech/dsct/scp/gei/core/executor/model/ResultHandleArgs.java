package com.cainiao.cntech.dsct.scp.gei.core.executor.model;

import com.cainiao.cntech.dsct.scp.gei.biz.dao.dto.ScpGeiTaskDTO;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import lombok.Data;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-10 16:12
 * @description
 */
@Data
public class ResultHandleArgs {
    private ScpGeiTaskDTO geiTask;
    private GeiTypeEnum geiType;
    private String templateDictCode;

    private ResultHandleArgs(String templateDictCode, GeiTypeEnum geiType, ScpGeiTaskDTO geiTask) {
        this.templateDictCode = templateDictCode;
        this.geiType = geiType;
        this.geiTask = geiTask;
    }

    public static ResultHandleArgs exportArgs(String templateDictCode, ScpGeiTaskDTO geiTask) {
        return new ResultHandleArgs(templateDictCode, GeiTypeEnum.EXPORT, geiTask);
    }

    public static ResultHandleArgs importArgs(String templateDictCode, ScpGeiTaskDTO geiTask) {
        return new ResultHandleArgs(templateDictCode, GeiTypeEnum.IMPORT, geiTask);
    }
}
