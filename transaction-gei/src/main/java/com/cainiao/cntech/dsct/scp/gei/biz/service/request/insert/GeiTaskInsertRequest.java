package com.cainiao.cntech.dsct.scp.gei.biz.service.request.insert;

import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:44
 * @description 主任务列表insert请求
 */
@Data
public class GeiTaskInsertRequest {
    /**
     * '任务名称'
     */
    private String taskName;
    /**
     * '用户请求参数'
     */
    private String requestParams;
    /**
     * '任务的类型
     * @see GeiTypeEnum
     */
    private String taskType;
    /**
     * '数据的总条数'
     */
    private Long dataTotalCnt;
    /**
     * '子任务的数量'
     */
    private Integer childTaskTotalCnt;
    /**
     * 'merge阶段的尝试次数'
     */
    private Integer mergeTryTimes;
    /**
     * 'merge阶段的最大尝试次数'
     */
    private Integer mergeMaxTryTimes;
    /**
     * '任务执行过期的时间'
     */
    private LocalDateTime gmtExpired;
    /**
     * '任务清理过期的时间'
     */
    private LocalDateTime gmtClearExpired;
    /**
     * '任务的扩展属性'
     */
    private String properties;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 执行模版的编码
     */
    private String executeTemplateCode;
    /**
     * 模版绑定的字典编码
     */
    private String templateDictCode;
}
