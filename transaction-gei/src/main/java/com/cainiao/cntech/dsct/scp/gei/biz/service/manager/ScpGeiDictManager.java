package com.cainiao.cntech.dsct.scp.gei.biz.service.manager;

import com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiDictDO;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper.ScpGeiDictMapper;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;

import javax.annotation.Resource;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 16:40
 * @description
 */
public class ScpGeiDictManager {

    @Resource
    private ScpGeiDictMapper scpGeiDictMapper;

    public ScpGeiDictDO queryByCode(String code, GeiTypeEnum typeEnum){
        return scpGeiDictMapper.selectByCode(code, typeEnum.getCode());
    }


}
