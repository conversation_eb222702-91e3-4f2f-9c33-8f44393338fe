package com.cainiao.cntech.dsct.scp.gei.biz.dao.dto;

import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTaskStatusEnum;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:19
 * @description 通用导入导出主任务
 */
@Data
public class ScpGeiTaskDTO {
    /**
     * '创建时间'
     */
    private LocalDateTime gmtCreate;
    /**
     * '修改时间'
     */
    private LocalDateTime gmtModified;
    /**
     * '任务ID'
     */
    private Long taskId;
    /**
     * '任务名称'
     */
    private String taskName;
    /**
     * '用户请求参数'
     */
    private String requestParams;
    /**
     * '任务的类型
     * @see GeiTypeEnum
     */
    private String taskType;
    /**
     * 任务类型描述
     */
    private String taskTypeDesc;
    /**
     * 任务状态，CREATED新建，AWAIT待运行，RUNNING运行中，FAILED异常，SUCCESS成功
     * @see GeiTaskStatusEnum
     */
    private String status;
    private String statusName;
    /**
     * '任务状态产生的原因'
     */
    private String statusDesc;
    /**
     * '任务开始处理的时间戳'
     */
    private LocalDateTime gmtProcessStarted;
    /**
     * '任务结束的时间戳'
     */
    private LocalDateTime gmtProcessFinished;
    /**
     * '数据的总条数'
     */
    private Long dataTotalCnt;
    /**
     * '子任务的数量'
     */
    private Integer childTaskTotalCnt;
    /**
     * '合并开始的时间'
     */
    private LocalDateTime gmtMergeStarted;
    /**
     * 'merge阶段的尝试次数'
     */
    private Integer mergeTryTimes;
    /**
     * 'merge阶段的最大尝试次数'
     */
    private Integer mergeMaxTryTimes;
    /**
     * '任务执行过期的时间'
     */
    private LocalDateTime gmtExpired;
    /**
     * '任务清理过期的时间'
     */
    private LocalDateTime gmtClearExpired;
    /**
     * '文件名'
     */
    private String fileName;
    /**
     * '文件地址'
     */
    private String fileAddress;
    /**
     * '任务的扩展属性'
     */
    private String properties;

    /**
     * 执行模版的编码
     */
    private String executeTemplateCode;
    /**
     * 模版绑定的字典编码
     */
    private String templateDictCode;
    /**
     * '创建人编码'
     */
    private String creatorCode;
    /**
     * '创建人名称'
     */
    private String creatorName;
    /**
     * '操作人编码'
     */
    private String operatorCode;
    /**
     * '操作人名称'
     */
    private String operatorName;

    /**
     * '未完成的子任务数量'
     */
    private Long taskCompletedCnt;
    /**
     * '未完成的子任务数量'
     */
    private Long taskUncompletedCnt;

    /**
     * 任务进度
     */
    private BigDecimal taskProgress;

    /**
     * 允许下载标记
     */
    private Boolean allowDownload = Boolean.FALSE;
}
