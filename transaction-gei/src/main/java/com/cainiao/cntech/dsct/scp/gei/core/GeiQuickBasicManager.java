package com.cainiao.cntech.dsct.scp.gei.core;

import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.biz.web.model.OptionVO;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelPageMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.common.utils.ReflectionUtils;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import com.cainiao.cntech.dsct.scp.gei.support.quick.QuickBasicOperation;
import com.cainiao.cntech.dsct.scp.gei.support.quick.QuickService;
import com.cainiao.cntech.dsct.scp.gei.support.quick.annotation.QuickBasicService;
import com.cainiao.cntech.dsct.scp.gei.support.quick.search.BaseSearchCondition;
import com.cainiao.cntech.dsct.scp.gei.support.quick.search.SearchService;
import com.cainiao.cntech.dsct.scp.gei.support.template.QuickEIportDataTemplate;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ConfigurableApplicationContext;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-21 10:16
 * @description 基本操作管理器，crud
 * @see QuickBasicOperation
 */
public class GeiQuickBasicManager implements ApplicationRunner {
    private static final Class<QuickBasicService> QUICK_BASIC_SERVICE = QuickBasicService.class;
    /**
     * 导出数据模版标记的注解映射map： key=QuickBasicService#value(), value：SearchClass
     */
    private static final Map<String, BaseSearchCondition<Object>> SEARCH_CLASS_MAP = new LinkedHashMap<>();
    /**
     * 快速基本操作模版： key=QuickBasicService#value(), value：快速基本操作模版实现对象
     */
    private final Map<String, QuickEIportDataTemplate<? extends ImportTemplateVO, Object, Object>> QUICK_BASIC_OPERATIONS = new LinkedHashMap<>();
    /**
     * 导出数据模版标记的注解映射map： key=QuickBasicService#value(), value：QuickBasicService
     */
    private final Map<String, QuickBasicService> QUICK_BASIC_ANNOTATIONS = new LinkedHashMap<>();
    @Resource
    private ConfigurableApplicationContext context;

    public static void registerSearch(QuickBasicOperation<?, ?> quickObject, BaseSearchCondition<?>[] searchConditions) {
        for (BaseSearchCondition<?> baseSearchCondition : searchConditions) {
            registerSearch(quickObject, baseSearchCondition);
        }
    }

    public static void registerSearch(QuickBasicOperation<?, ?> quickObject, BaseSearchCondition<?> searchCondition) {
        Class<?> clazz = quickObject.getClass();
        if (clazz.isAnnotationPresent(QUICK_BASIC_SERVICE)) {
            QuickBasicService annotation = clazz.getAnnotation(QUICK_BASIC_SERVICE);
            SEARCH_CLASS_MAP.put(genSearchCode(annotation.value(), searchCondition.getFieldCode()), (BaseSearchCondition<Object>) searchCondition);
        }
    }

    private static String genSearchCode(String code, String fieldCode) {
        return code + fieldCode;
    }

    public List<OptionVO> getSearchItems(String code, String fieldCode, String queryKey) {
        QuickEIportDataTemplate<? extends ImportTemplateVO, Object, Object> template = getOperation(code);
        QuickService<Object> service = template.getIService();
        BaseSearchCondition<Object> searchCondition = SEARCH_CLASS_MAP.get(genSearchCode(code, fieldCode));
        return SearchService.get(service, searchCondition, queryKey);
    }

    public ExcelPageMetaInfoWrapper list(String code, Map<String, Object> queryMap) {
        QuickEIportDataTemplate<? extends ImportTemplateVO, Object, Object> template = getOperation(code);
        Object request = ReflectionUtils.resolveMapToGenerics(template, QuickBasicOperation.class, queryMap, 0);
        return template.list(request);
    }

    public Boolean insert(String code, List<Map<String, Object>> list) {
        QuickEIportDataTemplate<? extends ImportTemplateVO, Object, Object> template = getOperation(code);
        List<Object> request = ReflectionUtils.resolveListMapToGenerics(template, QuickBasicOperation.class, list, 1);
        return template.insert(request);
    }

    public Boolean updateById(String code, List<Map<String, Object>> list) {
        QuickEIportDataTemplate<? extends ImportTemplateVO, Object, Object> template = getOperation(code);
        List<Object> request = ReflectionUtils.resolveListMapToGenerics(template, QuickBasicOperation.class, list, 1);
        return template.updateById(request);
    }

    public Boolean deleteById(String code, List<?> ids) {
        QuickEIportDataTemplate<? extends ImportTemplateVO, Object, Object> template = getOperation(code);
        return template.deleteById(ids);
    }

    private QuickEIportDataTemplate<? extends ImportTemplateVO, Object, Object> getOperation(String code) {
        QuickEIportDataTemplate<? extends ImportTemplateVO, Object, Object> template = QUICK_BASIC_OPERATIONS.get(code);
        Assert.notNull(template, ErrorCode.SYSTEM_ERROR, "快速基本操作模版获取失败，dataCode={}", code);
        return template;
    }

    private QuickBasicService getServiceAnnotation(String code) {
        QuickBasicService service = QUICK_BASIC_ANNOTATIONS.get(code);
        Assert.notNull(service, ErrorCode.SYSTEM_ERROR, "快速基本操作模版获取失败，dataCode={}", code);
        return service;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        Map<String, QuickEIportDataTemplate> exportBeans = context.getBeansOfType(QuickEIportDataTemplate.class);
        for (Map.Entry<String, QuickEIportDataTemplate> entry : exportBeans.entrySet()) {
            QuickEIportDataTemplate<? extends ImportTemplateVO, Object, Object> template = entry.getValue();
            Class<?> clazz = AopProxyUtils.ultimateTargetClass(template);
            if (clazz.isAnnotationPresent(QUICK_BASIC_SERVICE)) {
                QuickBasicService annotation = clazz.getAnnotation(QUICK_BASIC_SERVICE);
                QUICK_BASIC_OPERATIONS.put(annotation.value(), template);
                QUICK_BASIC_ANNOTATIONS.put(annotation.value(), annotation);
            }
        }
    }
}
