package com.cainiao.cntech.dsct.scp.gei.ext.converter;

import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterRegistry;
import org.springframework.core.convert.support.DefaultConversionService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-06-14 16:23
 * @description 导入数据扩展转换器
 * 用于导入数据将map转换为对象
 */
public class ImportExtConversionService extends DefaultConversionService {
    private volatile static ImportExtConversionService service;

    private ImportExtConversionService() {
        super();
        addDefaultConverters(this);
    }

    public static ImportExtConversionService getInstance() {
        if (service == null) {
            synchronized (ImportExtConversionService.class) {
                if (service == null) {
                    service = new ImportExtConversionService();
                }
            }
        }
        return service;
    }

    public static void addDefaultConverters(ConverterRegistry converterRegistry) {
        converterRegistry.addConverter(new LocalDateStringToLocalDateConverter());
        converterRegistry.addConverter(new LocalDateStringToLocalDateTimeConverter());
        converterRegistry.addConverter(new StringToIntegerCompatibleDateStringConverter());
    }

    final static class LocalDateStringToLocalDateConverter implements Converter<String, LocalDate> {
        private final List<String> dateFormats = Arrays.asList(
                DateUtil.STANDARD_DATE_FORMAT,
                DateUtil.EXCEL_STANDARD_DATE_FORMAT,
                DateUtil.EXCEL_DATE_FORMAT
        );
        LocalDateStringToLocalDateConverter() {
        }

        public LocalDate convert(String source) {
            LocalDate localDate = null;
            for (String dateFormat : dateFormats) {
                localDate = DateUtil.stringToLocalDate(source, dateFormat);
                if (Objects.nonNull(localDate)) {
                    return localDate;
                }
            }
            return localDate;
        }
    }

    final static class LocalDateStringToLocalDateTimeConverter implements Converter<String, LocalDateTime> {
        private final List<String> dateFormats = Arrays.asList(
            DateUtil.DEFAULT_DATETIME_FORMAT,
            DateUtil.IPART_DATETIME_FORMAT,
            DateUtil.EXCEL_IPART_DATETIME_FORMAT
        );
        LocalDateStringToLocalDateTimeConverter() {
        }

        public LocalDateTime convert(String source) {
            LocalDateTime localDateTime = null;
            for (String dateFormat : dateFormats) {
                localDateTime = DateUtil.stringToLocalDateTime(source, dateFormat);
                if (Objects.nonNull(localDateTime)) {
                    return localDateTime;
                }
            }
            return localDateTime;
        }
    }

    final static class StringToIntegerCompatibleDateStringConverter implements Converter<String, Integer> {
        StringToIntegerCompatibleDateStringConverter() {
        }

        public Integer convert(String source) {
            Integer date = DateUtil.dateToInteger(DateUtil.stringToLocalDate(source));
            if (date != 0) {
                return date;
            }
            try {
                return Integer.valueOf(source);
            } catch (NumberFormatException e) {
                return null;
            }
        }
    }
}
