package com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 16:31
 * @description 模版配置
 */
@Data
@TableName("scp_gei_template_cfg")
public class ScpGeiDictDO {
    /**
     * '创建时间'
     */
    private LocalDateTime gmtCreate;
    /**
     * '修改时间'
     */
    private LocalDateTime gmtModified;

    /**
     * 模版类型：EXPORT导出模版配置，IMPORT导入模版配置
     * @see GeiTypeEnum
     */
    private String type;

    /**
     * '模版编码'
     */
    private String code;

    /**
     * '模版配置'
     */
    private String value;

    /**
     * 模版使用模式， 0全量覆盖，1替换
     */
    private Integer mode;

    /**
     * '模版描述'
     */
    private String description;
}
