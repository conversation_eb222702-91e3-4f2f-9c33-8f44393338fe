package com.cainiao.cntech.dsct.scp.gei.configuration;

import com.cainiao.cntech.dsct.scp.gei.common.constants.StorageType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 11:41
 * @description
 */
@Data
@ConfigurationProperties(prefix = "gei.storage")
public class GeiStorageProperties {
    /**
     * @see StorageType
     */
    private String type = "oss";
    /**
     * oss类型配置
     */
    private OSS oss;
    /**
     * minio类型配置
     */
    private Minio minio;

    @Data
    public static class OSS {
        private String ak;
        private String sk;
        private String endpoint;
        private String bucket;
        private String path;
    }
    @Data
    public static class Minio {
        private String ak;
        private String sk;
        private String endpoint;
        private String bucket;
        private String domain;
    }
}
