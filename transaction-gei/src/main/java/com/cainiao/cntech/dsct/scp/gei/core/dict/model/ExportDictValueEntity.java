package com.cainiao.cntech.dsct.scp.gei.core.dict.model;

import lombok.Data;

import java.util.Collection;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 17:53
 * @description
 */
@Data
public class ExportDictValueEntity {
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 子meta
     */
    private Collection<ExportDictValueEntity> children;
}
