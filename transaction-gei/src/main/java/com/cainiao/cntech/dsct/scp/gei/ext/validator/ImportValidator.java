package com.cainiao.cntech.dsct.scp.gei.ext.validator;


import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;

import java.lang.reflect.Field;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 15:53
 * @description 导入数据验证器注册器
 * 泛型<RowValidator>代指行校验器类型
 * 泛型<ColValidatorValidator>代指字段校验器类型
 */
public interface ImportValidator<T> {

    default boolean require(T t, Field field, String fieldName) {
        return true;
    }

    /**
     * 规则校验
     *
     * @param t 对象
     * @return 返回错误信息，如果校验通过，返回null
     */
    String validate(ImportDataWrapper<T> importDataWrapper, T t, Field field, Object fieldValue);
}
