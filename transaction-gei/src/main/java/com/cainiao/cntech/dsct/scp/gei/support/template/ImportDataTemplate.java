package com.cainiao.cntech.dsct.scp.gei.support.template;

import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.GenericsSearchUtils;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 11:06
 * @description 导入数据模版 功能
 * <p>
 * 这里泛型<I>代指模版的VO类型
 */
public interface ImportDataTemplate<I extends ImportTemplateVO> {

    /**
     * 合并请求维度和字段
     */
    static List<String> mergedDimAndFields(ImportQueryRequest request) {
        List<String> mergedDimField = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getDimensionList())) {
            mergedDimField.addAll(request.getDimensionList());
        }
        if (CollectionUtils.isNotEmpty(request.getFieldsList())) {
            mergedDimField.addAll(request.getFieldsList());
        }
        return mergedDimField;
    }

    /**
     * 导入数据
     *
     * @param importDataWrapper 解析后的数据列表 以及 维度的包装
     */
    ImportResult importData(ImportDataWrapper<I> importDataWrapper);

    /**
     * 获取导入模版的字段列信息
     * *** 如无特殊需求处理metainfo列表，一般无需重写 ***
     *
     * @param request 请求对象，包含维度，字段等信息
     */
    default List<ExcelMetaInfo> getImportTemplateMetaInfo(ImportQueryRequest request) {
        Class<?> modelClass = GenericsSearchUtils.searchClass(this, ImportDataTemplate.class, 0);
        return ExcelMetaInfoCreator.create(modelClass, request.isDeep(), mergedDimAndFields(request).toArray(new String[0]));
    }

    /**
     * 构建导入模版的表格数据
     * *** 如果透出到前端的导入模版中不需要数据，则无需重写 ***
     *
     * @param request 查询参数【包含了维度，选择字段等】
     * @return 导入模版的数据
     */
    default List<I> getImportTemplateData(ImportQueryRequest request) {
        return null;
    }

    /**
     * 获取标题所在行
     * 0-第1行
     * 1-第2行
     * 导入模版中如果不需要模版描述时，改值设置为0， 一般无需重写
     *
     * @return HeadRowNumber
     */
    default int getHeadRowNumber() {
        return 1;
    }

    /**
     * 获取导入模版的描述
     * 默认解析注解标记的描述内容，如果不需要模版描述时，可在注解中填写空字符串，一般无需重写
     *
     * @param request
     * @return
     */
    default String getTemplateDesc(ImportQueryRequest request, ImportService importService) {
        if (importService != null) {
            return importService.templateDesc();
        }
        return this.getClass().getSimpleName();
    }
}
