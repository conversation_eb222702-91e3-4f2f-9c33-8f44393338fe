package com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper;

import com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiTaskDO;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.dto.ScpGeiTaskDTO;

import java.util.List;
import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:31
 * @description 通用导入导出主任务Mapper
 */
public interface ScpGeiTaskMapper {
    List<ScpGeiTaskDTO> selectByCondition(Map<String, Object> params);
    Long selectCount(Map<String, Object> params);
    /**
     * 查询文件已过期的任务
     * @return
     */
    List<ScpGeiTaskDO> selectExpireTaskList();
    /**
     * 查询执行超时的任务
     * @return
     */
    List<ScpGeiTaskDO> selectProcessExpireTaskList();

    Long insert(ScpGeiTaskDO params);

    Long delete(List<Long> taskIds);

    Long updateByTaskId(ScpGeiTaskDO params);

    Long closeProcessExpireByTaskId(List<Long> taskIds);
}
