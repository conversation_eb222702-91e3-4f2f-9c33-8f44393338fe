package com.cainiao.cntech.dsct.scp.gei.biz.service.service;

import com.cainiao.cntech.dsct.scp.gei.biz.service.dto.ScpGeiChildTaskDTO;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.query.GeiChildTaskPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.update.GeiChildTaskUpdateRequest;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:31
 * @description 通用导入导出子任务Service
 */
public interface ScpGeiChildTaskService {
    List<ScpGeiChildTaskDTO> queryPageData(GeiChildTaskPageQueryRequest request);
    List<ScpGeiChildTaskDTO> queryByParendId(Long parentId);
    Long queryCount(GeiChildTaskPageQueryRequest request);
    /**
     * 按任务id修改子任务信息
     * @param request
     * @return
     */
    Long updateByTaskId(GeiChildTaskUpdateRequest request);

    /**
     * 按任务id修改子任务信息
     *
     * @param parentId
     * @return
     */
    Long updateNotIsoTaskStatusFailedByTaskId(Long parentId);
}
