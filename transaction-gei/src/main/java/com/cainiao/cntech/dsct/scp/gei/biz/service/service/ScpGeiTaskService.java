package com.cainiao.cntech.dsct.scp.gei.biz.service.service;

import com.cainiao.cntech.dsct.scp.gei.biz.dao.dto.ScpGeiTaskDTO;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.insert.GeiChildTaskInsertRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.insert.GeiTaskInsertRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.query.GeiTaskPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.update.GeiTaskUpdateRequest;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:31
 * @description 通用导入导出主任务Service
 */
public interface ScpGeiTaskService {
    List<ScpGeiTaskDTO> queryPageData(GeiTaskPageQueryRequest request);
    Long queryCount(GeiTaskPageQueryRequest request);

    /**
     * 查询文件已过期的任务列表
     */
    List<ScpGeiTaskDTO> queryExpireTaskList();
    /**
     * 查询执行超时的任务列表
     */
    List<ScpGeiTaskDTO> queryProcessExpireTaskList();

    /**
     * 按任务id查询主任务信息
     * @param taskId
     * @return
     */
    ScpGeiTaskDTO getByTaskId(Long taskId);
    /**
     * 按任务id修改主任务信息
     * @param request
     * @return
     */
    Long updateByTaskId(GeiTaskUpdateRequest request);

    /**
     * 创建任务
     * @param parentTask 父任务请求体
     * @param childTasks 子任务请求体
     * @return 返回父任务id
     */
    Long createTask(GeiTaskInsertRequest parentTask, List<GeiChildTaskInsertRequest> childTasks);

    /**
     * 清除任务
     * @param parendTaskIds
     * @return
     */
    Long clearTask(List<Long> parendTaskIds);

    /**
     * 关闭处理超时的任务
     * @param parendTaskIds
     * @return
     */
    Long closeProcessExpireTask(List<Long> parendTaskIds);
}
