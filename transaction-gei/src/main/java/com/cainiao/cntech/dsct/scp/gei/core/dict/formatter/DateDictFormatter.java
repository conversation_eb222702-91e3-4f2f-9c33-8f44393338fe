package com.cainiao.cntech.dsct.scp.gei.core.dict.formatter;

import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import com.cainiao.cntech.dsct.scp.gei.common.utils.RegexUtils;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 16:46
 * @description Y: 年、M：月、W：周、D：天
 * date{yyyy-MM-dd_+1Y} 当前日期加一年
 * date{yyyy/MM/dd_-1M} 当前日期减一个月
 */
public class DateDictFormatter implements DictFormatter {
    public static final String DELIMITER = "_";
    /**
     * 区分每一组，方便维护
     */
    private static final String REGEX_ALL = "date\\{yyyy\\W+MM\\W+dd_[+-]\\d{1,}[YMWD]\\}";
    private static final String REGEX_INT = "date\\{yyyyMMdd_[+-]\\d{1,}[YMWD]\\}";
    private static final String REGEX_Y = "date\\{yyyy_[+-]\\d{1,}[YMWD]\\}";
    private static final String REGEX_YM = "date\\{yyyy\\W+MM_[+-]\\d{1,}[YMWD]\\}";
    private static final String REGEX_YD = "date\\{yyyy\\W+dd_[+-]\\d{1,}[YMWD]\\}";
    private static final String REGEX_MD = "date\\{MM\\W+dd_[+-]\\d{1,}[YMWD]\\}";
    private static final String REGEX_M = "date\\{MM_[+-]\\d{1,}[YMWD]\\}";
    private static final String REGEX_D = "date\\{dd_[+-]\\d{1,}[YMWD]\\}";


    private static final List<String> REGEX_LIST = Arrays.asList(
            REGEX_ALL, REGEX_INT, REGEX_Y, REGEX_YM, REGEX_YD, REGEX_MD, REGEX_M, REGEX_D
    );

    public static boolean validateStringWithRegex(String input) {
        for (String regex : REGEX_LIST) {
            if (RegexUtils.matches(regex, input)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean support(String input) {
        return input.startsWith("date{") && input.endsWith("}");
    }

    @Override
    public String format(String input) {
        try {
            String expr = DictFormatter.subFormat(input, 5);
            LocalDate now = LocalDate.now();
            if (!validateStringWithRegex(input)) {
                return DateUtil.localDateToString(now, expr);
            }
            String dateFormat = expr.substring(0, expr.lastIndexOf(DELIMITER));
            String dateCalc = expr.substring(expr.lastIndexOf(DELIMITER) + 1);
            LocalDate localDate = calcDate(now, dateCalc);
            return DateUtil.localDateToString(localDate, dateFormat);
        } catch (Exception ignored) {
        }
        return input;
    }

    private LocalDate calcDate(LocalDate date, String dateCalc) {
        try {
            int number = Integer.parseInt(dateCalc.substring(0, dateCalc.length() - 1));
            String expr = dateCalc.substring(dateCalc.length() - 1);
            switch (expr) {
                case "Y":
                    date = DateUtil.plusYears(date, number);
                    break;
                case "M":
                    date = DateUtil.plusMonths(date, number);
                    break;
                case "W":
                    date = DateUtil.plusWeeks(date, number);
                    break;
                case "D":
                    date = DateUtil.plusDays(date, number);
                    break;
                default:
                    return date;
            }
        } catch (Exception ignored) {
        }
        return date;
    }
}
