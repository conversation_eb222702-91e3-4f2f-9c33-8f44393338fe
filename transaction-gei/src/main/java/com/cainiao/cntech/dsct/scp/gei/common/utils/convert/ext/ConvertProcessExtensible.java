package com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext;

import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.model.ConvertExtensibleWrapper;

/**
 * <AUTHOR>
 * @date 2023-08-22 22:33:50
 * @description: 抽象的处理扩展
 */
public interface ConvertProcessExtensible<T> {

    /**
     * 将传递原属性的值，返回同类型的结果以替换原属性值
     * @param ew 转换扩展包装类，内包含了转换时的源类/属性，目标类/属性的信息
     * @see ConvertExtensibleWrapper
     * @return 返回结果非null时，将作为转换值 对目标类属性进行赋值操作
     */
    T doConvert(ConvertExtensibleWrapper ew) throws IllegalAccessException;
}
