package com.cainiao.cntech.dsct.scp.gei.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Supplier;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 15:27
 * @description 线程池拒绝策略
 */
@Getter
@AllArgsConstructor
public enum GeiPoolRejectedStrategy {
    CALLER("CALLER", "调用线程执行", ThreadPoolExecutor.CallerRunsPolicy::new),
    ABORT("ABORT", "抛弃任务并抛异常", ThreadPoolExecutor.AbortPolicy::new),
    DISCARD("DISCARD", "抛弃任务不抛异常", ThreadPoolExecutor.DiscardPolicy::new),
    DISCARD_OLDEST("DiscardOldestPolicy", "丢弃阻塞队列最前面的任务，添加新任务", ThreadPoolExecutor.DiscardOldestPolicy::new),
    ;
    private final String code;
    private final String name;
    private final Supplier<RejectedExecutionHandler> creator;
    public boolean isThis(String code){
        return StringUtils.equalsIgnoreCase(getCode(), code);
    }
    public static GeiPoolRejectedStrategy getByCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (GeiPoolRejectedStrategy value : GeiPoolRejectedStrategy.values()) {
            if (StringUtils.equalsIgnoreCase(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
    public RejectedExecutionHandler getStrategy(){
        return creator.get();
    }
}
