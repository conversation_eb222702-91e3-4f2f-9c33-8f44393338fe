package com.cainiao.cntech.dsct.scp.gei.ext.validator.common;


import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.ext.validator.ImportValidator;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelValid;

import java.lang.reflect.Field;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-13 18:49
 * @description
 */
public class GtZeroImportValidator<T> implements ImportValidator<T> {
    @Override
    public boolean require(T t, Field field, String fieldName) {
        return field.isAnnotationPresent(ExcelValid.class) && field.getAnnotation(ExcelValid.class).gtZero();
    }

    @Override
    public String validate(ImportDataWrapper<T> importDataWrapper, T t, Field field, Object fieldValue) {
        if (fieldValue instanceof Number && ((Number) fieldValue).doubleValue() < 1) {
            return "%s必须大于0！";
        }
        return null;
    }
}
