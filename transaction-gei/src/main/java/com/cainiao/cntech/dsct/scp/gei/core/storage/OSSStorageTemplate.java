package com.cainiao.cntech.dsct.scp.gei.core.storage;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ResponseHeaderOverrides;
import com.cainiao.cntech.dsct.scp.gei.configuration.GeiStorageProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 10:30
 * @description
 */
@Slf4j
public class OSSStorageTemplate implements StorageTemplate {
    private GeiStorageProperties.OSS config;
    private OSS ossClient;

    @Override
    public void initial(GeiStorageProperties geiStorageProperties) {
        config = geiStorageProperties.getOss();
        ossClient = new OSSClientBuilder().build(config.getEndpoint(), config.getAk(), config.getSk());
    }

    @Override
    public void destroy() {
        if (ossClient != null) {
            ossClient.shutdown();
        }
    }

    @Override
    public boolean isExists(String key) {
        return ossClient.doesObjectExist(config.getBucket(), handleKey(key));
    }

    @Override
    public InputStream getResource(String key) {
        key = handleKey(key);
        if (isExists(key)) {
            OSSObject object = ossClient.getObject(config.getBucket(), key);
            return object.getObjectContent();
        }
        return null;
    }

    @Override
    public boolean upload(String key, byte[] resource) {
        key = handleKey(key);
        ossClient.putObject(config.getBucket(), key, new ByteArrayInputStream(resource));
        return true;
    }

    @Override
    public boolean delete(String key) {
        key = handleKey(key);
        ossClient.deleteObject(config.getBucket(), key);
        return true;
    }

    @Override
    public String getUrl(String key, String fileName, LocalDateTime expireTime) {
        key = handleKey(key);
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(config.getBucket(), key);
        request.setExpiration(Date.from(expireTime.atZone(ZoneId.systemDefault()).toInstant()));
        ResponseHeaderOverrides responseHeaderOverrides = new ResponseHeaderOverrides();
        responseHeaderOverrides.setContentDisposition(String.format("attachment;filename=\"%s.xlsx\"", fileName));
        //responseHeaderOverrides.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        request.setResponseHeaders(responseHeaderOverrides);
        URL signedUrl = ossClient.generatePresignedUrl(request);
        String url = signedUrl.toString();
        if (url.startsWith("http://")) {
            url = url.replace("http://", "https://");
        }
        return url;
    }

    private String handleKey(String key) {
        String path = config.getPath();
        if (StringUtils.isBlank(path)) {
            return key;
        }
        path = path.startsWith("/") ? path.substring(1) : path;
        return path.endsWith("/") ? path + key : path + "/" + key;
    }
}
