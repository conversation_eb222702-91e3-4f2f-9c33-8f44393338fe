package com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper;

import com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiDictDO;
import org.apache.ibatis.annotations.Param;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 16:38
 * @description
 */
public interface ScpGeiDictMapper {
    /**
     * 根据code查询模版
     *
     * @param code
     * @return
     */
    ScpGeiDictDO selectByCode(@Param("code") String code, @Param("type") String type);
}
