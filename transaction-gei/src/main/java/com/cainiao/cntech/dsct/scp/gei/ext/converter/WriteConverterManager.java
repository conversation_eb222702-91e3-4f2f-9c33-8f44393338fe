package com.cainiao.cntech.dsct.scp.gei.ext.converter;

import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-12 14:17
 * @description 转换器管理者
 * 会通过contextAware回调函数，将容器所有的converter收集到本类中，通过调用convert函数对特定类型的值进行转换
 */
public class WriteConverterManager implements ApplicationContextAware {
    private final static Map<String, ExcelWriteConverter<?, ?>> CONTAINER
            = Maps.newHashMapWithExpectedSize(8);

    private static void register(String key, ExcelWriteConverter<?, ?> converter) {
        CONTAINER.put(key, converter);
    }

    public static Object convert(Object fieldValue, List<String> assignUseWriteConverterNames, List<ExcelWriteConverter> assignUseExcelWriteConverters) {
        if (fieldValue == null) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(assignUseWriteConverterNames)) {
            for (String assignUseWriteConverterName : assignUseWriteConverterNames) {
                try {
                    ExcelWriteConverter converter = CONTAINER.get(assignUseWriteConverterName);
                    if (Objects.nonNull(converter) && converter.support(fieldValue)) {
                        return converter.convert(fieldValue);
                    }
                } catch (Exception ignored) {
                }
            }
        }
        if (CollectionUtils.isNotEmpty(assignUseExcelWriteConverters)) {
            for (ExcelWriteConverter converter : assignUseExcelWriteConverters) {
                try {
                    if (Objects.nonNull(converter) && converter.support(fieldValue)) {
                        return converter.convert(fieldValue);
                    }
                } catch (Exception ignored) {
                }
            }
        }
        for (ExcelWriteConverter converter : CONTAINER.values()) {
            try {
                if (converter.support(fieldValue)) {
                    return converter.convert(fieldValue);
                }
            } catch (Exception ignored) {
            }
        }
        return fieldValue;
    }

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        Map<String, ExcelWriteConverter> beansOfType = context.getBeansOfType(ExcelWriteConverter.class);
        beansOfType.forEach(WriteConverterManager::register);
    }
}
