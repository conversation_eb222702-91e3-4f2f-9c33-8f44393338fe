package com.cainiao.cntech.dsct.scp.gei.core.storage;

import com.cainiao.cntech.dsct.scp.gei.common.exception.AppException;
import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.configuration.GeiStorageProperties;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectOptions;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 19:20
 * @description
 */
@Slf4j
public class MinioStorageTemplate implements StorageTemplate {
    private GeiStorageProperties.Minio config;
    private MinioClient minioClient;

    @Override
    public void initial(GeiStorageProperties geiStorageProperties) {
        config = geiStorageProperties.getMinio();
        this.minioClient = new MinioClient(config.getEndpoint(), config.getAk(), config.getSk());
    }

    @Override
    public void destroy() {
    }

    @Override
    public boolean isExists(String key) {
        BucketExistsArgs bucketExistsArgs = BucketExistsArgs.builder().bucket(config.getBucket()).build();
        try {
            return minioClient.bucketExists(bucketExistsArgs);
        } catch (Exception e) {
            log.error("com.cainiao.cntech.dsct.scp.gei.core.storage.MinioStorageTemplate#isExists error. Bucket is {}, key is {})", config.getBucket(), key, e);
        }
        return false;
    }

    @Override
    public InputStream getResource(String key) {
        return null;
    }

    @Override
    public boolean upload(String key, byte[] resource) {
        try {
            if (!isExists(key)) {
                MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder().bucket(config.getBucket()).build();
                minioClient.makeBucket(makeBucketArgs);
            }
            InputStream inputStream = new ByteArrayInputStream(resource);
            long partSize = 0L;
            if (resource.length > PutObjectOptions.MIN_MULTIPART_SIZE) {
                partSize = PutObjectOptions.MIN_MULTIPART_SIZE;
            }
            minioClient.putObject(config.getBucket(), key, inputStream, new PutObjectOptions(resource.length, partSize));
            return true;
        } catch (Exception e) {
            log.error("com.cainiao.cntech.dsct.scp.gei.core.storage.MinioStorageTemplate#upload error. Bucket is {}, key is {})", config.getBucket(), key, e);
            return false;
        }
    }

    @Override
    public boolean delete(String key) {
        try {
            if (!isExists(key)) {
                MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder().bucket(config.getBucket()).build();
                minioClient.makeBucket(makeBucketArgs);
            }
            minioClient.removeObject(config.getBucket(), key);
            return true;
        } catch (Exception e) {
            log.error("com.cainiao.cntech.dsct.scp.gei.core.storage.MinioStorageTemplate#delete error. Bucket is {}, key is {})", config.getBucket(), key, e);
            return false;
        }
    }

    @Override
    public String getUrl(String key, String fileName, LocalDateTime expireTime) {
        try {
            if (isExists(key)) {
                return minioClient.getObjectUrl(config.getBucket(), key) + "?fileName=" + fileName;
            }
            return null;
        } catch (Exception e) {
            log.error("com.cainiao.cntech.dsct.scp.gei.core.storage.MinioStorageTemplate#getUrl error. Bucket is {}, key is {})", config.getBucket(), key, e);
            throw new AppException(ErrorCode.SYSTEM_ERROR, "minio 获取文件url出错");
        }
    }
}
