package com.cainiao.cntech.dsct.scp.gei.support.quick;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-16 20:50
 * @description
 */
public interface QuickService<T> extends IService<T> {
    boolean upsert(List<T> requests, List<SFunction<T, ?>> primaryKeys);
}
