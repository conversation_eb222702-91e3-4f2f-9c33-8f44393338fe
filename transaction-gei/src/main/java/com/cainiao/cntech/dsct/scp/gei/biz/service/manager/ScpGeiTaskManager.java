package com.cainiao.cntech.dsct.scp.gei.biz.service.manager;

import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiTaskDO;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.dto.ScpGeiTaskDTO;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper.ScpGeiTaskMapper;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.query.GeiTaskPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTaskStatusEnum;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import com.cainiao.cntech.dsct.scp.gei.common.error.GeiError;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.PageUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.github.pagehelper.PageHelper;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:45
 * @description
 */
public class ScpGeiTaskManager {
    @Resource
    private ScpGeiTaskMapper scpGeiTaskMapper;

    public List<ScpGeiTaskDTO> queryPageData(GeiTaskPageQueryRequest request) {
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        return scpGeiTaskMapper.selectByCondition(JsonUtils.toMap(request));
    }
    public Long queryCount(GeiTaskPageQueryRequest request) {
        return scpGeiTaskMapper.selectCount(JsonUtils.toMap(request));
    }
    public List<ScpGeiTaskDTO> queryExpireTaskList() {
        return GeiCommonConvert.convert(scpGeiTaskMapper.selectExpireTaskList(), ScpGeiTaskDTO.class);
    }
    public List<ScpGeiTaskDTO> queryProcessExpireTaskList() {
        return GeiCommonConvert.convert(scpGeiTaskMapper.selectProcessExpireTaskList(), ScpGeiTaskDTO.class);
    }

    public Long updateByTaskId(ScpGeiTaskDO scpGeiTaskDO) {
        Assert.notNull(scpGeiTaskDO.getTaskId(), ErrorCode.ILLEGAL_ARGUMENT, GeiError.TASK_ID_EMPTY.getError());
        return scpGeiTaskMapper.updateByTaskId(scpGeiTaskDO);
    }
    public Long insert(ScpGeiTaskDO scpGeiTaskDO) {
        Assert.notNull(scpGeiTaskDO.getTaskId(), ErrorCode.ILLEGAL_ARGUMENT, GeiError.TASK_ID_EMPTY.getError());
        Assert.notNull(scpGeiTaskDO.getTaskType(), ErrorCode.ILLEGAL_ARGUMENT, GeiError.TASK_TYPE_EMPTY.getError());
        GeiTypeEnum taskType = GeiTypeEnum.getByCode(scpGeiTaskDO.getTaskType());
        Assert.isTrue(Objects.nonNull(taskType), ErrorCode.ILLEGAL_ARGUMENT, GeiError.TASK_TYPE_ILLEGAL.getError());
        Assert.notNull(scpGeiTaskDO.getExecuteTemplateCode(), ErrorCode.ILLEGAL_ARGUMENT, GeiError.EXECUTE_TEMPLATE_CODE_EMPTY.getError());
        scpGeiTaskDO.setStatus(GeiTaskStatusEnum.CREATED.getCode());
        return scpGeiTaskMapper.insert(scpGeiTaskDO);
    }

    public Long delete(List<Long> parendTaskIds){
        return scpGeiTaskMapper.delete(parendTaskIds);
    }
    public Long closeProcessExpireByTaskId(List<Long> parendTaskIds) {
        return scpGeiTaskMapper.closeProcessExpireByTaskId(parendTaskIds);
    }
}
