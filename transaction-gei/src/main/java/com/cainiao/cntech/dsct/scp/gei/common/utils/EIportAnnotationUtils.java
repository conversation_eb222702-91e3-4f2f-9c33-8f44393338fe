package com.cainiao.cntech.dsct.scp.gei.common.utils;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.quick.QuickBasicOperation;
import com.cainiao.cntech.dsct.scp.gei.support.quick.annotation.QuickBasicService;
import com.cainiao.cntech.dsct.scp.gei.support.template.ExportDataTemplate;
import com.cainiao.cntech.dsct.scp.gei.support.template.ImportDataTemplate;
import com.google.common.collect.Maps;

import java.util.Map;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-02-24 17:44
 * @description
 */
public class EIportAnnotationUtils {
    private static final Class<QuickBasicService> QUICK_BASIC_SERVICE_CLASS = QuickBasicService.class;
    private static final Class<ExportService> EXPORT_SERVICE_CLASS = ExportService.class;
    private static final Class<ImportService> IMPORT_SERVICE_CLASS = ImportService.class;

    private static final Map<String, QuickBasicService> QUICK_BASIC_SERVICE_CACHE = Maps.newHashMap();
    private static final Map<String, ExportService> EXPORT_SERVICE_CACHE = Maps.newHashMap();
    private static final Map<String, ImportService> IMPORT_SERVICE_CACHE = Maps.newHashMap();

    public static QuickBasicService getQuickBasicService(QuickBasicOperation<?, ?> quickBasicOperation) {
        Class<? extends QuickBasicOperation> clazz = quickBasicOperation.getClass();
        String name = getCacheKey(clazz);
        QuickBasicService service = QUICK_BASIC_SERVICE_CACHE.get(name);
        if (Objects.isNull(service) && clazz.isAnnotationPresent(QUICK_BASIC_SERVICE_CLASS)) {
            service = clazz.getAnnotation(QUICK_BASIC_SERVICE_CLASS);
            QUICK_BASIC_SERVICE_CACHE.put(name, service);
        }
        return service;
    }

    public static ExportService getExportService(ExportDataTemplate<?> template) {
        Class<? extends ExportDataTemplate> clazz = template.getClass();
        String name = getCacheKey(clazz);
        ExportService service = EXPORT_SERVICE_CACHE.get(name);
        if (Objects.isNull(service) && clazz.isAnnotationPresent(EXPORT_SERVICE_CLASS)) {
            service = clazz.getAnnotation(EXPORT_SERVICE_CLASS);
            EXPORT_SERVICE_CACHE.put(name, service);
        }
        return service;
    }

    public static ImportService getImportService(ImportDataTemplate<?> template) {
        Class<? extends ImportDataTemplate> clazz = template.getClass();
        String name = getCacheKey(clazz);
        ImportService service = IMPORT_SERVICE_CACHE.get(name);
        if (Objects.isNull(service) && clazz.isAnnotationPresent(IMPORT_SERVICE_CLASS)) {
            service = clazz.getAnnotation(IMPORT_SERVICE_CLASS);
            IMPORT_SERVICE_CACHE.put(name, service);
        }
        return service;
    }

    private static String getCacheKey(Class<?> clazz) {
        return clazz.getName();
    }
}
