package com.cainiao.cntech.dsct.scp.gei.ext.processor;


import com.cainiao.cntech.dsct.scp.gei.core.model.BeforeWriteCellWrapper;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-02-08 15:39
 * @description 导出后处理器
 */
public interface ExportPostProcessor {

    /**
     * 在写单元格值之前回调，可以返回需要的单元格内容以替换原始单元格的值
     *
     * @param beforeWriteCellWrapper 包含了单元格值的一些信息
     * @return
     */
    default Object postProcessBeforeWriteCellValue(BeforeWriteCellWrapper beforeWriteCellWrapper) {
        return null;
    }
}
