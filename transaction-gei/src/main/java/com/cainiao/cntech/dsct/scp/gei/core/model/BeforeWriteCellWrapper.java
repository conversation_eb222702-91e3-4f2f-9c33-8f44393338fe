package com.cainiao.cntech.dsct.scp.gei.core.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-02-08 16:31
 * @description
 */
@Data
@Accessors(chain = true)
public class BeforeWriteCellWrapper {
    /**
     * 所有数据行
     */
    private List<?> dataList;
    /**
     * 字段列标题列表
     */
    private List<String> columnList;
    /**
     * 当前行数据
     */
    private Object rowData;
    /**
     * 当前数据行类型
     */
    private Class<?> modelClass;
    /**
     * 当前字段名
     */
    private String fieldName;
    /**
     * 当前字段值
     */
    private Object fieldValue;
    /**
     * 当前行索引
     */
    private int rowIndex;
    /**
     * 当前字段列索引
     */
    private int columnIndex;

    public static BeforeWriteCellWrapper of(List<?> dataList, List<String> columnList) {
        BeforeWriteCellWrapper beforeWriteCellWrapper = new BeforeWriteCellWrapper();
        return beforeWriteCellWrapper.setDataList(dataList).setColumnList(columnList);
    }
}
