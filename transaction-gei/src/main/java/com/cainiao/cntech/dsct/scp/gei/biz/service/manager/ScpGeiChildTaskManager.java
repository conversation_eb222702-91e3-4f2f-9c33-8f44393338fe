package com.cainiao.cntech.dsct.scp.gei.biz.service.manager;

import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiChildTaskDO;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper.ScpGeiChildTaskMapper;
import com.cainiao.cntech.dsct.scp.gei.biz.service.dto.ScpGeiChildTaskDTO;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.query.GeiChildTaskPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTaskStatusEnum;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import com.cainiao.cntech.dsct.scp.gei.common.error.GeiError;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.PageUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.github.pagehelper.PageHelper;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:45
 * @description
 */
public class ScpGeiChildTaskManager {
    @Resource
    private ScpGeiChildTaskMapper scpGeiChildTaskMapper;

    public List<ScpGeiChildTaskDTO> queryPageData(GeiChildTaskPageQueryRequest request) {
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        return GeiCommonConvert.convert(scpGeiChildTaskMapper.selectByCondition(JsonUtils.toMap(request)), ScpGeiChildTaskDTO.class);
    }
    public Long queryCount(GeiChildTaskPageQueryRequest request) {
        return scpGeiChildTaskMapper.selectCount(JsonUtils.toMap(request));
    }
    public Long updateByTaskId(ScpGeiChildTaskDO scpGeiChildTaskDO) {
        Assert.notNull(scpGeiChildTaskDO.getTaskId(), ErrorCode.ILLEGAL_ARGUMENT, GeiError.TASK_ID_EMPTY.getError());
        return scpGeiChildTaskMapper.updateByTaskId(scpGeiChildTaskDO);
    }
    public Long insert(List<ScpGeiChildTaskDO> list) {
        for (ScpGeiChildTaskDO scpGeiChildTaskDO : list) {
            Assert.notNull(scpGeiChildTaskDO.getParentTaskId(), ErrorCode.ILLEGAL_ARGUMENT, GeiError.PARENT_TASK_ID_EMPTY.getError());
            Assert.notNull(scpGeiChildTaskDO.getTaskId(), ErrorCode.ILLEGAL_ARGUMENT, GeiError.TASK_ID_EMPTY.getError());
            Assert.notNull(scpGeiChildTaskDO.getTaskType(), ErrorCode.ILLEGAL_ARGUMENT, GeiError.TASK_TYPE_EMPTY.getError());
            Assert.notNull(scpGeiChildTaskDO.getExecuteTemplateCode(), ErrorCode.ILLEGAL_ARGUMENT, GeiError.EXECUTE_TEMPLATE_CODE_EMPTY.getError());
            GeiTypeEnum taskType = GeiTypeEnum.getByCode(scpGeiChildTaskDO.getTaskType());
            Assert.isTrue(Objects.nonNull(taskType), ErrorCode.ILLEGAL_ARGUMENT, GeiError.TASK_TYPE_ILLEGAL.getError());
            scpGeiChildTaskDO.setStatus(GeiTaskStatusEnum.CREATED.getCode());
        }
        return scpGeiChildTaskMapper.batchInsert(list);
    }

    public Long delete(List<Long> parentTaskIds){
        return scpGeiChildTaskMapper.delete(parentTaskIds);
    }

    public Long closeProcessExpireByParentTaskId(List<Long> parendTaskIds) {
        return scpGeiChildTaskMapper.closeProcessExpireByParentTaskId(parendTaskIds);
    }

    public Long updateNotIsoTaskStatusFailedByTaskId(Long parendTaskId) {
        return scpGeiChildTaskMapper.updateNotIsoTaskStatusFailedByTaskId(parendTaskId);
    }
}
