package com.cainiao.cntech.dsct.scp.gei.ext.validator;

import com.cainiao.cntech.dsct.scp.gei.ext.validator.common.*;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-13 16:17
 * @description 校验工厂，单例.
 * 因为类不是很大，所以单利对象直接new出来在全局中
 * 用于向抽象的excelHelper填充通用的校验器
 */
public class ValidatorFactory<T extends ImportTemplateVO> {
    public static final String NOT_EMPTY = "notEmpty";
    public static final String NOT_BLANK = "notBlank";
    public static final String VALID_EMAIL = "validEmail";
    public static final String GE_ZERO = "geZero";
    public static final String GT_ZERO = "gtZero";
    public static final String CHAR_SIZE_LIMIT = "charSizeLimit";
    public static final String PERCENT = "percent";
    public static final String DECIMAL_SIZE_LIMIT = "decimalSizeLimit";
    public static final String BOOL_INT = "boolInt";
    private static final ValidatorFactory<? extends ImportTemplateVO> factory = new ValidatorFactory<>();
    private final Map<String, ImportValidator<T>> CONTAINER = Maps.newHashMapWithExpectedSize(7);

    private ValidatorFactory() {
        register(NOT_EMPTY, new NotEmptyImportValidator<>());
        register(NOT_BLANK, new NotBlankImportValidator<>());
        register(VALID_EMAIL, new EmailImportValidator<>());
        register(GE_ZERO, new GeZeroImportValidator<>());
        register(GT_ZERO, new GtZeroImportValidator<>());
        register(CHAR_SIZE_LIMIT, new CharSizeImportValidator<>());
        register(PERCENT, new PercentImportValidator<>());
        register(DECIMAL_SIZE_LIMIT, new DecimalSizeLimitValidator<>());
        register(BOOL_INT, new BooleanIntImportValidator<>());
    }

    public static ValidatorFactory<? extends ImportTemplateVO> getInstance() {
        return factory;
    }

    private void register(String key, ImportValidator<T> validator) {
        CONTAINER.put(key, validator);
    }

    public Collection<ImportValidator<T>> getValidators() {
        return CONTAINER.values();
    }

    public List<ImportValidator<T>> getValidators(String... keys) {
        List<ImportValidator<T>> result = new LinkedList<>();
        if (keys != null) {
            for (String key : keys) {
                ImportValidator<T> validator = CONTAINER.get(key);
                if (validator != null) {
                    result.add(validator);
                }
            }
        }
        return result;
    }
}
