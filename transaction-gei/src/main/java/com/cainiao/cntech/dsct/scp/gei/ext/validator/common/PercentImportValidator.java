package com.cainiao.cntech.dsct.scp.gei.ext.validator.common;


import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.ext.validator.ImportValidator;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelValid;

import java.lang.reflect.Field;
import java.math.BigDecimal;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-22 16:07
 * @description
 */
public class PercentImportValidator<T> implements ImportValidator<T> {
    @Override
    public boolean require(T t, Field field, String fieldName) {
        return field.isAnnotationPresent(ExcelValid.class) && field.getAnnotation(ExcelValid.class).percent();
    }

    @Override
    public String validate(ImportDataWrapper<T> importDataWrapper, T t, Field field, Object fieldValue) {
        if (fieldValue instanceof Number) {
            double value = ((Number) fieldValue).doubleValue();
            if (value < 0 || value > 100) {
                return "%s数据无效，请输入正确的百分比数值！";
            }
            BigDecimal bd = new BigDecimal(String.valueOf(value));
            String valueStr = bd.toString();
            int length = valueStr.length() - valueStr.indexOf('.') - 1;
            if (length > 2) {
                return "%s数值小数点后最多只能保留两位！";
            }
        }
        return null;
    }
}
