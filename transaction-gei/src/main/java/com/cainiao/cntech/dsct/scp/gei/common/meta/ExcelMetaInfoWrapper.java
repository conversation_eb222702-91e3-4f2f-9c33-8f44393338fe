package com.cainiao.cntech.dsct.scp.gei.common.meta;

import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 14:30
 * @description 表格数据包装
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ExcelMetaInfoWrapper {

    /**
     * 列表数据(表头)
     */
    private List<ExcelMetaInfo> meta;

    /**
     * 数据列表（展示）
     */
    private List<?> list;

    /**
     * 主键（区分唯一数据）
     */
    private String key;

    public ExcelMetaInfoWrapper(List<?> list, List<ExcelMetaInfo> meta) {
        this(list, meta, null);
    }

    public ExcelMetaInfoWrapper(List<?> list, List<ExcelMetaInfo> meta, String key) {
        this.list = list;
        this.meta = meta;
        this.key = key;
    }

    public static ExcelMetaInfoWrapper of() {
        return new ExcelMetaInfoWrapper();
    }

    public static <T> ExcelMetaInfoWrapper of(List<T> list, List<ExcelMetaInfo> meta) {
        return new ExcelMetaInfoWrapper(list, meta);
    }

    public static <T> ExcelMetaInfoWrapper of(List<T> list) {
        Class<?> metaInfoClazz = null;
        if (Objects.nonNull(list) && !list.isEmpty()) {
            metaInfoClazz = list.get(0).getClass();
        }
        return of(metaInfoClazz, list);
    }

    public static ExcelMetaInfoWrapper of(Class<?> metaInfoClazz) {
        return of(metaInfoClazz, Collections.emptyList());
    }

    public static ExcelMetaInfoWrapper of(Class<?> metaInfoClazz, boolean deep) {
        return of(metaInfoClazz, deep, Collections.emptyList());
    }

    public static <T> ExcelMetaInfoWrapper of(Class<?> metaInfoClazz, List<T> list) {
        return of(metaInfoClazz, false, list);
    }

    public static <T> ExcelMetaInfoWrapper of(Class<?> metaInfoClazz, boolean deep, List<T> list) {
        return new ExcelMetaInfoWrapper(list, ExcelMetaInfoCreator.create(metaInfoClazz, deep));
    }

    public <T> void convert(Class<T> clazz) {
        setMeta(ExcelMetaInfoCreator.create(clazz));
        setList(GeiCommonConvert.convert(list, clazz));
    }

    public <T, M> void convert(Class<T> clazz, Class<M> metaClazz) {
        setMeta(ExcelMetaInfoCreator.create(metaClazz));
        setList(GeiCommonConvert.convert(list, clazz));
    }
}
