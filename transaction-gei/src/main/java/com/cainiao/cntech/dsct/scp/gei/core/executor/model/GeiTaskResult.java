package com.cainiao.cntech.dsct.scp.gei.core.executor.model;

import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTaskStatusEnum;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelMetaInfoWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 20:13
 * @description
 */
@Data
@AllArgsConstructor
public class GeiTaskResult {
    private GeiTaskStatusEnum status;
    /**
     * 导入返回
     */
    private String errorMsg;
    /**
     * 导出返回
     */
    private String filename;
    private String fileAddress;

    private GeiTaskResult(GeiTaskStatusEnum status, String filename, String fileAddress) {
        this.filename = filename;
        this.fileAddress = fileAddress;
        this.status = status;
    }

    private GeiTaskResult(GeiTaskStatusEnum status, String errorMsg) {
        this.status = status;
        this.errorMsg = errorMsg;
    }


    public static GeiTaskResult of(String filename, String fileAddress) {
        return new GeiTaskResult(GeiTaskStatusEnum.SUCCESS, filename, fileAddress);
    }

    public static GeiTaskResult of(ExcelMetaInfoWrapper importResult) {
        return new GeiTaskResult(importResult.isSuccess() ? GeiTaskStatusEnum.SUCCESS : GeiTaskStatusEnum.FAILED, importResult.isSuccess() ? null : JsonUtils.toStr(importResult));
    }
    public static GeiTaskResult of(Boolean success, List<ExcelMetaInfoWrapper> importResult) {
        return new GeiTaskResult(Boolean.TRUE.equals(success) ? GeiTaskStatusEnum.SUCCESS : GeiTaskStatusEnum.FAILED
                , Boolean.TRUE.equals(success) ? null : JsonUtils.toStr(importResult));
    }
}
