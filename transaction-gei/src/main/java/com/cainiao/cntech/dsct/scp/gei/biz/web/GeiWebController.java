package com.cainiao.cntech.dsct.scp.gei.biz.web;

import com.cainiao.cntech.dsct.scp.gei.biz.dao.dto.ScpGeiTaskDTO;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.query.GeiTaskPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.service.ScpGeiTaskService;
import com.cainiao.cntech.dsct.scp.gei.biz.web.api.GeiWebApi;
import com.cainiao.cntech.dsct.scp.gei.biz.web.model.OptionVO;
import com.cainiao.cntech.dsct.scp.gei.biz.web.vo.ScpGeiTaskVO;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelPageMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.common.model.ResponseResult;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.GeiExcelManager;
import com.cainiao.cntech.dsct.scp.gei.core.dict.GeiDictResolver;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:19
 * @description 通用导入导出web层
 *  锅圈项目后期时优化该组件，所以锅圈项目中，采用之前的导入导出的请求接口，以下导入数据、导出模版、导出数据接口后续项目使用
 */
@Controller
@RequestMapping(GeiWebApi.ROOT)
@Api(value = "通用导入导出功能", tags = {"通用导入导出功能"})
public class GeiWebController {
    @Resource
    private GeiExcelManager geiManager;
    @Resource
    private GeiDictResolver geiDictResolver;
    @Resource
    private ScpGeiTaskService scpGeiTaskService;

    @ApiOperation(value = "导入数据")
    @PostMapping(path = GeiWebApi.UPLOAD, consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            headers = {"content-type=" + MediaType.MULTIPART_FORM_DATA_VALUE})
    @ResponseBody
    public ResponseResult<Object> upload(@RequestPart MultipartFile file, @PathVariable @ApiParam("文件处理服务编码") String dataCode, @RequestParam @ApiParam("附加数据") Map<String,Object> params) throws Exception {
        return ResponseResult.success(geiManager.importData(dataCode, file, params));
    }

    @ApiOperation(value = "导出模版")
    @PostMapping(GeiWebApi.EXPORT_TEMPLATE)
    public void downloadTemplate(@PathVariable @ApiParam("文件处理服务编码") String dataCode, HttpServletResponse response, @RequestParam Map<String, Object> queryMap) {
        geiManager.exportTemplate(dataCode, response, queryMap);
    }

    @ApiOperation(value = "导出数据")
    @PostMapping (value = GeiWebApi.EXPORT_DATA)
    public void download(@PathVariable @ApiParam("文件处理服务编码") String dataCode, @RequestParam Map<String, Object> queryMap,
                         HttpServletResponse response) {
        geiManager.exportData(dataCode, response, queryMap);
    }

    @ApiOperation(value = "检查是否支持异步")
    @GetMapping (value = GeiWebApi.CHECK_ASYNC)
    @ResponseBody
    public ResponseResult<Boolean> checkAsync(@PathVariable @ApiParam("文件处理服务编码") String dataCode) {
        return ResponseResult.success(geiManager.isAsync(dataCode));
    }


    /**
     * 根据code查询字典值
     * @param dictCode
     * @return
     */
    @GetMapping(GeiWebApi.QUERY_DICT)
    @ResponseBody
    public ResponseResult<List<OptionVO>> queryDictValueByCode(@RequestParam String dictCode) {
        return ResponseResult.success(geiDictResolver.resolveImportDict(dictCode));
    }

    /**
     * 任务列表查询
     * @param request 请求体
     * @return 任务列表
     */
    @PostMapping(GeiWebApi.View.LIST)
    @ResponseBody
    public ResponseResult<ExcelPageMetaInfoWrapper> list(@RequestBody GeiTaskPageQueryRequest request) {
        List<ScpGeiTaskDTO> queryList = scpGeiTaskService.queryPageData(request);
        return ResponseResult.success(
            ExcelPageMetaInfoWrapper.of( request, scpGeiTaskService.queryCount(request)
                    , GeiCommonConvert.convert(queryList, ScpGeiTaskVO.class)
                    , ExcelMetaInfoCreator.create(ScpGeiTaskVO.class, true))
        );
    }


}
