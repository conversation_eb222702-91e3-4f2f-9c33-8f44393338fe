package com.cainiao.cntech.dsct.scp.gei.support.annotation;

import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-13 09:44
 * @description 导出服务注解
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface ExportService {
    /**
     * 导出模版编码
     *
     * @return
     */
    String value();

    /**
     * 导出字段配置的dictCode
     *
     * @return
     */
    String dict() default "";

    /**
     * 导出文件名
     *
     * @return
     */
    String filename();

    /**
     * 是否为异步导出
     */
    boolean async() default true;

    /**
     * 拆分阈值
     */
    int sliceSize() default 2000;

    /**
     * 任务过期清理时间， 单位（秒）
     */
    long expire() default 60 * 60 * 48;

    /**
     * 任务执行过期时间， 单位（秒）
     */
    long processExpire() default 60 * 60 * 5;
}
