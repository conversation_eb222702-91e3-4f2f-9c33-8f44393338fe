package com.cainiao.cntech.dsct.scp.gei.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:24
 * @description gei任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum GeiTaskStatusEnum {
    CREATED("CREATED", "新建"),
    AWAIT("AWAIT", "待运行"),
    RUNNING("RUNNING", "运行中"),
    FAILED("FAILED", "异常"),
    SUCCESS("SUCCESS", "成功"),
    ;

    private final String code;
    private final String name;

    public static GeiTaskStatusEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (GeiTaskStatusEnum value : GeiTaskStatusEnum.values()) {
            if (StringUtils.equalsIgnoreCase(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
