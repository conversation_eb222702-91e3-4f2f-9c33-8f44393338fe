package com.cainiao.cntech.dsct.scp.gei.common.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-13 17:22
 * @description
 */
public class RegexUtils {

    public static boolean matches(String regex, String input) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        return matcher.matches();
    }
    public static String findFirst(String regex, String input) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
    public static List<String> find(String regex, String input) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        List<String> result = new ArrayList<>();
        while (matcher.find()) {
            result.add(matcher.group(1));
        }
        return result;
    }

    public static String findAndReplace(String regex, String input, Function<String, String> func) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String value = func.apply(matcher.group(1));
            input = matcher.replaceFirst(value);
            matcher = pattern.matcher(input);
        }
        return input;
    }
}
