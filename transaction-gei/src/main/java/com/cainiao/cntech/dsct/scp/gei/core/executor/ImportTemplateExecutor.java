package com.cainiao.cntech.dsct.scp.gei.core.executor;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.Session;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import com.alibaba.excel.EasyExcel;
import com.cainiao.cntech.dsct.scp.gei.common.exception.AppException;
import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.insert.GeiChildTaskInsertRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.insert.GeiTaskInsertRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.service.ScpGeiTaskService;
import com.cainiao.cntech.dsct.scp.gei.common.enums.BooleanEnum;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.*;
import com.cainiao.cntech.dsct.scp.gei.core.dict.GeiDictResolver;
import com.cainiao.cntech.dsct.scp.gei.core.event.GeiAsyncEvent;
import com.cainiao.cntech.dsct.scp.gei.core.event.GeiEventPublisher;
import com.cainiao.cntech.dsct.scp.gei.core.executor.pool.GeiChildTaskThreadPool;
import com.cainiao.cntech.dsct.scp.gei.core.model.*;
import com.cainiao.cntech.dsct.scp.gei.ext.converter.ImportExtConversionService;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.ext.validator.ImportValidator;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportErrorVO;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportMapProxy;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import com.cainiao.cntech.dsct.scp.gei.support.template.ImportDataTemplate;
import com.cainiao.cntech.dsct.scp.gei.support.template.ImportDataValidatorRegister;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 15:50
 * @description 导出模版执行器
 */
public class ImportTemplateExecutor {

    private static final String ID = "id";

    private static final String NAME = "name";

    /**
     * 导入模版实体的字段缓存
     */
    private static final Map<Class<?>, Map<String, Field>> FIELD_CACHE = new ConcurrentHashMap<>();
    /**
     * 类型转换器
     */
    private static final DefaultConversionService CONVERTER = ImportExtConversionService.getInstance();
    @Resource
    private GeiDictResolver geiDictResolver;
    @Resource
    private ScpGeiTaskService scpGeiTaskService;
    @Resource
    private GeiEventPublisher geiEventPublisher;
    @Resource
    private GeiChildTaskThreadPool geiChildTaskThreadPool;

    /**
     * 格式化异常对象转换为描述
     *
     * @param e 异常对象
     * @return 异常描述信息
     */
    private static String formatError(Throwable e) {
        return String.format("%s: %s", e.getClass().getName(), e.getMessage());
    }

    /**
     * 平衡分配
     *
     * @param index          当前循环的索引
     * @param start          起始位
     * @param end            结束位
     * @param sliceSize      分片长度
     * @param currentRequest 当前请求体
     * @param requests       已保存的请求体列表
     */
    public static void balanceAllocate(int index, long start, long end, int sliceSize, GeiChildTaskInsertRequest currentRequest, List<GeiChildTaskInsertRequest> requests) {
        if (index > 0 && (end - start) < sliceSize / 2) {
            int cycleCount = 1;
            long total = (end - start), tempStart;
            while (index - cycleCount >= 0) {
                GeiChildTaskInsertRequest pre = requests.get(index - cycleCount);
                total += (pre.getPageSize() - pre.getPage());
                long avg = total / (cycleCount + 1);
                tempStart = pre.getPage();
                if (avg < sliceSize / 2) {
                    cycleCount++;
                    continue;
                }
                for (int j = cycleCount; j > 0; j--) {
                    pre = requests.get(index - j);
                    pre.setPage(tempStart);
                    pre.setPageSize(pre.getPage() + Math.min(avg, total));
                    tempStart = pre.getPageSize();
                    total -= (pre.getPageSize() - pre.getPage());
                }
                currentRequest.setPageSize(tempStart + total);
                currentRequest.setPage(tempStart);
                break;
            }
        }
    }

    public static <T extends ImportTemplateVO> List<T> sliceImportList(List<T> list, int start, int end) {
        List<T> result = new LinkedList<>();
        for (int i = start; i < end; i++) {
            result.add(list.get(i));
        }
        return result;
    }

    public ExcelWriteWrapper exportTemplate(ImportDataTemplate<? extends ImportTemplateVO> importDataTemplate, ImportService importService, Map<String, Object> queryMap) {
        ImportQueryRequest request = JsonUtils.toObject(queryMap, ImportQueryRequest.class);
        request.setParams(queryMap).resolveToList();
        List<? extends ImportTemplateVO> importTemplateData = importDataTemplate.getImportTemplateData(request);
        if (importTemplateData == null) {
            importTemplateData = new ArrayList<>();
        }
        ExcelWriteWrapper excelWriteWrapper = ExcelWriteWrapper.of(
                getTemplateFileName(request, importService)
                , importDataTemplate.getTemplateDesc(request, importService)
                , importTemplateData
                , importDataTemplate.getImportTemplateMetaInfo(request)
        );
        excelWriteWrapper.formatFileName(request.getDimension(), request.getFields());
        return excelWriteWrapper;
    }

    public ExcelMetaInfoWrapper sync(ImportDataTemplate<? extends ImportTemplateVO> importDataTemplate
            , ImportService importService, MultipartFile multipartFile, Map<String, Object> queryMap) {
        return execute(importDataTemplate, importService, multipartFile, queryMap, false);
    }

    public ExcelMetaInfoWrapper async(ImportDataTemplate<? extends ImportTemplateVO> importDataTemplate
            , ImportService importService, MultipartFile multipartFile, Map<String, Object> queryMap) {
        return execute(importDataTemplate, importService, multipartFile, queryMap, true);
    }

    private <T extends ImportTemplateVO> ExcelMetaInfoWrapper execute(ImportDataTemplate<T> importDataTemplate, ImportService importService, MultipartFile multipartFile, Map<String, Object> params, boolean async) {
        try {
            if (Objects.isNull(importDataTemplate)) {
                throw new AppException(ErrorCode.SYSTEM_ERROR, "未能找到处理导入服务!");
            }
            invokeBeforeImportProcessing(importDataTemplate);
            ImportDataWrapper<T> wrapper;
            String filename = multipartFile.getResource().getFilename();
            if (async) {
                wrapper = previewData(importDataTemplate, importService, multipartFile, params);
                if (wrapper == null) {
                    throw new AppException(ErrorCode.SYSTEM_ERROR, "请不要修改文件名的维度标记或模版字段!");
                } else if (wrapper.getData().isEmpty()) {
                    throw new AppException(ErrorCode.SYSTEM_ERROR, "请不要上传空文档!");
                }
                GeiTaskInsertRequest parentTask = createTaskInsertRequest(importService, wrapper, params, filename);
                List<GeiChildTaskInsertRequest> childTasks = createChildTaskInsertRequests(parentTask, importService);
                Long taskId = scpGeiTaskService.createTask(parentTask, childTasks);
                geiEventPublisher.sendAsyncImportEvent(GeiAsyncEvent.of(taskId, wrapper));
                // 设置data = true， 给前端标记是异步导入， 不需要等待结果
                return ExcelMetaInfoWrapper.of(Collections.emptyList(), Collections.emptyList()).success().setData(true);
            }
            // 同步
            wrapper = previewData(importDataTemplate, importService, multipartFile, params);
            ExcelMetaInfoWrapper metaInfoWrapper = invokeTemplate(importDataTemplate, importService, wrapper);
            invokeAfterImportProcessed(importDataTemplate, wrapper);
            return metaInfoWrapper;
        } catch (Exception e) {
            return ExcelMetaInfoWrapper.of(Collections.singletonList(ImportErrorVO.of("「解析文件失败」 " + formatError(e)))).failed();
        }
    }

    /**
     * 同步导入时调用
     *
     * @param importDataTemplate 导入数据模版实现
     * @return ExcelMetaInfoWrapper
     */
    public <T extends ImportTemplateVO> ExcelMetaInfoWrapper invokeTemplate(ImportDataTemplate<T> importDataTemplate, ImportService importService
            , ImportDataWrapper<T> importDataWrapper) {
        if (importDataWrapper == null) {
            return ExcelMetaInfoWrapper.of(Collections.singletonList(ImportErrorVO.of("「解析文件失败」请不要修改文件名的维度标记或模版字段!"))).failed();
        } else if (importDataWrapper.getData().isEmpty()) {
            return ExcelMetaInfoWrapper.of(Collections.singletonList(ImportErrorVO.of("「解析文件失败」请不要上传空文档!"))).failed();
        }
        List<T> importList = importDataWrapper.getData();
        importDataWrapper.setData(null);
        // step1: 这里两种场景
        //      case1： 启用任务隔离，invokeTemplateAndCreateTask方法将各自校验 + 上传
        //      case2： 关闭任务隔离，invokeTemplateAndCreateTask方法，只返回校验的结果
        List<CompletableFuture<ExcelMetaInfoWrapper>> tasks = invokeTemplateAndCreateTask(importDataTemplate, importDataWrapper,
                importList, importService, true);
        // step2: 收集任务的结果
        List<ExcelMetaInfoWrapper> taskResultList = new LinkedList<>();
        boolean isSuccess = true;
        for (CompletableFuture<ExcelMetaInfoWrapper> task : tasks) {
            ExcelMetaInfoWrapper wrapper = task.join();
            taskResultList.add(wrapper);
            // 关闭任务隔离的场景下，如果校验成功则返回null
            if (Objects.nonNull(wrapper)) {
                isSuccess = false;
            }
        }
        // step3: 判断如果关闭任务隔离 并且 校验成功，并发上传数据得到结果
        if (!importService.sliceIso() && isSuccess) {
            taskResultList.clear();
            tasks = invokeTemplateAndCreateTask(importDataTemplate, importDataWrapper,
                    importList, importService, true);
            for (CompletableFuture<ExcelMetaInfoWrapper> task : tasks) {
                ExcelMetaInfoWrapper wrapper = task.join();
                taskResultList.add(wrapper);
            }
        }
        // step4: 这里正常处理任务结果即可
        //      case1:  启用任务隔离，那么taskResultList就是各自任务并发上传处理的结果
        //      case2:  关闭任务隔离 且 校验失败，那么taskResultList就是校验错误信息
        //      case3:  关闭任务隔离 且 校验成功，那么taskResultList就是各自任务并发上传处理的结果
        AtomicBoolean importState = new AtomicBoolean(true);
        ExcelMetaInfoWrapper finalResult = ExcelMetaInfoWrapper.of(new ArrayList<>(), null);
        for (ExcelMetaInfoWrapper wrapper : taskResultList) {
            if (Objects.isNull(wrapper)) {
                continue;
            }
            if (!wrapper.isSuccess()) {
                importState.set(false);
            }
            List list = wrapper.getList();
            if (Objects.nonNull(list)) {
                finalResult.getList().addAll(list);
            }
            if (Objects.nonNull(wrapper.getData())) {
                finalResult.setData(wrapper.getData());
            }
            if (Objects.isNull(finalResult.getMeta())) {
                finalResult.setMeta(wrapper.getMeta());
            }
            if (Objects.nonNull(wrapper.getKey())) {
                finalResult.setKey(wrapper.getKey());
            }
        }
        return finalResult.setSuccess(importState.get());
    }

    public <T extends ImportTemplateVO> List<CompletableFuture<ExcelMetaInfoWrapper>> invokeTemplateAndCreateTask(ImportDataTemplate<T> importDataTemplate, ImportDataWrapper<T> importDataWrapper,
                                                                                                                  List<T> importList, ImportService importService, boolean isNeedValidate) {
        Class<T> modelClass = GenericsSearchUtils.searchClass(importDataTemplate, ImportDataTemplate.class, 0);
        List<CompletableFuture<ExcelMetaInfoWrapper>> tasks = new ArrayList<>();
        for (int i = 0; i < importList.size(); i += importService.sliceSize()) {
            int finalI = i;
            tasks.add(
                    CompletableFuture.supplyAsync(() -> {
                        List<T> partition = sliceImportList(importList, finalI, Math.min(finalI + importService.sliceSize(), importList.size()));
                        ImportDataWrapper<T> copyWrapper = JsonUtils.copy(importDataWrapper);
                        copyWrapper.setData(partition);
                        return invokeTemplate(importDataTemplate, copyWrapper, modelClass, importService, isNeedValidate);
                    }, geiChildTaskThreadPool).exceptionally(e -> {
                        List<ImportErrorVO> error = new ArrayList<>();
                        error.add(ImportErrorVO.of(e.getMessage()));
                        return ExcelMetaInfoWrapper.of(error).failed();
                    })
            );
        }
        return tasks;
    }

    public <T extends ImportTemplateVO> ExcelMetaInfoWrapper invokeTemplate(ImportDataTemplate<T> importDataTemplate, ImportDataWrapper<T> importDataWrapper
            , Class<T> modelClass, ImportService importService, boolean isNeedValidate) {
        boolean validate = true;
        if (isNeedValidate) {
            validate = validateImportData(importDataTemplate, importDataWrapper);
            if (!importService.sliceIso() && validate) {
                // isNeedValidate = true && 关闭了任务隔离 && 校验通过 返回null表示该任务校验通过
                return null;
            }
        }
        // 校验通过
        if (validate) {
            invokeBeforeImportData(importDataTemplate, importDataWrapper);
            ImportResult importResult = importDataTemplate.importData(importDataWrapper);
            invokeAfterImportData(importDataTemplate, importDataWrapper, importResult);
            if (importResult == null) {
                return ExcelMetaInfoWrapper.of().success();
            }
            return ExcelMetaInfoWrapper.of(importResult).success();
        }
        List<ExcelMetaInfo> meta = importDataTemplate.getImportTemplateMetaInfo(ImportQueryRequest.of(importDataWrapper.getDimension(), importDataWrapper.getFields(), true));
        List<?> errorData;
        if (ImportMapProxy.class.equals(modelClass)) {
            meta.addAll(ExcelMetaInfoCreator.create(modelClass, true));
            List<ImportMapProxy> data = (List<ImportMapProxy>) importDataWrapper.getData();
            errorData = data.stream().filter(item -> StringUtils.isNotBlank(item.getErrorMsg())).map(ImportMapProxy::getRow).collect(Collectors.toList());
        } else {
            errorData = importDataWrapper.getData().stream().filter(item -> StringUtils.isNotBlank(item.getErrorMsg())).collect(Collectors.toList());
        }
        return ExcelMetaInfoWrapper.of(errorData, meta).failed();
    }

    /**
     * 解析文件流
     *
     * @param importDataTemplate 导入数据模版实现
     * @param importService      导入模版注解
     * @param multipartFile      文件流
     * @return
     */
    public <T extends ImportTemplateVO> ImportDataWrapper<T> previewData(ImportDataTemplate<T> importDataTemplate, ImportService importService, MultipartFile multipartFile, Map<String, Object> params) throws IOException {
        List<String> codes = ImportFileDimResolver.resolverByFilename(multipartFile.getResource().getFilename());
        if (CollectionUtils.isEmpty(codes) || codes.size() != 2)  {
            return null;
        }
        String dim = codes.get(0);
        String fields = codes.get(1);
        if (!ImportFileDimResolver.dimAndFieldsValid(dim, geiDictResolver.resolveImportDimSet(importService.dict()))) {
            return null;
        }
        if (!ImportFileDimResolver.dimAndFieldsValid(fields, geiDictResolver.resolveImportFieldsSet(importService.dict()))) {
            return null;
        }
        Class<T> modelClass = GenericsSearchUtils.searchClass(importDataTemplate, ImportDataTemplate.class, 0);
        boolean isMapProxy = ImportMapProxy.class.equals(modelClass);
        // 模版列
        List<ExcelMetaInfo> templateMetaInfo = importDataTemplate.getImportTemplateMetaInfo(ImportQueryRequest.of(dim, fields, false));
        // 字段名称 -> metaInfo
        Map<String, ExcelMetaInfo> templateMetaInfoMap = StreamUtils.singleGroup(templateMetaInfo, ExcelMetaInfo::getName);
        List<T> dataFiledList = Collections.synchronizedList(new LinkedList<>());
        List<Map<Integer, String>> rawUploadList = EasyExcel.read(multipartFile.getInputStream())
                .sheet()
                .headRowNumber(importDataTemplate.getHeadRowNumber())
                .doReadSync();
        // index -> 字段名称
        Map<Integer, String> headList = rawUploadList.get(0);
        // 校验维度对应的模版字段和excel中的字段是否一致
        for (String value : headList.values()) {
            if (StringUtils.isEmpty(value)) {
                continue;
            }
            if (Objects.isNull(templateMetaInfoMap.get(value))) {
                return null;
            }
        }
        rawUploadList.remove(0);
        for (Map<Integer, String> data : rawUploadList) {
            T t = ReflectionUtils.createInstance(modelClass);
            for (Map.Entry<Integer, String> dateValue : data.entrySet()) {
                // 获取字段名称，并根据字段名称获取metaInfo
                ExcelMetaInfo metaInfo = templateMetaInfoMap.get(headList.get(dateValue.getKey()));
                if (Objects.isNull(metaInfo)) {
                    continue;
                }
                String fieldName = metaInfo.getCode();
                String fieldValue = trimRawValue(metaInfo, dateValue.getValue());
                if (isMapProxy) {
                    ImportMapProxy proxy = (ImportMapProxy) t;
                    Class<?> fieldClass = metaInfo.getFieldClass();
                    fieldClass = fieldClass == null ? Object.class : fieldClass;
                    try {
                        proxy.put(fieldName, Objects.nonNull(fieldValue) ? CONVERTER.convert(fieldValue, fieldClass) : null);
                    } catch (Exception ignored) {
                        proxy.put(fieldName, null);
                    }
                    proxy.putName(fieldName, metaInfo.getName());
                } else {
                    Field field = getCacheFieldOrCreate(modelClass, fieldName);
                    if (Objects.nonNull(field)) {
                        try {
                            ReflectionUtils.setFieldValue(t, field, CONVERTER.convert(fieldValue, field.getType()));
                        } catch (Exception ignored) {
                        }
                    }
                }
            }
            dataFiledList.add(t);
        }
        checkImportData(dataFiledList, templateMetaInfo, modelClass, isMapProxy);
        return ImportDataWrapper.of(dataFiledList, codes.get(0), codes.get(1)).setParams(params);
    }

    /**
     * 解析excel后检验数据合理性
     * @param dataList 数据列表
     * @param templateMetaInfo 模版meta列表
     * @param modelClass 模版class
     * @param isMapProxy 是否为mapProxy
     */
    private <T extends ImportTemplateVO> void checkImportData(List<T> dataList, List<ExcelMetaInfo> templateMetaInfo, Class<T> modelClass, boolean isMapProxy) {
        List<ExcelMetaInfo> primaryKeyList = StreamUtils.filter(templateMetaInfo, item -> BooleanEnum.isTrue(item.getImportKey()));
        if (CollectionUtils.isEmpty(primaryKeyList)) {
            return;
        }
        StringBuilder pkBuilder = new StringBuilder();
        Map<String, List<T>> dataPkGroup = StreamUtils.group(dataList, t -> {
            pkBuilder.delete(0, pkBuilder.length());
            for (ExcelMetaInfo excelMetaInfo : primaryKeyList) {
                if (isMapProxy) {
                    ImportMapProxy proxy = (ImportMapProxy) t;
                    pkBuilder.append(proxy.get(excelMetaInfo.getCode()));
                } else {
                    Field field = getCacheFieldOrCreate(modelClass, excelMetaInfo.getCode());
                    pkBuilder.append(ReflectionUtils.getFieldValue(field, t));
                }
            }
            return pkBuilder.toString();
        });
        for (Map.Entry<String, List<T>> entry : dataPkGroup.entrySet()) {
            List<T> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value) && value.size() > 1) {
                throw new AppException(ErrorCode.SYSTEM_ERROR, "导入数据主键冲突, 不允许上传相同主键的多行数据!");
            }
        }
    }

    /**
     * 校验导入数据
     *
     * @param importDataWrapper
     * @return
     */
    public <T extends ImportTemplateVO, W extends ImportTemplateVO> boolean validateImportData(ImportDataTemplate<T> importDataTemplate, ImportDataWrapper<W> importDataWrapper) {
        if (!(importDataTemplate instanceof ImportDataValidatorRegister)) {
            return true;
        }
        Class<ImportTemplateVO> modelClass = GenericsSearchUtils.searchClass(importDataTemplate, ImportDataTemplate.class, 0);
        boolean isMapProxy = ImportMapProxy.class.equals(modelClass);
        ImportDataValidatorRegister<T> validatorRegister = (ImportDataValidatorRegister<T>) importDataTemplate;
        List<ImportValidator<T>> rowValidators = validatorRegister.getRowValidators();
        List<ImportValidator<T>> colValidators = validatorRegister.getColValidators();
        boolean result;
        List<W> data = importDataWrapper.getData();
        // 校验前扩展
        result = invokePreValid(importDataTemplate, importDataWrapper);
        if (!result) {
            return result;
        }
        StringBuilder errorBuilder = new StringBuilder();
        for (W t : data) {
            int errorIndex = 1;
            errorBuilder.delete(0, errorBuilder.length());
            // 行校验器
            for (ImportValidator<T> rowValidator : rowValidators) {
                boolean require = rowValidator.require((T) t, null, null);
                String error = rowValidator.validate((ImportDataWrapper<T>) importDataWrapper, (T) t, null, null);
                if (require && Objects.nonNull(error)) {
                    errorBuilder.append(errorIndex++).append(": ").append(error).append("   ");
                    result = false;
                }
            }
            if (!isMapProxy) {
                // 字段校验器
                for (Field field : modelClass.getDeclaredFields()) {
                    for (ImportValidator<T> fieldValidator : colValidators) {
                        boolean require = fieldValidator.require((T) t, field, field.getName());
                        String error = require ? fieldValidator.validate((ImportDataWrapper<T>) importDataWrapper, (T) t, field, ReflectionUtils.getFieldValue(field, t)) : null;
                        if (require && Objects.nonNull(error)) {
                            errorBuilder.append(errorIndex++).append(": ").append(getValidFailedMsg(error, field)).append("   ");
                            result = false;
                        }
                    }
                }
            } else {
                ImportMapProxy mapProxy = (ImportMapProxy) t;
                for (String key : mapProxy.keySet()) {
                    for (ImportValidator<T> fieldValidator : colValidators) {
                        boolean require = fieldValidator.require((T) t, null, key);
                        String error = fieldValidator.validate((ImportDataWrapper<T>) importDataWrapper, (T) t, null, mapProxy.get(key));
                        if (require && Objects.nonNull(error)) {
                            errorBuilder.append(errorIndex++).append(": ").append(
                                    String.format(error, mapProxy.getNameByCode(key))
                            ).append("   ");
                            result = false;
                        }
                    }
                }
            }
            if (errorBuilder.length() > 0) {
                t.setErrorMsg(errorBuilder.toString());
            }
        }
        return result;
    }

    /**
     * 调用后置处理器的校验前扩展，如果返回true则继续执行，否则终止执行
     *
     * @param importDataTemplate 导入模版实现
     * @param importDataWrapper  导入数据包装
     * @return
     */
    private boolean invokePreValid(ImportDataTemplate<? extends ImportTemplateVO> importDataTemplate, ImportDataWrapper<? extends ImportTemplateVO> importDataWrapper) {
        if (importDataTemplate instanceof ImportPostProcessor) {
            ImportPostProcessor processor = (ImportPostProcessor<? extends ImportTemplateVO>) importDataTemplate;
            Map<String, Object> params = importDataWrapper.getParams();
            if (Objects.nonNull(params)) {
                Object id = params.get(ID);
                Object name = params.get(NAME);
                if (Objects.isNull(ServiceContextUtils.currentSession()) && Objects.nonNull(id) && Objects.nonNull(name)) {
                    Account account = new Account();
                    account.setId(id.toString());
                    account.setName(name.toString());
                    Session session = new Session();
                    session.setAccount(account);
                    ServiceContextUtils.setSession(session);
                }
            }
            return processor.preValid(importDataWrapper);
        }
        return true;
    }

    /**
     * 调用后置处理器的导入前扩展，
     *
     * @param importDataTemplate 导入模版实现
     * @param importDataWrapper  导入数据包装
     * @return
     */
    private <T extends ImportTemplateVO> void invokeBeforeImportData(ImportDataTemplate<T> importDataTemplate, ImportDataWrapper<T> importDataWrapper) {
        if (importDataTemplate instanceof ImportPostProcessor) {
            ImportPostProcessor<T> processor = (ImportPostProcessor<T>) importDataTemplate;
            processor.postProcessorBeforeImportData(importDataWrapper);
        }
    }

    /**
     * 调用后置处理器的导入后扩展，
     *
     * @param importDataTemplate 导入模版实现
     * @param importDataWrapper  导入数据包装
     * @param importResult       导入返回结果
     * @return
     */
    private <T extends ImportTemplateVO> void invokeAfterImportData(ImportDataTemplate<T> importDataTemplate, ImportDataWrapper<T> importDataWrapper, ImportResult importResult) {
        if (importDataTemplate instanceof ImportPostProcessor) {
            ImportPostProcessor<T> processor = (ImportPostProcessor<T>) importDataTemplate;
            processor.postProcessorAfterImportData(importDataWrapper, importResult);
        }
    }

    /**
     * 调用后置处理器的导入流程开始前扩展，
     *
     * @param importDataTemplate 导入模版实现
     * @return
     */
    public <T extends ImportTemplateVO> void invokeBeforeImportProcessing(ImportDataTemplate<T> importDataTemplate) {
        if (importDataTemplate instanceof ImportPostProcessor) {
            ImportPostProcessor<T> processor = (ImportPostProcessor<T>) importDataTemplate;
            processor.postProcessorBeforeImportProcessing();
        }
    }

    /**
     * 调用后置处理器的导入流程结束后扩展，
     *
     * @param importDataTemplate 导入模版实现
     * @param importDataWrapper  导入数据包装
     * @return
     */
    public <T extends ImportTemplateVO> void invokeAfterImportProcessed(ImportDataTemplate<T> importDataTemplate, ImportDataWrapper<T> importDataWrapper) {
        if (importDataTemplate instanceof ImportPostProcessor) {
            ImportPostProcessor<T> processor = (ImportPostProcessor<T>) importDataTemplate;
            processor.postProcessorAfterImportProcessed(importDataWrapper);
        }
    }


    private <T extends ImportTemplateVO> GeiTaskInsertRequest createTaskInsertRequest(ImportService importService, ImportDataWrapper<T> wrapper, Object params, String filename) {
        LocalDateTime now = LocalDateTime.now();
        GeiTaskInsertRequest request = new GeiTaskInsertRequest();
        request.setTaskName(importService.filename());
        request.setRequestParams(JsonUtils.toStr(params));
        request.setTaskType(GeiTypeEnum.IMPORT.getCode());
        request.setExecuteTemplateCode(importService.value());
        request.setTemplateDictCode(importService.dict());
        request.setFileName(filename);
        request.setGmtClearExpired(now.plusSeconds(importService.expire()));
        request.setGmtExpired(now.plusSeconds(importService.processExpire()));
        int count = wrapper.getData().size();
        request.setDataTotalCnt((long) count);
        if (count < 1) {
            request.setChildTaskTotalCnt(1);
        } else {
            int taskCount = (int) Math.ceil((double) count / importService.sliceSize());
            request.setChildTaskTotalCnt(taskCount);
        }
        return request;
    }

    private List<GeiChildTaskInsertRequest> createChildTaskInsertRequests(GeiTaskInsertRequest parentTask, ImportService importService) {
        List<GeiChildTaskInsertRequest> requests = new ArrayList<>(parentTask.getChildTaskTotalCnt());
        long count = Objects.nonNull(parentTask.getDataTotalCnt()) ? parentTask.getDataTotalCnt() : 0L;
        Integer childTaskTotalCnt = parentTask.getChildTaskTotalCnt();
        long start = 0;
        for (int i = 0; i < childTaskTotalCnt; i++) {
            GeiChildTaskInsertRequest request = new GeiChildTaskInsertRequest();
            request.setExecuteTemplateCode(importService.value());
            request.setTemplateDictCode(importService.dict());
            if (childTaskTotalCnt == 1) {
                request.setPaging(Boolean.FALSE);
            } else {
                request.setPaging(Boolean.TRUE);
                request.setPage(start);
                long end = Math.min(importService.sliceSize(), count);
                count -= end;
                end += start;
                request.setPageSize(end);
                // 平衡分配
                balanceAllocate(i, start, end, importService.sliceSize(), request, requests);
                start = request.getPageSize();
            }
            requests.add(request);
        }
        return requests;
    }

    /**
     * 导入数据祛除空格
     *
     * @param rawValue
     * @return
     */
    private String trimRawValue(ExcelMetaInfo metaInfo, String rawValue) {
        if ((rawValue = StringUtils.trim(rawValue)) != null) {
            if (Objects.equals(LocalDateTime.class, metaInfo.getFieldClass())) {
                return rawValue;
            }
            rawValue = rawValue.replace(" ", "");
            return rawValue.replace(" ", "");
        }
        return null;
    }

    /**
     * 格式化导入错误信息
     *
     * @param error 错误信息模版String
     * @param field field字段
     * @return 格式化后的错误信息
     */
    private String getValidFailedMsg(String error, Field field) {
        ExcelField excelField = field.getAnnotation(ExcelField.class);
        String tag;
        if (excelField != null) {
            tag = excelField.value();
        } else {
            tag = field.getName();
        }
        return String.format(error, tag);
    }

    /**
     * 获取excel模版的文件名。
     * 子类可以重写该方法以自定义取值逻辑。
     *
     * @param request
     * @return
     */
    private String getTemplateFileName(ImportQueryRequest request, ImportService importService) {
        if (importService != null) {
            String fileName = geiDictResolver.generateImportTemplateFileName(request, importService.dict(), "导入模版");
            if (StringUtils.isNotBlank(fileName)) {
                return fileName;
            }
            return importService.filename();
        }
        return this.getClass().getSimpleName();
    }

    /**
     * 获取缓存字段或创建缓存
     *
     * @param modelClass 模型class
     * @param fieldName  字段名称
     * @return Field
     */
    private <T> Field getCacheFieldOrCreate(Class<T> modelClass, String fieldName) {
        Map<String, Field> cache = FIELD_CACHE.get(modelClass);
        if (Objects.isNull(cache)) {
            List<Field> fields = ReflectionUtils.getFields(modelClass);
            cache = StreamUtils.singleGroup(fields, Field::getName);
            FIELD_CACHE.put(modelClass, cache);
        }
        return cache.get(fieldName);
    }
}
