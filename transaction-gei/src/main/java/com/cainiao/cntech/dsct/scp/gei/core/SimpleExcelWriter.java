package com.cainiao.cntech.dsct.scp.gei.core;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.cainiao.cntech.dsct.scp.gei.common.exception.AppException;
import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.ReflectionUtils;
import com.cainiao.cntech.dsct.scp.gei.core.model.BeforeWriteCellWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelWriteWrapper;
import com.cainiao.cntech.dsct.scp.gei.ext.converter.ExcelWriteConverter;
import com.cainiao.cntech.dsct.scp.gei.ext.converter.WriteConverterManager;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ExportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.ext.style.CustomHeadCellStyleStrategy;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-02 18:06
 * @description excel 写出器
 */
public class SimpleExcelWriter {
    private static final int MAX_WRITE_LINE = 1048575;
    @Resource
    private ExportPostProcessorManager exportPostProcessorManager;

    /**
     * 数据转 excel 列
     *
     * @param headList 列名
     * @param title    标题
     * @return 行数据
     */
    private static List<List<String>> convertWriteHeader(List<List<String>> headList, String title) {
        for (List<String> value : headList) {
            if (StringUtils.isNotBlank(title)) {
                value.add(0, title);
            }
        }
        return headList;
    }

    /**
     * 输出数据到 HttpServletResponse
     *
     * @param writeWrapper Excel写出包装类
     * @param response     HttpServletResponse
     */
    public void write(ExcelWriteWrapper writeWrapper, HttpServletResponse response) {
        if (Objects.isNull(writeWrapper)) {
            return;
        }
        String fileName = writeWrapper.getFileName();
        List<?> dataList = writeWrapper.getExportDataList();
        List<ExcelMetaInfo> metaInfoList = writeWrapper.getMetaInfoList();
        List<List<String>> headList = Lists.newArrayList();
        List<String> columnList = Lists.newArrayList();
        for (ExcelMetaInfo metaInfo : Optional.ofNullable(metaInfoList).orElse(new ArrayList<>())) {
            List<String> tempHead = new ArrayList<>();
            genMultiHeaderList(metaInfo, tempHead, columnList, headList);
        }
        try {
            preWriteExcel(response, fileName);
            EasyExcel.write(response.getOutputStream())
                    .head(convertWriteHeader(headList, writeWrapper.getTitle()))
                    .includeColumnFieldNames(columnList)
                    .registerWriteHandler(getWriteHandler(writeWrapper.getTitle()))
                    .sheet(writeWrapper.getSheetName())
                    .doWrite(convertWriteData(dataList, columnList, writeWrapper));
        } catch (IOException e) {
            throw new AppException(e, ErrorCode.SYSTEM_ERROR, "写出数据错误");
        }
    }

    public byte[] getByteArray(ExcelWriteWrapper writeWrapper) {
        if (Objects.isNull(writeWrapper)) {
            return new byte[0];
        }
        List<ExcelMetaInfo> metaInfoList = writeWrapper.getMetaInfoList();
        List<List<String>> headList = Lists.newArrayList();
        List<String> columnList = Lists.newArrayList();
        for (ExcelMetaInfo metaInfo : Optional.ofNullable(metaInfoList).orElse(new ArrayList<>())) {
            List<String> tempHead = new ArrayList<>();
            genMultiHeaderList(metaInfo, tempHead, columnList, headList);
        }
        List<List<String>> header = convertWriteHeader(headList, writeWrapper.getTitle());
        WriteHandler writeHandler = getWriteHandler(writeWrapper.getTitle());
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<?> dataList = writeWrapper.getExportDataList();
        int dataListIndex = 0;
        int sheetIndex = 1;
        ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
        try {
            while (dataListIndex < dataList.size()) {
                List<?> list = dataList.subList(dataListIndex, Math.min(dataListIndex + MAX_WRITE_LINE, dataList.size()));
                dataListIndex += MAX_WRITE_LINE;
                // 创建新的 Sheet（按序号命名）
                WriteSheet writeSheet = EasyExcel.writerSheet(writeWrapper.getSheetName() + sheetIndex++)
                        .head(header)
                        .includeColumnFieldNames(columnList)
                        .registerWriteHandler(writeHandler)
                        .build();
                // 写入当前 Sheet
                excelWriter.write(convertWriteData(list, columnList, writeWrapper), writeSheet);
            }
        } finally {
            if (Objects.nonNull(excelWriter)) {
                excelWriter.finish();
            }
        }
        return outputStream.toByteArray();
    }

    private void genMultiHeaderList(ExcelMetaInfo excelMetaInfo, List<String> headList, List<String> columnList, List<List<String>> multiHeadList) {
        if (Objects.isNull(excelMetaInfo)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(excelMetaInfo.getChildren())) {
            headList.add(excelMetaInfo.getName());
            for (ExcelMetaInfo child : excelMetaInfo.getChildren()) {
                genMultiHeaderList(child, headList, columnList, multiHeadList);
            }
        } else {
            List<String> headListCopy = JsonUtils.copy(headList);
            headListCopy.add(excelMetaInfo.getName());
            multiHeadList.add(headListCopy);
            columnList.add(excelMetaInfo.getCode());
        }
    }

    private void preWriteExcel(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition",
                String.format("attachment;filename=%s.xlsx", URLEncoder.encode(fileName, "utf-8")));
    }

    /**
     * 数据转 excel 行数据
     *
     * @param dataList   数据
     * @param columnList 字段
     * @return 行数据
     */
    private List<List<?>> convertWriteData(List<?> dataList, List<String> columnList, ExcelWriteWrapper writeWrapper) {
        List<List<?>> dataResultList = new ArrayList<>();
        List<String> assignUseWriteConverterNames = Optional.ofNullable(writeWrapper.getAssignUseWriteConverterNames()).orElse(new ArrayList<>());
        List<ExcelWriteConverter> assignUseExcelWriteConverters = Optional.ofNullable(writeWrapper.getAssignUseExcelWriteConverters()).orElse(new ArrayList<>());
        BeforeWriteCellWrapper beforeWriteCellWrapper = BeforeWriteCellWrapper.of(dataList, columnList);
        int rowIndex = 0;
        Class<?> modelClass = null;
        for (Object t : Optional.ofNullable(dataList).orElse(new ArrayList<>())) {
            if (Objects.isNull(modelClass)) {
                modelClass = t.getClass();
                beforeWriteCellWrapper.setModelClass(modelClass);
            }
            List<Object> data = new ArrayList<>();
            int columnIndex = 0;
            for (String fieldName : columnList) {
                Object fieldValue;
                if (t instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) t;
                    fieldValue = map.get(fieldName);
                } else {
                    fieldValue = ReflectionUtils.getFieldValue(fieldName, t);
                }
                beforeWriteCellWrapper.setRowData(t).setRowIndex(rowIndex).setColumnIndex(columnIndex)
                        .setFieldName(fieldName).setFieldValue(fieldValue);
                Object replaceValue = invokePostProcessBeforeWriteCellValue(beforeWriteCellWrapper);
                if (Objects.nonNull(replaceValue)) {
                    fieldValue = replaceValue;
                }
                data.add(WriteConverterManager.convert(fieldValue, assignUseWriteConverterNames, assignUseExcelWriteConverters));
                columnIndex++;
            }
            dataResultList.add(data);
            rowIndex++;
        }
        return dataResultList;
    }

    /**
     * 获取Excel WriteHandler处理器
     * // 自适应 com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
     *
     * @return WriteHandler处理器
     */
    private WriteHandler getWriteHandler(String title) {
        Boolean hasTitle = StringUtils.isNotEmpty(title);
        return new CustomHeadCellStyleStrategy(hasTitle);
    }

    private Object invokePostProcessBeforeWriteCellValue(BeforeWriteCellWrapper beforeWriteCellWrapper) {
        Object replaceValue = null;
        for (ExportPostProcessor processor : exportPostProcessorManager.getProcessors()) {
            replaceValue = processor.postProcessBeforeWriteCellValue(beforeWriteCellWrapper);
        }
        return replaceValue;
    }
}
