package com.cainiao.cntech.dsct.scp.gei.common.utils;

import com.cainiao.cntech.dsct.scp.gei.common.request.GeiPageQueryRequest;

import java.util.Objects;
import java.util.function.Supplier;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-18 17:02
 * @description
 */
public class PageUtils {
    public static Long prePageHandle(GeiPageQueryRequest request, Supplier<Long> totalGetter) {
        if (Objects.isNull(totalGetter)) {
            return null;
        }
        if (request.enablePaging()) {
            Long total = totalGetter.get();
            request.setCurrentPage(PageUtils.calcCurrentPage(total, request.getPageSize(), request.getCurrentPage()));
            request.calcPage();
            return total;
        }
        return null;
    }

    /**
     * 计算总页
     * @param total 数据总量
     * @param pageSize 页步长
     * @return 页数量
     */
    private static long calcTotalPage(Long total, Long pageSize) {
        if (total == null || total == 0 || pageSize == null || pageSize == 0) {
            return 1L;
        }
        return (total + pageSize - 1) / pageSize;
    }
    /**
     * 根据数据总量和页步长计算共多少页， 并与传入当前页做比较，取最小值
     * @param total 数据总量
     * @param pageSize 页步长
     * @param currentPage 当前页
     * @return 页数量
     */
    public static long calcCurrentPage(Long total, Long pageSize, Long currentPage) {
        if (Objects.isNull(currentPage)) {
            currentPage = 1L;
        }
        return Math.min(calcTotalPage(total, pageSize), currentPage);
    }
}
