package com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext;

import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.model.ConvertExtensibleWrapper;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-23 10:51
 * @description LocalDateTime to String
 */
public class StringToLocalDateTimeConvertExt implements ConvertProcessExtensible<LocalDateTime> {
    @Override
    public LocalDateTime doConvert(ConvertExtensibleWrapper ew) throws IllegalAccessException {
        Object o = ew.getSourceField().get(ew.getSourceObject());
        if (o instanceof String) {
            String date = (String) o;
            return DateUtil.stringToLocalDateTime(date, DateUtil.DEFAULT_DATETIME_FORMAT);
        }
        return null;
    }
}
