package com.cainiao.cntech.dsct.scp.gei.biz.service.request.update;

import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTaskStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:44
 * @description 主任务修改请求
 */
@Data
public class GeiTaskUpdateRequest {
    /**
     * '任务ID'
     */
    private Long taskId;
    /**
     * 任务状态，CREATED新建，AWAIT待运行，RUNNING运行中，FAILED异常，SUCCESS成功
     * @see GeiTaskStatusEnum
     */
    private String status;
    /**
     * '任务状态产生的原因'
     */
    private String statusDesc;
    /**
     * '任务开始处理的时间戳'
     */
    private LocalDateTime gmtProcessStarted;
    /**
     * '任务结束的时间戳'
     */
    private LocalDateTime gmtProcessFinished;

    /**
     * '合并开始的时间'
     */
    private LocalDateTime gmtMergeStarted;
    /**
     * 'merge阶段的尝试次数'
     */
    private Integer mergeTryTimes;
    /**
     * '文件名'
     */
    private String fileName;
    /**
     * '文件地址'
     */
    private String fileAddress;
    /**
     * '操作人编码'
     */
    private String operatorCode;
    /**
     * '操作人名称'
     */
    private String operatorName;
}
