package com.cainiao.cntech.dsct.scp.gei.ext.processor;

import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-10 11:20
 * @description 数据导入扩展
 */
public interface ImportPostProcessor<T extends ImportTemplateVO> {
    /**
     * 导入数据校验前执行该方法
     *
     * @param importDataWrapper 导入的数据包装
     * @return true 继续校验，false 终止导入行为，返回失败
     */
    default boolean preValid(ImportDataWrapper<T> importDataWrapper) {
        return true;
    }

    /**
     * 导入数据前扩展
     *
     * @param importDataWrapper
     */
    default void postProcessorBeforeImportData(ImportDataWrapper<T> importDataWrapper) {
    }

    /**
     * 导入数据后扩展
     *
     * @param importDataWrapper
     */
    default void postProcessorAfterImportData(ImportDataWrapper<T> importDataWrapper, ImportResult importResult) {
    }
    /**
     * 导入流程开始前扩展
     */
    default void postProcessorBeforeImportProcessing() {
    }

    /**
     * 导入流程结束后扩展
     *
     * @param importDataWrapper
     */
    default void postProcessorAfterImportProcessed(ImportDataWrapper<T> importDataWrapper) {
    }
}
