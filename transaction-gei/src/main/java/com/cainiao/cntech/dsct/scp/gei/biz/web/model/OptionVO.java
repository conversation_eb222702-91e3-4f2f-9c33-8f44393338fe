package com.cainiao.cntech.dsct.scp.gei.biz.web.model;

import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OptionVO {
    @ConvertField("name")
    private String label;
    @ConvertField("code")
    private Object value;
    private Boolean highlight = false;
    private String type;
    @ConvertField(deep = true)
    private List<OptionVO> children;

    public OptionVO() {}

    public OptionVO(Object value, String label) {
        this.value = value;
        this.label = label;
    }

    public OptionVO(Object value, String label, String type) {
        this.value = value;
        this.label = label;
        this.type = type;
    }

    public static OptionVO of(Object value, String label){
        return new OptionVO(value, label);
    }
    public static OptionVO of(String label, Object value, Boolean highlight){
        OptionVO optionVO = new OptionVO(value, label);
        optionVO.setHighlight(highlight);
        return optionVO;
    }
    /**
     * 添加子元素
     *
     * @param child
     */
    public void addChild(OptionVO child) {
        if (child == null) {
            return;
        }
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
    }

    /**
     * 添加子元素，去重
     *
     * @param child
     */
    public OptionVO addChildUnique(OptionVO child) {
        OptionVO ret = child;
        if (child == null) {
            return ret;
        }
        if (children == null) {
            children = new ArrayList<>();
        }
        boolean hasSame = false;
        for (OptionVO vo : children) {
            if (StringUtils.equals(vo.getValue() + "", child.getValue() + "")) {
                hasSame = true;
                ret = vo;
                break;
            }
        }
        if (!hasSame) {
            children.add(child);
        }
        return ret;
    }
}
