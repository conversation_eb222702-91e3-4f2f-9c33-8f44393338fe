package com.cainiao.cntech.dsct.scp.gei.core;

import com.cainiao.cntech.dsct.scp.gei.ext.processor.SliceDataPostProcessor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ConfigurableApplicationContext;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-14 19:57
 * @description
 */

public class SliceDataManager implements ApplicationRunner {

    @Resource
    private ConfigurableApplicationContext context;
    private List<SliceDataPostProcessor> SLICE_PROCESSORS;

    public List<SliceDataPostProcessor> getProcessors() {
        return SLICE_PROCESSORS;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        Map<String, SliceDataPostProcessor> beans = context.getBeansOfType(SliceDataPostProcessor.class);
        SLICE_PROCESSORS = new LinkedList<>();
        SLICE_PROCESSORS.addAll(beans.values());
    }
}
