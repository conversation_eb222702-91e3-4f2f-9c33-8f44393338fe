package com.cainiao.cntech.dsct.scp.gei.support.annotation;

import java.lang.annotation.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-13 09:44
 * @description 通用的校验注解
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelValid {
    /**
     * 是否进行非空校验
     */
    boolean notEmpty() default true;

    /**
     * 是否进行字符串有效性校验
     */
    boolean notBlank() default true;

    /**
     * 是否进行邮箱校验
     */
    boolean validEmail() default false;

    /**
     * 是否进行大于等于0的校验
     */
    boolean geZero() default false;

    /**
     * 是否进行大于0的校验
     */
    boolean gtZero() default false;

    /**
     * 是否进行字符串长度限制的校验，
     * 值填写为校验的长度： -1 表示不校验，大于的等于0表示需要校验的长度
     */
    int charSizeLimit() default -1;

    /**
     * 是否进行百分比数据的校验
     */
    boolean percent() default false;

    /**
     * 是否进行小数位限制的校验
     * 值填写为校验的长度： -1 表示不校验，大于的等于0表示需要校验的长度
     */
    int decimalSizeLimit() default -1;

    /**
     * 是否进行布尔数值校验，1是，0否
     */
    boolean boolInt() default false;
}
