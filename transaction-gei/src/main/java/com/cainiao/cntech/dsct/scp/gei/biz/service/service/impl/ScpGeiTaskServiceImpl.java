package com.cainiao.cntech.dsct.scp.gei.biz.service.service.impl;

import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiChildTaskDO;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiTaskDO;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.dto.ScpGeiTaskDTO;
import com.cainiao.cntech.dsct.scp.gei.biz.service.manager.ScpGeiChildTaskManager;
import com.cainiao.cntech.dsct.scp.gei.biz.service.manager.ScpGeiTaskManager;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.insert.GeiChildTaskInsertRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.insert.GeiTaskInsertRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.query.GeiTaskPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.update.GeiTaskUpdateRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.service.ScpGeiTaskService;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTaskStatusEnum;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import com.cainiao.cntech.dsct.scp.gei.common.error.GeiError;
import com.cainiao.cntech.dsct.scp.gei.common.utils.SnowflakeIdWorker;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:45
 * @description
 */
@Service
public class ScpGeiTaskServiceImpl implements ScpGeiTaskService {
    @Resource
    private ScpGeiTaskManager scpGeiTaskManager;
    @Resource
    private ScpGeiChildTaskManager scpGeiChildTaskManager;
    private final SnowflakeIdWorker TASK_ID_GENERATOR = new SnowflakeIdWorker(0, 0);
    @Override
    public List<ScpGeiTaskDTO> queryPageData(GeiTaskPageQueryRequest request) {
        List<ScpGeiTaskDTO> list = scpGeiTaskManager.queryPageData(request);
        LocalDateTime now = LocalDateTime.now();
        for (ScpGeiTaskDTO dto : list) {
            GeiTypeEnum geiType = GeiTypeEnum.getByCode(dto.getTaskType());
            if(Objects.nonNull(geiType)){ dto.setTaskTypeDesc(geiType.getName()); }
            GeiTaskStatusEnum status = GeiTaskStatusEnum.getByCode(dto.getStatus());
            if(Objects.nonNull(status)){ dto.setStatusName(status.getName()); }
            BigDecimal taskProgress = dto.getTaskProgress();
            if(Objects.isNull(taskProgress) || GeiTypeEnum.IMPORT.isThis(dto.getTaskType())){
                continue;
            }
            LocalDateTime gmtClearExpired = dto.getGmtClearExpired();
            // 进度百分比=1 且 （过期时间等于null 或 当前时间小于过期时间） 可以下载
            if(BigDecimal.ONE.compareTo(taskProgress) == 0 && Objects.nonNull(dto.getFileAddress())
                    && ( Objects.isNull(gmtClearExpired) || now.isBefore(gmtClearExpired) ) ) {
                dto.setAllowDownload(Boolean.TRUE);
            }else {
                dto.setFileAddress(null);
            }
        }
        return list;
    }
    @Override
    public Long queryCount(GeiTaskPageQueryRequest request) {
        return scpGeiTaskManager.queryCount(request);
    }

    @Override
    public List<ScpGeiTaskDTO> queryExpireTaskList() {
        return scpGeiTaskManager.queryExpireTaskList();
    }
    @Override
    public List<ScpGeiTaskDTO> queryProcessExpireTaskList() {
        return scpGeiTaskManager.queryProcessExpireTaskList();
    }

    @Override
    public ScpGeiTaskDTO getByTaskId(Long taskId) {
        GeiTaskPageQueryRequest request = new GeiTaskPageQueryRequest();
        request.setPaging(false);
        request.setTaskId(taskId);
        List<ScpGeiTaskDTO> list = scpGeiTaskManager.queryPageData(request);
        if(CollectionUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    @Override
    public Long updateByTaskId(GeiTaskUpdateRequest request) {
        return scpGeiTaskManager.updateByTaskId(GeiCommonConvert.convert(request, ScpGeiTaskDO.class));
    }

    @Transactional
    @Override
    public Long createTask(GeiTaskInsertRequest request, List<GeiChildTaskInsertRequest> childRequests) {
        // 校验请求参数
        verifyInsertRequest(request, childRequests);
        // 转换并填充参数
        ScpGeiTaskDO scpGeiTaskDO = GeiCommonConvert.convert(request, ScpGeiTaskDO.class);
        List<ScpGeiChildTaskDO> scpGeiChildTaskDOList = GeiCommonConvert.convert(childRequests, ScpGeiChildTaskDO.class);
        fillInsertRequest(scpGeiTaskDO, scpGeiChildTaskDOList);
        // 保存主任务
        scpGeiTaskManager.insert(scpGeiTaskDO);
        // 保存子任务
        scpGeiChildTaskManager.insert(scpGeiChildTaskDOList);
        return scpGeiTaskDO.getTaskId();
    }

    @Transactional
    @Override
    public Long clearTask(List<Long> parendTaskIds) {
        scpGeiTaskManager.delete(parendTaskIds);
        scpGeiChildTaskManager.delete(parendTaskIds);
        return 1L;
    }

    @Transactional
    @Override
    public Long closeProcessExpireTask(List<Long> parendTaskIds) {
        scpGeiTaskManager.closeProcessExpireByTaskId(parendTaskIds);
        scpGeiChildTaskManager.closeProcessExpireByParentTaskId(parendTaskIds);
        return 1L;
    }


    /**
     * 校验新增请求体
     * @param request 主任务新增请求体
     * @param childRequests 子任务新增请求体
     */
    private void verifyInsertRequest(GeiTaskInsertRequest request, List<GeiChildTaskInsertRequest> childRequests){
        // verify request
        Assert.notNull(request.getTaskType(), ErrorCode.ILLEGAL_ARGUMENT, GeiError.TASK_TYPE_EMPTY.getError());
        GeiTypeEnum taskType = GeiTypeEnum.getByCode(request.getTaskType());
        Assert.isTrue(Objects.nonNull(taskType), ErrorCode.ILLEGAL_ARGUMENT, GeiError.TASK_TYPE_ILLEGAL.getError());

        for (GeiChildTaskInsertRequest childRequest : childRequests) {
            // 校验
        }

    }

    /**
     * 填充新增请求体
     * @param parentTask 主任务新增请求体
     * @param childTaskList 子任务新增请求体
     */
    private void fillInsertRequest(ScpGeiTaskDO parentTask, List<ScpGeiChildTaskDO> childTaskList) {
        long parentTaskId = TASK_ID_GENERATOR.nextId();
        // 填充父任务数据对象
        parentTask.setTaskId(parentTaskId);
        parentTask.setStatus(GeiTaskStatusEnum.CREATED.getCode());
        // 填充子任务数据对象
        int i = 0;
        for (ScpGeiChildTaskDO childTask : childTaskList) {
            childTask.setParentTaskId(parentTaskId);
            childTask.setTaskId(String.format("%s_%s", parentTaskId, i++));
            childTask.setRequestParams(parentTask.getRequestParams());
            childTask.setTaskType(parentTask.getTaskType());
            childTask.setStatus(GeiTaskStatusEnum.CREATED.getCode());
            childTask.setProperties(parentTask.getProperties());
        }
    }
}
