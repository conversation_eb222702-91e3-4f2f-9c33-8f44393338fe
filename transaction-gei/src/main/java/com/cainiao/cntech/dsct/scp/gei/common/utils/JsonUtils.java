package com.cainiao.cntech.dsct.scp.gei.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-04-30 17:01
 * @description
 */
public class JsonUtils {
    public static <T> String toStr(T t) {
        if (Objects.isNull(t)) {
            return "";
        }
        return JSON.toJSONString(t);
    }

    public static Map<String, Object> toMap(String str, JSONReader.Feature... features) {
        return JSONObject.parseObject(str, Map.class, features);
    }

    public static <T> Map<String, Object> toMap(T t, JSONReader.Feature... features) {
        return toMap(toStr(t), features);
    }

    public static <T> List<Map<String, Object>> toList(List<T> t) {
        return t.stream().map(JsonUtils::toMap).collect(Collectors.toList());
    }

    public static <T> T toObject(String str, TypeReference<T> reference, JSONReader.Feature... features) {
        if (StringUtils.isBlank(str) || reference == null) {
            return null;
        }
        return JSON.parseObject(str, reference, features);
    }

    public static <T> T toObject(String str, Class<T> tClass) {
        return JSON.parseObject(str, tClass);
    }

    public static <K, V, T> T toObject(Map<K, V> map, Class<T> tClass) {
        return toObject(JSON.toJSONString(map), tClass);
    }

    public static <K, V> Map<K, V> copy(Map<K, V> map) {
        if (Objects.isNull(map)) {
            return null;
        }
        return toObject(toStr(map), new TypeReference<Map<K, V>>() {
        });
    }

    public static <T> T copy(T t) {
        if (Objects.isNull(t)) {
            return null;
        }
        return toObject(toStr(t), (Class<T>) t.getClass());
    }

    public static <T> List<T> copy(List<T> t) {
        if (Objects.isNull(t)) {
            return null;
        } else if (CollectionUtils.isEmpty(t)) {
            return new ArrayList<>();
        }
        return t.stream().map(JsonUtils::copy).collect(Collectors.toList());
    }
}
