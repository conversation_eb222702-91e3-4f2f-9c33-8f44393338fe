package com.cainiao.cntech.dsct.scp.gei.core.model;


import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import com.cainiao.cntech.dsct.scp.gei.ext.converter.ExcelWriteConverter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 14:30
 * @description excel数据包装
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ExcelMetaInfoWrapper {
    /**
     * 用作响应体时 所需要的字段
     */
    private boolean success;

    /**
     * 列表数据(表头)
     */
    @ConvertField(deep = true)
    private List<ExcelMetaInfo> meta;

    /**
     * 数据列表（展示）
     */
    private List<?> list;

    /**
     * 主键（区分唯一数据）
     */
    private String key;

    private Object data;
    /**
     * 指定优先使用写出转换器
     */
    private List<String> assignUseWriteConverterNames;
    private List<ExcelWriteConverter> assignUseExcelWriteConverters;

    public ExcelMetaInfoWrapper(List<?> list, List<ExcelMetaInfo> meta) {
        this.list = list;
        this.meta = meta;
    }

    public ExcelMetaInfoWrapper(List<?> list, List<ExcelMetaInfo> meta, Object data) {
        this(list, meta);
        this.data = data;
    }

    public static <T> ExcelMetaInfoWrapper of(ImportResult importResult) {
        return new ExcelMetaInfoWrapper(importResult.getList(), importResult.getMeta(), importResult.getData());
    }

    public static <T> ExcelMetaInfoWrapper of() {
        return new ExcelMetaInfoWrapper(Collections.emptyList(), Collections.emptyList());
    }

    public static <T> ExcelMetaInfoWrapper of(List<T> list, List<ExcelMetaInfo> meta) {
        return new ExcelMetaInfoWrapper(list, meta);
    }

    public static <T> ExcelMetaInfoWrapper of(List<T> list) {
        Class<?> metaInfoClazz = null;
        if (Objects.nonNull(list) && !list.isEmpty()) {
            metaInfoClazz = list.get(0).getClass();
        }
        return of(metaInfoClazz, list);
    }

    public static ExcelMetaInfoWrapper of(Class<?> metaInfoClazz) {
        return of(metaInfoClazz, Collections.emptyList());
    }

    public static <T> ExcelMetaInfoWrapper of(Class<?> metaInfoClazz, List<T> list) {
        return new ExcelMetaInfoWrapper(list, ExcelMetaInfoCreator.create(metaInfoClazz));
    }

    public ExcelMetaInfoWrapper success() {
        this.setSuccess(true);
        return this;
    }

    public ExcelMetaInfoWrapper failed() {
        this.setSuccess(false);
        return this;
    }

    public ExcelMetaInfoWrapper addWriteConverter(String beanName) {
        if (StringUtils.isBlank(beanName)) {
            return this;
        }
        if (getAssignUseExcelWriteConverters() == null) {
            setAssignUseWriteConverterNames(new ArrayList<>());
        }
        getAssignUseWriteConverterNames().add(beanName);
        return this;
    }

    public ExcelMetaInfoWrapper addWriteConverter(ExcelWriteConverter converter) {
        if (Objects.isNull(converter)) {
            return this;
        }
        if (getAssignUseExcelWriteConverters() == null) {
            setAssignUseExcelWriteConverters(new ArrayList<>());
        }
        getAssignUseExcelWriteConverters().add(converter);
        return this;
    }
}
