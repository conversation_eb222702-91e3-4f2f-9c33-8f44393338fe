package com.cainiao.cntech.dsct.scp.gei.ext.validator.common;

import cn.hutool.core.lang.Validator;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.ext.validator.ImportValidator;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelValid;

import java.lang.reflect.Field;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-13 16:27
 * @description
 */
public class EmailImportValidator<T> implements ImportValidator<T> {
    @Override
    public boolean require(T t, Field field, String fieldName) {
        return field.isAnnotationPresent(ExcelValid.class) && field.getAnnotation(ExcelValid.class).validEmail();
    }

    @Override
    public String validate(ImportDataWrapper<T> importDataWrapper, T t, Field field, Object fieldValue) {
        if (null == fieldValue) {
            return null;
        } else {
            return Validator.isEmail(fieldValue.toString()) ? null : "%s必须为邮箱格式！";
        }
    }
}
