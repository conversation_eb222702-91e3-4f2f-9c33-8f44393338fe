//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cainiao.cntech.dsct.scp.gei.common.util;

import java.net.InetAddress;

public class IpUtils {
    public IpUtils() {
    }

    public static String getServerIp() {
        String serverIp = "";

        try {
            serverIp = InetAddress.getLocalHost().getHostAddress();
        } catch (Exception var2) {
            var2.printStackTrace();
        }

        return serverIp;
    }
}
