//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cainiao.cntech.dsct.scp.gei.common.exception;

import com.cainiao.cntech.dsct.scp.gei.common.log.FormatUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

public class Assert {
    public Assert() {
    }

    public static <T> T notNull(T reference, ErrorCode errorCode) {
        if (reference == null) {
            throw new AppException(errorCode);
        } else {
            return reference;
        }
    }

    public static <T> T notNull(T reference, ErrorCode errorCode, String logTemplate, Object... argv) {
        if (reference == null) {
            throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
        } else {
            return reference;
        }
    }

    public static <T> T isNull(T reference, ErrorCode errorCode, String logTemplate, Object... argv) {
        if (reference != null) {
            throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
        } else {
            return reference;
        }
    }

    public static void isTrue(boolean expression, ErrorCode errorCode, String logTemplate, Object... argv) {
        if (!expression) {
            throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
        }
    }

    public static void isFalse(boolean expression, ErrorCode errorCode, String logTemplate, Object... argv) {
        if (expression) {
            throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
        }
    }

    public static void isEqual(Object a, Object b, ErrorCode errorCode, String logTemplate, Object... argv) {
        if (!Objects.equals(a, b)) {
            throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
        }
    }

    public static void notEqual(Object a, Object b, ErrorCode errorCode, String logTemplate, Object... argv) {
        if (Objects.equals(a, b)) {
            throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
        }
    }

    public static Collection<?> notEmpty(Collection<?> c, ErrorCode errorCode, String logTemplate, Object... argv) {
        if (c != null && !c.isEmpty()) {
            return c;
        } else {
            throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
        }
    }

    public static Collection<?> notEmpty(Collection<?> c, ErrorCode errorCode) {
        if (c != null && !c.isEmpty()) {
            return c;
        } else {
            throw new AppException(errorCode);
        }
    }

    public static Map<?, ?> notEmpty(Map<?, ?> m, ErrorCode errorCode, String logTemplate, Object... argv) {
        if (m != null && !m.isEmpty()) {
            return m;
        } else {
            throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
        }
    }

    public static String notEmpty(String str, ErrorCode errorCode, String logTemplate, Object... argv) {
        if (str != null && str.length() != 0) {
            return str;
        } else {
            throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
        }
    }

    public static void notBlank(String str, ErrorCode errorCode, String logTemplate, Object... argv) {
        if (StringUtils.isBlank(str)) {
            throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
        }
    }

    public static void throwException(ErrorCode errorCode, String logTemplate, Object... argv) throws AppException {
        throw new AppException(errorCode, FormatUtils.format(logTemplate, argv));
    }

    public static void throwException(Throwable t, ErrorCode errorCode, String logTemplate, Object... argv) throws AppException {
        throw new AppException(t, errorCode, FormatUtils.format(logTemplate, argv));
    }
}
