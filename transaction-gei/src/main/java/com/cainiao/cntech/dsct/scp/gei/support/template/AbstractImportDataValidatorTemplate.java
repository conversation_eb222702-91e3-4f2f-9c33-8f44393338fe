package com.cainiao.cntech.dsct.scp.gei.support.template;

import com.cainiao.cntech.dsct.scp.gei.common.utils.GenericsSearchUtils;
import com.cainiao.cntech.dsct.scp.gei.ext.validator.ImportValidator;
import com.cainiao.cntech.dsct.scp.gei.ext.validator.ValidatorFactory;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportMapProxy;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 20:25
 * @description 抽象导入校验器模板
 */
public abstract class AbstractImportDataValidatorTemplate<I extends ImportTemplateVO> implements ImportDataTemplate<I>
        , ImportDataValidatorRegister<I> {
    /**
     * 通用校验器工厂
     */
    protected final ValidatorFactory<? extends ImportTemplateVO> validatorFactory = ValidatorFactory.getInstance();
    /**
     * 按行粒度的导入校验器
     * 所有行依次经历所有行校验器的校验，调用校验器方法时仅会传递维度code，一行数据的对象，属性及属性名为null
     */
    protected final List<ImportValidator<I>> rowValidators = new LinkedList<>();
    /**
     * 按字段粒度的导入校验器,
     * 所有字段依次经历所有字段校验器的校验，调用校验器方法时会传递维度code，一行数据的对象，属性及属性名
     */
    protected final List<ImportValidator<I>> colValidators = new LinkedList<>();

    public AbstractImportDataValidatorTemplate() {
        register();
        Class<?> modelClass = GenericsSearchUtils.searchClass(this, ImportDataTemplate.class, 0);
        boolean isMapProxy = ImportMapProxy.class.equals(modelClass);
        if (!isMapProxy) {
            Collection<? extends ImportValidator<? extends ImportTemplateVO>> common = validatorFactory.getValidators();
            colValidators.addAll((Collection<? extends ImportValidator<I>>) common);
        }
    }

    /**
     * 注册函数， 在对象实例化阶段触发调用。
     * 子类自行重写，以注册自定义的校验器
     */
    protected void register() {
    }

    @Override
    public void registerColValidator(ImportValidator<I> validator) {
        colValidators.add(validator);
    }

    @Override
    public void registerRowValidator(ImportValidator<I> validator) {
        rowValidators.add(validator);
    }

    @Override
    public List<ImportValidator<I>> getColValidators() {
        return colValidators;
    }

    @Override
    public List<ImportValidator<I>> getRowValidators() {
        return rowValidators;
    }
}
