package com.cainiao.cntech.dsct.scp.gei.ext.converter;


import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-11 18:03
 * @description list -> string转换器。 用于将对象实体中字段值类型为list的数据转换为字符串返回
 */
public class ListToStringExcelConverter implements ExcelWriteConverter<String, List> {
    @Override
    public boolean support(Object fieldValue) {
        return fieldValue instanceof List;
    }

    @Override
    public String convert(List fieldValue) {
        StringBuilder stringBuilder = new StringBuilder();
        for (Object o : fieldValue) {
            stringBuilder.append(o).append("\n");
        }
        if (stringBuilder.length() < 1) {
            return "";
        }
        return stringBuilder.substring(0, stringBuilder.length() - 1);
    }
}