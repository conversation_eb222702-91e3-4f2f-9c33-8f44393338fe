package com.cainiao.cntech.dsct.scp.gei.core.executor;

import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.core.SimpleExcelWriter;
import com.cainiao.cntech.dsct.scp.gei.core.dict.GeiDictResolver;
import com.cainiao.cntech.dsct.scp.gei.core.executor.model.GeiTaskResult;
import com.cainiao.cntech.dsct.scp.gei.core.executor.model.ResultHandleArgs;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelWriteWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.storage.StorageTemplate;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiFunction;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 18:51
 * @description 任务返回结果处理器
 */
public class GeiTaskResultHandler {
    @Resource
    private StorageTemplate storageTemplate;
    @Resource
    private GeiDictResolver geiDictResolver;
    @Resource
    private SimpleExcelWriter simpleExcelWriter;
    private final Map<GeiTypeEnum, BiFunction<ResultHandleArgs, List<CompletableFuture<?>>, GeiTaskResult>> HANDLERS;

    public GeiTaskResultHandler() {
        HANDLERS = Maps.newHashMapWithExpectedSize(2);
        HANDLERS.put(GeiTypeEnum.EXPORT, (args, tasks) -> {
            ExcelWriteWrapper writeWrapper = null;
            String filename = null;
            List dataList = new LinkedList<>();
            for (CompletableFuture<?> task : tasks) {
                ExcelWriteWrapper result = (ExcelWriteWrapper) task.join();
                if (Objects.isNull(writeWrapper)) {
                    writeWrapper = result;
                }
                if (Objects.isNull(filename)) {
                    filename = result.getFileName();
                }
                dataList.addAll(result.getExportDataList());
            }
            writeWrapper.setExportDataList(dataList);
            List<ExcelMetaInfo> metaInfoList = writeWrapper.getMetaInfoList();
            metaInfoList = geiDictResolver.resolveExportDict(args.getTemplateDictCode(), metaInfoList);
            writeWrapper.setMetaInfoList(metaInfoList);
            storageTemplate.upload(filename, simpleExcelWriter.getByteArray(writeWrapper));

            LocalDateTime gmtClearExpired = args.getGeiTask().getGmtClearExpired();
            if (Objects.isNull(gmtClearExpired)) {
                gmtClearExpired = LocalDateTime.now().plusDays(2);
            }
            return GeiTaskResult.of(filename, storageTemplate.getUrl(filename, filename, gmtClearExpired));
        });
        HANDLERS.put(GeiTypeEnum.IMPORT, (args, tasks) -> {
            AtomicBoolean importState = new AtomicBoolean(true);
            List<ExcelMetaInfoWrapper> finalResultList = new ArrayList<>();
            int currentIndex = 0;
            for (CompletableFuture<?> task : tasks) {
                ExcelMetaInfoWrapper wrapper = (ExcelMetaInfoWrapper) task.join();
                if (Objects.isNull(wrapper) || wrapper.isSuccess()) {
                    continue;
                }
                List<ExcelMetaInfo> meta = wrapper.getMeta();
                int importResultIndex = getImportResultIndex(finalResultList, meta);
                ExcelMetaInfoWrapper finalResult;
                if (importResultIndex == -1) {
                    finalResult = ExcelMetaInfoWrapper.of(new ArrayList<>(), null);
                    finalResultList.add(currentIndex++, finalResult);
                } else {
                    finalResult = finalResultList.get(importResultIndex);
                }
                if (!wrapper.isSuccess()) {
                    importState.set(false);
                }
                List list = wrapper.getList();
                if (Objects.nonNull(list)) {
                    finalResult.getList().addAll(list);
                }
                if (Objects.nonNull(wrapper.getData())) {
                    finalResult.setData(wrapper.getData());
                }
                if (CollectionUtils.isEmpty(finalResult.getMeta())) {
                    finalResult.setMeta(wrapper.getMeta());
                }
                if (Objects.nonNull(wrapper.getKey())) {
                    finalResult.setKey(wrapper.getKey());
                }
            }
            return GeiTaskResult.of(importState.get(), finalResultList);
        });
    }
    private int getImportResultIndex(List<ExcelMetaInfoWrapper> finalResultList, List<ExcelMetaInfo> meta) {
        if (CollectionUtils.isEmpty(finalResultList)) {
            return -1;
        }
        int finalResultIndex = 0;
        a: for (ExcelMetaInfoWrapper metaInfoWrapper : finalResultList) {
            List<ExcelMetaInfo> metaInfo = metaInfoWrapper.getMeta();
            for (ExcelMetaInfo info : meta) {
                boolean isSameTemp = false;
                for (ExcelMetaInfo excelMetaInfo : metaInfo) {
                    if(StringUtils.equals(excelMetaInfo.getCode(), info.getCode())) {
                        isSameTemp = true;
                        break;
                    }
                }
                if (!isSameTemp) {
                    finalResultIndex++;
                    continue a;
                }
            }
            return finalResultIndex;
        }
        return -1;
    }

    /**
     * 处理子任务
     *
     * @param tasks 子任务列表
     */
    public GeiTaskResult handle(ResultHandleArgs handleArgs, List<CompletableFuture<?>> tasks) {
        BiFunction<ResultHandleArgs, List<CompletableFuture<?>>, GeiTaskResult> handler = HANDLERS.get(handleArgs.getGeiType());
        return handler.apply(handleArgs, tasks);
    }
}
