package com.cainiao.cntech.dsct.scp.gei.common.utils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.*;
import java.util.function.*;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-24 17:55
 * @description
 */
public class StreamUtils {
    public static <T> boolean anyMatch(T[] arr, Predicate<? super T> predicate) {
        return anyMatch(Arrays.asList(arr), predicate);
    }

    public static <T> boolean anyMatch(List<T> list, Predicate<? super T> predicate) {
        for (T t : list) {
            if (predicate.test(t)) {
                return true;
            }
        }
        return false;
    }

    public static <T> List<T> filter(Collection<T> list, Predicate<? super T> predicate) {
        return list.stream().filter(predicate).collect(Collectors.toList());
    }

    public static <T, R> List<R> mapNotDistinct(List<T> list, Function<? super T, ? extends R> mapper) {
        return list.stream().map(mapper).collect(Collectors.toList());
    }

    public static <T, R> List<R> map(List<T> list, Function<? super T, ? extends R> mapper) {
        return list.stream().map(mapper).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    public static <T, K> Map<K, List<T>> group(Collection<T> list, Function<? super T, ? extends K> group) {
        return list.stream().collect(Collectors.groupingBy(group));
    }

    public static <T, K> Map<K, T> singleGroup(Collection<T> list, Function<T, K> group) {
        Map<K, T> result = new HashMap<>();
        for (T t : list) {
            result.put(group.apply(t), t);
        }
        return result;
    }

    public static <T, U extends Comparable<? super U>> void sort(List<T> list, Function<T, U> sort) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.sort(Comparator.comparing(sort));
    }

    public static <T> Integer sumInt(List<T> list, ToIntFunction<? super T> mapper) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return list.stream().mapToInt(mapper).sum();
    }

    public static <T> Long sumLong(List<T> list, ToLongFunction<? super T> mapper) {
        if (CollectionUtils.isEmpty(list)) {
            return 0L;
        }
        return list.stream().mapToLong(mapper).sum();
    }

    public static <T> Double sumDouble(List<T> list, ToDoubleFunction<? super T> mapper) {
        if (CollectionUtils.isEmpty(list)) {
            return 0D;
        }
        return list.stream().mapToDouble(mapper).sum();
    }

    public static <K, V extends Number> List<K> getSortedKeyList(Map<K, V> map, V maxValue) {
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyList();
        }
        Map<K, V> copyMap = new LinkedHashMap<>(map);
        List<K> keyList = new LinkedList<>();
        copyMap.forEach((k, v) -> {
            Number min = v;
            K minKey = k;
            for (Map.Entry<K, V> entry : copyMap.entrySet()) {
                if (min.doubleValue() > entry.getValue().doubleValue()) {
                    min = entry.getValue();
                    minKey = entry.getKey();
                }
            }
            keyList.add(minKey);
            copyMap.put(minKey, maxValue);
        });
        return keyList;
    }
}
