package com.cainiao.cntech.dsct.scp.gei.support.template;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.cainiao.cntech.dsct.scp.gei.common.enums.ImportMode;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelPageMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelPagingWrapper;
import com.cainiao.cntech.dsct.scp.gei.common.utils.EIportAnnotationUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.GenericsSearchUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.ReflectionUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.StreamUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.dict.GeiDictResolver;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import com.cainiao.cntech.dsct.scp.gei.support.quick.QuickBasicOperation;
import com.cainiao.cntech.dsct.scp.gei.support.quick.QuickQueryWrapper;
import com.cainiao.cntech.dsct.scp.gei.support.quick.QuickService;
import com.cainiao.cntech.dsct.scp.gei.support.quick.annotation.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-16 13:46
 * @description
 */
@Slf4j
public abstract class QuickEIportDataTemplate<I extends ImportTemplateVO, E, ImportDOClass> extends EIPortDataTemplate<I, E> implements QuickBasicOperation<E, ImportDOClass> {
    private final static String DEFAULT_PAGING_FIELD = "paging";
    private final static String DEFAULT_PAGESIZE_FIELD = "pageSize";
    private final static String DEFAULT_CURRENT_PAGE_FIELD = "currentPage";
    private final static String DEFAULT_CURRENT_PAGE_FIELD_2 = "pageNum";

    @Resource
    private GeiDictResolver geiDictResolver;

    /**
     * 子类返回Service实现类
     */
    protected abstract QuickService<ImportDOClass> getService();

    /**
     * 子类返回批量导入的唯一键列表，用于upsert
     */
    protected abstract List<SFunction<ImportDOClass, ?>> getImportPrimaryKeys();

    /**
     * 子类返回列表查询的meta集合
     */
    protected List<ExcelMetaInfo> getListMeta(E request) {
        return Collections.emptyList();
    }

    /**
     * 子类返回导出数据的查询wrapper
     *
     * @param request 实现类设置的接收请求体类型
     * @return QueryWrapper
     */
    protected QueryWrapper<ImportDOClass> getExportQuery(E request) {
        QueryWrapper<ImportDOClass> queryWrapper = new QueryWrapper<>();
        Class<ImportDOClass> modelClass = GenericsSearchUtils.searchClass(this, QuickEIportDataTemplate.class, 2);
        TableInfo tableInfo = TableInfoHelper.getTableInfo(modelClass);
        List<TableFieldInfo> modelFields = tableInfo.getFieldList();
        buildExportQuery(queryWrapper, request, request.getClass(), modelFields);
        return queryWrapper;
    }

    /**
     * 补充导出分页信息
     *
     * @param request 实现类设置的接收请求体类型
     * @return QueryWrapper
     */
    protected void paddingExportDataPageInfo(E request, QueryWrapper<ImportDOClass> queryWrapper) {
        Number currentPage = getCurrentPage(request);
        Number pageSize = getPageSize(request);
        Boolean paging = getPaging(request);
        if (Boolean.TRUE.equals(paging)) {
            resetPageInfo(queryWrapper, currentPage, pageSize);
        }
    }

    @Override
    public ImportResult importData(ImportDataWrapper<I> importDataWrapper) {
        QuickService<ImportDOClass> service = getIService();
        List<SFunction<ImportDOClass, ?>> primaryKeys = getImportPrimaryKeys();
        ImportMode importMode = importDataWrapper.getImportMode();
        if (Objects.isNull(importMode)) {
            importMode = ImportMode.UPSERT;
        }
        List<I> data = importDataWrapper.getData();
        Class<ImportDOClass> modelClass = GenericsSearchUtils.searchClass(this, QuickEIportDataTemplate.class, 2);
        List<ImportDOClass> importData = GeiCommonConvert.convert(data, modelClass);
        if (ImportMode.UPSERT.isThis(importMode)) {
            service.upsert(importData, primaryKeys);
        } else if (ImportMode.COVER.isThis(importMode)) {
            /* todo: Deprecated
            // 可以解析到表名称时，则使用TRUNCATE TABLE
            TableInfo tableInfo = TableInfoHelper.getTableInfo(modelClass);
            if (tableInfo != null) {
                String tableName = tableInfo.getTableName();
                jdbcTemplate.execute("TRUNCATE TABLE " + tableName);
            }else {
                service.remove(new QueryWrapper<>());
            }*/
            service.remove(new QueryWrapper<>());
            service.upsert(importData, primaryKeys);
        }
        return null;
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(E request) {
        return GeiCommonConvert.convert(getListMeta(request), ExcelMetaInfo.class);
    }

    @Override
    public List<?> getExportTableData(E request) {
        QuickService<ImportDOClass> service = getIService();
        QueryWrapper<ImportDOClass> exportQuery = getExportQuery(request);
        if (Objects.isNull(exportQuery)) {
            exportQuery = new QueryWrapper<>();
        }
        paddingExportDataPageInfo(request, exportQuery);
        return service.list(exportQuery);
    }

    @Override
    public Long getExportDataCount(E request) {
        QuickService<ImportDOClass> service = getIService();
        QueryWrapper<ImportDOClass> exportQuery = getExportQuery(request);
        if (Objects.isNull(exportQuery)) {
            exportQuery = new QueryWrapper<>();
        }
        return service.count(exportQuery);
    }

    @Override
    public ExcelPageMetaInfoWrapper list(E request) {
        List<?> list = getExportTableData(request);
        List<ExcelMetaInfo> listMeta = getListMeta(request);
        QuickBasicService service = EIportAnnotationUtils.getQuickBasicService(this);
        listMeta = geiDictResolver.resolveQuickQueryMetaInfo(service.value(), listMeta);
        Number currentPage = getCurrentPage(request);
        if (Objects.isNull(currentPage)) {
            currentPage = 1;
        }
        Number pageSize = getPageSize(request);
        if (Objects.isNull(pageSize)) {
            pageSize = 0;
        }
        return ExcelPageMetaInfoWrapper.of(
                ExcelPagingWrapper.of(currentPage.longValue(), pageSize.longValue(), count(request))
                , ExcelMetaInfoWrapper.of(list, listMeta)
        );
    }

    protected ExcelPageMetaInfoWrapper buildPageInfoWrapper(E request, List<?> list, List<ExcelMetaInfo> listMeta ) {
        Number currentPage = getCurrentPage(request);
        if (Objects.isNull(currentPage)) {
            currentPage = 1;
        }
        Number pageSize = getPageSize(request);
        if (Objects.isNull(pageSize)) {
            pageSize = 0;
        }
        return ExcelPageMetaInfoWrapper.of(
                ExcelPagingWrapper.of(currentPage.longValue(), pageSize.longValue(), count(request))
                , ExcelMetaInfoWrapper.of(list, listMeta)
        );
    }

    @Override
    public Long count(E request) {
        return getExportDataCount(request);
    }

    @Override
    public boolean updateById(List<ImportDOClass> model) {
        return getIService().updateBatchById(model);
    }

    @Override
    public boolean insert(List<ImportDOClass> model) {
        return getIService().saveBatch(model);
    }

    @Override
    public boolean deleteById(List<?> ids) {
        return getIService().removeBatchByIds(ids);
    }


    public QuickService<ImportDOClass> getIService() {
        QuickService<ImportDOClass> service = getService();
        if (Objects.isNull(service)) {
            throw new UnsupportedOperationException("QuickEIportDataTemplate get service is null");
        }
        return service;
    }

    protected Number getCurrentPage(Object request) {
        Field currentPage = ReflectionUtils.getFieldByAnnotation(request.getClass(), QuickCurrentPage.class);
        if (Objects.isNull(currentPage)) {
            currentPage = ReflectionUtils.getFieldByName(request.getClass(), DEFAULT_CURRENT_PAGE_FIELD);
        }
        if (Objects.isNull(currentPage)) {
            currentPage = ReflectionUtils.getFieldByName(request.getClass(), DEFAULT_CURRENT_PAGE_FIELD_2);
        }
        Number value = ReflectionUtils.getFieldValue(currentPage, request, Long.class);
        if (Objects.isNull(value)) {
            value = ReflectionUtils.getFieldValue(currentPage, request, Integer.class);
        }
        return value;
    }

    protected Number getPageSize(Object request) {
        Field pageSize = ReflectionUtils.getFieldByAnnotation(request.getClass(), QuickPageSize.class);
        if (Objects.isNull(pageSize)) {
            pageSize = ReflectionUtils.getFieldByName(request.getClass(), DEFAULT_PAGESIZE_FIELD);
        }
        Number value = ReflectionUtils.getFieldValue(pageSize, request, Long.class);
        if (Objects.isNull(value)) {
            value = ReflectionUtils.getFieldValue(pageSize, request, Integer.class);
        }
        return value;
    }

    protected Boolean getPaging(Object request) {
        Field paging = ReflectionUtils.getFieldByAnnotation(request.getClass(), QuickPaging.class);
        if (Objects.isNull(paging)) {
            paging = ReflectionUtils.getFieldByName(request.getClass(), DEFAULT_PAGING_FIELD);
        }
        return ReflectionUtils.getFieldValue(paging, request, Boolean.class);
    }

    protected void buildExportQuery(QueryWrapper<ImportDOClass> queryWrapper, E request, Class<?> requestClass, List<TableFieldInfo> modelFields) {
        if (Objects.equals(Object.class, requestClass)) {
            return;
        }
        Field[] requestFields = requestClass.getDeclaredFields();
        Map<String, TableFieldInfo> modelFieldsMap = StreamUtils.singleGroup(modelFields, item -> item.getField().getName());
        QuickQueryWrapper quickQueryWrapper = null;
        for (Field requestField : requestFields) {
            QuickQuery quickQuery = requestField.getAnnotation(QuickQuery.class);
            quickQueryWrapper = getQuickQueryWrapper(quickQueryWrapper, quickQuery);
            Object fieldValue = ReflectionUtils.getFieldValue(requestField, request);
            if (Objects.isNull(fieldValue)) {
                continue;
            }
            TableFieldInfo tableFieldInfo = modelFieldsMap.get(quickQueryWrapper.getModelFieldName(requestField));
            if (Objects.isNull(tableFieldInfo)) {
                continue;
            }
            boolean buildArrayResult = buildExportQueryArrayClause(fieldValue, tableFieldInfo, quickQueryWrapper, queryWrapper);
            if (buildArrayResult) {
                continue;
            }
            buildExportQueryGeneralClause(fieldValue, tableFieldInfo, quickQueryWrapper, queryWrapper);
        }
        buildExportQuery(queryWrapper, request, requestClass.getSuperclass(), modelFields);
    }

    private QuickQueryWrapper getQuickQueryWrapper(QuickQueryWrapper quickQueryWrapper, QuickQuery quickQuery) {
        if (Objects.isNull(quickQueryWrapper)) {
            return QuickQueryWrapper.of(quickQuery);
        }
        return quickQueryWrapper.reset(quickQuery);
    }

    private boolean buildExportQueryArrayClause(Object fieldValue, TableFieldInfo tableFieldInfo, QuickQueryWrapper quickQueryWrapper, QueryWrapper<ImportDOClass> queryWrapper) {
        if (Objects.isNull(fieldValue)) {
            return false;
        }
        Object[] array = null;
        if (fieldValue instanceof Collection) {
            Collection<?> collection = (Collection<?>) fieldValue;
            if (CollectionUtils.isNotEmpty((Collection<?>) fieldValue)) {
                array = collection.toArray();
            }
        } else if (fieldValue.getClass().isArray()) {
            array = (Object[]) fieldValue;
        }
        if (Objects.isNull(array)) {
            return false;
        }
        if (quickQueryWrapper.getIsRange() && array.length >= 2) {
            queryWrapper.between(tableFieldInfo.getColumn(), array[0], array[1]);
        } else if (!quickQueryWrapper.getIsRange() && array.length > 0) {
            queryWrapper.in(tableFieldInfo.getColumn(), array);
        }
        return true;
    }
    private void buildExportQueryGeneralClause(Object fieldValue, TableFieldInfo tableFieldInfo, QuickQueryWrapper quickQueryWrapper, QueryWrapper<ImportDOClass> queryWrapper) {
        if (Objects.isNull(fieldValue)) {
            return;
        }
        boolean isBooleanOrNumber = false;
        int booleanOrNumberValue = 0;
        if (fieldValue instanceof Number) {
            isBooleanOrNumber = true;
            booleanOrNumberValue = ((Number) fieldValue).intValue();
        } else if (fieldValue instanceof Boolean) {
            isBooleanOrNumber = true;
            booleanOrNumberValue = (Boolean) fieldValue ? 1 : 0;
        }
        if (quickQueryWrapper.getIsNull() && isBooleanOrNumber && booleanOrNumberValue > 0) {
            queryWrapper.isNull(tableFieldInfo.getColumn());
        } else if (quickQueryWrapper.getIsNotNull() && isBooleanOrNumber && booleanOrNumberValue > 0) {
            queryWrapper.isNotNull(tableFieldInfo.getColumn());
        } else {
            queryWrapper.eq(tableFieldInfo.getColumn(), fieldValue);
        }
    }

    protected void resetPageInfo(QueryWrapper<ImportDOClass> queryWrapper, Number currentPage, Number pageSize) {
        if (Objects.nonNull(pageSize) && Objects.nonNull(currentPage)) {
            queryWrapper.last("limit " + (currentPage.longValue() - 1) * pageSize.longValue() + ", " + pageSize.longValue());
        }
    }
}
