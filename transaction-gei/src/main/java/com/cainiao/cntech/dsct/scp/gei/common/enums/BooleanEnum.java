package com.cainiao.cntech.dsct.scp.gei.common.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-11 10:02
 * @description
 */
@Getter
@AllArgsConstructor
public enum BooleanEnum {

    TRUE("1", 1, "Y", "是"),
    FALSE("0", 0, "N","否"),
    ;

    private final String code;
    private final Integer iCode;
    private final String enCode;
    private final String name;

    private static final Map<String, BooleanEnum> CACHE = Maps.newHashMapWithExpectedSize(2);
    static {
        for (BooleanEnum booleanEnum : values()) {
            CACHE.put(booleanEnum.getCode(), booleanEnum);
        }
    }

    public boolean isThis(String code){
        return this.getCode().equals(code);
    }
    public boolean isThis(Integer code){
        return this.getICode().equals(code);
    }

    public static BooleanEnum getByBool(Boolean bool){
        return Boolean.TRUE.equals(bool) ? TRUE : FALSE;
    }
    public static BooleanEnum getByCode(Integer code) {
        return getByCode(Objects.isNull(code) ? "" : code.toString());
    }
    public static BooleanEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return CACHE.get(code);
    }
    public static BooleanEnum getByEnCode(String enCode) {
        if (StringUtils.isBlank(enCode)) {
            return null;
        }
        for (BooleanEnum booleanEnum : values()) {
            if (StringUtils.equalsIgnoreCase(booleanEnum.getEnCode(), enCode)) {
                return booleanEnum;
            }
        }
        return null;
    }

    public static boolean isTrue(Integer num){
        return TRUE.getICode().equals(num);
    }
    public static boolean isTrue(String code){
        return TRUE.getCode().equals(code);
    }
    public static boolean isTrue(Boolean bool){
        return Boolean.TRUE.equals(bool);
    }
    public static boolean isFalse(Integer num){
        return !isTrue(num);
    }
    public static boolean isFalse(Boolean bool){
        return !isTrue(bool);
    }
    public static boolean isFalse(String code){
        return !isTrue(code);
    }
}
