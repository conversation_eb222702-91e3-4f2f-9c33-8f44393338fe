package com.cainiao.cntech.dsct.scp.gei.core.dict;

import com.alibaba.fastjson2.TypeReference;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiDictDO;
import com.cainiao.cntech.dsct.scp.gei.biz.service.manager.ScpGeiDictManager;
import com.cainiao.cntech.dsct.scp.gei.biz.web.model.OptionVO;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.RegexUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.StreamUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.dict.formatter.DictFormatter;
import com.cainiao.cntech.dsct.scp.gei.core.dict.model.ExportDictDTO;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportQueryRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ConfigurableApplicationContext;

import javax.annotation.Resource;
import java.util.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 16:42
 * @description 模版解析器
 */
public class GeiDictResolver implements ApplicationRunner {
    private static final String DIMENSION_NAME = "dimension";
    private static final String FIELDS_NAME = "fields";
    private static final String REGEX = "\\$(.*?)\\$";
    private final List<DictFormatter> dictFormatters = new LinkedList<>();
    @Resource
    private ScpGeiDictManager scpGeiDictManager;
    @Resource
    private ConfigurableApplicationContext context;

    /**
     * 解析Quick列表查询meta字典
     **/
    public List<ExcelMetaInfo> resolveQuickQueryMetaInfo(String dictCode, List<ExcelMetaInfo> metaInfoList) {
        ExportDictDTO geiDict = resolveQuickQueryMetaInfo(dictCode);
        if (Objects.isNull(geiDict)) {
            return metaInfoList;
        }
        List<ExcelMetaInfo> dictEntities = geiDict.getDictEntities();
        Integer mode = geiDict.getMode();
        if (DictMode.COVER.isThis(mode)) {
            return dictEntities;
        } else {
            replaceMetaInfo(dictEntities, metaInfoList);
            return metaInfoList;
        }
    }

    public ExportDictDTO resolveQuickQueryMetaInfo(String dictCode) {
        ScpGeiDictDO geiTemplateCfg = scpGeiDictManager.queryByCode(dictCode, GeiTypeEnum.QUICK_LIST);
        if (Objects.isNull(geiTemplateCfg) || !GeiTypeEnum.QUICK_LIST.isThis(geiTemplateCfg.getType())) {
            return null;
        }
        ExportDictDTO dto = GeiCommonConvert.convert(geiTemplateCfg, ExportDictDTO.class);
        assert dto != null;
        String dictValue = geiTemplateCfg.getValue();
        List<ExcelMetaInfo> list = JsonUtils.toObject(dictValue, new TypeReference<List<ExcelMetaInfo>>() {
        });
        deepResolveExportDict(list);
        dto.setDictEntities(list);
        return dto;
    }

    /**
     * 解析导出字典
     **/
    public ExportDictDTO resolveExportDict(String dictCode) {
        ScpGeiDictDO geiTemplateCfg = scpGeiDictManager.queryByCode(dictCode, GeiTypeEnum.EXPORT);
        if (Objects.isNull(geiTemplateCfg) || !GeiTypeEnum.EXPORT.isThis(geiTemplateCfg.getType())) {
            return null;
        }
        ExportDictDTO dto = GeiCommonConvert.convert(geiTemplateCfg, ExportDictDTO.class);
        assert dto != null;
        String dictValue = geiTemplateCfg.getValue();
        List<ExcelMetaInfo> list = JsonUtils.toObject(dictValue, new TypeReference<List<ExcelMetaInfo>>() {
        });
        deepResolveExportDict(list);
        dto.setDictEntities(list);
        return dto;
    }

    private void deepResolveExportDict(Collection<ExcelMetaInfo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ExcelMetaInfo metaInfo : list) {
            String result = RegexUtils.findAndReplace(REGEX, metaInfo.getCode(), expr -> {
                for (DictFormatter dictFormatter : dictFormatters) {
                    if (dictFormatter.support(expr)) {
                        return dictFormatter.format(expr);
                    }
                }
                return "";
            });
            metaInfo.setCode(result);
            result = RegexUtils.findAndReplace(REGEX, metaInfo.getName(), expr -> {
                for (DictFormatter dictFormatter : dictFormatters) {
                    if (dictFormatter.support(expr)) {
                        return dictFormatter.format(expr);
                    }
                }
                return "";
            });
            metaInfo.setName(result);
            deepResolveExportDict(metaInfo.getChildren());
        }
    }

    public List<ExcelMetaInfo> resolveExportDict(String dictCode, List<ExcelMetaInfo> metaInfoList) {
        ExportDictDTO geiDict = resolveExportDict(dictCode);
        if (Objects.isNull(geiDict)) {
            return metaInfoList;
        }
        List<ExcelMetaInfo> dictEntities = geiDict.getDictEntities();
        Integer mode = geiDict.getMode();
        if (DictMode.COVER.isThis(mode)) {
            return GeiCommonConvert.convert(dictEntities, ExcelMetaInfo.class);
        } else {
            replaceExcelMetaInfo(dictEntities, metaInfoList);
            return metaInfoList;
        }
    }

    private void replaceExcelMetaInfo(Collection<ExcelMetaInfo> dictEntities, Collection<ExcelMetaInfo> metaInfoList) {
        if (CollectionUtils.isEmpty(dictEntities) || CollectionUtils.isEmpty(metaInfoList)) {
            return;
        }
        Map<String, ExcelMetaInfo> entityMap = StreamUtils.singleGroup(dictEntities, ExcelMetaInfo::getCode);
        for (ExcelMetaInfo excelMetaInfo : metaInfoList) {
            ExcelMetaInfo entity = entityMap.get(excelMetaInfo.getCode());
            if (Objects.nonNull(entity)) {
                excelMetaInfo.setName(entity.getName());
            }
            replaceExcelMetaInfo(entity.getChildren(), excelMetaInfo.getChildren());
        }
    }

    private void replaceMetaInfo(Collection<ExcelMetaInfo> dictEntities, Collection<ExcelMetaInfo> metaInfoList) {
        if (CollectionUtils.isEmpty(dictEntities) || CollectionUtils.isEmpty(metaInfoList)) {
            return;
        }
        Map<String, ExcelMetaInfo> entityMap = StreamUtils.singleGroup(dictEntities, ExcelMetaInfo::getCode);
        for (ExcelMetaInfo excelMetaInfo : metaInfoList) {
            ExcelMetaInfo entity = entityMap.get(excelMetaInfo.getCode());
            if (Objects.nonNull(entity)) {
                excelMetaInfo.setName(entity.getName());
            }
            replaceMetaInfo(entity.getChildren(), excelMetaInfo.getChildren());
        }
    }

    /** 解析导入字典 */

    /**
     * 解析导入模版的维度返回set
     *
     * @param dictCode dict模版编码
     * @return 维度列表
     */
    public Set<String> resolveImportDimSet(String dictCode) {
        if (StringUtils.isBlank(dictCode)) {
            return null;
        }
        List<OptionVO> list = resolveImportDict(dictCode);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Set<String> dimSet = new HashSet<>();
        for (OptionVO option : list) {
            if (StringUtils.equals(DIMENSION_NAME, option.getValue() + "")) {
                for (OptionVO child : option.getChildren()) {
                    dimSet.add(child.getValue() + "");
                }
                break;
            }
        }
        return dimSet;
    }

    /**
     * 解析导入模版的维度返回set
     *
     * @param dictCode dict模版编码
     * @return 维度列表
     */
    public Set<String> resolveImportFieldsSet(String dictCode) {
        if (StringUtils.isBlank(dictCode)) {
            return null;
        }
        List<OptionVO> list = resolveImportDict(dictCode);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Set<String> fieldsSet = new HashSet<>();
        for (OptionVO option : list) {
            if (StringUtils.equals(FIELDS_NAME, option.getValue() + "")) {
                for (OptionVO child : option.getChildren()) {
                    fieldsSet.add(child.getValue() + "");
                }
                break;
            }
        }
        return fieldsSet;
    }

    /**
     * 解析导入模版到Option列表
     *
     * @param dictCode dict模版编码
     * @return
     */
    public List<OptionVO> resolveImportDict(String dictCode) {
        ScpGeiDictDO geiTemplateCfg = scpGeiDictManager.queryByCode(dictCode, GeiTypeEnum.IMPORT);
        if (Objects.isNull(geiTemplateCfg) || !GeiTypeEnum.IMPORT.isThis(geiTemplateCfg.getType())) {
            return null;
        }
        String dictValue = geiTemplateCfg.getValue();
        List<OptionVO> list = JsonUtils.toObject(dictValue, new TypeReference<List<OptionVO>>() {
        });
        deepResolveImportDict(list);
        return list;
    }

    private void deepResolveImportDict(List<OptionVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (OptionVO optionVO : list) {
            String label = optionVO.getLabel();
            String result = RegexUtils.findAndReplace(REGEX, label, expr -> {
                for (DictFormatter dictFormatter : dictFormatters) {
                    if (dictFormatter.support(expr)) {
                        return dictFormatter.format(expr);
                    }
                }
                return "";
            });
            optionVO.setLabel(result);
            deepResolveImportDict(optionVO.getChildren());
        }
    }

    /**
     * 生成导入模版的文件名
     *
     * @param request  导入模版请求体
     * @param dictCode 导入模版的dict编码
     * @param suffix   后缀
     * @return
     */
    public String generateImportTemplateFileName(ImportQueryRequest request, String dictCode, String suffix) {
        try {
            if (StringUtils.isBlank(dictCode) || Objects.isNull(request)) {
                return "";
            }
            List<OptionVO> list = resolveImportDict(dictCode);
            Map<String, OptionVO> configMap = StreamUtils.singleGroup(list, vo -> vo.getValue() + "");
            StringBuilder filenameBuilder = new StringBuilder();
            for (OptionVO child : configMap.get(DIMENSION_NAME).getChildren()) {
                List<String> dimensionList = request.getDimensionList();
                if (dimensionList.contains(child.getValue())) {
                    filenameBuilder.append(child.getLabel()).append("&");
                }
            }
            filenameBuilder.delete(filenameBuilder.length() - 1, filenameBuilder.length());
            filenameBuilder.append("_");
            for (OptionVO child : configMap.get(FIELDS_NAME).getChildren()) {
                List<String> fieldsList = request.getFieldsList();
                if (fieldsList.contains(child.getValue())) {
                    filenameBuilder.append(child.getLabel()).append("&");
                }
            }
            if (StringUtils.isNotBlank(suffix)) {
                filenameBuilder.delete(filenameBuilder.length() - 1, filenameBuilder.length());
                filenameBuilder.append("_").append(suffix);
            }
            return filenameBuilder.toString();
        } catch (Exception ignored) {
        }
        return "";
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        Map<String, DictFormatter> beans = context.getBeansOfType(DictFormatter.class);
        dictFormatters.addAll(beans.values());
    }
}
