package com.cainiao.cntech.dsct.scp.gei.ext.validator.common;

import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.ext.validator.ImportValidator;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelValid;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-13 16:22
 * @description
 */
public class NotBlankImportValidator<T> implements ImportValidator<T> {
    @Override
    public boolean require(T o, Field field, String fieldName) {
        return field.isAnnotationPresent(ExcelValid.class) && field.getAnnotation(ExcelValid.class).notBlank();
    }

    @Override
    public String validate(ImportDataWrapper<T> importDataWrapper, T o, Field field, Object fieldValue) {
        if (fieldValue == null || StringUtils.isBlank(fieldValue.toString())) {
            return "%s无效字符串！";
        } else {
            return null;
        }
    }
}
