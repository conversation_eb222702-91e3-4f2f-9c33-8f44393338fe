package com.cainiao.cntech.dsct.scp.gei.ext.converter;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-13 13:16
 * @description 写出类型转换接口，
 * 如果需要自定义类型的值进行转换别的类型，可以自定义子类注册到容器中
 * 泛型T表示要转换成哪一个类型
 * 泛型表示原始字段类型
 */
public interface ExcelWriteConverter<T, U> {
    /**
     * 传递字段值，判断是否支持转换
     *
     * @param fieldValue 字段值
     * @return true支持，false不支持
     */
    boolean support(Object fieldValue);

    /**
     * 转换方法
     *
     * @param fieldValue 字段值
     * @return 将字段值转换为需要转换的类型并返回
     */
    T convert(U fieldValue);
}
