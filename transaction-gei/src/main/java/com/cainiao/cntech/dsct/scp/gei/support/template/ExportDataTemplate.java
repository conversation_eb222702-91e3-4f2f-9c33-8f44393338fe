package com.cainiao.cntech.dsct.scp.gei.support.template;

import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;

import java.util.Collections;
import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 11:12
 * @description 数据导出模版
 * 这里泛型<E>代指导出数据的请求参数体，通常和业务层的查询参数体一致
 */
public interface ExportDataTemplate<E> {
    /**
     * 获取导出数据的表格头
     * *** 如果需要metaInfo和tableData一起返回，则重写getExportWrapper即可 ***
     *
     * @param request 查询参数
     * @return 导出的表格meta
     */
    default List<ExcelMetaInfo> getExportMetaInfo(E request) {
        return Collections.emptyList();
    }

    /**
     * 构建excel导出表格数据
     * *** 如果需要metaInfo和tableData一起返回，则重写getExportWrapper即可 ***
     *
     * @param request 查询参数
     * @return 导出的表格数据
     */
    default List<?> getExportTableData(E request) {
        return Collections.emptyList();
    }

    /**
     * 获取导出数据包装，如果子类重写了这个方法，将不会调用getExportTableData 和 getExportMetaInfo
     *
     * @param request 查询参数
     * @return 导出的表格数据 + meta 包装
     */
    default ExcelMetaInfoWrapper getExportWrapper(E request) {
        return null;
    }

    /**
     * 获取导出数据的总数
     * 组件内部通过该方法返回的数据总数，对导出数据做大任务拆分，建议重写
     * 如没有重写，则不会进行大任务拆分，仅创建一个任务做导出数据处理
     *
     * @param request 查询参数
     */
    default Long getExportDataCount(E request) {
        return null;
    }

    /**
     * 获取导出数据的文件名，如果实现该方法且返回有效字符串，则会替换导出注解标记的名称
     * *** 如无需特殊处理文件名，则无需重写该方法 ***
     *
     * @param request 请求体
     * @return 文件名
     * @see ExportService 文件名通过该注解filename字段标记
     */
    default String getExportFileName(E request) {
        return null;
    }
}
