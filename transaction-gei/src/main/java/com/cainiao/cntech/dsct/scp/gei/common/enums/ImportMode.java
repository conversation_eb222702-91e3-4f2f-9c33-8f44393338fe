package com.cainiao.cntech.dsct.scp.gei.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-16 13:44
 * @description
 */
public enum ImportMode {
    COVER,
    UPSERT;

    public static ImportMode getImportMode(String mode){
        return ImportMode.COVER.isThis(mode) ? COVER : UPSERT;
    }

    public boolean isThis(ImportMode mode){
        return this.equals(mode);
    }
    public boolean isThis(String mode){
        return StringUtils.equalsIgnoreCase(this.name(), mode);
    }
}
