package com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext;


import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.model.ConvertExtensibleWrapper;

import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-25 11:53
 * @description
 */
public class StringNullToDefaultConvertExt implements ConvertProcessExtensible<String> {
    @Override
    public String doConvert(ConvertExtensibleWrapper ew) throws IllegalAccessException {
        Object o = ew.getSourceField().get(ew.getSourceObject());
        if(Objects.isNull(o) && String.class.equals(ew.getSourceField().getType())){
            return "-";
        }
        return null;
    }
}
