package com.cainiao.cntech.dsct.scp.gei.common.error;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-14 20:52
 * @description
 */
@Getter
@AllArgsConstructor
public enum GeiError {
    TASK_TYPE_EMPTY("任务类型不允许为空！"),
    TASK_TYPE_ILLEGAL("非法的任务类型！"),
    PARENT_TASK_ID_EMPTY("主任务id不允许为空！"),
    TASK_ID_EMPTY("任务id不允许为空！"),
    EXECUTE_TEMPLATE_CODE_EMPTY("任务执行模版编码不允许为空！"),
    PARENT_TASK_ID_NOT_FOUND("主任务id不存在！"),
    CHILD_TASKS_NOT_FOUND("子任务不存在！parentId is {}"),
    ;

    private final String error;
}
