package com.cainiao.cntech.dsct.scp.gei.biz.web.api;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-19 17:29
 * @description
 */
public interface GeiWebApi {
    String ROOT = "/api/gei";

    String UPLOAD = "uploadImport/{dataCode}";
    String EXPORT_TEMPLATE = "/exportTemplate/{dataCode}";
    String EXPORT_DATA = "/export/{dataCode}";
    String CHECK_ASYNC = "/checkAsync/{dataCode}";
    String QUERY_DICT = "/queryDictValueByCode.json";

    interface View {
        String LIST = "task/list";
    }
    interface Basic {
        String SEARCH = "basic/search/{dataCode}/{fieldCode}";

        String LIST = "basic/list/{dataCode}";
        String UPDATE_BY_ID = "basic/updateById/{dataCode}";
        String INSERT = "basic/insert/{dataCode}";
        String DELETE_BY_ID = "basic/deleteById/{dataCode}";
    }

}
