package com.cainiao.cntech.dsct.scp.gei.biz.service.service.impl;

import com.cainiao.cntech.dsct.scp.gei.biz.dao.dataobject.ScpGeiChildTaskDO;
import com.cainiao.cntech.dsct.scp.gei.biz.service.dto.ScpGeiChildTaskDTO;
import com.cainiao.cntech.dsct.scp.gei.biz.service.manager.ScpGeiChildTaskManager;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.query.GeiChildTaskPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.update.GeiChildTaskUpdateRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.service.ScpGeiChildTaskService;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;

import javax.annotation.Resource;
import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 17:10
 * @description
 */
public class ScpGeiChildTaskServiceImpl implements ScpGeiChildTaskService {
    @Resource
    private ScpGeiChildTaskManager scpGeiChildTaskManager;
    @Override
    public List<ScpGeiChildTaskDTO> queryPageData(GeiChildTaskPageQueryRequest request) {
        return scpGeiChildTaskManager.queryPageData(request);
    }

    @Override
    public List<ScpGeiChildTaskDTO> queryByParendId(Long parentId) {
        GeiChildTaskPageQueryRequest request = new GeiChildTaskPageQueryRequest();
        request.setParent(parentId);
        request.setPaging(false);
        return scpGeiChildTaskManager.queryPageData(request);
    }

    @Override
    public Long queryCount(GeiChildTaskPageQueryRequest request) {
        return scpGeiChildTaskManager.queryCount(request);
    }

    @Override
    public Long updateByTaskId(GeiChildTaskUpdateRequest request) {
        return scpGeiChildTaskManager.updateByTaskId(GeiCommonConvert.convert(request, ScpGeiChildTaskDO.class));
    }

    @Override
    public Long updateNotIsoTaskStatusFailedByTaskId(Long parentId) {
        return scpGeiChildTaskManager.updateNotIsoTaskStatusFailedByTaskId(parentId);
    }
}
