package com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext;

import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.model.ConvertExtensibleWrapper;


/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-10-08 19:27
 * @description
 */
public class LongToStringConvertExt implements ConvertProcessExtensible<String> {
    @Override
    public String doConvert(ConvertExtensibleWrapper ew) throws IllegalAccessException {
        Object o = ew.getSourceField().get(ew.getSourceObject());
        if(o instanceof Long){
            Long date = (Long) o;
            return String.valueOf(date);
        }
        return null;
    }
}
