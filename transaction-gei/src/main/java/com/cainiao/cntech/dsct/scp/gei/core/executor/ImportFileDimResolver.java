package com.cainiao.cntech.dsct.scp.gei.core.executor;

import com.cainiao.cntech.dsct.scp.gei.common.utils.RegexUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-10 10:51
 * @description 导入文件维度解析器
 */
public class ImportFileDimResolver {
    public static final String REGEX = "\\$\\{([^}]+)\\}";
    public static final String FILENAME_MARK = "^${%s}${%s}^";

    /**
     * 通过文件名获取维度编码
     *
     * @param filename
     * @return
     */
    public static List<String> resolverByFilename(String filename) {
        if (StringUtils.isNotBlank(filename)) {
            String[] split = filename.split("\\^");
            // length must 3
            if (split.length == 3) {
                return RegexUtils.find(REGEX, split[1]);
            }
        }
        return null;
    }

    public static boolean dimAndFieldsValid(String dimCodes, Set<String> dimSet) {
        if (dimCodes == null) {
            return false;
        }
        if (CollectionUtils.isEmpty(dimSet)) {
            return true;
        }
        List<String> fileDims = Arrays.stream(dimCodes.split(",")).collect(Collectors.toList());
        for (String fileDim : fileDims) {
            if (dimSet.contains(fileDim)) {
                return true;
            }
        }
        return false;
    }
}
