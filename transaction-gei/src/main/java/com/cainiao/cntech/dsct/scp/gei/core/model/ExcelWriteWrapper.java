package com.cainiao.cntech.dsct.scp.gei.core.model;

import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.utils.RegexUtils;
import com.cainiao.cntech.dsct.scp.gei.core.executor.ImportFileDimResolver;
import com.cainiao.cntech.dsct.scp.gei.ext.converter.ExcelWriteConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-12 14:17
 * @description excel写出包装类
 */
@Data
@ApiModel
public class ExcelWriteWrapper {
    public static final String REGEX = "\\$\\{([^}]+)\\}";

    public static void main(String[] args) {
        String file = "测试导出^${lv1,aaa}${test,aaa}^202405121417.xlsx";
        System.out.println(RegexUtils.find(REGEX, file));
    }

    /**
     * 指定优先使用写出转换器
     */
    private List<String> assignUseWriteConverterNames;
    private List<ExcelWriteConverter> assignUseExcelWriteConverters;
    /**
     * sheet页名称
     */
    @ApiModelProperty("sheet页名称")
    private String sheetName = "数据";
    /**
     * 表头行数
     */
    @ApiModelProperty("表头行数")
    private Integer headRowNumber;
    /**
     * 表名称
     */
    @ApiModelProperty("表名称")
    private String fileName;
    /**
     * 标题 [将第一列作为Title]
     */
    @ApiModelProperty("标题 [将第一列作为Title]")
    private String title;
    /**
     * 导出数据
     */
    @ApiModelProperty("导出数据")
    private List<?> exportDataList;
    /**
     * 导出数据Meta信息
     */
    @ApiModelProperty("导出数据Meta信息")
    private List<ExcelMetaInfo> metaInfoList;

    public static ExcelWriteWrapper of(String fileName, String title, List<?> data) {
        ExcelWriteWrapper result = new ExcelWriteWrapper();
        result.setFileName(fileName);
        result.setExportDataList(data);
        result.setTitle(title);
        return result;
    }

    public static ExcelWriteWrapper of(String fileName, String title, List<?> data, List<ExcelMetaInfo> metaInfoList) {
        ExcelWriteWrapper result = new ExcelWriteWrapper();
        result.setFileName(fileName);
        result.setExportDataList(data);
        result.setTitle(title);
        result.setMetaInfoList(metaInfoList);
        return result;
    }

    public void formatFileName() {
        // 获取当前的日期和时间
        LocalDateTime now = LocalDateTime.now();
        // 创建格式化对象，指定格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 格式化当前日期和时间
        String formattedNow = now.format(formatter);
        setFileName(getFileName() + formattedNow);
    }

    public void formatFileName(String dimension, String fields) {
        // 获取当前的日期和时间
        LocalDateTime now = LocalDateTime.now();
        // 创建格式化对象，指定格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 格式化当前日期和时间
        String formattedNow = now.format(formatter);
        String mark = String.format(ImportFileDimResolver.FILENAME_MARK,
                StringUtils.isNotBlank(dimension) ? dimension : "-",
                StringUtils.isNotBlank(fields) ? fields : "-");
        setFileName(String.format("%s%s%s",
                getFileName(), mark, formattedNow)
        );
    }

}
