package com.cainiao.cntech.dsct.scp.gei.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:23
 * @description gei任务类型
 */
@Getter
@AllArgsConstructor
public enum GeiTypeEnum {
    EXPORT("EXPORT", "导出任务"),
    IMPORT("IMPORT", "导入任务"),
    QUICK_LIST("QUICK_LIST", "快速集成列表查询"),
    ;
    private final String code;
    private final String name;
    public boolean isThis(String code){
        return StringUtils.equalsIgnoreCase(getCode(), code);
    }
    public static GeiTypeEnum getByCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (GeiTypeEnum value : GeiTypeEnum.values()) {
            if (StringUtils.equalsIgnoreCase(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
