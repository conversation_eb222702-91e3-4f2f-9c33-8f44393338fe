package com.cainiao.cntech.dsct.scp.gei.support.quick;

import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelPageMetaInfoWrapper;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-21 09:59
 * @description 快速集成基本操作 crud
 */
public interface QuickBasicOperation<QueryRequest, DataObject> {
    ExcelPageMetaInfoWrapper list(QueryRequest request);

    Long count(QueryRequest request);

    boolean updateById(List<DataObject> model);

    boolean insert(List<DataObject> model);

    boolean deleteById(List<?> ids);
}
