package com.cainiao.cntech.dsct.scp.gei.core.model;

import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-21 15:30
 * @description
 */
@Data
@AllArgsConstructor
public class ImportResult {
    /**
     *
     */
    private List<ExcelMetaInfo> meta;

    /**
     * 数据列表（展示）
     */
    private List<?> list;


    private Object data;

    public static ImportResult of(List<ExcelMetaInfo> meta, List<?> list, Object data) {
        return new ImportResult(meta, list, data);
    }
}
