package com.cainiao.cntech.dsct.scp.gei.ext.validator.common;


import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.ext.validator.ImportValidator;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelValid;

import java.lang.reflect.Field;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-16 15:42
 * @description
 */
public class CharSizeImportValidator<T> implements ImportValidator<T> {
    @Override
    public boolean require(T t, Field field, String fieldName) {
        return field.isAnnotationPresent(ExcelValid.class) && field.getAnnotation(ExcelValid.class).charSizeLimit() >= 0;
    }

    @Override
    public String validate(ImportDataWrapper<T> importDataWrapper, T t, Field field, Object fieldValue) {
        ExcelValid excelValid = field.getAnnotation(ExcelValid.class);
        if (fieldValue instanceof String) {
            String rawValue = String.valueOf(fieldValue);
            if (rawValue.length() > excelValid.charSizeLimit()) {
                return "%s字段长度限制为「" + excelValid.charSizeLimit() + "」";
            }
        }
        return null;
    }
}
