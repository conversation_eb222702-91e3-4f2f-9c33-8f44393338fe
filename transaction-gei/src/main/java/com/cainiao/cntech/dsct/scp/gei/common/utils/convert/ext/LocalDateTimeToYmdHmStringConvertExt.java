package com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext;

import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.model.ConvertExtensibleWrapper;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-23 10:51
 * @description LocalDateTime to String
 */
public class LocalDateTimeToYmdHmStringConvertExt implements ConvertProcessExtensible<String> {
    @Override
    public String doConvert(ConvertExtensibleWrapper ew) throws IllegalAccessException {
        Object o = ew.getSourceField().get(ew.getSourceObject());
        if (o instanceof LocalDateTime) {
            LocalDateTime date = (LocalDateTime) o;
            return DateUtil.localDateTimeToString(date, DateUtil.MINUTE_DATE_FORMAT);
        }
        return null;
    }
}
