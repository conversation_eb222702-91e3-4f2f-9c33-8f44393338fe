package com.cainiao.cntech.dsct.scp.gei.core.executor;

import com.cainiao.cntech.dsct.scp.gei.biz.service.request.insert.GeiChildTaskInsertRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.insert.GeiTaskInsertRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.service.ScpGeiTaskService;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.utils.GenericsSearchUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.ReflectionUtils;
import com.cainiao.cntech.dsct.scp.gei.core.SliceDataManager;
import com.cainiao.cntech.dsct.scp.gei.core.dict.GeiDictResolver;
import com.cainiao.cntech.dsct.scp.gei.core.event.GeiEventPublisher;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelWriteWrapper;
import com.cainiao.cntech.dsct.scp.gei.ext.converter.ExcelWriteConverter;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.SliceDataPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.ExportDataTemplate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 15:50
 * @description 导出模版执行器
 */
public class ExportTemplateExecutor {
    public static final String UNDEFINED = "undefined";
    public static final String ACTION = "action";
    @Resource
    private ScpGeiTaskService scpGeiTaskService;
    @Resource
    private GeiEventPublisher geiEventPublisher;
    @Resource
    private SliceDataManager sliceDataManager;
    @Resource
    private GeiDictResolver geiDictResolver;

    public ExcelWriteWrapper sync(ExportDataTemplate<Object> exportDataTemplate, ExportService exportService, Map<String, Object> queryMap) {
        return execute(exportDataTemplate, exportService, queryMap, false);
    }

    public void async(ExportDataTemplate<Object> exportDataTemplate, ExportService exportService, Map<String, Object> queryMap) {
        execute(exportDataTemplate, exportService, queryMap, true);
    }

    private ExcelWriteWrapper execute(ExportDataTemplate<Object> exportDataTemplate, ExportService exportService, Map<String, Object> queryMap, boolean async) {
        if (Objects.isNull(exportDataTemplate)) {
            return null;
        }
        Object request = resolveExportRequest(exportDataTemplate, queryMap);
        for (Field declaredField : request.getClass().getDeclaredFields()) {
            if (UNDEFINED.equals(ReflectionUtils.getFieldValue(declaredField, request))) {
                ReflectionUtils.setFieldValue(request, declaredField, null);
            }
        }
        if (async) {
            GeiTaskInsertRequest parentTask = createTaskInsertRequest(exportDataTemplate, exportService, request);
            List<GeiChildTaskInsertRequest> childTasks = createChildTaskInsertRequests(parentTask, exportService, request);
            Long taskId = scpGeiTaskService.createTask(parentTask, childTasks);
            geiEventPublisher.sendAsyncExportEvent(taskId);
            return null;
        }
        invokeBeforeSyncExportDataProcessors(request);
        ExcelWriteWrapper writeWrapper = invokeTemplate(exportDataTemplate, exportService, request);
        List<ExcelMetaInfo> metaInfoList = writeWrapper.getMetaInfoList();
        metaInfoList = geiDictResolver.resolveExportDict(exportService.dict(), metaInfoList);
        writeWrapper.setMetaInfoList(metaInfoList);
        return writeWrapper;
    }

    /**
     * 调用模版方法
     *
     * @param exportDataTemplate
     * @param exportService
     * @param request
     * @return
     */
    public ExcelWriteWrapper invokeTemplate(ExportDataTemplate<Object> exportDataTemplate, ExportService exportService, Object request) {
        ExcelMetaInfoWrapper exportWrapper = exportDataTemplate.getExportWrapper(request);
        List<?> exportData;
        List<ExcelMetaInfo> metaInfoList;
        List<String> assignUseWriteConverterNames = Collections.emptyList();
        List<ExcelWriteConverter> assignUseExcelWriteConverters = Collections.emptyList();
        if (exportWrapper != null) {
            exportData = exportWrapper.getList();
            metaInfoList = exportWrapper.getMeta();
            assignUseExcelWriteConverters = exportWrapper.getAssignUseExcelWriteConverters();
            assignUseWriteConverterNames = exportWrapper.getAssignUseWriteConverterNames();
        } else {
            exportData = exportDataTemplate.getExportTableData(request);
            metaInfoList = exportDataTemplate.getExportMetaInfo(request);
        }
        if (metaInfoList == null) {
            metaInfoList = Collections.emptyList();
        }
        if (exportData == null) {
            exportData = Collections.emptyList();
        }
        if (CollectionUtils.isNotEmpty(metaInfoList)) {
            metaInfoList = metaInfoList.stream().filter(meta -> !Boolean.TRUE.equals(meta.getHidden())).collect(Collectors.toList());
        }
        ExcelWriteWrapper excelWriteWrapper = buildExcelWriteWrapper(exportDataTemplate, request, exportService, exportData);
        metaInfoList = metaInfoList.stream().filter(item -> !ACTION.equals(item.getCode())).collect(Collectors.toList());
        excelWriteWrapper.setMetaInfoList(metaInfoList);
        excelWriteWrapper.setAssignUseExcelWriteConverters(assignUseExcelWriteConverters);
        excelWriteWrapper.setAssignUseWriteConverterNames(assignUseWriteConverterNames);

        return excelWriteWrapper;
    }

    private GeiTaskInsertRequest createTaskInsertRequest(ExportDataTemplate<Object> exportDataTemplate, ExportService exportService, Object params) {
        LocalDateTime now = LocalDateTime.now();
        GeiTaskInsertRequest request = new GeiTaskInsertRequest();
        request.setTaskName(exportService.filename());
        request.setRequestParams(JsonUtils.toStr(params));
        request.setTaskType(GeiTypeEnum.EXPORT.getCode());
        request.setExecuteTemplateCode(exportService.value());
        request.setTemplateDictCode(exportService.dict());
        request.setGmtClearExpired(now.plusSeconds(exportService.expire()));
        request.setGmtExpired(now.plusSeconds(exportService.processExpire()));
        Long count = exportDataTemplate.getExportDataCount(params);
        request.setDataTotalCnt(count);
        if (Objects.isNull(count) || count < 1 || !invokeSupportSliceData(params)) {
            request.setChildTaskTotalCnt(1);
        } else {
            int taskCount = (int) Math.ceil((double) count / exportService.sliceSize());
            request.setChildTaskTotalCnt(taskCount);
        }
        return request;
    }

    private List<GeiChildTaskInsertRequest> createChildTaskInsertRequests(GeiTaskInsertRequest parentTask, ExportService exportService, Object params) {
        List<GeiChildTaskInsertRequest> requests = new ArrayList<>(parentTask.getChildTaskTotalCnt());
        long count = Objects.nonNull(parentTask.getDataTotalCnt()) ? parentTask.getDataTotalCnt() : 0L;
        Integer childTaskTotalCnt = parentTask.getChildTaskTotalCnt();
        for (int i = 0; i < childTaskTotalCnt; i++) {
            GeiChildTaskInsertRequest request = new GeiChildTaskInsertRequest();
            request.setExecuteTemplateCode(exportService.value());
            request.setTemplateDictCode(exportService.dict());
            request.setPage(i + 1L);
            if (!invokeSupportSliceData(params)) {
                request.setPaging(Boolean.FALSE);
                request.setPageSize((long) exportService.sliceSize());
            } else {
                request.setPage(i + 1L);
                request.setPaging(Boolean.TRUE);
                long currentPageSize = Math.min(exportService.sliceSize(), count);
                request.setPageSize(currentPageSize);
            }
            requests.add(request);
        }
        return requests;
    }

    /**
     * 解析导出数据请求参数体
     *
     * @param queryMap
     * @return
     */
    private Object resolveExportRequest(ExportDataTemplate<?> exportDataTemplate, Map<String, Object> queryMap) {
        Class<?> queryRequestClass = GenericsSearchUtils.searchClass(exportDataTemplate, ExportDataTemplate.class, 0);
        if (queryMap == null) {
            return ReflectionUtils.createInstance(queryRequestClass);
        }
        for (String key : queryMap.keySet()) {
            Object value = queryMap.get(key);
            if (value instanceof String) {
                String valueStr = String.valueOf(value);
                if ((valueStr.contains(","))) {
                    if (valueStr.contains("[") && valueStr.contains("]")) {
                        queryMap.put(key, JsonUtils.toObject(valueStr, List.class));
                    } else {
                        String[] split = valueStr.split(",");
                        queryMap.put(key, split);
                    }
                }
            }
        }
        return JsonUtils.toObject(queryMap, queryRequestClass);
    }

    /**
     * 构建excel写出包装类
     *
     * @param exportData
     * @return
     */
    private ExcelWriteWrapper buildExcelWriteWrapper(ExportDataTemplate<Object> exportDataTemplate, Object request,
                                                     ExportService exportService, List<?> exportData) {
        String exportFileName = exportDataTemplate.getExportFileName(request);
        if (StringUtils.isBlank(exportFileName)) {
            exportFileName = getExportFileName(exportService);
        }
        ExcelWriteWrapper result = ExcelWriteWrapper.of(exportFileName, "", exportData);
        result.formatFileName();
        return result;
    }

    protected String getExportFileName(ExportService exportService) {
        if (exportService != null) {
            return exportService.filename();
        }
        return this.getClass().getSimpleName();
    }

    private boolean invokeSupportSliceData(Object request) {
        for (SliceDataPostProcessor processor : sliceDataManager.getProcessors()) {
            if (processor.supportSliceData(request)) {
                return true;
            }
        }
        return false;
    }

    private void invokeBeforeSyncExportDataProcessors(Object request) {
        for (SliceDataPostProcessor processor : sliceDataManager.getProcessors()) {
            processor.beforeSyncExportData(request);
        }
    }
}
