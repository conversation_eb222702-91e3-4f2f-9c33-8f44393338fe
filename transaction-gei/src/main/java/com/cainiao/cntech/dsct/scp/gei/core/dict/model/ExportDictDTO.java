package com.cainiao.cntech.dsct.scp.gei.core.dict.model;

import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import lombok.Data;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 19:41
 * @description
 */
@Data
public class ExportDictDTO {
    /**
     * 模版类型：EXPORT导出模版配置，IMPORT导入模版配置
     *
     * @see GeiTypeEnum
     */
    private String type;

    /**
     * '模版编码'
     */
    private String code;

    /**
     * '模版配置'
     */
    private List<ExcelMetaInfo> dictEntities;

    /**
     * 模版使用模式， 0全量覆盖，1替换
     */
    private Integer mode;

    /**
     * '模版描述'
     */
    private String description;
}
