package com.cainiao.cntech.dsct.scp.gei.support.quick;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;

import java.util.List;
import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-16 20:39
 * @description
 */
public class QuickServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements QuickService<T> {

    @Override
    public boolean upsert(List<T> requests, List<SFunction<T, ?>> primaryKeys) {
        if (CollectionUtils.isEmpty(primaryKeys)) {
            return saveOrUpdateBatch(requests);
        }
        return SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, requests, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            LambdaQueryWrapper<T> queryWrapper = Wrappers.lambdaQuery();
            for (SFunction<T, ?> primaryKey : primaryKeys) {
                queryWrapper.eq(primaryKey, primaryKey.apply(entity));
            }
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            LambdaUpdateWrapper<T> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            for (SFunction<T, ?> primaryKey : primaryKeys) {
                lambdaUpdateWrapper.eq(primaryKey, primaryKey.apply(entity));
            }
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });
    }
}
