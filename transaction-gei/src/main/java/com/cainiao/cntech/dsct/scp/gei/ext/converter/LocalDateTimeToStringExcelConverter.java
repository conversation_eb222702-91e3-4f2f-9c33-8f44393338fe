package com.cainiao.cntech.dsct.scp.gei.ext.converter;


import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-22 13:42
 * @description LocalDate -> string转换器。 用于将对象实体中字段值类型为LocalDate的数据转换为字符串返回
 */
public class LocalDateTimeToStringExcelConverter implements ExcelWriteConverter<String, LocalDateTime> {
    @Override
    public boolean support(Object fieldValue) {
        return fieldValue instanceof LocalDateTime;
    }

    @Override
    public String convert(LocalDateTime fieldValue) {
        return DateUtil.localDateTimeToString(fieldValue, DateUtil.MINUTE_DATE_FORMAT);
    }
}
