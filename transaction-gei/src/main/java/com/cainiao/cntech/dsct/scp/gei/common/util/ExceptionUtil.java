//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cainiao.cntech.dsct.scp.gei.common.util;

import com.cainiao.cntech.dsct.scp.gei.common.exception.AppException;

public class ExceptionUtil {
    public ExceptionUtil() {
    }

    public static String getExceptionErrorCode(Exception e) {
        if (e instanceof AppException) {
            AppException appException = (AppException)e;
            return appException.getErrorCode().getCode();
        } else {
            return e.getClass().getName();
        }
    }
}
