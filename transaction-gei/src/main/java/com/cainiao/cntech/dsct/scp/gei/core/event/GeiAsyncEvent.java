package com.cainiao.cntech.dsct.scp.gei.core.event;

import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-10 15:16
 * @description
 */
@Data
@AllArgsConstructor
public class GeiAsyncEvent<T extends ImportTemplateVO> {
    private Long taskId;
    private ImportDataWrapper<T> wrapper;

    public static <T extends ImportTemplateVO> GeiAsyncEvent<T> of(Long taskId, ImportDataWrapper<T> wrapper) {
        return new GeiAsyncEvent<>(taskId, wrapper);
    }
}
