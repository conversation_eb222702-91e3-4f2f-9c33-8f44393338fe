package com.cainiao.cntech.dsct.scp.gei.biz.service.request.query;

import com.cainiao.cntech.dsct.scp.gei.common.request.GeiPageQueryRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:44
 * @description 主任务列表分页查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GeiTaskPageQueryRequest extends GeiPageQueryRequest {

    private Long taskId;
}
