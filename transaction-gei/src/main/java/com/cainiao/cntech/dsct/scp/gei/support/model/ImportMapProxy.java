package com.cainiao.cntech.dsct.scp.gei.support.model;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.ToString;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.BiConsumer;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-20 22:47
 * @description map代理对象
 * 模版存在动态生成字段时，实体对象无法使用，故用于该场景的模版映射实体
 */
@ToString
public final class ImportMapProxy extends ImportTemplateVO implements Map<String, Object> {
    private final Map<String, Object> data;
    private final Map<String, String> nameMap;

    public ImportMapProxy() {
        this.data = new LinkedHashMap<>();
        this.nameMap = new LinkedHashMap<>();
    }

    public static ImportMapProxy of(String key, Object value) {
        ImportMapProxy proxy = new ImportMapProxy();
        proxy.put(key, value);
        return proxy;
    }

    @Override
    public int size() {
        return data.size();
    }

    @Override
    public boolean isEmpty() {
        return data.isEmpty();
    }

    @Override
    public boolean containsKey(Object key) {
        return data.containsKey(key);
    }

    @Override
    public boolean containsValue(Object value) {
        return data.containsValue(value);
    }

    @Override
    public Object get(Object key) {
        return data.get(key);
    }
    public <T> T get(Object key, Class<T> clazz) {
        Object value = data.get(key);
        return Objects.isNull(value) ? null : clazz.cast(value);
    }

    public Object put(String k, Object v) {
        return data.put(k, v);
    }

    public Object putName(String code, String name) {
        return nameMap.put(code, name);
    }

    public Object getNameByCode(String code) {
        return nameMap.get(code);
    }

    @Override
    public Object remove(Object key) {
        return data.remove(key);
    }

    @Override
    public void putAll(@NotNull Map<? extends String, ? extends Object> m) {
        data.putAll(m);
    }

    @Override
    public void clear() {
        data.clear();
    }

    @NotNull
    @Override
    public Set<String> keySet() {
        return data.keySet();
    }

    @NotNull
    @Override
    public Collection<Object> values() {
        return data.values();
    }

    @NotNull
    @Override
    public Set<Entry<String, Object>> entrySet() {
        return data.entrySet();
    }

    @Override
    public void forEach(BiConsumer<? super String, ? super Object> action) {
        data.forEach(action);
    }

    public Map<String, Object> getRow() {
        Map<String, Object> result = new LinkedHashMap<>(data);
        Class<?> superclass = this.getClass().getSuperclass();
        for (Field declaredField : superclass.getDeclaredFields()) {
            try {
                declaredField.setAccessible(true);
                if (declaredField.isAnnotationPresent(ExcelField.class)) {
                    result.put(declaredField.getName(), declaredField.get(this));
                }
            } catch (IllegalAccessException ignored) {
            }
        }
        return result;
    }
}
