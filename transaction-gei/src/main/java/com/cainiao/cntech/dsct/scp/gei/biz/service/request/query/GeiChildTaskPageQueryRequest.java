package com.cainiao.cntech.dsct.scp.gei.biz.service.request.query;

import com.cainiao.cntech.dsct.scp.gei.common.request.GeiPageQueryRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:44
 * @description 子任务列表分页查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GeiChildTaskPageQueryRequest extends GeiPageQueryRequest {
    /**
     * 父任务id
     */
    private Long parent;
}
