package com.cainiao.cntech.dsct.scp.gei.biz.service.request.insert;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:44
 * @description 子任务列表insert请求
 */
@Data
public class GeiChildTaskInsertRequest {
    /**
     * 是否分页，当只有一个子任务时，该字段为true，否则为false
     */
    private Boolean paging = Boolean.TRUE;
    /**
     * '当前页'
     */
    private Long page;
    /**
     * '期望记录条数'
     */
    private Long pageSize;
    /**
     * 'process阶段的尝试次数'
     */
    private Integer processTryTimes;
    /**
     * 'process阶段的最大尝试次数'
     */
    private Integer processMaxTryTimes;
    /**
     * '任务执行过期的时间'
     */
    private LocalDateTime gmtExpired;
    /**
     * 执行模版的编码
     */
    private String executeTemplateCode;
    /**
     * 模版绑定的字典编码
     */
    private String templateDictCode;

}
