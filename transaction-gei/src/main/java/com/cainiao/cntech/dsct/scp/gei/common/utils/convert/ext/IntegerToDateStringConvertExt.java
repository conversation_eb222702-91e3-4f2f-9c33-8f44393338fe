package com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext;

import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.model.ConvertExtensibleWrapper;

import java.time.LocalDate;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-10-08 19:27
 * @description
 */
public class IntegerToDateStringConvertExt implements ConvertProcessExtensible<LocalDate> {
    @Override
    public LocalDate doConvert(ConvertExtensibleWrapper ew) throws IllegalAccessException {
        Object o = ew.getSourceField().get(ew.getSourceObject());
        if(o instanceof Integer){
            Integer date = (Integer) o;
            return DateUtil.integerToLocalDate(date, DateUtil.STANDARD_DATE_FORMAT);
        }
        return null;
    }
}
