package com.cainiao.cntech.dsct.scp.gei.core.event;

import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import org.springframework.context.ApplicationEventPublisher;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024-12-17 14:45
 * @description Excel执行事件发布器
 */
public class GeiEventPublisher {
    @Resource
    private ApplicationEventPublisher publishEvent;

    /**
     * 发送任务执行事件
     */
    public void sendAsyncExportEvent(Long parentId) {
        publishEvent.publishEvent(GeiAsyncEvent.of(parentId, null));
    }

    /**
     * 发送任务执行事件
     */
    public <T extends ImportTemplateVO> void sendAsyncImportEvent(GeiAsyncEvent<T> event) {
        publishEvent.publishEvent(event);
    }
}
