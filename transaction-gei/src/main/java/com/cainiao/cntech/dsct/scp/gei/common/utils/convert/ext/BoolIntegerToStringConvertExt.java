package com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext;

import com.cainiao.cntech.dsct.scp.gei.common.enums.BooleanEnum;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.model.ConvertExtensibleWrapper;

import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-20 14:14
 * @description 布尔值的Integer 转 字符串(是，否)
 */
public class BoolIntegerToStringConvertExt implements ConvertProcessExtensible<String> {
    @Override
    public String doConvert(ConvertExtensibleWrapper ew) throws IllegalAccessException {
        Object o = ew.getSourceField().get(ew.getSourceObject());
        if (Objects.isNull(o)) {
            return BooleanEnum.FALSE.getName();
        }
        if (o instanceof Integer) {
            Integer bool = (Integer) o;
            BooleanEnum booleanEnum = BooleanEnum.getByCode(bool);
            return Objects.isNull(booleanEnum) ? BooleanEnum.FALSE.getName() : booleanEnum.getName();
        }else if (o instanceof String) {
            String bool = (String) o;
            BooleanEnum booleanEnum = BooleanEnum.getByCode(bool);
            return Objects.isNull(booleanEnum) ? BooleanEnum.FALSE.getName() : booleanEnum.getName();
        }
        return null;
    }
}
