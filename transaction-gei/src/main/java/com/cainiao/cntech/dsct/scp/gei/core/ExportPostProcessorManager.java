package com.cainiao.cntech.dsct.scp.gei.core;

import com.cainiao.cntech.dsct.scp.gei.ext.processor.ExportPostProcessor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ConfigurableApplicationContext;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-14 19:57
 * @description
 */

public class ExportPostProcessorManager implements ApplicationRunner {

    @Resource
    private ConfigurableApplicationContext context;
    private List<ExportPostProcessor> exportPostProcessors;

    public List<ExportPostProcessor> getProcessors() {
        return exportPostProcessors;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        Map<String, ExportPostProcessor> beans = context.getBeansOfType(ExportPostProcessor.class);
        exportPostProcessors = new LinkedList<>();
        exportPostProcessors.addAll(beans.values());
    }
}
