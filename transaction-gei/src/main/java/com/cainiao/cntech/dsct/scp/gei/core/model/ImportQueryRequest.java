package com.cainiao.cntech.dsct.scp.gei.core.model;

import com.cainiao.cntech.dsct.scp.gei.common.utils.ReflectionUtils;
import com.cainiao.cntech.dsct.scp.gei.ext.converter.ImportExtConversionService;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.support.DefaultConversionService;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-12 12:17
 * @description excel下载模版的request
 */
@Data
public class ImportQueryRequest {
    /**
     * 维度
     */
    private String dimension;
    /**
     * 字段
     */
    private String fields;
    /**
     * 按逗号分割后的维度列表
     */
    private List<String> dimensionList = Collections.emptyList();
    /**
     * 按逗号分割后的字段列表
     */
    private List<String> fieldsList;
    /**
     * 构建meta时是否深层构建父类meta标记
     */
    private boolean deep = false;

    /**
     * 附带查询参数
     */
    private Map<String, Object> params;

    public static ImportQueryRequest of(String dim, String fields, boolean isDeep) {
        ImportQueryRequest request = new ImportQueryRequest();
        request.setDimension(dim);
        request.setFields(fields);
        request.setDeep(isDeep);
        request.resolveToList();
        return request;
    }

    public void resolveToList() {
        if (StringUtils.isNotBlank(dimension)) {
            String[] split = dimension.split(",");
            dimensionList = Arrays.stream(split).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(fields)) {
            String[] split = fields.split(",");
            fieldsList = Arrays.stream(split).collect(Collectors.toList());
        }
    }

    public ImportQueryRequest setParams(Map<String, Object> params) {
        this.params = params;
        return this;
    }

    private <Q> Q getParams(Class<Q> clazz, Object... args) {
        DefaultConversionService convertor = ImportExtConversionService.getInstance();
        Q instance = ReflectionUtils.createInstance(clazz, args);
        if (Objects.isNull(instance)) {
            return null;
        }
        for (Map.Entry<String, Object> entry : getParams().entrySet()) {
            Field field = ReflectionUtils.getFieldByName(clazz, entry.getKey());
            if (Objects.nonNull(field)) {
                ReflectionUtils.setFieldValue(instance, field, convertor.convert(entry.getValue(), field.getType()));
            }
        }
        return instance;
    }
}
