package com.cainiao.cntech.dsct.scp.gei.biz.service.dto;

import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTaskStatusEnum;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:19
 * @description 通用导入导出子任务
 */
@Data
public class ScpGeiChildTaskDTO {
    /**
     * '创建时间'
     */
    private LocalDateTime gmtCreate;
    /**
     * '修改时间'
     */
    private LocalDateTime gmtModified;

    /**
     * '任务ID'
     */
    private String taskId;
    /**
     * '父任务ID'
     */
    private Long parentTaskId;

    /**
     * '用户请求参数'
     */
    private String requestParams;
    /**
     * '任务的类型
     * @see GeiTypeEnum
     */
    private String taskType;
    /**
     * 任务状态，CREATED新建，AWAIT待运行，RUNNING运行中，FAILED异常，SUCCESS成功
     * @see GeiTaskStatusEnum
     */
    private String status;
    /**
     * '任务状态产生的原因'
     */
    private String statusDesc;
    /**
     * '任务开始处理的时间戳'
     */
    private LocalDateTime gmtProcessStarted;
    /**
     * '任务结束的时间戳'
     */
    private LocalDateTime gmtProcessFinished;
    /**
     * 是否分页，当只有一个子任务时，该字段为true，否则为false
     */
    private Boolean paging;
    /**
     * '当前页'
     */
    private Long page;
    /**
     * '期望记录条数'
     */
    private Long pageSize;
    /**
     * 'process阶段的尝试次数'
     */
    private Integer processTryTimes;
    /**
     * 'process阶段的最大尝试次数'
     */
    private Integer processMaxTryTimes;
    /**
     * '任务执行过期的时间'
     */
    private LocalDateTime gmtExpired;
    /**
     * '数据文件地址'
     */
    private String dataFileAddress;
    /**
     * 执行模版的编码
     */
    private String executeTemplateCode;
    /**
     * 模版绑定的字典编码
     */
    private String templateDictCode;

    /**
     * '任务的扩展属性'
     */
    private String properties;
    /**
     * '创建人编码'
     */
    private String creatorCode;
    /**
     * '创建人名称'
     */
    private String creatorName;
    /**
     * '操作人编码'
     */
    private String operatorCode;
    /**
     * '操作人名称'
     */
    private String operatorName;
}
