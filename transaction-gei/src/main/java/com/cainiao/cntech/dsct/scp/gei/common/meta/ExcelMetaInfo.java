package com.cainiao.cntech.dsct.scp.gei.common.meta;

import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import lombok.*;

import java.util.Collection;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-21 14:21
 * @description excel表头元信息
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelMetaInfo {
    /**
     * 列唯一id，与字段名对应
     */
    private String code;
    /**
     * 表头文字，列名
     */
    private String name;

    /**
     * 设置冻结该列，如果不设置当前字段，则表示不冻结 left - 左侧冻结 / right - 右侧冻结
     */
    private String fixed;

    /**
     * 对象字段类型
     */
    private Class<?> fieldClass;

    private Boolean hidden;
    /**
     * 提示
     */
    private String tooltip;
    /**
     * 顺序
     */
    private Integer sort = 0;

    /**
     * 导入时是否为一行的主键；
     * 如果设置为true，则会校验导入数据是否出现重复行
     */
    private Boolean importKey = Boolean.FALSE;

    @ConvertField(deep = true)
    private Collection<ExcelMetaInfo> children;

    public ExcelMetaInfo(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ExcelMetaInfo of(String code, String name, Class<?> fieldClass) {
        ExcelMetaInfo metaInfo = new ExcelMetaInfo(code, name);
        metaInfo.setFieldClass(fieldClass);
        return metaInfo;
    }

    public static ExcelMetaInfo of(String code, String name) {
        return new ExcelMetaInfo(code, name);
    }
}
