package com.cainiao.cntech.dsct.scp.gei.core.storage;

import com.cainiao.cntech.dsct.scp.gei.configuration.GeiStorageProperties;

import java.io.InputStream;
import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-17 16:26
 * @description
 */
public class NoneStorageTemplate implements StorageTemplate {
    @Override
    public void initial(GeiStorageProperties geiStorageProperties) {

    }

    @Override
    public void destroy() {

    }

    @Override
    public boolean isExists(String key) {
        return false;
    }

    @Override
    public InputStream getResource(String key) {
        return null;
    }

    @Override
    public boolean upload(String key, byte[] resource) {
        return true;
    }

    @Override
    public boolean delete(String key) {
        return true;
    }

    @Override
    public String getUrl(String key, String fileName, LocalDateTime expireTime) {
        return "";
    }
}
