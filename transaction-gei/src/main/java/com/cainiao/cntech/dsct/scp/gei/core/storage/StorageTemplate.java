package com.cainiao.cntech.dsct.scp.gei.core.storage;

import com.cainiao.cntech.dsct.scp.gei.configuration.GeiStorageProperties;

import javax.validation.constraints.NotNull;
import java.io.InputStream;
import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 19:15
 * @description
 */
public interface StorageTemplate {
    /**
     * 初始化方法
     *
     * @param geiStorageProperties 数据存储配置文件信息
     */
    void initial(GeiStorageProperties geiStorageProperties);

    /**
     * 销毁方法，需要手动实现者指定销毁执行函数
     */
    void destroy();

    /**
     * 判断文件是否存在
     *
     * @param key
     * @return
     */
    boolean isExists(@NotNull String key);

    /**
     * 获取文件流
     *
     * @param key 文件key
     * @return
     */
    InputStream getResource(@NotNull String key);

    /**
     * 上传文件
     *
     * @param key      文件key
     * @param resource 文件字节数组
     * @return
     */
    boolean upload(String key, byte[] resource);

    /**
     * 删除文件
     *
     * @param key key
     */
    boolean delete(String key);

    /**
     * 获取文件下载地址
     *
     * @param key      文件key
     * @param fileName 文件名
     * @return
     */
    String getUrl(@NotNull String key, String fileName, LocalDateTime expireTime);
}
