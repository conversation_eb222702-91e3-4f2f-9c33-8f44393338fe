//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cainiao.cntech.dsct.scp.gei.common.model;


import com.cainiao.cntech.dsct.scp.gei.common.exception.AppException;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.common.util.ExceptionUtil;

public class ResponseResult<T> {
    private Boolean success;
    private T data;
    private String errorCode;
    private String errorMessage;

    private ResponseResult() {
        this.success = Boolean.TRUE;
    }

    private ResponseResult(T model) {
        this.success = Boolean.TRUE;
        this.data = model;
    }

    private ResponseResult(String errorCode, String errorMessage) {
        this.success = Boolean.FALSE;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public static <T> ResponseResult<T> success(T model) {
        return new ResponseResult(model);
    }

    public static <T> ResponseResult<T> fail(Exception e) {
        if (e instanceof AppException) {
            return fail(((AppException)e).getErrorCode(), ((AppException)e).getDetailedMessage());
        } else {
            String errorCode = ExceptionUtil.getExceptionErrorCode(e);
            return new ResponseResult(errorCode, ErrorCode.SYSTEM_ERROR.getDesc());
        }
    }

    public static <T> ResponseResult<T> fail(ErrorCode errorCode) {
        return new ResponseResult(errorCode.getCode(), errorCode.getDesc());
    }

    public static <T> ResponseResult<T> fail(ErrorCode errorCode, String errorMessage) {
        return new ResponseResult(errorCode.getCode(), errorMessage);
    }

    public Boolean getSuccess() {
        return this.success;
    }

    public T getData() {
        return this.data;
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public void setSuccess(final Boolean success) {
        this.success = success;
    }

    public void setData(final T data) {
        this.data = data;
    }

    public void setErrorCode(final String errorCode) {
        this.errorCode = errorCode;
    }

    public void setErrorMessage(final String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ResponseResult)) {
            return false;
        } else {
            ResponseResult<?> other = (ResponseResult)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label59: {
                    Object this$success = this.getSuccess();
                    Object other$success = other.getSuccess();
                    if (this$success == null) {
                        if (other$success == null) {
                            break label59;
                        }
                    } else if (this$success.equals(other$success)) {
                        break label59;
                    }

                    return false;
                }

                Object this$data = this.getData();
                Object other$data = other.getData();
                if (this$data == null) {
                    if (other$data != null) {
                        return false;
                    }
                } else if (!this$data.equals(other$data)) {
                    return false;
                }

                Object this$errorCode = this.getErrorCode();
                Object other$errorCode = other.getErrorCode();
                if (this$errorCode == null) {
                    if (other$errorCode != null) {
                        return false;
                    }
                } else if (!this$errorCode.equals(other$errorCode)) {
                    return false;
                }

                Object this$errorMessage = this.getErrorMessage();
                Object other$errorMessage = other.getErrorMessage();
                if (this$errorMessage == null) {
                    if (other$errorMessage != null) {
                        return false;
                    }
                } else if (!this$errorMessage.equals(other$errorMessage)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ResponseResult;
    }

    public int hashCode() {
//        int PRIME = true;
        int result = 1;
        Object $success = this.getSuccess();
        result = result * 59 + ($success == null ? 43 : $success.hashCode());
        Object $data = this.getData();
        result = result * 59 + ($data == null ? 43 : $data.hashCode());
        Object $errorCode = this.getErrorCode();
        result = result * 59 + ($errorCode == null ? 43 : $errorCode.hashCode());
        Object $errorMessage = this.getErrorMessage();
        result = result * 59 + ($errorMessage == null ? 43 : $errorMessage.hashCode());
        return result;
    }

    public String toString() {
        return "ResponseResult(success=" + this.getSuccess() + ", data=" + this.getData() + ", errorCode=" + this.getErrorCode() + ", errorMessage=" + this.getErrorMessage() + ")";
    }
}
