package com.cainiao.cntech.dsct.scp.gei.common.meta;

import com.cainiao.cntech.dsct.scp.gei.common.request.GeiPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 14:30
 * @description 表格数据分页返回包装
 */
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
public class ExcelPageMetaInfoWrapper extends ExcelMetaInfoWrapper {
    /**
     * 分页数据
     */
    private ExcelPagingWrapper paging;


    public ExcelPageMetaInfoWrapper(List<ExcelMetaInfo> meta, ExcelPagingWrapper paging, List<?> list) {
        super(list, meta);
        this.paging = paging;
    }

    public ExcelPageMetaInfoWrapper(List<ExcelMetaInfo> meta, ExcelPagingWrapper paging, List<?> list, String key) {
        super(list, meta, key);
        this.paging = paging;
    }

    public static ExcelPageMetaInfoWrapper of(GeiPageQueryRequest request, Class<?> metaInfoClazz) {
        return of(metaInfoClazz, Collections.emptyList(), request.getCurrentPage(), request.getPageSize(), 0L);
    }

    public static <T> ExcelPageMetaInfoWrapper of(GeiPageQueryRequest request, Long totalCount, List<?> list, Class<T> metaVOClass) {
        return of(request, totalCount, list, metaVOClass, true);
    }

    public static <T> ExcelPageMetaInfoWrapper of(GeiPageQueryRequest request, Long totalCount, List<?> list, Class<T> metaVOClass, boolean metaDeep) {
        return of(request, totalCount, GeiCommonConvert.convert(list, metaVOClass), ExcelMetaInfoCreator.create(metaVOClass, metaDeep));
    }


    public static <T> ExcelPageMetaInfoWrapper of(GeiPageQueryRequest request, Long totalCount, List<T> list, List<ExcelMetaInfo> meta) {
        return new ExcelPageMetaInfoWrapper(meta,
                ExcelPagingWrapper.of(request.getCurrentPage(), request.getPageSize(), totalCount),
                list);
    }

    public static <T> ExcelPageMetaInfoWrapper of(GeiPageQueryRequest request, Long totalCount, Class<?> metaInfoClass, List<T> list) {
        return of(metaInfoClass, list, request.getCurrentPage(), request.getPageSize(), totalCount);
    }

    public static <T> ExcelPageMetaInfoWrapper of(GeiPageQueryRequest request, Long totalCount, List<T> list) {
        return of(list, request.getCurrentPage(), request.getPageSize(), totalCount);
    }

    public static ExcelPageMetaInfoWrapper of(GeiPageQueryRequest request, Long totalCount, Class<?> metaInfoClazz) {
        return of(metaInfoClazz, request.getCurrentPage(), request.getPageSize(), totalCount);
    }

    public static <T> ExcelPageMetaInfoWrapper of(List<T> list, Long currentPage, Long pageSize, Long totalCount) {
        Class<?> metaInfoClazz = null;
        if (Objects.nonNull(list) && !list.isEmpty()) {
            metaInfoClazz = list.get(0).getClass();
        }
        return of(metaInfoClazz, list, currentPage, pageSize, totalCount);
    }

    public static ExcelPageMetaInfoWrapper of(Class<?> metaInfoClazz, Long currentPage, Long pageSize, Long totalCount) {
        return of(metaInfoClazz, Collections.emptyList(), currentPage, pageSize, totalCount);
    }

    public static <T> ExcelPageMetaInfoWrapper of(Class<?> metaInfoClazz, List<T> list, Long currentPage, Long pageSize, Long totalCount) {
        return new ExcelPageMetaInfoWrapper(ExcelMetaInfoCreator.create(metaInfoClazz),
                ExcelPagingWrapper.of(currentPage, pageSize, totalCount),
                list);
    }

    public static ExcelPageMetaInfoWrapper of(ExcelMetaInfoWrapper excelMetaInfoWrapper) {
        return of(null, excelMetaInfoWrapper);
    }

    public static ExcelPageMetaInfoWrapper of(ExcelPagingWrapper paging, ExcelMetaInfoWrapper excelMetaInfoWrapper) {
        return new ExcelPageMetaInfoWrapper(excelMetaInfoWrapper.getMeta(),
                paging,
                excelMetaInfoWrapper.getList());
    }

    public static ExcelPageMetaInfoWrapper of() {
        return new ExcelPageMetaInfoWrapper();
    }
}
