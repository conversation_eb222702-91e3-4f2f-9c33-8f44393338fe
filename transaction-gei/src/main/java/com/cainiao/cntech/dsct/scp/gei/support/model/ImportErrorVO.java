package com.cainiao.cntech.dsct.scp.gei.support.model;


import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-17 17:15
 * @description
 */
@Data
public class ImportErrorVO {
    @ExcelField("异常编码")
    private String errorCode;
    @ExcelField("异常描述")
    private String errorDesc;

    public static ImportErrorVO of(String errorDesc) {
        ImportErrorVO error = new ImportErrorVO();
        error.setErrorCode("SYSTEM_ERROR");
        error.setErrorDesc(errorDesc);
        return error;
    }
}
