package com.cainiao.cntech.dsct.scp.gei.core.model;

import com.cainiao.cntech.dsct.scp.gei.common.enums.ImportMode;
import com.cainiao.cntech.dsct.scp.gei.common.utils.ReflectionUtils;
import com.cainiao.cntech.dsct.scp.gei.ext.converter.ImportExtConversionService;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.core.convert.support.DefaultConversionService;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-18 15:52
 * @description
 */
@Data
@Accessors(chain = true)
public class ImportDataWrapper<T> {
    private static final String UNDEFINED = "undefined";
    private static final String NULL = "null";
    private String dimension;
    private List<String> dimensionList;
    private String fields;
    private List<String> fieldsList;
    private List<T> data;
    /**
     * 导入数据附带参数
     */
    private Map<String, Object> params;
    /**
     * 导入模式
     */
    private ImportMode importMode = ImportMode.UPSERT;

    public static <T> ImportDataWrapper<T> of(List<T> data, String dimension, String fields) {
        ImportDataWrapper<T> result = new ImportDataWrapper<>();
        result.setData(data);
        result.setDimension(dimension);
        result.setFields(fields);
        result.setDimensionList(Arrays.stream(dimension.split(",")).collect(Collectors.toList()));
        result.setFieldsList(Arrays.stream(fields.split(",")).collect(Collectors.toList()));
        return result;
    }

    public ImportDataWrapper<T> setParams(Map<String, Object> params) {
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (entry.getValue() instanceof String) {
                if (UNDEFINED.equals(String.valueOf(entry.getValue())) || NULL.equals(String.valueOf(entry.getValue()))) {
                    entry.setValue(null);
                }
            }
        }
        this.params = params;
        setImportMode(ImportMode.getImportMode(String.valueOf(params.get("importMode"))));
        return this;
    }

    private <Q> Q getParams(Class<Q> clazz, Object... args) {
        DefaultConversionService convertor = ImportExtConversionService.getInstance();
        Q instance = ReflectionUtils.createInstance(clazz, args);
        if (Objects.isNull(instance)) {
            return null;
        }
        for (Map.Entry<String, Object> entry : getParams().entrySet()) {
            Field field = ReflectionUtils.getFieldByName(clazz, entry.getKey());
            if (Objects.nonNull(field)) {
                ReflectionUtils.setFieldValue(instance, field, convertor.convert(entry.getValue(), field.getType()));
            }
        }
        return instance;
    }
}
