//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cainiao.cntech.dsct.scp.gei.common.exception;

public class AppException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    private String code;
    private String detailedMessage;
    private ErrorCode errorCode;

    public AppException(ErrorCode errorCode) {
        this(errorCode, errorCode.getDesc());
    }

    public AppException(ErrorCode errorCode, String detailedMessage) {
        super(errorCode.getCode() + "," + errorCode.getDesc() + "," + detailedMessage);
        this.errorCode = errorCode;
        this.detailedMessage = detailedMessage;
    }

    public AppException(Throwable t, ErrorCode errorCode, String detailedMessage) {
        super(errorCode.getCode() + "," + errorCode.getDesc() + "," + detailedMessage, t);
        this.errorCode = errorCode;
        this.detailedMessage = detailedMessage;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDetailedMessage() {
        return this.detailedMessage;
    }

    public void setDetailedMessage(String detailedMessage) {
        this.detailedMessage = detailedMessage;
    }

    public ErrorCode getErrorCode() {
        return this.errorCode;
    }

    public void setErrorCode(ErrorCode errorCode) {
        this.errorCode = errorCode;
    }
}
