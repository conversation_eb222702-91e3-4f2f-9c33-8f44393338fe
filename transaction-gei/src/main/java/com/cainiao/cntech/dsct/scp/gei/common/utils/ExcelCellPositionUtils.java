package com.cainiao.cntech.dsct.scp.gei.common.utils;

import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-02-08 15:08
 * @description
 */
public class ExcelCellPositionUtils {
    public static String getPosition(int rowOffset , int rowIndex, int columnIndex) {
        Assert.isTrue(rowOffset >= 0, ErrorCode.ILLEGAL_ARGUMENT, "rowOffset must be gte 0");
        Assert.isTrue(rowIndex >= 0, ErrorCode.ILLEGAL_ARGUMENT, "rowIndex must be gte 0");
        Assert.isTrue(columnIndex >= 0, ErrorCode.ILLEGAL_ARGUMENT, "columnIndex must be gte 0");
        StringBuilder positionBuilder = new StringBuilder();
        columnIndex += 1;
        while (columnIndex > 0) {
            int remainder = (columnIndex - 1) % 26;
            positionBuilder.insert(0, (char) ('A' + remainder));
            columnIndex = (columnIndex - remainder) / 26;
        }
        positionBuilder.append(rowIndex + rowOffset);
        return positionBuilder.toString();
    }
}
