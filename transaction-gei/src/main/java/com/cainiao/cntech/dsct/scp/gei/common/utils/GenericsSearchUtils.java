package com.cainiao.cntech.dsct.scp.gei.common.utils;

import org.springframework.core.ResolvableType;

import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 15:57
 * @description 泛型查找工具
 */
public class GenericsSearchUtils {
    /**
     * 查找泛型类型
     *
     * @param source      原对象
     * @param searchClass 查找的泛型类型
     * @param index       泛型索引下标
     * @return class
     */
    public static <T> Class<T> searchClass(Object source, Class<?> searchClass, int index) {
        ResolvableType resolvableType = searchClass(ResolvableType.forClass(source.getClass()), searchClass);
        if (Objects.nonNull(resolvableType)) {
            return (Class<T>) resolvableType.getGeneric(index).resolve();
        }
        throw new RuntimeException("Generic type not found！");
    }

    /**
     * 查找泛型的ResolvableType
     *
     * @param resolvableType 原对象的ResolvableType
     * @param searchClass    查找的泛型类型
     * @return 泛型类型对应的ResolvableType
     */
    private static ResolvableType searchClass(ResolvableType resolvableType, Class<?> searchClass) {
        if (Objects.equals(resolvableType.getRawClass(), Object.class)) {
            return null;
        }
        if (Objects.equals(resolvableType.getRawClass(), searchClass)) {
            return resolvableType;
        }
        ResolvableType searchResult = searchClass(resolvableType.getSuperType(), searchClass);
        if (Objects.nonNull(searchResult)) {
            return searchResult;
        }
        ResolvableType result = null;
        a: for (ResolvableType temp0 : resolvableType.getInterfaces()) {
            if (!Objects.equals(temp0.getRawClass(), Object.class)) {
                if (Objects.equals(temp0.getRawClass(), searchClass)) {
                    result = temp0;
                    break;
                }
                for (ResolvableType temp1 : temp0.getInterfaces()) {
                    ResolvableType t = searchClass(temp1, searchClass);
                    if (Objects.nonNull(t)) {
                        result = t;
                        break a;
                    }
                }
            }
        }
        return result;
    }
}
