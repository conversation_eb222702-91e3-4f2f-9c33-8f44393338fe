package com.cainiao.cntech.dsct.scp.gei.common.enums;

import com.cainiao.cntech.dsct.scp.gei.configuration.GeiPoolProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.function.Function;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 15:32
 * @description
 */
@Getter
@AllArgsConstructor
public enum GeiPoolBlockQueue {
    ARRAY("ARRAY", "有边队列", config -> new ArrayBlockingQueue<>(config.getQueueSize())),
    LINKED("LINKED", "链表队列", config -> new LinkedBlockingQueue<>()),
    SYNC("SYNC", "同步队列", config -> new SynchronousQueue<>()),
    ;
    private final String code;
    private final String name;
    private final Function<GeiPoolProperties, BlockingQueue<Runnable>> creator;
    public BlockingQueue<Runnable> getQueue(GeiPoolProperties geiPoolProperties) {
        return creator.apply(geiPoolProperties);
    }
}
