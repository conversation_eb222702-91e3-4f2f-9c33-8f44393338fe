#!/bin/bash

process_branch() {
    local branch=$1
    git checkout -f $branch

    # 定义要处理的文件列表
    local files=("application-dev.properties" "application-localhost.properties" "application-pro.properties" "application-test.properties")

    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            # mac 机器命令
            sed -i '' 's/LTAI5tFPG2pjF6wzWzn661N3/testak/g' "$file"
            sed -i '' 's/******************************/testak/g' "$file"
            sed -i '' 's/cu7TdfhARb1hHrHw/testak/g' "$file"
            sed -i '' 's/1huBpnuHgyfZNsh4Szhp5JNAi6xtlYyK/testak/g' "$file"
            sed -i '' 's/aAo4pUUiuRi0Oo4p/testak/g' "$file"
            sed -i '' 's/KaNI3h0VvdogKPajpPg4O0YJR4AKy71/testak/g' "$file"
            sed -i '' 's/LTAI5t6CNDYFiKjRcukxrokZ/testak/g' "$file"
            sed -i '' 's/******************************/testak/g' "$file"

        else
            echo "文件 $file 不存在"
        fi
    done

    # 添加所有修改过的文件
    git add "${files[@]}"
    git commit -m "remove ak"
    git push
}

# 获取所有远程分支
branches=$(git branch -r | grep -v '\->' | sed 's/origin\///')

for branch in $branches; do
    echo "处理分支：$branch"
    process_branch $branch
done

echo "处理完成。"
