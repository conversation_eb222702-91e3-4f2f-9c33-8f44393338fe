#druid \u4EE5\u4E0B\u914D\u7F6E\u5747\u4E3Adruid\u6570\u636E\u5E93\u8FDE\u63A5\u6C60\u914D\u7F6E\uFF0C\u5177\u4F53\u53C2\u6570\u53C2\u8003Druid\u8D44\u6599\uFF0C\u53EF\u9002\u5F53\u4FEE\u6539
spring.datasource.druid.url=***************************************************************************************************************
spring.datasource.druid.username=scp
spring.datasource.druid.password=G5bT8JfK3QeR
spring.datasource.druid.driver-class-name=com.p6spy.engine.spy.P6SpyDriver
# \u521D\u59CB\u5316\u5927\u5C0F\uFF0C\u6700\u5C0F\uFF0C\u6700\u5927\uFF0C\u4E00\u822C\u4FEE\u6539\u4EE5\u4E0B\u4E09\u4E2A\u53C2\u6570\u5373\u53EF
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=100
# \u914D\u7F6E\u83B7\u53D6\u8FDE\u63A5\u7B49\u5F85\u8D85\u65F6\u7684\u65F6\u95F4
spring.datasource.druid.max-wait=60000
# \u914D\u7F6E\u95F4\u9694\u591A\u4E45\u624D\u8FDB\u884C\u4E00\u6B21\u68C0\u6D4B\uFF0C\u68C0\u6D4B\u9700\u8981\u5173\u95ED\u7684\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.startTime-between-eviction-runs-millis=60000
# \u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.min-evictable-idle-startTime-millis=300000
spring.datasource.druid.keepAlive=true
spring.datasource.druid.keepAliveBetweenTimeMillis=120000
# \u68C0\u6D4B\u8FDE\u63A5\u662F\u5426\u6709\u6548\u7684sql
spring.datasource.druid.validation-query=SELECT 'x'
spring.datasource.druid.validation-query-timeout=60000
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
# \u914D\u7F6E\u76D1\u63A7\u7EDF\u8BA1\u62E6\u622A\u7684filters\uFF0C\u8BE5\u65B9\u5F0F\u542F\u7528\u7684filter\u90FD\u4E3A\u9ED8\u8BA4\u914D\u7F6E\uFF0C\u5982\u679C\u9ED8\u8BA4\u914D\u7F6E\u4E0D\u80FD\u6EE1\u8DB3\u9700\u6C42\uFF0C\u5219\u4E0D\u8981\u7528\u8FD9\u79CD\u65B9\u5F0F\uFF0C\u6539\u6210\u4E3A\u6BCF\u4E00\u4E2Afilter\u5355\u72EC\u914D\u7F6E
spring.datasource.druid.filters=stat
#\u963F\u91CCdataq\u6570\u636E\u6E90
#druid \u4EE5\u4E0B\u914D\u7F6E\u5747\u4E3Adruid\u6570\u636E\u5E93\u8FDE\u63A5\u6C60\u914D\u7F6E\uFF0C\u5177\u4F53\u53C2\u6570\u53C2\u8003Druid\u8D44\u6599\uFF0C\u53EF\u9002\u5F53\u4FEE\u6539
spring.datasource.druid.dataq.url=***************************************************************************************************************
spring.datasource.druid.dataq.username=scp
spring.datasource.druid.dataq.password=G5bT8JfK3QeR
spring.datasource.druid.dataq.driver-class-name=com.p6spy.engine.spy.P6SpyDriver
# \u521D\u59CB\u5316\u5927\u5C0F\uFF0C\u6700\u5C0F\uFF0C\u6700\u5927\uFF0C\u4E00\u822C\u4FEE\u6539\u4EE5\u4E0B\u4E09\u4E2A\u53C2\u6570\u5373\u53EF
spring.datasource.druid.dataq.initial-size=5
spring.datasource.druid.dataq.min-idle=5
spring.datasource.druid.dataq.max-active=100
# \u914D\u7F6E\u83B7\u53D6\u8FDE\u63A5\u7B49\u5F85\u8D85\u65F6\u7684\u65F6\u95F4
spring.datasource.druid.dataq.max-wait=60000
# \u914D\u7F6E\u95F4\u9694\u591A\u4E45\u624D\u8FDB\u884C\u4E00\u6B21\u68C0\u6D4B\uFF0C\u68C0\u6D4B\u9700\u8981\u5173\u95ED\u7684\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.dataq.startTime-between-eviction-runs-millis=60000
# \u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.dataq.min-evictable-idle-startTime-millis=300000
# \u68C0\u6D4B\u8FDE\u63A5\u662F\u5426\u6709\u6548\u7684sql
spring.datasource.druid.dataq.validation-query=SELECT 'x'
spring.datasource.druid.dataq.validation-query-timeout=60000
spring.datasource.druid.dataq.test-while-idle=true
spring.datasource.druid.dataq.test-on-borrow=false
spring.datasource.druid.dataq.test-on-return=false
spring.datasource.druid.dataq.keepAlive=true
spring.datasource.druid.dataq.keepAliveBetweenTimeMillis=120000
# \u914D\u7F6E\u76D1\u63A7\u7EDF\u8BA1\u62E6\u622A\u7684filters\uFF0C\u8BE5\u65B9\u5F0F\u542F\u7528\u7684filter\u90FD\u4E3A\u9ED8\u8BA4\u914D\u7F6E\uFF0C\u5982\u679C\u9ED8\u8BA4\u914D\u7F6E\u4E0D\u80FD\u6EE1\u8DB3\u9700\u6C42\uFF0C\u5219\u4E0D\u8981\u7528\u8FD9\u79CD\u65B9\u5F0F\uFF0C\u6539\u6210\u4E3A\u6BCF\u4E00\u4E2Afilter\u5355\u72EC\u914D\u7F6E
spring.datasource.druid.dataq.filters=stat
# Mapper.xml\u6240\u5728\u7684\u4F4D\u7F6E
mybatis.mapper-locations=classpath*:mapper/*Dao.xml
mybatis.dataqmapper-locations=classpath*:dataqmapper/*Dao.xml
# \u5F00\u542FMyBatis\u7684\u4E8C\u7EA7\u7F13\u5B58
mybatis.configuration.cache-enabled=true
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.type-handlers-package=cn.aliyun.ryytn.common.mybatis.handler
# \u8BBE\u7F6E\u65B9\u8A00\uFF0C\u6B64\u5904\u6307\u5B9A PGSQL \u6570\u636E\u5E93
pagehelper.helper-dialect=postgresql
# \u662F\u5426\u542F\u52A8\u5408\u7406\u5316\uFF0C\u9ED8\u8BA4\u662F false\u3002
# \u542F\u7528\u5408\u7406\u5316\u65F6\uFF0C\u5982\u679CpageNum<1\u4F1A\u67E5\u8BE2\u7B2C\u4E00\u9875\uFF0C\u5982\u679CpageNum>pages\uFF08\u6700\u5927\u9875\u6570\uFF09\u4F1A\u67E5\u8BE2\u6700\u540E\u4E00\u9875\u3002
# \u7981\u7528\u5408\u7406\u5316\u65F6\uFF0C\u5982\u679CpageNum<1\u6216pageNum>pages\u4F1A\u8FD4\u56DE\u7A7A\u6570\u636E
pagehelper.reasonable=true
# \u662F\u5426\u652F\u6301\u63A5\u53E3\u53C2\u6570\u6765\u4F20\u9012\u5206\u9875\u53C2\u6570\uFF0C\u9ED8\u8BA4false
pagehelper.support-methods-arguments=true
# \u9ED8\u8BA4\u503C\u4E3A false\uFF0C\u5F53\u8BE5\u53C2\u6570\u8BBE\u7F6E\u4E3A true \u65F6\uFF0C\u5982\u679C pageSize=0 \u6216\u8005 RowBounds.limit = 0 \u5C31\u4F1A\u67E5\u8BE2\u51FA\u5168\u90E8\u7684\u7ED3\u679C\uFF08\u76F8\u5F53\u4E8E\u6CA1\u6709\u6267\u884C\u5206\u9875\u67E5\u8BE2\uFF0C\u4F46\u662F\u8FD4\u56DE\u7ED3\u679C\u4ECD\u7136\u662F Page \u7C7B\u578B\uFF09
pagehelper.page-size-zero=true
# REDIS (RedisProperties)
# Redis\u6570\u636E\u5E93\u7D22\u5F15   \u6240\u6709\u7684session\u4FE1\u606F\u5B58\u5230 1\u8FD9\u4E2A\u6570\u636E\u5E93\u4E2D
spring.redis.database=0
# Redis\u670D\u52A1\u5668\u5730\u5740 \u534E\u4E3A\u4E91\u670D\u52A1\u5668\u5185\u90E8\u5730\u5740
spring.redis.host=localhost
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u7AEF\u53E3
spring.redis.port=6379
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u5BC6\u7801\uFF08\u9ED8\u8BA4\u4E3A\u7A7A\uFF09
spring.redis.password=123456
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4
spring.redis.timeout=5000
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.lettuce.pool.max-active=2000
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.lettuce.pool.max-wait=3000
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.redis.lettuce.pool.max-idle=50
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.lettuce.pool.min-idle=5
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.redis.lettuce.shutdown-timeout=100
spring.redis.lettuce.pool.testOnBorrow=true
spring.redis.lettuce.pool.testOnReturn=true
spring.redis.lettuce.pool.testWhileIdle=true
# Alibaba OSS
# \u963F\u91CC\u4E91OSS\u914D\u7F6E\u670D\u52A1\u5730\u5740
aliyun.oss.endpoint=oss-cn-hangzhou.aliyuncs.com
# \u963F\u91CC\u4E91OSS\u5B58\u50A8\u6876
aliyun.oss.bucket=ryytn-scp-test
# \u963F\u91CC\u4E91OSS access key
aliyun.oss.ak=LTAI5t6CNDYFiKjRcukxrokZ
# \u963F\u91CC\u4E91OSS secret key
aliyun.oss.sk=******************************
# Swagger\u5F00\u5173\uFF0C\u751F\u4EA7\u73AF\u5883\u5173\u95ED
springfox.documentation.enabled:true
springfox.documentation.auto-startup:true
springfox.documentation.swagger-ui.enabled:true
# Scheduled\u914D\u7F6E
spring.task.scheduling.pool.size=20
# \u6D88\u606F\u961F\u5217\u914D\u7F6E
mq.type=2
mq.group.id=group-1
# \u963F\u91CC\u4E91DATAQ\u670D\u52A1\u5730\u5740
aliyun.dataq.endpoint=http://47.98.52.48
# \u963F\u91CC\u4E91DATAQ APPCODE
aliyun.dataq.appcode=ryytn_dev
# \u963F\u91CC\u4E91DATAQ APPKEY
aliyun.dataq.ak=4GRCbBYoqZ8BepPK
# \u963F\u91CC\u4E91DATAQ APPSECRETEKEY
aliyun.dataq.sk=bDSNmxiDrq1gAmUeWofvW1wCjM8e7HhH
# \u963F\u91CC\u4E91DATAQ \u8D85\u65F6\u65F6\u95F4
aliyun.dataq.timeout=60000
# \u963F\u91CC\u4E91DATAQ \u6700\u5927\u8FDE\u63A5\u6570
aliyun.dataq.maxconnection=30
# \u6570\u636E\u63A2\u7D22\u5FAE\u5E94\u7528sdk
dtb.boot.dtboost.access-id=4GRCbBYoqZ8BepPK
dtb.boot.dtboost.access-key=bDSNmxiDrq1gAmUeWofvW1wCjM8e7HhH
dtb.boot.dtboost.dataindustry-endpoint=http://dataq.aliyuncs.com/dataexplore
dataq.scheduler.userId=1656878636427662
dataq.scheduler.tenantCode=tenant_1656878636427662
dataq.scheduler.workspaceCode=znyy_test
datax.csEngine.code=ry_sys_adp
datax.table.schema=cdop_sys
dataq.scheduler.constantCode=ryytn_s
# \u8DE8\u57DF\u767D\u540D\u5355\u914D\u7F6E
cors.allow.orign=*

gei.pool.core-ratio=0.25
gei.pool.max-ratio=0.25
gei.pool.keep-alive=10
gei.pool.keep-alive-unit=SECONDS
gei.pool.queue-size=32
gei.pool.queue=LINKED
gei.pool.rejected=CALLER
gei.storage.type=oss
gei.storage.oss.ak=LTAI5t7En3RhaWVjrPnnX2Eg
gei.storage.oss.sk=******************************
gei.storage.oss.endpoint=oss-cn-beijing.aliyuncs.com
gei.storage.oss.bucket=gq-cainiao-static
gei.datasource.enable=true

task-manage.access-key=LTAI5tSmrDXTd8EGBtVTbbtQ
task-manage.secret-key=******************************
task-manage.region=cn-hangzhou
task-manage.env=PROD
task-manage.project-id=83107
task-manage.checker.initial-delay=10
task-manage.checker.delay=120