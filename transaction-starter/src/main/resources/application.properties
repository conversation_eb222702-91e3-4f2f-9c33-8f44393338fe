server.port=7001
spring.profiles.active=dataq
spring.messages.basename=i18n/message
spring.messages.encoding=UTF-8
spring.messages.use-code-as-default-message=true
logging.config=classpath:logback.xml
#\u542F\u7528\u6587\u4EF6\u4E0A\u4F20
spring.http.multipart.enabled=true 
#\u6587\u4EF6\u5927\u4E8E\u8BE5\u9608\u503C\u65F6\uFF0C\u5C06\u5199\u5165\u78C1\u76D8\uFF0C\u652F\u6301KB/MB\u5355\u4F4D
spring.http.multipart.file-size-threshold=0 
#\u81EA\u5B9A\u4E49\u4E34\u65F6\u8DEF\u5F84
spring.http.multipart.location=
#\u5355\u4E2A\u6587\u4EF6\u6700\u5927\u9650\u5236
spring.servlet.multipart.max-file-size=1000MB
#\u63D0\u4EA4\u7684\u5168\u90E8\u6587\u4EF6\u6700\u5927\u9650\u5236
spring.servlet.multipart.max-request-size=1000MB
tmp.file.location=/tmp/transaction/
#\u963F\u91CC\u5B89\u5168\u5305\u6846\u67B6\u589E\u5F3A\u80FD\u529B\uFF0C\u9ED8\u8BA4\u5173\u95ED
spring.security.csrf.enabled=false
spring.security.xss.enabled=false
spring.security.json.enabled=false
management.endpoints.web.exposure.include=health