package cn.aliyun.ryytn.starter;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 *
 * @Description 服务容器入口
 * <AUTHOR>
 * @date 2023年9月19日 下午2:12:04
 */
@SpringBootApplication
@ComponentScan(basePackages = {"cn.aliyun.ryytn"})
//@MapperScan({"cn.aliyun.ryytn.**.dao"})
//@EnableScheduling
@EnableAsync
public class Application
{
    public static void main(String[] args)
    {
        SpringApplication.run(Application.class, args);
    }
}
