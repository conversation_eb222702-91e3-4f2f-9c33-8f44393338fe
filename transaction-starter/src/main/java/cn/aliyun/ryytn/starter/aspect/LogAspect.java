package cn.aliyun.ryytn.starter.aspect;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description Controller和Service方法出入口日志
 * @date 2023/10/12 10:29
 */
@Slf4j
@Aspect
@Component
public class LogAspect {
    private static final int LOG_LIMIT = 500;

    /**
     * @Description 切所有Controller的公共方法
     * <AUTHOR>
     * @date 2023年10月10日 17:02
     */
    @Pointcut("execution(public * cn.aliyun.ryytn..controller..*Controller.*(..))")
    public void controllerLogAspect() {
    }

    /**
     * @Description 切所有消费者的公共方法
     * <AUTHOR>
     * @date 2023年10月10日 17:02
     */
    @Pointcut("execution(public * cn.aliyun.ryytn..consumer..*Consumer.*(..))")
    public void consumerLogAspect() {
    }

    /**
     * @Description 切所有任务的公共方法
     * <AUTHOR>
     * @date 2023年10月10日 17:02
     */
    @Pointcut("execution(public * cn.aliyun.ryytn..task..*ServiceImpl.*(..))")
    public void taskLogAspect() {
    }

    /**
     * @Description 切所有Handler的公共方法
     * <AUTHOR>
     * @date 2023年10月10日 17:02
     */
    @Pointcut("execution(public * cn.aliyun.ryytn..handler..*Handler.*(..))")
    public void handlerLogAspect() {
    }

    @Pointcut(value = "execution(* cn.aliyun.ryytn.modules..*Service*.*(..))")
    public void serviceLogAspect() {
    }

    @Pointcut("controllerLogAspect() || consumerLogAspect() || taskLogAspect() || handlerLogAspect() || serviceLogAspect()")
    public void logAspect() {
    }

    /**
     * 方法执行
     */
    @Around("logAspect()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        MethodSignature msne = (MethodSignature) pjp.getSignature();
        String methodName = msne.getMethod().getName();
        String className = msne.getDeclaringTypeName();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            // 入口日志
//        log.info("Enter className:{},methodName:{},param:{}", className, methodName, pjp.getArgs());
//        log.info("Enter {}.{}", className, methodName);
            Object result = pjp.proceed(pjp.getArgs());
            stopWatch.stop();
            // 出口日志
            log.info("Exit className:[{}],methodName:[{}],param:[{}],spend-ms:[{}]",
                    className, methodName, truncateContent(pjp.getArgs()), stopWatch.getTotalTimeMillis());
            return result;
        } catch (Throwable e) {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            log.info("Exception className:[{}],methodName:[{}],param:[{}],spend-ms:[{}],Exception:[{}]",
                    className, methodName, truncateContent(pjp.getArgs()), stopWatch.getTotalTimeMillis(), e.getMessage());
            throw e;
        }
    }


    @Pointcut("execution(public * cn.aliyun.ryytn.common.dataq.impl.DataqServiceImpl.invoke(..))")
    public void dataqLogAspect() {
    }

    @Around("dataqLogAspect()")
    public Object dataqAround(ProceedingJoinPoint pjp) throws Throwable {
        Object[] args = pjp.getArgs();
        Object path = args[1];

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            Object result = pjp.proceed(pjp.getArgs());
            stopWatch.stop();
            log.info("Exit dataq httpMethod:[{}],spend-ms:[{}]", path, stopWatch.getTotalTimeMillis());
            return result;
        } catch (Throwable e) {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            log.info("Exception dataq httpMethod:[{}],spend-ms:[{}],Exception:[[{}]]",
                    path, stopWatch.getTotalTimeMillis(), e.getMessage());
            throw e;
        }
    }


    private String truncateContent(Object object) {
        if (Objects.isNull(object)) {
            return null;
        }

        String content;
        try {
            content = JSON.toJSONString(object);
        } catch (Exception e) {
            content = object.toString();
        }
        if (content.length() > LOG_LIMIT) {
            return content.substring(0, LOG_LIMIT) + "...";
        }
        return content;
    }

//curl --location --request POST 'http://localhost:7001/actuator/loggers/cn.aliyun.ryytn.starter.aspect.LogAspect' \
//--header 'Content-Type: application/json' \
//--header 'Accept: */*' \
//--header 'Host: localhost:7001' \
//--data-raw '{
//       "configuredLevel": "DEBUG"
//   }'
}
