package cn.aliyun.ryytn.starter.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import cn.aliyun.ryytn.starter.interceptor.Interceptor;

/**
 *
 * @Description 过滤器配置类
 * <AUTHOR>
 * @date 2023年9月19日 下午2:12:23
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer
{
    @Bean
    public Interceptor getInterceptor()
    {
        return new Interceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry interceptorRegistry)
    {
        interceptorRegistry.addInterceptor(getInterceptor()).order(Ordered.LOWEST_PRECEDENCE);
    }
}
