package cn.aliyun.ryytn.starter.config;

import java.util.Collection;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;

import com.p6spy.engine.logging.Category;
import com.p6spy.engine.spy.appender.FormattedLogger;

import cn.aliyun.ryytn.common.utils.string.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description p6spy日志配置
 * <AUTHOR>
 * @date 2023/9/28 18:57
 */
@Slf4j
public class SpyLogger extends FormattedLogger
{
    private static final String LONG_SQL_FORMATMESSAGE = "Long sql length: {} ,sql content: {} ...";

    @Override
    public void logSQL(int connectionId, String now, long elapsed, Category category, String prepared, String sql, String url)
    {
        // 如果是超长sql，不做sql格式化
        String msg = null;
        int sqlLength = StringUtils.length(sql);
        if (sqlLength < 2048)
        {
            msg = strategy.formatMessage(connectionId, now, elapsed, category.toString(), prepared, sql, url);
        }
        else
        {
            msg = StringUtils.format(LONG_SQL_FORMATMESSAGE, sqlLength, StringUtils.substring(sql, 0, 1024));
        }

        String traceName = stackTraceName();
        if (StringUtils.isNotBlank(traceName))
        {
            msg = String.join(StringUtils.TRACENAME_SEPARATOR, traceName, msg);
        }

        if (Category.ERROR.equals(category))
        {
            log.error(msg);
        }
        else if (Category.WARN.equals(category))
        {
            log.warn(msg);
        }
        else if (Category.DEBUG.equals(category))
        {
            log.debug(msg);
        }
        else
        {
            log.info(msg);
        }
    }

    @Override
    public void logException(Exception e)
    {
        log.info(StringUtils.trimToEmpty(stackTraceName()), e);
    }

    @Override
    public void logText(String text)
    {
        log.info(text);
    }

    @Override
    public boolean isCategoryEnabled(Category category)
    {
        if (Category.ERROR.equals(category))
        {
            return log.isErrorEnabled();
        }
        else if (Category.WARN.equals(category))
        {
            return log.isWarnEnabled();
        }
        else if (Category.DEBUG.equals(category))
        {
            return log.isDebugEnabled();
        }
        else
        {
            return log.isInfoEnabled();
        }
    }


    private String stackTraceName()
    {
        Map<Thread, StackTraceElement[]> allStackTraces = Thread.currentThread().getAllStackTraces();

        if (MapUtils.isNotEmpty(allStackTraces))
        {
            Collection<StackTraceElement[]> stackTraceElements = allStackTraces.values();

            if (CollectionUtils.isNotEmpty(stackTraceElements))
            {
                for (StackTraceElement[] traceElements : stackTraceElements)
                {
                    if (ArrayUtils.isNotEmpty(traceElements))
                    {
                        for (StackTraceElement element : traceElements)
                        {
                            String className = element.getClassName();
                            String methodName = element.getMethodName();
                            String fileName = element.getFileName();
                            int lineNumber = element.getLineNumber();

                            return new StringBuilder(className).append(".")
                                .append(methodName).append("(")
                                .append(fileName).append(":")
                                .append(lineNumber).append(")")
                                .toString();
                        }
                    }
                }
            }

        }

        return StringUtils.EMPTY;
    }
}
