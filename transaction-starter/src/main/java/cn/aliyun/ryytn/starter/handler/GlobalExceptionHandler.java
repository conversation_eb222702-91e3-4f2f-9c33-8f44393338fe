package cn.aliyun.ryytn.starter.handler;

import javax.servlet.http.HttpServletRequest;

import cn.aliyun.ryytn.modules.inv.common.exception.AppException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.exception.BaseException;
import com.google.common.base.Throwables;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * @Description 全局异常处理
 * <AUTHOR>
 * @date 2023年9月19日 下午2:06:22
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler
{
    /**
     *
     * @Description 处理参数验证异常 MethodArgumentNotValidException
     * @param request
     * @param ex
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月19日 下午2:06:40
     */
    @SneakyThrows
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultInfo<?> handleValidException(HttpServletRequest request, MethodArgumentNotValidException ex)
    {
        log.error("handleValidException:{}", Throwables.getStackTraceAsString(ex));
        return ResultInfo.fail(ErrorCodeConstants.FAIL_PARAM_INVALID);
    }

    /**
     *
     * @Description 处理自定义异常:BaseException
     * @param request
     * @param e
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月19日 下午2:06:55
     */
    @ExceptionHandler(BaseException.class)
    public ResultInfo<?> handleAbstractException(HttpServletRequest request, BaseException e)
    {
        log.error("handleAbstractException:{}", Throwables.getStackTraceAsString(e));
        return ResultInfo.fail(e);
    }

    /**
     *
     * @Description 兜底处理：Throwable
     * @param request
     * @param throwable
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月19日 下午2:07:09
     */
    @ExceptionHandler(value = Throwable.class)
    public ResultInfo<?> handleThrowable(HttpServletRequest request, Throwable throwable)
    {
        log.error("handleThrowable:{}", Throwables.getStackTraceAsString(throwable));
        return ResultInfo.fail();
    }

    @ExceptionHandler(value = AppException.class)
    public ResultInfo<?> handleThrowable(HttpServletRequest request, AppException exception)
    {
        log.error("handleThrowable", exception);
        return ResultInfo.fail(0, exception.getDetailedMessage());
    }
}
