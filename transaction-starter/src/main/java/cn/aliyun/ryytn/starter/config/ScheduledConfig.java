package cn.aliyun.ryytn.starter.config;

import java.util.Properties;
import javax.sql.DataSource;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

/**
 * @Description 调度配置
 * <AUTHOR>
 * @date 2023/9/25 17:20
 */
@Configuration
//@EnableScheduling
public class ScheduledConfig
{
//    private int corePoolSize;
//
//    private int maxPoolSize;
//
//    private int queueCapacity;
//
//    private int keepAliveSeconds;
//
//    private String threadNamePrefix = "task-executor-";
//
//    @Bean
//    public ThreadPoolTaskExecutor taskExecutor()
//    {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        executor.setCorePoolSize(10);
//        executor.setMaxPoolSize(50);
//        executor.setQueueCapacity(1000);
//        executor.setKeepAliveSeconds(60);
//        executor.setThreadNamePrefix(threadNamePrefix);
//        return executor;
//    }

    @Bean
    public SchedulerFactoryBean schedulerFactoryBean(DataSource dataSource)
    {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setDataSource(dataSource);

        // quartz参数
        Properties prop = new Properties();
        prop.put("org.quartz.scheduler.instanceName", "transaction");
        prop.put("org.quartz.scheduler.instanceId", "AUTO");
        // 线程池配置
        prop.put("org.quartz.threadPool.class", "org.quartz.simpl.SimpleThreadPool");
        prop.put("org.quartz.threadPool.threadCount", "20");
        prop.put("org.quartz.threadPool.threadPriority", "5");
        // 是否设置调度器线程为守护线程
//        prop.put("org.quartz.scheduler.makeSchedulerThreadDaemon", true);
        // JobStore配置
        // springboot
        // 2.5.6以上版本需要设置为org.springframework.scheduling.quartz.LocalDataSourceJobStore
        // prop.put("org.quartz.jobStore.class",
        // "org.quartz.impl.jdbcjobstore.JobStoreTX");
        //PostgreSQL数据库，需要添加此配置
        prop.put("org.quartz.jobStore.driverDelegateClass", "org.quartz.impl.jdbcjobstore.PostgreSQLDelegate");
        prop.put("org.quartz.jobStore.class", "org.springframework.scheduling.quartz.LocalDataSourceJobStore");
        // 集群配置
        prop.put("org.quartz.jobStore.isClustered", "true");
        prop.put("org.quartz.jobStore.clusterCheckinInterval", "15000");
        prop.put("org.quartz.jobStore.maxMisfiresToHandleAtATime", "1");
        // 配置为true启动会报错：不能在事务交易过程中改变事物交易隔绝等级。
        prop.put("org.quartz.jobStore.txIsolationLevelSerializable", "false");

        // sqlserver 启用
        // prop.put("org.quartz.jobStore.selectWithLockSQL", "SELECT * FROM
        // {0}LOCKS UPDLOCK WHERE LOCK_NAME = ?");
        prop.put("org.quartz.jobStore.misfireThreshold", "12000");
        prop.put("org.quartz.jobStore.tablePrefix", "QRTZ_");
        prop.put("org.quartz.scheduler.instanceName", "DefaultQuartzScheduler");
        prop.put("org.quartz.scheduler.instanceid", "AUTO");
        factory.setQuartzProperties(prop);

        factory.setSchedulerName("transaction");
        // 延时启动
        factory.setStartupDelay(1);
        factory.setApplicationContextSchedulerContextKey("applicationContextKey");
        // 可选，QuartzScheduler
        // 启动时更新己存在的Job，这样就不用每次修改targetObject后删除qrtz_job_details表对应记录了
        factory.setOverwriteExistingJobs(true);
        // 设置自动启动，默认为true
        factory.setAutoStartup(true);

        return factory;
    }
}
