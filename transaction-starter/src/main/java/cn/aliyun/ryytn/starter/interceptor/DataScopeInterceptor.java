package cn.aliyun.ryytn.starter.interceptor;//package cn.aliyun.ryytn.starter.interceptor;
//
//
//import java.lang.reflect.Method;
//import java.util.Objects;
//import java.util.Properties;
//import java.util.stream.Collectors;
//
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.ArrayUtils;
//import org.apache.ibatis.cache.CacheKey;
//import org.apache.ibatis.executor.Executor;
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.mapping.SqlSource;
//import org.apache.ibatis.plugin.Interceptor;
//import org.apache.ibatis.plugin.Intercepts;
//import org.apache.ibatis.plugin.Invocation;
//import org.apache.ibatis.plugin.Plugin;
//import org.apache.ibatis.plugin.Signature;
//import org.apache.ibatis.session.ResultHandler;
//import org.apache.ibatis.session.RowBounds;
//import org.springframework.stereotype.Component;
//
//import cn.aliyun.ryytn.common.annotation.DataScope;
//import cn.aliyun.ryytn.common.entity.Session;
//import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
//import cn.aliyun.ryytn.common.utils.string.StringUtils;
//import lombok.extern.slf4j.Slf4j;
//
//@Slf4j
//@Component
//@Intercepts({
//    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
//    @Signature(type = Executor.class, method = "query",
//        args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class})
//})
//public class DataScopeInterceptor implements Interceptor
//{
//    private static final String DATASCOPE_TEMPLATE = "originalSql.{}=ANY(STRING_TO_ARRAY('{}',','))";
//
//    private static final String TARGET_SQL_TEMPLATE = "SELECT * FROM ({}) originalSql WHERE {}";
//
//    private static final String ALL_DATASCOPE = "1 = 1";
//
//    private static final String NO_DATASCOPE = "1 = 0";
//
//    private static final String AND = " AND ";
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable
//    {
//        Object[] args = invocation.getArgs();
//        if (ArrayUtils.isEmpty(args))
//        {
//            return invocation.proceed();
//        }
//
//        MappedStatement mappedStatement = (MappedStatement) args[0];
//
//        // id为执行的mapper方法的全路径名，如com.cq.UserMapper.insertUser， 便于后续使用反射
//        String id = mappedStatement.getId();
//        String className = StringUtils.substringBeforeLast(id, StringUtils.POINT_SEPARATOR);
//        String methodName = StringUtils.substringAfterLast(id, StringUtils.POINT_SEPARATOR);
//
//        Class<?> clazz = Class.forName(className);
//        // 遍历Dao接口方法，mybatis不支持重载，直接根据方法名过滤
//        if (Objects.isNull(clazz.getDeclaredMethods()))
//        {
//            return invocation.proceed();
//        }
//
//        DataScope dataScope = null;
//        for (Method method : clazz.getDeclaredMethods())
//        {
//            if (StringUtils.equals(methodName, method.getName()))
//            {
//                // 判断方法上是否带有自定义@InterceptAnnotation注解
//                dataScope = method.getAnnotation(DataScope.class);
//            }
//        }
//
//        Session session = ServiceContextUtils.currentSession();
//        if (Objects.isNull(dataScope) || Objects.isNull(session) || Objects.isNull(session.getAccount()))
//        {
//            return invocation.proceed();
//        }
//
//        StringBuilder sqlString = new StringBuilder(ALL_DATASCOPE);
//
//        boolean needAppend = true;
//        if (StringUtils.isNotBlank(dataScope.category()))
//        {
//            if (CollectionUtils.isNotEmpty(session.getAccount().getCategoryIdList()))
//            {
//                String categoryCodes = session.getAccount().getCategoryIdList().stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//                sqlString.append(AND).append(StringUtils.format(DATASCOPE_TEMPLATE, dataScope.category(), categoryCodes));
//            }
//            else
//            {
//                sqlString = new StringBuilder(NO_DATASCOPE);
//                needAppend = false;
//            }
//        }
//
//        if (needAppend && StringUtils.isNotBlank(dataScope.channel()))
//        {
//            if (CollectionUtils.isNotEmpty(session.getAccount().getChannelIdList()))
//            {
//                String channelCodes = session.getAccount().getChannelIdList().stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//                sqlString.append(AND).append(StringUtils.format(DATASCOPE_TEMPLATE, dataScope.channel(), channelCodes));
//            }
//            else
//            {
//                sqlString = new StringBuilder(NO_DATASCOPE);
//                needAppend = false;
//            }
//        }
//        if (needAppend && StringUtils.isNotBlank(dataScope.warehouse()))
//        {
//
//            if (CollectionUtils.isNotEmpty(session.getAccount().getDepositoryIdList()))
//            {
//                String warehouseCodes = session.getAccount().getDepositoryIdList().stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//                sqlString.append(AND).append(StringUtils.format(DATASCOPE_TEMPLATE, dataScope.warehouse(), warehouseCodes));
//            }
//            else
//            {
//                sqlString = new StringBuilder(NO_DATASCOPE);
//                needAppend = false;
//            }
//        }
//
//        Object parameter = invocation.getArgs()[1];
//        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
//        String originalSql = boundSql.getSql();
//        String targetSql = StringUtils.format(TARGET_SQL_TEMPLATE, StringUtils.trim(originalSql), sqlString.toString());
//
//        BoundSql newBoundSql = new BoundSql(mappedStatement.getConfiguration(), targetSql, boundSql.getParameterMappings(), parameter);
//        // 把新的查询放到statement里
//        MappedStatement newMs = copyFromMappedStatement(mappedStatement, new BoundSqlSqlSource(newBoundSql));
//        args[0] = newMs;
//
//        return invocation.proceed();
//    }
//
//    @Override
//    public Object plugin(Object target)
//    {
//        return Plugin.wrap(target, this);
//    }
//
//    @Override
//    public void setProperties(Properties properties)
//    {
//    }
//
//    private MappedStatement copyFromMappedStatement(MappedStatement ms, SqlSource newSqlSource)
//    {
//        MappedStatement.Builder builder = new MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());
//        builder.resource(ms.getResource());
//        builder.fetchSize(ms.getFetchSize());
//        builder.statementType(ms.getStatementType());
//        builder.keyGenerator(ms.getKeyGenerator());
//        if (ms.getKeyProperties() != null && ms.getKeyProperties().length > 0)
//        {
//            builder.keyProperty(ms.getKeyProperties()[0]);
//        }
//        builder.timeout(ms.getTimeout());
//        builder.parameterMap(ms.getParameterMap());
//        builder.resultMaps(ms.getResultMaps());
//        builder.resultSetType(ms.getResultSetType());
//        builder.cache(ms.getCache());
//        builder.flushCacheRequired(ms.isFlushCacheRequired());
//        builder.useCache(ms.isUseCache());
//        return builder.build();
//    }
//
//    private static class BoundSqlSqlSource implements SqlSource
//    {
//        private BoundSql boundSql;
//
//        public BoundSqlSqlSource(BoundSql boundSql)
//        {
//            this.boundSql = boundSql;
//        }
//
//        @Override
//        public BoundSql getBoundSql(Object parameterObject)
//        {
//            return boundSql;
//        }
//    }
//}
