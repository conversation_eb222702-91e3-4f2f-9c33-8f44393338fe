package cn.aliyun.ryytn.starter.aspect;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.Button;
import cn.aliyun.ryytn.common.entity.Page;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 权限控制切面
 * <AUTHOR>
 * @date 2023/10/10 16:58
 */
@Slf4j
@Aspect
@Component
public class PermissionAspect
{
    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 切所有加了RequiresPermissions注解的方法
     * <AUTHOR>
     * @date 2023年10月10日 17:02
     */
    @Pointcut("@annotation(org.apache.shiro.authz.annotation.RequiresPermissions)")
    public void permissionAspect()
    {
    }

    /**
     * 方法执行
     */
    @Around("permissionAspect()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable
    {
        // 获取会话绑定的账号
        Account account = ServiceContextUtils.currentSession().getAccount();
        // 只切加了RequiresPermissions注解的方法，所以校验登录，如果会话未绑定账号，返回失败
        if (Objects.isNull(account))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_NEEDLOGIN_ERROR);
        }

        // 超级管理员直接放行
        if (account.isAdmin())
        {
            return pjp.proceed(pjp.getArgs());
        }

        MethodSignature msne = (MethodSignature) pjp.getSignature();
        // 根据当前方法获得注解信息
        RequiresPermissions requiresPermissions = msne.getMethod().getAnnotation(RequiresPermissions.class);
        // 获取方法注解的权限码数组
        String[] permissionsArray = requiresPermissions.value();
        // 权限码数组关系，是并还是或
        Logical logical = requiresPermissions.logical();
        // 如果方法不依赖任何权限码，透传
        if (ArrayUtils.isEmpty(permissionsArray))
        {
            return pjp.proceed(pjp.getArgs());
        }

        // 初始化用户已被授权的权限码集合
        Set<String> accountPermission = new HashSet<String>();
        if (CollectionUtils.isNotEmpty(account.getPageList()))
        {
            Set<String> pagePermission = account.getPageList().stream().map(Page::getPermission).collect(Collectors.toSet());
            accountPermission.addAll(pagePermission);
            pagePermission.clear();
        }
        if (CollectionUtils.isNotEmpty(account.getButtonList()))
        {
            Set<String> buttonPermission = account.getButtonList().stream().map(Button::getPermission).collect(Collectors.toSet());
            accountPermission.addAll(buttonPermission);
            buttonPermission.clear();
        }

        // 如果用户已被授权的权限码集合为空集合，返回失败
        if (CollectionUtils.isEmpty(accountPermission))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_NOAUTH_ERROR);
        }

        if (Logical.OR.equals(logical))
        {
            // 循环校验方法依赖的权限是否已被授权
            for (String permission : permissionsArray)
            {
                // 依赖多个权限，只要有一个权限已授权，就放行
                if (accountPermission.contains(permission))
                {
                    break;
                }
            }
        }
        else
        {
            // 循环校验方法依赖的权限是否已被授权
            for (String permission : permissionsArray)
            {
                // 依赖多个权限，只要有一个权限未被授权，就返回失败
                if (!accountPermission.contains(permission))
                {
                    throw new ServiceException(ErrorCodeConstants.FAIL_NOAUTH_ERROR);
                }
            }
        }

        return pjp.proceed(pjp.getArgs());
    }
}
