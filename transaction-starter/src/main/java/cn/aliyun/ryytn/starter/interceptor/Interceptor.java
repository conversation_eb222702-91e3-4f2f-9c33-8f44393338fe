package cn.aliyun.ryytn.starter.interceptor;

import java.util.*;
import javax.servlet.http.<PERSON>ie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.Session;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 会话拦截器
 * <AUTHOR>
 * @date 2023年9月19日 下午2:13:40
 */
@Slf4j

public class Interceptor implements HandlerInterceptor
{
    @Autowired
    private RedisUtils redisUtils;

    private int timeOut;

    public int getTimeOut()
    {
        return timeOut;
    }

    public void setTimeOut(int timeOut)
    {
        this.timeOut = timeOut;
    }

    public Interceptor()
    {
        super();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        Map<String, String> cookies = null;
        if (null != request.getCookies() && request.getCookies().length > 0)
        {
            cookies = new HashMap<String, String>();
            for (Cookie cookie : request.getCookies())
            {
                cookies.put(cookie.getName(), cookie.getValue());
            }
        }

        String sessionId = null;
        String xToken = "X-Token";
        // 优先从Cookie中获取SessionId
        if (Objects.nonNull(cookies) && cookies.containsKey(xToken) && StringUtils.isNotBlank(cookies.get(xToken)))
        {
            sessionId = cookies.get(xToken);
        }

        // 如果Cookie中获取不到，则从Header中获取
        if (StringUtils.isBlank(sessionId))
        {
            sessionId = request.getHeader(xToken);
            setCookie(response, xToken, sessionId);
        }

        // 如果Cookie和Header中都没有，则随机生成
        if (StringUtils.isBlank(sessionId))
        {
            sessionId = SeqUtils.getRandomUid();
            setCookie(response, xToken, sessionId);
        }

        // 从缓存中获取会话信息
        String sessionKey = StringUtils.format(CommonConstants.SESSION_CACHE_KEY, sessionId);
        Session session = (Session) redisUtils.get(sessionKey);

        // 如果缓存中没有该会话，则创建该会话并写入缓存，此时会话未绑定账号登录信息，在后续的权限控制切面控制是否可以访问接口
        if (Objects.isNull(session))
        {
            Locale locale = request.getLocale();
            session = new Session(sessionId, locale);
            redisUtils.set(sessionKey, session, CommonConstants.SESSION_EXPIRE_TIME);
        }
        if(null != session.getAccount()){
            Date now = new Date();
            long expireDiff = session.getExpireTime().getTime() - now.getTime();
            if(expireDiff > 0 && expireDiff/1000  < (CommonConstants.SESSION_EXPIRE_TIME-300)){
                //如果不过期，5分钟延期一次
                session.setExpireTime(DateUtils.addSeconds(now,Integer.parseInt(String.valueOf(CommonConstants.SESSION_EXPIRE_TIME))));
                redisUtils.set(sessionKey, session, CommonConstants.SESSION_EXPIRE_TIME);
            }
        }
        // 设置会话上下文
        ServiceContextUtils.setSession(session);

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception
    {
        ServiceContextUtils.remove();
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception
    {
    }

    /**
     *
     * @Description 框架使用的设置cookie方法
     * @param name
     * @param value
     */
    private void setCookie(HttpServletResponse response, String name, String value)
    {
        Cookie cookie = new Cookie(name, value);
        cookie.setHttpOnly(true);
        cookie.setPath("/");
        cookie.setMaxAge(-1);
        response.addCookie(cookie);
    }
}
