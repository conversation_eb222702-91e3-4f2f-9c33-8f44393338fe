package cn.aliyun.ryytn.starter.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * @Description 跨站攻击防护过滤器 
 * <AUTHOR>
 * @date 2023年9月19日 下午2:13:12
 */
@Order(1)
@Component
@Slf4j
public class XssFilter implements Filter
{

    @Override
    public void init(FilterConfig filterConfig)
    {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws IOException, ServletException
    {
        filterChain.doFilter(request, response);

    }

    @Override
    public void destroy()
    {

    }

}
