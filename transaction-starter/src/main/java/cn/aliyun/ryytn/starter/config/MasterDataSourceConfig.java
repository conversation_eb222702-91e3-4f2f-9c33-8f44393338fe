package cn.aliyun.ryytn.starter.config;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.alibaba.druid.pool.DruidDataSource;

@Configuration
@MapperScan(basePackages = "cn.aliyun.ryytn.**.dao", sqlSessionTemplateRef = "masterSqlSessionTemplate")
public class MasterDataSourceConfig
{
    @Value("${spring.datasource.druid.driver-class-name}")
    private String driverClassName;

    @Value("${spring.datasource.druid.url}")
    private String url;

    @Value("${spring.datasource.druid.username}")
    private String username;

    @Value("${spring.datasource.druid.password}")
    private String password;

    @Value("${spring.datasource.druid.initial-size:5}")
    private int initialSize;

    @Value("${spring.datasource.druid.min-idle:5}")
    private int minIdle;

    @Value("${spring.datasource.druid.max-active:100}")
    private int maxActive;

    @Value("${spring.datasource.druid.max-wait:60000}")
    private int maxWait;

    @Value("${spring.datasource.druid.startTime-between-eviction-runs-millis:60000}")
    private int startTimebetweenEvictionRunsMillis;

    @Value("${spring.datasource.druid.min-evictable-idle-startTime-millis:300000}")
    private int minEvicatableIdleStartTimeMillis;

    @Value("${spring.datasource.druid.validation-query:SELECT 'x'}")
    private String validationQuery;

    @Value("${spring.datasource.druid.validation-query-timeout:60000}")
    private int validationQueryTimeout;

    @Value("${spring.datasource.druid.test-while-idle:true}")
    private boolean testWhileIdle;

    @Value("${spring.datasource.druid.test-on-borrow:false}")
    private boolean testOnBorrow;

    @Value("${spring.datasource.druid.test-on-return:false}")
    private boolean testOnReturn;

    @Value("${spring.datasource.druid.filters:stat}")
    private String filters;

    @Value("${mybatis.mapper-locations:classpath*:mapper/*Dao.xml}")
    private String mapperLocations;

    @Value("${mybatis.configuration.cache-enabled:true}")
    private boolean cacheEnabled;

    @Value("${mybatis.configuration.map-underscore-to-camel-case:true}")
    private boolean mapUnderscoreToCamelCase;

    @Value("${mybatis.type-handlers-package:cn.aliyun.ryytn.common.mybatis.handler}")
    private String handlersPackage;

    @Value("${spring.datasource.druid.keepAlive:false}")
    private boolean keepAlive;
    @Value("${spring.datasource.druid.keepAliveBetweenTimeMillis:60000}")
    private long keepAliveBetweenTimeMillis;

    @Bean(name = "masterDataSource")
    @Primary
    public DataSource dataSource() throws Exception
    {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(this.driverClassName);
        dataSource.setUrl(this.url);
        dataSource.setUsername(this.username);
        dataSource.setPassword(this.password);
        dataSource.setInitialSize(this.initialSize);
        dataSource.setMinIdle(this.minIdle);
        dataSource.setMaxActive(this.maxActive);
        dataSource.setMaxWait(this.maxWait);
        dataSource.setTimeBetweenEvictionRunsMillis(this.startTimebetweenEvictionRunsMillis);
        dataSource.setMinEvictableIdleTimeMillis(this.minEvicatableIdleStartTimeMillis);
        dataSource.setValidationQuery(this.validationQuery);
        dataSource.setValidationQueryTimeout(this.validationQueryTimeout);
        dataSource.setTestWhileIdle(this.testWhileIdle);
        dataSource.setTestOnBorrow(this.testOnBorrow);
        dataSource.setTestOnReturn(this.testOnReturn);
        dataSource.setFilters(this.filters);
        dataSource.setKeepAlive(this.keepAlive);
        dataSource.setKeepAliveBetweenTimeMillis(this.keepAliveBetweenTimeMillis);
        return dataSource;
    }

    @Bean(name = "masterSqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory(@Qualifier("masterDataSource") DataSource dataSource) throws Exception
    {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(this.mapperLocations));
        bean.setTypeHandlersPackage(this.handlersPackage);
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(this.mapUnderscoreToCamelCase);
        configuration.setCacheEnabled(this.cacheEnabled);
        bean.setConfiguration(configuration);
        return bean.getObject();
    }

    @Bean(name = "masterTransactionManager")
    @Primary
    public DataSourceTransactionManager transactionManager(@Qualifier("masterDataSource") DataSource dataSource)
    {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "masterSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("masterSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception
    {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
