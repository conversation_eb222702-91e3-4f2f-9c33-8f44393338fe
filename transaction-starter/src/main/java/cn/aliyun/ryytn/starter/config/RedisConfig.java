package cn.aliyun.ryytn.starter.config;

import java.time.Duration;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import lombok.extern.slf4j.Slf4j;


/**
 *
 * @Description Redis配置类
 * <AUTHOR>
 * @date 2023/9/21 16:09
 */
@Slf4j
@Configuration
@EnableCaching
public class RedisConfig
{
    /**
     * Redis服务IP
     */
    @Value("${spring.redis.host:127.0.0.1}")
    private String hostName;

    /**
     * Redis服务端口
     */
    @Value("${spring.redis.port:6379}")
    private int port;

    /**
     * 连接密码
     */
    @Value("${spring.redis.password:''}")
    private String password;

    /**
     * 连接超时时间
     */
    @Value("${spring.redis.timeout:3000}")
    private int timeout;

    /**
     * 最大空闲连接数
     */
    @Value("${spring.redis.lettuce.pool.max-idle:50}")
    private int maxIdle;

    /**
     * 最小空闲连接数
     */
    @Value("${spring.redis.lettuce.pool.min-idle:5}")
    private int minIdle;

    /**
     * 连接池最大阻塞等待时间（使用负值表示没有限制）
     */
    @Value("${spring.redis.lettuce.pool.max-wait:3000}")
    private long maxWaitMillis;

    /**
     * 连接池最大连接数（使用负值表示没有限制）
     */
    @Value("${spring.redis.lettuce.pool.max-active:2000}")
    private int maxActive;

    /**
     * 数据库编号
     */
    @Value("${spring.redis.database:0}")
    private int databaseId;

    @Value("${spring.redis.lettuce.pool.testWhileIdle:true}")
    private boolean testWhileIdle;

    @Value("${spring.redis.lettuce.pool.testOnBorrow:true}")
    private boolean testOnBorrow;

    @Value("${spring.redis.lettuce.pool.testOnReturn:true}")
    private boolean testOnReturn;

    /**
     *
     * @Description 连接工厂
     * @return LettuceConnectionFactory
     * <AUTHOR>
     * @date 2023年09月21日 16:21
     */
    @Bean
    public RedisConnectionFactory redisConnectionFactory()
    {
        RedisConfiguration redisConfiguration = new RedisStandaloneConfiguration(hostName, port);

        // 设置选用的数据库号码
        ((RedisStandaloneConfiguration) redisConfiguration).setDatabase(databaseId);

        // 设置 redis 数据库密码
        ((RedisStandaloneConfiguration) redisConfiguration).setPassword(password);

        // 连接池配置
        GenericObjectPoolConfig<Object> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);
        poolConfig.setMaxTotal(maxActive);
        poolConfig.setMaxWaitMillis(maxWaitMillis);
        poolConfig.setTestWhileIdle(testWhileIdle);
        poolConfig.setTestOnBorrow(testOnBorrow);
        poolConfig.setTestOnReturn(testOnReturn);

//        ClusterClientOptions clusterClientOptions = ClusterClientOptions.builder()
//            // 关闭ping
//            .pingBeforeActivateConnection(false)
//            .build();

//        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder =
//            LettucePoolingClientConfiguration.builder().clientOptions(clusterClientOptions).commandTimeout(Duration.ofMillis(timeout)).poolConfig(poolConfig);
//
//        LettucePoolingClientConfiguration lettucePoolingClientConfiguration = builder.build();
//
//        // 根据配置和客户端配置创建连接
//        RedisConnectionFactory factory = new LettuceConnectionFactory(redisConfiguration, lettucePoolingClientConfiguration);

        JedisClientConfiguration.JedisPoolingClientConfigurationBuilder builder =
            JedisClientConfiguration.builder().connectTimeout(Duration.ofMillis(timeout)).usePooling().poolConfig(poolConfig);

        JedisClientConfiguration jedisClientConfiguration = builder.build();
        RedisConnectionFactory factory = new JedisConnectionFactory((RedisStandaloneConfiguration) redisConfiguration, jedisClientConfiguration);

        return factory;
    }

    /**
     *
     * @Description pringboot2.x 使用LettuceConnectionFactory 代替 RedisConnectionFactory
     * application.yml配置基本信息后,springboot2.x  RedisAutoConfiguration能够自动装配
     * LettuceConnectionFactory 和 RedisConnectionFactory 及其 RedisTemplate
     * @param lettuceConnectionFactory
     * @return RedisTemplate<String, Object>
     * <AUTHOR>
     * @date 2023年09月21日 16:10
     */
    @Bean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory)
    {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);

        // key 的 String 序列化采用 StringRedisSerializer
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringRedisSerializer);
        redisTemplate.setHashKeySerializer(stringRedisSerializer);

        // value 的值序列化采用 genericJackson2JsonRedisSerializer
        GenericJackson2JsonRedisSerializer genericJackson2JsonRedisSerializer = new GenericJackson2JsonRedisSerializer();
        redisTemplate.setValueSerializer(genericJackson2JsonRedisSerializer);
        redisTemplate.setHashValueSerializer(genericJackson2JsonRedisSerializer);
        redisTemplate.afterPropertiesSet();

        return redisTemplate;
    }
}