package cn.aliyun.ryytn.starter.config;//package cn.aliyun.ryytn.starter.config;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Configuration;
//
//import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
//
//import cn.aliyun.ryytn.starter.interceptor.DataScopeInterceptor;
//
///**
// * @Description 数据权限拦截器配置类
// * <AUTHOR>
// * @date 2024/1/31 21:43
// */
//@Configuration
////@AutoConfigureAfter(PageHelperAutoConfiguration.class)
//public class DataScopeInterceptorConfig
//{
//    @Resource(name = "dataqSqlSessionFactory")
//    private SqlSessionFactory dataqSqlSessionFactory;
//
//    @Resource(name = "masterSqlSessionFactory")
//    private SqlSessionFactory masterSqlSessionFactory;
//
//    @Autowired
//    PageHelperAutoConfiguration pageHelperAutoConfiguration;
//
//    @PostConstruct
//    public void addDataScopeInterceptor()
//    {
//        DataScopeInterceptor dataScopeInterceptor = new DataScopeInterceptor();
//        dataqSqlSessionFactory.getConfiguration().addInterceptor(dataScopeInterceptor);
//        masterSqlSessionFactory.getConfiguration().addInterceptor(dataScopeInterceptor);
//    }
//}
