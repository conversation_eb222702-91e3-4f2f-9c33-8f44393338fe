package cn.aliyun.ryytn.starter.config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;

import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ApiKey;
import springfox.documentation.service.AuthorizationScope;
import springfox.documentation.service.Parameter;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.service.SecurityScheme;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * @Description Swagger配置
 * <AUTHOR>
 * @date 2023/9/22 16:38
 */
@Configuration
@EnableKnife4j
public class SwaggerConfig
{
    @Bean
    public Docket api()
    {
        return new Docket(DocumentationType.OAS_30)
            .apiInfo(apiInfo())
            .globalOperationParameters(this.getParameterList())
            .securitySchemes(securitySchemes())
            .securityContexts(securityContexts())
            .groupName("ryytn")
            .select()
            .apis(RequestHandlerSelectors.any())
            .paths(PathSelectors.any())
            .build();
    }

    private ApiInfo apiInfo()
    {
        return new ApiInfoBuilder()
            .title("认养一头牛")
            .description("Project Api document")
            .version("API V1.0")
            .build();
    }

    private List<SecurityScheme> securitySchemes()
    {
        return Collections.singletonList(new ApiKey("X-Token", "X-Token", "header"));
    }

    private List<SecurityContext> securityContexts()
    {
        return Collections.singletonList(SecurityContext.builder()
            .securityReferences(Collections.singletonList(SecurityReference.builder()
                .reference("X-Token")
                .scopes(new AuthorizationScope[]{new AuthorizationScope("global", "用户token")})
                .build()))
            .build());
    }

    /**
     * 添加head参数配置
     */
    private List<Parameter> getParameterList()
    {
        ParameterBuilder clientIdTicket = new ParameterBuilder();
        List<Parameter> parameterList = new ArrayList<Parameter>();
        clientIdTicket.name("X-Token").description("token令牌")
            .modelRef(new ModelRef("string"))
            .parameterType("header")
            .required(false).build();
        parameterList.add(clientIdTicket.build());

        return parameterList;

    }
}
