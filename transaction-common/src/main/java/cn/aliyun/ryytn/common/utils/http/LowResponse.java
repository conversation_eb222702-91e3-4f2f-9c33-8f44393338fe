/* ====================================================================
 Copyright (C) 2023, All rights reserved.
 Company ISS EDU. 
 Description com-iss-edu-common-base
 FileName LowResponse.java
 author Myl
 date 2023年6月20日 下午5:09:12
 Function List:
 ==================================================================== */
package cn.aliyun.ryytn.common.utils.http;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.exception.ServiceException;

/**
 * @Description Http工具返回低级响应对象
 * <AUTHOR>
 * @date 2023年6月20日 下午5:09:12
 */
public class LowResponse
{
    /**
     * 响应 Http状态码
     */
    private int httpCode;

    /**
     * Http Cookie
     */
    private String cookie;

    /**
     * 响应 消息头
     */
    private Map<String, List<String>> headers;

    /**
     * 响应 消息体
     */
    private byte[] body;

    public LowResponse(HttpURLConnection httpURLConnection)
    {
        InputStream inputStream = null;

        try
        {
            this.httpCode = httpURLConnection.getResponseCode();
            this.headers = httpURLConnection.getHeaderFields();

            if (MapUtils.isNotEmpty(this.headers) && CollectionUtils.isNotEmpty(this.headers.get("Set-Cookie")))
            {
                this.cookie = this.headers.get("Set-Cookie").get(0);
            }
            else
            {
                this.cookie = httpURLConnection.getRequestProperty("Cookie");
            }

            if (this.httpCode == 200)
            {
                inputStream = httpURLConnection.getInputStream();
            }
            else
            {
                inputStream = httpURLConnection.getErrorStream();
            }
            if (Objects.nonNull(inputStream))
            {
                this.body = IOUtils.toByteArray(inputStream);
            }
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_HTTP_ERROR, e);
        }
        finally
        {
            IOUtils.closeQuietly(inputStream);
        }
    }

    public int getHttpCode()
    {
        return httpCode;
    }

    public String getCookie()
    {
        return cookie;
    }

    public Map<String, List<String>> getHeaders()
    {
        return headers;
    }

    public String getContentType()
    {
        if (MapUtils.isNotEmpty(headers))
        {
            List<String> strings = headers.get("Content-Type");

            if (CollectionUtils.isNotEmpty(strings))
            {
                return strings.get(0);
            }
        }

        return null;
    }

    public byte[] getBody()
    {
        return body;
    }

    public String getBodyString()
    {
        if (ArrayUtils.isEmpty(body))
        {
            return StringUtils.EMPTY;
        }
        return new String(body, StandardCharsets.UTF_8);
    }

    public JSONObject getBodyJsonObj()
    {
        if (ArrayUtils.isEmpty(body))
        {
            return null;
        }
        Object jsonObject = JSON.parse(getBodyString());
        return jsonObject instanceof JSONObject ? (JSONObject) jsonObject : null;
    }

    public JSONArray getBodyJsonArr()
    {
        if (ArrayUtils.isEmpty(body))
        {
            return new JSONArray(0);
        }
        Object jsonArray = JSON.parse(getBodyString());
        return jsonArray instanceof JSONArray ? (JSONArray) jsonArray : new JSONArray(0);
    }

    public <T> T getBodyJsonObj(Class<T> clazz)
    {
        if (ArrayUtils.isEmpty(body))
        {
            return null;
        }
        return JSON.parseObject(getBodyString(), clazz);
    }

    public <T> List<T> getBodyJsonArr(Class<T> clazz)
    {
        if (ArrayUtils.isEmpty(body))
        {
            return Collections.emptyList();
        }
        return JSON.parseArray(getBodyString(), clazz);
    }

    @Override
    public String toString()
    {
        return new StringJoiner(", ", LowResponse.class.getSimpleName() + "[", "]")
            .add("httpCode='" + httpCode + "'")
            .add("body='" + getBodyString() + "'")
            .toString();
    }
}
