package cn.aliyun.ryytn.common.mq.impl;

import java.util.Objects;
import java.util.Observer;

import com.alibaba.fastjson.JSON;

import cn.aliyun.ryytn.common.mq.MqFactory;
import cn.aliyun.ryytn.common.mq.MqRecord;
import cn.aliyun.ryytn.common.mq.api.ConsumerService;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.spring.SpringUtil;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description Redis List消息队列消费者
 * 此实现类不支持多消费者消费
 * <AUTHOR>
 * @date 2023/10/13 16:33
 */
@Slf4j
public class RedisListConsumerServiceImpl extends ConsumerService
{
    public RedisListConsumerServiceImpl(String topic, String group)
    {
        super(topic, group);
    }

    /**
     *
     * @Description 增加观察者
     * @param observer
     * <AUTHOR>
     * @date 2023年10月13日 16:50
     */
    @Override
    public void addObservers(Observer observer)
    {
        this.addObserver(observer);
        MqFactory.listenerThreadPool.execute(new Runnable()
        {
            @Override
            public void run()
            {
                consume();
            }
        });
    }

    /**
     *
     * @Description 消息回执
     * @param mqRecord
     * <AUTHOR>
     * @date 2023年10月13日 16:50
     */
    @Override
    public void ack(MqRecord mqRecord)
    {
        return;
    }

    /**
     *
     * @Description 循环消费
     * <AUTHOR>
     * @date 2023年10月13日 17:28
     */
    private void consume()
    {
        RedisUtils redisUtils = SpringUtil.getBean(RedisUtils.class);
        while (true)
        {
            try
            {
                Thread.sleep(10000);
            }
            catch (Exception e)
            {

            }

            Object object = redisUtils.lPop(topic, 5);
            if (Objects.isNull(object))
            {
                continue;
            }
            MqRecord mqRecord = new MqRecord();
            mqRecord.setId(SeqUtils.getSequenceUid());
            mqRecord.setTopic(topic);
            mqRecord.setGroup(group);
            mqRecord.setValue(JSON.toJSONString(object));
            mqRecord.setTimestamp(System.currentTimeMillis());

            setChanged();
            MqFactory.executorThreadPool.execute(new Runnable()
            {
                @Override
                public void run()
                {
                    try
                    {
                        notifyObservers(mqRecord);
                    }
                    catch (Exception e)
                    {
                        log.error("consume msg{} has exception:{}",mqRecord, e);
                        // 异常导致消费失败的情况，目前只有生成调拨计划使用这个队列，可以通过页面手动点击重新生成再次出发，暂时不塞回去。
                        // redisUtils.lSet(topic, object);
                    }
                }
            });
        }
    }
}