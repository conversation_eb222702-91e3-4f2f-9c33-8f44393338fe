package cn.aliyun.ryytn.common.excel;

import java.util.List;

import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.handler.context.WorkbookWriteHandlerContext;

/**
 * @Description 按列合并行处理类
 * <AUTHOR>
 * @date 2023/10/25 9:49
 */
public class MergeRowWorkbookWriterHandler implements WorkbookWriteHandler
{
    /**
     * 合并的列数组
     */
    private List<Integer> mergeColList;

    /**
     * 合并的行数组
     */
    private List<List<Integer>> mergeRowList;

    public MergeRowWorkbookWriterHandler(List<Integer> mergeColList, List<List<Integer>> mergeRowList)
    {
        this.mergeColList = mergeColList;
        this.mergeRowList = mergeRowList;
    }

    @Override
    public void afterWorkbookDispose(WorkbookWriteHandlerContext context)
    {
        Sheet sheet = context.getWriteContext().writeSheetHolder().getSheet();

        for (Integer col : mergeColList)
        {
            for (List<Integer> list : mergeRowList)
            {
                CellRangeAddress cellRangeAddress = new CellRangeAddress(list.get(0), list.get(list.size() - 1), col, col);
                sheet.addMergedRegion(cellRangeAddress);
            }
        }
    }
}
