package cn.aliyun.ryytn.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 异常code枚举值
 */
@AllArgsConstructor
@Getter
public enum ExceptionEnums {

    SUCCESS(0,"成功"),

    /**
     * 参数非法
     */
    DATA_NOT_EXISTED(404, "参数非法"),

    /**
     * 参数非法
     */
    ILLEGAL_PARAMETER(1001, "参数非法"),

    INTERNAL_SERVER_ERROR(500, "服务内部错误"),
    DATAQ_SERVER_ERROR(5001, "DataQ服务内部错误"),

    /**
     * 权限异常
     */
    NO_AUTHORITY(403, "权限异常"),


    /**
     * 预警配置，异常信息
     */
    ALGO_WARN_DETAIL_CONFIG_SIZE_ERROR(7001, "预警配置，详细配置长度不符"),
    /**
     * 分组配置，异常信息
     */
    ALGO_GROUP_DETAIL_CONFIG_SIZE_ERROR(7003, "分组配置，详细配置长度不符"),
    ALGO_NULL_ERROR(7002, "算法不存在"),


    /**
     * 预警配置，异常信息
     */
    ADD_TABLE_DATA_ALBUM_ERROR(1002, "新增专辑表配置失败，索引%s处专辑不存在"),


    /**
     * 预警配置，异常信息
     */
    DATA_MGMT_OUT_SERVICE(2001, "数据管理外部服务查询异常，请联系管理员"),

    //数字格式：5300
    //第一位：业务模块，模型评估4 预测结果5，归因分析6 偏差比对7
    //第二位 业务场景 工厂：1 仓库：2 订单：3 销售4
    //第三四位：错误编号
    /**
     * DataQ 异常
     */
    ORDER_MODEL_LIST_ERROR(4301, "模型预测评估，订单结果查询异常"),

    ORDER_MODEL_SUM_ERROR(4302, "模型预测评估，汇总每月数据查询异常"),
    SALE_MODEL_LIST_ERROR(4401, "模型预测评估，销售预测结果查询异常"),
    FACTORY_MODEL_LIST_ERROR(4101, "模型预测评估，工厂列表查询异常"),
    FACTORY_MODEL_DETAIL_ERROR(4102, "模型预测评估，工厂详情查询异常"),
    FACTORY_MODEL_SUM_ERROR(4103, "模型预测评估,工厂汇总接口查询异常"),
    WAREHOUSE_MODEL_LIST_ERROR(4201, "模型预测评估，仓库列表查询异常"),
    WAREHOUSE_MODEL_DETAIL_ERROR(4202, "模型预测评估，仓库详情查询异常"),
    WAREHOUSE_MODEL_SUM_ERROR(4203, "模型预测评估,仓库汇总接口查询异常"),
    SALE_MODEL_SUM_ERROR(4004, "模型预测评估，汇总每月数据查询异常"),
    FACTORY_DEVIATION_LIST_ERROR(7101, "结果偏差比对，工厂商品列表查询异常"),

    FACTORY_DEVIATION_DETAIL_ERROR(7102, "结果偏差比对，工厂商品版本详情查询异常"),
    WAREHOUSE_DEVIATION_LIST_ERROR(7201, "结果偏差比对，仓库商品列表查询异常"),

    WAREHOUSE_DEVIATION_DETAIL_ERROR(7202, "结果偏差比对，仓库商品版本详情查询异常"),
    ORDER_DEVIATION_LIST_ERROR(7301, "结果偏差比对，订单结果查询异常"),
    ORDER_DEVIATION_DETAIL_ERROR(7302, "结果偏差比对，订单商品版本详情查询异常"),

    SALE_DEVIATION_LIST_ERROR(7401, "结果偏差比对，销售商品列表查询异常"),

    SALE_DEVIATION_DETAIL_ERROR(7402, "结果偏差比对，销售商品版本详情查询异常"),
    ORDER_FORECAST_LIST_ERROR(5300, "订单预测结果查询异常，请重新刷新页面"),
    ORDER_FORECAST_AGGREGATION_ERROR(5301, "订单预测结果聚合异常，请重新刷新页面"),
    ORDER_ATTRIBUTION_LIST_ERROR(6300, "订单归因分析查询异常，请重新刷新页面"),
    OPEN_API_OUT_SERVICE(9001, "DataQOpenApi服务调用异常"),
    OPEN_API_OUT_SERVICE_NEED_LOGIN_TMP(9002, "临时解决OpenApi调用问题导致的登录异常，请登录DataQ后刷新此页面"),
    SALE_FORECAST_LIST_ERROR(6300, "预测结果-销售预测-筛选参数查询异常，请重新刷新页面"),
    SALE_FORECAST_CORE_ERROR(6301, "预测结果-销售预测-详情查询异常，请重新刷新页面"),
    SALE_FORECAST_SUM_ERROR(6302, "预测结果-销售预测-预测汇总异常，请重新刷新页面"),
    FORECAST_FACTORY_LIST_ERROR(5100, "预测结果-工厂预测-筛选参数查询异常，请重新刷新页面"),
    FORECAST_FACTORY_CORE_ERROR(5101, "预测结果-工厂预测-详情查询异常，请重新刷新页面"),
    FORECAST_FACTORY_SUM_ERROR(5102, "预测结果-工厂预测-预测汇总异常，请重新刷新页面"),

    FORECAST_WAREHOUSE_LIST_ERROR(5200, "预测结果-仓库预测-筛选参数查询异常，请重新刷新页面"),
    FORECAST_WAREHOUSE_CORE_ERROR(5201, "预测结果-仓库预测-详情查询异常，请重新刷新页面"),
    FORECAST_WAREHOUSE_SUM_ERROR(5202, "预测结果-仓库预测-预测汇总异常，请重新刷新页面"),


    FORECAST_ORDER_LIST_ERROR(5300, "订单预测结果查询异常，请重新刷新页面"),


    FORECAST_SALE_LIST_ERROR(5400, "预测结果-销售预测-筛选参数查询异常，请重新刷新页面"),
    FORECAST_SALE_CORE_ERROR(5401, "预测结果-销售预测-详情查询异常，请重新刷新页面"),
    FORECAST_SALE_SUM_ERROR(5402, "预测结果-销售预测-预测汇总异常，请重新刷新页面"),
    SKU_BASE_LIST_ERROR(5001, "产品完整列表获取异常，请重新刷新页面"),

    AGGREGATION_ORDER_ERROR(6301, "订单预测结果聚合异常，请重新刷新页面"),
    ATTRIBUTION_ORDER_LIST_ERROR(6302, "订单归因分析查询异常，请重新刷新页面"),
    ATTRIBUTION_FACTORY_LIST_ERROR(6101, "归因分析-工厂预测-贡献度查询异常，请重新刷新页面"),
    ATTRIBUTION_FACTORY_IMPORTANCE_ERROR(6102, "归因分析-工厂预测-贡献度查询异常，请重新刷新页面"),

    ATTRIBUTION_WAREHOUSE_LIST_ERROR(6201, "归因分析-仓库预测-贡献度查询异常，请重新刷新页面"),
    ATTRIBUTION_WAREHOUSE_IMPORTANCE_ERROR(6202, "归因分析-仓库预测-贡献度查询异常，请重新刷新页面"),

    ATTRIBUTION_SALE_LIST_ERROR(6401, "归因分析-销售预测-贡献度查询异常，请重新刷新页面"),
    ATTRIBUTION_SALE_IMPORTANCE_ERROR(6402, "归因分析-销售预测-贡献度查询异常，请重新刷新页面"),
    OPTION_LIST_ERROR(6003, "选项卡列表获取异常"),


    /**
     * 下载异常 异常
     */
    ORDER_MODEL_EXCEL_ERROR(4311, "订单模型评估Excel下载异常，请联系管理员"),
    ORDER_FORECAST_EXCEL_ERROR(5311, "订单预测结果Excel下载异常，请联系管理员"),
    FILE_NAME_OVERLENGTH_EXCEL_ERROR(1002, "文件名过长，最长32字符"),

    ORDER_DEVIATION_EXCEL_ERROR(6311, "订单偏差比对Excel下载异常"),

    /**
     * 供应计划-算法管理
     */
    TASK_NOT_EXISTED(8001, "算法任务不存在"),
    TASK_IS_OFFLINE(8002, "算法任务已停止,无法更新"),
    TASK_IS_ONLINE(8003, "算法任务正在进行中,无法删除"),
    STOP_ALGO_TASK_ERROR(8004, "停止算法任务失败"),
    DELETE_ALGO_TASK_ERROR(8005, "删除算法任务失败"),
    CREATE_ALGO_TASK_ERROR(8006, "创建算法任务失败"),
    /**
     * 供应计划-计划管理
     */
    PRODUCTION_PLAN_LIST_ERROR(9001, "主生产计划列表获取异常"),
    FACTORY_PRODUCTION_DETAIL_ERROR(9002, "主生产计划工厂生产详情获取异常"),
    FACTORY_SUM_DETAIL_ERROR(9003, "主生产计划工厂生产详情汇总获取异常"),
    SALES_COMPARISON_LIST_ERROR(9004, "产销存对比列表获取数据异常"),
    SALES_COMPARISON_DETAIL_ERROR(9005, "产销存对比详情获取异常"),
    MATERIAL_REQUIREMENT_LIST_ERROR(10001, "物料计划列表获取异常"),
    MATERIAL_REQUIREMENT_DETAIL_ERROR(10002, "物料计划详情获取异常"),
    MATERIAL_REQUIREMENTS_LIST_ERROR(10003, "物料需求明细列表获取异常"),
    MATERIAL_REQUIREMENT_FACTORY_DETAIL_ERROR(10004, "物料需求明细工厂生产详情获取异常"),
    PURCHASING_PLAN_List_ERROR(11005, "外协计划基本信息列表获取异常"),
    PURCHASING_PLAN_DETAIL_ERROR(11006, "外协计划基本信息详情获取异常"),
    PLAN_VERSION_OPTION_LIST_ERROR(11007, "计划版本和滚动日期信息获取异常"),
    SKU_PURCHASE_INFO_PAGE_ERROR(12001, "参数管理需求分页获取异常"),
    SKU_PURCHASE_INFO_LIST_ERROR(12002, "参数管理需求列表获取异常"),
    SKU_PURCHASE_CONFIG_LIST_ERROR(12003, "参数管理需求配置列表获取异常"),
    SKU_PURCHASE_HISTORY_LIST_ERROR(13003, "参数管理物料版本列表获取异常"),
    MATERIAL_INFO_PAGE_ERROR(13001, "参数管理物料分页获取异常"),
    MATERIAL_CONFIG_LIST_ERROR(13002, "参数管理物料配置列表获取异常"),
    MATERIAL_HISTORY_LIST_ERROR(13003, "参数管理物料版本列表获取异常"),
    MATERIAL_INFO_DISTINCT_LIST_ERROR(13004, "参数管理物料去重列表获取异常"),
    MATERIAL_INVENTORY_INFO_PAGE_ERROR(14001, "参数管理物料库存分页获取异常"),
    MATERIAL_INVENTORY_CONFIG_LIST_ERROR(14002, "参数管理物料库存配置列表获取异常"),
    MATERIAL_INVENTORY_HISTORY_LIST_ERROR(14003, "参数管理物料库存版本列表获取异常"),
    SKU_INFO_PAGE_ERROR(15001, "参数管理产成品分页获取异常"),
    SKU_CONFIG_LIST_ERROR(15002, "参数管理产成品配置列表获取异常"),
    SKU_HISTORY_LIST_ERROR(15003, "参数管理产成品版本列表获取异常"),
    SKU_INVENTORY_INFO_PAGE_ERROR(16001, "参数管理产成品库存分页获取异常"),
    SKU_INVENTORY_CONFIG_LIST_ERROR(16002, "参数管理产成品库存配置列表获取异常"),
    SKU_INVENTORY_HISTORY_LIST_ERROR(16003, "参数管理产成品库存版本列表获取异常"),
    SALE_PLAN_LIST_ERROR(17001, "业务参数年度销售计划列表获取异常"),
    SALE_PLAN_EXCEL_EXPORT_ERROR(17002, "业务参数年度销售计划导出异常"),

    EVAL_ORDER_LIST_ERROR(31001, "模型评测订单列表获取异常"),
    EVAL_CONFIG_NULL_ERROR(32000, "模型评测配置不存在"),
    EVAL_SALE_LIST_ERROR(33001, "模型评测销售列表获取异常"),
    EVAL_FACTORY_LIST_ERROR(34001, "模型评测工厂列表获取异常"),
    EVAL_WAREHOUSE_LIST_ERROR(30001, "模型评测仓库列表获取异常"),

    MARKETING_PLAN_LIST_ERROR(18001, "业务参数营销计划列表获取异常"),
    MARKETING_PLAN_PRODUCT_LIST_ERROR(18002, "业务参数营销计划使用的商品列表获取异常"),
    MARKETING_PLAN_SUP_LIST_ERROR(18003, "业务参数营销计划使用的渠道列表获取异常"),
    MARKETING_PLAN_INSERT_ERROR(18004, "业务参数营销计划添加活动基本信息异常"),
    MARKETING_PLAN_SKU_INSERT_ERROR(18005, "业务参数营销计划添加活动商品异常"),
    MARKETING_PLAN_RESELLER_INSERT_ERROR(18006, "业务参数营销计划添加活动销售信息异常"),
    MARKETING_PLAN_DETAIL_UPDATE_ERROR(18007, "业务参数营销计划修改活动基本信息异常"),
    MARKETING_PLAN_SKU_DETAIL_UPDATE_ERROR(18008, "业务参数营销计划修改活动商品信息异常"),
    MARKETING_PLAN_RESELLER_DETAIL_UPDATE_ERROR(18009, "业务参数营销计划修改活动渠道信息异常"),
    MARKETING_PLAN_DOWNLOAD_EXCEL_ERROR(18010, "业务参数营销计划下载活动异常"),
    PRODUCT_SALE_PLAN_LIST_ERROR(19001, "业务参数产品销售计划列表获取异常"),
    PRODUCT_SALE_PLAN_DETAIL_ERROR(19002, "业务参数产品销售详情数据获取异常"),
    /**
     * 营销事件
     */
    MARKETING_EVENT_LIST_ERROR(20001, "业务参数营销事件列表获取异常"),
    MARKETING_EVENT_SKU_LIST_ERROR(20002, "业务参数营销事件SKU列表获取异常"),
    MARKETING_EVENT_INSERT_ERROR(20003, "业务参数营销事件添加事件异常"),
    MARKETING_EVENT_SKU_INSERT_ERROR(20004, "业务参数营销事件添加SKU异常"),
    MARKETING_EVENT_SKU_DELETE_ERROR(20005, "业务参数营销事件删除对应的SKU信息异常"),
    MARKETING_EVENT_DELETE_ERROR(20006, "业务参数营销事件删除对应的事件信息异常"),
    MARKETING_EVENT_DOWNLOAD_EXCEL_ERROR(20007, "业务参数营销事件下载活动异常"),
    PRODUCT_SALE_PLAN_UPLOAD_EXCEL_ERROR(20008, "业务参数产品销售计划列表上传异常"),
    MARKETING_EVENT_UPDATE_STATUS_ERROR(20011, "业务参数营销事件修改状态异常"),
    MARKETING_PLAN_UPLOAD_ERROR(20009, "业务参数营销计划导入异常"),
    MARKETING_PLAN_EVENT_UPLOAD_ERROR(20010, "业务参数营销事件导入异常"),
    /**
     * 补货计划
     */
    REPLENISHMENT_PLAN_LIST_ERROR(21001, "补货计划商品数据获取异常"),
    REPLENISHMENT_PLAN_DETAIL_ERROR(21002, "补货计划商品生产指标获取异常"),
    REPLENISHMENT_PLAN_SKU_DEMAND_ERROR(21003, "补货计划商品分仓需求计划获取异常"),
    AVERAGE_REAL_NUM_ERROR(21004, "补货计划商品过去30天平均值获取异常"),
    FUTURE_SKU_DEMAND_NUM_ERROR(21005, "补货计划未来一段时间需求计划获取异常"),
    SAVE_REPLENISHMENT_PLAN_ERROR(21006, "补货计划，保存补货计划失败"),
    SUBMIT_REPLENISHMENT_PLAN_ERROR(21007, "补货计划，提交补货计划失败"),
    REPLENISHMENT_PLAN_ERROR(21008, "补货计划，获取选项卡失败"),
    INDEX_MANAGE_ERROR(23001, "指标管理-获取指标列表失败"),
    CYCLE_WEIGHT_ERROR(23002, "指标管理-权重列表获取失败"),
    UPDATE_CYCLE_WEIGHT_ERROR(23003, "指标管理-权重列表修改失败"),
    SALES_PRESENTATION_LIST_ERROR(22001, "销售提报，获取列表信息失败"),
    SALES_PRESENTATION_DETAIL_ERROR(22002, "销售提报，商品详情获取失败"),
    EDIT_SALES_PRESENTATION_DETAIL_ERROR(22003, "销售提报，修改商品具体的数值失败"),

    MARKET_ACTIVITY_ID_NULL_ERROR(13001, "促销活动主键不存在"),
    MARKET_ACTIVITY_CODE_NULL_ERROR(13003, "促销活动唯一Code不存在"),
    MARKET_ACTIVITY_OPERATE_ERROR(13002, "促销活动处理失败"),
    MARKET_ACTIVITY_SKU_OPERATE_ERROR(13004, "促销活动产品关联处理失败"),
    MARKET_ACTIVITY_RESELLER_OPERATE_ERROR(13004, "促销活动渠道关联处理失败"),
    SKU_REPORTING_UPDATE_ERROR(14001, "对应需求提报修改失败"),
    SKU_REPORTING_ROLLING_VERSION_IS_NULL_ERROR(14002, "导入的需求提报版本不能为空"),
    INV_STRAT_IS_NULL(15001, "修改的库存配置不存在，请刷新页面"),
    INV_STRAT_IS_REPEAT(15002, "创建的配置已存在，情刷新页面"),
    INV_STRAT_PARAM_CHECK(15003, "检查创建配置，特殊参数无时间范围"),
    INV_STRAT_PARAM_DELETE(15004, "删除除库存配置失败，配置可能已删除"),
    INV_STRAT_PARAM_ADD(15005, "创建库存配置失败，配置可能已创建"),
    INV_STRAT_SALES_ADD(15006, "插入日均销量配置失败");
    private final Integer code;

    private final String message;

}
