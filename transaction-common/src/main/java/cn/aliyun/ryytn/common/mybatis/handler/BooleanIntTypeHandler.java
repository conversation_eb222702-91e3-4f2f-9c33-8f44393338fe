package cn.aliyun.ryytn.common.mybatis.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * @Description 布尔类型与整型转换
 * <AUTHOR>
 * @date 2023/11/13 16:31
 */
@MappedTypes(value = Boolean.class)
@MappedJdbcTypes(value = {JdbcType.SMALLINT, JdbcType.TINYINT})
public class BooleanIntTypeHandler extends BaseTypeHandler<Boolean>
{
    /**
     *
     * @Description 设置非空参数
     * @param ps
     * @param i
     * @param parameter
     * @param jdbcType
     * @throws SQLException
     * <AUTHOR>
     * @date 2023年11月13日 16:31
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Boolean parameter, JdbcType jdbcType) throws SQLException
    {
        ps.setInt(i, parameter ? 1 : 0);
    }

    /**
     *
     * @Description 设置非空结果，0：false，非0：true
     * @param rs
     * @param columnName
     * @return Boolean
     * @throws SQLException
     * <AUTHOR>
     * @date 2023年11月13日 16:31
     */
    @Override
    public Boolean getNullableResult(ResultSet rs, String columnName) throws SQLException
    {
        return rs.getInt(columnName) == 0 ? false : true;
    }

    /**
     *
     * @Description 空值默认为false
     * @param rs
     * @param columnIndex
     * @return Boolean
     * @throws SQLException
     * <AUTHOR>
     * @date 2023年11月13日 16:31
     */
    @Override
    public Boolean getNullableResult(ResultSet rs, int columnIndex) throws SQLException
    {
        return false;
    }

    /**
     *
     * @Description 空值默认为false
     * @param cs
     * @param columnIndex
     * @return Boolean
     * @throws SQLException
     * <AUTHOR>
     * @date 2023年11月13日 16:31
     */
    @Override
    public Boolean getNullableResult(CallableStatement cs, int columnIndex) throws SQLException
    {
        return false;
    }
}
