package cn.aliyun.ryytn.common.constants;

/**
 *
 * @Description 响应消息结果码常量
 * <AUTHOR>
 * @date 2023年9月19日 下午2:01:17
 */
public class ErrorCodeConstants
{
    /**
     * 成功
     */
    public static final Integer SUCCESS = 0;
    /**
     * 失败：其他
     */
    public static final Integer FAIL_OTHER = -1;
    /**
     * 失败：参数为空
     */
    public static final Integer FAIL_PARAM_EMPTY = 1;
    /**
     * 失败：参数非法
     */
    public static final Integer FAIL_PARAM_INVALID = 2;
    /**
     * 失败：数据不存在
     */
    public static final Integer FAIL_DATA_NOTEXISTS = 3;
    /**
     * 失败：数据重复
     */
    public static final Integer FAIL_DATA_REPEAT = 4;
    /**
     * 失败：OSS错误
     */
    public static final Integer FAIL_OSS_ERROR = 5;
    /**
     * 失败：数据已被引用
     */
    public static final Integer FAIL_DATA_REFERENCED = 6;
    /**
     * 失败：初始化数据或外部系统同步不可修改删除
     */
    public static final Integer FAIL_DATA_INITED = 7;
    /**
     * 失败：HTTP请求失败
     */
    public static final Integer FAIL_HTTP_ERROR = 8;
    /**
     * 失败：多线程失败，已存在任务执行中
     */
    public static final Integer FAIL_TASK_PROCESSING_ERROR = 9;
    /**
     * 失败：验证码错误
     */
    public static final Integer FAIL_CAPTCHA_ERROR = 10;
    /**
     * 失败：登录失败，使用模糊提示语，防止暴力破解
     */
    public static final Integer FAIL_LOGIN_ERROR = 11;
    /**
     * 用户未登录，需要登录
     */
    public static final Integer FAIL_NEEDLOGIN_ERROR = 12;
    /**
     * 用户无权限
     */
    public static final Integer FAIL_NOAUTH_ERROR = 13;
    /**
     * 缓存操作失败
     */
    public static final Integer FAIL_CACHE_ERROR = 14;
    /**
     * 已登录其他账号，前端应刷新页面
     */
    public static final Integer FAIL_LOGIN_OTHER_ERROR = 15;
    /**
     * 阿里dataq接口失败
     */
    public static final Integer FAIL_DATAQ_ERROR = 16;
}
