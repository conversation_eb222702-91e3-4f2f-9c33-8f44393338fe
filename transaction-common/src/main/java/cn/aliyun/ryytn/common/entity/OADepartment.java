package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description OA系统查询部门信息响应
 * <AUTHOR>
 * @date 2023/9/26 17:55
 */
@Setter
@Getter
@ToString
public class OADepartment implements Serializable
{
    private static final long serialVersionUID = -7772838642210107803L;
    /**
     * 部门编号
     */
    @JSONField(name = "id")
    private String id;

    /**
     * 部门简称
     */
    @JSONField(name = "departmentmark")
    private String departmentMark;

    /**
     * 部门全称
     */
    @JSONField(name = "departmentname")
    private String departmentName;

    /**
     * 部门编码
     */
    @JSONField(name = "departmentcode")
    private String departmentCode;

    /**
     * 分部编号，对应t_ryytn_subcompany表id字段
     */
    @JSONField(name = "subcompanyid1")
    private String subCompanyId;

    /**
     * 上级部门编号，0或者空为表示没有上级分部
     */
    @JSONField(name = "supdepid")
    private String supDepId;

    /**
     * 封存标志，1 封存，其他为未封存
     */
    @JSONField(name = "canceled")
    private String canceled;

    /**
     * 排序
     */
    @JSONField(name = "showorder")
    private Double sortNo;

    /**
     * 创建时间
     */
    @JSONField(name = "created")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date createdTime;

    /**
     * 修改时间
     */
    @JSONField(name = "modified")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date updatedTime;

    /**
     * 同步时间，精确到毫秒
     */
    private Long syncTime;

    /**
     * 子部门列表
     */
    private List<OADepartment> subDepartmentList;

    /**
     * 所有父部门编号
     */
    private String supDepIds;

    /**
     * 所有父部门名称
     */
    private String supDepNames;

    /**
     * 公司层级，根据supDepIds逗号个数计算，顶级公司level从1开始
     */
    private Integer level;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        OADepartment departmentOA = (OADepartment) o;
        return Objects.equals(id, departmentOA.getId());
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        return result;
    }
}
