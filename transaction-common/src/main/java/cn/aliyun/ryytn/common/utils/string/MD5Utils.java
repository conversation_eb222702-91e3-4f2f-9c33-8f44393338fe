package cn.aliyun.ryytn.common.utils.string;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * 
 * @Description MD5加密工具类 
 * <AUTHOR>
 * @date 2023年5月16日 上午11:12:24
 */
public class MD5Utils
{
    /**
     * 
     * @Description source
     * @param source
     * @return String
     * <AUTHOR>
     * @date 2023年5月16日 上午11:12:55
     */
    public static String encrypt(String source)
    {
        return DigestUtils.md5Hex(source);
    }
}