package cn.aliyun.ryytn.common.entity.sap;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 主SKU信息
 * <AUTHOR>
 * @date 2024/10/10 10:00
 */
@Setter
@Getter
@ToString
@ApiModel("主SKU信息")
public class MasterSku implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 物料类型
     */
    @ApiModelProperty("物料类型")
    private String materialType;

    /**
     * 物料类型描述
     */
    @ApiModelProperty("物料类型描述")
    private String materialTypeDesc;

    /**
     * 物料组
     */
    @ApiModelProperty("物料组")
    private String materialGroup;

    /**
     * 物料组描述
     */
    @ApiModelProperty("物料组描述")
    private String materialGroupDesc;

    /**
     * 基本计量单位描述
     */
    @ApiModelProperty("基本计量单位描述")
    private String baseUnitDesc;

    /**
     * 毛重
     */
    @ApiModelProperty("毛重（3位小数）")
    private BigDecimal grossWeight;

    /**
     * 净重
     */
    @ApiModelProperty("净重（3位小数）")
    private BigDecimal netWeight;

    /**
     * 重量单位
     */
    @ApiModelProperty("重量单位")
    private String weightUnit;

    /**
     * 条码
     */
    @ApiModelProperty("条码")
    private String barcode;

    /**
     * 品牌
     */
    @ApiModelProperty("品牌")
    private String brand;

    /**
     * 简称
     */
    @ApiModelProperty("简称")
    private String shortName;

    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String modelSpec;

    /**
     * 保质期
     */
    @ApiModelProperty("保质期")
    private BigDecimal shelfLife;

    /**
     * 长
     */
    @ApiModelProperty("长（6位小数）")
    private BigDecimal length;

    /**
     * 宽
     */
    @ApiModelProperty("宽（6位小数）")
    private BigDecimal width;

    /**
     * 高
     */
    @ApiModelProperty("高（6位小数）")
    private BigDecimal height;

    /**
     * 体积
     */
    @ApiModelProperty("体积（6位小数）")
    private BigDecimal volume;

    /**
     * 批次管理需求的标识
     */
    @ApiModelProperty("批次管理需求的标识")
    private String batchManagementFlag;

    /**
     * 11层：单位
     */
    @ApiModelProperty("11层：单位")
    private String layer11Unit;

    /**
     * 9层：入数
     */
    @ApiModelProperty("9层：入数（3位小数）")
    private BigDecimal layer9Quantity;

    /**
     * 10层：提数
     */
    @ApiModelProperty("10层：提数（3位小数）")
    private BigDecimal layer10Quantity;

    /**
     * 税收分类
     */
    @ApiModelProperty("税收分类")
    private String taxClassification;

    /**
     * 是否启用SN
     */
    @ApiModelProperty("是否启用SN（Y/N）")
    private String snEnabled;

    /**
     * 是否溯源
     */
    @ApiModelProperty("是否溯源（Y/N）")
    private String traceabilityEnabled;

    /**
     * 一级分类
     */
    @ApiModelProperty("一级分类")
    private String primaryCategory;

    /**
     * 二级分类
     */
    @ApiModelProperty("二级分类")
    private String secondaryCategory;

    /**
     * 三级分类
     */
    @ApiModelProperty("三级分类")
    private String tertiaryCategory;

    /**
     * 四级分类
     */
    @ApiModelProperty("四级分类")
    private String quaternaryCategory;

    /**
     * 下市状态
     */
    @ApiModelProperty("下市状态")
    private String offMarketStatus;

    /**
     * 下市日期
     */
    @ApiModelProperty("下市日期")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    private Date offMarketDate;

    /**
     * 商品状态
     */
    @ApiModelProperty("商品状态")
    private String productStatus;

    /**
     * 零级描述
     */
    @ApiModelProperty("零级描述")
    private String zeroLevelDesc;

    /**
     * 零级编码
     */
    @ApiModelProperty("零级编码")
    private String zeroLevelCode;

    /**
     * SAP创建日期（ERSDA）
     */
    @ApiModelProperty("SAP创建日期")
    private String sapCreateDate;

    /**
     * SAP创建时间（CREATED）
     */
    @ApiModelProperty("SAP创建时间")
    private String sapCreateTime;

    /**
     * SAP更改日期（LAEDA）
     */
    @ApiModelProperty("SAP更改日期")
    private String sapUpdateDate;

    /**
     * SAP更改者（AENAM）
     */
    @ApiModelProperty("SAP更改者")
    private String sapUpdater;
}
