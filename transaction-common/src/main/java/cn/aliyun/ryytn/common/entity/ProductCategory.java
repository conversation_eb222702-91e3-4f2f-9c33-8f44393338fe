package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 产品品类
 * <AUTHOR>
 * @date 2023年10月09日 16:34
 */
@Setter
@Getter
@ToString
@ApiModel("产品品类")
public class ProductCategory implements Serializable
{
    private static final long serialVersionUID = 6085430472831606892L;
    /**
     * 产品品类编号
     */
    @ApiModelProperty("产品品类编号")
    private String id;

    /**
     * 产品品类名称
     */
    @ApiModelProperty("产品品类名称")
    private String name;

    /**
     * 父产品品类编号，根页面为-1
     */
    @ApiModelProperty("父产品品类编号")
    private String parentId;
    /**
     * 层级
     */
    @ApiModelProperty("层级")
    private Integer level;

    /**
     * 子品类列表
     */
    @ApiModelProperty("子品类列表")
    private List<ProductCategory> subList=new ArrayList<>();
}
