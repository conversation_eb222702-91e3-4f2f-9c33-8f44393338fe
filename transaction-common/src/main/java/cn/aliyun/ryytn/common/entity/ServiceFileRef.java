package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * @Description 业务文件关联数据实体
 * <AUTHOR>
 * @date 2023年9月22日 上午9:40:11
 */
@Setter
@Getter
@ToString
public class ServiceFileRef implements Serializable
{
    private static final long serialVersionUID = -2327232699201755056L;

    /**
     * @Description 文件编号
     * <AUTHOR>
     * @date 2023年9月22日 上午9:40:03
     */
    private String fileId;

    /**
     * @Description 业务编号
     * <AUTHOR>
     * @date 2023年9月22日 上午9:40:03
     */
    private String serviceId;

    /**
     * @Description 业务数据表名
     * <AUTHOR>
     * @date 2023年9月22日 上午9:40:03
     */
    private String serviceType;
}
