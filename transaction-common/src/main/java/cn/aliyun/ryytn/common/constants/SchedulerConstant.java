package cn.aliyun.ryytn.common.constants;

/**
 *
 * @Description 调度服务常量
 * <AUTHOR>
 * @date 2022年6月6日 下午2:29:51
 */
public class SchedulerConstant
{
    public static final String JOB_GROUP_NAME = "JOB_GROUP";

    /** 调度任务类型：CRON表达式 */
    public static final Integer JOB_TYPE_CRON = 1;

    /** 调度任务类型：循环执行 */
    public static final Integer JOB_TYPE_CYCLE = 2;

    /** 调度任务类型：一次性执行 */
    public static final Integer JOB_TYPE_ONCE = 3;

    /** 任务前缀 */
    public static final String JOB_KEY_PREFIX = "JOB";

    /** 执行目标key */
    public static final String TASK_PROPERTIES = "TASK_PROPERTIES";

    public enum StatusEnum
    {
        /**
         * 正常
         */
        NORMAL(1),
        /**
         * 暂停
         */
        PAUSE(2),
        /**
         * 已完成
         */
        FINISHED(3);

        private Integer value;

        private StatusEnum(Integer value)
        {
            this.value = value;
        }

        public Integer getValue()
        {
            return value;
        }
    }
}
