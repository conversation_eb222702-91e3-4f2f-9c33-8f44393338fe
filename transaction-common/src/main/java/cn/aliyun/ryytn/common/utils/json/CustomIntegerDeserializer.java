package cn.aliyun.ryytn.common.utils.json;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import cn.aliyun.ryytn.common.utils.string.StringUtils;

/**
 * @Description 前端可能会传-，JSON反序列化有问题，项目没有前端，只能暂时后端兼容
 * <AUTHOR>
 * @date 2024/5/15 18:14
 */
public class CustomIntegerDeserializer extends JsonDeserializer<Integer>
{
    @Override
    public Integer deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException, JsonProcessingException
    {
        String value = jp.getText();
        if (StringUtils.DATE_SEPARATOR.equals(value))
        {
            return null;
        }
        else
        {
            return Integer.valueOf(value);
        }
    }
}