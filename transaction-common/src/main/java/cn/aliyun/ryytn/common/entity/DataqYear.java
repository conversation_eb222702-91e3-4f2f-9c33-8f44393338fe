package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description Dataq日历表年数据
 * <AUTHOR>
 * @date 2023/11/2 15:38
 */
@Setter
@Getter
@ToString
@ApiModel("Dataq日历表年数据")
public class DataqYear implements Serializable
{
    private static final long serialVersionUID = -8867434334975003930L;
    /**
     * 财年：yyyy
     */
    @ApiModelProperty("财年：yyyy")
    private String fsclYear;

    /**
     * 财年开始日期：yyyyMMdd
     */
    @ApiModelProperty("财年开始日期：yyyyMMdd")
    private String fsclYearBegDate;

    /**
     * 财年结束日期：yyyyMMdd
     */
    @ApiModelProperty("财年结束日期：yyyyMMdd")
    private String fsclYearEndDate;
}
