package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description OA系统查询分部信息响应
 * <AUTHOR>
 * @date 2023/9/26 17:54
 */
@Setter
@Getter
@ToString
public class OASubCompany implements Serializable
{
    private static final long serialVersionUID = -2384981807728857891L;
    /**
     * 分部编号
     */
    @JSONField(name = "id")
    private String id;

    /**
     * 分部简称
     */
    @JSONField(name = "subcompanyname")
    private String subCompanyName;

    /**
     * 分部全称
     */
    @JSONField(name = "subcompanydesc")
    private String subCompanyDesc;

    /**
     * 分部编码
     */
    @JSONField(name = "subcompanycode")
    private String subCompanyCode;

    /**
     * 上级分部编号
     */
    @JSONField(name = "supsubcomid")
    private String supSubComId;

    /**
     * 封存标志，1 封存，其他为未封存
     */
    @JSONField(name = "canceled")
    private String canceled;

    /**
     * 排序
     */
    @JSONField(name = "showorder")
    private Double sortNo;

    /**
     * 创建时间
     */
    @JSONField(name = "created")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date createdTime;

    /**
     * 修改时间
     */
    @JSONField(name = "modified")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date updatedTime;

    /**
     * 同步时间，精确到毫秒
     */
    private Long syncTime;

    /**
     * 下属子公司列表
     */
    private List<OASubCompany> subCompanyList;

    /**
     * 所有上级公司编号，英文逗号分隔
     */
    private String supSubComIds;

    /**
     * 公司名称全称，包含所有上级，/分隔
     */
    private String subComNames;

    /**
     * 公司层级，根据supSubComIds逗号个数计算，顶级公司level从1开始
     */
    private Integer level;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        OASubCompany subCompanyOA = (OASubCompany) o;
        return Objects.equals(id, subCompanyOA.getId());
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        return result;
    }
}
