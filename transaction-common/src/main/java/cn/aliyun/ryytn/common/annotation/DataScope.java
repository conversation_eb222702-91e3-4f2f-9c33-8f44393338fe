package cn.aliyun.ryytn.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import cn.aliyun.ryytn.common.utils.string.StringUtils;

/**
 * @Description 数据权限注解
 * <AUTHOR>
 * @date 2024/1/31 15:05
 */
@Documented
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DataScope
{
    String category() default StringUtils.EMPTY;

    String channel() default StringUtils.EMPTY;

    String warehouse() default StringUtils.EMPTY;
}
