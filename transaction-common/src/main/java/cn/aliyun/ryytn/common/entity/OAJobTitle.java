package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description OA系统查询岗位信息响应
 * <AUTHOR>
 * @date 2023/9/26 17:56
 */
@Setter
@Getter
@ToString
public class OAJobTitle implements Serializable
{
    private static final long serialVersionUID = -926531031576954944L;
    /**
     * 岗位编号
     */
    @JSONField(name = "id")
    private String id;

    /**
     * 岗位简称
     */
    @JSONField(name = "jobtitlemark")
    private String jobTitleMark;

    /**
     * 岗位全称
     */
    @JSONField(name = "jobtitlename")
    private String jobTitleName;

    /**
     * 相关文档编号
     */
    @JSONField(name = "jobdoc")
    private String jobDoc;

    /**
     * 部门编号，OA系统接口文档标注废弃字段
     */
    @Deprecated
    @JSONField(name = "jobdepartmentid")
    private String jobDepartmentId;

    /**
     * 岗位职责
     */
    @JSONField(name = "jobresponsibility")
    private String jobResponsibility;

    /**
     * 任职资格
     */
    @JSONField(name = "jobcompetency")
    private String jobCompetency;

    /**
     * 备注
     */
    @JSONField(name = "jobtitleremark")
    private String jobTitleRemark;

    /**
     * 创建时间
     */
    @JSONField(name = "created")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date createdTime;

    /**
     * 修改时间
     */
    @JSONField(name = "modified")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date updatedTime;

    /**
     * 同步时间，精确到毫秒
     */
    private Long syncTime;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        OAJobTitle jobTitleOA = (OAJobTitle) o;
        return Objects.equals(id, jobTitleOA.getId());
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        return result;
    }
}
