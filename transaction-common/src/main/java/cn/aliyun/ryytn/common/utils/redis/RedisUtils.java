package cn.aliyun.ryytn.common.utils.redis;


import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.stream.ObjectRecord;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.connection.stream.StreamRecords;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 基于spring和redis的redisTemplate工具类
 *  * 针对所有的hash 都是以h开头的方法
 *  * 针对所有的Set 都是以s开头的方法
 *  * 针对所有的List 都是以l开头的方法
 * <AUTHOR>
 * @date 2023/9/21 16:06
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisUtils
{
    /**
     * redis操作实例
     */
    private RedisTemplate redisTemplate;

    @Autowired(required = false)
    public void setRedisTemplate(RedisTemplate redisTemplate)
    {
        RedisSerializer stringSerializer = new StringRedisSerializer();
        GenericJackson2JsonRedisSerializer genericJackson2JsonRedisSerializer = new GenericJackson2JsonRedisSerializer();
        redisTemplate.setKeySerializer(stringSerializer);
        redisTemplate.setHashKeySerializer(stringSerializer);
        redisTemplate.setStringSerializer(stringSerializer);
        redisTemplate.setValueSerializer(genericJackson2JsonRedisSerializer);
        redisTemplate.setHashValueSerializer(genericJackson2JsonRedisSerializer);
        this.redisTemplate = redisTemplate;
    }

    /**
     *
     * @Description 加锁
     * @param key
     * @param time 单位秒
     * @return boolean true:加锁成功，false:加锁失败
     * <AUTHOR>
     * @date 2023年09月21日 17:36
     */
    public boolean lock(String key, long time)
    {
        return locks(key, null, time);
    }

    /**
     *
     * @Description 加锁
     * @param key
     * @param value
     * @param time 单位秒
     * @return boolean true:加锁成功，false:加锁失败
     * <AUTHOR>
     * @date 2023年09月21日 17:36
     */
    public boolean locks(String key, String value, long time)
    {
        boolean result = false;
        if (StringUtils.isBlank(value))
        {
            value = DateUtils.getDate(DateUtils.YMDHMS_STD_MS);
        }
        try
        {
            // 设置锁，value为锁的时间
            result = redisTemplate.opsForValue().setIfAbsent(key, value, time, TimeUnit.SECONDS);
        }
        catch (Exception e)
        {
            log.error("lock has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 释放锁
     * @param key
     * @return boolean
     * <AUTHOR>
     * @date 2023年09月21日 17:41
     */
    public boolean unlock(String key)
    {
        boolean result = false;
        try
        {
            return redisTemplate.delete(key);
        }
        catch (Exception e)
        {
            log.error("unlock has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 返回key集合
     * @param patten
     * @return Set
     * <AUTHOR>
     * @date 2023年09月21日 16:39
     */
    public Set<String> keys(String patten)
    {
        Set<String> set = null;
        try
        {
            set = redisTemplate.keys(patten);
        }
        catch (Exception e)
        {
            log.error("keys has exception:", e);
        }
        return set;
    }

    /**
     *
     * @Description 设置key失效时间
     * @param key
     * @param time 单位秒
     * @return boolean
     * <AUTHOR> true:成功,false:失败
     * @date 2023年09月21日 16:40
     */
    public boolean expire(String key, long time)
    {
        boolean result = false;
        try
        {
            if (time > 0)
            {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            result = true;
        }
        catch (Exception e)
        {
            log.error("expire has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 根据key 获取过期时间
     * @param key
     * @return long 剩余过期时间，单位秒
     * <AUTHOR>
     * @date 2023年09月21日 16:42
     */
    public long getExpire(String key)
    {
        long result = 0L;
        try
        {
            result = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        }
        catch (Exception e)
        {
            log.error("getExpire has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 判断key是否存在
     * @param key
     * @return boolean true:存在，false:不存在
     * <AUTHOR>
     * @date 2023年09月21日 16:42
     */
    public boolean hasKey(String key)
    {
        boolean result = false;
        try
        {
            result = redisTemplate.hasKey(key);
        }
        catch (Exception e)
        {
            log.error("hasKey has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 删除指定key，返回key对应的value
     * @param key
     * @return Object
     * <AUTHOR>
     * @date 2023年09月21日 16:49
     */
    public Object del(String key)
    {
        Object object = null;
        try
        {
            object = get(key);
            redisTemplate.delete(key);
        }
        catch (Exception e)
        {
            log.error("del has exception:", e);
        }
        return object;
    }

    /**
     * 根据pattern模糊匹配删除
     * @param pattern
     */
    public void vagueDel(String pattern){
        Set<String> keys = keys(pattern);
        del(keys.stream().collect(Collectors.toList()));
    }

    /**
     *
     * @Description 删除指定多个key，返回删除成功key的个数
     * @param key
     * @return long
     * <AUTHOR>
     * @date 2023年09月21日 16:43
     */
    public long del(List<String> keys)
    {
        long result = 0L;
        if (CollectionUtils.isNotEmpty(keys))
        {
            result = redisTemplate.delete(keys);
        }
        return result;
    }

    /**
     *
     * @Description 获取指定key的value
     * @param key
     * @return Object
     * <AUTHOR>
     * @date 2023年09月21日 16:49
     */
    public Object get(String key) throws Exception
    {
        Object object = null;
        try
        {
            object = redisTemplate.opsForValue().get(key);
        }
        catch (Exception e)
        {
            log.error("get has exception:", e);
        }
        return object;
    }

    /**
     *
     * @Description 获取指定key的value
     * @param keys
     * @return List<Object>
     * <AUTHOR>
     * @date 2023年09月21日 16:49
     */
    public List<Object> multiGet(Collection<String> keys) throws Exception
    {
        List<Object> result = null;
        try
        {
            result = redisTemplate.opsForValue().multiGet(keys);
        }
        catch (Exception e)
        {
            log.error("multiGet has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 设置缓存
     * @param key
     * @param value
     * @return boolean true成功 false失败
     * <AUTHOR>
     * @date 2023年09月21日 16:50
     */
    public boolean set(String key, Object value)
    {
        boolean result = false;
        try
        {
            redisTemplate.opsForValue().set(key, value);
            result = true;
        }
        catch (Exception e)
        {
            log.error("set has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 设置缓存
     * @param map
     * @return boolean true成功 false失败
     * <AUTHOR>
     * @date 2023年09月21日 16:50
     */
    public boolean multiSet(Map<String, Object> map)
    {
        boolean result = false;
        try
        {
            redisTemplate.opsForValue().multiSet(map);
            result = true;
        }
        catch (Exception e)
        {
            log.error("multiSet has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 普通缓存放入并设置时间
     * @param key
     * @param value
     * @param time 时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return boolean true：成功，false：失败
     * <AUTHOR>
     * @date 2023年09月21日 16:51
     */
    public boolean set(String key, Object value, long time)
    {
        boolean result = false;
        try
        {
            if (time > 0)
            {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            }
            else
            {
                set(key, value);
            }
            result = true;
        }
        catch (Exception e)
        {
            log.error("set has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 指定key递增
     * @param key
     * @param delta
     * @return long
     * <AUTHOR>
     * @date 2023年09月21日 16:52
     */
    public long incr(String key, long delta)
    {
        return incr(key, delta, -1);
    }

    /**
     *
     * @Description 指定key递增
     * @param key
     * @param delta
     * @param llt 有效时间，单位秒
     * @return long
     * <AUTHOR>
     * @date 2023年09月21日 16:52
     */
    public long incr(String key, long delta, long llt)
    {
        long result = 0L;
        try
        {
            result = redisTemplate.opsForValue().increment(key, delta);
            if (Objects.nonNull(llt))
            {
                redisTemplate.expire(key, llt, TimeUnit.SECONDS);
            }
        }
        catch (Exception e)
        {
            log.error("incr has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 指定key递减
     * @param key
     * @param delta
     * @return long
     * <AUTHOR>
     * @date 2023年09月21日 16:52
     */
    public long decr(String key, long delta)
    {
        long result = 0L;
        try
        {
            result = redisTemplate.opsForValue().increment(key, -delta);
        }
        catch (Exception e)
        {
            log.error("decr has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 获取指定hashkey指定item的value
     * @param key
     * @param item
     * @return Object
     * <AUTHOR>
     * @date 2023年09月21日 16:53
     */
    public Object hget(String key, String item)
    {
        Object object = null;
        try
        {
            object = redisTemplate.opsForHash().get(key, item);
        }
        catch (Exception e)
        {
            log.error("hget has exception:", e);
        }
        return object;
    }

    /**
     *
     * @Description 获取hashKey对应键的值
     * @param key
     * @param items
     * @return List<Object>
     * <AUTHOR>
     * @date 2023年09月21日 16:55
     */
    public List<Object> hmultiGet(String key, Collection<String> items)
    {
        List<Object> result = null;
        try
        {
            result = redisTemplate.opsForHash().multiGet(key, items);
        }
        catch (Exception e)
        {
            log.error("hmultiGet has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 获取hashKey对应的所有键值
     * @param key
     * @return Map<Object, Object>
     * <AUTHOR>
     * @date 2023年09月21日 16:55
     */
    public Map<String, Object> hmget(String key)
    {
        Map<String, Object> map = null;
        try
        {
            map = redisTemplate.opsForHash().entries(key);
        }
        catch (Exception e)
        {
            log.error("hmget has exception:", e);
        }
        return map;
    }

    /**
     *
     * @Description 设置指定hashkey的值
     * @param key
     * @param map
     * @return boolean true：成功,false：失败
     * <AUTHOR>
     * @date 2023年09月21日 16:55
     */
    public boolean hmset(String key, Map<String, Object> map)
    {
        boolean result = false;
        try
        {
            redisTemplate.opsForHash().putAll(key, map);
            result = true;
        }
        catch (Exception e)
        {
            log.error("hmset has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 设置指定hashkey的值并设置时间
     * @param key
     * @param map
     * @return boolean true：成功,false：失败
     * <AUTHOR>
     * @date 2023年09月21日 16:55
     */
    public boolean hmset(String key, Map<String, Object> map, long time)
    {
        boolean result = false;
        try
        {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0)
            {
                expire(key, time);
            }
            result = true;
        }
        catch (Exception e)
        {
            log.error("hmset has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 向一张hash表中放入数据, 如果不存在将创建
     * @param key
     * @param item
     * @param value
     * @return boolean true：成功,false：失败
     * <AUTHOR>
     * @date 2023年09月21日 16:56
     */
    public boolean hset(String key, String item, Object value)
    {
        boolean result = false;
        try
        {
            redisTemplate.opsForHash().put(key, item, value);
            result = true;
        }
        catch (Exception e)
        {
            log.error("hset has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 向一张hash表中放入数据, 如果不存在将创建，并设置失效时间
     * @param key
     * @param item
     * @param value
     * @param time 单位秒
     * @return boolean true：成功,false：失败
     * <AUTHOR>
     * @date 2023年09月21日 16:56
     */
    public boolean hset(String key, String item, Object value, long time)
    {
        boolean result = false;
        try
        {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0)
            {
                expire(key, time);
            }
            result = true;
        }
        catch (Exception e)
        {
            log.error("hset has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 删除指定hashkey指定item数据
     * @param key
     * @param item
     * @return Object 返回原来的数据
     * <AUTHOR>
     * @date 2023年09月21日 16:57
     */
    public Object hdel(String key, String item)
    {
        Object object = hget(key, item);
        redisTemplate.opsForHash().delete(key, item);
        return object;
    }

    /**
     *
     * @Description 删除指定hashkey指定item数据，支持多个item
     * @param key
     * @param item
     * <AUTHOR>
     * @date 2023年09月21日 16:57
     */
    public void hdel(String key, String... item)
    {
        try
        {
            redisTemplate.opsForHash().delete(key, item);
        }
        catch (Exception e)
        {
            log.error("hdel has exception:", e);
        }
    }

    /**
     *
     * @Description 获取hash表中所有的item集合
     * @param key
     * @return Set<String>
     * <AUTHOR>
     * @date 2023年09月21日 16:58
     */
    public Set<String> hKeys(String key)
    {
        Set<String> result = null;
        try
        {
            result = redisTemplate.opsForHash().keys(key);
        }
        catch (Exception e)
        {
            log.error("hKeys has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 判断hash表中是否有该项的值
     * @param key
     * @param item
     * @return boolean true 存在 false不存在
     * <AUTHOR>
     * @date 2023年09月21日 16:58
     */
    public boolean hHasKey(String key, String item)
    {
        boolean result = false;
        try
        {
            result = redisTemplate.opsForHash().hasKey(key, item);
        }
        catch (Exception e)
        {
            log.error("hHasKey has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description hash递增 如果不存在,就会创建一个 并把新增后的值返回
     * @param key
     * @param item
     * @param delta
     * @return double
     * <AUTHOR>
     * @date 2023年09月21日 16:59
     */
    public double hincr(String key, String item, double delta)
    {
        double result = 0;
        try
        {
            result = redisTemplate.opsForHash().increment(key, item, delta);
        }
        catch (Exception e)
        {
            log.error("hincr has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description hash递减 如果不存在,就会创建一个 并把新增后的值返回
     * @param key
     * @param item
     * @param delta
     * @return double
     * <AUTHOR>
     * @date 2023年09月21日 16:59
     */
    public double hdecr(String key, String item, double delta)
    {
        double result = 0;
        try
        {
            result = redisTemplate.opsForHash().increment(key, item, -delta);
        }
        catch (Exception e)
        {
            log.error("hdecr has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 根据key获取Set中的所有值
     * @param key
     * @return Set<Object>
     * <AUTHOR>
     * @date 2023年09月21日 17:00
     */
    public Set<Object> sGet(String key)
    {
        Set<Object> set = null;
        try
        {
            set = redisTemplate.opsForSet().members(key);
        }
        catch (Exception e)
        {
            log.error("sGet has exception:", e);
        }
        return set;
    }

    /**
     *
     * @Description 根据value从一个set中查询, 是否存在
     * @param key
     * @param value
     * @return boolean true 存在 false不存在
     * <AUTHOR>
     * @date 2023年09月21日 17:01
     */
    public boolean sHasKey(String key, Object value)
    {
        boolean result = false;
        try
        {
            result = redisTemplate.opsForSet().isMember(key, value);
        }
        catch (Exception e)
        {
            log.error("sHasKey has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 将数据放入set缓存
     * @param key
     * @param values
     * @return long 放入成功数据数量
     * <AUTHOR>
     * @date 2023年09月21日 17:01
     */
    public long sSet(String key, Object... values)
    {
        long result = 0;
        try
        {
            result = redisTemplate.opsForSet().add(key, values);
        }
        catch (Exception e)
        {
            log.error("sSet has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 将set数据放入缓存，并设置失效时间
     * @param key
     * @param time
     * @param values
     * @return long 放入成功数据数量
     * <AUTHOR>
     * @date 2023年09月21日 17:02
     */
    public long sSetAndTime(String key, long time, Object... values)
    {
        long result = 0;
        try
        {
            result = redisTemplate.opsForSet().add(key, values);
            if (time > 0)
            {
                expire(key, time);
            }
        }
        catch (Exception e)
        {
            log.error("sSetAndTime has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 获取set缓存的长度
     * @param key
     * @return long
     * <AUTHOR>
     * @date 2023年09月21日 17:04
     */
    public long sGetSetSize(String key)
    {
        long result = 0;
        try
        {
            result = redisTemplate.opsForSet().size(key);
        }
        catch (Exception e)
        {
            log.error("sGetSetSize has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 移除值为value的
     * @param key
     * @param values
     * @return long 删除成功数据的数量
     * <AUTHOR>
     * @date 2023年09月21日 17:04
     */
    public long setRemove(String key, Object... values)
    {
        long result = 0;
        try
        {
            result = redisTemplate.opsForSet().remove(key, values);
        }
        catch (Exception e)
        {
            log.error("setRemove has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 获取list缓存的内容, start传0，end传-1可以查询全部数据
     * @param key
     * @param start
     * @param end
     * @return List<Object>
     * <AUTHOR>
     * @date 2023年09月21日 17:05
     */
    public List<Object> lGet(String key, long start, long end)
    {
        List<Object> list = null;
        try
        {
            list = redisTemplate.opsForList().range(key, start, end);
        }
        catch (Exception e)
        {
            log.error("lGet has exception:", e);
        }
        return list;
    }

    /**
     *
     * @Description 获取list缓存的长度
     * @param key
     * @return long
     * <AUTHOR>
     * @date 2023年09月21日 17:05
     */
    public long lGetListSize(String key)
    {
        long result = 0;
        try
        {
            result = redisTemplate.opsForList().size(key);
        }
        catch (Exception e)
        {
            log.error("lGetListSize has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 通过索引 获取list中的值
     * @param key
     * @param index 索引  index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return Object
     * <AUTHOR>
     * @date 2023年09月21日 17:06
     */
    public Object lGetIndex(String key, long index)
    {
        Object object = null;
        try
        {
            object = redisTemplate.opsForList().index(key, index);
        }
        catch (Exception e)
        {
            log.error("lGetIndex has exception:", e);
        }
        return object;
    }

    /**
     *
     * @Description 将list放入缓存尾部
     * @param key
     * @param value
     * @return boolean true：成功，false：失败
     * <AUTHOR>
     * @date 2023年09月21日 17:06
     */
    public boolean lSet(String key, Object value)
    {
        boolean result = false;
        try
        {
            redisTemplate.opsForList().rightPush(key, value);
            result = true;
        }
        catch (Exception e)
        {
            log.error("lSet has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 将list放入缓存尾部，并设置失效时间
     * @param key
     * @param value
     * @param time
     * @return boolean true：成功，false：失败
     * <AUTHOR>
     * @date 2023年09月21日 17:07
     */
    public boolean lSet(String key, Object value, long time)
    {
        boolean result = false;
        try
        {
            redisTemplate.opsForList().rightPush(key, value);
            if (time > 0)
            {
                expire(key, time);
            }
            result = true;
        }
        catch (Exception e)
        {
            log.error("lSet has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 将list放入缓存尾部
     * @param key
     * @param value
     * @return boolean true：成功，false：失败
     * <AUTHOR>
     * @date 2023年09月21日 17:08
     */
    public boolean lSet(String key, List<Object> value)
    {
        boolean result = false;
        try
        {
            redisTemplate.opsForList().rightPushAll(key, value);
            result = true;
        }
        catch (Exception e)
        {
            log.error("lSet has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 将list放入缓存尾部，并设置失效时间
     * @param key
     * @param value
     * @param time
     * @return boolean true：成功，false：失败
     * <AUTHOR>
     * @date 2023年09月21日 17:08
     */
    public boolean lSet(String key, List<Object> value, long time)
    {
        boolean result = false;
        try
        {
            redisTemplate.opsForList().rightPushAll(key, value);
            if (time > 0)
            {
                expire(key, time);
            }
            result = true;
        }
        catch (Exception e)
        {
            log.error("lSet has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 根据索引修改list中的某条数据
     * @param key
     * @param index
     * @param value
     * @return Object 返回修改前的值
     * <AUTHOR>
     * @date 2023年09月21日 17:09
     */
    public Object lUpdateIndex(String key, long index, Object value)
    {
        Object object = null;
        try
        {
            object = lGetIndex(key, index);
            redisTemplate.opsForList().set(key, index, value);
        }
        catch (Exception e)
        {
            log.error("lUpdateIndex has exception:", e);
        }
        return object;
    }

    /**
     *
     * @Description 移除N个值为value
     * @param key
     * @param count
     * @param value
     * @return long 返回移除成功数量
     * <AUTHOR>
     * @date 2023年09月21日 17:11
     */
    public long lRemove(String key, long count, Object value)
    {
        long result = 0L;
        try
        {
            result = redisTemplate.opsForList().remove(key, count, value);
        }
        catch (Exception e)
        {
            log.error("lRemove has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 从缓存头部获取数据，并删除
     * @param key
     * @return Object
     * <AUTHOR>
     * @date 2023年10月12日 14:28
     */
    public Object lPop(String key)
    {
        Object result = null;
        try
        {
            result = redisTemplate.opsForList().leftPop(key);
        }
        catch (Exception e)
        {
            log.error("leftPop has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 从缓存尾部获取数据，并删除
     * @param key
     * @param long
     * @return Object
     * <AUTHOR>
     * @date 2023年10月12日 14:28
     */
    public Object lPop(String key, long timeout)
    {
        Object result = null;
        try
        {
            result = redisTemplate.opsForList().leftPop(key, timeout, TimeUnit.SECONDS);
        }
        catch (Exception e)
        {
            log.error("rightPop has exception:", e);
        }
        return result;
    }

    /**
     *
     * @Description 给指定topic创建用户组
     * @param key
     * @param group
     * <AUTHOR>
     * @date 2023年10月12日 15:06
     */
    public void xAddGroup(String key, String group)
    {
        try
        {
            redisTemplate.opsForStream().createGroup(key, group);
        }
        catch (Exception e)
        {
            log.warn("key:{},group:{},has exists.", key, group);
        }
    }

    /**
     *
     * @Description 新增消息到Stream
     * @param key
     * @param content
     * @return String
     * <AUTHOR>
     * @date 2023年10月12日 15:13
     */
    public String xAdd(String key, Object value)
    {
        ObjectRecord<String, String> record = StreamRecords.newRecord().in(key).ofObject(JSON.toJSONString(value));
        RecordId recordId = redisTemplate.opsForStream().add(record);
        return recordId.getValue();
    }

    /**
     *
     * @Description 消费消息回执，回执后消息会被删除
     * @param key
     * @param group
     * @param ids
     * <AUTHOR>
     * @date 2023年10月12日 15:18
     */
    public void acknowledge(String key, String group, String... ids)
    {
        redisTemplate.opsForStream().acknowledge(key, group, ids);
    }
}
