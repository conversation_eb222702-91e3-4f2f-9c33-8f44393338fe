package cn.aliyun.ryytn.common.utils.validate;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.string.StringUtils;

/**
 *
 * @Description 校验字段工具类
 * <AUTHOR>
 * @date 2022年5月10日 上午10:13:05
 */
public class ValidateUtil
{
    /**
     * 比较类型：大于
     */
    private static final int COMPARE_TYPE_GT = 1;
    /**
     * 比较类型：等于
     */
    private static final int COMPARE_TYPE_EQ = 2;
    /**
     * 比较类型：小于
     */
    private static final int COMPARE_TYPE_LT = 3;

    /**
     *
     * @Description: 校验字符串不能为空
     * @param fieldValue
     *            字段值
     * @param errorCode
     *            错误码
     * @param errorMsg
     *            错误描述
     * @throws ServiceException
     *             异常
     * <AUTHOR>
     * @date 2022年5月10日 上午10:13:05
     */
    public static void checkIsNotEmpty(String fieldValue) throws ServiceException
    {
        fieldValue = StringUtils.getValue(fieldValue);
        if (StringUtils.isBlank(fieldValue))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
    }

    /**
     *
     * @Description: 校验对象不能为空
     * @param fieldValue
     *            对象
     * @throws ServiceException
     *             异常
     * <AUTHOR>
     * @date 2022年5月10日 上午10:13:05
     */
    public static void checkIsNotEmpty(Object fieldValue) throws ServiceException
    {
        if (null == fieldValue)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
    }

    /**
     *
     * @Description: 校验集合不能为空
     * @param fieldValue
     *            字段值
     * @throws ServiceException
     *             异常
     * <AUTHOR>
     * @date 2022年5月10日 上午10:13:05
     */
    public static void checkIsNotEmpty(List<?> fieldValue) throws ServiceException
    {
        if (CollectionUtils.isEmpty(fieldValue))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_EMPTY);
        }
    }

    /**
     *
     * @Description: 校验字符串是否非法
     * @param fieldValue
     *            字段值
     * @param regexp
     *            正则表达式
     * @param isEmpty
     *            是否可以为空, true：是 false：否
     * @throws ServiceException
     *             异常
     * <AUTHOR>
     * @date 2022年5月10日 上午10:13:05
     */
    public static void checkStrRegexp(String fieldValue, String regexp, boolean isEmpty) throws ServiceException
    {
        fieldValue = StringUtils.getValue(fieldValue);
        if (isEmpty && StringUtils.isBlank(fieldValue))
        {
            return;
        }

        checkIsNotEmpty(fieldValue);

        if (StringUtils.isNoneBlank(regexp))
        {
            if (!fieldValue.matches(regexp))
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
            }
        }
    }

    /**
     *
     * @Description: 校验字符串长度
     * @param fieldValue
     *            字段值
     * @param minLen
     *            最小长度
     * @param maxLen
     *            最大长度
     * @param isEmpty
     *            是否可以为空, true：是 false：否
     * @throws ServiceException
     *             异常
     * <AUTHOR>
     * @date 2022年5月10日 上午10:13:05
     */
    public static void checkStrLength(String fieldValue, int minLen, int maxLen, boolean isEmpty) throws ServiceException
    {
        fieldValue = StringUtils.getValue(fieldValue);
        if (isEmpty && StringUtils.isBlank(fieldValue))
        {
            return;
        }

        checkIsNotEmpty(fieldValue);

        int fieldLength = null == fieldValue ? 0 : fieldValue.length();
        if (fieldLength < minLen || fieldLength > maxLen)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
    }

    /**
     *
     * @Description: 校验数字在范围内
     * @param fieldValue
     *            字段值
     * @param minValue
     *            最小值
     * @param maxValue
     *            最大值
     * @throws ServiceException
     *             异常
     * <AUTHOR>
     * @date 2022年5月10日 上午10:13:05
     */
    public static void checkNumRange(int fieldValue, int minValue, int maxValue) throws ServiceException
    {
        if (fieldValue < minValue || fieldValue > maxValue)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
    }

    /**
     *
     * @Description: 校验数字大小
     * @param fieldValue
     *            字段值
     * @param compareValue
     *            比较值
     * @param type
     *            1:大于 2：等于 3：小于
     * @throws ServiceException
     *             异常
     * <AUTHOR>
     * @date 2022年5月10日 上午10:13:05
     */
    public static void checkNum(long fieldValue, long compareValue, int type) throws ServiceException
    {
        if (COMPARE_TYPE_GT == type)
        {
            if (fieldValue > compareValue)
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
            }
        }
        else if (COMPARE_TYPE_EQ == type)
        {
            if (fieldValue == compareValue)
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
            }
        }
        else if (COMPARE_TYPE_LT == type)
        {
            if (fieldValue < compareValue)
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
            }
        }
    }

    /**
     *
     * @Description 校验数字是大于1的正整数
     * @param fieldValue
     *            字段值
     * @throws ServiceException
     *             异常
     * <AUTHOR>
     * @date 2022年5月10日 上午10:13:05
     */
    public static void checkNum(Integer fieldValue) throws ServiceException
    {
        if (null == fieldValue)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_EMPTY);
        }

        if (fieldValue < 1)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
    }

    /**
     *
     * @Description: 校验是long类型
     * @param fieldValue
     *            字段值
     * @throws ServiceException
     *             异常
     * <AUTHOR>
     * @date 2022年5月10日 上午10:13:05
     */
    public static void checkLong(Object fieldValue) throws ServiceException
    {
        checkIsNotEmpty(fieldValue);

        try
        {
            Long.valueOf(String.valueOf(fieldValue));
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
    }

    /**
     *
     * @Description 校验时间格式
     * @param fieldValue
     *            字段值
     * @param pattern
     *            时间格式
     * @param isEmpty
     *            是否可以为空
     * @param isOverCurTime
     *            是否可以超过当前时间
     * @throws ServiceException
     *             异常
     * <AUTHOR>
     * @date 2022年5月10日 上午10:13:05
     */
    public static void checkTimeFormat(String fieldValue, String pattern, boolean isEmpty, boolean isOverCurTime) throws ServiceException
    {
        fieldValue = StringUtils.getValue(fieldValue);
        if (isEmpty && StringUtils.isBlank(fieldValue))
        {
            return;
        }

        Date date = null;
        Date curDate = null;
        try
        {
            SimpleDateFormat format = new SimpleDateFormat(pattern);
            format.setLenient(false);
            date = format.parse(fieldValue);

            String s = format.format(new Date());
            curDate = format.parse(s);
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }

        if (isOverCurTime && date.before(curDate))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
    }

    /**
     *
     * @Description 校验数字大小
     * @param fieldValue
     * @param compareValue
     * @param type 1:大于 2：等于 3：小于
     * @param isEmpty
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年08月04日 16:48
     */
    public static void checkNum(BigDecimal fieldValue, BigDecimal compareValue, int type, boolean isEmpty) throws ServiceException
    {
        if (isEmpty && Objects.isNull(fieldValue))
        {
            return;
        }

        if (Objects.isNull(fieldValue))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_EMPTY);
        }

        if (COMPARE_TYPE_GT == type)
        {
            if (fieldValue.compareTo(compareValue) > 0)
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
            }
        }
        else if (COMPARE_TYPE_EQ == type)
        {
            if (fieldValue.compareTo(compareValue) == 0)
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
            }
        }
        else if (COMPARE_TYPE_LT == type)
        {
            if (fieldValue.compareTo(compareValue) < 0)
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
            }
        }
    }

    /**
     *
     * @Description 小数位判断
     * @param fieldValue
     * @param scale 小数位
     * @param isEmpty
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年08月04日 16:54
     */
    public static void checkNumScale(BigDecimal fieldValue, int scale, boolean isEmpty) throws ServiceException
    {
        if (isEmpty && Objects.isNull(fieldValue))
        {
            return;
        }

        if (Objects.isNull(fieldValue))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_EMPTY);
        }

        if (fieldValue.scale() > scale)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
    }

}
