package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 阿里dataq任务
 * <AUTHOR>
 * @date 2023/12/7 16:31
 */
@Setter
@Getter
@ToString
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DataqTask implements Serializable
{
    private static final long serialVersionUID = -3418942995376137712L;
    private Long id;

    private Long taskId;

    private String jobId;

    private String appCode;

    private String param;

    private String lockKey;

    private String status;

    private Long reloadId;

    private Date startTime;

    private Date endTime;
}
