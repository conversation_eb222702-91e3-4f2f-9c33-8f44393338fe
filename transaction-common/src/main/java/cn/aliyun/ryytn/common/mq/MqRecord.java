package cn.aliyun.ryytn.common.mq;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 消息队列数据记录
 * <AUTHOR>
 * @date 2023/10/13 15:44
 */
@Setter
@Getter
@ToString
public class MqRecord implements Serializable
{
    private static final long serialVersionUID = -8792681405308316215L;
    /**
     * 唯一编号
     */
    private String id;
    /**
     * 主题
     */
    private String topic;
    /**
     * 消费组
     */
    private String group;
    /**
     * 业务数据json
     */
    private String value;
    /**
     * 消费时间
     */
    private long timestamp;
}
