package cn.aliyun.ryytn.common.constants;

import java.util.HashMap;
import java.util.Map;

import com.aliyun.brain.dataindustry.common.enums.TaskStatus;

/**
 * @Description 公共常量类
 * <AUTHOR>
 * @date 2023/9/25 11:21
 */
public class CommonConstants
{

    /**
     * session失效期单位秒
     */
    public static final long SESSION_EXPIRE_TIME = 1800L;
    /**
     * 树形结构根节点的父节点编号
     */
    public static final String TREE_ROOT_PARENTID = "-1";

    /**
     * 字符串拼接分隔符
     */
    public static final String STRING_SEPARATOR = ",";

    /**
     * 数据类型，业务数据表dataType字段：初始化预置数据，不可以修改/删除
     */
    public static final Integer DATA_TYPE_INITIAL = 1;

    /**
     * 数据类型，业务数据表dataType字段：系统管理新增的数据，可以修改/删除
     */
    public static final Integer DATA_TYPE_MANAGEMENT = 2;

    /**
     * 数据类型，业务数据表dataType字段：外部系统同步的数据
     */
    public static final Integer DATA_TYPE_EXTERNAL = 3;

    /**
     * 数据状态，业务数据表status字段：正常
     */
    public static final Integer DATA_STATUS_ENABLE = 1;

    /**
     * 数据状态，业务数据表status字段：停用
     */
    public static final Integer DATA_STATUS_DISABLE = 2;

    /**
     * 数据状态，业务数据表deleteFlag字段：未删除
     */
    public static final Integer DELETE_FLAG_NO = 0;

    /**
     * 数据状态，业务数据表deleteFlag字段：已删除
     */
    public static final Integer DELETE_FLAG_YES = 1;

    /**
     * 超级管理员角色编号
     */
    public static final String SYSADMIN_ROLE_ID = "100000001";

    /**
     * 时间粒度字段名映射
     */
    public static final Map<BizDateTypeEnum, String> BIZDATETYPE_COLUMNNAME_MAP = new HashMap<BizDateTypeEnum, String>();

    static
    {
        BIZDATETYPE_COLUMNNAME_MAP.put(BizDateTypeEnum.DAY, "^[y]\\d{4}[m]\\d{2}[d]\\d{2}$");
        BIZDATETYPE_COLUMNNAME_MAP.put(BizDateTypeEnum.WEEK, "^[y]\\d{4}[m]\\d{2}[w]\\d{2}[_]\\d{2}[_]\\d{2}$");
        BIZDATETYPE_COLUMNNAME_MAP.put(BizDateTypeEnum.MONTH, "^[y]\\d{4}[m]\\d{2}$");
        BIZDATETYPE_COLUMNNAME_MAP.put(BizDateTypeEnum.QUARTER, "^[y]\\d{4}[q]\\d{2}$");
        BIZDATETYPE_COLUMNNAME_MAP.put(BizDateTypeEnum.YEAR, "^[y]\\d{4}$");
    }

    /**
     * 所有字典缓存KEY，list存储，key：DICTDATA:ALL:${DICTTYPE}，value：对应dictType的dictData列表
     */
    public static final String DICTDATA_ALL_CACHE_KEY = "DICTDATA:ALL:{}";

    /**
     * 所有字典缓存KEY，Hash存储，key：DICTDATA:CODE2NAME:${DICTTYPE}，item：dictData.code，value：dictData对象
     */
    public static final String DICTDATA_CODE2NAME_CACHE_KEY = "DICTDATA:CODE2NAME:{}";

    /**
     * 所有字典缓存KEY，Hash存储，key：DICTDATA:NAME2CODE:${DICTTYPE}，item：dictData.name，value：dictData对象
     */
    public static final String DICTDATA_NAME2CODE_CACHE_KEY = "DICTDATA:NAME2CODE:{}";

    /**
     * 系统参数配置缓存KEY,Hash存储，key：SYSTEMCONFIG:ALL，item：configId，value:configValue
     */
    public static final String SYSTEMCONFIG_ALL_CACHE_KEY = "SYSTEMCONFIG:ALL";

    /**
     * 登录验证码缓存KEY，key：CAPTCHA:LOGIN:${sessionId}，value：验证码
     * 不用Hash存储，方便使用key失效机制控制验证码有效期
     * 不用列表存储，登录验证码同时只有一个有效
     */
    public static final String LOGIN_CAPTCHA_CACHE_KEY = "CAPTCHA:LOGIN:{}";

    /**
     * 会话缓存KEY，key：SESSION:${sessionId}，value：Session对象
     */
    public static final String SESSION_CACHE_KEY = "SESSION:{}";

    /**
     * REDIS STREAM实现MQ KE
     */
    public static final String REDIS_STREAM_MQ_KEY = "REDIS_STREAM_MQ:{}";

    /**
     * REDIS LIST实现MQ KEY
     */
    public static final String REDIS_LIST_MQ_KEY = "REDIS_LIST_MQ:{}";

    /**
     * 电子表格导入导出任务，key: EXECLTASK:${任务处理Bean名称}，value: ExcelTask对象
     */
    public static final String EXCELTASK_KEY = "EXCELTASK:{}";

    /**
     * 单点登录签名缓存KEY，防止同一签名被重复使用，key: SSO_SIGNATURE:${requestSource}:${rid}，value: ssoSignature对象
     */
    public static final String SSO_SIGNATURE_RID_KEY = "SSO_SIGNATURE:{}:{}";

    /**
     * 日历日期周缓存KEY，item：周第一天日期yyyyMMdd
     */
    public static final String CALENDAR_DAYWEEK_CACHE_KEY = "CALENDAR:DAYWEEK";

    /**
     * 日历日期周缓存KEY，item：月周标识yyyyMMWw
     */
    public static final String CALENDAR_MONTHWEEK_CACHE_KEY = "CALENDAR:MONTHWEEK";

    /**
     * 销售SKU产品缓存
     */
    public static final String SKU_CACHE_KEY = "SKU:{}";

    /**
     * 算法调度分布式锁
     */
    public static final String REDIS_ALGO_DISTRIBUTED_LOCK_KEY = "LOCK:ALGO_DISTRIBUTED_LOCK";

    /**
     * oms同步共识数据文件生成锁
     */
    public static final String REDIS_OMS_FILE_LOCK_KEY = "LOCK:OMS_FILE_LOCK";

    /**
     * 仓库逻辑仓到物理仓映射缓存key
     */
    public static final String WAREHOUSE_LOGIC_TO_PHYSIC_KEY = "WAREHOUSE:LOGIC_TO_PHYSIC";

    /**
     * 仓库物理仓到逻辑仓映射缓存key
     */
    public static final String WAREHOUSE_PHYSIC_TO_LOGIC_KEY = "WAREHOUSE:PHYSIC_TO_LOGIC";

    /**
     * 确认分仓需求计划分布式锁,LOCK:CONFIRM_WAREHOUSE_DEMAND_PLAN:计划编号:版本号:分组编号
     */
    public static final String REDIS_CONFIRM_WAREHOUSE_DEMAND_PLAN_LOCK_KEY = "LOCK:CONFIRM_WAREHOUSE_DEMAND_PLAN:{}:{}:{}";

    /**
     * 确认渠道需求计划分布式锁,LOCK:CONFIRM_CHANNEL_DEMAND_PLAN:计划编号:版本号:分组编号
     */
    public static final String REDIS_CONFIRM_CHANNEL_DEMAND_PLAN_LOCK_KEY = "LOCK:CONFIRM_CHANNEL_DEMAND_PLAN:{}:{}:{}";

    /**
     * 发布分仓需求提报分布式锁,LOCK:PUBLISH_WAREHOUSE_DEMAND_REPORT:计划编号:版本号
     */
    public static final String REDIS_PUBLISH_WAREHOUSE_DEMAND_REPORT_LOCK_KEY = "LOCK:PUBLISH_WAREHOUSE_DEMAND_REPORT:{}:{}";

    /**
     * 生成日分仓编辑分布式锁,LOCK:GEN_DAILY_DEMAND:计划编号:版本号
     */
    public static final String REDIS_GEN_DAILY_DEMAND_LOCK_KEY = "LOCK:GEN_DAILY_DEMAND:{}:{}";

    /**
     * 生成日分仓调拨计划分布式锁,LOCK:GEN_DAILY_AIPLAN:计划编号:版本号
     */
    public static final String REDIS_GEN_DAILY_AIPLAN_LOCK_KEY = "LOCK:GEN_DAILY_AIPLAN:{}:{}";

    /**
     * 导入渠道需求提报分布式锁,LOCK:IMPORT_CHANNEL_DEMAND_REPORT:版本号
     */
    public static final String REDIS_IMPORT_CHANNEL_DEMAND_REPORT_LOCK_KEY = "LOCK:IMPORT_CHANNEL_DEMAND_REPORT:{}";

    /**
     * 新增分仓需求计划版本分布式锁,LOCK:ADD_WAREHOUSE_DEMAND_PLAN_VERSION:计划编号:版本号
     */
    public static final String REDIS_ADD_WAREHOUSE_DEMAND_PLAN_VERSION_LOCK_KEY = "LOCK:ADD_WAREHOUSE_DEMAND_PLAN_VERSION:{}:{}";

    /**
     * 新增分仓需求计划版本分布式锁,LOCK:ADD_WAREHOUSE_DEMAND_REPORT_VERSION:计划编号:版本号
     */
    public static final String REDIS_ADD_WAREHOUSE_DEMAND_REPORT_VERSION_LOCK_KEY = "LOCK:ADD_WAREHOUSE_DEMAND_REPORT_VERSION:{}:{}";

    /**
     * 调拨计划触发任务topic
     */
    public static final String TOPIC_GENERATE_FREIGHTPLAN = "topic_generate_freightplan";

    /**
     * OMS数据同步topic
     */
    public static final String TOPIC_OMS_FILE = "topic_generate_oms_file";

    /**
     * 计划数量正则（非负整数）
     */
    public static final String PLAN_VALUE_PATTERN = "^[1-9]\\d*|0$";

    /**
     * 刷新阿里算法仓容中间表调度任务编号
     */
    public static final String REFRESH_SO_WT_STOCK_CAPACITY_JOBID = "100000018";

    /**
     * 调拨任务缓存KEY：FREIGHT_TASK:计划编号
     */
    public static final String FREIGHT_TASK_KEY = "FREIGHT_TASK:{}";

    /**
     * 复制渠道需求计划锁KEY：LOCK:DUPLICATE_CHANNELDEMAND_PLAN:渠道需求计划
     */
    public static final String REDIS_DUPLICATE_CHANNELDEMAND_PLAN_LOCK_KEY = "LOCK:DUPLICATE_CHANNELDEMAND_PLAN:{}";

    /**
     * 调拨计划版本号自增序列：INCREAMENT:FREIGHT_PLAN:计划编号:yyyyMMdd
     */
    public static final String INCREAMENT_FREIGHT_PLAN_PREDICTION_VERSION_KEY = "INCREAMENT:FREIGHT_PLAN:{}:{}";

    /**
     * 重新生成调拨计划分布式锁KEY：LOCK:GENERATE_FREIGHT_PLAN:计划编号
     */
    public static final String REDIS_GENERATE_FREIGHT_PLAN_LOCK_KEY = "LOCK:GENERATE_FREIGHT_PLAN:{}";

    /**
     * dataq未完成任务状态
     */
    public static final String[] TASK_STATUS_NOT_COMPLETED = {TaskStatus.INIT.name(), TaskStatus.RUNNING.name(), TaskStatus.STOPPING.name()};

    /**
     * dataq需要补偿任务状态
     */
    public static final String[] TASK_STATUS_NEED_RELOAD = {TaskStatus.FAILED.name()};

    /**
     * dataq任务成功状态
     */
    public static final String TASK_STATUS_SUCCESS = "SUCCESS";

    /**
     * dataq任务超时状态
     */
    public static final String TASK_STATUS_TIMEOUT = "TIMEOUT";

    /**
     * 达人活动类型
     */
    public static final String TALENT = "TALENT";

    /**
     * 新增
     */
    public static final String ADD = "ADD";

    /**
     * 更新
     */
    public static final String UPDATE = "UPD";

    /**
     * 删除
     */
    public static final String DELETE = "DEL";

    /**
     * C
     */
    public static final String C = "C";

    /**
     * 类
     */
    public static final String LEI_CHINESE = "类";

    /**
     * 下划线
     */
    public static final String UNDERLINE_STRING = "_";

    /**
     * 字符串1
     */
    public static final String STRING_ONE = "1";

    /**
     * 字符串0
     */
    public static final String STRING_ZERO = "0";

    /**
     * 渠道需求计划列表数据优化前缀key
     */
    public static final String CALENDAR_TABLE_DATA_PRE_KEY = "CALENDAR_FAST_CACHE:";

    /**
     * 渠道需求计划列表数据优化页码key
     */
    public static final String CALENDAR_TABLE_DATA_PAGE_KEY = "PAGE_NUM_";
}
