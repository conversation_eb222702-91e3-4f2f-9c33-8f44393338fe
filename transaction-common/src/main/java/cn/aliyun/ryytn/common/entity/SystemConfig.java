package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * @Description 系统配置
 * <AUTHOR>
 * @date 2022年6月2日 下午4:18:59
 */
@Setter
@Getter
@ToString
public class SystemConfig implements Serializable
{
    private static final long serialVersionUID = 6280579923926514826L;
    /**
     * @Description 配置类型编号
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private String categoryId;

    /**
     * @Description 配置编号
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private String configId;

    /**
     * @Description 配置名称
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private String configName;

    /**
     * @Description 配置类型，1：密码类型；2：文本类型；3：文件类型；4：枚举类型；5：数字文本；6：文本域
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private Integer configType;

    /**
     * @Description 配置值
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private String configValue;

    /**
     * @Description 配置状态（-1：已删除； 0：禁用； 1：启用）
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private String status;

    /**
     * @Description 是否展示，0:不展示, 1:展示
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private Integer isDisplay;

    /**
     * @Description 校验规则，正则表达式
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private String validator;

    /**
     * @Description 配置描述
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private String description;

    /**
     * @Description 排序编号
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private Integer sortNo;

    /**
     * @Description 创建人
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private String createdBy;

    /**
     * @Description 创建时间
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date createdTime;

    /**
     * @Description 修改人
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    private String updatedBy;

    /**
     * @Description 修改时间
     * <AUTHOR>
     * @date 2022年6月2日 下午4:19:10
     */
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date updatedTime;
}
