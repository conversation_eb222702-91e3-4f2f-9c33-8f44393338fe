package cn.aliyun.ryytn.common.dataq;

/**
 * @Description Dataq特殊参数枚举
 * <AUTHOR>
 * @date 2023/11/2 14:27
 */
public enum DataqSpecialParamEnum
{
    /**
     * 指定返回字段（多个时逗号分隔）
     */
    return_fields,
    /**
     * 聚合计算字段（聚合字段,运算方式,聚合后字段名）
     *
     * （多组聚合函数时分号分隔）
     */
    aggregation,
    /**
     * 分组字段（多个时逗号分隔）
     */
    group_by,
    /**
     * 排序字段（默认为顺序，逆序加desc）（多个时逗号分隔）
     */
    order_by,
    /**
     * 分页大小
     */
    page_size,
    /**
     * 分页页码（同时返回下一页页码）
     */
    page_token,
    /**
     * 指定返回查询数据总量
     */
    fetch_all,
    /**
     * 返回数据转小写
     */
    return_lower_case;
}
