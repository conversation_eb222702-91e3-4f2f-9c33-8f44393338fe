package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description Dataq日历周粒度数据
 * <AUTHOR>
 * @date 2023/11/2 15:32
 */
@Setter
@Getter
@ToString
@ApiModel("Dataq日历周粒度数据")
public class DataqWeek implements Serializable
{
    private static final long serialVersionUID = 8557700341397170329L;
    /**
     * 财年的第几周
     */
    @ApiModelProperty("财年的第几周")
    private String weekOfFsclYear;

    /**
     * 周开始日期，yyyyMMdd
     */
    @ApiModelProperty("周开始日期，yyyyMMdd")
    private String fsclWeekStart;

    /**
     * 财年,yyyy
     */
    @ApiModelProperty("财年，yyyy")
    private String fsclYear;

    /**
     * 财年月的第几周
     */
    @ApiModelProperty("财年月的第几周")
    private Integer weekOfFsclMonth;

    /**
     * 周的范围，周第一天到周最后一天，yyyyMMdd-yyyyMMdd
     */
    @ApiModelProperty("周的范围，周第一天到周最后一天，yyyyMMdd-yyyyMMdd")
    private String fsclWeekRange;

    /**
     * 财年第几月
     */
    @ApiModelProperty("财年第几月")
    private Integer monthOfFsclYear;

    /**
     * 周结束日期：yyyyMMdd
     */
    @ApiModelProperty("周结束日期：yyyyMMdd")
    private String fsclWeekEnd;

    /**
     * 财年月：yyyyMM
     */
    @ApiModelProperty("财年月：yyyyMM")
    private String fsclYearMonth;

    /**
     *
     * @Description 查询该周所有日列表
     * @return List<String>
     * <AUTHOR>
     * @date 2024年01月01日 16:19
     */
    @JsonIgnore
    public List<String> getDayList()
    {
        if (StringUtils.isEmpty(this.fsclWeekRange) || !this.fsclWeekRange.contains(StringUtils.DATE_SEPARATOR))
        {
            return Collections.EMPTY_LIST;
        }
        String[] strs = StringUtils.split(this.fsclWeekRange, StringUtils.DATE_SEPARATOR);
        String startDay = strs[0];
        String endDay = strs[1];

        List dateList = new ArrayList<>();
        LocalDate startDate = LocalDate.parse(startDay, DateTimeFormatter.ofPattern(DateUtils.YMD));
        LocalDate endDate = LocalDate.parse(endDay, DateTimeFormatter.ofPattern(DateUtils.YMD));
        long numOfDays = ChronoUnit.DAYS.between(startDate, endDate);
        for (int i = 0; i <= numOfDays; i++)
        {
            LocalDate currentDate = startDate.plusDays(i);
            String format = currentDate.format(DateTimeFormatter.ofPattern(DateUtils.YMD));
            dateList.add(format);
        }
        return dateList;
    }

    /**
     *
     * @Description 获取月周，MMWw
     * @return String
     * <AUTHOR>
     * @date 2023年11月02日 15:56
     */
    @JsonIgnore
    public String getMonthWeek()
    {
        return new StringBuilder().append(String.format("%02d", monthOfFsclYear)).append(StringUtils.WEEK_PREFIX_UPPER).append(weekOfFsclMonth)
            .toString();
    }

    /**
     *
     * @Description 获取年月周，yyyyMMWw
     * @return String
     * <AUTHOR>
     * @date 2023年11月02日 15:57
     */
    @JsonIgnore
    public String getYearMonthWeek()
    {
        return new StringBuilder().append(fsclYear).append(String.format("%02d", monthOfFsclYear)).append(StringUtils.WEEK_PREFIX_UPPER).append(weekOfFsclMonth)
            .toString();
    }

    /**
     *
     * @Description 获取月周，MM月Ww
     * @return String
     * <AUTHOR>
     * @date 2023年11月02日 15:56
     */
    @JsonIgnore
    public String getMonthWeekLabel()
    {
        return new StringBuilder().append(monthOfFsclYear).append(StringUtils.MONTH_UNIT).append(StringUtils.WEEK_PREFIX_UPPER).append(weekOfFsclMonth)
            .toString();
    }

    /**
     *
     * @Description 获取年月周，yyyy年MM月Ww
     * @return String
     * <AUTHOR>
     * @date 2023年11月02日 15:57
     */
    @JsonIgnore
    public String getYearMonthWeekLabel()
    {
        return new StringBuilder().append(fsclYear).append(StringUtils.YEAR_UNIT).append(monthOfFsclYear).append(StringUtils.MONTH_UNIT)
            .append(StringUtils.WEEK_PREFIX_UPPER).append(weekOfFsclMonth).toString();
    }

    /**
     *
     * @Description 获取开始日期，dd
     * @return String
     * <AUTHOR>
     * @date 2023年11月16日 17:53
     */
    @JsonIgnore
    public String getStartDay()
    {
        return fsclWeekStart.substring(6);
    }

    /**
     *
     * @Description 获取结束日期，dd
     * @return String
     * <AUTHOR>
     * @date 2023年11月16日 17:53
     */
    @JsonIgnore
    public String getEndDay()
    {
        return fsclWeekEnd.substring(6);
    }

    /**
     *
     * @Description 获取月颗粒度标题，MM月
     * @return String
     * <AUTHOR>
     * @date 2023年11月16日 17:50
     */
    @JsonIgnore
    public String getMonthLabel()
    {
        return new StringBuilder().append(monthOfFsclYear).append(StringUtils.MONTH_UNIT).toString();
    }

    /**
     *
     * @Description 获取周颗粒度标题，MM月Ww(n-n)
     * @return String
     * <AUTHOR>
     * @date 2023年11月16日 17:50
     */
    @JsonIgnore
    public String getWeekLabel()
    {
        return new StringBuilder().append(monthOfFsclYear).append(StringUtils.MONTH_UNIT)
            .append(StringUtils.WEEK_PREFIX_UPPER).append(weekOfFsclMonth)
            .append(StringUtils.S_PARENTHESES_LEFT).append(getStartDay()).append(StringUtils.DATE_SEPARATOR).append(getEndDay())
            .append(StringUtils.S_PARENTHESES_RIGHT).toString();
    }

    /**
     *
     * @Description 某天是否在这周
     * @param dateRecorded
     * @return
     * <AUTHOR>
     * @date 2023年12月22日 9:58     */
    @JsonIgnore
    public boolean isInWeek(String dateRecorded)
    {
        int start = Integer.parseInt(fsclWeekStart);
        int end = Integer.parseInt(fsclWeekEnd);
        int checkDay = Integer.parseInt(dateRecorded);
        for (int i = start; i <= end; i++)
        {
            if (checkDay == i)
            {
                return true;
            }
        }
        return false;
    }

}
