package cn.aliyun.ryytn.common.excel;

import java.util.List;

import org.apache.poi.ss.usermodel.Sheet;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;

/**
 * @Description 隐藏列处理类
 * <AUTHOR>
 * @date 2023/10/27 15:51
 */
public class HideColSheetWriterHandler implements SheetWriteHandler
{
    /**
     * 隐藏列索引列表
     */
    private List<Integer> colList;

    public HideColSheetWriterHandler(List<Integer> colList)
    {
        this.colList = colList;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder)
    {
        Sheet sheet = writeSheetHolder.getSheet();
        for (Integer i : colList)
        {
            sheet.setColumnHidden(i, true);
        }
    }
}
