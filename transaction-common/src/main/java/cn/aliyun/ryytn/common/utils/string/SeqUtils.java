package cn.aliyun.ryytn.common.utils.string;

import java.net.InetAddress;
import java.util.Random;
import java.util.UUID;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * @Description 唯一编号生成工具类 
 * <AUTHOR>
 * @date 2023年9月19日 下午2:09:21
 */
@Data
@Slf4j
public class SeqUtils
{
    /**
     * 集群ID 暂时废弃，原5位的workerId修改为10位
     * 机器ID 2进制10位 2^10 - 1 = 1023个 未来通过注册生成，当前通过mac地址模余1024获取
     */
    private static long workerId;
    static
    {
        try
        {
            String ip = InetAddress.getLocalHost().getHostAddress();
            workerId = Math.abs(ip.hashCode()) % 1024;
        }
        catch (Exception e)
        {
            Random random = new Random();
            workerId = random.nextInt(1024) - 1;
            log.error("EduSequence has exception:", e);
            log.error("Please check hosts config.Make Sure the hostname has bean config in hosts.");
            log.error("WorkerId get random value is:" + workerId);
        }
    }

    /**
     * 设置一个时间初始值 2进制41位 2^41 - 1差不多可以用69年
     * 2021-01-01 => 1609430400000
     */
    private static final long EPOCH = 1609430400000L;

    /**
     * 12位的最新序号
     */
    private static final long SEQUENCE_BITS = 12L;
    /**
     * 10位的机器id
     */
    private static final long WORKERID_BITS = 10L;

    /**
     * 10 bit机器id最大值31
     */
    private static final long MAX_WORKERID = -1L ^ (-1L << WORKERID_BITS);

    /**
     * 序号移位
     */
    private static final long SEQUENCE_SHIFT = -1L ^ (-1L << SEQUENCE_BITS);

    /**
     * 机器id移位
     */
    private static final long WORKERID_SHIFT = SEQUENCE_BITS;

    /**
     * 时间戳移位
     */
    private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS + WORKERID_BITS;

    /**
     * 序号
     */
    private static long sequence;

    /**
     * 上次生成雪花编号时间
     */
    private static long lastTimestamp;

    /**
     * 
     * @Description 获取顺序唯一编号，用于需要入库的业务数据唯一编号，有序查询效率较高
     * @return String
     * <AUTHOR>
     * @date 2023年5月17日 下午4:46:25
     */
    public static synchronized String getSequenceUid()
    {
        // 因为二进制里第一个 bit 为如果是 1，那么都是负数，但是我们生成的 id 都是正数，所以第一个 bit 统一都是 0。
        // 代表一毫秒内生成的多个id的最新序号 2进制12位 2^12 - 1 = 4095个

        // 获取当前时间戳，单位毫秒
        long timestamp = System.currentTimeMillis();
        if (timestamp < lastTimestamp)
        {
            log.error("Found time roll back!");
            return null;
        }
        // 同一个毫秒内生成多个id，seqence序号递增1，最大是4095
        if (lastTimestamp == timestamp)
        {
            // 这个位运算避免sequence超过4095
            sequence = (sequence + 1) & SEQUENCE_SHIFT;
            // 当某一毫秒产生的id数超过4095，系统会进入等待，直到下一毫秒继续产生id
            if (sequence == 0)
            {
                while (timestamp <= lastTimestamp)
                {
                    timestamp = System.currentTimeMillis();
                }
            }
        }
        else
        {
            sequence = 0;
        }
        lastTimestamp = timestamp;

        // 二进制位运算操作，生成一个64bit的id
        return String.valueOf(((timestamp - EPOCH) << TIMESTAMP_SHIFT) | (workerId << WORKERID_SHIFT) | sequence);
    }

    /**
     * 
     * @Description 获取随机唯一编号，用于安全等无序场景的唯一编号生成
     * @return String
     * <AUTHOR>
     * @date 2023年5月17日 下午4:45:31
     */
    public static String getRandomUid()
    {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
