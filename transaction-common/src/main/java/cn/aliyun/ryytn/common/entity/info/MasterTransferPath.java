package cn.aliyun.ryytn.common.entity.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 调拨路径
 *
 * <AUTHOR>
 * @since 2025-05-27 13:38
 */
@Data
@ApiModel("调拨路径")
public class MasterTransferPath implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updatedTime;

    /**
     * 状态 1-生效 2-失效
     */
    @ApiModelProperty("状态 1-生效 2-失效")
    private Integer status;

    /**
     * 发货仓ID
     */
    @ApiModelProperty("发货仓ID")
    private String sendWarehouseId;

    /**
     * 收货仓ID
     */
    @ApiModelProperty("收货仓ID")
    private String receiveWarehouseId;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    private Date activeTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Date disableTime;
}
