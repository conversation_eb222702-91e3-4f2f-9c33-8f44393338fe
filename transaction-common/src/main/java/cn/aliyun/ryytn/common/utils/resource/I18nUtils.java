package cn.aliyun.ryytn.common.utils.resource;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import cn.aliyun.ryytn.common.utils.string.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 资源工具类
 * <AUTHOR>
 * @date 2022年5月31日 下午6:16:58
 */
@Slf4j
public class I18nUtils
{
    /**
     * 国际化资源文件的名称
     */
    private static final String LANGUAGE_FILE_NAME = "i18n/message";

    public static final String ZH = "zh";

    public static final String EN = "en";

    /**
     *  暂时支持中文/英文
     */
    private static Map<String, String> language = new HashMap<String, String>();

    static
    {
        language.put(ZH, "zh_CN");
        language.put(EN, "en_US");
    }

    /**
     *
     * @Description 根据key获取资源
     * @param key
     * @return String
     * <AUTHOR>
     * @date 2023年09月22日 14:09
     */
    public static String getValue(Integer key)
    {
        return getValue(StringUtils.getValue(key));
    }

    /**
     *
     * @Description 根据语言和key获取资源值
     * @param key
     * @param args
     * @return String
     * <AUTHOR>
     * @date 2022年5月31日 下午6:19:52
     */
    public static String getValue(Integer key, String... args)
    {
        return getValue(StringUtils.getValue(key), args);
    }

    /**
     *
     * @Description 根据语言和key获取资源值
     * @param key
     * @param args
     * @return String
     * <AUTHOR>
     * @date 2022年5月31日 下午6:19:52
     */
    public static String getValue(String key, String[] args)
    {
        String value = getValue(key);
        if (StringUtils.isNotEmpty(value) && null != args)
        {
            StringUtils.messageFormat(value, args);
        }
        return value;
    }

    /**
     *
     * @Description 根据key获取资源
     * @param key
     * @return String
     * <AUTHOR>
     * @date 2023年09月22日 14:09
     */
    public static String getValue(String key)
    {
        String value = null;
        // 默认中文
        Locale locale = new Locale(language.get(ZH));
        try
        {
//            Session session = ServiceContextUtils.currentSession();
//            if (Objects.nonNull(session) && Objects.nonNull(session.getLocale()))
//            {
//                if (StringUtils.equalsIgnoreCase(session.getLocale().toString(), language.get(EN)))
//                {
//                    locale = new Locale(language.get(EN));
//                }
//            }

            ResourceBundle config = ResourceBundle.getBundle(LANGUAGE_FILE_NAME, locale);

            value = StringUtils.trim(config.getString(key));
        }
        catch (Exception e)
        {
            log.error("用户语言环境参数language = " + locale);
            log.error("无法找到key为 " + key + " 对应的value.");
        }
        // 根据key找不到时直接返回key
        if (StringUtils.isEmpty(value))
        {
            value = key;
        }
        return value;
    }

    public static Locale language2Locale(String languagestr)
    {
        try
        {
            String[] locale = languagestr.split("_");
            return new Locale(locale[0], locale[1]);
        }
        catch (Exception e)
        {
            log.error("语言转换成Locale失败，已设置成默认语言", e);
            return new Locale(language.get(ZH));
        }
    }
}
