package cn.aliyun.ryytn.common.mybatis.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * @Description 主键类型转换
 * <AUTHOR>
 * @date 2023/11/13 16:31
 */
@MappedTypes(value = String.class)
@MappedJdbcTypes(value = {JdbcType.BIGINT})
public class PrimaryKeyTypeHandler extends BaseTypeHandler<String>
{
    /**
     *
     * @Description 设置非空参数
     * @param ps
     * @param i
     * @param parameter
     * @param jdbcType
     * @throws SQLException
     * <AUTHOR>
     * @date 2023年11月13日 16:31
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException
    {
        if (jdbcType == JdbcType.BIGINT)
        {
            ps.setLong(i, Long.valueOf(parameter));
        }
    }

    /**
     *
     * @Description 设置非空结果
     * @param rs
     * @param columnName
     * @return
     * @throws SQLException
     * <AUTHOR>
     * @date 2023年11月13日 16:31
     */
    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException
    {
        return String.valueOf(rs.getObject(columnName));
    }

    /**
     *
     * @Description TODO
     * @param rs
     * @param columnIndex
     * @return
     * @throws SQLException
     * <AUTHOR>
     * @date 2023年11月13日 16:31
     */
    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException
    {
        return null;
    }

    /**
     *
     * @Description TODO
     * @param cs
     * @param columnIndex
     * @return
     * @throws SQLException
     * <AUTHOR>
     * @date 2023年11月13日 16:31
     */
    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException
    {
        return null;
    }
}
