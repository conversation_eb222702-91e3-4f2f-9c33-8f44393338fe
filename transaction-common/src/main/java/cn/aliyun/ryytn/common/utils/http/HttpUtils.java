/* ====================================================================
 Copyright (C) 2023, All rights reserved.
 Company ISS EDU. 
 Description com-iss-edu-common-base
 FileName HttpUtils.java
 author Myl
 date 2023年6月20日 下午4:40:12
 Function List:
 ==================================================================== */
package cn.aliyun.ryytn.common.utils.http;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.string.StringUtils;

/**
 * @Description Http工具类
 * <AUTHOR>
 * @date 2023年6月20日 下午4:40:12
 */
public final class HttpUtils
{
    static
    {
        try
        {
            TrustManager[] trustAllCerts = {new X509TrustManager()
            {
                @Override
                public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException
                {

                }

                @Override
                public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException
                {

                }

                @Override
                public X509Certificate[] getAcceptedIssuers()
                {
                    return null;
                }
            }};
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, null);

            HostnameVerifier ignoreVer = new HostnameVerifier()
            {
                @Override
                public boolean verify(String s, SSLSession sslSession)
                {
                    return true;
                }
            };

            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier(ignoreVer);
        }
        catch (Exception e)
        {
        }
    }


    /**
     *
     * @Description Post Form
     * @param url
     * @param formParam
     * @param headerMap
     * @return
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年06月21日 10:48
     */
    public static LowResponse postForm(String url, Map<String, Object> formParam, Map<String, Object> headerMap) throws ServiceException
    {
        return postForm(url, Collections.emptyMap(), formParam, headerMap);
    }

    /**
     *
     * @Description Post Form
     * @param url
     * @param uriVar
     * @param formParam
     * @param headerMap
     * @return
     * <AUTHOR>
     * @date 2023年06月21日 9:49
     */
    public static LowResponse postForm(String url, Map<String, Object> uriVar, Map<String, Object> formParam, Map<String, Object> headerMap)
        throws ServiceException
    {
        return postForm(url, Collections.emptyMap(), uriVar, formParam, headerMap);
    }

    /**
     *
     * @Description Post Form File
     * @param url
     * @param fileParam
     * @param uriVar
     * @param formParam
     * @param headerMap
     * @return
     * <AUTHOR>
     * @date 2023年06月21日 9:48
     */
    public static LowResponse postForm(String url, Map<String, List<MultipartFile>> fileParam, Map<String, Object> uriVar, Map<String, Object> formParam,
        Map<String, Object> headerMap) throws ServiceException
    {
        // 拼接请求带参的URL
        StringBuilder urlBuilder = new StringBuilder(url);
        if (MapUtils.isNotEmpty(uriVar))
        {
            if (!StringUtils.contains(url, "?"))
            {
                urlBuilder.append("?r=1");
            }
            uriVar.entrySet().forEach(a -> urlBuilder.append("&").append(a.getKey()).append("=").append(StringUtils.getValue(a.getValue())));
        }

        String boundary = "--------------------------" + System.currentTimeMillis();

        OutputStream out = null;
        DataOutputStream dataOut = null;
        InputStream in = null;
        DataInputStream dataIn = null;

        HttpURLConnection conn = null;

        try
        {
            conn = (HttpURLConnection) new URL(urlBuilder.toString()).openConnection();

            conn.setRequestProperty("Accept", "*/*");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
            conn.setRequestProperty("Connection", "keep-alive");
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");

            // 设置消息头
            if (MapUtils.isNotEmpty(headerMap))
            {
                Iterator<Map.Entry<String, Object>> it = headerMap.entrySet().iterator();
                while (it.hasNext())
                {
                    Map.Entry<String, Object> entry = it.next();
                    String key = entry.getKey();
                    String value = StringUtils.getValue(entry.getValue());

                    if (StringUtils.isNoneBlank(key, value))
                    {
                        conn.setRequestProperty(key, value);
                    }
                }
            }

            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);

            conn.setUseCaches(false);
            conn.setRequestMethod("POST");

            // 获取URLConnection对象对应的输出流
            out = conn.getOutputStream();
            dataOut = new DataOutputStream(out);

            // 设置参数
            if (MapUtils.isNotEmpty(formParam))
            {
                Iterator<Map.Entry<String, Object>> it = formParam.entrySet().iterator();
                while (it.hasNext())
                {
                    Map.Entry<String, Object> entry = it.next();
                    String key = entry.getKey();
                    String value = StringUtils.getValue(entry.getValue());

                    if (StringUtils.isNoneBlank(key, value))
                    {
                        StringBuffer strBuf1 = new StringBuffer();
                        strBuf1.append("\r\n").append("--").append(boundary).append("\r\n");
                        strBuf1.append("Content-Disposition: form-data; name=\"" + key + "\"");
                        strBuf1.append("\r\n\r\n");
                        strBuf1.append(value);
                        dataOut.write(strBuf1.toString().getBytes("UTF-8"));
                    }
                }
            }

            // 设置文件
            if (MapUtils.isNotEmpty(fileParam))
            {
                Iterator<Map.Entry<String, List<MultipartFile>>> it = fileParam.entrySet().iterator();
                while (it.hasNext())
                {
                    Map.Entry<String, List<MultipartFile>> entry = it.next();
                    String key = entry.getKey();
                    List<MultipartFile> value = entry.getValue();

                    if (StringUtils.isNotBlank(key) && CollectionUtils.isNotEmpty(value))
                    {
                        for (MultipartFile file : value)
                        {
                            StringBuffer strBuf = new StringBuffer();
                            strBuf.append("\r\n").append("--").append(boundary).append("\r\n");
                            strBuf.append("Content-Disposition: form-data; name=\"" + key + "\"; filename=\"" + file.getOriginalFilename() + "\"\r\n");
                            strBuf.append("Content-Type:" + file.getContentType() + "\r\n\r\n");

                            dataOut.write(strBuf.toString().getBytes("UTF-8"));

                            in = file.getInputStream();
                            dataIn = new DataInputStream(in);
                            int bytes = 0;
                            byte[] bufferOut = new byte[1024];
                            while ((bytes = dataIn.read(bufferOut)) != -1)
                            {
                                dataOut.write(bufferOut, 0, bytes);
                            }
                        }
                    }
                }
            }


            dataOut.write(("\r\n--" + boundary + "--\r\n").getBytes("UTF-8"));
            dataOut.flush();

            // 构造响应对象
            return new LowResponse(conn);
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_HTTP_ERROR, e);
        }
        finally
        {
            IOUtils.closeQuietly(dataOut);
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(dataIn);
            IOUtils.closeQuietly(in);
            if (Objects.nonNull(conn))
            {
                try
                {
                    conn.disconnect();
                }
                catch (Exception e)
                {
                }
            }
        }
    }

    /**
     *
     * @Description Post X-WWW-FORM
     * @param url
     * @param formParam
     * @param headerMap
     * @return
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年06月21日 11:14
     */
    public static LowResponse postXwwwForm(String url, Map<String, Object> formParam, Map<String, Object> headerMap) throws ServiceException
    {
        return postXwwwForm(url, Collections.emptyMap(), formParam, headerMap);
    }

    /**
     *
     * @Description Post X-WWW-FORM
     * @param url
     * @param uriVar
     * @param formParam
     * @param headerMap
     * @return
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年06月21日 11:13
     */
    public static LowResponse postXwwwForm(String url, Map<String, Object> uriVar, Map<String, Object> formParam, Map<String, Object> headerMap)
        throws ServiceException
    {
        // 拼接请求带参的URL
        StringBuilder urlBuilder = new StringBuilder(url);
        if (MapUtils.isNotEmpty(uriVar))
        {
            if (!StringUtils.contains(url, "?"))
            {
                urlBuilder.append("?r=1");
            }
            uriVar.entrySet().forEach(a -> urlBuilder.append("&").append(a.getKey()).append("=").append(StringUtils.getValue(a.getValue())));
        }

        // 设置参数
        StringBuffer strBuf1 = new StringBuffer();
        if (MapUtils.isNotEmpty(formParam))
        {
            Iterator<Map.Entry<String, Object>> it = formParam.entrySet().iterator();
            while (it.hasNext())
            {
                Map.Entry<String, Object> entry = it.next();
                String key = entry.getKey();
                String value = StringUtils.getValue(entry.getValue());

                if (StringUtils.isNoneBlank(key, value))
                {
                    if (strBuf1.length() > 0)
                    {
                        strBuf1.append("&");
                    }
                    strBuf1.append(key).append("=").append(value);
                }
            }
        }

        // 声明资源
        OutputStream out = null;
        OutputStreamWriter outWriter = null;
        PrintWriter writer = null;
        HttpURLConnection conn = null;
        try
        {
            // 打开URL连接
            conn = (HttpURLConnection) new URL(urlBuilder.toString()).openConnection();

            // 设置通用的请求头属性
            conn.setRequestProperty("Accept", "*/*");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
            conn.setRequestProperty("Connection", "keep-alive");
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");

            // 设置消息头
            if (MapUtils.isNotEmpty(headerMap))
            {
                Iterator<Map.Entry<String, Object>> it = headerMap.entrySet().iterator();
                while (it.hasNext())
                {
                    Map.Entry<String, Object> entry = it.next();
                    String key = entry.getKey();
                    String value = StringUtils.getValue(entry.getValue());

                    if (StringUtils.isNoneBlank(key, value))
                    {
                        conn.setRequestProperty(key, value);
                    }
                }
            }

            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);

            conn.setUseCaches(false);
            conn.setRequestMethod("POST");

            // 获取URLConnection对象对应的输出流
            out = conn.getOutputStream();
            outWriter = new OutputStreamWriter(out, StandardCharsets.UTF_8);
            writer = new PrintWriter(outWriter);

            // 发送key=value消息体
            writer.print(strBuf1.toString());
            // flush输出流的缓冲
            writer.flush();

            // 构造响应对象
            return new LowResponse(conn);
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_HTTP_ERROR, e);
        }
        finally
        {
            IOUtils.closeQuietly(writer);
            IOUtils.closeQuietly(outWriter);
            IOUtils.closeQuietly(out);
            if (Objects.nonNull(conn))
            {
                try
                {
                    conn.disconnect();
                }
                catch (Exception e)
                {
                }
            }
        }
    }


    /**
     *
     * @Description Post Json
     * @param url
     * @param objParam
     * @param headerMap
     * @return
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年06月21日 10:46
     */
    public static LowResponse postJson(String url, Object objParam, Map<String, Object> headerMap) throws ServiceException
    {
        return postJson(url, Collections.emptyMap(), objParam, headerMap);
    }

    /**
     *
     * @Description Post Json
     * @param url
     * @param uriVar
     * @param param
     * @param headerMap
     * @return
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年06月20日 17:37
     */
    public static LowResponse postJson(String url, Map<String, Object> uriVar, Object objParam, Map<String, Object> headerMap) throws ServiceException
    {
        // 拼接请求带参的URL
        StringBuilder urlBuilder = new StringBuilder(url);
        if (MapUtils.isNotEmpty(uriVar))
        {
            if (!StringUtils.contains(url, "?"))
            {
                urlBuilder.append("?r=1");
            }
            uriVar.entrySet().forEach(a -> urlBuilder.append("&").append(a.getKey()).append("=").append(StringUtils.getValue(a.getValue())));
        }

        // 参数json消息体
        String jsonString = JSON.toJSONString(objParam);

        // 声明资源
        OutputStream out = null;
        OutputStreamWriter outWriter = null;
        PrintWriter writer = null;
        HttpURLConnection conn = null;
        try
        {
            // 打开URL连接
            conn = (HttpURLConnection) new URL(urlBuilder.toString()).openConnection();

            // 设置通用的请求头属性
            conn.setRequestProperty("Accept", "*/*");
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            conn.setRequestProperty("Connection", "keep-alive");
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");

            // 设置消息头
            if (MapUtils.isNotEmpty(headerMap))
            {
                Iterator<Map.Entry<String, Object>> it = headerMap.entrySet().iterator();
                while (it.hasNext())
                {
                    Map.Entry<String, Object> entry = it.next();
                    String key = entry.getKey();
                    String value = StringUtils.getValue(entry.getValue());

                    if (StringUtils.isNoneBlank(key, value))
                    {
                        conn.setRequestProperty(key, value);
                    }
                }
            }

            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);

            conn.setUseCaches(false);
            conn.setRequestMethod("POST");

            // 获取URLConnection对象对应的输出流
            out = conn.getOutputStream();
            outWriter = new OutputStreamWriter(out, StandardCharsets.UTF_8);
            writer = new PrintWriter(outWriter);
            // 发送请求Json消息
            writer.print(jsonString);
            // flush输出流的缓冲
            writer.flush();

            // 构造响应对象
            return new LowResponse(conn);
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_HTTP_ERROR, e);
        }
        finally
        {
            IOUtils.closeQuietly(writer);
            IOUtils.closeQuietly(outWriter);
            IOUtils.closeQuietly(out);
            if (Objects.nonNull(conn))
            {
                try
                {
                    conn.disconnect();
                }
                catch (Exception e)
                {
                }
            }
        }
    }

    public static LowResponse getR(String url, Map<String, Object> uriVar, Map<String, Object> headerMap) throws ServiceException
    {
        // 拼接请求带参的URL
        StringBuilder urlBuilder = new StringBuilder(url);
        if (MapUtils.isNotEmpty(uriVar))
        {
            if (!StringUtils.contains(url, "?"))
            {
                urlBuilder.append("?r=1");
            }
            uriVar.entrySet().forEach(a -> urlBuilder.append("&").append(a.getKey()).append("=").append(StringUtils.getValue(a.getValue())));
        }

        // 声明资源
        OutputStream out = null;
        OutputStreamWriter outWriter = null;
        PrintWriter writer = null;
        HttpURLConnection conn = null;
        try
        {
            // 打开URL连接
            conn = (HttpURLConnection) new URL(urlBuilder.toString()).openConnection();

            // 设置通用的请求头属性
            conn.setRequestProperty("Accept", "*/*");
            conn.setRequestProperty("Connection", "keep-alive");
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");

            // 设置消息头
            if (MapUtils.isNotEmpty(headerMap))
            {
                Iterator<Map.Entry<String, Object>> it = headerMap.entrySet().iterator();
                while (it.hasNext())
                {
                    Map.Entry<String, Object> entry = it.next();
                    String key = entry.getKey();
                    String value = StringUtils.getValue(entry.getValue());

                    if (StringUtils.isNoneBlank(key, value))
                    {
                        conn.setRequestProperty(key, value);
                    }
                }
            }

            conn.setUseCaches(false);
            conn.connect();

            // 构造响应对象
            return new LowResponse(conn);
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_HTTP_ERROR, e);
        }
        finally
        {
            IOUtils.closeQuietly(writer);
            IOUtils.closeQuietly(outWriter);
            IOUtils.closeQuietly(out);
            if (Objects.nonNull(conn))
            {
                try
                {
                    conn.disconnect();
                }
                catch (Exception e)
                {
                }
            }
        }
    }

    /**
     * @Title HttpUtils.
     * @Description 工具类私有构造，禁止实例化
     */
    private HttpUtils()
    {
    }
}
