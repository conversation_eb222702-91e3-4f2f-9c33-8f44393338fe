package cn.aliyun.ryytn.common.excel;

/**
 * @Description 导出电子表格抽象处理类
 * <AUTHOR>
 * @date 2023/10/17 11:21
 */
public abstract class AbstractExportExcelHandler extends AbstractExcelHandler
{
    /**
     * 导出下载文件名
     */
    public String fileName;

    /**
     * 是否自定义实现
     */
    public Boolean customFlag;

    /**
     *
     * @Description 导出文件字节数组
     * @param condition
     * @return byte[]
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月19日 14:15
     */
    public abstract byte[] export(ExcelCondition condition) throws Exception;
}
