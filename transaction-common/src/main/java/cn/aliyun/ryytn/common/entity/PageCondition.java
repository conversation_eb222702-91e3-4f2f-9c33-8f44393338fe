package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * @Description 分页条件
 * <AUTHOR>
 * @date 2023年9月19日 下午2:39:21
 */
@Setter
@Getter
@ToString
@ApiModel("分页条件")
public class PageCondition<C> implements Serializable
{
    private static final long serialVersionUID = -4694138722256179520L;
    /**
     * 当前页码
     */
    @ApiModelProperty("当前页码")
    private Integer pageNum = 1;

    /**
     * 每页显示记录数
     */
    @ApiModelProperty("每页显示记录数")
    private Integer pageSize = 10;

    /**
     * 查询条件
     */
    @ApiModelProperty("查询条件")
    private C condition;

    public PageCondition()
    {
    }

    public PageCondition(C condition)
    {
        this.condition = condition;
    }

    public void setCondition(C condition)
    {
        this.condition = condition;
    }

    public C getCondition()
    {
        return this.condition;
    }
}
