package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 角色
 * <AUTHOR>
 * @date 2023/10/8 14:00
 */
@Setter
@Getter
@ToString
@ApiModel("角色")
public class Role implements Serializable
{
    private static final long serialVersionUID = 5152792651292135555L;
    /**
     * 角色编号
     */
    @ApiModelProperty("角色编号")
    private String id;

    /**
     * 角色名称
     */
    @ApiModelProperty("角色名称")
    private String name;

    /**
     * 是否默认角色，false：不是默认角色，true：是默认角色，默认值false
     */
    @ApiModelProperty("是否默认角色")
    private Boolean defaultFlag;

    /**
     * 状态，1：正常，2：禁用，默认值：1
     */
    @ApiModelProperty("角色状态，1：正常，2：禁用")
    private Integer status;

    /**
     * 描述
     */
    @ApiModelProperty("角色描述")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty("角色排序")
    private Integer sortNo;

    /**
     * 数据类型，1:表示初始化数据不允许删除；2：表示管理端创建数据；默认为2
     */
    @ApiModelProperty("数据类型，1：初始化数据不允许删除，2：管理数据")
    private Integer dataType;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date createdTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date updatedTime;

    /**
     * 已授权菜单编号列表
     */
    @ApiModelProperty("已授权菜单编号列表")
    private List<String> pageIdList;

    /**
     * 已授权按钮编号列表
     */
    @ApiModelProperty("已授权按钮编号列表")
    private List<String> buttonIdList;

    /**
     * 已授权渠道编号列表
     */
    @ApiModelProperty("已授权渠道编号列表")
    private List<String> channelIdList;

    /**
     * 已授权产品品类编号列表
     */
    @ApiModelProperty("已授权产品品类编号列表")
    private List<String> categoryIdList;

    /**
     * 已授权工厂编号列表
     */
    @ApiModelProperty("已授权工厂编号列表")
    private List<String> factoryIdList;

    /**
     * 已授权仓库编号列表
     */
    @ApiModelProperty("已授权仓库编号列表")
    private List<String> depositoryIdList;
}
