package cn.aliyun.ryytn.common.mybatis.handler;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeException;

import com.alibaba.fastjson.JSON;

/**
 * @Description 列表类型转换器
 * <AUTHOR>
 * @date 2024/2/28 18:57
 */
public class ListTypeHandler extends BaseTypeHandler<List<Object>>
{

    private static final String TYPE_NAME_VARCHAR = "varchar";
    private static final String TYPE_NAME_INTEGER = "integer";
    private static final String TYPE_NAME_BOOLEAN = "boolean";
    private static final String TYPE_NAME_NUMERIC = "numeric";

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Object> parameter, JdbcType jdbcType) throws SQLException
    {
        String typeName = null;
        if (typeName == null)
        {
            throw new TypeException("parameter typeName error:" + parameter.getClass().getName());
        }

        Connection conn = ps.getConnection();
        int size = parameter.size();
        Array array = conn.createArrayOf(typeName, parameter.toArray(new Object[size]));
        ps.setArray(i, array);
    }

    @Override
    public List<Object> getNullableResult(ResultSet resultSet, String s) throws SQLException
    {
        return getArray(resultSet.getArray(s));
    }

    @Override
    public List<Object> getNullableResult(ResultSet resultSet, int i) throws SQLException
    {
        return getArray(resultSet.getArray(i));
    }

    @Override
    public List<Object> getNullableResult(CallableStatement callableStatement, int i) throws SQLException
    {
        return getArray(callableStatement.getArray(i));
    }

    private List<Object> getArray(Array array)
    {
        if (array == null)
        {
            return null;
        }
        try
        {
            return JSON.parseArray(JSON.toJSONString(array.getArray()), Object.class);
        }
        catch (Exception e)
        {
        }
        return null;
    }
}