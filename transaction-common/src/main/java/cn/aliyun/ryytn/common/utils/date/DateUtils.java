package cn.aliyun.ryytn.common.utils.date;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;

import cn.aliyun.ryytn.common.utils.string.StringUtils;

/**
 * 日期工具类, 继承org.apache.commons.lang.time.DateUtils类
 *
 * <AUTHOR>
 * @version 2014-4-15
 */
@Slf4j
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static final String Y = "yyyy";
    public static final String M = "MM";
    public static final String D = "dd";
    public static final String YM_SLASH = "yyyy/MM";
    public static final String YM_SLASH_YY = "yy/MM";
    public static final String MD_SLASH = "MM/dd";
    public static final String YMD_SLASH = "yyyy/MM/dd";
    public static final String YM = "yyyyMM";
    public static final String YMD = "yyyyMMdd";
    public static final String YM_DASH = "yyyy-MM";
    public static final String YMD_DASH = "yyyy-MM-dd";
    public static final String YMD_DOT = "yyyy.MM.dd";
    public static final String YMDH = "yyyy-MM-dd HH";
    public static final String YMDHM = "yyyy-MM-dd HH:mm";
    public static final String YMDHMS = "yyyyMMddHHmmss";
    public static final String YMDHMS_MS = "yyyyMMddHHmmssSSS";
    public static final String YMDHMS_STD = "yyyy-MM-dd HH:mm:ss";
    public static final String YMDHMS_STD_MS = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String DEFAULT_TIMESTAMP = "19700101000000";
    public static final String MAX_DATE = "19991231235959";
    public static final String YMDHMS_STD_UTC = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    private static String[] parsePatterns =
        {"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd",
            "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd）
     */
    public static String getDate()
    {
        return getDate("yyyy-MM-dd");
    }

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String getDate(String pattern)
    {
        return DateFormatUtils.format(new Date(), pattern);
    }

    public static String getDate(String pattern, int addDay)
    {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, addDay);
        date = calendar.getTime();
        return DateFormatUtils.format(date, pattern);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, Object... pattern)
    {
        String formatDate = null;
        if (pattern != null && pattern.length > 0)
        {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        }
        else
        {
            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
        }
        return formatDate;
    }

    /**
     * 得到当前星期字符串 格式（E）星期几
     */
    public static String getWeek()
    {
        return formatDate(new Date(), "E");
    }

    /**
     * 日期型字符串转化为日期 格式 { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy/MM/dd",
     * "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd
     * HH:mm" }
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取过去的天数
     */
    public static long pastDays(Date date)
    {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (24 * 60 * 60 * 1000);
    }

    /**
     * 获取过去的小时
     */
    public static long pastHour(Date date)
    {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (60 * 60 * 1000);
    }

    /**
     * 获取过去的分钟
     */
    public static long pastMinutes(Date date)
    {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (60 * 1000);
    }

    /**
     *
     * @Description 获取过去的秒
     * @param date
     * @return long
     * <AUTHOR>
     * @date 2023年7月12日 下午6:04:19
     */
    public static long pastSecond(Date date)
    {
        long t = System.currentTimeMillis() - date.getTime();
        return t / 1000;
    }

    /**
     *
     * @Description 获取过去的微秒
     * @param date
     * @return long
     * <AUTHOR>
     * @date 2023年7月12日 下午6:04:19
     */
    public static long pastMillSecond(Date date)
    {
        return System.currentTimeMillis() - date.getTime();
    }

    /**
     * 转换为时间（天,时:分:秒.毫秒）
     */
    public static String formatDateTime(long timeMillis)
    {
        long day = timeMillis / (24 * 60 * 60 * 1000);
        long hour = (timeMillis / (60 * 60 * 1000) - day * 24);
        long min = ((timeMillis / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (timeMillis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        long sss = (timeMillis - day * 24 * 60 * 60 * 1000 - hour * 60 * 60 * 1000 - min * 60 * 1000 - s * 1000);
        return (day > 0 ? day + "," : "") + hour + ":" + min + ":" + s + "." + sss;
    }

    /**
     * 获取两个日期之间的天数
     */
    public static double getDistanceOfTwoDate(Date before, Date after)
    {
        long beforeTime = before.getTime();
        long afterTime = after.getTime();
        return (afterTime - beforeTime) / (1000 * 60 * 60 * 24);
    }

    public static String formatTime(Date date, String format)
    {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(date);
    }

    /**
     * 将字符串转换成日期长整形 参数：time，String，日期字符串 pattern, String, 解析的格式 返回：long，日期长整形
     *
     * @param time    时间
     * @param pattern 政则表达式
     * @return long
     */
    public static long timeStr2Long(String time, String pattern)
    {
        return timeStr2Date(time, pattern).getTime();
    }

    /**
     * 将字符串转换成日期形 参数：time，String，日期字符串 pattern, String, 解析的格式 返回：Date，日期形
     *
     * @param time    时间
     * @param pattern 政则表达式
     * @return Date
     */
    public static Date timeStr2Date(String time, String pattern)
    {
        if (time == null)
        {
            throw new IllegalArgumentException("time parameter can not be null");
        }
        if (pattern == null)
        {
            throw new IllegalArgumentException("pattern parameter can not be null");
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try
        {
            return sdf.parse(time);
        }
        catch (ParseException e)
        {
            throw new IllegalArgumentException("using [" + pattern + "] parse [" + time + "] failed");
        }
    }

    /**
     *
     * @Description 获取指定日期所在周第一天
     * @param date
     * @param partition
     * @return
     * <AUTHOR>
     * @date 2023年12月16日 20:39
     */
    public static String getWeekBegin(Date date, String partition)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.WEEK_OF_MONTH, 0);
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == 1)
        {
            cal.add(Calendar.DAY_OF_MONTH, -6);
        }
        else
        {
            cal.set(Calendar.DAY_OF_WEEK, 2);
        }
        Date time = cal.getTime();
        return DateUtils.formatTime(time, partition);
    }

    /**
     *
     * @Description 获取指定日期所在周最后一天
     * @param date
     * @param partition
     * @return
     * <AUTHOR>
     * @date 2023年12月16日 20:39
     */
    public static String getWeekEnd(Date date, String partition)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek != 1)
        {
            cal.add(Calendar.DAY_OF_MONTH, 8 - dayOfWeek);
        }
        Date time = cal.getTime();
        return DateUtils.formatTime(time, partition);
    }

    public static String getNowWeekBegin()
    {
        int dayPlus;
        Calendar cd = Calendar.getInstance();
        // 获得今天是一周的第几天，星期日是第一天，星期二是第二天......
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == 1)
        {
            dayPlus = dayOfWeek - 7;
        }
        else
        {
            dayPlus = dayOfWeek - 2;
        }
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, dayPlus);
        Date sunday = currentDate.getTime();
        return DateUtils.formatTime(sunday, DateUtils.YMD_DASH);
    }

    public static String getNowWeekEnd()
    {
        int dayPlus;
        Calendar cd = Calendar.getInstance();
        // 获得今天是一周的第几天，星期日是第一天，星期二是第二天......
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == 1)
        {
            dayPlus = 0;
        }
        else
        {
            dayPlus = 7 - dayOfWeek - 1;
        }
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, dayPlus);
        Date saturday = currentDate.getTime();
        return DateUtils.formatTime(saturday, DateUtils.YMD_DASH);
    }

    /**
     * 根据日期字符串判断当月第几周
     */
    public static int getMonthWeek(String dateTime) throws Exception
    {
        SimpleDateFormat sdf = new SimpleDateFormat(YMD_DASH);
        Date date = sdf.parse(dateTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(date);
        // 第几周
        int week = calendar.get(Calendar.WEEK_OF_MONTH);
        return week;
    }

    public static int getMonthWeek(String dateTime, String partition) throws Exception
    {
        SimpleDateFormat sdf = new SimpleDateFormat(partition);
        Date date = sdf.parse(dateTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(date);
        // 第几周
        int week = calendar.get(Calendar.WEEK_OF_MONTH);
        return week;
    }

    public static int getDayWeek(String dateTime, String partition) throws Exception
    {
        Date date = DateUtils.parseDate(dateTime, partition);
        int year = Integer.valueOf(DateUtils.formatDate(date, DateUtils.Y));
        int month = Integer.valueOf(DateUtils.formatDate(date, DateUtils.M));
        int day = Integer.valueOf(DateUtils.formatDate(date, DateUtils.D));
        LocalDateTime localDateTime = LocalDateTime.now();
        return localDateTime.withYear(year).withMonth(month).withDayOfMonth(day).getDayOfWeek().getValue();
    }

    public static long getBetweenDays(Date beginDate, Date endDate)
    {
        long beginTime = beginDate.getTime();
        long endTime = endDate.getTime();
        long betweenDays = (long) Math.ceil((endTime - beginTime) / (1000 * 60 * 60 * 24.0));
        return betweenDays;
    }

    public static String getYestodayDate(String format)
    {
        Calendar cd = Calendar.getInstance();
        cd.add(Calendar.DATE, -1);
        Date yesterday = cd.getTime();
        return DateUtils.formatTime(yesterday, format);
    }

    /**
     * <不同时区时间转换>
     *
     * @param sourceDate     源日期时间
     * @param formatter      转换格式
     * @param sourceTimeZone 原时区
     * @param targetTimeZone 目标时区
     * @see [类、类#方法、类#成员]
     */
    public static String dateTransformBetweenTimeZone(Date sourceDate, DateFormat formatter, TimeZone sourceTimeZone, TimeZone targetTimeZone)
    {
        Long targetTime = sourceDate.getTime() - sourceTimeZone.getRawOffset() + targetTimeZone.getRawOffset();
        return getTime(new Date(targetTime), formatter);
    }

    public static String getTime(Date date, DateFormat formatter)
    {
        return formatter.format(date);
    }

    public static String str2strDate(String dateStr, String basePatten, String convertPatten)
    {
        if (StringUtils.isBlank(dateStr))
        {
            return "";
        }
        return formatTime(timeStr2Date(dateStr, basePatten), convertPatten);
    }

    public static String utc2cst(String dateStr, String datePatten)
    {
        String result = null;
        try
        {
            SimpleDateFormat sdf = new SimpleDateFormat(datePatten);
            Date date = sdf.parse(dateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(datePatten);
            result = simpleDateFormat.format(calendar.getTime());
        }
        catch (ParseException e)
        {
            log.warn("utc2cst error:{}", Throwables.getStackTraceAsString(e));
        }

        return result;
    }

    public static String cst2utc(Date date, String datePatten)
    {
        String result = null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) - 8);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(datePatten);
        result = simpleDateFormat.format(calendar.getTime());

        return result;
    }

    public static String cst2utc(String dateStr, String datePatten)
    {
        String result = null;
        try
        {
            SimpleDateFormat sdf = new SimpleDateFormat(datePatten);
            Date date = sdf.parse(dateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) - 8);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(datePatten);
            result = simpleDateFormat.format(calendar.getTime());
        }
        catch (ParseException e)
        {
            log.warn("cst2utc error:{}", Throwables.getStackTraceAsString(e));
        }

        return result;
    }

    public static long utc2cstlong(long utcTime)
    {
        return utcTime + 8 * 3600 * 1000;
    }

    public static long cst2utclong(long cstTime)
    {
        return cstTime - 8 * 3600 * 1000;
    }

    public static int getQuarter(int month)
    {
        int quarter;
        if (month >= 1 && month <= 3)
        {
            quarter = 1;
        }
        else if (month >= 4 && month <= 6)
        {
            quarter = 2;
        }
        else if (month >= 7 && month <= 9)
        {
            quarter = 3;
        }
        else
        {
            quarter = 4;
        }
        return quarter;
    }

    private static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(YMD_DASH);

    public static List<Map> getScope(int offset)
    {
        String date = DateUtils.formatDate(DateUtils.addMonths(new Date(), offset - 1), YMD_DASH);
        String timeStrs[] = date.split("-");
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, Integer.parseInt(timeStrs[0]));
        c.set(Calendar.MONTH, Integer.parseInt(timeStrs[1]) - 1);
        c.setFirstDayOfWeek(Calendar.MONDAY);
        int weeks = c.getActualMaximum(Calendar.WEEK_OF_MONTH);
        LocalDate localDateate = LocalDate.parse(date, dateTimeFormatter);
        // 月份第一周的起始时间和结束时间
        LocalDate firstDay = localDateate.with(TemporalAdjusters.firstDayOfMonth());
        String firstDayStr = firstDay.format(dateTimeFormatter);
        String sunStr = getSunOfWeek(firstDayStr);
        List<Map> weekInfos = new ArrayList<>();
        for (int i = 1; i <= weeks; i++)
        {
            Map weekInfo = new HashMap();
            // 第一周的起始时间就是当月的1号，结束时间就是周日
            if (i == 1)
            {
                weekInfo.put("start", firstDayStr);
                weekInfo.put("end", sunStr);
                weekInfo.put("order", String.valueOf(i));
                // 计算接下来每周的周一和周日
            }
            else if (i < weeks)
            {
                // 由于sunStr是上一周的周日，所以取周一要取sunStr的下一周的周一
                String monDay = getLastMonOfWeek(sunStr);
                sunStr = getSunOfWeek(monDay);
                weekInfo.put("start", monDay);
                weekInfo.put("end", sunStr);
                weekInfo.put("order", String.valueOf(i));
                // 由于最后一周可能结束时间不是周日，所以要单独处理
            }
            else
            {
                String monDay = getLastMonOfWeek(sunStr);
                // 结束时间肯定就是当前月的最后一天
                LocalDate lastDay = localDateate.with(TemporalAdjusters.lastDayOfMonth());
                String endDay = lastDay.format(dateTimeFormatter);
                weekInfo.put("start", monDay);
                weekInfo.put("end", endDay);
                weekInfo.put("order", String.valueOf(i));
            }
            weekInfos.add(weekInfo);
        }
        return weekInfos;
    }

    // 算出所在周的周日
    public static String getSunOfWeek(String time)
    {
        LocalDate localDateate = LocalDate.parse(time, dateTimeFormatter);
        LocalDate endday = localDateate.with(TemporalAdjusters.next(java.time.DayOfWeek.MONDAY)).minusDays(1);
        String endDayStr = endday.format(dateTimeFormatter);
        return endDayStr;
    }

    // 下一周的周一
    public static String getLastMonOfWeek(String time)
    {
        LocalDate localDateate = LocalDate.parse(time, dateTimeFormatter);
        LocalDate endday = localDateate.with(TemporalAdjusters.next(java.time.DayOfWeek.MONDAY));
        String endDayStr = endday.format(dateTimeFormatter);
        return endDayStr;
    }

    /**
     * 返回日期所在周的周一
     * @param date
     * @return
     */
    public static String getMondayByWeek(Date date,String format) {
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        // 设置为本周的第一天（星期日）
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        // 设置为当天的0点0分0秒
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 转换为Date对象
        Date startOfWeek = calendar.getTime();
        // 输出结果
        return formatTime(startOfWeek,format);

    }
}
