package cn.aliyun.ryytn.common.utils.dict;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.DictData;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;

/**
 * @Description 字典工具类
 * <AUTHOR>
 * @date 2023/11/17 15:42
 */
@Component
public class DictUtils
{
    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 根据字典类型获取字典数据列表
     * @param dictType
     * @return List<DictData>
     * <AUTHOR>
     * @date 2023年11月13日 上午11:08:43
     */
    public List<DictData> getDictDataByDictType(String dictType) throws Exception
    {
        if (StringUtils.isEmpty(dictType))
        {
            return Collections.emptyList();
        }
        // 获取缓存中所有的字典数据
        String key = StringUtils.format(CommonConstants.DICTDATA_ALL_CACHE_KEY, dictType);
        List<DictData> dictDataList = (List<DictData>) redisUtils.get(key);
        return dictDataList;
    }

    /**
     *
     * @Description 获取字典显示内容
     * @param dictType
     * @param code
     * @return String
     * <AUTHOR>
     * @date 2023年11月10日 下午3:28:18
     */
    public String codeToName(String dictType, String code) throws Exception
    {
        if (StringUtils.isEmpty(dictType) || StringUtils.isEmpty(code))
        {
            return StringUtils.EMPTY;
        }

        // 获取缓存中字典数据
        String key = StringUtils.format(CommonConstants.DICTDATA_CODE2NAME_CACHE_KEY, dictType);
        DictData dictData = (DictData) redisUtils.hget(key, code);

        return Objects.isNull(dictData) ? null : dictData.getName();
    }

    /**
     *
     * @Description 获取字典编码
     * @param dictType
     * @param name
     * @return String
     * <AUTHOR>
     * @date 2023年11月10日 下午3:28:18
     */
    public String nameToCode(String dictType, String name) throws Exception
    {
        if (StringUtils.isEmpty(dictType) || StringUtils.isEmpty(name))
        {
            return StringUtils.EMPTY;
        }

        // 获取缓存中字典数据
        String key = StringUtils.format(CommonConstants.DICTDATA_NAME2CODE_CACHE_KEY, dictType);
        DictData dictData = (DictData) redisUtils.hget(key, name);

        return Objects.isNull(dictData) ? null : dictData.getCode();
    }

    /**
     *
     * @Description 根据字典编码获取字典数据（获取字典其他信息时使用）
     * @param dictType
     * @param code
     * @return DictData
     * <AUTHOR>
     * @date 2023年11月10日 下午3:28:18
     */
    public DictData getDictDataByCode(String dictType, String code) throws Exception
    {
        if (StringUtils.isEmpty(dictType) || StringUtils.isEmpty(code))
        {
            return null;
        }

        // 获取缓存中字典数据
        String key = StringUtils.format(CommonConstants.DICTDATA_CODE2NAME_CACHE_KEY, dictType);
        DictData dictData = (DictData) redisUtils.hget(key, code);

        return dictData;
    }

    /**
     *
     * @Description 根据字典显示内容获取字典数据（获取字典其他信息时使用）
     * @param dictType
     * @param name
     * @return DictData
     * <AUTHOR>
     * @date 2023年11月10日 下午3:28:18
     */
    public DictData getDictDataByName(String dictType, String name)
    {
        if (StringUtils.isEmpty(dictType) || StringUtils.isEmpty(name))
        {
            return null;
        }

        // 获取缓存中字典数据
        String key = StringUtils.format(CommonConstants.DICTDATA_NAME2CODE_CACHE_KEY, dictType);
        DictData dictData = (DictData) redisUtils.hget(key, name);

        return dictData;
    }
}
