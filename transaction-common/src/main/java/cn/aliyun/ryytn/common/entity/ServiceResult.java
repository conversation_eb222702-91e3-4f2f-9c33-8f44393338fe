package cn.aliyun.ryytn.common.entity;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.exception.ExceptionEnums;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024-08-27 15:18
 * @Description
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
public class ServiceResult {

    private int code; // 状态码
    private String message; // 消息

    public ServiceResult(){
        this.code = ErrorCodeConstants.SUCCESS;
        this.message = "成功";
    }

    public ServiceResult(ExceptionEnums exceptionEnum) {
        this.code = exceptionEnum.getCode();
        this.message = exceptionEnum.getMessage();
    }

    public void fail(ExceptionEnums exceptionEnums){
        this.code = exceptionEnums.getCode();
        this.message = exceptionEnums.getMessage();
    }

}
