package cn.aliyun.ryytn.common.dataq;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description Dataq响应消息体
 * <AUTHOR>
 * @date 2023/10/23 15:22
 */
@Setter
@Getter
@ToString
public class DataqResult<T> implements Serializable
{
    private static final long serialVersionUID = 248803488357441047L;
    /**
     * 消息编号
     */
    private String traceId;

    /**
     * 响应结果
     */
    private String code;

    /**
     * 数据对象
     */
    private T data;

    /**
     * 错误消息，成功时为空
     */
    private String message;

    /**
     * 接口耗时
     */
    private String timeCost;

    /**
     * 总数
     */
    private Integer total;
}