package cn.aliyun.ryytn.common.mq.impl;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.mq.api.ProduceService;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.spring.SpringUtil;
import cn.aliyun.ryytn.common.utils.string.StringUtils;

/**
 * @Description Redis List生产实现
 * <AUTHOR>
 * @date 2023/10/13 16:33
 */
public class RedisListProduceServiceImpl extends ProduceService
{
    /**
     *
     * @Description 生产消息
     * @param topic
     * @param value
     * <AUTHOR>
     * @date 2023年10月12日 14:47
     */
    @Override
    public void produce(String topic, Object value)
    {
        RedisUtils redisUtils = SpringUtil.getBean(RedisUtils.class);
        redisUtils.lSet(StringUtils.format(CommonConstants.REDIS_LIST_MQ_KEY, topic), value);
    }
}
