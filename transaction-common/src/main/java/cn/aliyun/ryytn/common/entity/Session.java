package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.Locale;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * @Description 会话对象
 * <AUTHOR>
 * @date 2023年9月19日 下午2:01:59
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class Session implements Serializable
{
    private static final long serialVersionUID = 6897157757957503690L;
    /**
     * @Description 会话编号
     * <AUTHOR>
     * @date 2022年4月24日 下午7:30:26
     */
    private String sessionId;

    /**
     * @Description 账号，待定义
     * <AUTHOR>
     * @date 2022年4月24日 下午7:30:26
     */
    private Account account;

    /**
     * @Description 登录时间
     * <AUTHOR>
     * @date 2022年4月24日 下午7:30:26
     */
    @JsonFormat(pattern = DateUtils.YMDHMS_STD,timezone = "GMT+8")
    private Date loginTime;

    /**
     * @Description 会话失效时间
     * <AUTHOR>
     * @date 2022年4月24日 下午7:30:26
     */
    @JsonFormat(pattern = DateUtils.YMDHMS_STD,timezone = "GMT+8")
    private Date expireTime;

    /**
     * @Description 语言
     * <AUTHOR>
     * @date 2022年4月24日 下午7:30:26
     */
    private Locale locale;

    public Session(String sessionId, Locale locale)
    {
        this.sessionId = sessionId;
        this.locale = locale;
    }
}
