package cn.aliyun.ryytn.common.excel;

import java.io.File;

/**
 * @Description 导入电子表格接口
 * <AUTHOR>
 * @date 2023/10/17 11:21
 */
public abstract class AbstractImportExcelHandler<T extends ExcelData> extends AbstractExcelHandler
{
    /**
     *
     * @Description 导入电子表格
     * @param file
     * @param condition
     * @return ExcelResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月30日 16:31
     */
    public abstract ExcelResult<?> importExcel(File file, ExcelCondition condition) throws Exception;
}
