package cn.aliyun.ryytn.common.excel;

import java.io.Serializable;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;

import cn.aliyun.ryytn.common.entity.Session;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description Excel导出条件类
 * <AUTHOR>
 * @date 2023/10/18 17:39
 */
@Setter
@Getter
@ToString
public class ExcelCondition implements Serializable
{
    private static final long serialVersionUID = -899661656284037398L;
    /**
     * 菜单编号，用于获取页面表格配置
     * 暂时调整的需求，后端不需要根据页面表格配置做冻结和聚合，暂不使用此字段
     */
    private String pageId;

    /**
     * 业务处理Bean的name
     */
    private String key;

    /**
     * 业务过滤条件
     */
    private JSONObject condition;

    /**
     * 会话信息
     */
    @JsonIgnore
    private Session session;
}
