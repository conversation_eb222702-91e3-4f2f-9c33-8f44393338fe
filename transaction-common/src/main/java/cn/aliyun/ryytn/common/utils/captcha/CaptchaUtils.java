package cn.aliyun.ryytn.common.utils.captcha;

import java.awt.*;
import java.awt.geom.*;
import java.awt.image.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Random;
import javax.imageio.ImageIO;

import cn.aliyun.ryytn.common.entity.Captcha;

/**
 *
 * @Description 验证码工具类
 * <AUTHOR>
 * @date 2023/10/9 17:48
 */
public class CaptchaUtils
{
    // 默认验证码字符集
    // private static final char[] chars = { '0', '1', '2', '3', '4', '5', '6',
    // '7', '8', '9', 'a', 'b', 'c', 'd', 'e',
    // 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's',
    // 't', 'u', 'v', 'w', 'x', 'y', 'z',
    // 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',
    // 'O', 'P', 'Q', 'R', 'S', 'T', 'U',
    // 'V', 'W', 'X', 'Y', 'Z' };
    // 参考车牌号，验证码字符集中剔除容易混淆的I、l、O和o
    private static final char[] chars = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e',
        'f', 'g', 'h', 'i', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B',
        'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y',
        'Z'};
    private static final char[] digits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
    // 默认字符数量
    private final Integer SIZE;
    // 默认干扰线数量
    private final int LINES;
    // 默认宽度
    private final int WIDTH;
    // 默认高度
    private final int HEIGHT;
    // 默认字体大小
    private final int FONT_SIZE;
    // 默认字体倾斜
    private final boolean TILT;
    // 默认文件格式
    public static final String suffix = "png";

    private final Color BACKGROUND_COLOR;

    /**
     *
     * @Description 初始化基础参数
     * @param builder
     * <AUTHOR>
     * @date 2023年10月09日 17:54
     */
    private CaptchaUtils(Builder builder)
    {
        SIZE = builder.size;
        LINES = builder.lines;
        WIDTH = builder.width;
        HEIGHT = builder.height;
        FONT_SIZE = builder.fontSize;
        TILT = builder.tilt;
        BACKGROUND_COLOR = builder.backgroundColor;
    }

    /**
     *
     * @Description 实例化构造器对象
     * @return Builder
     * <AUTHOR>
     * @date 2023年10月09日 17:54
     */
    public static Builder newBuilder()
    {
        return new Builder();
    }

    /**
     *
     * @Description 生成随机验证码及图片
     * @return Captcha
     * <AUTHOR>
     * @date 2023年10月09日 17:52
     */
    public Captcha getCaptcha()
    {
        StringBuffer sb = new StringBuffer();
        // 创建空白图片
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        // 获取图片画笔
        Graphics2D graphic = image.createGraphics();
        // 设置抗锯齿
        graphic.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        // 设置画笔颜色
        graphic.setColor(BACKGROUND_COLOR);
        // 绘制矩形背景
        graphic.fillRect(0, 0, WIDTH, HEIGHT);
        // 画随机字符
        Random ran = new Random();

        // graphic.setBackground(Color.WHITE);

        // 计算每个字符占的宽度，这里预留一个字符的位置用于左右边距
        int codeWidth = WIDTH / (SIZE + 1);
        // 字符所处的y轴的坐标
        int y = HEIGHT * 3 / 4;

        for (int i = 0; i < SIZE; i++)
        {
            // 设置随机颜色
            graphic.setColor(getRandomColor());
            // 初始化字体
            Font font = new Font(null, Font.BOLD + Font.ITALIC, FONT_SIZE);

            if (TILT)
            {
                // 随机一个倾斜的角度 -45到45度之间
                int theta = ran.nextInt(30);
                // 随机一个倾斜方向 左或者右
                theta = (ran.nextBoolean() == true) ? theta : -theta;
                AffineTransform affineTransform = new AffineTransform();
                affineTransform.rotate(Math.toRadians(theta), 0, 0);
                font = font.deriveFont(affineTransform);
            }
            // 设置字体大小
            graphic.setFont(font);

            // 计算当前字符绘制的X轴坐标
            int x = (i * codeWidth) + (codeWidth / 2);

            // 取随机字符索引
            int n = ran.nextInt(chars.length);
            // 得到字符文本
            String code = String.valueOf(chars[n]);
            // 画字符
            graphic.drawString(code, x, y);

            // 记录字符
            sb.append(code);
        }
        // 画干扰线
        for (int i = 0; i < LINES; i++)
        {
            // 设置随机颜色
            graphic.setColor(getRandomColor());
            // 随机画线
            graphic.drawLine(ran.nextInt(WIDTH), ran.nextInt(HEIGHT), ran.nextInt(WIDTH), ran.nextInt(HEIGHT));
        }

        byte[] bytes = null;
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try
        {
            ImageIO.write(image, suffix, os);
            bytes = os.toByteArray();
            os.flush();
            os.close();
        }
        catch (IOException e)
        {
        }
        finally
        {
            try
            {
                os.flush();
                os.close();
            }
            catch (IOException e)
            {
            }
        }

        // 返回验证码和图片
        Captcha captcha = new Captcha();
        captcha.setContent(sb.toString());
        captcha.setBytes(bytes);
        return captcha;
    }

    /**
     *
     * @Description 生成数字验证码
     * @return String
     * <AUTHOR>
     * @date 2023年10月9日 上午10:52:11
     */
    public String getCaptchaContent()
    {
        StringBuffer sb = new StringBuffer();
        Random ran = new Random();
        for (int i = 0; i < SIZE; i++)
        {
            // 取随机字符索引
            int n = ran.nextInt(digits.length);
            // 得到字符文本
            String code = String.valueOf(digits[n]);
            // 记录字符
            sb.append(code);
        }
        return sb.toString();
    }

    /**
     *
     * @Description 随机取色
     * @return Color
     * <AUTHOR>
     * @date 2023年10月09日 17:53
     */
    private Color getRandomColor()
    {
        Random ran = new Random();
        Color color = new Color(ran.nextInt(256), ran.nextInt(32), ran.nextInt(256));
        return color;
    }

    /**
     *
     * @Description 构造器对象
     * @return Color
     * <AUTHOR>
     * @date 2023年10月09日 17:53
     */
    public static class Builder
    {
        // 默认字符数量
        private int size = 4;
        // 默认干扰线数量
        private int lines = 8;
        // 默认宽度
        private int width = 120;
        // 默认高度
        private int height = 40;
        // 默认字体大小
        private int fontSize = 30;
        // 默认字体倾斜
        private boolean tilt = true;
        // 背景颜色
        private Color backgroundColor = Color.LIGHT_GRAY;

        public Builder setSize(int size)
        {
            this.size = size;
            return this;
        }

        public Builder setLines(int lines)
        {
            this.lines = lines;
            return this;
        }

        public Builder setWidth(int width)
        {
            this.width = width;
            return this;
        }

        public Builder setHeight(int height)
        {
            this.height = height;
            return this;
        }

        public Builder setFontSize(int fontSize)
        {
            this.fontSize = fontSize;
            return this;
        }

        public Builder setTilt(boolean tilt)
        {
            this.tilt = tilt;
            return this;
        }

        public Builder setBackgroundColor(Color backgroundColor)
        {
            this.backgroundColor = backgroundColor;
            return this;
        }

        public CaptchaUtils build()
        {
            return new CaptchaUtils(this);
        }
    }
}
