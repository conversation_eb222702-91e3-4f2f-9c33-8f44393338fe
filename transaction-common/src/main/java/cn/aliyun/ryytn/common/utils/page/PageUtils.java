package cn.aliyun.ryytn.common.utils.page;

import java.util.List;
import java.util.stream.Collectors;

import com.github.pagehelper.PageInfo;

/**
 * @Description Page工具类
 * <AUTHOR>
 * @date 2023/11/2 15:19
 */
public class PageUtils
{
    /**
     *
     * @Description 外部列表未完成分页查询，封装pageInfo时做skip和limit
     * @param list
     * @param pageNum
     * @param pageSize
     * @return PageInfo<T>
     * @param <T>
     * <AUTHOR>
     * @date 2023年11月16日 15:05
     */
    public static <T> PageInfo<T> init(List<T> list, int pageNum, int pageSize)
    {
        PageInfo<T> pageInfo = new PageInfo<>();
        //手动设置PageInfo参数，进行手动分页
        int start = 0;
        int end = 0;
        int totalRecord = list.size();
        int totalPages = 0;
        pageInfo.setTotal(totalRecord);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageNum(pageNum);
        pageInfo.setSize(totalRecord);
        //计算获取对应的要显示的数据
        if (totalRecord % pageSize == 0)
        {
            totalPages = totalRecord / pageSize;
        }
        else
        {
            totalPages = totalRecord / pageSize + 1;
        }
        pageInfo.setPages(totalPages);
        //初始边界值计算
        if (pageNum == 1)
        {
            start = 0;
            pageInfo.setHasPreviousPage(false);
            pageInfo.setPrePage(0);
            pageInfo.setIsFirstPage(true);
        }
        else
        {
            start = pageInfo.getPageSize() * (pageInfo.getPageNum() - 1);
            pageInfo.setHasPreviousPage(true);
            pageInfo.setPrePage(pageNum - 1);
            pageInfo.setIsFirstPage(false);
        }
        pageInfo.setStartRow((pageNum - 1) * pageSize);
        //结束边界值计算
        if ((start + pageInfo.getPageSize() > pageInfo.getTotal()))
        {
            end = totalRecord;
            pageInfo.setHasNextPage(false);
            pageInfo.setIsLastPage(true);
            pageInfo.setEndRow(totalRecord);
        }
        else
        {
            end = start + pageInfo.getPageSize();
            pageInfo.setHasNextPage(true);
            pageInfo.setNextPage(pageNum + 1);
            pageInfo.setIsLastPage(false);
            pageInfo.setEndRow((pageNum) * pageSize);
        }
        if (start < end && end <= totalRecord)
        {
            long offset = Math.multiplyExact(pageNum - 1, pageSize);
            list = list.stream().skip(offset).limit(pageSize).collect(Collectors.toList());
            pageInfo.setList(list);
        }
        if (pageInfo.getSize() == 0)
        {
            pageInfo.setStartRow(0);
            pageInfo.setEndRow(0);
        }
        else
        {
            pageInfo.setStartRow(pageInfo.getStartRow() + 1);
            pageInfo.setEndRow(pageInfo.getStartRow() - 1 + pageInfo.getSize());
        }
        pageInfo.setPages(totalPages);
        pageInfo.setNavigateLastPage(totalPages > pageNum ? pageNum + 1 : totalPages);
        return pageInfo;
    }

    /**
     *
     * @Description 外部列表已完成分页查询，封装pageInfo
     * @param list
     * @param pageNum
     * @param pageSize
     * @param total
     * @return PageInfo<T>
     * @param <T>
     * <AUTHOR>
     * @date 2023年11月16日 15:05
     */
    public static <T> PageInfo<T> init(List<T> list, int pageNum, int pageSize, int total)
    {
        PageInfo<T> pageInfo = new PageInfo<>();
        //手动设置PageInfo参数，进行手动分页
        int start = 0;
        int end = 0;
        int totalRecord = total;
        int totalPages = 0;
        pageInfo.setTotal(totalRecord);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageNum(pageNum);
        pageInfo.setSize(totalRecord);
        //计算获取对应的要显示的数据
        if (pageSize != 0 && totalRecord % pageSize == 0)
        {
            totalPages = totalRecord / pageSize;
        }
        else
        {
            totalPages = totalRecord / pageSize + 1;
        }
        pageInfo.setPages(totalPages);
        //初始边界值计算
        if (pageNum == 1)
        {
            start = 0;
            pageInfo.setHasPreviousPage(false);
            pageInfo.setPrePage(0);
            pageInfo.setIsFirstPage(true);
        }
        else
        {
            start = pageInfo.getPageSize() * (pageInfo.getPageNum() - 1);
            pageInfo.setHasPreviousPage(true);
            pageInfo.setPrePage(pageNum - 1);
            pageInfo.setIsFirstPage(false);
        }
        pageInfo.setStartRow((pageNum - 1) * pageSize);
        //结束边界值计算
        if ((start + pageInfo.getPageSize() > pageInfo.getTotal()))
        {
            end = totalRecord;
            pageInfo.setHasNextPage(false);
            pageInfo.setIsLastPage(true);
            pageInfo.setEndRow(totalRecord);
        }
        else
        {
            end = start + pageInfo.getPageSize();
            pageInfo.setHasNextPage(true);
            pageInfo.setNextPage(pageNum + 1);
            pageInfo.setIsLastPage(false);
            pageInfo.setEndRow((pageNum) * pageSize);
        }
        if (start < end && end <= totalRecord)
        {
            pageInfo.setList(list);
        }
        if (pageInfo.getSize() == 0)
        {
            pageInfo.setStartRow(0);
            pageInfo.setEndRow(0);
        }
        else
        {
            pageInfo.setStartRow(pageInfo.getStartRow() + 1);
            pageInfo.setEndRow(pageInfo.getStartRow() - 1 + pageInfo.getSize());
        }
        pageInfo.setPages(totalPages);
        pageInfo.setNavigateLastPage(totalPages > pageNum ? pageNum + 1 : totalPages);
        return pageInfo;
    }
}
