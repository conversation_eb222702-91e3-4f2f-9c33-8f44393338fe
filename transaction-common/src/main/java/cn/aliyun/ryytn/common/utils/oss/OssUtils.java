package cn.aliyun.ryytn.common.utils.oss;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.Bucket;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 阿里云OSS文件存储工具类
 * <AUTHOR>
 * @date 2023/9/21 18:29
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OssUtils
{
    /**
     * OSS连接客户端实例
     */
    @Autowired
    private OSS ossClient;

    /**
     * 阿里云OSS服务桶名称
     */
    @Value("${aliyun.oss.bucket:transaction}")
    private String defaultBucket;

    /**
     *
     * @Description 创建存储桶
     * @param bucketName
     * <AUTHOR>
     * @date 2023年09月21日 18:57
     */
    public Bucket createBucket(String bucketName) throws Exception
    {
        if (StringUtils.isBlank(bucketName))
        {
            bucketName = defaultBucket;
        }
        Bucket bucket = null;
        try
        {
            bucket = ossClient.createBucket(bucketName);
        }
        catch (Exception e)
        {
            log.error("createBucket has exception:", e);
            throw new ServiceException(ErrorCodeConstants.FAIL_OSS_ERROR);
        }

        return bucket;
    }

    /**
     *
     * @Description 上传文件
     * @param bucketName
     * @param bytes
     * @return String 文件名称，唯一编号与业务关联
     * <AUTHOR>
     * @date 2023年09月21日 下午4:44:11
     */
    public void uploadFile(String fileId, byte[] bytes) throws Exception
    {
        uploadFile(fileId, bytes, null);
    }

    /**
     *
     * @Description 上传文件
     * @param bucketName
     * @param bytes
     * @param expireTime
     * @return String 文件名称，唯一编号与业务关联
     * <AUTHOR>
     * @date 2023年09月21日 下午4:44:11
     */
    public void uploadFile(String fileId, byte[] bytes, ObjectMetadata metadata) throws Exception
    {
        uploadFile(defaultBucket, fileId, bytes, metadata);
    }

    /**
     *
     * @Description 上传指定名称文件
     * @param bucketName
     * @param objectName
     * @param bytes
     * @return String
     * <AUTHOR>
     * @date 2023年09月21日 下午4:44:11
     */
    public void uploadFile(String bucketName, String fileId, byte[] bytes, ObjectMetadata metadata) throws Exception
    {
        InputStream inputStream = new ByteArrayInputStream(bytes);
        try
        {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileId, new ByteArrayInputStream(bytes));

            if (Objects.nonNull(metadata))
            {
                putObjectRequest.setMetadata(metadata);
            }

            ossClient.putObject(putObjectRequest);
        }
        catch (Exception e)
        {
            log.error("uploadFile has exception:", e);
            throw new ServiceException(ErrorCodeConstants.FAIL_OSS_ERROR);
        }
        finally
        {
            IOUtils.closeQuietly(inputStream);
        }
    }

    /**
     *
     * @Description 生成临时下载URL
     * @param request
     * @return URL
     * @throws Exception
     * <AUTHOR>
     * @date 2024年05月16日 11:19
     */
    public URL generatePresignedUrl(GeneratePresignedUrlRequest request) throws Exception
    {
        return ossClient.generatePresignedUrl(request);
    }

    /**
     *
     * @Description 下载文件
     * @param fileId
     * @return byte[]
     * <AUTHOR>
     * @date 2023年09月21日 19:39
     */
    public byte[] downloadFile(String fileId) throws Exception
    {
        return downloadFile(defaultBucket, fileId);
    }

    /**
     *
     * @Description 下载文件
     * @param bucketName
     * @param fileId
     * @return byte[]
     * <AUTHOR>
     * @date 2023年09月21日 19:39
     */
    public byte[] downloadFile(String bucketName, String fileId) throws Exception
    {
        byte[] bytes = null;
        InputStream content = null;
        try
        {
            // 调用ossClient.getObject返回一个OSSObject实例，该实例包含文件内容及文件元信息。
            OSSObject ossObject = ossClient.getObject(bucketName, fileId);
            // 调用ossObject.getObjectContent获取文件输入流，可读取此输入流获取其内容。
            content = ossObject.getObjectContent();
            bytes = IOUtils.toByteArray(content);
        }
        catch (Exception e)
        {
            log.error("downloadFile has exception:", e);
            throw new ServiceException(ErrorCodeConstants.FAIL_OSS_ERROR);
        }
        finally
        {
            IOUtils.closeQuietly(content);
        }
        return bytes;
    }

    public void deleteFile(List<String> fileIdList)
    {
        deleteFile(defaultBucket, fileIdList);
    }

    /**
     *
     * @Description 删除文件
     * @param bucketName
     * @param fileIdList
     * <AUTHOR>
     * @date 2023年09月21日 19:40
     */
    public void deleteFile(String bucketName, List<String> fileIdList)
    {
        if (CollectionUtils.isEmpty(fileIdList))
        {
            return;
        }

        // 批量删除接口一次最多删除1000个文件
        try
        {
            int times = 1;
            if (fileIdList.size() > DeleteObjectsRequest.DELETE_OBJECTS_ONETIME_LIMIT)
            {
                // 需要执行多少次批量删除
                times = Math.floorDiv(fileIdList.size(), DeleteObjectsRequest.DELETE_OBJECTS_ONETIME_LIMIT);
                // 如果有余数，执行次数+1
                if (Math.floorMod(fileIdList.size(), DeleteObjectsRequest.DELETE_OBJECTS_ONETIME_LIMIT) > 0)
                {
                    times++;
                }
            }
            // 至少执行一次
            for (int i = 0; i < times; i++)
            {
                long skip = Math.multiplyExact(i, DeleteObjectsRequest.DELETE_OBJECTS_ONETIME_LIMIT);
                List<String> keys = fileIdList.stream().skip(skip).limit(DeleteObjectsRequest.DELETE_OBJECTS_ONETIME_LIMIT).collect(Collectors.toList());
                ossClient.deleteObjects(new DeleteObjectsRequest(bucketName).withKeys(keys).withEncodingType("url"));
            }
        }
        catch (Exception e)
        {
            log.error("deleteFile has exception:", e);
            throw new ServiceException(ErrorCodeConstants.FAIL_OSS_ERROR);
        }
    }
}
