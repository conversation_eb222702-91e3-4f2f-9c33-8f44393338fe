package cn.aliyun.ryytn.common.excel;

import org.apache.poi.ss.usermodel.Row;

import com.alibaba.excel.write.style.row.AbstractRowHeightStyleStrategy;

/**
 * @Description 隐藏行处理类
 * <AUTHOR>
 * @date 2023/10/30 15:51
 */
public class HideRowSheetWriterHandler extends AbstractRowHeightStyleStrategy
{
    private Integer index;

    public HideRowSheetWriterHandler(Integer index)
    {
        this.index = index;
    }

    @Override
    protected void setHeadColumnHeight(Row row, int i)
    {
        if (index.equals(i))
        {
            row.setHeightInPoints(0);
        }
    }

    @Override
    protected void setContentColumnHeight(Row row, int i)
    {
    }
}
