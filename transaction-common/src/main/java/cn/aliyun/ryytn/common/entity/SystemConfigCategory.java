package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * @Description 系统配置类别
 * <AUTHOR>
 * @date 2022年5月30日 下午7:18:47
 */
@Setter
@Getter
@ToString
public class SystemConfigCategory implements Serializable
{
    private static final long serialVersionUID = -1348040954686389349L;
    /**
     * @Description 系统配置类型编号
     * <AUTHOR>
     * @date 2022年5月30日 下午7:18:59
     */
    private String categoryId;

    /**
     * @Description 系统配置类型父编号
     * <AUTHOR>
     * @date 2022年5月30日 下午7:18:59
     */
    private String parentId;

    /**
     * @Description 系统配置类型所有父编号
     * <AUTHOR>
     * @date 2022年5月30日 下午7:18:59
     */
    private String parentIds;

    /**
     * @Description 系统配置类型名称
     * <AUTHOR>
     * @date 2022年5月30日 下午7:18:59
     */
    private String name;

    /**
     * @Description 系统配置类型状态
     * <AUTHOR>
     * @date 2022年5月30日 下午7:18:59
     */
    private Integer status;

    /**
     * @Description 系统配置类型排序
     * <AUTHOR>
     * @date 2022年5月30日 下午7:18:59
     */
    private Integer sortNo;
}
