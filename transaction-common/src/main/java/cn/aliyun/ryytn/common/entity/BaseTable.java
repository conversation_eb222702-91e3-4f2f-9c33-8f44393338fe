package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * @Description 基础表数据类
 * <AUTHOR>
 * @date 2023/10/25 15:59
 */
@Setter
@Getter
@ToString
public class BaseTable<T> implements Serializable
{
    private static final long serialVersionUID = -7296052726671142542L;
    /**
     * 动态数据表头数组
     */
    private List<String> headArray;

    /**
     * 表数据
     */
    private T list;
}
