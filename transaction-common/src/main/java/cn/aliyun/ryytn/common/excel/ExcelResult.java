package cn.aliyun.ryytn.common.excel;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 电子表格导入结果
 * <AUTHOR>
 * @date 2023/10/19 16:22
 */
@Setter
@Getter
@ToString
public class ExcelResult<T> implements Serializable
{
    private static final long serialVersionUID = 7492748262094708322L;
    /**
     * 成功数量
     */
    private long success;

    /**
     * 失败数量
     */
    private long failed;

    /**
     * 失败列表
     */
    private List<T> failedList;

    /**
     * 导入结果表格字节数组
     */
    private byte[] bytes;
}
