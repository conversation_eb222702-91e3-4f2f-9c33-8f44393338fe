package cn.aliyun.ryytn.common.exception;

/**
 * 业务受理失败异常
 *
 * <AUTHOR>
 * @date 2022年04月19日 14:23
 */
public class DataqServiceException extends RuntimeException {


    private static final long serialVersionUID = -6193372276385384454L;
    private Integer errorCode;
    private String errorMessage;


    public DataqServiceException() {
        super();
    }

    public DataqServiceException(Integer errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }

    public DataqServiceException(ExceptionEnums exceptionEnum, String message) {
        super(message);
        this.errorCode = exceptionEnum.getCode();
        this.errorMessage = message;
    }

    public DataqServiceException(ExceptionEnums exceptionEnum) {
        super(exceptionEnum.getMessage());
        this.errorCode = exceptionEnum.getCode();
        this.errorMessage = exceptionEnum.getMessage();
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }


}


