package cn.aliyun.ryytn.common.dataq.api;

import java.util.Map;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;

import cn.aliyun.ryytn.common.dataq.DataqResult;


/**
 * @Description Dataq接口
 * <AUTHOR>
 * @date 2023/10/20 16:34
 */
public interface DataqService
{
    /**
     *
     * @Description Dataq接口调用
     * @param method
     * @param path
     * @param head
     * @param param
     * @param body
     * @return T
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 10:35
     */
    DataqResult<?> invoke(HttpMethod method, String path, Map<String, Object> head, Map<String, Object> param, Object body) throws Exception;
}
