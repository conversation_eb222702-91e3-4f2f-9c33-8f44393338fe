package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 菜单对象，申晓新要求，菜单和按钮分两张表设计
 * <AUTHOR>
 * @date 2023/10/8 11:05
 */
@Setter
@Getter
@ToString
public class Page implements Serializable
{
    private static final long serialVersionUID = 7105920832679515375L;
    /**
     * 菜单编号
     */
    private String id;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单别名
     */
    private String alias;

    /**
     * 菜单权限码
     */
    private String permission;

    /**
     * 父菜单编号，根页面为-1
     */
    private String parentId;

    /**
     * 所有父菜单编号，多个编号英文逗号分隔
     */
    private String parentIds;

    /**
     * 依赖菜单编号，多个编号英文逗号分隔
     */
    private String dependencyIds;

    /**
     * 菜单类型
     */
    private String type;

    /**
     * 菜单路由
     */
    private String path;

    /**
     * 菜单配置路由
     */
    private String configPath;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 图标路径
     */
    private String icon;

    /**
     * 模块编号
     */
    private String moudelId;

    /**
     * 排序
     */
    private Long sortNo;

    /**
     * 是否合计
     */
    private Boolean sumFlag;

    /**
     * 描述
     */
    private String description;

    /**
     * 子菜单列表
     */
    private List<Page> subPageList;

    /**
     * 子按钮列表
     */
    private List<Button> subButtonList;
}
