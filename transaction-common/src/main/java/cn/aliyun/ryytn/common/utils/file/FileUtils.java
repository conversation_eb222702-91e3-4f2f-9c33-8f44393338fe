package cn.aliyun.ryytn.common.utils.file;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 文件工具类
 * <AUTHOR>
 * @date 2023年9月21日 下午2:28:11
 */
@Slf4j
public class FileUtils
{
    /**
     * 文件名分隔符
     */
    public static final String FILENAME_SEPARATOR = ".";

    /**
     * 文件响应头：浏览器直接展示
     */
    public static final String CONTENTDISPOSITION_INLINE = "inline;filename=";

    /**
     * 文件响应头：浏览器附件下载
     */
    public static final String CONTENTDISPOSITION_ATTACHMENT = "attachment;filename=";

    /**
     * 文件类型：图片文件，对应edu_service_file表fileType字段
     */
    public static final Integer FILE_TYPE_PICTURE = 0;

    /**
     * 文件类型：视频文件，对应edu_service_file表fileType字段
     */
    public static final Integer FILE_TYPE_VIDEO = 1;

    /**
     * 文件类型：音频文件，对应edu_service_file表fileType字段
     */
    public static final Integer FILE_TYPE_AUDIO = 2;

    /**
     * 文件类型：文本文件，对应edu_service_file表fileType字段
     */
    public static final Integer FILE_TYPE_TEXT = 3;

    /**
     * 文件类型：文档文件，对应edu_service_file表fileType字段
     */
    public static final Integer FILE_TYPE_DOCUMENT = 4;

    /**
     * 文件类型：压缩文件，对应edu_service_file表fileType字段
     */
    public static final Integer FILE_TYPE_ZIP = 5;

    /**
     * 文件类型：脚本文件，对应edu_service_file表fileType字段
     */
    public static final Integer FILE_TYPE_SCRIPT = 6;

    /**
     * 文件后缀在Map中找不到时，文件类型为其他，对应edu_service_file表fileType字段
     */
    public static final Integer FILE_TYPE_OTHER = 99;

    /**
     * 文件后缀文件类型Map，key：文件后缀，value：文件类型枚举，对应edu_service_file表fileType字段
     */
    public static final Map<String, Integer> FILE_EXTNAME_FILETYPE_MAP = new HashMap<String, Integer>();

    static
    {
        FILE_EXTNAME_FILETYPE_MAP.put("doc", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("323", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("acx", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("ai", FILE_TYPE_SCRIPT);
        FILE_EXTNAME_FILETYPE_MAP.put("aif", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("aifc", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("aiff", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("asf", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("asr", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("asx", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("au", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("avi", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("axs", FILE_TYPE_SCRIPT);
        FILE_EXTNAME_FILETYPE_MAP.put("bas", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("bcpio", FILE_TYPE_ZIP);
        FILE_EXTNAME_FILETYPE_MAP.put("bin", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("bmp", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("c", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("cat", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("cdf", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("cdf", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("cer", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("class", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("clp", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("cmx", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("cod", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("cpio", FILE_TYPE_ZIP);
        FILE_EXTNAME_FILETYPE_MAP.put("crd", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("crl", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("crt", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("csh", FILE_TYPE_SCRIPT);
        FILE_EXTNAME_FILETYPE_MAP.put("css", FILE_TYPE_SCRIPT);
        FILE_EXTNAME_FILETYPE_MAP.put("dcr", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("der", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("dir", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("dll", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("dms", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("doc", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("docm", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("docx", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("dot", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("dotm", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("dotx", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("dvi", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("dxr", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("eps", FILE_TYPE_SCRIPT);
        FILE_EXTNAME_FILETYPE_MAP.put("etx", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("evy", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("exe", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("fif", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("flr", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("gif", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("gtar", FILE_TYPE_ZIP);
        FILE_EXTNAME_FILETYPE_MAP.put("gz", FILE_TYPE_ZIP);
        FILE_EXTNAME_FILETYPE_MAP.put("h", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("hdf", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("hlp", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("hqx", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("hta", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("htc", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("htm", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("html", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("htt", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("ico", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("ief", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("iii", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("ins", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("isp", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("jfif", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("jpe", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("jpeg", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("jpg", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("js", FILE_TYPE_SCRIPT);
        FILE_EXTNAME_FILETYPE_MAP.put("latex", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("lha", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("lsf", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("lsx", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("lzh", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("m13", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("m14", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("m3u", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("man", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("mdb", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("me", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("mht", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("mhtml", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("mid", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("mny", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("mov", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("movie", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("mp2", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("mp3", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("mp4", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("mpa", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("mpe", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("mpeg", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("mpg", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("mpp", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("mpv2", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("ms", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("msg", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("mvb", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("nc", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("nws", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("oda", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("p10", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("p12", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("p7b", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("p7c", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("p7m", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("p7r", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("p7s", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("pbm", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("pdf", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("pfx", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("pgm", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("pko", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("pma", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("pmc", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("pml", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("pmr", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("pmw", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("png", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("pnm", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("pot", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("potm", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("potx", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("ppa", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("ppam", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("ppm", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("pps", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("ppsm", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("ppsx", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("ppt", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("pptm", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("pptx", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("prf", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("ps", FILE_TYPE_SCRIPT);
        FILE_EXTNAME_FILETYPE_MAP.put("pub", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("qt", FILE_TYPE_VIDEO);
        FILE_EXTNAME_FILETYPE_MAP.put("ra", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("ram", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("ras", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("rgb", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("rmi", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("roff", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("rtf", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("rtx", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("scd", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("sct", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("setpay", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("setreg", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("sh", FILE_TYPE_SCRIPT);
        FILE_EXTNAME_FILETYPE_MAP.put("shar", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("sit", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("snd", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("spc", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("spl", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("src", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("sst", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("stl", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("stm", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("sv4cpio", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("sv4crc", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("svg", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("swf", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("t", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("tar", FILE_TYPE_ZIP);
        FILE_EXTNAME_FILETYPE_MAP.put("tcl", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("tex", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("texi", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("texinfo", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("tgz", FILE_TYPE_ZIP);
        FILE_EXTNAME_FILETYPE_MAP.put("tif", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("tiff", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("tr", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("trm", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("tsv", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("txt", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("uls", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("ustar", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("vcf", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("vrml", FILE_TYPE_TEXT);
        FILE_EXTNAME_FILETYPE_MAP.put("wav", FILE_TYPE_AUDIO);
        FILE_EXTNAME_FILETYPE_MAP.put("wcm", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("wdb", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("wks", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("wmf", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("wps", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("wri", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("wrl", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("wrz", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("xaf", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("xbm", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("xla", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xlam", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xlc", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xlm", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xls", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xlsb", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xlsm", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xlsx", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xlt", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xltm", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xltx", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xlw", FILE_TYPE_DOCUMENT);
        FILE_EXTNAME_FILETYPE_MAP.put("xof", FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("xpm", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("xwd", FILE_TYPE_PICTURE);
        FILE_EXTNAME_FILETYPE_MAP.put("z", FILE_TYPE_ZIP);
        FILE_EXTNAME_FILETYPE_MAP.put("zip", FILE_TYPE_ZIP);
        FILE_EXTNAME_FILETYPE_MAP.put(null, FILE_TYPE_OTHER);
        FILE_EXTNAME_FILETYPE_MAP.put("", FILE_TYPE_OTHER);
    }

    /**
     * 响应头其他CONTENTTYPE
     */
    public static final String CONTENTTYPE_OTHER = "application/octet-stream";

    /**
     *文件后缀-CONTENTTYPE映射关系
     */
    public static final Map<String, String> FILE_EXTNAME_CONTENTTYPE_MAP = new HashMap<String, String>();

    static
    {
        FILE_EXTNAME_CONTENTTYPE_MAP.put("doc", "application/msword");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("323", "text/h323");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("acx", "application/internet-property-stream");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ai", "application/postscript");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("aif", "audio/x-aiff");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("aifc", "audio/x-aiff");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("aiff", "audio/x-aiff");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("asf", "video/x-ms-asf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("asr", "video/x-ms-asf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("asx", "video/x-ms-asf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("au", "audio/basic");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("avi", "video/x-msvideo");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("axs", "application/olescript");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("bas", "text/plain");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("bcpio", "application/x-bcpio");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("bin", "application/octet-stream");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("bmp", "image/bmp");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("c", "text/plain");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("cat", "application/vnd.ms-pkiseccat");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("cdf", "application/x-cdf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("cdf", "application/x-netcdf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("cer", "application/x-x509-ca-cert");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("class", "application/octet-stream");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("clp", "application/x-msclip");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("cmx", "image/x-cmx");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("cod", "image/cis-cod");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("cpio", "application/x-cpio");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("crd", "application/x-mscardfile");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("crl", "application/pkix-crl");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("crt", "application/x-x509-ca-cert");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("csh", "application/x-csh");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("css", "text/css");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("dcr", "application/x-director");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("der", "application/x-x509-ca-cert");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("dir", "application/x-director");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("dll", "application/x-msdownload");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("dms", "application/octet-stream");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("doc", "application/msword");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("docm", "application/vnd.ms-word.document.macroEnabled.12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("docx",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("dot", "application/msword");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("dotm", "application/vnd.ms-word.template.macroEnabled.12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("dotx",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.template");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("dvi", "application/x-dvi");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("dxr", "application/x-director");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("eps", "application/postscript");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("etx", "text/x-setext");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("evy", "application/envoy");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("exe", "application/octet-stream");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("fif", "application/fractals");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("flr", "x-world/x-vrml");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("gif", "image/gif");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("gtar", "application/x-gtar");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("gz", "application/x-gzip");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("h", "text/plain");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("hdf", "application/x-hdf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("hlp", "application/winhlp");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("hqx", "application/mac-binhex40");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("hta", "application/hta");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("htc", "text/x-component");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("htm", "text/html");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("html", "text/html");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("htt", "text/webviewhtml");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ico", "image/x-icon");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ief", "image/ief");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("iii", "application/x-iphone");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ins", "application/x-internet-signup");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("isp", "application/x-internet-signup");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("jfif", "image/pipeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("jpe", "image/jpeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("jpeg", "image/jpeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("jpg", "image/jpeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("js", "application/x-javascript");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("latex", "application/x-latex");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("lha", "application/octet-stream");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("lsf", "video/x-la-asf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("lsx", "video/x-la-asf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("lzh", "application/octet-stream");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("m13", "application/x-msmediaview");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("m14", "application/x-msmediaview");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("m3u", "audio/x-mpegurl");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("man", "application/x-troff-man");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mdb", "application/x-msaccess");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("me", "application/x-troff-me");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mht", "message/rfc822");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mhtml", "message/rfc822");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mid", "audio/mid");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mny", "application/x-msmoney");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mov", "video/quicktime");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("movie", "video/x-sgi-movie");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mp2", "video/mpeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mp3", "audio/mpeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mp4", "video/mp4");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mpa", "video/mpeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mpe", "video/mpeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mpeg", "video/mpeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mpg", "video/mpeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mpp", "application/vnd.ms-project");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mpv2", "video/mpeg");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ms", "application/x-troff-ms");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("msg", "application/vnd.ms-outlook");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("mvb", "application/x-msmediaview");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("nc", "application/x-netcdf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("nws", "message/rfc822");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("oda", "application/oda");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("p10", "application/pkcs10");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("p12", "application/x-pkcs12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("p7b", "application/x-pkcs7-certificates");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("p7c", "application/x-pkcs7-mime");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("p7m", "application/x-pkcs7-mime");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("p7r", "application/x-pkcs7-certreqresp");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("p7s", "application/x-pkcs7-signature");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pbm", "image/x-portable-bitmap");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pdf", "application/pdf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pfx", "application/x-pkcs12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pgm", "image/x-portable-graymap");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pko", "application/ynd.ms-pkipko");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pma", "application/x-perfmon");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pmc", "application/x-perfmon");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pml", "application/x-perfmon");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pmr", "application/x-perfmon");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pmw", "application/x-perfmon");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("png", "image/png");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pnm", "image/x-portable-anymap");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pot", "application/vnd.ms-powerpoint");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("potm", "application/vnd.ms-powerpoint.presentation.macroEnabled.12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("potx",
            "application/vnd.openxmlformats-officedocument.presentationml.template");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ppa", "application/vnd.ms-powerpoint");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ppam", "application/vnd.ms-powerpoint.addin.macroEnabled.12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ppm", "image/x-portable-pixmap");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pps", "application/vnd.ms-powerpoint");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ppsm", "application/vnd.ms-powerpoint.slideshow.macroEnabled.12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ppsx",
            "application/vnd.openxmlformats-officedocument.presentationml.slideshow");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ppt", "application/vnd.ms-powerpoint");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pptm", "application/vnd.ms-powerpoint.presentation.macroEnabled.12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pptx",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("prf", "application/pics-rules");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ps", "application/postscript");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("pub", "application/x-mspublisher");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("qt", "video/quicktime");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ra", "audio/x-pn-realaudio");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ram", "audio/x-pn-realaudio");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ras", "image/x-cmu-raster");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("rgb", "image/x-rgb");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("rmi", "audio/mid");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("roff", "application/x-troff");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("rtf", "application/rtf");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("rtx", "text/richtext");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("scd", "application/x-msschedule");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("sct", "text/scriptlet");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("setpay", "application/set-payment-initiation");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("setreg", "application/set-registration-initiation");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("sh", "application/x-sh");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("shar", "application/x-shar");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("sit", "application/x-stuffit");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("snd", "audio/basic");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("spc", "application/x-pkcs7-certificates");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("spl", "application/futuresplash");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("src", "application/x-wais-source");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("sst", "application/vnd.ms-pkicertstore");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("stl", "application/vnd.ms-pkistl");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("stm", "text/html");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("sv4cpio", "application/x-sv4cpio");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("sv4crc", "application/x-sv4crc");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("svg", "image/svg+xml");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("swf", "application/x-shockwave-flash");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("t", "application/x-troff");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("tar", "application/x-tar");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("tcl", "application/x-tcl");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("tex", "application/x-tex");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("texi", "application/x-texinfo");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("texinfo", "application/x-texinfo");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("tgz", "application/x-compressed");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("tif", "image/tiff");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("tiff", "image/tiff");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("tr", "application/x-troff");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("trm", "application/x-msterminal");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("tsv", "text/tab-separated-values");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("txt", "text/html;charset=UTF-8");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("uls", "text/iuls");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("ustar", "application/x-ustar");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("vcf", "text/x-vcard");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("vrml", "x-world/x-vrml");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("wav", "audio/x-wav");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("wcm", "application/vnd.ms-works");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("wdb", "application/vnd.ms-works");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("wks", "application/vnd.ms-works");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("wmf", "application/x-msmetafile");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("wps", "application/vnd.ms-works");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("wri", "application/x-mswrite");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("wrl", "x-world/x-vrml");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("wrz", "x-world/x-vrml");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xaf", "x-world/x-vrml");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xbm", "image/x-xbitmap");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xla", "application/vnd.ms-excel");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xlam", "application/vnd.ms-excel.addin.macroEnabled.12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xlc", "application/vnd.ms-excel");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xlm", "application/vnd.ms-excel");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xls", "application/vnd.ms-excel");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xlsb", "application/vnd.ms-excel.sheet.binary.macroEnabled.12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xlsm", "application/vnd.ms-excel.sheet.macroEnabled.12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xlt", "application/vnd.ms-excel");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xltm", "application/vnd.ms-excel.template.macroEnabled.12");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xltx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.template");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xlw", "application/vnd.ms-excel");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xof", "x-world/x-vrml");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xpm", "image/x-xpixmap");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("xwd", "image/x-xwindowdump");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("z", "application/x-compress");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("zip", "application/zip");
        FILE_EXTNAME_CONTENTTYPE_MAP.put(null, "text/html;charset=UTF-8");
        FILE_EXTNAME_CONTENTTYPE_MAP.put("", "text/html;charset=UTF-8");
    }

    /**
     *
     * @Description 获取文件扩展名，并统一转换为小写英文字母
     * @param file
     * @return String
     * <AUTHOR>
     * @date 2023年9月21日 下午2:28:24
     */
    public static String getExtName(MultipartFile file)
    {
        // 获取文件的扩展名
        String originalFilename = file.getOriginalFilename().toLowerCase();
        return StringUtils.substringAfterLast(originalFilename, FILENAME_SEPARATOR);
    }

    /**
     *
     * @Description 获取文件扩展名，并统一转换为小写英文字母
     * @param file
     * @return String
     * <AUTHOR>
     * @date 2023年9月21日 下午2:28:24
     */
    public static String getExtName(String fileName)
    {
        // 获取文件的扩展名
        return StringUtils.substringAfterLast(fileName, FILENAME_SEPARATOR);
    }

    /**
     *
     * @Description 根据文件名获取文件类型
     * @param fileName
     * @return String
     * <AUTHOR>
     * @date 2023年09月22日 11:51
     */
    public static Integer getFileType(String fileName)
    {
        return FILE_EXTNAME_FILETYPE_MAP.get(getExtName(fileName));
    }

    /**
     *
     * @Description 根据文件名获取文件http contenttype
     * @param fileName
     * @return String
     * <AUTHOR>
     * @date 2023年09月22日 11:51
     */
    public static String getContentType(String fileName)
    {
        return FILE_EXTNAME_CONTENTTYPE_MAP.get(getExtName(fileName));
    }

    /**
     *
     * @Description 获取文件字节数组
     * @param fileUrl
     * @return byte[]
     * <AUTHOR>
     * @date 2023年9月21日 下午2:32:15
     */
    public static byte[] urlToByte(String fileUrl)
    {
        byte[] bytes = null;
        InputStream inputStream = null;
        try
        {

            URL url = new URL(fileUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5 * 1000);
            inputStream = conn.getInputStream();
            bytes = IOUtils.toByteArray(inputStream);
        }
        catch (Exception e)
        {
            log.error("urlToByte has exception:", e);
        }
        finally
        {
            IOUtils.closeQuietly(inputStream);
        }
        return bytes;
    }

    /**
     *
     * @Description 文件转换byte数组
     * @param file
     * @return byte[]
     * <AUTHOR>
     * @date 2023年9月21日 下午2:43:51
     */
    public static byte[] fileTobyte(File file)
    {
        byte[] bytes = null;
        FileInputStream inputStream = null;
        try
        {
            inputStream = new FileInputStream(file);
            bytes = IOUtils.toByteArray(inputStream);
        }
        catch (Exception e)
        {
            log.error("fileTobyte has exception:", e);
        }
        finally
        {
            IOUtils.closeQuietly(inputStream);
        }
        return bytes;
    }
}
