package cn.aliyun.ryytn.common.entity.sap;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/3/12 15:27
 * @Description
 */
@Data
public class SapCtrl implements Serializable {

    private static final long serialVersionUID = 6603963946039805990L;
    /**
     * 发送系统
     */
    @JSONField(name = "SYSID")
    private String sysId;
    /**
     * 接收系统
     */
    @JSONField(name = "REVID")
    private String revId;
    /**
     * 接口ID
     */
    @JSONField(name = "FUNID")
    private String funId;
    /**
     * 调用ID
     */
    @JSONField(name = "INFID")
    private String infId;
    /**
     * 调用人员
     */
    @JSONField(name = "UNAME")
    private String uname;
    /**
     * 调用日期
     */
    @JSONField(name = "DATUM", format = DatePattern.NORM_DATE_PATTERN)
    private Date datum;
    /**
     * 调用时间
     */
    @JSONField(name = "UZEIT", format = DatePattern.NORM_TIME_PATTERN)
    private Date uzEit;
    /**
     * 数据主键值
     */
    @JSONField(name = "KEYID")
    private String keyId;
    /**
     * 消息类型
     */
    @JSONField(name = "MSGTY")
    private String msgTy;
    /**
     * 消息内容
     */
    @JSONField(name = "MSAGE")
    private String msAge;
    /**
     * MD5
     */
    @JSONField(name = "MD5")
    private String md5;


    /**
     * METHOD
     */
    @JSONField(name = "METHOD")
    private String method;
    /**
     * PAGE_NO
     */
    @JSONField(name = "PAGE_NO")
    private String pageNo;
    /**
     * PAGE_SIZE
     */
    @JSONField(name = "PAGE_SIZE")
    private String pageSize;
}
