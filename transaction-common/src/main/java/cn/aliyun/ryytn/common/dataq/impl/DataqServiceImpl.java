package cn.aliyun.ryytn.common.dataq.impl;

import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.aliyun.dataq.service.dbus.signature.DaasClient;
import com.aliyun.dataq.service.dbus.signature.DataQService;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description dataq接口实现类
 * <AUTHOR>
 * @date 2023/10/20 16:43
 */
@Slf4j
@Service
public class DataqServiceImpl implements DataqService
{

    @Value("${aliyun.dataq.endpoint}")
    private String endPoint;

    @Value("${aliyun.dataq.appcode}")
    private String appCode;

    @Value("${aliyun.dataq.ak}")
    private String ak;

    @Value("${aliyun.dataq.sk}")
    private String sk;

    @Value("${aliyun.dataq.timeout}")
    private Integer timeout;

    @Value("${aliyun.dataq.maxconnection}")
    private Integer maxConnection;

    private static final String SUCCESS = "SUCCESS";

    /**
     *
     * @Description Dataq接口调用
     * @param method
     * @param path
     * @param head
     * @param param
     * @param body
     * @return DataqResult<T>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 10:35
     */
    @Override
    public DataqResult<?> invoke(HttpMethod method, String path, Map<String, Object> head, Map<String, Object> param, Object body)
        throws Exception
    {
        DataQService.Builder builder = new DataQService.Builder();
        builder.setPath(path);
        builder.setAppKey(ak);
        builder.setAppSecret(sk);
        builder.setModuleName(appCode);
        // 请求头
        if (MapUtils.isNotEmpty(head))
        {
            builder.addHeader(head);
        }
        //添加queryString
        if (MapUtils.isNotEmpty(param))
        {
            builder.addQueryParam(param);
        }
        //添加RequestBody参数
        if (Objects.nonNull(body))
        {
            builder.setRequestBody(body);
        }
        //设置请求方法
        DataQService dataQService = builder.setHttpMethod(method).build();
        //初始化请求客户端
        DaasClient client = new DaasClient(endPoint);
        //发送请求
        Object response = client.invoke(dataQService, timeout, maxConnection);

        if (Objects.isNull(response))
        {
            log.error("dataq url is:{}", path);
            log.error("dataq request is:{}", JSON.toJSONString(body));
            log.error("dataq response is null");
            throw new ServiceException(ErrorCodeConstants.FAIL_DATAQ_ERROR);
        }

        DataqResult dataqResult = JSON.parseObject(response.toString(), DataqResult.class);

        if (Objects.isNull(dataqResult) || !Objects.equals(SUCCESS, dataqResult.getCode()))
        {
            log.error("dataq url is:{}", path);
            log.error("dataq request is:{}", JSON.toJSONString(body));
            log.error("dataq response is:{}", JSON.toJSONString(dataqResult));
            throw new ServiceException(ErrorCodeConstants.FAIL_DATAQ_ERROR);
        }
        return dataqResult;
    }
}
