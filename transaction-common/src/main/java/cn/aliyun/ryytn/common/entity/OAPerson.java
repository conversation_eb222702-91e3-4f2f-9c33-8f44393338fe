package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description OA系统查询人员信息响应
 * <AUTHOR>
 * @date 2023/9/26 17:56
 */
@Setter
@Getter
@ToString
public class OAPerson implements Serializable
{
    private static final long serialVersionUID = -9111982812598779059L;
    /**
     * 编号
     */
    @JSONField(name = "id")
    private String id;

    /**
     * 编码
     */
    @JSONField(name = "workcode")
    private String workCode;

    /**
     * 名称
     */
    @JSONField(name = "lastname")
    private String lastName;

    /**
     * 登录名
     */
    @JSONField(name = "loginid")
    private String loginId;

    /**
     * 主次账号标志：1：次账号,其他：主账号
     */
    @JSONField(name = "accounttype")
    private Integer accountType;

    /**
     * 主账号id （当accounttype 为 1 有效）
     */
    @JSONField(name = "belongto")
    private String beLongTo;

    /**
     * 部门编号
     */
    @JSONField(name = "departmentid")
    private String departmentId;

    /**
     * 岗位编号
     */
    @JSONField(name = "jobtitle")
    private String jobTitleId;

    /**
     * 办公地点
     */
    @JSONField(name = "locationid")
    private String locationId;

    /**
     * 状态: 0 试用 1 正式 2 临时 3 试用延期 4 解聘 5
     * 离职 6 退休 7 无效
     */
    @JSONField(name = "status")
    private Integer status;

    /**
     * 系统语言
     */
    @JSONField(name = "language")
    private String language;

    /**
     * 职责描述
     */
    @JSONField(name = "jobactivitydesc")
    private String jobActivityDesc;

    /**
     * 职级
     */
    @JSONField(name = "joblevel")
    private String jobLevel;

    /**
     * 职称
     */
    @JSONField(name = "jobcall")
    private String jobCall;

    /**
     * 上级人员编号
     */
    @JSONField(name = "managerid")
    private String managerId;

    /**
     * 助理人员编号
     */
    @JSONField(name = "assistantid")
    private String assistantId;

    /**
     * 性别
     */
    @JSONField(name = "sex")
    private String sex;

    /**
     * 办公电话
     */
    @JSONField(name = "telephone")
    private String telephone;

    /**
     * 移动电话
     */
    @JSONField(name = "mobile")
    private String mobile;

    /**
     * 其他电话
     */
    @JSONField(name = "mobilecall")
    private String mobileCall;

    /**
     * 邮箱
     */
    @JSONField(name = "email")
    private String email;

    /**
     * 合同开始日期
     */
    @JSONField(name = "startdate")
    private String startDate;

    /**
     * 合同结束日期
     */
    @JSONField(name = "enddate")
    private String endDate;

    /**
     * 安全级别
     */
    @JSONField(name = "seclevel")
    private String secLevel;

    /**
     * 密码，密文
     */
    @JSONField(name = "password")
    private String password;

    /**
     * 身份证
     */
    @JSONField(name = "certificatenum")
    private String certificateNum;

    /**
     * 生日
     */
    @JSONField(name = "birthday")
    private String birthday;

    /**
     * 身高
     */
    @JSONField(name = "height")
    private String height;

    /**
     * 体重
     */
    @JSONField(name = "weight")
    private String weight;

    /**
     * 民族
     */
    @JSONField(name = "folk")
    private String folk;

    /**
     * 籍贯
     */
    @JSONField(name = "nativeplace")
    private String nativePlace;

    /**
     * 健康状况
     */
    @JSONField(name = "healthinfo")
    private String healthInfo;

    /**
     * 婚姻状况
     */
    @JSONField(name = "maritalstatus")
    private String maritalStatus;

    /**
     * 暂住证号码
     */
    @JSONField(name = "tempresidentnumber")
    private String tempResidentNumber;

    /**
     * 户口
     */
    @JSONField(name = "residentplace")
    private String residentPlace;

    /**
     * 户口所在地
     */
    @JSONField(name = "regresidentplace")
    private String regresidentPlace;

    /**
     * 家庭联系方式
     */
    @JSONField(name = "homeaddress")
    private String homeAddress;

    /**
     * 政治面貌
     */
    @JSONField(name = "policy")
    private String policy;

    /**
     * 入团日期
     */
    @JSONField(name = "bememberdate")
    private String beMemberDate;

    /**
     * 入党日期
     */
    @JSONField(name = "bepartydate")
    private String bePartyDate;

    /**
     * 学位
     */
    @JSONField(name = "degree")
    private String degree;

    /**
     * 学历
     */
    @JSONField(name = "educationlevel")
    private String educationLevel;

    /**
     * 是否公会会员
     */
    @JSONField(name = "islabouunion")
    private Integer isLabouunion;

    /**
     * 最后修改日期
     */
    @JSONField(name = "lastmoddate")
    private String lastModDate;

    /**
     * 排序
     */
    @JSONField(name = "dsporder")
    private Double sortNo;

    /**
     * 创建时间
     */
    @JSONField(name = "created")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date createdTime;

    /**
     * 修改时间
     */
    @JSONField(name = "modified")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date updatedTime;

    /**
     * 同步时间，精确到毫秒
     */
    private Long syncTime;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        OAPerson personOA = (OAPerson) o;
        return Objects.equals(id, personOA.getId());
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        return result;
    }
}
