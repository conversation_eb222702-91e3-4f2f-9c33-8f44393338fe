package cn.aliyun.ryytn.common.entity.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 商品继承关系实体
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "商品继承关系实体")
public class ProductInheritance {

    @ApiModelProperty("自增主键")
    private String id;

    @ApiModelProperty("原商品ID")
    private String originalSkuId;

    @ApiModelProperty("新品商品ID")
    private String newSkuId;

    @ApiModelProperty("关系类型")
    private Integer relationType;

    @ApiModelProperty("定制店铺")
    private String customStore;

    @ApiModelProperty("定制渠道")
    private String customChannel;

    @ApiModelProperty("渠道归属")
    private Integer channelOwnership;

    @ApiModelProperty("创建人账号")
    private String createdBy;

    @ApiModelProperty("最后修改人账号")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后修改时间")
    private Date updatedTime;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;
}
