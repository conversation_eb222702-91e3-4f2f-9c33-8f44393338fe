package cn.aliyun.ryytn.common.utils.context;

import java.util.Map;

import cn.aliyun.ryytn.common.entity.Session;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 上下文工具
 * <AUTHOR>
 * @date 2023年6月26日 上午11:13:22
 */
@Slf4j
public class ServiceContextUtils
{
    /**
     * 当前线程上下文
     */
    private static ThreadLocal<Map<String, Object>> contextHolder = new ThreadLocal<Map<String, Object>>();

    private static ThreadLocal<Session> sessionHolder = new ThreadLocal<Session>();

    /**
     *
     * @Description 获取上下文
     * @return Map<String, Object>
     * <AUTHOR>
     * @date 2023年6月26日 上午11:14:06
     */
    public static Map<String, Object> getContext()
    {
        return contextHolder.get();
    }

    /**
     *
     * @Description 设置上下文
     * @param Map<String, Object>
     * <AUTHOR>
     * @date 2023年6月26日 上午11:14:23
     */
    public static void setContext(Map<String, Object> context)
    {
        contextHolder.set(context);
    }

    /**
     *
     * @Description 移除上下文
     * <AUTHOR>
     * @date 2023年6月26日 上午11:15:14
     */
    public static void remove()
    {
        contextHolder.remove();
        sessionHolder.remove();
    }

    /**
     *
     * @Description 设置会话
     * @param session
     * <AUTHOR>
     * @date 2023年9月19日 上午11:43:16
     */
    public static void setSession(Session session)
    {
        sessionHolder.set(session);
    }

    /**
     *
     * @Description 获取当前会话
     * @return Session
     * <AUTHOR>
     * @date 2023年9月19日 上午11:24:01
     */
    public static Session currentSession()
    {
        return sessionHolder.get();
    }
}