package cn.aliyun.ryytn.common.utils.string;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.util.Collections;
import java.util.Iterator;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.support.spring.PropertyPreFilters;

/**
 * 字符串工具类, 继承org.apache.commons.lang3.StringUtils类
 *
 * <AUTHOR>
 * @version 2013-05-22
 */
@Slf4j
public class StringUtils extends org.apache.commons.lang3.StringUtils
{
    private static final char SEPARATOR = '_';
    public static final String TRACENAME_SEPARATOR = " - ";
    public static final String COMMA_SEPARATOR = ",";
    public static final String POINT_SEPARATOR = ".";
    private static final String CHARSET_NAME = "UTF-8";
    public static final String EMPTY_JSON = "{}";
    public static final char C_BACKSLASH = '\\';
    public static final char C_DELIM_START = '{';
    public static final char C_DELIM_END = '}';
    public static final String NULL_STR = "null";
    public static final String S_DELIM_START = "{";
    public static final String S_DELIM_END = "}";
    public static final String S_DELIM_START_DASH = "'{'";
    public static final String S_DELIM_END_DASH = "'}'";
    public static final char S_PARENTHESES_LEFT = '(';
    public static final char S_PARENTHESES_RIGHT = ')';
    public static final String STAND_LINE_SEPARATOR = "|";
    public static final String SLASH_SEPARATOR = "/";
    public static final String DATE_SEPARATOR = "-";
    public static final String TO_SEPARATOR = "~";
    public static final String ZERO = "0";
    public static final String MONTH_PREFIX = "m";
    public static final String WEEK_PREFIX = "w";
    public static final String YEAR_PREFIX = "y";
    public static final String WEEK_PREFIX_UPPER = "W";
    public static final String QUARTER_PREFIX_UPPER = "Q";
    public static final String BOTTOM_LINE_SEPARATOR = "_";
    public static final String WEEK_UNIT = "周";
    public static final String MONTH_UNIT = "月";
    public static final String YEAR_UNIT = "年";
    public static final String MONTH_TOTAL_HEAD = "Total";
    public static final String MONTH_TOTAL_HEAD_REGX = "^[y]\\d{4}[m]\\d{2}Total$";
    public static final String SPACE = " ";
    public static final String EQUALS = "=";
    public static final String DOUBLE_DATE_SEPARATOR = "--";

    /**
     * 转换为字节数组
     */
    public static byte[] getBytes(String str)
    {
        if (str != null)
        {
            try
            {
                return str.getBytes(CHARSET_NAME);
            }
            catch (UnsupportedEncodingException e)
            {
                return null;
            }
        }
        else
        {
            return null;
        }
    }

    /**
     * 转换为字节数组
     */
    public static String toString(byte[] bytes)
    {
        try
        {
            return new String(bytes, CHARSET_NAME);
        }
        catch (UnsupportedEncodingException e)
        {
            return EMPTY;
        }
    }

    /**
     * 是否包含字符串
     *
     * @param str
     *            验证字符串
     * @param strs
     *            字符串组
     * @return 包含返回true
     */
    public static boolean inString(String str, String... strs)
    {
        if (str != null)
        {
            for (String s : strs)
            {
                if (str.equals(trim(s)))
                {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 替换掉HTML标签方法
     */
    public static String replaceHtml(String html)
    {
        if (isBlank(html))
        {
            return "";
        }
        String regEx = "<.+?>";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(html);
        String s = m.replaceAll("");
        return s;
    }

    /**
     * 替换为手机识别的HTML，去掉样式及属性，保留回车。
     */
    public static String replaceMobileHtml(String html)
    {
        if (html == null)
        {
            return "";
        }
        return html.replaceAll("<([a-z]+?)\\s+?.*?>", "<$1>");
    }

    /**
     * 缩略字符串（不区分中英文字符）
     *
     * @param str
     *            目标字符串
     * @param length
     *            截取长度
     */
    public static String abbr(String str, int length)
    {
        if (str == null)
        {
            return "";
        }
        try
        {
            StringBuilder sb = new StringBuilder();
            int currentLength = 0;
            for (char c : replaceHtml(StringEscapeUtils.unescapeHtml4(str)).toCharArray())
            {
                currentLength += String.valueOf(c).getBytes("GBK").length;
                if (currentLength <= length - 3)
                {
                    sb.append(c);
                }
                else
                {
                    sb.append("...");
                    break;
                }
            }
            return sb.toString();
        }
        catch (UnsupportedEncodingException e)
        {
            log.warn("StringUtils.abbr error:{}", Throwables.getStackTraceAsString(e));
        }
        return "";
    }

    /**
     * 转换为Double类型
     */
    public static Double toDouble(Object val)
    {
        if (val == null)
        {
            return 0D;
        }
        try
        {
            return Double.valueOf(trim(val.toString()));
        }
        catch (Exception e)
        {
            return 0D;
        }
    }

    /**
     * 转换为Float类型
     */
    public static Float toFloat(Object val)
    {
        return toDouble(val).floatValue();
    }

    /**
     * 转换为Long类型
     */
    public static Long toLong(Object val)
    {
        return toDouble(val).longValue();
    }

    /**
     * 转换为Integer类型
     */
    public static Integer toInteger(Object val)
    {
        return toLong(val).intValue();
    }

    /**
     * 驼峰命名法工具
     *
     * @return toCamelCase(" hello_world ") == "helloWorld"
     *         toCapitalizeCamelCase("hello_world") == "HelloWorld"
     *         toUnderScoreCase("helloWorld") = "hello_world"
     */
    public static String toCamelCase(String s)
    {
        if (s == null)
        {
            return null;
        }

        s = s.toLowerCase();

        StringBuilder sb = new StringBuilder(s.length());
        boolean upperCase = false;
        for (int i = 0; i < s.length(); i++)
        {
            char c = s.charAt(i);

            if (c == SEPARATOR)
            {
                upperCase = true;
            }
            else if (upperCase)
            {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            }
            else
            {
                sb.append(c);
            }
        }

        return sb.toString();
    }

    /**
     * 驼峰命名法工具
     *
     * @return toCamelCase(" hello_world ") == "helloWorld"
     *         toCapitalizeCamelCase("hello_world") == "HelloWorld"
     *         toUnderScoreCase("helloWorld") = "hello_world"
     */
    public static String toCapitalizeCamelCase(String s)
    {
        if (s == null)
        {
            return null;
        }
        s = toCamelCase(s);
        return s.substring(0, 1).toUpperCase() + s.substring(1);
    }

    /**
     * 驼峰命名法工具
     *
     * @return toCamelCase(" hello_world ") == "helloWorld"
     *         toCapitalizeCamelCase("hello_world") == "HelloWorld"
     *         toUnderScoreCase("helloWorld") = "hello_world"
     */
    public static String toUnderScoreCase(String s)
    {
        if (s == null)
        {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        boolean upperCase = false;
        for (int i = 0; i < s.length(); i++)
        {
            char c = s.charAt(i);

            boolean nextUpperCase = true;

            if (i < (s.length() - 1))
            {
                nextUpperCase = Character.isUpperCase(s.charAt(i + 1));
            }

            if ((i > 0) && Character.isUpperCase(c))
            {
                if (!upperCase || !nextUpperCase)
                {
                    sb.append(SEPARATOR);
                }
                upperCase = true;
            }
            else
            {
                upperCase = false;
            }

            sb.append(Character.toLowerCase(c));
        }

        return sb.toString();
    }

    /**
     * 如果不为空，则设置值
     */
    public static void setValueIfNotBlank(String target, String source)
    {
        if (isNotBlank(source))
        {
            target = source;
        }
    }

    /**
     * 转换为JS获取对象值，生成三目运算返回结果
     *
     * @param objectString
     *            对象串 例如：row.user.id
     *            返回：!row?'':!row.user?'':!row.user.id?'':row.user.id
     */
    public static String jsGetVal(String objectString)
    {
        StringBuilder result = new StringBuilder();
        StringBuilder val = new StringBuilder();
        String[] vals = split(objectString, ".");
        for (int i = 0; i < vals.length; i++)
        {
            val.append("." + vals[i]);
            result.append("!" + (val.substring(1)) + "?'':");
        }
        result.append(val.substring(1));
        return result.toString();
    }

    public static String mySqlEncoder(String source)
    {
        if (source != null)
        {
            Map<String, String> referencesMap = Collections.singletonMap("_", "\\_");
            StringBuffer sbuffer = new StringBuffer(source.length());

            for (int i = 0; i < source.length(); i++)
            {
                String c = source.substring(i, i + 1);

                // System.out.println("c=" + c);
                // System.out.println(referencesMap.get(c));

                if (referencesMap.get(c) != null)
                {
                    sbuffer.append(referencesMap.get(c));
                }
                else
                {
                    sbuffer.append(c);
                }
            }
            return sbuffer.toString();
        }
        return source;
    }

    public static String getValue(String str)
    {
        if (null == str || NULL_STR.equals(str))
        {
            return StringUtils.EMPTY;
        }

        return str.trim();
    }

    public static String getValue(String str, int limit)
    {
        if (null == str || NULL_STR.equals(str))
        {
            return StringUtils.EMPTY;
        }
        String result = str.trim();
        if (result.length() > limit)
        {
            return result.substring(0, limit);
        }
        else
        {
            return result;
        }

    }

    public static String getDefaultValueIfNull(String str, String defValue)
    {
        if (null == str || NULL_STR.equals(str) || StringUtils.EMPTY.equals(str))
        {
            return defValue;
        }

        return str.trim();
    }

    public static String getDefaultValueIfNull(Object str, String defValue)
    {
        if (null == str)
        {
            return defValue;
        }

        return str.toString().trim();
    }

    public static String getValue(Object object)
    {
        if (null == object)
        {
            return StringUtils.EMPTY;
        }

        return getValue(object.toString());
    }

    public static String getValue(Object object, int limit)
    {
        if (null == object)
        {
            return StringUtils.EMPTY;
        }

        return getValue(object.toString(), limit);
    }

    public static String messageFormat(String message, Object[] args)
    {
        if (StringUtils.isNotEmpty(message) && ArrayUtils.isNotEmpty(args))
        {
            if (message.startsWith(S_DELIM_START) && message.endsWith(S_DELIM_END))
            {
                StringBuilder sb = new StringBuilder();
                sb.append(S_DELIM_START_DASH).append(message.substring(1, message.length() - 1)).append(S_DELIM_END_DASH);
                MessageFormat mf = new MessageFormat(sb.toString());
                message = mf.format(args);
            }
            else
            {
                MessageFormat mf = new MessageFormat(message);
                message = mf.format(args);
            }
        }

        return message;
    }

    public static int strToNum(String s, int defValue)
    {
        int value = defValue;

        try
        {
            value = Integer.parseInt(s);
        }
        catch (Exception e)
        {
            value = defValue;
        }

        return value;
    }

    public static String getJsonString(Object obj, String[] excludeProperties)
    {
        String jsonStr = null;

        if (null == obj)
        {
            return jsonStr;
        }

        if (null != excludeProperties && excludeProperties.length != 0)
        {
            PropertyPreFilters filters = new PropertyPreFilters();
            PropertyPreFilters.MySimplePropertyPreFilter excludefilter = filters.addFilter();
            excludefilter.addExcludes(excludeProperties);
            jsonStr = JSONObject.toJSONString(obj, excludefilter);
        }
        else
        {
            jsonStr = JSONObject.toJSONString(obj);
        }

        return jsonStr;
    }

    /**
     *
     * @Description 生成固定长度的字母+数字随机码
     * @param length 生成随机码长度
     * @return String
     * <AUTHOR>
     * @date 2021年5月17日 上午10:20:53
     * @version V200R005C02L10103B090
     */
    public static String getRandomCode(int length)
    {
        // 最终生成的随机码
        StringBuffer randomBuffer = new StringBuffer();
        Random random = new Random();
        for (int i = 0; i < length; i++)
        {
            // 随机生成0或1，用来确定是当前使用数字还是字母 (0则输出数字，1则输出字母)
            int charOrNum = random.nextInt(2);
            if (charOrNum == 1)
            {
                // 随机生成0或1，用来判断是大写字母还是小写字母 (0则输出小写字母，1则输出大写字母)
                int temp = random.nextInt(2) == 1 ? 65 : 97;
                randomBuffer.append((char) (random.nextInt(26) + temp));
            }
            else
            {
                // 生成随机数字
                randomBuffer.append(random.nextInt(10));
            }
        }
        String randomCode = new String(randomBuffer);
        return randomCode;
    }

    /**
     *
     * @Description 输入流转String字符串
     * @param inputStream
     * @return String
     * <AUTHOR>
     * @date 2022年6月21日 下午6:29:13
     */
    public static String inputStreamToString(InputStream inputStream)
    {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        String result = null;
        try
        {
            byte[] b = new byte[10240];
            int n;
            while ((n = inputStream.read(b)) != -1)
            {
                outputStream.write(b, 0, n);
            }
            result = outputStream.toString();
        }
        catch (Exception e)
        {
        }
        finally
        {
            try
            {
                inputStream.close();
                outputStream.close();
            }
            catch (Exception e)
            {
            }
        }
        return result;
    }

    public static String format(final String strPattern, final Object... argArray)
    {
        if (StringUtils.isEmpty(strPattern) || ArrayUtils.isEmpty(argArray))
        {
            return strPattern;
        }
        final int strPatternLength = strPattern.length();

        // 初始化定义好的长度以获得更好的性能
        StringBuilder sbuf = new StringBuilder(strPatternLength + 50);

        int handledPosition = 0;
        int delimIndex;// 占位符所在位置
        for (int argIndex = 0; argIndex < argArray.length; argIndex++)
        {
            delimIndex = strPattern.indexOf(EMPTY_JSON, handledPosition);
            if (delimIndex == -1)
            {
                if (handledPosition == 0)
                {
                    return strPattern;
                }
                else
                { // 字符串模板剩余部分不再包含占位符，加入剩余部分后返回结果
                    sbuf.append(strPattern, handledPosition, strPatternLength);
                    return sbuf.toString();
                }
            }
            else
            {
                if (delimIndex > 0 && strPattern.charAt(delimIndex - 1) == C_BACKSLASH)
                {
                    if (delimIndex > 1 && strPattern.charAt(delimIndex - 2) == C_BACKSLASH)
                    {
                        // 转义符之前还有一个转义符，占位符依旧有效
                        sbuf.append(strPattern, handledPosition, delimIndex - 1);
                        sbuf.append(utf8Str(argArray[argIndex]));
                        handledPosition = delimIndex + 2;
                    }
                    else
                    {
                        // 占位符被转义
                        argIndex--;
                        sbuf.append(strPattern, handledPosition, delimIndex - 1);
                        sbuf.append(C_DELIM_START);
                        handledPosition = delimIndex + 1;
                    }
                }
                else
                {
                    // 正常占位符
                    sbuf.append(strPattern, handledPosition, delimIndex);
                    sbuf.append(utf8Str(argArray[argIndex]));
                    handledPosition = delimIndex + 2;
                }
            }
        }
        // 加入最后一个占位符后所有的字符
        sbuf.append(strPattern, handledPosition, strPattern.length());

        return sbuf.toString();
    }

    /**
     * 将对象转为字符串<br>
     * 1、Byte数组和ByteBuffer会被转换为对应字符串的数组 2、对象数组会调用Arrays.toString方法
     *
     * @param obj 对象
     * @return 字符串
     */
    public static String utf8Str(Object obj)
    {
        return str(obj, CharsetKit.CHARSET_UTF_8);
    }

    /**
     * 将对象转为字符串<br>
     * 1、Byte数组和ByteBuffer会被转换为对应字符串的数组 2、对象数组会调用Arrays.toString方法
     *
     * @param obj 对象
     * @param charset 字符集
     * @return 字符串
     */
    public static String str(Object obj, Charset charset)
    {
        if (null == obj)
        {
            return null;
        }

        if (obj instanceof String)
        {
            return (String) obj;
        }
        else if (obj instanceof byte[] || obj instanceof Byte[])
        {
            if (obj instanceof byte[])
            {
                return str((byte[]) obj, charset);
            }
            else
            {
                Byte[] bytes = (Byte[]) obj;
                int length = bytes.length;
                byte[] dest = new byte[length];
                for (int i = 0; i < length; i++)
                {
                    dest[i] = bytes[i];
                }
                return str(dest, charset);
            }
        }
        else if (obj instanceof ByteBuffer)
        {
            return str((ByteBuffer) obj, charset);
        }
        return obj.toString();
    }

    /**
     *
     * @Description 格式化html
     * @param sourceHtml
     * @return String  destHtml
     * <AUTHOR>
     * @date 2022年7月25日 下午5:43:01
     */
    public static String htmlFormat(String sourceHtml)
    {
        if (isEmpty(sourceHtml))
        {
            return "";
        }
        Document document = Jsoup.parse(sourceHtml);
        document.outputSettings().syntax(document.outputSettings().syntax().xml);
        Elements elements = document.getAllElements();
        Iterator<Element> iterator = elements.iterator();
        while (iterator.hasNext())
        {
            Element element = iterator.next();
            if (null != element.ownText() && !"".equals(element.ownText()))
            {
                String ownText = element.ownText();
                element.html(element.children().toString());
                element.prepend("<![CDATA[" + ownText + "]]>");
            }
        }
        String destHtml = document.body().toString().replace("<body>", "");
        return StringEscapeUtils.unescapeHtml4(destHtml.substring(0, destHtml.lastIndexOf("</body>")));
    }

    /**
     *
     * @Description 字符串脱敏
     * @param fullStr 原始字符串
     * @return String
     * <AUTHOR>
     * @date 2022年8月26日 下午5:51:23
     */
    public static String desensitise(String fullStr)
    {
        // 保留字符数
        int num = 6;
        int left = num / 2;
        int right = num / 2;
        // 脱敏字符数长度固定，不管原字符多长，除保留字符外，中间替换为6个*
        String desensitiseStr = "******";
        if (fullStr == null)
        {
            return fullStr;
        }
        if (fullStr.length() <= num)
        {
            return desensitiseStr;
        }

        StringBuilder result = new StringBuilder();
        result.append(fullStr.substring(0, left)).append(desensitiseStr).append(fullStr.substring(fullStr.length() - right, fullStr.length()));

        return result.toString();
    }

    /**
     *
     * @Description 字符串脱敏（保留原始长度）
     * @param fullStr 原始字符串
     * @return String
     * <AUTHOR>
     * @date 2022年8月26日 下午5:51:23
     */
    @Deprecated
    public static String desensitiseLength(String fullStr)
    {
        int num = 6;
        if (fullStr == null)
        {
            return fullStr;
        }
        if (fullStr.length() <= num)
        {
            return "******";
        }

        int mid = fullStr.length() / 2;

        StringBuilder stringBuilder = new StringBuilder(fullStr);

        // 计数器
        int c = 0;

        int left = mid;
        int right = mid - 1;
        char tm = '*';

        while (c < num)
        {

            // 记住开始计数器
            int ct = c;
            // 处理前后两个字符
            if (++right < fullStr.length())
            {
                stringBuilder.setCharAt(right, tm);
                c++;
            }

            if (c >= num)
            {
                break;
            }

            if (--left >= 0)
            {
                stringBuilder.setCharAt(left, tm);
                c++;
            }

            // 如果c不变则也跳出循环
            if (ct == c)
            {
                break;
            }
        }

        return stringBuilder.toString();
    }
}
