package cn.aliyun.ryytn.common.mq.impl;

import java.time.Duration;
import java.util.Objects;
import java.util.Observer;

import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.stream.Consumer;
import org.springframework.data.redis.connection.stream.ObjectRecord;
import org.springframework.data.redis.connection.stream.ReadOffset;
import org.springframework.data.redis.connection.stream.StreamOffset;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.data.redis.stream.StreamMessageListenerContainer;

import com.alibaba.fastjson.JSON;

import cn.aliyun.ryytn.common.mq.MqFactory;
import cn.aliyun.ryytn.common.mq.MqRecord;
import cn.aliyun.ryytn.common.mq.api.ConsumerService;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description Redis消费者实现类，序列化反序列化有问题未解决，暂时使用ObjectRecord<String,String>
 * 需要Redis版本5.X以上
 * <AUTHOR>
 * @date 2023/10/12 14:42
 */
@Slf4j
public class RedisStreamConsumerServiceImpl extends ConsumerService implements StreamListener<String, ObjectRecord<String, String>>
{
    public RedisStreamConsumerServiceImpl(String topic, String group)
    {
        super(topic, group);

        // 创建主题和用户组
        RedisUtils redisUtils = SpringUtil.getBean(RedisUtils.class);
        redisUtils.xAddGroup(topic, group);

        //构建StreamMessageListenerContainerOptions
        StreamMessageListenerContainer.StreamMessageListenerContainerOptions<String, ObjectRecord<String, String>> options =
            StreamMessageListenerContainer.StreamMessageListenerContainerOptions
                .builder()
                //超时时间
                .pollTimeout(Duration.ofSeconds(2))
                .batchSize(10)
                .targetType(String.class)
                //执行时用的线程池
                .executor(MqFactory.listenerThreadPool)
                //还可以设置序列化方式，这里不做设置，使用默认的方式
                .build();

        //创建StreamMessageListenerContainer
        RedisConnectionFactory redisConnectionFactory = SpringUtil.getBean(RedisConnectionFactory.class);
        StreamMessageListenerContainer<String, ObjectRecord<String, String>> container = StreamMessageListenerContainer
            .create(redisConnectionFactory, options);

        //指定消费最新的消息
        StreamOffset<String> offset = StreamOffset.create(topic, ReadOffset.lastConsumed());

        //创建消费者 指定消费者组和消费者名字（注意，这里使用到用户组时，发送消息时必须有用户组，不然会报错，消息消费不成功）
        Consumer consumer = Consumer.from(group, "Consumer-1");

        StreamMessageListenerContainer.StreamReadRequest<String> streamReadRequest = StreamMessageListenerContainer.StreamReadRequest.builder(offset)
//            .errorHandler((error) -> log.error(error.getMessage()))
            .cancelOnError(e -> false)
            .consumer(consumer)
            //关闭自动ack确认
            .autoAcknowledge(false)
            .build();
        //指定消费者对象
        container.register(streamReadRequest, this);
        container.start();
    }

    /**
     *
     * @Description 消费消息
     * @param message
     * <AUTHOR>
     * @date 2023年10月13日 11:04
     */
    @Override
    public void onMessage(ObjectRecord<String, String> message)
    {
        if (Objects.isNull(message))
        {
            return;
        }

        MqRecord mqRecord = new MqRecord();
        mqRecord.setId(message.getId().getValue());
        mqRecord.setTopic(this.topic);
        mqRecord.setGroup(this.group);
        mqRecord.setValue(JSON.parseObject(message.getValue(), String.class));

        setChanged();
        MqFactory.executorThreadPool.execute(new Runnable()
        {
            @Override
            public void run()
            {
                notifyObservers(mqRecord);
            }
        });
    }

    /**
     *
     * @Description 添加观察者
     * @param observer
     * <AUTHOR>
     * @date 2023年10月12日 14:48
     */
    @Override
    public void addObservers(Observer observer)
    {
        this.addObserver(observer);
    }

    /**
     *
     * @Description 消息回执
     * @param id
     * <AUTHOR>
     * @date 2023年10月13日 16:46
     */
    @Override
    public void ack(MqRecord mqRecord)
    {
        // 通过RedisTemplate手动确认消息，确认之后消息会从队列中消失，如果不确认，可能存在重复消费
        RedisUtils redisUtils = SpringUtil.getBean(RedisUtils.class);
        redisUtils.acknowledge(mqRecord.getTopic(), mqRecord.getGroup(), mqRecord.getId());
    }
}
