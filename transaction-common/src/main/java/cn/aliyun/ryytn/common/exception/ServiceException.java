package cn.aliyun.ryytn.common.exception;

import cn.aliyun.ryytn.common.utils.resource.I18nUtils;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * @Description 自定义业务异常类
 * <AUTHOR>
 * @date 2023年9月19日 下午2:08:21
 */
@Getter
@Setter
public class ServiceException extends BaseException
{
    private static final long serialVersionUID = -5725055632343472585L;

    public ServiceException()
    {

    }

    public ServiceException(Throwable e)
    {
        super(e);
    }

    public ServiceException(Integer errorCode)
    {
        super(errorCode);
        this.errorCode = errorCode;
    }

    public ServiceException(String errorMessage)
    {
        this.errorMessage = errorMessage;
    }

    public ServiceException(Integer errorCode, String... args)
    {
        this.errorCode = errorCode;
        this.args = args;
    }

    public ServiceException(Integer errorCode, Throwable e)
    {
        super(e);
        this.errorCode = errorCode;
        String errorMessage = I18nUtils.getValue(errorCode);
        if ((e instanceof BaseException))
        {
            this.errorMessage = ((BaseException) e).getErrorMessage();
        }
        else
        {
            StackTraceElement[] elements = e.getStackTrace();
            StringBuilder sbf = new StringBuilder(errorMessage);
            for (int i = 0; i < elements.length; i++)
            {
                sbf.append(elements[i].toString());
                sbf.append("\n");
            }
            sbf.append(e.toString());
            this.errorMessage = sbf.toString();
        }
    }

    public ServiceException(Integer errorCode, Throwable e, String... args)
    {
        super(e);
        this.errorCode = errorCode;
        String errorMessage = I18nUtils.getValue(errorCode, args);
        StringBuilder sb = new StringBuilder();
        sb.append(errorMessage);
        sb.append("\n");
        if ((e instanceof BaseException))
        {
            sb.append(((BaseException) e).getErrorMessage());
        }
        else
        {
            StackTraceElement[] elements = e.getStackTrace();
            for (int i = 0; i < elements.length; i++)
            {
                sb.append(elements[i].toString());
                sb.append("\n");
            }
            sb.append(e.toString());
        }
        this.errorMessage = sb.toString();
    }

    public void setErrorCode(Integer errorCode)
    {
        this.errorCode = errorCode;
    }

    public Integer getErrorCode()
    {
        return this.errorCode;
    }

    public String getErrorMessage()
    {
        return this.errorMessage;
    }

    public void setErrorMessage(String key, String[] args)
    {
        this.errorMessage = key;
        this.args = args;
    }

    public String toString()
    {
        return "ServiceException [errorCode=" + this.errorCode + ", errorMessage=" + this.errorMessage + "]";
    }
}
