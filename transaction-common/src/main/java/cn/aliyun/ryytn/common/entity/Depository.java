package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 仓库
 * <AUTHOR>
 * @date 2023年10月10日 10:10
 */
@Setter
@Getter
@ToString
@ApiModel("仓库")
public class Depository implements Serializable
{
    private static final long serialVersionUID = 8746169059055589512L;
    /**
     * 仓库编号
     */
    @ApiModelProperty("仓库编号")
    private String id;

    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String name;
    /**
     * 仓库类型编码
     */
    @ApiModelProperty("仓库类型编码")
    private String typeCode1;

    /**
     * 仓库类型名称
     */
    @ApiModelProperty("仓库类型名称")
    private String typeName1;


}
