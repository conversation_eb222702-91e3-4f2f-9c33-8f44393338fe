package cn.aliyun.ryytn.common.entity.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 区域信息
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("区域信息")
public class MasterRegion implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 编码即ID
     */
    @ApiModelProperty("编码即ID")
    private String id;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updatedTime;

    /**
     * 状态 1-生效 2-失效
     */
    @ApiModelProperty("状态 1-生效 2-失效")
    private Integer status;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 父级ID
     */
    @ApiModelProperty("父级ID")
    private String parentId;
}
