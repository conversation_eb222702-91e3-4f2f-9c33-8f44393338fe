package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.util.CollectionUtils;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 账号对象
 * <AUTHOR>
 * @date 2023/9/28 11:36
 */
@Setter
@Getter
@ToString
@ApiModel("账号")
public class Account implements Serializable
{
    private static final long serialVersionUID = 6425967296707041093L;
    /**
     * 编号
     */
    @ApiModelProperty("账号编号")
    private String id;

    /**
     * 账号名称
     */
    @ApiModelProperty("账号名称")
    private String name;

    /**
     * 账号昵称
     */
    @ApiModelProperty("账号昵称")
    private String nickName;

    /**
     * 工号
     */
    @ApiModelProperty("工号")
    private String workCode;

    /**
     * 登录账号，本地账号必填，OA账号对应loginId，可能为空
     */
    @ApiModelProperty("登录账号")
    private String loginId;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;

    /**
     * OA人员编号
     */
    @ApiModelProperty("OA人员编号")
    private String oaId;

    /**
     * 状态，1：正常，2：冻结
     */
    @ApiModelProperty("状态，1：正常，2：冻结")
    private Integer status;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 数据类型，1:表示初始化数据不允许删除；2:表示管理端创建数据；3:OA系统同步，默认为2
     */
    private Integer dataType;

    /**
     * 角色编号列表
     */
    private List<String> roleIdList;

    private String roleIds;

    /**
     * 角色名称，多个角色英文逗号分隔
     */
    @ApiModelProperty("角色名称")
    private String roleNames;

    /**
     * 部门编号
     */
    private String departmentId;

    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String departmentName;

    /**
     * 岗位编号
     */
    private String jobTitleId;

    /**
     * 岗位名称
     */
    @ApiModelProperty("岗位名称")
    private String jobTitleName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date createdTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date updatedTime;

    /**
     * 账号被授权的菜单树，前端根据此字段渲染菜单
     */
    @ApiModelProperty("账号被授权的菜单树")
    private List<Page> pageTree;

    /**
     * 账号被授权的菜单列表（平铺），后端做功能权限校验，需要从此列表中过滤出所有的permission集合进行判断
     */
    @ApiModelProperty("账号被授权的菜单列表")
    private List<Page> pageList;

    /**
     * 账号被授权的按钮列表，考虑到前端可能需要此对象中的字段判断按钮是否显示，使用对象列表，而不是编号列表
     * 后端做功能权限校验，也需要从此列表中过滤出所有的permission集合进行判断
     */
    @ApiModelProperty("账号被授权的按钮列表")
    private List<Button> buttonList;

    /**
     * 账号被授权的渠道编号列表
     */
    @ApiModelProperty("账号被授权的渠道编号列表")
    private List<String> channelIdList;

    /**
     * 账号被授权的产品品类编号列表
     */
    @ApiModelProperty("账号被授权的产品品类编号列表")
    private List<String> categoryIdList;

    /**
     * 账号被授权的工厂编号列表
     */
    @ApiModelProperty("账号被授权的工厂编号列表")
    private List<String> factoryIdList;

    /**
     * 账号被授权的仓库编号列表
     */
    @ApiModelProperty("账号被授权的仓库编号列表")
    private List<String> depositoryIdList;

    /**
     * 是否管理员
     */
    @ApiModelProperty("是否管理员")
    private Boolean isAdmin;

    @ApiModelProperty("新密码")
    private String newPassword;

    /**
     *
     * @Description 是否管理员
     * @return Boolean
     * <AUTHOR>
     * @date 2023年11月23日 10:11
     */
    @JsonIgnore
    public Boolean isAdmin()
    {
        if (CollectionUtils.isEmpty(roleIdList))
        {
            return false;
        }
        return roleIdList.contains(CommonConstants.SYSADMIN_ROLE_ID);
    }
}
