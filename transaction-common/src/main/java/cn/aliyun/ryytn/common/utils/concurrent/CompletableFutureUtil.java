package cn.aliyun.ryytn.common.utils.concurrent;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 多线程工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class CompletableFutureUtil
{
    /**
     * 传入多个线程,返回最终合并结果
     *
     * @param futures
     * @param <T>
     * @return
     */
    public static <T> CompletableFuture<List<T>> sequence(List<CompletableFuture<T>> futures)
    {
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(
            futures.toArray(
                new CompletableFuture[futures.size()]));
        return allDoneFuture.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.<T>toList()));
    }

    public static <T> CompletableFuture<List<T>> sequence(Stream<CompletableFuture<T>> futures)
    {
        List<CompletableFuture<T>> futureList = futures.filter(f -> f != null).collect(Collectors.toList());
        return sequence(futureList);
    }

    /**
     * 传入T,进行多线程操作 并返回结果R
     *
     * @param list     需要多线程执行的对象list
     * @param executor 线程池
     * @param function 耗时操作
     * @param <T>      多线程入参
     * @param <R>      多线程出参
     * @return 出参集合
     */
    public static <T, R> List<R> getResultApplyCompletableFuture(List<T> list, Executor executor, Function<T, R> function)
    {
        try
        {
            Stream<CompletableFuture<R>> futureStream = list.stream().map(t -> CompletableFuture.supplyAsync(() -> function.apply(t), executor));
            return sequence(futureStream).get();
        }
        catch (Exception e)
        {
            log.warn("getResultApplyCompletableFuture error:{}", Throwables.getStackTraceAsString(e));
        }
        return new ArrayList<>();
    }

    /**
     *
     * @param t 入参
     * @param executor 线程池
     * @param function 耗时操作
     * @param <T> 传入类型
     * @param <R> 返回类型
     * @return
     */
    public static <T, R> CompletableFuture<R> getFuture(T t, Executor executor, Function<T, R> function)
    {
        return CompletableFuture.supplyAsync(() -> function.apply(t), executor);
    }
}
