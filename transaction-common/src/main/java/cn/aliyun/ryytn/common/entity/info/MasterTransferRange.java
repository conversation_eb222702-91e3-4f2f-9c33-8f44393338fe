package cn.aliyun.ryytn.common.entity.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 调拨仓辐射
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("调拨范围")
public class MasterTransferRange implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updatedTime;

    /**
     * 状态 1-生效 0-失效
     */
    @ApiModelProperty("状态 1-生效 0-失效")
    private Integer status;

    /**
     * 辐射类型 0-TOB 1-TOC
     */
    @ApiModelProperty("辐射类型 0-TOB 1-TOC")
    private Integer radiationType;

    /**
     * 商品ID
     */
    @ApiModelProperty("商品ID")
    private String skuId;

    /**
     * 省
     */
    @ApiModelProperty("省")
    private String provinceId;

    /**
     * 市
     */
    @ApiModelProperty("市")
    private String cityId;

    /**
     * 仓库
     */
    @ApiModelProperty("仓库")
    private String warehouseId;

    /**
     * 销售部门
     */
    @ApiModelProperty("销售部门")
    private String salesDepartment;

    /**
     * 起始公斤重量（不包含）
     */
    @ApiModelProperty("起始公斤重量（不包含）")
    private BigDecimal startKg;

    /**
     * 结束公斤重量（不包含）
     */
    @ApiModelProperty("结束公斤重量（不包含）")
    private BigDecimal endKg;
}