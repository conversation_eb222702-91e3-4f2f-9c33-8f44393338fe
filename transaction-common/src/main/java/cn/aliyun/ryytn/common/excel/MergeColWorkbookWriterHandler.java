package cn.aliyun.ryytn.common.excel;

import java.util.List;

import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.handler.context.WorkbookWriteHandlerContext;

/**
 * @Description 按行合并列处理类
 * <AUTHOR>
 * @date 2023/10/25 9:49
 */
public class MergeColWorkbookWriterHandler implements WorkbookWriteHandler
{
    /**
     * 合并的行数组
     */
    private List<Integer> mergeRowList;

    /**
     * 合并的列数组
     */
    private List<List<Integer>> mergeColList;

    public MergeColWorkbookWriterHandler(List<Integer> mergeRowList, List<List<Integer>> mergeColList)
    {
        this.mergeRowList = mergeRowList;
        this.mergeColList = mergeColList;
    }

    @Override
    public void afterWorkbookDispose(WorkbookWriteHandlerContext context)
    {
        Sheet sheet = context.getWriteContext().writeSheetHolder().getSheet();

        for (Integer row : mergeRowList)
        {
            for (List<Integer> list : mergeColList)
            {
                CellRangeAddress cellRangeAddress = new CellRangeAddress(row, row, list.get(0), list.get(list.size() - 1));
                sheet.addMergedRegion(cellRangeAddress);
            }
        }
    }
}
