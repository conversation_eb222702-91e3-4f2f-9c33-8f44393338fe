package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * @Description 业务文件
 * <AUTHOR>
 * @date 2023年9月22日 上午9:40:11
 */
@Setter
@Getter
@ToString
public class ServiceFile implements Serializable
{
    private static final long serialVersionUID = 4815081422172232524L;
    /**
     * @Description 文件编号
     * <AUTHOR>
     * @date 2023年9月22日 上午9:40:03
     */
    private String fileId;

    /**
     * @Description 文件类型，0：图片，1：视频，2：音频，3：文本，4：文档，5：压缩文件，6：脚本文件，99：其他
     * <AUTHOR>
     * @date 2023年9月22日 上午9:40:03
     */
    private Integer fileType;

    /**
     * @Description 文件后缀
     * <AUTHOR>
     * @date 2023年9月22日 上午9:40:03
     */
    private String suffix;

    /**
     * @Description 原文件名
     * <AUTHOR>
     * @date 2023年9月22日 上午9:40:03
     */
    private String oFileName;

    /**
     * @Description 创建人
     * <AUTHOR>
     * @date 2023年9月22日 上午9:40:03
     */
    private String createdBy;

    /**
     * @Description 创建时间
     * <AUTHOR>
     * @date 2023年9月22日 上午9:40:03
     */
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date createdTime;

    /**
     * @Description 文件内容
     * <AUTHOR>
     * @date 2023年9月22日 上午9:40:03
     */
    @JsonIgnore
    private byte[] content;
}
