package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * @Description 调度任务
 * <AUTHOR>
 * @date 2023年11月8日 上午11:16:18
 */
@Setter
@Getter
@ToString
public class SchedulerJob implements Serializable
{
    private static final long serialVersionUID = 1830830684322520519L;
    /**
     * @Description 任务编号
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private String jobId;

    /**
     * @Description 任务名称
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private String jobName;

    /**
     * @Description 调度类型，1：定时调度，2：循环调度，3：延迟调度，4：实时调度
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private Integer jobType;

    /**
     * 开始时间，默认当前时间
     */
    private Date startDate = new Date();

    /**
     * 结束时间，默认9999-12-31
     */
    private Date endDate;

    /**
     * @Description 调度配置，根据调度类型赋值，jobType值，1：cron表达式，2：循环间隔时间，3：执行时间yyyy-MM-dd HH:mm:ss，4：空
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private String jobConf;

    /**
     * @Description 实现类名
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private String className;

    /**
     * @Description 调度参数的Json字符串
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private String param;

    /**
     * 关联业务编号
     */
    private String serviceId;

    /**
     * @Description 计划错误策略
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private Integer misfirePolicy;

    /**
     * @Description 是否允许并发，默认不允许
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private Boolean concurrent = false;

    /**
     * @Description 状态，1：正常，2：禁用，3：已完成，默认值：1
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private Integer status;

    /**
     * @Description 描述
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private String description;

    /**
     * @Description 创建人
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private String createdBy;

    /**
     * @Description 创建时间
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private String createdTime;

    /**
     * @Description 修改人
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private String updatedBy;

    /**
     * @Description 修改时间
     * <AUTHOR>
     * @date 2023年11月8日 上午11:16:12
     */
    private String updatedTime;
}