package cn.aliyun.ryytn.common.utils.mybatis;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.google.common.collect.Lists;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.concurrent.ThreadPoolExecutorUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description Mybatis工具类
 * <AUTHOR>
 * @date 2023/12/16 14:33
 */
@Slf4j
@Component
public class MybatisUtils
{
    @Resource(name = "masterSqlSessionFactory")
    private SqlSessionFactory masterSqlSessionFactory;

    @Resource(name = "dataqSqlSessionFactory")
    private SqlSessionFactory dataqSqlSessionFactory;

    private static final int BATCH_SIZE = 1000;

    private static final String LOCAL_DAO_PACKAGE_REGEX = "^(cn\\.aliyun\\.ryytn\\.)[a-z,A-Z,\\.]+(\\.dao)$";

    private static final String DATAQ_DAO_PACKAGE_REGEX = "^(cn\\.aliyun\\.ryytn\\.)[a-z,A-Z,\\.]+(\\.dataqdao)$";

    /**
     * 批量处理修改或者插入
     *
     * @param data     需要被处理的数据
     * @param mapperClass  Mybatis的Mapper类
     * @param function 自定义处理逻辑
     * @return int 影响的总行数
     */
    public <T, U, R> int batchUpdateOrInsert(List<T> data, Class<U> mapperClass, BiFunction<T, U, R> function)
    {
        String packageName = mapperClass.getPackage().getName();
        SqlSessionFactory sqlSessionFactory = null;
        if (packageName.matches(LOCAL_DAO_PACKAGE_REGEX))
        {
            sqlSessionFactory = masterSqlSessionFactory;
        }
        else if (packageName.matches(DATAQ_DAO_PACKAGE_REGEX))
        {
            sqlSessionFactory = dataqSqlSessionFactory;
        }
        else
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }

        int i = 1;
        SqlSession batchSqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
        try
        {
            U mapper = batchSqlSession.getMapper(mapperClass);
            int size = data.size();
            for (T element : data)
            {
                function.apply(element, mapper);
                if ((i % BATCH_SIZE == 0) || i == size)
                {
                    batchSqlSession.flushStatements();
                    log.info("batchUpdateOrInsert commit :{}", i);
                }
                i++;
            }
            // 非事务环境下强制commit，事务情况下该commit相当于无效
            batchSqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
        }
        catch (Exception e)
        {
            batchSqlSession.rollback();
            throw e;
        }
        finally
        {
            batchSqlSession.close();
        }
        return i - 1;
    }


    public <T, U, R> void batchUpdateOrInsertFragment(List<T> data, Class<U> mapperClass, BiFunction<T, U, R> function) throws Exception
    {
        String packageName = mapperClass.getPackage().getName();
        SqlSessionFactory tempSqlSessionFactory = null;
        if (packageName.matches(LOCAL_DAO_PACKAGE_REGEX))
        {
            tempSqlSessionFactory = masterSqlSessionFactory;
        }
        else if (packageName.matches(DATAQ_DAO_PACKAGE_REGEX))
        {
            tempSqlSessionFactory = dataqSqlSessionFactory;
        }
        else
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        SqlSessionFactory sqlSessionFactory = tempSqlSessionFactory;

        // 如果数据量过大，需要分片
        int fragmentSize = BATCH_SIZE;
        int dataSize = data.size();
        if (dataSize < fragmentSize)
        {
            int i = 1;
            SqlSession batchSqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try
            {
                U mapper = batchSqlSession.getMapper(mapperClass);
                int size = data.size();
                for (T element : data)
                {
                    function.apply(element, mapper);
                    if ((i % BATCH_SIZE == 0) || i == size)
                    {
                        batchSqlSession.flushStatements();
                        batchSqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
                        log.info("batchUpdateOrInsert commit :{}", i);
                    }
                    i++;
                }
                // 非事务环境下强制commit，事务情况下该commit相当于无效
                batchSqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
                log.info("batchUpdateOrInsert commit :{}", i);
            }
            catch (Exception e)
            {
                batchSqlSession.rollback();
                throw e;
            }
            finally
            {
                batchSqlSession.close();
            }
        }
        else
        {
            // 单例map获取线程池，不需要shutdown
            ThreadPoolExecutor pool = ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.fragmentThread);
            int threadCount =
                Math.floorMod(dataSize, fragmentSize) == 0 ? Math.floorDiv(dataSize, fragmentSize) : Math.floorDiv(dataSize, fragmentSize) + 1;
            CountDownLatch countDownLatch = new CountDownLatch(threadCount);
            for (Integer count = 0; count < threadCount; count++)
            {
                final int start = count * fragmentSize;
                final int fragmentNum = count;
                List<T> subData = data.stream().skip(start).limit(fragmentSize).collect(Collectors.toList());
                pool.submit(() -> {
                    SqlSession batchSqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
                    try
                    {
                        U mapper = batchSqlSession.getMapper(mapperClass);
                        int size = subData.size();
                        for (T element : subData)
                        {
                            function.apply(element, mapper);
                        }
                        batchSqlSession.flushStatements();
                        // 非事务环境下强制commit，事务情况下该commit相当于无效
                        batchSqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
                        log.info("batchUpdateOrInsert fragement:{} commited", fragmentNum);
                    }
                    catch (Exception e)
                    {
                        batchSqlSession.rollback();
                        log.error("batchUpdateOrInsert has error.", e);
                    }
                    finally
                    {
                        batchSqlSession.close();
                        countDownLatch.countDown();
                        subData.clear();
                        log.info("batchUpdateOrInsert fragement:{} completed", fragmentNum);
                    }
                });
            }
            countDownLatch.await();
        }
    }

    /**
     *
     * @Description 分片修改/插入
     * @param data
     * @param mapperClass
     * @param function
     * @param <T>
     * @param <R>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年04月10日 22:04
     */
    public <T, R> void batchUpdateOrInsertPartition(List<T> data, Function<List<T>, R> function) throws Exception
    {
        ThreadPoolExecutor pool = ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.fragmentThread);
        List<List<T>> partition = Lists.partition(data, BATCH_SIZE);
        List<CompletableFuture<Void>> listFuture = partition.stream().map(item -> CompletableFuture.allOf(
            CompletableFuture.runAsync(() -> function.apply(item), pool))).collect(Collectors.toCollection(
            CopyOnWriteArrayList::new));
        CompletableFuture.allOf(listFuture.toArray(new CompletableFuture[0])).join();
    }
}
