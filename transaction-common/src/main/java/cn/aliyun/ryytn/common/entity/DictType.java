package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * @Description 系统字典
 * <AUTHOR>
 * @date 2023年9月25日 下午2:22:52
 */
@Setter
@Getter
@ToString
public class DictType implements Serializable
{
    private static final long serialVersionUID = -8418423256967188562L;

    /**
     * @Description 字典类型编号
     * <AUTHOR>
     * @date 2023年9月25日 下午8:46:46
     */
    private String dictTypeId;

    /**
     * @Description 字典类型
     * <AUTHOR>
     * @date 2023年9月25日 下午2:22:34
     */
    private String dictType;

    /**
     * @Description 字典名称
     * <AUTHOR>
     * @date 2023年9月25日 下午2:22:34
     */
    private String dictName;

    /**
     * @Description 状态，1：正常，2：停用，默认值：1
     * <AUTHOR>
     * @date 2023年9月25日 下午2:22:34
     */
    private Integer status;

    /**
     * @Description 删除状态，0：未删除，1：已删除，默认0
     * <AUTHOR>
     * @date 2023年9月25日 下午2:22:34
     */
    private Boolean deleteFlag;

    /**
     * @Description 描述
     * <AUTHOR>
     * @date 2023年9月25日 下午2:22:34
     */
    private String description;

    /**
     * @Description 数据类型，0:表示初始化数据不允许删除；1：表示管理端创建数据；默认为1
     * <AUTHOR>
     * @date 2023年9月25日 下午2:22:34
     */
    private Integer dataType;

    /**
     * @Description 创建人
     * <AUTHOR>
     * @date 2023年9月25日 下午8:46:46
     */
    private String createdBy;

    /**
     * @Description 创建时间
     * <AUTHOR>
     * @date 2023年9月25日 下午8:46:46
     */
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date createdTime;

    /**
     * @Description 修改人
     * <AUTHOR>
     * @date 2023年9月25日 下午8:46:46
     */
    private String updatedBy;

    /**
     * @Description 修改时间
     * <AUTHOR>
     * @date 2023年9月25日 下午8:46:46
     */
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date updatedTime;
}
