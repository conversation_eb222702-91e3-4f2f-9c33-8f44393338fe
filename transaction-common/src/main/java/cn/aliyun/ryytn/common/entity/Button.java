package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 按钮对象，申晓新要求，菜单和按钮分两张表设计
 * <AUTHOR>
 * @date 2023/10/8 11:05
 */
@Setter
@Getter
@ToString
public class Button implements Serializable
{
    private static final long serialVersionUID = 9163844806898749737L;
    /**
     * 按钮编号
     */
    private String id;

    /**
     * 按钮名称
     */
    private String name;

    /**
     * 按钮别名
     */
    private String alias;

    /**
     * 按钮权限
     */
    private String permission;

    /**
     * 依赖按钮编号，多个编号英文逗号分隔
     */
    private String dependencyIds;

    /**
     * 页面编号
     */
    private String pageId;

    /**
     * 排序
     */
    private String sortNo;
}
