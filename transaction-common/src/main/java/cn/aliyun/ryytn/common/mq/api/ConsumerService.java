package cn.aliyun.ryytn.common.mq.api;

import java.util.Observable;
import java.util.Observer;

import cn.aliyun.ryytn.common.mq.MqRecord;

/**
 * @Description 消费者抽象类
 * <AUTHOR>
 * @date 2023/10/12 14:41
 */
public abstract class ConsumerService extends Observable
{
    protected String topic;

    protected String group;

    public ConsumerService(String topic, String group)
    {
        this.topic = topic;
        this.group = group;
    }

    /**
     *
     * @Description 添加观察者
     * @param observer
     * <AUTHOR>
     * @date 2023年10月12日 14:47
     */
    public abstract void addObservers(Observer observer);

    /**
     *
     * @Description 消息回执
     * @param mqRecord
     * <AUTHOR>
     * @date 2023年10月13日 16:45
     */
    public abstract void ack(MqRecord mqRecord);
}
