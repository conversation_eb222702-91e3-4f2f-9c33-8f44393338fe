package cn.aliyun.ryytn.common.excel;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description 隐藏属性
 * <AUTHOR>
 * @date 2023/10/27 23:09
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface HideProperty
{
    boolean isHide() default true;
}
