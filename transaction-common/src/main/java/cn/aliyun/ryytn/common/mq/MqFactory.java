package cn.aliyun.ryytn.common.mq;


import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.mq.api.ConsumerService;
import cn.aliyun.ryytn.common.mq.api.ProduceService;
import cn.aliyun.ryytn.common.mq.impl.RedisListConsumerServiceImpl;
import cn.aliyun.ryytn.common.mq.impl.RedisListProduceServiceImpl;
import cn.aliyun.ryytn.common.mq.impl.RedisStreamConsumerServiceImpl;
import cn.aliyun.ryytn.common.mq.impl.RedisStreamProduceServiceImpl;
import cn.aliyun.ryytn.common.utils.spring.SpringUtil;
import cn.aliyun.ryytn.common.utils.string.StringUtils;


/**
 *
 * @Description 消息队列工厂
 * <AUTHOR>
 * @date 2023/10/13 15:22
 */
public class MqFactory
{
    private static String CONSUMER_LISTEN_THREAD_POOL_NAME = "CONSUMER_LISTEN_";

    private static String CONSUMER_EXCUTE_THREAD_POOL_NAME = "CONSUMER_EXCUTE_";

    public static final ExecutorService listenerThreadPool;

    public static final ExecutorService executorThreadPool;

    static
    {
        ThreadPoolTaskExecutor listenerThreadFactory = new ThreadPoolTaskExecutor();
        listenerThreadFactory.setThreadNamePrefix(CONSUMER_LISTEN_THREAD_POOL_NAME);
        listenerThreadPool = Executors.newCachedThreadPool(listenerThreadFactory);
        ThreadPoolTaskExecutor executorThreadFactory = new ThreadPoolTaskExecutor();
        executorThreadFactory.setThreadNamePrefix(CONSUMER_EXCUTE_THREAD_POOL_NAME);
        executorThreadPool = Executors.newCachedThreadPool(executorThreadFactory);
    }

    /**
     *
     * @Description MQ类型枚举
     * <AUTHOR>
     * @date 2023/10/13 16:19
     */
    public static enum MQModel
    {
        REDISSTREAM(1), REDISLIST(2);

        private int type;

        MQModel(int value)
        {
            this.type = value;
        }

        public int getType()
        {
            return type;
        }

    }

    public static String getTopic(String topic)
    {
        Environment env = SpringUtil.getBean(Environment.class);
        String mqType = StringUtils.defaultIfBlank(env.getProperty("mq.type"), "1");

        String result = null;
        if (StringUtils.equals(String.valueOf(MQModel.REDISSTREAM.type), mqType))
        {
            result = StringUtils.format(CommonConstants.REDIS_STREAM_MQ_KEY, topic);
        }
        else if (StringUtils.equals(String.valueOf(MQModel.REDISLIST.type), mqType))
        {
            result = StringUtils.format(CommonConstants.REDIS_LIST_MQ_KEY, topic);
        }
        return result;
    }

    /**
     *
     * @Description 创建消费者
     * @param group
     * @param topic
     * @param mqType
     * @return ConsumerService
     * <AUTHOR>
     * @date 2023年10月12日 15:47
     */
    public static ConsumerService newConsumerService(String topic)
    {
        Environment env = SpringUtil.getBean(Environment.class);
        String mqType = StringUtils.defaultIfBlank(env.getProperty("mq.type"), "1");
        String group = StringUtils.defaultIfBlank(env.getProperty("mq.group.id"), "group-1");

        if (StringUtils.equals(String.valueOf(MQModel.REDISSTREAM.type), mqType))
        {
            return new RedisStreamConsumerServiceImpl(StringUtils.format(CommonConstants.REDIS_STREAM_MQ_KEY, topic), group);
        }
        else if (StringUtils.equals(String.valueOf(MQModel.REDISLIST.type), mqType))
        {
            return new RedisListConsumerServiceImpl(StringUtils.format(CommonConstants.REDIS_LIST_MQ_KEY, topic), group);
        }
        return null;
    }

    /**
     *
     * @Description 创建生产者
     * @param mqType
     * @return ProduceService
     * <AUTHOR>
     * @date 2023年10月12日 15:47
     */
    public static ProduceService newProducerService()
    {
        Environment env = SpringUtil.getBean(Environment.class);
        String mqType = StringUtils.defaultIfBlank(env.getProperty("mq.type"), "1");
        if (StringUtils.equals(String.valueOf(MQModel.REDISSTREAM.type), mqType))
        {
            return new RedisStreamProduceServiceImpl();
        }
        else if (StringUtils.equals(String.valueOf(MQModel.REDISLIST.type), mqType))
        {
            return new RedisListProduceServiceImpl();
        }
        return null;
    }

}
