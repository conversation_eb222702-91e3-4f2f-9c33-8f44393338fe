package cn.aliyun.ryytn.common.entity;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.exception.BaseException;
import cn.aliyun.ryytn.common.utils.resource.I18nUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yomahub.tlog.context.TLogContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Objects;
import java.util.Optional;

/**
 *
 * @Description 响应结果对象
 * <AUTHOR>
 * @date 2023年9月19日 上午10:29:16
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel("统一响应结果")
public class ResultInfo<T> implements Serializable
{
    private static final long serialVersionUID = -2900617815814148227L;

    /**
     * 响应编号
     */
    @ApiModelProperty("响应编号")
    private String rid;

    /**
     * 是否错误
     */
    @ApiModelProperty("是否错误")
    private Boolean isError;

    /**
     * 结果码
     */
    @ApiModelProperty("结果码")
    private Integer code;

    /**
     *  结果描述
     */
    @JsonProperty(value = "Messages")
    @JSONField(name = "Messages")
    @ApiModelProperty("结果描述")
    private String messages;

    /**
     * 结果对象
     */
    @ApiModelProperty("结果对象")
    private T data;

    /**
     * 异常
     */
    @JsonIgnore
    @JSONField(serialize = false)
    private BaseException baseException;

    /**
     * @param code    消息编码
     * @param messages 消息内容
     * @param isError    是否失败 false：成功,true：失败
     * @Title:ResultInfo
     * @Description:自定义构造函数
     */
    public ResultInfo(Integer code, String messages, boolean isError, BaseException baseException)
    {
//        this.rid = SeqUtils.getSequenceUid();
        this.rid = genRandomId();
        this.code = code;
        this.messages = messages;
        this.isError = isError;
        this.setBaseException(baseException);
    }

    public Integer getCode()
    {
        if (this.code == null)
        {
            if (this.baseException != null)
            {
                return this.baseException.getErrorCode();
            }
        }
        return this.code;
    }

    public static <T> ResultInfo<T> success(T data)
    {
        ResultInfo<T> resultInfo = (ResultInfo<T>) success();
        resultInfo.setData(data);
        return resultInfo;
    }

    public static ResultInfo<?> success()
    {
        ResultInfo<?> resultInfo = new ResultInfo();
        resultInfo.setRid(genRandomId());
//        resultInfo.setRid(SeqUtils.getSequenceUid());
        resultInfo.setCode(ErrorCodeConstants.SUCCESS);
        resultInfo.setIsError(false);
        resultInfo.setMessages(I18nUtils.getValue(ErrorCodeConstants.SUCCESS));
        return resultInfo;
    }

    public static ResultInfo<?> fail()
    {
        return fail(ErrorCodeConstants.FAIL_OTHER);
    }

    public static ResultInfo<?> fail(Integer code)
    {
        if (Objects.isNull(code))
        {
            code = ErrorCodeConstants.FAIL_OTHER;
        }
        return fail(code, null, null);
    }

    public static ResultInfo<?> fail(BaseException e)
    {
        Integer code = e.errorCode;
        if (Objects.isNull(code))
        {
            code = ErrorCodeConstants.FAIL_OTHER;
        }
        ResultInfo<?> resultInfo = new ResultInfo();
        resultInfo.setRid(genRandomId());
//        resultInfo.setRid(SeqUtils.getSequenceUid());
        resultInfo.setCode(code);
        resultInfo.setIsError(true);
        if (StringUtils.isEmpty(e.errorMessage))
        {
            resultInfo.setMessages(I18nUtils.getValue(StringUtils.getValue(code), e.args));
        }
        else
        {
            resultInfo.setMessages(e.errorMessage);
        }
        return resultInfo;
    }

    public static ResultInfo<?> fail(Integer code, String... args)
    {
        if (Objects.isNull(code))
        {
            code = ErrorCodeConstants.FAIL_OTHER;
        }
        ResultInfo<?> resultInfo = new ResultInfo();
        resultInfo.setRid(genRandomId());
//        resultInfo.setRid(SeqUtils.getSequenceUid());
        resultInfo.setCode(code);
        resultInfo.setIsError(true);
        resultInfo.setMessages(I18nUtils.getValue(StringUtils.getValue(code), args));
        return resultInfo;
    }

    public static ResultInfo<?> fail(Integer code, String message)
    {
        if (Objects.isNull(code))
        {
            code = ErrorCodeConstants.FAIL_OTHER;
        }
        ResultInfo<?> resultInfo = new ResultInfo();
        resultInfo.setRid(genRandomId());
//        resultInfo.setRid(SeqUtils.getSequenceUid());
        resultInfo.setCode(code);
        resultInfo.setIsError(true);
        resultInfo.setMessages(message);
        return resultInfo;
    }




    public void setData(T data)
    {
        this.data = data;
    }

    public T getData()
    {
        return this.data;
    }

    private static String genRandomId() {
//        return SeqUtils.getSequenceUid() + "_" + TLogContext.getTraceId();
        return Optional.ofNullable(TLogContext.getTraceId()).orElse(SeqUtils.getSequenceUid());
    }
}