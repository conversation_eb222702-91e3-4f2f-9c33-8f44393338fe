package cn.aliyun.ryytn.common.entity.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 运输成本
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("运输成本")
public class MasterTransferCost implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updatedTime;

    /**
     * 状态 1-生效 0-失效
     */
    @ApiModelProperty("状态 1-生效 0-失效")
    private Integer status;

    /**
     * 出发省
     */
    @ApiModelProperty("出发省")
    private String fromProvinceId;

    /**
     * 出发市
     */
    @ApiModelProperty("出发市")
    private String fromCityId;

    /**
     * 到达省
     */
    @ApiModelProperty("到达省")
    private String toProvinceId;

    /**
     * 到达市
     */
    @ApiModelProperty("到达市")
    private String toCityId;

    /**
     * 十三米（元/吨）
     */
    @ApiModelProperty("十三米（元/吨）")
    private BigDecimal cost13m;

    /**
     * 十五米（元/吨）
     */
    @ApiModelProperty("十五米（元/吨）")
    private BigDecimal cost15m;

    /**
     * 铁柜（元/吨）
     */
    @ApiModelProperty("铁柜（元/吨）")
    private BigDecimal costRail;

    /**
     * 汽运时效（天）
     */
    @ApiModelProperty("汽运时效（天）")
    private Integer roadDuration;

    /**
     * 铁运时效（天）
     */
    @ApiModelProperty("铁运时效（天）")
    private Integer railDuration;
}