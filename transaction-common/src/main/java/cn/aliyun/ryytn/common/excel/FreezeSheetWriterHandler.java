package cn.aliyun.ryytn.common.excel;

import org.apache.poi.ss.usermodel.Sheet;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;

/**
 * @Description 冻结单元格处理类
 * <AUTHOR>
 * @date 2023/10/25 9:48
 */
public class FreezeSheetWriterHandler implements SheetWriteHandler
{
    /**
     * 开始冻结的列，不做列冻结传0
     */
    private int beginCol;

    /**
     * 结束冻结的列，不做列冻结传0，只冻结1列与beginCol相同
     */
    private int endCol;

    /**
     * 开始冻结的行，不做行冻结传0
     */
    private int beginRow;

    /**
     * 结束冻结的行，不做行冻结传0，只冻结1行与beginRow相同
     */
    private int endRow;

    public FreezeSheetWriterHandler(int beginCol, int endCol, int beginRow, int endRow)
    {
        this.beginCol = beginCol;
        this.endCol = endCol;
        this.beginRow = beginRow;
        this.endRow = endRow;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder)
    {
        Sheet sheet = writeSheetHolder.getSheet();
        sheet.createFreezePane(beginCol, beginRow, endCol, endRow);
    }
}
