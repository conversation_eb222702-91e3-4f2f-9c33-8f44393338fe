package cn.aliyun.ryytn.common.utils.sap;

import cn.aliyun.ryytn.common.entity.sap.SapCommonRequest;
import cn.aliyun.ryytn.common.entity.sap.SapCtrl;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 调用sap服务
 * 目前oms调用sap服务主要通过http请求调用部署在windows机器上的【RYYTN-SAP-V3.0】服务实现
 * 具体dataId有：【oms-core、ip、ic、sg、ac】
 * ems项目复用oms的调用过程
 *
 * <AUTHOR>
 * @since 2024-03-18 10:27
 */
@Slf4j
@Component
public class SapPushUtil {
    /**
     * 签收单推送SAP
     */
    public static final String GET_SKU_FROM_SAP_FUN_ID = "ZSCMINF001";

    @Value(value = "${sap.url}")
    private String sapUrl;

    /**
     * 执行SAP请求
     *
     * @param funId    方法名
     * @param keyId    业务ID（唯一键）
     * @param dataList 数据对象
     * @return 执行结果
     */
    public <T> String execute(String funId, String keyId, List<T> dataList) {
        SapCommonRequest request = new SapCommonRequest();
        request.setSapCtrl(buildCtrl(funId, keyId));
        request.setDataList(dataList);
        return doExecute(request);
    }

    /**
     * 发起SAP请求
     *
     * @param request 请SAP入参对象
     * @return SAP返回体
     */
    private String doExecute(SapCommonRequest request) {
        // 使用FastJSON进行序列化，现在已经使用@JSONField注解
        String requestBody = JSONObject.toJSONString(request);
        String responseBody = null;

        try (HttpResponse httpResponse = HttpRequest.post(sapUrl)
                .header(Header.CONTENT_TYPE, ContentType.JSON.getValue())
                .body(requestBody)
                .execute()) {
            responseBody = httpResponse.body();
            log.info("httpRequestSap.success requestBody:{}, responseBody:{}", requestBody, responseBody);

            if (StringUtils.isEmpty(responseBody)) {
                throw new ServiceException("SAP返回体为空");
            }
        } catch (Exception e) {
            log.warn("httpRequestSap.fail requestBody:{}, responseBody:{}，ex:{}",
                    requestBody, responseBody, Throwables.getStackTraceAsString(e));
            throw new ServiceException("HTTP请求SAP失败:" + e.getMessage());
        }

        return responseBody;
    }


    /**
     * 组装基础请求参数
     *
     * @param funId 接口名，目前仅【SIGN_ORDER_TO_SAP_FUN_ID】
     * @param keyId 业务参数，数据主键
     * @return sap请求头
     */
    private SapCtrl buildCtrl(String funId, String keyId) {
        SapCtrl ctrl = new SapCtrl();
        ctrl.setFunId(funId);
        ctrl.setInfId(funId);
        ctrl.setKeyId(keyId);
        ctrl.setSysId("SCM");
        ctrl.setUname("SCM平台");
        ctrl.setRevId("SAP");
        ctrl.setDatum(new Date());
        ctrl.setUzEit(new Date());
        ctrl.setMd5("");
        ctrl.setMsAge("");
        ctrl.setMsgTy("");
        return ctrl;
    }

}
