package cn.aliyun.ryytn.common.excel;

import java.io.Serializable;

import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 电子表格任务
 * <AUTHOR>
 * @date 2023/10/19 13:32
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
public class ExcelTask<T> implements Serializable
{
    private static final long serialVersionUID = -8459040237733300717L;
    /**
     * 任务编号
     */
    private String id;

    /**
     * 业务Handler Bean的name
     */
    private String name;

    /**
     * 状态
     */
    private ExcelTaskStatusEnum status;

    /**
     * 失败信息
     */
    private String msg;

    /**
     * 文件编号
     */
    private String fileId;

    /**
     * 结果数据
     */
    private T result;

    public ExcelTask(String name)
    {
        this.id = name + SeqUtils.getSequenceUid();
        this.name = name;
        this.status = ExcelTaskStatusEnum.PROCESSING;
        this.fileId = null;
        this.result = null;
    }

    public enum ExcelTaskStatusEnum
    {
        PROCESSING, SUCCESS, FAILED;
    }
}
