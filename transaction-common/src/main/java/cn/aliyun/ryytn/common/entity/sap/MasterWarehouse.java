package cn.aliyun.ryytn.common.entity.sap;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 主数据-仓库档案
 * @date 2024/10/10 10:00
 */
@Setter
@Getter
@ToString
@ApiModel("主数据-仓库档案")
public class MasterWarehouse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;
    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    private String plantCode;

    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String contactPerson;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String mobile;
    /**
     * 电话号码
     */
    @ApiModelProperty("电话号码")
    private String telephone;

    /**
     * 所在省
     */
    @ApiModelProperty("所在省")
    private String province;

    /**
     * 所在市
     */
    @ApiModelProperty("所在市")
    private String city;

    /**
     * 所在区
     */
    @ApiModelProperty("所在区")
    private String district;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String fullAddress;

    /**
     * 仓库类型-枚举如下：RDC仓、低温仓、工厂仓、奶粉仓、其他仓、虚拟仓、原辅料仓
     */
    @ApiModelProperty("仓库类型")
    private Integer warehouseType;

    /**
     * 创建人(登录账号)
     */
    @ApiModelProperty("创建人(登录账号)")
    private String createdBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updatedTime;

    /**
     * 仓库名称
     */
    @ApiModelProperty("实体仓名称")
    private String phyWarehouseName;
}
