package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 页面配置
 * <AUTHOR>
 * @date 2023年10月10日 17:10
 */
@Setter
@Getter
@ToString
@ApiModel("页面配置")
public class PageConfig implements Serializable
{
    private static final long serialVersionUID = 5612369059093989512L;
    /**
     * 配置编号
     */
    @ApiModelProperty("配置编号")
    private String id;

    /**
     * 页面编号
     */
    @ApiModelProperty("页面编号")
    private String pageId;

    /**
     * 列名称
     */
    @ApiModelProperty("列名称")
    private String rowName;

    /**
     * 列字段名
     */
    @ApiModelProperty("列字段名")
    private String rowField;

    /**
     * 列宽度
     */
    @ApiModelProperty("列宽度")
    private Integer width;

    /**
     * 列顺序
     */
    @ApiModelProperty("列顺序")
    private Long sortNo;

    /**
     * 列冻结/解冻，0：解冻，1：解冻，默认0
     */
    @ApiModelProperty("列冻结/解冻，false：解冻，true：冻结，默认false")
    private Boolean freezeFlag;
    /**
     * 列展示/隐藏，0：展示，1：隐藏，默认0
     */
    @ApiModelProperty("列展示/隐藏，false：展示，true：隐藏，默认false")
    private Boolean showFlag;

    /**
     * 列聚合，0：不聚合，1：聚合，默认0
     */
    @ApiModelProperty("列聚合，false：不聚合，true：聚合，默认false")
    private Boolean gatherFlag;

    /**
     * 创建人(登录账号)
     */
    @ApiModelProperty("创建人(登录账号)")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createdTime;

    /**
     * 修改人(登录账号)
     */
    @ApiModelProperty("修改人(登录账号)")
    private String updatedBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private String updatedTime;


}
