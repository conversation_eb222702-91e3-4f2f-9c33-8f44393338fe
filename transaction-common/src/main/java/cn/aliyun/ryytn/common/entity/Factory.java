package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 工厂
 * <AUTHOR>
 * @date 2023年10月09日 17:10
 */
@Setter
@Getter
@ToString
@ApiModel("工厂")
public class Factory implements Serializable
{
    private static final long serialVersionUID = -8460827690904429827L;
    @ApiModelProperty("工厂编码")
    private String actoryCode;

    @ApiModelProperty("工厂名称")
    private String factoryName;

    @ApiModelProperty("工厂状态，1/0，是否有效")
    private Integer status;

    @ApiModelProperty("是否自有工厂，1//0")
    private Integer isOwn;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("工厂类型编码")
    private String typeCode;

    @ApiModelProperty("工厂类型")
    private String typeName;

    @ApiModelProperty("地址编码")
    private String addrCode;

    @ApiModelProperty("地址")
    private String addrName;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市")
    private String cityName;

    @ApiModelProperty("区编码")
    private String countyCode;

    @ApiModelProperty("区")
    private String countyName;

    @ApiModelProperty("日期分区yyyymmdd")
    private String ds;
}
