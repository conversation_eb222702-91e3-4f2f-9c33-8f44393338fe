package cn.aliyun.ryytn.common.entity.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 生产销售关系实体
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "生产销售关系实体")
public class ProductionSalesRelation {

    @ApiModelProperty("自增主键")
    private String id;

    @ApiModelProperty("销售商品ID")
    private String salesSkuId;

    @ApiModelProperty("生产商品ID")
    private String productionSkuId;

    @ApiModelProperty("转换系数（生产1个单位商品可转换的销售商品数量）")
    private Integer conversionFactor;

    @ApiModelProperty("创建人账号")
    private String createdBy;

    @ApiModelProperty("最后修改人账号")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后修改时间")
    private Date updatedTime;

    @ApiModelProperty("状态标识（1-有效，0-无效）")
    private Integer status;
}
