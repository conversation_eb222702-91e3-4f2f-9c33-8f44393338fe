package cn.aliyun.ryytn.common.entity.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 可用库存实体
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "可用库存实体")
public class AvailableStock {

    @ApiModelProperty("自增主键")
    private String id;

    @ApiModelProperty("工厂名称")
    private String factoryName;

    @ApiModelProperty("商品ID")
    private String skuId;

    @ApiModelProperty("仓库ID")
    private String warehouseId;

    @ApiModelProperty("生产日期")
    private String productionDate;

    @ApiModelProperty("可用库存数量")
    private BigDecimal quantity;

    @ApiModelProperty("库存快照日期")
    private String stockDate;

    @ApiModelProperty("创建人账号")
    private String createdBy;

    @ApiModelProperty("最后修改人账号")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后修改时间")
    private Date updatedTime;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;
}
