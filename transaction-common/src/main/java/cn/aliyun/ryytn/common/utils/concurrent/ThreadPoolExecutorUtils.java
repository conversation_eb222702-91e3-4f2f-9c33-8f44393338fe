package cn.aliyun.ryytn.common.utils.concurrent;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

/**
 *
 * @Description 线程池工具类
 * <AUTHOR>
 * @date 2023/10/12 16:15
 */
public class ThreadPoolExecutorUtils
{
    public enum ThreadPoolNameEnum
    {
        excelThread, fragmentThread,channelDemandReportTemplateThread;
    }

    private static Map<String, ThreadPoolExecutor> threadPoolExecutorMap = new HashMap<String, ThreadPoolExecutor>();

    public static ThreadPoolExecutor newThreadPoolExecutor(ThreadPoolNameEnum threadPoolNameEnum)
    {
        return newThreadPoolExecutor(threadPoolNameEnum, null, null);
    }

    public static ThreadPoolExecutor newThreadPoolExecutor(ThreadPoolNameEnum threadPoolNameEnum, Integer corePoolSize, Integer maxPoolSize)
    {
        String key = threadPoolNameEnum.name();
        if (!threadPoolExecutorMap.containsKey(key))
        {
            synchronized (threadPoolExecutorMap)
            {
                if (!threadPoolExecutorMap.containsKey(key))
                {
//                    int coreSize = Objects.isNull(corePoolSize) ? Runtime.getRuntime().availableProcessors() * 2 + 1 : corePoolSize;
//                    int maxSize = Objects.isNull(maxPoolSize) ? 64 : maxPoolSize;
                    int coreSize = 16;
                    int maxSize = 64;
                    LinkedBlockingQueue<Runnable> queue = new LinkedBlockingQueue<>();
                    ThreadFactory threadFactory = new CustomizableThreadFactory(key);
                    ThreadPoolExecutor.AbortPolicy handler = new ThreadPoolExecutor.AbortPolicy();

                    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(coreSize, maxSize, 60L,
                        TimeUnit.SECONDS, queue, threadFactory, handler);

                    threadPoolExecutorMap.put(key, threadPoolExecutor);
                }
            }
        }

        return threadPoolExecutorMap.get(key);
    }
}
