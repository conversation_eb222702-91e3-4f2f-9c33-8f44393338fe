package cn.aliyun.ryytn.common.entity.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 转移替代关系实体
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "转移替代关系实体")
public class TransferSubstitution {

    @ApiModelProperty("自增主键")
    private String id;

    @ApiModelProperty("原商品ID")
    private String originalSkuId;

    @ApiModelProperty("替代商品ID")
    private String substituteSkuId;

    @ApiModelProperty("创建人账号")
    private String createdBy;

    @ApiModelProperty("最后修改人账号")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后修改时间")
    private Date updatedTime;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;
}
