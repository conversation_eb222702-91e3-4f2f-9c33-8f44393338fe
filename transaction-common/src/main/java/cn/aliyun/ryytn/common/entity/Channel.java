package cn.aliyun.ryytn.common.entity;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道
 * <AUTHOR>
 * @date 2023年10月09日 16:33
 */
@Setter
@Getter
@ToString
@ApiModel("渠道")
public class Channel implements Serializable
{
    private static final long serialVersionUID = 8746169059093924568L;
    /**
     * 渠道编号
     */
    @ApiModelProperty("渠道编号")
    private String id;

    /**
     * 渠道名称
     */
    @ApiModelProperty("渠道名称")
    private String name;

    /**
     * 父渠道编号，根页面为-1
     */
    @ApiModelProperty("父渠道编号")
    private String parentId;

    /**
     * 第几级渠道
     */
    @ApiModelProperty("第几级渠道")
    private Integer level;

    /**
     * 子渠道列表
     */
    @ApiModelProperty("子渠道列表")
    private List<Channel> subList;
}
