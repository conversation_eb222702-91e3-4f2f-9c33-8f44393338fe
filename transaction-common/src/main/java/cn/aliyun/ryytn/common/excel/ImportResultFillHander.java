package cn.aliyun.ryytn.common.excel;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.CollectionUtils;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;

/**
 * @Description 导入结果回填处理
 * <AUTHOR>
 * @date 2024/4/1 17:09
 */
public class ImportResultFillHander implements SheetWriteHandler, RowWriteHandler
{
    /**
     * 错误结果集
     */
    private final Map<Integer, String> failMap;
    /**
     * 标题所在行, 从1开始
     */
    private final Integer titleLineNumber;

    /**
     * 结果列序号
     */
    private int resultColNum;

    /**
     * 默认导入成功的提示
     */
    private static final String SUCCESS_MSG = "导入成功";

    public ImportResultFillHander(List<ExcelData> failList, Integer titleLineNumber)
    {
        if (CollectionUtils.isEmpty(failList))
        {
            this.failMap = Collections.EMPTY_MAP;
        }
        else
        {
            this.failMap = failList.stream().collect(Collectors.toMap(ExcelData::getRowIndex, ExcelData::getResult, (key1, key2) -> key2));
        }
        this.titleLineNumber = titleLineNumber;
    }

    private static void setCellStyle(Cell cell, IndexedColors color)
    {
        Workbook workbook = cell.getSheet().getWorkbook();
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setColor(color.getIndex());
        style.setFont(font);
        cell.setCellStyle(style);
    }

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context)
    {
        SheetWriteHandler.super.afterSheetCreate(context);
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder)
    {
        Sheet cachedSheet = writeSheetHolder.getCachedSheet();
        for (int i = 1; i <= cachedSheet.getLastRowNum() + 1; i++)
        {
            // 空白数据, 不做处理
            if (i < titleLineNumber)
            {
                continue;
            }
            Row row = cachedSheet.getRow(i - 1);
            // 标题行, 创建标题
            if (i == titleLineNumber)
            {
                this.resultColNum = row.getLastCellNum();
                continue;
            }
            // 结果行
            Cell cell = row.createCell(this.resultColNum, CellType.STRING);
            String errMsg = failMap.get(i - 1);
            if (errMsg == null)
            {
                // 数据量较大会抛出异常：The maximum number of Cell Styles was exceeded. You can define up to 64000 style in a .xlsx Workbook
                // 暂时注释，后续修改setCellStyle方法，全局只创建两个CellStyle，其他单元格均复制这两个CellStyle
//                setCellStyle(cell, IndexedColors.GREEN);
                cell.setCellValue(SUCCESS_MSG);
                continue;
            }
            // 数据量较大会抛出异常：The maximum number of Cell Styles was exceeded. You can define up to 64000 style in a .xlsx Workbook
            // 暂时注释，后续修改setCellStyle方法，全局只创建两个CellStyle，其他单元格均复制这两个CellStyle
//            setCellStyle(cell, IndexedColors.RED);
            cell.setCellValue(errMsg);
        }
    }
}
