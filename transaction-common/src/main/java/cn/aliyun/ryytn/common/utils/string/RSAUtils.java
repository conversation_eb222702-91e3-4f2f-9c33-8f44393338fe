package cn.aliyun.ryytn.common.utils.string;


import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;


/**
 * @Description RSA加密工具类
 * <AUTHOR>
 * @date 2023/10/20 9:32
 */
@Slf4j
public class RSAUtils
{
    //私钥
    private static String privateKey =
        "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMERR3LEcIcDEYHmiqrx1+4U9Y2b3F1sNzprMzNWKHz/OG3vvT9sVyvo+JsjqJAt5Q9I9v/4jiKCc8e9uVfUV+2sW1nmhtbJZxxCyhVwrZ4ZtUuAgqXQpgMU6qK0njQd1g6jpJf45pdvs0L8YDB46ptGFlAEHsiJOD7P6y+m0Tw3AgMBAAECgYEAhGMf1h/DdaDyAVwfnAaH5Xazxm2o1trcqA9YJYyf6M5tWF07LOFSiQaQankyHeOKnUeQL4e1lw5S7BU5oXM28s5Xijxocbv3Sz7T0p0ViMj9+7pUp8oWtgzppcqrZFz8oNZZoQvNWysZS7BTJ611vcmbE6jE5NIBihlzIj7AZlkCQQDtwZXL7hZP3ZB8bpnnlpkPQeQrzS5Tsbk2HX4CectOhlMeLDimpMXvCxXJnRenqJTfACyon5odk5deHvBVFgYVAkEAz+HXioCzMBuZpq2YwQNA0WxgKDXflW94pbeidr4cB1rRsVTaCnuzqT+Tm8HZcGnDXmIBCrojTR/XuqOakAI4GwJAfyMGnE9LhReg3Lkz+YAxhtQgVD/a5wxhUwvAR0ijq/DuX3McNBhmc6ZE11P2w0PFn93zA5SdD/XNepomwAkP5QJBALIkRdHhwixKqpwkDU/xmr0cUzaCHCxxzwCr4eRBu8EtuZ5uKT9sjCRQYAzMzKnBaHNcJqprok64J+U/x/b6f9sCQQDTcmB/hvMM0eCfctxAJHeiGJzchAJih0ga4ijcgZzO2UKFGVW7n9IOewt2ckgoXC4PqWY7POsUUlPZE0iBAzof";

    //公钥
    private static String publicKey =
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBEUdyxHCHAxGB5oqq8dfuFPWNm9xdbDc6azMzVih8/zht770/bFcr6PibI6iQLeUPSPb/+I4ignPHvblX1FftrFtZ5obWyWccQsoVcK2eGbVLgIKl0KYDFOqitJ40HdYOo6SX+OaXb7NC/GAweOqbRhZQBB7IiTg+z+svptE8NwIDAQAB";

    /**
     *
     * @Description 私钥解密
     * @param text
     * @return String
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月20日 10:04
     */
    public static String decryptByPrivateKey(String text) throws Exception
    {
        return decryptByPrivateKey(privateKey, text);
    }

//    public static void main(String args[]) throws Exception
//    {
//        String txt = "{\"code\":\"SuiTZM\",\"loginName\":\"c060122\",\"requestSource\":\"oa\",\"ts\":" + System.currentTimeMillis() + "}";
//        String str1 = URLEncoder.encode(RSAUtils.encryptByPublicKey(txt));
//        System.out.println(str1);
//        String str2 = URLDecoder.decode(str1.replace("+", "%2B"), CharsetKit.UTF_8);
//        System.out.println(str2);
//        System.out.println(str2.getBytes().length);
//        String str3 = RSAUtils.decryptByPrivateKey(str2);
//        System.out.println(str3);
//    }

    /**
     *
     * @Description 私钥加密
     * @param text
     * @return String
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月20日 10:00
     */
    public static String encryptByPrivateKey(String text) throws Exception
    {
        return encryptByPrivateKey(privateKey, text);
    }

    /**
     *
     * @Description 公钥解密
     * @param text
     * @return String
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月20日 10:03
     */
    public static String decryptByPublicKey(String text) throws Exception
    {
        return decryptByPublicKey(publicKey, text);
    }

    /**
     *
     * @Description 公钥加密
     * @param text
     * @return String
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月20日 10:03
     */
    public static String encryptByPublicKey(String text) throws Exception
    {
        return encryptByPublicKey(publicKey, text);
    }

    /**
     * 公钥解密
     *
     * @param publicKeyString 公钥
     * @param text 待解密的信息
     * @return 解密后的文本
     */
    private static String decryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 私钥加密
     *
     * @param privateKeyString 私钥
     * @param text 待加密的信息
     * @return 加密后的文本
     */
    private static String encryptByPrivateKey(String privateKeyString, String text) throws Exception
    {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 私钥解密
     *
     * @param privateKeyString 私钥
     * @param text 待解密文本
     * @return 解密后的文本
     */
    private static String decryptByPrivateKey(String privateKeyString, String text) throws Exception
    {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec5 = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec5);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 公钥加密
     *
     * @param publicKeyString 公钥
     * @param text 待加密的文本
     * @return 加密后的文本
     */
    private static String encryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 构建RSA密钥对
     *
     * @return 生成后的公私钥信息
     */
    private static RsaKeyPair generateKeyPair() throws NoSuchAlgorithmException
    {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(1024);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
        String publicKeyString = Base64.encodeBase64String(rsaPublicKey.getEncoded());
        String privateKeyString = Base64.encodeBase64String(rsaPrivateKey.getEncoded());
        return new RsaKeyPair(publicKeyString, privateKeyString);
    }

    /**
     * RSA密钥对对象
     */
    public static class RsaKeyPair
    {
        private final String publicKey;
        private final String privateKey;

        public RsaKeyPair(String publicKey, String privateKey)
        {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey()
        {
            return publicKey;
        }

        public String getPrivateKey()
        {
            return privateKey;
        }
    }


    /**
     * 只需调用一次 生成/打印新的公钥私钥  并测试是否可用
     * 控制台打印结果，解密成功 则将打印的公钥私钥重新赋值给工具类的 privateKey 、 publicKey
     * @throws NoSuchAlgorithmException
     */
    private static void printNewPubKeypriKey()
    {
        //调用 RsaUtils.generateKeyPair() 生成RSA公钥秘钥
        //私钥
        String tmpPriKey = "";
        //公钥
        String tmpPubKey = "";
        try
        {
            RsaKeyPair rsaKeyPair = RSAUtils.generateKeyPair();
            tmpPriKey = rsaKeyPair.getPrivateKey();
            tmpPubKey = rsaKeyPair.getPublicKey();
            log.info("私钥：" + tmpPriKey);
            log.info("公钥：" + tmpPubKey);
        }
        catch (NoSuchAlgorithmException exception)
        {
            log.info("生成秘钥公钥失败");
        }
        //公钥加密、私钥解密
        try
        {
            //注意需要加密的原文长度不要太长 过长的字符串会导致加解密失败
            String txt = "{\"code\":\"SuiTZM\",\"loginName\":\"c060122\",\"requestSource\":\"oa\",\"ts\":" + System.currentTimeMillis() + "}";
            //加密后文本
            log.info("加密前原文：" + txt);
            //公钥加密 ！！！
            String rsaText = URLEncoder.encode(RSAUtils.encryptByPublicKey(tmpPubKey, txt));
            //加密后文本
            log.info("密文：" + rsaText);
            //私钥解密 ！！！
            log.info("解密后原文：" + RSAUtils.decryptByPrivateKey(tmpPriKey, URLDecoder.decode(rsaText.replace("+", "%2B"))));
        }
        catch (BadPaddingException e)
        {
            log.warn("加解密失败:{}", Throwables.getStackTraceAsString(e));
        }
        catch (Exception e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 使用固定的 privateKey 、 publicKey 进行加解密测试
     * 注意 需要加密的原文长度不要太长 过长的字符串会导致加解密失败
     */
    private static void tryEncryptAndDecrypt()
    {
        //公钥加密、私钥解密
        try
        {
            //注意需要加密的原文长度不要太长 过长的字符串会导致加解密失败
            String txt = "{\"code\":\"SuiTZM\",\"loginName\":\"c060122\",\"requestSource\":\"oa\",\"ts\":" + System.currentTimeMillis() + "}";
            //加密后文本
            log.info("加密前原文：" + txt);
            //RsaUtils.publicKey 公钥加密 ！！！
            String rsaText = URLEncoder.encode(RSAUtils.encryptByPublicKey(txt));
            //加密后文本
            log.info("密文：" + rsaText);
            //RsaUtils.privateKey 私钥解密 ！！！
            log.info("解密后原文：" + RSAUtils.decryptByPrivateKey(URLDecoder.decode(rsaText.replace("+", "%2B"), CharsetKit.UTF_8)));
        }
        catch (BadPaddingException e)
        {
            log.warn("加解密失败:{}", Throwables.getStackTraceAsString(e));
        }
        catch (Exception e)
        {
            throw new RuntimeException(e);
        }
    }
}
