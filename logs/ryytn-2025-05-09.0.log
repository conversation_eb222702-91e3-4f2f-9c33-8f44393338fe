2025-05-07 15:49:29.592 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-07 15:49:29.604 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-07 15:49:29.606 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-07 15:49:29.620 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 25816 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-07 15:49:29.620 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-07 15:49:31.112 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-07 15:49:31.115 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-07 15:49:31.141 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces. 
2025-05-07 15:49:31.399 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-07 15:49:31.579 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@6ac4944a , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-07 15:49:31.629 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-07 15:49:31.645 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-07 15:49:31.647 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-07 15:49:31.650 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-07 15:49:31.699 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-07 15:49:31.699 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-07 15:49:31.699 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-07 15:49:31.699 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-07 15:49:31.711 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-07 15:49:31.713 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-07 15:49:31.714 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-07 15:49:32.568 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-07 15:49:32.579 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-07 15:49:32.579 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-07 15:49:32.579 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-07 15:49:32.667 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-07 15:49:32.667 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3001 ms 
2025-05-07 15:49:33.158 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-07 15:49:33.729 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-07 15:49:35.024 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-07 15:49:35.029 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-07 15:49:35.031 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-07 15:49:35.452 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@58439402] is availabel 
2025-05-07 15:49:35.453 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@58439402] is availabel 
2025-05-07 15:49:37.912 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 2dded378-ebf3-499f-8e69-66bb18562e65
 
2025-05-07 15:49:37.969 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-07 15:49:37.971 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-07 15:49:37.998 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3751baf6, org.springframework.security.web.context.SecurityContextPersistenceFilter@fd5c7f6, org.springframework.security.web.header.HeaderWriterFilter@aca2a0b, org.springframework.security.web.authentication.logout.LogoutFilter@1e4b9592, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@77604a86, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4bc39640, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@27e656e6, org.springframework.security.web.session.SessionManagementFilter@5fe4848, org.springframework.security.web.access.ExceptionTranslationFilter@7945986a] 
2025-05-07 15:49:38.534 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-07 15:49:38.560 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-07 15:49:41.157 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-07 15:49:41.313 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=94 
2025-05-07 15:49:42.135 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@631e06ab, started on Wed May 07 15:49:29 CST 2025]]],spend-ms:[956] 
2025-05-07 15:49:42.165 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-07 15:49:43.257 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[616] 
2025-05-07 15:49:44.082 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@631e06ab, started on Wed May 07 15:49:29 CST 2025]]],spend-ms:[1943] 
2025-05-07 15:49:44.158 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=62 
2025-05-07 15:49:44.200 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@631e06ab, started on Wed May 07 15:49:29 CST 2025]]],spend-ms:[116] 
2025-05-07 15:49:44.221 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=17 
2025-05-07 15:49:44.559 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@631e06ab, started on Wed May 07 15:49:29 CST 2025]]],spend-ms:[358] 
2025-05-07 15:49:45.415 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-07 15:49:45.713 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=292 
2025-05-07 15:49:46.145 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[389] 
2025-05-07 15:49:46.717 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@631e06ab, started on Wed May 07 15:49:29 CST 2025]]],spend-ms:[2157] 
2025-05-07 15:49:46.992 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[213] 
2025-05-07 15:49:47.190 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@631e06ab, started on Wed May 07 15:49:29 CST 2025]]],spend-ms:[470] 
2025-05-07 15:49:47.201 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@631e06ab, started on Wed May 07 15:49:29 CST 2025]]],spend-ms:[10] 
2025-05-07 15:49:47.204 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@631e06ab, started on Wed May 07 15:49:29 CST 2025]]],spend-ms:[2] 
2025-05-07 15:49:47.228 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 18.104 seconds (JVM running for 19.06) 
2025-05-07 15:52:42.783 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1746604174123_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: detected 1 failed or restarted instances. 
2025-05-07 15:52:42.790 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1746604174123_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: Scanning for instance "DESKTOP-QISESQA1746604250415"'s failed in-progress jobs. 
2025-05-07 15:54:36.525 [http-nio-7001-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-07 15:54:36.526 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-07 15:54:36.535 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 9 ms 
2025-05-07 15:55:12.543 [http-nio-7001-exec-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><93971072183541847142400> SQL:SELECT count(0) FROM t_ryytn_master_sku    cost=31 
2025-05-07 15:55:12.623 [http-nio-7001-exec-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><93971072183541847142400> SQL:SELECT id, product_code, product_name, create_date, create_time, update_date, updater, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code FROM t_ryytn_master_sku ORDER BY id DESC LIMIT /*First_PageHelper*/10     cost=61 
2025-05-07 15:55:35.418 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><93971072183541847142400> Exit className:[cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl],methodName:[queryMasterSkuPage],param:[[PageCondition(pageNum=0, pageSize=10, condition=MasterSkuQueryDto(productCode=, productName=, primaryCategory=, secondaryCategory=, tertiaryCategory=, quaternaryCategory=, zeroLevelCode=, zeroLevelDesc=, productStatus=, offMarketStatus=, offMarketDateStart=null, offMarketDateEnd=null))]],spend-ms:[38341]
2025-05-07 15:55:35.446 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><93971072183541847142400> Exit className:[cn.aliyun.ryytn.modules.master.controller.MasterSkuController],methodName:[page],param:[[MasterSkuQueryRequest(pageNum=0, pageSize=10, productCode=, productName=, primaryCategory=, secondaryCategory=, tertiaryCategory=, quaternaryCategory=, zeroLevelCode=, zeroLevelDesc=, productStatus=, offMarketStatus=, offMarketDateStart=null, offMarketDateEnd=null)]],spend-ms:[58696]
2025-05-09 14:33:13.660 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-09 14:33:13.671 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-09 14:33:13.673 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-09 14:33:13.686 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 45707 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-09 14:33:13.687 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-09 14:33:15.149 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-09 14:33:15.152 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-09 14:33:15.178 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces. 
2025-05-09 14:33:15.447 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-09 14:33:15.604 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@6a175569 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-09 14:33:15.651 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 14:33:15.671 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 14:33:15.673 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 14:33:15.676 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 14:33:15.733 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-09 14:33:15.733 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-09 14:33:15.733 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-09 14:33:15.733 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-09 14:33:15.743 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-09 14:33:15.744 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-09 14:33:15.745 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-09 14:33:16.561 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-09 14:33:16.571 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-09 14:33:16.571 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-09 14:33:16.572 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-09 14:33:16.660 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-09 14:33:16.660 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2930 ms 
2025-05-09 14:33:17.099 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-09 14:33:17.589 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-09 14:33:19.175 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-09 14:33:19.185 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-09 14:33:19.187 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-09 14:33:19.597 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@48fc9533] is availabel 
2025-05-09 14:33:19.601 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@48fc9533] is availabel 
2025-05-09 14:33:22.943 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 81abb9ec-8bb0-4be4-a9f6-a1040ae9f993
 
2025-05-09 14:33:23.036 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-09 14:33:23.037 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-09 14:33:23.080 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2351255a, org.springframework.security.web.context.SecurityContextPersistenceFilter@5a6d4dee, org.springframework.security.web.header.HeaderWriterFilter@563bd6a4, org.springframework.security.web.authentication.logout.LogoutFilter@6d2f910b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@48224381, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3e3a07ab, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4f836d9, org.springframework.security.web.session.SessionManagementFilter@25762f04, org.springframework.security.web.access.ExceptionTranslationFilter@3cc548f6] 
2025-05-09 14:33:23.634 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-09 14:33:23.671 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-09 14:33:26.480 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-09 14:33:26.677 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=117 
2025-05-09 14:33:27.488 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-09 14:33:27.747 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7383eae2, started on Fri May 09 14:33:13 CST 2025]]],spend-ms:[1241] 
2025-05-09 14:33:29.147 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[741] 
2025-05-09 14:33:30.248 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7383eae2, started on Fri May 09 14:33:13 CST 2025]]],spend-ms:[2496] 
2025-05-09 14:33:30.317 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=55 
2025-05-09 14:33:30.369 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7383eae2, started on Fri May 09 14:33:13 CST 2025]]],spend-ms:[120] 
2025-05-09 14:33:30.396 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=19 
2025-05-09 14:33:30.816 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7383eae2, started on Fri May 09 14:33:13 CST 2025]]],spend-ms:[445] 
2025-05-09 14:33:31.659 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-09 14:33:32.122 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=458 
2025-05-09 14:33:32.567 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[385] 
2025-05-09 14:33:33.129 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7383eae2, started on Fri May 09 14:33:13 CST 2025]]],spend-ms:[2311] 
2025-05-09 14:33:33.443 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[229] 
2025-05-09 14:33:33.693 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7383eae2, started on Fri May 09 14:33:13 CST 2025]]],spend-ms:[555] 
2025-05-09 14:33:33.712 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7383eae2, started on Fri May 09 14:33:13 CST 2025]]],spend-ms:[18] 
2025-05-09 14:33:33.742 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7383eae2, started on Fri May 09 14:33:13 CST 2025]]],spend-ms:[29] 
2025-05-09 14:33:33.775 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 20.534 seconds (JVM running for 21.551) 
2025-05-09 14:33:53.149 [http-nio-7001-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-09 14:33:53.151 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-09 14:33:53.156 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms 
2025-05-09 14:34:40.294 [http-nio-7001-exec-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48081072888086873092096> SQL:SELECT count(0) FROM t_ryytn_master_sku    cost=38 
2025-05-09 14:34:40.409 [http-nio-7001-exec-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48081072888086873092096> SQL:SELECT id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater FROM t_ryytn_master_sku ORDER BY id DESC LIMIT /*First_PageHelper*/10     cost=93 
2025-05-09 14:34:48.733 [http-nio-7001-exec-3] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><48081072888086873092096> Exception className:[cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl],methodName:[queryMasterSkuPage],param:[[PageCondition(pageNum=0, pageSize=10, condition=MasterSkuQueryRequest(pageNum=0, pageSize=10, productCode=, productName=, primaryCategory=, secondaryCategory=, tertiaryCategory=, quaternaryCategory=, zeroLevelCode=, zeroLevelDesc=, productStatus=, offMarketStatus=, offMarketDateStart=null, offMarketDateEnd=null))]],spend-ms:[12241],Exception:[
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113
### The error may exist in file [/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-modules/transaction-system/transaction-system-service/target/classes/mapper/MasterSkuDao.xml]
### The error may involve cn.aliyun.ryytn.modules.master.dao.MasterSkuDao.queryMasterSkuPage-Inline
### The error occurred while setting parameters
### SQL: SELECT             id,             product_code,             product_name,             create_time,             update_time,             material_type,             material_type_desc,             material_group,             material_group_desc,             base_unit_desc,             gross_weight,             net_weight,             weight_unit,             barcode,             brand,             short_name,             model_spec,             shelf_life,             length,             width,             height,             volume,             batch_management_flag,             layer11_unit,             layer9_quantity,             layer10_quantity,             tax_classification,             sn_enabled,             traceability_enabled,             primary_category,             secondary_category,             tertiary_category,             quaternary_category,             off_market_status,             off_market_date,             product_status,             zero_level_desc,             zero_level_code,             sap_create_date,             sap_create_time,             sap_update_date,             sap_updater         FROM t_ryytn_master_sku                    ORDER BY id DESC LIMIT ?
### Cause: org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113] 
2025-05-09 14:34:48.737 [http-nio-7001-exec-3] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><48081072888086873092096> Exception className:[cn.aliyun.ryytn.modules.master.controller.MasterSkuController],methodName:[page],param:[[MasterSkuQueryRequest(pageNum=0, pageSize=10, productCode=, productName=, primaryCategory=, secondaryCategory=, tertiaryCategory=, quaternaryCategory=, zeroLevelCode=, zeroLevelDesc=, productStatus=, offMarketStatus=, offMarketDateStart=null, offMarketDateEnd=null)]],spend-ms:[35342],Exception:[
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113
### The error may exist in file [/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-modules/transaction-system/transaction-system-service/target/classes/mapper/MasterSkuDao.xml]
### The error may involve cn.aliyun.ryytn.modules.master.dao.MasterSkuDao.queryMasterSkuPage-Inline
### The error occurred while setting parameters
### SQL: SELECT             id,             product_code,             product_name,             create_time,             update_time,             material_type,             material_type_desc,             material_group,             material_group_desc,             base_unit_desc,             gross_weight,             net_weight,             weight_unit,             barcode,             brand,             short_name,             model_spec,             shelf_life,             length,             width,             height,             volume,             batch_management_flag,             layer11_unit,             layer9_quantity,             layer10_quantity,             tax_classification,             sn_enabled,             traceability_enabled,             primary_category,             secondary_category,             tertiary_category,             quaternary_category,             off_market_status,             off_market_date,             product_status,             zero_level_desc,             zero_level_code,             sap_create_date,             sap_create_time,             sap_update_date,             sap_updater         FROM t_ryytn_master_sku                    ORDER BY id DESC LIMIT ?
### Cause: org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113] 
2025-05-09 14:34:48.767 [http-nio-7001-exec-3] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - <0><48081072888086873092096> handleThrowable:org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113
### The error may exist in file [/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-modules/transaction-system/transaction-system-service/target/classes/mapper/MasterSkuDao.xml]
### The error may involve cn.aliyun.ryytn.modules.master.dao.MasterSkuDao.queryMasterSkuPage-Inline
### The error occurred while setting parameters
### SQL: SELECT             id,             product_code,             product_name,             create_time,             update_time,             material_type,             material_type_desc,             material_group,             material_group_desc,             base_unit_desc,             gross_weight,             net_weight,             weight_unit,             barcode,             brand,             short_name,             model_spec,             shelf_life,             length,             width,             height,             volume,             batch_management_flag,             layer11_unit,             layer9_quantity,             layer10_quantity,             tax_classification,             sn_enabled,             traceability_enabled,             primary_category,             secondary_category,             tertiary_category,             quaternary_category,             off_market_status,             off_market_date,             product_status,             zero_level_desc,             zero_level_code,             sap_create_date,             sap_create_time,             sap_update_date,             sap_updater         FROM t_ryytn_master_sku                    ORDER BY id DESC LIMIT ?
### Cause: org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy108.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy171.queryMasterSkuPage(Unknown Source)
	at cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl.queryMasterSkuPage(MasterSkuServiceImpl.java:375)
	at cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl$$FastClassBySpringCGLIB$$15f6443f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at cn.aliyun.ryytn.starter.aspect.LogAspect.around(LogAspect.java:82)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)
	at cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl$$EnhancerBySpringCGLIB$$ab74021d.queryMasterSkuPage(<generated>)
	at cn.aliyun.ryytn.modules.master.controller.MasterSkuController.page(MasterSkuController.java:46)
	at cn.aliyun.ryytn.modules.master.controller.MasterSkuController$$FastClassBySpringCGLIB$$bf767351.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at cn.aliyun.ryytn.starter.aspect.LogAspect.around(LogAspect.java:82)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)
	at cn.aliyun.ryytn.modules.master.controller.MasterSkuController$$EnhancerBySpringCGLIB$$86f98a93.page(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:164)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:153)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3461)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at cn.aliyun.ryytn.starter.config.MybatisSqlCompletePrintInterceptor.intercept(MybatisSqlCompletePrintInterceptor.java:56)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy214.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.util.ExecutorUtil.pageQuery(ExecutorUtil.java:177)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:104)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy213.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 159 more
 
2025-05-09 14:34:48.780 [http-nio-7001-exec-3] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><48081072888086873092096> Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@2338ffeb], org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113
### The error may exist in file [/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-modules/transaction-system/transaction-system-service/target/classes/mapper/MasterSkuDao.xml]
### The error may involve cn.aliyun.ryytn.modules.master.dao.MasterSkuDao.queryMasterSkuPage-Inline
### The error occurred while setting parameters
### SQL: SELECT             id,             product_code,             product_name,             create_time,             update_time,             material_type,             material_type_desc,             material_group,             material_group_desc,             base_unit_desc,             gross_weight,             net_weight,             weight_unit,             barcode,             brand,             short_name,             model_spec,             shelf_life,             length,             width,             height,             volume,             batch_management_flag,             layer11_unit,             layer9_quantity,             layer10_quantity,             tax_classification,             sn_enabled,             traceability_enabled,             primary_category,             secondary_category,             tertiary_category,             quaternary_category,             off_market_status,             off_market_date,             product_status,             zero_level_desc,             zero_level_code,             sap_create_date,             sap_create_time,             sap_update_date,             sap_updater         FROM t_ryytn_master_sku                    ORDER BY id DESC LIMIT ?
### Cause: org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "update_time" does not exist
  位置：113]],spend-ms:[31] 
2025-05-09 14:37:44.449 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-09 14:37:44.460 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-09 14:37:44.462 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-09 14:37:44.476 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 45794 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-09 14:37:44.476 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-09 14:37:45.981 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-09 14:37:45.984 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-09 14:37:46.010 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces. 
2025-05-09 14:37:46.234 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-09 14:37:46.394 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@3e2fc448 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-09 14:37:46.435 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 14:37:46.452 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 14:37:46.454 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 14:37:46.457 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 14:37:46.517 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-09 14:37:46.517 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-09 14:37:46.517 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-09 14:37:46.518 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-09 14:37:46.527 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-09 14:37:46.528 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-09 14:37:46.529 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-09 14:37:47.352 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-09 14:37:47.363 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-09 14:37:47.363 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-09 14:37:47.363 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-09 14:37:47.455 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-09 14:37:47.455 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2936 ms 
2025-05-09 14:37:47.946 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-09 14:37:48.443 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-09 14:37:49.994 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-09 14:37:50.001 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-09 14:37:50.003 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-09 14:37:50.425 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@508630b4] is availabel 
2025-05-09 14:37:50.427 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@508630b4] is availabel 
2025-05-09 14:37:53.279 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 6ac339ba-25bc-4e51-a74c-33697237c219
 
2025-05-09 14:37:53.363 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-09 14:37:53.364 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-09 14:37:53.405 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@144792d5, org.springframework.security.web.context.SecurityContextPersistenceFilter@423d662a, org.springframework.security.web.header.HeaderWriterFilter@7fa86ddd, org.springframework.security.web.authentication.logout.LogoutFilter@1718de70, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5e7ea81b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@341c0dfc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1da61a29, org.springframework.security.web.session.SessionManagementFilter@2eb73a42, org.springframework.security.web.access.ExceptionTranslationFilter@28f6a008] 
2025-05-09 14:37:53.959 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-09 14:37:53.994 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-09 14:37:56.721 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-09 14:37:56.901 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=100 
2025-05-09 14:37:57.726 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-09 14:37:57.860 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c9f0a20, started on Fri May 09 14:37:44 CST 2025]]],spend-ms:[1108] 
2025-05-09 14:37:58.775 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[729] 
2025-05-09 14:38:00.094 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c9f0a20, started on Fri May 09 14:37:44 CST 2025]]],spend-ms:[2231] 
2025-05-09 14:38:00.183 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=53 
2025-05-09 14:38:00.346 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c9f0a20, started on Fri May 09 14:37:44 CST 2025]]],spend-ms:[245] 
2025-05-09 14:38:00.376 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=24 
2025-05-09 14:38:00.729 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c9f0a20, started on Fri May 09 14:37:44 CST 2025]]],spend-ms:[382] 
2025-05-09 14:38:01.730 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-09 14:38:02.203 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=466 
2025-05-09 14:38:02.610 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[362] 
2025-05-09 14:38:03.210 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c9f0a20, started on Fri May 09 14:37:44 CST 2025]]],spend-ms:[2480] 
2025-05-09 14:38:03.502 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[229] 
2025-05-09 14:38:03.731 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c9f0a20, started on Fri May 09 14:37:44 CST 2025]]],spend-ms:[518] 
2025-05-09 14:38:03.757 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c9f0a20, started on Fri May 09 14:37:44 CST 2025]]],spend-ms:[22] 
2025-05-09 14:38:03.771 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c9f0a20, started on Fri May 09 14:37:44 CST 2025]]],spend-ms:[13] 
2025-05-09 14:38:03.835 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 19.772 seconds (JVM running for 21.11) 
2025-05-09 14:38:12.904 [http-nio-7001-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-09 14:38:12.904 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-09 14:38:12.908 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms 
2025-05-09 14:38:22.948 [CONSUMER_LISTEN_2] ERROR cn.aliyun.ryytn.common.utils.redis.RedisUtils - rightPop has exception: 
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:514)
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.leftPop(DefaultListOperations.java:108)
	at cn.aliyun.ryytn.common.utils.redis.RedisUtils.lPop(RedisUtils.java:1163)
	at cn.aliyun.ryytn.common.mq.impl.RedisListConsumerServiceImpl.consume(RedisListConsumerServiceImpl.java:84)
	at cn.aliyun.ryytn.common.mq.impl.RedisListConsumerServiceImpl.access$000(RedisListConsumerServiceImpl.java:23)
	at cn.aliyun.ryytn.common.mq.impl.RedisListConsumerServiceImpl$1.run(RedisListConsumerServiceImpl.java:46)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
	at redis.clients.jedis.util.Pool.getResource(Pool.java:84)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:370)
	at redis.clients.jedis.JedisPool.getResource(JedisPool.java:15)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:283)
	... 15 common frames omitted
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:110)
	at redis.clients.jedis.Connection.connect(Connection.java:226)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309)
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87)
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:292)
	at redis.clients.jedis.Jedis.<init>(Jedis.java:167)
	at redis.clients.jedis.JedisFactory.makeObject(JedisFactory.java:177)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:918)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:431)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:356)
	at redis.clients.jedis.util.Pool.getResource(Pool.java:75)
	... 18 common frames omitted
Caused by: java.net.SocketException: Can't connect to SOCKS proxy:null
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:430)
	at java.net.Socket.connect(Socket.java:606)
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:80)
	... 29 common frames omitted
2025-05-09 14:38:23.213 [http-nio-7001-exec-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><62811072889092470059008> SQL:SELECT count(0) FROM t_ryytn_master_sku    cost=21 
2025-05-09 14:38:23.301 [http-nio-7001-exec-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><62811072889092470059008> SQL:SELECT id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater FROM t_ryytn_master_sku ORDER BY id DESC LIMIT /*First_PageHelper*/10     cost=68 
2025-05-09 14:39:01.910 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><62811072889092470059008> Exit className:[cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl],methodName:[queryMasterSkuPage],param:[[PageCondition(pageNum=0, pageSize=10, condition=MasterSkuQueryRequest(pageNum=0, pageSize=10, productCode=, productName=, primaryCategory=, secondaryCategory=, tertiaryCategory=, quaternaryCategory=, zeroLevelCode=, zeroLevelDesc=, productStatus=, offMarketStatus=, offMarketDateStart=null, offMarketDateEnd=null))]],spend-ms:[44822] 
2025-05-09 14:39:01.960 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1746772668747_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1746772668747) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-09 14:39:01.962 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><62811072889092470059008> Exit className:[cn.aliyun.ryytn.modules.master.controller.MasterSkuController],methodName:[page],param:[[MasterSkuQueryRequest(pageNum=0, pageSize=10, productCode=, productName=, primaryCategory=, secondaryCategory=, tertiaryCategory=, quaternaryCategory=, zeroLevelCode=, zeroLevelDesc=, productStatus=, offMarketStatus=, offMarketDateStart=null, offMarketDateEnd=null)]],spend-ms:[48814] 
2025-05-09 15:22:30.673 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-09 15:22:30.684 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-09 15:22:30.686 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-09 15:22:30.700 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 46632 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-09 15:22:30.701 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-09 15:22:32.214 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-09 15:22:32.218 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-09 15:22:32.244 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces. 
2025-05-09 15:22:32.526 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-09 15:22:32.683 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@3277e499 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-09 15:22:32.723 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 15:22:32.738 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 15:22:32.740 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 15:22:32.743 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 15:22:32.800 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-09 15:22:32.801 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-09 15:22:32.801 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-09 15:22:32.801 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-09 15:22:32.811 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-09 15:22:32.813 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-09 15:22:32.814 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-09 15:22:33.639 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-09 15:22:33.649 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-09 15:22:33.649 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-09 15:22:33.649 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-09 15:22:33.773 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-09 15:22:33.773 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3021 ms 
2025-05-09 15:22:34.207 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-09 15:22:34.714 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-09 15:22:37.524 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-09 15:22:37.532 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-09 15:22:37.534 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-09 15:22:37.896 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@7736a085] is availabel 
2025-05-09 15:22:37.899 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@7736a085] is availabel 
2025-05-09 15:22:40.955 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 8c756a44-af5b-47f5-9b0e-e4071284fed0
 
2025-05-09 15:22:41.041 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-09 15:22:41.042 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-09 15:22:41.085 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7a482b3a, org.springframework.security.web.context.SecurityContextPersistenceFilter@2165d4ab, org.springframework.security.web.header.HeaderWriterFilter@474deb4c, org.springframework.security.web.authentication.logout.LogoutFilter@55a5eea3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5325674a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6fe91918, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@600b3bee, org.springframework.security.web.session.SessionManagementFilter@65fe1f47, org.springframework.security.web.access.ExceptionTranslationFilter@397f925e] 
2025-05-09 15:22:41.695 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-09 15:22:41.731 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-09 15:22:44.612 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-09 15:22:44.855 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=158 
2025-05-09 15:22:45.620 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-09 15:22:46.667 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64dafeed, started on Fri May 09 15:22:30 CST 2025]]],spend-ms:[2018] 
2025-05-09 15:22:48.578 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[946] 
2025-05-09 15:22:49.925 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64dafeed, started on Fri May 09 15:22:30 CST 2025]]],spend-ms:[3251] 
2025-05-09 15:22:49.986 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=41 
2025-05-09 15:22:50.074 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64dafeed, started on Fri May 09 15:22:30 CST 2025]]],spend-ms:[147] 
2025-05-09 15:22:50.126 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=30 
2025-05-09 15:22:50.709 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64dafeed, started on Fri May 09 15:22:30 CST 2025]]],spend-ms:[631] 
2025-05-09 15:22:52.367 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-09 15:22:52.963 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=590 
2025-05-09 15:22:53.389 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[300] 
2025-05-09 15:22:54.078 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64dafeed, started on Fri May 09 15:22:30 CST 2025]]],spend-ms:[3366] 
2025-05-09 15:22:54.455 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[297] 
2025-05-09 15:22:54.704 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64dafeed, started on Fri May 09 15:22:30 CST 2025]]],spend-ms:[623] 
2025-05-09 15:22:54.722 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64dafeed, started on Fri May 09 15:22:30 CST 2025]]],spend-ms:[17] 
2025-05-09 15:22:54.734 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64dafeed, started on Fri May 09 15:22:30 CST 2025]]],spend-ms:[11] 
2025-05-09 15:22:54.751 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 24.576 seconds (JVM running for 25.756) 
2025-05-09 16:49:34.580 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-09 16:49:34.591 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-09 16:49:34.593 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-09 16:49:34.606 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 48281 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-09 16:49:34.607 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-09 16:49:36.150 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-09 16:49:36.154 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-09 16:49:36.182 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces. 
2025-05-09 16:49:36.482 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-09 16:49:36.652 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@28fa700e , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-09 16:49:36.696 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 16:49:36.712 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 16:49:36.715 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 16:49:36.717 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-09 16:49:36.772 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-09 16:49:36.772 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-09 16:49:36.772 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-09 16:49:36.772 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-09 16:49:36.782 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-09 16:49:36.783 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-09 16:49:36.784 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-09 16:49:37.619 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-09 16:49:37.629 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-09 16:49:37.629 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-09 16:49:37.629 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-09 16:49:37.721 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-09 16:49:37.722 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3071 ms 
2025-05-09 16:49:38.209 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-09 16:49:38.816 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-09 16:49:40.443 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-09 16:49:40.447 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-09 16:49:40.449 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-09 16:49:40.845 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@628ddf] is availabel 
2025-05-09 16:49:40.848 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@628ddf] is availabel 
2025-05-09 16:49:43.864 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: f9d8d21c-8363-4321-836b-6c0d6602cee7
 
2025-05-09 16:49:43.953 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-09 16:49:43.954 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-09 16:49:44.022 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@45a05350, org.springframework.security.web.context.SecurityContextPersistenceFilter@630bd5e7, org.springframework.security.web.header.HeaderWriterFilter@260f05ee, org.springframework.security.web.authentication.logout.LogoutFilter@981d9d2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@260ccf0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@66cb9a63, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7d6a9d5e, org.springframework.security.web.session.SessionManagementFilter@5325674a, org.springframework.security.web.access.ExceptionTranslationFilter@2111d7b9] 
2025-05-09 16:49:44.638 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-09 16:49:44.675 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-09 16:49:47.903 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-09 16:49:48.088 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=99 
2025-05-09 16:49:48.908 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-09 16:49:49.083 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@19fe4644, started on Fri May 09 16:49:34 CST 2025]]],spend-ms:[1141] 
2025-05-09 16:49:50.108 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[666] 
2025-05-09 16:49:51.150 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@19fe4644, started on Fri May 09 16:49:34 CST 2025]]],spend-ms:[2059] 
2025-05-09 16:49:51.207 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=40 
2025-05-09 16:49:51.262 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@19fe4644, started on Fri May 09 16:49:34 CST 2025]]],spend-ms:[111] 
2025-05-09 16:49:51.298 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=28 
2025-05-09 16:49:51.682 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@19fe4644, started on Fri May 09 16:49:34 CST 2025]]],spend-ms:[419] 
2025-05-09 16:49:52.689 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-09 16:49:53.226 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=530 
2025-05-09 16:49:53.553 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[257] 
2025-05-09 16:49:54.306 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@19fe4644, started on Fri May 09 16:49:34 CST 2025]]],spend-ms:[2622] 
2025-05-09 16:49:54.600 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[225] 
2025-05-09 16:49:54.929 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@19fe4644, started on Fri May 09 16:49:34 CST 2025]]],spend-ms:[614] 
2025-05-09 16:49:54.956 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@19fe4644, started on Fri May 09 16:49:34 CST 2025]]],spend-ms:[24] 
2025-05-09 16:49:54.965 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@19fe4644, started on Fri May 09 16:49:34 CST 2025]]],spend-ms:[8] 
2025-05-09 16:49:54.992 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 20.835 seconds (JVM running for 21.989) 
2025-05-09 16:50:13.133 [http-nio-7001-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-09 16:50:13.134 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-09 16:50:13.147 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 13 ms 
2025-05-09 16:50:13.597 [http-nio-7001-exec-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><31591072922312368566272> SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE job_id = /*jobId*/'576066760335966208'    cost=27 
2025-05-09 16:50:13.812 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><31591072922312368566272> Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[triggerSchedulerJob],param:[[576066760335966208]],spend-ms:[251] 
2025-05-09 16:50:13.848 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><31591072922312368566272> Exit className:[cn.aliyun.ryytn.modules.scheduler.controller.SchedulerController],methodName:[triggerSchedulerJob],param:[[576066760335966208]],spend-ms:[325] 
2025-05-09 16:50:14.279 [transaction_Worker-1] INFO  c.a.r.m.m.task.SyncMasterSkuFromSapTaskServiceImpl - <0><31591072922316755808256> Start executing sync master SKU from SAP scheduled task 
2025-05-09 16:50:32.467 [transaction_Worker-1] INFO  c.a.r.m.m.task.SyncMasterSkuFromSapTaskServiceImpl - <0><31591072922316755808256> Using default sync time: 20250508 (yesterday) 
2025-05-09 16:50:32.478 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Start syncing master SKU for all products from SAP, sync date: 20250508 
2025-05-09 16:50:32.483 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Processing batch 1/1, with 1 requests 
2025-05-09 16:50:32.493 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1746780579254_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1746780579254) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-09 16:50:35.162 [transaction_Worker-1] INFO  cn.aliyun.ryytn.common.utils.sap.SapPushUtil - <0><31591072922316755808256> httpRequestSap.success requestBody:{"CTRL":{"DATUM":"2025-05-09","FUNID":"ZSCMINF001","INFID":"ZSCMINF001","KEYID":"eeb1136e-1614-4481-ba6c-0b56fc171528","MD5":"","MSAGE":"","MSGTY":"","REVID":"SAP","SYSID":"SCM","UNAME":"SCM平台","UZEIT":"16:50:32"},"DATA":[{"DATUM":"20250508"}]}, responseBody:{"CTRL":{"MSGTY":"S","UNAME":"SCM平台","DATUM":"2025-05-09","SYSID":"SCM","INFID":"ZSCMINF001","PAGE_NO":0,"FUNID":"ZSCMINF001","PAGE_SIZE":0,"METHOD":"","REVID":"SAP","TABIX":0,"MSAGE":"OK共5条","KEYID":"eeb1136e-1614-4481-ba6c-0b56fc171528","UZEIT":"16:50:32","MD5":""},"DATA":[{"XCHPF":"X","ZPLFL":"","MAXLZ":180,"ZWIDTH":0.290000,"ZAMOUNT":"","MEINST":"箱","ZJJXX":0,"ZEJFL":"250利乐峰","GEWEI":"KG","ZCW_UNIT":"TI","ZSJID":"","ZPACKAGING":"","ZGUIGE2":2.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"250利乐峰A2纯（卡片款）10入-2提","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":6.640,"MBEW":1.00,"STPRS":0,"ZSJFL":"A2纯","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-11","ZSL":0.09,"ZZSJFL":"梦幻盖A2纯","MTARTT":"产成品","ZGUIGE":10.000,"ZFHJC":"","NTGEW":5.180,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"02:42:25","ZLENGTH":0.334000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.021503,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000600","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"1*2 250利乐峰（卡片款）","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.222000,"MEINS":"CS","MODEL":"","ZYJFL":"常温白奶","BCODE":"69110101000600","ZYN":"","MATKLT":"纯牛奶-利乐峰250ml","MSTAE":"","MATKL":"10112","ZZB_DB":""},{"XCHPF":"","ZPLFL":"","MAXLZ":365,"ZWIDTH":0.330000,"ZAMOUNT":"","MEINST":"个","ZJJXX":0,"ZEJFL":"定制赠品","GEWEI":"KG","ZCW_UNIT":"EA","ZSJID":"YN3012","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"(液奶专用)2024龙年定制帆布袋","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.125,"MBEW":4.60,"STPRS":0,"ZSJFL":"纺织品","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-14","ZSL":0.13,"ZZSJFL":"其他","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.125,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"YN2008","CREATED":"01:13:43","ZLENGTH":0.310000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.008184,"ZYJID":"YN1003","ZBZXSJ":0,"ZZSJID":"YN4008","ZFWB":"","MATNR":"110101000603","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"（液奶专用）2024龙年定制帆布袋","UEBTO":0,"ZLJID":"YN","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.080000,"MEINS":"EA","MODEL":"","ZYJFL":"营销品-入仓","BCODE":"6970037317034","ZYN":"","MATKLT":"非卖品","MSTAE":"","MATKL":"10500","ZZB_DB":""},{"XCHPF":"","ZPLFL":"","MAXLZ":365,"ZWIDTH":0.330000,"ZAMOUNT":"","MEINST":"个","ZJJXX":0,"ZEJFL":"定制赠品","GEWEI":"KG","ZCW_UNIT":"EA","ZSJID":"","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"","ZZB_GHL":"","MAKTX":"(奶粉专用)2024龙年定制帆布袋","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.125,"MBEW":1.00,"STPRS":0,"ZSJFL":"纺织品","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-14","ZSL":0.13,"ZZSJFL":"其他","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.125,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"05:36:29","ZLENGTH":0.310000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.008184,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000605","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"2024龙年定制帆布袋","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.080000,"MEINS":"EA","MODEL":"","ZYJFL":"营销品-入仓","BCODE":"69110101000605","ZYN":"","MATKLT":"非卖品","MSTAE":"","MATKL":"10500","ZZB_DB":""},{"XCHPF":"X","ZPLFL":"","MAXLZ":180,"ZWIDTH":0.035000,"ZAMOUNT":"","MEINST":"盒","ZJJXX":0,"ZEJFL":"200柳叶","GEWEI":"KG","ZCW_UNIT":"HE","ZSJID":"","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"103020401","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"200柳叶有机纯-单盒","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.200,"MBEW":1.00,"STPRS":0,"ZSJFL":"有机纯","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-18","ZSL":0.09,"ZZSJFL":"200有机纯","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.006,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"06:05:21","ZLENGTH":0.046000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.000192,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000609","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"200柳叶有机纯16入-单盒","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.119000,"MEINS":"HE","MODEL":"","ZYJFL":"常温白奶","BCODE":"69110101000609","ZYN":"","MATKLT":"纯牛奶-柳叶包200ml","MSTAE":"","MATKL":"10105","ZZB_DB":""},{"XCHPF":"X","ZPLFL":"纯奶","MAXLZ":180,"ZWIDTH":0.295000,"ZAMOUNT":"250ML","MEINST":"箱","ZJJXX":120.00,"ZEJFL":"250砖","GEWEI":"KG","ZCW_UNIT":"TI","ZSJID":"YN3031","ZPACKAGING":"利乐砖","ZGUIGE2":6.000,"ZSJB":"线下开窗款","ZSSFL":"103020401","ZPRODUCT_DL":"液奶","ZZB_GHL":"120G/100ML","MAKTX":"250砖普通纯（线下）12入-6提","LAEDA":"2025-05-09","ERNAMT":"BOZHI","BRGEW":21.440,"MBEW":137.16,"STPRS":0,"ZSJFL":"普通纯","ZZB_ZF":"3.6G/100ML","BRAND":"认养一头牛","ERSDA":"2020-08-11","ZSL":0.09,"ZZSJFL":"250普通纯","MTARTT":"产成品","ZGUIGE":12.000,"ZFHJC":"1*6开窗纯","NTGEW":18.648,"ZA1_PX":"04","ZZBXZHZ":"全脂","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"250纯砖线下","ZEJID":"YN2027","CREATED":"13:46:56","ZLENGTH":0.500000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.044693,"ZYJID":"YN1009","ZBZXSJ":269.40,"ZZSJID":"YN4046","ZFWB":"原味","MATNR":"110101100601","ERNAM":"BOZHI","AENAM":"C011802","BRNAM":"250mL利乐砖纯奶（线下版）六提装","UEBTO":0,"ZLJID":"YN","ZCWUNIT":"","ZJJSX":420.00,"ZHIGH":0.303000,"MEINS":"CS","MODEL":"250ML*12*6","ZYJFL":"常温白奶","BCODE":"6970037310172","ZYN":"普通奶","MATKLT":"纯牛奶-利乐砖250ml","MSTAE":"","MATKL":"10102","ZZB_DB":"3.3G/100ML"}]} 
2025-05-09 16:50:35.893 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Batch 1/1 response received 
2025-05-09 16:50:35.894 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Batch 1/1 processing 5 records 
2025-05-09 16:50:35.894 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Processing batch 1/1, with 5 records 
2025-05-09 16:50:35.894 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Batch 1/1 found 5 valid product codes 
2025-05-09 16:50:35.947 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><31591072922316755808256> SQL:SELECT id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater FROM t_ryytn_master_sku WHERE product_code IN ( /*__frch_productCode_0*/'110101000600' , /*__frch_productCode_1*/'110101000603' , /*__frch_productCode_2*/'110101000605' , /*__frch_productCode_3*/'110101000609' , /*__frch_productCode_4*/'110101100601' )    cost=39 
2025-05-09 16:50:35.948 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Batch 1/1 found 5 existing master SKUs in database 
2025-05-09 16:51:13.882 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1746780579254_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1746780579254) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-09 16:51:13.991 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><31591072922316755808256> SQL:UPDATE t_ryytn_master_sku SET product_code = /*__frch_item_0.productCode*/'110101000600', product_name = /*__frch_item_0.productName*/'250利乐峰A2纯（卡片款）10入-2提', update_time = /*__frch_item_0.updateTime*/'2025-05-09 16:50:35.948', material_type = /*__frch_item_0.materialType*/'ZERT', material_type_desc = /*__frch_item_0.materialTypeDesc*/'产成品', material_group = /*__frch_item_0.materialGroup*/'10112', material_group_desc = /*__frch_item_0.materialGroupDesc*/'纯牛奶-利乐峰250ml', base_unit_desc = /*__frch_item_0.baseUnitDesc*/'箱', gross_weight = /*__frch_item_0.grossWeight*/6.640, net_weight = /*__frch_item_0.netWeight*/5.180, weight_unit = /*__frch_item_0.weightUnit*/'KG', barcode = /*__frch_item_0.barcode*/'69110101000600', brand = /*__frch_item_0.brand*/'认养一头牛', short_name = /*__frch_item_0.shortName*/'1*2 250利乐峰（卡片款）', model_spec = /*__frch_item_0.modelSpec*/'', shelf_life = /*__frch_item_0.shelfLife*/180, length = /*__frch_item_0.length*/0.334000, width = /*__frch_item_0.width*/0.290000, height = /*__frch_item_0.height*/0.222000, volume = /*__frch_item_0.volume*/0.021503, batch_management_flag = /*__frch_item_0.batchManagementFlag*/'X', layer11_unit = /*__frch_item_0.layer11Unit*/'TI', layer9_quantity = /*__frch_item_0.layer9Quantity*/10.000, layer10_quantity = /*__frch_item_0.layer10Quantity*/2.000, tax_classification = /*__frch_item_0.taxClassification*/'', sn_enabled = /*__frch_item_0.snEnabled*/'N', traceability_enabled = /*__frch_item_0.traceabilityEnabled*/'N', primary_category = /*__frch_item_0.primaryCategory*/'常温白奶', secondary_category = /*__frch_item_0.secondaryCategory*/'250利乐峰', tertiary_category = /*__frch_item_0.tertiaryCategory*/'A2纯', quaternary_category = /*__frch_item_0.quaternaryCategory*/'梦幻盖A2纯', off_market_status = /*__frch_item_0.offMarketStatus*/'', off_market_date = /*__frch_item_0.offMarketDate*/null, product_status = /*__frch_item_0.productStatus*/'', zero_level_desc = /*__frch_item_0.zeroLevelDesc*/'液奶', zero_level_code = /*__frch_item_0.zeroLevelCode*/'', sap_create_date = /*__frch_item_0.sapCreateDate*/'2023-12-11', sap_create_time = /*__frch_item_0.sapCreateTime*/'02:42:25', sap_update_date = /*__frch_item_0.sapUpdateDate*/'2025-05-09', sap_updater = /*__frch_item_0.sapUpdater*/'C011802' WHERE id = /*__frch_item_0.id*/'576087942581985280' ; UPDATE t_ryytn_master_sku SET product_code = /*__frch_item_1.productCode*/'110101000603', product_name = /*__frch_item_1.productName*/'(液奶专用)2024龙年定制帆布袋', update_time = /*__frch_item_1.updateTime*/'2025-05-09 16:50:35.948', material_type = /*__frch_item_1.materialType*/'ZERT', material_type_desc = /*__frch_item_1.materialTypeDesc*/'产成品', material_group = /*__frch_item_1.materialGroup*/'10500', material_group_desc = /*__frch_item_1.materialGroupDesc*/'非卖品', base_unit_desc = /*__frch_item_1.baseUnitDesc*/'个', gross_weight = /*__frch_item_1.grossWeight*/0.125, net_we...4042...    cost=36 
2025-05-09 16:51:13.992 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Batch 1/1 updated 1 existing master SKUs 
2025-05-09 16:51:13.992 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Batch 1/1 completed, success: 5, fail: 0 
2025-05-09 16:51:13.992 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Process master SKU data completed, total success: 5, total fail: 0, total inserted: 0, total updated: 1 
2025-05-09 16:51:13.992 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Batch 1/1 processed 5 records, total processed so far: 5 
2025-05-09 16:51:13.992 [transaction_Worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922316755808256> Sync master SKU from SAP completed, processed 5 records in total 
2025-05-09 16:51:13.993 [transaction_Worker-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><31591072922316755808256> Exit className:[cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl],methodName:[syncMasterSkuFromSap],param:[[20250508, null]],spend-ms:[41523] 
2025-05-09 16:51:13.993 [transaction_Worker-1] INFO  c.a.r.m.m.task.SyncMasterSkuFromSapTaskServiceImpl - <0><31591072922316755808256> Sync master SKU from SAP scheduled task completed successfully 
2025-05-09 16:51:13.993 [transaction_Worker-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><31591072922316755808256> Exit className:[cn.aliyun.ryytn.modules.master.task.SyncMasterSkuFromSapTaskServiceImpl],methodName:[process],param:[[SchedulerJob(jobId=576066760335966208, jobName=从SAP同步主SKU信息定时任务, jobType=1, startDate=Fri May 09 15:23:38 CST 2025, endDate=null, jobConf=0 0 1 * * ?, className=cn.aliyun.ryytn.modules.master.task.SyncMasterSkuFromSapTaskServiceImpl, param=null, serviceId=null, misfirePolicy=null, concurrent=false, status=1, description=null, createdBy=null, createdTime=2025-05-09 15:23:38.468893, updatedBy=null, updatedTime=null)]],spend-ms:[59728] 
2025-05-09 16:52:30.539 [transaction_Worker-2] INFO  c.a.r.m.m.task.SyncMasterSkuFromSapTaskServiceImpl - <0><31591072922888393306112> Start executing sync master SKU from SAP scheduled task 
2025-05-09 16:52:33.118 [transaction_Worker-2] INFO  c.a.r.m.m.task.SyncMasterSkuFromSapTaskServiceImpl - <0><31591072922888393306112> Using default sync time: 20250508 (yesterday) 
2025-05-09 16:52:33.126 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Start syncing master SKU for all products from SAP, sync date: 20250508 
2025-05-09 16:52:33.127 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Processing batch 1/1, with 1 requests 
2025-05-09 16:52:33.369 [transaction_Worker-2] INFO  cn.aliyun.ryytn.common.utils.sap.SapPushUtil - <0><31591072922888393306112> httpRequestSap.success requestBody:{"CTRL":{"DATUM":"2025-05-09","FUNID":"ZSCMINF001","INFID":"ZSCMINF001","KEYID":"df4420fa-10b9-4b30-a27b-19f3a7124f89","MD5":"","MSAGE":"","MSGTY":"","REVID":"SAP","SYSID":"SCM","UNAME":"SCM平台","UZEIT":"16:52:33"},"DATA":[{"DATUM":"20250508"}]}, responseBody:{"CTRL":{"MSGTY":"S","UNAME":"SCM平台","DATUM":"2025-05-09","SYSID":"SCM","INFID":"ZSCMINF001","PAGE_NO":0,"FUNID":"ZSCMINF001","PAGE_SIZE":0,"METHOD":"","REVID":"SAP","TABIX":0,"MSAGE":"OK共5条","KEYID":"df4420fa-10b9-4b30-a27b-19f3a7124f89","UZEIT":"16:52:33","MD5":""},"DATA":[{"XCHPF":"X","ZPLFL":"","MAXLZ":180,"ZWIDTH":0.290000,"ZAMOUNT":"","MEINST":"箱","ZJJXX":0,"ZEJFL":"250利乐峰","GEWEI":"KG","ZCW_UNIT":"TI","ZSJID":"","ZPACKAGING":"","ZGUIGE2":2.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"250利乐峰A2纯（卡片款）10入-2提","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":6.640,"MBEW":1.00,"STPRS":0,"ZSJFL":"A2纯","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-11","ZSL":0.09,"ZZSJFL":"梦幻盖A2纯","MTARTT":"产成品","ZGUIGE":10.000,"ZFHJC":"","NTGEW":5.180,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"02:42:25","ZLENGTH":0.334000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.021503,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000600","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"1*2 250利乐峰（卡片款）","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.222000,"MEINS":"CS","MODEL":"","ZYJFL":"常温白奶","BCODE":"69110101000600","ZYN":"","MATKLT":"纯牛奶-利乐峰250ml","MSTAE":"","MATKL":"10112","ZZB_DB":""},{"XCHPF":"","ZPLFL":"","MAXLZ":365,"ZWIDTH":0.330000,"ZAMOUNT":"","MEINST":"个","ZJJXX":0,"ZEJFL":"定制赠品","GEWEI":"KG","ZCW_UNIT":"EA","ZSJID":"YN3012","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"(液奶专用)2024龙年定制帆布袋","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.125,"MBEW":4.60,"STPRS":0,"ZSJFL":"纺织品","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-14","ZSL":0.13,"ZZSJFL":"其他","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.125,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"YN2008","CREATED":"01:13:43","ZLENGTH":0.310000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.008184,"ZYJID":"YN1003","ZBZXSJ":0,"ZZSJID":"YN4008","ZFWB":"","MATNR":"110101000603","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"（液奶专用）2024龙年定制帆布袋","UEBTO":0,"ZLJID":"YN","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.080000,"MEINS":"EA","MODEL":"","ZYJFL":"营销品-入仓","BCODE":"6970037317034","ZYN":"","MATKLT":"非卖品","MSTAE":"","MATKL":"10500","ZZB_DB":""},{"XCHPF":"","ZPLFL":"","MAXLZ":365,"ZWIDTH":0.330000,"ZAMOUNT":"","MEINST":"个","ZJJXX":0,"ZEJFL":"定制赠品","GEWEI":"KG","ZCW_UNIT":"EA","ZSJID":"","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"","ZZB_GHL":"","MAKTX":"(奶粉专用)2024龙年定制帆布袋","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.125,"MBEW":1.00,"STPRS":0,"ZSJFL":"纺织品","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-14","ZSL":0.13,"ZZSJFL":"其他","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.125,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"05:36:29","ZLENGTH":0.310000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.008184,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000605","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"2024龙年定制帆布袋","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.080000,"MEINS":"EA","MODEL":"","ZYJFL":"营销品-入仓","BCODE":"69110101000605","ZYN":"","MATKLT":"非卖品","MSTAE":"","MATKL":"10500","ZZB_DB":""},{"XCHPF":"X","ZPLFL":"","MAXLZ":180,"ZWIDTH":0.035000,"ZAMOUNT":"","MEINST":"盒","ZJJXX":0,"ZEJFL":"200柳叶","GEWEI":"KG","ZCW_UNIT":"HE","ZSJID":"","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"103020401","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"200柳叶有机纯-单盒","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.200,"MBEW":1.00,"STPRS":0,"ZSJFL":"有机纯","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-18","ZSL":0.09,"ZZSJFL":"200有机纯","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.006,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"06:05:21","ZLENGTH":0.046000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.000192,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000609","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"200柳叶有机纯16入-单盒","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.119000,"MEINS":"HE","MODEL":"","ZYJFL":"常温白奶","BCODE":"69110101000609","ZYN":"","MATKLT":"纯牛奶-柳叶包200ml","MSTAE":"","MATKL":"10105","ZZB_DB":""},{"XCHPF":"X","ZPLFL":"纯奶","MAXLZ":180,"ZWIDTH":0.295000,"ZAMOUNT":"250ML","MEINST":"箱","ZJJXX":120.00,"ZEJFL":"250砖","GEWEI":"KG","ZCW_UNIT":"TI","ZSJID":"YN3031","ZPACKAGING":"利乐砖","ZGUIGE2":6.000,"ZSJB":"线下开窗款","ZSSFL":"103020401","ZPRODUCT_DL":"液奶","ZZB_GHL":"120G/100ML","MAKTX":"250砖普通纯（线下）12入-6提","LAEDA":"2025-05-09","ERNAMT":"BOZHI","BRGEW":21.440,"MBEW":137.16,"STPRS":0,"ZSJFL":"普通纯","ZZB_ZF":"3.6G/100ML","BRAND":"认养一头牛","ERSDA":"2020-08-11","ZSL":0.09,"ZZSJFL":"250普通纯","MTARTT":"产成品","ZGUIGE":12.000,"ZFHJC":"1*6开窗纯","NTGEW":18.648,"ZA1_PX":"04","ZZBXZHZ":"全脂","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"250纯砖线下","ZEJID":"YN2027","CREATED":"13:46:56","ZLENGTH":0.500000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.044693,"ZYJID":"YN1009","ZBZXSJ":269.40,"ZZSJID":"YN4046","ZFWB":"原味","MATNR":"110101100601","ERNAM":"BOZHI","AENAM":"C011802","BRNAM":"250mL利乐砖纯奶（线下版）六提装","UEBTO":0,"ZLJID":"YN","ZCWUNIT":"","ZJJSX":420.00,"ZHIGH":0.303000,"MEINS":"CS","MODEL":"250ML*12*6","ZYJFL":"常温白奶","BCODE":"6970037310172","ZYN":"普通奶","MATKLT":"纯牛奶-利乐砖250ml","MSTAE":"","MATKL":"10102","ZZB_DB":"3.3G/100ML"}]} 
2025-05-09 16:52:33.375 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Batch 1/1 response received 
2025-05-09 16:52:33.375 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Batch 1/1 processing 5 records 
2025-05-09 16:52:33.375 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Processing batch 1/1, with 5 records 
2025-05-09 16:52:33.375 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Batch 1/1 found 5 valid product codes 
2025-05-09 16:52:33.410 [transaction_Worker-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><31591072922888393306112> SQL:SELECT id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater FROM t_ryytn_master_sku WHERE product_code IN ( /*__frch_productCode_0*/'110101000600' , /*__frch_productCode_1*/'110101000603' , /*__frch_productCode_2*/'110101000605' , /*__frch_productCode_3*/'110101000609' , /*__frch_productCode_4*/'110101100601' )    cost=28 
2025-05-09 16:52:33.411 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Batch 1/1 found 5 existing master SKUs in database 
2025-05-09 16:52:33.512 [transaction_Worker-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><31591072922888393306112> SQL:UPDATE t_ryytn_master_sku SET product_code = /*__frch_item_0.productCode*/'110101000600', product_name = /*__frch_item_0.productName*/'250利乐峰A2纯（卡片款）10入-2提', update_time = /*__frch_item_0.updateTime*/'2025-05-09 16:52:33.411', material_type = /*__frch_item_0.materialType*/'ZERT', material_type_desc = /*__frch_item_0.materialTypeDesc*/'产成品', material_group = /*__frch_item_0.materialGroup*/'10112', material_group_desc = /*__frch_item_0.materialGroupDesc*/'纯牛奶-利乐峰250ml', base_unit_desc = /*__frch_item_0.baseUnitDesc*/'箱', gross_weight = /*__frch_item_0.grossWeight*/6.640, net_weight = /*__frch_item_0.netWeight*/5.180, weight_unit = /*__frch_item_0.weightUnit*/'KG', barcode = /*__frch_item_0.barcode*/'69110101000600', brand = /*__frch_item_0.brand*/'认养一头牛', short_name = /*__frch_item_0.shortName*/'1*2 250利乐峰（卡片款）', model_spec = /*__frch_item_0.modelSpec*/'', shelf_life = /*__frch_item_0.shelfLife*/180, length = /*__frch_item_0.length*/0.334000, width = /*__frch_item_0.width*/0.290000, height = /*__frch_item_0.height*/0.222000, volume = /*__frch_item_0.volume*/0.021503, batch_management_flag = /*__frch_item_0.batchManagementFlag*/'X', layer11_unit = /*__frch_item_0.layer11Unit*/'TI', layer9_quantity = /*__frch_item_0.layer9Quantity*/10.000, layer10_quantity = /*__frch_item_0.layer10Quantity*/2.000, tax_classification = /*__frch_item_0.taxClassification*/'', sn_enabled = /*__frch_item_0.snEnabled*/'N', traceability_enabled = /*__frch_item_0.traceabilityEnabled*/'N', primary_category = /*__frch_item_0.primaryCategory*/'常温白奶', secondary_category = /*__frch_item_0.secondaryCategory*/'250利乐峰', tertiary_category = /*__frch_item_0.tertiaryCategory*/'A2纯', quaternary_category = /*__frch_item_0.quaternaryCategory*/'梦幻盖A2纯', off_market_status = /*__frch_item_0.offMarketStatus*/'', off_market_date = /*__frch_item_0.offMarketDate*/null, product_status = /*__frch_item_0.productStatus*/'', zero_level_desc = /*__frch_item_0.zeroLevelDesc*/'液奶', zero_level_code = /*__frch_item_0.zeroLevelCode*/'', sap_create_date = /*__frch_item_0.sapCreateDate*/'2023-12-11', sap_create_time = /*__frch_item_0.sapCreateTime*/'02:42:25', sap_update_date = /*__frch_item_0.sapUpdateDate*/'2025-05-09', sap_updater = /*__frch_item_0.sapUpdater*/'C011802' WHERE id = /*__frch_item_0.id*/'576087942581985280' ; UPDATE t_ryytn_master_sku SET product_code = /*__frch_item_1.productCode*/'110101000603', product_name = /*__frch_item_1.productName*/'(液奶专用)2024龙年定制帆布袋', update_time = /*__frch_item_1.updateTime*/'2025-05-09 16:52:33.411', material_type = /*__frch_item_1.materialType*/'ZERT', material_type_desc = /*__frch_item_1.materialTypeDesc*/'产成品', material_group = /*__frch_item_1.materialGroup*/'10500', material_group_desc = /*__frch_item_1.materialGroupDesc*/'非卖品', base_unit_desc = /*__frch_item_1.baseUnitDesc*/'个', gross_weight = /*__frch_item_1.grossWeight*/0.125, net_we...4042...    cost=53 
2025-05-09 16:52:33.512 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Batch 1/1 updated 1 existing master SKUs 
2025-05-09 16:52:33.512 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Batch 1/1 completed, success: 5, fail: 0 
2025-05-09 16:52:33.512 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Process master SKU data completed, total success: 5, total fail: 0, total inserted: 0, total updated: 1 
2025-05-09 16:52:33.512 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Batch 1/1 processed 5 records, total processed so far: 5 
2025-05-09 16:52:33.513 [transaction_Worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><31591072922888393306112> Sync master SKU from SAP completed, processed 5 records in total 
2025-05-09 16:52:33.513 [transaction_Worker-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><31591072922888393306112> Exit className:[cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl],methodName:[syncMasterSkuFromSap],param:[[20250508, null]],spend-ms:[386] 
2025-05-09 16:52:33.513 [transaction_Worker-2] INFO  c.a.r.m.m.task.SyncMasterSkuFromSapTaskServiceImpl - <0><31591072922888393306112> Sync master SKU from SAP scheduled task completed successfully 
2025-05-09 16:52:33.513 [transaction_Worker-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><31591072922888393306112> Exit className:[cn.aliyun.ryytn.modules.master.task.SyncMasterSkuFromSapTaskServiceImpl],methodName:[process],param:[[SchedulerJob(jobId=576066760335966208, jobName=从SAP同步主SKU信息定时任务, jobType=1, startDate=Fri May 09 15:23:38 CST 2025, endDate=null, jobConf=0 0 1 * * ?, className=cn.aliyun.ryytn.modules.master.task.SyncMasterSkuFromSapTaskServiceImpl, param=null, serviceId=null, misfirePolicy=null, concurrent=false, status=1, description=null, createdBy=null, createdTime=2025-05-09 15:23:38.468893, updatedBy=null, updatedTime=null)]],spend-ms:[2973] 
2025-05-09 16:52:34.404 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler 
2025-05-09 16:52:34.413 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-05-09 16:52:34.422 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-05-09 16:52:34.424 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ... 
2025-05-09 16:52:34.426 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed 
