2025-05-16 15:43:48.255 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-16 15:43:48.270 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-16 15:43:48.274 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-16 15:43:48.290 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 15194 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-16 15:43:48.291 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-16 15:43:50.257 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-16 15:43:50.260 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-16 15:43:50.289 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces. 
2025-05-16 15:43:50.535 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-16 15:43:50.687 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@7726e185 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-16 15:43:50.735 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 15:43:50.754 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 15:43:50.757 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 15:43:50.759 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 15:43:50.848 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-16 15:43:50.848 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-16 15:43:50.848 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-16 15:43:50.848 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-16 15:43:50.861 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-16 15:43:50.862 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-16 15:43:50.862 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-16 15:43:51.899 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-16 15:43:51.909 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-16 15:43:51.910 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-16 15:43:51.910 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-16 15:43:52.002 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-16 15:43:52.002 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3667 ms 
2025-05-16 15:43:52.631 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-16 15:43:53.227 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-16 15:43:55.235 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-16 15:43:55.242 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-16 15:43:55.246 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-16 15:43:55.786 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@4cc3b263] is availabel 
2025-05-16 15:43:55.788 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@4cc3b263] is availabel 
2025-05-16 15:43:58.443 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-16 15:43:59.328 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e1fe74c4-fa7f-4db2-98f6-3461dee8ec11
 
2025-05-16 15:43:59.434 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-16 15:43:59.435 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-16 15:43:59.490 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7d43c89f, org.springframework.security.web.context.SecurityContextPersistenceFilter@73b034ca, org.springframework.security.web.header.HeaderWriterFilter@3ba6707e, org.springframework.security.web.authentication.logout.LogoutFilter@3b4b2c03, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5eaa4ed0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@251db193, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@149ef64a, org.springframework.security.web.session.SessionManagementFilter@30d3f583, org.springframework.security.web.access.ExceptionTranslationFilter@58fbecde] 
2025-05-16 15:44:00.576 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-16 15:44:00.615 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-16 15:44:03.810 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-16 15:44:04.026 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=116 
2025-05-16 15:44:04.819 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-16 15:44:05.541 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=17 
2025-05-16 15:44:05.736 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@36b1b28],spend-ms:[1327] 
2025-05-16 15:44:07.222 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[722] 
2025-05-16 15:44:08.211 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=19 
2025-05-16 15:44:08.265 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@54214c34],spend-ms:[2436] 
2025-05-16 15:44:08.309 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=37 
2025-05-16 15:44:08.431 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=57 
2025-05-16 15:44:08.457 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@42530cd6],spend-ms:[94] 
2025-05-16 15:44:08.485 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=24 
2025-05-16 15:44:08.840 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-16 15:44:08.864 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4de6c6b9],spend-ms:[341] 
2025-05-16 15:44:09.687 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-16 15:44:10.108 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=409 
2025-05-16 15:44:10.469 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[312] 
2025-05-16 15:44:11.151 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=24 
2025-05-16 15:44:11.179 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@7e452d2b],spend-ms:[2222] 
2025-05-16 15:44:11.439 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[201] 
2025-05-16 15:44:11.838 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-16 15:44:11.877 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@102ee705],spend-ms:[447] 
2025-05-16 15:44:12.099 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-16 15:44:12.139 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@266e0341],spend-ms:[21] 
2025-05-16 15:44:12.485 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=26 
2025-05-16 15:44:12.513 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@51934be2],spend-ms:[11] 
2025-05-16 15:44:12.546 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 24.738 seconds (JVM running for 26.004) 
2025-05-16 15:44:13.078 [RMI TCP Connection(6)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-16 15:44:13.081 [RMI TCP Connection(6)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-16 15:44:13.088 [RMI TCP Connection(6)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms 
2025-05-16 15:45:00.477 [transaction_Worker-1] INFO  c.a.r.m.d.t.RereshAiFreightPlanVersionServiceImpl - <0><69031075442616124432384>  RereshAiFreightPlanVersionServiceImpl data start=========== 
2025-05-16 15:45:00.513 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><69031075442616124432384> SQL:delete from cdop_sys.t_ryytn_freight_version_list_third    cost=31 
2025-05-16 15:45:20.094 [http-nio-7001-exec-1] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - <0><69031075442697774948352> handleThrowable:org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing: public cn.aliyun.ryytn.common.entity.ResultInfo<java.lang.String> cn.aliyun.ryytn.modules.master.controller.MasterSkuController.syncMasterSkuFromSap(cn.aliyun.ryytn.modules.master.entity.vo.MasterSkuSyncByCodeRequest)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:163)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:133)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 
2025-05-16 15:45:20.107 [http-nio-7001-exec-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><69031075442697774948352> Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@6d68b737],spend-ms:[18] 
2025-05-16 15:45:36.226 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-16 15:45:36.236 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-16 15:45:36.238 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-16 15:45:36.249 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 15254 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-16 15:45:36.250 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-16 15:45:37.917 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-16 15:45:37.919 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-16 15:45:37.940 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces. 
2025-05-16 15:45:38.128 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-16 15:45:38.275 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@3fcdcf , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-16 15:45:38.315 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 15:45:38.328 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 15:45:38.330 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 15:45:38.332 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 15:45:38.397 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-16 15:45:38.397 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-16 15:45:38.397 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-16 15:45:38.397 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-16 15:45:38.408 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-16 15:45:38.409 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-16 15:45:38.410 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-16 15:45:39.163 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-16 15:45:39.172 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-16 15:45:39.173 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-16 15:45:39.173 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-16 15:45:39.255 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-16 15:45:39.256 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2965 ms 
2025-05-16 15:45:39.810 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-16 15:45:40.289 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-16 15:45:41.943 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-16 15:45:41.952 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-16 15:45:41.956 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-16 15:45:42.467 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@6721d62b] is availabel 
2025-05-16 15:45:42.469 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@6721d62b] is availabel 
2025-05-16 15:45:44.766 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-16 15:45:45.389 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 50acdb5a-23f1-480b-8006-38432408eb78
 
2025-05-16 15:45:45.468 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-16 15:45:45.468 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-16 15:45:45.506 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@49d5c751, org.springframework.security.web.context.SecurityContextPersistenceFilter@6b6bae60, org.springframework.security.web.header.HeaderWriterFilter@5dfc7b3f, org.springframework.security.web.authentication.logout.LogoutFilter@411e567e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2a41d17a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@381c826c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@40e90634, org.springframework.security.web.session.SessionManagementFilter@e594c46, org.springframework.security.web.access.ExceptionTranslationFilter@7ef34eb3] 
2025-05-16 15:45:46.109 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-16 15:45:46.142 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-16 15:45:48.982 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-16 15:45:49.192 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=131 
2025-05-16 15:45:49.989 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-16 15:45:50.136 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: detected 1 failed or restarted instances. 
2025-05-16 15:45:50.137 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: Scanning for instance "lijindeMacBook-Pro.local1747381433965"'s failed in-progress jobs. 
2025-05-16 15:45:50.227 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: ......Cleaned-up 1 other failed job(s). 
2025-05-16 15:45:50.604 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-16 15:45:50.774 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@5d6de24e],spend-ms:[1283] 
2025-05-16 15:45:51.604 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[622] 
2025-05-16 15:45:52.987 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=21 
2025-05-16 15:45:53.013 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@32c3102],spend-ms:[2153] 
2025-05-16 15:45:53.061 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=29 
2025-05-16 15:45:53.287 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-16 15:45:53.311 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@71da2e9e],spend-ms:[237] 
2025-05-16 15:45:53.342 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=25 
2025-05-16 15:45:53.763 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=19 
2025-05-16 15:45:53.790 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@56c45636],spend-ms:[407] 
2025-05-16 15:45:54.828 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-16 15:45:55.318 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=482 
2025-05-16 15:45:55.693 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[313] 
2025-05-16 15:45:56.315 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=27 
2025-05-16 15:45:56.342 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@5a9ee776],spend-ms:[2458] 
2025-05-16 15:45:56.633 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[211] 
2025-05-16 15:45:57.064 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=28 
2025-05-16 15:45:57.115 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@1276caf2],spend-ms:[497] 
2025-05-16 15:45:57.350 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-16 15:45:57.382 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@1310b98a],spend-ms:[21] 
2025-05-16 15:45:57.586 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-16 15:45:57.616 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4670caf6],spend-ms:[10] 
2025-05-16 15:45:57.643 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 21.804 seconds (JVM running for 22.721) 
2025-05-16 15:45:57.771 [RMI TCP Connection(5)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-16 15:45:57.774 [RMI TCP Connection(5)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-16 15:45:57.780 [RMI TCP Connection(5)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms 
2025-05-16 15:49:54.033 [http-nio-7001-exec-2] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - <0><40731075443846586425344> handleThrowable:org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing: public cn.aliyun.ryytn.common.entity.ResultInfo<java.lang.String> cn.aliyun.ryytn.modules.master.controller.MasterSkuController.syncMasterSkuFromSap(cn.aliyun.ryytn.modules.master.entity.vo.MasterSkuSyncByCodeRequest)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:163)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:133)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 
2025-05-16 15:49:54.048 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><40731075443846586425344> Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@1750bfdd],spend-ms:[19] 
2025-05-16 15:50:52.641 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.m.master.controller.MasterSkuController - <0><40731075443996667011072> Start syncing master SKU from SAP asynchronously, request: MasterSkuSyncByCodeRequest(productCodes=null) 
2025-05-16 15:50:52.653 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1747381540795_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1747381540795) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-16 15:50:52.660 [http-nio-7001-exec-3] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><40731075443996667011072> Exit className:[cn.aliyun.ryytn.modules.master.controller.MasterSkuController],methodName:[syncMasterSkuFromSap],param:[[{}]],spend-ms:[22953] 
2025-05-16 15:50:57.298 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><40731075443996667011072> SQL:SELECT product_code FROM t_ryytn_master_sku    cost=29 
2025-05-16 15:51:03.488 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Using default sync time: 20250515 (yesterday) 
2025-05-16 15:51:03.494 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Start syncing master SKU for 5 products from SAP 
2025-05-16 15:51:03.500 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Processing batch 1/1, with 5 requests 
2025-05-16 15:51:09.547 [ForkJoinPool.commonPool-worker-1] INFO  cn.aliyun.ryytn.common.utils.sap.SapPushUtil - <0><40731075443996667011072> httpRequestSap.success requestBody:{"CTRL":{"DATUM":"2025-05-16","FUNID":"ZSCMINF001","INFID":"ZSCMINF001","KEYID":"b7a11e72-e7e2-4ca7-8cb5-2347058cb996","MD5":"","MSAGE":"","MSGTY":"","REVID":"SAP","SYSID":"SCM","UNAME":"SCM平台","UZEIT":"15:51:03"},"DATA":[{"MATNR":"110101100601"},{"MATNR":"110101000600"},{"MATNR":"110101000603"},{"MATNR":"110101000605"},{"MATNR":"110101000609"}]}, responseBody:{"CTRL":{"MSGTY":"S","UNAME":"SCM平台","DATUM":"2025-05-16","SYSID":"SCM","INFID":"ZSCMINF001","PAGE_NO":0,"FUNID":"ZSCMINF001","PAGE_SIZE":0,"METHOD":"","REVID":"SAP","TABIX":0,"MSAGE":"OK共5条","KEYID":"b7a11e72-e7e2-4ca7-8cb5-2347058cb996","UZEIT":"15:51:03","MD5":""},"DATA":[{"XCHPF":"X","ZPLFL":"","MAXLZ":180,"ZWIDTH":0.290000,"ZAMOUNT":"","MEINST":"箱","ZJJXX":0,"ZEJFL":"250利乐峰","GEWEI":"KG","ZCW_UNIT":"TI","ZSJID":"","ZPACKAGING":"","ZGUIGE2":2.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"250利乐峰A2纯（卡片款）10入-2提","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":6.640,"MBEW":1.00,"STPRS":0,"ZSJFL":"A2纯","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-11","ZSL":0.09,"ZZSJFL":"梦幻盖A2纯","MTARTT":"产成品","ZGUIGE":10.000,"ZFHJC":"","NTGEW":5.180,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"02:42:25","ZLENGTH":0.334000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.021503,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000600","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"1*2 250利乐峰（卡片款）","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.222000,"MEINS":"CS","MODEL":"","ZYJFL":"常温白奶","BCODE":"69110101000600","ZYN":"","MATKLT":"纯牛奶-利乐峰250ml","MSTAE":"","MATKL":"10112","ZZB_DB":""},{"XCHPF":"","ZPLFL":"","MAXLZ":365,"ZWIDTH":0.330000,"ZAMOUNT":"","MEINST":"个","ZJJXX":0,"ZEJFL":"定制赠品","GEWEI":"KG","ZCW_UNIT":"EA","ZSJID":"YN3012","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"(液奶专用)2024龙年定制帆布袋","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.125,"MBEW":4.60,"STPRS":0,"ZSJFL":"纺织品","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-14","ZSL":0.13,"ZZSJFL":"其他","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.125,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"YN2008","CREATED":"01:13:43","ZLENGTH":0.310000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.008184,"ZYJID":"YN1003","ZBZXSJ":0,"ZZSJID":"YN4008","ZFWB":"","MATNR":"110101000603","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"（液奶专用）2024龙年定制帆布袋","UEBTO":0,"ZLJID":"YN","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.080000,"MEINS":"EA","MODEL":"","ZYJFL":"营销品-入仓","BCODE":"6970037317034","ZYN":"","MATKLT":"非卖品","MSTAE":"","MATKL":"10500","ZZB_DB":""},{"XCHPF":"","ZPLFL":"","MAXLZ":365,"ZWIDTH":0.330000,"ZAMOUNT":"","MEINST":"个","ZJJXX":0,"ZEJFL":"定制赠品","GEWEI":"KG","ZCW_UNIT":"EA","ZSJID":"","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"","ZZB_GHL":"","MAKTX":"(奶粉专用)2024龙年定制帆布袋","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.125,"MBEW":1.00,"STPRS":0,"ZSJFL":"纺织品","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-14","ZSL":0.13,"ZZSJFL":"其他","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.125,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"05:36:29","ZLENGTH":0.310000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.008184,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000605","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"2024龙年定制帆布袋","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.080000,"MEINS":"EA","MODEL":"","ZYJFL":"营销品-入仓","BCODE":"69110101000605","ZYN":"","MATKLT":"非卖品","MSTAE":"","MATKL":"10500","ZZB_DB":""},{"XCHPF":"X","ZPLFL":"","MAXLZ":180,"ZWIDTH":0.035000,"ZAMOUNT":"","MEINST":"盒","ZJJXX":0,"ZEJFL":"200柳叶","GEWEI":"KG","ZCW_UNIT":"HE","ZSJID":"","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"103020401","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"200柳叶有机纯-单盒","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.200,"MBEW":1.00,"STPRS":0,"ZSJFL":"有机纯","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-18","ZSL":0.09,"ZZSJFL":"200有机纯","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.006,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"06:05:21","ZLENGTH":0.046000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.000192,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000609","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"200柳叶有机纯16入-单盒","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.119000,"MEINS":"HE","MODEL":"","ZYJFL":"常温白奶","BCODE":"69110101000609","ZYN":"","MATKLT":"纯牛奶-柳叶包200ml","MSTAE":"","MATKL":"10105","ZZB_DB":""},{"XCHPF":"X","ZPLFL":"纯奶","MAXLZ":180,"ZWIDTH":0.295000,"ZAMOUNT":"250ML","MEINST":"箱","ZJJXX":120.00,"ZEJFL":"250砖","GEWEI":"KG","ZCW_UNIT":"TI","ZSJID":"YN3031","ZPACKAGING":"利乐砖","ZGUIGE2":6.000,"ZSJB":"线下开窗款","ZSSFL":"103020401","ZPRODUCT_DL":"液奶","ZZB_GHL":"120G/100ML","MAKTX":"250砖普通纯（线下）12入-6提","LAEDA":"2025-05-09","ERNAMT":"BOZHI","BRGEW":21.440,"MBEW":137.16,"STPRS":0,"ZSJFL":"普通纯","ZZB_ZF":"3.6G/100ML","BRAND":"认养一头牛","ERSDA":"2020-08-11","ZSL":0.09,"ZZSJFL":"250普通纯","MTARTT":"产成品","ZGUIGE":12.000,"ZFHJC":"1*6开窗纯","NTGEW":18.648,"ZA1_PX":"04","ZZBXZHZ":"全脂","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"250纯砖线下","ZEJID":"YN2027","CREATED":"13:46:56","ZLENGTH":0.500000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.044693,"ZYJID":"YN1009","ZBZXSJ":269.40,"ZZSJID":"YN4046","ZFWB":"原味","MATNR":"110101100601","ERNAM":"BOZHI","AENAM":"C011802","BRNAM":"250mL利乐砖纯奶（线下版）六提装","UEBTO":0,"ZLJID":"YN","ZCWUNIT":"","ZJJSX":420.00,"ZHIGH":0.303000,"MEINS":"CS","MODEL":"250ML*12*6","ZYJFL":"常温白奶","BCODE":"6970037310172","ZYN":"普通奶","MATKLT":"纯牛奶-利乐砖250ml","MSTAE":"","MATKL":"10102","ZZB_DB":"3.3G/100ML"}]} 
2025-05-16 15:51:22.113 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Batch 1/1 response received 
2025-05-16 15:51:22.114 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Batch 1/1 processing 5 records 
2025-05-16 15:51:22.115 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Processing batch 1/1, with 5 records 
2025-05-16 15:51:22.115 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Batch 1/1 found 5 valid product codes 
2025-05-16 15:51:22.164 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><40731075443996667011072> SQL:SELECT id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater FROM t_ryytn_master_sku WHERE product_code IN ( /*__frch_productCode_0*/'110101000600' , /*__frch_productCode_1*/'110101000603' , /*__frch_productCode_2*/'110101000605' , /*__frch_productCode_3*/'110101000609' , /*__frch_productCode_4*/'110101100601' )    cost=32 
2025-05-16 15:51:22.165 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Batch 1/1 found 5 existing master SKUs in database 
2025-05-16 15:51:22.237 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><40731075443996667011072> SQL:UPDATE t_ryytn_master_sku SET product_code = /*__frch_item_0.productCode*/'110101000600', product_name = /*__frch_item_0.productName*/'250利乐峰A2纯（卡片款）10入-2提', update_time = /*__frch_item_0.updateTime*/'2025-05-16 15:51:22.166', material_type = /*__frch_item_0.materialType*/'ZERT', material_type_desc = /*__frch_item_0.materialTypeDesc*/'产成品', material_group = /*__frch_item_0.materialGroup*/'10112', material_group_desc = /*__frch_item_0.materialGroupDesc*/'纯牛奶-利乐峰250ml', base_unit_desc = /*__frch_item_0.baseUnitDesc*/'箱', gross_weight = /*__frch_item_0.grossWeight*/6.640, net_weight = /*__frch_item_0.netWeight*/5.180, weight_unit = /*__frch_item_0.weightUnit*/'KG', barcode = /*__frch_item_0.barcode*/'69110101000600', brand = /*__frch_item_0.brand*/'认养一头牛', short_name = /*__frch_item_0.shortName*/'1*2 250利乐峰（卡片款）', model_spec = /*__frch_item_0.modelSpec*/'', shelf_life = /*__frch_item_0.shelfLife*/180, length = /*__frch_item_0.length*/0.334000, width = /*__frch_item_0.width*/0.290000, height = /*__frch_item_0.height*/0.222000, volume = /*__frch_item_0.volume*/0.021503, batch_management_flag = /*__frch_item_0.batchManagementFlag*/'X', layer11_unit = /*__frch_item_0.layer11Unit*/'TI', layer9_quantity = /*__frch_item_0.layer9Quantity*/10.000, layer10_quantity = /*__frch_item_0.layer10Quantity*/2.000, tax_classification = /*__frch_item_0.taxClassification*/'', sn_enabled = /*__frch_item_0.snEnabled*/'N', traceability_enabled = /*__frch_item_0.traceabilityEnabled*/'N', primary_category = /*__frch_item_0.primaryCategory*/'常温白奶', secondary_category = /*__frch_item_0.secondaryCategory*/'250利乐峰', tertiary_category = /*__frch_item_0.tertiaryCategory*/'A2纯', quaternary_category = /*__frch_item_0.quaternaryCategory*/'梦幻盖A2纯', off_market_status = /*__frch_item_0.offMarketStatus*/'', off_market_date = /*__frch_item_0.offMarketDate*/null, product_status = /*__frch_item_0.productStatus*/'', zero_level_desc = /*__frch_item_0.zeroLevelDesc*/'液奶', zero_level_code = /*__frch_item_0.zeroLevelCode*/'', sap_create_date = /*__frch_item_0.sapCreateDate*/'2023-12-11', sap_create_time = /*__frch_item_0.sapCreateTime*/'02:42:25', sap_update_date = /*__frch_item_0.sapUpdateDate*/'2025-05-09', sap_updater = /*__frch_item_0.sapUpdater*/'C011802' WHERE id = /*__frch_item_0.id*/'576087942581985280' ; UPDATE t_ryytn_master_sku SET product_code = /*__frch_item_1.productCode*/'110101000603', product_name = /*__frch_item_1.productName*/'(液奶专用)2024龙年定制帆布袋', update_time = /*__frch_item_1.updateTime*/'2025-05-16 15:51:22.166', material_type = /*__frch_item_1.materialType*/'ZERT', material_type_desc = /*__frch_item_1.materialTypeDesc*/'产成品', material_group = /*__frch_item_1.materialGroup*/'10500', material_group_desc = /*__frch_item_1.materialGroupDesc*/'非卖品', base_unit_desc = /*__frch_item_1.baseUnitDesc*/'个', gross_weight = /*__frch_item_1.grossWeight*/0.125, net_we...4042...    cost=30 
2025-05-16 15:51:22.238 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Batch 1/1 updated 1 existing master SKUs 
2025-05-16 15:51:22.238 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Batch 1/1 completed, success: 5, fail: 0 
2025-05-16 15:51:22.238 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Process master SKU data completed, total success: 5, total fail: 0, total inserted: 0, total updated: 1 
2025-05-16 15:51:22.238 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Batch 1/1 processed 5 records, total processed so far: 5 
2025-05-16 15:51:22.239 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075443996667011072> Sync master SKU from SAP completed, processed 5 records in total 
2025-05-16 15:51:22.239 [ForkJoinPool.commonPool-worker-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><40731075443996667011072> Exit className:[cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl],methodName:[syncMasterSkuFromSapByProductCode],param:[[{}]],spend-ms:[29595] 
2025-05-16 15:51:22.240 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.m.master.controller.MasterSkuController - <0><40731075443996667011072> Async sync master SKU from SAP completed successfully 
2025-05-16 15:54:20.200 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.m.master.controller.MasterSkuController - <0><40731075444912015138816> Start syncing master SKU from SAP asynchronously, request: MasterSkuSyncByCodeRequest(productCodes=[110101000600, 110101000603, 110101000605, 110101100601, 110101000609, 104000000001, 104000000002]) 
2025-05-16 15:54:20.202 [http-nio-7001-exec-6] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><40731075444912015138816> Exit className:[cn.aliyun.ryytn.modules.master.controller.MasterSkuController],methodName:[syncMasterSkuFromSap],param:[[{"productCodes":["110101000600","110101000603","110101000605","110101100601","110101000609","104000000001","104000000002"]}]],spend-ms:[12319] 
2025-05-16 15:54:35.046 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Using default sync time: 20250515 (yesterday) 
2025-05-16 15:54:35.052 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Start syncing master SKU for 7 products from SAP 
2025-05-16 15:54:47.643 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Processing batch 1/1, with 7 requests 
2025-05-16 15:54:49.760 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1747381540795_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1747381540795) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-16 15:54:51.607 [ForkJoinPool.commonPool-worker-2] INFO  cn.aliyun.ryytn.common.utils.sap.SapPushUtil - <0><40731075444912015138816> httpRequestSap.success requestBody:{"CTRL":{"DATUM":"2025-05-16","FUNID":"ZSCMINF001","INFID":"ZSCMINF001","KEYID":"5732496d-7c6a-4f06-85ad-fb030fd10ae1","MD5":"","MSAGE":"","MSGTY":"","REVID":"SAP","SYSID":"SCM","UNAME":"SCM平台","UZEIT":"15:54:49"},"DATA":[{"MATNR":"110101000600"},{"MATNR":"110101000603"},{"MATNR":"110101000605"},{"MATNR":"110101100601"},{"MATNR":"110101000609"},{"MATNR":"104000000001"},{"MATNR":"104000000002"}]}, responseBody:{"CTRL":{"MSGTY":"S","UNAME":"SCM平台","DATUM":"2025-05-16","SYSID":"SCM","INFID":"ZSCMINF001","PAGE_NO":0,"FUNID":"ZSCMINF001","PAGE_SIZE":0,"METHOD":"","REVID":"SAP","TABIX":0,"MSAGE":"OK共7条","KEYID":"5732496d-7c6a-4f06-85ad-fb030fd10ae1","UZEIT":"15:54:49","MD5":""},"DATA":[{"XCHPF":"X","ZPLFL":"成人粉","MAXLZ":720,"ZWIDTH":0.289000,"ZAMOUNT":"800G","MEINST":"箱","ZJJXX":354.00,"ZEJFL":"800G罐","GEWEI":"KG","ZCW_UNIT":"KAN","ZSJID":"","ZPACKAGING":"罐装","ZGUIGE2":6.000,"ZSJB":"","ZSSFL":"*********","ZPRODUCT_DL":"奶粉","ZZB_GHL":"780MG/100G","MAKTX":"【冻结】全脂营养奶粉800G快递单罐-6罐","LAEDA":"2025-03-31","ERNAMT":"BOZHI","BRGEW":5.600,"MBEW":240.00,"STPRS":0,"ZSJFL":"全脂脱脂奶粉","ZZB_ZF":"29.0G/100G","BRAND":"认养一头牛","ERSDA":"2020-08-21","ZSL":0.13,"ZZSJFL":"全脂营养奶粉800G","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"1*6全脂奶粉","NTGEW":4.800,"ZA1_PX":"13","ZZBXZHZ":"全脂高钙","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"全脂奶粉","ZEJID":"","CREATED":"07:06:55","ZLENGTH":0.420000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.025975,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"原味","MATNR":"104000000001","ERNAM":"BOZHI","AENAM":"C010494","BRNAM":"800g全脂奶粉六罐装","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":714.00,"ZHIGH":0.214000,"MEINS":"CS","MODEL":"","ZYJFL":"基础奶粉","BCODE":"69104000000001","ZYN":"普通奶","MATKLT":"奶粉-听装800g","MSTAE":"","MATKL":"10401","ZZB_DB":"23.5G/100G"},{"XCHPF":"X","ZPLFL":"成人粉","MAXLZ":720,"ZWIDTH":0,"ZAMOUNT":"800G","MEINST":"箱","ZJJXX":0,"ZEJFL":"800G罐","GEWEI":"KG","ZCW_UNIT":"KAN","ZSJID":"","ZPACKAGING":"罐装","ZGUIGE2":6.000,"ZSJB":"","ZSSFL":"*********","ZPRODUCT_DL":"奶粉","ZZB_GHL":"780MG/100G","MAKTX":"【冻结】全脂营养奶粉800G裸礼盒-6罐","LAEDA":"2025-03-31","ERNAMT":"BOZHI","BRGEW":6.000,"MBEW":240.00,"STPRS":0,"ZSJFL":"全脂脱脂奶粉","ZZB_ZF":"29.0G/100G","BRAND":"认养一头牛","ERSDA":"2020-08-21","ZSL":0.13,"ZZSJFL":"全脂营养奶粉800G","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":4.800,"ZA1_PX":"13","ZZBXZHZ":"全脂高钙","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"全脂奶粉","ZEJID":"","CREATED":"07:06:55","ZLENGTH":0,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"原味","MATNR":"104000000002","ERNAM":"BOZHI","AENAM":"C010494","BRNAM":"800g全脂奶粉六罐装礼盒款","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0,"MEINS":"CS","MODEL":"","ZYJFL":"基础奶粉","BCODE":"6970037311391","ZYN":"普通奶","MATKLT":"奶粉-听装800g","MSTAE":"","MATKL":"10401","ZZB_DB":"23.5G/100G"},{"XCHPF":"X","ZPLFL":"","MAXLZ":180,"ZWIDTH":0.290000,"ZAMOUNT":"","MEINST":"箱","ZJJXX":0,"ZEJFL":"250利乐峰","GEWEI":"KG","ZCW_UNIT":"TI","ZSJID":"","ZPACKAGING":"","ZGUIGE2":2.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"250利乐峰A2纯（卡片款）10入-2提","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":6.640,"MBEW":1.00,"STPRS":0,"ZSJFL":"A2纯","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-11","ZSL":0.09,"ZZSJFL":"梦幻盖A2纯","MTARTT":"产成品","ZGUIGE":10.000,"ZFHJC":"","NTGEW":5.180,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"02:42:25","ZLENGTH":0.334000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.021503,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000600","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"1*2 250利乐峰（卡片款）","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.222000,"MEINS":"CS","MODEL":"","ZYJFL":"常温白奶","BCODE":"69110101000600","ZYN":"","MATKLT":"纯牛奶-利乐峰250ml","MSTAE":"","MATKL":"10112","ZZB_DB":""},{"XCHPF":"","ZPLFL":"","MAXLZ":365,"ZWIDTH":0.330000,"ZAMOUNT":"","MEINST":"个","ZJJXX":0,"ZEJFL":"定制赠品","GEWEI":"KG","ZCW_UNIT":"EA","ZSJID":"YN3012","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"(液奶专用)2024龙年定制帆布袋","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.125,"MBEW":4.60,"STPRS":0,"ZSJFL":"纺织品","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-14","ZSL":0.13,"ZZSJFL":"其他","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.125,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"YN2008","CREATED":"01:13:43","ZLENGTH":0.310000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.008184,"ZYJID":"YN1003","ZBZXSJ":0,"ZZSJID":"YN4008","ZFWB":"","MATNR":"110101000603","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"（液奶专用）2024龙年定制帆布袋","UEBTO":0,"ZLJID":"YN","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.080000,"MEINS":"EA","MODEL":"","ZYJFL":"营销品-入仓","BCODE":"6970037317034","ZYN":"","MATKLT":"非卖品","MSTAE":"","MATKL":"10500","ZZB_DB":""},{"XCHPF":"","ZPLFL":"","MAXLZ":365,"ZWIDTH":0.330000,"ZAMOUNT":"","MEINST":"个","ZJJXX":0,"ZEJFL":"定制赠品","GEWEI":"KG","ZCW_UNIT":"EA","ZSJID":"","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"","ZPRODUCT_DL":"","ZZB_GHL":"","MAKTX":"(奶粉专用)2024龙年定制帆布袋","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.125,"MBEW":1.00,"STPRS":0,"ZSJFL":"纺织品","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-14","ZSL":0.13,"ZZSJFL":"其他","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.125,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"05:36:29","ZLENGTH":0.310000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.008184,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000605","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"2024龙年定制帆布袋","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.080000,"MEINS":"EA","MODEL":"","ZYJFL":"营销品-入仓","BCODE":"69110101000605","ZYN":"","MATKLT":"非卖品","MSTAE":"","MATKL":"10500","ZZB_DB":""},{"XCHPF":"X","ZPLFL":"","MAXLZ":180,"ZWIDTH":0.035000,"ZAMOUNT":"","MEINST":"盒","ZJJXX":0,"ZEJFL":"200柳叶","GEWEI":"KG","ZCW_UNIT":"HE","ZSJID":"","ZPACKAGING":"","ZGUIGE2":1.000,"ZSJB":"","ZSSFL":"103020401","ZPRODUCT_DL":"液奶","ZZB_GHL":"","MAKTX":"200柳叶有机纯-单盒","LAEDA":"2025-05-09","ERNAMT":"RFC_CONN","BRGEW":0.200,"MBEW":1.00,"STPRS":0,"ZSJFL":"有机纯","ZZB_ZF":"","BRAND":"认养一头牛","ERSDA":"2023-12-18","ZSL":0.09,"ZZSJFL":"200有机纯","MTARTT":"产成品","ZGUIGE":1.000,"ZFHJC":"","NTGEW":0.006,"ZA1_PX":"","ZZBXZHZ":"","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"","ZEJID":"","CREATED":"06:05:21","ZLENGTH":0.046000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.000192,"ZYJID":"","ZBZXSJ":0,"ZZSJID":"","ZFWB":"","MATNR":"110101000609","ERNAM":"RFC_CONN","AENAM":"C011802","BRNAM":"200柳叶有机纯16入-单盒","UEBTO":0,"ZLJID":"","ZCWUNIT":"","ZJJSX":0,"ZHIGH":0.119000,"MEINS":"HE","MODEL":"","ZYJFL":"常温白奶","BCODE":"69110101000609","ZYN":"","MATKLT":"纯牛奶-柳叶包200ml","MSTAE":"","MATKL":"10105","ZZB_DB":""},{"XCHPF":"X","ZPLFL":"纯奶","MAXLZ":180,"ZWIDTH":0.295000,"ZAMOUNT":"250ML","MEINST":"箱","ZJJXX":120.00,"ZEJFL":"250砖","GEWEI":"KG","ZCW_UNIT":"TI","ZSJID":"YN3031","ZPACKAGING":"利乐砖","ZGUIGE2":6.000,"ZSJB":"线下开窗款","ZSSFL":"103020401","ZPRODUCT_DL":"液奶","ZZB_GHL":"120G/100ML","MAKTX":"250砖普通纯（线下）12入-6提","LAEDA":"2025-05-09","ERNAMT":"BOZHI","BRGEW":21.440,"MBEW":137.16,"STPRS":0,"ZSJFL":"普通纯","ZZB_ZF":"3.6G/100ML","BRAND":"认养一头牛","ERSDA":"2020-08-11","ZSL":0.09,"ZZSJFL":"250普通纯","MTARTT":"产成品","ZGUIGE":12.000,"ZFHJC":"1*6开窗纯","NTGEW":18.648,"ZA1_PX":"04","ZZBXZHZ":"全脂","ZSFSY":"N","ZZB_T":"","ZXSRQ":"0000-00-00","ZA2_JC":"250纯砖线下","ZEJID":"YN2027","CREATED":"13:46:56","ZLENGTH":0.500000,"ZXSZT":"","ZSFSN":"N","MTART":"ZERT","ZVOLUME":0.044693,"ZYJID":"YN1009","ZBZXSJ":269.40,"ZZSJID":"YN4046","ZFWB":"原味","MATNR":"110101100601","ERNAM":"BOZHI","AENAM":"C011802","BRNAM":"250mL利乐砖纯奶（线下版）六提装","UEBTO":0,"ZLJID":"YN","ZCWUNIT":"","ZJJSX":420.00,"ZHIGH":0.303000,"MEINS":"CS","MODEL":"250ML*12*6","ZYJFL":"常温白奶","BCODE":"6970037310172","ZYN":"普通奶","MATKLT":"纯牛奶-利乐砖250ml","MSTAE":"","MATKL":"10102","ZZB_DB":"3.3G/100ML"}]} 
2025-05-16 15:55:27.143 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Batch 1/1 response received 
2025-05-16 15:55:27.147 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Batch 1/1 processing 7 records 
2025-05-16 15:55:27.148 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Processing batch 1/1, with 7 records 
2025-05-16 15:55:27.148 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Batch 1/1 found 7 valid product codes 
2025-05-16 15:55:27.180 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1747381540795_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1747381540795) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-16 15:55:27.246 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><40731075444912015138816> SQL:SELECT id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater FROM t_ryytn_master_sku WHERE product_code IN ( /*__frch_productCode_0*/'104000000001' , /*__frch_productCode_1*/'104000000002' , /*__frch_productCode_2*/'110101000600' , /*__frch_productCode_3*/'110101000603' , /*__frch_productCode_4*/'110101000605' , /*__frch_productCode_5*/'110101000609' , /*__frch_productCode_6*/'110101100601' )    cost=39 
2025-05-16 15:55:27.255 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Batch 1/1 found 5 existing master SKUs in database 
2025-05-16 15:55:27.300 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><40731075444912015138816> SQL:INSERT INTO t_ryytn_master_sku ( id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater ) VALUES ( /*__frch_item_0.id*/'578611481438310400', /*__frch_item_0.productCode*/'104000000001', /*__frch_item_0.productName*/'【冻结】全脂营养奶粉800G快递单罐-6罐', /*__frch_item_0.createTime*/'2025-05-16 15:55:27.255', /*__frch_item_0.updateTime*/'2025-05-16 15:55:27.255', /*__frch_item_0.materialType*/'ZERT', /*__frch_item_0.materialTypeDesc*/'产成品', /*__frch_item_0.materialGroup*/'10401', /*__frch_item_0.materialGroupDesc*/'奶粉-听装800g', /*__frch_item_0.baseUnitDesc*/'箱', /*__frch_item_0.grossWeight*/5.600, /*__frch_item_0.netWeight*/4.800, /*__frch_item_0.weightUnit*/'KG', /*__frch_item_0.barcode*/'69104000000001', /*__frch_item_0.brand*/'认养一头牛', /*__frch_item_0.shortName*/'800g全脂奶粉六罐装', /*__frch_item_0.modelSpec*/'', /*__frch_item_0.shelfLife*/720, /*__frch_item_0.length*/0.420000, /*__frch_item_0.width*/0.289000, /*__frch_item_0.height*/0.214000, /*__frch_item_0.volume*/0.025975, /*__frch_item_0.batchManagementFlag*/'X', /*__frch_item_0.layer11Unit*/'KAN', /*__frch_item_0.layer9Quantity*/1.000, /*__frch_item_0.layer10Quantity*/6.000, /*__frch_item_0.taxClassification*/'*********', /*__frch_item_0.snEnabled*/'N', /*__frch_item_0.traceabilityEnabled*/'N', /*__frch_item_0.primaryCategory*/'基础奶粉', /*__frch_item_0.secondaryCategory*/'800G罐', /*__frch_item_0.tertiaryCategory*/'全脂脱脂奶粉', /*__frch_item_0.quaternaryCategory*/'全脂营养奶粉800G', /*__frch_item_0.offMarketStatus*/'', /*__frch_item_0.offMarketDate*/null, /*__frch_item_0.productStatus*/'', /*__frch_item_0.zeroLevelDesc*/'奶粉', /*__frch_item_0.zeroLevelCode*/'', /*__frch_item_0.sapCreateDate*/'2020-08-21', /*__frch_item_0.sapCreateTime*/'07:06:55', /*__frch_item_0.sapUpdateDate*/'2025-03-31', /*__frch_item_0.sapUpdater*/'C010494' ) , ( /*__frch_item_1.id*/'578611481438310401', /*__frch_item_1.productCode*/'104000000002', /*__frch_item_1.productName*/'【冻结】全脂营养奶粉800G裸礼盒-6罐', /*__frch_item_1.createTime*/'2025-05-16 15:55:27.255', /*__frch_item_1.updateTime*/'2025-05-16 15:55:27.255', /*__frch_item_1.materialType*/'ZERT', /*__frch_item_1.materialTypeDesc*/'产成品', /*__frch_item_1.materialGroup*/'10401', /*__frch_item_1.materialGroupDesc*/'奶粉-听装800g', /*__frch_item_1.baseUnitDesc*/'箱', /*__frch_item_1.grossWeight*/6.000, /*__frch_item_1.netWeight*/4.800, /*__frch_item_1.weightUnit*/'KG', /*__frch_item_1.barcode*/'6970037311391', /*__frch_item_1.brand*/'认养一头牛', /*__frch_item_1.shortName*/'800g全脂奶粉六罐装礼盒款', /*__frch_item_1.modelSpec*/'', /*__frch_item_1.shelfLife*/720, /*__frch_item_1.length*/0, /*__frch_item_1.width*/0, /*__frch_item_1.height*/0, /*__frch_item_1.volume*/0, /*__frch_item_1.batchManagementFlag*/'X', /*__frch_item_1.layer11Unit*/'KAN', /*__frch_item_1.layer9Quantity*/1.000, /*__frch_item_1.layer10Quantity*/6.000, /*__frch_item_1.taxClassification*/'*********', /*__frch_item_1.snEnabled*/'N', /*__frch_item_1.traceabilityEnabled*/'N', /*__frch_item_1.primaryCategory*/'基础奶粉', /*__frch_item_1.secondaryCategory*/'800G罐', /*__frch_item_1.tertiaryCategory*/'全脂脱脂奶粉', /*__frch_item_1.quaternaryCategory*/'全脂营养奶粉800G', /*__frch_item_1.offMarketStatus*/'', /*__frch_item_1.offMarketDate*/null, /*__frch_item_1.productStatus*/'', /*__frch_item_1.zeroLevelDesc*/'奶粉', /*__frch_item_1.zeroLevelCode*/'', /*__frch_item_1.sapCreateDate*/'2020-08-21', /*__frch_item_1.sapCreateTime*/'07:06:55', /*__frch_item_1.sapUpdateDate*/'2025-03-31', /*__frch_item_1.sapUpdater*/'C010494' )    cost=31 
2025-05-16 15:55:27.301 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Batch 1/1 inserted 2 new master SKUs 
2025-05-16 15:55:27.366 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><40731075444912015138816> SQL:UPDATE t_ryytn_master_sku SET product_code = /*__frch_item_0.productCode*/'110101000600', product_name = /*__frch_item_0.productName*/'250利乐峰A2纯（卡片款）10入-2提', update_time = /*__frch_item_0.updateTime*/'2025-05-16 15:55:27.255', material_type = /*__frch_item_0.materialType*/'ZERT', material_type_desc = /*__frch_item_0.materialTypeDesc*/'产成品', material_group = /*__frch_item_0.materialGroup*/'10112', material_group_desc = /*__frch_item_0.materialGroupDesc*/'纯牛奶-利乐峰250ml', base_unit_desc = /*__frch_item_0.baseUnitDesc*/'箱', gross_weight = /*__frch_item_0.grossWeight*/6.640, net_weight = /*__frch_item_0.netWeight*/5.180, weight_unit = /*__frch_item_0.weightUnit*/'KG', barcode = /*__frch_item_0.barcode*/'69110101000600', brand = /*__frch_item_0.brand*/'认养一头牛', short_name = /*__frch_item_0.shortName*/'1*2 250利乐峰（卡片款）', model_spec = /*__frch_item_0.modelSpec*/'', shelf_life = /*__frch_item_0.shelfLife*/180, length = /*__frch_item_0.length*/0.334000, width = /*__frch_item_0.width*/0.290000, height = /*__frch_item_0.height*/0.222000, volume = /*__frch_item_0.volume*/0.021503, batch_management_flag = /*__frch_item_0.batchManagementFlag*/'X', layer11_unit = /*__frch_item_0.layer11Unit*/'TI', layer9_quantity = /*__frch_item_0.layer9Quantity*/10.000, layer10_quantity = /*__frch_item_0.layer10Quantity*/2.000, tax_classification = /*__frch_item_0.taxClassification*/'', sn_enabled = /*__frch_item_0.snEnabled*/'N', traceability_enabled = /*__frch_item_0.traceabilityEnabled*/'N', primary_category = /*__frch_item_0.primaryCategory*/'常温白奶', secondary_category = /*__frch_item_0.secondaryCategory*/'250利乐峰', tertiary_category = /*__frch_item_0.tertiaryCategory*/'A2纯', quaternary_category = /*__frch_item_0.quaternaryCategory*/'梦幻盖A2纯', off_market_status = /*__frch_item_0.offMarketStatus*/'', off_market_date = /*__frch_item_0.offMarketDate*/null, product_status = /*__frch_item_0.productStatus*/'', zero_level_desc = /*__frch_item_0.zeroLevelDesc*/'液奶', zero_level_code = /*__frch_item_0.zeroLevelCode*/'', sap_create_date = /*__frch_item_0.sapCreateDate*/'2023-12-11', sap_create_time = /*__frch_item_0.sapCreateTime*/'02:42:25', sap_update_date = /*__frch_item_0.sapUpdateDate*/'2025-05-09', sap_updater = /*__frch_item_0.sapUpdater*/'C011802' WHERE id = /*__frch_item_0.id*/'576087942581985280' ; UPDATE t_ryytn_master_sku SET product_code = /*__frch_item_1.productCode*/'110101000603', product_name = /*__frch_item_1.productName*/'(液奶专用)2024龙年定制帆布袋', update_time = /*__frch_item_1.updateTime*/'2025-05-16 15:55:27.255', material_type = /*__frch_item_1.materialType*/'ZERT', material_type_desc = /*__frch_item_1.materialTypeDesc*/'产成品', material_group = /*__frch_item_1.materialGroup*/'10500', material_group_desc = /*__frch_item_1.materialGroupDesc*/'非卖品', base_unit_desc = /*__frch_item_1.baseUnitDesc*/'个', gross_weight = /*__frch_item_1.grossWeight*/0.125, net_we...4042...    cost=37 
2025-05-16 15:55:27.367 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Batch 1/1 updated 1 existing master SKUs 
2025-05-16 15:55:27.367 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Batch 1/1 completed, success: 7, fail: 0 
2025-05-16 15:55:27.367 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Process master SKU data completed, total success: 7, total fail: 0, total inserted: 2, total updated: 1 
2025-05-16 15:55:27.367 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Batch 1/1 processed 7 records, total processed so far: 7 
2025-05-16 15:55:27.367 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><40731075444912015138816> Sync master SKU from SAP completed, processed 7 records in total 
2025-05-16 15:55:27.368 [ForkJoinPool.commonPool-worker-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><40731075444912015138816> Exit className:[cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl],methodName:[syncMasterSkuFromSapByProductCode],param:[[{"productCodes":["110101000600","110101000603","110101000605","110101100601","110101000609","104000000001","104000000002"]}]],spend-ms:[67163] 
2025-05-16 15:55:27.368 [ForkJoinPool.commonPool-worker-2] INFO  c.a.r.m.master.controller.MasterSkuController - <0><40731075444912015138816> Async sync master SKU from SAP completed successfully 
2025-05-16 15:56:02.675 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler 
2025-05-16 15:56:02.679 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-05-16 15:56:02.687 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-05-16 15:56:02.689 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ... 
2025-05-16 15:56:02.692 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed 
2025-05-16 16:19:27.282 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-16 16:19:27.294 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-16 16:19:27.296 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-16 16:19:27.307 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 15675 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-16 16:19:27.308 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-16 16:19:29.042 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-16 16:19:29.045 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-16 16:19:29.066 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces. 
2025-05-16 16:19:29.340 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-16 16:19:29.559 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@25243bc1 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-16 16:19:29.602 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 16:19:29.617 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 16:19:29.619 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 16:19:29.621 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-16 16:19:29.683 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-16 16:19:29.683 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-16 16:19:29.683 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-16 16:19:29.683 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-16 16:19:29.694 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-16 16:19:29.695 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-16 16:19:29.696 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-16 16:19:30.501 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-16 16:19:30.510 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-16 16:19:30.510 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-16 16:19:30.511 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-16 16:19:30.613 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-16 16:19:30.614 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3264 ms 
2025-05-16 16:19:31.170 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-16 16:19:31.663 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-16 16:19:33.414 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-16 16:19:33.423 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-16 16:19:33.427 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-16 16:19:33.972 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@6839a3e2] is availabel 
2025-05-16 16:19:33.976 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@6839a3e2] is availabel 
2025-05-16 16:19:36.531 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-16 16:19:37.170 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 1ec9eeba-1681-4af1-9c44-a3677f4eebf2
 
2025-05-16 16:19:37.248 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-16 16:19:37.249 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-16 16:19:37.286 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1a000fff, org.springframework.security.web.context.SecurityContextPersistenceFilter@1d2ce035, org.springframework.security.web.header.HeaderWriterFilter@67748053, org.springframework.security.web.authentication.logout.LogoutFilter@106c73ed, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@98a0842, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@60ae950f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c855ef0, org.springframework.security.web.session.SessionManagementFilter@3e3d8e6c, org.springframework.security.web.access.ExceptionTranslationFilter@505a48a2] 
2025-05-16 16:19:37.914 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-16 16:19:37.948 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-16 16:19:40.730 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-16 16:19:40.936 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=124 
2025-05-16 16:19:41.738 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-16 16:19:42.245 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=18 
2025-05-16 16:19:42.394 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@3fc2197d],spend-ms:[1194] 
2025-05-16 16:19:43.387 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[649] 
2025-05-16 16:19:44.396 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-16 16:19:44.455 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@3ca26839],spend-ms:[1958] 
2025-05-16 16:19:44.502 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=44 
2025-05-16 16:19:44.581 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=17 
2025-05-16 16:19:44.607 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@313b6907],spend-ms:[99] 
2025-05-16 16:19:44.633 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=22 
2025-05-16 16:19:45.000 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=18 
2025-05-16 16:19:45.023 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@2ea7e25d],spend-ms:[346] 
2025-05-16 16:19:45.857 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-16 16:19:46.178 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=311 
2025-05-16 16:19:46.639 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[412] 
2025-05-16 16:19:47.338 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-16 16:19:47.366 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@23ef226d],spend-ms:[2235] 
2025-05-16 16:19:47.665 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[235] 
2025-05-16 16:19:48.162 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-16 16:19:48.204 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@3def5336],spend-ms:[501] 
2025-05-16 16:19:48.426 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=24 
2025-05-16 16:19:48.458 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@20865ff2],spend-ms:[20] 
2025-05-16 16:19:48.667 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-16 16:19:48.692 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@630ae6ce],spend-ms:[18] 
2025-05-16 16:19:48.725 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 21.83 seconds (JVM running for 22.634) 
2025-05-16 16:19:49.148 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-16 16:19:49.149 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-16 16:19:49.157 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 8 ms 
2025-05-16 16:20:21.674 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.m.master.controller.MasterSkuController - <0><06671075451469167714304> Start syncing master SKU from SAP asynchronously, request: MasterSkuSyncByCodeRequest(productCodes=null) 
2025-05-16 16:20:21.694 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><06671075451469167714304> Exit className:[cn.aliyun.ryytn.modules.master.controller.MasterSkuController],methodName:[syncMasterSkuFromSap],param:[[null]],spend-ms:[10305] 
2025-05-16 16:20:21.783 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><06671075451469167714304> SQL:SELECT product_code FROM t_ryytn_master_sku    cost=21 
2025-05-16 16:20:21.783 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><06671075451469167714304> Using default sync time: 20250515 (yesterday) 
2025-05-16 16:20:21.785 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><06671075451469167714304> Start syncing master SKU for 7 products from SAP 
2025-05-16 16:20:21.787 [ForkJoinPool.commonPool-worker-1] INFO  c.a.r.modules.master.service.MasterSkuServiceImpl - <0><06671075451469167714304> Processing batch 1/1, with 7 requests 
