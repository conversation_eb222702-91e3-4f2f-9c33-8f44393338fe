2025-05-28 17:57:58.716 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-28 17:57:58.733 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-28 17:57:58.733 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-28 17:57:58.748 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 5741 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-28 17:57:58.750 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-28 17:58:00.741 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-28 17:58:00.745 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-28 17:58:00.769 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-28 17:58:01.049 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-28 17:58:01.209 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@7e8e8651 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-28 17:58:01.260 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 17:58:01.279 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 17:58:01.281 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 17:58:01.283 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 17:58:01.349 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-28 17:58:01.349 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-28 17:58:01.349 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-28 17:58:01.349 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-28 17:58:01.362 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-28 17:58:01.363 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-28 17:58:01.363 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-28 17:58:01.369 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 17:58:02.283 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-28 17:58:02.292 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-28 17:58:02.292 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-28 17:58:02.292 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-28 17:58:02.386 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-28 17:58:02.386 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3583 ms 
2025-05-28 17:58:03.007 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-28 17:58:03.561 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-28 17:58:05.260 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-28 17:58:05.266 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-28 17:58:05.270 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-28 17:58:05.706 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@4c8b9462] is availabel 
2025-05-28 17:58:05.708 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@4c8b9462] is availabel 
2025-05-28 17:58:07.497 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  Start Init System Enum  
2025-05-28 17:58:07.499 [main] ERROR c.a.r.m.inv.common.service.impl.CommonServiceImpl -  system enum init enumName com.cainiao.cntech.dsct.application.enums.FileBizEnum error com.cainiao.cntech.dsct.application.enums.FileBizEnum   
2025-05-28 17:58:07.502 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  End Init System Enum [fileBiz] 
2025-05-28 17:58:08.562 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-28 17:58:09.118 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 23fe4340-bbca-4c46-8c40-95acc795fa24
 
2025-05-28 17:58:09.174 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-28 17:58:09.174 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-28 17:58:09.206 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@dd26290, org.springframework.security.web.context.SecurityContextPersistenceFilter@729a98e9, org.springframework.security.web.header.HeaderWriterFilter@4e6881e, org.springframework.security.web.authentication.logout.LogoutFilter@2f32fe68, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@62a09668, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4caca86d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21d30ba5, org.springframework.security.web.session.SessionManagementFilter@7892635c, org.springframework.security.web.access.ExceptionTranslationFilter@1ed763aa] 
2025-05-28 17:58:09.946 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-28 17:58:09.970 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-28 17:58:12.662 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-28 17:58:12.858 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=131 
2025-05-28 17:58:13.670 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-28 17:58:14.576 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=43 
2025-05-28 17:58:14.732 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4374c051],spend-ms:[1554] 
2025-05-28 17:58:15.755 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[745] 
2025-05-28 17:58:17.146 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=29 
2025-05-28 17:58:17.174 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@3dbb7f60],spend-ms:[2346] 
2025-05-28 17:58:17.236 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=58 
2025-05-28 17:58:17.352 [pool-4-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-28 17:58:17.361 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=29 
2025-05-28 17:58:17.403 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@1ecf9602],spend-ms:[131] 
2025-05-28 17:58:17.446 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=36 
2025-05-28 17:58:17.482 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=24 
2025-05-28 17:58:17.502 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=18 
2025-05-28 17:58:17.593 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=39 
2025-05-28 17:58:17.679 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=48 
2025-05-28 17:58:17.712 [pool-4-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-28 17:58:18.135 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-28 17:58:18.175 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@436c910e],spend-ms:[668] 
2025-05-28 17:58:19.678 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-28 17:58:20.242 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=550 
2025-05-28 17:58:20.860 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[547] 
2025-05-28 17:58:21.730 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=36 
2025-05-28 17:58:21.771 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@d0da1d6],spend-ms:[3490] 
2025-05-28 17:58:22.112 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[248] 
2025-05-28 17:58:22.729 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=52 
2025-05-28 17:58:22.794 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@593ec306],spend-ms:[613] 
2025-05-28 17:58:23.166 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=43 
2025-05-28 17:58:23.222 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@2c720867],spend-ms:[20] 
2025-05-28 17:58:23.546 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=29 
2025-05-28 17:58:23.571 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@587e0acf],spend-ms:[10] 
2025-05-28 17:58:23.596 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 25.258 seconds (JVM running for 26.33) 
2025-05-28 17:58:24.295 [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-28 17:58:24.301 [RMI TCP Connection(4)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-28 17:58:24.304 [RMI TCP Connection(4)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms 
2025-05-28 19:35:30.187 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-28 19:35:30.198 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-28 19:35:30.199 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-28 19:35:30.211 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 6449 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-28 19:35:30.212 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-28 19:35:32.359 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-28 19:35:32.362 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-28 19:35:32.386 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-28 19:35:32.669 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-28 19:35:32.835 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@5710768a , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-28 19:35:32.888 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 19:35:32.905 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 19:35:32.908 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 19:35:32.910 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 19:35:32.973 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-28 19:35:32.973 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-28 19:35:32.973 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-28 19:35:32.973 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-28 19:35:32.986 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-28 19:35:32.987 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-28 19:35:32.987 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-28 19:35:32.992 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 19:35:33.891 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-28 19:35:33.899 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-28 19:35:33.900 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-28 19:35:33.900 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-28 19:35:33.994 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-28 19:35:33.995 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3736 ms 
2025-05-28 19:35:34.629 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-28 19:35:35.278 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-28 19:35:36.853 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-28 19:35:36.857 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-28 19:35:36.858 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-28 19:35:37.338 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@b2e5f21] is availabel 
2025-05-28 19:35:37.342 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@b2e5f21] is availabel 
2025-05-28 19:35:39.587 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  Start Init System Enum  
2025-05-28 19:35:39.591 [main] ERROR c.a.r.m.inv.common.service.impl.CommonServiceImpl -  system enum init enumName com.cainiao.cntech.dsct.application.enums.FileBizEnum error com.cainiao.cntech.dsct.application.enums.FileBizEnum   
2025-05-28 19:35:39.595 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  End Init System Enum [fileBiz] 
2025-05-28 19:35:41.463 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-28 19:35:42.621 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 2b3d8260-5b42-49dd-bd40-63bc5796b15c
 
2025-05-28 19:35:42.689 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-28 19:35:42.689 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-28 19:35:42.727 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@79690a0a, org.springframework.security.web.context.SecurityContextPersistenceFilter@627f678d, org.springframework.security.web.header.HeaderWriterFilter@445adbd7, org.springframework.security.web.authentication.logout.LogoutFilter@3129792a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6f13ed1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5ea50a98, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@619944a7, org.springframework.security.web.session.SessionManagementFilter@56d4481f, org.springframework.security.web.access.ExceptionTranslationFilter@549dfe1d] 
2025-05-28 19:35:43.505 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-28 19:35:43.533 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-28 19:35:46.637 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-28 19:35:46.860 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=100 
2025-05-28 19:35:47.642 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-28 19:35:48.128 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-28 19:35:48.361 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@72c8af69],spend-ms:[1147] 
2025-05-28 19:35:49.374 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-28 19:35:49.496 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=26 
2025-05-28 19:35:49.522 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=18 
2025-05-28 19:35:49.535 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[566] 
2025-05-28 19:35:49.621 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=23 
2025-05-28 19:35:49.693 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=25 
2025-05-28 19:35:49.711 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-28 19:35:50.683 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-28 19:35:50.707 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@713d2fe8],spend-ms:[2260] 
2025-05-28 19:35:50.752 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=26 
2025-05-28 19:35:50.834 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=17 
2025-05-28 19:35:50.857 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@2c7ceffa],spend-ms:[91] 
2025-05-28 19:35:50.884 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=23 
2025-05-28 19:35:51.217 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=18 
2025-05-28 19:35:51.236 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@526ee5e6],spend-ms:[331] 
2025-05-28 19:35:52.027 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-28 19:35:52.346 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=310 
2025-05-28 19:35:52.757 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[363] 
2025-05-28 19:35:53.292 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=38 
2025-05-28 19:35:53.325 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@620a2725],spend-ms:[1979] 
2025-05-28 19:35:53.554 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[181] 
2025-05-28 19:35:53.934 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-28 19:35:53.972 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@2c8d537e],spend-ms:[399] 
2025-05-28 19:35:54.192 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=28 
2025-05-28 19:35:54.217 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@136fea21],spend-ms:[18] 
2025-05-28 19:35:54.423 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=24 
2025-05-28 19:35:54.448 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@5b7b281],spend-ms:[6] 
2025-05-28 19:35:54.473 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 24.717 seconds (JVM running for 25.793) 
2025-05-28 19:35:54.709 [RMI TCP Connection(17)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-28 19:35:54.715 [RMI TCP Connection(17)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-28 19:35:54.721 [RMI TCP Connection(17)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms 
2025-05-28 19:37:49.716 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-28 19:37:49.768 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=25 
2025-05-28 19:37:49.799 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=21 
2025-05-28 19:37:49.829 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=25 
2025-05-28 19:37:49.860 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=27 
2025-05-28 19:37:49.879 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-28 19:39:49.886 [pool-3-thread-2] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-28 19:39:49.928 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=25 
2025-05-28 19:39:49.947 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=17 
2025-05-28 19:39:49.972 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=17 
2025-05-28 19:39:49.997 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=18 
2025-05-28 19:39:50.017 [pool-3-thread-2] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-28 19:40:00.089 [pool-3-thread-4] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><94581079850408562253824> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=18 
2025-05-28 19:40:48.434 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1748432135763_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: detected 1 failed or restarted instances. 
2025-05-28 19:40:48.435 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1748432135763_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: Scanning for instance "L-WV6Y621W961748431255789"'s failed in-progress jobs. 
2025-05-28 19:40:53.432 [http-nio-7001-exec-2] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - handleThrowable:org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1261)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1043)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 
2025-05-28 19:40:53.449 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@8730afd],spend-ms:[23] 
2025-05-28 19:41:50.022 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-28 19:41:50.067 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=25 
2025-05-28 19:41:50.090 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=21 
2025-05-28 19:41:50.117 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=18 
2025-05-28 19:41:50.139 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=18 
2025-05-28 19:41:50.155 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-28 19:41:51.996 [http-nio-7001-exec-3] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - handleThrowable:org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1261)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1043)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 
2025-05-28 19:41:51.998 [http-nio-7001-exec-3] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@4e89d67a],spend-ms:[1] 
2025-05-28 19:41:53.651 [http-nio-7001-exec-4] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - handleThrowable:org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1261)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1043)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 
2025-05-28 19:41:53.654 [http-nio-7001-exec-4] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@13e19955],spend-ms:[3] 
2025-05-28 19:42:33.670 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1748432135763_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: detected 1 failed or restarted instances. 
2025-05-28 19:42:33.671 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1748432135763_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: Scanning for instance "L-WV6Y621W961748432441458"'s failed in-progress jobs. 
2025-05-28 19:42:39.877 [http-nio-7001-exec-5] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - handleThrowable:org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1261)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1043)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 
2025-05-28 19:42:39.879 [http-nio-7001-exec-5] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@5728fec6],spend-ms:[1] 
2025-05-28 19:42:55.826 [http-nio-7001-exec-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><94581079851142003417088> SQL:select * from cdop_biz.tdm_common_constant WHERE type = /*type*/'quick_query_sku_repln_type'    cost=29 
2025-05-28 19:42:55.846 [http-nio-7001-exec-6] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><94581079851142003417088> Exit className:[cn.aliyun.ryytn.modules.inv.common.ability.constant.service.ConstantServiceImpl],methodName:[queryData],param:[[{"blur":"","code":[],"type":"quick_query_sku_repln_type"}]],spend-ms:[59] 
2025-05-28 19:42:56.043 [http-nio-7001-exec-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><94581079851142003417088> SQL:select * from cdop_biz.tdm_kcjh_sku_repln_type WHERE status = 1 order by case when repln_type is null then 0 else 1 end, gmt_modified desc    cost=193 
2025-05-28 19:42:56.047 [http-nio-7001-exec-6] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><94581079851142003417088> Exit className:[cn.aliyun.ryytn.modules.inv.service.business.InvSkuReplnTypeServiceImpl],methodName:[queryPageData],param:[[{"currentPage":1,"orderType":"asc","pageSize":10,"paging":false,"quickQueryClause":"all"}]],spend-ms:[275] 
2025-05-28 19:43:02.092 [http-nio-7001-exec-7] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><94581079851171275464704> SQL:select * from cdop_biz.tdm_common_constant WHERE type = /*type*/'quick_query_sku_repln_type'    cost=72 
2025-05-28 19:43:02.100 [http-nio-7001-exec-7] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><94581079851171275464704> Exit className:[cn.aliyun.ryytn.modules.inv.common.ability.constant.service.ConstantServiceImpl],methodName:[queryData],param:[[{"blur":"","code":[],"type":"quick_query_sku_repln_type"}]],spend-ms:[82] 
2025-05-28 19:43:02.406 [http-nio-7001-exec-7] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><94581079851171275464704> SQL:select * from cdop_biz.tdm_kcjh_sku_repln_type WHERE status = 1 order by case when repln_type is null then 0 else 1 end, gmt_modified desc    cost=304 
2025-05-28 19:43:02.408 [http-nio-7001-exec-7] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><94581079851171275464704> Exit className:[cn.aliyun.ryytn.modules.inv.service.business.InvSkuReplnTypeServiceImpl],methodName:[queryPageData],param:[[{"currentPage":1,"orderType":"asc","pageSize":10,"paging":false,"quickQueryClause":"all"}]],spend-ms:[392] 
2025-05-28 19:43:55.668 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-28 19:43:55.725 [http-nio-7001-exec-8] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><94581079851282256748544> SQL:select * from cdop_biz.tdm_common_constant WHERE type = /*type*/'quick_query_sku_repln_type'    cost=34 
2025-05-28 19:43:55.727 [http-nio-7001-exec-8] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><94581079851282256748544> Exit className:[cn.aliyun.ryytn.modules.inv.common.ability.constant.service.ConstantServiceImpl],methodName:[queryData],param:[[{"blur":"","code":[],"type":"quick_query_sku_repln_type"}]],spend-ms:[63] 
2025-05-28 19:43:55.747 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=19 
2025-05-28 19:43:55.776 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=25 
2025-05-28 19:43:55.798 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=20 
2025-05-28 19:43:55.827 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=21 
2025-05-28 19:43:55.847 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-28 19:43:56.961 [http-nio-7001-exec-8] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><94581079851282256748544> SQL:select * from cdop_biz.tdm_kcjh_sku_repln_type WHERE status = 1 order by case when repln_type is null then 0 else 1 end, gmt_modified desc    cost=1221 
2025-05-28 19:43:56.962 [http-nio-7001-exec-8] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><94581079851282256748544> Exit className:[cn.aliyun.ryytn.modules.inv.service.business.InvSkuReplnTypeServiceImpl],methodName:[queryPageData],param:[[{"currentPage":1,"orderType":"asc","pageSize":10,"paging":false,"quickQueryClause":"all"}]],spend-ms:[1299] 
2025-05-28 19:45:21.110 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1748432135763_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1748432135763) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-28 19:45:21.429 [pool-3-thread-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><94581079851755168718848> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=26 
