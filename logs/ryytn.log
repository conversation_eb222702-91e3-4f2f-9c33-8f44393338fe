2025-05-28 17:57:58.716 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-28 17:57:58.733 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-28 17:57:58.733 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-28 17:57:58.748 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 5741 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-28 17:57:58.750 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-28 17:58:00.741 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-28 17:58:00.745 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-28 17:58:00.769 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-28 17:58:01.049 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-28 17:58:01.209 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@7e8e8651 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-28 17:58:01.260 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 17:58:01.279 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 17:58:01.281 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 17:58:01.283 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 17:58:01.349 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-28 17:58:01.349 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-28 17:58:01.349 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-28 17:58:01.349 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-28 17:58:01.362 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-28 17:58:01.363 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-28 17:58:01.363 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-28 17:58:01.369 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-28 17:58:02.283 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-28 17:58:02.292 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-28 17:58:02.292 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-28 17:58:02.292 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-28 17:58:02.386 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-28 17:58:02.386 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3583 ms 
2025-05-28 17:58:03.007 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-28 17:58:03.561 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-28 17:58:05.260 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-28 17:58:05.266 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-28 17:58:05.270 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-28 17:58:05.706 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@4c8b9462] is availabel 
2025-05-28 17:58:05.708 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@4c8b9462] is availabel 
2025-05-28 17:58:07.497 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  Start Init System Enum  
2025-05-28 17:58:07.499 [main] ERROR c.a.r.m.inv.common.service.impl.CommonServiceImpl -  system enum init enumName com.cainiao.cntech.dsct.application.enums.FileBizEnum error com.cainiao.cntech.dsct.application.enums.FileBizEnum   
2025-05-28 17:58:07.502 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  End Init System Enum [fileBiz] 
2025-05-28 17:58:08.562 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-28 17:58:09.118 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 23fe4340-bbca-4c46-8c40-95acc795fa24
 
2025-05-28 17:58:09.174 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-28 17:58:09.174 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-28 17:58:09.206 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@dd26290, org.springframework.security.web.context.SecurityContextPersistenceFilter@729a98e9, org.springframework.security.web.header.HeaderWriterFilter@4e6881e, org.springframework.security.web.authentication.logout.LogoutFilter@2f32fe68, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@62a09668, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4caca86d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21d30ba5, org.springframework.security.web.session.SessionManagementFilter@7892635c, org.springframework.security.web.access.ExceptionTranslationFilter@1ed763aa] 
2025-05-28 17:58:09.946 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-28 17:58:09.970 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-28 17:58:12.662 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-28 17:58:12.858 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=131 
2025-05-28 17:58:13.670 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-28 17:58:14.576 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=43 
2025-05-28 17:58:14.732 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4374c051],spend-ms:[1554] 
2025-05-28 17:58:15.755 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[745] 
2025-05-28 17:58:17.146 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=29 
2025-05-28 17:58:17.174 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@3dbb7f60],spend-ms:[2346] 
2025-05-28 17:58:17.236 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=58 
2025-05-28 17:58:17.352 [pool-4-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-28 17:58:17.361 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=29 
2025-05-28 17:58:17.403 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@1ecf9602],spend-ms:[131] 
2025-05-28 17:58:17.446 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=36 
2025-05-28 17:58:17.482 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=24 
2025-05-28 17:58:17.502 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=18 
2025-05-28 17:58:17.593 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=39 
2025-05-28 17:58:17.679 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=48 
2025-05-28 17:58:17.712 [pool-4-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-28 17:58:18.135 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-28 17:58:18.175 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@436c910e],spend-ms:[668] 
2025-05-28 17:58:19.678 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-28 17:58:20.242 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=550 
2025-05-28 17:58:20.860 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[547] 
2025-05-28 17:58:21.730 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=36 
2025-05-28 17:58:21.771 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@d0da1d6],spend-ms:[3490] 
2025-05-28 17:58:22.112 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[248] 
2025-05-28 17:58:22.729 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=52 
2025-05-28 17:58:22.794 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@593ec306],spend-ms:[613] 
2025-05-28 17:58:23.166 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=43 
2025-05-28 17:58:23.222 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@2c720867],spend-ms:[20] 
2025-05-28 17:58:23.546 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=29 
2025-05-28 17:58:23.571 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@587e0acf],spend-ms:[10] 
2025-05-28 17:58:23.596 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 25.258 seconds (JVM running for 26.33) 
2025-05-28 17:58:24.295 [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-28 17:58:24.301 [RMI TCP Connection(4)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-28 17:58:24.304 [RMI TCP Connection(4)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms 
