2025-05-29 10:44:04.118 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-29 10:44:04.133 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 10:44:04.133 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 10:44:04.135 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final 
2025-05-29 10:44:04.155 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 10257 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-29 10:44:04.157 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-29 10:44:06.227 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-29 10:44:06.230 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-29 10:44:06.255 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-29 10:44:06.620 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-29 10:44:06.782 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@3301500b , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-29 10:44:06.843 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 10:44:06.864 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 10:44:06.866 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 10:44:06.868 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 10:44:06.941 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-29 10:44:06.941 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-29 10:44:06.941 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-29 10:44:06.941 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-29 10:44:06.954 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-29 10:44:06.955 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-29 10:44:06.956 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-29 10:44:06.960 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 10:44:07.751 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-29 10:44:07.760 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-29 10:44:07.760 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-29 10:44:07.760 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-29 10:44:07.859 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-29 10:44:07.859 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3657 ms 
2025-05-29 10:44:08.701 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 10:44:09.414 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 10:44:10.835 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-29 10:44:10.843 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-29 10:44:10.845 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-29 10:44:11.463 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@6aee6374] is availabel 
2025-05-29 10:44:11.466 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@6aee6374] is availabel 
2025-05-29 10:44:13.180 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  Start Init System Enum  
2025-05-29 10:44:13.181 [main] ERROR c.a.r.m.inv.common.service.impl.CommonServiceImpl -  system enum init enumName com.cainiao.cntech.dsct.application.enums.FileBizEnum error com.cainiao.cntech.dsct.application.enums.FileBizEnum   
2025-05-29 10:44:13.183 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  End Init System Enum [fileBiz] 
2025-05-29 10:44:14.186 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-29 10:44:14.795 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 4de60ca6-fd40-4c55-88e9-3bc76b815079
 
2025-05-29 10:44:14.861 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-29 10:44:14.861 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-29 10:44:14.892 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2c2dfde3, org.springframework.security.web.context.SecurityContextPersistenceFilter@6edd8d56, org.springframework.security.web.header.HeaderWriterFilter@61f2bf3a, org.springframework.security.web.authentication.logout.LogoutFilter@68596992, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@40032b7b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2a5917f6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1131fcfd, org.springframework.security.web.session.SessionManagementFilter@352288bc, org.springframework.security.web.access.ExceptionTranslationFilter@64a0ee65] 
2025-05-29 10:44:15.687 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-29 10:44:15.710 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-29 10:44:18.476 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-29 10:44:18.674 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=124 
2025-05-29 10:44:19.478 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-29 10:44:20.213 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-29 10:44:20.369 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@35e55afa],spend-ms:[1371] 
2025-05-29 10:44:21.556 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[840] 
2025-05-29 10:44:22.842 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-29 10:44:22.898 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@10462124],spend-ms:[2413] 
2025-05-29 10:44:22.940 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=37 
2025-05-29 10:44:23.027 [pool-4-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 10:44:23.042 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-29 10:44:23.075 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@2ff19416],spend-ms:[96] 
2025-05-29 10:44:23.120 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=26 
2025-05-29 10:44:23.147 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=22 
2025-05-29 10:44:23.166 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=18 
2025-05-29 10:44:23.251 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=23 
2025-05-29 10:44:23.329 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=29 
2025-05-29 10:44:23.348 [pool-4-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 10:44:23.558 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-29 10:44:23.582 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@196cfaed],spend-ms:[437] 
2025-05-29 10:44:24.452 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-29 10:44:24.903 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=443 
2025-05-29 10:44:25.275 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[318] 
2025-05-29 10:44:26.003 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=26 
2025-05-29 10:44:26.034 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@13ed9738],spend-ms:[2356] 
2025-05-29 10:44:26.285 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[186] 
2025-05-29 10:44:26.807 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=28 
2025-05-29 10:44:26.853 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@5b2f8813],spend-ms:[494] 
2025-05-29 10:44:27.096 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=24 
2025-05-29 10:44:27.120 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@5f9b21a1],spend-ms:[17] 
2025-05-29 10:44:27.339 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=24 
2025-05-29 10:44:27.365 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@7f5a3e41],spend-ms:[6] 
2025-05-29 10:44:27.394 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 23.686 seconds (JVM running for 24.738) 
2025-05-29 10:44:27.688 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-29 10:44:27.692 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-29 10:44:27.702 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 10 ms 
2025-05-29 10:45:00.260 [pool-4-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><51891080078159244288000> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=25 
2025-05-29 10:45:00.320 [transaction_Worker-1] INFO  c.a.r.m.d.t.RereshAiFreightPlanVersionServiceImpl - <0><51891080078160301252608>  RereshAiFreightPlanVersionServiceImpl data start=========== 
2025-05-29 10:45:00.356 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><51891080078160301252608> SQL:delete from cdop_sys.t_ryytn_freight_version_list_third    cost=32 
2025-05-29 10:45:52.413 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><51891080078160301252608> SQL:insert into cdop_sys.t_ryytn_freight_version_list_third(prediction_version,demand_plan_code,create_time) select distinct prediction_version,demand_plan_code,now() from cdop_biz.tdm_kcjh_txn_freight_qty_di    cost=52055 
2025-05-29 10:45:52.446 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><51891080078160301252608> SQL:delete from cdop_sys.t_ryytn_freight_version_list    cost=26 
2025-05-29 10:45:52.475 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><51891080078160301252608> SQL:insert into cdop_sys.t_ryytn_freight_version_list(prediction_version,demand_plan_code,create_time) select prediction_version,demand_plan_code,create_time from cdop_sys.t_ryytn_freight_version_list_third    cost=28 
2025-05-29 10:45:52.506 [transaction_Worker-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><51891080078160301252608> Exit className:[cn.aliyun.ryytn.modules.demand.task.RereshAiFreightPlanVersionServiceImpl],methodName:[process],param:[[{"className":"cn.aliyun.ryytn.modules.demand.task.RereshAiFreightPlanVersionServiceImpl","concurrent":false,"createdTime":"2023-12-07 17:33:39.158","jobConf":"0 */15 * * * ?","jobId":"100000089","jobName":"定时同步调拨计划版本数据信息，供应用侧页面提升性能使用","jobType":1,"serviceId":"null","startDate":1730390400000,"status":1}]],spend-ms:[52160] 
2025-05-29 10:46:23.353 [pool-4-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 10:46:23.404 [pool-4-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=29 
2025-05-29 10:46:23.433 [pool-4-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=22 
2025-05-29 10:46:23.455 [pool-4-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=19 
2025-05-29 10:46:23.480 [pool-4-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=19 
2025-05-29 10:46:23.501 [pool-4-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 10:48:23.508 [pool-4-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 10:48:23.556 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=23 
2025-05-29 10:48:23.595 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=37 
2025-05-29 10:48:23.624 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=22 
2025-05-29 10:48:23.667 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=37 
2025-05-29 10:48:23.686 [pool-4-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:04:04.809 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-29 11:04:04.821 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 11:04:04.821 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 11:04:04.823 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final 
2025-05-29 11:04:04.841 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 10474 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-29 11:04:04.842 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-29 11:04:06.966 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-29 11:04:06.970 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-29 11:04:06.993 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-29 11:04:07.349 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-29 11:04:07.511 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@50d68830 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-29 11:04:07.572 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:04:07.587 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:04:07.589 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:04:07.592 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:04:07.661 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-29 11:04:07.662 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-29 11:04:07.662 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-29 11:04:07.662 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-29 11:04:07.674 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-29 11:04:07.676 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-29 11:04:07.678 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-29 11:04:07.682 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:04:08.448 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-29 11:04:08.456 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-29 11:04:08.457 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-29 11:04:08.457 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-29 11:04:08.560 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-29 11:04:08.560 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3668 ms 
2025-05-29 11:04:09.209 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 11:04:09.803 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 11:04:11.216 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-29 11:04:11.223 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-29 11:04:11.225 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-29 11:04:11.826 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@637838af] is availabel 
2025-05-29 11:04:11.830 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@637838af] is availabel 
2025-05-29 11:04:13.360 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  Start Init System Enum  
2025-05-29 11:04:13.360 [main] ERROR c.a.r.m.inv.common.service.impl.CommonServiceImpl -  system enum init enumName com.cainiao.cntech.dsct.application.enums.FileBizEnum error com.cainiao.cntech.dsct.application.enums.FileBizEnum   
2025-05-29 11:04:13.362 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  End Init System Enum [fileBiz] 
2025-05-29 11:04:14.393 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-29 11:04:15.079 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a3f2539e-1cab-46db-8fa0-4d6f681d47d6
 
2025-05-29 11:04:15.139 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-29 11:04:15.139 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-29 11:04:15.170 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@769d0ef2, org.springframework.security.web.context.SecurityContextPersistenceFilter@466f7259, org.springframework.security.web.header.HeaderWriterFilter@2f95ce11, org.springframework.security.web.authentication.logout.LogoutFilter@6fa48ee4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@64a0ee65, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3d94b49d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6d58ed12, org.springframework.security.web.session.SessionManagementFilter@35ec3604, org.springframework.security.web.access.ExceptionTranslationFilter@1cb4cad0] 
2025-05-29 11:04:15.903 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-29 11:04:15.926 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-29 11:04:18.607 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-29 11:04:18.766 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=95 
2025-05-29 11:04:19.614 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-29 11:04:20.177 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=26 
2025-05-29 11:04:20.326 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@1a6864f0],spend-ms:[1188] 
2025-05-29 11:04:21.269 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[738] 
2025-05-29 11:04:22.483 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-29 11:04:22.540 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@27ae4c06],spend-ms:[2090] 
2025-05-29 11:04:22.583 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=40 
2025-05-29 11:04:22.678 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=18 
2025-05-29 11:04:22.704 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@52416fd7],spend-ms:[103] 
2025-05-29 11:04:22.735 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=27 
2025-05-29 11:04:23.200 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=21 
2025-05-29 11:04:23.216 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:04:23.224 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@c98017f],spend-ms:[447] 
2025-05-29 11:04:23.298 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=25 
2025-05-29 11:04:23.325 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=23 
2025-05-29 11:04:23.418 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=21 
2025-05-29 11:04:23.485 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=25 
2025-05-29 11:04:23.503 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:04:24.069 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-29 11:04:24.420 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=345 
2025-05-29 11:04:24.827 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[336] 
2025-05-29 11:04:25.499 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=25 
2025-05-29 11:04:25.532 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@37890e3],spend-ms:[2207] 
2025-05-29 11:04:25.799 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[201] 
2025-05-29 11:04:26.250 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=30 
2025-05-29 11:04:26.287 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4002f09c],spend-ms:[452] 
2025-05-29 11:04:26.515 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=24 
2025-05-29 11:04:26.548 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@154ca57c],spend-ms:[19] 
2025-05-29 11:04:26.844 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=66 
2025-05-29 11:04:26.900 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4f9d220b],spend-ms:[7] 
2025-05-29 11:04:26.932 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 22.51 seconds (JVM running for 23.498) 
2025-05-29 11:04:27.329 [RMI TCP Connection(2)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-29 11:04:27.335 [RMI TCP Connection(2)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-29 11:04:27.341 [RMI TCP Connection(2)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms 
2025-05-29 11:05:00.098 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><52291080083192409088000> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=27 
2025-05-29 11:06:15.860 [http-nio-7001-exec-2] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - handleThrowable:org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1261)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1043)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 
2025-05-29 11:06:15.878 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@58c990da],spend-ms:[19] 
2025-05-29 11:06:23.494 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:06:23.535 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=22 
2025-05-29 11:06:23.556 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=18 
2025-05-29 11:06:23.580 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=19 
2025-05-29 11:06:23.603 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=17 
2025-05-29 11:06:23.620 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:08:23.627 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:08:23.668 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=25 
2025-05-29 11:08:23.693 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=23 
2025-05-29 11:08:23.716 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=19 
2025-05-29 11:08:23.747 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=22 
2025-05-29 11:08:23.766 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:10:00.016 [pool-3-thread-4] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><52291080084450452824064> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=26 
2025-05-29 11:10:23.772 [pool-3-thread-5] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:10:23.797 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=19 
2025-05-29 11:10:23.821 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=21 
2025-05-29 11:10:23.850 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=19 
2025-05-29 11:10:23.879 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=20 
2025-05-29 11:10:23.898 [pool-3-thread-5] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:12:06.665 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1748487850276_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1748487850276) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-29 11:13:22.665 [pool-3-thread-5] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:13:22.802 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1748487850276_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1748487850276) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-29 11:13:22.863 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=44 
2025-05-29 11:13:22.912 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=46 
2025-05-29 11:13:22.966 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=50 
2025-05-29 11:13:22.992 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=21 
2025-05-29 11:13:23.009 [pool-3-thread-5] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:15:00.649 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><52291080085708832104448> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=637 
2025-05-29 11:15:01.018 [transaction_Worker-1] INFO  c.a.r.m.d.t.RereshAiFreightPlanVersionServiceImpl - <0><52291080085712892190720>  RereshAiFreightPlanVersionServiceImpl data start=========== 
2025-05-29 11:15:01.059 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><52291080085712892190720> SQL:delete from cdop_sys.t_ryytn_freight_version_list_third    cost=36 
2025-05-29 11:15:23.014 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:15:23.086 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=28 
2025-05-29 11:15:23.126 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=38 
2025-05-29 11:15:23.154 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=24 
2025-05-29 11:15:23.186 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=24 
2025-05-29 11:15:23.206 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:16:16.214 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><52291080085712892190720> SQL:insert into cdop_sys.t_ryytn_freight_version_list_third(prediction_version,demand_plan_code,create_time) select distinct prediction_version,demand_plan_code,now() from cdop_biz.tdm_kcjh_txn_freight_qty_di    cost=75154 
2025-05-29 11:16:16.252 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1748487850276_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1748487850276) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-29 11:16:16.252 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><52291080085712892190720> SQL:delete from cdop_sys.t_ryytn_freight_version_list    cost=27 
2025-05-29 11:16:16.287 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><52291080085712892190720> SQL:insert into cdop_sys.t_ryytn_freight_version_list(prediction_version,demand_plan_code,create_time) select prediction_version,demand_plan_code,create_time from cdop_sys.t_ryytn_freight_version_list_third    cost=29 
2025-05-29 11:16:16.307 [transaction_Worker-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><52291080085712892190720> Exit className:[cn.aliyun.ryytn.modules.demand.task.RereshAiFreightPlanVersionServiceImpl],methodName:[process],param:[[{"className":"cn.aliyun.ryytn.modules.demand.task.RereshAiFreightPlanVersionServiceImpl","concurrent":false,"createdTime":"2023-12-07 17:33:39.158","jobConf":"0 */15 * * * ?","jobId":"100000089","jobName":"定时同步调拨计划版本数据信息，供应用侧页面提升性能使用","jobType":1,"serviceId":"null","startDate":1730390400000,"status":1}]],spend-ms:[75280] 
2025-05-29 11:17:23.212 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:17:23.257 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=23 
2025-05-29 11:17:23.280 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=19 
2025-05-29 11:17:23.379 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=92 
2025-05-29 11:17:23.414 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=26 
2025-05-29 11:17:23.434 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:19:23.439 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:19:23.467 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=25 
2025-05-29 11:19:23.487 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=19 
2025-05-29 11:19:23.506 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=17 
2025-05-29 11:19:23.526 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=19 
2025-05-29 11:19:23.543 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:20:00.046 [pool-3-thread-8] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><52291080086967110721536> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=33 
2025-05-29 11:21:23.558 [pool-3-thread-6] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:21:23.590 [pool-3-thread-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=28 
2025-05-29 11:21:23.613 [pool-3-thread-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=20 
2025-05-29 11:21:23.641 [pool-3-thread-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=18 
2025-05-29 11:21:23.669 [pool-3-thread-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=22 
2025-05-29 11:21:23.689 [pool-3-thread-6] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:23:23.693 [pool-3-thread-6] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:23:23.729 [pool-3-thread-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=18 
2025-05-29 11:23:23.754 [pool-3-thread-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=23 
2025-05-29 11:23:23.779 [pool-3-thread-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=23 
2025-05-29 11:23:23.810 [pool-3-thread-6] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=25 
2025-05-29 11:23:23.829 [pool-3-thread-6] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:25:18.980 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-29 11:25:18.992 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final 
2025-05-29 11:25:18.994 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 11:25:18.994 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 11:25:19.016 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 10761 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-29 11:25:19.017 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-29 11:25:21.320 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-29 11:25:21.323 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-29 11:25:21.345 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces. 
2025-05-29 11:25:21.691 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-29 11:25:21.860 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@77b325b3 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-29 11:25:21.928 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:25:21.945 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:25:21.947 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:25:21.949 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:25:22.019 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-29 11:25:22.020 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-29 11:25:22.020 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-29 11:25:22.020 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-29 11:25:22.033 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-29 11:25:22.035 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-29 11:25:22.036 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-29 11:25:22.040 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:25:22.820 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-29 11:25:22.829 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-29 11:25:22.829 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-29 11:25:22.830 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-29 11:25:22.941 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-29 11:25:22.942 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3865 ms 
2025-05-29 11:25:23.588 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 11:25:24.224 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 11:25:25.684 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-29 11:25:25.690 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-29 11:25:25.692 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-29 11:25:26.341 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@c822dad] is availabel 
2025-05-29 11:25:26.347 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@c822dad] is availabel 
2025-05-29 11:25:28.146 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  Start Init System Enum  
2025-05-29 11:25:28.148 [main] ERROR c.a.r.m.inv.common.service.impl.CommonServiceImpl -  system enum init enumName com.cainiao.cntech.dsct.application.enums.FileBizEnum error com.cainiao.cntech.dsct.application.enums.FileBizEnum   
2025-05-29 11:25:28.150 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  End Init System Enum [fileBiz] 
2025-05-29 11:25:29.216 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-29 11:25:29.892 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: c550f86c-d6e7-4228-9238-ffe47f0e0633
 
2025-05-29 11:25:29.950 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-29 11:25:29.950 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-29 11:25:29.981 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4dfe5727, org.springframework.security.web.context.SecurityContextPersistenceFilter@28c38eeb, org.springframework.security.web.header.HeaderWriterFilter@f111e97, org.springframework.security.web.authentication.logout.LogoutFilter@238cac6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@641001c2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6eac71db, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3129792a, org.springframework.security.web.session.SessionManagementFilter@5d5a77de, org.springframework.security.web.access.ExceptionTranslationFilter@76d4e1af] 
2025-05-29 11:25:30.669 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-29 11:25:30.692 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-29 11:25:34.166 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-29 11:25:34.340 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=105 
2025-05-29 11:25:35.173 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-29 11:25:35.644 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=21 
2025-05-29 11:25:35.807 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@41e5038a],spend-ms:[1078] 
2025-05-29 11:25:37.176 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[672] 
2025-05-29 11:25:38.000 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:25:38.111 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=20 
2025-05-29 11:25:38.128 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=16 
2025-05-29 11:25:38.221 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=21 
2025-05-29 11:25:38.307 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=36 
2025-05-29 11:25:38.327 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:25:38.425 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=19 
2025-05-29 11:25:38.449 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@1880dbb2],spend-ms:[2577] 
2025-05-29 11:25:38.502 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=38 
2025-05-29 11:25:38.584 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=17 
2025-05-29 11:25:38.606 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@1269538a],spend-ms:[103] 
2025-05-29 11:25:38.630 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=21 
2025-05-29 11:25:38.997 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-29 11:25:39.021 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@637ca94b],spend-ms:[342] 
2025-05-29 11:25:39.861 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-29 11:25:40.425 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=556 
2025-05-29 11:25:40.788 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[315] 
2025-05-29 11:25:41.318 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-29 11:25:41.348 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@7af66b8a],spend-ms:[2249] 
2025-05-29 11:25:41.431 [http-nio-7001-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-29 11:25:41.432 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-29 11:25:41.438 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms 
2025-05-29 11:25:41.601 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[202] 
2025-05-29 11:25:41.677 [http-nio-7001-exec-2] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - <0><96321080088399431983104> handleThrowable:com.cainiao.cntech.dsct.scp.gei.common.exception.AppException: SYSTEM_ERROR,系统异常，请稍后再试,数据导入模版获取失败，dataCode=productionSalesRelationEIportDataTemplate
	at com.cainiao.cntech.dsct.scp.gei.common.exception.Assert.notNull(Assert.java:29)
	at com.cainiao.cntech.dsct.scp.gei.core.GeiExcelManager.importData(GeiExcelManager.java:104)
	at com.cainiao.cntech.dsct.scp.gei.biz.web.GeiWebController.upload(GeiWebController.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 
2025-05-29 11:25:41.700 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><96321080088399431983104> Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@56196431],spend-ms:[26] 
2025-05-29 11:25:42.034 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-29 11:25:42.067 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@de58d71],spend-ms:[465] 
2025-05-29 11:25:42.296 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-29 11:25:42.328 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@15598336],spend-ms:[21] 
2025-05-29 11:25:42.521 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-29 11:25:42.555 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@433f9449],spend-ms:[5] 
2025-05-29 11:25:42.580 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 24.057 seconds (JVM running for 25.176) 
2025-05-29 11:27:38.335 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:27:38.385 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=24 
2025-05-29 11:27:38.418 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=20 
2025-05-29 11:27:38.439 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=18 
2025-05-29 11:27:38.462 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=20 
2025-05-29 11:27:38.480 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:27:53.100 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><96321080088826051420160> SQL:SELECT id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater FROM t_ryytn_master_sku WHERE product_code IN ( /*__frch_productCode_0*/'************' , /*__frch_productCode_1*/'************' )    cost=53 
2025-05-29 11:28:00.169 [pool-4-thread-1] WARN  c.a.r.m.m.e.ProductionSalesRelationEIportDataTemplate - <0><96321080088826051420160> 获取当前账户失败 
java.lang.NullPointerException: null
	at cn.aliyun.ryytn.modules.master.eiport.ProductionSalesRelationEIportDataTemplate.getCurrentAccount(ProductionSalesRelationEIportDataTemplate.java:282)
	at cn.aliyun.ryytn.modules.master.eiport.ProductionSalesRelationEIportDataTemplate.validate(ProductionSalesRelationEIportDataTemplate.java:264)
	at cn.aliyun.ryytn.modules.master.eiport.ProductionSalesRelationEIportDataTemplate.preValid(ProductionSalesRelationEIportDataTemplate.java:66)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.invokePreValid(ImportTemplateExecutor.java:523)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.validateImportData(ImportTemplateExecutor.java:449)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.invokeTemplate(ImportTemplateExecutor.java:289)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.lambda$invokeTemplateAndCreateTask$0(ImportTemplateExecutor.java:274)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:28:04.067 [pool-4-thread-1] WARN  c.a.r.m.m.s.ProductionSalesRelationServiceImpl - <0><96321080088826051420160> 获取当前用户失败，使用默认用户 
java.lang.NullPointerException: null
	at cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl.getCurrentUser(ProductionSalesRelationServiceImpl.java:244)
	at cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl.saveOrUpdate(ProductionSalesRelationServiceImpl.java:58)
	at cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl$$FastClassBySpringCGLIB$$c71e736b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at cn.aliyun.ryytn.starter.aspect.LogAspect.around(LogAspect.java:85)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)
	at cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl$$EnhancerBySpringCGLIB$$b480e963.saveOrUpdate(<generated>)
	at cn.aliyun.ryytn.modules.master.eiport.ProductionSalesRelationEIportDataTemplate.importData(ProductionSalesRelationEIportDataTemplate.java:96)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.invokeTemplate(ImportTemplateExecutor.java:298)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.lambda$invokeTemplateAndCreateTask$0(ImportTemplateExecutor.java:274)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:28:04.113 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><96321080088826051420160> SQL:SELECT COUNT(1) FROM t_ryytn_master_production_sales_relation WHERE sales_sku_id = /*salesSkuId*/'576087942581985280' AND production_sku_id = /*productionSkuId*/'576087942581985281' AND status = 1    cost=25 
2025-05-29 11:28:04.144 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><96321080088826051420160> SQL:INSERT INTO t_ryytn_master_production_sales_relation (id, sales_sku_id, production_sku_id, conversion_factor, created_by, updated_by, created_time, updated_time, status) VALUES (/*id*/'583255233876447232', /*salesSkuId*/'576087942581985280', /*productionSkuId*/'576087942581985281', /*conversionFactor*/1, /*createdBy*/'SYSTEM', /*updatedBy*/'SYSTEM', /*createdTime*/'2025-05-29 11:28:04.070', /*updatedTime*/'2025-05-29 11:28:04.070', /*status*/1)    cost=23 
2025-05-29 11:28:04.144 [pool-4-thread-1] INFO  c.a.r.m.m.s.ProductionSalesRelationServiceImpl - <0><96321080088826051420160> 新增生产销售关系成功，ID: 583255233876447232 
2025-05-29 11:28:04.159 [pool-4-thread-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><96321080088826051420160> Exit className:[cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl],methodName:[saveOrUpdate],param:[[{"conversionFactor":1,"productionSkuId":"576087942581985281","salesSkuId":"576087942581985280"}]],spend-ms:[108] 
2025-05-29 11:30:11.878 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-29 11:30:11.891 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 11:30:11.892 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 11:30:11.895 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final 
2025-05-29 11:30:11.911 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 10821 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-29 11:30:11.912 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-29 11:30:13.925 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-29 11:30:13.929 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-29 11:30:13.951 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-29 11:30:14.285 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-29 11:30:14.449 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@77b325b3 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-29 11:30:14.510 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:30:14.526 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:30:14.528 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:30:14.530 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:30:14.598 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-29 11:30:14.598 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-29 11:30:14.598 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-29 11:30:14.598 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-29 11:30:14.611 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-29 11:30:14.614 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-29 11:30:14.615 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-29 11:30:14.619 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:30:15.361 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-29 11:30:15.369 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-29 11:30:15.369 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-29 11:30:15.369 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-29 11:30:15.470 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-29 11:30:15.471 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3512 ms 
2025-05-29 11:30:16.098 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 11:30:16.686 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 11:30:18.110 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-29 11:30:18.118 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-29 11:30:18.120 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-29 11:30:18.719 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@50b26b08] is availabel 
2025-05-29 11:30:18.724 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@50b26b08] is availabel 
2025-05-29 11:30:20.295 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  Start Init System Enum  
2025-05-29 11:30:20.297 [main] ERROR c.a.r.m.inv.common.service.impl.CommonServiceImpl -  system enum init enumName com.cainiao.cntech.dsct.application.enums.FileBizEnum error com.cainiao.cntech.dsct.application.enums.FileBizEnum   
2025-05-29 11:30:20.299 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  End Init System Enum [fileBiz] 
2025-05-29 11:30:21.312 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-29 11:30:21.907 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 63a05a05-97ea-4dcf-bd7d-6422eb5e9f92
 
2025-05-29 11:30:21.963 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-29 11:30:21.963 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-29 11:30:21.997 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@60abe420, org.springframework.security.web.context.SecurityContextPersistenceFilter@36b6802f, org.springframework.security.web.header.HeaderWriterFilter@39240aa3, org.springframework.security.web.authentication.logout.LogoutFilter@4a9c188f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@28ab7ffb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@283240b0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21e60eed, org.springframework.security.web.session.SessionManagementFilter@2acfd27b, org.springframework.security.web.access.ExceptionTranslationFilter@61f18997] 
2025-05-29 11:30:22.713 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-29 11:30:22.734 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-29 11:30:25.595 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-29 11:30:25.765 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=103 
2025-05-29 11:30:26.603 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-29 11:30:27.131 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=19 
2025-05-29 11:30:27.284 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@763e1ec9],spend-ms:[1187] 
2025-05-29 11:30:28.037 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[610] 
2025-05-29 11:30:29.030 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-29 11:30:29.060 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@926d264],spend-ms:[1686] 
2025-05-29 11:30:29.352 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=286 
2025-05-29 11:30:29.432 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=20 
2025-05-29 11:30:29.453 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@52b3a9c6],spend-ms:[339] 
2025-05-29 11:30:29.481 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=25 
2025-05-29 11:30:29.830 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-29 11:30:29.857 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@3b7777ea],spend-ms:[333] 
2025-05-29 11:30:30.154 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:30:30.266 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=24 
2025-05-29 11:30:30.291 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=21 
2025-05-29 11:30:30.381 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=19 
2025-05-29 11:30:30.442 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=22 
2025-05-29 11:30:30.460 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:30:30.697 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-29 11:30:31.048 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=343 
2025-05-29 11:30:31.418 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[312] 
2025-05-29 11:30:32.064 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=25 
2025-05-29 11:30:32.096 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4be4680b],spend-ms:[2129] 
2025-05-29 11:30:32.360 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[205] 
2025-05-29 11:30:32.761 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-29 11:30:32.798 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@7d440b1f],spend-ms:[441] 
2025-05-29 11:30:33.018 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=29 
2025-05-29 11:30:33.045 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@6b22bd04],spend-ms:[16] 
2025-05-29 11:30:33.245 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-29 11:30:33.272 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@485fe2cd],spend-ms:[9] 
2025-05-29 11:30:33.297 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 21.821 seconds (JVM running for 22.713) 
2025-05-29 11:30:33.479 [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-29 11:30:33.484 [RMI TCP Connection(4)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-29 11:30:33.491 [RMI TCP Connection(4)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms 
2025-05-29 11:31:58.823 [http-nio-7001-exec-5] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - handleThrowable:org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1261)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1043)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 
2025-05-29 11:31:58.840 [http-nio-7001-exec-5] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@7242a136],spend-ms:[27] 
2025-05-29 11:32:27.645 [http-nio-7001-exec-6] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - handleThrowable:org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1261)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1043)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 
2025-05-29 11:32:27.650 [http-nio-7001-exec-6] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@438470bc],spend-ms:[5] 
2025-05-29 11:32:30.465 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:32:30.500 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=21 
2025-05-29 11:32:30.527 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=17 
2025-05-29 11:32:30.546 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=17 
2025-05-29 11:32:30.570 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=21 
2025-05-29 11:32:30.586 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:32:37.605 [http-nio-7001-exec-7] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48511080090144040148992> SQL:SELECT count(0) FROM t_ryytn_master_production_sales_relation    cost=21 
2025-05-29 11:32:37.630 [http-nio-7001-exec-7] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48511080090144040148992> SQL:SELECT CAST(id AS VARCHAR) as id, CAST(sales_sku_id AS VARCHAR) as sales_sku_id, CAST(production_sku_id AS VARCHAR) as production_sku_id, conversion_factor, created_by, updated_by, created_time, updated_time, status FROM t_ryytn_master_production_sales_relation ORDER BY id DESC    cost=24 
2025-05-29 11:32:37.666 [http-nio-7001-exec-7] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48511080090144040148992> SQL:SELECT id, product_code, product_name, primary_category, secondary_category, tertiary_category, quaternary_category, product_status, off_market_status, off_market_date, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater, create_time, update_time FROM t_ryytn_master_sku WHERE id IN ( /*__frch_id_0*/'576087942581985280' , /*__frch_id_1*/'576087942581985281' )    cost=29 
2025-05-29 11:32:37.691 [http-nio-7001-exec-7] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><48511080090144040148992> Exit className:[cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl],methodName:[queryPage],param:[[{"condition":{},"pageNum":1,"pageSize":2147483647}]],spend-ms:[113] 
2025-05-29 11:32:37.726 [http-nio-7001-exec-7] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48511080090144040148992> SQL:select * from cdop_biz.scp_gei_dict where code = /*code*/'' and type = /*type*/'EXPORT'    cost=20 
2025-05-29 11:34:30.593 [pool-3-thread-2] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:34:30.656 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=38 
2025-05-29 11:34:30.697 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=39 
2025-05-29 11:34:30.730 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=24 
2025-05-29 11:34:30.782 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=39 
2025-05-29 11:34:30.809 [pool-3-thread-2] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:35:00.093 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48511080090742143705088> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=20 
2025-05-29 11:36:30.814 [pool-3-thread-4] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:36:30.844 [pool-3-thread-4] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=19 
2025-05-29 11:36:30.864 [pool-3-thread-4] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=17 
2025-05-29 11:36:30.889 [pool-3-thread-4] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=22 
2025-05-29 11:36:30.924 [pool-3-thread-4] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=24 
2025-05-29 11:36:30.943 [pool-3-thread-4] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:37:02.386 [http-nio-7001-exec-9] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48511080091254528270336> SQL:SELECT count(0) FROM t_ryytn_master_production_sales_relation    cost=24 
2025-05-29 11:37:02.415 [http-nio-7001-exec-9] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48511080091254528270336> SQL:SELECT CAST(id AS VARCHAR) as id, CAST(sales_sku_id AS VARCHAR) as sales_sku_id, CAST(production_sku_id AS VARCHAR) as production_sku_id, conversion_factor, created_by, updated_by, created_time, updated_time, status FROM t_ryytn_master_production_sales_relation ORDER BY id DESC    cost=21 
2025-05-29 11:37:02.439 [http-nio-7001-exec-9] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48511080091254528270336> SQL:SELECT id, product_code, product_name, primary_category, secondary_category, tertiary_category, quaternary_category, product_status, off_market_status, off_market_date, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater, create_time, update_time FROM t_ryytn_master_sku WHERE id IN ( /*__frch_id_0*/'576087942581985280' , /*__frch_id_1*/'576087942581985281' )    cost=20 
2025-05-29 11:37:02.446 [http-nio-7001-exec-9] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><48511080091254528270336> Exit className:[cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl],methodName:[queryPage],param:[[{"condition":{},"pageNum":1,"pageSize":2147483647}]],spend-ms:[97] 
2025-05-29 11:37:12.506 [http-nio-7001-exec-9] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><48511080091254528270336> SQL:select * from cdop_biz.scp_gei_dict where code = /*code*/'' and type = /*type*/'EXPORT'    cost=30 
2025-05-29 11:37:13.592 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler 
2025-05-29 11:37:13.597 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-05-29 11:37:13.607 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-05-29 11:37:13.608 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ... 
2025-05-29 11:37:13.611 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed 
2025-05-29 11:43:09.048 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-29 11:43:09.059 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 11:43:09.060 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 11:43:09.058 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final 
2025-05-29 11:43:09.079 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 11007 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-29 11:43:09.079 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-29 11:43:11.198 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-29 11:43:11.201 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-29 11:43:11.228 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces. 
2025-05-29 11:43:11.605 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-29 11:43:11.779 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@49ef32e0 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-29 11:43:11.845 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:43:11.861 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:43:11.863 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:43:11.865 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:43:11.932 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-29 11:43:11.932 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-29 11:43:11.932 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-29 11:43:11.933 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-29 11:43:11.946 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-29 11:43:11.949 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-29 11:43:11.949 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-29 11:43:11.954 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:43:12.761 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-29 11:43:12.769 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-29 11:43:12.769 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-29 11:43:12.769 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-29 11:43:12.875 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3748 ms 
2025-05-29 11:43:25.169 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-29 11:43:25.182 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 11:43:25.182 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 11:43:25.189 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final 
2025-05-29 11:43:25.203 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 11021 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-29 11:43:25.204 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-29 11:43:27.103 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-29 11:43:27.106 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-29 11:43:27.129 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-29 11:43:27.481 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-29 11:43:27.644 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@49ef32e0 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-29 11:43:27.703 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:43:27.719 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:43:27.721 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:43:27.724 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:43:27.802 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-29 11:43:27.802 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-29 11:43:27.802 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-29 11:43:27.802 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-29 11:43:27.817 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-29 11:43:27.819 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-29 11:43:27.819 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-29 11:43:27.823 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 11:43:28.563 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-29 11:43:28.573 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-29 11:43:28.574 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-29 11:43:28.574 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-29 11:43:28.674 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-29 11:43:28.675 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3427 ms 
2025-05-29 11:43:29.283 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 11:43:29.868 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 11:43:31.745 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-29 11:43:31.753 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-29 11:43:31.756 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-29 11:43:32.389 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@1e71741b] is availabel 
2025-05-29 11:43:32.394 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@1e71741b] is availabel 
2025-05-29 11:43:34.044 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  Start Init System Enum  
2025-05-29 11:43:34.046 [main] ERROR c.a.r.m.inv.common.service.impl.CommonServiceImpl -  system enum init enumName com.cainiao.cntech.dsct.application.enums.FileBizEnum error com.cainiao.cntech.dsct.application.enums.FileBizEnum   
2025-05-29 11:43:34.048 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  End Init System Enum [fileBiz] 
2025-05-29 11:43:35.077 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-29 11:43:35.714 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: cced013a-9a1e-4280-9cb8-e197d83b89d9
 
2025-05-29 11:43:35.772 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-29 11:43:35.772 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-29 11:43:35.806 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@329cef32, org.springframework.security.web.context.SecurityContextPersistenceFilter@52811dc1, org.springframework.security.web.header.HeaderWriterFilter@402d45c6, org.springframework.security.web.authentication.logout.LogoutFilter@a8ef0c2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@46e32574, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2c64ee53, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5f171392, org.springframework.security.web.session.SessionManagementFilter@63fef83c, org.springframework.security.web.access.ExceptionTranslationFilter@6e3552b0] 
2025-05-29 11:43:36.508 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-29 11:43:36.531 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-29 11:43:39.439 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-29 11:43:39.649 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=131 
2025-05-29 11:43:40.446 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-29 11:43:41.227 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=21 
2025-05-29 11:43:41.376 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@7e2cebdf],spend-ms:[1362] 
2025-05-29 11:43:42.705 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[638] 
2025-05-29 11:43:43.885 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:43:43.940 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=21 
2025-05-29 11:43:44.001 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4fd1458d],spend-ms:[2506] 
2025-05-29 11:43:44.024 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=18 
2025-05-29 11:43:44.046 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=17 
2025-05-29 11:43:44.052 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=38 
2025-05-29 11:43:44.193 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=84 
2025-05-29 11:43:44.246 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=24 
2025-05-29 11:43:44.282 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@7ef9d004],spend-ms:[186] 
2025-05-29 11:43:44.297 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=35 
2025-05-29 11:43:44.320 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:43:44.322 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=24 
2025-05-29 11:43:44.831 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=69 
2025-05-29 11:43:44.903 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@16bf9b0a],spend-ms:[441] 
2025-05-29 11:43:46.118 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-29 11:43:46.494 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=370 
2025-05-29 11:43:46.974 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[430] 
2025-05-29 11:43:47.669 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=28 
2025-05-29 11:43:47.698 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@72b1ee4b],spend-ms:[2691] 
2025-05-29 11:43:48.090 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[329] 
2025-05-29 11:43:48.610 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=43 
2025-05-29 11:43:48.645 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@48c8a84d],spend-ms:[665] 
2025-05-29 11:43:48.916 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=31 
2025-05-29 11:43:48.943 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@56926ff1],spend-ms:[18] 
2025-05-29 11:43:49.215 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=26 
2025-05-29 11:43:49.242 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@6bc67b0b],spend-ms:[7] 
2025-05-29 11:43:49.273 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 24.494 seconds (JVM running for 25.292) 
2025-05-29 11:43:49.474 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-29 11:43:49.481 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-29 11:43:49.486 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms 
2025-05-29 11:45:00.104 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><45571080093258696744960> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=46 
2025-05-29 11:45:00.506 [transaction_Worker-1] INFO  c.a.r.m.d.t.RereshAiFreightPlanVersionServiceImpl - <0><45571080093260550627328>  RereshAiFreightPlanVersionServiceImpl data start=========== 
2025-05-29 11:45:00.565 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><45571080093260550627328> SQL:delete from cdop_sys.t_ryytn_freight_version_list_third    cost=55 
2025-05-29 11:45:23.328 [http-nio-7001-exec-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><45571080093355111211008> SQL:SELECT count(0) FROM t_ryytn_master_production_sales_relation    cost=29 
2025-05-29 11:45:23.356 [http-nio-7001-exec-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><45571080093355111211008> SQL:SELECT CAST(id AS VARCHAR) as id, CAST(sales_sku_id AS VARCHAR) as sales_sku_id, CAST(production_sku_id AS VARCHAR) as production_sku_id, conversion_factor, created_by, updated_by, created_time, updated_time, status FROM t_ryytn_master_production_sales_relation ORDER BY id DESC    cost=26 
2025-05-29 11:45:23.403 [http-nio-7001-exec-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><45571080093355111211008> SQL:SELECT id, product_code, product_name, primary_category, secondary_category, tertiary_category, quaternary_category, product_status, off_market_status, off_market_date, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater, create_time, update_time FROM t_ryytn_master_sku WHERE id IN ( /*__frch_id_0*/'576087942581985280' , /*__frch_id_1*/'576087942581985281' )    cost=41 
2025-05-29 11:45:23.426 [http-nio-7001-exec-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><45571080093355111211008> Exit className:[cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl],methodName:[queryPage],param:[[{"condition":{},"pageNum":1,"pageSize":2147483647}]],spend-ms:[138] 
2025-05-29 11:45:27.707 [http-nio-7001-exec-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><45571080093355111211008> SQL:select * from cdop_biz.scp_gei_dict where code = /*code*/'' and type = /*type*/'EXPORT'    cost=42 
2025-05-29 11:45:44.325 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:45:44.409 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=62 
2025-05-29 11:45:44.505 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=93 
2025-05-29 11:45:44.554 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=40 
2025-05-29 11:45:44.756 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=192 
2025-05-29 11:45:44.813 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 11:45:50.242 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><45571080093260550627328> SQL:insert into cdop_sys.t_ryytn_freight_version_list_third(prediction_version,demand_plan_code,create_time) select distinct prediction_version,demand_plan_code,now() from cdop_biz.tdm_kcjh_txn_freight_qty_di    cost=49676 
2025-05-29 11:45:50.297 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><45571080093260550627328> SQL:delete from cdop_sys.t_ryytn_freight_version_list    cost=46 
2025-05-29 11:45:50.337 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><45571080093260550627328> SQL:insert into cdop_sys.t_ryytn_freight_version_list(prediction_version,demand_plan_code,create_time) select prediction_version,demand_plan_code,create_time from cdop_sys.t_ryytn_freight_version_list_third    cost=39 
2025-05-29 11:45:50.371 [transaction_Worker-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><45571080093260550627328> Exit className:[cn.aliyun.ryytn.modules.demand.task.RereshAiFreightPlanVersionServiceImpl],methodName:[process],param:[[{"className":"cn.aliyun.ryytn.modules.demand.task.RereshAiFreightPlanVersionServiceImpl","concurrent":false,"createdTime":"2023-12-07 17:33:39.158","jobConf":"0 */15 * * * ?","jobId":"100000089","jobName":"定时同步调拨计划版本数据信息，供应用侧页面提升性能使用","jobType":1,"serviceId":"null","startDate":1730390400000,"status":1}]],spend-ms:[49841] 
2025-05-29 11:47:44.818 [pool-3-thread-5] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 11:47:44.876 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=42 
2025-05-29 11:47:44.911 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=32 
2025-05-29 11:47:44.949 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=32 
2025-05-29 11:47:45.011 [pool-3-thread-5] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=56 
2025-05-29 11:47:45.048 [pool-3-thread-5] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 13:39:51.981 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-29 13:39:51.994 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 13:39:51.994 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 13:39:51.997 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final 
2025-05-29 13:39:52.019 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 11827 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-29 13:39:52.020 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-29 13:39:54.135 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-29 13:39:54.138 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-29 13:39:54.161 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-29 13:39:54.517 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-29 13:39:54.687 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@7b02e036 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-29 13:39:54.754 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 13:39:54.771 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 13:39:54.773 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 13:39:54.775 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 13:39:54.845 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-29 13:39:54.845 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-29 13:39:54.845 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-29 13:39:54.845 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-29 13:39:54.859 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-29 13:39:54.861 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-29 13:39:54.862 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-29 13:39:54.867 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 13:39:55.640 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-29 13:39:55.648 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-29 13:39:55.648 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-29 13:39:55.648 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-29 13:39:55.748 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-29 13:39:55.748 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3680 ms 
2025-05-29 13:39:56.400 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 13:39:57.008 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 13:39:58.611 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-29 13:39:58.619 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-29 13:39:58.622 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-29 13:39:59.220 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@30a4f455] is availabel 
2025-05-29 13:39:59.225 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@30a4f455] is availabel 
2025-05-29 13:40:00.816 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  Start Init System Enum  
2025-05-29 13:40:00.818 [main] ERROR c.a.r.m.inv.common.service.impl.CommonServiceImpl -  system enum init enumName com.cainiao.cntech.dsct.application.enums.FileBizEnum error com.cainiao.cntech.dsct.application.enums.FileBizEnum   
2025-05-29 13:40:00.820 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  End Init System Enum [fileBiz] 
2025-05-29 13:40:01.844 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-29 13:40:02.454 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0a0e91b9-b577-4c21-a750-3f0273107d58
 
2025-05-29 13:40:02.512 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-29 13:40:02.513 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-29 13:40:02.547 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@42d4de20, org.springframework.security.web.context.SecurityContextPersistenceFilter@76d654, org.springframework.security.web.header.HeaderWriterFilter@46d2be66, org.springframework.security.web.authentication.logout.LogoutFilter@362109d0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6f999cf4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5275d709, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2cc9a5a2, org.springframework.security.web.session.SessionManagementFilter@4e6881e, org.springframework.security.web.access.ExceptionTranslationFilter@2c2dfde3] 
2025-05-29 13:40:03.342 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-29 13:40:03.368 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-29 13:40:06.098 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-29 13:40:06.285 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=108 
2025-05-29 13:40:07.102 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-29 13:40:07.738 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=25 
2025-05-29 13:40:07.897 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@2eaaef66],spend-ms:[1271] 
2025-05-29 13:40:08.882 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[605] 
2025-05-29 13:40:10.041 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=28 
2025-05-29 13:40:10.095 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@1cc1813a],spend-ms:[2076] 
2025-05-29 13:40:10.138 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=40 
2025-05-29 13:40:10.251 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=25 
2025-05-29 13:40:10.276 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@65028252],spend-ms:[111] 
2025-05-29 13:40:10.306 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=26 
2025-05-29 13:40:10.660 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 13:40:10.724 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-29 13:40:10.754 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@5c781425],spend-ms:[393] 
2025-05-29 13:40:10.797 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=21 
2025-05-29 13:40:10.815 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=17 
2025-05-29 13:40:10.892 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=23 
2025-05-29 13:40:10.976 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=27 
2025-05-29 13:40:10.995 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 13:40:11.643 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-29 13:40:11.962 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=311 
2025-05-29 13:40:12.309 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[299] 
2025-05-29 13:40:13.005 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=24 
2025-05-29 13:40:13.036 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4bb2da93],spend-ms:[2179] 
2025-05-29 13:40:13.300 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[200] 
2025-05-29 13:40:13.780 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=38 
2025-05-29 13:40:13.819 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4e8c27f7],spend-ms:[454] 
2025-05-29 13:40:14.060 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=32 
2025-05-29 13:40:14.087 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@76ba2139],spend-ms:[18] 
2025-05-29 13:40:14.359 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=25 
2025-05-29 13:40:14.387 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@481b276c],spend-ms:[6] 
2025-05-29 13:40:14.409 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 22.905 seconds (JVM running for 24.181) 
2025-05-29 13:40:14.866 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-29 13:40:14.869 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-29 13:40:14.878 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms 
2025-05-29 13:40:38.724 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><01911080122276997357568> SQL:SELECT id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater FROM t_ryytn_master_sku WHERE product_code IN ( /*__frch_productCode_0*/'************' , /*__frch_productCode_1*/'************' )    cost=31 
2025-05-29 13:40:38.750 [pool-4-thread-1] WARN  c.a.r.m.m.e.ProductionSalesRelationEIportDataTemplate - <0><01911080122276997357568> 获取当前账户失败 
java.lang.NullPointerException: null
	at cn.aliyun.ryytn.modules.master.eiport.ProductionSalesRelationEIportDataTemplate.getCurrentAccount(ProductionSalesRelationEIportDataTemplate.java:283)
	at cn.aliyun.ryytn.modules.master.eiport.ProductionSalesRelationEIportDataTemplate.validate(ProductionSalesRelationEIportDataTemplate.java:265)
	at cn.aliyun.ryytn.modules.master.eiport.ProductionSalesRelationEIportDataTemplate.preValid(ProductionSalesRelationEIportDataTemplate.java:66)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.invokePreValid(ImportTemplateExecutor.java:523)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.validateImportData(ImportTemplateExecutor.java:449)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.invokeTemplate(ImportTemplateExecutor.java:289)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.lambda$invokeTemplateAndCreateTask$0(ImportTemplateExecutor.java:274)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:40:38.777 [pool-4-thread-1] WARN  c.a.r.m.m.s.ProductionSalesRelationServiceImpl - <0><01911080122276997357568> 获取当前用户失败，使用默认用户 
java.lang.NullPointerException: null
	at cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl.getCurrentUser(ProductionSalesRelationServiceImpl.java:244)
	at cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl.saveOrUpdate(ProductionSalesRelationServiceImpl.java:58)
	at cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl$$FastClassBySpringCGLIB$$c71e736b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at cn.aliyun.ryytn.starter.aspect.LogAspect.around(LogAspect.java:85)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)
	at cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl$$EnhancerBySpringCGLIB$$40b422a.saveOrUpdate(<generated>)
	at cn.aliyun.ryytn.modules.master.eiport.ProductionSalesRelationEIportDataTemplate.importData(ProductionSalesRelationEIportDataTemplate.java:96)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.invokeTemplate(ImportTemplateExecutor.java:298)
	at com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor.lambda$invokeTemplateAndCreateTask$0(ImportTemplateExecutor.java:274)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:40:38.812 [pool-4-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><01911080122276997357568> SQL:SELECT COUNT(1) FROM t_ryytn_master_production_sales_relation WHERE sales_sku_id = /*salesSkuId*/'576087942581985280' AND production_sku_id = /*productionSkuId*/'576087942581985281' AND status = 1    cost=25 
2025-05-29 13:40:38.830 [pool-4-thread-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><01911080122276997357568> Exception className:[cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl],methodName:[saveOrUpdate],param:[[{"conversionFactor":1,"productionSkuId":"576087942581985281","salesSkuId":"576087942581985280"}]],spend-ms:[60],Exception:[null] 
2025-05-29 13:41:09.961 [http-nio-7001-exec-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><01911080122491963826176> SQL:SELECT count(0) FROM t_ryytn_master_production_sales_relation    cost=22 
2025-05-29 13:41:09.983 [http-nio-7001-exec-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><01911080122491963826176> SQL:SELECT CAST(id AS VARCHAR) as id, CAST(sales_sku_id AS VARCHAR) as sales_sku_id, CAST(production_sku_id AS VARCHAR) as production_sku_id, conversion_factor, created_by, updated_by, created_time, updated_time, status FROM t_ryytn_master_production_sales_relation ORDER BY id DESC    cost=21 
2025-05-29 13:41:10.014 [http-nio-7001-exec-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><01911080122491963826176> SQL:SELECT id, product_code, product_name, primary_category, secondary_category, tertiary_category, quaternary_category, product_status, off_market_status, off_market_date, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater, create_time, update_time FROM t_ryytn_master_sku WHERE id IN ( /*__frch_id_0*/'576087942581985280' , /*__frch_id_1*/'576087942581985281' )    cost=28 
2025-05-29 13:41:10.050 [http-nio-7001-exec-3] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><01911080122491963826176> Exit className:[cn.aliyun.ryytn.modules.master.service.ProductionSalesRelationServiceImpl],methodName:[queryPage],param:[[{"condition":{},"pageNum":1,"pageSize":2147483647}]],spend-ms:[109] 
2025-05-29 13:41:10.084 [http-nio-7001-exec-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><01911080122491963826176> SQL:select * from cdop_biz.scp_gei_dict where code = /*code*/'' and type = /*type*/'EXPORT'    cost=19 
2025-05-29 13:42:11.001 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 13:42:11.026 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=21 
2025-05-29 13:42:11.059 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=32 
2025-05-29 13:42:11.081 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=20 
2025-05-29 13:42:11.112 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=22 
2025-05-29 13:42:11.131 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 13:44:11.138 [pool-3-thread-2] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 13:44:11.181 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=33 
2025-05-29 13:44:11.201 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=19 
2025-05-29 13:44:11.225 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=18 
2025-05-29 13:44:11.255 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=27 
2025-05-29 13:44:11.276 [pool-3-thread-2] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 13:45:00.096 [pool-3-thread-4] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><01911080123457777819648> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=19 
2025-05-29 13:45:00.235 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler 
2025-05-29 13:45:00.239 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-05-29 13:45:00.247 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-05-29 13:45:00.249 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ... 
2025-05-29 13:45:00.251 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed 
2025-05-29 13:48:15.139 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-29 13:48:15.153 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 13:48:15.153 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-29 13:48:15.156 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final 
2025-05-29 13:48:15.178 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 11974 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-29 13:48:15.178 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-29 13:48:17.425 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-29 13:48:17.429 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-29 13:48:17.452 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-29 13:48:17.815 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-29 13:48:17.984 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@25243bc1 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-29 13:48:18.050 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 13:48:18.067 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 13:48:18.069 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 13:48:18.071 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 13:48:18.141 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-29 13:48:18.142 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-29 13:48:18.142 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-29 13:48:18.142 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-29 13:48:18.157 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-29 13:48:18.159 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-29 13:48:18.161 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-29 13:48:18.166 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method GeiCustomSqlSessionConfiguration.datasourcePostProcessor is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-29 13:48:18.962 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-29 13:48:18.970 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-29 13:48:18.970 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-29 13:48:18.970 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-29 13:48:19.072 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-29 13:48:19.072 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3802 ms 
2025-05-29 13:48:19.780 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 13:48:20.454 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-29 13:48:21.935 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-29 13:48:21.942 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-29 13:48:21.944 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-29 13:48:22.573 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@1b64eda6] is availabel 
2025-05-29 13:48:22.580 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@1b64eda6] is availabel 
2025-05-29 13:48:24.564 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  Start Init System Enum  
2025-05-29 13:48:24.566 [main] ERROR c.a.r.m.inv.common.service.impl.CommonServiceImpl -  system enum init enumName com.cainiao.cntech.dsct.application.enums.FileBizEnum error com.cainiao.cntech.dsct.application.enums.FileBizEnum   
2025-05-29 13:48:24.568 [main] INFO  c.a.r.m.inv.common.service.impl.CommonServiceImpl -  End Init System Enum [fileBiz] 
2025-05-29 13:48:25.603 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-29 13:48:26.319 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 29814ed4-20ba-45e4-97f0-d319a4bc83c9
 
2025-05-29 13:48:26.381 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-29 13:48:26.381 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-29 13:48:26.417 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@16a3d9a7, org.springframework.security.web.context.SecurityContextPersistenceFilter@39240aa3, org.springframework.security.web.header.HeaderWriterFilter@587df3e4, org.springframework.security.web.authentication.logout.LogoutFilter@2c5e2a44, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@20ed6de, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7892635c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@24c94e95, org.springframework.security.web.session.SessionManagementFilter@61f18997, org.springframework.security.web.access.ExceptionTranslationFilter@2d036335] 
2025-05-29 13:48:27.245 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-29 13:48:27.268 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-29 13:48:30.184 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-29 13:48:30.360 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=105 
2025-05-29 13:48:31.191 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-29 13:48:31.997 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=29 
2025-05-29 13:48:32.221 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@6fab250b],spend-ms:[1461] 
2025-05-29 13:48:33.226 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[611] 
2025-05-29 13:48:34.379 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 13:48:34.505 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=28 
2025-05-29 13:48:34.524 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=19 
2025-05-29 13:48:34.618 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=28 
2025-05-29 13:48:34.690 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=24 
2025-05-29 13:48:34.718 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 13:48:35.460 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=22 
2025-05-29 13:48:35.489 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@86126ad],spend-ms:[3174] 
2025-05-29 13:48:35.582 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=78 
2025-05-29 13:48:35.708 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=27 
2025-05-29 13:48:35.739 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@6013565b],spend-ms:[171] 
2025-05-29 13:48:35.769 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=26 
2025-05-29 13:48:36.360 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=52 
2025-05-29 13:48:36.385 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@726aaaf0],spend-ms:[538] 
2025-05-29 13:48:37.472 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-29 13:48:37.886 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=407 
2025-05-29 13:48:38.253 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[309] 
2025-05-29 13:48:39.235 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=33 
2025-05-29 13:48:39.264 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@28c776f2],spend-ms:[2774] 
2025-05-29 13:48:39.516 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[198] 
2025-05-29 13:48:40.010 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-29 13:48:40.079 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@47f4d61],spend-ms:[473] 
2025-05-29 13:48:40.360 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=28 
2025-05-29 13:48:40.392 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@5eb66704],spend-ms:[24] 
2025-05-29 13:48:40.618 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-29 13:48:40.656 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@6166edcf],spend-ms:[6] 
2025-05-29 13:48:40.683 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 26.101 seconds (JVM running for 27.168) 
2025-05-29 13:48:41.020 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-29 13:48:41.025 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-29 13:48:41.037 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 11 ms 
2025-05-29 13:50:00.116 [pool-3-thread-2] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><24231080124716006105088> SQL:select * from cdop_biz.scp_gei_task where gmt_expired is not null and gmt_expired <= now() and status not in ('SUCCESS', 'FAILED')    cost=29 
2025-05-29 13:50:34.724 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 13:50:34.757 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=29 
2025-05-29 13:50:34.775 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=17 
2025-05-29 13:50:34.793 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=15 
2025-05-29 13:50:34.817 [pool-3-thread-3] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=22 
2025-05-29 13:50:34.834 [pool-3-thread-3] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 13:52:34.839 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查开始 >>>>>>>>>>>>> 
2025-05-29 13:52:34.884 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'monitor'    cost=35 
2025-05-29 13:52:34.906 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_core_stat WHERE core_code = /*core*/'executor'    cost=21 
2025-05-29 13:52:34.927 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select * from cdop_biz.scp_task_exec_chain WHERE status in( /*__frch_code_0*/'running' )    cost=18 
2025-05-29 13:52:34.972 [pool-3-thread-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT count(0) FROM cdop_biz.scp_task_exec_chain WHERE status IN (/*__frch_code_0*/'await')    cost=41 
2025-05-29 13:52:34.991 [pool-3-thread-1] INFO  c.a.r.m.i.s.task.core.checker.AsyncCoreChecker - >>>>>>>>>>>>>>>> AsyncCoreChecker检查结束 >>>>>>>>>>>>> 
2025-05-29 13:52:46.475 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler 
2025-05-29 13:52:46.480 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-05-29 13:52:46.487 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-05-29 13:52:46.488 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ... 
2025-05-29 13:52:46.490 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed 
