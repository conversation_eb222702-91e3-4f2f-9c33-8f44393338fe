2025-05-12 10:59:22.755 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-12 10:59:22.767 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-12 10:59:22.769 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-12 10:59:22.783 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 61122 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-12 10:59:22.786 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-12 10:59:24.551 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-12 10:59:24.554 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-12 10:59:24.578 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-12 10:59:24.804 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-12 10:59:24.951 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@31198ceb , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-12 10:59:24.993 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-12 10:59:25.008 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-12 10:59:25.010 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-12 10:59:25.012 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-12 10:59:25.084 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-12 10:59:25.084 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-12 10:59:25.084 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-12 10:59:25.084 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-12 10:59:25.096 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-12 10:59:25.097 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-12 10:59:25.098 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-12 10:59:25.919 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-12 10:59:25.929 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-12 10:59:25.929 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-12 10:59:25.929 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-12 10:59:26.020 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-12 10:59:26.020 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3192 ms 
2025-05-12 10:59:26.624 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-12 10:59:27.129 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-12 10:59:30.183 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-12 10:59:30.191 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-12 10:59:30.193 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-12 10:59:30.628 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@6b932107] is availabel 
2025-05-12 10:59:30.630 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@6b932107] is availabel 
2025-05-12 10:59:32.930 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-12 10:59:33.522 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 446eab85-b18a-4603-a996-e7a6234a7fb0
 
2025-05-12 10:59:33.578 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-12 10:59:33.578 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-12 10:59:33.606 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7d43c89f, org.springframework.security.web.context.SecurityContextPersistenceFilter@73b034ca, org.springframework.security.web.header.HeaderWriterFilter@3ba6707e, org.springframework.security.web.authentication.logout.LogoutFilter@3b4b2c03, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5eaa4ed0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@251db193, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@149ef64a, org.springframework.security.web.session.SessionManagementFilter@30d3f583, org.springframework.security.web.access.ExceptionTranslationFilter@58fbecde] 
2025-05-12 10:59:34.132 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-12 10:59:34.156 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-12 10:59:36.714 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-12 10:59:36.888 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=114 
2025-05-12 10:59:37.719 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-12 10:59:38.550 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=18 
2025-05-12 10:59:38.693 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@15d25244],spend-ms:[1511] 
2025-05-12 10:59:42.793 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[2746] 
2025-05-12 10:59:44.472 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=39 
2025-05-12 10:59:44.533 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@2254919b],spend-ms:[5702] 
2025-05-12 10:59:44.585 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=39 
2025-05-12 10:59:44.704 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=42 
2025-05-12 10:59:44.884 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@29788adf],spend-ms:[118] 
2025-05-12 10:59:44.912 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=25 
2025-05-12 10:59:45.254 [http-nio-7001-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-12 10:59:45.255 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-12 10:59:45.260 [http-nio-7001-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms 
2025-05-12 10:59:45.305 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=21 
2025-05-12 10:59:45.336 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@77f0b9d7],spend-ms:[380] 
2025-05-12 11:00:05.521 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><28451073921278556090368> Exception className:[cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl],methodName:[queryMasterSkuPage],param:[[{"pageNum":1,"pageSize":10}]],spend-ms:[8916],Exception:[nested exception is org.apache.ibatis.builder.BuilderException: Error evaluating expression 'condition.productCode != null and condition.productCode != '''. Cause: org.apache.ibatis.ognl.OgnlException: source is null for getProperty(null, "productCode")] 
2025-05-12 11:00:05.523 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><28451073921278556090368> Exception className:[cn.aliyun.ryytn.modules.master.controller.MasterSkuController],methodName:[page],param:[[{"pageNum":1,"pageSize":10}]],spend-ms:[19883],Exception:[nested exception is org.apache.ibatis.builder.BuilderException: Error evaluating expression 'condition.productCode != null and condition.productCode != '''. Cause: org.apache.ibatis.ognl.OgnlException: source is null for getProperty(null, "productCode")] 
2025-05-12 11:00:05.552 [http-nio-7001-exec-2] ERROR c.a.ryytn.starter.handler.GlobalExceptionHandler - <0><28451073921278556090368> handleThrowable:org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.builder.BuilderException: Error evaluating expression 'condition.productCode != null and condition.productCode != '''. Cause: org.apache.ibatis.ognl.OgnlException: source is null for getProperty(null, "productCode")
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy125.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy188.queryMasterSkuPage(Unknown Source)
	at cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl.queryMasterSkuPage(MasterSkuServiceImpl.java:375)
	at cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl$$FastClassBySpringCGLIB$$15f6443f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at cn.aliyun.ryytn.starter.aspect.LogAspect.around(LogAspect.java:85)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)
	at cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl$$EnhancerBySpringCGLIB$$d35f5383.queryMasterSkuPage(<generated>)
	at cn.aliyun.ryytn.modules.master.controller.MasterSkuController.page(MasterSkuController.java:43)
	at cn.aliyun.ryytn.modules.master.controller.MasterSkuController$$FastClassBySpringCGLIB$$bf767351.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at cn.aliyun.ryytn.starter.aspect.LogAspect.around(LogAspect.java:85)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)
	at cn.aliyun.ryytn.modules.master.controller.MasterSkuController$$EnhancerBySpringCGLIB$$aee4dbf9.page(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.CorsFilter.doFilter(CorsFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.aliyun.ryytn.starter.filter.XssFilter.doFilter(XssFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.security.spring.web.filter.SecurityFilter.doFilterInternal(SecurityFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.ibatis.builder.BuilderException: Error evaluating expression 'condition.productCode != null and condition.productCode != '''. Cause: org.apache.ibatis.ognl.OgnlException: source is null for getProperty(null, "productCode")
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:48)
	at org.apache.ibatis.scripting.xmltags.ExpressionEvaluator.evaluateBoolean(ExpressionEvaluator.java:32)
	at org.apache.ibatis.scripting.xmltags.IfSqlNode.apply(IfSqlNode.java:34)
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.lambda$apply$0(MixedSqlNode.java:32)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:32)
	at org.apache.ibatis.scripting.xmltags.TrimSqlNode.apply(TrimSqlNode.java:55)
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.lambda$apply$0(MixedSqlNode.java:32)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:32)
	at org.apache.ibatis.scripting.xmltags.DynamicSqlSource.getBoundSql(DynamicSqlSource.java:39)
	at org.apache.ibatis.mapping.MappedStatement.getBoundSql(MappedStatement.java:305)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:82)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy238.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 163 more
Caused by: org.apache.ibatis.ognl.OgnlException: source is null for getProperty(null, "productCode")
	at org.apache.ibatis.ognl.OgnlRuntime.getProperty(OgnlRuntime.java:3366)
	at org.apache.ibatis.ognl.ASTProperty.getValueBody(ASTProperty.java:121)
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212)
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258)
	at org.apache.ibatis.ognl.ASTChain.getValueBody(ASTChain.java:141)
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212)
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258)
	at org.apache.ibatis.ognl.ASTNotEq.getValueBody(ASTNotEq.java:50)
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212)
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258)
	at org.apache.ibatis.ognl.ASTAnd.getValueBody(ASTAnd.java:61)
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212)
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258)
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:586)
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:550)
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:46)
	... 185 more
 
2025-05-12 11:00:05.566 [http-nio-7001-exec-2] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><28451073921278556090368> Exit className:[cn.aliyun.ryytn.starter.handler.GlobalExceptionHandler],methodName:[handleThrowable],param:[[Ljava.lang.Object;@575556bf],spend-ms:[25] 
2025-05-12 11:00:06.288 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-12 11:00:06.644 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=350 
2025-05-12 11:00:07.033 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[309] 
2025-05-12 11:00:08.003 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=28 
2025-05-12 11:00:08.044 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@1e418480],spend-ms:[22611] 
2025-05-12 11:00:08.321 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[209] 
2025-05-12 11:00:08.615 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=29 
2025-05-12 11:00:08.645 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@2ff35d7],spend-ms:[526] 
2025-05-12 11:00:09.315 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=41 
2025-05-12 11:00:09.365 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@37884b27],spend-ms:[9] 
2025-05-12 11:00:09.641 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=26 
2025-05-12 11:00:09.668 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@4151af88],spend-ms:[12] 
2025-05-12 11:00:09.691 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 47.346 seconds (JVM running for 48.339) 
2025-05-12 11:09:02.018 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - Security PropertySource[name : security-property-source:default] was initialized and added into Environment as first one. 
2025-05-12 11:09:02.029 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : security.basic.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-12 11:09:02.031 [main] INFO  c.a.s.s.b.c.SecurityDefaultPropertyValueApplierApplicationContextInitializer - The property [name : management.security.enabled] was not found in Environment , the default value [false] will be applied. 
2025-05-12 11:09:02.044 [main] INFO  cn.aliyun.ryytn.starter.Application - Starting Application using Java 1.8.0_333 on lijindeMacBook-Pro.local with PID 61340 (/Users/<USER>/Documents/workspace/scp/backend_transaction.git/transaction-starter/target/classes started by lijin in /Users/<USER>/Documents/workspace/scp/backend_transaction.git) 
2025-05-12 11:09:02.044 [main] INFO  cn.aliyun.ryytn.starter.Application - The following profiles are active: dataq 
2025-05-12 11:09:03.875 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-05-12 11:09:03.878 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-05-12 11:09:03.902 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
2025-05-12 11:09:04.130 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'com.aliyun.dataq.dataindustry.starter.DataIndustryServiceLoadAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'. 
2025-05-12 11:09:04.287 [main] INFO  c.a.s.s.b.f.c.SecurityHandlerInterceptorBeanPostProcessor - org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport Beans[ names : org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration ] were be found in org.springframework.beans.factory.support.DefaultListableBeanFactory@7e8e8651 , thus com.alibaba.security.spring.web.servlet.handler.SecurityHandlerInterceptor Beans will not be set into org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping. 
2025-05-12 11:09:04.364 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableCsrfComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-12 11:09:04.385 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableXssComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-12 11:09:04.388 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableHttpComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-12 11:09:04.391 [main] INFO  o.s.context.annotation.ConfigurationClassEnhancer - @Bean method WebSecurityConfiguration.configurableJsonComponentsRegister is non-static and returns an object assignable to Spring's BeanFactoryPostProcessor interface. This will result in a failure to process annotations such as @Autowired, @Resource and @PostConstruct within the method's declaring @Configuration class. Add the 'static' modifier to this method to avoid these container lifecycle issues; see @Bean javadoc for complete details. 
2025-05-12 11:09:04.457 [main] WARN  c.a.s.s.config.ConfigurableJsonpComponentRegister - Security Component[ name : jsonp ] is disable , caused by configuration property : "spring.security.jsonp.enabled = false " ! 
2025-05-12 11:09:04.458 [main] WARN  c.a.s.s.config.ConfigurableCsrfComponentRegister - Security Component[ name : csrf ] is disable , caused by configuration property : "spring.security.csrf.enabled = false " ! 
2025-05-12 11:09:04.458 [main] WARN  c.a.s.s.config.ConfigurableXssComponentRegister - Security Component[ name : xss ] is disable , caused by configuration property : "spring.security.xss.enabled = false " ! 
2025-05-12 11:09:04.458 [main] INFO  c.a.s.s.config.ConfigurableHttpComponentRegister - Security Component[ name :http ] will register components :  
2025-05-12 11:09:04.469 [main] WARN  c.a.s.s.config.ConfigurableJsonComponentRegister - Security Component[ name : json ] is disable , caused by configuration property : "spring.security.json.enabled = false " ! 
2025-05-12 11:09:04.470 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get applicationContext 
2025-05-12 11:09:04.471 [main] INFO  cn.aliyun.ryytn.common.utils.spring.SpringUtil - get BeanFactory Success. 
2025-05-12 11:09:05.363 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7001 (http) 
2025-05-12 11:09:05.376 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7001"] 
2025-05-12 11:09:05.377 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat] 
2025-05-12 11:09:05.377 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.55] 
2025-05-12 11:09:05.475 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext 
2025-05-12 11:09:05.475 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3385 ms 
2025-05-12 11:09:06.020 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-12 11:09:06.587 [main] WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis 
2025-05-12 11:09:08.406 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-05-12 11:09:08.415 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization). 
2025-05-12 11:09:08.420 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized. 
2025-05-12 11:09:08.953 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@3b2074a5] is availabel 
2025-05-12 11:09:08.958 [check environment thread] INFO  c.a.rk.remote.core.support.AbstractServiceContext - environment[com.aliyun.dataq.dataindustry.DataIndustryEnvironment@3b2074a5] is availabel 
2025-05-12 11:09:11.432 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-05-12 11:09:12.197 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 92474b07-c21f-49f7-abd5-02f151e665e7
 
2025-05-12 11:09:12.303 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Registered com.alibaba.security.spring.web.filter.SecurityFilter Beans List : [refererValidationSecurityFilter, redirectValidationSecurityFilter, optionsSuppressCookieSecurityFilter, characterEncodingSecurityFilter, sameSiteCookieSecurityFilter] 
2025-05-12 11:09:12.304 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Enabled com.alibaba.security.spring.web.filter.SecurityFilter List : [] 
2025-05-12 11:09:12.379 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4ceb368b, org.springframework.security.web.context.SecurityContextPersistenceFilter@7f922523, org.springframework.security.web.header.HeaderWriterFilter@3aebdb4, org.springframework.security.web.authentication.logout.LogoutFilter@14d9ae22, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6441cff1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6c8f5262, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1ddc5857, org.springframework.security.web.session.SessionManagementFilter@3c20abd6, org.springframework.security.web.access.ExceptionTranslationFilter@5ab1107d] 
2025-05-12 11:09:13.045 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7001"] 
2025-05-12 11:09:13.084 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 7001 (http) with context path '' 
2025-05-12 11:09:15.913 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Will start Quartz Scheduler [transaction] in 1 seconds 
2025-05-12 11:09:16.119 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT job_id, job_name, job_type, start_date, end_date, job_conf, class_name, param, service_id, misfire_policy, concurrent, status, description, created_by, created_time, updated_by, updated_time FROM t_ryytn_job WHERE status <> 3    cost=115 
2025-05-12 11:09:16.920 [Quartz Scheduler [transaction]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now, after delay of 1 seconds 
2025-05-12 11:09:17.819 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-12 11:09:18.050 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.scheduler.service.SchedulerServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@794eb6e7],spend-ms:[1495] 
2025-05-12 11:09:19.879 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/calendar/week-list],spend-ms:[683] 
2025-05-12 11:09:21.070 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=18 
2025-05-12 11:09:21.107 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.CalendarServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@396ea7ab],spend-ms:[2959] 
2025-05-12 11:09:21.162 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT config_id, config_value FROM t_ryytn_config    cost=34 
2025-05-12 11:09:21.311 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=31 
2025-05-12 11:09:21.343 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ConfigServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@2a801d1e],spend-ms:[147] 
2025-05-12 11:09:21.371 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT dict_id, dict_type, name, code, parent_id, parent_ids, level, leaf_flag, css_class, list_class, item_check, sort_no, status, delete_flag, description, data_type, created_by, created_time, updated_by, updated_time FROM t_ryytn_dict_data ORDER BY dict_type ASC,sort_no ASC,code ASC    cost=24 
2025-05-12 11:09:21.793 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-12 11:09:21.831 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.DictServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@1eb9ab8f],spend-ms:[408] 
2025-05-12 11:09:23.792 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited 
2025-05-12 11:09:24.092 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:select sku_code, code_69, sku_name, status_id, lifecycle, category_code, category_name, shelf_life, rought_weight, net_weight, weight_unit, volume, volume_unit, length, width, height, unit, plan_unit_cnt, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, fin_category_code, fin_category_name, atomic_unit_cnt, price, price_unit, gift, brand_code, brand_name, brand_group, brand_org, gmt_create, gmt_modified, ds from dim_bas_sku_baisc_info_df where 1=1 AND status_id = /*statusId*/1    cost=293 
2025-05-12 11:09:24.424 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/sku-product-table],spend-ms:[287] 
2025-05-12 11:09:25.176 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=26 
2025-05-12 11:09:25.210 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.ProductServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@670132b9],spend-ms:[3274] 
2025-05-12 11:09:25.483 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit dataq httpMethod:[/elastic-15561-551/znyy_test/ryytn_dev/base-bus/warehouse-list],spend-ms:[209] 
2025-05-12 11:09:26.024 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=32 
2025-05-12 11:09:26.062 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.system.service.WarehouseServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@196ca821],spend-ms:[521] 
2025-05-12 11:09:26.294 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=23 
2025-05-12 11:09:26.335 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.demand.service.OmsFileTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@54856056],spend-ms:[28] 
2025-05-12 11:09:26.595 [main] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - SQL:SELECT a.id, a.file_type, a.file_count, a.file_url, a.record_time, a.success_flag, a.description, a.demand_plan_code, a.version_id, a.expire_date FROM t_ryytn_oms_file a WHERE 1=1 and success_flag = 0 and record_time > now() - INTERVAL '9 day'    cost=32 
2025-05-12 11:09:26.626 [main] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - Exit className:[cn.aliyun.ryytn.modules.distribution.service.AiplanTaskServiceImpl],methodName:[onApplicationEvent],param:[[Ljava.lang.Object;@77eb383a],spend-ms:[13] 
2025-05-12 11:09:26.657 [main] INFO  cn.aliyun.ryytn.starter.Application - Started Application in 25.084 seconds (JVM running for 26.13) 
2025-05-12 11:09:27.007 [RMI TCP Connection(6)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet' 
2025-05-12 11:09:27.009 [RMI TCP Connection(6)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet' 
2025-05-12 11:09:27.012 [RMI TCP Connection(6)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms 
2025-05-12 11:15:00.360 [transaction_Worker-1] INFO  c.a.r.m.d.t.RereshAiFreightPlanVersionServiceImpl - <0><36021073925116419747840>  RereshAiFreightPlanVersionServiceImpl data start=========== 
2025-05-12 11:15:00.396 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><36021073925116419747840> SQL:delete from cdop_sys.t_ryytn_freight_version_list_third    cost=30 
2025-05-12 11:16:01.863 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><36021073925116419747840> SQL:insert into cdop_sys.t_ryytn_freight_version_list_third(prediction_version,demand_plan_code,create_time) select distinct prediction_version,demand_plan_code,now() from cdop_biz.tdm_kcjh_txn_freight_qty_di    cost=61459 
2025-05-12 11:16:01.895 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><36021073925116419747840> SQL:delete from cdop_sys.t_ryytn_freight_version_list    cost=24 
2025-05-12 11:16:01.940 [transaction_Worker-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><36021073925116419747840> SQL:insert into cdop_sys.t_ryytn_freight_version_list(prediction_version,demand_plan_code,create_time) select prediction_version,demand_plan_code,create_time from cdop_sys.t_ryytn_freight_version_list_third    cost=38 
2025-05-12 11:16:01.969 [transaction_Worker-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><36021073925116419747840> Exit className:[cn.aliyun.ryytn.modules.demand.task.RereshAiFreightPlanVersionServiceImpl],methodName:[process],param:[[{"className":"cn.aliyun.ryytn.modules.demand.task.RereshAiFreightPlanVersionServiceImpl","concurrent":false,"createdTime":"2023-12-07 17:33:39.158","jobConf":"0 */15 * * * ?","jobId":"100000089","jobName":"定时同步调拨计划版本数据信息，供应用侧页面提升性能使用","jobType":1,"serviceId":"null","startDate":1730390400000,"status":1}]],spend-ms:[61588] 
2025-05-12 11:17:33.338 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1747019347231_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: detected 1 failed or restarted instances. 
2025-05-12 11:17:33.342 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1747019347231_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: Scanning for instance "DESKTOP-QISESQA1747019121261"'s failed in-progress jobs. 
2025-05-12 11:19:48.563 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1747019347231_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: detected 1 failed or restarted instances. 
2025-05-12 11:19:48.564 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1747019347231_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: Scanning for instance "scp-transaction-76bbf5bf66-sg8sf1747017131108"'s failed in-progress jobs. 
2025-05-12 11:25:04.174 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1747019347231_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: detected 1 failed or restarted instances. 
2025-05-12 11:25:04.185 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1747019347231_ClusterManager] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore - ClusterManager: Scanning for instance "qiqi.local1747020180140"'s failed in-progress jobs. 
2025-05-12 11:25:49.010 [http-nio-7001-exec-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><36021073927771481288704> SQL:SELECT count(0) FROM t_ryytn_master_sku    cost=214 
2025-05-12 11:25:49.060 [http-nio-7001-exec-1] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><36021073927771481288704> SQL:SELECT id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater FROM t_ryytn_master_sku ORDER BY id DESC LIMIT /*First_PageHelper*/10     cost=39 
2025-05-12 11:25:49.074 [http-nio-7001-exec-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><36021073927771481288704> Exit className:[cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl],methodName:[queryMasterSkuPage],param:[[{"pageNum":1,"pageSize":10}]],spend-ms:[11907] 
2025-05-12 11:25:49.086 [http-nio-7001-exec-1] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><36021073927771481288704> Exit className:[cn.aliyun.ryytn.modules.master.controller.MasterSkuController],methodName:[page],param:[[{"pageNum":1,"pageSize":10}]],spend-ms:[15577] 
2025-05-12 11:26:00.234 [QuartzScheduler_transaction-lijindeMacBook-Pro.local1747019347231_ClusterManager] WARN  o.s.scheduling.quartz.LocalDataSourceJobStore - This scheduler instance (lijindeMacBook-Pro.local1747019347231) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior. 
2025-05-12 11:26:51.707 [http-nio-7001-exec-4] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><36021073928098821550080> SQL:SELECT count(0) FROM t_ryytn_master_sku WHERE product_code = /*condition.productCode*/'110101100601'    cost=25 
2025-05-12 11:26:51.747 [http-nio-7001-exec-4] INFO  c.a.r.s.config.MybatisSqlCompletePrintInterceptor - <0><36021073928098821550080> SQL:SELECT id, product_code, product_name, create_time, update_time, material_type, material_type_desc, material_group, material_group_desc, base_unit_desc, gross_weight, net_weight, weight_unit, barcode, brand, short_name, model_spec, shelf_life, length, width, height, volume, batch_management_flag, layer11_unit, layer9_quantity, layer10_quantity, tax_classification, sn_enabled, traceability_enabled, primary_category, secondary_category, tertiary_category, quaternary_category, off_market_status, off_market_date, product_status, zero_level_desc, zero_level_code, sap_create_date, sap_create_time, sap_update_date, sap_updater FROM t_ryytn_master_sku WHERE product_code = /*condition.productCode*/'110101100601' ORDER BY id DESC LIMIT /*First_PageHelper*/10     cost=37 
2025-05-12 11:26:51.758 [http-nio-7001-exec-4] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><36021073928098821550080> Exit className:[cn.aliyun.ryytn.modules.master.service.MasterSkuServiceImpl],methodName:[queryMasterSkuPage],param:[[{"condition":{"productCode":"110101100601"},"pageNum":1,"pageSize":10}]],spend-ms:[129] 
2025-05-12 11:26:51.759 [http-nio-7001-exec-4] INFO  cn.aliyun.ryytn.starter.aspect.LogAspect - <0><36021073928098821550080> Exit className:[cn.aliyun.ryytn.modules.master.controller.MasterSkuController],methodName:[page],param:[[{"condition":{"productCode":"110101100601"},"pageNum":1,"pageSize":10}]],spend-ms:[140] 
