<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

    <groupId>cn.aliyun.ryytn</groupId>
    <artifactId>transaction</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <description>认养一头牛</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.7</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
	
    <modules>
        <module>transaction-common</module>
        <module>transaction-starter</module>
		<module>transaction-modules</module>
		<module>transaction-udf</module>
        <module>transaction-gei</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.5.7</spring-boot.version>
        <spring-boot-admin.version>2.5.4</spring-boot-admin.version>
        <spring-boot.mybatis>2.2.0</spring-boot.mybatis>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <pagehelper.boot.version>1.4.0</pagehelper.boot.version>
        <druid.version>1.2.8</druid.version>
        <dynamic-ds.version>3.5.0</dynamic-ds.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <velocity.version>2.3</velocity.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>
        <common-pool.version>2.10.0</common-pool.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <transmittable-thread-local.version>2.12.2</transmittable-thread-local.version>
        <mysql.connector.version>8.0.19</mysql.connector.version>
		<security.springboot.version>2.5.1</security.springboot.version>
<!--		<cntech-dsct-common.version>1.0.8-RELEASE</cntech-dsct-common.version>-->
		<cntech-dsct-rpc.version>0.0.1-SNAPSHOT</cntech-dsct-rpc.version>
		<hutool-tool.version>5.8.16</hutool-tool.version>
	</properties>

	<dependencyManagement>
	  <dependencies>
		<!-- 必加，不然会依赖springboot指定的spring security版本 -->
          <dependency>
		  <groupId>com.alibaba.security</groupId>
		  <artifactId>security-spring-dependencies</artifactId>
		  <version>${security.springboot.version}</version>
		  <type>pom</type>
		  <scope>import</scope>
		</dependency>
		<dependency>
		  <groupId>com.alibaba.security</groupId>
		  <artifactId>security-spring-boot-starter</artifactId>
		  <version>${security.springboot.version}</version>
		</dependency>
		  <dependency>
			  <groupId>com.alibaba</groupId>
			  <artifactId>easyexcel</artifactId>
			  <version>3.1.1</version>
		  </dependency>
<!--		  <dependency>-->
<!--			  <groupId>com.cainiao.cntech.dsct</groupId>-->
<!--			  <artifactId>cntech-dsct-rpc</artifactId>-->
<!--			  <version>${cntech-dsct-rpc.version}</version>-->
<!--		  </dependency>-->
		  <dependency>
			  <groupId>cn.hutool</groupId>
			  <artifactId>hutool-all</artifactId>
			  <version>${hutool-tool.version}</version>
		  </dependency>
	  </dependencies>
	</dependencyManagement>

	<dependencies>
		<!-- SpringBoot 依赖配置 -->
        <dependency>
        	<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-dependencies</artifactId>
			<version>${spring-boot.version}</version>
			<type>pom</type>
			<scope>import</scope>
        </dependency>
        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
			<version>${spring-boot.mybatis}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>${druid.version}</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
        <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>lettuce-core</artifactId>
					<groupId>io.lettuce</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.1</version>
        </dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
			<version>4.4</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.15</version>
		</dependency>
		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>1.6.1</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>log4j-over-slf4j</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>1.2.10</version>
		</dependency>
    	<dependency>
		    <groupId>commons-fileupload</groupId>
		    <artifactId>commons-fileupload</artifactId>
		    <version>1.4</version>
		</dependency>
		<dependency>
		    <groupId>org.jsoup</groupId>
		    <artifactId>jsoup</artifactId>
		    <version>1.11.3</version>
		</dependency>
    	<dependency>
		    <groupId>javax.mail</groupId>
		    <artifactId>mail</artifactId>
		    <version>1.5.0-b01</version>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
		</dependency>
		<dependency>
	    	<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
	        <version>1.9.3</version>
	    </dependency>
    	<dependency>
		    <groupId>commons-codec</groupId>
		    <artifactId>commons-codec</artifactId>
		    <version>1.15</version>
		</dependency>
        <dependency>
            <groupId>eu.bitwalker</groupId>
            <artifactId>UserAgentUtils</artifactId>
            <version>1.21</version>
        </dependency>
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>3.17.1</version>
		</dependency>
		<dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>
        <!--Knife4j-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>3.0.2</version>
        </dependency>
		<dependency>
			<groupId>p6spy</groupId>
			<artifactId>p6spy</artifactId>
			<version>3.9.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-spring</artifactId>
			<version>1.10.1</version>
		</dependency>
		<dependency>
		    <groupId>com.alibaba</groupId>
		    <artifactId>easyexcel</artifactId>
		    <version>3.3.2</version>
		</dependency>
        <!--dataqsdk 内部-->
        <dependency>
			<groupId>com.aliyun.dataq.service.dbus</groupId>
			<artifactId>dbus-sdk</artifactId>
			<version>1.0.3-SNAPSHOT</version>
		</dependency>
        <!--dataqsdk 外部-->
        <!--        <dependency>-->
        <!--			<groupId>com.aliyun.dataq.service.dbus</groupId>-->
        <!--			<artifactId>dbus-sdk</artifactId>-->
        <!--			<version>1.0.0</version>-->
        <!--		</dependency>-->
        <!--pgsql驱动-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!--数据探索SDK 23-04-12 zyp-->
        <dependency>
			<groupId>com.aliyun.brain</groupId>
			<artifactId>data-industry-sdk</artifactId>
			<version>3.1.9-SNAPSHOT</version>
		</dependency>
        <!-- 阿里巴巴安全包 -->
        <dependency>
			<groupId>com.alibaba.security</groupId>
			<artifactId>security-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>com.yomahub</groupId>
			<artifactId>tlog-all-spring-boot-starter</artifactId>
			<version>1.5.2</version>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.cainiao.cntech.dsct</groupId>-->
<!--			<artifactId>cntech-dsct-common</artifactId>-->
<!--			<version>${cntech-dsct-common.version}</version>-->
<!--		</dependency>-->

		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<version>5.11.4</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<version>5.11.4</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
	</dependencies>
</project>
