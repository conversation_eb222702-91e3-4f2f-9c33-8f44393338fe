{"name": "transaction", "version": "0.0.0", "build": "7", "main": "./transaction", "honeycomb": {"processorNum": 1, "env": {"JAVA_OPTS": "-server -Xms2g -Xmx2g -Xmn1g -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m -XX:MaxDirectMemorySize=1g -XX:SurvivorRatio=10 -XX:+UseConcMarkSweepGC -XX:CMSMaxAbortablePrecleanTime=5000 -XX:+CMSClassUnloadingEnabled -XX:CMSInitiatingOccupancyFraction=80 -XX:+UseCMSInitiatingOccupancyOnly -XX:+ExplicitGCInvokesConcurrent -Dsun.rmi.dgc.server.gcInterval=********** -XX:ParallelGCThreads=2 -Xloggc:/home/<USER>/honeycomb/logs/java-gc.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/home/<USER>/honeycomb/logs/java.hprof"}, "service": {"router": "/", "exec": "java", "cwd": "./transaction/", "argv": ["-server", "-Xms2g", "-Xmx2g", "-Xmn1g", "-javaagent:/home/<USER>/arms/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar", "-Darms.licenseKey=${ARMS_KEY}", "-Darms.appName=${ARMS_APPNAME}", "-Dproject.name=transaction", "-DLOG_PATH=/home/<USER>/honeycomb/logs/transaction/", "-Dsun.rmi.dgc.client.gcInterval=**********", "-Djava.awt.headless=true", "-Dsun.net.client.defaultConnectTimeout=10000", "-Dsun.net.client.defaultReadTimeout=30000", "-Dfile.encoding=UTF-8", "-Dmanagement.info.build.mode=full", "-Dspring.profiles.active=shuguang", "-Dspring.config.location=/home/<USER>/honeycomb/conf/transaction.properties", "org.springframework.boot.loader.JarLauncher"], "type": "http", "upstream": "port", "bind": 80}}}