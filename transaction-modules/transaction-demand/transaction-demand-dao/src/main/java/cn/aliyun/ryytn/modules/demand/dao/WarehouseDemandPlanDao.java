package cn.aliyun.ryytn.modules.demand.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandPlanMarkDto;

/**
 * @Description 分仓需求计划Dao
 * <AUTHOR>
 * @date 2024/2/28 17:39
 */
public interface WarehouseDemandPlanDao
{
    /**
     *
     * @Description 新增分仓需求计划标记数据
     * @param warehouseDemandPlanMarkDto
     * <AUTHOR>
     * @date 2024年02月28日 17:39
     */
    void addWarehouseDemandPlanMark(WarehouseDemandPlanMarkDto warehouseDemandPlanMarkDto);

    /**
     *
     * @Description 查询分仓需求计划标记列表
     * @param warehouseDemandPlanMarkDto
     * @return List<WarehouseDemandPlanMarkDto>
     * <AUTHOR>
     * @date 2024年02月28日 17:53
     */
    List<WarehouseDemandPlanMarkDto> queryWarehouseDemandPlanMarkList(WarehouseDemandPlanMarkDto warehouseDemandPlanMarkDto);

    /**
     *
     * @Description 删除分仓需求计划标记数据
     * @param demandPlanCode
     * @param versionId
     * <AUTHOR>
     * @date 2024年02月28日 19:17
     */
    void deleteWarehouseDemandPlanMark(@Param("demandPlanCode") String demandPlanCode, @Param("versionId") String versionId);
}
