package cn.aliyun.ryytn.modules.demand.dataqdao;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetRspVo;

/**
 * @Description Dataq渠道需求提报数据库接口
 * <AUTHOR>
 * @date 2023/12/20 10:33
 */
public interface DataqSaleTargetDao
{
    /**
     *
     * @Description 查询销售目标动态表头
     * @param querySaleTargetReqVo
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 17:21
     */
    List<String> querySaleTargetHeadList(QuerySaleTargetReqVo querySaleTargetReqVo);

    /**
     *
     * @Description 查询渠道需求提报表头下拉列表
     * @param QuerySaleTargetReqVo
     * @return List<QuerySaleTargetRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<QuerySaleTargetRspVo> querySaleTargetHeadSelect(QuerySaleTargetReqVo QuerySaleTargetReqVo);

    /**
     *
     * @Description 查询渠道需求提报分组聚合列表
     * @param QuerySaleTargetReqVo
     * @return List<QuerySaleTargetRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    List<QuerySaleTargetRspVo> querySaleTargetGroupList(QuerySaleTargetReqVo QuerySaleTargetReqVo);

    /**
     *
     * @Description 查询渠道需求提报数据唯一键列表，先分页，再分组聚合，提高性能
     * @param QuerySaleTargetReqVo
     * @return List<QuerySaleTargetRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QuerySaleTargetRspVo> querySaleTargetDataKeyList(QuerySaleTargetReqVo QuerySaleTargetReqVo);

    /**
     *
     * @Description 查询渠道需求提报数据列表（动态字段json格式）
     * @param QuerySaleTargetReqVo
     * @return List<QuerySaleTargetListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QuerySaleTargetRspVo> querySaleTargetDataJsonList(QuerySaleTargetReqVo QuerySaleTargetReqVo);

    /**
     *
     * @Description 查询渠道需求提报数据汇总
     * @param QuerySaleTargetReqVo
     * @return List<ChannelDemandReportDataVo>
     * <AUTHOR>
     * @date 2023年12月21日 14:38
     */
    List<ChannelDemandReportDataVo> querySaleTargetSummary(QuerySaleTargetReqVo QuerySaleTargetReqVo);
}
