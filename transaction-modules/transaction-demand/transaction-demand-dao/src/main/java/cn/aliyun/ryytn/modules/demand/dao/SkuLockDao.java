package cn.aliyun.ryytn.modules.demand.dao;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.dto.LockChannelDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.SkuLockDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockVo;

/**
 * @Description 产品锁定期dao层
 * <AUTHOR>
 * @date 2023/10/26 15:01
 */
public interface SkuLockDao
{
    List<SkuLockRspVo> pageSkuLocList(SkuLockDto skuLockDto);

    void addSkuLock(SkuLockDto skuLockDto);

    void addSkuLockChannel(List<LockChannelDto> lockChannelList);

    void deleteSkuLock(String id);

    void deleteLockChannelIds(String id);

    /**
     *
     * @Description 查询时间范围内的锁定期数据
     * @param SkuLockVo
     * @return List<SkuLockVo>
     * <AUTHOR>
     * @date 2023年12月04日 20:26
     */
    List<SkuLockVo> querySkuLockList(SkuLockVo skuLockVo);

    /**
     *
     * @Description 查询产品锁定期范围（平铺）
     * @param skuLockVo
     * @return List<SkuLockVo>
     * <AUTHOR>
     * @date 2024年01月18日 15:12
     */
    List<SkuLockVo> queryLockList(SkuLockVo skuLockVo);
}
