package cn.aliyun.ryytn.modules.demand.dao;

import cn.aliyun.ryytn.modules.demand.entity.dto.LockChannelDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.SkuLockDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockVo;

import java.util.List;

/**
 * @Description 调拨计划版本数据表
 * <AUTHOR>
 * @date 2023/10/26 15:01
 */
public interface FreightVersionDao
{
    /**
     * 删除临时表数据
     */
   void deleteTempTable();

    /**
     * 插入到临时表中
     */
   void insertTempTable();
    /**
     * 删除正式表数据
     */
   void deleteVersionTable();
    /**
     * 从临时表中获取数据插入到正式表中
     */
   void insertVersionTable();


    /**
     * 定时执行日分仓调拨计划视图
     */
    void refreshAiDailyWarehouseView();
}
