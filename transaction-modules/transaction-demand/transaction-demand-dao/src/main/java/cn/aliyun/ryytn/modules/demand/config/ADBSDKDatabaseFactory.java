package cn.aliyun.ryytn.modules.demand.config;

import com.alibaba.cloud.analyticdb.adb4pgclient.DatabaseConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version V1.0
 * @title ADBSDKConfig
 * @description 通过SDK操作ADBPG的工厂类
 * @date 2024/1/29 11:03
 **/
@Configuration
public class ADBSDKDatabaseFactory {

    @Value("${spring.datasource.druid.dataq.url}")
    private String url;
    @Value("${spring.datasource.druid.dataq.username}")
    private String username;
    @Value("${spring.datasource.druid.dataq.password}")
    private String password;

    private final static Pattern DB_REGX = Pattern.compile("^.+postgresql://(.+):(\\d+)/(\\w+)\\?.+");

    @Bean
    @Scope("prototype")
    public DatabaseConfig databaseConfig() {
        Matcher matcher = DB_REGX.matcher(url);
        if (!matcher.matches()) {
            throw new RuntimeException(String.format("ADBPG配置参数异常,%s", url));
        }
        DatabaseConfig databaseConfig = new DatabaseConfig();
        databaseConfig.setHost(matcher.group(1));
        databaseConfig.setUser(username);
        databaseConfig.setPassword(password);
        databaseConfig.setPort(Integer.parseInt(matcher.group(2)));
        databaseConfig.setDatabase(matcher.group(3));
        return databaseConfig;
    }
}
