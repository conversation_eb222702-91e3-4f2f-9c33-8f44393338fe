package cn.aliyun.ryytn.modules.demand.dataqdao;

import cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivitySkuDetailDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-09-04 16:24
 * @Description
 */

public interface DataqMarketActivitySkuDetailDao {

    List<MarketActivitySkuDetailDO> queryMarketActivitySkuDetailList(MarketActivitySkuDetailDO marketActivitySkuDetailDO);

    int batchInsertSkuDetail(List<MarketActivitySkuDetailDO> marketActivitySkuDetailDOList);

    int deleteSkuDetail(@Param("actiCode")String actiCode);

}
