package cn.aliyun.ryytn.modules.demand.dao;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportVersionVo;

/**
 * @Description 渠道需求提报Dao
 * <AUTHOR>
 * @date 2024/2/20 15:56
 */
public interface ChannelDemandReportDao
{
    /**
     *
     * @Description 新增渠道需求提报版本
     * @param channelDemandReportVersionDto
     * <AUTHOR>
     * @date 2024年02月20日 15:58
     */
    void addChannelDemandReportVersion(ChannelDemandReportVersionDto channelDemandReportVersionDto);

    /**
     *
     * @Description 修改渠道需求提报版本锁定状态
     * @param channelDemandReportVersionDto
     * <AUTHOR>
     * @date 2024年02月20日 15:58
     */
    void updateChannelDemandReportVersionLock(ChannelDemandReportVersionDto channelDemandReportVersionDto);

    /**
     *
     * @Description 查询渠道需求提报版本列表
     * @param channelDemandReportVersionDto
     * @return List<ChannelDemandReportVersionVo>
     * <AUTHOR>
     * @date 2024年02月20日 16:00
     */
    List<ChannelDemandReportVersionVo> queryChannelDemandReportVersionList(ChannelDemandReportVersionVo channelDemandReportVersionVo);


    /**
     *
     * @Description 查询渠道需求提报版本
     * @param rollingVersion
     * @return ChannelDemandReportVersionDto
     * <AUTHOR>
     * @date 2024年02月20日 16:00
     */
    ChannelDemandReportVersionDto queryChannelDemandReportVersion(String rollingVersion);

    /**
     *
     * @Description 追加渠道需求计划版本已编辑三级渠道编号
     * @param channelDemandReportVersionDto
     * <AUTHOR>
     * @date 2024年04月01日 15:36
     */
    void appendChannelDemandReportVersionLv3ChannelCodes(ChannelDemandReportVersionDto channelDemandReportVersionDto);

    /**
     *
     * @Description 修改渠道需求计划版本已编辑三级渠道编号
     * @param channelDemandReportVersionDto
     * <AUTHOR>
     * @date 2024年04月01日 15:36
     */
    void updateChannelDemandReportVersionLv3ChannelCodes(ChannelDemandReportVersionDto channelDemandReportVersionDto);

    /**
     *
     * @Description 查询指定渠道需求提报版本易操作三级渠道编号集合
     * @param rollingVersion
     * @return String
     * <AUTHOR>
     * @date 2024年04月01日 15:50
     */
    String queryChannelDemandReportVersionLv3ChannelCodes(String rollingVersion);
}
