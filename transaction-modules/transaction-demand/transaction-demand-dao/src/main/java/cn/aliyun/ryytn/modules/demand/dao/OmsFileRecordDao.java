package cn.aliyun.ryytn.modules.demand.dao;


import cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto;

import java.util.List;

/**
 * @Description OMS同步数据文件DAO
 * <AUTHOR>
 * @date 2024/7/9 16:17
 */
public interface OmsFileRecordDao
{

    /**
     * 新增oms同步数据文件
     * @param omsFileRecordDto
     */
    void addOmsFileRecord(OmsFileRecordDto omsFileRecordDto);


    /**
     * 更新oms文件记录,将未发送的版本的数据更新为已经发送,但描述字段备注为"新的版本数据已覆盖,无需发送"
     * @param omsFileRecordDto
     */
    void updateNoSendRecordInvalid(OmsFileRecordDto omsFileRecordDto);


    /**
     * 获取要发送的同步文件信息
     * @return
     */
    List<OmsFileRecordDto> getSendOmsFiles();


    /**
     * 批量更新发送状态
     * @param omsFileRecordDtoList
     * @return
     */
    int updateSendResult(List<OmsFileRecordDto> omsFileRecordDtoList);
}
