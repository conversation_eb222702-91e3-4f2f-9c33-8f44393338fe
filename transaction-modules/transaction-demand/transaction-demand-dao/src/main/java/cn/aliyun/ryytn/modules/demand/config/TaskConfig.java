package cn.aliyun.ryytn.modules.demand.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @title TaskConfig
 * @description TODO
 * @date 2024/1/30 15:21
 **/
@Configuration
public class TaskConfig {

    @Bean("taskCurrentExecutor")
    public ExecutorService taskCurrentExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(16,
                32,
                30,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy());
        return TtlExecutors.getTtlExecutorService(executor);
    }

}
