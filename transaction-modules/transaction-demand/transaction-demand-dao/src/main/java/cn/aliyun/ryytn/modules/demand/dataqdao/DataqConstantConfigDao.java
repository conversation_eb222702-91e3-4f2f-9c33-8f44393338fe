package cn.aliyun.ryytn.modules.demand.dataqdao;

import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketConstantReqVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-08-25 15:43
 * @Description 活动常量配置
 */
public interface DataqConstantConfigDao {

    /**
     * 查询活动常量配置
     * @param queryMarketConstantReqVo
     * @return
     */
    List<Map<String ,Object>> queryMarketConstantConfigList(QueryMarketConstantReqVo queryMarketConstantReqVo);
}
