package cn.aliyun.ryytn.modules.demand.dataqdao;

import cn.aliyun.ryytn.modules.demand.entity.dto.AbcTypeDto;

import java.util.List;

/**
 * @Description abc分类
 * <AUTHOR>
 * @date 2023/09/14 10:42
 */
public interface DataqAbcTypeDao {

     /**
      * 查询abc分类
      * @param abcTypeDto
      * @return
      */
     List<AbcTypeDto> queryAbcTypes(AbcTypeDto abcTypeDto);


     /**
      * 根据skucode分组查询最大的abc分类,不区分tob、toc
      * @param abcTypeDto
      * @return
      */
     AbcTypeDto queryAbcTypeGoupBySkucode(AbcTypeDto abcTypeDto);
}
