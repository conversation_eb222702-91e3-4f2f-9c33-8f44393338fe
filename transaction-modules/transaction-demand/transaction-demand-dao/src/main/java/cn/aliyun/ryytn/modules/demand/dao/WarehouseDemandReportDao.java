package cn.aliyun.ryytn.modules.demand.dao;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportTemplateDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportVersionDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ImportWarehouseDemandReportVo;

/**
 * @Description 分仓需求提报Dao接口
 * <AUTHOR>
 * @date 2023/11/27 10:27
 */
public interface WarehouseDemandReportDao
{
    /**
     *
     * @Description 创建分区表
     * @param partitionList
     * <AUTHOR>
     * @date 2023年12月08日 18:21
     */
    void createPartitionTable(List<String> partitionList);

    /**
     *
     * @Description 新增分仓需求提报数据接口
     * @param warehouseDemandReportList
     * <AUTHOR>
     * @date 2023年11月29日 9:34
     */
    void addWarehouseDemandReport(List<WarehouseDemandReportDto> warehouseDemandReportList);

    /**
     *
     * @Description 新增分仓需求提报数据接口
     * @param warehouseDemandReport
     * @return Integer
     * <AUTHOR>
     * @date 2023年11月29日 9:34
     */
    Integer batchAddWarehouseDemandReport(WarehouseDemandReportDto warehouseDemandReport);

    /**
     *
     * @Description 查询分仓需求提报对应渠道需求计划列表
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 11:55
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportPlanList();

    /**
     *
     * @Description 查询分仓需求提报版本列表
     * @param demandPlanCode
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月06日 16:34
     */
    List<String> queryWarehouseDemandReportVersionList(String demandPlanCode);

    /**
     *
     * @Description 查询分仓需求提报版本时间范围列表
     * @param rollingVersion
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月16日 15:34
     */
    List<String> queryWarehouseDemandReportDateList(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 查询分仓需求提报数据列表
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * <AUTHOR>
     * @date 2023年11月30日 20:56
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportList(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 查询分仓需求提报数据动态表头列表
     * @param warehouseDemandReportDto
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    List<String> queryWarehouseDemandReportHeadList(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 查询分仓需求提报表头下拉列表
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportHeadSelect(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 查询分仓需求提报分组聚合列表
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportGroupList(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 查询分仓需求提报数据唯一键列表，先分页，再分组聚合，提高性能
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportDataKeyList(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 查询分仓需求提报数据列表（动态字段json格式）
     * @param warehouseDemandReportDto
     * @return List<QueryWarehouseDemandReportListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportDataJsonList(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 查询分仓需求提报数据汇总
     * @param warehouseDemandReportDto
     * @return List<ChannelDemandReportDataVo>
     * <AUTHOR>
     * @date 2023年12月21日 14:38
     */
    List<ChannelDemandReportDataVo> queryWarehouseDemandReportSummary(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 查询分仓需求提报模板列表
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportTemplateDto>
     * <AUTHOR>
     * @date 2023年11月30日 20:56
     */
    List<WarehouseDemandReportTemplateDto> queryWarehouseDemandReportTemplateList(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 批量导入分仓需求提报
     * @param warehouseDemandReportList
     * <AUTHOR>
     * @date 2023年12月09日 14:58
     */
    int batchImportWarehouseDemandReport(ImportWarehouseDemandReportVo warehouseDemandReport);

    /**
     *
     * @Description 批量导入分仓需求提报
     * @param warehouseDemandReportList
     * <AUTHOR>
     * @date 2023年12月09日 14:58
     */
    void importWarehouseDemandReport(List<ImportWarehouseDemandReportVo> warehouseDemandReportList);

    /**
     *
     * @Description 根据编号查询分仓需求提报数据
     * @param ids
     * @return List<WarehouseDemandReportDto>
     * <AUTHOR>
     * @date 2023年12月09日 17:22
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportDataByIds(List<String> ids);

    /**
     *
     * @Description 批量修改分仓需求提报数据
     * @param dataList
     * <AUTHOR>
     * @date 2023年12月09日 17:38
     */
    void batchUpdateWarehouseDemandReport(List<WarehouseDemandReportDto> dataList);

    /**
     *
     * @Description 新增分仓需求提报数据修改历史
     * @param dataList
     * <AUTHOR>
     * @date 2023年12月09日 17:45
     */
    void addWarehouseDemandReportHistory(List<WarehouseDemandReportDto> dataList);

    /**
     *
     * @Description 查询分仓需求提报数据修改记录
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * <AUTHOR>
     * @date 2023年12月09日 17:52
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportHistoryList(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 删除分仓需求提报版本
     * @param demandPlanCode
     * <AUTHOR>
     * @date 2024年02月02日 20:54
     */
    void deleteWarehouseDemandReportVersion(String demandPlanCode);

    /**
     *
     * @Description 删除分仓需求提报版本
     * @param demandPlanCode
     * <AUTHOR>
     * @date 2023年12月11日 16:24
     */
    void deleteWarehouseDemandReport(String demandPlanCode);

    /**
     *
     * @Description 查询分仓需求提报版本是否存在
     * @param warehouseDemandReportDto
     * @return int
     * <AUTHOR>
     * @date 2024年01月12日 16:01
     */
    int queryWarehouseDemandReportVersionExists(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 新增分仓需求提报版本
     * @param warehouseDemandReportVersion
     * <AUTHOR>
     * @date 2024年02月02日 20:49
     */
    void addWarehouseDemandReportVersion(WarehouseDemandReportVersionDto warehouseDemandReportVersion);

    /**
     *
     * @Description 查询分仓需求提报数据数量
     * @param warehouseDemandReportDto
     * @return int
     * <AUTHOR>
     * @date 2024年04月11日 14:24
     */
    int queryWarehouseDemandReportCount(WarehouseDemandReportDto warehouseDemandReportDto);
}
