package cn.aliyun.ryytn.modules.demand.repository.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.cloud.analyticdb.adb4pgclient.Adb4pgClient;
import com.alibaba.cloud.analyticdb.adb4pgclient.DatabaseConfig;
import com.alibaba.cloud.analyticdb.adb4pgclient.Row;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.concurrent.CompletableFutureUtil;
import cn.aliyun.ryytn.modules.demand.config.ADBSDKDatabaseFactory;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportDto;
import cn.aliyun.ryytn.modules.demand.repository.DataqChannelDemandReportRepository;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @version V1.0
 * @title DataqChannelDemandReportRepositoryImpl
 * @description 需求提报持久层实现
 * @date 2024/1/30 15:06
 **/
@Slf4j
@Repository
public class DataqChannelDemandReportRepositoryImpl implements DataqChannelDemandReportRepository
{
    //以下为SDK使用参数
    private static final Map<String, List<String>> TABLE_COLUMNS = new ConcurrentHashMap<>();
    private static final String SCHEMA_NAME = "cdop_biz";
    private DatabaseConfig databaseConfig;

    public DataqChannelDemandReportRepositoryImpl(ADBSDKDatabaseFactory factory)
    {
        initTableColumns();
        databaseConfig = factory.databaseConfig();
        databaseConfig.addTable(new ArrayList<>(TABLE_COLUMNS.keySet()), SCHEMA_NAME);
        TABLE_COLUMNS.forEach((k, v) -> {
            databaseConfig.setColumns(v, k, SCHEMA_NAME);
        });
        // If the value of column is empty, set null
        databaseConfig.setEmptyAsNull(true);
        // commit时，写入数据库出现异常时重试的3次。
        databaseConfig.setRetryTimes(2);
        // 重试间隔的时间为1s，单位是ms。
        databaseConfig.setRetryIntervalTime(500);
        databaseConfig.setInsertIgnore(true);
    }

    private static final int SIZE = 1000;
    @Resource
    private DataqChannelDemandReportDao mapper;

    @Resource(name = "taskCurrentExecutor")
    private ExecutorService executorService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveBatch(List<ChannelDemandReportDto> dtos, Map<ChannelDemandReportDto, ChannelDemandReportDto> mapIdBeyKey)
    {
        try
        {
//            StopWatch saveBatchClock = new StopWatch("saveBatchClock");
            String rollingVersion = dtos.stream().map(ChannelDemandReportDto::getRollingVersion).
                filter(Objects::nonNull).findFirst().orElseThrow(() -> new ServiceException("导入的需求提报版本不能为空"));
            Optional<String> bizDateType = dtos.stream()
                .map(ChannelDemandReportDto::getBizDateType)
                .filter(Objects::nonNull).findFirst();
            String userId = "超级管理员";
            Set<Long> oldSet = new HashSet<>();
            List<ChannelDemandReportDto> newList = new ArrayList<>();
//            saveBatchClock.start("packageData");
            for (ChannelDemandReportDto reportingDO : dtos)
            {
                //循环历史数据，根据特征值及订单值是否有变化进行筛选需要修改的数据
                Optional<ChannelDemandReportDto> optionalMap = Optional.ofNullable(mapIdBeyKey.get(reportingDO));
                if (optionalMap.isPresent())
                {
                    String dataCycle = reportingDO.getBizDateValue();
                    if(Objects.isNull(optionalMap.get().getValues().get(dataCycle))){
                        log.warn("datacycle {} value is not exists,please check,obj content{}",dataCycle,JSONObject.toJSONString(reportingDO));
                    }
                    if (!NumberUtil.equals(Double.parseDouble(optionalMap.get().getValues().get(dataCycle).toString()), reportingDO.getOrderNum()))
                    {
                        //记录修改过的id
                        reportingDO.setPreviousOrderNum(Double.parseDouble(optionalMap.get().getValues().get(dataCycle+"_PRE").toString()));
                        reportingDO.setCreator(StringUtils.isNotBlank(reportingDO.getCreator()) ? reportingDO.getCreator() : userId);
                        reportingDO.setLastModifier(StringUtils.isNotBlank(reportingDO.getLastModifier()) ? reportingDO.getLastModifier() : userId);
                        oldSet.add(Long.valueOf(optionalMap.get().getValues().get(dataCycle+"_ID").toString()));
                        newList.add(reportingDO);
                    }
                }
                else
                {
                    log.info("数据为新增，做丢弃处理:{}", JSONObject.toJSONString(reportingDO));
//                    reportingDO.setCreator(StringUtils.isNotBlank(reportingDO.getCreator()) ? reportingDO.getCreator() : userId);
//                    reportingDO.setLastModifier(StringUtils.isNotBlank(reportingDO.getLastModifier()) ? reportingDO.getLastModifier() : userId);
//                    newSet.add(reportingDO);
                }
            }
//            saveBatchClock.stop();
            boolean flg = true;
            if (!CollectionUtils.isEmpty(oldSet))
            {
//                saveBatchClock.start("updatemodify1");
//                log.info("导入销售提报数据，删除旧数据，条数{}",oldSet.size());
                flg = mapper.batchUpdate(ChannelDemandReportDto.builder().isModify(1).gmtModify(new Date()).lastModifier(userId).build(), oldSet,rollingVersion.substring(3,9));
//                saveBatchClock.stop();
            }
            if (flg)
            {
                if (!CollectionUtils.isEmpty(newList))
                {
//                    log.info("导入销售提报数据，插入新数据，条数{}",newList.size());
//                    saveBatchClock.start("sdkAdd");
                    flg = this.sdkAdd(newList,dtos.get(0).getRollingVersion().substring(3,9));
//                    saveBatchClock.stop();
                }
            }
            if (!flg)
            {
                throw new ServiceException("对应需求提报修改失败");
            }
//            log.info("saveBatchClock cost:{}s ",saveBatchClock.getTotalTimeSeconds());
//            log.info("saveBatchClock cost:{}",saveBatchClock.prettyPrint());
            return flg;
        }
        finally
        {
//            log.info("导入销售提报数据完成");
        }
    }

    public Boolean sdkAdd(Collection<ChannelDemandReportDto> list,String tableSuffix)
    {
        try
        {
            AtomicLong maxId;
            synchronized (this)
            {
                maxId = new AtomicLong(mapper.getSeq(list.size()));
            }
            String tableName = "tdm_xqyc_txn_sku_reporting_di_"+tableSuffix;
            if(!TABLE_COLUMNS.containsKey(tableName)){
                TABLE_COLUMNS.put(tableName,TABLE_COLUMNS.get("tdm_xqyc_txn_sku_reporting_di"));
                databaseConfig.addTable(new ArrayList<>(TABLE_COLUMNS.keySet()), SCHEMA_NAME);
                TABLE_COLUMNS.forEach((k, v) -> {
                    databaseConfig.setColumns(v, k, SCHEMA_NAME);
                });
            }
            Adb4pgClient adbClient = new Adb4pgClient(databaseConfig);
            String now = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());

            List<Row> rowsAdd = list.stream().map(rdo -> {
                List<Object> values = new ArrayList<>();
                values.add(maxId.getAndAdd(-1));
                values.add(now);
                values.add(rdo.getLastModifier());
                values.add(0);
                values.add(rdo.getBizDateType());
                values.add(rdo.getBizDateValue());
                values.add(rdo.getRollingVersion());
                values.add(rdo.getSkuCode());
                values.add(rdo.getSkuName());
                values.add(rdo.getLv1CategoryCode());
                values.add(rdo.getLv1CategoryName());
                values.add(rdo.getLv2CategoryCode());
                values.add(rdo.getLv2CategoryName());
                values.add(rdo.getLv3CategoryCode());
                values.add(rdo.getLv3CategoryName());
                values.add(rdo.getLv1ChannelCode());
                values.add(rdo.getLv1ChannelName());
                values.add(rdo.getLv2ChannelCode());
                values.add(rdo.getLv2ChannelName());
                values.add(rdo.getLv3ChannelCode());
                values.add(rdo.getLv3ChannelName());
                values.add(rdo.getOrderNum());
                values.add(rdo.getUnit());
                values.add(rdo.getExtend());
                values.add(rdo.getCreator());
                values.add(rdo.getPreviousOrderNum());
                values.add(rdo.getDeviationScore());
                values.add(now);
                values.add(0);
                Row row = new Row(values.size());
                row.setColumnValues(values);
                return row;
            }).collect(Collectors.toList());
            Lists.partition(rowsAdd, SIZE).forEach(rows -> {
                adbClient.addRows(rows, "tdm_xqyc_txn_sku_reporting_di_"+tableSuffix, SCHEMA_NAME);
            });
            adbClient.commit();
            return true;
        }
        catch (Exception e)
        {
            log.error("通过ADBSDK导入数据失败，异常信息：", e);
            return false;
        }
    }

    private List<ChannelDemandReportDto> queryDuplicate(List<ChannelDemandReportDto> items,
        String rollingVersion,
        String bizDateType)
    {
        List<List<ChannelDemandReportDto>> lists = Lists.partition(items, SIZE);
        List<ChannelDemandReportDto> all = CompletableFutureUtil.getResultApplyCompletableFuture(lists,
            executorService,
            list -> mapper.queryDuplicate(list, rollingVersion, bizDateType,null,null,null)).stream().flatMap(Collection::stream).collect(Collectors.toList());
        return all;
    }

    private static void initTableColumns()
    {
        List<String> columns = new ArrayList<>();
        columns.add("id");
        columns.add("gmt_modify");
        columns.add("last_modifier");
        columns.add("is_modify");
        columns.add("biz_date_type");
        columns.add("biz_date_value");
        columns.add("rolling_version");
        columns.add("sku_code");
        columns.add("sku_name");
        columns.add("lv1_category_code");
        columns.add("lv1_category_name");
        columns.add("lv2_category_code");
        columns.add("lv2_category_name");
        columns.add("lv3_category_code");
        columns.add("lv3_category_name");
        columns.add("lv1_channel_code");
        columns.add("lv1_channel_name");
        columns.add("lv2_channel_code");
        columns.add("lv2_channel_name");
        columns.add("lv3_channel_code");
        columns.add("lv3_channel_name");
        columns.add("order_num");
        columns.add("unit");
        columns.add("extend");
        columns.add("creator");
        columns.add("previous_order_num");
        columns.add("deviation_score");
        columns.add("gmt_create");
        columns.add("is_locked");
        TABLE_COLUMNS.put("tdm_xqyc_txn_sku_reporting_di", columns);
    }
}
