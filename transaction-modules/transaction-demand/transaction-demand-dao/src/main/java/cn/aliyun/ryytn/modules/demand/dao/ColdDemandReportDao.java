package cn.aliyun.ryytn.modules.demand.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportTemplateDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportVersionDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ImportColdDemandReportVo;

/**
 * @Description 低温需求提报Dao
 * <AUTHOR>
 * @date 2023/12/16 11:18
 */
public interface ColdDemandReportDao
{
    /**
     *
     * @Description 创建分区表
     * @param partitionList
     * <AUTHOR>
     * @date 2023年12月08日 18:21
     */
    void createPartitionTable(List<String> partitionList);

    /**
     *
     * @Description 查询历史版本号集合
     * @param rollingVersion
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月16日 11:19
     */
    List<String> queryColdDemandReportVersionList();

    /**
     *
     * @Description 新增低温需求提报版本
     * @param coldDemandReportVersionDto
     * <AUTHOR>
     * @date 2024年02月20日 16:23
     */
    void addColdDemandReportVersion(ColdDemandReportVersionDto coldDemandReportVersionDto);

    /**
     *
     * @Description 新增低温提报数据
     * @param coldDemandReport
     * <AUTHOR>
     * @date 2023年12月16日 13:10
     */
    Integer addColdDemandReport(ColdDemandReportDto coldDemandReport);

    /**
     *
     * @Description 根据历史版本修改提报数据
     * @param rollingVersion
     * @param tableSuffix
     * @param latestRollingVersion
     * @param latestTableSuffix
     * <AUTHOR>
     * @date 2023年12月16日 13:12
     */
    void updateOrderNumFromLatestVersion(@Param("rollingVersion") String rollingVersion, @Param("tableSuffix") String tableSuffix,
        @Param("latestRollingVersion") String latestRollingVersion, @Param("latestTableSuffix") String latestTableSuffix);

    /**
     *
     * @Description 查询低温需求提报版本时间范围列表
     * @param coldDemandReportDto
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月16日 15:34
     */
    List<String> queryColdDemandReportDateList(ColdDemandReportDto coldDemandReportDto);

    /**
     *
     * @Description 查询低温需求提报数据列表
     * @param coldDemandReportDto
     * @return List<ColdDemandReportDto>
     * <AUTHOR>
     * @date 2023年12月18日 9:36
     */
    List<ColdDemandReportDto> queryColdDemandReportDataList(ColdDemandReportDto coldDemandReportDto);

    /**
     *
     * @Description 查询低温需求提报数据列表
     * @param coldDemandReportDto
     * @return List<ColdDemandReportDto>
     * <AUTHOR>
     * @date 2023年12月16日 15:32
     */
    List<ColdDemandReportDto> queryColdDemandReportList(ColdDemandReportDto coldDemandReportDto);

    /**
     *
     * @Description 批量修改低温提报数据
     * @param dataList
     * <AUTHOR>
     * @date 2023年12月16日 17:03
     */
    void batchUpdateColdDemandReport(List<ColdDemandReportDto> dataList);

    /**
     *
     * @Description 查询低温提报数据导入模板
     * @param coldDemandReportDto
     * @return List<ColdDemandReportTemplateDto>
     * <AUTHOR>
     * @date 2023年12月16日 20:19
     */
    List<ColdDemandReportTemplateDto> queryColdDemandReportTemplateList(ColdDemandReportDto coldDemandReportDto);

    /**
     *
     * @Description 批量导入低温提报数据
     * @param dataList
     * <AUTHOR>
     * @date 2023年12月16日 21:39
     */
    void importColdDemandReport(List<ImportColdDemandReportVo> dataList);

    /**
     *
     * @Description 查询低温需求提报版本是否存在
     * @param rollingVersion
     * @return int
     * <AUTHOR>
     * @date 2024年01月11日 16:59
     */
    int queryColdDemandReportVersionExists(String rollingVersion);

    /**
     *
     * @Description 查询低温需求提报表头下拉列表
     * @param coldDemandReportDto
     * @return List<ColdDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<ColdDemandReportDto> queryColdDemandReportHeadSelect(ColdDemandReportDto coldDemandReportDto);

    /**
     *
     * @Description 分组聚合查询低温需求提报列表
     * @param coldDemandReportDto
     * @return List<ColdDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月01日 16:16
     */
    List<ColdDemandReportDto> queryColdDemandReportGroupList(ColdDemandReportDto coldDemandReportDto);
}
