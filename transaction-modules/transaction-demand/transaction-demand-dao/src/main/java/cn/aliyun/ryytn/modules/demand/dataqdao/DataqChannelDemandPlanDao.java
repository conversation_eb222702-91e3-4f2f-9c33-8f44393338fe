package cn.aliyun.ryytn.modules.demand.dataqdao;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandPlan16WeekDto;
import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemanPlanHistoryDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanReceiverDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddChannelDemandPlanVersionVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionGroupListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo;

/**
 * @Description 阿里dataq渠道需求计划Dao
 * <AUTHOR>
 * @date 2023/12/21 10:42
 */
public interface DataqChannelDemandPlanDao
{
    /**
     *
     * @Description 查询渠道需求计划数据动态表头列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    List<String> queryChannelDemandPlanHeadList(QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询渠道需求计划表头下拉列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionGroupListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<QueryChannelDemandPlanVersionGroupListRspVo> queryChannelDemandPlanHeadSelect(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询渠道需求计划数据分组聚合列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionGroupListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    List<QueryChannelDemandPlanVersionGroupListRspVo> queryChannelDemandPlanDataGroupList(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询渠道需求计划数据唯一键列表，先分页，再分组聚合，提高性能
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionGroupListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryChannelDemandPlanVersionGroupListRspVo> queryChannelDemandPlanDataKeyList(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询渠道需求计划数据列表（动态字段json格式）
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionGroupListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryChannelDemandPlanVersionGroupListRspVo> queryChannelDemandPlanDataJsonList(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询渠道需求提报数据汇总
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<PlanValue>
     * <AUTHOR>
     * @date 2023年12月21日 14:38
     */
    List<PlanValue> queryChannelDemandPlanSummary(QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询渠道需求计划版本详情列表（查询dataq接口json反序列化速度太慢，修改为直接查询数据库）
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2024年01月09日 15:06
     */
    List<QueryChannelDemandPlanVersionListRspVo> queryChannelDemandPlanVersionList(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询渠道需求计划版本详情列表（如果计划数据为null，取提报数据）
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2024年01月09日 15:06
     */
    List<QueryChannelDemandPlanVersionListRspVo> queryChannelDemandPlanVersionDataList(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 修改渠道需求计划数据
     * @param dataList
     * <AUTHOR>
     * @date 2024年01月10日 19:20
     */
    void batchUpdateChannelDemandPlanData(List<ChannelDemanPlanHistoryDto> dataList);

    /**
     *
     * @Description 修改渠道需求计划数据
     * @param channelDemanPlanHistoryDto
     * <AUTHOR>
     * @date 2024年01月10日 19:20
     */
    Integer updateChannelDemandPlanData(ChannelDemanPlanHistoryDto channelDemanPlanHistoryDto);

    /**
     *
     * @Description 查询渠道需求计划当前值
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<ChannelDemanPlanHistoryDto>
     * <AUTHOR>
     * @date 2024年01月10日 19:16
     */
    List<ChannelDemanPlanHistoryDto> queryChannelDemandPlanOldDataList(QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询版本是否已存在
     * @param addChannelDemandPlanVersionVo
     * @return int
     * <AUTHOR>
     * @date 2024年01月11日 16:33
     */
    int queryChannelDemandPlanVersionExists(AddChannelDemandPlanVersionVo addChannelDemandPlanVersionVo);

    /**
     *
     * @Description 查询名称是否已存在
     * @param addDemandPlanListReqVo
     * @return int
     * <AUTHOR>
     * @date 2024年01月11日 16:33
     */
    int queryChannelDemandPlanNameExists(AddDemandPlanListReqVo addDemandPlanListReqVo);

    /**
     *
     * @Description 查询偏差比对渠道需求计划列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2024年01月26日 14:49
     */
    List<QueryChannelDemandPlanVersionListRspVo> queryDeviationChannelDemandPlanList(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询渠道需求计划名称
     * @param demandPlanCode
     * @return String
     * <AUTHOR>
     * @date 2024年02月02日 20:46
     */
    String queryChannelDemandPlanName(String demandPlanCode);

    /**
     *
     * @Description 根据产品渠道分组查询渠道需求计划（用于查询同步认养系统中间表基础数据）
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2024年03月26日 20:04
     */
    List<QueryChannelDemandPlanVersionListRspVo> queryChannelDemandPlanGroupBySkuChannel(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询渠道需求计划共识版本TOBC基准数据列表
     * @param channelDemandPlanReceiverDto
     * @return List<ChannelDemandPlanReceiverDto>
     * <AUTHOR>
     * @date 2024年04月07日 17:51
     */
    List<ChannelDemandPlanReceiverDto> queryChannelDemandPlanReceiverListJoinOrderRate(ChannelDemandPlanReceiverDto channelDemandPlanReceiverDto);

    /**
     *
     * @Description 查询渠道需求计划共识版本TOBC基准数据列表
     * @param channelDemandPlanReceiverDto
     * @return List<ChannelDemandPlanReceiverDto>
     * <AUTHOR>
     * @date 2024年04月07日 17:51
     */
    List<ChannelDemandPlanReceiverDto> queryChannelDemandPlanReceiverListJoinForecast(ChannelDemandPlanReceiverDto channelDemandPlanReceiverDto);

    /**
     *
     * @Description 查询渠道需求计划复制数据列表
     * @param demandPlanCode
     * @param newVersionId
     * @param oldVersionId
     * <AUTHOR>
     * @date 2024年04月24日 15:23
     */
    void duplicateChannelDemandPlanDuplicateData(@Param("demandPlanCode") String demandPlanCode, @Param("newVersionId") String newVersionId,
        @Param("oldVersionId") String oldVersionId);

    /**
     *
     * @Description 更新渠道需求计划数据状态
     * @param demandPlanCode
     * @param versionId
     * <AUTHOR>
     * @date 2024年04月24日 15:25
     */
    void updateChannelDemandPlanDataStatusTrail(@Param("demandPlanCode") String demandPlanCode, @Param("versionId") String versionId);

    /**
     *
     * @Description 查询指定计划已共识的版本
     * @param demandPlanCode
     * @return List<String>
     * <AUTHOR>
     * @date 2024年05月15日 16:40
     */
    List<String> queryConfirmedVersionList(String demandPlanCode);

    /**
     * 根据分仓需求计划查询历史16week分仓比例数据
     * @param channelDemandPlanReceiverDto
     * @return
     */
    List<WarehouseDemandPlan16WeekDto> queryWareHouseDemandPlan16weekData(ChannelDemandPlanReceiverDto channelDemandPlanReceiverDto);
}
