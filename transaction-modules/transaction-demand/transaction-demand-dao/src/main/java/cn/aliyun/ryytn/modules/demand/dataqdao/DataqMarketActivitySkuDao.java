package cn.aliyun.ryytn.modules.demand.dataqdao;

import cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivitySkuDO;
import cn.aliyun.ryytn.modules.demand.entity.vo.MarketActivitySkuVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketSkuListReqVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-08-25 17:11
 * @Description
 */
public interface DataqMarketActivitySkuDao {

    /**
     * 查询活动产品列表
     * @param queryMarketSkuListReqVo
     * @return
     */
    List<MarketActivitySkuVo> queryMarketActivitySkuList(QueryMarketSkuListReqVo queryMarketSkuListReqVo);


    int updateByActiCode(MarketActivitySkuDO marketActivitySkuDO);


    List<Long> selectIdsByActiCode(@Param("actiCode")String actiCode);

    int batchUpdateMarketActivitySku(List<MarketActivitySkuDO> updateDO);


    int batchInsert(List<MarketActivitySkuDO> saveSkuDOs);

}
