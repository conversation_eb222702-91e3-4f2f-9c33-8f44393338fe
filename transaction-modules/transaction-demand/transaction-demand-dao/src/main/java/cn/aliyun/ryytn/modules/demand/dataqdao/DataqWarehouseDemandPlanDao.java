package cn.aliyun.ryytn.modules.demand.dataqdao;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListRspVo;

/**
 * @Description 阿里dataq分仓需求计划Dao
 * <AUTHOR>
 * @date 2023/12/21 10:42
 */
public interface DataqWarehouseDemandPlanDao
{
    /**
     *
     * @Description 查询分仓需求计划数据动态表头列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    List<String> queryWarehouseDemandPlanHeadList(QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询分仓需求计划表头下拉列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<QueryWarehouseDemandPlanVersionListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanHeadSelect(
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询分仓需求计划数据分组聚合列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<QueryWarehouseDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    List<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanDataGroupList(
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询分仓需求计划数据唯一键列表，先分页，再分组聚合，提高性能
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<QueryWarehouseDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanDataKeyList(
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询分仓需求计划数据列表（动态字段json格式）
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<QueryWarehouseDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanDataJsonList(
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询渠道需求提报数据汇总
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<PlanValue>
     * <AUTHOR>
     * @date 2023年12月21日 14:38
     */
    List<PlanValue> queryWarehouseDemandPlanSummary(QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo);

    /**
     *
     * @Description 查询分仓需求计划版本数量
     * @param warehouseDemandReportDto
     * @return int
     * <AUTHOR>
     * @date 2023年12月11日 15:34
     */
    int queryWarehouseDemandPlanVersionCount(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 修改分仓需求计划版本状态为待试算
     * @param warehouseDemandReportDto
     * <AUTHOR>
     * @date 2024年04月07日 16:27
     */
    void updateWarehouseDemandPlanStatus(WarehouseDemandReportDto warehouseDemandReportDto);

    /**
     *
     * @Description 查询平铺的分仓需求计划数据
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<QueryWarehouseDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2024年04月10日 20:53
     */
    List<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanList(
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo);
}
