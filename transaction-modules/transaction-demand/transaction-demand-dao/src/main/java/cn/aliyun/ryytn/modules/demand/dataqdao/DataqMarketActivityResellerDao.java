package cn.aliyun.ryytn.modules.demand.dataqdao;

import cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityResellerDO;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketChannelListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketChannelListRspVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-08-25 16:22
 * @Description 活动渠道列表
 */
public interface DataqMarketActivityResellerDao {

    /**
     * 查询活动渠道列表
     * @param marketChannelReqVo
     * @return
     */
    List<QueryMarketChannelListRspVo> queryMarketChannelList(QueryMarketChannelListReqVo marketChannelReqVo);


    /**
     * 根据活动编码更新渠道列表数据
     * @param marketActivityResellerDO
     * @return
     */
    int updateByActiCode(MarketActivityResellerDO marketActivityResellerDO);

    List<String> selectResellerCodesByActiCode(@Param("actiCode")String actiCode);

    int batchUpdateByActiCodes(List<MarketActivityResellerDO> marketActivityResellerDOList);

    int batchInsert(List<MarketActivityResellerDO> saveResellerDOs);


}
