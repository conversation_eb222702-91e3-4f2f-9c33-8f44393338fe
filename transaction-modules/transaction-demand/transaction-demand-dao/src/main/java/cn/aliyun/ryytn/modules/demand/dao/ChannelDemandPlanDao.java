package cn.aliyun.ryytn.modules.demand.dao;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemanPlanHistoryDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanDataSyncDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanDataParamVo;

/**
 * @Description 渠道需求计划Dao接口
 * <AUTHOR>
 * @date 2023/11/27 10:27
 */
public interface ChannelDemandPlanDao
{
    /**
     *
     * @Description 删除旧版本需求计划
     * @param demandPlanCode
     * <AUTHOR>
     * @date 2023年11月29日 18:31
     */
    void deleteChannelDemandPlanDataSync(String demandPlanCode);

    /**
     *
     * @Description 新增需求计划数据同步
     * @param syncList
     * <AUTHOR>
     * @date 2023年11月29日 18:30
     */
    void addChannelDemandPlanDataSync(List<ChannelDemandPlanDataSyncDto> syncList);

    /**
     *
     * @Description 批量新增需求计划数据同步
     * @param syncList
     * @return Integer
     * <AUTHOR>
     * @date 2023年11月29日 18:30
     */
    Integer batchAddChannelDemandPlanDataSync(ChannelDemandPlanDataSyncDto channelDemandPlanDataSync);

    /**
     *
     * @Description 查询渠道需求计划同步数据
     * @param channelDemandPlanDataSync
     * @return List<ChannelDemandPlanDataSyncDto>
     * <AUTHOR>
     * @date 2024年05月16日 10:25
     */
    List<ChannelDemandPlanDataSyncDto> queryChannelDemandPlanDataSyncList(ChannelDemandPlanDataSyncDto channelDemandPlanDataSync);

    /**
     *
     * @Description 新增渠道需求计划修改历史
     * @param dataList
     * <AUTHOR>
     * @date 2024年01月10日 19:20
     */
    void batchAddChannelDemandPlanHistory(List<ChannelDemanPlanHistoryDto> dataList);

    /**
     *
     * @Description 新增渠道需求计划修改历史
     * @param channelDemanPlanHistoryDto
     * <AUTHOR>
     * @date 2024年01月10日 19:20
     */
    Integer addChannelDemandPlanHistory(ChannelDemanPlanHistoryDto channelDemanPlanHistoryDto);


    /**
     *
     * @Description 查询渠道需求计划修改历史列表
     * @param channelDemandPlanDataParamVo
     * @return List<ChannelDemanPlanHistoryDto>
     * <AUTHOR>
     * @date 2024年01月10日 19:16
     */
    List<ChannelDemanPlanHistoryDto> queryChannelDemandPlanHistoryList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo);


    /**
     * 渠道需求计划共识表备份
     * @return
     */
    int executeChannelDemandPlanDataSyncBackData();

    /**
     * 清空渠道需求计划共识 同步数据表
     */
    void truncateChannelDemandPlanDataSyncData();

}
