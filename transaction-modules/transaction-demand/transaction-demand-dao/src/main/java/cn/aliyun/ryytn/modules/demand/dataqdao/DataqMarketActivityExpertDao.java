package cn.aliyun.ryytn.modules.demand.dataqdao;

import cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityExpertDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-08-27 20:31
 * @Description
 */
public interface DataqMarketActivityExpertDao {


    List<MarketActivityExpertDO> queryMarketActivityExpertList(MarketActivityExpertDO marketActivityExpertDO);

    int batchInsertExperts(List<MarketActivityExpertDO> saveExpertDOs);

    int updateMarketActivityExpert(MarketActivityExpertDO marketActivityExpertDO);

    int batchUpdateActivityExpertList(List<MarketActivityExpertDO> batchUpdateExpertList);

    List<MarketActivityExpertDO> queryMarketDisExpertList(MarketActivityExpertDO marketActivityExpertDO);
}
