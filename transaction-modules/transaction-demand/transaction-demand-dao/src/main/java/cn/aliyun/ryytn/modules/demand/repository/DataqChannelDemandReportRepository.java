package cn.aliyun.ryytn.modules.demand.repository;

import java.util.List;
import java.util.Map;

import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportDto;

/**
 * <AUTHOR>
 * @version V1.0
 * @title DataqChannelDemandReportRepository
 * @description 需求提报持久层
 * @date 2024/1/30 15:06
 **/
public interface DataqChannelDemandReportRepository
{
    /**
     * 根据数据库内数据进行批量插入
     * @param dtos
     * @return {@link Boolean}
     * <AUTHOR>
     * @date 2024-01-30 15:43
     * @description 根据数据库内数据进行批量插入
     */
    Boolean saveBatch(List<ChannelDemandReportDto> dtos, Map<ChannelDemandReportDto, ChannelDemandReportDto> mapIdBeyKey);
}
