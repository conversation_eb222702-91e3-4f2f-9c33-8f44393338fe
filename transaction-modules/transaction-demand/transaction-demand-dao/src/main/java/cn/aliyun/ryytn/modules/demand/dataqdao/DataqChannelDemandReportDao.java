package cn.aliyun.ryytn.modules.demand.dataqdao;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportTemplateDataDto;
import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportGroupListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListRspVo;

/**
 * <AUTHOR>
 * @Description Dataq渠道需求提报数据库接口
 * @date 2023/12/20 10:33
 */
public interface DataqChannelDemandReportDao
{
    /**
     * @param queryChannelDemandReportListReqVo
     * @return List<String>
     * @Description 查询渠道需求提报数据动态表头列表
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    List<String> queryChannelDemandReportHeadList(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo);

    /**
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportGroupListRspVo>
     * @throws Exception
     * @Description 查询渠道需求提报表头下拉列表
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<QueryChannelDemandReportGroupListRspVo> queryChannelDemandReportHeadSelect(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo);

    /**
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportGroupListRspVo>
     * @Description 查询渠道需求提报分组聚合列表
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    List<QueryChannelDemandReportGroupListRspVo> queryChannelDemandReportGroupList(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo);

    /**
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportListRspVo>
     * @Description 查询渠道需求提报数据唯一键列表，先分页，再分组聚合，提高性能
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryChannelDemandReportListRspVo> queryChannelDemandReportDataKeyList(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo);

    /**
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportListRspVo>
     * @Description 查询渠道需求提报数据列表（动态字段json格式）
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryChannelDemandReportListRspVo> queryChannelDemandReportDataJsonList(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo);

    /**
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportListRspVo>
     * @Description 查询渠道需求提报数据列表（平铺）
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryChannelDemandReportListRspVo> queryChannelDemandReportDataList(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo);

    /**
     * @param queryChannelDemandReportListReqVo
     * @return List<ChannelDemandReportDataVo>
     * @Description 查询渠道需求提报数据汇总
     * <AUTHOR>
     * @date 2023年12月21日 14:38
     */
    List<ChannelDemandReportDataVo> queryChannelDemandReportSummary(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo);

    /**
     * @param rollingVersion
     * @return int
     * @Description 查询渠道需求提报版本是否存在
     * <AUTHOR>
     * @date 2024年01月11日 16:40
     */
    int queryChannelDemandReportVersionExists(String rollingVersion);

    /**
     * @param id
     * @return String
     * @Description 根据id查询渠道需求提报版本
     * <AUTHOR>
     * @date 2024年01月12日 15:32
     */
    String queryChannelDemandReportVersionById(String id);

    /**
     * @param queryChannelDemandReportListReqVo
     * @return int
     * @Description 查询渠道需求提报数量
     * <AUTHOR>
     * @date 2024年01月29日 18:52
     */
    int queryChannelDemandReportVersionCount(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo);

    /**
     * 查询重复数据
     *
     * @param list
     * @param rollingVersion
     * @param bizDateType
     * @return {@link List< ChannelDemandReportDto>}
     * <AUTHOR>
     * @date 2024-01-30 14:52
     * @description 查询重复数据
     */
    List<ChannelDemandReportDto> queryDuplicate(@Param("items") List<ChannelDemandReportDto> items,
        @Param("rollingVersion") String rollingVersion,
        @Param("bizDateType") String bizDateType,
        @Param("lv3CategoryCodes") String lv3CategoryCodes,
        @Param("skuCodes")String skuCodes,
        @Param("tableSuffix")String tableSuffix);


    /**
     * 查询重复数据,优化的方法
     * @param items
     * @param rollingVersion
     * @param bizDateType
     * @param lv3CategoryCodes
     * @param skuCodes
     * @param tableSuffix
     * @param dynamicsSql
     * @return
     */
    List<Map<String,Object>> queryDuplicateOptimize(
                                            @Param("rollingVersion") String rollingVersion,
                                            @Param("bizDateType") String bizDateType,
                                            @Param("lv3CategoryCodes") String lv3CategoryCodes,
                                            @Param("skuCodes")String skuCodes,
                                            @Param("tableSuffix")String tableSuffix,
                                            @Param("dynamicsSql")String dynamicsSql);

    /**
     * 根据id批量修改需求提报
     *
     * @param channelDemandReportDto 修改数据
     * @param ids                    主键列表
     * @param tableSuffix            分区表
     * @return {@link Boolean}
     * <AUTHOR>
     * @date 2023-11-22 18:24
     * @description 根据id批量修改需求提报
     */
    Boolean batchUpdate(@Param("channelDemandReportDto") ChannelDemandReportDto channelDemandReportDto,
        @Param("ids") Collection<Long> ids,@Param("tableSuffix") String tableSuffix);

    /**
     * 获取一段范围的序列
     *
     * @param size
     * @return {@link Long}
     * <AUTHOR>
     * @date 2024-01-29 16:47
     * @description 获取一段范围的序列
     */
    Long getSeq(@Param("size") Integer size);

    /**
     * 渠道需求提报分区表创建
     * @param partitionList
     */
    void createChannelDemandReportPartitionTable(List<String> partitionList);

    /**
     * 渠道需求提报数据生成
     * @param channelDemandReportTemplateDataDto
     * @return
     */
    int createChannelDemandReportTemplateData(ChannelDemandReportTemplateDataDto channelDemandReportTemplateDataDto) throws Exception;

    /**
     * 查询最大版本
     * @return
     */
    String queryMaxRollingVersion();

}
