package cn.aliyun.ryytn.modules.demand.dataqdao;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.dto.DeliveryOrderDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.DeliveryOrderRateDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandRealityDataVo;

/**
 * @Description 历史订单比例Dao
 * <AUTHOR>
 * @date 2024/1/18 17:56
 */
public interface DataqDeliveryOrderRateDao
{
    /**
     *
     * @Description 根据仓库分组查询历史订单比例
     * @param deliveryOrderRateDto
     * @return List<DeliveryOrderRateDto>
     * <AUTHOR>
     * @date 2024年01月19日 9:25
     */
    List<DeliveryOrderRateDto> queryDeliveryOrderRateGroupByWarehouse(DeliveryOrderRateDto deliveryOrderRateDto);

    /**
     *
     * @Description 根据月份分组查询历史订单数量
     * @param deliveryOrderDto
     * @return List<DeliveryOrderDto>
     * <AUTHOR>
     * @date 2024年03月12日 17:13
     */
    List<DeliveryOrderDto> queryDeliveryOrderGroupByMonth(DeliveryOrderDto deliveryOrderDto);

    /**
     * 查询认养周历史真实数据
     * @param channelDemandRealityDataVo
     * @return
     */
    List<ChannelDemandRealityDataVo> queryHistoryRealityDeliveryOrderByWeek(ChannelDemandRealityDataVo channelDemandRealityDataVo);
}
