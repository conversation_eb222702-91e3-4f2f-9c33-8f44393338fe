package cn.aliyun.ryytn.modules.demand.dataqdao;

import cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityDO;
import cn.aliyun.ryytn.modules.demand.entity.dto.MarketActivityDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.MarketRspVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-08-22 16:00
 * @Description
 */
public interface DataqMarketActivityDao {

    List<MarketRspVo> queryMarketList(MarketActivityDto marketActivityDto);

    MarketActivityDO getById(@Param("id")Long id);

    int updateById(MarketActivityDO marketActivityDo);

    void addMarketActivity(MarketActivityDO activityDO);

}
