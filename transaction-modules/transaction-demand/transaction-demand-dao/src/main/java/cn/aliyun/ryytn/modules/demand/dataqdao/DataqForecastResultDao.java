package cn.aliyun.ryytn.modules.demand.dataqdao;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.vo.ExportChannelForecastResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ExportWarehouseForecastResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseForecastResultRspVo;

/**
 * @Description 阿里dataq预测结果Dao
 * <AUTHOR>
 * @date 2023/12/21 10:42
 */
public interface DataqForecastResultDao
{
    /**
     *
     * @Description 查询渠道预测结果数据动态表头列表
     * @param queryChannelForecastResultReqVo
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    List<String> queryChannelForecastResultHeadList(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo);

    /**
     *
     * @Description 查询渠道预测结果表头下拉列表
     * @param queryChannelForecastResultReqVo
     * @return List<QueryForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<QueryForecastResultRspVo> queryChannelForecastResultHeadSelect(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo);

    /**
     *
     * @Description 查询渠道预测结果分组聚合列表
     * @param queryChannelForecastResultReqVo
     * @return List<QueryForecastResultRspVo>
     * <AUTHOR>
     * @date 2024年03月09日 11:27
     */
    List<QueryForecastResultRspVo> queryChannelForecastResultGroupBy(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo);

    /**
     *
     * @Description 查询渠道预测结果数据唯一键列表，先分页，再分组聚合，提高性能
     * @param queryChannelForecastResultReqVo
     * @return List<QueryForecastResultRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryForecastResultRspVo> queryChannelForecastResultDataKeyList(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo);

    /**
     *
     * @Description 查询渠道预测结果数据列表（动态字段json格式）
     * @param queryChannelForecastResultReqVo
     * @return List<QueryForecastResultRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryForecastResultRspVo> queryChannelForecastResultDataJsonList(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo);

    /**
     *
     * @Description 查询分仓预测结果数据动态表头列表
     * @param queryForecastWarehouseListReqVo
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    List<String> queryWarehouseForecastResultHeadList(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo);

    /**
     *
     * @Description 查询分仓预测结果表头下拉列表
     * @param queryForecastWarehouseListReqVo
     * @return List<QueryWarehouseForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<QueryWarehouseForecastResultRspVo> queryWarehouseForecastResultHeadSelect(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo);

    /**
     *
     * @Description 查询分仓预测结果数据唯一键列表，先分页，再分组聚合，提高性能
     * @param queryForecastWarehouseListReqVo
     * @return List<QueryWarehouseForecastResultRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryWarehouseForecastResultRspVo> queryWarehouseForecastResultDataKeyList(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo);

    /**
     *
     * @Description 查询分仓预测结果数据列表（动态字段json格式）
     * @param queryForecastWarehouseListReqVo
     * @return List<QueryWarehouseForecastResultRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    List<QueryWarehouseForecastResultRspVo> queryWarehouseForecastResultDataJsonList(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo);

    /**
     *
     * @Description 根据仓库分组查询分仓预测结果
     * @param queryForecastWarehouseListReqVo
     * @return List<QueryWarehouseForecastResultRspVo>
     * <AUTHOR>
     * @date 2024年01月16日 16:36
     */
    List<QueryWarehouseForecastResultRspVo> queryWarehouseForecastResultGroupByWarehouse(
        QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo);

    /**
     *
     * @Description 查询偏差比对渠道预测结果列表
     * @param queryChannelForecastResultReqVo
     * @return List<QueryDeviationListRspVo>
     * <AUTHOR>
     * @date 2024年01月26日 14:00
     */
    List<QueryDeviationListRspVo> queryDeviationChannelForecastResultList(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo);

    /**
     *
     * @Description 导出分仓预测结果
     * @param queryForecastWarehouseListReqVo
     * @return List<ExportWarehouseForecastResultRspVo>
     * <AUTHOR>
     * @date 2024年02月29日 11:08
     */
    List<ExportWarehouseForecastResultRspVo> queryExportWarehouseForecastResultList(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo);

    /**
     *
     * @Description 导出渠道预测结果
     * @param queryChannelForecastResultReqVo
     * @return List<ExportChannelForecastResultRspVo>
     * <AUTHOR>
     * @date 2024年04月15日 11:07
     */
    List<ExportChannelForecastResultRspVo> queryExportChannelForecastResultList(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo);
}
