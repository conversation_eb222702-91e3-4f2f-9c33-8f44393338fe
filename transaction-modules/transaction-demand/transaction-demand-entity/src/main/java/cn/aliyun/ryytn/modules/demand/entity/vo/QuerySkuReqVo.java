package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询产品详情请求Vo
 * <AUTHOR>
 * @date 2023/10/26 16:48
 */
@Setter
@Getter
@ToString
@ApiModel("查询产品详情")
public class QuerySkuReqVo implements Serializable
{
    private static final long serialVersionUID = -7379432119871817717L;

    @ApiModelProperty("产品编码  逗号分隔")
    private String skuCodes;

    @ApiModelProperty("产品编码")
    private String skuCode;

    @ApiModelProperty("产品名称")
    private String skuName;

    private String skuKey;
}
