package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 新增需求计划参数
 * <AUTHOR>
 * @date 2023/10/25 10:49
 */
@Setter
@Getter
@ToString
@ApiModel("新增需求计划参数Vo")
public class AddDemandPlanConfigReqVo implements Serializable
{
    private static final long serialVersionUID = -6119632928807676750L;
    @ApiModelProperty("需求计划Code 外键")
    private String demandPlanCode;

    @ApiModelProperty("版本id")
    private String VersionId;

    @ApiModelProperty("版本日期")
    private String versionDate;

    @ApiModelProperty("主体类型:factory:工厂,warehouse:仓库,order:分销渠道，all:汇总")
    private SubjectTypeEnum subjectType;

    @ApiModelProperty("产品小类")
    private String lv3CategoryCode;

    @ApiModelProperty("时间")
    private String bizDateValue;

    @ApiModelProperty("渠道类型")
    private String receiverType;

    @ApiModelProperty("商品")
    private List<DemandPlanConfigSkuVo> planList;

}
