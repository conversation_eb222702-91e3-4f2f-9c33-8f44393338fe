package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import cn.aliyun.ryytn.modules.demand.entity.dto.SaleTargetDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道需求提报列表数据子类，仅重写equals和hashcode，品类+渠道唯一标识
 * <AUTHOR>
 * @date 2023/11/21 14:27
 */
@Setter
@Getter
@ToString
@ApiModel("渠道需求提报列表数据子类，仅重写equals和hashcode，品类+渠道唯一标识")
public class QueryChannelDemandPlanDataSaleTargetVo extends SaleTargetDto
{
    private static final long serialVersionUID = -7601715588340969122L;
    @ApiModelProperty("动态数据映射")
    private Map<String, Double> dataMap = new HashMap<String, Double>();

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        QueryChannelDemandPlanDataSaleTargetVo queryChannelDemandPlanDataSaleTargetVo = (QueryChannelDemandPlanDataSaleTargetVo) o;
        return Objects.equals(this.getLv1CategoryCode(), queryChannelDemandPlanDataSaleTargetVo.getLv1CategoryCode())
            && Objects.equals(this.getLv2CategoryCode(), queryChannelDemandPlanDataSaleTargetVo.getLv2CategoryCode())
            && Objects.equals(this.getLv3CategoryCode(), queryChannelDemandPlanDataSaleTargetVo.getLv3CategoryCode())
            && Objects.equals(this.getLv1ChannelCode(), queryChannelDemandPlanDataSaleTargetVo.getLv1ChannelCode())
            && Objects.equals(this.getLv2ChannelCode(), queryChannelDemandPlanDataSaleTargetVo.getLv2ChannelCode())
            && Objects.equals(this.getLv3ChannelCode(), queryChannelDemandPlanDataSaleTargetVo.getLv3ChannelCode());
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.getLv1CategoryCode() == null) ? 0 : this.getLv1CategoryCode().hashCode());
        result = prime * result + ((this.getLv2CategoryCode() == null) ? 0 : this.getLv2CategoryCode().hashCode());
        result = prime * result + ((this.getLv3CategoryCode() == null) ? 0 : this.getLv3CategoryCode().hashCode());
        result = prime * result + ((this.getLv1ChannelCode() == null) ? 0 : this.getLv1ChannelCode().hashCode());
        result = prime * result + ((this.getLv2ChannelCode() == null) ? 0 : this.getLv2ChannelCode().hashCode());
        result = prime * result + ((this.getLv3ChannelCode() == null) ? 0 : this.getLv3ChannelCode().hashCode());
        return result;
    }
}
