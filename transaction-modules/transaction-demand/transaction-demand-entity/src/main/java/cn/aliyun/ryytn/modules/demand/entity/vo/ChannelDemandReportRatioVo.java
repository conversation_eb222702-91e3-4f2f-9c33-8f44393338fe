package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.util.Map;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求提报比例
 * <AUTHOR>
 * @date 2023/10/26 10:11
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求提报比例")
public class ChannelDemandReportRatioVo extends QueryChannelDemandReportListRspVo
{
    private static final long serialVersionUID = 2651305157446259328L;
    @ApiModelProperty("三级渠道数据")
    private Map<String, Double> lv3ChannelData;

    @ApiModelProperty("总数")
    private Double totalNum;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        ChannelDemandReportRatioVo object = (ChannelDemandReportRatioVo) o;
        return Objects.equals(this.getSkuCode(), object.getSkuCode())
            && Objects.equals(this.getLv1CategoryCode(), object.getLv1CategoryCode())
            && Objects.equals(this.getLv2CategoryCode(), object.getLv2CategoryCode())
            && Objects.equals(this.getLv3CategoryCode(), object.getLv3CategoryCode())
            && Objects.equals(this.getLv1ChannelCode(), object.getLv1ChannelCode())
            && Objects.equals(this.getLv2ChannelCode(), object.getLv2ChannelCode())
            && Objects.equals(this.getBizDateValue(), object.getBizDateValue());
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.getSkuCode() == null) ? 0 : this.getSkuCode().hashCode());
        result = prime * result + ((this.getLv1CategoryCode() == null) ? 0 : this.getLv1CategoryCode().hashCode());
        result = prime * result + ((this.getLv2CategoryCode() == null) ? 0 : this.getLv2CategoryCode().hashCode());
        result = prime * result + ((this.getLv3CategoryCode() == null) ? 0 : this.getLv3CategoryCode().hashCode());
        result = prime * result + ((this.getLv1ChannelCode() == null) ? 0 : this.getLv1ChannelCode().hashCode());
        result = prime * result + ((this.getLv2ChannelCode() == null) ? 0 : this.getLv2ChannelCode().hashCode());
        result = prime * result + ((this.getBizDateValue() == null) ? 0 : this.getBizDateValue().hashCode());
        return result;
    }
}
