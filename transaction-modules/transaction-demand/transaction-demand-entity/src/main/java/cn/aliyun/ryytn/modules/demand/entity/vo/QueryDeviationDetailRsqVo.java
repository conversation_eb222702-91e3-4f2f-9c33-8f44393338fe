package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.excel.annotation.ExcelIgnore;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询偏差比对-详情结果Vo
 * <AUTHOR>
 * @date 2023/11/10 14:17
 */
@Setter
@Getter
@ToString
@ApiModel("查询偏差比对-详情结果Vo")
@AllArgsConstructor
@NoArgsConstructor
public class QueryDeviationDetailRsqVo implements Serializable
{
    private static final long serialVersionUID = -6960121135263482038L;
    @ApiModelProperty("年月")
    private String targetBizDate;
    @ApiModelProperty("预测值")
    private Object orderNum;
    @ApiModelProperty("实际值")
    private Object obserValue;
    @ApiModelProperty("版本")
    private String version;
    /**
     * 动态字段数据映射
     */
    @ApiModelProperty("动态数据映射")
    private Map<String, DeviationDetailDataVo> dataMap = new HashMap<String, DeviationDetailDataVo>();

}
