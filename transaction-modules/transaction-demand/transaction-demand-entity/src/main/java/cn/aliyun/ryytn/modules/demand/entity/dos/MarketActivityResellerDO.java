package cn.aliyun.ryytn.modules.demand.entity.dos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @title MarketActivityResellerDO
 * @description 促销活动关联渠道实体
 * @date 2024/08/26 18:25
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class MarketActivityResellerDO implements Serializable {

    private static final long serialVersionUID = 3938667965913544059L;

    private Long id;
    /**
     * 促销活动唯一Code
     */
    private String actiCode;
    /**
     * 促销活动名称
     */
    private String actiName;
    /**
     * 分销渠道code
     */
    private String resellerCode;
    /**
     * 分销渠道名称
     */
    private String resellerName;
    /**
     * 一级渠道Code
     */
    private String lv1ChannelCode;
    /**
     * 一级渠道名称
     */
    private String lv1ChannelName;
    /**
     * 二级渠道Code
     */
    private String lv2ChannelCode;
    /**
     * 二级渠道名称
     */
    private String lv2ChannelName;
    /**
     * 三级渠道Code
     */
    private String lv3ChannelCode;
    /**
     * 三级渠道名称
     */
    private String lv3ChannelName;
    /**
     * 计划渠道类型
     */
    private String actiResellerObj;
    /**
     * 删除状态
     */
    private Integer deleted;
}
