package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 确认渠道需求计划子计划清单组Vo
 * <AUTHOR>
 * @date 2023/11/17 15:54
 */
@Setter
@Getter
@ToString
@ApiModel("确认渠道需求计划子计划清单组Vo")
public class ConfirmChannelDemandPlanSubPlanGroupVo implements Serializable
{
    private static final long serialVersionUID = 6464776096870197700L;
    @ApiModelProperty("计划主体")
    private SubjectTypeEnum subjectType;

    @ApiModelProperty("需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty("版本编号")
    private String versionId;

    @ApiModelProperty("子计划清单组编号")
    private Integer groupId;
}
