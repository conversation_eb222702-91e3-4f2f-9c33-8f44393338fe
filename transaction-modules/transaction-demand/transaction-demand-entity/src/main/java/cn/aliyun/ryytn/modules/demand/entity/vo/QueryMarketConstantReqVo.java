package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询促销活动常量请求
 * <AUTHOR>
 * @date 2023/10/26 14:53
 */
@Getter
@Setter
@ToString
@ApiModel("查询促销活动常量请求")
public class QueryMarketConstantReqVo implements Serializable
{

    private static final long serialVersionUID = -45641655525680585L;
    @ApiModelProperty("常量类型  ACTI_LEVEL:活动等级，ACTIVITY_STATUS:活动状态，ACTI_AREA:活动区域，ACTI_TYPE:活动类型")
    private String constantType;
    @ApiModelProperty("多个常量类型，以逗号分隔")
    private String constantTypes;
}
