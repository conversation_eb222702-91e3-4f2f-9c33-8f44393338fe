package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivitySkuDetailDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 活动产品保存实体
 * <AUTHOR>
 * @date 2023/10/27 17:22
 */
@Setter
@Getter
@ToString
@ApiModel("活动产品保存实体")
public class MarketProductVo implements Serializable
{
    private static final long serialVersionUID = -95641655527780789L;
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("活动唯一标识")
    private String actiCode;
    @ApiModelProperty("活动名称")
    private String actiName;
    @ApiModelProperty("产品code")
    private String skuCode;
    @ApiModelProperty("产品名称")
    private String skuName;
    @ApiModelProperty("活动数量上限")
    private String actiNumsLimit;
    @ApiModelProperty("计划商品类型,认养项目默认空")
    private String actiSkuObj;
    @ApiModelProperty("计划出货开始时间")
    private String actiSkuDeliveryBeginTime;
    @ApiModelProperty("计划开始时间")
    private String actiSkuBeginTime;
    @ApiModelProperty("计划活动出库量")
    private String actiSkuDeliveryNumsLimit;
    @ApiModelProperty("每周的开始时间")
    private String actiSkuWeekBeginTime;
    @ApiModelProperty("是否删除")
    private Integer deleted;
    @ApiModelProperty("产品明细")
    private List<MarketActivitySkuDetailDO> skuDetailList;


}
