package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询销售目标响应
 * <AUTHOR>
 * @date 2023/10/25 14:57
 */
@Setter
@Getter
@ToString
@ApiModel("查询销售目标响应")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class QuerySaleTargetRspVo implements Serializable
{
    private static final long serialVersionUID = 5901515627618797999L;
    /**
     * 产品编号
     */
    @ApiModelProperty("产品编号")
    private String skuCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String skuName;

    /**
     * 一级渠道编号
     */
    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    /**
     * 一级渠道名称
     */
    @ApiModelProperty("一级渠道名称")
    private String lv1ChannelName;

    /**
     * 二级渠道编号
     */
    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    /**
     * 二级渠道名称
     */
    @ApiModelProperty("二级渠道名称")
    private String lv2ChannelName;

    /**
     * 三级渠道编号
     */
    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    /**
     * 三级渠道名称
     */
    @ApiModelProperty("三级渠道名称")
    private String lv3ChannelName;

    /**
     * 一级品类编号
     */
    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    /**
     * 一级品类名称
     */
    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    /**
     * 二级品类编号
     */
    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    /**
     * 二级品类名称
     */
    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    /**
     * 三级品类编号
     */
    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    /**
     * 三级品类名称
     */
    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("动态字段json字符串，格式不固定，看sql的聚合函数")
    private String data;

    /**
     * 动态字段数据映射
     */
    @ApiModelProperty("动态字段数据映射")
    private Map<String, Double> dataMap = new HashMap<>();

    /**
     * 总数
     */
    @ApiModelProperty("总数")
    private Double total;

    private Double orderNum;
}
