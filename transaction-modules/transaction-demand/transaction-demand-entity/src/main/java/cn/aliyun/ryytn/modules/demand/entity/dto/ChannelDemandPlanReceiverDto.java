package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonInclude;

import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道需求计划TOBC基准
 * <AUTHOR>
 * @date 2024/4/7 16:53
 */
@Setter
@Getter
@ToString
@ApiModel("渠道需求计划TOBC基准")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ChannelDemandPlanReceiverDto implements Serializable
{
    private static final long serialVersionUID = -7647111837411102204L;
    @ApiModelProperty("编号")
    private String id;

    @ApiModelProperty("需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty("版本编号")
    private String versionId;

    @ApiModelProperty("版本日期")
    private String versionDate;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品编号，英文逗号分隔")
    private String skuCodes;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("渠道类型编号，toB/toC")
    private String receiverType;

    @ApiModelProperty("计划数据类型:0/1:日销计划/活动计划")
    private Integer planDataType;

    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("日期")
    private String planDate;

    @ApiModelProperty("数量")
    private Double planValue;

    @ApiModelProperty("比例")
    private Double outboundRate;

    @ApiModelProperty("日期类型")
    private String bizDateType;

    @ApiModelProperty("历史订单比例计算粒度")
    private String dimComb;

    @ApiModelProperty("算法版本")
    private String algoNameAndVersion;

    @ApiModelProperty("算法结果版本")
    private String predictionVersion;

    @ApiModelProperty("动态列数据")
    private Map<String, PlanValue> dataMap;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        ChannelDemandPlanReceiverDto object = (ChannelDemandPlanReceiverDto) o;
        return Objects.equals(this.demandPlanCode, object.demandPlanCode)
            && Objects.equals(this.versionId, object.versionId)
            && Objects.equals(this.skuCode, object.skuCode)
            && Objects.equals(this.lv1CategoryCode, object.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, object.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, object.lv3CategoryCode)
            && Objects.equals(this.receiverType, object.receiverType)
            && Objects.equals(this.planDataType, object.planDataType)
            && Objects.equals(this.warehouseCode, object.warehouseCode)
            && Objects.equals(this.planDate, object.planDate);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((demandPlanCode == null) ? 0 : demandPlanCode.hashCode());
        result = prime * result + ((versionId == null) ? 0 : versionId.hashCode());
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        result = prime * result + ((receiverType == null) ? 0 : receiverType.hashCode());
        result = prime * result + ((planDataType == null) ? 0 : planDataType.hashCode());
        result = prime * result + ((warehouseCode == null) ? 0 : warehouseCode.hashCode());
        result = prime * result + ((planDate == null) ? 0 : planDate.hashCode());
        return result;
    }
}
