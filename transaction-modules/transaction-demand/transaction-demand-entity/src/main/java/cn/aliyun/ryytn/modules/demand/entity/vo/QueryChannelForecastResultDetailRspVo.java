package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道预测结果详情响应Vo
 * <AUTHOR>
 * @date 2023/11/8 14:07
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道预测结果dataq响应Vo")
public class QueryChannelForecastResultDetailRspVo implements Serializable
{
    private static final long serialVersionUID = 5238430715236782851L;
    private String lv1CategoryCode;

    @ApiModelProperty("产品分类")
    private String lv1CategoryName;

    private String lv2CategoryCode;

    @ApiModelProperty("产品大类")
    private String lv2CategoryName;

    private String lv3CategoryCode;

    @ApiModelProperty("产品小类")
    private String lv3CategoryName;

    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道")
    private String lv1ChannelName;

    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道")
    private String lv2ChannelName;

    @ApiModelProperty("预测时间类型")
    private String periodType;

    @ApiModelProperty("数据单位")
    private String dataUnit;

    @ApiModelProperty("数据范围开始时间")
    private String startDate;

    @ApiModelProperty("数据范围结束时间")
    private String endDate;

    @ApiModelProperty("算法版本")
    private String algoVersion;

    @ApiModelProperty("日期")
    private List<String> channelChartTargetBizDateList;

    @ApiModelProperty("实际出库数量")
    private List<String> channelChartObserValueList;

    @ApiModelProperty("历史同期数量")
    private List<String> channelChartOldObserValueList;

    @ApiModelProperty("预测结果数量")
    private List<String> channelChartPredictionResultList;

    @ApiModelProperty("销售目标数量")
    private List<String> channelChartPlanOrderNumList;

    @ApiModelProperty("需求提报数量")
    private List<String> channelChartReportingOrderNumList;

    @ApiModelProperty("图表数据列表")
    private List<ChannelDetailChart> chartList;
}
