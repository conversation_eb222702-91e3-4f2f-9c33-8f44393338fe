package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询偏差比对-详情请求参数
 * <AUTHOR>
 * @date 2023/11/9 15:32
 */
@Setter
@Getter
@ToString
@ApiModel("查询偏差比对-详情请求参数")
public class QueryDeviationDetailReqVo implements Serializable
{
    private static final long serialVersionUID = 3953892304105164108L;
    @ApiModelProperty("产品分类编码")
    private String lv1CategoryCode;
    @ApiModelProperty("产品大类编码")
    private String lv2CategoryCode;
    @ApiModelProperty("产品小类编码")
    private String lv3CategoryCode;
    @ApiModelProperty("一级渠道类型编码")
    private String lv1ChannelCode;
    @ApiModelProperty("二级渠道类型编码")
    private String lv2ChannelCode;
    @ApiModelProperty("时间观测点")
    private Date viewTime;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("分组字段")
    private String groupColumn;
    @ApiModelProperty("用做行转列")
    private String selectColumn;
    @ApiModelProperty("下钻类型（0：销售目标 1：需求预测 2：需求计划）")
    private Integer type;
    @ApiModelProperty("算法版本")
    private String algoNameAndVersion;
    @ApiModelProperty("预测版本")
    private String predictionVersion;
    @ApiModelProperty("需求计划版本Code")
    private String demandPlanCode;

}
