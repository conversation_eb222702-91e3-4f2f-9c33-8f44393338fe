package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 低温需求提报版本
 * @date 2024/2/20 9:48
 */
@Setter
@Getter
@ToString
@ApiModel("低温需求提报版本")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ColdDemandReportVersionDto implements Serializable
{
    private static final long serialVersionUID = -9150348400925400350L;
    @ApiModelProperty("编号")
    private String id;

    @ApiModelProperty("版本号")
    private String rollingVersion;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("修改人")
    private String lastModifier;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("修改时间")
    private Date gmtModify;
}
