package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.excel.ExcelData;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 导入渠道需求提报Vo
 * <AUTHOR>
 * @date 2023/10/18 16:30
 */
@Setter
@Getter
@ToString
public class ImportChannelDemandReportVo extends ExcelData implements Serializable
{
    private static final long serialVersionUID = 8814467262575177382L;
    /**
     * 产品分类
     */
    @ExcelProperty(value = {"产品分类", "产品分类", "产品分类"}, index = 0)
    private String lv1CategoryName;

    /**
     * 产品大类
     */
    @ExcelProperty(value = {"产品大类", "产品大类", "产品大类"}, index = 1)
    private String lv2CategoryName;

    /**
     * 产品小类
     */
    @ExcelProperty(value = {"产品小类", "产品小类", "产品小类"}, index = 2)
    private String lv3CategoryName;

    /**
     * 产品编码
     */
    @ExcelProperty(value = {"产品编码", "产品编码", "产品编码"}, index = 3)
    private String skuCode;

    /**
     * 产品简称
     */
    @ExcelProperty(value = {"产品简称", "产品简称", "产品简称"}, index = 4)
    private String skuName;

    /**
     * 产品状态，阿里产品没有状态字段，默认正常
     */
    @ExcelProperty(value = {"产品状态", "产品状态", "产品状态"}, index = 5)
    public static String status = "正常";

    /**
     * 一级渠道名称
     */
    @ExcelProperty(value = {"一级渠道", "一级渠道", "一级渠道"}, index = 6)
    private String lv1ChannelName;

    /**
     * 二级渠道名称
     */
    @ExcelProperty(value = {"二级渠道", "二级渠道", "二级渠道"}, index = 7)
    private String lv2ChannelName;

    /**
     * 三级渠道名称
     */
    @ExcelProperty(value = {"三级渠道", "三级渠道", "三级渠道"}, index = 8)
    private String lv3ChannelName;

    /**
     * 起始年份
     */
    @ExcelProperty(value = {"起始年份", "起始年份", "起始年份"}, index = 9)
    private String year;

    /**
     * 产品分类编码，隐藏列
     */
    @ExcelProperty(value = {"产品分类编码", "产品分类编码", "产品分类编码"}, index = 10)
    private String lv1CategoryCode;

    /**
     * 产品大类编码，隐藏列
     */
    @ExcelProperty(value = {"产品大类编码", "产品大类编码", "产品大类编码"}, index = 11)
    private String lv2CategoryCode;

    /**
     * 产品小类编码，隐藏列
     */
    @ExcelProperty(value = {"产品小类编码", "产品小类编码", "产品小类编码"}, index = 12)
    private String lv3CategoryCode;

    /**
     * 一级渠道编码，隐藏列
     */
    @ExcelProperty(value = {"一级渠道编码", "一级渠道编码", "一级渠道编码"}, index = 13)
    private String lv1ChannelCode;

    /**
     * 二级渠道编码，隐藏列
     */
    @ExcelProperty(value = {"二级渠道编码", "二级渠道编码", "二级渠道编码"}, index = 14)
    private String lv2ChannelCode;

    /**
     * 三级渠道编码，隐藏列
     */
    @ExcelProperty(value = {"三级渠道编码", "三级渠道编码", "三级渠道编码"}, index = 15)
    private String lv3ChannelCode;

    /**
     * 单位，隐藏列，正产应该阿里从sku表实时获取，阿里接口希望业务这边传入
     */
    @ExcelProperty(value = {"单位", "单位", "单位"}, index = 16)
    private String unit;

    /**
     * 时间粒度类型，渠道需求提报导入固定为周粒度
     */
    @ExcelIgnore
    private final BizDateTypeEnum bizDateType = BizDateTypeEnum.WEEK;

    /**
     * 时间值，每周第一天的日期，格式为yyyyMMdd
     */
    @ExcelIgnore
    private String bizDateValue;

    /**
     * 需求提报版本
     */
    @ExcelIgnore
    private String rollingVersion;

    /**
     * 提报数据
     */
    @ExcelIgnore
    private Double orderNum;

    /**
     * 扩展字段
     */
    @ExcelIgnore
    private String extend;

    /**
     * 动态字段映射关系，Key：周第一天的日期-周最后一天的日期，格式：yyyyMMdd-yyyyMMdd，Value：字段值
     */
    @ExcelIgnore
    @JsonIgnore
    @JSONField(serialize = false)
    private Map<String, Double> weekDateValue = new HashMap<>();

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        ImportChannelDemandReportVo object = (ImportChannelDemandReportVo) o;
        return Objects.equals(this.skuCode, object.skuCode)
            && Objects.equals(this.lv1CategoryCode, object.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, object.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, object.lv3CategoryCode)
            && Objects.equals(this.lv1ChannelCode, object.lv1ChannelCode)
            && Objects.equals(this.lv2ChannelCode, object.lv2ChannelCode)
            && Objects.equals(this.lv3ChannelCode, object.lv3ChannelCode)
            && Objects.equals(this.bizDateType, object.bizDateType)
            && Objects.equals(this.bizDateValue, object.bizDateValue);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        result = prime * result + ((lv1ChannelCode == null) ? 0 : lv1ChannelCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        result = prime * result + ((bizDateType == null) ? 0 : bizDateType.hashCode());
        result = prime * result + ((bizDateValue == null) ? 0 : bizDateValue.hashCode());
        return result;
    }
}
