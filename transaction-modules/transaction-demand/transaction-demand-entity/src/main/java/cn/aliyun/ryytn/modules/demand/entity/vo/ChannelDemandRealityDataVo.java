package cn.aliyun.ryytn.modules.demand.entity.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Objects;

/**
 * @Description 渠道需求实际值VO
 * <AUTHOR>
 * @date 2024/05/23 17:20
 */
@Setter
@Getter
@ToString
public class ChannelDemandRealityDataVo implements Serializable {
    /**
     * 认养周的第一天时间2024-05-01
     */
    private String bizWeekDateStart;


    /**
     * 实际已经发生的数值，按周进行统计
     */
    private Double realityOrderNum;

    /**
     * sku_code
     */
    private String skuCode;

    /**
     * 三级渠道
     */
    private String lv3ChannelCode;


    /**
     * 二级渠道代码
     */
    private String lv2ChannelCode;


    /**
     * 统计维度
     */
    private String dimComb;

    /**
     * 产品编码集合
     */
    private String skuCodes;

    /**
     * 三级渠道集合
     */
    private String lv3ChannelCodes;

    /**
     * 三级渠道集合
     */
    private String lv2ChannelCodes;

    /**
     * 业务数据周期集合
     */
    private String bizDates;



    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        ChannelDemandRealityDataVo channelDemandRealityDataVo = (ChannelDemandRealityDataVo) o;
        return Objects.equals(this.skuCode, channelDemandRealityDataVo.skuCode)
                && Objects.equals(this.lv2ChannelCode, channelDemandRealityDataVo.lv2ChannelCode)
                && Objects.equals(this.lv3ChannelCode, channelDemandRealityDataVo.lv3ChannelCode)
                && Objects.equals(this.bizWeekDateStart, channelDemandRealityDataVo.bizWeekDateStart);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        result = prime * result + ((bizWeekDateStart == null) ? 0 : bizWeekDateStart.hashCode());
        return result;
    }
}
