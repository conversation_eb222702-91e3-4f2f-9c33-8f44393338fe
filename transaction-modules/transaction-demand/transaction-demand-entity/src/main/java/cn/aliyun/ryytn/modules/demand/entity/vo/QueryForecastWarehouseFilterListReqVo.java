package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询分仓预测结果过滤条件请求Vo
 * <AUTHOR>
 * @date 2023/10/24 9:18
 */
@Setter
@Getter
@ToString
@ApiModel("查询分仓预测结果过滤条件请求Vo")
public class QueryForecastWarehouseFilterListReqVo implements Serializable
{
    private static final long serialVersionUID = -14764784785655677L;
    /**
     * 查询所需的字段，多个字段英文逗号分隔
     */
    @ApiModelProperty("查询所需的字段，多个字段英文逗号分隔")
    private String selectColumns;

    /**
     * 算法版本
     */
    @ApiModelProperty("算法版本")
    private String algoNameAndVersion;

    /**
     * 预测版本
     */
    @ApiModelProperty("预测版本")
    private String predictionVersion;

    /**
     * sku产品编号
     */
    @ApiModelProperty("sku产品编号")
    private String skuCodes;

    /**
     * 仓库编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("仓库编号，多个编号英文逗号分隔")
    private String warehouseCodes;

    /**
     * 配送中心：RDC区域配送中心；CDC中央配送中心
     */
    @ApiModelProperty("配送中心：RDC区域配送中心；CDC中央配送中心")
    private String dcTypes;

    /**
     * 一级品类编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("一级品类编号，多个编号英文逗号分隔")
    private String lv1CategoryCodes;

    /**
     * 二级品类编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("二级品类编号，多个编号英文逗号分隔")
    private String lv2CategoryCodes;

    /**
     * 三级品类编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("三级品类编号，多个编号英文逗号分隔")
    private String lv3CategoryCodes;
}
