package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询分仓需求提报仓库比例请求
 * <AUTHOR>
 * @date 2023/11/28 19:45
 */
@Setter
@Getter
@ToString
@ApiModel("查询分仓需求提报仓库比例请求")
public class QueryWarehouseDemandReportRateReqVo implements Serializable
{
    private static final long serialVersionUID = 7404725173520762258L;
    @ApiModelProperty("产品编号，英文逗号分隔")
    private String skuCodes;

    @ApiModelProperty("一级渠道编号，英文逗号分隔")
    private String lv1ChannelCodes;

    @ApiModelProperty("二级渠道编号，英文逗号分隔")
    private String lv2ChannelCodes;

    @ApiModelProperty("三级渠道编号，英文逗号分隔")
    private String lv3ChannelCodes;

    @ApiModelProperty("一级品类编号，英文逗号分隔")
    private String lv1CategoryCodes;

    @ApiModelProperty("二级品类编号，英文逗号分隔")
    private String lv2CategoryCodes;

    @ApiModelProperty("三级品类编号，英文逗号分隔")
    private String lv3CategoryCodes;

    @ApiModelProperty("仓库编号，英文逗号分隔")
    private String warehouseCodes;

    @ApiModelProperty("渠道类型，英文逗号分隔")
    private String channelTypes;

    @ApiModelProperty("日期版本")
    private String dateVersion;
}
