package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道预测结果列表Vo
 * <AUTHOR>
 * @date 2023/11/13 16:28
 */
@Setter
@Getter
@ToString
@ApiModel("渠道预测结果列表Vo")
public class ChannelForecastResultBaseTable<T> implements Serializable
{
    @ApiModelProperty("周期起始时间")
    private String startDate;

    @ApiModelProperty("周期结束时间")
    private String endDate;

    @ApiModelProperty("动态数据表头数组")
    private List<String> headArray;

    @ApiModelProperty("表数据")
    private T list;
}
