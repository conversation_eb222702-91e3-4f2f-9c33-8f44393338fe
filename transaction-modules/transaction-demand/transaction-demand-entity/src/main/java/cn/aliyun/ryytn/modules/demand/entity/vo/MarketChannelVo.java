package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 活动渠道保存参数
 * <AUTHOR>
 * @date 2023/10/27 17:22
 */
@Setter
@Getter
@ToString
@ApiModel("活动渠道保存参数")
public class MarketChannelVo implements Serializable
{
    private static final long serialVersionUID = -95641655525680789L;
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("活动唯一标识")
    private String actiCode;
    @ApiModelProperty("活动名称")
    private String actiName;
    @ApiModelProperty("渠道客户编码")
    private String resellerCode;
    @ApiModelProperty("渠道客户名称")
    private String resellerName;
    @ApiModelProperty("一级渠道类型编码")
    private String lv1ChannelCode;
    @ApiModelProperty("一级渠道类型名称")
    private String lv1ChannelName;
    @ApiModelProperty("二级渠道类型编码")
    private String lv2ChannelCode;
    @ApiModelProperty("二级渠道类型名称")
    private String lv2ChannelName;
    @ApiModelProperty("三级渠道类型编码")
    private String lv3ChannelCode;
    @ApiModelProperty("三级渠道类型名称")
    private String lv3ChannelName;
    @ApiModelProperty("渠道类型，枚举：LV3_CHANNEL三级渠道;LV2_CHANNEL,二级; LV1_CHANNEL:一级渠道;RESELLER_C客户")
    private String actiResellerObj;
}
