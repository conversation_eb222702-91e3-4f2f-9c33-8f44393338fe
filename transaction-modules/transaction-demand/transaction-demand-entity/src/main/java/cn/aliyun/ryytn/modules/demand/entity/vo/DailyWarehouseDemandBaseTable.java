package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日分仓需求结果列表Vo
 * <AUTHOR>
 * @date 2023/12/13 17:14
 */
@Setter
@Getter
@ToString
@ApiModel("日分仓需求列表Vo")
public class DailyWarehouseDemandBaseTable<T> implements Serializable
{
    private static final long serialVersionUID = -5077807035183671813L;

    @ApiModelProperty("动态数据表头数组")
    private List<String> headArray;

    @ApiModelProperty("表数据")
    private T list;
}
