package cn.aliyun.ryytn.modules.demand.entity.dto;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 渠道需求提报版本生成参数
 * @date 2025/2/12 17:48
 */
@Setter
@Getter
@ToString
@ApiModel("渠道需求提报版本生成参数")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelDemandReportTemplateDataDto implements Serializable {
    private static final long serialVersionUID = -752124094512364852L;

    /**
     * 创建人
     */
    private String creator;
    /**
     * 滚动版本号
     */
    private String rollingVersion;

    /**
     * 最后版本号
     */
    private String lastRollingVersion;

    /**
     * 开始时间
     */
    private String beginDate;
    /**
     * 结束时间
     */
    private String endDate;
}
