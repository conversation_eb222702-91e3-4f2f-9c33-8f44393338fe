package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分仓需求计划数据参数
 * <AUTHOR>
 * @date 2023/11/16 15:45
 */
@Setter
@Getter
@ToString
@ApiModel("分仓需求计划数据参数")
public class WarehouseDemandPlanDataParamVo implements Serializable
{
    private static final long serialVersionUID = -4904615383511813230L;
    @ApiModelProperty("需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty("版本编号")
    private String versionId;

    @ApiModelProperty("子计划清单组编号")
    private Long groupId;

    @ApiModelProperty("需求提报滚动版本，查询需求提报数据时传入")
    private String rollingVersion;

    @ApiModelProperty("算法版本，查询需求预测结果时传入")
    private String algoNameAndVersion;

    @ApiModelProperty("预测版本，查询需求预测结果时传入")
    private String predictionVersion;

    @ApiModelProperty("一级品类编号，可选条件")
    private String lv1CategoryCode;

    @ApiModelProperty("二级品类编号，可选条件")
    private String lv2CategoryCode;

    @ApiModelProperty("三级品类编号，可选条件")
    private String lv3CategoryCode;

    @ApiModelProperty("渠道类型")
    private String receiverType;

    @ApiModelProperty("时间日期")
    private String planDate;
}
