package cn.aliyun.ryytn.modules.demand.constant;
/**
 * @Description oms同步文件类型
 * <AUTHOR>
 * @date 2024/7/10 9:27
 */
public enum OmsFileTypeEnum {
    MILK(1,"液奶"),
    MILK_POWDER(2,"奶粉");
    private Integer type;
    private String typeName;
    private OmsFileTypeEnum(Integer type,String typeName){
        this.type = type;
        this.typeName = typeName;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
}
