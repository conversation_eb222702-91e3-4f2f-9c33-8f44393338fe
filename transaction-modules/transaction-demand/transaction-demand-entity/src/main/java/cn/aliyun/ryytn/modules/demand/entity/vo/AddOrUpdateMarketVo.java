package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 新增修改促销活动-活动Vo
 * <AUTHOR>
 * @date 2023/10/27 16:58
 */
@Setter
@Getter
@ToString
@ApiModel("新增修改促销活动-活动vo")
public class AddOrUpdateMarketVo implements Serializable
{
    private static final long serialVersionUID = -5704351795666187210L;
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("活动唯一标识")
    private String actiCode;
    @ApiModelProperty("活动名称")
    private String actiName;
    @ApiModelProperty("活动等级，枚举：S、A、B、C")
    private String actiLevel;
    @ApiModelProperty("活动频率，枚举：IRREGULARS、WEEK、MONTH、YEAR")
    private String eventPeriod;
    @ApiModelProperty("活动起始时间，yyyy-MM-dd hh:mm:ss")
    private String startDate;
    @ApiModelProperty("活动截止时间，yyyy-MM-dd hh:mm:ss")
    private String endDate;
    @ApiModelProperty("活动状态，枚举：READY、RUNNING、END")
    private String activityStatus;
    @ApiModelProperty("活动地区，各省份编码")
    private String actiArea;
    @ApiModelProperty("活动类型，枚举：OFFLINE_PROMOTION、ONLINE_PROMOTION、TALENT")
    private String actiType;
    @ApiModelProperty("每周，月的几号")
    private String actiDeliveryDate;
    @ApiModelProperty("活动出库时间")
    private String eventCycle;
    @ApiModelProperty("用户输入的活动开始和结束日期")
    private String activityCycle;
    @ApiModelProperty("备注")
    private String screenDesc;
    @ApiModelProperty("年份")
    private String year;
    @ApiModelProperty("渠道集合")
    private List<MarketChannelVo> channelList;
    @ApiModelProperty("产品集合")
    private List<MarketProductVo> skuList;
    @ApiModelProperty("计划开始时间")
    private String actiBeginTime;
    @ApiModelProperty("计划结束时间")
    private String actiEndTime;
    @ApiModelProperty("计划出货开始时间")
    private String actiDeliveryBeginTime;
    @ApiModelProperty("计划出货结束时间")
    private String actiDeliveryEndTime;
    @ApiModelProperty("达人组")
    private String expertGroup;
    @ApiModelProperty("达人ID")
    private String expertId;
    @ApiModelProperty("审核状态")
    private int auditStatus;
}
