package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 历史订单
 * <AUTHOR>
 * @date 2024/1/18 17:59
 */
@Setter
@Getter
@ToString
@ApiModel("历史订单")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DeliveryOrderDto implements Serializable
{
    private static final long serialVersionUID = 1607716591252595520L;
    private String dimComb;

    @ApiModelProperty("时间粒度")
    private String bizDateType;

    @ApiModelProperty("时间")
    private String bizDateValue;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道名称")
    private String lv1ChannelName;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道名称")
    private String lv2ChannelName;

    @ApiModelProperty("销售区域编码")
    private String saleRegionCode;

    @ApiModelProperty("销售区域名称")
    private String saleRegionName;

    @ApiModelProperty("订单数量")
    private String deliveryNum;

    @ApiModelProperty("订单数量")
    private Double outboundNum;

    @ApiModelProperty("接收数量")
    private Double receiveNum;

    @ApiModelProperty("损耗数量")
    private Double transLossNum;

    @ApiModelProperty("单位")
    private String numUnit;

    @ApiModelProperty("时间")
    private String ds;

    @ApiModelProperty("开始时间")
    private String beginDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("一级品类编号，英文逗号分隔")
    private String lv1CategoryCodes;

    @ApiModelProperty("二级品类编号，英文逗号分隔")
    private String lv2CategoryCodes;

    @ApiModelProperty("三级品类编号，英文逗号分隔")
    private String lv3CategoryCodes;

    @ApiModelProperty("产品编号，英文逗号分隔")
    private String skuCodes;

    @ApiModelProperty("一级渠道编号，英文逗号分隔")
    private String lv1ChannelCodes;

    @ApiModelProperty("一级渠道编号，英文逗号分隔")
    private String lv2ChannelCodes;

    /**
     *
     * @Description 历史订单比例聚合粒度枚举
     * <AUTHOR>
     * @date 2024/4/7 16:46
     */
    public enum DIM_COMB
    {
        // 产品+渠道+渠道类型+仓库
        WEEK("LV2_CHANNEL_CODE+LV3_CATEGORY_CODE+WEEK"),
        // 产品+渠道类型+仓库
        MONTH("LV2_CHANNEL_CODE+LV3_CATEGORY_CODE+MONTH");

        private final String value;

        DIM_COMB(String value)
        {
            this.value = value;
        }

        public String getValue()
        {
            return this.value;
        }
    }
}
