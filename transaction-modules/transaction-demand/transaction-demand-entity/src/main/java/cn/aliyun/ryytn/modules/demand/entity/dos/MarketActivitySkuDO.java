package cn.aliyun.ryytn.modules.demand.entity.dos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @title MarketActivitySkuDO
 * @description 促销活动关联产品实体
 * @date 2024/08/26 18:25
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketActivitySkuDO implements Serializable {

    private static final long serialVersionUID = 6436768326660539536L;

    private Long id;
    /**
     * 促销活动唯一Code
     */
    private String actiCode;
    /**
     * 促销活动名称
     */
    private String actiName;
    /**
     * 产品Code
     */
    private String skuCode;
    /**
     * 产品名称
     */
    private String skuName;
    /**
     * 活动数量上限
     */
    private String actiNumsLimit;
    /**
     * 计划商品类型
     */
    private String actiSkuObj;
    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 计划开始时间
     */
    private String actiSkuBeginTime;

    /**
     * 计划出货开始时间
     */
    private String actiSkuDeliveryBeginTime;

    /**
     * 计划活动出库量
     */
    private String actiSkuDeliveryNumsLimit;

    /**
     * 每周的开始时间
     */
    private String actiSkuWeekBeginTime;
}
