package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

import cn.aliyun.ryytn.common.utils.string.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 产品锁定期平铺数据
 * <AUTHOR>
 * @date 2023/12/4 19:27
 */
@Setter
@Getter
@ToString
@ApiModel("产品锁定期平铺数据")
public class SkuLockVo implements Serializable
{
    private static final long serialVersionUID = 8664614933837686973L;
    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品编号集合")
    private List<String> skuCodes;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("渠道编号集合")
    private List<String> lv2ChannelCodes;

    @ApiModelProperty("锁定开始日期，yyyyMMdd")
    private String lockStartDate;

    @ApiModelProperty("锁定结束日期，yyyyMMdd")
    private String lockEndDate;

    @ApiModelProperty("渠道编号")
    private String channelCode;

    /**
     *
     * @Description 获取产品编码+渠道编码，英文-分隔
     * @return String
     * <AUTHOR>
     * @date 2024年01月18日 14:26
     */
    @JsonIgnore
    public String getSkuChannelCode()
    {
        return new StringBuilder(this.skuCode).append(StringUtils.DATE_SEPARATOR).append(this.channelCode).toString();
    }
}
