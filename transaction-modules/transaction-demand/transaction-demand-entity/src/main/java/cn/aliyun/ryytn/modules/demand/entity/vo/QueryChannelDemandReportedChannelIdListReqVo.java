package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求提报已提报渠道编号列表
 * <AUTHOR>
 * @date 2023/10/24 9:58
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求提报已提报渠道编号列表")
public class QueryChannelDemandReportedChannelIdListReqVo implements Serializable
{
    private static final long serialVersionUID = 5627497366934782189L;
    /**
     * 提报版本
     */
    @ApiModelProperty("提报版本")
    private String rollingVersion;

    /**
     * 时间类型:DAY,WEEK，MONTH,YEAR,QUARTER
     */
    @ApiModelProperty("时间类型:DAY,WEEK，MONTH,YEAR,QUARTER")
    private BizDateTypeEnum bizDateType;

    @ApiModelProperty("是否修改")
    private Integer isModify;
}
