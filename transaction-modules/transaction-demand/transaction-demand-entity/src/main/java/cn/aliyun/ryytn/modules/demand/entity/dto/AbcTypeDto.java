package cn.aliyun.ryytn.modules.demand.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Objects;

/**
 * @Description abcType分类
 * <AUTHOR>
 * @date 2024/9/14 9:37
 */
@Setter
@Getter
@ToString
@ApiModel("abcType分类")
public class AbcTypeDto implements Serializable {
    private static final long serialVersionUID = 1775432909285725131L;

    @ApiModelProperty("产品编号")
    private String skuCode;
    @ApiModelProperty("skuName")
    private String skuName;
    @ApiModelProperty("渠道类型toB/toC")
    private String sourceOrderType;
    @ApiModelProperty("abc分类")
    private String abcType;
    @ApiModelProperty("产品编号集合")
    private String skuCodes;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        AbcTypeDto object = (AbcTypeDto) o;
        return Objects.equals(this.skuCode, object.skuCode)
                && Objects.equals(this.sourceOrderType, object.sourceOrderType);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((sourceOrderType == null) ? 0 : sourceOrderType.hashCode());
        return result;
    }
}
