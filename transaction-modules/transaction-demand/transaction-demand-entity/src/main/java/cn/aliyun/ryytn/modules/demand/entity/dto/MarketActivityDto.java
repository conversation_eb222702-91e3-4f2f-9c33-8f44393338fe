package cn.aliyun.ryytn.modules.demand.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @title MarketActivityDTO
 * @description 促销活动DTO
 * @date 2023/10/18 18:25
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketActivityDto implements Serializable {

    private static final long serialVersionUID = -756220110142673129L;

    private Long id;
    /**
     * 创建修改人
     */
    private String creator;
    private String lastModifier;
    /**
     * 促销活动唯一Code
     */
    private String actiCode;
    /**
     * 促销活动名称
     */
    private String actiName;
    /**
     * 活动等级
     */
    private String actiLevel;
    /**
     * 活动频率
     */
    private String eventPeriod;
    /**
     * 活动起始时间
     */
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(locale = "zn", pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private LocalDateTime startDate;
//    /**
//     * 活动截止时间
//     */
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(locale = "zn", pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private LocalDateTime endDate;

    private String startDate;
    private String endDate;
    /**
     * 活动状态
     */
    private String activityStatus;
    /**
     * 活动区域
     */
    private String actiArea;
    /**
     * 活动类型
     */
    private String actiType;
    /**
     * 活动出库时间
     */
    private String actiDeliveryDate;
    /**
     * 删除状态
     */
    private Boolean deleted;
    /**
     * 每周，月的第几号
     */
    private String eventCycle;
    /**
     * 用户输入的活动开始和结束日期
     */
    private String activityCycle;
    /**
     * 备注
     */
    private String screenDesc;
    /**
     * 年份
     */
    private String year;

    @ApiModelProperty("计划开始时间")
    private String acti_begin_time;

    @ApiModelProperty("计划结束时间")
    private String acti_end_time;

    @ApiModelProperty("计划出货开始时间")
    private String acti_delivery_begin_time;

    @ApiModelProperty("计划出货结束时间")
    private String acti_delivery_end_time;

    @ApiModelProperty("达人组")
    private String expert_group;

    @ApiModelProperty("达人ID")
    private String expert_id;

    @ApiModelProperty("审核状态 0-待审核 1-已审核")
    private Integer audit_status;

    private List<String> actiLevels;

    private List<String> actiTypes;

    private List<String> actiNames;

    @ApiModelProperty("未知？")
    private String optDate;
    @ApiModelProperty("活动End 传now")
    private String loseDate;
    @ApiModelProperty("活动Ready 传now")
    private String readyDate;
    @ApiModelProperty("排序字段")
    private String sort;
}
