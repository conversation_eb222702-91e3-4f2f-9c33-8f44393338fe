package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道需求预测列表Vo
 * <AUTHOR>
 * @date 2023/10/23 18:21
 */
@Setter
@Getter
@ToString
@ApiModel("渠道需求预测列表Vo")
public class QueryForecastChannelListReqVo implements Serializable
{
    private static final long serialVersionUID = -1169748315366507347L;

    /**
     * 算法版本
     */
    @ApiModelProperty("算法版本")
    private String algoNameAndVersion;

    /**
     * 预测版本
     */
    @ApiModelProperty("预测版本")
    private String predictionVersion;

    /**
     * 预测版本
     */
    @ApiModelProperty("模糊匹配预测版本")
    private String predictionVersionKey;

    @ApiModelProperty("查询字段")
    private String selectColumns;
}
