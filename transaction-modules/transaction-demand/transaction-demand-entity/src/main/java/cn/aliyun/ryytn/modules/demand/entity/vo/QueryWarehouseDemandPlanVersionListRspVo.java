package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.*;

import cn.aliyun.ryytn.common.utils.string.StringUtils;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求计划详情列表响应
 * <AUTHOR>
 * @date 2023/11/8 12:51
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求计划详情列表响应")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class QueryWarehouseDemandPlanVersionListRspVo implements Serializable
{
    private static final long serialVersionUID = 5900302228161292743L;
    @ExcelProperty(value = {"#"}, index = 0)
    private Integer rowId;

    @ApiModelProperty("编号")
    @ExcelIgnore
    private String id;

    @ApiModelProperty("需求计划编号")
    @ExcelIgnore
    private String demandPlanCode;

    @ApiModelProperty("子计划清单组编号")
    @ExcelIgnore
    private Long groupId;

    @ApiModelProperty("产品编号")
    @ExcelProperty(value = {"产品编号"}, index = 7)
    private String skuCode;

    @ApiModelProperty("产品名称")
    @ExcelProperty(value = {"产品名称"}, index = 8)
    private String skuName;

    @ApiModelProperty("货品名称")
    @ExcelProperty(value = {"货品名称"}, index = 9)
    private String wareName;

    @ApiModelProperty("一级品类编号")
    @ExcelIgnore
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    @ExcelProperty(value = "产品分类", index = 4)
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    @ExcelIgnore
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    @ExcelProperty(value = "产品大类", index = 5)
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    @ExcelIgnore
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    @ExcelProperty(value = "产品小类", index = 6)
    private String lv3CategoryName;

    @ApiModelProperty("版本编号")
    @ExcelIgnore
    private String versionId;

    @ApiModelProperty("版本时间")
    @ExcelIgnore
    private String versionDate;

    @ApiModelProperty("客户code")
    @ExcelIgnore
    private String resellerCode;

    @ApiModelProperty("客户名称")
    @ExcelIgnore
    private String resellerName;

    @ApiModelProperty("生命周期编号")
    @ExcelIgnore
    private String lifecycleCode;

    @ApiModelProperty("生命周期名称")
    @ExcelIgnore
    private String lifecycleName;

    @ApiModelProperty("仓库编号")
    @ExcelIgnore
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    @ExcelProperty(value = "仓库", index = 1)
    private String warehouseName;

    @ApiModelProperty("一级仓库类型编码，BDC/CDC/RDC/FDC/eDC")
    @ExcelIgnore
    private String lv1TypeCode;

    @ApiModelProperty("一级仓库名称")
    @ExcelIgnore
    private String lv1TypeName;

    @ApiModelProperty("渠道类型")
    @ExcelProperty(value = "渠道类型", index = 2)
    private String receiverType;

    @ApiModelProperty("类型")
    @ExcelProperty(value = "类型", index = 3)
    private String planDataTypeName;

    @ApiModelProperty("计划数据类型")
    @ExcelIgnore
    private Integer planDataType;
    @ApiModelProperty("算法版本")
    @ExcelIgnore
    private String algoVersion;

    @ApiModelProperty("计划日期")
    @ExcelIgnore
    private String planDate;

    @ApiModelProperty("计划数")
    @ExcelProperty(value = {"数量"}, index = 12)
    private Double planValue;

    @ApiModelProperty("备注")
    @ExcelIgnore
    private String planRemark;

    @ApiModelProperty("参考类型")
    @ExcelIgnore
    private String refType;

    @ApiModelProperty("参考版本")
    @ExcelIgnore
    private String refVersion;

    @ApiModelProperty("标签")
    @ExcelIgnore
    private String label;

    @ApiModelProperty("状态，-1:待试算;未提交:0，已提交:1")
    @ExcelIgnore
    private Integer status;

    @ApiModelProperty("是否调整")
    @ExcelIgnore
    private Boolean isModify;

    @ApiModelProperty("是否删除")
    @ExcelIgnore
    private Boolean deleted;

    @ApiModelProperty("创建时间")
    @ExcelIgnore
    private String gmtCreate;

    @ApiModelProperty("修改时间")
    @ExcelIgnore
    private String gmtModify;

    @ExcelProperty(value = {"出货周"}, index = 10)
    private String week;

    @ExcelProperty(value = {"出货月"}, index = 11)
    private String month;

    @ApiModelProperty("动态数据json字符串，格式不确定，看sql聚合函数")
    @ExcelIgnore
    private String data;

    @ApiModelProperty("动态数据映射")
    @ExcelIgnore
    private Map<String, PlanValue> dataMap = new HashMap<String, PlanValue>();

    @ApiModelProperty("动态数据比例映射")
    @ExcelIgnore
    private Map<String, Double> rateMap = new HashMap<String, Double>();

    @ApiModelProperty("日实发数据量")
    @ExcelIgnore
    List<DayAcutalDeliveryVo>  listActualDeliveryDays = new ArrayList<>();

    @ApiModelProperty("abc分类")
    @ExcelProperty(value = "abc分类", index = 13)
    private String abcType;

    /**
     *
     */
    public  List<DayAcutalDeliveryVo>  parseData(){
        if(!StringUtils.isEmpty(this.data)){
            listActualDeliveryDays = (List<DayAcutalDeliveryVo>)JSONArray.parseArray(this.data,DayAcutalDeliveryVo.class);
        }
        return listActualDeliveryDays;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        QueryWarehouseDemandPlanVersionListRspVo object = (QueryWarehouseDemandPlanVersionListRspVo) o;
        return Objects.equals(this.skuCode, object.skuCode)
            && Objects.equals(this.lv1CategoryCode, object.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, object.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, object.lv3CategoryCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        return result;
    }
}
