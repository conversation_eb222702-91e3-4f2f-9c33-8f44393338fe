package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道预测结果详情请求Vo
 * <AUTHOR>
 * @date 2023/11/8 10:02
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道预测结果详情请求Vo")
public class QueryChannelForecastResultDetailReqVo implements Serializable
{
    private static final long serialVersionUID = 6546416400401582472L;

    private String selectColumn;

    @ApiModelProperty("聚合字段")
    private String groupColumn;

    @ApiModelProperty("算法版本")
    private String algoVersion;

    @ApiModelProperty("预测版本")
    private String predictionVersion;

    @ApiModelProperty("一级渠道")
    private String lv1ChannelCode;

    @ApiModelProperty("二级渠道")
    private String lv2ChannelCode;

    @ApiModelProperty("一级分类")
    private String lv1CategoryCode;

    @ApiModelProperty("二级分类")
    private String lv2CategoryCode;

    @ApiModelProperty("三级分类")
    private String lv3CategoryCode;

    @ApiModelProperty("一级渠道")
    private String lv1ChannelName;

    @ApiModelProperty("二级渠道")
    private String lv2ChannelName;

    @ApiModelProperty("一级分类")
    private String lv1CategoryName;

    @ApiModelProperty("二级分类")
    private String lv2CategoryName;

    @ApiModelProperty("三级分类")
    private String lv3CategoryName;

    @ApiModelProperty("跨度起始时间，yyyy-MM-dd")
    private String startDate;

    @ApiModelProperty("跨度截止时间，yyyy-MM-dd")
    private String endDate;
}
