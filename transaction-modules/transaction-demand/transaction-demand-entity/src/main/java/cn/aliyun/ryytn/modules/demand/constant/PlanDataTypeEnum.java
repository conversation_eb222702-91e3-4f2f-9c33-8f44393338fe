package cn.aliyun.ryytn.modules.demand.constant;
/**
 * @Description 计划数据类型
 * <AUTHOR>
 * @date 2024/8/19 18:17
 */
public enum PlanDataTypeEnum {
    DAY_SALE_NUM(0,"日销数据计划"),
    MARKET_ACTIVITY(1,"促销活动计划");

    private Integer planDataType;

    private String planDataTypeName;

    PlanDataTypeEnum(Integer planDataType,String planDataTypeName){
        this.planDataType = planDataType;
        this.planDataTypeName = planDataTypeName;
    }
    public Integer getPlanDataType() {
        return planDataType;
    }

    public void setPlanDataType(Integer planDataType) {
        this.planDataType = planDataType;
    }

    public String getPlanDataTypeName() {
        return planDataTypeName;
    }

    public void setPlanDataTypeName(String planDataTypeName) {
        this.planDataTypeName = planDataTypeName;
    }
}
