package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求提报列表响应
 * <AUTHOR>
 * @date 2023/10/26 10:11
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求提报列表响应")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class QueryChannelDemandReportGroupListRspVo implements Serializable
{
    private static final long serialVersionUID = 8465395636488681595L;
    /**
     * 产品编号
     */
    @ApiModelProperty("产品编号")
    private String skuCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String skuName;

    /**
     * 一级渠道编号
     */
    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    /**
     * 一级渠道名称
     */
    @ApiModelProperty("一级渠道名称")
    private String lv1ChannelName;

    /**
     * 二级渠道编号
     */
    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    /**
     * 二级渠道名称
     */
    @ApiModelProperty("二级渠道名称")
    private String lv2ChannelName;

    /**
     * 三级渠道编号
     */
    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    /**
     * 三级渠道名称
     */
    @ApiModelProperty("三级渠道名称")
    private String lv3ChannelName;

    /**
     * 一级品类编号
     */
    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    /**
     * 一级品类名称
     */
    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    /**
     * 二级品类编号
     */
    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    /**
     * 二级品类名称
     */
    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    /**
     * 三级品类编号
     */
    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    /**
     * 三级品类名称
     */
    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    /**
     * 时间粒度值
     */
    @ApiModelProperty("时间粒度值")
    private String bizDateValue;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Double orderNum;

    @ApiModelProperty("动态数据列表")
    private String data;

    @ApiModelProperty("明细数量")
    private Long total;

    /**
     * 动态字段数据映射，KEY：响应结构外部headArray的元素，VALUE：动态数据对象
     */
    @ApiModelProperty("动态数据映射")
    private Map<String, ChannelDemandReportDataVo> dataMap = new HashMap<String, ChannelDemandReportDataVo>();

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        QueryChannelDemandReportGroupListRspVo queryChannelDemandReportListRspVo = (QueryChannelDemandReportGroupListRspVo) o;
        return Objects.equals(this.skuCode, queryChannelDemandReportListRspVo.skuCode)
            && Objects.equals(this.lv1CategoryCode, queryChannelDemandReportListRspVo.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, queryChannelDemandReportListRspVo.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, queryChannelDemandReportListRspVo.lv3CategoryCode)
            && Objects.equals(this.lv1ChannelCode, queryChannelDemandReportListRspVo.lv1ChannelCode)
            && Objects.equals(this.lv2ChannelCode, queryChannelDemandReportListRspVo.lv2ChannelCode)
            && Objects.equals(this.lv3ChannelCode, queryChannelDemandReportListRspVo.lv3ChannelCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        result = prime * result + ((lv1ChannelCode == null) ? 0 : lv1ChannelCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        return result;
    }
}
