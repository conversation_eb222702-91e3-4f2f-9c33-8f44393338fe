package cn.aliyun.ryytn.modules.demand.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description oms文件表记录
 * <AUTHOR>
 * @date 2024/7/9 16:21
 */
@Setter
@Getter
@ToString
@ApiModel("oms文件表记录")
public class OmsFileRecordDto  implements Serializable{

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("文件类型")
    private Integer fileType;
    @ApiModelProperty("文件记录数")
    private Integer fileCount;
    @ApiModelProperty("文件地址url")
    private String  fileUrl;
    @ApiModelProperty("记录时间")
    private Date recordTime;
    @ApiModelProperty("是否成功:0:未同步;1:成功;-1:失败")
    private Integer successFlag;
    @ApiModelProperty("描述")
    private String  description;
    @ApiModelProperty("计划代码")
    private String demandPlanCode;
    @ApiModelProperty("版本")
    private String versionId;
    @ApiModelProperty("过期时间")
    private Date expireDate;
}
