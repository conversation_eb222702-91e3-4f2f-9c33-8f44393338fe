package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 产品VO
 * <AUTHOR>
 * @date 2023/10/23 16:08
 */
@Setter
@Getter
@ToString
@ApiModel("查询产品列表响应参数")
public class QueryProductListRspVo implements Serializable
{
    private static final long serialVersionUID = 95641655525680581L;

    @JsonProperty("sku_code")
    @ApiModelProperty("商品编码，10码")
    private String skuCode;

    @JsonProperty("69_code")
    @ApiModelProperty("商品69码")
    private String code_69;
    @JsonProperty("sku_name")
    @ApiModelProperty("商品名称")
    private String skuName;
    @JsonProperty("status_id")
    @ApiModelProperty("商品状态，是否冻结")
    private Integer statusId;

    @JsonProperty("lifecycle")
    @ApiModelProperty("生命周期，新品/常规品/...")
    private String lifeCycle;
    @JsonProperty("category_code")
    @ApiModelProperty("品类编码，计划分类")
    private String categoryCode;
    @JsonProperty("category_name")
    @ApiModelProperty("品类编码，计划分类")
    private String categoryName;
    @JsonProperty("shelf_life")
    @ApiModelProperty("保质期天数")
    private Integer shelfLife;
    @JsonProperty("rought_weight")
    @ApiModelProperty("毛重")
    private Double roughtWeight;
    @JsonProperty("net_weight")
    @ApiModelProperty("净重")
    private Double netWeight;
    @JsonProperty("weight_unit")
    @ApiModelProperty("重量单位")
    private String weightUnit;
    @JsonProperty("volume")
    @ApiModelProperty("体积")
    private Double volume;
    @JsonProperty("volume_unit")
    @ApiModelProperty("体积单位")
    private String volumeUnit;
    @ApiModelProperty("体积单位")
    private Double length;

    @ApiModelProperty("宽")
    private Double width;

    @ApiModelProperty("高")
    private Double height;
    @ApiModelProperty("单位")
    private String unit;
    @JsonProperty("plan_unit_cnt")
    @ApiModelProperty("提数/罐数/瓶数的转换系数")
    private Integer planUnitCnt;
    @JsonProperty("lv1_category_code")
    @ApiModelProperty("产品分类编码")
    private String lv1CategoryCode;
    @JsonProperty("lv1_category_name")
    @ApiModelProperty("产品分类名称")
    private String lv1CategoryName;
    @JsonProperty("lv2_category_code")
    @ApiModelProperty("产品大类编码")
    private String lv2CategoryCode;
    @JsonProperty("lv2_category_name")
    @ApiModelProperty("产品大类名称")
    private String lv2CategoryName;
    @JsonProperty("lv3_category_code")
    @ApiModelProperty("产品小类编码")
    private String lv3CategoryCode;
    @JsonProperty("lv3_category_name")
    @ApiModelProperty("产品小类名称")
    private String lv3CategoryName;
    @JsonProperty("fin_category_code")
    @ApiModelProperty("财务分类编码")
    private String fin_category_code;
    @JsonProperty("fin_category_name")
    @ApiModelProperty("财务分类名称")
    private String finCategoryName;
    @JsonProperty("atomic_unit_cnt")
    @ApiModelProperty("入数，常温奶专用")
    private String atomicUnitCnt;
    @ApiModelProperty("最新单价，成本价")
    private Double price;

    @JsonProperty("price_unit")
    @ApiModelProperty("入数，常温奶专用")
    private String price_unit;
    @ApiModelProperty("是否赠品")
    private Integer gift;

    @JsonProperty("brand_code")
    @ApiModelProperty("品牌编码")
    private String brandCode;

    @JsonProperty("brand_name")
    @ApiModelProperty("品牌名称")
    private String brandName;
    @JsonProperty("brand_group")
    @ApiModelProperty("所属集团")
    private String brandGroup;

    @JsonProperty("brand_org")
    @ApiModelProperty("所属组织")
    private String brandOrg;

    @JsonProperty("gmt_create")
    @ApiModelProperty("创建时间")
    private String gmtCreate;
    @JsonProperty("gmt_modified")
    @ApiModelProperty("修改时间")
    private String gmtModified;
    @ApiModelProperty("日期分区yyyymmdd")
    private String ds;

}
