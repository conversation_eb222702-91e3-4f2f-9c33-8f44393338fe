package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求预测结果列表请求
 * <AUTHOR>
 * @date 2023/11/21 18:25
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求预测结果列表请求")
public class QueryChannelDemandForecastResultListReqVo implements Serializable
{
    private static final long serialVersionUID = 5331586170091689915L;
    @ApiModelProperty("算法版本")
    private String algoNameAndVersion;

    @ApiModelProperty("预测版本")
    private String predictionVersion;

    @ApiModelProperty("sku产品编号")
    private String skuCodes;

    @ApiModelProperty("一级渠道编号，多个编号英文逗号分隔")
    private String lv1ChannelCodes;

    @ApiModelProperty("二级渠道编号，多个编号英文逗号分隔")
    private String lv2ChannelCodes;

    @ApiModelProperty("三级渠道编号，多个编号英文逗号分隔")
    private String lv3ChannelCodes;

    @ApiModelProperty("一级品类编号，多个编号英文逗号分隔")
    private String lv1CategoryCodes;

    @ApiModelProperty("二级品类编号，多个编号英文逗号分隔")
    private String lv2CategoryCodes;

    @ApiModelProperty("三级品类编号，多个编号英文逗号分隔")
    private String lv3CategoryCodes;

    @ApiModelProperty("时间类型")
    private String periodType;
}
