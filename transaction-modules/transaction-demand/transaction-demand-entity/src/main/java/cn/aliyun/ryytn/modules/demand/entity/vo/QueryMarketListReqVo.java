package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 促销活动列表Vo
 * <AUTHOR>
 * @date 2023/10/24 9:49
 */
@Setter
@Getter
@ToString
@ApiModel("促销活动列表Vo")
public class QueryMarketListReqVo implements Serializable
{
    private static final long serialVersionUID = 45641655525680581L;
    @ApiModelProperty("活动编码")
    private String actiCode;
    @ApiModelProperty("未知？")
    private String optDate;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("是否已删除，默认0，0:未删除；1:已删除")
    private Integer deleted;
    @ApiModelProperty("活动状态，枚举：READY、RUNNING、END")
    private String activityStatus;
    @ApiModelProperty("等级,多选的话 以逗号分隔")
    private String actiLevels;
    @ApiModelProperty("类型,多选的话 以逗号分隔")
    private String actiTypes;
    @ApiModelProperty("名称,多选的话 以逗号分隔")
    private String actiNames;
    @ApiModelProperty("排序字段,多个的话 以逗号分隔")
    private String sort;
    @ApiModelProperty("活动End 传now")
    private String loseDate;
    @ApiModelProperty("活动Ready 传now")
    private String readyDate;

}
