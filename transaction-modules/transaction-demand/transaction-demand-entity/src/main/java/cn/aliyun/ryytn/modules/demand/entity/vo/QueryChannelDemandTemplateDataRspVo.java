package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelProperty;

import cn.aliyun.ryytn.common.excel.HideProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 导出渠道需求提报模板Vo
 * <AUTHOR>
 * @date 2023/10/18 16:30
 */
@Setter
@Getter
@ToString
public class QueryChannelDemandTemplateDataRspVo implements Serializable
{
    private static final long serialVersionUID = 4384386069261898580L;
    /**
     * 产品分类
     */
    @ExcelProperty(value = {"产品分类", "产品分类", "产品分类"}, index = 0)
    private String lv1CategoryName;

    /**
     * 产品大类
     */
    @ExcelProperty(value = {"产品大类", "产品大类", "产品大类"}, index = 1)
    private String lv2CategoryName;

    /**
     * 产品小类
     */
    @ExcelProperty(value = {"产品小类", "产品小类", "产品小类"}, index = 2)
    private String lv3CategoryName;

    /**
     * 产品编码
     */
    @ExcelProperty(value = {"产品编码", "产品编码", "产品编码"}, index = 3)
    private String skuCode;

    /**
     * 产品简称
     */
    @ExcelProperty(value = {"产品简称", "产品简称", "产品简称"}, index = 4)
    private String skuName;

    /**
     * 产品状态，阿里产品没有状态字段，默认正常
     */
    @ExcelProperty(value = {"产品状态", "产品状态", "产品状态"}, index = 5)
    public static String status = "正常";

    /**
     * 一级渠道名称
     */
    @ExcelProperty(value = {"一级渠道", "一级渠道", "一级渠道"}, index = 6)
    private String lv1ChannelName;

    /**
     * 二级渠道名称
     */
    @ExcelProperty(value = {"二级渠道", "二级渠道", "二级渠道"}, index = 7)
    private String lv2ChannelName;

    /**
     * 三级渠道名称
     */
    @ExcelProperty(value = {"三级渠道", "三级渠道", "三级渠道"}, index = 8)
    private String lv3ChannelName;

    /**
     * 起始年份
     */
    @ExcelProperty(value = {"起始年份", "起始年份", "起始年份"}, index = 9)
    private String year;

    /**
     * 产品分类编码，隐藏列
     */
    @HideProperty
    @ExcelProperty(value = {"产品分类编码", "产品分类编码", "产品分类编码"}, index = 10)
    private String lv1CategoryCode;

    /**
     * 产品大类编码，隐藏列
     */
    @HideProperty
    @ExcelProperty(value = {"产品大类编码", "产品大类编码", "产品大类编码"}, index = 11)
    private String lv2CategoryCode;

    /**
     * 产品小类编码，隐藏列
     */
    @HideProperty
    @ExcelProperty(value = {"产品小类编码", "产品小类编码", "产品小类编码"}, index = 12)
    private String lv3CategoryCode;

    /**
     * 一级渠道编码，隐藏列
     */
    @HideProperty
    @ExcelProperty(value = {"一级渠道编码", "一级渠道编码", "一级渠道编码"}, index = 13)
    private String lv1ChannelCode;

    /**
     * 二级渠道编码，隐藏列
     */
    @HideProperty
    @ExcelProperty(value = {"二级渠道编码", "二级渠道编码", "二级渠道编码"}, index = 14)
    private String lv2ChannelCode;

    /**
     * 三级渠道编码，隐藏列
     */
    @HideProperty
    @ExcelProperty(value = {"三级渠道编码", "三级渠道编码", "三级渠道编码"}, index = 15)
    private String lv3ChannelCode;

    /**
     * 单位，隐藏列，正产应该阿里从sku表实时获取，阿里接口希望业务这边传入
     */
    @HideProperty
    @ExcelProperty(value = {"单位", "单位", "单位"}, index = 16)
    private String unit;
}
