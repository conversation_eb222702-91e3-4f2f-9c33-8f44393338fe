package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分页查询渠道预测结果Vo
 * <AUTHOR>
 * @date 2023/11/6 16:14
 */
@Setter
@Getter
@ToString
@ApiModel("分页查询渠道预测结果Vo")
public class QueryChannelForecastResultRspVo implements Serializable
{
    private static final long serialVersionUID = 5963737111572119962L;

    @ApiModelProperty("预测结果版本")
    private String predictionVersion;

    @ApiModelProperty("预测周期起始日期原始数据")
    private String originalMaxTargetBizDate;

    @ApiModelProperty("预测周期终止日期原始数据")
    private String originalMinTargetBizDate;

    @ApiModelProperty("预测周期起始日期")
    private String maxTargetBizDate;

    @ApiModelProperty("预测周期终止日期")
    private String minTargetBizDate;
}
