package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 导出月预测结果响应Vo
 * <AUTHOR>
 * @date 2023/11/24 14:59
 */
@Setter
@Getter
@ToString
@ApiModel("导出月预测结果响应Vo")
public class ExportChannelForecastMonthResultRspVo implements Serializable
{
    private static final long serialVersionUID = 3900867791752952324L;
    @ExcelProperty(value = {"#"}, index = 0)
    private Integer rowId;

    @ColumnWidth(20)
    @ExcelProperty(value = {"一级渠道"}, index = 1)
    private String lv1ChannelName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"二级渠道"}, index = 2)
    private String lv2ChannelName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"产品分类"}, index = 3)
    private String lv1CategoryName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"产品大类"}, index = 4)
    private String lv2CategoryName;

    @ColumnWidth(25)
    @ExcelProperty(value = {"产品小类"}, index = 5)
    private String lv3CategoryName;

    @ExcelIgnore
    private String targetBizDate;

    @ExcelProperty(value = {"渠道预测年"}, index = 6)
    private String year;

    @ExcelProperty(value = {"渠道预测月"}, index = 7)
    private String month;

    @ColumnWidth(10)
    @ExcelProperty(value = {"预测结果数量"}, index = 8)
    private String predictionResult;

    @ExcelIgnore
    private String dataUnit;
}
