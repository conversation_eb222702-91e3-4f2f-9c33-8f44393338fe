package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 计划值Vo
 * <AUTHOR>
 * @date 2023/10/25 14:37
 */
@Setter
@Getter
@ToString
@ApiModel("计划值")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PlanValue implements Serializable
{
    private static final long serialVersionUID = -4927519617711180240L;
    @ApiModelProperty("数据编号")
    private String id;

    @ApiModelProperty("计划日期")
    private String planDate;

    @ApiModelProperty("计划值备注")
    private String planRemark;

    @ApiModelProperty("计划值")
    private Double planValue;
    @ApiModelProperty("计划值,原始值,这个值不做修改,只冗余")
    private Double planValueOriginal;
    @ApiModelProperty("周日期")
    private String planWeek;

    @ApiModelProperty("周原始值")
    private Double weekRawValue;

    @ApiModelProperty("周实际值")
    private Double weekActualValue;

    @ApiModelProperty("是否锁定期")
    private Boolean lockFlag;

    @ApiModelProperty("计划值")
    private Double srcPlanValue;

    @ApiModelProperty("比例")
    private Double rate;

    @ApiModelProperty("历史16Week比例")
    private Double outboundRate16Week;
    @ApiModelProperty("toC的促销活动量")
    private PlanValue toCMarketActivityNum;
}
