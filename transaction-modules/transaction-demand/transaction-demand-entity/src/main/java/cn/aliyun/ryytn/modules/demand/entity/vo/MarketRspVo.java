package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 促销活动Vo
 * <AUTHOR>
 * @date 2023/10/31 14:32
 */
@Setter
@Getter
@ToString
@ApiModel("促销活动Vo")
public class MarketRspVo implements Serializable
{
    private static final long serialVersionUID = -8746169059055585689L;
    @ApiModelProperty("id")
    private Long id;
    @JSONField(defaultValue = "acti_code")
    @ApiModelProperty("营销活动编码")
    private String actiCode;
    @JSONField(defaultValue = "acti_name")
    @ApiModelProperty("营销活动名称")
    private String actiName;
    @JSONField(defaultValue = "acti_level")
    @ApiModelProperty("活动等级，枚举：S、A、B、C")
    private String actiLevel;
    @JSONField(defaultValue = "event_period")
    @ApiModelProperty("活动频率：irregulars不定期、week每周（1~7）、month每月（1~31）、year每年（日期）")
    private String eventPeriod;
    @ApiModelProperty("活动状态，枚举：READY、RUNNING、END")
    private String activityStatus;
    @JSONField(defaultValue = "acti_area")
    @ApiModelProperty("活动区域-省份")
    private String actiArea;
    @JSONField(defaultValue = "acti_type")
    @ApiModelProperty("活动类型：线上促销、线下促销、达人活动")
    private String actiType;
    @JSONField(defaultValue = "screen_desc")
    @ApiModelProperty("备注")
    private String screenDesc;
    @JSONField(defaultValue = "event_cycle")
    @ApiModelProperty("每周，月的几号")
    private String eventCycle;
    @JSONField(defaultValue = "activity_cycle")
    @ApiModelProperty("用户输入的活动开始和结束日期")
    private String activityCycle;
    @JSONField(defaultValue = "acti_delivery_date")
    @ApiModelProperty("活动出库时间")
    private String actiDeliveryDate;
    @JSONField(defaultValue = "start_date")
    @ApiModelProperty("合约生效日期")
    private String startDate;
    @JSONField(defaultValue = "end_date")
    @ApiModelProperty("合约截止日期")
    private String endDate;
    @JSONField(defaultValue = "gmt_create")
    @ApiModelProperty("创建时间")
    private String gmtCreate;
    @JSONField(defaultValue = "gmt_modified")
    @ApiModelProperty("修改时间")
    private String gmtModified;
    @ApiModelProperty("是否已删除，默认0，0:未删除；1:已删除")
    private Integer deleted;
    @ApiModelProperty("创建人")
    private String creator;
    @JSONField(defaultValue = "last_modifier")
    @ApiModelProperty("最后修改人")
    private String lastModifier;
    @ApiModelProperty("扩展字段")
    private String extend;
    @ApiModelProperty("年份")
    private String year;

    private String actiBeginTime;

    private String actiEndTime;

    private String actiDeliveryBeginTime;

    private String actiDeliveryEndTime;

    private String expertGroup;

    private String expertId;

    private String auditStatus;


}
