package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分仓需求提报
 * <AUTHOR>
 * @date 2023/11/27 14:56
 */
@Setter
@Getter
@ToString
@ApiModel("分仓需求提报")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class WarehouseDemandReportDto implements Serializable, Cloneable
{
    private static final long serialVersionUID = 5190707545505093774L;
    @ExcelProperty(value = {"#"}, index = 0)
    private Integer rowId;

    @ApiModelProperty("编号")
    @ExcelIgnore
    private String id;

    @ApiModelProperty("需求计划名称")
    @ExcelIgnore
    @JSONField(name = "demandPlanName")
    private String name;

    @ApiModelProperty("需求计划编号")
    @ExcelIgnore
    private String demandPlanCode;

    @ApiModelProperty("需求计划版本")
    @ExcelIgnore
    private String rollingVersion;

    @ApiModelProperty("产品编号")
    @ExcelProperty(value = {"产品编码"}, index = 9)
    private String skuCode;

    @ApiModelProperty("产品名称")
    @ExcelProperty(value = {"产品简称"}, index = 10)
    private String skuName;

    @ApiModelProperty("一级品类编号")
    @ExcelIgnore
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    @ExcelProperty(value = {"产品分类"}, index = 6)
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    @ExcelIgnore
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    @ExcelProperty(value = {"产品大类"}, index = 7)
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    @ExcelIgnore
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    @ExcelProperty(value = {"产品小类"}, index = 8)
    private String lv3CategoryName;

    @ApiModelProperty("一级渠道编号")
    @ExcelIgnore
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道名称")
    @ExcelProperty(value = {"一级渠道"}, index = 1)
    private String lv1ChannelName;

    @ApiModelProperty("二级渠道编号")
    @ExcelIgnore
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道名称")
    @ExcelProperty(value = {"二级渠道"}, index = 2)
    private String lv2ChannelName;

    @ApiModelProperty("三级渠道编号")
    @ExcelIgnore
    private String lv3ChannelCode;

    @ApiModelProperty("三级渠道名称")
    @ExcelProperty(value = {"三级渠道"}, index = 3)
    private String lv3ChannelName;

    @ApiModelProperty("三级渠道类型")
    @ExcelProperty(value = {"渠道类型"}, index = 5)
    private String receiverType;

    @ApiModelProperty("仓库编号")
    @ExcelIgnore
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    @ExcelProperty(value = {"仓库"}, index = 4)
    private String warehouseName;

    @ApiModelProperty("时间类型:DAY,WEEK，MONTH,YEAR")
    @ExcelIgnore
    private BizDateTypeEnum bizDateType;

    @ApiModelProperty("时间类型值,日：20230101;周:0230103;月:202301")
    @ExcelIgnore
    private String bizDateValue;

    @ApiModelProperty("订单数量/订单金额")
    @ExcelProperty(value = {"数量"}, index = 14)
    private Double orderNum;

    @ApiModelProperty("旧订单数量/旧订单金额")
    @ExcelIgnore
    private Double oldOrderNum;

    @ApiModelProperty("计量单位:件/瓶/吨ml/元")
    @ExcelIgnore
    private String unit;

    @ApiModelProperty("渠道需求提报二级渠道数据偏差率")
    @ExcelIgnore
    private Double deviationRadio;

    @ApiModelProperty("备注")
    @ExcelIgnore
    private String remark;

    @ApiModelProperty("扩展字段")
    @ExcelIgnore
    private String extend;

    @ApiModelProperty("是否调整：0为否;1为是，默认0")
    @ExcelIgnore
    private Integer isModify;

    @ApiModelProperty("创建人")
    @ExcelIgnore
    private String creator;

    @ApiModelProperty("最后修改人")
    @ExcelIgnore
    private String lastModifier;

    @ApiModelProperty("创建时间")
    @ExcelIgnore
    private Date gmtCreate;

    @ApiModelProperty("修改时间")
    @ExcelIgnore
    private Date gmtModify;

    @ApiModelProperty("是否删除：0为否;1为是，默认0")
    @ExcelIgnore
    private Integer isDelete;

    @ApiModelProperty("动态字段值")
    @ExcelIgnore
    private Map<String, ChannelDemandReportDataVo> dataMap = new HashMap<>();

    @ApiModelProperty("货品名称")
    @ExcelProperty(value = {"货品名称"}, index = 11)
    private String waresName;

    @ApiModelProperty("出货周")
    @ExcelProperty(value = {"出货周"}, index = 12)
    private String week;

    @ApiModelProperty("出货月")
    @ExcelProperty(value = {"出货月"}, index = 13)
    private String month;

    @ApiModelProperty("分组列枚举列表")
    @ExcelIgnore
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("分组列")
    @ExcelIgnore
    private String groupColumn;

    @ApiModelProperty("排序列")
    @ExcelIgnore
    private String sortColumn;

    @ApiModelProperty("动态字段json，格式不固定，看具体sql聚合函数")
    @ExcelIgnore
    private String data;

    @ApiModelProperty("业务标识集合")
    @ExcelIgnore
    private List<WarehouseDemandReportDto> keyList;

    @ApiModelProperty("产品编号，英文逗号分隔")
    @ExcelIgnore
    private String skuCodes;

    @ApiModelProperty("一级品类编号，英文逗号分隔")
    @ExcelIgnore
    private String lv1CategoryCodes;

    @ApiModelProperty("二级品类编号，英文逗号分隔")
    @ExcelIgnore
    private String lv2CategoryCodes;

    @ApiModelProperty("三级品类编号，英文逗号分隔")
    @ExcelIgnore
    private String lv3CategoryCodes;

    @ApiModelProperty("一级渠道编号，英文逗号分隔")
    @ExcelIgnore
    private String lv1ChannelCodes;

    @ApiModelProperty("二级渠道编号，英文逗号分隔")
    @ExcelIgnore
    private String lv2ChannelCodes;

    @ApiModelProperty("三级渠道编号，英文逗号分隔")
    @ExcelIgnore
    private String lv3ChannelCodes;

    @ApiModelProperty("三级渠道类型，英文逗号分隔")
    @ExcelIgnore
    private String receiverTypes;

    @ApiModelProperty("仓库编号，英文逗号分隔")
    @ExcelIgnore
    private String warehouseCodes;

    @ApiModelProperty("分表后缀")
    @ExcelIgnore
    private String tableSuffix;

    @ApiModelProperty("开始日期")
    @ExcelIgnore
    private String planDateStart;

    @ApiModelProperty("结束日期")
    @ExcelIgnore
    private String planDateEnd;

    @ApiModelProperty("权限控制字段，有权限的渠道编号，英文逗号分隔")
    @ExcelIgnore
    private String channelCodes;

    @ApiModelProperty("权限控制字段，有权限的品类编号，英文逗号分隔")
    @ExcelIgnore
    private String categoryCodes;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        WarehouseDemandReportDto object = (WarehouseDemandReportDto) o;
        return Objects.equals(this.skuCode, object.skuCode)
            && Objects.equals(this.lv1CategoryCode, object.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, object.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, object.lv3CategoryCode)
            && Objects.equals(this.lv1ChannelCode, object.lv1ChannelCode)
            && Objects.equals(this.lv2ChannelCode, object.lv2ChannelCode)
            && Objects.equals(this.lv3ChannelCode, object.lv3ChannelCode)
            && Objects.equals(this.warehouseCode, object.warehouseCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        result = prime * result + ((lv1ChannelCode == null) ? 0 : lv1ChannelCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        result = prime * result + ((warehouseCode == null) ? 0 : warehouseCode.hashCode());
        return result;
    }

    @Override
    public WarehouseDemandReportDto clone() throws CloneNotSupportedException
    {
        WarehouseDemandReportDto warehouseDemandReportDto = (WarehouseDemandReportDto) super.clone();
        return warehouseDemandReportDto;
    }
}
