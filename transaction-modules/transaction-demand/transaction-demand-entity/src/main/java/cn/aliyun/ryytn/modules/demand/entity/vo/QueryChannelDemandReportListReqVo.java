package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询需求提报列表请求Vo
 * <AUTHOR>
 * @date 2023/10/24 9:58
 */
@Setter
@Getter
@ToString
@ApiModel("查询需求提报列表请求Vo")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class QueryChannelDemandReportListReqVo implements Serializable
{
    private static final long serialVersionUID = -3015467233094729090L;
    /**
     * 上一版本号，如果为空，则认为没有上一版本号
     */
    @ApiModelProperty("上一版本号，如果为空，则认为没有上一版本号")
    private String lastRollingVersion;

    /**
     * 提报版本
     */
    @ApiModelProperty("提报版本")
    private String rollingVersion;

    /**
     * 时间类型:DAY,WEEK，MONTH,YEAR,QUARTER
     */
    @ApiModelProperty("时间类型:DAY,WEEK，MONTH,YEAR,QUARTER")
    private BizDateTypeEnum bizDateType;

    /**
     * 是否修改
     */
    @ApiModelProperty("是否修改")
    private Integer isModify;

    /**
     * sku产品编号
     */
    @ApiModelProperty("sku产品编号，英文逗号分隔")
    private String skuCodes;

    @ApiModelProperty("sku产品编号")
    private String skuCode;

    /**
     * 一级渠道编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("一级渠道编号，多个编号英文逗号分隔")
    private String lv1ChannelCodes;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    /**
     * 二级渠道编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("二级渠道编号，多个编号英文逗号分隔")
    private String lv2ChannelCodes;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    /**
     * 三级渠道编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("三级渠道编号，多个编号英文逗号分隔")
    private String lv3ChannelCodes;

    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    /**
     * 一级品类编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("一级品类编号，多个编号英文逗号分隔")
    private String lv1CategoryCodes;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    /**
     * 二级品类编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("二级品类编号，多个编号英文逗号分隔")
    private String lv2CategoryCodes;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    /**
     * 三级品类编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("三级品类编号，多个编号英文逗号分隔")
    private String lv3CategoryCodes;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    /**
     * 字段信息，通过dataq接口动态获取，前端不需要传
     */
    @ApiModelProperty("字段信息，通过dataq接口动态获取，前端不需要传")
    private String selectColumn;

    @ApiModelProperty("bizDateValue开始时间")
    private String beginDate;

    @ApiModelProperty("bizDateValue结束时间")
    private String endDate;

    @ApiModelProperty("分组列枚举列表")
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("分组列")
    private String groupColumn;

    @ApiModelProperty("排序列")
    private String sortColumn;

    @ApiModelProperty("业务数据标识列表")
    private List<QueryChannelDemandReportListRspVo> keyList;

    @ApiModelProperty("是否做数据权限过滤")
    private Boolean dataScope;

    @ApiModelProperty("偏差率，如果不需要过滤，则不传或者传null")
    private Double deviationScore;

    @ApiModelProperty("权限控制字段，有权限的渠道编号，英文逗号分隔")
    private String channelCodes;

    @ApiModelProperty("权限控制字段，有权限的品类编号，英文逗号分隔")
    private String categoryCodes;
    /**
     *
     * 表分区
     */
    private String tableSuffix;
}
