package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 渠道需求提报版本
 * @date 2024/2/20 9:48
 */
@Setter
@Getter
@ToString
@ApiModel("渠道需求提报版本")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelDemandReportVersionDto implements Serializable
{
    private static final long serialVersionUID = -752124094588364852L;
    @ApiModelProperty("编号")
    private String id;

    @ApiModelProperty("版本号")
    private String rollingVersion;

    @ApiModelProperty("是否锁定")
    private Integer isLocked;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("修改人")
    private String lastModifier;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("修改时间")
    private Date gmtModify;

    @ApiModelProperty("三级渠道编号，英文逗号分隔")
    private String lv3ChannelCodes;
}
