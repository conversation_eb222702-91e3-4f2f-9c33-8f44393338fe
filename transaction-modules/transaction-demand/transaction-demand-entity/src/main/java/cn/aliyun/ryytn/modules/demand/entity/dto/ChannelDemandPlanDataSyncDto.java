package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;
import java.util.Objects;

import com.alibaba.excel.annotation.ExcelProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道需求计划数据同步对象
 * <AUTHOR>
 * @date 2023/11/29 17:35
 */
@Setter
@Getter
@ToString
@ApiModel("渠道需求计划数据同步对象")
public class ChannelDemandPlanDataSyncDto implements Serializable
{
    private static final long serialVersionUID = 1517693745932646302L;
    @ApiModelProperty("编号")
    @ExcelProperty(value = {"编号"}, index = 0)
    private String id;

    @ApiModelProperty("计划编号")
    @ExcelProperty(value = {"计划编号"}, index = 1)
    private String demandPlanCode;

    @ApiModelProperty("滚动版本号")
    @ExcelProperty(value = {"滚动版本号"}, index = 2)
    private String versionId;

    @ApiModelProperty("版本名称，格式：计划编号-滚动版本号")
    @ExcelProperty(value = {"计划编号-滚动版本号"}, index = 3)
    private String versionName;

    @ApiModelProperty("二级渠道编号")
    @ExcelProperty(value = {"二级渠道编号"}, index = 4)
    private String lv2ChannelCode;

    @ApiModelProperty("SKU产品编号")
    @ExcelProperty(value = {"产品编号"}, index = 5)
    private String skuCode;

    @ApiModelProperty("时间，格式yyyyMMdd，二级渠道为生产计划部时放周的第一天，其他二级渠道放周的最后一天")
    @ExcelProperty(value = {"时间"}, index = 6)
    private String planDate;

    @ApiModelProperty("需求数据")
    @ExcelProperty(value = {"需求数量"}, index = 7)
    private Double planValue;

    @ApiModelProperty("月份周数，格式MMWn")
    @ExcelProperty(value = {"月份周数"}, index = 8)
    private String monthWeek;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        ChannelDemandPlanDataSyncDto object = (ChannelDemandPlanDataSyncDto) o;
        return Objects.equals(this.demandPlanCode, object.demandPlanCode)
            && Objects.equals(this.versionId, object.versionId)
            && Objects.equals(this.skuCode, object.skuCode)
            && Objects.equals(this.lv2ChannelCode, object.lv2ChannelCode)
            && Objects.equals(this.planDate, object.planDate);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((demandPlanCode == null) ? 0 : demandPlanCode.hashCode());
        result = prime * result + ((versionId == null) ? 0 : versionId.hashCode());
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((planDate == null) ? 0 : planDate.hashCode());
        return result;
    }
}
