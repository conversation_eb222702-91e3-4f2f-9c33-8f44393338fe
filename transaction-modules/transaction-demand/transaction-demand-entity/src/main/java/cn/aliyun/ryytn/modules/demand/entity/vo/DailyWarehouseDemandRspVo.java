package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日分仓需求响应数据Vo
 * <AUTHOR>
 * @date 2023/12/13 17:16
 */
@Setter
@Getter
@ToString
@ApiModel("日分仓需求列表Vo")
public class DailyWarehouseDemandRspVo implements Serializable
{

    private static final long serialVersionUID = 3964354701231842590L;
    @ApiModelProperty("需求计划编码")
    private String demandPlanCode;

    @ApiModelProperty("需求计划名称")
    private String demandPlanName;

    @ApiModelProperty("需求计划版本")
    private String demandPlanVersion;

    @ApiModelProperty("产品编码")
    private String skuCode;

    @ApiModelProperty("产品简称")
    private String skuName;

    @ApiModelProperty("0:TOB业务，1:TOC业务")
    private Integer distributeType;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("产品分类编码")
    private String lv1CategoryCode;

    @ApiModelProperty("产品分类名称")
    private String lv1CategoryName;

    @ApiModelProperty("产品大类编码")
    private String lv2CategoryCode;

    @ApiModelProperty("产品大类名称")
    private String lv2CategoryName;

    @ApiModelProperty("产品小类编码")
    private String lv3CategoryCode;

    @ApiModelProperty("产品小类名称")
    private String lv3CategoryName;

    @ApiModelProperty("动态数据json串，格式不固定，看sql聚合函数")
    private String data;

    @ApiModelProperty("动态字段数据映射")
    private Map<String, PlanValue> dataMap = new HashMap<>();

    @ApiModelProperty("ABC分类")
    private String abcType;

    @ApiModelProperty("数据类型:0:日销;1:活动")
    private Integer planDataType;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DailyWarehouseDemandRspVo that = (DailyWarehouseDemandRspVo) o;
        return Objects.equals(demandPlanCode, that.demandPlanCode) && Objects.equals(demandPlanName, that.demandPlanName) && Objects.equals(demandPlanVersion, that.demandPlanVersion) && Objects.equals(skuCode, that.skuCode) && Objects.equals(skuName, that.skuName) && Objects.equals(distributeType, that.distributeType) && Objects.equals(warehouseCode, that.warehouseCode) && Objects.equals(warehouseName, that.warehouseName) && Objects.equals(lv1CategoryCode, that.lv1CategoryCode) && Objects.equals(lv1CategoryName, that.lv1CategoryName) && Objects.equals(lv2CategoryCode, that.lv2CategoryCode) && Objects.equals(lv2CategoryName, that.lv2CategoryName) && Objects.equals(lv3CategoryCode, that.lv3CategoryCode) && Objects.equals(lv3CategoryName, that.lv3CategoryName) && Objects.equals(data, that.data) && Objects.equals(dataMap, that.dataMap) && Objects.equals(abcType, that.abcType) && Objects.equals(planDataType, that.planDataType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(demandPlanCode, demandPlanName, demandPlanVersion, skuCode, skuName, distributeType, warehouseCode, warehouseName, lv1CategoryCode, lv1CategoryName, lv2CategoryCode, lv2CategoryName, lv3CategoryCode, lv3CategoryName, data, dataMap, abcType, planDataType);
    }
}
