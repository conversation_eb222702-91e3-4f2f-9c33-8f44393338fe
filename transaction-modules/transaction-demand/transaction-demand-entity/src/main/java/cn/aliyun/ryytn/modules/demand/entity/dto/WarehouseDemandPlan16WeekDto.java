package cn.aliyun.ryytn.modules.demand.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Description 分仓需求计划历史16周数据
 * <AUTHOR>
 * @date 2024/7/4 9:37
 */
@Setter
@Getter
@ToString
@ApiModel("分仓需求计划历史16周数据")
public class WarehouseDemandPlan16WeekDto  implements Serializable{
    private static final long serialVersionUID = 1775432909285765181L;
    @ApiModelProperty("产品编号")
    private String skuCode;
    @ApiModelProperty("渠道类型toB/toC")
    private String receiverType;
    @ApiModelProperty("仓库编码")
    private String warehouseCode;
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @ApiModelProperty("计算出来的16week数量")
    private Double outboundRate;
    @ApiModelProperty("类型")
    private Integer planDataType;

    /**
     * 原始的值
     */
    private Double planValueOriginal;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        WarehouseDemandPlan16WeekDto object = (WarehouseDemandPlan16WeekDto) o;
        return Objects.equals(this.skuCode, object.skuCode)
                && Objects.equals(this.receiverType, object.receiverType)
                && Objects.equals(this.warehouseCode, object.warehouseCode)
                &&Objects.equals(this.planDataType, object.planDataType);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((receiverType == null) ? 0 : receiverType.hashCode());
        result = prime * result + ((warehouseCode == null) ? 0 : warehouseCode.hashCode());
        result = prime * result + ((planDataType == null) ? 0 : planDataType.hashCode());
        return result;
    }

}
