package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.aliyun.ryytn.common.excel.HideProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分仓需求提报导入模板
 * <AUTHOR>
 * @date 2023/11/27 14:56
 */
@Setter
@Getter
@ToString
@ApiModel("分仓需求提报导入模板")
public class WarehouseDemandReportTemplateDto implements Serializable
{
    private static final long serialVersionUID = -3636562617548763477L;
    @ApiModelProperty("产品编号")
    @ExcelProperty(value = {"产品编码", "产品编码", "产品编码"}, index = 3)
    private String skuCode;

    @ApiModelProperty("产品名称")
    @ExcelProperty(value = {"产品简称", "产品简称", "产品简称"}, index = 4)
    private String skuName;

    @HideProperty
    @ApiModelProperty("一级品类编号")
    @ExcelProperty(value = {"产品分类编码", "产品分类编码", "产品分类编码"}, index = 10)
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    @ExcelProperty(value = {"产品分类", "产品分类", "产品分类"}, index = 0)
    private String lv1CategoryName;

    @HideProperty
    @ApiModelProperty("二级品类编号")
    @ExcelProperty(value = {"产品大类编码", "产品大类编码", "产品大类编码"}, index = 11)
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    @ExcelProperty(value = {"产品大类", "产品大类", "产品大类"}, index = 1)
    private String lv2CategoryName;

    @HideProperty
    @ApiModelProperty("三级品类编号")
    @ExcelProperty(value = {"产品小类编码", "产品小类编码", "产品小类编码"}, index = 12)
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    @ExcelProperty(value = {"产品小类", "产品小类", "产品小类"}, index = 2)
    private String lv3CategoryName;

    @HideProperty
    @ApiModelProperty("一级渠道编号")
    @ExcelProperty(value = {"一级渠道编码", "一级渠道编码", "一级渠道编码"}, index = 13)
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道")
    @ExcelProperty(value = {"一级渠道", "一级渠道", "一级渠道"}, index = 5)
    private String lv1ChannelName;

    @HideProperty
    @ApiModelProperty("二级渠道编号")
    @ExcelProperty(value = {"二级渠道编码", "二级渠道编码", "二级渠道编码"}, index = 14)
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道")
    @ExcelProperty(value = {"二级渠道", "二级渠道", "二级渠道"}, index = 6)
    private String lv2ChannelName;

    @HideProperty
    @ApiModelProperty("三级渠道编号")
    @ExcelProperty(value = {"三级渠道编码", "三级渠道编码", "三级渠道编码"}, index = 15)
    private String lv3ChannelCode;

    @ApiModelProperty("三级渠道")
    @ExcelProperty(value = {"三级渠道", "三级渠道", "三级渠道"}, index = 7)
    private String lv3ChannelName;

    @HideProperty
    @ApiModelProperty("仓库编码")
    @ExcelProperty(value = {"仓库编码", "仓库编码", "仓库编码"}, index = 16)
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    @ExcelProperty(value = {"仓库", "仓库", "仓库"}, index = 8)
    private String warehouseName;

    @ApiModelProperty("起始年份")
    @ExcelProperty(value = {"起始年份", "起始年份", "起始年份"}, index = 9)
    private String year;

    @ApiModelProperty("滚动版本号")
    @ExcelIgnore
    private String rollingVersion;

    @ApiModelProperty("日期值映射")
    @ExcelIgnore
    private String data;
}
