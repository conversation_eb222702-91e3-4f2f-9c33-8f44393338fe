package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 产品锁定期dto
 * <AUTHOR>
 * @date 2023/10/26 14:25
 */
@Setter
@Getter
@ToString
@ApiModel("产品锁定期dto")
public class SkuLockDto implements Serializable
{
    private static final long serialVersionUID = -3160808125589910498L;
    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("商品编号")
    private String skuCode;

    @ApiModelProperty("商品简称")
    private String SkuName;

    @ApiModelProperty("产品分类一级编码")
    private String lv1CategoryCode;

    @ApiModelProperty("产品分类一级名称")
    private String lv1CategoryName;

    @ApiModelProperty("产品分类二级编码")
    private String lv2CategoryCode;

    @ApiModelProperty("产品分类一级名称")
    private String lv2CategoryName;

    @ApiModelProperty("产品分类三级编码")
    private String lv3CategoryCode;

    @ApiModelProperty("产品分类一级名称")
    private String lv3CategoryName;

    @ApiModelProperty("锁定日期开始时间 yyyyMMdd")
    private String lockStartDate;

    @ApiModelProperty("锁定日期结束时间 yyyyMMdd")
    private String lockEndDate;

    @ApiModelProperty("锁定日期开始时间 yyyy/MM/W1")
    private String lockStartWeek;

    @ApiModelProperty("锁定日期结束时间 yyyy/MM/W1")
    private String lockEndWeek;

    @ApiModelProperty("锁定渠道（二级）")
    private List<LockChannelDto> channelList;

    @ApiModelProperty("创建人")
    private String CreatedBy;
}
