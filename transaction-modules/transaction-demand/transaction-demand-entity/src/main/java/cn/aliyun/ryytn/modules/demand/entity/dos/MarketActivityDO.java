package cn.aliyun.ryytn.modules.demand.entity.dos;

import cn.aliyun.ryytn.modules.demand.entity.dos.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version V1.0
 * @title MarketActivityDO
 * @description 促销活动实体
 * @date 2024/08/26 18:25
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class MarketActivityDO extends BaseDO {
    private static final long serialVersionUID = -4382653355926690295L;
    /**
     * 促销活动唯一Code
     */
    private String actiCode;
    /**
     * 促销活动名称
     */
    private String actiName;
    /**
     * 活动等级
     */
    private String actiLevel;
    /**
     * 活动频率
     */
    private String eventPeriod;
    /**
     * 活动起始时间
     */
    private String startDate;
    /**
     * 活动截止时间
     */
    private String endDate;
    /**
     * 活动状态
     */
    private String activityStatus;
    /**
     * 活动区域
     */
    private String actiArea;
    /**
     * 活动类型
     */
    private String actiType;
    /**
     * 活动出库时间
     */
    private String actiDeliveryDate;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 每周，月的第几号
     */
    private String eventCycle;
    /**
     * 用户输入的活动开始和结束日期
     */
    private String activityCycle;
    /**
     * 备注
     */
    private String screenDesc;
    /**
     * 年份
     */
    private String year;
    private String actiBeginTime;
    private String actiEndTime;
    private String actiDeliveryBeginTime;
    private String actiDeliveryEndTime;
    private String expertGroup;
    private String expertId;
    private Integer auditStatus;
}
