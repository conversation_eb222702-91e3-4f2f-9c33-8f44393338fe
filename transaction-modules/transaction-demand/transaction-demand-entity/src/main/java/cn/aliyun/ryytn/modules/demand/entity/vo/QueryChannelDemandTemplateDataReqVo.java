package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求提报模板请求
 * <AUTHOR>
 * @date 2023/10/27 11:14
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求提报模板请求")
public class QueryChannelDemandTemplateDataReqVo implements Serializable
{
    private static final long serialVersionUID = -3010128482914160824L;
    /**
     * 提报版本
     */
    @ApiModelProperty("提报版本")
    private String rollingVersion;

    /**
     * 时间粒度，导入模板固定WEEK
     */
    @ApiModelProperty("时间粒度，导入模板固定WEEK")
    private BizDateTypeEnum bizDateType;

    /**
     * 财年，
     */
    @ApiModelProperty("财年")
    private String fsclYear;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private String endDate;

    /**
     * 字段信息，通过dataq接口动态获取，前端不需要传
     */
    @ApiModelProperty("字段信息，通过dataq接口动态获取，前端不需要传")
    private String selectColumn;

    @ApiModelProperty("产品编号，英文逗号分隔")
    private String skuCodes;

    @ApiModelProperty("一级渠道编号，英文逗号分隔")
    private String lv1ChannelCodes;

    @ApiModelProperty("二级渠道编号，英文逗号分隔")
    private String lv2ChannelCodes;

    @ApiModelProperty("三级渠道编号，英文逗号分隔")
    private String lv3ChannelCodes;

    @ApiModelProperty("一级品类编号，英文逗号分隔")
    private String lv1CategoryCodes;

    @ApiModelProperty("二级品类编号，英文逗号分隔")
    private String lv2CategoryCodes;

    @ApiModelProperty("三级品类编号，英文逗号分隔")
    private String lv3CategoryCodes;
}
