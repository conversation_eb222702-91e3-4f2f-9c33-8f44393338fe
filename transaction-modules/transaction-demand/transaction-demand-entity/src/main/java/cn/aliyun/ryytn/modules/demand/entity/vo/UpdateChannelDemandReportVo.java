package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 修改渠道需求提报Vo
 * <AUTHOR>
 * @date 2024/4/1 15:22
 */
@Setter
@Getter
@ToString
@ApiModel("修改渠道需求提报Vo")
public class UpdateChannelDemandReportVo implements Serializable
{
    private static final long serialVersionUID = 8279343976389394228L;
    @ApiModelProperty("提报版本")
    private String rollingVersion;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    @ApiModelProperty("修改数据集合")
    private List<ChannelDemandReportDataVo> channelDemandReportDataVoList;
}
