package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * @Description 导出分仓预测结果Vo
 * <AUTHOR>
 * @date 2024/2/29 10:03
 */
@Setter
@Getter
@ToString
@ApiModel("导出分仓预测结果Vo")
public class ExportWarehouseForecastResultRspVo implements Serializable
{
    private static final long serialVersionUID = 698526085063611220L;
    @ExcelProperty(value = {"#"}, index = 0)
    private Integer rowId;

    @ColumnWidth(15)
    @ExcelProperty(value = {"产品分类"}, index = 1)
    private String lv1CategoryName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"产品大类"}, index = 2)
    private String lv2CategoryName;

    @ColumnWidth(25)
    @ExcelProperty(value = {"产品小类"}, index = 3)
    private String lv3CategoryName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"产品编码"}, index = 4)
    private String skuCode;

    @ColumnWidth(25)
    @ExcelProperty(value = {"产品简称"}, index = 5)
    private String skuName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"仓库"}, index = 6)
    private String warehouseName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"渠道类型"}, index = 7)
    private String receiverType;

    @ExcelIgnore
    private String targetBizDate;

    @ColumnWidth(10)
    @ExcelProperty(value = {"出货周"}, index = 8)
    private String week;

    @ColumnWidth(10)
    @ExcelProperty(value = {"出货月"}, index = 9)
    private String month;

    @ColumnWidth(10)
    @ExcelProperty(value = {"分仓比例"}, index = 10)
    private Double predictionResult;
}
