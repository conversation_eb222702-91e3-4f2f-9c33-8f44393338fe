package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * @Description 需求计划商品Vo
 * <AUTHOR>
 * @date 2023/10/25 14:29
 */
@Setter
@Getter
@ToString
@ApiModel("需求计划商品Vo")
public class DemandPlanConfigSkuVo implements Serializable
{
    private static final long serialVersionUID = -8950792274674677589L;
    @ApiModelProperty("子计划分组编号")
    private Long groupId;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道编号")
    private Set<String> lv1ChannelCodes;

    @ApiModelProperty("一级渠道名称")
    private String lv1ChannelName;

    @ApiModelProperty("二级品类编号")
    private String lv2ChannelCode;

    @ApiModelProperty("二级品类编号")
    private Set<String> lv2ChannelCodes;

    @ApiModelProperty("二级品类名称")
    private String lv2ChannelName;

    @ApiModelProperty("三级品类编号")
    private String lv3ChannelCode;

    @ApiModelProperty("三级品类名称")
    private String lv3ChannelName;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类编号")
    private Set<String> lv1CategoryCodes;

    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类编号")
    private Set<String> lv2CategoryCodes;

    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类编号")
    private Set<String> lv3CategoryCodes;

    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    @JSONField(serialize = false)
    @ApiModelProperty("数据日期，仅用于业务逻辑判断，不作为参数发给阿里")
    private String planDate;

    @JSONField(serialize = false)
    @ApiModelProperty("数据值，仅用于业务逻辑判断，不作为参数发给阿里")
    private Double planValue;

    @ApiModelProperty("数据类型")
    private Integer planDataType;
    @JSONField(serialzeFeatures = {SerializerFeature.DisableCircularReferenceDetect})
    @ApiModelProperty("值")
    private List<PlanValue> planValues;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        DemandPlanConfigSkuVo demandPlanConfigSkuVo = (DemandPlanConfigSkuVo) o;
        return Objects.equals(this.skuCode, demandPlanConfigSkuVo.skuCode)
            && Objects.equals(this.lv1ChannelCode, demandPlanConfigSkuVo.lv1ChannelCode)
            && Objects.equals(this.lv2ChannelCode, demandPlanConfigSkuVo.lv2ChannelCode)
            && Objects.equals(this.lv3ChannelCode, demandPlanConfigSkuVo.lv3ChannelCode)
            && Objects.equals(this.lv1CategoryCode, demandPlanConfigSkuVo.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, demandPlanConfigSkuVo.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, demandPlanConfigSkuVo.lv3CategoryCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1ChannelCode == null) ? 0 : lv1ChannelCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        return result;
    }
}
