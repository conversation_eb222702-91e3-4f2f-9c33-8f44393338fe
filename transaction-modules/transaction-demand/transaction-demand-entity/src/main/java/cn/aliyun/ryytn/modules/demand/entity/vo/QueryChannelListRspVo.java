package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道VO
 * <AUTHOR>
 * @date 2023/10/23 15:24
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道列表响应参数")
public class QueryChannelListRspVo implements Serializable
{
    private static final long serialVersionUID = 95641655525680789L;

    @JsonProperty("reseller_code")
    @ApiModelProperty("渠道客户编码")
    private String resellerCode;

    @JsonProperty("reseller_name")
    @ApiModelProperty("渠道客户名称")
    private String resellerName;
    @ApiModelProperty("渠道客户状态，1/0，是否有效")
    private Integer status;
    @JsonProperty("is_own_reseller")
    @ApiModelProperty("是否自有销售渠道，1/0")
    private String isOwnReseller;
    @JsonProperty("sale_region_code")
    @ApiModelProperty("销售区域编码")
    private String saleRegionCode;
    @JsonProperty("sale_region_name")
    @ApiModelProperty("销售区域名称")
    private String saleRegionName;
    @JsonProperty("lv1_channel_code")
    @ApiModelProperty("一级渠道类型编码")
    private String lv1ChannelCode;
    @JsonProperty("lv1_channel_name")
    @ApiModelProperty("一级渠道类型名称")
    private String lv1ChannelName;
    @JsonProperty("lv2_channel_code")
    @ApiModelProperty("二级渠道类型编码")
    private String lv2ChannelCode;
    @JsonProperty("lv2_channel_name")
    @ApiModelProperty("二级渠道类型名称")
    private String lv2ChannelName;

    @JsonProperty("lv3_channel_code")
    @ApiModelProperty("三级渠道类型编码")
    private String lv3ChannelCode;
    @JsonProperty("lv2_channel_name")
    @ApiModelProperty("三级渠道类型名称")
    private String lv3ChannelName;
    @JsonProperty("default_warehouse_code")
    @ApiModelProperty("默认发货仓库编码")
    private String defaultWarehouseCode;
    @JsonProperty("default_warehouse_name")
    @ApiModelProperty("默认发货仓库名称")
    private String defaultWarehouseName;
    @JsonProperty("province_code")
    @ApiModelProperty("省编码")
    private String provinceCode;
    @JsonProperty("province_name")
    @ApiModelProperty("省名称")
    private String provinceName;
    @JsonProperty("city_code")
    @ApiModelProperty("城市编码")
    private String cityCode;
    @JsonProperty("city_code")
    @ApiModelProperty("城市名称")
    private String cityName;
    @JsonProperty("county_code")
    @ApiModelProperty("区域编码")
    private String countyCode;
    @JsonProperty("county_name")
    @ApiModelProperty("区域名称")
    private String countyName;
    @JsonProperty("contract_type")
    @ApiModelProperty("最新合约类型")
    private String contractType;

    @JsonProperty("start_date")
    @ApiModelProperty("合约生效日期")
    private String startDate;
    @JsonProperty("end_date")
    @ApiModelProperty("合约截止日期")
    private String endDate;
    @JsonProperty("gmt_create")
    @ApiModelProperty("创建时间")
    private String gmtCreate;
    @JsonProperty("gmt_modified")
    @ApiModelProperty("修改时间")
    private String gmtModified;
    @JsonProperty("ds")
    @ApiModelProperty("日期分区yyyymmdd")
    private String ds;

}
