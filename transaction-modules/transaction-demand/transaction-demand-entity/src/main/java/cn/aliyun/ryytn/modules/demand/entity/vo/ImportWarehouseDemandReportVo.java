package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.excel.ExcelData;
import cn.aliyun.ryytn.common.excel.HideProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 导入分仓需求提报Vo
 * <AUTHOR>
 * @date 2023/10/18 16:30
 */
@Setter
@Getter
@ToString
public class ImportWarehouseDemandReportVo extends ExcelData implements Serializable
{
    private static final long serialVersionUID = 8052992249435155798L;
    @ApiModelProperty("产品编号")
    @ExcelProperty(value = {"产品编码", "产品编码", "产品编码"}, index = 3)
    private String skuCode;

    @ApiModelProperty("产品名称")
    @ExcelProperty(value = {"产品简称", "产品简称", "产品简称"}, index = 4)
    private String skuName;

    @HideProperty
    @ApiModelProperty("一级品类编号")
    @ExcelProperty(value = {"产品分类编码", "产品分类编码", "产品分类编码"}, index = 10)
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    @ExcelProperty(value = {"产品分类", "产品分类", "产品分类"}, index = 0)
    private String lv1CategoryName;

    @HideProperty
    @ApiModelProperty("二级品类编号")
    @ExcelProperty(value = {"产品大类编码", "产品大类编码", "产品大类编码"}, index = 11)
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    @ExcelProperty(value = {"产品大类", "产品大类", "产品大类"}, index = 1)
    private String lv2CategoryName;

    @HideProperty
    @ApiModelProperty("三级品类编号")
    @ExcelProperty(value = {"产品小类编码", "产品小类编码", "产品小类编码"}, index = 12)
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    @ExcelProperty(value = {"产品小类", "产品小类", "产品小类"}, index = 2)
    private String lv3CategoryName;

    @HideProperty
    @ApiModelProperty("一级渠道编号")
    @ExcelProperty(value = {"一级渠道编码", "一级渠道编码", "一级渠道编码"}, index = 13)
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道")
    @ExcelProperty(value = {"一级渠道", "一级渠道", "一级渠道"}, index = 5)
    private String lv1ChannelName;

    @HideProperty
    @ApiModelProperty("二级渠道编号")
    @ExcelProperty(value = {"二级渠道编码", "二级渠道编码", "二级渠道编码"}, index = 14)
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道")
    @ExcelProperty(value = {"二级渠道", "二级渠道", "二级渠道"}, index = 6)
    private String lv2ChannelName;

    @HideProperty
    @ApiModelProperty("三级渠道编号")
    @ExcelProperty(value = {"三级渠道编码", "三级渠道编码", "三级渠道编码"}, index = 15)
    private String lv3ChannelCode;

    @ApiModelProperty("三级渠道")
    @ExcelProperty(value = {"三级渠道", "三级渠道", "三级渠道"}, index = 7)
    private String lv3ChannelName;

    @HideProperty
    @ApiModelProperty("仓库编码")
    @ExcelProperty(value = {"仓库编码", "仓库编码", "仓库编码"}, index = 16)
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    @ExcelProperty(value = {"仓库", "仓库", "仓库"}, index = 8)
    private String warehouseName;

    @ApiModelProperty("起始年份")
    @ExcelProperty(value = {"起始年份", "起始年份", "起始年份"}, index = 9)
    private String year;

    @ApiModelProperty("时间粒度类型，渠道需求提报导入固定为周粒度")
    @ExcelIgnore
    private final BizDateTypeEnum bizDateType = BizDateTypeEnum.WEEK;

    @ApiModelProperty("时间值，每周第一天的日期，格式为yyyyMMdd")
    @ExcelIgnore
    private String bizDateValue;

    @ApiModelProperty("计划编号")
    @ExcelIgnore
    private String demandPlanCode;

    @ApiModelProperty("需求提报版本")
    @ExcelIgnore
    private String rollingVersion;

    @ApiModelProperty("提报数据")
    @ExcelIgnore
    private Double orderNum;

    @ApiModelProperty("偏差率")
    @ExcelIgnore
    private Double deviationRadio;

    @ApiModelProperty("备注")
    @ExcelIgnore
    private String remark;

    @ApiModelProperty("扩展字段")
    @ExcelIgnore
    private String extend;

    @ApiModelProperty("是否调整：0为否;1为是，默认0")
    @ExcelIgnore
    private Integer isModify;

    @ApiModelProperty("创建人")
    @ExcelIgnore
    private String creator;

    @ApiModelProperty("最后修改人")
    @ExcelIgnore
    private String lastModifier;

    @ApiModelProperty("创建时间")
    @ExcelIgnore
    private Date gmtCreate;

    @ApiModelProperty("修改时间")
    @ExcelIgnore
    private Date gmtModify;

    /**
     * 动态字段映射关系，Key：周第一天的日期-周最后一天的日期，格式：yyyyMMdd-yyyyMMdd，Value：字段值
     */
    @ExcelIgnore
    @JsonIgnore
    @JSONField(serialize = false)
    private Map<String, Double> weekDateValue = new HashMap<>();

    @ExcelIgnore
    private String tableSuffix;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        ImportWarehouseDemandReportVo object = (ImportWarehouseDemandReportVo) o;
        return Objects.equals(this.demandPlanCode, object.demandPlanCode)
            && Objects.equals(this.rollingVersion, object.rollingVersion)
            && Objects.equals(this.skuCode, object.skuCode)
            && Objects.equals(this.lv1CategoryCode, object.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, object.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, object.lv3CategoryCode)
            && Objects.equals(this.lv1ChannelCode, object.lv1ChannelCode)
            && Objects.equals(this.lv2ChannelCode, object.lv2ChannelCode)
            && Objects.equals(this.lv3ChannelCode, object.lv3ChannelCode)
            && Objects.equals(this.warehouseCode, object.warehouseCode)
            && Objects.equals(this.bizDateType, object.bizDateType)
            && Objects.equals(this.bizDateValue, object.bizDateValue);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((demandPlanCode == null) ? 0 : demandPlanCode.hashCode());
        result = prime * result + ((rollingVersion == null) ? 0 : rollingVersion.hashCode());
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        result = prime * result + ((lv1ChannelCode == null) ? 0 : lv1ChannelCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        result = prime * result + ((warehouseCode == null) ? 0 : warehouseCode.hashCode());
        result = prime * result + ((bizDateType == null) ? 0 : bizDateType.hashCode());
        result = prime * result + ((bizDateValue == null) ? 0 : bizDateValue.hashCode());
        return result;
    }
}
