package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询分仓预测结果响应Vo
 * <AUTHOR>
 * @date 2023/11/10 15:06
 */
@Setter
@Getter
@ToString
@ApiModel("查询分仓预测结果响应Vo")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class QueryWarehouseForecastResultRspVo implements Serializable
{
    private static final long serialVersionUID = 7369905121815681100L;
    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("渠道类型")
    private String receiverType;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("仓库")
    private String dcType;

    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;
    /**
     * 一级品类编号
     */
    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    /**
     * 一级品类名称
     */
    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    /**
     * 二级品类编号
     */
    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    /**
     * 二级品类名称
     */
    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    /**
     * 三级品类编号
     */
    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    /**
     * 三级品类名称
     */
    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    @ApiModelProperty("动态字段json字符串，格式不固定，看sql的聚合函数")
    private String data;

    @ApiModelProperty("预测结果")
    private Double predictionResult;

    /**
     * 动态字段数据映射
     */
    @ApiModelProperty("动态字段数据映射")
    private Map<String, Double> dataMap = new HashMap<>();
}
