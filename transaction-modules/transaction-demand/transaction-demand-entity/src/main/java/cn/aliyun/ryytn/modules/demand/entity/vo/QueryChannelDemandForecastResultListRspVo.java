package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求预测结果响应
 * <AUTHOR>
 * @date 2023/11/21 18:25
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求预测结果响应")
public class QueryChannelDemandForecastResultListRspVo implements Serializable
{
    private static final long serialVersionUID = 5433291269824784812L;
    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道名称")
    private String lv1ChannelName;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道名称")
    private String lv2ChannelName;

    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    @ApiModelProperty("三级渠道名称")
    private String lv3ChannelName;

    @ApiModelProperty("预测目标编码")
    private String targetCode;

    @ApiModelProperty("预测目标名称")
    private String targetName;

    @ApiModelProperty("预测算法版本")
    private String algoVersion;

    @ApiModelProperty("预测算法")
    private String algoName;

    @ApiModelProperty("算法版本code")
    private String algoNameAndVersion;

    @ApiModelProperty("算法版本名称")
    private String algoNameAndVersionName;

    @ApiModelProperty("算法预测版本：v- +biz_date")
    private String predictionVersion;

    @ApiModelProperty("模型名称")
    private String modelName;

    @ApiModelProperty("时间")
    private String targetBizDate;

    @ApiModelProperty("时间类型，month:月,week:周")
    private String periodType;

    @ApiModelProperty("预测结果")
    private Double predictionResult;

    @ApiModelProperty("预测结果数据类型，1预测结果，2前6期")
    private Integer forecastResultTypes;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("创建时间")
    private String gmtCreate;

    @ApiModelProperty("日期分区")
    private String ds;

    @ApiModelProperty("动态列数据")
    private Map<String, Double> dataMap = new HashMap<String, Double>();

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        QueryChannelDemandForecastResultListRspVo queryChannelDemandForecastResultListRspVo = (QueryChannelDemandForecastResultListRspVo) o;
        return Objects.equals(this.getLv1CategoryCode(), queryChannelDemandForecastResultListRspVo.getLv1CategoryCode())
            && Objects.equals(this.getLv2CategoryCode(), queryChannelDemandForecastResultListRspVo.getLv2CategoryCode())
            && Objects.equals(this.getLv3CategoryCode(), queryChannelDemandForecastResultListRspVo.getLv3CategoryCode())
            && Objects.equals(this.getLv1ChannelCode(), queryChannelDemandForecastResultListRspVo.getLv1ChannelCode())
            && Objects.equals(this.getLv2ChannelCode(), queryChannelDemandForecastResultListRspVo.getLv2ChannelCode())
            && Objects.equals(this.getLv3ChannelCode(), queryChannelDemandForecastResultListRspVo.getLv3ChannelCode());
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.getLv1CategoryCode() == null) ? 0 : this.getLv1CategoryCode().hashCode());
        result = prime * result + ((this.getLv2CategoryCode() == null) ? 0 : this.getLv2CategoryCode().hashCode());
        result = prime * result + ((this.getLv3CategoryCode() == null) ? 0 : this.getLv3CategoryCode().hashCode());
        result = prime * result + ((this.getLv1ChannelCode() == null) ? 0 : this.getLv1ChannelCode().hashCode());
        result = prime * result + ((this.getLv2ChannelCode() == null) ? 0 : this.getLv2ChannelCode().hashCode());
        result = prime * result + ((this.getLv3ChannelCode() == null) ? 0 : this.getLv3ChannelCode().hashCode());
        return result;
    }
}
