package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求预测筛选条件请求Vo
 * <AUTHOR>
 * @date 2023/10/23 18:00
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求预测筛选条件请求Vo")
public class QueryForecastChannelFilterListReqVo implements Serializable
{
    private static final long serialVersionUID = 2351963753460623538L;
    /**
     * 查询所需的字段，多个字段英文逗号分隔
     */
    @ApiModelProperty("查询所需的字段，多个字段英文逗号分隔")
    private String selectColumns;

    /**
     * 算法版本
     */
    @ApiModelProperty("算法版本")
    private String algoNameAndVersion;

    /**
     * 预测版本
     */
    @ApiModelProperty("预测版本")
    private String predictionVersion;

    /**
     * sku产品编号
     */
    @ApiModelProperty("sku产品编号")
    private String skuCodes;

    /**
     * 一级渠道编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("一级渠道编号，多个编号英文逗号分隔")
    private String lv1ChannelCodes;

    /**
     * 二级渠道编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("二级渠道编号，多个编号英文逗号分隔")
    private String lv2ChannelCodes;

    /**
     * 三级渠道编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("三级渠道编号，多个编号英文逗号分隔")
    private String lv3ChannelCodes;

    /**
     * 一级品类编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("一级品类编号，多个编号英文逗号分隔")
    private String lv1CategoryCodes;

    /**
     * 二级品类编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("二级品类编号，多个编号英文逗号分隔")
    private String lv2CategoryCodes;

    /**
     * 三级品类编号，多个编号英文逗号分隔
     */
    @ApiModelProperty("三级品类编号，多个编号英文逗号分隔")
    private String lv3CategoryCodes;

    @ApiModelProperty("时间类型")
    private String periodType;
}
