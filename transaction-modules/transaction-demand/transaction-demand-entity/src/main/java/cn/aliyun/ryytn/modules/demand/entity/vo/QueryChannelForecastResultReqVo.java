package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道预测结果请求参数Vo
 * <AUTHOR>
 * @date 2023/11/1 15:23
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道列表响应参数")
public class QueryChannelForecastResultReqVo implements Serializable
{
    private static final long serialVersionUID = 8834002538389436022L;
    @ApiModelProperty("算法名称和版本")
    private String algoNameAndVersion;

    @ApiModelProperty("预测结果版本")
    private String predictionVersion;

    @ApiModelProperty("时间类型 week month")
    private String periodType;

    private String selectColumns;

    @ApiModelProperty("产品分类")
    private String lv1CategoryCode;

    @ApiModelProperty("产品分类，英文逗号分隔")
    private String lv1CategoryCodes;

    @ApiModelProperty("产品大类")
    private String lv2CategoryCode;

    @ApiModelProperty("产品大类，英文逗号分隔")
    private String lv2CategoryCodes;

    @ApiModelProperty("产品小类")
    private String lv3CategoryCode;

    @ApiModelProperty("产品小类，英文逗号分隔")
    private String lv3CategoryCodes;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道编号，英文逗号分隔")
    private String lv1ChannelCodes;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道编号，英文逗号分隔")
    private String lv2ChannelCodes;

    @ApiModelProperty("分组字段列表")
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("分组字段")
    private String groupColumn;

    @ApiModelProperty("排序字段")
    private String sortColumn;

    @ApiModelProperty("业务标识列表")
    private List<QueryForecastResultRspVo> keyList;

    @ApiModelProperty("查询字段")
    private String selectColumn;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;
}
