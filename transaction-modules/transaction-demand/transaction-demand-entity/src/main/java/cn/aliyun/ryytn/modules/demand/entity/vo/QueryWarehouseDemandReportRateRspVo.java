package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询分仓需求提报仓库比例响应
 * <AUTHOR>
 * @date 2023/11/28 19:45
 */
@Setter
@Getter
@ToString
@ApiModel("查询分仓需求提报仓库比例响应")
public class QueryWarehouseDemandReportRateRspVo implements Serializable
{
    private static final long serialVersionUID = -8319263024196392572L;
    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("渠道类型")
    private String channelType;

    @ApiModelProperty("日期，yyyyMMdd")
    private String bizDateValue;

    @ApiModelProperty("比例")
    private Double outboundRate;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        QueryWarehouseDemandReportRateRspVo object = (QueryWarehouseDemandReportRateRspVo) o;
        return Objects.equals(this.getSkuCode(), object.getSkuCode())
            && Objects.equals(this.getLv1CategoryCode(), object.getLv1CategoryCode())
            && Objects.equals(this.getLv2CategoryCode(), object.getLv2CategoryCode())
            && Objects.equals(this.getLv3CategoryCode(), object.getLv3CategoryCode())
            && Objects.equals(this.getLv1ChannelCode(), object.getLv1ChannelCode())
            && Objects.equals(this.getLv2ChannelCode(), object.getLv2ChannelCode())
            && Objects.equals(this.getLv3ChannelCode(), object.getLv3ChannelCode());
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.getSkuCode() == null) ? 0 : this.getSkuCode().hashCode());
        result = prime * result + ((this.getLv1CategoryCode() == null) ? 0 : this.getLv1CategoryCode().hashCode());
        result = prime * result + ((this.getLv2CategoryCode() == null) ? 0 : this.getLv2CategoryCode().hashCode());
        result = prime * result + ((this.getLv3CategoryCode() == null) ? 0 : this.getLv3CategoryCode().hashCode());
        result = prime * result + ((this.getLv1ChannelCode() == null) ? 0 : this.getLv1ChannelCode().hashCode());
        result = prime * result + ((this.getLv2ChannelCode() == null) ? 0 : this.getLv2ChannelCode().hashCode());
        result = prime * result + ((this.getLv3ChannelCode() == null) ? 0 : this.getLv3ChannelCode().hashCode());
        return result;
    }
}
