package cn.aliyun.ryytn.modules.demand.entity.dos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @Date 2024-08-26 22:15
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Accessors(chain = true)
public class MarketActivityExpertDO extends BaseDO{

    private static final long serialVersionUID = -8559241237346878203L;


    public String actiCode;

    /**
     * 达人ID
     */
    public String expertId;

    /**
     * 达人组
     */
    public String expertGroup;

    /**
     * 备用字段1
     */
    public String remark1;

    /**
     * 备用字段2
     */
    public String remark2;

    /**
     * 删除状态
     */
    public Integer deleted;
}
