package cn.aliyun.ryytn.modules.demand.entity.dos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @Date 2024-09-04 16:29
 * @Description 达人活动-产品明细表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Accessors(chain = true)
public class MarketActivitySkuDetailDO extends BaseDO{

    private static final long serialVersionUID = 2758580549888128938L;

    private String actiCode;

    private String skuCode;

    /**
     * 计划出货开始时间
     */
    private String actiSkuDeliveryBeginTime;

    /**
     * 计划活动出库量
     */
    private String actiSkuDeliveryNumsLimit;

    private Integer auditStatus;

    private String actiSkuWeekBeginTime;
    /**
     * 删除状态
     */
    public Integer deleted;
}
