package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;

import cn.aliyun.ryytn.common.utils.string.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求计划详情列表响应
 * <AUTHOR>
 * @date 2023/11/8 12:51
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求计划详情列表响应")
public class QueryChannelDemandPlanVersionListRspVo implements Serializable
{
    private static final long serialVersionUID = 818830244209882185L;
    @ExcelProperty(value = {"#"}, index = 0)
    private Integer rowId;

    @ApiModelProperty("编号")
    @ExcelIgnore
    private String id;

    @ApiModelProperty("需求计划编号")
    @ExcelIgnore
    private String demandPlanCode;

    @ApiModelProperty("子计划清单组编号")
    @ExcelIgnore
    private Long groupId;

    @ApiModelProperty("产品编号")
    @ExcelProperty(value = {"产品编号"}, index = 6)
    private String skuCode;

    @ApiModelProperty("产品名称")
    @ExcelProperty(value = {"产品名称"}, index = 7)
    private String skuName;

    @ApiModelProperty("一级渠道编号")
    @ExcelIgnore
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道名称")
    @ExcelProperty(value = "一级渠道", index = 4)
    private String lv1ChannelName;

    @ApiModelProperty("二级渠道编号")
    @ExcelIgnore
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道名称")
    @ExcelProperty(value = "二级渠道", index = 5)
    private String lv2ChannelName;

    @ApiModelProperty("三级渠道编号")
    @ExcelIgnore
    private String lv3ChannelCode;

    @ApiModelProperty("三级渠道名称")
    @ExcelIgnore
    private String lv3ChannelName;

    @ApiModelProperty("一级品类编号")
    @ExcelIgnore
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    @ExcelProperty(value = "产品分类", index = 1)
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    @ExcelIgnore
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    @ExcelProperty(value = "产品大类", index = 2)
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    @ExcelIgnore
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    @ExcelProperty(value = "产品小类", index = 3)
    private String lv3CategoryName;

    @ApiModelProperty("版本编号")
    @ExcelIgnore
    private String versionId;

    @ApiModelProperty("版本时间")
    @ExcelIgnore
    private String versionDate;

    @ApiModelProperty("客户code")
    @ExcelIgnore
    private String resellerCode;

    @ApiModelProperty("客户名称")
    @ExcelIgnore
    private String resellerName;

    @ApiModelProperty("生命周期编号")
    @ExcelIgnore
    private String lifecycleCode;

    @ApiModelProperty("生命周期名称")
    @ExcelIgnore
    private String lifecycleName;

    @ApiModelProperty("算法版本")
    @ExcelIgnore
    private String algoVersion;

    @ApiModelProperty("计划日期")
    @ExcelIgnore
    private String planDate;

    @ApiModelProperty("计划数")
    @ExcelIgnore
//    @ExcelProperty(value = {"数量"}, index = 10)
    private Double planValue;


    /**
     * 已过周的取实发数量,未过周的取计划数量,默认为计划量,外层程序根据滚动版本时间判断是否取实发还是计划
     */
    @ApiModelProperty("数量实发/计划")
    @ExcelProperty(value = {"数量实发/计划"}, index = 10)
    private Double orderNum;

    /**
     * 数据量实发,关联到了为实发,关联不到为0
     */
    @ApiModelProperty("数据量实发")
    @ExcelIgnore
    private Double orderNumReal;

    @ApiModelProperty("备注")
    @ExcelIgnore
    private String planRemark;

    @ApiModelProperty("参考类型")
    @ExcelIgnore
    private String refType;

    @ApiModelProperty("参考版本")
    @ExcelIgnore
    private String refVersion;

    @ApiModelProperty("标签")
    @ExcelIgnore
    private String label;

    @ApiModelProperty("状态，-1:待试算;未提交:0，已提交:1")
    @ExcelIgnore
    private Integer status;

    @ApiModelProperty("是否调整")
    @ExcelIgnore
    private Boolean isModify;

    @ApiModelProperty("是否删除")
    @ExcelIgnore
    private Boolean deleted;

    @ApiModelProperty("创建时间")
    @ExcelIgnore
    private String gmtCreate;

    @ApiModelProperty("修改时间")
    @ExcelIgnore
    private String gmtModify;

    @ApiModelProperty("修改人")
    @ExcelIgnore
    private String lastModifier;

    @ExcelProperty(value = {"出货周"}, index = 8)
    private String week;

    @ExcelProperty(value = {"出货月"}, index = 9)
    private String month;

    @ApiModelProperty("动态数据映射")
    @ExcelIgnore
    private Map<String, PlanValue> dataMap = new HashMap<String, PlanValue>();

    @ApiModelProperty("编号，英文逗号分隔")
    @ExcelIgnore
    private String ids;

    @ApiModelProperty("产品单位系数，页面编辑必须为此系数的倍数")
    @ExcelIgnore
    private Integer planUnitCnt;

    /**
     *
     * @Description 获取版本号前缀，用于复制版本号排序
     * @return String
     * <AUTHOR>
     * @date 2024年01月26日 15:21
     */
    @JsonIgnore
    public String getVersionIdPreffix()
    {
        return StringUtils.substringBefore(this.versionId, StringUtils.DATE_SEPARATOR);
    }

    /**
     *
     * @Description 获取版本号后缀，用于复制版本号排序
     * @return String
     * <AUTHOR>
     * @date 2024年01月26日 15:21
     */
    @JsonIgnore
    public String getVersionIdSuffix()
    {
        return StringUtils.substringAfter(this.versionId, StringUtils.DATE_SEPARATOR);
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        QueryChannelDemandPlanVersionListRspVo queryChannelDemandPlanVersionListRspVo = (QueryChannelDemandPlanVersionListRspVo) o;
        return Objects.equals(this.skuCode, queryChannelDemandPlanVersionListRspVo.skuCode)
            && Objects.equals(this.lv1CategoryCode, queryChannelDemandPlanVersionListRspVo.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, queryChannelDemandPlanVersionListRspVo.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, queryChannelDemandPlanVersionListRspVo.lv3CategoryCode)
            && Objects.equals(this.lv1ChannelCode, queryChannelDemandPlanVersionListRspVo.lv1ChannelCode)
            && Objects.equals(this.lv2ChannelCode, queryChannelDemandPlanVersionListRspVo.lv2ChannelCode)
            && Objects.equals(this.lv3ChannelCode, queryChannelDemandPlanVersionListRspVo.lv3ChannelCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        result = prime * result + ((lv1ChannelCode == null) ? 0 : lv1ChannelCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        return result;
    }
}
