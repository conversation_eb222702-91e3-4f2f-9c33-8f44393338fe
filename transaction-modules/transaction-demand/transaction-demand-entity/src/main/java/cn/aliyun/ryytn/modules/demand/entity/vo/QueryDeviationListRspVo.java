package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询偏差比对-列表返回数据
 * <AUTHOR>
 * @date 2023/11/2 10:48
 */
@Getter
@Setter
@ToString
@ApiModel("查询偏差比对-列表返回数据")
public class QueryDeviationListRspVo implements Serializable
{
    private static final long serialVersionUID = 3126279544194163363L;
    @ApiModelProperty("时间： 年月")
    private String targetBizDate;
    @ApiModelProperty("需求提报")
    private Double orderNum;
    @ApiModelProperty("销售目标")
    private Double planOrderNum;
    @ApiModelProperty("实际值")
    private Double obserValue;
    @ApiModelProperty("需求预测版本")
    private String predictionVersion;
    @ApiModelProperty("需求预测")
    private Double predictionResult;
    @ApiModelProperty("需求计划")
    private Double demandPlanResult;
    /**
     * 动态字段数据映射
     */
    @ApiModelProperty("动态字段数据映射")
    private Map<String, Object> dataMap = new HashMap<>();

}
