package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分仓需求提报标记
 * <AUTHOR>
 * @date 2024/2/28 17:25
 */
@Setter
@Getter
@ToString
@ApiModel("分仓需求提报标记")
public class WarehouseDemandPlanMarkDto implements Serializable
{
    private static final long serialVersionUID = 1775432909285765151L;
    @ApiModelProperty("计划编号")
    private String demandPlanCode;

    @ApiModelProperty("版本号")
    private String versionId;

    @ApiModelProperty("渠道类型")
    private String receiverType;

    @ApiModelProperty("产品小类")
    private String lv3CategoryCode;

    @ApiModelProperty("时间")
    private String bizDateValue;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("修改人")
    private String lastModifier;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("修改时间")
    private Date gmtModify;

    @ApiModelProperty("时间集合")
    private List<String> bizDateValueList;

    @ApiModelProperty("数据类型0:日销;1活动")
    private Integer planDataType;
}