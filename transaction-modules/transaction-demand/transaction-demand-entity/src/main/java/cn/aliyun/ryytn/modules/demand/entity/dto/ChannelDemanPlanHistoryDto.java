package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道需求计划修改历史
 * <AUTHOR>
 * @date 2024/1/10 18:54
 */
@Setter
@Getter
@ToString
@ApiModel("渠道需求计划修改历史")
public class ChannelDemanPlanHistoryDto implements Serializable
{
    private static final long serialVersionUID = 4251909092270270363L;
    @ApiModelProperty("编号")
    private String id;

    @ApiModelProperty("计划编号")
    private String demandPlanCode;

    @ApiModelProperty("版本号")
    private String versionId;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道名称")
    private String lv1ChannelName;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道名称")
    private String lv2ChannelName;

    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    @ApiModelProperty("三级渠道名称")
    private String lv3ChannelName;

    @ApiModelProperty("日期名称")
    private String planDateLabel;

    @ApiModelProperty("日期")
    private String planDate;

    @ApiModelProperty("数量")
    private Double planValue;

    @ApiModelProperty("旧数量")
    private Double oldPlanValue;

    @ApiModelProperty("偏差比例")
    private Double deviationRadio;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("扩展字段")
    private String extend;

    @ApiModelProperty("修改人")
    private String lastModifier;

    @ApiModelProperty("修改时间")
    private Date gmtModify;

    @ApiModelProperty("分组编号")
    private Integer groupId;
}
