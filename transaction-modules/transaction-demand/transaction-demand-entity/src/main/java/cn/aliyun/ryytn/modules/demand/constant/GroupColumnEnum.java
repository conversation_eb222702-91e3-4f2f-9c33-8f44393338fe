package cn.aliyun.ryytn.modules.demand.constant;

/**
 * @Description 分组聚合列枚举
 * <AUTHOR>
 * @date 2023/12/23 12:35
 */
public enum GroupColumnEnum
{
    lv1Category("lv1_category_code,lv1_category_name", SortColumnEnum.lv1Category),
    lv2Category("lv2_category_code,lv2_category_name", SortColumnEnum.lv2Category),
    lv3Category("lv3_category_code,lv3_category_name", SortColumnEnum.lv3Category),
    sku("sku_code,sku_name", SortColumnEnum.sku),
    lv1Channel("lv1_channel_code,lv1_channel_name", SortColumnEnum.lv1Channel),
    lv2Channel("lv2_channel_code,lv2_channel_name", SortColumnEnum.lv2Channel),
    lv3Channel("lv3_channel_code,lv3_channel_name", SortColumnEnum.lv3Channel),
    receiverType("receiver_type", SortColumnEnum.receiverType),
    warehouse("warehouse_code,warehouse_name", SortColumnEnum.warehouse),
    item("item_id,item_name", SortColumnEnum.item),
    startPoint("start_point_id,start_point_name", SortColumnEnum.startPoint),
    endPoint("end_point_id,end_point_name", SortColumnEnum.endPoint),
    startPhysicalPoint("start_physical_point_id,start_physical_point_name", SortColumnEnum.startPhysicalPoint),
    endPhysicalPoint("end_physical_point_id,end_physical_point_name", SortColumnEnum.endPhysicalPoint),
    shippingType("shipping_type_group_id,shipping_type_group_name", SortColumnEnum.shippingType),
    validRule("validity_period", SortColumnEnum.validRule),
    productionDate("production_date", SortColumnEnum.productionDate),
    distributeType("distribute_type", SortColumnEnum.distributeType),
    departureDate("departure_date", SortColumnEnum.departureDate),
    departureValid("departure_valid_name", SortColumnEnum.departureValid),
    arrivalDate("arrival_date", SortColumnEnum.arrivalDate),
    arrivalValid("arrival_valid_name", SortColumnEnum.arrivalValid),
    stockPoint("stock_point_id,stock_point_name", SortColumnEnum.stockPoint),
    physicalStockPoint("physical_point_id,physical_point_name", SortColumnEnum.physicalStockPoint),
    validName("valid_name", SortColumnEnum.validName),
    date("date", SortColumnEnum.date),
    planDataType("plan_data_type", SortColumnEnum.planDataType),
    validityPeriod("validity_period", SortColumnEnum.validityPeriod);

    private final String columnName;

    private final SortColumnEnum sortColumn;

    GroupColumnEnum(String columnName, SortColumnEnum sortColumn)
    {
        this.columnName = columnName;
        this.sortColumn = sortColumn;
    }

    public String getColumnName()
    {
        return this.columnName;
    }

    public SortColumnEnum getSortColumn()
    {
        return this.sortColumn;
    }
}
