package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询分仓需求计划列表详情Vo
 * <AUTHOR>
 * @date 2023/10/24 16:14
 */
@Setter
@Getter
@ToString
@ApiModel("查询分仓需求计划列表详情请求Vo")
public class QueryWarehouseDemandPlanVersionListReqVo implements Serializable
{
    private static final long serialVersionUID = 7416930658747570699L;
    @ApiModelProperty("版本ID 逗号分隔")
    private String versionIds;

    @ApiModelProperty("版本ID")
    private String versionId;

    @ApiModelProperty("需求计划Code 逗号分隔")
    private String demandPlanCodes;

    @ApiModelProperty("需求计划Code")
    private String demandPlanCode;

    @ApiModelProperty("版本日期 逗号分隔")
    private String versionDates;

    @ApiModelProperty("版本日期")
    private String versionDate;

    @ApiModelProperty("分组编号")
    private Long groupId;

    @ApiModelProperty("产品分类一级编码 逗号分隔")
    private String lv1CategoryCodes;

    @ApiModelProperty("产品分类一级编码")
    private String lv1CategoryCode;

    @ApiModelProperty("产品分类二级编码 逗号分隔")
    private String lv2CategoryCodes;

    @ApiModelProperty("产品分类二级编码")
    private String lv2CategoryCode;

    @ApiModelProperty("产品分类三级级编码 逗号分隔")
    private String lv3CategoryCodes;

    @ApiModelProperty("产品分类三级级编码")
    private String lv3CategoryCode;

    @ApiModelProperty("商品code 逗号分隔")
    private String skuCodes;

    @ApiModelProperty("商品code")
    private String skuCode;

    @ApiModelProperty("生命周期 逗号分隔")
    private String lifecycleCodes;

    @ApiModelProperty("生命周期")
    private String lifecycleCode;

    @ApiModelProperty("仓库编号 逗号分隔")
    private String warehouseCodes;

    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    @ApiModelProperty("一级仓库类型编码，BDC/CDC/RDC/FDC/eDC 逗号分隔")
    private String lv1TypeCodes;

    @ApiModelProperty("一级仓库类型编码，BDC/CDC/RDC/FDC/eDC")
    private String lv1TypeCode;

    @ApiModelProperty("渠道类型")
    private String receiverType;

    @ApiModelProperty("开始时间")
    private String beginDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("状态 -待试算:-1;未提交:0;已提交:1")
    private Integer status;

    @ApiModelProperty("是否调整：0为否;1为是，默认0")
    private Integer isModify;

    @ApiModelProperty("是否删除：0为否;1为是 默认0")
    private Integer deleted;

    @ApiModelProperty("分组列枚举列表")
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("分组列")
    private String groupColumn;

    @ApiModelProperty("排序列")
    private String sortColumn;
    @ApiModelProperty("类型0:日销;1活动")
    private Integer planDataType;

    @ApiModelProperty("类型0:日销;1活动")
    private String planDataTypes;
    @ApiModelProperty("业务唯一表示列表")
    private List<QueryWarehouseDemandPlanVersionListRspVo> keyList;
}
