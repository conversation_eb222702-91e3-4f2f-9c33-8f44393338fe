package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 删除需求计划请求Vo
 * <AUTHOR>
 * @date 2023/10/27 15:51
 */
@Setter
@Getter
@ToString
public class DeleteDemandPlanReqVo implements Serializable
{
    private static final long serialVersionUID = 3314601117291344835L;
    @ApiModelProperty("需求计划code")
    private String demandPlanCode;

    @ApiModelProperty("计划类型： warehouse: 分仓 order： 渠道")
    private SubjectTypeEnum subjectType;
}
