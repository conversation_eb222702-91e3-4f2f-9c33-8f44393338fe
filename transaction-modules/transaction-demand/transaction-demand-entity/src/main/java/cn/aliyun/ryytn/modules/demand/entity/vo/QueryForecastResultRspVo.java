package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询预测结果
 * <AUTHOR>
 * @date 2023/11/3 14:57
 */
@Setter
@Getter
@ToString
@ApiModel("查询预测结果响应Vo")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class QueryForecastResultRspVo implements Serializable
{

    private static final long serialVersionUID = -9071558345139231202L;
    /**
     * 一级渠道编号
     */
    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    /**
     * 一级渠道名称
     */
    @ApiModelProperty("一级渠道名称")
    private String lv1ChannelName;

    /**
     * 二级渠道编号
     */
    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    /**
     * 二级渠道名称
     */
    @ApiModelProperty("二级渠道名称")
    private String lv2ChannelName;

    /**
     * 一级品类编号
     */
    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    /**
     * 一级品类名称
     */
    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    /**
     * 二级品类编号
     */
    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    /**
     * 二级品类名称
     */
    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    /**
     * 三级品类编号
     */
    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    /**
     * 三级品类名称
     */
    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    @ApiModelProperty("动态字段json串，格式以查询sql聚合函数为准")
    private String data;

    /**
     * 动态字段数据映射
     */
    @ApiModelProperty("动态字段数据映射")
    private Map<String, Double> dataMap = new HashMap<>();
}
