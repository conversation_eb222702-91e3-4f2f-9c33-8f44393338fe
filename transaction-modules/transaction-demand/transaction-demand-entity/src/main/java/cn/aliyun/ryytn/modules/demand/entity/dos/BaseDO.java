package cn.aliyun.ryytn.modules.demand.entity.dos;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @description 创建基类
 * @date 2024/08/26 18:25
 **/
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class BaseDO implements Serializable {

    private static final long serialVersionUID = 2299447888075812358L;

    private Long id;
    private String creator;
    private String lastModifier;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale = "zn", pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime gmtCreate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale = "zn", pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime gmtModify;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BaseDO baseDO = (BaseDO) o;
        return Objects.equals(id, baseDO.id) && Objects.equals(creator, baseDO.creator) && Objects.equals(lastModifier, baseDO.lastModifier) && Objects.equals(gmtCreate, baseDO.gmtCreate) && Objects.equals(gmtModify, baseDO.gmtModify);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, creator, lastModifier, gmtCreate, gmtModify);
    }
}
