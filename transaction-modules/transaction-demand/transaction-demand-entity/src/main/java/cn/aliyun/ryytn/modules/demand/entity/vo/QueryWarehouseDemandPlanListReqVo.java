package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.modules.demand.constant.SubjectDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询分仓需求计划列表Vo
 * <AUTHOR>
 * @date 2023/10/24 10:18
 */
@Setter
@Getter
@ToString
@ApiModel("查询分仓需求计划列表请求Vo")
public class QueryWarehouseDemandPlanListReqVo implements Serializable
{
    private static final long serialVersionUID = -8981288169152072139L;
    @ApiModelProperty("需求计划Code")
    private String demandPlanCode;

    @ApiModelProperty("需求计划Code,逗号分隔")
    private String demandPlanCodes;

    @ApiModelProperty("销售年度")
    private String marketingYear;

    @ApiModelProperty("销售年度,逗号分隔")
    private String marketingYears;

    @ApiModelProperty("主体类型:factory:工厂,warehouse:仓库,order:分销渠道，all:汇总")
    private SubjectTypeEnum subjectType;

    @ApiModelProperty("主体维度类型")
    private SubjectDimensionTypeEnum subjectDimensionType;

    @ApiModelProperty("状态: 0 已发布 -1 已下线")
    private Integer status;

    @ApiModelProperty("是否已删除")
    private Boolean deleted;
}
