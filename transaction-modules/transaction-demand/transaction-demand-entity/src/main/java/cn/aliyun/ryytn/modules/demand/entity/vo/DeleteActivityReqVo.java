package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 删除促销活动-活动vo
 * <AUTHOR>
 * @date 2023/10/23 16:38
 */
@Setter
@Getter
@ToString
@ApiModel("删除促销活动-活动vo")
public class DeleteActivityReqVo implements Serializable
{

    private static final long serialVersionUID = 55641665525687581L;

    @ApiModelProperty("自增主键")
    private String id;

    @ApiModelProperty("创建人")
    private String creator;
    @JsonProperty("last_modifier")
    @ApiModelProperty("最后修改人")
    private String lastModifier;

    @JsonProperty("gmt_create")
    @ApiModelProperty("创建时间")
    private String gmtCreate;

    @JsonProperty("gmt_modified")
    @ApiModelProperty("修改时间")
    private String gmtModified;

    @ApiModelProperty("扩展字段")
    private String extend;

    @JsonProperty("acti_code")
    @ApiModelProperty("营销活动编码")
    private String actiCode;

    @JsonProperty("acti_name")
    @ApiModelProperty("营销活动名称")
    private String actiName;

    @JsonProperty("acti_level")
    @ApiModelProperty("活动等级:S级、A级、B级、C级")
    private String actiLevel;

    @JsonProperty("event_period")
    @ApiModelProperty("活动频率：irregulars不定期、week每周（1~7）、month每月（1~31）、year每年（日期")
    private String eventPeriod;

    @JsonProperty("start_date")
    @ApiModelProperty("生效日期")
    private String startDate;

    @JsonProperty("end_date")
    @ApiModelProperty("截止日期")
    private String endDate;

    @JsonProperty("acti_area")
    @ApiModelProperty("活动区域-省份")
    private String actiArea;

    @JsonProperty("acti_type")
    @ApiModelProperty("活动类型：线上促销、线下促销、达人活动")
    private String actiType;

    @JsonProperty("acti_delivery_date")
    @ApiModelProperty("活动出库时间")
    private String actiDeliveryDate;

    @ApiModelProperty("是否已删除，默认0，0:未删除；1:已删除")
    private Integer deleted;

    @JsonProperty("event_cycle")
    @ApiModelProperty("每周，月的几号")
    private String eventCycle;

    @JsonProperty("activity_cycle")
    @ApiModelProperty("用户输入的活动开始和结束日期")
    private String activityCycle;

    @JsonProperty("screen_desc")
    @ApiModelProperty("备注")
    private String screenDesc;
    @ApiModelProperty("年份")
    private String year;

}
