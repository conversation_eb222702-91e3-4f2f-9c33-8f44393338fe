package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分仓需求计划子计划清单组
 * <AUTHOR>
 * @date 2023/11/16 11:36
 */
@Setter
@Getter
@ToString
@ApiModel("分仓需求计划子计划清单组")
public class WarehouseDemandSubPlanGroupVo implements Serializable
{
    private static final long serialVersionUID = -1862941272189764193L;
    @ApiModelProperty("需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty("版本编号")
    private String versionId;

    @ApiModelProperty("分组编号")
    private Long groupId;

    @ApiModelProperty("渠道类型")
    private String receiverType;

    @ApiModelProperty("子计划编制状态")
    private Integer status;

    @ApiModelProperty("提交时间")
    private String submitTime;
}
