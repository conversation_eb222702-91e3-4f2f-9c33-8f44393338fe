package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道需求计划子计划清单组
 * <AUTHOR>
 * @date 2023/11/16 11:36
 */
@Setter
@Getter
@ToString
@ApiModel("渠道需求计划子计划清单组")
public class ChannelDemandSubPlanGroupVo implements Serializable
{
    private static final long serialVersionUID = 4487107680864107434L;
    @ApiModelProperty("需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty("版本编号")
    private String versionId;

    @ApiModelProperty("分组编号")
    private Long groupId;

    @ApiModelProperty("计划对象")
    private String planObject;

    @ApiModelProperty("计划主体（渠道）")
    private String planSubject;

    @ApiModelProperty("子计划编制状态")
    private Integer status;

    @ApiModelProperty("提交时间")
    private String submitTime;
}
