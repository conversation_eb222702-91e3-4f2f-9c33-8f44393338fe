package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 产品锁定渠道Dto
 * <AUTHOR>
 * @date 2023/10/26 17:21
 */
@Setter
@Getter
@ToString
@ApiModel("产品锁定渠道Dto")
public class LockChannelDto implements Serializable
{
    private static final long serialVersionUID = 5630944403378713765L;

    @ApiModelProperty("主键id 关联产品锁定期表id")
    private String lockId;

    @ApiModelProperty("锁定渠道")
    private String channelId;

    @ApiModelProperty("锁定渠道名称")
    private String channelName;
}
