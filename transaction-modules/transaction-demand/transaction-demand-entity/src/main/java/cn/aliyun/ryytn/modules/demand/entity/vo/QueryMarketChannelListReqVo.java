package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询促销活动渠道树请求参数
 * <AUTHOR>
 * @date 2023/10/26 10:32
 */
@Setter
@Getter
@ToString
@ApiModel("查询促销活动渠道请求参数")
public class QueryMarketChannelListReqVo implements Serializable
{
    private static final long serialVersionUID = -3884812455578255615L;
    @ApiModelProperty("营销活动编码")
    private String actiCode;
    @ApiModelProperty("是否已删除，默认0，0:未删除；")
    private Integer deleted;
}
