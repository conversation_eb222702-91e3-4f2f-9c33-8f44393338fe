package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询分仓预测结果请求Vo
 * <AUTHOR>
 * @date 2023/10/24 9:26
 */
@Setter
@Getter
@ToString
@ApiModel("查询分仓预测结果请求Vo")
public class QueryForecastWarehouseListReqVo implements Serializable
{
    private static final long serialVersionUID = 444924829466943938L;
    @ApiModelProperty("算法版本")
    private String algoNameAndVersion;


    @ApiModelProperty("预测版本")
    private String predictionVersion;

    @ApiModelProperty("预测版本模糊匹配")
    private String predictionVersionKey;

    @ApiModelProperty("分组字段列表")
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("业务数据唯一表示列表")
    private List<QueryWarehouseForecastResultRspVo> keyList;

    @ApiModelProperty("分组字段")
    private String groupColumn;

    @ApiModelProperty("排序字段")
    private String sortColumn;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("三级渠道类型")
    private String receiverType;

    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    @ApiModelProperty("产品编号，英文逗号分隔")
    private String skuCodes;

    @ApiModelProperty("一级品类编号，英文逗号分隔")
    private String lv1CategoryCodes;

    @ApiModelProperty("二级品类编号，英文逗号分隔")
    private String lv2CategoryCodes;

    @ApiModelProperty("三级品类编号，英文逗号分隔")
    private String lv3CategoryCodes;

    @ApiModelProperty("一级渠道编号，英文逗号分隔")
    private String lv1ChannelCodes;

    @ApiModelProperty("二级渠道编号，英文逗号分隔")
    private String lv2ChannelCodes;

    @ApiModelProperty("三级渠道编号，英文逗号分隔")
    private String lv3ChannelCodes;

    @ApiModelProperty("三级渠道类型，英文逗号分隔")
    private String receiverTypes;

    @ApiModelProperty("仓库编号，英文逗号分隔")
    private String warehouseCodes;

    @ApiModelProperty("查询字段")
    private String selectColumn;

    @ApiModelProperty("时间粒度")
    private BizDateTypeEnum periodType;

    @ApiModelProperty("时间")
    private String targetBizDate;
}
