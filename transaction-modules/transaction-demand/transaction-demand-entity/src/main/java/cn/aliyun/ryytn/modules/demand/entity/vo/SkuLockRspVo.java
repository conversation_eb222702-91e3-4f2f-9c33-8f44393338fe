package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.modules.demand.entity.dto.SkuLockDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 产品锁定期响应Vo
 * <AUTHOR>
 * @date 2023/10/26 14:25
 */
@Setter
@Getter
@ToString
@ApiModel("产品锁定期dto")
public class SkuLockRspVo extends SkuLockDto implements Serializable
{
    private static final long serialVersionUID = -8707788764798502352L;
    
    @ApiModelProperty("产品锁定渠道名称（二级）")
    private String ChannelNameList;
}
