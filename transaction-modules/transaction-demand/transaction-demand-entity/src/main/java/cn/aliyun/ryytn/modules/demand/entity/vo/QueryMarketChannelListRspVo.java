package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询促销活动渠道列表响应参数
 * <AUTHOR>
 * @date 2023/10/26 9:46
 */
@Setter
@Getter
@ToString
@ApiModel("查询促销活动渠道列表响应参数")
public class QueryMarketChannelListRspVo implements Serializable
{

    private static final long serialVersionUID = -3994812455578255615L;
    @ApiModelProperty("分销渠道编码")
    private String id;

    @JsonProperty("acti_code")
    @ApiModelProperty("营销活动编码")
    private String actiCode;
    @JsonProperty("acti_name")
    @ApiModelProperty("营销活动名称")
    private String actiName;
    @JsonProperty("reseller_code")
    @ApiModelProperty("分销渠道编码")
    private String resellerCode;

    @JsonProperty("reseller_name")
    @ApiModelProperty("分销渠道名称")
    private String resellerName;
    @JsonProperty("lv1_channel_code")
    @ApiModelProperty("一级渠道类型编码")
    private String lv1ChannelCode;
    @JsonProperty("lv1_channel_name")
    @ApiModelProperty("一级渠道类型名称")
    private String lv1ChannelName;
    @JsonProperty("lv2_channel_code")
    @ApiModelProperty("二级渠道类型编码")
    private String lv2ChannelCode;
    @JsonProperty("lv2_channel_name")
    @ApiModelProperty("二级渠道类型名称")
    private String lv2ChannelName;

    @JsonProperty("lv3_channel_code")
    @ApiModelProperty("三级渠道类型编码")
    private String lv3ChannelCode;
    @JsonProperty("lv2_channel_name")
    @ApiModelProperty("三级渠道类型名称")
    private String lv3ChannelName;
    @JsonProperty("acti_reseller_obj")
    @ApiModelProperty("计划渠道类型：lv3_channel三级渠道;lv2_channel,二级; lv1_channel:一级渠道;reseller_c客户")
    private String actiResellerObj;
    @ApiModelProperty("是否已删除，默认0，0:未删除；1:已删除")
    private Integer deleted;


}
