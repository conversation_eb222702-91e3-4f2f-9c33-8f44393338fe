package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;
import java.util.Objects;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 销售目标
 * <AUTHOR>
 * @date 2023/11/3 16:59
 */
@Setter
@Getter
@ToString
public class SaleTargetDto implements Serializable
{
    private static final long serialVersionUID = -1388495514364566019L;
    private String id;

    private String skuCode;

    private String skuName;

    private String lv1CategoryCode;

    private String lv1CategoryName;

    private String lv2CategoryCode;

    private String lv2CategoryName;

    private String lv3CategoryCode;

    private String lv3CategoryName;

    private String lv1ChannelCode;

    private String lv1ChannelName;

    private String lv2ChannelCode;

    private String lv2ChannelName;

    private String lv3ChannelCode;

    private String lv3ChannelName;

    private BizDateTypeEnum bizDateType;

    private String rollingVersion;

    private String bizDateValue;

    private Double orderNum;

    private String unit;

    private String gmtCreate;

    private String creator;

    private String gmtModify;

    private String lastModifier;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        SaleTargetDto saleTargetDto = (SaleTargetDto) o;
        return Objects.equals(this.skuCode, saleTargetDto.skuCode)
            && Objects.equals(this.lv1CategoryCode, saleTargetDto.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, saleTargetDto.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, saleTargetDto.lv3CategoryCode)
            && Objects.equals(this.lv1ChannelCode, saleTargetDto.lv1ChannelCode)
            && Objects.equals(this.lv2ChannelCode, saleTargetDto.lv2ChannelCode)
            && Objects.equals(this.lv3ChannelCode, saleTargetDto.lv3ChannelCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        result = prime * result + ((lv1ChannelCode == null) ? 0 : lv1ChannelCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        return result;
    }
}
