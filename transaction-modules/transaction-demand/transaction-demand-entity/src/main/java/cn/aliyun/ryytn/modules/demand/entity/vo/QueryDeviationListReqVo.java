package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询偏差比对-列表参数
 * <AUTHOR>
 * @date 2023/10/31 17:03
 */
@Setter
@Getter
@ToString
@ApiModel("查询偏差比对-列表参数")
public class QueryDeviationListReqVo implements Serializable
{
    private static final long serialVersionUID = 7830263703835505978L;
    @ApiModelProperty("产品分类编码")
    private String lv1CategoryCode;
    @ApiModelProperty("产品大类编码")
    private String lv2CategoryCode;
    @ApiModelProperty("产品小类编码")
    private String lv3CategoryCode;
    @ApiModelProperty("一级渠道类型编码")
    private String lv1ChannelCode;
    @ApiModelProperty("二级渠道类型编码")
    private String lv2ChannelCode;
    @ApiModelProperty("时间观测点")
    private Date viewTime;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("分组字段")
    private String groupColumn;
    @ApiModelProperty("算法版本")
    private String algoNameAndVersion;
    @ApiModelProperty("预测版本")
    private String predictionVersion;
    @ApiModelProperty("需求计划版本Code")
    private String demandPlanCode;
    @ApiModelProperty("时间粒度")
    private String periodType;

}
