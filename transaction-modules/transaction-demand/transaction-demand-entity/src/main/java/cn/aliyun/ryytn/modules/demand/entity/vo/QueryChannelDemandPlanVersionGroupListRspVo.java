package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求计划详情分组聚合列表响应
 * <AUTHOR>
 * @date 2023/11/8 12:51
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求计划详情分组聚合列表响应")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class QueryChannelDemandPlanVersionGroupListRspVo implements Serializable
{
    private static final long serialVersionUID = 6787027536071624232L;
    @ApiModelProperty("需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty("子计划清单组编号")
    private Long groupId;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道名称")
    private String lv1ChannelName;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道名称")
    private String lv2ChannelName;

    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    @ApiModelProperty("三级渠道名称")
    private String lv3ChannelName;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    @ApiModelProperty("版本编号")
    private String versionId;

    @ApiModelProperty("版本时间")
    private String versionDate;

    @ApiModelProperty("客户code")
    private String resellerCode;

    @ApiModelProperty("客户名称")
    private String resellerName;

    @ApiModelProperty("生命周期编号")
    private String lifecycleCode;

    @ApiModelProperty("生命周期名称")
    private String lifecycleName;

    @ApiModelProperty("算法版本")
    private String algoVersion;

    @ApiModelProperty("计划日期")
    private String planDate;

    @ApiModelProperty("计划数")
    private Double planValue;

    @ApiModelProperty("备注")
    private String planRemark;

    @ApiModelProperty("参考类型")
    private String refType;

    @ApiModelProperty("参考版本")
    private String refVersion;

    @ApiModelProperty("标签")
    private String label;

    @ApiModelProperty("状态，-1:待试算;未提交:0，已提交:1")
    private Integer status;

    @ApiModelProperty("是否调整")
    private Boolean isModify;

    @ApiModelProperty("是否删除")
    private Boolean deleted;

    @ApiModelProperty("动态时间字段json数据")
    private String data;

    @ApiModelProperty("动态数据映射")
    private Map<String, PlanValue> dataMap = new HashMap<String, PlanValue>();

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        QueryChannelDemandPlanVersionGroupListRspVo object = (QueryChannelDemandPlanVersionGroupListRspVo) o;
        return Objects.equals(this.skuCode, object.skuCode)
            && Objects.equals(this.lv1CategoryCode, object.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, object.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, object.lv3CategoryCode)
            && Objects.equals(this.lv1ChannelCode, object.lv1ChannelCode)
            && Objects.equals(this.lv2ChannelCode, object.lv2ChannelCode)
            && Objects.equals(this.lv3ChannelCode, object.lv3ChannelCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        result = prime * result + ((lv1ChannelCode == null) ? 0 : lv1ChannelCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        return result;
    }
}
