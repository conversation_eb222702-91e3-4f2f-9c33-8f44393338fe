package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.alibaba.fastjson.annotation.JSONField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询产品历史出库响应
 * <AUTHOR>
 * @date 2023/11/21 19:47
 */
@Setter
@Getter
@ToString
@ApiModel("查询产品历史出库响应")
public class QuerySkuDeliveryListRspVo implements Serializable
{
    private static final long serialVersionUID = -4229333837890214843L;
    @ApiModelProperty("产品编号")
    @JSONField(name = "skuCode")
    private String skuCode;

    @ApiModelProperty("一级渠道编号")
    @JSONField(name = "lv1ChannelCode")
    private String lv1ChannelCode;

    @ApiModelProperty("二级渠道编号")
    @JSONField(name = "lv2ChannelCode")
    private String lv2ChannelCode;

    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    @ApiModelProperty("一级品类编号")
    @JSONField(name = "lv1CategoryCode")
    private String lv1CategoryCode;

    @ApiModelProperty("二级品类编号")
    @JSONField(name = "lv2CategoryCode")
    private String lv2CategoryCode;

    @ApiModelProperty("三级品类编号")
    @JSONField(name = "lv3CategoryCode")
    private String lv3CategoryCode;

    @ApiModelProperty("指定时间，yyyy-MM-dd")
    @JSONField(name = "bizDateValue")
    private String bizDateValue;

    @ApiModelProperty("出库数量")
    @JSONField(name = "outboundNum")
    private Double outboundNum;

    @ApiModelProperty("动态字段")
    private Map<String, Double> dataMap = new HashMap<String, Double>();

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        QuerySkuDeliveryListRspVo querySkuDeliveryListRspVo = (QuerySkuDeliveryListRspVo) o;
        return Objects.equals(this.getLv1CategoryCode(), querySkuDeliveryListRspVo.getLv1CategoryCode())
            && Objects.equals(this.getLv2CategoryCode(), querySkuDeliveryListRspVo.getLv2CategoryCode())
            && Objects.equals(this.getLv3CategoryCode(), querySkuDeliveryListRspVo.getLv3CategoryCode())
            && Objects.equals(this.getLv1ChannelCode(), querySkuDeliveryListRspVo.getLv1ChannelCode())
            && Objects.equals(this.getLv2ChannelCode(), querySkuDeliveryListRspVo.getLv2ChannelCode())
            && Objects.equals(this.getLv3ChannelCode(), querySkuDeliveryListRspVo.getLv3ChannelCode());
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.getLv1CategoryCode() == null) ? 0 : this.getLv1CategoryCode().hashCode());
        result = prime * result + ((this.getLv2CategoryCode() == null) ? 0 : this.getLv2CategoryCode().hashCode());
        result = prime * result + ((this.getLv3CategoryCode() == null) ? 0 : this.getLv3CategoryCode().hashCode());
        result = prime * result + ((this.getLv1ChannelCode() == null) ? 0 : this.getLv1ChannelCode().hashCode());
        result = prime * result + ((this.getLv2ChannelCode() == null) ? 0 : this.getLv2ChannelCode().hashCode());
        result = prime * result + ((this.getLv3ChannelCode() == null) ? 0 : this.getLv3ChannelCode().hashCode());
        return result;
    }
}
