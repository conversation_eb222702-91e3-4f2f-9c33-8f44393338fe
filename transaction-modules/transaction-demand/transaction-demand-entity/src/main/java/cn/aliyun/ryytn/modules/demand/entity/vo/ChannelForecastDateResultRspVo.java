package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道详情周期起始日期Vo
 * <AUTHOR>
 * @date 2023/11/13 16:58
 */
@Setter
@Getter
@ToString
@ApiModel("渠道详情周期起始日期Vo")
public class ChannelForecastDateResultRspVo implements Serializable
{
    private static final long serialVersionUID = -4213306840592495062L;
    private String startDate;

    private String endDate;
}
