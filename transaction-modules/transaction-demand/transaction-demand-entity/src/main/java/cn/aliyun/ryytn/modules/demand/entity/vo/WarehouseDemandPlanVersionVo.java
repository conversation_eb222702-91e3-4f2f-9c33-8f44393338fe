package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分仓需求计划版本Vo
 * <AUTHOR>
 * @date 2023/11/15 14:41
 */
@Setter
@Getter
@ToString
@ApiModel("分仓需求计划版本")
public class WarehouseDemandPlanVersionVo implements Serializable
{
    private static final long serialVersionUID = 6556342558044059903L;
    @ApiModelProperty("需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty("版本编号")
    private String versionId;

    @ApiModelProperty("版本周期")
    private String versionRange;

    @ApiModelProperty("需求计划标签")
    private String label;

    @ApiModelProperty("状态，-1:待试算;未提交:0，已提交:1")
    private Integer status;

    @ApiModelProperty("创建时间")
    private String gmtCreate;

    @ApiModelProperty("数量")
    @JsonIgnore
    private Integer num;

    @ApiModelProperty("开始时间")
    @JsonIgnore
    private String beginDate;

    @ApiModelProperty("结束时间")
    @JsonIgnore
    private String endDate;
}
