package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 数据时间标签
 * <AUTHOR>
 * @date 2023/12/9 15:58
 */
@Setter
@Getter
@ToString
@ApiModel("数据时间标签")
public class DateLabelVo implements Serializable
{
    private static final long serialVersionUID = -6259304281375073153L;
    @ApiModelProperty("时间标签")
    private String bizDateLabel;

    @ApiModelProperty("时间值")
    private String bizDateValue;
}
