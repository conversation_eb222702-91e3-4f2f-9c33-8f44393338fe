package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道详情图表
 * <AUTHOR>
 * @date 2023/11/8 14:17
 */
@Setter
@Getter
@ToString
@ApiModel("渠道详情图表")
public class ChannelDetailChart implements Serializable
{
    private static final long serialVersionUID = 6132133848821509982L;

    @ApiModelProperty("日期")
    private String targetBizDate;

    @ApiModelProperty("实际出库数量")
    private String obserValue;

    @ApiModelProperty("历史同期数量")
    private String oldObserValue;

    @ApiModelProperty("预测结果数量")
    private String predictionResult;

    @ApiModelProperty("销售目标数量")
    private String planOrderNum;

    @ApiModelProperty("需求提报数量")
    private String reportingOrderNum;
}
