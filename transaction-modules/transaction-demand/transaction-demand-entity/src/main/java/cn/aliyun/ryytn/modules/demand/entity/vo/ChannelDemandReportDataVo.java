package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道需求提报数据值
 * <AUTHOR>
 * @date 2023/10/31 16:53
 */
@Setter
@Getter
@ToString
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ChannelDemandReportDataVo implements Serializable
{
    private static final long serialVersionUID = 3592664355666643305L;
    /**
     * 编号
     */
    private String id;

    /**
     * 时间戳，周粒度为周第一天日期，格式：yyyyMMdd
     */
    private String bizDateValue;

    /**
     * 数值
     */
    private Double orderNum;

    /**
     * 上一版本同期数值
     */
    private Double lastOrderNum;

    /**
     * 偏差率
     */
    private Double deviationRadio;

    /**
     * 备注
     */
    private String remark;

    @ApiModelProperty("是否锁定期")
    private Boolean lockFlag;


    /**
     * 实际发生的订单数值
     */
    private Double realityOrderNum;
}
