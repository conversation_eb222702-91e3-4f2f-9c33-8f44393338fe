package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道需求计划版本标签
 * <AUTHOR>
 * @date 2023/11/17 14:44
 */
@Setter
@Getter
@ToString
@ApiModel("渠道需求计划版本标签")
public class ChannelDemandPlanVersionLabelVo implements Serializable
{
    private static final long serialVersionUID = -3860696523517708879L;
    @ApiModelProperty("计划编号")
    private String demandPlanCode;

    @ApiModelProperty("版本号")
    private String versionId;

    @ApiModelProperty("计划主体")
    private SubjectTypeEnum subjectType;

    @ApiModelProperty("标签")
    private String label;
}
