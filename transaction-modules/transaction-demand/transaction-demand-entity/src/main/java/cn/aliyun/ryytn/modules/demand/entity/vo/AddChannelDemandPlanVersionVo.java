package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 新增渠道需求计划版本请求
 * <AUTHOR>
 * @date 2023/12/4 19:25
 */
@Setter
@Getter
@ToString
@ApiModel("新增渠道需求计划版本请求")
public class AddChannelDemandPlanVersionVo implements Serializable
{
    private static final long serialVersionUID = -3293376457517242581L;
    @ApiModelProperty("计划主体类型，渠道/分仓")
    private SubjectTypeEnum subjectType;

    @ApiModelProperty("渠道需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty("生成版本编号")
    private String versionId;

    @ApiModelProperty("上一版本编号")
    private String lastVersionId;

    @ApiModelProperty("时间集合，月粒度：月首日日期，周粒度：周首日日期")
    private List<String> planDates;

    @ApiModelProperty("产品锁定期列表")
    private List<SkuLockVo> lockList;

    @ApiModelProperty("计划主体/对象分组范围")
    private List<DemandPlanConfigSkuVo> groupPlanList;
}
