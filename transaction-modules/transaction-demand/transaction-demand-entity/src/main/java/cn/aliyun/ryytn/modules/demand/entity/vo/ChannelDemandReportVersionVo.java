package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询需求提报版本列表请求Vo
 * <AUTHOR>
 * @date 2023/10/24 9:57
 */
@Setter
@Getter
@ToString
@ApiModel("查询需求提报版本列表请求Vo")
public class ChannelDemandReportVersionVo implements Serializable
{
    private static final long serialVersionUID = -7398986445771385130L;
    @ApiModelProperty("时间粒度")
    private BizDateTypeEnum bizDateType;

    @ApiModelProperty("版本")
    private String rollingVersion;

    @ApiModelProperty("是否锁定，0：未锁定，1：已锁定")
    private Integer isLocked;

    @ApiModelProperty("是否默认展示")
    private Boolean defaultFlag;
}
