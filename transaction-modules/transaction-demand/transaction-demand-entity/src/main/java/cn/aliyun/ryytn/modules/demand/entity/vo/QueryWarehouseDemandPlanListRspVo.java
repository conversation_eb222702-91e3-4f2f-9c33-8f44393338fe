package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.modules.demand.constant.PeriodTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanPeriodEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询分仓需求计划列表响应
 * <AUTHOR>
 * @date 2023/11/15 11:07
 */
@Setter
@Getter
@ToString
@ApiModel("查询分仓需求计划列表响应")
public class QueryWarehouseDemandPlanListRspVo implements Serializable
{
    private static final long serialVersionUID = 6706464376132765048L;
    @ApiModelProperty("编号")
    private String id;

    @ApiModelProperty("销售年度")
    private String marketingYear;

    @ApiModelProperty("销售计划Code")
    private String demandPlanCode;

    @ApiModelProperty("销售计划名称")
    private String demandPlanName;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("周期粒度，月：MONTH，周：WEEK，天：DAY")
    private PeriodTypeEnum periodType;

    @ApiModelProperty("计划开始时间，yyyy-MM-dd，时间粒度为月时：该月第一天日期，时间粒度为周时：该月该周第一天日期")
    private String startDate;

    @ApiModelProperty("计划开始周数，时间粒度为周时有效")
    private Integer startWeek;

    @ApiModelProperty("预测期数")
    private Integer periods;

    @ApiModelProperty("滚动频率：每月:PER_MONTH;每两月：TWO_MONTH;每季度:PER_QUARTER;每半年:SIX_MONTH")
    private PlanPeriodEnum planPeriod;

    @ApiModelProperty("执行日期数")
    private Integer executionDateNum;

    @ApiModelProperty("主体类型:factory:工厂,warehouse:仓库,order:分销渠道，all:汇总")
    private SubjectTypeEnum subjectType;

    @ApiModelProperty("主体维度类型")
    private SubjectDimensionTypeEnum subjectDimensionType;

    @ApiModelProperty("计划维度类型 BRAND：品牌；SKU:SKU; SPU:SPU")
    private PlanTypeEnum planType;

    @ApiModelProperty("计划维度类型")
    private PlanDimensionTypeEnum planDimensionType;

    @ApiModelProperty("是否拆分计划主体 true或false")
    private Boolean isSplitSubject;

    @ApiModelProperty("是否拆分计划对象 true或false")
    private Boolean isSplitObject;

    @ApiModelProperty("状态: 0 已发布 -1 已下线")
    private Integer status;

    @ApiModelProperty("是否已删除")
    private Boolean deleted;

    @ApiModelProperty("创建时间")
    private String gmtCreate;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("修改时间")
    private String gmtModify;

    @ApiModelProperty("修改人")
    private String lastModifier;
}
