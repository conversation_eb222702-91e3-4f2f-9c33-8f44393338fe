package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询产品历史出库请求
 * <AUTHOR>
 * @date 2023/11/21 19:47
 */
@Setter
@Getter
@ToString
@ApiModel("查询产品历史出库请求")
public class QuerySkuDeliveryListReqVo implements Serializable
{
    private static final long serialVersionUID = -2641067278985560565L;
    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("时间类型，MONTH:月,WEEK:周")
    private BizDateTypeEnum bizDateType;

    @ApiModelProperty("指定时间，yyyy-MM-dd")
    private String bizDateValue;

    @ApiModelProperty("开始时间，yyyy-MM-dd")
    private String startDate;

    @ApiModelProperty("结束时间,yyyy-MM-dd")
    private String endDate;
}
