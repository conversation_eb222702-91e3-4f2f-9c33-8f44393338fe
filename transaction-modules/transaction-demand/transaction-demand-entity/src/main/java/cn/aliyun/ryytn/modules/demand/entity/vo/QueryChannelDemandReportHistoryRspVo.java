package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求提报修改历史记录响应
 * <AUTHOR>
 * @date 2023/11/2 14:34
 */
@Setter
@Getter
@ToString
public class QueryChannelDemandReportHistoryRspVo implements Serializable
{
    private static final long serialVersionUID = -6841861746788370899L;
    /**
     * 时间粒度
     */
    private String bizDateValue;

    /**
     * 修改前内容
     */
    private Double oldOrderNum;

    /**
     * 修改后内容
     */
    private Double newOrderNum;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private String updatedTime;

    /**
     * 备注
     */
    private String remark;
}
