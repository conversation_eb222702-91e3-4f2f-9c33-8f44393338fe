package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 商品响应Vo
 * <AUTHOR>
 * @date 2023/10/30 10:09
 */
@Setter
@Getter
@ToString
@ApiModel("查询产品详情")
public class QuerySkuRspVo implements Serializable
{
    private static final long serialVersionUID = 8677028829128908232L;

    private String skuCode;

    private String skuName;

    private String lv1CategoryName;

    private String lv1CategoryCode;

    private String lv2CategoryName;

    private String lv2CategoryCode;

    private String lv3CategoryName;

    private String lv3CategoryCode;
}
