package cn.aliyun.ryytn.modules.demand.entity.vo;

import cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivitySkuDetailDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.List;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024-08-25 17:18
 * @Description
 */
@Setter
@Getter
@ToString
@ApiModel("活动产品列表")
public class MarketActivitySkuVo implements Serializable {

    private static final long serialVersionUID = 3472930054664382470L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("促销活动唯一Code")
    private String actiCode;

    @ApiModelProperty("促销活动名称")
    private String actiName;

    @ApiModelProperty("产品Code")
    private String skuCode;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("活动数量上限")
    private String actiNumsLimit;

    @ApiModelProperty("计划商品类型")
    private String actiSkuObj;

    @ApiModelProperty("删除状态")
    private Integer deleted;

    @ApiModelProperty("计划开始时间")
    private String actiSkuBeginTime;

    @ApiModelProperty("计划出货开始时间")
    private String actiSkuDeliveryBeginTime;

    @ApiModelProperty("计划活动出库量")
    private String actiSkuDeliveryNumsLimit;

    @ApiModelProperty("计划出货开始时间周一")
    private String actiSkuWeekBeginTime;

    private List<MarketActivitySkuDetailDO> skuDetailList;
}
