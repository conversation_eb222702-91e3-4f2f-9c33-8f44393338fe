package cn.aliyun.ryytn.modules.demand.constant;

/**
 * @Description 排序字段枚举
 * <AUTHOR>
 * @date 2023/12/23 12:41
 */
public enum SortColumnEnum
{
    lv2Category("lv2_category_code"),
    lv1Category("lv1_category_code"),
    lv3Category("lv3_category_code"),
    sku("sku_code"),
    lv1Channel("lv1_channel_code"),
    lv2Channel("lv2_channel_code"),
    lv3Channel("lv3_channel_code"),
    receiverType("receiver_type"),
    warehouse("warehouse_code"),
    item("item_id"),
    startPoint("start_point_id"),
    endPoint("end_point_id"),
    startPhysicalPoint("start_physical_point_id"),
    endPhysicalPoint("end_physical_point_id"),
    shippingType("shipping_type_group_id"),
    validRule("validity_period"),
    productionDate("production_date"),
    distributeType("distribute_type"),
    departureDate("departure_date"),
    departureValid("departure_valid_name"),
    arrivalDate("arrival_date"),
    arrivalValid("arrival_valid_name"),
    stockPoint("stock_point_id"),
    physicalStockPoint("physical_point_id"),
    validName("valid_name"),
    date("date"),
    planDataType("plan_data_type"),
    validityPeriod("validity_period");

    private final String columnName;

    SortColumnEnum(String columnName)
    {
        this.columnName = columnName;
    }

    public String getColumnName()
    {
        return this.columnName;
    }
}
