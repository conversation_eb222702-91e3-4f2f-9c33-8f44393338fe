package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道需求提报列表响应
 * <AUTHOR>
 * @date 2023/10/26 10:11
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道需求提报列表响应")
public class QueryChannelDemandReportListRspVo implements Serializable
{
    private static final long serialVersionUID = -6760001404188006375L;
    @ApiModelProperty("提报数据编号")
    @ExcelIgnore
    private String id;
    /**
     * 导出表格行号
     */
    @ExcelProperty(value = {"#"}, index = 0)
    private Integer rowId;
    /**
     * 产品编号
     */
    @ApiModelProperty("产品编号")
    @ExcelProperty(value = {"产品编码"}, index = 7)
    private String skuCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    @ExcelProperty(value = {"产品简称"}, index = 8)
    private String skuName;

    /**
     * 一级渠道编号
     */
    @ApiModelProperty("一级渠道编号")
    @ExcelIgnore
    private String lv1ChannelCode;

    /**
     * 一级渠道名称
     */
    @ApiModelProperty("一级渠道名称")
    @ExcelProperty(value = {"一级渠道"}, index = 1)
    private String lv1ChannelName;

    /**
     * 二级渠道编号
     */
    @ApiModelProperty("二级渠道编号")
    @ExcelIgnore
    private String lv2ChannelCode;

    /**
     * 二级渠道名称
     */
    @ApiModelProperty("二级渠道名称")
    @ExcelProperty(value = {"二级渠道"}, index = 2)
    private String lv2ChannelName;

    /**
     * 三级渠道编号
     */
    @ApiModelProperty("三级渠道编号")
    @ExcelIgnore
    private String lv3ChannelCode;

    /**
     * 三级渠道名称
     */
    @ApiModelProperty("三级渠道名称")
    @ExcelProperty(value = {"三级渠道"}, index = 3)
    private String lv3ChannelName;

    /**
     * 一级品类编号
     */
    @ApiModelProperty("一级品类编号")
    @ExcelIgnore
    private String lv1CategoryCode;

    /**
     * 一级品类名称
     */
    @ApiModelProperty("一级品类名称")
    @ExcelProperty(value = {"产品分类"}, index = 4)
    private String lv1CategoryName;

    /**
     * 二级品类编号
     */
    @ApiModelProperty("二级品类编号")
    @ExcelIgnore
    private String lv2CategoryCode;

    /**
     * 二级品类名称
     */
    @ApiModelProperty("二级品类名称")
    @ExcelProperty(value = {"产品大类"}, index = 5)
    private String lv2CategoryName;

    /**
     * 三级品类编号
     */
    @ApiModelProperty("三级品类编号")
    @ExcelIgnore
    private String lv3CategoryCode;

    /**
     * 三级品类名称
     */
    @ApiModelProperty("三级品类名称")
    @ExcelProperty(value = {"产品小类"}, index = 6)
    private String lv3CategoryName;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @ExcelProperty(value = {"数量"}, index = 12)
    private Double orderNum;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    @ExcelIgnore
    private String unit;

    @ApiModelProperty("动态数据列表")
    @ExcelIgnore
    private List<ChannelDemandReportDataVo> dataList;

    @ApiModelProperty("动态数据json字符串")
    @ExcelIgnore
    private String data;

    /**
     * 动态字段数据映射，KEY：响应结构外部headArray的元素，VALUE：动态数据对象
     */
    @ApiModelProperty("动态数据映射")
    @ExcelIgnore
    private Map<String, ChannelDemandReportDataVo> dataMap = new HashMap<String, ChannelDemandReportDataVo>();

    /**
     * 总数
     */
    @ApiModelProperty("总数")
    @ExcelIgnore
    private Double total;

    /**
     * 货品名称（阿里产品没有此字段，阿里与产品确认，与skuName产品名称一致
     */
    @ExcelProperty(value = {"货品名称"}, index = 9)
    private String waresName;

    /**
     * 出货周
     */
    @ExcelProperty(value = {"出货周"}, index = 10)
    private String week;

    /**
     * 出货月
     */
    @ExcelProperty(value = {"出货月"}, index = 11)
    private String month;

    /**
     * 时间粒度值
     */
    @ApiModelProperty("时间粒度值")
    @ExcelIgnore
    private String bizDateValue;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private String gmtCreate;

    /**
     * 修改时间
     */
    @ExcelIgnore
    private String gmtModify;

    /**
     * 修改人
     */
    @ExcelIgnore
    private String lastModifier;

    /**
     * 扩展字段
     */
    @ExcelIgnore
    private String extend;

    /**
     * 表头
     */
    @ExcelIgnore
    private String head;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        QueryChannelDemandReportListRspVo queryChannelDemandReportListRspVo = (QueryChannelDemandReportListRspVo) o;
        return Objects.equals(this.skuCode, queryChannelDemandReportListRspVo.skuCode)
            && Objects.equals(this.lv1CategoryCode, queryChannelDemandReportListRspVo.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, queryChannelDemandReportListRspVo.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, queryChannelDemandReportListRspVo.lv3CategoryCode)
            && Objects.equals(this.lv1ChannelCode, queryChannelDemandReportListRspVo.lv1ChannelCode)
            && Objects.equals(this.lv2ChannelCode, queryChannelDemandReportListRspVo.lv2ChannelCode)
            && Objects.equals(this.lv3ChannelCode, queryChannelDemandReportListRspVo.lv3ChannelCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        result = prime * result + ((lv1ChannelCode == null) ? 0 : lv1ChannelCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        return result;
    }

    public List<ChannelDemandReportDataVo> getDataList()
    {
        if (CollectionUtils.isNotEmpty(this.dataList))
        {
            return this.dataList;
        }

        if (StringUtils.isBlank(this.data))
        {
            return null;
        }
        return JSON.parseArray(this.data, ChannelDemandReportDataVo.class);
    }
}
