package cn.aliyun.ryytn.modules.demand.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 渠道需求提报
 * @date 2024/1/22 9:48
 */
@Setter
@Getter
@ToString
@ApiModel("渠道需求提报")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelDemandReportDto implements Serializable {
    private static final long serialVersionUID = -8438170626368581874L;
    @ApiModelProperty("编号")
    private Long id;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("修改人")
    private String lastModifier;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("修改时间")
    private Date gmtModify;

    @ApiModelProperty("时间粒度")
    private String bizDateType;

    @ApiModelProperty("时间，yyyyMMdd")
    private String bizDateValue;

    @ApiModelProperty("版本号")
    private String rollingVersion;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道名称")
    private String lv1ChannelName;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道名称")
    private String lv2ChannelName;

    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    @ApiModelProperty("三级渠道名称")
    private String lv3ChannelName;

    @ApiModelProperty("提报数量")
    private Double orderNum;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("扩展字段")
    private String extend;

    @ApiModelProperty("是否已修改")
    private Integer isModify;

    @ApiModelProperty("是否锁定")
    private Integer isLocked;

    @ApiModelProperty("上一版本提报数量")
    private Double previousOrderNum;

    @ApiModelProperty("偏差率")
    private Double deviationScore;

    @ApiModelProperty("周期中的值，20250201,20250201_pre,其中20250201保存当前周期值，20250201_pre保存上个周期的值")
    private Map<String,Object> values = new HashMap<>();


    /**
     * 重写偏差率获取方法
     *
     * @return {@link Double}
     * <AUTHOR>
     * @date 2024-01-22 15:18
     * @description 重写偏差率获取方法
     */
    public Double getDeviationScore() {
        if (Objects.isNull(this.deviationScore)) {
            Double previousOrderNumTmp = Optional.ofNullable(this.previousOrderNum).orElse(0D);
            if (previousOrderNumTmp.equals(0D)) {
                this.deviationScore = 0D;
            } else {
                Double orderNumTmp = Optional.ofNullable(orderNum).orElse(0D);
                this.deviationScore = (orderNumTmp - previousOrderNumTmp) / previousOrderNumTmp;
            }
        }
        return deviationScore;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ChannelDemandReportDto that = (ChannelDemandReportDto) o;

        return new EqualsBuilder().append(bizDateType, that.bizDateType)
                .append(rollingVersion, that.rollingVersion)
                .append(skuCode, that.skuCode)
                .append(lv1CategoryCode, that.lv1CategoryCode)
                .append(lv2CategoryCode, that.lv2CategoryCode)
                .append(lv3CategoryCode, that.lv3CategoryCode)
                .append(lv1ChannelCode, that.lv1ChannelCode)
                .append(lv2ChannelCode, that.lv2ChannelCode)
                .append(lv3ChannelCode, that.lv3ChannelCode).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(bizDateType).append(rollingVersion).append(skuCode).append(lv1CategoryCode).append(lv2CategoryCode).append(lv3CategoryCode).append(lv1ChannelCode).append(lv2ChannelCode).append(lv3ChannelCode).toHashCode();
    }
}
