package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询预测算法渠道_分仓预测算法列表请求Vo
 * <AUTHOR>
 * @date 2023/10/23 17:32
 */
@Setter
@Getter
@ToString
@ApiModel("查询预测算法渠道_分仓预测算法列表请求参数")
public class QueryForecastAlgorithmListReqVo implements Serializable
{
    private static final long serialVersionUID = -2366157838546142980L;

    @ApiModelProperty("预测场景类型 传 分销渠道订单预测 或者 分仓需求预测 不传则全查出来")
    private String scene;
}
