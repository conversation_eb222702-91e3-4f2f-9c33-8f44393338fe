package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道预测结果dataq响应Vo
 * <AUTHOR>
 * @date 2023/11/8 11:19
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道预测结果dataq响应Vo")
public class QueryChannelForecastResultDetaildataqResultVo implements Serializable
{
    private static final long serialVersionUID = -2643089764365100096L;

    private String lv1categorycode;

    private String lv1categoryname;

    private String lv2categorycode;

    private String lv2categoryname;

    private String lv3categorycode;

    private String lv3categoryname;

    private String lv1channelcode;

    private String lv1channelname;

    private String lv2channelcode;

    private String lv2channelname;

    private String observalue;

    private String oldobservalue;

    private String predictionresult;

    private String targetbizdate;

    private String periodtype;

    private String dataunit;

    private String planordernum;

    private String reportingordernum;
}
