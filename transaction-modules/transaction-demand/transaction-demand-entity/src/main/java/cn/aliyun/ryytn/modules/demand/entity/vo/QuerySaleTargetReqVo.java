package cn.aliyun.ryytn.modules.demand.entity.vo;

import java.io.Serializable;
import java.util.List;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询销售目标列表接口请求Vo
 * <AUTHOR>
 * @date 2023/10/23 11:21
 */
@Setter
@Getter
@ToString
@ApiModel("查询销售目标列表接口请求Vo")
public class QuerySaleTargetReqVo implements Serializable
{
    private static final long serialVersionUID = 7564938511667962601L;
    @ApiModelProperty("时间类型:DAY,WEEK，MONTH,YEAR,QUARTER")
    private BizDateTypeEnum bizDateType;

    @ApiModelProperty("财年")
    private String fsclYear;

    @ApiModelProperty("产品分类一级编码 逗号分隔")
    private String lv1CategoryCodes;

    @ApiModelProperty("产品分类一级编码")
    private String lv1CategoryCode;

    @ApiModelProperty("产品分类二级编码 逗号分隔")
    private String lv2CategoryCodes;

    @ApiModelProperty("产品分类二级编码")
    private String lv2CategoryCode;

    @ApiModelProperty("产品分类三级级编码 逗号分隔")
    private String lv3CategoryCodes;

    @ApiModelProperty("产品分类三级级编码")
    private String lv3CategoryCode;

    @ApiModelProperty("商品code 逗号分隔")
    private String skuCodes;

    @ApiModelProperty("商品code")
    private String skuCode;

    @ApiModelProperty("一级渠道类型编码 逗号分隔")
    private String lv1ChannelCodes;

    @ApiModelProperty("一级渠道类型编码")
    private String lv1ChannelCode;

    @ApiModelProperty("二级渠道类型编码 逗号分隔")
    private String lv2ChannelCodes;

    @ApiModelProperty("二级渠道类型编码")
    private String lv2ChannelCode;

    @ApiModelProperty("三级渠道类型编码 逗号分隔")
    private String lv3ChannelCodes;

    @ApiModelProperty("三级渠道类型编码")
    private String lv3ChannelCode;

    /**
     * 字段信息，通过dataq接口动态获取，前端不需要传
     */
    @ApiModelProperty("字段信息，通过dataq接口动态获取，前端不需要传")
    private String selectColumn;

    @ApiModelProperty("开始月，yyyy-MM，支持跨年查询")
    private String startDate;

    @ApiModelProperty("结束月，yyyy-MM，支持跨年查询")
    private String endDate;

    @ApiModelProperty("分组列枚举列表")
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("分组列")
    private String groupColumn;

    @ApiModelProperty("排序列")
    private String sortColumn;

    @ApiModelProperty("业务数据标识列表")
    private List<QuerySaleTargetRspVo> keyList;
}
