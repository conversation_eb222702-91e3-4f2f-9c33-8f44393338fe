package cn.aliyun.ryytn.modules.demand.entity.dto;

import java.io.Serializable;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 历史订单比例
 * <AUTHOR>
 * @date 2024/1/18 17:59
 */
@Setter
@Getter
@ToString
@ApiModel("历史订单比例")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DeliveryOrderRateDto implements Serializable
{
    private static final long serialVersionUID = -1180991704438984101L;
    private String dimComb;

    @ApiModelProperty("时间粒度")
    private String bizDateType;

    @ApiModelProperty("时间")
    private String bizDateValue;

    @ApiModelProperty("一级品类编号")
    private String lv1CategoryCode;

    @ApiModelProperty("一级品类名称")
    private String lv1CategoryName;

    @ApiModelProperty("二级品类编号")
    private String lv2CategoryCode;

    @ApiModelProperty("二级品类名称")
    private String lv2CategoryName;

    @ApiModelProperty("三级品类编号")
    private String lv3CategoryCode;

    @ApiModelProperty("三级品类名称")
    private String lv3CategoryName;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("一级渠道编号")
    private String lv1ChannelCode;

    @ApiModelProperty("一级渠道名称")
    private String lv1ChannelName;

    @ApiModelProperty("二级渠道编号")
    private String lv2ChannelCode;

    @ApiModelProperty("二级渠道名称")
    private String lv2ChannelName;

    @ApiModelProperty("三级渠道编号")
    private String lv3ChannelCode;

    @ApiModelProperty("三级渠道名称")
    private String lv3ChannelName;

    @ApiModelProperty("")
    private String resellerCode;

    @ApiModelProperty("")
    private String resellerName;

    @ApiModelProperty("渠道类型")
    private String channelType;

    @ApiModelProperty("数量")
    private Double outboundNum;

    @ApiModelProperty("数量")
    private Double outboundNumSum;

    @ApiModelProperty("比例")
    private Double outboundRate;

    @ApiModelProperty("时间")
    private String ds;

    @ApiModelProperty("一级品类编号，英文逗号分隔")
    private String lv1CategoryCodes;

    @ApiModelProperty("二级品类编号，英文逗号分隔")
    private String lv2CategoryCodes;

    @ApiModelProperty("三级品类编号，英文逗号分隔")
    private String lv3CategoryCodes;

    @ApiModelProperty("产品编号，英文逗号分隔")
    private String skuCodes;

    @ApiModelProperty("仓库编号，英文逗号分隔")
    private String warehouseCodes;

    @ApiModelProperty("开始时间")
    private String beginDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        DeliveryOrderRateDto object = (DeliveryOrderRateDto) o;
        return Objects.equals(this.skuCode, object.skuCode)
            && Objects.equals(this.lv1CategoryCode, object.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, object.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, object.lv3CategoryCode)
            && Objects.equals(this.channelType, object.channelType)
            && Objects.equals(this.bizDateValue, object.bizDateValue)
            && Objects.equals(this.warehouseCode, object.warehouseCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        result = prime * result + ((channelType == null) ? 0 : channelType.hashCode());
        result = prime * result + ((bizDateValue == null) ? 0 : bizDateValue.hashCode());
        result = prime * result + ((warehouseCode == null) ? 0 : warehouseCode.hashCode());
        return result;
    }

    /**
     *
     * @Description 历史订单比例聚合粒度枚举
     * <AUTHOR>
     * @date 2024/4/7 16:46
     */
    public enum DIM_COMB
    {
        // 产品+渠道+渠道类型+仓库
        SKU_CHANNEL_TOBC_WAREHOUSE("SKU_CODE+LV3_CHANNEL_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK"),
        // 产品+渠道类型+仓库
        SKU_TOBC_WAREHOUSE("SKU_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK"),
        // 产品+渠道类型
        SKU_TOBC("SKU_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+CHANNEL_TYPE+16WEEK");

        private final String value;

        DIM_COMB(String value)
        {
            this.value = value;
        }

        public String getValue()
        {
            return this.value;
        }
    }
}
