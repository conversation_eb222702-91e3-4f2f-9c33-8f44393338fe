package cn.aliyun.ryytn.modules.demand.api;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanReceiverDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandPlanMarkDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanConfigReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanVersionLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ConfirmChannelDemandPlanSubPlanGroupVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DateLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DeleteDemandPlanReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.WarehouseDemandPlanDataParamVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.WarehouseDemandPlanVersionVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.WarehouseDemandSubPlanGroupVo;
import cn.aliyun.ryytn.modules.system.entity.dto.ProductCategoryDto;

/**
 * @Description 分仓需求计划接口
 * <AUTHOR>
 * @date 2023/11/27 16:02
 */
public interface WarehouseDemandPlanService
{
    /**
     *
     * @Description 新增分仓需求计划
     * @param addDemandPlanListReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 16:35
     */
    void addWarehouseDemandPlan(AddDemandPlanListReqVo addDemandPlanListReqVo) throws Exception;

    /**
     *
     * @Description 查询分仓需求计划列表
     * @param queryWarehouseDemandPlanListReqVo
     * @return List<QueryWarehouseDemandPlanListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 17:16
     */
    List<QueryWarehouseDemandPlanListRspVo> queryWarehouseDemandPlanList(QueryWarehouseDemandPlanListReqVo queryWarehouseDemandPlanListReqVo)
        throws Exception;

    /**
     *
     * @Description 分页查询分仓需求计划版本列表
     * @param condition
     * @return PageInfo<WarehouseDemandPlanVersionVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 17:21
     */
    PageInfo<WarehouseDemandPlanVersionVo> queryWarehouseDemandPlanVersionPage(PageCondition<String> condition) throws Exception;

    /**
     *
     * @Description 查询分仓需求计划版本列表
     * @param warehouseDemandPlanVersionVo
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 17:21
     */
    List<String> queryWarehouseDemandPlanVersionList(WarehouseDemandPlanVersionVo warehouseDemandPlanVersionVo) throws Exception;

    /**
     *
     * @Description 设置分仓需求计划版本标签
     * @param channelDemandPlanVersionLabelVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:55
     */
    void setWarehouseDemandPlanVersionLabel(ChannelDemandPlanVersionLabelVo channelDemandPlanVersionLabelVo) throws Exception;

    /**
     *
     * @Description 查询分仓需求子计划清单组列表
     * @param warehouseDemandSubPlanGroupVo
     * @return List<WarehouseDemandSubPlanGroupVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 15:29
     */
    List<WarehouseDemandSubPlanGroupVo> queryWarehouseDemandSubPlanGroupList(WarehouseDemandSubPlanGroupVo warehouseDemandSubPlanGroupVo) throws Exception;

    /**
     *
     * @Description 确认分仓需求计划子计划清单组
     * @param confirmChannelDemandPlanSubPlanGroupVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 16:26
     */
    void confirmWarehouseDemandPlanSubPlanGroup(ConfirmChannelDemandPlanSubPlanGroupVo confirmChannelDemandPlanSubPlanGroupVo) throws Exception;

    /**
     *
     * @Description 查询分仓需求计划品类树
     * @param warehouseDemandSubPlanGroupVo
     * @return List<ProductCategoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月02日 15:34
     */
    Set<ProductCategoryDto> queryWarehouseDemandPlanCategoryTree(WarehouseDemandSubPlanGroupVo warehouseDemandSubPlanGroupVo)
        throws Exception;

    /**
     *
     * @Description 查询分仓需求计划编辑页面日期页签
     * @param warehouseDemandSubPlanGroupVo
     * @return List<WarehouseDemandSubPlanGroupVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月13日 15:53
     */
    List<DateLabelVo> queryWarehouseDemandPlanDateLabelList(WarehouseDemandSubPlanGroupVo warehouseDemandSubPlanGroupVo) throws Exception;

    /**
     *
     * @Description 查询分仓需求计划版本数据列表
     * @param warehouseDemandPlanDataParamVo
     * @return BaseTable<List < QueryWarehouseDemandPlanVersionListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月13日 16:54
     */
    BaseTable<List<QueryWarehouseDemandPlanVersionListRspVo>> queryWarehouseDemandPlanVersionDataList(
        WarehouseDemandPlanDataParamVo warehouseDemandPlanDataParamVo)
        throws Exception;

    /**
     *
     * @Description 查询分仓需求计划数据动态表头列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    List<String> queryWarehouseDemandPlanHeadList(QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo) throws Exception;

    /**
     *
     * @Description 查询分仓需求计划表头下拉列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<QueryWarehouseDemandPlanVersionListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanHeadSelect(
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo) throws Exception;

    /**
     *
     * @Description 查询分仓需求计划数据分组聚合列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<QueryWarehouseDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    List<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanGroupList(
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo) throws Exception;

    /**
     *
     * @Description 分页查询分仓需求计划数据列表
     * @param condition
     * @return PageInfo<QueryWarehouseDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    PageInfo<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanDataPage(PageCondition<QueryWarehouseDemandPlanVersionListReqVo> condition)
        throws Exception;

    /**
     *
     * @Description 查询分仓需求计划数据汇总
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return Map<String, Double>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月21日 14:50
     */
    Map<String, Double> queryWarehouseDemandPlanSummary(QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo) throws Exception;

    /**
     *
     * @Description 修改分仓需求计划数据
     * @param addDemandPlanConfigReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 17:21
     */
    void updateWarehouseDemandPlanData(AddDemandPlanConfigReqVo addDemandPlanConfigReqVo) throws Exception;

    /**
     *
     * @Description 标记分仓需求计划数据
     * @param warehouseDemandPlanMarkDto
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 17:21
     */
    void markWarehouseDemandPlanData(WarehouseDemandPlanMarkDto warehouseDemandPlanMarkDto) throws Exception;

    /**
     *
     * @Description 删除分仓需求计划
     * @param deleteDemandPlanReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月12日 11:11
     */
    void deleteWarehouseDemandPlan(DeleteDemandPlanReqVo deleteDemandPlanReqVo) throws Exception;

    /**
     *
     * @Description 查询分仓需求数据预测结果
     * @param channelDemandPlanReceiverDto
     * @return List<ChannelDemandPlanReceiverDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月18日 18:03
     */
    List<ChannelDemandPlanReceiverDto> queryWarehouseDemandPlanDataForecastResultList(ChannelDemandPlanReceiverDto channelDemandPlanReceiverDto)
        throws Exception;

    /**
     *
     * @Description 查询分仓需求计划提报数据
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月19日 9:37
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandPlanDataReportList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 查询分仓需求计划列表
     * @return JSONArray
     * <AUTHOR>
     * @date 2024年02月29日 14:15
     */
    JSONArray queryWarehouseDemandPlanList() throws Exception;

    /**
     *
     * @Description 新增分仓需求计划版本
     * @param warehouseDemandReportDto
     * @throws Exception
     * <AUTHOR>
     * @date 2024年04月08日 14:38
     */
    void addWarehouseDemandPlanVersion(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;
}
