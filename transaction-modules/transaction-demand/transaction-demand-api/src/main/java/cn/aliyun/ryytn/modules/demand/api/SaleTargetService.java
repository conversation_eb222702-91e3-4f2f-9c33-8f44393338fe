package cn.aliyun.ryytn.modules.demand.api;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetRspVo;

/**
 * @Description 销售目标接口
 * <AUTHOR>
 * @date 2023/10/23 11:19
 */
public interface SaleTargetService
{
    /**
     *
     * @Description 查询销售目标表格
     * @param querySalePlanListVo
     * @return DataqResult<List < QuerySaleTargetRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 11:31
     */
    BaseTable<List<QuerySaleTargetRspVo>> querySaleTargetList(QuerySaleTargetReqVo querySalePlanListVo) throws Exception;

    /**
     *
     * @Description 查询销售目标动态表头
     * @param querySaleTargetReqVo
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 17:21
     */
    List<String> querySaleTargetHeadList(QuerySaleTargetReqVo querySaleTargetReqVo) throws Exception;

    /**
     *
     * @Description 查询销售目标表头下拉列表
     * @param QuerySaleTargetReqVo
     * @return List<QuerySaleTargetRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<QuerySaleTargetRspVo> querySaleTargetHeadSelect(QuerySaleTargetReqVo QuerySaleTargetReqVo)
        throws Exception;

    /**
     *
     * @Description 查询销售目标分组聚合数据列表
     * @param QuerySaleTargetReqVo
     * @return List<QuerySaleTargetRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    List<QuerySaleTargetRspVo> querySaleTargetListGroupBy(QuerySaleTargetReqVo QuerySaleTargetReqVo)
        throws Exception;

    /**
     *
     * @Description 分页查询销售目标明细数据列表
     * @param condition
     * @return PageInfo<QuerySaleTargetListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 14:28
     */
    PageInfo<QuerySaleTargetRspVo> querySaleTargetDataPage(PageCondition<QuerySaleTargetReqVo> condition) throws Exception;

    /**
     *
     * @Description 查询销售目标数据汇总
     * @param QuerySaleTargetReqVo
     * @return Map<String, Double>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月21日 14:50
     */
    Map<String, Double> querySaleTargetSummary(QuerySaleTargetReqVo QuerySaleTargetReqVo) throws Exception;
}
