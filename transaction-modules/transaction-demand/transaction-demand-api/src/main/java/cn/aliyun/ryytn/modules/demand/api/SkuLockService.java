package cn.aliyun.ryytn.modules.demand.api;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.demand.entity.dto.SkuLockDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySkuReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockRspVo;

/**
 * @Description 产品锁定接口
 * <AUTHOR>
 * @date 2023/10/26 14:20
 */
public interface SkuLockService
{
    PageInfo<SkuLockRspVo> pageSkuLockList(PageCondition<SkuLockDto> condition) throws Exception;

    DataqResult<?> querySku(QuerySkuReqVo querySkuReqVo) throws Exception;

    void addSkuLock(SkuLockDto skuLockDto) throws Exception;

    void deleteSkuLock(String id) throws Exception;
}
