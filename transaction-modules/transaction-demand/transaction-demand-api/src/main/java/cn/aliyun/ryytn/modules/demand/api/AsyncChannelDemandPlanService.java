package cn.aliyun.ryytn.modules.demand.api;

import cn.aliyun.ryytn.modules.demand.entity.vo.ConfirmChannelDemandPlanSubPlanGroupVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DeleteDemandPlanReqVo;

/**
 * @Description 异步渠道需求计划接口
 * <AUTHOR>
 * @date 2024/3/23 19:15
 */
public interface AsyncChannelDemandPlanService
{
    /**
     *
     * @Description 级联删除渠道需求计划后续数据
     * @param deleteDemandPlanReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月23日 19:15
     */
    void deleteChannelDemandPlanCascade(DeleteDemandPlanReqVo deleteDemandPlanReqVo) throws Exception;

    /**
     *
     * @Description 同步认养系统共识数据
     * @param confirmChannelDemandPlanSubPlanGroupVo
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月23日 19:16
     */
    void syncChannelDemandPlanData(ConfirmChannelDemandPlanSubPlanGroupVo confirmChannelDemandPlanSubPlanGroupVo) throws Exception;
}
