package cn.aliyun.ryytn.modules.demand.api;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportVersionVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportGroupListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportHistoryRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportedChannelIdListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.UpdateChannelDemandReportVo;

/**
 * @Description 渠道需求提报接口
 * <AUTHOR>
 * @date 2023/10/24 10:23
 */
public interface ChannelDemandReportService
{
    /**
     *
     * @Description 查询渠道需求提报版本列表
     * @param queryChannelDemandReportVersionListReqVo
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:36
     */
    List<ChannelDemandReportVersionVo> queryChannelDemandReportVersionList(ChannelDemandReportVersionVo queryChannelDemandReportVersionListReqVo)
        throws Exception;

    /**
     *
     * @Description 查询渠道需求提报版本
     * @param channelDemandReportVersionDto
     * @return ChannelDemandReportVersionDto
     * @throws Exception
     * <AUTHOR>
     * @date 2024年02月20日 16:33
     */
    ChannelDemandReportVersionDto queryChannelDemandReportVersion(ChannelDemandReportVersionDto channelDemandReportVersionDto) throws Exception;

    /**
     *
     * @Description 查询渠道需求提报列表
     * @param queryChannelDemandReportListReqVo
     * @return BaseTable<List < QueryChannelDemandReportListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:36
     */
    BaseTable<List<QueryChannelDemandReportListRspVo>> queryChannelDemandReportList(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
        throws Exception;

    /**
     *
     * @Description 查询渠道需求提报动态表头
     * @param queryChannelDemandReportListReqVo
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:16
     */
    List<String> queryChannelDemandReportHeadList(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception;

    /**
     *
     * @Description 查询渠道需求提报表头下拉列表
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportGroupListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<QueryChannelDemandReportGroupListRspVo> queryChannelDemandReportHeadSelect(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
        throws Exception;

    /**
     *
     * @Description 查询渠道需求提报分组聚合数据列表
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportGroupListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    List<QueryChannelDemandReportGroupListRspVo> queryChannelDemandReportListGroupBy(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
        throws Exception;

    /**
     *
     * @Description 分页查询渠道需求提报明细数据列表
     * @param condition
     * @return PageInfo<QueryChannelDemandReportListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 14:28
     */
    PageInfo<QueryChannelDemandReportListRspVo> queryChannelDemandReportDataPage(PageCondition<QueryChannelDemandReportListReqVo> condition) throws Exception;

    /**
     *
     * @Description 查询渠道需求提报数据汇总
     * @param queryChannelDemandReportListReqVo
     * @return Map<String, Double>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月21日 14:50
     */
    Map<String, Double> queryChannelDemandReportSummary(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception;

    /**
     *
     * @Description 修改渠道需求提报版本锁定状态
     * @param channelDemandReportVersionDto
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月26日 14:22
     */
    void updateChannelDemandReportVersionLock(ChannelDemandReportVersionDto channelDemandReportVersionDto) throws Exception;

    /**
     *
     * @Description 查询渠道需求提报已提报的渠道编号列表
     * @param queryChannelDemandReportedChannelIdListReqVo
     * @return Set<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月27日 9:43
     */
    Set<String> queryChannelDemandReportedChannelIdList(QueryChannelDemandReportedChannelIdListReqVo queryChannelDemandReportedChannelIdListReqVo)
        throws Exception;

    /**
     *
     * @Description 修改渠道需求提报数据
     * @param updateChannelDemandReportVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月31日 17:50
     */
    void updateChannelDemandReportData(UpdateChannelDemandReportVo updateChannelDemandReportVo) throws Exception;

    /**
     *
     * @Description 查询渠道需求提报数据修改历史
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportHistoryRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月02日 16:23
     */
    List<QueryChannelDemandReportHistoryRspVo> queryChannelDemandReportHistoryList(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
        throws Exception;

    /**
     *
     * @Description 查询渠道需求提报版本最后一天日期
     * @param queryChannelDemandReportListReqVo
     * @return String
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月12日 15:21
     */
    String queryChannelDemandReportLastDate(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception;

    /**
     *
     * @Description 封装数据权限
     * @param queryChannelDemandReportListReqVo
     * <AUTHOR>
     * @date 2024年02月04日 15:04
     */
    void generateDataScope(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo);


    /**
     * 查询周期数据认养周时间列表，按照升序排列
     * @param queryChannelDemandReportListReqVo
     * @return
     */
    List<String> queryChannelDemandReportDateByOrder(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo);
}
