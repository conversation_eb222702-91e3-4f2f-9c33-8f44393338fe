package cn.aliyun.ryytn.modules.demand.api;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.demand.constant.PlanDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemanPlanHistoryDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanConfigReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanDataParamVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanVersionLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanVersionVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandSubPlanGroupVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ConfirmChannelDemandPlanSubPlanGroupVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DeleteDemandPlanReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.OfflineDemandPlanReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandForecastResultListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanDataReportListVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanDataSaleTargetVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionGroupListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySkuDeliveryListRspVo;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;
import cn.aliyun.ryytn.modules.system.entity.dto.ProductCategoryDto;

/**
 * @Description 需求计划接口
 * <AUTHOR>
 * @date 2023/10/24 10:16
 */
public interface ChannelDemandPlanService
{
    /**
     *
     * @Description 查询渠道表
     * @param subjectDimensionType
     * @return Set<ChannelDto>
     * <AUTHOR>
     * @date 2023年11月06日 17:17
     */
    Set<ChannelDto> queryChannelList(SubjectDimensionTypeEnum subjectDimensionType) throws Exception;

    /**
     *
     * @Description 查询产品品类列表
     * @param planDimensionType
     * @return Set<ProductCategoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月06日 17:28
     */
    Set<ProductCategoryDto> queryProductCategoryList(PlanDimensionTypeEnum planDimensionType) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划品类树
     * @param channelDemandPlanDataParamVo
     * @return Set<ProductCategoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月02日 15:34
     */
    Set<ProductCategoryDto> queryChannelDemandPlanCategoryTree(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划列表
     * @param queryChannelDemandPlanListReqVo
     * @return List<QueryChannelDemandPlanListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月15日 11:02
     */
    List<QueryChannelDemandPlanListRspVo> queryChannelDemandPlanList(QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划版本列表
     * @param condition
     * @return PageInfo<ChannelDemandPlanVersionVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月08日 14:15
     */
    PageInfo<ChannelDemandPlanVersionVo> queryChannelDemandPlanVersionList(PageCondition<String> condition) throws Exception;

    /**
     *
     * @Description 查询渠道需求子计划清单组列表
     * @param channelDemandSubPlanGroupVo
     * @return List<ChannelDemandSubPlanGroupVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 15:29
     */
    List<ChannelDemandSubPlanGroupVo> queryChannelDemandSubPlanGroupList(ChannelDemandSubPlanGroupVo channelDemandSubPlanGroupVo) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划数据列表
     * @param channelDemandPlanDataParamVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月16日 15:48
     */
    BaseTable<List<QueryChannelDemandPlanVersionListRspVo>> queryChannelDemandPlanDataList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception;

    /**
     *
     * @Description 查询渠道需求计划数据动态表头列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    List<String> queryChannelDemandPlanHeadList(QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划表头下拉列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionGroupListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<QueryChannelDemandPlanVersionGroupListRspVo> queryChannelDemandPlanHeadSelect(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划数据分组聚合列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionGroupListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    List<QueryChannelDemandPlanVersionGroupListRspVo> queryChannelDemandPlanGroupList(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo) throws Exception;

    /**
     *
     * @Description 分页查询渠道需求计划数据列表
     * @param condition
     * @return PageInfo<QueryChannelDemandPlanVersionGroupListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    PageInfo<QueryChannelDemandPlanVersionGroupListRspVo> queryChannelDemandPlanDataPage(
        PageCondition<QueryChannelDemandPlanVersionListReqVo> condition) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划数据汇总
     * @param queryChannelDemandPlanVersionListReqVo
     * @return Map<String, Double>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月21日 14:50
     */
    Map<String, Double> queryChannelDemandPlanSummary(QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划数据销售目标列表
     * @param channelDemandPlanDataParamVo
     * @return List<QueryChannelDemandPlanDataSaleTargetVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 16:56
     */
    List<QueryChannelDemandPlanDataSaleTargetVo> queryChannelDemandPlanDataSaleTargetList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception;

    /**
     *
     * @Description 查询渠道需求计划数据需求提报列表
     * @param channelDemandPlanDataParamVo
     * @return List<QueryChannelDemandPlanDataReportListVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 11:39
     */
    List<QueryChannelDemandPlanDataReportListVo> queryChannelDemandPlanDataReportList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception;

    /**
     *
     * @Description 查询渠道需求计划数据预测结果列表
     * @param channelDemandPlanDataParamVo
     * @return List<QueryChannelDemandForecastResultListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 17:37
     */
    List<QueryChannelDemandForecastResultListRspVo> queryChannelDemandPlanDataForecastResultList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception;

    /**
     *
     * @Description 查询渠道需求计划数据历史同期出库列表
     * @param channelDemandPlanDataParamVo
     * @return List<QuerySkuDeliveryListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 17:33
     */
    List<QuerySkuDeliveryListRspVo> queryChannelDemandPlanDataObserList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception;

    /**
     *
     * @Description 新增渠道需求计划
     * @param addDemandPlanListReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:14
     */
    void addChannelDemandPlan(AddDemandPlanListReqVo addDemandPlanListReqVo) throws Exception;

    /**
     *
     * @Description 设置渠道需求计划版本标签
     * @param channelDemandPlanVersionLabelVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:55
     */
    void setChannelDemandPlanVersionLabel(ChannelDemandPlanVersionLabelVo channelDemandPlanVersionLabelVo) throws Exception;

    /**
     *
     * @Description 复制渠道需求计划版本
     * @param channelDemandPlanDataParamVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 10:50
     */
    void duplicateChannelDemandPlanVersion(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception;

    /**
     *
     * @Description 修改渠道需求计划数据
     * @param addDemandPlanConfigReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 17:20
     */
    void updateChannelDemandPlanData(AddDemandPlanConfigReqVo addDemandPlanConfigReqVo) throws Exception;

    /**
     *
     * @Description 确认渠道需求计划子计划清单组
     * @param confirmChannelDemandPlanSubPlanGroupVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 16:26
     */
    void confirmChannelDemandPlanSubPlanGroup(ConfirmChannelDemandPlanSubPlanGroupVo confirmChannelDemandPlanSubPlanGroupVo) throws Exception;

    /**
     *
     * @Description 下线需求计划
     * @param offlineDemandPlanReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:01
     */
    void offlineChannelDemandPlan(OfflineDemandPlanReqVo offlineDemandPlanReqVo) throws Exception;

    /**
     *
     * @Description 删除需求计划
     * @param deleteDemandPlanReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:10
     */
    void deleteChannelDemandPlan(DeleteDemandPlanReqVo deleteDemandPlanReqVo) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划变更历史明细
     * @param channelDemandPlanDataParamVo
     * @return List<ChannelDemanPlanHistoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月10日 18:30
     */
    List<ChannelDemanPlanHistoryDto> queryChannelDemandPlanHistoryList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划列表
     * @return JSONArray
     * <AUTHOR>
     * @date 2024年02月29日 14:15
     */
    JSONArray queryChannelDemandPlanList() throws Exception;
}
