package cn.aliyun.ryytn.modules.demand.api;

import java.util.List;
import java.util.Map;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationDetailReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationDetailRsqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationListRspVo;

/**
 * @Description 偏差对比接口
 * <AUTHOR>
 * @date 2023/11/9 16:12
 */
public interface DeviationService
{
    /**
     *
     * @Description 查询偏差比对-列表数据
     * @param deviationChannelListReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月02日 10:10
     */
    BaseTable<List<QueryDeviationListRspVo>> queryDeviationList(QueryDeviationListReqVo deviationChannelListReqVo) throws Exception;

    /**
     *
     * @Description 查询偏差比对-下钻详情数据
     * @param deviationDetailReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月09日 15:10
     */
    BaseTable<List<QueryDeviationDetailRsqVo>> queryDeviationDetail(QueryDeviationDetailReqVo deviationDetailReqVo) throws Exception;

    /**
     * 获取版本信息
     * @return
     * @throws Exception
     */
    Map<String,Object> getVersion() throws Exception;
}
