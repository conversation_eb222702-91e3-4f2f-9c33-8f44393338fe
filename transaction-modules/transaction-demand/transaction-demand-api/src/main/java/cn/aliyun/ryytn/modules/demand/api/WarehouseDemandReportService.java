package cn.aliyun.ryytn.modules.demand.api;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.DateLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;

/**
 * @Description 分仓需求提报服务接口
 * <AUTHOR>
 * @date 2023/11/27 10:22
 */
public interface WarehouseDemandReportService
{
    /**
     *
     * @Description 新增分仓需求提报
     * @param warehouseDemandReportDto
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 17:32
     */
    void addWarehouseDemandReport(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 查询分仓需求提报对应渠道需求计划列表
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 11:55
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportPlanList() throws Exception;

    /**
     *
     * @Description 查询分仓需求提报版本列表
     * @param demandPlanCode
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 16:32
     */
    List<String> queryWarehouseDemandReportVersionList(String demandPlanCode) throws Exception;

    /**
     *
     * @Description 查询分仓需求提报数据列表
     * @param warehouseDemandReportDto
     * @return BaseTable<List < WarehouseDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月30日 20:49
     */
    BaseTable<List<WarehouseDemandReportDto>> queryWarehouseDemandReportList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 查询分仓需求提报编辑页面时间页签
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDateLabelVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 15:55
     */
    List<DateLabelVo> queryWarehouseDemandReportDateLabelList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 查询分仓需求提报数据列表
     * @param warehouseDemandReportDto
     * @return BaseTable<List < WarehouseDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月30日 20:49
     */
    BaseTable<List<WarehouseDemandReportDto>> queryWarehouseDemandReportDataList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 查询分仓需求提报动态表头
     * @param warehouseDemandReportDto
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:16
     */
    List<String> queryWarehouseDemandReportHeadList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 查询分仓需求提报表头下拉列表
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportHeadSelect(WarehouseDemandReportDto warehouseDemandReportDto)
        throws Exception;

    /**
     *
     * @Description 查询分仓需求提报分组聚合数据列表
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportListGroupBy(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 分页查询分仓需求提报明细数据列表
     * @param condition
     * @return PageInfo<QueryWarehouseDemandReportListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 14:28
     */
    PageInfo<WarehouseDemandReportDto> queryWarehouseDemandReportDataPage(PageCondition<WarehouseDemandReportDto> condition)
        throws Exception;

    /**
     *
     * @Description 查询分仓需求提报数据汇总
     * @param warehouseDemandReportDto
     * @return Map<String, Double>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月21日 14:50
     */
    Map<String, Double> queryWarehouseDemandReportSummary(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 修改分仓需求提报数据
     * @param dataList
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:16
     */
    void updateWarehouseDemandReportData(List<WarehouseDemandReportDto> dataList) throws Exception;

    /**
     *
     * @Description 查询分仓需求提报数据修改记录
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:50
     */
    List<WarehouseDemandReportDto> queryWarehouseDemandReportHistoryList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 查询分仓需求提报数据发布状态
     * @param warehouseDemandReportDto
     * @return int
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月11日 15:30
     */
    int queryWarehouseDemandReportPublishStatus(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 发布分仓需求提报数据，需要出发调用阿里接口生成分仓需求提报计划滚动版本
     * @param warehouseDemandReportDto
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月11日 15:30
     */
    void publishWarehouseDemandReport(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 删除分仓需求提报数据
     * @param demandPlanCode
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月12日 11:14
     */
    void deleteWarehouseDemandReport(String demandPlanCode) throws Exception;

    /**
     *
     * @Description 查询渠道需求计划共识版汇总数据
     * @param warehouseDemandReportDto
     * @return List<PlanValue>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月18日 15:34
     */
    List<PlanValue> queryChannelDemandPlanConfirmSum(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception;

    /**
     *
     * @Description 封装数据权限
     * @param warehouseDemandReportDto
     * <AUTHOR>
     * @date 2024年02月01日 17:35
     */
    void generateDataScope(WarehouseDemandReportDto warehouseDemandReportDto);
}
