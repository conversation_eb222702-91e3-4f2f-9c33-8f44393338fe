package cn.aliyun.ryytn.modules.demand.api;

import java.util.List;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelForecastResultBaseTable;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultDetailReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultDetailRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastChannelFilterListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastChannelListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseFilterListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseForecastResultRspVo;

/**
 * @Description 预测结果接口
 * <AUTHOR>
 * @date 2023/10/23 17:59
 */
public interface ForecastResultService
{
    /**
     *
     * @Description 查询渠道预测结果筛选条件
     * @param queryForecastChannelFilterListReqVo
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 18:11
     */
    DataqResult<?> queryChannelForecastResultFilterList(QueryForecastChannelFilterListReqVo queryForecastChannelFilterListReqVo) throws Exception;

    /**
     *
     * @Description 分页查询渠道预测结果
     * @param condition
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 18:11
     */
    DataqResult<PageInfo<?>> pageChannelForecastResultList(PageCondition<QueryForecastChannelListReqVo> condition) throws Exception;

    /**
     *
     * @Description 查询分仓预测结果过滤条件
     * @param queryForecastWarehouseFilterListReqVo
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 9:31
     */
    DataqResult<?> queryWarehouseForecastResultFilterList(QueryForecastWarehouseFilterListReqVo queryForecastWarehouseFilterListReqVo) throws Exception;

    /**
     *
     * @Description 分页查询分仓预测结果
     * @param condition
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 9:31
     */
    DataqResult<PageInfo<?>> pageWarehouseForecastResultList(PageCondition<QueryForecastWarehouseListReqVo> condition) throws Exception;

    /**
     *
     * @Description 查询渠道预测结果
     * @param queryChannelForecastResultReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月06日 17:05     */
    ChannelForecastResultBaseTable<?> queryChannelForecastResult(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo) throws Exception;

    /**
     *
     * @Description 查询渠道预测结果动态表头
     * @param queryChannelForecastResultReqVo
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月29日 14:46
     */
    List<String> queryChannelForecastResultHeadList(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo) throws Exception;

    /**
     *
     * @Description 查询渠道预测结果表头筛选条件
     * @param queryChannelForecastResultReqVo
     * @return List<QueryForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月29日 14:22
     */
    List<QueryForecastResultRspVo> queryChannelForecastResultHeadSelect(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo) throws Exception;

    /**
     *
     * @Description 分组聚合查询渠道预测结果列表
     * @param queryChannelForecastResultReqVo
     * @return List<QueryForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月09日 11:21
     */
    List<QueryForecastResultRspVo> queryChannelForecastResultGroupBy(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo)
        throws Exception;

    /**
     *
     * @Description 分页查询渠道预测结果
     * @param condition
     * @return PageInfo<QueryWarehouseForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 11:26
     */
    PageInfo<QueryForecastResultRspVo> queryChannelForecastResultPage(PageCondition<QueryChannelForecastResultReqVo> condition) throws Exception;

    /**
     *
     * @Description 查询渠道预测结果下拉列表
     * @param queryForecastChannelListReqVo
     * @return
     * <AUTHOR>
     * @date 2023年11月07日 10:20     */
    DataqResult<?> queryChannelForecastResultList(QueryForecastChannelListReqVo queryForecastChannelListReqVo) throws Exception;

    /**
     *
     * @Description 查询渠道预测结果详情
     * @param queryChannelForecastResultDetailReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月08日 10:19     */
    QueryChannelForecastResultDetailRspVo queryChannelForecastResultDetail(QueryChannelForecastResultDetailReqVo queryChannelForecastResultDetailReqVo)
        throws Exception;

    /**
     *
     * @Description 查询渠道预测结果报表
     * @param queryChannelForecastResultDetailReqVo
     * @return QueryChannelForecastResultDetailRspVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月28日 15:47
     */
    QueryChannelForecastResultDetailRspVo queryChannelForecastResultDetailReport(QueryChannelForecastResultDetailReqVo queryChannelForecastResultDetailReqVo)
        throws Exception;

    /**
     *
     * @Description 查询分仓预测结果下拉列表
     * @param queryForecastWarehouseListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月09日 15:09     */
    DataqResult<?> queryWarehouseForecastResultList(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo) throws Exception;

    /**
     *
     * @Description 查询分仓预测结果
     * @param queryForecastWarehouseListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月10日 9:20     */
    BaseTable<?> queryWarehouseForecastResult(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo) throws Exception;

    /**
     *
     * @Description 查询分仓预测结果动态表头
     * @param queryForecastWarehouseListReqVo
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 11:26
     */
    List<String> queryWarehouseForecastResultHeadList(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo) throws Exception;

    /**
     *
     * @Description 查询分仓预测结果动态表头下拉列表
     * @param queryForecastWarehouseListReqVo
     * @return List<QueryWarehouseForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 17:21
     */
    List<QueryWarehouseForecastResultRspVo> queryWarehouseDemandReportHeadSelect(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo)
        throws Exception;

    /**
     *
     * @Description 分页查询分仓预测结果
     * @param condition
     * @return PageInfo<QueryWarehouseForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 11:26
     */
    PageInfo<QueryWarehouseForecastResultRspVo> queryWarehouseForecastResultPage(PageCondition<QueryForecastWarehouseListReqVo> condition) throws Exception;
}
