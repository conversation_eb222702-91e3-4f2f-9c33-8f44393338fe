package cn.aliyun.ryytn.modules.demand.api;

import java.util.List;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto;

/**
 * @Description 低温需求提报服务接口
 * <AUTHOR>
 * @date 2023/11/27 10:22
 */
public interface ColdDemandReportService
{
    /**
     *
     * @Description 查询低温需求提报版本列表
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 16:32
     */
    List<String> queryColdDemandReportVersionList() throws Exception;

    /**
     *
     * @Description 查询低温需求提报数据列表
     * @param ColdDemandReportDto
     * @return BaseTable<List < ColdDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月30日 20:49
     */
    BaseTable<List<ColdDemandReportDto>> queryColdDemandReportList(ColdDemandReportDto ColdDemandReportDto) throws Exception;

    /**
     *
     * @Description 修改低温需求提报数据
     * @param dataList
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:16
     */
    void updateColdDemandReportData(List<ColdDemandReportDto> dataList) throws Exception;

    /**
     *
     * @Description 查询低温需求提报动态表头
     * @param queryChannelDemandReportListReqVo
     * @return List < String>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年3月1日 14:15
     */
    List<String> queryColdDemandReportHeadList(ColdDemandReportDto coldDemandReportDto) throws Exception;

    /**
     *
     * @Description 查询低温需求提报表头下拉列表
     * @param coldDemandReportDto
     * @return List<ColdDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月15日 14:58
     */
    List<ColdDemandReportDto> queryColdDemandReportHeadSelect(ColdDemandReportDto coldDemandReportDto) throws Exception;

    /**
     *
     * @Description 分组聚合查询低温需求提报列表
     * @param coldDemandReportDto
     * @return List<ColdDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月01日 16:16
     */
    List<ColdDemandReportDto> queryColdDemandReportListGroupBy(ColdDemandReportDto coldDemandReportDto) throws Exception;

    /**
     *
     * @Description 分页查询低温需求提报列表
     * @param condition
     * @return PageInfo<ColdDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月01日 16:17
     */
    PageInfo<ColdDemandReportDto> queryColdDemandReportDataPage(PageCondition<ColdDemandReportDto> condition) throws Exception;

    /**
     *
     * @Description 封装数据权限
     * @param coldDemandReportDto
     * <AUTHOR>
     * @date 2024年02月01日 17:35
     */
    void generateDataScope(ColdDemandReportDto coldDemandReportDto);
}
