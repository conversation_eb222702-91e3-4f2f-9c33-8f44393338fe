package cn.aliyun.ryytn.modules.demand.api;

import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastAlgorithmListReqVo;

/**
 *
 * @Description 预测算法接口
 * <AUTHOR>
 * @date 2023/10/23 17:42 */
public interface ForecastAlgorithmService
{
    DataqResult<?> queryForecastAlgorithmList(QueryForecastAlgorithmListReqVo queryForecastAlgorithmListReqVo) throws Exception;
}
