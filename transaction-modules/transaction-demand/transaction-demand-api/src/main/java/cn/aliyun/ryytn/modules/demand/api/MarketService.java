package cn.aliyun.ryytn.modules.demand.api;

import java.util.List;
import java.util.Map;

import cn.aliyun.ryytn.common.entity.Channel;
import cn.aliyun.ryytn.common.entity.ServiceResult;
import cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityExpertDO;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddOrUpdateMarketVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.MarketRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketChannelListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketConstantReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketSkuListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.MarketActivitySkuVo;

/**
 * @Description 促销活动接口
 * <AUTHOR>
 * @date 2023/10/24 9:20
 */
public interface MarketService
{
    /**
     * @Description 查询活动常量配置列表
     * @param  constantTypeVo
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/24 9:45
     */
    Map<Object, List<Map>> queryConstantList(QueryMarketConstantReqVo constantTypeVo) throws Exception;

    /**
     * @Description 查询活动列表
     * @param  queryMarketListReqVo
     * @return List<MarketRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/24 9:50
     */
    List<MarketRspVo> queryMarketList(QueryMarketListReqVo queryMarketListReqVo) throws Exception;

    /**
     * @Description 查询活动渠道树
     * @param  marketChannelReqVo
     * @return List<Channel>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/24 9:50
     */
    List<Channel> queryMarketChannelTree(QueryMarketChannelListReqVo marketChannelReqVo) throws Exception;


    /**
     * @Description 查询活动产品列表
     * @param  marketSkuListReqVo
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/26 17:25
     */
    List<MarketActivitySkuVo> queryMarketSkuList(QueryMarketSkuListReqVo marketSkuListReqVo) throws Exception;


    /**
     * 查询达人组
     * @param marketActivityExpertDO
     * @return
     * @throws Exception
     */
    List<MarketActivityExpertDO> queryMarketActivityExpertList(MarketActivityExpertDO marketActivityExpertDO) throws Exception;

    /**
     * 新增达人组
     * @param marketActivityExpertDO
     * @return
     */
    Boolean insertExpertGroups(MarketActivityExpertDO marketActivityExpertDO) throws Exception;

    /**
     * @Description 查询活动详情
     * @param  actiCode
     * @return Map<String,Object>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/27 11:25
     */
    Map<String,Object> queryMarketActivityDetail(String actiCode) throws Exception;


    /**
     * @Description 活动新增或修改
     * @param  marketVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/27 14:13
     */
    void  addOrUpdateMarketActivity(AddOrUpdateMarketVo marketVo)throws Exception;

    /**
     * 审核达人活动
     * @param marketVo
     * @throws Exception
     */
    void  auditMarketActivityExpert(AddOrUpdateMarketVo marketVo)throws Exception;


    /**
     * @Description 删除活动
     * @param  id
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/30 14:13
     */
    ServiceResult deleteMarketActivity(String  id)throws Exception;


}
