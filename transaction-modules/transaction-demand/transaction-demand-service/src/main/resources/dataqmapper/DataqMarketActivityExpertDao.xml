<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqMarketActivityExpertDao">

    <select id="queryMarketActivityExpertList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityExpertDO"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityExpertDO" >
        SELECT id, acti_code,expert_group,expert_id,remark1,remark2,deleted FROM tdm_xqyc_txn_market_activity_expert_di
        <include refid="where_sql"/>
    </select>

    <sql id="where_sql">
        WHERE 1 = 1
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
            <if test="actiCode != null and actiCode != ''">
                AND acti_code = #{actiCode}
            </if>
            <if test="expertGroup != null and expertGroup != ''">
                AND expert_group = #{expertGroup}
            </if>
            <choose>
                <when test="deleted != null and deleted != ''">
                    AND deleted = #{deleted}
                </when>
                <otherwise>
                    AND deleted = 0
                </otherwise>
            </choose>
    </sql>


    <select id="queryMarketDisExpertList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityExpertDO"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityExpertDO" >
        SELECT distinct expert_group FROM tdm_xqyc_txn_market_activity_expert_di
    </select>

    <update id="updateMarketActivityExpert" parameterType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityExpertDO">
        UPDATE tdm_xqyc_txn_market_activity_expert_di
        <trim prefix="SET" suffixOverrides=",">
            <if test="expertGroup != null">expert_group = #{expertGroup},</if>
            <if test="expertId != null">expert_id = #{expertId},</if>
            <if test="deleted != null">deleted = #{deleted}</if>
        </trim>
        WHERE acti_code = #{actiCode}
    </update>


    <update id="batchUpdateActivityExpertList" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE tdm_xqyc_txn_market_activity_expert_di
            <trim prefix="set" suffixOverrides=",">
                <if test="item.remark1 != null">remark1 = #{item.remark1}</if>
            </trim>
            where expert_group = #{item.expertGroup} and expert_id = #{item.expertId}
        </foreach>
    </update>


    <insert id="batchInsertExperts">
        INSERT INTO tdm_xqyc_txn_market_activity_expert_di
        (expert_group, expert_id,remark1)
        VALUES
        <foreach collection="collection" separator="," item="item">
            (#{item.expertGroup},
            #{item.expertId},
            #{item.remark1}
            )
        </foreach>
    </insert>

</mapper>