<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqAbcTypeDao">
	<select id="queryAbcTypes" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.AbcTypeDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.AbcTypeDto">
		select distinct  sku_code,sku_name,source_order_type ,abc_type  from cdop_biz.tdm_xqyc_sku_abc_df
		where 1=1
		<if test="skuCodes != null and skuCodes != ''">
			AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="sourceOrderType != null and sourceOrderType != ''">
			AND source_order_type = #{sourceOrderType}
		</if>
	</select>

	<select id="queryAbcTypeGoupBySkucode" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.AbcTypeDto"
			resultType="cn.aliyun.ryytn.modules.demand.entity.dto.AbcTypeDto">
		select  sku_code,min(abc_type) as abc_type  from cdop_biz.tdm_xqyc_sku_abc_df
		where 1=1
		<if test="skuCode != null and skuCode != ''">
			AND sku_code = #{skuCode}
		</if>
		group by sku_code
		limit 1
	</select>


</mapper>
