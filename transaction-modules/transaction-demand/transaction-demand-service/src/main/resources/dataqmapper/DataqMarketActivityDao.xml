<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqMarketActivityDao">




    <select id="queryMarketList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.MarketActivityDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.MarketRspVo">
        SELECT id, creator, last_modifier, gmt_create, gmt_modify
        , extend, acti_code, acti_name, acti_level, event_period
        , to_char(start_date, 'YYYY-MM-DD HH24:MI:SS') AS "start_date"
        , to_char(end_date, 'YYYY-MM-DD HH24:MI:SS') AS "end_date", activity_status,acti_area
        , acti_type, acti_delivery_date, deleted, event_cycle, activity_cycle
        , screen_desc, year,acti_begin_time,acti_end_time,acti_delivery_begin_time,acti_delivery_end_time
        ,expert_group,expert_id,audit_status
        FROM tdm_xqyc_txn_market_activity_di
        WHERE 1 = 1
        <if test="actiCode != null and actiCode != ''">
            AND acti_code = #{actiCode}
        </if>
        <if test="optDate != null and optDate != ''">
            AND start_date &lt;= #{optDate}::timestamp
        </if>
        <if test="optDate != null and optDate != ''">
            AND end_date &gt;= #{optDate}::timestamp
        </if>
        <if test="loseDate != null and loseDate != ''">
            AND end_date &lt;= #{loseDate}::timestamp
        </if>
        <if test="readyDate != null and readyDate != ''">
            AND start_date &gt; #{readyDate}::timestamp
        </if>
<!--        &lt小于，&gt大于-->
<!--        <choose>-->
<!--            <when test="startDate != null and startDate != '' and endDate != null and endDate != ''">-->
<!--                AND (start_date &lt;= #{endDate}::timestamp OR end_date &gt;= #{startDate}::timestamp)-->
<!--            </when>-->
<!--            <when test="startDate != null and startDate != ''">-->
<!--                AND (start_date &lt;= #{startDate}::timestamp OR end_date &gt;= #{startDate}::timestamp)-->
<!--            </when>-->
<!--            <when test="endDate != null and endDate != ''">-->
<!--                AND (start_date &lt;= #{endDate}::timestamp OR end_date &gt;= #{endDate}::timestamp)-->
<!--            </when>-->
<!--        </choose>-->
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            AND (
            start_date BETWEEN #{startDate}::timestamp AND #{endDate}::timestamp
            OR
            end_date BETWEEN #{startDate}::timestamp AND #{endDate}::timestamp
            OR
            (start_date &lt;= #{startDate}::timestamp AND end_date  &gt;= #{endDate}::timestamp)
            )
        </if>
<!--        <if test="startDate != null and startDate != ''">-->
<!--            AND start_date &gt;= #{startDate}::timestamp-->
<!--        </if>-->
        <if test="actiLevels != null and actiLevels.size() > 0">
            AND acti_level IN
            <foreach item="item" index="index" collection="actiLevels" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="actiTypes != null and actiTypes.size() > 0">
            AND acti_type IN
            <foreach item="item" index="index" collection="actiTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="actiNames != null and actiNames.size() > 0">
            AND acti_name IN
            <foreach item="item" index="index" collection="actiNames" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND deleted = COALESCE(#{deleted}, 0)
        <choose>
            <when test="sort == 1">
                ORDER BY start_date
            </when>
            <when test="sort == 2">
                ORDER BY end_date
            </when>
            <otherwise>
                ORDER BY gmt_create desc
            </otherwise>
        </choose>
    </select>

    <select id="getById" resultType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityDO">
        SELECT id, creator, last_modifier, extend, acti_code, acti_name, acti_level, event_period,activity_Status
        ,acti_area, acti_type, acti_delivery_date, deleted, event_cycle, activity_cycle
        , screen_desc, year,acti_begin_time,acti_end_time,acti_delivery_begin_time,acti_delivery_end_time
        ,expert_group,expert_id,audit_status
        FROM tdm_xqyc_txn_market_activity_di
        WHERE id = #{id}
    </select>

    <update id="updateById" parameterType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityDO">
        UPDATE tdm_xqyc_txn_market_activity_di
        <trim prefix="SET" suffixOverrides=",">
            <if test="creator != null">creator = #{creator},</if>
            <if test="lastModifier != null">last_modifier = #{lastModifier},</if>
            <if test="gmtModify != null">gmt_modify = #{gmtModify},</if>
            <if test="actiCode != null">acti_code = #{actiCode},</if>
            <if test="actiName != null">acti_name = #{actiName},</if>
            <if test="actiLevel != null">acti_level = #{actiLevel},</if>
            <if test="eventPeriod != null">event_period = #{eventPeriod},</if>
            <if test="startDate != null">start_date = to_timestamp(#{startDate}, 'YYYY-MM-DD HH24:MI:SS'),</if>
            <if test="endDate != null">end_date = to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS'),</if>
            <if test="activityStatus != null">activity_status = #{activityStatus},</if>
            <if test="actiArea != null">acti_area = #{actiArea},</if>
            <if test="actiType != null">acti_type = #{actiType},</if>
            <if test="actiDeliveryDate != null">acti_delivery_date = #{actiDeliveryDate},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            <if test="eventCycle != null">event_cycle = #{eventCycle},</if>
            <if test="activityCycle != null">activity_cycle = #{activityCycle},</if>
            <if test="screenDesc != null">screen_desc = #{screenDesc},</if>
            <if test="year != null">year = #{year},</if>
            <if test="actiBeginTime != null">acti_begin_time = #{actiBeginTime},</if>
            <if test="actiEndTime != null">acti_end_time = #{actiEndTime},</if>
            <if test="actiDeliveryBeginTime != null">acti_delivery_begin_time = #{actiDeliveryBeginTime},</if>
            <if test="actiDeliveryEndTime != null">acti_delivery_end_time = #{actiDeliveryEndTime},</if>
            <if test="expertGroup != null">expert_group = #{expertGroup},</if>
            <if test="expertId != null">expert_id = #{expertId},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus}</if>
        </trim>
        WHERE id = #{id}
    </update>

    <insert id="addMarketActivity" parameterType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityDO">
        INSERT INTO tdm_xqyc_txn_market_activity_di (
        creator,
        last_modifier,
        acti_code,
        acti_name,
        acti_level,
        event_period,
        start_date,
        end_date,
        activity_status,
        acti_area,
        acti_type,
        acti_delivery_date,
        event_cycle,
        activity_cycle,
        screen_desc,
        year,
        acti_begin_time,
        acti_end_time,
        acti_delivery_begin_time,
        acti_delivery_end_time,
        expert_group,
        expert_id,
        audit_status
        ) VALUES (
        #{creator},
        #{lastModifier},
        #{actiCode},
        #{actiName},
        #{actiLevel},
        #{eventPeriod},
        to_timestamp(#{startDate}, 'YYYY-MM-DD HH24:MI:SS'),
        to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS'),
        #{activityStatus},
        #{actiArea},
        #{actiType},
        #{actiDeliveryDate},
        #{eventCycle},
        #{activityCycle},
        #{screenDesc},
        #{year},
        #{actiBeginTime},
        #{actiEndTime},
        #{actiDeliveryBeginTime},
        #{actiDeliveryEndTime},
        #{expertGroup},
        #{expertId},
        #{auditStatus}
        )
    </insert>



</mapper>