<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandPlanDao">
	<select id="queryChannelDemandPlanHeadList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="java.lang.String">
		select distinct plan_date
		from tdm_xqyc_txn_demand_plan_order_di
		<include refid="where_sql"/>
		order by plan_date
	</select>

	<select id="queryChannelDemandPlanHeadSelect" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionGroupListRspVo">
		select distinct ${groupColumn}
		from tdm_xqyc_txn_demand_plan_order_di
		<include refid="where_sql"/>
		order by ${sortColumn}
	</select>

	<select id="queryChannelDemandPlanDataGroupList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionGroupListRspVo">
		SELECT
		json_agg("data") AS "data",
		${groupColumn}
		FROM (
			(
			SELECT json_build_object('plan_value', SUM(plan_value), 'plan_date', plan_date) AS "data"
				, ${groupColumn}
			FROM tdm_xqyc_txn_demand_plan_order_di
			<include refid="where_sql"/>
			GROUP BY ${groupColumn}, plan_date
			)
		) agg
		GROUP BY ${groupColumn}
		ORDER BY ${sortColumn}
	</select>

	<select id="queryChannelDemandPlanDataKeyList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionGroupListRspVo">
		select distinct
			lv1_category_code,
			lv2_category_code,
			lv3_category_code,
			sku_code,
			lv1_channel_code,
			lv2_channel_code,
			lv3_channel_code
		from tdm_xqyc_txn_demand_plan_order_di
		<include refid="where_sql"/>
		ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code, lv3_channel_code
	</select>

	<select id="queryChannelDemandPlanDataJsonList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionGroupListRspVo">
		SELECT
			json_agg(json_build_object('id', id::varchar, 'plan_date', plan_date::varchar, 'plan_value', plan_value::varchar)) AS "data",
			sku_code AS "skuCode",
			MAX(sku_name) AS "skuName",
			lv1_category_code AS "lv1CategoryCode",
			MAX(lv1_category_name) AS "lv1CategoryName",
			lv2_category_code AS "lv2CategoryCode",
			MAX(lv2_category_name) AS "lv2CategoryName",
			lv3_category_code AS "lv3CategoryCode",
			MAX(lv3_category_name) AS "lv3CategoryName",
			lv1_channel_code AS "lv1ChannelCode",
			MAX(lv1_channel_name) AS "lv1ChannelName",
			lv2_channel_code AS "lv2ChannelCode",
			MAX(lv2_channel_name) AS "lv2ChannelName",
			lv3_channel_code AS "lv3ChannelCode",
			MAX(lv3_channel_name) AS "lv3ChannelName"
		FROM tdm_xqyc_txn_demand_plan_order_di
		<include refid="where_sql"/>
		GROUP BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code, lv3_channel_code
		ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code, lv3_channel_code
	</select>

	<select id="queryChannelDemandPlanSummary" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue">
		SELECT
		plan_date,
		sum(plan_value) as plan_value
		FROM tdm_xqyc_txn_demand_plan_order_di
		<include refid="where_sql"/>
		GROUP BY plan_date
	</select>

	<select id="queryChannelDemandPlanVersionList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo">
		SELECT
		   <![CDATA[
			 a.plan_value as  order_num,
			 c.outbound_num as order_num_real,
			]]>
			a.*,
			b.plan_unit_cnt
		from
		(
		SELECT
			id,
			creator,
			last_modifier,
			gmt_create,
			gmt_modify,
			version_id,
			demand_plan_code,
			version_date,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			sku_code,
			sku_name,
			lifecycle_code,
			lifecycle_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			reseller_code,
			reseller_name,
			algo_version,
			ref_version,
			ref_type,
			plan_date,
			plan_value,
			plan_remark,
			is_modify,
			status,
			deleted,
			"group_id",
			"label"
		FROM tdm_xqyc_txn_demand_plan_order_di
		<include refid="where_sql"/>
		) a
		left join dim_bas_sku_baisc_info_df b
			on a.sku_code=b.sku_code
		left join tdm_xqyc_txn_delivery_order_week_df c on
			a.lv2_channel_code = c.lv2_channel_code
			and to_char(a.plan_date,'YYYYMMdd') = c.biz_date_value
			and a.sku_code = c.sku_code
			and a.lv3_category_code = c.lv3_category_code
			and c.dim_comb= 'LV2_CHANNEL_CODE+SKU+LV3_CATEGORY_CODE+WEEK'
		left join (
		SELECT DISTINCT SUBSTRING(fscl_week_range FROM 0 FOR 9) AS fscl_week_start, SUBSTRING(fscl_week_range FROM 10 FOR 19) AS fscl_week_end
		FROM dim_bas_calendar_df
		) d
			on  to_char(a.plan_date,'YYYYMMdd') = d.fscl_week_start
		ORDER BY a.lv1_category_code, a.lv2_category_code, a.lv3_category_code, a.sku_code, a.lv1_channel_code, a.lv2_channel_code, a.lv3_channel_code
	</select>

	<select id="queryChannelDemandPlanVersionDataList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo">
		SELECT
        			a.id,
        			a.creator,
        			a.last_modifier,
        			a.gmt_create,
        			a.gmt_modify,
        			a.version_id,
        			a.demand_plan_code,
        			a.version_date,
        			a.lv1_category_code,
        			a.lv1_category_name,
        			a.lv2_category_code,
        			a.lv2_category_name,
        			a.lv3_category_code,
        			a.lv3_category_name,
        			a.sku_code,
        			a.sku_name,
        			a.lifecycle_code,
        			a.lifecycle_name,
        			a.lv1_channel_code,
        			a.lv1_channel_name,
        			a.lv2_channel_code,
        			a.lv2_channel_name,
        			a.lv3_channel_code,
        			a.lv3_channel_name,
        			a.reseller_code,
        			a.reseller_name,
        			a.algo_version,
        			a.ref_version,
        			a.ref_type,
        			a.plan_date,
					a.plan_value,
        <!--        			case when a.plan_value is not null then a.plan_value else (case when c.lv3ChannelSum is null then 0 else c.lv3ChannelSum end) end as plan_value,-->
        a.plan_remark,
        			a.is_modify,
        			a.status,
        			a.deleted,
        			a."group_id",
        			a."label",
        			b.plan_unit_cnt
        		from
        		(
        SELECT
			id,
			creator,
			last_modifier,
			gmt_create,
			gmt_modify,
			version_id,
			demand_plan_code,
			version_date,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			sku_code,
			sku_name,
			lifecycle_code,
			lifecycle_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			reseller_code,
			reseller_name,
			algo_version,
			ref_version,
			ref_type,
			plan_date,
			plan_value,
			plan_remark,
			is_modify,
			status,
			deleted,
			"group_id",
			"label"
		FROM tdm_xqyc_txn_demand_plan_order_di
		<include refid="where_sql"/>
		) a
		left join dim_bas_sku_baisc_info_df b
		on a.sku_code=b.sku_code
        <!--		left join (select-->
        <!--			biz_date_value,-->
        <!--			sku_code,-->
        <!--			lv1_category_code,-->
        <!--			lv2_category_code,-->
        <!--			lv3_category_code,-->
        <!--			lv1_channel_code,-->
        <!--			lv2_channel_code,-->
        <!--			sum(order_num) lv3ChannelSum-->
        <!--		from-->
        <!--			tdm_xqyc_txn_sku_reporting_di-->
        <!--		where-->
        <!--			biz_date_type = 'WEEK'-->
        <!--			and is_modify = 0-->
        <!--		group by-->
        <!--			rolling_version,-->
        <!--			biz_date_value,-->
        <!--			sku_code,-->
        <!--			lv1_category_code,-->
        <!--			lv2_category_code,-->
        <!--			lv3_category_code,-->
        <!--			lv1_channel_code,-->
        <!--			lv2_channel_code-->
        <!--		having-->
        <!--			rolling_version =-->
        <!--			(select MAX(tmp.rolling_version) from tdm_xqyc_txn_sku_reporting_di tmp-->
        <!--			where biz_date_value=tmp.biz_date_value-->
        <!--			and sku_code=tmp.sku_code-->
        <!--			and lv1_category_code=tmp.lv1_category_code-->
        <!--			and lv2_category_code=tmp.lv2_category_code-->
        <!--			and lv3_category_code=tmp.lv3_category_code-->
        <!--			and lv1_channel_code=tmp.lv1_channel_code-->
        <!--			and lv2_channel_code=tmp.lv2_channel_code-->
        <!--			and replace(tmp.rolling_version,'CDP','DP') &lt;= #{versionId})-->
        <!--		) c-->
        <!--		on replace(a.plan_date::varchar,'-','')=c.biz_date_value-->
        <!--		and a.sku_code=c.sku_code-->
        <!--		and a.lv1_category_code=c.lv1_category_code-->
        <!--		and a.lv2_category_code=c.lv2_category_code-->
        <!--		and a.lv3_category_code=c.lv3_category_code-->
        <!--		and a.lv1_channel_code=c.lv1_channel_code-->
        <!--		and a.lv2_channel_code=c.lv2_channel_code-->
        ORDER BY a.lv1_category_code, a.lv2_category_code, a.lv3_category_code, a.sku_code, a.lv1_channel_code, a.lv2_channel_code, a.lv3_channel_code
	</select>

	<sql id="where_sql">
		WHERE 1 = 1
		<if test="demandPlanCode != null and demandPlanCode != ''">
			AND demand_plan_code = #{demandPlanCode}
		</if>
		<if test="versionId != null and versionId != ''">
			AND version_id = #{versionId}
		</if>
	    <if test="isModify != null">
			AND is_modify = #{isModify}::boolean
		</if>
		<if test="deleted != null">
			AND deleted = #{deleted}::boolean
		</if>
		<if test="groupId != null and groupId != ''">
			AND group_id = #{groupId}
		</if>
		<if test="skuCodes != null and skuCodes != ''">
			AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="skuCode != null and skuCode != ''">
			AND sku_code = #{skuCode}
		</if>
		<if test="lv1ChannelCodes != null and lv1ChannelCodes != ''">
			AND lv1_channel_code = ANY(STRING_TO_ARRAY(#{lv1ChannelCodes},','))
		</if>
		<if test="lv1ChannelCode != null and lv1ChannelCode != ''">
			AND lv1_channel_code = #{lv1ChannelCode}
		</if>
		<if test="lv2ChannelCodes != null and lv2ChannelCodes != ''">
			AND lv2_channel_code = ANY(STRING_TO_ARRAY(#{lv2ChannelCodes},','))
		</if>
		<if test="lv2ChannelCode != null and lv2ChannelCode != ''">
			AND lv2_channel_code = #{lv2ChannelCode}
		</if>
		<if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
			AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
		</if>
		<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
			AND lv1_category_code = #{lv1CategoryCode}
		</if>
		<if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
			AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
		</if>
		<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
			AND lv2_category_code = #{lv2CategoryCode}
		</if>
		<if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
			AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
		</if>
		<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
			AND lv3_category_code = #{lv3CategoryCode}
		</if>
		<if test="beginDate != null and endDate != null">
			AND plan_date::varchar &gt;= #{beginDate}
			AND plan_date::varchar &lt;= #{endDate}
		</if>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
				<if test="item.skuCode != null and item.skuCode != ''">
					AND sku_code = #{item.skuCode}
				</if>
				<if test="item.lv1ChannelCode != null and item.lv1ChannelCode != ''">
					AND lv1_channel_code = #{item.lv1ChannelCode}
				</if>
				<if test="item.lv2ChannelCode != null and item.lv2ChannelCode != ''">
					AND lv2_channel_code = #{item.lv2ChannelCode}
				</if>
				<if test="item.lv3ChannelCode != null and item.lv3ChannelCode != ''">
					AND lv3_channel_code = #{item.lv3ChannelCode}
				</if>
				<if test="item.lv1CategoryCode != null and item.lv1CategoryCode != ''">
					AND lv1_category_code = #{item.lv1CategoryCode}
				</if>
				<if test="item.lv2CategoryCode != null and item.lv2CategoryCode != ''">
					AND lv2_category_code = #{item.lv2CategoryCode}
				</if>
				<if test="item.lv3CategoryCode != null and item.lv3CategoryCode != ''">
					AND lv3_category_code = #{item.lv3CategoryCode}
				</if>
				)
			</foreach>
		</if>
	</sql>

	<update id="batchUpdateChannelDemandPlanData" parameterType="java.util.List">
		<foreach collection="list" item="item" separator=";">
			update tdm_xqyc_txn_demand_plan_order_di
			set last_modifier = #{item.lastModifier},
				gmt_modify = #{item.gmtModify},
				plan_value = #{item.planValue}
			where id = #{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</foreach>
	</update>

	<update id="updateChannelDemandPlanData" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemanPlanHistoryDto">
			update tdm_xqyc_txn_demand_plan_order_di
			set last_modifier = #{lastModifier},
				gmt_modify = #{gmtModify},
				plan_value = #{planValue}
			where id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<select id="queryChannelDemandPlanOldDataList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemanPlanHistoryDto">
		SELECT
		    id,
			version_id,
			demand_plan_code,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			sku_code,
			sku_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			plan_date,
			plan_value as "oldPlanValue",
			group_id
		FROM tdm_xqyc_txn_demand_plan_order_di
		<include refid="where_sql"/>
	</select>

	<select id="queryChannelDemandPlanVersionExists" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.AddChannelDemandPlanVersionVo"
            resultType="java.lang.Integer">
		select count(1) from tdm_xqyc_txn_demand_plan_order_di
		where demand_plan_code = #{demandPlanCode}
		  and version_id = #{versionId}
		  and deleted = false
	</select>

	<select id="queryChannelDemandPlanNameExists" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanListReqVo"
            resultType="java.lang.Integer">
		select count(1) from tdm_xqyc_txn_demand_config_info_di
		 where demand_plan_name = #{demandPlanName}
	</select>

	<select id="queryDeviationChannelDemandPlanList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo">
		select
			TO_CHAR(plan_date, 'YYYYMM') as plan_date,
			version_id,
			label,
			sum(plan_value) as plan_value
		from tdm_xqyc_txn_demand_plan_order_di
		<include refid="where_sql"/>
		group by TO_CHAR(plan_date, 'YYYYMM'),version_id,label
	</select>

	<select id="queryChannelDemandPlanName" parameterType="java.lang.String" resultType="java.lang.String">
		select demand_plan_name from tdm_xqyc_txn_demand_config_info_di where demand_plan_code = #{demandPlanCode} limit 1
	</select>

	<select id="queryChannelDemandPlanGroupBySkuChannel" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo">
		select demand_plan_code,version_id,sku_code,lv2_channel_code,plan_date,sum(plan_value) as plan_value
		from tdm_xqyc_txn_demand_plan_order_di
		where demand_plan_code = #{demandPlanCode}
		  and version_id = #{versionId}
		  and is_modify = false
		  and deleted = false
		group by demand_plan_code,version_id,sku_code,lv2_channel_code,plan_date
	</select>

	<select id="queryChannelDemandPlanReceiverListJoinOrderRate" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanReceiverDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanReceiverDto">
		select distinct
			plan.demand_plan_code,
			plan.version_id,
			plan.lv1_category_name,
			plan.lv1_category_code,
			plan.lv1_category_name,
			plan.lv2_category_code,
			plan.lv2_category_name,
			plan.lv3_category_code,
			plan.lv3_category_name,
			plan.sku_code,
			plan.sku_name,
			rate.channel_type as receiver_type,
			rate.warehouse_code,
			rate.warehouse_name,
			plan.plan_date,
			round(plan.plan_value * rate.outbound_rate::numeric,0) as plan_value,
			rate.outbound_rate
		from
		(
			select
			demand_plan_code,
			version_id,
			lv1_category_code,
			max(lv1_category_name) as lv1_category_name,
			lv2_category_code,
			max(lv2_category_name) as lv2_category_name,
			lv3_category_code,
			max(lv3_category_name) as lv3_category_name,
			sku_code,
			max(sku_name) as sku_name,
			plan_date,
			sum(plan_value) as plan_value
			from tdm_xqyc_txn_demand_plan_order_di
			where 1 = 1
			<if test="demandPlanCode != null and demandPlanCode != ''">
				and demand_plan_code = #{demandPlanCode}
			</if>
			<if test="versionId != null and versionId != ''">
				and version_id = #{versionId}
			</if>
			<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
				and lv1_category_code = #{lv1CategoryCode}
			</if>
			<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
				and lv2_category_code = #{lv2CategoryCode}
			</if>
			<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
				and lv3_category_code = #{lv3CategoryCode}
			</if>
			<if test="skuCode != null and skuCode != ''">
				and sku_code = #{skuCode}
			</if>
			<if test="skuCodes != null and skuCodes != ''">
				and sku_code = ANY(STRING_TO_ARRAY(#{skuCodes}, ','))
			</if>
			<if test="planDate != null and planDate != ''">
				and to_char(plan_date,'yyyyMMdd') = #{planDate}
			</if>
		      and is_modify = false
			  and deleted = false
			group by demand_plan_code,version_id,lv1_category_code,lv2_category_code,lv3_category_code,sku_code,plan_date
		) plan
		left join
		(
			select
				sku_code,
				lv1_category_code,
				lv2_category_code,
				lv3_category_code,
				channel_type,
				warehouse_code,
				warehouse_name,
				biz_date_value,
				outbound_rate
			from
				tdm_xqyc_txn_delivery_order_rate_df
			where
				biz_date_type = '16WEEK'
				and dim_comb = 'SKU_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK'
				and biz_date_value =(
				select
					MAX(biz_date_value)
				from
					tdm_xqyc_txn_delivery_order_rate_df
				where
					biz_date_type = '16WEEK'
					and dim_comb = 'SKU_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE+16WEEK')
		) as rate
		on
			plan.lv1_category_code = rate.lv1_category_code
			and plan.lv2_category_code = rate.lv2_category_code
			and plan.lv3_category_code = rate.lv3_category_code
			and plan.sku_code = rate.sku_code
		where 1=1
		<if test="receiverType != null and receiverType != ''">
			and lower(rate.channel_type) = lower(#{receiverType})
		</if>
		and warehouse_code is not null
	</select>

	<select id="queryChannelDemandPlanReceiverListJoinForecast" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanReceiverDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanReceiverDto">
		select distinct
			plan.demand_plan_code,
			plan.version_id,
			plan.lv1_category_name,
			plan.lv1_category_code,
			plan.lv1_category_name,
			plan.lv2_category_code,
			plan.lv2_category_name,
			plan.lv3_category_code,
			plan.lv3_category_name,
			plan.sku_code,
			plan.sku_name,
			rate.channel_type as receiver_type,
			forecast.warehouse_code,
			forecast.warehouse_name,
			plan.plan_date,
			round(plan.plan_value * rate.outbound_rate::numeric * forecast.outbound_rate::numeric,0) as plan_value,
			forecast.outbound_rate
		from
		(
			select
			demand_plan_code,
			version_id,
			lv1_category_code,
			max(lv1_category_name) as lv1_category_name,
			lv2_category_code,
			max(lv2_category_name) as lv2_category_name,
			lv3_category_code,
			max(lv3_category_name) as lv3_category_name,
			sku_code,
			max(sku_name) as sku_name,
			plan_date,
			sum(plan_value) as plan_value
			from tdm_xqyc_txn_demand_plan_order_di
			where 1 = 1
			<if test="demandPlanCode != null and demandPlanCode != ''">
				and demand_plan_code = #{demandPlanCode}
			</if>
			<if test="versionId != null and versionId != ''">
				and version_id = #{versionId}
			</if>
			<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
				and lv1_category_code = #{lv1CategoryCode}
			</if>
			<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
				and lv2_category_code = #{lv2CategoryCode}
			</if>
			<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
				and lv3_category_code = #{lv3CategoryCode}
			</if>
			<if test="skuCode != null and skuCode != ''">
				and sku_code = #{skuCode}
			</if>
			<if test="skuCodes != null and skuCodes != ''">
				and sku_code = ANY(STRING_TO_ARRAY(#{skuCodes}, ','))
			</if>
			<if test="planDate != null and planDate != ''">
				and to_char(plan_date,'yyyyMMdd') = #{planDate}
			</if>
		      and is_modify = false
			  and deleted = false
			group by demand_plan_code,version_id,lv1_category_code,lv2_category_code,lv3_category_code,sku_code,plan_date
		) plan
		left join
		(
			select lv1_category_code,lv2_category_code,lv3_category_code,sku_code,channel_type,outbound_rate
			from tdm_xqyc_txn_delivery_order_rate_df
			where biz_date_type='16WEEK'
			  and dim_comb='SKU_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+CHANNEL_TYPE+16WEEK'
			  and biz_date_value=(select max(biz_date_value) from tdm_xqyc_txn_delivery_order_rate_df where biz_date_type='16WEEK' and dim_comb='SKU_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+CHANNEL_TYPE+16WEEK')
		) as rate
		on plan.lv1_category_code = rate.lv1_category_code
		and plan.lv2_category_code = rate.lv2_category_code
		and plan.lv3_category_code = rate.lv3_category_code
		and plan.sku_code = rate.sku_code
		left join
		(
			select sku_code,
			lv1_category_code,
			lv2_category_code,
			lv3_category_code,
			receiver_type,
			warehouse_code,
			warehouse_name,
			target_biz_date,
			prediction_result as outbound_rate
			from tdm_xqyc_txn_forcast_warehouse_di
			where algo_name_and_version = #{algoNameAndVersion}
			and prediction_version = #{predictionVersion}
		) as forecast
		on
		plan.lv1_category_code = forecast.lv1_category_code
		and plan.lv2_category_code = forecast.lv2_category_code
		and plan.lv3_category_code = forecast.lv3_category_code
		and plan.sku_code = forecast.sku_code
		and lower(rate.channel_type) = lower(forecast.receiver_type)
		and to_char(plan.plan_date,'yyyyMMdd')=forecast.target_biz_date
		where 1=1
		<if test="receiverType != null and receiverType != ''">
			and lower(forecast.receiver_type) = lower(#{receiverType})
		</if>
		and warehouse_code is not null
	</select>

	<insert id="duplicateChannelDemandPlanDuplicateData">
		insert into tdm_xqyc_txn_demand_plan_order_di
		(creator, last_modifier, gmt_create, gmt_modify, version_id, demand_plan_code, version_date, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv3_category_code, lv3_category_name, sku_code, sku_name, lifecycle_code, lifecycle_name, lv1_channel_code, lv1_channel_name, lv2_channel_code, lv2_channel_name, lv3_channel_code, lv3_channel_name, reseller_code, reseller_name, algo_version, ref_version, ref_type, plan_date, plan_value, plan_remark, is_modify, status, deleted, "group_id", "label")
		SELECT
		'sys' AS creator,
		'sys' AS last_modifier,
		CURRENT_TIMESTAMP AS gmt_create,
		CURRENT_TIMESTAMP AS gmt_modify,
		#{newVersionId} AS version_id,
		po.demand_plan_code AS demand_plan_code,
		po.version_date AS version_date,
		po.lv1_category_code AS lv1_category_code,
		po.lv1_category_name AS lv1_category_name,
		po.lv2_category_code AS lv2_category_code,
		po.lv2_category_name AS lv2_category_name,
		po.lv3_category_code AS lv3_category_code,
		po.lv3_category_name AS lv3_category_name,
		po.sku_code AS sku_code,
		po.sku_name AS sku_name,
		po.lifecycle_code AS lifecycle_code,
		po.lifecycle_name AS lifecycle_name,
		po.lv1_channel_code AS lv1_channel_code,
		po.lv1_channel_name AS lv1_channel_name,
		po.lv2_channel_code AS lv2_channel_code,
		po.lv2_channel_name AS lv2_channel_name,
		po.lv3_channel_code AS lv3_channel_code,
		po.lv3_channel_name AS lv3_channel_name,
		po.reseller_code AS reseller_code,
		po.reseller_name AS reseller_name,
		po.algo_version AS algo_version,
		po.ref_version AS ref_version,
		po.ref_type AS ref_type,
		po.plan_date AS plan_date,
		po.plan_value AS plan_value,
		po.plan_remark AS plan_remark,
		false AS is_modify,
		-1 AS status,
		false AS deleted,
		po.group_id AS group_id,
		NULL AS label
		FROM
	( SELECT * FROM tdm_xqyc_txn_demand_plan_order_di
		WHERE version_id = #{oldVersionId}
		AND demand_plan_code = #{demandPlanCode}
		AND is_modify = FALSE
		) po
	</insert>

	<update id="updateChannelDemandPlanDataStatusTrail">
		update tdm_xqyc_txn_demand_plan_order_di set status=-1
		WHERE demand_plan_code = #{demandPlanCode}
		AND version_id = #{versionId}
		AND status = -2
	</update>

	<select id="queryConfirmedVersionList" parameterType="java.lang.String" resultType="java.lang.String">
		select t1.version_id
		from
		(
		select version_id,count(1) as confirmNum
		from tdm_xqyc_txn_demand_plan_order_di
		where demand_plan_code = #{demandPlanCode}
		  and is_modify=false
		  and deleted=false
		  and status=1
		group by version_id
		) t1
		left join
		(
		select version_id,count(1) as totalNum
		from tdm_xqyc_txn_demand_plan_order_di
		where demand_plan_code = #{demandPlanCode}
		  and is_modify=false
		  and deleted=false
		group by version_id
		) t2
		on t1.version_id=t2.version_id
		where t1.confirmNum=t2.totalNum
	</select>

	<select id="queryWareHouseDemandPlan16weekData" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanReceiverDto"
			resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandPlan16WeekDto">
			with P as (
				select
				sku_code,
				receiver_type,
				warehouse_code,
				lv3_category_code,
				plan_data_type,
				plan_rate,
				plan_value_original
				from
				tdm_xqyc_txn_demand_plan_warehouse_di
				where
				1 = 1
				<if test="demandPlanCode != null and demandPlanCode != ''">
					and demand_plan_code = #{demandPlanCode}
				</if>
				<if test="versionId != null and versionId != ''">
					and version_id = #{versionId}
				</if>
				<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
					and lv1_category_code = #{lv1CategoryCode}
				</if>
				<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
					and lv2_category_code = #{lv2CategoryCode}
				</if>
				<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
					and lv3_category_code = #{lv3CategoryCode}
				</if>
				and is_modify = '0'
				and deleted = '0'
				and plan_date = to_date(#{planDate},'YYYYMMdd')
		)
		select
			P.sku_code,
			P.receiver_type,
			P.warehouse_code,
			P.lv3_category_code,
			P.plan_data_type,
			case when P.plan_data_type = 0 then coalesce(cast(H.outbound_rate as DOUBLE precision),
			0)
			when P.plan_data_type = 1 then coalesce(cast(P.plan_rate as DOUBLE PRECISION),0)
			else 0
			end as outbound_rate,
			P.plan_value_original
			from
			P
			left join  (
				select
				lv1_category_code,
				lv2_category_code,
				lv3_category_code,
				warehouse_code,
				channel_type,
				outbound_rate,
				outbound_num,
				sku_code,
				data_type
				from
				tdm_xqyc_txn_delivery_order_rate_df_v2
				where
				dim_comb = 'SKU_CODE+LV1_CATEGORY_CODE+LV2_CATEGORY_CODE+LV3_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE'
				<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
					and lv1_category_code = #{lv1CategoryCode}
				</if>
				<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
					and lv2_category_code = #{lv2CategoryCode}
				</if>
				<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
					and lv3_category_code = #{lv3CategoryCode}
				</if>
				and biz_date_type = '16WEEKS'
				and biz_date_value =(
				select
				MAX(biz_date_value)
				from
				tdm_xqyc_txn_delivery_order_rate_df_v2
				where
				biz_date_type = '16WEEKS'
				and dim_comb = 'SKU_CODE+LV1_CATEGORY_CODE+LV2_CATEGORY_CODE+LV3_CATEGORY_CODE+WAREHOUSE_CODE+CHANNEL_TYPE') ) H on
					P.receiver_type = H.channel_type
					and P.sku_code = H.sku_code
					and cast(P.plan_data_type as char) = H.data_type
					and P.lv3_category_code= H.lv3_category_code
					and P.warehouse_code = H.warehouse_code
					and H.data_type = '0'
		where 1=1
			<if test="receiverType != null and receiverType != ''">
				and P.receiver_type = #{receiverType}
			</if>
		order by P.sku_code,P.receiver_type
	</select>

</mapper>
