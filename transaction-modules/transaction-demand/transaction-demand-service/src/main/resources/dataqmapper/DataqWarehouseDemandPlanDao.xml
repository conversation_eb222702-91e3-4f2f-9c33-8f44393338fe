<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqWarehouseDemandPlanDao">
	<select id="queryWarehouseDemandPlanHeadList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo"
            resultType="java.lang.String">
    	select distinct plan_date
    	from tdm_xqyc_txn_demand_plan_warehouse_di
    	<include refid="where_sql"/>
    	order by plan_date
    </select>

	<select id="queryWarehouseDemandPlanHeadSelect" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListRspVo">
		select distinct ${groupColumn}
		from tdm_xqyc_txn_demand_plan_warehouse_di
    	<include refid="where_sql"/>
		order by ${sortColumn}
	</select>

    <select id="queryWarehouseDemandPlanDataGroupList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListRspVo">
    	SELECT
    	json_agg("data") AS "data",
    	${groupColumn}
    	FROM (
    		(
    		SELECT json_build_object('plan_value', SUM(plan_value), 'plan_date', plan_date) AS "data"
    			, ${groupColumn}
    		FROM tdm_xqyc_txn_demand_plan_warehouse_di
    		<include refid="where_sql"/>
    		GROUP BY ${groupColumn}, plan_date
    		)
    	) agg
    	GROUP BY ${groupColumn}
    	ORDER BY ${sortColumn}
    </select>

    <select id="queryWarehouseDemandPlanDataKeyList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListRspVo">
    	select distinct
    		lv1_category_code,
    		lv2_category_code,
    		lv3_category_code,
    		sku_code,
    		receiver_type,
			plan_data_type,
    		warehouse_code
    	from tdm_xqyc_txn_demand_plan_warehouse_di
    	<include refid="where_sql"/>
    	ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, receiver_type,plan_data_type, warehouse_code
    </select>

    <select id="queryWarehouseDemandPlanDataJsonList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListRspVo">
		SELECT
		json_agg(json_build_object('id', p.id::varchar, 'plan_date', p.plan_date::varchar, 'plan_value', p.plan_value::varchar)) AS "data",
		p.sku_code AS "skuCode",
		MAX(p.sku_name) AS "skuName",
		p.lv1_category_code AS "lv1CategoryCode",
		MAX(p.lv1_category_name) AS "lv1CategoryName",
		p.lv2_category_code AS "lv2CategoryCode",
		MAX(p.lv2_category_name) AS "lv2CategoryName",
		p.lv3_category_code AS "lv3CategoryCode",
		MAX(p.lv3_category_name) AS "lv3CategoryName",
		p.receiver_type AS "receiverType",
		p.plan_data_type as "planDataType",
		p.warehouse_code AS "warehouseCode",
		MAX(p.warehouse_name) AS "warehouseName",
		max(abc.abc_type) as abc_type
		FROM tdm_xqyc_txn_demand_plan_warehouse_di p left join tdm_xqyc_sku_abc_df abc on p.sku_code  = abc.sku_code  and p.receiver_type = abc.source_order_type
		<include refid="where_sql_p"/>
		GROUP BY p.lv1_category_code, p.lv2_category_code, p.lv3_category_code, p.sku_code, p.receiver_type,p.plan_data_type, p.warehouse_code, p.warehouse_name
		ORDER BY p.lv1_category_code, p.lv2_category_code, p.lv3_category_code, p.sku_code, p.receiver_type,p.plan_data_type, p.warehouse_code, p.warehouse_name
    </select>

    <select id="queryWarehouseDemandPlanSummary" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue">
    	SELECT
    	plan_date,
    	sum(plan_value) as plan_value
    	FROM tdm_xqyc_txn_demand_plan_warehouse_di
    	<include refid="where_sql"/>
    	GROUP BY plan_date
    </select>

	<sql id="where_sql_p">
		WHERE
		 p.demand_plan_code = #{demandPlanCode}
		AND p.version_id = #{versionId}
		<if test="isModify != null">
			AND p.is_modify = #{isModify}::boolean
		</if>
		<if test="deleted != null">
			AND p.deleted = #{deleted}::boolean
		</if>
		<if test="groupId != null and groupId != ''">
			AND p.group_id = #{groupId}
		</if>
		<if test="skuCodes != null and skuCodes != ''">
			AND p.sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="skuCode != null and skuCode != ''">
			AND p.sku_code = #{skuCode}
		</if>
		<if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
			AND p.lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
		</if>
		<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
			AND p.lv1_category_code = #{lv1CategoryCode}
		</if>
		<if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
			AND p.lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
		</if>
		<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
			AND p.lv2_category_code = #{lv2CategoryCode}
		</if>
		<if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
			AND p.lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
		</if>
		<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
			AND p.lv3_category_code = #{lv3CategoryCode}
		</if>
		<if test="receiverType != null and receiverType != ''">
			and p.receiver_type = #{receiverType}
		</if>
		<if test="warehouseCodes != null and warehouseCodes != ''">
			and p.warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
		</if>
		<if test="warehouseCode != null and warehouseCode != ''">
			and p.warehouse_code = #{warehouseCode}
		</if>
		<if test="planDataTypes != null and planDataTypes != ''">
			and cast(p.plan_data_type as varchar)  = ANY(STRING_TO_ARRAY(#{planDataTypes},','))
		</if>

		<if test="planDataType != null and planDataType != ''">
			and p.plan_data_type = #{planDataType}
		</if>
		<if test="beginDate != null and endDate != null">
			AND to_char(p.plan_date,'yyyyMMdd') &gt;= #{beginDate}
			AND to_char(p.plan_date,'yyyyMMdd') &lt;= #{endDate}
		</if>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
				<if test="item.skuCode != null and item.skuCode != ''">
					AND p.sku_code = #{item.skuCode}
				</if>
				<if test="item.lv1CategoryCode != null and item.lv1CategoryCode != ''">
					AND p.lv1_category_code = #{item.lv1CategoryCode}
				</if>
				<if test="item.lv2CategoryCode != null and item.lv2CategoryCode != ''">
					AND p.lv2_category_code = #{item.lv2CategoryCode}
				</if>
				<if test="item.lv3CategoryCode != null and item.lv3CategoryCode != ''">
					AND p.lv3_category_code = #{item.lv3CategoryCode}
				</if>
				<if test="item.receiverType != null and item.receiverType != ''">
					and p.receiver_type = #{item.receiverType}
				</if>
				<if test="item.warehouseCode != null and item.warehouseCode != ''">
					and p.warehouse_code = #{item.warehouseCode}
				</if>
				<if test="item.planDataType != null and item.planDataType != ''">
					and p.plan_data_type = #{item.planDataType}
				</if>
				)
			</foreach>
		</if>
	</sql>

    <sql id="where_sql">
    	WHERE 1 = 1
    		AND demand_plan_code = #{demandPlanCode}
    		AND version_id = #{versionId}
        <if test="isModify != null">
    		AND is_modify = #{isModify}::boolean
    	</if>
    	<if test="deleted != null">
    		AND deleted = #{deleted}::boolean
    	</if>
    	<if test="groupId != null and groupId != ''">
    		AND group_id = #{groupId}
    	</if>
    	<if test="skuCodes != null and skuCodes != ''">
    		AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
    	</if>
    	<if test="skuCode != null and skuCode != ''">
    		AND sku_code = #{skuCode}
    	</if>
    	<if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
    		AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
    	</if>
    	<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
    		AND lv1_category_code = #{lv1CategoryCode}
    	</if>
    	<if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
    		AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
    	</if>
    	<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
    		AND lv2_category_code = #{lv2CategoryCode}
    	</if>
    	<if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
    		AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
    	</if>
    	<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
    		AND lv3_category_code = #{lv3CategoryCode}
    	</if>
    	<if test="receiverType != null and receiverType != ''">
    		and receiver_type = #{receiverType}
    	</if>
    	<if test="warehouseCodes != null and warehouseCodes != ''">
    		and warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
    	</if>
    	<if test="warehouseCode != null and warehouseCode != ''">
    		and warehouse_code = #{warehouseCode}
    	</if>
		<if test="planDataTypes != null and planDataTypes != ''">
			and cast(plan_data_type as varchar)  = ANY(STRING_TO_ARRAY(#{planDataTypes},','))
		</if>

		<if test="planDataType != null and planDataType != ''">
			and plan_data_type = #{planDataType}
		</if>
    	<if test="beginDate != null and endDate != null">
    		AND to_char(plan_date,'yyyyMMdd') &gt;= #{beginDate}
    		AND to_char(plan_date,'yyyyMMdd') &lt;= #{endDate}
    	</if>
    	<if test="keyList != null and keyList.size() > 0">
    		AND
    		<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
    			(1=1
    			<if test="item.skuCode != null and item.skuCode != ''">
    				AND sku_code = #{item.skuCode}
    			</if>
    			<if test="item.lv1CategoryCode != null and item.lv1CategoryCode != ''">
    				AND lv1_category_code = #{item.lv1CategoryCode}
    			</if>
    			<if test="item.lv2CategoryCode != null and item.lv2CategoryCode != ''">
    				AND lv2_category_code = #{item.lv2CategoryCode}
    			</if>
    			<if test="item.lv3CategoryCode != null and item.lv3CategoryCode != ''">
    				AND lv3_category_code = #{item.lv3CategoryCode}
    			</if>
				<if test="item.receiverType != null and item.receiverType != ''">
					and receiver_type = #{item.receiverType}
				</if>
				<if test="item.warehouseCode != null and item.warehouseCode != ''">
					and warehouse_code = #{item.warehouseCode}
				</if>
				<if test="item.planDataType != null and item.planDataType != ''">
					and plan_data_type = #{item.planDataType}
				</if>
    			)
    		</foreach>
    	</if>
    </sql>

	<select id="queryWarehouseDemandPlanVersionCount" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="java.lang.Integer">
		select count(1) from tdm_xqyc_txn_demand_plan_warehouse_di
		where demand_plan_code = #{demandPlanCode}
		  and version_id = #{rollingVersion}
		  and status &lt;&gt; -2
	</select>

	<select id="updateWarehouseDemandPlanStatus" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto">
		update tdm_xqyc_txn_demand_plan_warehouse_di set status=-1
		where  demand_plan_code = #{demandPlanCode}
		  and version_id = #{rollingVersion}
		  and status = -2
	</select>

	<select id="queryWarehouseDemandPlanList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListRspVo">
		with P as (
		select
			id,
			version_id,
			demand_plan_code,
			version_date,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			sku_code,
			sku_name,
			warehouse_code,
			warehouse_name,
			plan_date,
			plan_value,
			"group_id",
			receiver_type,
			plan_data_type,
			decode(plan_data_type,0,'日销计划量',1,'活动计划量','') as plan_data_type_name,
			"label"
		from
			tdm_xqyc_txn_demand_plan_warehouse_di
    	<include refid="where_sql"/>
		),
		a as (
			select
			to_json(json_agg(json_build_object('biz_date_value',
			to_char(to_date(d.biz_date_value::varchar, 'YYYY-MM-DD'),'yyyyMMdd'),
			'outbound_num',
			d.outbound_num::varchar))) as json_array,
			d.sku_code,
			d.lv1_category_code ,
			d.lv2_category_code ,
			d.lv3_category_code ,
			d.warehouse_code ,
			d.source_order_type ,
			d.data_type
			from
			cdop_biz.tdm_xqyc_txn_delivery_order_day_df d,
			(
			select
			distinct lv1_category_code,
			lv2_category_code,
			lv3_category_code,
			warehouse_code,
			sku_code,
			receiver_type,
			plan_data_type
			from
			P) as pp
			where
			d.lv1_category_code = pp.lv1_category_code
			and d.lv2_category_code = pp.lv2_category_code
			and d.lv3_category_code = pp.lv3_category_code
			and d.warehouse_code = pp.warehouse_code
			and d.sku_code = pp.sku_code
			and d.source_order_type = pp.receiver_type
			and d.data_type = cast(pp.plan_data_type as varchar)
			and d.dim_comb = 'SKU+LV3_CATEGORY_CODE+LV2_CATEGORY_CODE+LV1_CATEGORY_CODE+BC+WAREHOUSE+DAY'
			and d.biz_date_type = 'DAY'
			and to_date(d.biz_date_value,
			'YYYY-MM-DD') <![CDATA[>=]]> to_date(#{beginDate},
			'YYYYMMDD')
			and to_date(d.biz_date_value,
			'YYYY-MM-DD') <![CDATA[<=]]> to_date(#{endDate},
			'YYYYMMDD')
			group by
			d.sku_code,
			d.lv1_category_code ,
			d.lv2_category_code ,
			d.lv3_category_code ,
			d.warehouse_code ,
			d.source_order_type ,
			d.data_type
		)
		select
			pt.*,
			x.json_array as data,
			abc.abc_type
		from
			P pt
		left join tdm_xqyc_sku_abc_df abc on pt.sku_code = abc.sku_code and pt.receiver_type = abc.source_order_type
		left join a as x
		on
		x .sku_code = pt.sku_code
		and x.lv1_category_code = pt.lv1_category_code
		and x.lv2_category_code = pt.lv2_category_code
		and x.lv3_category_code = pt.lv3_category_code
		and x.warehouse_code = pt.warehouse_code
		and x.source_order_type = pt.receiver_type
		and x.data_type = cast(pt.plan_data_type as varchar)
		order by pt.warehouse_code,pt.lv1_category_code,pt.lv2_category_code,pt.lv3_category_code,pt.sku_code,pt.plan_date
	</select>
</mapper>
