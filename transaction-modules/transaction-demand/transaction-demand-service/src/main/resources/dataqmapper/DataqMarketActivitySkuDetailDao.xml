<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqMarketActivitySkuDetailDao">

    <select id="queryMarketActivitySkuDetailList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivitySkuDetailDO"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivitySkuDetailDO" >
        SELECT id, acti_code,  sku_code, acti_sku_delivery_nums_limit, acti_sku_delivery_begin_time,acti_sku_week_begin_time,audit_status
        FROM tdm_xqyc_txn_market_activity_sku_detail_di
        <include refid="where_sql"/>
    </select>

    <sql id="where_sql">
        WHERE 1 = 1
            <if test="actiCode != null and actiCode != ''">
                AND acti_code = #{actiCode}
            </if>
            <if test="skuCode != null and skuCode != ''">
                AND sku_code = #{skuCode}
            </if>
            <choose>
                <when test="deleted != null and deleted != ''">
                    AND deleted = #{deleted}
                </when>
                <otherwise>
                    AND deleted = 0
                </otherwise>
            </choose>
    </sql>

    <delete id="deleteSkuDetail" parameterType="java.lang.String">
        DELETE FROM tdm_xqyc_txn_market_activity_sku_detail_di WHERE acti_code = #{actiCode}
    </delete>


    <insert id="batchInsertSkuDetail">
        INSERT INTO tdm_xqyc_txn_market_activity_sku_detail_di
        (acti_code, sku_code, acti_sku_delivery_begin_time,acti_sku_delivery_nums_limit,acti_sku_week_begin_time,audit_status)
        VALUES
        <foreach collection="collection" separator="," item="item">
            (#{item.actiCode},
            #{item.skuCode},
            #{item.actiSkuDeliveryBeginTime},
            #{item.actiSkuDeliveryNumsLimit},
            #{item.actiSkuWeekBeginTime},
            #{item.auditStatus}
            )
        </foreach>
    </insert>

</mapper>