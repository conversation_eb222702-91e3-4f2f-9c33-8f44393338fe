<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqDeliveryOrderRateDao">

    <select id="queryHistoryRealityDeliveryOrderByWeek" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandRealityDataVo" resultType="cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandRealityDataVo">
        select
		    biz_date_value as biz_week_date_start,
            sku_code,
            lv3_channel_code,
            lv2_channel_code,
            outbound_num as reality_order_num
        from tdm_xqyc_txn_delivery_order_week_df where dim_comb= #{dimComb}
        and sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
        and biz_date_value = ANY(STRING_TO_ARRAY(#{bizDates},','))
        <if test="lv3ChannelCodes != null and lv3ChannelCodes != ''">
            and lv3_channel_code = ANY(STRING_TO_ARRAY(#{lv3ChannelCodes},','))
        </if>
        <if test="lv2ChannelCodes != null and lv2ChannelCodes != ''">
            and lv2_channel_code = ANY(STRING_TO_ARRAY(#{lv2ChannelCodes},','))
        </if>
    </select>

    <select id="queryDeliveryOrderRateGroupByWarehouse" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.DeliveryOrderRateDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.DeliveryOrderRateDto">
        select
            biz_date_value,
            lv1_category_code,
            lv2_category_code,
            lv3_category_code,
            sku_code,
            channel_type,
            warehouse_code,
            MAX(warehouse_name) as warehouse_name,
            round(sum(outbound_rate::numeric), 2) as outbound_rate
        from
            tdm_xqyc_txn_delivery_order_rate_df
        where 1 = 1
        <if test="dimComb != null and dimComb != ''">
            and dim_comb = #{dimComb}
        </if>
        <if test="bizDateType != null and bizDateType != ''">
            and biz_date_type = #{bizDateType}
        </if>
        <if test="bizDateValue != null and bizDateValue != ''">
            and biz_date_value = #{bizDateValue}
        </if>
        <if test="beginDate != null and beginDate != ''">
            and biz_date_value &gt;= #{beginDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and biz_date_value &lt;= #{endDate}
        </if>
        <if test="lv1CategoryCode != null and lv1CategoryCode != ''">
            and lv1_category_code = #{lv1CategoryCode}
        </if>
        <if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
            and lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
        </if>
        <if test="lv2CategoryCode != null and lv2CategoryCode != ''">
            and lv2_category_code = #{lv2CategoryCode}
        </if>
        <if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
            and lv2category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
        </if>
        <if test="lv3CategoryCode != null and lv3CategoryCode != ''">
            and lv3_category_code = #{lv3CategoryCode}
        </if>
        <if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
            and lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
        </if>
        <if test="skuCode != null and skuCode != ''">
            and sku_code = #{skuCode}
        </if>
        <if test="skuCodes != null and skuCodes != ''">
            and sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
        </if>
        <if test="channelType != null and channelType != ''">
            and channel_type = #{channelType}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            and warehouse_code = #{warehouseCode}
        </if>
        <if test="warehouseCodes != null and warehouseCodes != ''">
            and warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
        </if>
        group by
            biz_date_value,
            lv1_category_code,
            lv2_category_code,
            lv3_category_code,
            sku_code,
            channel_type,
            warehouse_code
    </select>

    <select id="queryDeliveryOrderGroupByMonth" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.DeliveryOrderDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.DeliveryOrderDto">
        select
			SUBSTRING(biz_date_value, 1, 6) as biz_date_value,
			sum(outbound_num) as outbound_num
		from
			tdm_xqyc_txn_delivery_order_df
        where 1 = 1
        <if test="dimComb != null and dimComb != ''">
            and dim_comb = #{dimComb}
        </if>
        <if test="bizDateType != null and bizDateType != ''">
            and biz_date_type = #{bizDateType}
        </if>
        <if test="bizDateValue != null and bizDateValue != ''">
            and biz_date_value = #{bizDateValue}
        </if>
        <if test="beginDate != null and beginDate != ''">
            and biz_date_value &gt;= #{beginDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and biz_date_value &lt;= #{endDate}
        </if>
        <if test="lv1ChannelCode != null and lv1ChannelCode != ''">
            and lv1_channel_code = #{lv1ChannelCode}
        </if>
        <if test="lv2ChannelCode != null and lv2ChannelCode != ''">
            and lv2_channel_code = #{lv2ChannelCode}
        </if>
        <if test="lv1CategoryCode != null and lv1CategoryCode != ''">
            and lv1_category_code = #{lv1CategoryCode}
        </if>
        <if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
            and lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
        </if>
        <if test="lv2CategoryCode != null and lv2CategoryCode != ''">
            and lv2_category_code = #{lv2CategoryCode}
        </if>
        <if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
            and lv2category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
        </if>
        <if test="lv3CategoryCode != null and lv3CategoryCode != ''">
            and lv3_category_code = #{lv3CategoryCode}
        </if>
        <if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
            and lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
        </if>
        <if test="skuCode != null and skuCode != ''">
            and sku_code = #{skuCode}
        </if>
        <if test="skuCodes != null and skuCodes != ''">
            and sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
        </if>
		group by SUBSTRING(biz_date_value, 1, 6)
    </select>
</mapper>
