<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqWarehouseDao">
	<select id="queryRdcPhysicWarehouseList" resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDto">
		SELECT distinct biz_warehouse_code, biz_warehouse_name
		FROM dim_bas_warehouse_info_df
		WHERE status='1'
		and biz_warehouse_code is not null
		and lv1_type_code='1607108726'
		and warehouse_type_code!='644734982'
	</select>
</mapper>
