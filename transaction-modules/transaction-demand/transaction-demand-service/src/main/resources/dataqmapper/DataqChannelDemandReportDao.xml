<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandReportDao">
    <select id="queryChannelDemandReportHeadList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo"
            resultType="java.lang.String">
        select distinct biz_date_value from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                tdm_xqyc_txn_sku_reporting_di_${tableSuffix}
            </when>
            <otherwise>
                tdm_xqyc_txn_sku_reporting_di
            </otherwise>
        </choose>
        <include refid="where_sql"/>
        order by biz_date_value
    </select>

    <select id="queryMaxRollingVersion"
            resultType="java.lang.String">
        select max(rolling_version) rolling_version from tdm_xqyc_txn_sku_reporting_di
    </select>

    <select id="queryChannelDemandReportHeadSelect"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportGroupListRspVo">
        select distinct ${groupColumn}
        from tdm_xqyc_txn_sku_reporting_di
        <include refid="where_sql"/>
        order by ${sortColumn}
    </select>

    <select id="queryChannelDemandReportGroupList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportGroupListRspVo">
        SELECT
        json_agg("data") AS "data",
        ${groupColumn}
        FROM (
        (
        SELECT json_build_object('order_num', SUM(order_num), 'biz_date_value', biz_date_value) AS "data"
        , ${groupColumn}
        FROM tdm_xqyc_txn_sku_reporting_di
        <include refid="where_sql"/>
        GROUP BY ${groupColumn}, biz_date_value
        )
        ) agg
        GROUP BY ${groupColumn}
        ORDER BY ${sortColumn}
    </select>



    <select id="queryChannelDemandReportDataKeyList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListRspVo">
    with P as (
        select
        distinct
        lv3_category_code,
        lv3_channel_code,
        sku_code
        from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                tdm_xqyc_txn_sku_reporting_di_${tableSuffix}
            </when>
            <otherwise>
                tdm_xqyc_txn_sku_reporting_di
            </otherwise>
        </choose>
        <include refid="where_sql"/>
        ORDER BY
        lv3_category_code,
        lv3_channel_code,
        sku_code
        )
        select lv3_category_code,
        lv3_channel_code,
        sku_code from P

    </select>

    <select id="queryChannelDemandReportDataJsonList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListRspVo">

        with P as (
        select  * from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                tdm_xqyc_txn_sku_reporting_di_${tableSuffix}
            </when>
            <otherwise>
                tdm_xqyc_txn_sku_reporting_di
            </otherwise>
        </choose>
        <include refid="where_sql"/>
        )

        SELECT
        json_agg(json_build_object('id', a.id::varchar,
        'remark',
        A.extend:: varchar, 'bizDateValue', a.biz_date_value::varchar, 'orderNum',
        a.order_num::varchar,'realityOrderNum',case when(b.outbound_num::varchar is null or b.outbound_num::varchar  ='')  then '0' else b.outbound_num end,'lastOrderNum',a.previous_order_num::varchar,'deviationRadio', case when a.previous_order_num=0 then (case when a.order_num=0 then 0 else 1
        end) else round((a.order_num-previous_order_num)/ a.previous_order_num,2) end)) AS "data",
        a.sku_code AS "skuCode",
        MAX(a.sku_name) AS "skuName",
        a.lv1_category_code AS "lv1CategoryCode",
        MAX(a.lv1_category_name) AS "lv1CategoryName",
        a.lv2_category_code AS "lv2CategoryCode",
        MAX(a.lv2_category_name) AS "lv2CategoryName",
        a.lv3_category_code AS "lv3CategoryCode",
        MAX(a.lv3_category_name) AS "lv3CategoryName",
        a.lv1_channel_code AS "lv1ChannelCode",
        MAX(a.lv1_channel_name) AS "lv1ChannelName",
        a.lv2_channel_code AS "lv2ChannelCode",
        MAX(a.lv2_channel_name) AS "lv2ChannelName",
        a.lv3_channel_code AS "lv3ChannelCode",
        MAX(a.lv3_channel_name) AS "lv3ChannelName"
        FROM  P a left join (select  outbound_num,lv3_channel_code, biz_date_value,sku_code,lv3_category_code from tdm_xqyc_txn_delivery_order_week_df where
        biz_date_value <![CDATA[>=]]> (
        select
        min(biz_date_value)
        from
        P )
        and biz_date_value <![CDATA[<=]]> (
        select
        max(biz_date_value)
        from
        P)
        and  dim_comb = 'LV3_CHANNEL_CODE+SKU+LV3_CATEGORY_CODE+WEEK' ) b on
        a.lv3_channel_code = b.lv3_channel_code
        and a.biz_date_value = b.biz_date_value
        and a.sku_code = b.sku_code
        and a.lv3_category_code = b.lv3_category_code
        GROUP BY a.lv1_category_code, a.lv2_category_code, a.lv3_category_code, a.sku_code, a.lv1_channel_code, a.lv2_channel_code,
        a.lv3_channel_code
        ORDER BY  a.lv1_channel_code, a.lv2_channel_code,
        a.lv3_channel_code,a.lv1_category_code, a.lv2_category_code, a.lv3_category_code, a.sku_code

        <!--        SELECT-->
<!--        json_agg(json_build_object('id', id::varchar, 'bizDateValue', biz_date_value::varchar, 'orderNum',-->
<!--        order_num::varchar,'lastOrderNum',previous_order_num::varchar,'deviationRadio', case when previous_order_num=0 then (case when order_num=0 then 0 else 1-->
<!--        end) else round((order_num-previous_order_num)/ previous_order_num,2) end)) AS "data",-->
<!--        sku_code AS "skuCode",-->
<!--        MAX(sku_name) AS "skuName",-->
<!--        lv1_category_code AS "lv1CategoryCode",-->
<!--        MAX(lv1_category_name) AS "lv1CategoryName",-->
<!--        lv2_category_code AS "lv2CategoryCode",-->
<!--        MAX(lv2_category_name) AS "lv2CategoryName",-->
<!--        lv3_category_code AS "lv3CategoryCode",-->
<!--        MAX(lv3_category_name) AS "lv3CategoryName",-->
<!--        lv1_channel_code AS "lv1ChannelCode",-->
<!--        MAX(lv1_channel_name) AS "lv1ChannelName",-->
<!--        lv2_channel_code AS "lv2ChannelCode",-->
<!--        MAX(lv2_channel_name) AS "lv2ChannelName",-->
<!--        lv3_channel_code AS "lv3ChannelCode",-->
<!--        MAX(lv3_channel_name) AS "lv3ChannelName"-->
<!--        FROM tdm_xqyc_txn_sku_reporting_di-->
<!--        <include refid="where_sql"/>-->
<!--        GROUP BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code,-->
<!--        lv3_channel_code-->
<!--        ORDER BY  lv1_channel_code, lv2_channel_code,-->
<!--        lv3_channel_code,lv1_category_code, lv2_category_code, lv3_category_code, sku_code-->
    </select>

    <select id="queryChannelDemandReportDataList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListRspVo">
        SELECT
        biz_date_type,
        biz_date_value,
        sku_code,
        sku_name,
        lv1_category_code,
        lv1_category_name,
        lv2_category_code,
        lv2_category_name,
        lv3_category_code,
        lv3_category_name,
        lv1_channel_code,
        lv1_channel_name,
        lv2_channel_code,
        lv2_channel_name,
        lv3_channel_code,
        lv3_channel_name,
        order_num,
        unit
        FROM tdm_xqyc_txn_sku_reporting_di
        <include refid="where_sql"/>
        ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code,
        lv3_channel_code
    </select>

    <select id="queryChannelDemandReportSummary"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo">
        SELECT
        biz_date_value,
        sum(order_num) as order_num
        FROM tdm_xqyc_txn_sku_reporting_di
        <include refid="where_sql"/>
        GROUP BY biz_date_value
    </select>

    <sql id="where_sql">
        WHERE 1 = 1
        <if test="rollingVersion != null and rollingVersion != ''">
            AND rolling_version = #{rollingVersion}
        </if>
        <if test="bizDateType != null">
            AND biz_date_type = #{bizDateType}
        </if>
        <if test="isModify != null">
            AND is_modify = #{isModify}
        </if>
        <if test="skuCodes != null and skuCodes != ''">
            AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
        </if>
        <if test="skuCode != null and skuCode != ''">
            AND sku_code = #{skuCode}
        </if>
        <if test="lv1ChannelCodes != null and lv1ChannelCodes != ''">
            AND lv1_channel_code = ANY(STRING_TO_ARRAY(#{lv1ChannelCodes},','))
        </if>
        <if test="lv1ChannelCode != null and lv1ChannelCode != ''">
            AND lv1_channel_code = #{lv1ChannelCode}
        </if>
        <if test="lv2ChannelCodes != null and lv2ChannelCodes != ''">
            AND lv2_channel_code = ANY(STRING_TO_ARRAY(#{lv2ChannelCodes},','))
        </if>
        <if test="lv2ChannelCode != null and lv2ChannelCode != ''">
            AND lv2_channel_code = #{lv2ChannelCode}
        </if>
        <if test="lv3ChannelCodes != null and lv3ChannelCodes != ''">
            AND lv3_channel_code = ANY(STRING_TO_ARRAY(#{lv3ChannelCodes},','))
        </if>
        <if test="lv3ChannelCode != null and lv3ChannelCode != ''">
            AND lv3_channel_code = #{lv3ChannelCode}
        </if>
        <if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
            AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
        </if>
        <if test="lv1CategoryCode != null and lv1CategoryCode != ''">
            AND lv1_category_code = #{lv1CategoryCode}
        </if>
        <if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
            AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
        </if>
        <if test="lv2CategoryCode != null and lv2CategoryCode != ''">
            AND lv2_category_code = #{lv2CategoryCode}
        </if>
        <if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
            AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
        </if>
        <if test="lv3CategoryCode != null and lv3CategoryCode != ''">
            AND lv3_category_code = #{lv3CategoryCode}
        </if>
        <if test="beginDate != null and endDate != null">
            AND biz_date_value &gt;= #{beginDate}
            AND biz_date_value &lt;= #{endDate}
        </if>
        <if test="deviationScore != null and deviationScore != ''">
            AND case when previous_order_num=0 then (case when order_num=0 then 0 else 1 end) else round(ABS(order_num-previous_order_num)/
            previous_order_num,2) end &gt;= #{deviationScore}
        </if>
        <if test="channelCodes != null and channelCodes != ''">
            AND (
                lv1_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
             OR lv2_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
             OR lv3_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
            )
        </if>
        <if test="categoryCodes != null and categoryCodes != ''">
            AND (
                lv1_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
             OR lv2_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
             OR lv3_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
            )
        </if>
        <if test="keyList != null and keyList.size() > 0">
            AND
            <foreach collection="keyList" item="item" open="(" close=")" separator="OR">
                (1=1
                <if test="item.skuCode != null and item.skuCode != ''">
                    AND sku_code = #{item.skuCode}
                </if>
                <if test="item.lv1ChannelCode != null and item.lv1ChannelCode != ''">
                    AND lv1_channel_code = #{item.lv1ChannelCode}
                </if>
                <if test="item.lv2ChannelCode != null and item.lv2ChannelCode != ''">
                    AND lv2_channel_code = #{item.lv2ChannelCode}
                </if>
                <if test="item.lv3ChannelCode != null and item.lv3ChannelCode != ''">
                    AND lv3_channel_code = #{item.lv3ChannelCode}
                </if>
                <if test="item.lv1CategoryCode != null and item.lv1CategoryCode != ''">
                    AND lv1_category_code = #{item.lv1CategoryCode}
                </if>
                <if test="item.lv2CategoryCode != null and item.lv2CategoryCode != ''">
                    AND lv2_category_code = #{item.lv2CategoryCode}
                </if>
                <if test="item.lv3CategoryCode != null and item.lv3CategoryCode != ''">
                    AND lv3_category_code = #{item.lv3CategoryCode}
                </if>
                )
            </foreach>
        </if>
    </sql>

    <select id="queryChannelDemandReportVersionExists" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from tdm_xqyc_txn_sku_reporting_di
        where rolling_version = #{rollingVersion}
    </select>

    <select id="queryChannelDemandReportVersionById" parameterType="java.lang.String" resultType="java.lang.String">
        select rolling_version
        from tdm_xqyc_txn_sku_reporting_di
        where id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <select id="queryChannelDemandReportVersionCount"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo"
            resultType="java.lang.Integer">
        select count(1) from tdm_xqyc_txn_sku_reporting_di
        <include refid="where_sql"/>
    </select>

    <select id="queryDuplicateOptimize" resultType="java.util.Map">
        SELECT
            sku_code AS SKU_CODE,
            MAX(sku_name) AS SKU_NAME,
            ${dynamicsSql}
            lv1_category_code AS LV1_CATEGORY_CODE,
            lv2_category_code AS LV2_CATEGORY_CODE,
            lv3_category_code AS LV3_CATEGORY_CODE,
            lv1_channel_code AS LV1_CHANNEL_CODE,
            lv2_channel_code AS LV2_CHANNEL_CODE,
            lv3_channel_code AS LV3_CHANNEL_CODE
            FROM
                 <choose>
                    <when test="tableSuffix != null and tableSuffix != ''">
                        tdm_xqyc_txn_sku_reporting_di_${tableSuffix}
                    </when>
                    <otherwise>
                        tdm_xqyc_txn_sku_reporting_di
                    </otherwise>
                </choose>
            WHERE rolling_version = #{rollingVersion}
                AND is_modify = 0
                <if test="bizDateType != null">
                    AND biz_date_type = #{bizDateType}
                </if>
                <if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
                    AND (lv1_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},',')) or lv2_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},',')) or lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},',')) )
                </if>
                <if test="skuCodes != null and skuCodes != ''">
                    AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
                </if>
            GROUP BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code,
            lv3_channel_code
    </select>

    <select id="queryDuplicate" resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportDto">
        SELECT id,biz_date_type, biz_date_value, rolling_version, sku_code, lv1_category_code,
        lv2_category_code, lv3_category_code, lv1_channel_code,
        lv2_channel_code, lv3_channel_code, order_num, unit,previous_order_num
        FROM
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                tdm_xqyc_txn_sku_reporting_di_${tableSuffix}
            </when>
            <otherwise>
                tdm_xqyc_txn_sku_reporting_di
            </otherwise>
        </choose>
        WHERE rolling_version = #{rollingVersion}
        AND is_modify = 0
        <if test="bizDateType != null">
            AND biz_date_type = #{bizDateType}
        </if>
        <if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
            AND (lv1_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},',')) or lv2_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},',')) or lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},',')) )
        </if>
        <if test="skuCodes != null and skuCodes != ''">
            AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
        </if>
        <!--        AND-->
        <!--        <foreach item="item" collection="items" open="(" separator=" OR " close=")">-->
        <!--            (-->
        <!--            lv1_category_code = #{item.lv1CategoryCode}-->
        <!--            AND lv2_category_code = #{item.lv2CategoryCode}-->
        <!--            AND lv3_category_code = #{item.lv3CategoryCode}-->
        <!--            AND lv1_channel_code = #{item.lv1ChannelCode}-->
        <!--            AND lv2_channel_code = #{item.lv2ChannelCode}-->
        <!--            AND lv3_channel_code = #{item.lv3ChannelCode}-->
        <!--            AND biz_date_value = #{item.bizDateValue}-->
        <!--            )-->
        <!--        </foreach>-->
    </select>

    <update id="batchUpdate">
        UPDATE
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                tdm_xqyc_txn_sku_reporting_di_${tableSuffix}
            </when>
            <otherwise>
                tdm_xqyc_txn_sku_reporting_di
            </otherwise>
        </choose>
        <set>
            <if test="channelDemandReportDto.lastModifier != null">
                last_modifier = #{channelDemandReportDto.lastModifier},
            </if>
            <if test="channelDemandReportDto.gmtModify != null">
                gmt_modify = #{channelDemandReportDto.gmtModify},
            </if>
            <if test="channelDemandReportDto.isModify != null">
                is_modify = #{channelDemandReportDto.isModify},
            </if>
        </set>
        WHERE id IN
        <foreach item="idItem" collection="ids" open="(" separator="," close=")">
            #{idItem}
        </foreach>
    </update>

    <select id="getSeq" resultType="java.lang.Long">
        SELECT setval('tdm_xqyc_txn_sku_reporting_di_id_seq',
                      (SELECT last_value FROM tdm_xqyc_txn_sku_reporting_di_id_seq) + #{size}) AS id;
    </select>

    <update id="createChannelDemandReportPartitionTable" parameterType="java.util.List">
        <foreach collection="partitionList" item="item" separator=";">
            create table if not exists tdm_xqyc_txn_sku_reporting_di_${item} (like tdm_xqyc_txn_sku_reporting_di including all, CHECK (substr(rolling_version,4,6)='${item}')) inherits
            (tdm_xqyc_txn_sku_reporting_di);
            CREATE OR REPLACE RULE tdm_xqyc_txn_sku_reporting_di_${item} AS
            ON INSERT TO tdm_xqyc_txn_sku_reporting_di WHERE (substr(rolling_version,4,6)='${item}')
            DO INSTEAD INSERT INTO tdm_xqyc_txn_sku_reporting_di_${item} VALUES (NEW.*);
        </foreach>
    </update>

    <insert id="createChannelDemandReportTemplateData" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportTemplateDataDto">
        insert into tdm_xqyc_txn_sku_reporting_di (creator,last_modifier,biz_date_type,biz_date_value,rolling_version,sku_code,sku_name,lv1_category_code,lv1_category_name,lv2_category_code,lv2_category_name,lv3_category_code,lv3_category_name,lv1_channel_code,lv1_channel_name,lv2_channel_code,lv2_channel_name,lv3_channel_code,lv3_channel_name,order_num,unit,extend,is_modify,is_locked,previous_order_num,deviation_score)
        SELECT  '${creator}' AS creator
        ,'${creator}' AS last_modifier
        ,'WEEK' AS biz_date_type
        ,d.biz_date_value AS biz_date_value
        ,'${rollingVersion}' AS rolling_version
        ,sr.sku_code AS sku_code
        ,sr.sku_name AS sku_name
        ,sr.lv1_category_code AS lv1_category_code
        ,sr.lv1_category_name AS lv1_category_name
        ,sr.lv2_category_code AS lv2_category_code
        ,sr.lv2_category_name AS lv2_category_name
        ,sr.lv3_category_code AS lv3_category_code
        ,sr.lv3_category_name AS lv3_category_name
        ,sr.lv1_channel_code AS lv1_channel_code
        ,sr.lv1_channel_name AS lv1_channel_name
        ,sr.lv2_channel_code AS lv2_channel_code
        ,sr.lv2_channel_name AS lv2_channel_name
        ,sr.lv3_channel_code AS lv3_channel_code
        ,sr.lv3_channel_name AS lv3_channel_name
        ,CASE   WHEN tsr.order_num IS NOT NULL THEN tsr.order_num
        ELSE 0
        END AS order_num
        ,sr.unit AS unit
        ,NULL AS extend
        ,0 AS is_modify
        ,0 AS is_locked
        ,CASE   WHEN tsr.order_num IS NOT NULL THEN tsr.order_num
        ELSE 0
        END AS previous_order_num
        ,0 AS deviation_score
        FROM    (
        SELECT  a.sku_code
        ,a.sku_name
        ,a.lv1_category_code
        ,a.lv1_category_name
        ,a.lv2_category_code
        ,a.lv2_category_name
        ,a.lv3_category_code
        ,a.lv3_category_name
        ,a.lv1_channel_code
        ,a.lv1_channel_name
        ,a.lv2_channel_code
        ,a.lv2_channel_name
        ,a.lv3_channel_code
        ,a.lv3_channel_name
        ,a.unit
        FROM   (

        select * from cdop_biz.tdm_rltn_sku_reseller_df where (lv1_category_name in ('常温白奶','常温酸奶','常温调制乳','儿童奶')
        and lv1_channel_name in ('社区生鲜运营中心','新零售运营中心','创新电商运营中心','用户认养部','品牌电商运营中心','餐饮运营部','奶卡提奶'))
        union all
        select *  from cdop_biz.tdm_rltn_sku_reseller_df where lv1_channel_name = '供应链中心' and  lv2_channel_name  = '生产计划部'
        and lv1_category_name in ('常温白奶','常温酸奶','常温调制乳','儿童奶')
        union all
        select * from cdop_biz.tdm_rltn_sku_reseller_df where lv1_channel_name = '奶粉事业部' and lv2_channel_name  = '新零售母婴运营部'
        and lv1_category_name  = '儿童奶'
        ) a
        ) sr -- 奶卡
        CROSS JOIN  (
        SELECT  DISTINCT LEFT(fscl_week_range,8) AS biz_date_value
        FROM    cdop_biz.dim_bas_calendar_df
        WHERE   cal_date <![CDATA[>=]]> '${beginDate}'
        AND     cal_date <![CDATA[<=]]> '${endDate}'
        ) d
        LEFT JOIN   (
        SELECT  *
        FROM    cdop_biz.tdm_xqyc_txn_sku_reporting_di
        WHERE   id IN (
        SELECT  MAX(id) AS id
        FROM    cdop_biz.tdm_xqyc_txn_sku_reporting_di
        WHERE   biz_date_value <![CDATA[>=]]>  '${beginDate}'
        AND     biz_date_value <![CDATA[<=]]>  '${endDate}'
        AND     rolling_version = '${lastRollingVersion}'
        AND     is_modify = 0
        GROUP BY biz_date_type
        ,biz_date_value
        ,sku_code
        ,lv1_category_code
        ,lv2_category_code
        ,lv3_category_code
        ,lv1_channel_code
        ,lv2_channel_code
        ,lv3_channel_code
        )
        ) tsr
        ON      sr.sku_code = tsr.sku_code
        AND     sr.lv1_channel_code = tsr.lv1_channel_code
        AND     sr.lv2_channel_code = tsr.lv2_channel_code
        AND     sr.lv3_channel_code = tsr.lv3_channel_code
        AND     d.biz_date_value = tsr.biz_date_value
    </insert>
</mapper>
