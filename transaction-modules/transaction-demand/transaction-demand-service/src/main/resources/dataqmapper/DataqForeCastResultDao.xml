<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqForecastResultDao">
	<select id="queryChannelForecastResultHeadList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo"
            resultType="java.lang.String">
    	select distinct target_biz_date
    	from tdm_xqyc_txn_forcast_order_di
    	<include refid="channelForecastResult_where_sql"/>
    	order by target_biz_date
    </select>

	<select id="queryChannelForecastResultHeadSelect" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastResultRspVo">
		select distinct ${groupColumn}
		from tdm_xqyc_txn_forcast_order_di
    	<include refid="channelForecastResult_where_sql"/>
		order by ${sortColumn}
	</select>

	<select id="queryChannelForecastResultGroupBy" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastResultRspVo">
		SELECT
        json_agg("data") AS "data",
        ${groupColumn}
        FROM (
        (
        SELECT json_build_object('plan_value', SUM(prediction_result), 'plan_date', target_biz_date) AS "data"
        , ${groupColumn}
        FROM tdm_xqyc_txn_forcast_order_di
        <include refid="channelForecastResult_where_sql"/>
        GROUP BY ${groupColumn}, target_biz_date
        )
        ) agg
        GROUP BY ${groupColumn}
        ORDER BY ${sortColumn}
	</select>

    <select id="queryChannelForecastResultDataKeyList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastResultRspVo">
    	select distinct
    		lv1_category_code,
    		lv2_category_code,
    		lv3_category_code,
			lv1_channel_code,
			lv2_channel_code
    	from tdm_xqyc_txn_forcast_order_di
    	<include refid="channelForecastResult_where_sql"/>
    	ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, lv1_channel_code, lv2_channel_code
    </select>

    <select id="queryChannelForecastResultDataJsonList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastResultRspVo">
    	SELECT
    		json_agg(json_build_object('plan_date', target_biz_date::varchar, 'plan_value', prediction_result::varchar)) AS "data",
    		lv1_category_code AS "lv1CategoryCode",
    		MAX(lv1_category_name) AS "lv1CategoryName",
    		lv2_category_code AS "lv2CategoryCode",
    		MAX(lv2_category_name) AS "lv2CategoryName",
    		lv3_category_code AS "lv3CategoryCode",
    		MAX(lv3_category_name) AS "lv3CategoryName",
			lv1_channel_code AS "lv1ChannelCode",
			MAX(lv1_channel_name) AS "lv1ChannelName",
			lv2_channel_code AS "lv2ChannelCode",
			MAX(lv2_channel_name) AS "lv2ChannelName"
    	FROM tdm_xqyc_txn_forcast_order_di
    	<include refid="channelForecastResult_where_sql"/>
    	GROUP BY lv1_category_code, lv2_category_code, lv3_category_code, lv1_channel_code, lv2_channel_code
    	ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, lv1_channel_code, lv2_channel_code
    </select>

    <sql id="channelForecastResult_where_sql">
    	WHERE 1 = 1
		<if test="algoNameAndVersion !=null and algoNameAndVersion != ''">
    		AND algo_name_and_version = #{algoNameAndVersion}
		</if>
		<if test="predictionVersion != null and predictionVersion != ''">
    		AND prediction_version = #{predictionVersion}
		</if>
    	<if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
    		AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
    	</if>
    	<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
    		AND lv1_category_code = #{lv1CategoryCode}
    	</if>
    	<if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
    		AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
    	</if>
    	<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
    		AND lv2_category_code = #{lv2CategoryCode}
    	</if>
    	<if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
    		AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
    	</if>
    	<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
    		AND lv3_category_code = #{lv3CategoryCode}
    	</if>
    	<if test="lv1ChannelCodes != null and lv1ChannelCodes != ''">
    		AND lv1_channel_code = ANY(STRING_TO_ARRAY(#{lv1ChannelCodes},','))
    	</if>
    	<if test="lv1ChannelCode != null and lv1ChannelCode != ''">
    		AND lv1_channel_code = #{lv1ChannelCode}
    	</if>
    	<if test="lv2ChannelCodes != null and lv2ChannelCodes != ''">
    		AND lv2_channel_code = ANY(STRING_TO_ARRAY(#{lv2ChannelCodes},','))
    	</if>
    	<if test="lv2ChannelCode != null and lv2ChannelCode != ''">
    		AND lv2_channel_code = #{lv2ChannelCode}
    	</if>
		<if test="startDate != null and startDate != ''">
			AND target_biz_date &gt;= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND target_biz_date &lt;= #{endDate}
		</if>
		<if test="periodType !=null and periodType != ''">
			AND period_type = #{periodType}
		</if>
    	<if test="keyList != null and keyList.size() > 0">
    		AND
    		<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
    			(1=1
    			<if test="item.lv1CategoryCode != null and item.lv1CategoryCode != ''">
    				AND lv1_category_code = #{item.lv1CategoryCode}
    			</if>
    			<if test="item.lv2CategoryCode != null and item.lv2CategoryCode != ''">
    				AND lv2_category_code = #{item.lv2CategoryCode}
    			</if>
    			<if test="item.lv3CategoryCode != null and item.lv3CategoryCode != ''">
    				AND lv3_category_code = #{item.lv3CategoryCode}
    			</if>
				<if test="lv1ChannelCode != null and lv1ChannelCode != ''">
					AND lv1_channel_code = #{lv1ChannelCode}
				</if>
				<if test="lv2ChannelCode != null and lv2ChannelCode != ''">
					AND lv2_channel_code = #{lv2ChannelCode}
				</if>
    			)
    		</foreach>
    	</if>
    </sql>

	<select id="queryWarehouseForecastResultHeadList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo"
            resultType="java.lang.String">
    	select distinct target_biz_date
    	from tdm_xqyc_txn_forcast_warehouse_di
    	<include refid="where_sql"/>
    	order by target_biz_date
    </select>

	<select id="queryWarehouseForecastResultHeadSelect" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseForecastResultRspVo">
		select distinct ${groupColumn}
		from tdm_xqyc_txn_forcast_warehouse_di
    	<include refid="where_sql"/>
		order by ${sortColumn}
	</select>

    <select id="queryWarehouseForecastResultDataKeyList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseForecastResultRspVo">
    	select distinct
    		lv1_category_code,
    		lv2_category_code,
    		lv3_category_code,
    		sku_code,
    		receiver_type,
    		warehouse_code
    	from tdm_xqyc_txn_forcast_warehouse_di
    	<include refid="where_sql"/>
    	ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, receiver_type, warehouse_code
    </select>

    <select id="queryWarehouseForecastResultDataJsonList"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseForecastResultRspVo">
    	SELECT
    		json_agg(json_build_object('plan_date', target_biz_date::varchar, 'plan_value', prediction_result::varchar)) AS "data",
    		sku_code AS "skuCode",
    		MAX(sku_name) AS "skuName",
    		lv1_category_code AS "lv1CategoryCode",
    		MAX(lv1_category_name) AS "lv1CategoryName",
    		lv2_category_code AS "lv2CategoryCode",
    		MAX(lv2_category_name) AS "lv2CategoryName",
    		lv3_category_code AS "lv3CategoryCode",
    		MAX(lv3_category_name) AS "lv3CategoryName",
    		receiver_type AS "receiverType",
    		warehouse_code AS "warehouseCode",
    		MAX(warehouse_name) AS "warehouseName"
    	FROM tdm_xqyc_txn_forcast_warehouse_di
    	<include refid="where_sql"/>
    	GROUP BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code,  receiver_type, warehouse_code, warehouse_name
    	ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, receiver_type, warehouse_code, warehouse_name
    </select>

	<select id="queryExportWarehouseForecastResultList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.ExportWarehouseForecastResultRspVo">
		SELECT
		sku_code,
		sku_name,
		warehouse_name,
		target_biz_date,
		prediction_result,
		lv1_category_name,
		lv2_category_name,
		lv3_category_name,
		receiver_type
		FROM tdm_xqyc_txn_forcast_warehouse_di
    	<include refid="where_sql"/>
    	ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, receiver_type, warehouse_code, warehouse_name, target_biz_date
	</select>

    <sql id="where_sql">
    	WHERE 1 = 1
    		AND algo_name_and_version = #{algoNameAndVersion}
    		AND prediction_version = #{predictionVersion}
    	<if test="skuCodes != null and skuCodes != ''">
    		AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
    	</if>
    	<if test="skuCode != null and skuCode != ''">
    		AND sku_code = #{skuCode}
    	</if>
    	<if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
    		AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
    	</if>
    	<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
    		AND lv1_category_code = #{lv1CategoryCode}
    	</if>
    	<if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
    		AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
    	</if>
    	<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
    		AND lv2_category_code = #{lv2CategoryCode}
    	</if>
    	<if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
    		AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
    	</if>
    	<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
    		AND lv3_category_code = #{lv3CategoryCode}
    	</if>
    	<if test="receiverType != null and receiverType != ''">
    		and lower(receiver_type) = lower(#{receiverType})
    	</if>
		<if test="receiverTypes != null and receiverTypes != ''">
    		and lower(receiver_type) = ANY(STRING_TO_ARRAY(lower(#{receiverTypes}),','))
    	</if>
    	<if test="warehouseCodes != null and warehouseCodes != ''">
    		and warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
    	</if>
    	<if test="warehouseCode != null and warehouseCode != ''">
    		and warehouse_code = #{warehouseCode}
    	</if>
		<if test="targetBizDate != null and targetBizDate != ''">
			and target_biz_date = #{targetBizDate}
		</if>
    	<if test="keyList != null and keyList.size() > 0">
    		AND
    		<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
    			(1=1
    			<if test="item.skuCode != null and item.skuCode != ''">
    				AND sku_code = #{item.skuCode}
    			</if>
    			<if test="item.lv1CategoryCode != null and item.lv1CategoryCode != ''">
    				AND lv1_category_code = #{item.lv1CategoryCode}
    			</if>
    			<if test="item.lv2CategoryCode != null and item.lv2CategoryCode != ''">
    				AND lv2_category_code = #{item.lv2CategoryCode}
    			</if>
    			<if test="item.lv3CategoryCode != null and item.lv3CategoryCode != ''">
    				AND lv3_category_code = #{item.lv3CategoryCode}
    			</if>
				<if test="item.receiverType != null and item.receiverType != ''">
					and lower(receiver_type) = lower(#{item.receiverType})
				</if>
				<if test="item.warehouseCode != null and item.warehouseCode != ''">
					and warehouse_code = #{item.warehouseCode}
				</if>
    			)
    		</foreach>
    	</if>
    </sql>

	<select id="queryWarehouseForecastResultGroupByWarehouse"
            parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseForecastResultRspVo">
		SELECT sum(prediction_result) AS "predictionResult", warehouse_code, warehouse_name
		FROM tdm_xqyc_txn_forcast_warehouse_di
    	<include refid="where_sql"/>
		group by warehouse_code,warehouse_name
	</select>

	<select id="queryDeviationChannelForecastResultList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationListRspVo">
		select
			substring(target_biz_date,0,7) as target_biz_date,
			prediction_version,
			sum(prediction_result) as "predictionResult"
		from
			tdm_xqyc_txn_forcast_order_di
    	<include refid="channelForecastResult_where_sql"/>
		group by target_biz_date, prediction_version
	</select>

	<select id="queryExportChannelForecastResultList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.ExportChannelForecastResultRspVo">
		select
			target_biz_date as "targetBizDate",
			prediction_version as "predictionVersion",
			prediction_result as "predictionResult",
			lv1_category_code as "lv1CategoryCode",
			lv1_category_name as "lv1CategoryName",
			lv2_category_code as "lv2CategoryCode",
			lv2_category_name as "lv2CategoryName",
			lv3_category_code as "lv3CategoryCode",
			lv3_category_name as "lv3CategoryName",
			lv1_channel_code as "lv1ChannelCode",
			lv1_channel_name as "lv1ChannelName",
			lv2_channel_code as "lv2ChannelCode",
			lv2_channel_name as "lv2ChannelName",
			data_unit as "dataUnit"
		from
			tdm_xqyc_txn_forcast_order_di
    	<include refid="channelForecastResult_where_sql"/>
	</select>
</mapper>
