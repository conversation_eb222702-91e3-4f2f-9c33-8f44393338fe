<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqSaleTargetDao">
	<select id="querySaleTargetHeadList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetReqVo"
            resultType="java.lang.String">
		select distinct biz_date_value from tdm_xqyc_txn_sku_sales_plan_di
		<include refid="where_sql"/>
		order by biz_date_value
	</select>

	<select id="querySaleTargetHeadSelect" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetRspVo">
		select distinct ${groupColumn}
		from tdm_xqyc_txn_sku_sales_plan_di
		<include refid="where_sql"/>
		order by ${sortColumn}
	</select>

	<select id="querySaleTargetGroupList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetRspVo">
		SELECT
		json_agg("data") AS "data",
		${groupColumn}
		FROM (
			(
			SELECT json_build_object('order_num', SUM(order_num), 'biz_date_value', biz_date_value) AS "data"
				, ${groupColumn}
			FROM tdm_xqyc_txn_sku_sales_plan_di
			<include refid="where_sql"/>
			GROUP BY ${groupColumn}, biz_date_value
			)
		) agg
		GROUP BY ${groupColumn}
		ORDER BY ${sortColumn}
	</select>
	
	<select id="querySaleTargetDataKeyList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetRspVo">
		select distinct
			lv1_category_code,
			lv2_category_code,
			lv3_category_code,
			sku_code,
			lv1_channel_code,
			lv2_channel_code,
			lv3_channel_code
		from tdm_xqyc_txn_sku_sales_plan_di
		<include refid="where_sql"/>
		ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code, lv3_channel_code
	</select>

	<select id="querySaleTargetDataJsonList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetRspVo">
		SELECT
			json_agg(json_build_object('bizDateValue', biz_date_value::varchar, 'orderNum', order_num::varchar)) AS "data",
			sku_code AS "skuCode",
			MAX(sku_name) AS "skuName",
			lv1_category_code AS "lv1CategoryCode",
			MAX(lv1_category_name) AS "lv1CategoryName",
			lv2_category_code AS "lv2CategoryCode",
			MAX(lv2_category_name) AS "lv2CategoryName",
			lv3_category_code AS "lv3CategoryCode",
			MAX(lv3_category_name) AS "lv3CategoryName",
			lv1_channel_code AS "lv1ChannelCode",
			MAX(lv1_channel_name) AS "lv1ChannelName",
			lv2_channel_code AS "lv2ChannelCode",
			MAX(lv2_channel_name) AS "lv2ChannelName",
			lv3_channel_code AS "lv3ChannelCode",
			MAX(lv3_channel_name) AS "lv3ChannelName",
			MAX(unit) as "unit"
		FROM tdm_xqyc_txn_sku_sales_plan_di
		<include refid="where_sql"/>
		GROUP BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code, lv3_channel_code
		ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code, lv3_channel_code
	</select>

	<select id="querySaleTargetSummary" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo">
		SELECT
		biz_date_value,
		sum(order_num) as order_num
		FROM tdm_xqyc_txn_sku_sales_plan_di
		<include refid="where_sql"/>
		GROUP BY biz_date_value
	</select>

	<sql id="where_sql">
		WHERE 1 = 1
			AND rolling_version = #{fsclYear}
			AND biz_date_type = #{bizDateType}
		<if test="skuCodes != null and skuCodes != ''">
			AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="skuCode != null and skuCode != ''">
			AND sku_code = #{skuCode}
		</if>
		<if test="lv1ChannelCodes != null and lv1ChannelCodes != ''">
			AND lv1_channel_code = ANY(STRING_TO_ARRAY(#{lv1ChannelCodes},','))
		</if>
		<if test="lv1ChannelCode != null and lv1ChannelCode != ''">
			AND lv1_channel_code = #{lv1ChannelCode}
		</if>
		<if test="lv2ChannelCodes != null and lv2ChannelCodes != ''">
			AND lv2_channel_code = ANY(STRING_TO_ARRAY(#{lv2ChannelCodes},','))
		</if>
		<if test="lv2ChannelCode != null and lv2ChannelCode != ''">
			AND lv2_channel_code = #{lv2ChannelCode}
		</if>
		<if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
			AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
		</if>
		<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
			AND lv1_category_code = #{lv1CategoryCode}
		</if>
		<if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
			AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
		</if>
		<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
			AND lv2_category_code = #{lv2CategoryCode}
		</if>
		<if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
			AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
		</if>
		<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
			AND lv3_category_code = #{lv3CategoryCode}
		</if>
		<if test="startDate != null and endDate != null">
			AND biz_date_value &gt;= #{startDate}
			AND biz_date_value &lt;= #{endDate}
		</if>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
				<if test="item.skuCode != null and item.skuCode != ''">
					AND sku_code = #{item.skuCode}
				</if>
				<if test="item.lv1ChannelCode != null and item.lv1ChannelCode != ''">
					AND lv1_channel_code = #{item.lv1ChannelCode}
				</if>
				<if test="item.lv2ChannelCode != null and item.lv2ChannelCode != ''">
					AND lv2_channel_code = #{item.lv2ChannelCode}
				</if>
				<if test="item.lv3ChannelCode != null and item.lv3ChannelCode != ''">
					AND lv3_channel_code = #{item.lv3ChannelCode}
				</if>
				<if test="item.lv1CategoryCode != null and item.lv1CategoryCode != ''">
					AND lv1_category_code = #{item.lv1CategoryCode}
				</if>
				<if test="item.lv2CategoryCode != null and item.lv2CategoryCode != ''">
					AND lv2_category_code = #{item.lv2CategoryCode}
				</if>
				<if test="item.lv3CategoryCode != null and item.lv3CategoryCode != ''">
					AND lv3_category_code = #{item.lv3CategoryCode}
				</if>
				)
			</foreach>
		</if>
	</sql>
</mapper>
