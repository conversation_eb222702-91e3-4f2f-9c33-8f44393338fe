<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqMarketActivityResellerDao">

    <select id="queryMarketChannelList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketChannelListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketChannelListRspVo" >
        SELECT id, acti_code, acti_name, reseller_code, reseller_name
        , lv1_channel_code, lv1_channel_name, lv2_channel_code, lv2_channel_name, lv3_channel_code
        , lv3_channel_name, acti_reseller_obj, deleted
        FROM tdm_xqyc_txn_market_activity_reseller_di
        <include refid="where_sql"/>
    </select>

    <select id="selectResellerCodesByActiCode" resultType="java.lang.String" >
        SELECT reseller_code FROM tdm_xqyc_txn_market_activity_reseller_di where acti_code = #{actiCode}
    </select>



    <update id="updateByActiCode" parameterType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityResellerDO">
        UPDATE tdm_xqyc_txn_market_activity_reseller_di
        <trim prefix="SET" suffixOverrides=",">
            <if test="actiName != null">acti_name = #{actiName},</if>
            <if test="resellerCode != null">reseller_code = #{resellerCode},</if>
            <if test="resellerName != null">reseller_name = #{resellerName},</if>
            <if test="lv1ChannelCode != null">lv1_channel_code = #{lv1ChannelCode},</if>
            <if test="lv1ChannelName != null">lv1_channel_name = #{lv1ChannelName},</if>
            <if test="lv2ChannelCode != null">lv2_channel_code = #{lv2ChannelCode},</if>
            <if test="lv2ChannelName != null">lv2_channel_name = #{lv2ChannelName},</if>
            <if test="lv3ChannelCode != null">lv3_channel_code = #{lv3ChannelCode},</if>
            <if test="lv3ChannelName != null">lv3_channel_name = #{lv3ChannelName},</if>
            <if test="actiResellerObj != null">acti_reseller_obj = #{actiResellerObj},</if>
            <if test="deleted != null">deleted = #{deleted}</if>
        </trim>
        WHERE acti_code = #{actiCode}
    </update>

    <update id="batchUpdateByActiCodes" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE tdm_xqyc_txn_market_activity_reseller_di
            <trim prefix="set" suffixOverrides=",">
                <if test="item.deleted != null">deleted = #{item.deleted}</if>
            </trim>
        where acti_code = #{item.actiCode} and reseller_code = #{item.resellerCode}
    </foreach>
    </update>

    <insert id="batchInsert">
        INSERT INTO tdm_xqyc_txn_market_activity_reseller_di
        (acti_code, acti_name, reseller_code, reseller_name, lv1_channel_code, lv1_channel_name, lv2_channel_code,
        lv2_channel_name, lv3_channel_code, lv3_channel_name, acti_reseller_obj)
        VALUES
        <foreach collection="collection" separator="," item="item">
            (
            #{item.actiCode},
            #{item.actiName},
            #{item.resellerCode},
            #{item.resellerName},
            #{item.lv1ChannelCode},
            #{item.lv1ChannelName},
            #{item.lv2ChannelCode},
            #{item.lv2ChannelName},
            #{item.lv3ChannelCode},
            #{item.lv3ChannelName},
            #{item.actiResellerObj}
            )
        </foreach>
    </insert>






    <sql id="where_sql">
        WHERE 1 = 1
            <if test="actiCode != null and actiCode != ''">
                AND acti_code = #{actiCode}
            </if>
            <choose>
                <when test="deleted != null and deleted != ''">
                    AND deleted = #{deleted}
                </when>
                <otherwise>
                    AND deleted = 0
                </otherwise>
            </choose>
    </sql>




</mapper>