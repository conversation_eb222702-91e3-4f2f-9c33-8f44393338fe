<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqMarketActivitySkuDao">

    <select id="queryMarketActivitySkuList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketSkuListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.MarketActivitySkuVo" >
        SELECT id, acti_code, acti_name, sku_code, sku_name
        , acti_nums_limit, acti_sku_obj, deleted,acti_sku_begin_time,acti_sku_delivery_begin_time,acti_sku_delivery_nums_limit,acti_sku_week_begin_time
        FROM tdm_xqyc_txn_market_activity_sku_di
        <include refid="where_sql"/>
    </select>

    <select id="selectIdsByActiCode" resultType="java.lang.Long" >
        SELECT id FROM tdm_xqyc_txn_market_activity_sku_di where acti_code = #{actiCode}
    </select>


    <sql id="where_sql">
        WHERE 1 = 1
            <if test="actiCode != null and actiCode != ''">
                AND acti_code = #{actiCode}
            </if>
            <choose>
                <when test="deleted != null and deleted != ''">
                    AND deleted = #{deleted}
                </when>
                <otherwise>
                    AND deleted = 0
                </otherwise>
            </choose>
    </sql>


    <update id="updateByActiCode" parameterType="cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivitySkuDO">
        UPDATE tdm_xqyc_txn_market_activity_sku_di
        <trim prefix="SET" suffixOverrides=",">
            <if test="actiName != null">acti_name = #{actiName},</if>
            <if test="skuCode != null">sku_Code = #{skuCode},</if>
            <if test="skuName != null">sku_Name = #{skuName},</if>
            <if test="actiNumsLimit != null">acti_nums_limit = #{actiNumsLimit},</if>
            <if test="actiSkuObj != null">acti_sku_obj = #{actiSkuObj},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            <if test="actiSkuBeginTime != null">acti_sku_begin_time = #{actiSkuBeginTime},</if>
            <if test="actiSkuDeliveryBeginTime != null">acti_sku_delivery_begin_time = #{actiSkuDeliveryBeginTime},</if>
            <if test="actiSkuDeliveryNumsLimit != null">acti_sku_delivery_nums_limit = #{actiSkuDeliveryNumsLimit},</if>
            <if test="actiSkuWeekBeginTime != null">acti_sku_week_begin_time = #{actiSkuWeekBeginTime}</if>
        </trim>
        WHERE acti_code = #{actiCode}
    </update>


    <update id="batchUpdateMarketActivitySku" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tdm_xqyc_txn_market_activity_sku_di
            <trim prefix="set" suffixOverrides=",">
                <if test="item.actiNumsLimit != null">acti_nums_limit = #{item.actiNumsLimit},</if>
                <if test="item.actiSkuBeginTime != null">acti_sku_begin_time = #{item.actiSkuBeginTime},</if>
                <if test="item.actiSkuDeliveryBeginTime != null">acti_sku_delivery_begin_time = #{item.actiSkuDeliveryBeginTime},</if>
                <if test="item.actiSkuDeliveryNumsLimit != null">acti_sku_delivery_nums_limit = #{item.actiSkuDeliveryNumsLimit},</if>
                <if test="item.actiSkuWeekBeginTime != null">acti_sku_week_begin_time = #{item.actiSkuWeekBeginTime},</if>
                <if test="item.deleted != null">deleted = #{item.deleted}</if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>


    <update id="batchUpdateByIds">
        UPDATE tdm_xqyc_txn_market_activity_sku_di
        <trim prefix="SET" suffixOverrides=",">
            <if test="deleted != null">deleted = #{deleted}</if>
        </trim>
        WHERE id IN
        <foreach item="idItem" collection="ids" open="(" separator="," close=")">
            #{idItem}
        </foreach>
    </update>


    <insert id="batchInsert">
        INSERT INTO tdm_xqyc_txn_market_activity_sku_di
        (acti_code, acti_name, sku_code, sku_name, acti_nums_limit, acti_sku_obj,acti_sku_begin_time
        ,acti_sku_delivery_begin_time,acti_sku_delivery_nums_limit,acti_sku_week_begin_time)
        VALUES
        <foreach collection="collection" separator="," item="item">
            (#{item.actiCode},
            #{item.actiName},
            #{item.skuCode},
            #{item.skuName},
            #{item.actiNumsLimit},
            #{item.actiSkuObj},
            #{item.actiSkuBeginTime},
            #{item.actiSkuDeliveryBeginTime},
            #{item.actiSkuDeliveryNumsLimit},
            #{item.actiSkuWeekBeginTime}
            )
        </foreach>
    </insert>

</mapper>