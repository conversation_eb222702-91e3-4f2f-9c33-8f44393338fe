<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dataqdao.DataqConstantConfigDao">

    <select id="queryMarketConstantConfigList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketConstantReqVo"
            resultType="java.util.HashMap" >
        SELECT constant_code, constant_name, constant_type, extend
        FROM dim_xqyc_txn_constant_config
        <include refid="where_sql"/>
    </select>

    <sql id="where_sql">
        WHERE 1 = 1
            <if test="constantType != null and constantType != ''">
                AND constant_type = #{constantType}
            </if>
            <if test="constantTypes != null and constantTypes != ''">
                AND constant_type = ANY(STRING_TO_ARRAY(#{constantTypes},','))
            </if>
    </sql>

</mapper>