<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dao.FreightVersionDao">
    <delete id="deleteTempTable">
        delete from cdop_sys.t_ryytn_freight_version_list_third
    </delete>

    <delete id="deleteVersionTable">
        delete from cdop_sys.t_ryytn_freight_version_list
    </delete>
    <insert id="insertTempTable">
          insert into  cdop_sys.t_ryytn_freight_version_list_third(prediction_version,demand_plan_code,create_time)
         select distinct prediction_version,demand_plan_code,now()  from   cdop_biz.tdm_kcjh_txn_freight_qty_di
    </insert>
    <insert id="insertVersionTable">
             insert into  cdop_sys.t_ryytn_freight_version_list(prediction_version,demand_plan_code,create_time)
             select prediction_version,demand_plan_code,create_time from cdop_sys.t_ryytn_freight_version_list_third
    </insert>
    <select id="refreshAiDailyWarehouseView" resultType="String">
                 SELECT  cdop_biz.execute_dynamic_query_ai_daily_warehouse(to_char(now(), 'YYYYMM'::text), to_char(date_trunc('month'::text, 'now'::text::date - '1 mon'::interval), 'YYYYMM'::text))
    </select>


</mapper>