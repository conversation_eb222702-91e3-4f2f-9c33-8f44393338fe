<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dao.SkuLockDao">
	<select id="pageSkuLocList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.SkuLockDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockRspVo">
		SELECT
		  a.id,
		  a.sku_code,
		  a.sku_name,
		  a.lv1_category_code,
          a.lv1_category_name,
		  a.lv2_category_code,
          a.lv2_category_name,
		  a.lv3_category_code,
          a.lv3_category_name,
          a.lock_start_date,
          a.lock_end_date,
          a.lock_start_week,
          a.lock_end_week,
          array_to_string(array_agg(b.channel_name order by b.channel_name),',') AS channelNameList
		FROM
		  t_ryytn_sku_lock a
		  LEFT JOIN t_ryytn_sku_lock_channel b
			ON a.id = b.lock_id
		WHERE 1=1
		<if test="skuCode != null and skuCode != ''">
			AND a.sku_code = #{skuCode}
		</if>
		<if test="skuName != null and skuName != ''">
			AND strpos(a.sku_name, #{skuName}) &gt; 0
		</if>
		<if test="lv1CategoryName != null and lv1CategoryName != ''">
			AND strpos(a.lv1_category_name, #{lv1CategoryName}) &gt; 0
		</if>
		<if test="lv2CategoryName != null and lv2CategoryName != ''">
			AND strpos(a.lv2_category_name, #{lv2CategoryName}) &gt; 0
		</if>
		<if test="lv3CategoryName != null and lv3CategoryName != ''">
			AND strpos(a.lv3_category_name, #{lv3CategoryName}) &gt; 0
		</if>
		GROUP BY a.id
		ORDER BY a.created_time desc
	</select>

	<insert id="addSkuLock" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.SkuLockDto">
		INSERT INTO t_ryytn_sku_lock (
		  id,
		  sku_code,
		  sku_name,
		  lv1_category_code,
		  lv1_category_name,
		  lv2_category_code,
		  lv2_category_name,
		  lv3_category_code,
		  lv3_category_name,
		  lock_start_date,
		  lock_end_date,
          lock_start_week,
          lock_end_week,
		  created_by
		)
		VALUES
		  (
			#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{skuCode},
			#{skuName},
			#{lv1CategoryCode},
			#{lv1CategoryName},
			#{lv2CategoryCode},
			#{lv2CategoryName},
			#{lv3CategoryCode},
			#{lv3CategoryName},
			#{lockStartDate},
			#{lockEndDate},
			#{lockStartWeek},
			#{lockEndWeek},
			#{createdBy}
		  )
	</insert>

	<insert id="addSkuLockChannel" parameterType="java.util.List">
		INSERT INTO t_ryytn_sku_lock_channel (lock_id, channel_id,channel_name)
			VALUES
		<foreach collection="list" item="item" separator=",">
  		(#{item.lockId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}, #{item.channelId},#{item.channelName})
		</foreach>
	</insert>

	<delete id="deleteSkuLock" parameterType="string">
		DELETE FROM t_ryytn_sku_lock WHERE id=#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<delete id="deleteLockChannelIds" parameterType="string">
		DELETE FROM t_ryytn_sku_lock_channel WHERE
		lock_id=#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<resultMap id="querySkuLockListResult" type="cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockVo">
		<result column="sku_code" property="skuCode"></result>
		<result column="lv1_category_code" property="lv1CategoryCode"></result>
		<result column="lv2_category_code" property="lv2CategoryCode"></result>
		<result column="lv3_category_code" property="lv3CategoryCode"></result>
		<result column="lock_start_date" property="lockStartDate"></result>
		<result column="lock_end_date" property="lockEndDate"></result>
		<collection property="lv2ChannelCodes" ofType="java.lang.String" javaType="list">
			<result column="lv2ChannelCode"/>
		</collection>
	</resultMap>
	<select id="querySkuLockList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockVo" resultMap="querySkuLockListResult">
		select distinct
			a.sku_code,
			a.lv1_category_code ,
			a.lv2_category_code ,
			a.lv3_category_code ,
			a.lock_start_date ,
			a.lock_end_date ,
			b.channel_id as "lv2ChannelCode"
		from
			t_ryytn_sku_lock a
		left join t_ryytn_sku_lock_channel b
		on
			a.id = b.lock_id
		<include refid="where_sql"/>
	</select>

	<select id="queryLockList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockVo">
		select distinct
			a.sku_code,
			a.lv1_category_code ,
			a.lv2_category_code ,
			a.lv3_category_code ,
			a.lock_start_date ,
			a.lock_end_date ,
			b.channel_id as "channelCode"
		from
			t_ryytn_sku_lock a
		left join t_ryytn_sku_lock_channel b
		on
			a.id = b.lock_id
		<include refid="where_sql"/>
	</select>

	<sql id="where_sql">
		where 1=1
		<if test="lockStartDate!=null and lockStartDate!=''">
			and a.lock_start_date &lt;= #{lockStartDate}
		</if>
		<if test="lockEndDate!=null and lockEndDate!=''">
			and a.lock_end_date &gt;= #{lockEndDate}
		</if>
		<if test="skuCode != null and skuCode != ''">
			a.sku_code = #{skuCode}
		</if>
		<if test="skuCodes != null and skuCodes.size() > 0">
			and a.sku_code in
			<foreach collection="skuCodes" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="lv2ChannelCode != null and lv2ChannelCode != ''">
			and b.channel_id = #{lv2ChannelCode}
		</if>
		<if test="lv2ChannelCodes != null and lv2ChannelCodes.size() > 0">
			and b.channel_id in
			<foreach collection="lv2ChannelCodes" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
	</sql>
</mapper>