<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dao.ColdDemandReportDao">
	<update id="createPartitionTable" parameterType="java.util.List">
		<foreach collection="partitionList" item="item" separator=";">
			create table if not exists t_ryytn_cold_demand_report_${item} (like t_ryytn_cold_demand_report including all ,CHECK (substr(rolling_version,4,6)='${item}')) inherits
			(t_ryytn_cold_demand_report);
			CREATE OR REPLACE RULE rule_insert_cold_demand_report_${item} AS
			ON INSERT TO t_ryytn_cold_demand_report WHERE ( substr(rolling_version,4,6)='${item}')
			DO INSTEAD INSERT INTO t_ryytn_cold_demand_report_${item} VALUES (NEW.*);
		</foreach>
	</update>

	<insert id="addColdDemandReportVersion" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportVersionDto">
		INSERT INTO t_ryytn_cold_demand_report_version
		(id, rolling_version, creator, last_modifier, gmt_create, gmt_modify)
		VALUES
		(
			#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{rollingVersion},
			#{creator},
			#{lastModifier},
			now(),
			now()
		)
	</insert>

    <insert id="addColdDemandReport" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto">
            insert into
			<choose>
				<when test="tableSuffix != null and tableSuffix != ''">
					t_ryytn_cold_demand_report_${tableSuffix}
				</when>
				<otherwise>
					t_ryytn_cold_demand_report
				</otherwise>
			</choose>
			(
			id,
			rolling_version,
			sku_code,
			sku_name,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			receiver_type,
			biz_date_type,
			biz_date_value,
			order_num,
			unit,
			last_order_num,
			deviation_radio,
			remark,
			extend,
			is_modify,
			creator,
			last_modifier,
			gmt_create,
			gmt_modify)
			values
			(
			#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{rollingVersion},
			#{skuCode},
			#{skuName},
			#{lv1CategoryCode},
			#{lv1CategoryName},
			#{lv2CategoryCode},
			#{lv2CategoryName},
			#{lv3CategoryCode},
			#{lv3CategoryName},
			#{lv1ChannelCode},
			#{lv1ChannelName},
			#{lv2ChannelCode},
			#{lv2ChannelName},
			#{lv3ChannelCode},
			#{lv3ChannelName},
			#{receiverType},
			#{bizDateType},
			#{bizDateValue},
			#{orderNum},
			#{unit},
		    #{lastOrderNum},
			#{deviationRadio},
			#{remark},
			#{extend},
			#{isModify},
			#{creator},
			#{lastModifier},
			#{gmtCreate},
			#{gmtModify}
			)
    </insert>

	<update id="updateOrderNumFromLatestVersion">
		update
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_cold_demand_report_${tableSuffix} current
			</when>
			<otherwise>
				t_ryytn_cold_demand_report current
			</otherwise>
		</choose>
		   set order_num = latest.order_num,
		  	   last_order_num = latest.order_num
		  from
			<choose>
				<when test="latestTableSuffix != null and latestTableSuffix != ''">
					t_ryytn_cold_demand_report_${latestTableSuffix} latest
				</when>
				<otherwise>
					t_ryytn_cold_demand_report latest
				</otherwise>
			</choose>
		 where current.rolling_version=#{rollingVersion}
		   and latest.rolling_version=#{latestRollingVersion}
		   and current.sku_code=latest.sku_code
		   and current.lv1_category_code=latest.lv1_category_code
		   and current.lv2_category_code=latest.lv2_category_code
		   and current.lv3_category_code=latest.lv3_category_code
		   and current.lv1_channel_code=latest.lv1_channel_code
		   and current.lv2_channel_code=latest.lv2_channel_code
		   and current.lv3_channel_code=latest.lv3_channel_code
		   and current.biz_date_value=latest.biz_date_value
	</update>

	<select id="queryColdDemandReportVersionList" resultType="java.lang.String">
		select distinct rolling_version from t_ryytn_cold_demand_report_version
		order by rolling_version desc
	</select>

	<select id="queryColdDemandReportDateList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto" resultType="java.lang.String">
		select distinct biz_date_value
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_cold_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_cold_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		order by biz_date_value
	</select>

	<select id="queryColdDemandReportDataList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto">
		select
		a.*,
		b.plan_unit_cnt
		from
		(
		select
			json_agg(json_build_object('id', id::varchar, 'bizDateValue', biz_date_value::varchar, 'orderNum',
			order_num::varchar,'lastOrderNum',last_order_num::varchar,'deviationRadio',
			case when last_order_num=0 then (case when order_num=0 then 0 else 1 end) else round((order_num-last_order_num)/ last_order_num,2) end))
			AS "data",
			sku_code AS "skuCode",
			MAX(sku_name) AS "skuName",
			lv1_category_code AS "lv1CategoryCode",
			MAX(lv1_category_name) AS "lv1CategoryName",
			lv2_category_code AS "lv2CategoryCode",
			MAX(lv2_category_name) AS "lv2CategoryName",
			lv3_category_code AS "lv3CategoryCode",
			MAX(lv3_category_name) AS "lv3CategoryName",
			lv1_channel_code AS "lv1ChannelCode",
			MAX(lv1_channel_name) AS "lv1ChannelName",
			lv2_channel_code AS "lv2ChannelCode",
			MAX(lv2_channel_name) AS "lv2ChannelName",
			lv3_channel_code AS "lv3ChannelCode",
			MAX(lv3_channel_name) AS "lv3ChannelName"
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_cold_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_cold_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		group by lv1_category_code,lv2_category_code,lv3_category_code,sku_code,lv1_channel_code,lv2_channel_code,lv3_channel_code
		) a
		left join cdop_biz.dim_bas_sku_baisc_info_df b
		on a."skuCode"=b.sku_code
		order by a."lv1CategoryCode",a."lv2CategoryCode",a."lv3CategoryCode",a."skuCode",a."lv1ChannelCode",a."lv2ChannelCode",a."lv3ChannelCode"
	</select>

	<select id="queryColdDemandReportList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto">
		select
			id,
			rolling_version,
			sku_code,
			sku_name,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			biz_date_type,
			biz_date_value,
			order_num,
			unit,
			deviation_radio,
			remark,
			extend,
			is_modify,
			creator,
			last_modifier,
			gmt_create,
			gmt_modify
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_cold_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_cold_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		order by lv1_category_code,lv2_category_code,lv3_category_code,sku_code,lv1_channel_code,lv2_channel_code,lv3_channel_code,biz_date_value asc
	</select>

	<update id="batchUpdateColdDemandReport" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
			update t_ryytn_cold_demand_report
			   set order_num=#{item.orderNum},
				   deviation_radio=0,
				   remark=#{item.remark},
			       is_modify=#{item.isModify},
				   last_modifier=#{item.lastModifier},
				   gmt_modify=#{item.gmtModify}
			where id = #{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</foreach>
	</update>

	<select id="queryColdDemandReportTemplateList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportTemplateDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportTemplateDto">
		select
		a.*,
		b.plan_unit_cnt
		from
		(
		select
			json_object_agg(biz_date_value,order_num) as "data",
			sku_code AS "skuCode",
			MAX(sku_name) AS "skuName",
			lv1_category_code AS "lv1CategoryCode",
			MAX(lv1_category_name) AS "lv1CategoryName",
			lv2_category_code AS "lv2CategoryCode",
			MAX(lv2_category_name) AS "lv2CategoryName",
			lv3_category_code AS "lv3CategoryCode",
			MAX(lv3_category_name) AS "lv3CategoryName",
			lv1_channel_code AS "lv1ChannelCode",
			MAX(lv1_channel_name) AS "lv1ChannelName",
			lv2_channel_code AS "lv2ChannelCode",
			MAX(lv2_channel_name) AS "lv2ChannelName",
			lv3_channel_code AS "lv3ChannelCode",
			MAX(lv3_channel_name) AS "lv3ChannelName",
		    MAX(rolling_version) AS "rollingVersion"
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_cold_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_cold_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		group by lv1_category_code,lv2_category_code,lv3_category_code,sku_code,lv1_channel_code,lv2_channel_code,lv3_channel_code
		) a
		left join cdop_biz.dim_bas_sku_baisc_info_df b
		on a."skuCode"=b.sku_code
		order by a."lv1ChannelCode",a."lv2ChannelCode",a."lv3ChannelCode",a."lv1CategoryCode",a."lv2CategoryCode",a."lv3CategoryCode",a."skuCode"
	</select>

	<update id="importColdDemandReport" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.ImportColdDemandReportVo">
        <foreach collection="list" item="item" separator=";">
			update
			<choose>
				<when test="item.tableSuffix != null and item.tableSuffix != ''">
					t_ryytn_cold_demand_report_${item.tableSuffix}
				</when>
				<otherwise>
					t_ryytn_cold_demand_report
				</otherwise>
			</choose>
			   set order_num=#{item.orderNum},
				   remark=#{item.remark},
				   last_modifier=#{item.lastModifier},
				   gmt_modify=#{item.gmtModify}
			where rolling_version = #{item.rollingVersion}
			  and sku_code = #{item.skuCode}
			  and lv1_category_code = #{item.lv1CategoryCode}
			  and lv2_category_code = #{item.lv2CategoryCode}
			  and lv3_category_code = #{item.lv3CategoryCode}
			  and lv1_channel_code = #{item.lv1ChannelCode}
			  and lv2_channel_code = #{item.lv2ChannelCode}
			  and lv3_channel_code = #{item.lv3ChannelCode}
			  and biz_date_value = #{item.bizDateValue}
		</foreach>
	</update>

	<select id="queryColdDemandReportVersionExists" parameterType="java.lang.String" resultType="java.lang.Integer">
		select count(1) from t_ryytn_cold_demand_report_version
		where rolling_version = #{rollingVersion}
	</select>

	<select id="queryColdDemandReportHeadSelect" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto">
		select distinct ${groupColumn}
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_cold_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_cold_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		order by ${sortColumn}
	</select>

	<select id="queryColdDemandReportGroupList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto">
		SELECT
		json_agg("data") AS "data",
		${groupColumn}
		FROM (
			(
			SELECT
				json_build_object('order_num', SUM(order_num), 'biz_date_value', biz_date_value) AS "data",
				${groupColumn}
			FROM
			<choose>
				<when test="tableSuffix != null and tableSuffix != ''">
					t_ryytn_cold_demand_report_${tableSuffix}
				</when>
				<otherwise>
					t_ryytn_cold_demand_report
				</otherwise>
			</choose>
			<include refid="where_sql"/>
			GROUP BY ${groupColumn}, biz_date_value
			)
		) agg
		GROUP BY ${groupColumn}
		ORDER BY ${sortColumn}
	</select>

	<sql id="where_sql">
		where is_delete = 0
		<if test="rollingVersion != null and rollingVersion != ''">
			and rolling_version = #{rollingVersion}
		</if>
		<if test="skuCodes != null and skuCodes != ''">
			and sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="skuCode != null and skuCode != ''">
			and sku_code = #{skuCode}
		</if>
    	<if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
    		AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
    	</if>
		<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
			and lv1_category_code = #{lv1CategoryCode}
		</if>
    	<if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
    		AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
    	</if>
		<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
			and lv2_category_code = #{lv2CategoryCode}
		</if>
    	<if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
    		AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
    	</if>
		<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
			and lv3_category_code = #{lv3CategoryCode}
		</if>
		<if test="lv1ChannelCodes != null and lv1ChannelCodes != ''">
    		AND lv1_channel_code = ANY(STRING_TO_ARRAY(#{lv1ChannelCodes},','))
    	</if>
		<if test="lv1ChannelCode != null and lv1ChannelCode != ''">
			and lv1_channel_code = #{lv1ChannelCode}
		</if>
    	<if test="lv2ChannelCodes != null and lv2ChannelCodes != ''">
    		AND lv2_channel_code = ANY(STRING_TO_ARRAY(#{lv2ChannelCodes},','))
    	</if>
		<if test="lv2ChannelCode != null and lv2ChannelCode != ''">
			and lv2_channel_code = #{lv2ChannelCode}
		</if>
    	<if test="lv3ChannelCodes != null and lv3ChannelCodes != ''">
    		AND lv3_channel_code = ANY(STRING_TO_ARRAY(#{lv3ChannelCodes},','))
    	</if>
		<if test="lv3ChannelCode != null and lv3ChannelCode != ''">
			and lv3_channel_code = #{lv3ChannelCode}
		</if>
		<if test="bizDateValue != null and bizDateValue != ''">
			and biz_date_value = #{bizDateValue}
		</if>
        <if test="channelCodes != null and channelCodes != ''">
            AND (
                lv1_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
             OR lv2_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
             OR lv3_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
            )
        </if>
        <if test="categoryCodes != null and categoryCodes != ''">
            AND (
                lv1_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
             OR lv2_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
             OR lv3_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
            )
        </if>
	</sql>
</mapper>
