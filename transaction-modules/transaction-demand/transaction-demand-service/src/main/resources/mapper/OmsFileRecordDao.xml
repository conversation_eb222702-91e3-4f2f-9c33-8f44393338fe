<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dao.OmsFileRecordDao">
	<select id="pageSkuLocList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto">
		SELECT
		  a.id,
		  a.file_type,
		  a.file_count,
		  a.file_url,
          a.record_time,
		  a.success_flag,
          a.description,
          a.demand_plan_code,
          a.version_id,
          a.expire_date
		FROM
			t_ryytn_oms_file a
		WHERE 1=1
		<if test="id != null and id != ''">
			AND a.id = #{id}
		</if>
		<if test="successFlag != null and successFlag != ''">
			AND a.success_flag =  #{successFlag}
		</if>
	</select>


	<insert id="addOmsFileRecord" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto">
	insert into t_ryytn_oms_file(
       	  id,
          file_type,
		  file_count,
		  file_url,
          record_time,
		  success_flag,
          description,
          demand_plan_code,
          version_id,
          expire_date
        )
        values(
        #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
        #{fileType},
        #{fileCount},
        #{fileUrl},
        #{recordTime},
        #{successFlag},
        #{description},
        #{demandPlanCode},
        #{versionId},
        #{expireDate}
        )
	</insert>

    <update id="updateNoSendRecordInvalid" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto">
		UPDATE
		  t_ryytn_oms_file
		SET
		  success_flag = 1,
		  description = '新的版本数据已覆盖,无需发送'
		WHERE demand_plan_code = #{demandPlanCode}
		and success_flag in(0,-1)
    </update>


	<update id="updateSendResult" parameterType="java.util.List">
		<foreach collection="list" item="item" open="" close="" separator=";">
			UPDATE
			t_ryytn_oms_file
			SET
			success_flag =  #{item.successFlag},
			description = #{item.description}
			WHERE  id =  #{item.id}
		</foreach>
    </update>


	<select id="getSendOmsFiles"
			resultType="cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto">
		SELECT
		a.id,
		a.file_type,
		a.file_count,
		a.file_url,
		a.record_time,
		a.success_flag,
		a.description,
		a.demand_plan_code,
		a.version_id,
		a.expire_date
		FROM
		t_ryytn_oms_file a
		WHERE 1=1
		 and success_flag = 0
		 and record_time  >  now() - INTERVAL '9 day'
	</select>

</mapper>