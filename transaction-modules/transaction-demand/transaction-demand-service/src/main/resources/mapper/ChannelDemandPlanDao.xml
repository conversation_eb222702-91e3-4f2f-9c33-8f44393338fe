<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dao.ChannelDemandPlanDao">
	<delete id="deleteChannelDemandPlanDataSync" parameterType="java.lang.String">
		DELETE FROM t_ryytn_channel_demand_plan_data_sync WHERE demand_plan_code = #{demandPlanCode}
	</delete>

	<insert id="addChannelDemandPlanDataSync" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanDataSyncDto">
		<foreach collection="list" item="item" separator=";">
		insert into t_ryytn_channel_demand_plan_data_sync
		(
			demand_plan_code,
			version_id,
			version_name,
			lv2_channel_code,
			sku_code,
			plan_date,
			plan_value,
			month_week
		)
		values
		(
			#{item.demandPlanCode},
			#{item.versionId},
			#{item.versionName},
			#{item.lv2ChannelCode},
			#{item.skuCode},
			#{item.planDate},
			#{item.planValue},
			#{item.monthWeek}
		)
		</foreach>
	</insert>



	<insert id="batchAddChannelDemandPlanDataSync" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanDataSyncDto">
		insert into t_ryytn_channel_demand_plan_data_sync
		(
			demand_plan_code,
			version_id,
			version_name,
			lv2_channel_code,
			sku_code,
			plan_date,
			plan_value,
			month_week
		)
		values
		(
			#{demandPlanCode},
			#{versionId},
			#{versionName},
			#{lv2ChannelCode},
			#{skuCode},
			#{planDate},
			#{planValue},
			#{monthWeek}
		)
	</insert>

	<insert id="executeChannelDemandPlanDataSyncBackData">
		insert into t_ryytn_channel_demand_plan_data_sync_his
		select * from t_ryytn_channel_demand_plan_data_sync
	</insert>

	<delete id="truncateChannelDemandPlanDataSyncData">
		truncate table t_ryytn_channel_demand_plan_data_sync
	</delete>

	<select id="queryChannelDemandPlanDataSyncList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanDataSyncDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanDataSyncDto">
	<![CDATA[
		SELECT
			id,
			demand_plan_code,
			version_id,
			version_name,
			lv2_channel_code,
			sku_code,
			plan_date,
			plan_value,
			month_week
		FROM t_ryytn_channel_demand_plan_data_sync
		where  plan_value > 0
	]]>
		<if test="demandPlanCode != null and demandPlanCode != ''">
			and demand_plan_code = #{demandPlanCode}
		</if>

	</select>

	<insert id="batchAddChannelDemandPlanHistory" parameterType="java.util.List">
		<foreach collection="list" item="item" separator=",">
		insert into
			t_ryytn_channel_demand_plan_history
		(
			demand_plan_code,
			version_id,
			sku_code,
			sku_name,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			plan_date,
			plan_value,
			old_plan_value,
			deviation_radio,
			remark,
			extend,
			last_modifier,
			gmt_modify,
			group_id
		)
		VALUES
			(
			#{item.demandPlanCode},
			#{item.versionId},
			#{item.skuCode},
			#{item.skuName},
			#{item.lv1CategoryCode},
			#{item.lv1CategoryName},
			#{item.lv2CategoryCode},
			#{item.lv2CategoryName},
			#{item.lv3CategoryCode},
			#{item.lv3CategoryName},
			#{item.lv1ChannelCode},
			#{item.lv1ChannelName},
			#{item.lv2ChannelCode},
			#{item.lv2ChannelName},
			#{item.lv3ChannelCode},
			#{item.lv3ChannelName},
			#{item.planDate},
			#{item.planValue},
			#{item.oldPlanValue},
			#{item.deviationRadio},
			#{item.remark},
			#{item.extend},
			#{item.lastModifier},
			#{item.gmtModify},
			#{item.group_id}
			)
		</foreach>
	</insert>


	<insert id="addChannelDemandPlanHistory" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemanPlanHistoryDto">
		insert into
			t_ryytn_channel_demand_plan_history
		(
			demand_plan_code,
			version_id,
			sku_code,
			sku_name,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			plan_date,
			plan_value,
			old_plan_value,
			deviation_radio,
			remark,
			extend,
			last_modifier,
			gmt_modify,
			group_id
		)
		VALUES
			(
			#{demandPlanCode},
			#{versionId},
			#{skuCode},
			#{skuName},
			#{lv1CategoryCode},
			#{lv1CategoryName},
			#{lv2CategoryCode},
			#{lv2CategoryName},
			#{lv3CategoryCode},
			#{lv3CategoryName},
			#{lv1ChannelCode},
			#{lv1ChannelName},
			#{lv2ChannelCode},
			#{lv2ChannelName},
			#{lv3ChannelCode},
			#{lv3ChannelName},
			#{planDate},
			#{planValue},
			#{oldPlanValue},
			#{deviationRadio},
			#{remark},
			#{extend},
			#{lastModifier},
			#{gmtModify},
			#{groupId}
			)
	</insert>

	<select id="queryChannelDemandPlanHistoryList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanDataParamVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemanPlanHistoryDto">
		select
			id,
			demand_plan_code,
			version_id,
			sku_code,
			sku_name,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			plan_date,
			plan_value,
			old_plan_value,
			deviation_radio,
			remark,
			extend,
			last_modifier,
			gmt_modify,
			group_id
		from
			t_ryytn_channel_demand_plan_history
		where demand_plan_code = #{demandPlanCode}
		  and version_id = #{versionId}
		<if test="groupId != null and groupId != ''">
		  and group_id = #{groupId}
		</if>
		<if test="skuCode != null and skuCode != ''">
		  and sku_code = ANY(STRING_TO_ARRAY(#{skuCode},','))
		</if>
		<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
		  and lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCode},','))
		</if>
		<if test="lv2ChannelCode != null and lv2ChannelCode != ''">
		  and lv2_channel_code = ANY(STRING_TO_ARRAY(#{lv2ChannelCode},','))
		</if>
		order by gmt_modify desc
	</select>
</mapper>