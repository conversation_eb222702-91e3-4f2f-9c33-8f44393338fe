<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dao.ChannelDemandReportDao">
	<insert id="addChannelDemandReportVersion" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto">
		INSERT INTO t_ryytn_channel_demand_report_version
		(id, rolling_version, is_locked, creator, last_modifier, gmt_create, gmt_modify)
		VALUES
		(
			#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{rollingVersion},
			#{isLocked},
			#{creator},
			#{lastModifier},
			now(),
			now()
		)
	</insert>

	<update id="updateChannelDemandReportVersionLock" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto">
		update t_ryytn_channel_demand_report_version set is_locked= #{isLocked} where rolling_version=#{rollingVersion}
	</update>

	<select id="queryChannelDemandReportVersionList" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportVersionVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportVersionVo">
		SELECT rolling_version, is_locked
		FROM t_ryytn_channel_demand_report_version
		WHERE 1=1
		<if test="rollingVersion != null and rollingVersion != ''">
			AND strpos(rolling_version,#{rollingVersion}) &gt; 0
		</if>
		<if test="isLocked != null">
			AND is_locked=#{isLocked}
		</if>
		order by rolling_version desc
	</select>

	<select id="queryChannelDemandReportVersion" parameterType="java.lang.String"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto">
		SELECT id, rolling_version, is_locked, creator, last_modifier, gmt_create, gmt_modify
		FROM t_ryytn_channel_demand_report_version
		WHERE rolling_version=#{rollingVersion}
	</select>

	<update id="appendChannelDemandReportVersionLv3ChannelCodes" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto">
		update t_ryytn_channel_demand_report_version
		   set lv3_channel_codes = ltrim(concat(replace(lv3_channel_codes,#{lv3ChannelCodes},''),',',#{lv3ChannelCodes}),',')
		where rolling_version = #{rollingVersion}
	</update>

	<update id="updateChannelDemandReportVersionLv3ChannelCodes" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto">
		update t_ryytn_channel_demand_report_version
		   set lv3_channel_codes = #{lv3ChannelCodes}
		where rolling_version = #{rollingVersion}
	</update>

	<select id="queryChannelDemandReportVersionLv3ChannelCodes" parameterType="java.lang.String" resultType="java.lang.String">
		select lv3_channel_codes
		from t_ryytn_channel_demand_report_version
		where rolling_version = #{rollingVersion}
	</select>
</mapper>
