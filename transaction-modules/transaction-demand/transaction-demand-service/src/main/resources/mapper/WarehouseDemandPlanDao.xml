<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dao.WarehouseDemandPlanDao">
	<insert id="addWarehouseDemandPlanMark" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandPlanMarkDto">
		INSERT INTO t_ryytn_warehouse_demand_plan_mark (
		  demand_plan_code,
		  version_id,
		  receiver_type,
		  lv3_category_code,
		  biz_date_value,
		  creator,
		  last_modifier,
		  gmt_create,
		  gmt_modify
		)
		VALUES
		  (
			#{demandPlanCode},
			#{versionId},
			#{receiverType},
			#{lv3CategoryCode},
			#{bizDateValue},
			#{creator},
			#{lastModifier},
			#{gmtCreate},
			#{gmtModify}
		  )
		ON CONFLICT (demand_plan_code,version_id,receiver_type,lv3_category_code,biz_date_value) DO UPDATE
		SET last_modifier = excluded.last_modifier,
		  gmt_modify = excluded.gmt_modify
	</insert>

	<resultMap id="markDataResult" type="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandPlanMarkDto">
		<result property="bizDateValueList" column="bizDateValueList" jdbcType="ARRAY"
                typeHandler="cn.aliyun.ryytn.common.mybatis.handler.ListTypeHandler"></result>
	</resultMap>
	<select id="queryWarehouseDemandPlanMarkList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandPlanMarkDto"
            resultMap="markDataResult">
		select lv3_category_code,array_agg(biz_date_value order by biz_date_value) as "bizDateValueList"
		from t_ryytn_warehouse_demand_plan_mark
		where demand_plan_code = #{demandPlanCode}
		  and version_id = #{versionId}
		  and receiver_type = #{receiverType}
		group by lv3_category_code
	</select>

	<delete id="deleteWarehouseDemandPlanMark">
		delete from t_ryytn_warehouse_demand_plan_mark
		where demand_plan_code = #{demandPlanCode}
		  and version_id = #{versionId}
	</delete>
</mapper>
