<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.demand.dao.WarehouseDemandReportDao">
	<update id="createPartitionTable" parameterType="java.util.List">
		<foreach collection="partitionList" item="item" separator=";">
			create table if not exists t_ryytn_warehouse_demand_report_${item} (like t_ryytn_warehouse_demand_report including all ,CHECK (substr(rolling_version,3,6)='${item}')) inherits
			(t_ryytn_warehouse_demand_report);
			CREATE OR REPLACE RULE rule_insert_warehouse_demand_report_${item} AS
			ON INSERT TO t_ryytn_warehouse_demand_report WHERE ( substr(rolling_version,3,6)='${item}')
			DO INSTEAD INSERT INTO t_ryytn_warehouse_demand_report_${item} VALUES (NEW.*);
		</foreach>
	</update>

    <insert id="addWarehouseDemandReport" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            insert into
			<choose>
				<when test="item.tableSuffix != null and item.tableSuffix != ''">
					t_ryytn_warehouse_demand_report_${item.tableSuffix}
				</when>
				<otherwise>
					t_ryytn_warehouse_demand_report
				</otherwise>
			</choose>
			(
			id,
			name,
			demand_plan_code,
			rolling_version,
			sku_code,
			sku_name,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			receiver_type,
			warehouse_code,
			warehouse_name,
			biz_date_type,
			biz_date_value,
			order_num,
			unit,
			deviation_radio,
			remark,
			extend,
			is_modify,
			creator,
			last_modifier,
			gmt_create,
			gmt_modify)
			values
			(
			#{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.name},
			#{item.demandPlanCode},
			#{item.rollingVersion},
			#{item.skuCode},
			#{item.skuName},
			#{item.lv1CategoryCode},
			#{item.lv1CategoryName},
			#{item.lv2CategoryCode},
			#{item.lv2CategoryName},
			#{item.lv3CategoryCode},
			#{item.lv3CategoryName},
			#{item.lv1ChannelCode},
			#{item.lv1ChannelName},
			#{item.lv2ChannelCode},
			#{item.lv2ChannelName},
			#{item.lv3ChannelCode},
			#{item.lv3ChannelName},
			#{item.receiverType},
			#{item.warehouseCode},
			#{item.warehouseName},
			#{item.bizDateType},
			#{item.bizDateValue},
			#{item.orderNum},
			#{item.unit},
			#{item.deviationRadio},
			#{item.remark},
			#{item.extend},
			#{item.isModify},
			#{item.creator},
			#{item.lastModifier},
			#{item.gmtCreate},
			#{item.gmtModify}
			)
        </foreach>
    </insert>

    <insert id="batchAddWarehouseDemandReport" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto">
            insert into
			<choose>
				<when test="tableSuffix != null and tableSuffix != ''">
					t_ryytn_warehouse_demand_report_${tableSuffix}
				</when>
				<otherwise>
					t_ryytn_warehouse_demand_report
				</otherwise>
			</choose>
			(
			id,
			name,
			demand_plan_code,
			rolling_version,
			sku_code,
			sku_name,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			receiver_type,
			warehouse_code,
			warehouse_name,
			biz_date_type,
			biz_date_value,
			order_num,
			unit,
			deviation_radio,
			remark,
			extend,
			is_modify,
			creator,
			last_modifier,
			gmt_create,
			gmt_modify)
			values
			(
			#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{name},
			#{demandPlanCode},
			#{rollingVersion},
			#{skuCode},
			#{skuName},
			#{lv1CategoryCode},
			#{lv1CategoryName},
			#{lv2CategoryCode},
			#{lv2CategoryName},
			#{lv3CategoryCode},
			#{lv3CategoryName},
			#{lv1ChannelCode},
			#{lv1ChannelName},
			#{lv2ChannelCode},
			#{lv2ChannelName},
			#{lv3ChannelCode},
			#{lv3ChannelName},
			#{receiverType},
			#{warehouseCode},
			#{warehouseName},
			#{bizDateType},
			#{bizDateValue},
			#{orderNum},
			#{unit},
			#{deviationRadio},
			#{remark},
			#{extend},
			#{isModify},
			#{creator},
			#{lastModifier},
			#{gmtCreate},
			#{gmtModify}
			)
    </insert>

	<select id="queryWarehouseDemandReportPlanList" resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto">
		select distinct name,
			demand_plan_code
		from t_ryytn_warehouse_demand_report_version
		order by demand_plan_code desc
	</select>

	<select id="queryWarehouseDemandReportVersionList" parameterType="java.lang.String" resultType="java.lang.String">
		select distinct rolling_version
		from t_ryytn_warehouse_demand_report_version where demand_plan_code = #{demandPlanCode}
		order by rolling_version desc
	</select>

	<select id="queryWarehouseDemandReportDateList" parameterType="java.lang.String" resultType="java.lang.String">
		select distinct biz_date_value
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_warehouse_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_warehouse_demand_report
			</otherwise>
		</choose>
		where demand_plan_code=#{demandPlanCode}
		and rolling_version=#{rollingVersion}
		and is_delete =0
		order by biz_date_value
	</select>

	<select id="queryWarehouseDemandReportList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto">
		select
			id,
			name,
			demand_plan_code,
			rolling_version,
			sku_code,
			sku_name,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			receiver_type,
			warehouse_code,
			warehouse_name,
			biz_date_type,
			biz_date_value,
			order_num,
			unit,
			deviation_radio,
			remark,
			extend,
			is_modify,
			creator,
			last_modifier,
			gmt_create,
			gmt_modify
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_warehouse_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_warehouse_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		  and is_delete = 0
		order by lv1_category_code,lv2_category_code,lv3_category_code,sku_code,lv1_channel_code,lv2_channel_code,lv3_channel_code,receiver_type,warehouse_code asc
	</select>

	<select id="queryWarehouseDemandReportTemplateList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportTemplateDto">
		select
			json_object_agg(biz_date_value,order_num) as "data",
			sku_code AS "skuCode",
			MAX(sku_name) AS "skuName",
			lv1_category_code AS "lv1CategoryCode",
			MAX(lv1_category_name) AS "lv1CategoryName",
			lv2_category_code AS "lv2CategoryCode",
			MAX(lv2_category_name) AS "lv2CategoryName",
			lv3_category_code AS "lv3CategoryCode",
			MAX(lv3_category_name) AS "lv3CategoryName",
			lv1_channel_code AS "lv1ChannelCode",
			MAX(lv1_channel_name) AS "lv1ChannelName",
			lv2_channel_code AS "lv2ChannelCode",
			MAX(lv2_channel_name) AS "lv2ChannelName",
			lv3_channel_code AS "lv3ChannelCode",
			MAX(lv3_channel_name) AS "lv3ChannelName",
			warehouse_code AS "warehouseCode",
			MAX(warehouse_name) AS "warehouseName",
		    MAX(rolling_version) AS "rollingVersion"
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_warehouse_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_warehouse_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		  and is_delete = 0
		group by lv1_channel_code,lv2_channel_code,lv3_channel_code,warehouse_code,lv1_category_code,lv2_category_code,lv3_category_code,sku_code
		order by lv1_channel_code,lv2_channel_code,lv3_channel_code,lv1_category_code,lv2_category_code,lv3_category_code,sku_code
	</select>

	<update id="batchImportWarehouseDemandReport" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.ImportWarehouseDemandReportVo">
			update
			<choose>
				<when test="tableSuffix != null and tableSuffix != ''">
					t_ryytn_warehouse_demand_report_${tableSuffix}
				</when>
				<otherwise>
					t_ryytn_warehouse_demand_report
				</otherwise>
			</choose>
			   set order_num=#{orderNum},
				   deviation_radio=0,
				   remark=#{remark},
				   last_modifier=#{lastModifier},
				   gmt_modify=#{gmtModify},
			       is_modify=1
			where demand_plan_code = #{demandPlanCode}
			  and rolling_version = #{rollingVersion}
			  and sku_code = #{skuCode}
			  and lv1_category_code = #{lv1CategoryCode}
			  and lv2_category_code = #{lv2CategoryCode}
			  and lv3_category_code = #{lv3CategoryCode}
			  and lv1_channel_code = #{lv1ChannelCode}
			  and lv2_channel_code = #{lv2ChannelCode}
			  and lv3_channel_code = #{lv3ChannelCode}
			  and warehouse_code = #{warehouseCode}
			  and biz_date_value = #{bizDateValue}
	</update>

	<update id="importWarehouseDemandReport" parameterType="cn.aliyun.ryytn.modules.demand.entity.vo.ImportWarehouseDemandReportVo">
        <foreach collection="list" item="item" separator=";">
			update
			<choose>
				<when test="item.tableSuffix != null and item.tableSuffix != ''">
					t_ryytn_warehouse_demand_report_${item.tableSuffix}
				</when>
				<otherwise>
					t_ryytn_warehouse_demand_report
				</otherwise>
			</choose>
			   set order_num=#{item.orderNum},
				   deviation_radio=0,
				   remark=#{item.remark},
				   last_modifier=#{item.lastModifier},
				   gmt_modify=#{item.gmtModify},
			       is_modify=1
			where demand_plan_code = #{item.demandPlanCode}
			  and rolling_version = #{item.rollingVersion}
			  and sku_code = #{item.skuCode}
			  and lv1_category_code = #{item.lv1CategoryCode}
			  and lv2_category_code = #{item.lv2CategoryCode}
			  and lv3_category_code = #{item.lv3CategoryCode}
			  and lv1_channel_code = #{item.lv1ChannelCode}
			  and lv2_channel_code = #{item.lv2ChannelCode}
			  and lv3_channel_code = #{item.lv3ChannelCode}
			  and warehouse_code = #{item.warehouseCode}
			  and biz_date_value = #{item.bizDateValue}
		</foreach>
	</update>

	<select id="queryWarehouseDemandReportDataByIds" parameterType="java.util.List"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto">
		select
			id,
			name,
			demand_plan_code,
			rolling_version,
			sku_code,
			sku_name,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			receiver_type,
			warehouse_code,
			warehouse_name,
			biz_date_type,
			biz_date_value,
			order_num,
			unit,
			deviation_radio,
			remark,
			extend,
			is_modify,
			creator,
			last_modifier,
			gmt_create,
			gmt_modify
		from
			t_ryytn_warehouse_demand_report
		where is_delete = 0
		<if test="ids != null and ids.size > 0">
			and id in
			<foreach collection="ids" item="item" open="(" close=")" separator=",">
				#{item,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
			</foreach>
		</if>
	</select>

	<update id="batchUpdateWarehouseDemandReport" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
			update t_ryytn_warehouse_demand_report
			   set order_num=#{item.orderNum},
				   deviation_radio=0,
				   remark=#{item.remark},
			       is_modify=#{item.isModify},
				   last_modifier=#{item.lastModifier},
				   gmt_modify=#{item.gmtModify}
			where id = #{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</foreach>
	</update>

	<insert id="addWarehouseDemandReportHistory" parameterType="java.util.List">
    	insert into t_ryytn_warehouse_demand_report_history
		(
			name,
			demand_plan_code,
			rolling_version,
			sku_code,
			sku_name,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			lv1_channel_code,
			lv1_channel_name,
			lv2_channel_code,
			lv2_channel_name,
			lv3_channel_code,
			lv3_channel_name,
			receiver_type,
			warehouse_code,
			warehouse_name,
			biz_date_type,
			biz_date_value,
			order_num,
			old_order_num,
			unit,
			deviation_radio,
			remark,
			extend,
			last_modifier,
			gmt_modify
		)
		values
		<foreach collection="list" item="item" separator=",">
			(
			#{item.name},
			#{item.demandPlanCode},
			#{item.rollingVersion},
			#{item.skuCode},
			#{item.skuName},
			#{item.lv1CategoryCode},
			#{item.lv1CategoryName},
			#{item.lv2CategoryCode},
			#{item.lv2CategoryName},
			#{item.lv3CategoryCode},
			#{item.lv3CategoryName},
			#{item.lv1ChannelCode},
			#{item.lv1ChannelName},
			#{item.lv2ChannelCode},
			#{item.lv2ChannelName},
			#{item.lv3ChannelCode},
			#{item.lv3ChannelName},
			#{item.receiverType},
			#{item.warehouseCode},
			#{item.warehouseName},
			#{item.bizDateType},
			#{item.bizDateValue},
			#{item.orderNum},
			#{item.oldOrderNum},
			#{item.unit},
			#{item.deviationRadio},
			#{item.remark},
			#{item.extend},
			#{item.lastModifier},
			#{item.gmtModify}
			)
        </foreach>
	</insert>

	<select id="queryWarehouseDemandReportHistoryList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto">
		select
			warehouse_code,
			warehouse_name,
			biz_date_type,
			biz_date_value,
			order_num,
			old_order_num,
			unit,
			deviation_radio,
			remark,
			extend,
			last_modifier,
			gmt_modify
		from
			t_ryytn_warehouse_demand_report_history
		<include refid="where_sql"/>
		order by gmt_modify desc
	</select>

	<delete id="deleteWarehouseDemandReportVersion" parameterType="java.lang.String">
		delete from t_ryytn_warehouse_demand_report_version where demand_plan_code = #{demandPlanCode}
	</delete>

	<delete id="deleteWarehouseDemandReport" parameterType="java.lang.String">
		update t_ryytn_warehouse_demand_report set is_delete = 1 where demand_plan_code = #{demandPlanCode}
	</delete>

	<select id="queryWarehouseDemandReportHeadList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="java.lang.String">
		select distinct biz_date_value from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_warehouse_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_warehouse_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		order by biz_date_value
	</select>

	<select id="queryWarehouseDemandReportHeadSelect" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto">
		select distinct ${groupColumn}
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_warehouse_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_warehouse_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		order by ${sortColumn}
	</select>

	<select id="queryWarehouseDemandReportGroupList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto">
		SELECT
		json_agg("data") AS "data",
		${groupColumn}
		FROM (
			(
			SELECT
				json_build_object('order_num', SUM(order_num), 'biz_date_value', biz_date_value) AS "data",
				${groupColumn}
			FROM
			<choose>
				<when test="tableSuffix != null and tableSuffix != ''">
					t_ryytn_warehouse_demand_report_${tableSuffix}
				</when>
				<otherwise>
					t_ryytn_warehouse_demand_report
				</otherwise>
			</choose>
			<include refid="group_where_sql"/>
			GROUP BY ${groupColumn}, biz_date_value
			)
		) agg
		GROUP BY ${groupColumn}
		ORDER BY ${sortColumn}
	</select>

	<select id="queryWarehouseDemandReportDataKeyList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto">
		select distinct
			sku_code,
			lv3_channel_code,
			receiver_type,
			warehouse_code
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_warehouse_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_warehouse_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		ORDER BY sku_code,lv3_channel_code,receiver_type,warehouse_code
	</select>

	<select id="queryWarehouseDemandReportDataJsonList" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto">
		SELECT
			json_agg(json_build_object('id', id::varchar, 'bizDateValue', biz_date_value::varchar, 'orderNum', order_num::varchar,
		'deviationRadio',deviation_radio::varchar)) AS "data",
			sku_code AS "skuCode",
			MAX(sku_name) AS "skuName",
			lv1_category_code AS "lv1CategoryCode",
			MAX(lv1_category_name) AS "lv1CategoryName",
			lv2_category_code AS "lv2CategoryCode",
			MAX(lv2_category_name) AS "lv2CategoryName",
			lv3_category_code AS "lv3CategoryCode",
			MAX(lv3_category_name) AS "lv3CategoryName",
			lv1_channel_code AS "lv1ChannelCode",
			MAX(lv1_channel_name) AS "lv1ChannelName",
			lv2_channel_code AS "lv2ChannelCode",
			MAX(lv2_channel_name) AS "lv2ChannelName",
			lv3_channel_code AS "lv3ChannelCode",
			MAX(lv3_channel_name) AS "lv3ChannelName",
			receiver_type AS "receiverType",
			warehouse_code AS "warehouseCode",
			MAX(warehouse_name) AS "warehouseName",
		    MIN(is_modify) AS "isModify"
		FROM
			<choose>
				<when test="tableSuffix != null and tableSuffix != ''">
					t_ryytn_warehouse_demand_report_${tableSuffix}
				</when>
				<otherwise>
					t_ryytn_warehouse_demand_report
				</otherwise>
			</choose>
		<include refid="where_sql"/>
		GROUP BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code, lv3_channel_code, receiver_type,
		warehouse_code
		ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, lv1_channel_code, lv2_channel_code, lv3_channel_code, receiver_type,
		warehouse_code
	</select>

	<select id="queryWarehouseDemandReportSummary" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo">
		SELECT
		biz_date_value,
		sum(order_num) as order_num
		FROM
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_warehouse_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_warehouse_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
		GROUP BY biz_date_value
	</select>

	<sql id="where_sql">
		WHERE 1 = 1
		<if test="demandPlanCode != null and demandPlanCode != ''">
			and demand_plan_code = #{demandPlanCode}
		</if>
		<if test="rollingVersion != null and rollingVersion != ''">
			and rolling_version = #{rollingVersion}
		</if>
    	<if test="skuCodes != null and skuCodes != ''">
    		AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
    	</if>
    	<if test="skuCode != null and skuCode != ''">
    		AND sku_code = #{skuCode}
    	</if>
		<if test="lv1ChannelCodes != null and lv1ChannelCodes != ''">
    		AND lv1_channel_code = ANY(STRING_TO_ARRAY(#{lv1ChannelCodes},','))
    	</if>
    	<if test="lv1ChannelCode != null and lv1ChannelCode != ''">
    		AND lv1_channel_code = #{lv1ChannelCode}
    	</if>
    	<if test="lv2ChannelCodes != null and lv2ChannelCodes != ''">
    		AND lv2_channel_code = ANY(STRING_TO_ARRAY(#{lv2ChannelCodes},','))
    	</if>
    	<if test="lv2ChannelCode != null and lv2ChannelCode != ''">
    		AND lv2_channel_code = #{lv2ChannelCode}
    	</if>
    	<if test="lv3ChannelCodes != null and lv3ChannelCodes != ''">
    		AND lv3_channel_code = ANY(STRING_TO_ARRAY(#{lv3ChannelCodes},','))
    	</if>
    	<if test="lv3ChannelCode != null and lv3ChannelCode != ''">
    		AND lv3_channel_code = #{lv3ChannelCode}
    	</if>
    	<if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
    		AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
    	</if>
    	<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
    		AND lv1_category_code = #{lv1CategoryCode}
    	</if>
    	<if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
    		AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
    	</if>
    	<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
    		AND lv2_category_code = #{lv2CategoryCode}
    	</if>
    	<if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
    		AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
    	</if>
    	<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
    		AND lv3_category_code = #{lv3CategoryCode}
    	</if>
    	<if test="receiverTypes != null and receiverTypes != ''">
    		and lower(receiver_type) = ANY(STRING_TO_ARRAY(lower(#{receiverTypes}),','))
    	</if>
    	<if test="receiverType != null and receiverType != ''">
    		and lower(receiver_type) = lower(#{receiverType})
    	</if>
    	<if test="warehouseCodes != null and warehouseCodes != ''">
    		and warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
    	</if>
    	<if test="warehouseCode != null and warehouseCode != ''">
    		and warehouse_code = #{warehouseCode}
    	</if>
		<if test="bizDateValue != null and bizDateValue != ''">
			and biz_date_value = #{bizDateValue}
		</if>
        <if test="channelCodes != null and channelCodes != ''">
            AND (
                lv1_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
             OR lv2_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
             OR lv3_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
            )
        </if>
        <if test="categoryCodes != null and categoryCodes != ''">
            AND (
                lv1_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
             OR lv2_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
             OR lv3_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
            )
        </if>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
				<if test="item.skuCode != null and item.skuCode != ''">
					AND sku_code = #{item.skuCode}
				</if>
                <!--				<if test="item.lv1ChannelCode != null and item.lv1ChannelCode != ''">-->
                <!--					AND lv1_channel_code = #{item.lv1ChannelCode}-->
                <!--				</if>-->
                <!--				<if test="item.lv2ChannelCode != null and item.lv2ChannelCode != ''">-->
                <!--					AND lv2_channel_code = #{item.lv2ChannelCode}-->
                <!--				</if>-->
                <if test="item.lv3ChannelCode != null and item.lv3ChannelCode != ''">
					AND lv3_channel_code = #{item.lv3ChannelCode}
				</if>
                <!--				<if test="item.lv1CategoryCode != null and item.lv1CategoryCode != ''">-->
                <!--					AND lv1_category_code = #{item.lv1CategoryCode}-->
                <!--				</if>-->
                <!--				<if test="item.lv2CategoryCode != null and item.lv2CategoryCode != ''">-->
                <!--					AND lv2_category_code = #{item.lv2CategoryCode}-->
                <!--				</if>-->
                <!--				<if test="item.lv3CategoryCode != null and item.lv3CategoryCode != ''">-->
                <!--					AND lv3_category_code = #{item.lv3CategoryCode}-->
                <!--				</if>-->
                <if test="item.warehouseCode != null and item.warehouseCode != ''">
					AND warehouse_code = #{item.warehouseCode}
				</if>
				<if test="item.receiverType != null and item.receiverType != ''">
					AND lower(receiver_type) = lower(#{item.receiverType})
				</if>
				)
			</foreach>
		</if>
	</sql>

	<sql id="group_where_sql">
		WHERE 1 = 1
		<if test="demandPlanCode != null and demandPlanCode != ''">
			and demand_plan_code = #{demandPlanCode}
		</if>
		<if test="rollingVersion != null and rollingVersion != ''">
			and rolling_version = #{rollingVersion}
		</if>
    	<if test="skuCodes != null and skuCodes != ''">
    		AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
    	</if>
    	<if test="skuCode != null and skuCode != ''">
    		AND sku_code = #{skuCode}
    	</if>
		<if test="lv1ChannelCodes != null and lv1ChannelCodes != ''">
    		AND lv1_channel_code = ANY(STRING_TO_ARRAY(#{lv1ChannelCodes},','))
    	</if>
    	<if test="lv1ChannelCode != null and lv1ChannelCode != ''">
    		AND lv1_channel_code = #{lv1ChannelCode}
    	</if>
    	<if test="lv2ChannelCodes != null and lv2ChannelCodes != ''">
    		AND lv2_channel_code = ANY(STRING_TO_ARRAY(#{lv2ChannelCodes},','))
    	</if>
    	<if test="lv2ChannelCode != null and lv2ChannelCode != ''">
    		AND lv2_channel_code = #{lv2ChannelCode}
    	</if>
    	<if test="lv3ChannelCodes != null and lv3ChannelCodes != ''">
    		AND lv3_channel_code = ANY(STRING_TO_ARRAY(#{lv3ChannelCodes},','))
    	</if>
    	<if test="lv3ChannelCode != null and lv3ChannelCode != ''">
    		AND lv3_channel_code = #{lv3ChannelCode}
    	</if>
    	<if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
    		AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
    	</if>
    	<if test="lv1CategoryCode != null and lv1CategoryCode != ''">
    		AND lv1_category_code = #{lv1CategoryCode}
    	</if>
    	<if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
    		AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
    	</if>
    	<if test="lv2CategoryCode != null and lv2CategoryCode != ''">
    		AND lv2_category_code = #{lv2CategoryCode}
    	</if>
    	<if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
    		AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
    	</if>
    	<if test="lv3CategoryCode != null and lv3CategoryCode != ''">
    		AND lv3_category_code = #{lv3CategoryCode}
    	</if>
    	<if test="receiverTypes != null and receiverTypes != ''">
    		and lower(receiver_type) = ANY(STRING_TO_ARRAY(lower(#{receiverTypes}),','))
    	</if>
    	<if test="receiverType != null and receiverType != ''">
    		and lower(receiver_type) = lower(#{receiverType})
    	</if>
    	<if test="warehouseCodes != null and warehouseCodes != ''">
    		and warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
    	</if>
    	<if test="warehouseCode != null and warehouseCode != ''">
    		and warehouse_code = #{warehouseCode}
    	</if>
		<if test="bizDateValue != null and bizDateValue != ''">
			and biz_date_value = #{bizDateValue}
		</if>
        <if test="channelCodes != null and channelCodes != ''">
            AND (
                lv1_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
             OR lv2_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
             OR lv3_channel_code = ANY(STRING_TO_ARRAY(#{channelCodes},','))
            )
        </if>
        <if test="categoryCodes != null and categoryCodes != ''">
            AND (
                lv1_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
             OR lv2_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
             OR lv3_category_code = ANY(STRING_TO_ARRAY(#{categoryCodes},','))
            )
        </if>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
				<if test="item.skuCode != null and item.skuCode != ''">
					AND sku_code = #{item.skuCode}
				</if>
                <!--				<if test="item.lv1ChannelCode != null and item.lv1ChannelCode != ''">-->
                <!--					AND lv1_channel_code = #{item.lv1ChannelCode}-->
                <!--				</if>-->
                <!--				<if test="item.lv2ChannelCode != null and item.lv2ChannelCode != ''">-->
                <!--					AND lv2_channel_code = #{item.lv2ChannelCode}-->
                <!--				</if>-->
                <if test="item.lv3ChannelCode != null and item.lv3ChannelCode != ''">
					AND lv3_channel_code = #{item.lv3ChannelCode}
				</if>
                <!--				<if test="item.lv1CategoryCode != null and item.lv1CategoryCode != ''">-->
                <!--					AND lv1_category_code = #{item.lv1CategoryCode}-->
                <!--				</if>-->
                <!--				<if test="item.lv2CategoryCode != null and item.lv2CategoryCode != ''">-->
                <!--					AND lv2_category_code = #{item.lv2CategoryCode}-->
                <!--				</if>-->
                <!--				<if test="item.lv3CategoryCode != null and item.lv3CategoryCode != ''">-->
                <!--					AND lv3_category_code = #{item.lv3CategoryCode}-->
                <!--				</if>-->
                <if test="item.warehouseCode != null and item.warehouseCode != ''">
					AND warehouse_code = #{item.warehouseCode}
				</if>
				<if test="item.receiverType != null and item.receiverType != ''">
					AND lower(receiver_type) = lower(#{item.receiverType})
				</if>
				)
			</foreach>
		</if>
	</sql>

	<select id="queryWarehouseDemandReportVersionExists" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="java.lang.Integer">
		select count(1)
		FROM t_ryytn_warehouse_demand_report_version
		where demand_plan_code = #{demandPlanCode}
		  and rolling_version = #{rollingVersion}
	</select>

	<insert id="addWarehouseDemandReportVersion" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportVersionDto">
		INSERT INTO t_ryytn_warehouse_demand_report_version
		("name", demand_plan_code, rolling_version)
		VALUES(#{name}, #{demandPlanCode}, #{rollingVersion});
	</insert>

	<select id="queryWarehouseDemandReportCount" parameterType="cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto"
            resultType="java.lang.Integer">
        select count(1) from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_warehouse_demand_report_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_warehouse_demand_report
			</otherwise>
		</choose>
		<include refid="where_sql"/>
	</select>
</mapper>
