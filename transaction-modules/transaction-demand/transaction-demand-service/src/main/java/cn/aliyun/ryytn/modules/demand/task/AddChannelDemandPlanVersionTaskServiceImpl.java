package cn.aliyun.ryytn.modules.demand.task;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.ChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.constant.PeriodTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanPeriodEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import cn.aliyun.ryytn.modules.demand.dao.SkuLockDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddChannelDemandPlanVersionVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DemandPlanConfigSkuVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockVo;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWeekListReqVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 新增需求计划版本任务实现类
 * <AUTHOR>
 * @date 2023/11/7 14:15
 */
@Slf4j
@Service
public class AddChannelDemandPlanVersionTaskServiceImpl implements TaskService
{
    @Autowired
    private CalendarService calendarService;

    @Autowired
    private ChannelDemandPlanService channelDemandPlanService;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SkuLockDao skuLockDao;

    @Autowired
    private DataqChannelDemandPlanDao dataqChannelDemandPlanDao;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        String param = schedulerJob.getParam();
        if (StringUtils.isBlank(param))
        {
            log.error("AddDemandPlanVersionJobServiceImpl process param is empty.");
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_EMPTY);
        }

        AddDemandPlanListReqVo addDemandPlanListReqVo = JSON.parseObject(param, AddDemandPlanListReqVo.class);

        // 获取当期周数据，当前时间所属周，根据当前周获取生成版本
        String currentDay = DateUtils.getDate(DateUtils.YMD);
        QueryWeekListReqVo queryWeekListReqVo = new QueryWeekListReqVo();
        queryWeekListReqVo.setClearDate(currentDay);
        DataqWeek dataqWeek = calendarService.queryWeekList(queryWeekListReqVo).get(0);
        String versionId = "DP" + dataqWeek.getYearMonthWeek();

        // 获取版本开始日期和结束日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        List<String> planDates = new ArrayList<>(addDemandPlanListReqVo.getPeriods());

        // 当前月
        String currentMonth = DateUtils.getDate(DateUtils.YM);
        // 当月一号
        String versionDate = currentMonth + "01";
        Date currentVersionStartDate = DateUtils.parseDate(versionDate, DateUtils.YMD);
        // 计划周期是月，计划粒度是月
        if (PeriodTypeEnum.MONTH.equals(addDemandPlanListReqVo.getPeriodType()) && PlanPeriodEnum.PER_MONTH.equals(addDemandPlanListReqVo.getPlanPeriod()))
        {
            // 循环多少期
            for (int i = 0; i < addDemandPlanListReqVo.getPeriods(); i++)
            {
                Date planDate = DateUtils.addMonths(currentVersionStartDate, i);
                planDates.add(DateUtils.formatTime(planDate, DateUtils.YMD));
            }
        }
        // 计划周期是月，计划粒度是周
        else if (PeriodTypeEnum.MONTH.equals(addDemandPlanListReqVo.getPeriodType()) && PlanPeriodEnum.PER_WEEK.equals(addDemandPlanListReqVo.getPlanPeriod()))
        {
            String currentVersionStartDay = versionDate;
            String currentVersionEndDay =
                DateUtils.formatTime(DateUtils.addMonths(currentVersionStartDate, addDemandPlanListReqVo.getPeriods()), DateUtils.YMD);
            // 获取所有周第一天字符串
            Set<String> allWeekStartDay = redisUtils.hKeys(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY);
            // 封装当前版本开始日期到结束日期期间的planValue对象
            planDates = allWeekStartDay.stream().filter(item -> {
                return item.compareTo(currentVersionStartDay) >= 0 && item.compareTo(currentVersionEndDay) < 0;
            }).collect(Collectors.toList());
        }
        // 计划周期是周，计划粒度是周
        else if (PeriodTypeEnum.WEEK.equals(addDemandPlanListReqVo.getPeriodType()) && PlanPeriodEnum.PER_WEEK.equals(addDemandPlanListReqVo.getPlanPeriod()))
        {
            String currentVersionStartDay = versionDate;
            String currentVersionEndDay = DateUtils.formatTime(DateUtils.addWeeks(currentVersionStartDate, addDemandPlanListReqVo.getPeriods()), DateUtils.YMD);
            // 获取所有周第一天字符串
            Set<String> allWeekStartDay = redisUtils.hKeys(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY);
            // 封装当前版本开始日期到结束日期期间的planValue对象
            planDates = allWeekStartDay.stream().filter(item -> {
                return item.compareTo(currentVersionStartDay) >= 0 && item.compareTo(currentVersionEndDay) < 0;
            }).collect(Collectors.toList());
        }
        // 计划周期为月时，计划粒度必须是月，不涉及
        else
        {

        }

//        // 计划粒度到月
//        if (PlanPeriodEnum.PER_MONTH.equals(addDemandPlanListReqVo.getPlanPeriod()))
//        {
//            // 当前日期
//            String currentMonth = DateUtils.getDate(DateUtils.YM);
//            // 当月一号
//            versionDate = currentMonth + "01";
//            Date currentVersionStartDate = DateUtils.parseDate(versionDate, DateUtils.YMD);
//            // 计划周期N月
//            if (PeriodTypeEnum.MONTH.equals(addDemandPlanListReqVo.getPeriodType()))
//            {
//                planDates = new ArrayList<>(addDemandPlanListReqVo.getPeriods());
//                // 循环多少期
//                for (int i = 0; i < addDemandPlanListReqVo.getPeriods(); i++)
//                {
//                    Date planDate = DateUtils.addMonths(currentVersionStartDate, i);
//                    planDates.add(DateUtils.formatTime(planDate, DateUtils.YMD));
//                }
//            }
//            // 计划周期N周
//            else
//            {
//                // 计划粒度为月时，计划周期必须是月
//            }
//        }
//        // 计划粒度到周
//        else
//        {
//            // 计划版本滚动时间，按自然月计算，可能会跳过月末/月初的业务周，与产品确认允许这种情况发生，通过多版本覆盖全部业务周的方式解决
//            String currentVersionStartDate = dataqWeek.getFsclWeekStart();
//            String currentVersionEndDate;
//            versionDate = currentVersionStartDate;
//
//            // 计划周期是N月
//            if (PeriodTypeEnum.MONTH.equals(addDemandPlanListReqVo.getPeriodType()))
//            {
//                currentVersionEndDate =
//                    DateUtils.formatTime(DateUtils.addMonths(DateUtils.parseDate(currentVersionStartDate, DateUtils.YMD), addDemandPlanListReqVo.getPeriods()),
//                        DateUtils.YMD);
//            }
//            // 计划周期是N周
//            else if (PeriodTypeEnum.WEEK.equals(addDemandPlanListReqVo.getPeriodType()))
//            {
//                currentVersionEndDate =
//                    DateUtils.formatTime(DateUtils.addWeeks(DateUtils.parseDate(currentVersionStartDate, DateUtils.YMD), addDemandPlanListReqVo.getPeriods()),
//                        DateUtils.YMD);
//            }
//            else
//            {
//                currentVersionEndDate = StringUtils.EMPTY;
//            }
//            // 获取所有周第一天字符串
//            Set<String> allWeekStartDay = redisUtils.hKeys(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY);
//            // 封装当前版本开始日期到结束日期期间的planValue对象
//            planDates = allWeekStartDay.stream().filter(item -> {
//                return item.compareTo(currentVersionStartDate) >= 0 && item.compareTo(currentVersionEndDate) <= 0;
//            }).collect(Collectors.toList());
//        }

        // 查询上一版本
        String lastVersionId = null;
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(addDemandPlanListReqVo.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);

        // 特殊参数，根据版本号分组聚合，倒序排序，只查询1条记录，获取最新版本号
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("group_by", "version_id");
        paramMap.put("order_by", "version_id desc");
        paramMap.put("page_token", "1");
        paramMap.put("page_size", "1");
        // 获取上一版本异常，不阻塞生成任务
        try
        {
            String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
            DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, paramMap, queryChannelDemandPlanVersionListReqVo);
            JSONArray jsonArray = (JSONArray) dataqResult.getData();
            List<QueryChannelDemandPlanVersionListRspVo> queryChannelDemandPlanVersionListRspVoList =
                jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);
            lastVersionId = queryChannelDemandPlanVersionListRspVoList.get(0).getVersionId();
        }
        catch (Exception e)
        {
            log.error("error渠道需求计划生成失败,Get lastVersionId has exception.",e);
        }

        // 封装计划主体/计划对象范围，仅在主体或对象不为全部时封装
        List<DemandPlanConfigSkuVo> planList = null;
        List<List<DemandPlanConfigSkuVo>> subPlanGroupList = addDemandPlanListReqVo.getSubPlanGroupList();
        if (CollectionUtils.isNotEmpty(subPlanGroupList))
        {
            planList = new ArrayList<>();
            long groupId = 1;
            for (List<DemandPlanConfigSkuVo> subPlanList : subPlanGroupList)
            {
                if (CollectionUtils.isEmpty(subPlanList))
                {
                    continue;
                }
                for (DemandPlanConfigSkuVo item : subPlanList)
                {
                    DemandPlanConfigSkuVo subPlan = new DemandPlanConfigSkuVo();
                    Set<String> lv1CategoryCodes = new HashSet<>();
                    Set<String> lv2CategoryCodes = new HashSet<>();
                    Set<String> lv3CategoryCodes = new HashSet<>();
                    Set<String> lv1ChannelCodes = new HashSet<>();
                    Set<String> lv2ChannelCodes = new HashSet<>();
                    if (StringUtils.isNotEmpty(item.getLv1CategoryCode()))
                    {
                        lv1CategoryCodes.add(item.getLv1CategoryCode());
                    }
                    if (StringUtils.isNotEmpty(item.getLv2CategoryCode()))
                    {
                        lv2CategoryCodes.add(item.getLv2CategoryCode());
                    }
                    if (StringUtils.isNotEmpty(item.getLv3CategoryCode()))
                    {
                        lv3CategoryCodes.add(item.getLv3CategoryCode());
                    }
                    if (StringUtils.isNotEmpty(item.getLv1ChannelCode()))
                    {
                        lv1ChannelCodes.add(item.getLv1ChannelCode());
                    }
                    if (StringUtils.isNotEmpty(item.getLv2ChannelCode()))
                    {
                        lv2ChannelCodes.add(item.getLv2ChannelCode());
                    }
                    subPlan.setGroupId(groupId);
                    subPlan.setLv1CategoryCodes(lv1CategoryCodes);
                    subPlan.setLv2CategoryCodes(lv2CategoryCodes);
                    subPlan.setLv3CategoryCodes(lv3CategoryCodes);
                    subPlan.setLv1ChannelCodes(lv1ChannelCodes);
                    subPlan.setLv2ChannelCodes(lv2ChannelCodes);
                    planList.add(subPlan);
                }
                groupId++;
            }
        }

        // 日期范围
        String beginDate = planDates.stream().min(Comparator.comparing(String::valueOf)).get();
        String endDate = planDates.stream().max(Comparator.comparing(String::valueOf)).get();

        // 查询产品锁定期数据
        SkuLockVo skuLockParam = new SkuLockVo();
        skuLockParam.setLockStartDate(beginDate);
        skuLockParam.setLockEndDate(endDate);
        List<SkuLockVo> lockList = skuLockDao.querySkuLockList(skuLockParam);

        // 发送消息给阿里创建滚动版本
        AddChannelDemandPlanVersionVo addChannelDemandPlanVersionVo = new AddChannelDemandPlanVersionVo();
        addChannelDemandPlanVersionVo.setSubjectType(SubjectTypeEnum.order);
        addChannelDemandPlanVersionVo.setDemandPlanCode(addDemandPlanListReqVo.getDemandPlanCode());
        addChannelDemandPlanVersionVo.setVersionId(versionId);
        addChannelDemandPlanVersionVo.setLastVersionId(lastVersionId);
        addChannelDemandPlanVersionVo.setPlanDates(planDates);
        addChannelDemandPlanVersionVo.setGroupPlanList(planList);
        addChannelDemandPlanVersionVo.setLockList(lockList);

        // 查询是否已触发生成该版本数据，如果已触发生成，直接退出
        // 由于quartz通过DB分布式锁控制任务在分布式环境下不会重复触发，此处直接查询数据库校验即可
        int num = dataqChannelDemandPlanDao.queryChannelDemandPlanVersionExists(addChannelDemandPlanVersionVo);
        if (num > 0)
        {
            log.info("{} {} has exists.", addChannelDemandPlanVersionVo.getDemandPlanCode(), addChannelDemandPlanVersionVo.getVersionId());
            return;
        }

        log.info("addChannelDemandPlanVersion param:{}", JSON.toJSONString(addChannelDemandPlanVersionVo));

        try{
            // 发送创建需求计划配置参数（需求计划版本）请求给阿里dataq
            String addDemandPlanConfigPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_CONFIG_CREATE"));
            dataqService.invoke(HttpMethod.POST, addDemandPlanConfigPath, null, null, addChannelDemandPlanVersionVo);
            // 阿里创建滚动版本后清除当前plan的页缓存
            redisUtils.vagueDel(CommonConstants.CALENDAR_TABLE_DATA_PRE_KEY + addDemandPlanListReqVo.getDemandPlanCode() + "*");
        }catch (Exception e){
            log.error("error渠道需求计划生成失败",e);
        }
    }
}
