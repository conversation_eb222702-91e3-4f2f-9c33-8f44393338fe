package cn.aliyun.ryytn.modules.demand.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.aliyun.ryytn.modules.demand.constant.PlanDataTypeEnum;
import cn.aliyun.ryytn.modules.demand.dataqdao.*;
import cn.aliyun.ryytn.modules.demand.entity.dto.*;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.aliyun.brain.dataindustry.common.enums.TaskStatus;
import com.aliyun.brain.dataindustry.microapp.MicroAppTaskInstanceOutputVO;
import com.aliyun.brain.dataindustry.microapp.request.ApiRunMicroAppRequest;
import com.aliyun.dataq.dataindustry.DataIndustrySpringServiceContext;
import com.aliyun.dataq.dataindustry.config.Header;
import com.aliyun.dataq.dataindustry.service.MicroAppService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.DataqTask;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.concurrent.ThreadPoolExecutorUtils;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.dict.DictUtils;
import cn.aliyun.ryytn.common.utils.page.PageUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandPlanService;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanPeriodEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import cn.aliyun.ryytn.modules.demand.dao.WarehouseDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.dao.WarehouseDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanConfigReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanVersionLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ConfirmChannelDemandPlanSubPlanGroupVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DateLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DeleteDemandPlanReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DemandPlanConfigSkuVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.WarehouseDemandPlanDataParamVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.WarehouseDemandPlanVersionVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.WarehouseDemandSubPlanGroupVo;
import cn.aliyun.ryytn.modules.distribution.api.DailyWarehouseDemandService;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo;
import cn.aliyun.ryytn.modules.scheduler.dao.DataqTaskDao;
import cn.aliyun.ryytn.modules.system.api.ProductCategoryService;
import cn.aliyun.ryytn.modules.system.entity.dto.ProductCategoryDto;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 分仓需求计划接口实现
 * <AUTHOR>
 * @date 2023/11/27 16:03
 */
@Slf4j
@Service
public class WarehouseDemandPlanServiceImpl implements WarehouseDemandPlanService
{
    @Autowired
    private ProductCategoryService productCategoryService;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private WarehouseDemandReportDao warehouseDemandReportDao;

    @Autowired
    private DataqWarehouseDemandPlanDao dataqWarehouseDemandPlanDao;

    @Autowired
    private DataqForecastResultDao dataqForecastResultDao;

    @Autowired
    private DataqDeliveryOrderRateDao dataqDeliveryOrderRateDao;

    @Autowired
    private WarehouseDemandPlanDao warehouseDemandPlanDao;

    @Autowired
    private DataqChannelDemandPlanDao dataqChannelDemandPlanDao;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private DictUtils dictUtils;

    @Autowired
    private DailyWarehouseDemandService dailyWarehouseDemandService;

    @Autowired
    private DataqTaskDao dataqTaskDao;
    @Autowired
    private DataqAbcTypeDao dataqAbcTypeDao;

    @Resource(name = "dataIndustryContext")
    private DataIndustrySpringServiceContext dataIndustryContext;

    @Value("${dataq.scheduler.userId}")
    private String userId;

    @Value("${dataq.scheduler.tenantCode}")
    private String tenantCode;

    @Value("${dataq.scheduler.workspaceCode}")
    private String workspaceCode;

    private static final Integer STATUS_SUBMITED = 1;

    private static final Integer STATUS_TRAIL = -1;

    private static final Integer STATUS_INITING = -2;

    private static final Integer STATUS_NOSUBMIT = 0;

    /**
     *
     * @Description 新增分仓需求计划
     * @param addDemandPlanListReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 16:34
     */
    @Override
    public void addWarehouseDemandPlan(AddDemandPlanListReqVo addDemandPlanListReqVo) throws Exception
    {
        addDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.warehouse);

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_CREATE"));
        dataqService.invoke(HttpMethod.POST, path, null, null, addDemandPlanListReqVo);
    }

    /**
     *
     * @Description 查询分仓需求计划列表
     * @param queryWarehouseDemandPlanListReqVo
     * @return List<QueryWarehouseDemandPlanListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月15日 11:02
     */
    @Override
    public List<QueryWarehouseDemandPlanListRspVo> queryWarehouseDemandPlanList(QueryWarehouseDemandPlanListReqVo queryWarehouseDemandPlanListReqVo)
        throws Exception
    {
        List<QueryWarehouseDemandPlanListRspVo> result = Collections.EMPTY_LIST;

        // 阿里接口入参暂不支持deleted字段
        queryWarehouseDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.warehouse);
        queryWarehouseDemandPlanListReqVo.setDeleted(false);

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryWarehouseDemandPlanListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            result = jsonArray.toJavaList(QueryWarehouseDemandPlanListRspVo.class);
            // 过滤不展示已删除数据，根据创建时间倒序排序(阿里创建时间为空，未写入，根据自增主键倒序排序)
            result = result.stream().filter(item -> {
                return !item.getDeleted();
            }).sorted(Comparator.comparing(QueryWarehouseDemandPlanListRspVo::getId).reversed()).collect(Collectors.toList());
        }

        return result;
    }

    /**
     *
     * @Description 分页查询分仓需求计划版本列表
     * @param condition
     * @return PageInfo<WarehouseDemandPlanVersionVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月08日 14:16
     */
    @Override
    public PageInfo<WarehouseDemandPlanVersionVo> queryWarehouseDemandPlanVersionPage(PageCondition<String> condition) throws Exception
    {
        String demandPlanCode = condition.getCondition();

        // 查询dataq接口分仓需求计划列表
        QueryWarehouseDemandPlanListReqVo queryWarehouseDemandPlanListReqVo = new QueryWarehouseDemandPlanListReqVo();
        queryWarehouseDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.warehouse);
        queryWarehouseDemandPlanListReqVo.setDemandPlanCode(demandPlanCode);
        List<QueryWarehouseDemandPlanListRspVo> planList = queryWarehouseDemandPlanList(queryWarehouseDemandPlanListReqVo);
        if (CollectionUtils.isEmpty(planList))
        {
            return null;
        }
        QueryWarehouseDemandPlanListRspVo warehouseDemandPlan = planList.get(0);

        // 查询dataq接口分仓需求计划版本列表
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo = new QueryWarehouseDemandPlanVersionListReqVo();
        queryWarehouseDemandPlanVersionListReqVo.setDemandPlanCode(demandPlanCode);
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);

        // 特殊参数，分页查询版本号，再根据版本号查询具体数据，最后内存分页
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("aggregation", "plan_date,min,beginDate;plan_date,max,endDate");
        paramMap.put("group_by", "version_id");
        paramMap.put("order_by", "version_id desc");
        // 返回总数量
        paramMap.put("fetch_all", true);

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, paramMap, queryWarehouseDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }

        // 解析dataq响应，获取对应分页条件的版本号
        List<WarehouseDemandPlanVersionVo> versionList = jsonArray.toJavaList(WarehouseDemandPlanVersionVo.class);
        String versionIds =
            versionList.stream().map(WarehouseDemandPlanVersionVo::getVersionId).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        queryWarehouseDemandPlanVersionListReqVo.setVersionIds(versionIds);

        // 封装版本条件，只查询分页条件对应页的版本数据，再进行聚合
        paramMap.clear();
        paramMap.put("aggregation", "*,count,num");
        paramMap.put("group_by", "version_id,\"status\"");

        dataqResult = dataqService.invoke(HttpMethod.POST, path, null, paramMap, queryWarehouseDemandPlanVersionListReqVo);
        jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }

        // 解析dataq响应，所有版本按状态聚合，查询各状态的数量
        List<WarehouseDemandPlanVersionVo> versionStatusList = jsonArray.toJavaList(WarehouseDemandPlanVersionVo.class);
        Set<String> initingVersionSet =
            versionStatusList.stream().filter(item -> STATUS_INITING.equals(item.getStatus())).map(WarehouseDemandPlanVersionVo::getVersionId)
                .collect(Collectors.toSet());

        versionList = versionList.stream().filter(item -> !initingVersionSet.contains(item.getVersionId())).collect(Collectors.toList());
        int total = CollectionUtils.size(versionList);

        for (WarehouseDemandPlanVersionVo warehouseDemandPlanVersion : versionList)
        {
            String beginDate = warehouseDemandPlanVersion.getBeginDate();
            String endDate = warehouseDemandPlanVersion.getEndDate();
            StringBuilder rangeSb = new StringBuilder();
            DataqWeek beginWeek = (DataqWeek) redisUtils.hget(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, beginDate);
            DataqWeek endWeek = (DataqWeek) redisUtils.hget(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, endDate);
            if (Objects.isNull(beginWeek) || Objects.isNull(endWeek))
            {
                rangeSb.append(beginDate).append(StringUtils.TO_SEPARATOR).append(endDate);
            }
            else
            {
                if (PlanPeriodEnum.PER_MONTH.equals(warehouseDemandPlan.getPlanPeriod()))
                {
                    rangeSb.append(beginWeek.getFsclYearMonth()).append(StringUtils.TO_SEPARATOR).append(endWeek.getFsclYearMonth());
                }
                else
                {
                    rangeSb.append(beginWeek.getMonthOfFsclYear()).append(StringUtils.DATE_SEPARATOR).append(StringUtils.WEEK_PREFIX_UPPER)
                        .append(beginWeek.getWeekOfFsclMonth()).append(StringUtils.TO_SEPARATOR).append(endWeek.getMonthOfFsclYear())
                        .append(StringUtils.DATE_SEPARATOR).append(StringUtils.WEEK_PREFIX_UPPER).append(endWeek.getWeekOfFsclMonth());
                }
            }

            // 过滤版本号下，状态不是已提交的数量
            long count = versionStatusList.stream().filter(item -> {
                return item.getVersionId().equals(warehouseDemandPlanVersion.getVersionId()) && !STATUS_SUBMITED.equals(item.getStatus());
            }).count();

            warehouseDemandPlanVersion.setDemandPlanCode(demandPlanCode);
            warehouseDemandPlanVersion.setVersionRange(rangeSb.toString());
            if (count == 0)
            {
                warehouseDemandPlanVersion.setStatus(STATUS_SUBMITED);
            }
            else
            {
                warehouseDemandPlanVersion.setStatus(STATUS_TRAIL);
            }
        }

        // 手动分页，阿里dataq是平铺数据，通过接口分页没有意义，此处必须代码在内存中分页
        PageInfo<WarehouseDemandPlanVersionVo> result = PageUtils.init(versionList, condition.getPageNum(), condition.getPageSize(), total);

        return result;
    }

    /**
     *
     * @Description 查询分仓需求计划版本列表
     * @param warehouseDemandPlanVersionVo
     * @return List<WarehouseDemandPlanVersionVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 17:21
     */
    @Override
    public List<String> queryWarehouseDemandPlanVersionList(WarehouseDemandPlanVersionVo warehouseDemandPlanVersionVo) throws Exception
    {
        String demandPlanCode = warehouseDemandPlanVersionVo.getDemandPlanCode();
        String versionId = warehouseDemandPlanVersionVo.getVersionId();
        // 查询dataq接口分仓需求计划版本列表
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo = new QueryWarehouseDemandPlanVersionListReqVo();
        queryWarehouseDemandPlanVersionListReqVo.setDemandPlanCode(demandPlanCode);
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);

        // 特殊参数，分页查询版本号，再根据版本号查询具体数据，最后内存分页
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("group_by", "version_id");
        paramMap.put("order_by", "version_id desc");
        // 返回总数量
        paramMap.put("fetch_all", true);

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, paramMap, queryWarehouseDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }

        // 解析dataq响应，获取对应分页条件的版本号
        List<WarehouseDemandPlanVersionVo> versionList = jsonArray.toJavaList(WarehouseDemandPlanVersionVo.class);

        List<String> result = null;
        if (StringUtils.isNotEmpty(versionId))
        {
            result = versionList.stream().filter(item -> item.getVersionId().contains(versionId)).map(WarehouseDemandPlanVersionVo::getVersionId)
                .collect(Collectors.toList());
        }
        else
        {
            result = versionList.stream().map(WarehouseDemandPlanVersionVo::getVersionId).collect(Collectors.toList());
        }
        return result;
    }

    /**
     *
     * @Description 设置分仓需求计划版本标签
     * @param channelDemandPlanVersionLabelVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:56
     */
    @Override
    public void setWarehouseDemandPlanVersionLabel(ChannelDemandPlanVersionLabelVo channelDemandPlanVersionLabelVo) throws Exception
    {
        // 阿里需要指定渠道需求计划主体为仓库
        channelDemandPlanVersionLabelVo.setSubjectType(SubjectTypeEnum.warehouse);
        // 翻译标签字典
        channelDemandPlanVersionLabelVo.setLabel(dictUtils.codeToName("DEMAND_PLAN_LABEL", channelDemandPlanVersionLabelVo.getLabel()));

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_LABEL_SET"));
        dataqService.invoke(HttpMethod.POST, path, null, null, channelDemandPlanVersionLabelVo);
    }

    /**
     *
     * @Description 查询分仓需求子计划清单组列表
     * @param warehouseDemandSubPlanGroupVo
     * @return List<WarehouseDemandSubPlanGroupVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 15:29
     */
    @Override
    public List<WarehouseDemandSubPlanGroupVo> queryWarehouseDemandSubPlanGroupList(WarehouseDemandSubPlanGroupVo warehouseDemandSubPlanGroupVo)
        throws Exception
    {
        String demandPlanCode = warehouseDemandSubPlanGroupVo.getDemandPlanCode();
        String versionId = warehouseDemandSubPlanGroupVo.getVersionId();

        // 查询渠道需求计划版本数据，根据分组编号分组，先查询所有子计划组和子计划组的状态
        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "group_id,receiver_type");
        param.put("order_by", "group_id,receiver_type");

        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo = new QueryWarehouseDemandPlanVersionListReqVo();
        queryWarehouseDemandPlanVersionListReqVo.setDemandPlanCode(demandPlanCode);
        queryWarehouseDemandPlanVersionListReqVo.setVersionId(versionId);
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryWarehouseDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }
        param.clear();

        // 解析dataq响应，获取所有三级渠道类型
        List<QueryWarehouseDemandPlanVersionListRspVo> dataList = jsonArray.toJavaList(QueryWarehouseDemandPlanVersionListRspVo.class);

        param.put("aggregation", "gmt_modify,max,gmtModify");
        param.put("group_by", "\"status\"");

        List<WarehouseDemandSubPlanGroupVo> subPlanGroupList = new ArrayList<>(dataList.size());
        // 循环所有三级渠道类型（循环次数只会有几次，取决于三级渠道类型数量）
        for (QueryWarehouseDemandPlanVersionListRspVo item : dataList)
        {
            String receiverType = item.getReceiverType();
            queryWarehouseDemandPlanVersionListReqVo.setReceiverType(receiverType);
            dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryWarehouseDemandPlanVersionListReqVo);
            jsonArray = (JSONArray) dataqResult.getData();
            List<QueryWarehouseDemandPlanVersionListRspVo> statusList = jsonArray.toJavaList(QueryWarehouseDemandPlanVersionListRspVo.class);

            long submitCount = statusList.stream().filter(item1 -> {
                return !STATUS_SUBMITED.equals(item1.getStatus());
            }).count();
            Integer status = STATUS_NOSUBMIT;
            String submitTime = null;
            if (submitCount == 0)
            {
                status = STATUS_SUBMITED;
                if (StringUtils.isNotBlank(statusList.get(0).getGmtModify()))
                {
                    submitTime = DateUtils.formatTime(new Date(Long.valueOf(statusList.get(0).getGmtModify())), DateUtils.YMDHMS_STD);
                }
            }

            WarehouseDemandSubPlanGroupVo subPlanGroup = new WarehouseDemandSubPlanGroupVo();
            subPlanGroup.setDemandPlanCode(demandPlanCode);
            subPlanGroup.setVersionId(versionId);
            subPlanGroup.setGroupId(item.getGroupId());
            subPlanGroup.setReceiverType(receiverType);
            subPlanGroup.setStatus(status);
            subPlanGroup.setSubmitTime(submitTime);
            subPlanGroupList.add(subPlanGroup);
        }

        return subPlanGroupList;
    }

    /**
     *
     * @Description 确认分仓需求计划子计划清单组
     * @param confirmChannelDemandPlanSubPlanGroupVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 16:27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmWarehouseDemandPlanSubPlanGroup(ConfirmChannelDemandPlanSubPlanGroupVo confirmChannelDemandPlanSubPlanGroupVo) throws Exception
    {
        String lockKey =
            StringUtils.format(CommonConstants.REDIS_CONFIRM_WAREHOUSE_DEMAND_PLAN_LOCK_KEY, confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode(),
                confirmChannelDemandPlanSubPlanGroupVo.getVersionId(), confirmChannelDemandPlanSubPlanGroupVo.getGroupId());
        boolean isLock = redisUtils.lock(lockKey, 1800);
        if (!isLock)
        {
            log.info("confirmWarehouseDemandPlanSubPlanGroup has processing in another node.");
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }
        try
        {
            // 阿里需要指定渠道需求计划计划主体为仓库
            confirmChannelDemandPlanSubPlanGroupVo.setSubjectType(SubjectTypeEnum.warehouse);

            String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_CONFIRM"));
            dataqService.invoke(HttpMethod.POST, path, null, null, confirmChannelDemandPlanSubPlanGroupVo);

            // 查询dataq接口渠道需求计划版本列表，判断计划+版本所有子计划是否都已提交
            Map<String, Object> param = new HashMap<>();
            param.put("group_by", "\"status\"");

            QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
            queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());
            queryChannelDemandPlanVersionListReqVo.setVersionId(confirmChannelDemandPlanSubPlanGroupVo.getVersionId());
            queryChannelDemandPlanVersionListReqVo.setIsModify(0);
            queryChannelDemandPlanVersionListReqVo.setDeleted(0);
            String versionListPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST"));
            DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, versionListPath, null, null, queryChannelDemandPlanVersionListReqVo);
            JSONArray jsonArray = (JSONArray) dataqResult.getData();
            if (Objects.isNull(jsonArray))
            {
                log.info("DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST response is null");
                return;
            }

            // 解析dataq响应，所有版本拆分子计划的平铺数据
            List<QueryChannelDemandPlanVersionListRspVo> dataList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);

            // 过滤是否存在状态不为已提交的数据
            long count = dataList.stream().filter(item -> {
                return !STATUS_SUBMITED.equals(item.getStatus());
            }).count();

            // 如果状态不为已提交的数据数量大于0，则认为版本还未共识，直接退出
            if (count > 0L)
            {
                log.info("count >0 ,don't general");
                return;
            }

            // 标记数据是为了在试算编辑页面标记哪些产品小类已保存，在共识之后数据已经不再需要，为了减少垃圾数据，删除标记数据
            warehouseDemandPlanDao.deleteWarehouseDemandPlanMark(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode(),
                confirmChannelDemandPlanSubPlanGroupVo.getVersionId());

            // 共识触发创建日分仓需求滚动版本
            QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo = new QueryDailyWarehouseDemandListReqVo();
            queryDailyWarehouseDemandListReqVo.setDemandPlanCode(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());
            queryDailyWarehouseDemandListReqVo.setDemandPlanVersion(confirmChannelDemandPlanSubPlanGroupVo.getVersionId());
            dailyWarehouseDemandService.addDailyWarehouseDemandList(queryDailyWarehouseDemandListReqVo,false);
        }
        catch (Exception e)
        {
            throw new ServiceException();
        }
        finally
        {
            redisUtils.unlock(lockKey);
        }
    }

    /**
     *
     * @Description 查询分仓需求计划品类树
     * @param warehouseDemandSubPlanGroupVo
     * @return List<ProductCategoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月02日 15:34
     */
    @Override
    public Set<ProductCategoryDto> queryWarehouseDemandPlanCategoryTree(WarehouseDemandSubPlanGroupVo warehouseDemandSubPlanGroupVo)
        throws Exception
    {
        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "lv1_category_code,lv1_category_name,lv2_category_code,lv2_category_name,lv3_category_code,lv3_category_name");
        param.put("order_by", "lv1_category_code,lv2_category_code,lv3_category_code");

        // 查询dataq接口渠道需求计划版本列表
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo = new QueryWarehouseDemandPlanVersionListReqVo();
        queryWarehouseDemandPlanVersionListReqVo.setDemandPlanCode(warehouseDemandSubPlanGroupVo.getDemandPlanCode());
        queryWarehouseDemandPlanVersionListReqVo.setVersionId(warehouseDemandSubPlanGroupVo.getVersionId());
        queryWarehouseDemandPlanVersionListReqVo.setReceiverType(warehouseDemandSubPlanGroupVo.getReceiverType());
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryWarehouseDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }

        // 解析dataq响应，所有版本拆分子计划的平铺数据，还需要对此数据根据子计划清单组编号过滤
        List<ProductCategoryDto> dataList = jsonArray.toJavaList(ProductCategoryDto.class);
        if (CollectionUtils.isEmpty(dataList))
        {
            return null;
        }

        // 查询已标记的计划数据
        WarehouseDemandPlanMarkDto warehouseDemandPlanMarkDto = new WarehouseDemandPlanMarkDto();
        warehouseDemandPlanMarkDto.setDemandPlanCode(warehouseDemandSubPlanGroupVo.getDemandPlanCode());
        warehouseDemandPlanMarkDto.setVersionId(warehouseDemandSubPlanGroupVo.getVersionId());
        warehouseDemandPlanMarkDto.setReceiverType(warehouseDemandSubPlanGroupVo.getReceiverType());
        List<WarehouseDemandPlanMarkDto> warehouseDemandPlanMarkList = warehouseDemandPlanDao.queryWarehouseDemandPlanMarkList(warehouseDemandPlanMarkDto);
        if (CollectionUtils.isNotEmpty(warehouseDemandPlanMarkList))
        {
            // 查询计划所有的日期集合，并从yyyy-MM-dd转为yyyyMMdd
            List<String> planDateList = dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanHeadList(queryWarehouseDemandPlanVersionListReqVo);
            planDateList =
                planDateList.stream().map(item -> StringUtils.replace(item, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());

            // 标记数据转换为Map，key：lv3CategoryCode产品小类，value：bizDateValueList时间集合
            Map<String, List<String>> markMap = warehouseDemandPlanMarkList.stream().collect(Collectors.toMap(WarehouseDemandPlanMarkDto::getLv3CategoryCode,
                WarehouseDemandPlanMarkDto::getBizDateValueList, (key1, key2) -> key2));
            for (ProductCategoryDto productCategoryDto : dataList)
            {
                List<String> bizDateValueList = markMap.get(productCategoryDto.getLv3CategoryCode());
                if (CollectionUtils.isEmpty(bizDateValueList))
                {
                    continue;
                }
                Collection<String> subList = CollectionUtils.subtract(planDateList, bizDateValueList);
                if (CollectionUtils.isEmpty(subList))
                {
                    productCategoryDto.setChecked(true);
                }
            }
        }

        return productCategoryService.createCategoryTree(dataList);
    }

    /**
     *
     * @Description 查询分仓需求计划编辑页面日期页签
     * @param warehouseDemandSubPlanGroupVo
     * @return List<WarehouseDemandSubPlanGroupVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月13日 15:53
     */
    @Override
    public List<DateLabelVo> queryWarehouseDemandPlanDateLabelList(WarehouseDemandSubPlanGroupVo warehouseDemandSubPlanGroupVo)
        throws Exception
    {
        // 查询dataq接口渠道需求计划版本列表，判断计划+版本所有子计划是否都已提交
        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "plan_date");
        param.put("order_by", "plan_date");

        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo = new QueryWarehouseDemandPlanVersionListReqVo();
        queryWarehouseDemandPlanVersionListReqVo.setDemandPlanCode(warehouseDemandSubPlanGroupVo.getDemandPlanCode());
        queryWarehouseDemandPlanVersionListReqVo.setVersionId(warehouseDemandSubPlanGroupVo.getVersionId());
        queryWarehouseDemandPlanVersionListReqVo.setReceiverType(warehouseDemandSubPlanGroupVo.getReceiverType());
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);
        String versionListPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, versionListPath, null, param, queryWarehouseDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }
        List<QueryChannelDemandPlanVersionListRspVo> dataList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);
        List<String> planDateList =
            dataList.stream().map(item -> {
                    return StringUtils.replace(item.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                }).distinct().sorted(Comparator.comparing(String::valueOf))
                .collect(Collectors.toList());
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateList);
        if (CollectionUtils.isEmpty(objectList))
        {
            return null;
        }

        List<DateLabelVo> result = objectList.stream().map(item -> {
            DataqWeek dataqWeek = (DataqWeek) item;
            DateLabelVo dateLabel = new DateLabelVo();
            dateLabel.setBizDateLabel(dataqWeek.getWeekLabel());
            dateLabel.setBizDateValue(dataqWeek.getFsclWeekStart());
            return dateLabel;
        }).sorted(Comparator.comparing(DateLabelVo::getBizDateValue)).collect(Collectors.toList());

        return result;
    }

    /**
     *
     * @Description 查询分仓需求计划版本数据列表
     * @param warehouseDemandPlanDataParamVo
     * @return BaseTable<List < QueryWarehouseDemandPlanVersionListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月13日 16:54
     */
    @Override
    public BaseTable<List<QueryWarehouseDemandPlanVersionListRspVo>> queryWarehouseDemandPlanVersionDataList(
        WarehouseDemandPlanDataParamVo warehouseDemandPlanDataParamVo) throws Exception
    {
        BaseTable<List<QueryWarehouseDemandPlanVersionListRspVo>> baseTable = new BaseTable<>();

        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo = new QueryWarehouseDemandPlanVersionListReqVo();
        queryWarehouseDemandPlanVersionListReqVo.setDemandPlanCode(warehouseDemandPlanDataParamVo.getDemandPlanCode());
        queryWarehouseDemandPlanVersionListReqVo.setVersionId(warehouseDemandPlanDataParamVo.getVersionId());
        queryWarehouseDemandPlanVersionListReqVo.setReceiverType(warehouseDemandPlanDataParamVo.getReceiverType());
        queryWarehouseDemandPlanVersionListReqVo.setLv1CategoryCode(warehouseDemandPlanDataParamVo.getLv1CategoryCode());
        queryWarehouseDemandPlanVersionListReqVo.setLv2CategoryCode(warehouseDemandPlanDataParamVo.getLv2CategoryCode());
        queryWarehouseDemandPlanVersionListReqVo.setLv3CategoryCode(warehouseDemandPlanDataParamVo.getLv3CategoryCode());
        queryWarehouseDemandPlanVersionListReqVo.setBeginDate(warehouseDemandPlanDataParamVo.getPlanDate());
        queryWarehouseDemandPlanVersionListReqVo.setEndDate(warehouseDemandPlanDataParamVo.getPlanDate());
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);
        String versionListPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST"));
        log.info("queryWarehouseDemandPlanVersionListReqVo path:{},body:{}",versionListPath, JSONObject.toJSONString(queryWarehouseDemandPlanVersionListReqVo));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, versionListPath, null, null, queryWarehouseDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            log.warn("JSONArray is empty");
            return baseTable;
        }
//        log.info("dataQ response content is :{}",jsonArray.toJSONString());
        List<QueryWarehouseDemandPlanVersionListRspVo> versionList = jsonArray.toJavaList(QueryWarehouseDemandPlanVersionListRspVo.class);

        List<String> headList =
            versionList.stream().map(QueryWarehouseDemandPlanVersionListRspVo::getWarehouseName).distinct().sorted(Comparator.comparing(String::valueOf))
                .collect(Collectors.toList());

        Map<QueryWarehouseDemandPlanVersionListRspVo, List<QueryWarehouseDemandPlanVersionListRspVo>> map =
            versionList.stream().collect(Collectors.groupingBy(Function.identity()));

        String skuCodes =
            versionList.stream().map(QueryWarehouseDemandPlanVersionListRspVo::getSkuCode).distinct().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));

        ChannelDemandPlanReceiverDto channelDemandPlanReceiverDto = new ChannelDemandPlanReceiverDto();
        channelDemandPlanReceiverDto.setDemandPlanCode(warehouseDemandPlanDataParamVo.getDemandPlanCode());
        channelDemandPlanReceiverDto.setVersionId(warehouseDemandPlanDataParamVo.getVersionId());
        channelDemandPlanReceiverDto.setLv1CategoryCode(warehouseDemandPlanDataParamVo.getLv1CategoryCode());
        channelDemandPlanReceiverDto.setLv2CategoryCode(warehouseDemandPlanDataParamVo.getLv2CategoryCode());
        channelDemandPlanReceiverDto.setLv3CategoryCode(warehouseDemandPlanDataParamVo.getLv3CategoryCode());
        channelDemandPlanReceiverDto.setSkuCodes(skuCodes);
        channelDemandPlanReceiverDto.setReceiverType(warehouseDemandPlanDataParamVo.getReceiverType());
        channelDemandPlanReceiverDto.setPlanDate(warehouseDemandPlanDataParamVo.getPlanDate());

        List<ChannelDemandPlanReceiverDto> channelDemandPlanReceiverList =
            dataqChannelDemandPlanDao.queryChannelDemandPlanReceiverListJoinOrderRate(channelDemandPlanReceiverDto);
        Map<ChannelDemandPlanReceiverDto, ChannelDemandPlanReceiverDto> channelDemandPlanReceiverMap = null;
        if (CollectionUtils.isEmpty(channelDemandPlanReceiverList))
        {
            channelDemandPlanReceiverMap = Collections.EMPTY_MAP;
        }
        else
        {
            channelDemandPlanReceiverMap = channelDemandPlanReceiverList.stream().collect(Collectors.toMap(Function.identity(), Function.identity(), (key1,
                key2) -> key2));
        }

        //取历史16Week计算数据
        List<WarehouseDemandPlan16WeekDto> list16WeekData =  dataqChannelDemandPlanDao.queryWareHouseDemandPlan16weekData(channelDemandPlanReceiverDto);
        List<AbcTypeDto> listAbcTyps = new ArrayList<>();
        if(!StringUtils.isEmpty(skuCodes)){
            AbcTypeDto abcTypeDto = new AbcTypeDto();
            abcTypeDto.setSkuCodes(skuCodes);
            abcTypeDto.setSourceOrderType(warehouseDemandPlanDataParamVo.getReceiverType());
            listAbcTyps = dataqAbcTypeDao.queryAbcTypes(abcTypeDto);
        }
        Map<AbcTypeDto,AbcTypeDto> mapAbcTypes = null;
        if(null != listAbcTyps){
            mapAbcTypes = listAbcTyps.stream().collect(Collectors.toMap(Function.identity(),v->v));
        }
        Map<WarehouseDemandPlan16WeekDto,WarehouseDemandPlan16WeekDto> map16WeekData = list16WeekData.stream().collect(Collectors.toMap(Function.identity(),v->v));

        List<QueryWarehouseDemandPlanVersionListRspVo> dataList = new ArrayList<>(map.size());
        ChannelDemandPlanReceiverDto key = new ChannelDemandPlanReceiverDto();
        for (Map.Entry<QueryWarehouseDemandPlanVersionListRspVo, List<QueryWarehouseDemandPlanVersionListRspVo>> entry : map.entrySet())
        {
            QueryWarehouseDemandPlanVersionListRspVo warehouseData = entry.getKey();
            if(null != mapAbcTypes){
                AbcTypeDto abcTypeDto = new AbcTypeDto();
                abcTypeDto.setSkuCode(warehouseData.getSkuCode());
                abcTypeDto.setSourceOrderType(warehouseDemandPlanDataParamVo.getReceiverType());
                AbcTypeDto abcTypeObj = mapAbcTypes.get(abcTypeDto);
                if(null != abcTypeObj){
                    warehouseData.setAbcType(abcTypeObj.getAbcType());
                }
            }
            List<QueryWarehouseDemandPlanVersionListRspVo> warehouseDataList = entry.getValue();
            for (QueryWarehouseDemandPlanVersionListRspVo item : warehouseDataList)
            {
                key.setDemandPlanCode(channelDemandPlanReceiverDto.getDemandPlanCode());
                key.setVersionId(channelDemandPlanReceiverDto.getVersionId());
                key.setLv1CategoryCode(item.getLv1CategoryCode());
                key.setLv2CategoryCode(item.getLv2CategoryCode());
                key.setLv3CategoryCode(item.getLv3CategoryCode());
                key.setSkuCode(item.getSkuCode());
                key.setReceiverType(item.getReceiverType());
                key.setPlanDataType(item.getPlanDataType());
                key.setWarehouseCode(item.getWarehouseCode());
                key.setPlanDate(item.getPlanDate());
                key.setPlanDataType(item.getPlanDataType());

                ChannelDemandPlanReceiverDto channelDemandPlanReceiver = channelDemandPlanReceiverMap.get(key);

                PlanValue data = warehouseData.getDataMap().get(item.getWarehouseName());
                if(null == data ){
                    data = new PlanValue();
                }
                if(item.getPlanDataType() == PlanDataTypeEnum.DAY_SALE_NUM.getPlanDataType()){
                    data.setId(item.getId());
                    data.setPlanValue(item.getPlanValue());
                    data.setPlanRemark(item.getPlanRemark());
                    data.setSrcPlanValue(Objects.isNull(channelDemandPlanReceiver) ? 0d : channelDemandPlanReceiver.getPlanValue());
                    data.setRate(Objects.isNull(channelDemandPlanReceiver) ? 0d : channelDemandPlanReceiver.getOutboundRate());

                    WarehouseDemandPlan16WeekDto warehouseDemandPlan16WeekDto = new WarehouseDemandPlan16WeekDto();
                    warehouseDemandPlan16WeekDto.setReceiverType(warehouseDemandPlanDataParamVo.getReceiverType());
                    warehouseDemandPlan16WeekDto.setWarehouseCode(item.getWarehouseCode());
                    warehouseDemandPlan16WeekDto.setWarehouseName(item.getWarehouseName());
                    warehouseDemandPlan16WeekDto.setSkuCode(item.getSkuCode());
                    warehouseDemandPlan16WeekDto.setPlanDataType(item.getPlanDataType());
                    if(Objects.isNull(map16WeekData.get(warehouseDemandPlan16WeekDto))){
                        data.setOutboundRate16Week(0D);
                        data.setPlanValueOriginal(0D);
                    }else{
                        data.setOutboundRate16Week(map16WeekData.get(warehouseDemandPlan16WeekDto).getOutboundRate());
                        data.setPlanValueOriginal(map16WeekData.get(warehouseDemandPlan16WeekDto).getPlanValueOriginal());
                    }
                }
                if(item.getPlanDataType() == PlanDataTypeEnum.MARKET_ACTIVITY.getPlanDataType()){
                    PlanValue toCMarketActivity = new PlanValue();
                    toCMarketActivity.setId(item.getId());
                    toCMarketActivity.setPlanValue(item.getPlanValue());
                    toCMarketActivity.setPlanRemark(item.getPlanRemark());
                    toCMarketActivity.setSrcPlanValue(Objects.isNull(channelDemandPlanReceiver) ? 0d : channelDemandPlanReceiver.getPlanValue());
                    toCMarketActivity.setRate(Objects.isNull(channelDemandPlanReceiver) ? 0d : channelDemandPlanReceiver.getOutboundRate());

                    WarehouseDemandPlan16WeekDto warehouseDemandPlan16WeekDto = new WarehouseDemandPlan16WeekDto();
                    warehouseDemandPlan16WeekDto.setReceiverType(warehouseDemandPlanDataParamVo.getReceiverType());
                    warehouseDemandPlan16WeekDto.setWarehouseCode(item.getWarehouseCode());
                    warehouseDemandPlan16WeekDto.setWarehouseName(item.getWarehouseName());
                    warehouseDemandPlan16WeekDto.setSkuCode(item.getSkuCode());
                    warehouseDemandPlan16WeekDto.setPlanDataType(item.getPlanDataType());
                    if(Objects.isNull(map16WeekData.get(warehouseDemandPlan16WeekDto))){
                        toCMarketActivity.setOutboundRate16Week(0D);
                        toCMarketActivity.setPlanValueOriginal(0D);
                    }else{
                        toCMarketActivity.setOutboundRate16Week(map16WeekData.get(warehouseDemandPlan16WeekDto).getOutboundRate());
                        toCMarketActivity.setPlanValueOriginal(map16WeekData.get(warehouseDemandPlan16WeekDto).getPlanValueOriginal());
                    }
                    data.setToCMarketActivityNum(toCMarketActivity);
                }
                warehouseData.getDataMap().put(item.getWarehouseName(), data);
            }
            dataList.add(warehouseData);
        }

        dataList = dataList.stream()
            .sorted(
                Comparator.comparing(QueryWarehouseDemandPlanVersionListRspVo::getLv1CategoryCode)
                    .thenComparing(QueryWarehouseDemandPlanVersionListRspVo::getLv2CategoryCode)
                    .thenComparing(QueryWarehouseDemandPlanVersionListRspVo::getLv3CategoryCode)
                    .thenComparing(QueryWarehouseDemandPlanVersionListRspVo::getSkuCode)).collect(
                Collectors.toList());

        baseTable.setHeadArray(headList);
        baseTable.setList(dataList);

        return baseTable;
    }

    /**
     *
     * @Description 查询分仓需求计划数据动态表头列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    @Override
    public List<String> queryWarehouseDemandPlanHeadList(QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo) throws Exception
    {
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);

        List<String> planDateList = dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanHeadList(queryWarehouseDemandPlanVersionListReqVo);
        // 阿里需求计划plan_date格式为yyyy-MM-dd，需要转为yyyyMMdd
        planDateList = planDateList.stream().map(item -> item.replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());

        // 查询日历周缓存
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateList);
        // 封装动态字段列表，格式：MM月Ww(dd-dd)
        List<String> headList =
            objectList.stream().map(item -> (DataqWeek) item).sorted(Comparator.comparing(DataqWeek::getFsclWeekStart)).map(DataqWeek::getWeekLabel)
                .collect(Collectors.toList());

        return headList;
    }

    /**
     *
     * @Description 查询分仓需求计划表头下拉列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<QueryWarehouseDemandPlanVersionListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @Override
    public List<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanHeadSelect(
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo) throws Exception
    {

        GroupColumnEnum groupColumnEnum = queryWarehouseDemandPlanVersionListReqVo.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        queryWarehouseDemandPlanVersionListReqVo.setGroupColumn(groupColumn);
        queryWarehouseDemandPlanVersionListReqVo.setSortColumn(sortColumn);
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);

        List<QueryWarehouseDemandPlanVersionListRspVo> result =
            dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanHeadSelect(queryWarehouseDemandPlanVersionListReqVo);
        return result;
    }

    /**
     *
     * @Description 查询分仓需求计划数据分组聚合列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return List<QueryWarehouseDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    @Override
    public List<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanGroupList(
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo) throws Exception
    {
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);

        // 分组查询
        List<GroupColumnEnum> groupColumnEnumList = queryWarehouseDemandPlanVersionListReqVo.getGroupColumnList();
        List<QueryWarehouseDemandPlanVersionListRspVo> dataList = new ArrayList<>();
        StringBuilder groupColumnSB = new StringBuilder();
        StringBuilder sortColumnSB = new StringBuilder();
        for (GroupColumnEnum groupColumnEnum : groupColumnEnumList)
        {
            String groupColumn = groupColumnSB.append(groupColumnEnum.getColumnName()).toString();
            String sortColumn = sortColumnSB.append(groupColumnEnum.getSortColumn().getColumnName()).toString();
            queryWarehouseDemandPlanVersionListReqVo.setGroupColumn(groupColumn);
            queryWarehouseDemandPlanVersionListReqVo.setSortColumn(sortColumn);

            List<QueryWarehouseDemandPlanVersionListRspVo> list =
                dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanDataGroupList(queryWarehouseDemandPlanVersionListReqVo);
            if (CollectionUtils.isNotEmpty(list))
            {
                dataList.addAll(list);
            }

            groupColumnSB.append(StringUtils.COMMA_SEPARATOR);
            sortColumnSB.append(StringUtils.COMMA_SEPARATOR);
        }

        // 获取所有周数据
        List<String> planDateList = dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanHeadList(queryWarehouseDemandPlanVersionListReqVo);
        planDateList = planDateList.stream().map(item -> StringUtils.replace(item, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());

        // 获取指定周的周对象
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateList);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart, Function.identity(), (key1, key2) -> key1));

        for (QueryWarehouseDemandPlanVersionListRspVo data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String planDate = StringUtils.replace(planValue.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                DataqWeek dataqWeek = dataqWeekMap.get(planDate);
                data.getDataMap().put(dataqWeek.getWeekLabel(), planValue);
            }
            data.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 分页查询分仓需求计划数据列表
     * @param condition
     * @return PageInfo<QueryWarehouseDemandPlanVersionListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    @Override
    public PageInfo<QueryWarehouseDemandPlanVersionListRspVo> queryWarehouseDemandPlanDataPage(
        PageCondition<QueryWarehouseDemandPlanVersionListReqVo> condition) throws Exception
    {

        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo = condition.getCondition();
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);

        // dataq接口效率过低，修改为直接读数据库
        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        PageHelper.startPage(pageNum, pageSize);
        List<QueryWarehouseDemandPlanVersionListRspVo> keyList =
            dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanDataKeyList(queryWarehouseDemandPlanVersionListReqVo);
        PageInfo pageInfo = new PageInfo(keyList);
        if (CollectionUtils.isEmpty(keyList))
        {
            return pageInfo;
        }

        int pageTotal = Integer.parseInt(String.valueOf(pageInfo.getTotal()));

        queryWarehouseDemandPlanVersionListReqVo.setKeyList(keyList);

        // 获取所有周数据
        List<String> planDateList = dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanHeadList(queryWarehouseDemandPlanVersionListReqVo);
        planDateList = planDateList.stream().map(item -> StringUtils.replace(item, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());

        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        List<QueryWarehouseDemandPlanVersionListRspVo> dataList =
            dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanDataJsonList(queryWarehouseDemandPlanVersionListReqVo);

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (QueryWarehouseDemandPlanVersionListRspVo data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String planDate = StringUtils.replace(planValue.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                DataqWeek dataqWeek = dataqWeekMap.get(planDate);
                data.getDataMap().put(dataqWeek.getWeekLabel(), planValue);
            }
            data.setData(null);
        }

        PageInfo<QueryWarehouseDemandPlanVersionListRspVo> result = PageUtils.init(dataList, pageNum, pageSize, pageTotal);
        return result;
    }

    /**
     *
     * @Description 查询分仓需求计划数据汇总
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return Map<String, Double>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月21日 14:50
     */
    @Override
    public Map<String, Double> queryWarehouseDemandPlanSummary(QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo)
        throws Exception
    {
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);

        List<PlanValue> dataList = dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanSummary(queryWarehouseDemandPlanVersionListReqVo);
        if (CollectionUtils.isEmpty(dataList))
        {
            return null;
        }
        List<String> planDateList =
            dataList.stream().map(item -> StringUtils.replace(item.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        Map<String, Double> result = new HashMap<>();
        for (PlanValue data : dataList)
        {
            String planDate = StringUtils.replace(data.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
            DataqWeek dataqWeek = dataqWeekMap.get(planDate);
            result.put(dataqWeek.getWeekLabel(), data.getPlanValue());
        }

        return result;
    }

    /**
     *
     * @Description 修改分仓需求计划数据
     * @param addDemandPlanConfigReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 17:21
     */
    @Override
    public void updateWarehouseDemandPlanData(AddDemandPlanConfigReqVo addDemandPlanConfigReqVo) throws Exception
    {
        List<DemandPlanConfigSkuVo> planList = addDemandPlanConfigReqVo.getPlanList();
        // 阿里需要指定分仓需求计划计划主体为渠道
        addDemandPlanConfigReqVo.setSubjectType(SubjectTypeEnum.warehouse);
        addDemandPlanConfigReqVo.setPlanList(null);
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_CONFIG_UPDATE"));
        if (CollectionUtils.isNotEmpty(planList))
        {
            // 如果数据量过大，需要分片
            int fragmentSize = 500;
            if (planList.size() < fragmentSize)
            {
                addDemandPlanConfigReqVo.setPlanList(planList);
                // 发送创建需求计划配置参数（需求计划版本）请求给阿里dataq
                dataqService.invoke(HttpMethod.POST, path, null, null, addDemandPlanConfigReqVo);
            }
            else
            {
                // 单例map获取线程池，不需要shutdown
                ThreadPoolExecutor pool = ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.fragmentThread);
                int dataSize = planList.size();
                int threadCount =
                    Math.floorMod(dataSize, fragmentSize) == 0 ? Math.floorDiv(dataSize, fragmentSize) : Math.floorDiv(dataSize, fragmentSize) + 1;
                CountDownLatch countDownLatch = new CountDownLatch(threadCount);
                for (Integer i = 0; i < threadCount; i++)
                {
                    final int start = i * fragmentSize;
                    final int fragmentNum = i;
                    final AddDemandPlanConfigReqVo subData = new AddDemandPlanConfigReqVo();
                    BeanUtils.copyProperties(addDemandPlanConfigReqVo, subData);
                    final List<DemandPlanConfigSkuVo> subPlanList = planList.stream().skip(start).limit(fragmentSize).collect(Collectors.toList());
                    subData.setPlanList(subPlanList);
                    pool.submit(() -> {
                        try
                        {
                            // 发送创建需求计划配置参数（需求计划版本）请求给阿里dataq
                            DataqResult temp = dataqService.invoke(HttpMethod.POST, path, null, null, subData);
                            log.info("fragment {} updateWarehouseDemandPlanData rsp:{}", fragmentNum, temp);
                        }
                        catch (Exception e)
                        {
                            log.error("fragment {} updateWarehouseDemandPlanData error has error.", fragmentNum, e);
                        }
                        finally
                        {
                            countDownLatch.countDown();
                            subPlanList.clear();
                        }
                    });
                }
                countDownLatch.await();
            }
        }
    }

    /**
     *
     * @Description 标记分仓需求计划数据
     * @param warehouseDemandPlanMarkDto
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 17:21
     */
    @Override
    public void markWarehouseDemandPlanData(WarehouseDemandPlanMarkDto warehouseDemandPlanMarkDto) throws Exception
    {
        // 保存产品小类和时间的标记状态
        String currentAccount = ServiceContextUtils.currentSession().getAccount().getName();
        Date currentDate = new Date();
        warehouseDemandPlanMarkDto.setCreator(currentAccount);
        warehouseDemandPlanMarkDto.setGmtCreate(currentDate);
        warehouseDemandPlanMarkDto.setLastModifier(currentAccount);
        warehouseDemandPlanMarkDto.setGmtModify(currentDate);

        warehouseDemandPlanDao.addWarehouseDemandPlanMark(warehouseDemandPlanMarkDto);
    }

    /**
     *
     * @Description 删除需求计划
     * @param deleteDemandPlanReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:09
     */
    @Override
    public void deleteWarehouseDemandPlan(DeleteDemandPlanReqVo deleteDemandPlanReqVo) throws Exception
    {
        // 阿里需要指定渠道需求计划主体为渠道
        deleteDemandPlanReqVo.setSubjectType(SubjectTypeEnum.warehouse);

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_DELETE"));
        dataqService.invoke(HttpMethod.POST, path, null, null, deleteDemandPlanReqVo);
    }


    /**
     *
     * @Description 查询渠道需求计划数据预测结果列表
     * @param channelDemandPlanReceiverDto
     * @return List<ChannelDemandPlanReceiverDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 17:37
     */
    @Override
    public List<ChannelDemandPlanReceiverDto> queryWarehouseDemandPlanDataForecastResultList(
        ChannelDemandPlanReceiverDto channelDemandPlanReceiverDto) throws Exception
    {
        List<ChannelDemandPlanReceiverDto> channelDemandPlanReceiverDtoList =
            dataqChannelDemandPlanDao.queryChannelDemandPlanReceiverListJoinForecast(channelDemandPlanReceiverDto);
        if (CollectionUtils.isEmpty(channelDemandPlanReceiverDtoList))
        {
            return Collections.EMPTY_LIST;
        }
        Map<String, List<ChannelDemandPlanReceiverDto>> map =
            channelDemandPlanReceiverDtoList.stream().collect(Collectors.groupingBy(ChannelDemandPlanReceiverDto::getSkuCode));

        List<ChannelDemandPlanReceiverDto> result = new ArrayList<>(map.size());
        for (Map.Entry<String, List<ChannelDemandPlanReceiverDto>> entry : map.entrySet())
        {
            String skuCode = entry.getKey();
            List<ChannelDemandPlanReceiverDto> list = entry.getValue();
            Map<String, PlanValue> dataMap = list.stream().collect(Collectors.toMap(ChannelDemandPlanReceiverDto::getWarehouseName,
                item -> {
                    PlanValue planValue = new PlanValue();
                    planValue.setPlanValue(item.getPlanValue());
                    planValue.setRate(item.getOutboundRate());
                    return planValue;
                }, (key1, key2) -> key2));

            ChannelDemandPlanReceiverDto channelDemandPlanReceiver = new ChannelDemandPlanReceiverDto();
            channelDemandPlanReceiver.setSkuCode(skuCode);
            channelDemandPlanReceiver.setDataMap(dataMap);
            result.add(channelDemandPlanReceiver);
        }

        return result;
    }

    /**
     *
     * @Description 查询分仓需求计划提报数据
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月19日 9:37
     */
    @Override
    public List<WarehouseDemandReportDto> queryWarehouseDemandPlanDataReportList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        warehouseDemandReportDto.setGroupColumn(GroupColumnEnum.warehouse.getColumnName());
        warehouseDemandReportDto.setSortColumn(GroupColumnEnum.warehouse.getSortColumn().getColumnName());
        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);
        List<WarehouseDemandReportDto> warehouseDemandReportList = warehouseDemandReportDao.queryWarehouseDemandReportGroupList(warehouseDemandReportDto);
        if (CollectionUtils.isEmpty(warehouseDemandReportList))
        {
            return Collections.EMPTY_LIST;
        }

        Map<String, ChannelDemandReportDataVo> map = new HashMap<>();
        for (WarehouseDemandReportDto warehouseDemandReport : warehouseDemandReportList)
        {
            List<ChannelDemandReportDataVo> dataList = JSON.parseArray(warehouseDemandReport.getData(), ChannelDemandReportDataVo.class);
            ChannelDemandReportDataVo data = new ChannelDemandReportDataVo();
            data.setBizDateValue(warehouseDemandReportDto.getBizDateValue());
            data.setOrderNum(dataList.stream().collect(Collectors.summarizingDouble(ChannelDemandReportDataVo::getOrderNum)).getSum());
            map.put(warehouseDemandReport.getWarehouseName(), data);
        }

        List<WarehouseDemandReportDto> result = new ArrayList<>(1);
        WarehouseDemandReportDto data = new WarehouseDemandReportDto();
        data.setDataMap(map);
        result.add(data);

        return result;
    }

    /**
     *
     * @Description 查询分仓需求计划列表
     * @return JSONArray
     * <AUTHOR>
     * @date 2024年02月29日 14:15
     */
    @Override
    public JSONArray queryWarehouseDemandPlanList() throws Exception
    {
        QueryWarehouseDemandPlanListReqVo queryWarehouseDemandPlanListReqVo = new QueryWarehouseDemandPlanListReqVo();
        queryWarehouseDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.warehouse);
        queryWarehouseDemandPlanListReqVo.setDeleted(false);

        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "demand_plan_code,demand_plan_name");
        param.put("order_by", "demand_plan_code");

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryWarehouseDemandPlanListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();

        return jsonArray;
    }

    /**
     *
     * @Description 新增分仓需求计划版本
     * @param warehouseDemandReportDto
     * @throws Exception
     * <AUTHOR>
     * @date 2024年04月08日 14:38
     */
    @Override
    public void addWarehouseDemandPlanVersion(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        // 分布式锁，dataq任务定时补偿机制里检查任务完成情况并视情况释放锁
        String lockKey =
            StringUtils.format(CommonConstants.REDIS_ADD_WAREHOUSE_DEMAND_PLAN_VERSION_LOCK_KEY, warehouseDemandReportDto.getDemandPlanCode(),
                warehouseDemandReportDto.getRollingVersion());
        boolean isLock = redisUtils.lock(lockKey, 1800);
        if (!isLock)
        {
            log.info("addWarehouseDemandPlanVersion has processing in another node.");
            return;
        }
        // 调用阿里数据探索服务，直接通过sql插入分仓需求提报数据
        String appCode = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_APPCODE_ADD_WAREHOUSE_DEMAND_PLAN_VERSION");

        ApiRunMicroAppRequest apiRunMicroAppRequest = new ApiRunMicroAppRequest();
        apiRunMicroAppRequest.setAppCode(appCode);
        Map<String, Object> map = new HashMap<>();
        map.put("demandPlanCode", warehouseDemandReportDto.getDemandPlanCode());
        map.put("versionId", warehouseDemandReportDto.getRollingVersion());
        map.put("planDateStart", warehouseDemandReportDto.getPlanDateStart());
        map.put("planDateEnd", warehouseDemandReportDto.getPlanDateEnd());
        map.put("creator", warehouseDemandReportDto.getCreator());
        apiRunMicroAppRequest.setApiParamValues(map);

        log.info("addWarehouseDemandPlanVersion param:{}", JSON.toJSONString(map));

        // 配置header
        Header header = new Header();
        header.setUserId(userId);
        header.setTenantCode(tenantCode);
        header.setWorkspaceCode(workspaceCode);

        // 执行算法调度任务
        MicroAppService service = dataIndustryContext.getService(MicroAppService.class);
        MicroAppTaskInstanceOutputVO execute = null;
        try
        {
            execute = service.execute(apiRunMicroAppRequest, header);
        }
        catch (Exception e)
        {
            log.error("addWarehouseDemandPlanVersion has exception:", e);
        }
        log.info("addWarehouseDemandPlanVersion.execute:{}", execute);

        if (Objects.isNull(execute))
        {
            execute = new MicroAppTaskInstanceOutputVO();
            Date currentTime = new Date();
            execute.setStatus(TaskStatus.FAILED);
            execute.setStartTime(currentTime);
            execute.setEndTime(currentTime);
        }

        // 新增阿里任务调度记录数据
        DataqTask dataqTask = new DataqTask();
        dataqTask.setTaskId(execute.getTaskInstanceId());
        dataqTask.setJobId("-1");
        dataqTask.setAppCode(appCode);
        dataqTask.setParam(JSON.toJSONString(map));
        dataqTask.setStatus(execute.getStatus().name());
        dataqTask.setReloadId(0L);
        dataqTask.setStartTime(execute.getStartTime());
        dataqTask.setEndTime(execute.getEndTime());
        dataqTask.setLockKey(lockKey);
        dataqTaskDao.addDataqTask(dataqTask);
    }
}
