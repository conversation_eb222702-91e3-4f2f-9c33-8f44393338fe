package cn.aliyun.ryytn.modules.demand.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.common.mq.MqFactory;
import cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.mybatis.MybatisUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.AsyncChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandPlanService;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandReportService;
import cn.aliyun.ryytn.modules.demand.dao.ChannelDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanDataSyncDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ConfirmChannelDemandPlanSubPlanGroupVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DeleteDemandPlanReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.dataqdao.DataqFreightPlanDao;
import cn.aliyun.ryytn.modules.scheduler.api.SchedulerService;
import cn.aliyun.ryytn.modules.scheduler.dao.SchedulerDao;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWeekListReqVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 异步渠道需求计划接口实现
 * <AUTHOR>
 * @date 2024/3/23 19:16
 */
@Slf4j
@Service
public class AsyncChannelDemandPlanServiceImpl implements AsyncChannelDemandPlanService
{
    @Autowired
    private CalendarService calendarService;

    @Autowired
    private WarehouseDemandPlanService warehouseDemandPlanService;

    @Autowired
    private WarehouseDemandReportService warehouseDemandReportService;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private SchedulerService schedulerService;

    @Autowired
    private ChannelDemandPlanDao channelDemandPlanDao;

    @Autowired
    private SchedulerDao schedulerDao;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private MybatisUtils mybatisUtils;

    @Autowired
    private DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Autowired
    private DataqFreightPlanDao dataqFreightPlanDao;

    @Autowired
    private DataqChannelDemandPlanDao dataqChannelDemandPlanDao;

    /**
     *
     * @Description 级联删除渠道需求计划后续业务数据
     * @param deleteDemandPlanReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月06日 15:39
     */
    @Async
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void deleteChannelDemandPlanCascade(DeleteDemandPlanReqVo deleteDemandPlanReqVo) throws Exception
    {
        // 查询计划对应的滚动版本生成任务，删除该任务
        SchedulerJob schedulerJob = schedulerDao.querySchedulerJobByServiceId(deleteDemandPlanReqVo.getDemandPlanCode());
        if (Objects.nonNull(schedulerJob))
        {
            schedulerService.deleteSchedulerJob(schedulerJob.getJobId());
        }

        // 删除渠道需求计划同步数据
        channelDemandPlanDao.deleteChannelDemandPlanDataSync(deleteDemandPlanReqVo.getDemandPlanCode());

        // 不需要调用阿里接口删除分仓需求计划，阿里在删除渠道需求计划的时候就级联删除分仓需求计划了
        warehouseDemandPlanService.deleteWarehouseDemandPlan(deleteDemandPlanReqVo);

        // 删除分仓需求提报
        warehouseDemandReportService.deleteWarehouseDemandReport(deleteDemandPlanReqVo.getDemandPlanCode());

        // 删除日分仓需求
        dailyWarehouseDemandDao.deleteDailyWarehouseDemand(deleteDemandPlanReqVo.getDemandPlanCode());

        // 删除日分仓调拨需求
        dailyWarehouseDemandDao.deleteDailyWarehouseAiPlanDemand(deleteDemandPlanReqVo.getDemandPlanCode());
        dailyWarehouseDemandDao.deleteAiplanAlgoList(deleteDemandPlanReqVo.getDemandPlanCode());

        // 删除调拨计划
        dataqFreightPlanDao.deleteFreightPlan(deleteDemandPlanReqVo.getDemandPlanCode());
    }

    /**
     *
     * @Description 同步渠道需求计划数据
     * @param demandPlanCode
     * @param dataList
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月29日 18:33
     */
    @Async
    public void syncChannelDemandPlanData(ConfirmChannelDemandPlanSubPlanGroupVo confirmChannelDemandPlanSubPlanGroupVo) throws Exception
    {
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());
        // 如果版本号不为空，则同步指定版本号
        if (StringUtils.isNotBlank(confirmChannelDemandPlanSubPlanGroupVo.getVersionId()))
        {
            queryChannelDemandPlanVersionListReqVo.setVersionId(confirmChannelDemandPlanSubPlanGroupVo.getVersionId());
        }
        // 如果版本号为空，取当前已共识最新版本号
        else
        {
            List<String> versionIdList = dataqChannelDemandPlanDao.queryConfirmedVersionList(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());
            if (CollectionUtils.isEmpty(versionIdList))
            {
                log.error("versionIdList is null,there is no version has confirmed.");
                return;
            }
            // 取最新的版本号
            String versionId = versionIdList.stream().max((str1, str2) -> {
                String[] v1 = StringUtils.split(str1, StringUtils.DATE_SEPARATOR);
                String[] v2 = StringUtils.split(str2, StringUtils.DATE_SEPARATOR);
                if (StringUtils.compare(v1[0], v2[0]) == 0)
                {
                    String index1 = v1.length == 1 ? StringUtils.ZERO : v1[1];
                    String index2 = v2.length == 1 ? StringUtils.ZERO : v2[1];
                    return StringUtils.compare(index1, index2);
                }
                else
                {
                    return StringUtils.compare(v1[0], v2[0]);
                }
            }).get();
            queryChannelDemandPlanVersionListReqVo.setVersionId(versionId);
        }

        // 解析dataq响应，所有版本拆分子计划的平铺数据
        List<QueryChannelDemandPlanVersionListRspVo> dataList =
            dataqChannelDemandPlanDao.queryChannelDemandPlanGroupBySkuChannel(queryChannelDemandPlanVersionListReqVo);

        if (CollectionUtils.isEmpty(dataList))
        {
            log.error("dataList is null,need not sync oms.");
            return;
        }

        // 获取计划数据涉及的时间点，并根据时间点获取日历周缓存
        Set<String> planDateSet = dataList.stream().map(item -> {
            return item.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
        }).collect(Collectors.toSet());
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateSet);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart, Function.identity(), (key1, key2) -> key1));

        // 共识时间月份周数
        String currentDay = DateUtils.getDate(DateUtils.YMD);
        QueryWeekListReqVo queryWeekListReqVo = new QueryWeekListReqVo();
        queryWeekListReqVo.setClearDate(currentDay);
        DataqWeek dataqWeek = calendarService.queryWeekList(queryWeekListReqVo).get(0);
        String monthWeek = dataqWeek.getMonthWeek();

        // 获取缓存同步数据特殊二级渠道配置
        List<String> spcialChannelCodeList = null;
        String specialChannelCodes =
            String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "SYNC_DEMAND_PLAN_DATA_SPECIAL_LV2CHANNELCODE"));
        if (StringUtils.isNotEmpty(specialChannelCodes))
        {
            spcialChannelCodeList = Arrays.asList(specialChannelCodes.split(StringUtils.COMMA_SEPARATOR));
        }
        else
        {
            spcialChannelCodeList = Collections.emptyList();
        }

        Set<String> skuCodeKeySet =
            dataList.stream().map(item -> {
                return StringUtils.format(CommonConstants.SKU_CACHE_KEY, item.getSkuCode());
            }).collect(Collectors.toSet());
        List<Object> objectList = redisUtils.multiGet(skuCodeKeySet);
        Map<String, SkuDto> skuMap = objectList.stream().filter(x->!Objects.isNull(x)).map(item -> {
            return (SkuDto) item;
        }).collect(Collectors.toMap(SkuDto::getSkuCode, Function.identity(),
            (key1, key2) -> key1));

        // 遍历计划数据，封装同步对象集合，并入库
        List<ChannelDemandPlanDataSyncDto> syncList = new ArrayList<>(dataList.size());
        for (QueryChannelDemandPlanVersionListRspVo item : dataList)
        {
            String planDate = null;
            // 特殊渠道，时间为周第一天
            if (spcialChannelCodeList.contains(item.getLv2ChannelCode()))
            {
                planDate = item.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
            }
            // 其他渠道，时间为周最后一天
            else
            {
                planDate = dataqWeekMap.get(item.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).getFsclWeekEnd();
            }

            if (Objects.isNull(item.getPlanValue()))
            {
                item.setPlanValue(0d);
            }
            Double syncPlanValue = item.getPlanValue();
            SkuDto skuDto = skuMap.get(item.getSkuCode());
            if (Objects.nonNull(skuDto))
            {
                BigDecimal dataPlanValue = new BigDecimal(item.getPlanValue());
                Double planUnitCnt = Objects.isNull(skuDto.getPlanUnitCnt()) || skuDto.getPlanUnitCnt().equals(0d) ? 1d : skuDto.getPlanUnitCnt();
                BigDecimal unitCnt = new BigDecimal(planUnitCnt);
                // 单位由提改为箱，多出的数据应该独占一箱，暂时RoundingMode按0位小数，有余就+1的逻辑处理。
                syncPlanValue = dataPlanValue.divide(unitCnt, 0, RoundingMode.UP).doubleValue();
            }

            String versionName =
                new StringBuilder().append(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode()).append(StringUtils.DATE_SEPARATOR)
                    .append(confirmChannelDemandPlanSubPlanGroupVo.getVersionId()).toString();

            ChannelDemandPlanDataSyncDto syncData = new ChannelDemandPlanDataSyncDto();
            syncData.setDemandPlanCode(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());
            syncData.setVersionId(confirmChannelDemandPlanSubPlanGroupVo.getVersionId());
            syncData.setVersionName(versionName);
            syncData.setLv2ChannelCode(item.getLv2ChannelCode());
            syncData.setSkuCode(item.getSkuCode());
            syncData.setPlanDate(planDate);
            syncData.setPlanValue(syncPlanValue);
            syncData.setMonthWeek(monthWeek);
            syncList.add(syncData);
        }

        // 删除该计划旧版本数据
        channelDemandPlanDao.deleteChannelDemandPlanDataSync(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());

        // 新增该计划共识版本数据，批量提交
        mybatisUtils.batchUpdateOrInsert(syncList, ChannelDemandPlanDao.class,
            (item, channelDemandPlanDao) -> channelDemandPlanDao.batchAddChannelDemandPlanDataSync(item));

        //发起异步线程,执行文件导出,并同步给oms
        OmsFileRecordDto omsFileRecordDto = new OmsFileRecordDto();
        omsFileRecordDto.setDemandPlanCode(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());
        omsFileRecordDto.setVersionId(confirmChannelDemandPlanSubPlanGroupVo.getVersionId());
        MqFactory.newProducerService().produce(CommonConstants.TOPIC_OMS_FILE, omsFileRecordDto);
    }
}
