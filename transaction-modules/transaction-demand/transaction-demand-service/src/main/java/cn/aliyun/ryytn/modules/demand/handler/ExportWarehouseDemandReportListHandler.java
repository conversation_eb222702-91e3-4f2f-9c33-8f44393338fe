package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.util.IOUtils;
import com.github.pagehelper.PageHelper;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandReportService;
import cn.aliyun.ryytn.modules.demand.dao.WarehouseDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto;

/**
 * @Description 导出分仓需求提报数据列表
 * <AUTHOR>
 * @date 2023/11/27 10:28
 */
@Component("exportWarehouseDemandReportList")
public class ExportWarehouseDemandReportListHandler extends AbstractExportExcelHandler
{
    @Autowired
    private WarehouseDemandReportDao warehouseDemandReportDao;

    @Autowired
    private WarehouseDemandReportService warehouseDemandReportService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 导出分仓需求提报数据列表
     * @param condition
     * @return byte[]
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 10:29
     */
    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());
        // 解析参数
        WarehouseDemandReportDto warehouseDemandReportVo =
            condition.getCondition().toJavaObject(WarehouseDemandReportDto.class);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportVo.getRollingVersion());
        // 只导出未修改的
        warehouseDemandReportVo.setIsModify(0);
        String tableSuffix = StringUtils.substring(warehouseDemandReportVo.getRollingVersion(), 2, 8);
        warehouseDemandReportVo.setTableSuffix(tableSuffix);

        // 数据权限
        warehouseDemandReportService.generateDataScope(warehouseDemandReportVo);

        List<String> bizDateValueList = warehouseDemandReportDao.queryWarehouseDemandReportHeadList(warehouseDemandReportVo);
        // 获取缓存中分仓需求提报时间对应的周对象
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);
        List<DataqWeek> dataqWeekList = objectList.stream().map(item -> {
            return (DataqWeek) item;
        }).sorted(Comparator.comparing(DataqWeek::getFsclWeekStart)).collect(Collectors.toList());

        Map<String, DataqWeek> dataqWeekMap =
            dataqWeekList.stream().collect(Collectors.toMap(DataqWeek::getFsclWeekStart, Function.identity(), (key1, key2) -> key1));

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream).excelType(ExcelTypeEnum.XLSX).build();
        try
        {
            // xlsx文件最大不能超过104W行
            int totalRowCount = warehouseDemandReportDao.queryWarehouseDemandReportCount(warehouseDemandReportVo);
            int rowId = 1;
            Integer pageSize = 1000000;
            if (totalRowCount <= pageSize)
            {
                // 查询数据库中的分仓需求提报数据
                List<WarehouseDemandReportDto> dataList = warehouseDemandReportDao.queryWarehouseDemandReportList(warehouseDemandReportVo);
                for (WarehouseDemandReportDto data : dataList)
                {
                    DataqWeek dataqWeek = dataqWeekMap.get(data.getBizDateValue());
                    data.setRowId(rowId++);
                    data.setWaresName(data.getSkuName());
                    data.setMonth(dataqWeek.getMonthLabel());
                    data.setWeek(StringUtils.WEEK_PREFIX_UPPER + dataqWeek.getWeekOfFsclMonth());
                }
                WriteSheet writeSheet = EasyExcel.writerSheet("分仓需求提报数据").head(WarehouseDemandReportDto.class).build();
                excelWriter.write(dataList, writeSheet);
                excelWriter.finish();
                dataList.clear();
            }
            else
            {
                Integer writeCount = totalRowCount % pageSize == 0 ? (totalRowCount / pageSize) : (totalRowCount / pageSize + 1);
                for (int i = 1; i <= writeCount; i++)
                {
                    PageHelper.startPage(i, pageSize);
                    // 查询数据库中的分仓需求提报数据
                    List<WarehouseDemandReportDto> dataList = warehouseDemandReportDao.queryWarehouseDemandReportList(warehouseDemandReportVo);
                    for (WarehouseDemandReportDto data : dataList)
                    {
                        DataqWeek dataqWeek = dataqWeekMap.get(data.getBizDateValue());
                        data.setRowId(rowId++);
                        data.setWaresName(data.getSkuName());
                        data.setMonth(dataqWeek.getMonthLabel());
                        data.setWeek(StringUtils.WEEK_PREFIX_UPPER + dataqWeek.getWeekOfFsclMonth());
                    }
                    WriteSheet writeSheet = EasyExcel.writerSheet("分仓需求提报数据" + i).head(WarehouseDemandReportDto.class).build();
                    excelWriter.write(dataList, writeSheet);
                    dataList.clear();
                }
                excelWriter.finish();
            }
            bytes = byteArrayOutputStream.toByteArray();
            byteArrayOutputStream.flush();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }
}
