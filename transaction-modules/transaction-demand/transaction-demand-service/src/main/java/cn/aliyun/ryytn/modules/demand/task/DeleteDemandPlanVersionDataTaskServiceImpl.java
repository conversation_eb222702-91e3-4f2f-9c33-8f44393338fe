package cn.aliyun.ryytn.modules.demand.task;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.modules.demand.dao.ChannelDemandPlanDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * @Description 删除渠道需求计划共识数据定时任务
 * <AUTHOR>
 * @date 2024/7/31 10:06
 */
@Slf4j
@Service
public class DeleteDemandPlanVersionDataTaskServiceImpl implements TaskService{

    @Autowired
    private ChannelDemandPlanDao channelDemandPlanDao;
    @Override
    public void process(SchedulerJob schedulerJob) throws Exception {
        log.info("start execute delete table t_ryytn_channel_demand_plan_data_sync");
        channelDemandPlanDao.truncateChannelDemandPlanDataSyncData();
        log.info("end execute delete table t_ryytn_channel_demand_plan_data_sync");
    }
}
