package cn.aliyun.ryytn.modules.demand.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.demand.api.SkuLockService;
import cn.aliyun.ryytn.modules.demand.dao.SkuLockDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.LockChannelDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.SkuLockDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySkuReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockRspVo;
import cn.aliyun.ryytn.modules.system.api.ChannelService;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;

/**
 * @Description 产品锁定期实现
 * <AUTHOR>
 * @date 2023/10/26 14:57
 */
@Service
public class SkuLockServiceImpl implements SkuLockService
{
    @Autowired
    private SkuLockDao skuLockDao;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private ChannelService channelService;

    /**
     *
     * @Description 分页查询产品锁定期
     * @param condition
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月26日 14:59     */
    @Override
    public PageInfo<SkuLockRspVo> pageSkuLockList(PageCondition<SkuLockDto> condition) throws Exception
    {
        // 初始化分页对象
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        SkuLockDto skuLockDto = condition.getCondition();

        // 查询数据库
        List<SkuLockRspVo> skuLockDtoList = skuLockDao.pageSkuLocList(skuLockDto);

        return new PageInfo<>(skuLockDtoList);
    }

    /**
     *
     * @Description 查询产品详情
     * @param querySkuReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月26日 17:01     */
    @Override
    public DataqResult<?> querySku(QuerySkuReqVo querySkuReqVo) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_SKULOCK_SKU_QUERY"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, querySkuReqVo);
        return dataqResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSkuLock(SkuLockDto skuLockDto) throws Exception
    {
        // 生产产品锁定期主键
        String skuLockId = SeqUtils.getSequenceUid();
        skuLockDto.setId(skuLockId);
        skuLockDto.setCreatedBy(ServiceContextUtils.currentSession().getAccount().getLoginId());

        // 插入产品锁定期数据
        skuLockDao.addSkuLock(skuLockDto);

        // 获取锁定渠道
        List<LockChannelDto> channels = skuLockDto.getChannelList();

        List<ChannelDto> channelDtos = channelService.queryChannelList();

        // 渠道id转换成name
        List<LockChannelDto> lockChannelList = getLockChannelDtos(channelDtos, channels, skuLockId);

        // 新增产品锁定渠道
        if (CollectionUtils.isNotEmpty(lockChannelList))
        {
            skuLockDao.addSkuLockChannel(lockChannelList);
        }
    }

    private static List<LockChannelDto> getLockChannelDtos(List<ChannelDto> channelDtos, List<LockChannelDto> channels, String skuLockId)
    {
        HashMap<String, String> channelMap = new HashMap<>();

        for (ChannelDto i : channelDtos)
        {
            channelMap.put(i.getLv2ChannelCode(), i.getLv2ChannelName());
        }

        // 产品锁定渠道新增list
        List<LockChannelDto> lockChannelList = new ArrayList<LockChannelDto>();

        // 获取产品锁定期列表
        for (LockChannelDto channel : channels)
        {
            LockChannelDto lockChannelDto = new LockChannelDto();
            lockChannelDto.setLockId(skuLockId);
            lockChannelDto.setChannelId(channel.getChannelId());
            lockChannelDto.setChannelName(channelMap.get(channel.getChannelId()));
            lockChannelList.add(lockChannelDto);
        }
        return lockChannelList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSkuLock(String id) throws Exception
    {
        skuLockDao.deleteSkuLock(id);

        skuLockDao.deleteLockChannelIds(id);
    }
}
