package cn.aliyun.ryytn.modules.demand.task;

import java.nio.charset.StandardCharsets;
import java.util.*;
import cn.aliyun.ryytn.common.utils.http.LowResponse;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.dao.OmsFileRecordDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto;
import cn.hutool.crypto.digest.MD5;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.http.HttpUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 同步渠道需求计划共识数据定时任务
 * <AUTHOR>
 * @date 2024/5/16 17:06
 */
@Slf4j
@Service
public class SyncDemandPlanVersionDataTaskServiceImpl implements TaskService
{
    @Autowired
    private OmsFileRecordDao omsFileRecordDao;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        log.info("sync channelDemandPlan data =========================xxxxxxxxxxxxxxxxxxxx.");
        List<OmsFileRecordDto> listSendFiles =  omsFileRecordDao.getSendOmsFiles();
        if(null == listSendFiles){
            log.warn("error同步渠道需求计划共识数据失败.sync channelDemandPlan data error, send Files to oms files is empty");
            return;
        }
        List<Map<String,Object>> listBody = new ArrayList<>();
        for(int i = 0 ;i <listSendFiles.size();i++){
            OmsFileRecordDto omsFileRecordDto = listSendFiles.get(i);
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("type", omsFileRecordDto.getFileType());
            requestBody.put("count", omsFileRecordDto.getFileCount());
            requestBody.put("url", omsFileRecordDto.getFileUrl());
            requestBody.put("timeout", DateUtils.formatDate(omsFileRecordDto.getExpireDate(), DateUtils.YMDHMS_STD));
            listBody.add(requestBody);
        }
        sendData2Oms(listBody,listSendFiles);
    }


    /**
     * 向oms系统发送请求接口,通知oms取数据进行处理
     * @param listBody
     */
    private void sendData2Oms(List<Map<String,Object>> listBody,List<OmsFileRecordDto> omsFiles){
        String token = getLoginToken();
        if(StringUtils.isEmpty(token)){
            log.error("error同步渠道需求计划共识数据失败.sync channelDemandPlan data error,reason get token is null error.");
        }else{
            //服务器地址
            String omsServer = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OMS_SERVER"));
            //认养接口
            String omsSyncChannelDemandPlanUrl = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OMS_SYNC_CHANNEL_DEMAND_PLAN_URL"));
            String omsUrl = omsServer + omsSyncChannelDemandPlanUrl;
            Map<String, Object> head = new HashMap<>();
            head.put("Authorization",getOmsGateWayAuthorization(omsSyncChannelDemandPlanUrl));
            head.put("r3-api-token",token);
            head.put("version","2.0.0");
            log.info("sync channelDemandPlan data request:{}", JSON.toJSONString(listBody));
            LowResponse lowResponse = HttpUtils.postJson(omsUrl, listBody, head);
            if(null == lowResponse){
                log.error("error同步渠道需求计划共识数据失败.sync channelDemandPlan data error, response null .error");
            }else{
                if(lowResponse.getHttpCode() == HttpStatus.OK.value()){
                    log.info("sync channelDemandPlan data,oms response boyd is :{}",lowResponse.getBodyString());
                    Map<String,Object> respResult = lowResponse.getBodyJsonObj(Map.class);
                    int code =  (int)respResult.get("code");
                    String msg = (String)respResult.get("message");
                    if(msg.length() > 1024){
                        msg = msg.substring(0,1000);
                    }
                    String description = msg;
                    if(code == 0){
                        log.info("send result code is 0,success");
                        //先备份再清理
                        omsFiles.stream().forEach(x->{
                            x.setSuccessFlag(1);
                            x.setDescription(description);
                        });
                        omsFileRecordDao.updateSendResult(omsFiles);
                    }else{
                        log.error("error同步渠道需求计划共识数据失败.sync channelDemandPlan data error,  error.msg is :{}",msg);
                        omsFiles.stream().forEach(x->{
                            x.setSuccessFlag(-1);
                            x.setDescription(description);
                        });
                        omsFileRecordDao.updateSendResult(omsFiles);
                    }
                }else{
                    omsFiles.stream().forEach(x->{
                        x.setSuccessFlag(-1);
                        x.setDescription("发送失败,接口调用回"+lowResponse.getHttpCode());
                    });
                    omsFileRecordDao.updateSendResult(omsFiles);
                    log.error("error同步渠道需求计划共识数据失败.sync channelDemandPlan data error,sync channelDemandPlan data  response status is not Ok 200");
                }
            }
        }
    }


    /**
     * 登录获取token
     * @return 返回token
     */
    private String getLoginToken(){
        //服务器地址
        String omsServer = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OMS_SERVER"));
        String loginUrl = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OMS_LOGIN_URL"));
        String userName = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OMS_USER_NAME"));
        String userKey = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OMS_USER_KEY"));
        String requestSign = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OMS_REQUEST_SIGN"));
        String url = omsServer + loginUrl + "?userName="+userName+"&userKey="+userKey+"&requestSign="+requestSign;
        log.info("getLoginToken request url is {}",url);
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("Authorization",getOmsGateWayAuthorization(loginUrl+ "?userName="+userName+"&userKey="+userKey+"&requestSign="+requestSign));
        headerMap.put("version","2.0.0");
        LowResponse lowResponse = HttpUtils.getR(url,new HashMap<>(),headerMap);
        if(null == lowResponse){
            log.error("getLoginToken response null");
            return null;
        }
        if(lowResponse.getHttpCode() == HttpStatus.OK.value()){
            String responseBody = lowResponse.getBodyString();
            log.info("getLoginToken response content is :{}",responseBody);
            Map<String,Object> respResult = lowResponse.getBodyJsonObj(Map.class);
            boolean success = (boolean)respResult.get("success");
            if(success){
                String loginToken =  (String)respResult.get("loginToken");
                log.info("get loginToken is :{}",loginToken);
                return loginToken;
            }

        }else{
            log.error("getLoginToken response status is not Ok 200");

        }
        return null;
    }

    /**
     * 根据请求路径获取authorization字符串
     * @param requestUrl
     * @return 返回授权信息字符串
     */
    private String getOmsGateWayAuthorization(String requestUrl) {
        String appKey = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OMS_APP_KEY"));
        String appSecret = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OMS_APP_SECRET"));
        Map<String,String> params = new LinkedHashMap<>();
        params.put("timestamp",String.valueOf(System.currentTimeMillis()));
        params.put("appKey",appKey);
        params.put("alg","MD5");
        String paramsJosn = JSON.toJSONString(params);
        log.info("getOmsGateWayAuthorization params before base64 encode json is :{}",paramsJosn);
        String base64Params = Base64.getEncoder().encodeToString(paramsJosn.getBytes());
        log.info("getOmsGateWayAuthorization params general base64 encode json is :{}",base64Params);

        String md5Original = base64Params+requestUrl+appSecret;
        log.info("before md5 string is :{}",md5Original);
        String md5String = MD5.create().digestHex(md5Original,
                StandardCharsets.UTF_8);
        log.info("md5 general md5String string :{}",md5String);
        String result = base64Params + "."+ md5String.toUpperCase();
        log.info("general Authorization string is:{}",result);
        return result;
    }

}
