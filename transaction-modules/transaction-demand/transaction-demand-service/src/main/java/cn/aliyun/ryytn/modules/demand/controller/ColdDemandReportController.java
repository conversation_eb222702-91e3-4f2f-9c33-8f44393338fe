package cn.aliyun.ryytn.modules.demand.controller;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.ColdDemandReportService;
import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 低温需求提报
 * <AUTHOR>
 * @date 2023/11/27 15:46
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/coldDemandReport")
@Api(tags = "低温需求提报")
public class ColdDemandReportController
{
    @Autowired
    private ColdDemandReportService coldDemandReportService;

    /**
     *
     * @Description 查询低温需求提报版本列表
     * @param demandPlanCode
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 16:33
     */
    @PostMapping("queryColdDemandReportVersionList")
    @ResponseBody
    @ApiOperation("查询低温需求提报版本列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryColdDemandReportVersionList() throws Exception
    {
        List<String> result = coldDemandReportService.queryColdDemandReportVersionList();

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询低温需求提报列表
     * @param ColdDemandReportDto
     * @return ResultInfo<BaseTable < List < ColdDemandReportDto>>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月30日 20:52
     */
    @PostMapping("queryColdDemandReportList")
    @ResponseBody
    @ApiOperation("查询低温需求提报列表")
    @RequiresPermissions(value = {})
    public ResultInfo<BaseTable<List<ColdDemandReportDto>>> queryColdDemandReportList(@RequestBody ColdDemandReportDto coldDemandReportDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(coldDemandReportDto);

        BaseTable<List<ColdDemandReportDto>> result = null;
        if (StringUtils.isNotBlank(coldDemandReportDto.getRollingVersion()))
        {
            result = coldDemandReportService.queryColdDemandReportList(coldDemandReportDto);
        }

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 修改低温需求提报数据
     * @param dataList
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:15
     */
    @PostMapping("updateColdDemandReportData")
    @ResponseBody
    @ApiOperation("修改低温需求提报数据")
    @RequiresPermissions(value = {})
    public ResultInfo<?> updateColdDemandReportData(@RequestBody List<ColdDemandReportDto> dataList) throws Exception
    {
        if (CollectionUtils.isNotEmpty(dataList))
        {
            coldDemandReportService.updateColdDemandReportData(dataList);
        }

        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询低温需求提报动态表头
     * @param queryChannelDemandReportListReqVo
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年3月1日 14:15
     */
    @PostMapping("queryColdDemandReportHeadList")
    @ResponseBody
    @ApiOperation("查询低温需求提报动态表头")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryColdDemandReportHeadList(@RequestBody ColdDemandReportDto coldDemandReportDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(coldDemandReportDto);
        if (Objects.isNull(coldDemandReportDto.getBizDateType()))
        {
            coldDemandReportDto.setBizDateType(BizDateTypeEnum.DAY);
        }
        List<String> result = Collections.EMPTY_LIST;
        if (StringUtils.isNotBlank(coldDemandReportDto.getRollingVersion()))
        {
            result = coldDemandReportService.queryColdDemandReportHeadList(coldDemandReportDto);
        }
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询低温需求提报表头下拉列表
     * @param warehouseDemandReportDto
     * @return ResultInfo<List < ColdDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @PostMapping("queryColdDemandReportHeadSelect")
    @ResponseBody
    @ApiOperation("查询低温需求提报表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<ColdDemandReportDto>> queryColdDemandReportHeadSelect(@RequestBody ColdDemandReportDto coldDemandReportDto) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(coldDemandReportDto);
        ValidateUtil.checkIsNotEmpty(coldDemandReportDto.getGroupColumnList());

        List<ColdDemandReportDto> result = Collections.EMPTY_LIST;
        if (StringUtils.isNotBlank(coldDemandReportDto.getRollingVersion()))
        {
            result = coldDemandReportService.queryColdDemandReportHeadSelect(coldDemandReportDto);
        }
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询低温需求提报分组聚合数据列表
     * @param queryChannelDemandReportListReqVo
     * @return ResultInfo<List < QueryChannelDemandReportGroupListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月01日 16:17
     */
    @PostMapping("queryColdDemandReportListGroupBy")
    @ResponseBody
    @ApiOperation("查询低温需求提报分组聚合数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<ColdDemandReportDto>> queryColdDemandReportListGroupBy(@RequestBody ColdDemandReportDto coldDemandReportDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(coldDemandReportDto);
        ValidateUtil.checkIsNotEmpty(coldDemandReportDto.getGroupColumnList());
        if (Objects.isNull(coldDemandReportDto.getBizDateType()))
        {
            coldDemandReportDto.setBizDateType(BizDateTypeEnum.DAY);
        }

        List<ColdDemandReportDto> result = Collections.EMPTY_LIST;
        if (StringUtils.isNotBlank(coldDemandReportDto.getRollingVersion()))
        {
            result = coldDemandReportService.queryColdDemandReportListGroupBy(coldDemandReportDto);
        }
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询低温需求提报明细数据列表
     * @param condition
     * @return ResultInfo<PageInfo < QueryChannelDemandReportListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月01日 16:17
     */
    @PostMapping("queryColdDemandReportDataPage")
    @ResponseBody
    @ApiOperation("分页查询低温需求提报明细数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<ColdDemandReportDto>> queryColdDemandReportDataPage(@RequestBody PageCondition<ColdDemandReportDto> condition) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());
        if (Objects.isNull(condition.getCondition().getBizDateType()))
        {
            condition.getCondition().setBizDateType(BizDateTypeEnum.DAY);
        }

        PageInfo<ColdDemandReportDto> result = null;
        if (StringUtils.isNotBlank(condition.getCondition().getRollingVersion()))
        {
            result = coldDemandReportService.queryColdDemandReportDataPage(condition);
        }
        else
        {
            result = new PageInfo<>();
        }

        return ResultInfo.success(result);
    }
}
