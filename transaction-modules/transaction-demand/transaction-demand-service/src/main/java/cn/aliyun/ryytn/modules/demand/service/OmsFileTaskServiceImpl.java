package cn.aliyun.ryytn.modules.demand.service;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.mq.MqFactory;
import cn.aliyun.ryytn.common.mq.MqRecord;
import cn.aliyun.ryytn.common.mq.api.ConsumerService;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.oss.OssUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.ChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.constant.OmsFileTypeEnum;
import cn.aliyun.ryytn.modules.demand.dao.ChannelDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.dao.OmsFileRecordDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanDataSyncDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListRspVo;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.*;

/**
 * @Description oms文件生成实现类
 * <AUTHOR>
 * @date 2024/7/10 9:07
 */
@Service
@Slf4j
public class OmsFileTaskServiceImpl implements  Observer, ApplicationListener<ContextRefreshedEvent>
{

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private ChannelDemandPlanDao channelDemandPlanDao;
    @Autowired
    private ChannelDemandPlanService channelDemandPlanService;
    private ConsumerService consumerService;
    /**
     * 阿里云OSS工具类
     */
    @Autowired
    private OssUtils ossUtils;
    @Autowired
    private OmsFileRecordDao omsFileRecordDao;
    /**
     * 阿里云OSS服务桶名称
     */
    @Value("${aliyun.oss.bucket:transaction}")
    private String defaultBucket;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event)
    {
        consumerService = MqFactory.newConsumerService(CommonConstants.TOPIC_OMS_FILE);
        consumerService.addObservers(this);
    }

    @Override
    public void update(Observable o, Object object)
    {
        if (Objects.isNull(object) || StringUtils.isBlank(((MqRecord) object).getValue()))
        {
            return;
        }
        MqRecord mqRecord = (MqRecord) object;
        String value = mqRecord.getValue();
        OmsFileRecordDto demandPlan = JSON.parseObject(value, OmsFileRecordDto.class);
        try
        {
            while (!redisUtils.locks(CommonConstants.REDIS_OMS_FILE_LOCK_KEY, demandPlan.getDemandPlanCode(), 1800))
            {
                // sleep30秒
                Thread.sleep(30000);
            }
            executeExportOmsFile(demandPlan.getDemandPlanCode(),demandPlan.getVersionId());
        }
        catch (Exception e)
        {
            // 让消费线程知道异常，回写mq
            throw (RuntimeException) e;
        }
        finally {
            redisUtils.unlock(CommonConstants.REDIS_OMS_FILE_LOCK_KEY);
        }
        // 消息回执
        consumerService.ack(mqRecord);
    }


    /**
     * 导出oms文件
     * @param demandPlanCode
     */
    private void executeExportOmsFile(String demandPlanCode,String versionId) throws Exception{

        log.info("start executeExportOmsFile,demandPlanCode:{},versionId:{}",demandPlanCode,versionId);
        int fileType = 0;
        List<QueryChannelDemandPlanListRspVo> channelDemandPlanList = channelDemandPlanService.queryChannelDemandPlanList(new QueryChannelDemandPlanListReqVo());
        log.info("get channelDemandPlanList is :{}",channelDemandPlanList);
        QueryChannelDemandPlanListRspVo channelDemandPlan = channelDemandPlanList.stream().filter(x->x.getDemandPlanCode().equals(demandPlanCode)).findFirst().orElse(null);
        if(null == channelDemandPlan){
            log.error("not found channDemandPlan,demandPlanCode:{},return",demandPlanCode);
            return;
        }
        String channelDemandPlanName = channelDemandPlan.getDemandPlanName();
        if(channelDemandPlanName.indexOf("液奶") != -1){
            fileType = OmsFileTypeEnum.MILK.getType();
        }
        if(channelDemandPlanName.indexOf("奶粉") != -1){
            fileType = OmsFileTypeEnum.MILK_POWDER.getType();
        }
        if(fileType == 0){
            log.error("channelDemandPlanName:{} is error,can not found keywords,return",channelDemandPlanName);
            return;
        }
        ChannelDemandPlanDataSyncDto channelDemandPlanDataSyncDto = new ChannelDemandPlanDataSyncDto();
        channelDemandPlanDataSyncDto.setDemandPlanCode(demandPlanCode);
        List<ChannelDemandPlanDataSyncDto> syncList = channelDemandPlanDao.queryChannelDemandPlanDataSyncList(channelDemandPlanDataSyncDto);
        int count = syncList.size();
        if(count < 1){
            log.warn("demandplanversion confirm data size is empty.no need export xlsx file.{}", JSONObject.toJSONString(channelDemandPlanDataSyncDto));
            return;
        }
        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream, ChannelDemandPlanDataSyncDto.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("渠道需求计划共识数据").doWrite(syncList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.closeQuietly(byteArrayOutputStream);
        }
        // 上传文件
        String fileId = new StringBuilder(SeqUtils.getSequenceUid()).append(ExcelTypeEnum.XLSX.getValue()).toString();

        ObjectMetadata metadata = new ObjectMetadata();
        // 过期时间7天
        Date expireDate = DateUtils.addDays(new Date(), 7);
        String expireTime = DateUtils.cst2utc(expireDate, DateUtils.YMDHMS_STD_UTC);
        metadata.setHeader(OSSHeaders.EXPIRES, expireTime);
        ossUtils.uploadFile(fileId, bytes, metadata);

        // 设置请求头。
        Map<String, String> headers = new HashMap<String, String>();
        // 设置用户自定义元数据。
        Map<String, String> userMetadata = new HashMap<String, String>();
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(defaultBucket, fileId, HttpMethod.GET);
        // 设置过期时间。
        request.setExpiration(expireDate);
        // 将请求头加入到request中。
        request.setHeaders(headers);
        // 添加用户自定义元数据。
        request.setUserMetadata(userMetadata);
        // 生成签名URL。
        URL url = ossUtils.generatePresignedUrl(request);
        if (Objects.isNull(url))
        {
            log.error("oms sync plan file,oss generatePresignedUrl url is null.");
            return;
        }
        OmsFileRecordDto omsFileRecordDto = new OmsFileRecordDto();
        omsFileRecordDto.setId(SeqUtils.getSequenceUid());
        omsFileRecordDto.setDemandPlanCode(demandPlanCode);
        omsFileRecordDto.setFileType(fileType);
        omsFileRecordDto.setFileCount(count);
        omsFileRecordDto.setFileUrl(url.toString());
        omsFileRecordDto.setExpireDate(expireDate);
        omsFileRecordDto.setRecordTime(new Date());
        omsFileRecordDto.setVersionId(versionId);
        omsFileRecordDto.setSuccessFlag(0);//未同步
        //先更新历史数据,同计划的数据,更新为无效,不用发送
        omsFileRecordDao.updateNoSendRecordInvalid(omsFileRecordDto);
        omsFileRecordDao.addOmsFileRecord(omsFileRecordDto);
        log.info("end executeExportOmsFile,demandPlanCode:{},versionId:{}",demandPlanCode,versionId);
    }

}
