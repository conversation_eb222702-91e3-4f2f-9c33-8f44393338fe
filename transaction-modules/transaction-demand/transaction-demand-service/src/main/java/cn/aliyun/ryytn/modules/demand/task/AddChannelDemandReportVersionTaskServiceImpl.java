package cn.aliyun.ryytn.modules.demand.task;

import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportTemplateDataDto;
import com.google.common.base.Throwables;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.aliyun.dataq.dataindustry.DataIndustrySpringServiceContext;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.concurrent.ThreadPoolExecutorUtils;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.dao.ChannelDemandReportDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportVersionVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ImportChannelDemandReportVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo;
import cn.aliyun.ryytn.modules.scheduler.dao.DataqTaskDao;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import cn.aliyun.ryytn.modules.system.api.ChannelService;
import cn.aliyun.ryytn.modules.system.api.ProductService;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWeekListReqVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

/**
 * @Description 新增渠道需求提报版本任务实现类
 * <AUTHOR>
 * @date 2023/11/7 14:15
 */
@Slf4j
@Service
public class AddChannelDemandReportVersionTaskServiceImpl implements TaskService
{
    @Autowired
    private CalendarService calendarService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Resource(name = "dataIndustryContext")
    private DataIndustrySpringServiceContext dataIndustryContext;

    @Value("${dataq.scheduler.userId}")
    private String userId;

    @Value("${dataq.scheduler.tenantCode}")
    private String tenantCode;

    @Value("${dataq.scheduler.workspaceCode}")
    private String workspaceCode;

    @Autowired
    private DataqTaskDao dataqTaskDao;

    @Autowired
    private DataqChannelDemandReportDao dataqChannelDemandReportDao;

    @Autowired
    private ChannelDemandReportDao channelDemandReportDao;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        // 获取版本起始周，大于当前时间的第一个周数据
//        String currentDate = DateUtils.getDate(DateUtils.YMD);
        generateData(false,schedulerJob.getJobId());
    }


    @Data
    public class AgainTaskThread extends  Thread{

        private boolean isAgain;
        private String jobId;
        public AgainTaskThread(boolean isAgain,String jobId){
            this.isAgain = isAgain;
            this.jobId = jobId;
        }
        @SneakyThrows
        @Override
        public  void run(){
            try {
                sleep(5 * 60000);
            } catch (InterruptedException e) {
                log.warn("sleep InterruptedException error:{}", Throwables.getStackTraceAsString(e));
            }
            log.info("retry call generateData,isAgain:{},jobId:{}",isAgain,jobId);
            try {
                generateData(isAgain, jobId);
            }catch (Exception e){
                log.error("渠道需求提报数据生成失败",e);
            }
        }
    }

    public void generateData(boolean isAgain,String jobId)throws Exception{
//        String currentDate = DateUtils.getDate(DateUtils.getMondayByWeek(new Date(),"yyyyMMdd"));
        String currentDate = DateUtils.getDate(DateUtils.YMD);
        Set<String> set = redisUtils.hKeys(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY);
        String versionWeekStart = set.stream().filter(item -> {
            //修改为大于等于当前时间的第一个周数据，取下一周数据
            return item.compareTo(currentDate) > 0;
        }).sorted(Comparator.comparing(String::valueOf)).limit(1).collect(Collectors.toList()).get(0);
        DataqWeek dataqWeek = (DataqWeek) redisUtils.hget(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, versionWeekStart);
        String rollingVersion = "CDP" + dataqWeek.getYearMonthWeek();

        // 获取版本开始日期和结束日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.parseDate(versionWeekStart, DateUtils.YMD));
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        String beginDate = DateUtils.formatTime(calendar.getTime(), DateUtils.YMD);
        calendar.add(Calendar.MONTH, 3);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        String endDate = DateUtils.formatTime(calendar.getTime(), DateUtils.YMD);

        // 获取上一版本号
        String lastRollingVersion = null;
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("group_by", "rolling_version");
//        paramMap.put("order_by", "rolling_version desc");
//        paramMap.put("page_token", "1");
//        paramMap.put("page_size", "1");

        ChannelDemandReportVersionVo channelDemandReportVersionVo = new ChannelDemandReportVersionVo();
        channelDemandReportVersionVo.setBizDateType(BizDateTypeEnum.WEEK);

        boolean getVersionSuccess = true;
        // 获取上一版本异常，不阻塞生成任务
        try
        {
//            String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_VERSION_LIST_V2"));
//            DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, channelDemandReportVersionVo);
//            JSONArray jsonArray = (JSONArray) dataqResult.getData();
//            List<ChannelDemandReportVersionVo> versionList = jsonArray.toJavaList(ChannelDemandReportVersionVo.class);
            lastRollingVersion = dataqChannelDemandReportDao.queryMaxRollingVersion();
        }
        catch (Exception e)
        {
            log.error("error渠道需求提报版本生成失败.Get lastRollingVersion has exception.");
            getVersionSuccess = false;
        }
        log.info("channelDemandVersion lastRollingVersion:{}",lastRollingVersion);
        if(!getVersionSuccess){
            if(!isAgain){//如果是第二次,就不再发起
                new AgainTaskThread(true,jobId).start();
            }
            return;
        }
        // 阿里接口上个版本号必填，设置默认为不存在的版本号
        if (StringUtils.isBlank(lastRollingVersion))
        {
            lastRollingVersion = "XXXX";
        }

        // 低温纯奶和低温酸奶不生成提报数据，过滤逻辑由阿里实现

        // 查询是否已触发生成该版本数据，如果已触发生成，直接退出
        // 由于quartz通过DB分布式锁控制任务在分布式环境下不会重复触发，此处直接查询数据库校验即可
        ChannelDemandReportVersionDto channelDemandReportVersionDto = channelDemandReportDao.queryChannelDemandReportVersion(rollingVersion);
        if (Objects.nonNull(channelDemandReportVersionDto))
        {
            log.info("{} has exists.", rollingVersion);
            return;
        }
//        int num = dataqChannelDemandReportDao.queryChannelDemandReportVersionExists(rollingVersion);
//        if (num > 0)
//        {
//            log.info("{} has exists.", rollingVersion);
//            return;
//        }

        // 新增本地数据库版本数据，由于通过阿里接口生成，所以无法保证事务，通过dataq任务补偿机制补偿
        channelDemandReportVersionDto = new ChannelDemandReportVersionDto();
        channelDemandReportVersionDto.setId(SeqUtils.getSequenceUid());
        channelDemandReportVersionDto.setRollingVersion(rollingVersion);
        channelDemandReportVersionDto.setCreator("定时任务");
        // 2024/03/28：由于周二自动生成下一周的提报版本，生成下周版本后，进场有人误操作到下周版本中，客户要求生成版本默认锁定状态
        //20240720：现在客户要求当周生成当周版本，默认不需要锁定
        channelDemandReportVersionDto.setIsLocked(1);
        channelDemandReportDao.addChannelDemandReportVersion(channelDemandReportVersionDto);

//        // 调用阿里提供创建版本接口
//        String appCode = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_APPCODE_ADD_CHANNEL_DEMAND_REPORT_VERSION");
//
//        ApiRunMicroAppRequest apiRunMicroAppRequest = new ApiRunMicroAppRequest();
//        apiRunMicroAppRequest.setAppCode(appCode);
//        Map<String, Object> map = new HashMap<>();
//        map.put("rollingVersion", rollingVersion);
//        map.put("lastRollingVersion", lastRollingVersion);
//        map.put("beginDate", beginDate);
//        map.put("endDate", endDate);
//        apiRunMicroAppRequest.setApiParamValues(map);
//
//        log.info("addChannelDemandReportVersion param:{}", JSON.toJSONString(map));

        //

        String getedLastRollingVersion = lastRollingVersion;
        ThreadPoolExecutor pool = ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.channelDemandReportTemplateThread);
        pool.submit(()->{
            StopWatch createChannelDemandReportTemplateDataClock = new StopWatch("createChannelDemandReportTemplateData");
            createChannelDemandReportTemplateDataClock.start("insertData");
            ChannelDemandReportTemplateDataDto channelDemandReportTemplateDataDto = new ChannelDemandReportTemplateDataDto();
            channelDemandReportTemplateDataDto.setCreator("sys");
            channelDemandReportTemplateDataDto.setRollingVersion(rollingVersion);
            channelDemandReportTemplateDataDto.setLastRollingVersion(getedLastRollingVersion);
            channelDemandReportTemplateDataDto.setBeginDate(beginDate);
            channelDemandReportTemplateDataDto.setEndDate(endDate);
            log.info("createChannelDemandReportTemplateData param is :{}",JSON.toJSONString(channelDemandReportTemplateDataDto));
            int records = -1;
            try {
                records = dataqChannelDemandReportDao.createChannelDemandReportTemplateData(channelDemandReportTemplateDataDto);
            } catch (Exception e) {
                log.error("渠道需求提报数据生成失败",e);
            }
            if(records ==-1){
                log.error("渠道需求提报数据生成失败");
            }else{
                log.error("渠道需求提报数据生成成功");
            }
            createChannelDemandReportTemplateDataClock.stop();
            log.info("createChannelDemandReportTemplateData data insert cost:{}s ",createChannelDemandReportTemplateDataClock.getTotalTimeSeconds());
            log.info("createChannelDemandReportTemplateData data insert cost:{}",createChannelDemandReportTemplateDataClock.prettyPrint());
        });
//
//        // 配置header
//        Header header = new Header();
//        header.setUserId(userId);
//        header.setTenantCode(tenantCode);
//        header.setWorkspaceCode(workspaceCode);
//
//        // 执行算法调度任务
//        MicroAppService service = dataIndustryContext.getService(MicroAppService.class);
//        MicroAppTaskInstanceOutputVO execute = null;
//        try
//        {
//            execute = service.execute(apiRunMicroAppRequest, header);
//        }
//        catch (Exception e)
//        {
//            log.error("error渠道需求提报版本生成失败.addChannelDemandReportVersion has exception:", e);
//        }
//        log.info("addChannelDemandReportVersion.execute:{}", execute);
//
//        if (Objects.isNull(execute))
//        {
//            execute = new MicroAppTaskInstanceOutputVO();
//            Date currentTime = new Date();
//            execute.setStatus(TaskStatus.FAILED);
//            execute.setStartTime(currentTime);
//            execute.setEndTime(currentTime);
//        }
//
//        // 新增阿里任务调度记录数据
//        DataqTask dataqTask = new DataqTask();
//        dataqTask.setTaskId(execute.getTaskInstanceId());
//        dataqTask.setJobId(jobId);
//        dataqTask.setAppCode(appCode);
//        dataqTask.setParam(JSON.toJSONString(map));
//        dataqTask.setStatus(execute.getStatus().name());
//        dataqTask.setReloadId(0L);
//        dataqTask.setStartTime(execute.getStartTime());
//        dataqTask.setEndTime(execute.getEndTime());
//        dataqTaskDao.addDataqTask(dataqTask);
    }

    /**
     *
     * @Description 废弃方法，修改方案为业务应用触发，提报版本数据由阿里生成
     * 新代码稳定后删除此方法
     * @param param
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月08日 15:14
     */
    @Deprecated
    public void processOld(String param) throws Exception
    {
        // 查询当前日期周数据
        String currentDate = DateUtils.formatTime(new Date(), DateUtils.YMD);
        QueryWeekListReqVo queryWeekListReqVo = new QueryWeekListReqVo();
        queryWeekListReqVo.setClearDate(currentDate);
        List<DataqWeek> dataqWeekList = calendarService.queryWeekList(queryWeekListReqVo);
        if (CollectionUtils.isEmpty(dataqWeekList))
        {
            log.error("dataq has no week in calendar:{}", currentDate);
            return;
        }
        String currentWeekStart = dataqWeekList.get(0).getFsclWeekStart();

        // 获取日期缓存
        Set<String> weekItemSet = redisUtils.hKeys(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY);
        // 过滤日期缓存item集合，获取下周第一周日期
        String nextVersionDate = weekItemSet.stream().filter(item -> {
            return item.compareTo(currentWeekStart) > 0;
        }).min(String::compareTo).get();
        DataqWeek nextWeek = (DataqWeek) redisUtils.hget(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, nextVersionDate);

        String rollingVersion = "CDP" + nextWeek.getYearMonthWeek();

        // 封装时间周期集合：当前月+下三月的周数据
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.parseDate(nextVersionDate, DateUtils.YMD));
        Set<String> bizDateValueList = new HashSet<>();
        for (int i = 0; i < 4; i++)
        {
            String yearMonth = DateUtils.formatTime(calendar.getTime(), DateUtils.YM);
            Set<String> temp = weekItemSet.stream().filter(item -> {
                return item.startsWith(yearMonth);
            }).collect(Collectors.toSet());
            bizDateValueList.addAll(temp);
            calendar.add(Calendar.MONTH, 1);
        }

        // 查询所有产品
        List<SkuDto> skuList = productService.querySkuList(null).stream().collect(Collectors.toList());
        // TODO  低温纯奶和低温酸奶不生成提报数据，过滤逻辑待确认

        // 查询所有渠道
        List<ChannelDto> channelList = channelService.queryChannelList().stream().collect(Collectors.toList());

        // 查询上一周
        Map<ImportChannelDemandReportVo, Double> latestVersionReportMap = Collections.emptyMap();
        String lastVersionDate = weekItemSet.stream().filter(item -> {
            return item.compareTo(currentWeekStart) < 0;
        }).max(String::compareTo).get();
        DataqWeek lastWeek = (DataqWeek) redisUtils.hget(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, lastVersionDate);
        if (Objects.nonNull(lastWeek))
        {
            String latestVersion = "CDP" + lastWeek.getYearMonthWeek();
            QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo = new QueryChannelDemandReportListReqVo();
            queryChannelDemandReportListReqVo.setRollingVersion(latestVersion);
            queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
            queryChannelDemandReportListReqVo.setIsModify(0);

            String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_LIST"));
            DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandReportListReqVo);
            JSONArray jsonArray = (JSONArray) dataqResult.getData();
            if (CollectionUtils.isNotEmpty(jsonArray))
            {
                List<ImportChannelDemandReportVo> latestVersionReportList = jsonArray.toJavaList(ImportChannelDemandReportVo.class);

                // 过滤与下一版本产品、品类、渠道、时间重叠的提报数据
                latestVersionReportMap = latestVersionReportList.stream()
                    .collect(Collectors.toMap(Function.identity(), ImportChannelDemandReportVo::getOrderNum, (key1, key2) -> key1));
            }
        }

        // 调用dataq服务批量导入渠道需求提报数据
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_IMPORT"));

        // 三层循环，将产品、渠道、品类、时间周期平铺成提报数据列表
        String enterStr = "\r\n";
        Set<ImportChannelDemandReportVo> dataSet = new HashSet<>(skuList.size() * channelList.size() * bizDateValueList.size());
        for (SkuDto skuDto : skuList)
        {
            for (ChannelDto channelDto : channelList)
            {
                for (String bizDateValue : bizDateValueList)
                {
                    ImportChannelDemandReportVo data = new ImportChannelDemandReportVo();
                    data.setRollingVersion(rollingVersion);
                    data.setSkuCode(skuDto.getSkuCode());
                    data.setSkuName(skuDto.getSkuName());
                    data.setLv1CategoryCode(StringUtils.replace(skuDto.getLv1CategoryCode(), enterStr, StringUtils.EMPTY));
                    data.setLv1CategoryName(StringUtils.replace(skuDto.getLv1CategoryName(), enterStr, StringUtils.EMPTY));
                    data.setLv2CategoryCode(StringUtils.replace(skuDto.getLv2CategoryCode(), enterStr, StringUtils.EMPTY));
                    data.setLv2CategoryName(StringUtils.replace(skuDto.getLv2CategoryName(), enterStr, StringUtils.EMPTY));
                    data.setLv3CategoryCode(StringUtils.replace(skuDto.getLv3CategoryCode(), enterStr, StringUtils.EMPTY));
                    data.setLv3CategoryName(StringUtils.replace(skuDto.getLv3CategoryName(), enterStr, StringUtils.EMPTY));
                    data.setLv1ChannelCode(StringUtils.replace(channelDto.getLv1ChannelCode(), enterStr, StringUtils.EMPTY));
                    data.setLv1ChannelName(StringUtils.replace(channelDto.getLv1ChannelName(), enterStr, StringUtils.EMPTY));
                    data.setLv2ChannelCode(StringUtils.replace(channelDto.getLv2ChannelCode(), enterStr, StringUtils.EMPTY));
                    data.setLv2ChannelName(StringUtils.replace(channelDto.getLv2ChannelName(), enterStr, StringUtils.EMPTY));
                    data.setLv3ChannelCode(StringUtils.replace(channelDto.getLv3ChannelCode(), enterStr, StringUtils.EMPTY));
                    data.setLv3ChannelName(StringUtils.replace(channelDto.getLv3ChannelName(), enterStr, StringUtils.EMPTY));
                    data.setUnit(skuDto.getUnit());
                    data.setYear(nextWeek.getFsclYear());
                    data.setBizDateValue(bizDateValue);
                    if (latestVersionReportMap.containsKey(data))
                    {

                        data.setOrderNum(latestVersionReportMap.get(data));
                    }
                    else
                    {
                        data.setOrderNum(0d);
                    }
                    data.setExtend(null);
                    dataSet.add(data);
                }
            }
        }
        skuList.clear();
        channelList.clear();

        // 如果数据量过大，需要分片
        int fragmentSize = 200;
        if (dataSet.size() < fragmentSize)
        {
            // 封装批量导入渠道需求提报数据请求参数
            Map<String, Set<ImportChannelDemandReportVo>> map = new HashMap<>();
            map.put("data", dataSet);
            // 发送创建需求计划配置参数（需求计划版本）请求给阿里dataq
            dataqService.invoke(HttpMethod.POST, path, null, null, map);
        }
        else
        {
            // 单例map获取线程池，不需要shutdown
            ThreadPoolExecutor pool = ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.fragmentThread);
            int dataSize = dataSet.size();
            int threadCount =
                Math.floorMod(dataSize, fragmentSize) == 0 ? Math.floorDiv(dataSize, fragmentSize) : Math.floorDiv(dataSize, fragmentSize) + 1;
            CountDownLatch countDownLatch = new CountDownLatch(threadCount);
            for (Integer i = 0; i < threadCount; i++)
            {
                final int start = i * fragmentSize;
                final int fragmentNum = i;
                final Set<ImportChannelDemandReportVo> subData = dataSet.stream().skip(start).limit(fragmentSize).collect(Collectors.toSet());
                pool.submit(() -> {
                    try
                    {
                        // 封装批量导入渠道需求提报数据请求参数
                        Map<String, Set<ImportChannelDemandReportVo>> map = new HashMap<>();
                        map.put("data", subData);
                        DataqResult temp = dataqService.invoke(HttpMethod.POST, path, null, null, map);
                        log.info("fragment {} addDemandReportData rsp:{}", fragmentNum, temp);
                    }
                    catch (Exception e)
                    {
                        log.error("fragment {} addDemandReportData error has error.", fragmentNum, e);
                    }
                    finally
                    {
                        countDownLatch.countDown();
                        subData.clear();
                    }
                });
            }
            countDownLatch.await();
        }
    }
}
