package cn.aliyun.ryytn.modules.demand.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.page.PageUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.SaleTargetService;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqSaleTargetDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.SaleTargetDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetRspVo;

/**
 * @Description 销售目标接口实现
 * <AUTHOR>
 * @date 2023/10/23 11:19
 */
@Service
public class SaleTargetServiceImpl implements SaleTargetService
{
    @Autowired
    private DataqSaleTargetDao dataqSaleTargetDao;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    private static final String[] MONTH_HEAD_ARRAY =
        new String[]{"1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"};
    private static final String[] QUARTER_HEAD_ARRAY = new String[]{"Q1", "Q2", "Q3", "Q4"};

    /**
     *
     * @Description 查询销售目标表格
     * 不依赖阿里行转列接口，业务侧自己实现平铺数据行转列
     * @param querySaleTargetReqVo
     * @return BaseTable<List < QuerySaleTargetRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月03日 16:38
     */
    @Override
    public BaseTable<List<QuerySaleTargetRspVo>> querySaleTargetList(QuerySaleTargetReqVo querySaleTargetReqVo) throws Exception
    {
        BaseTable<List<QuerySaleTargetRspVo>> baseTable = new BaseTable();

        JSONObject jsonObject = new JSONObject();
        // 阿里当前只有月数据
        jsonObject.put("bizDateType", BizDateTypeEnum.MONTH);
        jsonObject.put("fsclYear", querySaleTargetReqVo.getFsclYear());

        // 调用dataq查询销售目标表格数据接口，传入动态字段数据
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_SALE_TARGET"));
        DataqResult<?> saleTargetResult = dataqService.invoke(HttpMethod.POST, path, null, null, jsonObject);
        JSONArray jsonArray = (JSONArray) saleTargetResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return baseTable;
        }

        List<SaleTargetDto> saleTargetDtoList = jsonArray.toJavaList(SaleTargetDto.class);

        // 根据所有业务数据分组
        Map<SaleTargetDto, List<SaleTargetDto>> map = saleTargetDtoList.stream().collect(Collectors.groupingBy(Function.identity()));

        // 遍历所有业务数据
        List<String> headList = null;
        if (BizDateTypeEnum.MONTH.equals(querySaleTargetReqVo.getBizDateType()))
        {
            headList = new ArrayList<>(MONTH_HEAD_ARRAY.length + 1);
            headList.addAll(Arrays.asList(MONTH_HEAD_ARRAY));
        }
        else
        {
            headList = new ArrayList<>(QUARTER_HEAD_ARRAY.length + 1);
            headList.addAll(Arrays.asList(QUARTER_HEAD_ARRAY));
        }
        String totalKey = querySaleTargetReqVo.getFsclYear();
        headList.add(totalKey);
        baseTable.setHeadArray(headList);

        List<QuerySaleTargetRspVo> result = new ArrayList<>();
        for (SaleTargetDto saleTargetDto : map.keySet())
        {
            QuerySaleTargetRspVo querySaleTargetRspVo = new QuerySaleTargetRspVo();
            BeanUtils.copyProperties(saleTargetDto, querySaleTargetRspVo);
            List<SaleTargetDto> list = map.get(saleTargetDto);
            double total = 0d;
            for (SaleTargetDto item : list)
            {
                String key = null;
                int month = Integer.valueOf(StringUtils.substring(item.getBizDateValue(), 5, 7));
                if (BizDateTypeEnum.MONTH.equals(querySaleTargetReqVo.getBizDateType()))
                {
                    key = new StringBuilder(String.valueOf(month)).append(StringUtils.MONTH_UNIT).toString();
                }
                else
                {
                    key = new StringBuilder(StringUtils.QUARTER_PREFIX_UPPER).append(DateUtils.getQuarter(month)).toString();
                }
                Double orderNum = Objects.isNull(item.getOrderNum()) ? 0d : item.getOrderNum();
                querySaleTargetRspVo.getDataMap().put(key, orderNum);
                total += orderNum;
            }
            DecimalFormat df = new DecimalFormat("#.##");
            df.setRoundingMode(RoundingMode.HALF_UP);
            querySaleTargetRspVo.getDataMap().put(totalKey, Double.valueOf(df.format(total)));

            result.add(querySaleTargetRspVo);
        }
        baseTable.setList(result);

        return baseTable;
    }

    /**
     *
     * @Description 查询销售目标动态表头
     * @param querySaleTargetReqVo
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 17:21
     */
    @Override
    public List<String> querySaleTargetHeadList(QuerySaleTargetReqVo querySaleTargetReqVo) throws Exception
    {
        List<String> headList = Collections.EMPTY_LIST;
        if (BizDateTypeEnum.MONTH.equals(querySaleTargetReqVo.getBizDateType()))
        {
            headList = Arrays.asList(MONTH_HEAD_ARRAY);
        }
        else if (BizDateTypeEnum.QUARTER.equals(querySaleTargetReqVo.getBizDateType()))
        {
            headList = Arrays.asList(QUARTER_HEAD_ARRAY);
        }

        return headList;
    }

    /**
     *
     * @Description 查询销售目标表头下拉列表
     * @param querySaleTargetReqVo
     * @return List<QuerySaleTargetRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @Override
    public List<QuerySaleTargetRspVo> querySaleTargetHeadSelect(QuerySaleTargetReqVo querySaleTargetReqVo) throws Exception
    {
        querySaleTargetReqVo.setBizDateType(BizDateTypeEnum.MONTH);
        GroupColumnEnum groupColumnEnum = querySaleTargetReqVo.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        querySaleTargetReqVo.setGroupColumn(groupColumn);
        querySaleTargetReqVo.setSortColumn(sortColumn);

        List<QuerySaleTargetRspVo> result = dataqSaleTargetDao.querySaleTargetHeadSelect(querySaleTargetReqVo);
        return result;
    }

    /**
     *
     * @Description 查询销售目标分组聚合数据列表
     * @param querySaleTargetReqVo
     * @return List<QuerySaleTargetRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @Override
    public List<QuerySaleTargetRspVo> querySaleTargetListGroupBy(QuerySaleTargetReqVo querySaleTargetReqVo) throws Exception
    {
        BizDateTypeEnum bizDateType = querySaleTargetReqVo.getBizDateType();
        querySaleTargetReqVo.setBizDateType(BizDateTypeEnum.MONTH);
        // 分组查询
        List<GroupColumnEnum> groupColumnEnumList = querySaleTargetReqVo.getGroupColumnList();
        List<QuerySaleTargetRspVo> dataList = new ArrayList<>();
        StringBuilder groupColumnSB = new StringBuilder();
        StringBuilder sortColumnSB = new StringBuilder();
        for (GroupColumnEnum groupColumnEnum : groupColumnEnumList)
        {
            String groupColumn = groupColumnSB.append(groupColumnEnum.getColumnName()).toString();
            String sortColumn = sortColumnSB.append(groupColumnEnum.getSortColumn().getColumnName()).toString();
            querySaleTargetReqVo.setGroupColumn(groupColumn);
            querySaleTargetReqVo.setSortColumn(sortColumn);
            List<QuerySaleTargetRspVo> list = dataqSaleTargetDao.querySaleTargetGroupList(querySaleTargetReqVo);
            dataList.addAll(list);

            groupColumnSB.append(StringUtils.COMMA_SEPARATOR);
            sortColumnSB.append(StringUtils.COMMA_SEPARATOR);
        }

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (QuerySaleTargetRspVo saleTarget : dataList)
        {
            List<ChannelDemandReportDataVo> reportDataList = JSON.parseArray(saleTarget.getData(), ChannelDemandReportDataVo.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (ChannelDemandReportDataVo channelDemandReportDataVo : reportDataList)
            {
                if (Objects.isNull(channelDemandReportDataVo.getOrderNum()))
                {
                    channelDemandReportDataVo.setOrderNum(0d);
                }
                int month = Integer.valueOf(StringUtils.substringAfterLast(channelDemandReportDataVo.getBizDateValue(), StringUtils.DATE_SEPARATOR));
                // 月粒度直接加入dataMap
                if (BizDateTypeEnum.MONTH.equals(bizDateType))
                {
                    String head = month + StringUtils.MONTH_UNIT;
                    saleTarget.getDataMap().put(head, channelDemandReportDataVo.getOrderNum());
                }
                // 季度粒度按月求和
                else if (BizDateTypeEnum.QUARTER.equals(bizDateType))
                {
                    String head = StringUtils.QUARTER_PREFIX_UPPER + DateUtils.getQuarter(month);
                    if (saleTarget.getDataMap().containsKey(head))
                    {
                        BigDecimal total = new BigDecimal(saleTarget.getDataMap().get(head));
                        BigDecimal orderNum = new BigDecimal(channelDemandReportDataVo.getOrderNum());
                        Double value = total.add(orderNum).doubleValue();
                        saleTarget.getDataMap().put(head, value);
                    }
                    else
                    {
                        saleTarget.getDataMap().put(head, channelDemandReportDataVo.getOrderNum());
                    }
                }
            }
            saleTarget.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 分页查询销售目标明细数据列表
     * @param condition
     * @return PageInfo<QuerySaleTargetListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 14:28
     */
    @Override
    public PageInfo<QuerySaleTargetRspVo> querySaleTargetDataPage(PageCondition<QuerySaleTargetReqVo> condition) throws Exception
    {
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        QuerySaleTargetReqVo querySaleTargetReqVo = condition.getCondition();
        BizDateTypeEnum bizDateType = querySaleTargetReqVo.getBizDateType();
        querySaleTargetReqVo.setBizDateType(BizDateTypeEnum.MONTH);

        // dataq接口效率过低，修改为直接读数据库
        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        PageHelper.startPage(pageNum, pageSize);
        List<QuerySaleTargetRspVo> keyList = dataqSaleTargetDao.querySaleTargetDataKeyList(querySaleTargetReqVo);
        PageInfo pageInfo = new PageInfo(keyList);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(keyList))
        {
            return pageInfo;
        }

        int pageTotal = Integer.parseInt(String.valueOf(pageInfo.getTotal()));

        querySaleTargetReqVo.setKeyList(keyList);

        List<QuerySaleTargetRspVo> dataList = dataqSaleTargetDao.querySaleTargetDataJsonList(querySaleTargetReqVo);

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (QuerySaleTargetRspVo saleTarget : dataList)
        {
            List<ChannelDemandReportDataVo> planDataList = JSON.parseArray(saleTarget.getData(), ChannelDemandReportDataVo.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (ChannelDemandReportDataVo channelDemandReportDataVo : planDataList)
            {
                if (Objects.isNull(channelDemandReportDataVo.getOrderNum()))
                {
                    channelDemandReportDataVo.setOrderNum(0d);
                }
                int month = Integer.valueOf(StringUtils.substringAfterLast(channelDemandReportDataVo.getBizDateValue(), StringUtils.DATE_SEPARATOR));
                // 月粒度直接加入dataMap
                if (BizDateTypeEnum.MONTH.equals(bizDateType))
                {
                    String head = month + StringUtils.MONTH_UNIT;
                    saleTarget.getDataMap().put(head, channelDemandReportDataVo.getOrderNum());
                }
                // 季度粒度按月求和
                else if (BizDateTypeEnum.QUARTER.equals(bizDateType))
                {
                    String head = StringUtils.QUARTER_PREFIX_UPPER + DateUtils.getQuarter(month);
                    if (saleTarget.getDataMap().containsKey(head))
                    {
                        BigDecimal total = new BigDecimal(saleTarget.getDataMap().get(head));
                        BigDecimal orderNum = new BigDecimal(channelDemandReportDataVo.getOrderNum());
                        Double value = total.add(orderNum).doubleValue();
                        saleTarget.getDataMap().put(head, value);
                    }
                    else
                    {
                        saleTarget.getDataMap().put(head, channelDemandReportDataVo.getOrderNum());
                    }
                }
            }
            saleTarget.setData(null);
        }

        PageInfo<QuerySaleTargetRspVo> result = PageUtils.init(dataList, pageNum, pageSize, pageTotal);
        return result;
    }

    /**
     *
     * @Description 查询销售目标数据汇总
     * @param querySaleTargetReqVo
     * @return Map<String, Double>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月21日 14:50
     */
    @Override
    public Map<String, Double> querySaleTargetSummary(QuerySaleTargetReqVo querySaleTargetReqVo) throws Exception
    {
        BizDateTypeEnum bizDateType = querySaleTargetReqVo.getBizDateType();
        querySaleTargetReqVo.setBizDateType(BizDateTypeEnum.MONTH);
        List<ChannelDemandReportDataVo> dataList = dataqSaleTargetDao.querySaleTargetSummary(querySaleTargetReqVo);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(dataList))
        {
            return null;
        }
        List<String> bizDateValueList = dataList.stream().map(ChannelDemandReportDataVo::getBizDateValue).collect(Collectors.toList());
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        Map<String, Double> result = new HashMap<>();
        for (ChannelDemandReportDataVo data : dataList)
        {
            int month = Integer.valueOf(StringUtils.substringAfterLast(data.getBizDateValue(), StringUtils.DATE_SEPARATOR));
            // 月粒度直接加入dataMap
            if (BizDateTypeEnum.MONTH.equals(bizDateType))
            {
                String head = month + StringUtils.MONTH_UNIT;
                result.put(head, data.getOrderNum());
            }
            // 季度粒度按月求和
            else if (BizDateTypeEnum.QUARTER.equals(bizDateType))
            {
                String head = StringUtils.QUARTER_PREFIX_UPPER + DateUtils.getQuarter(month);
                if (result.containsKey(head))
                {
                    BigDecimal total = new BigDecimal(result.get(head));
                    BigDecimal orderNum = new BigDecimal(data.getOrderNum());
                    Double value = total.add(orderNum).doubleValue();
                    result.put(head, value);
                }
                else
                {
                    result.put(head, data.getOrderNum());
                }
            }
        }

        return result;
    }
}
