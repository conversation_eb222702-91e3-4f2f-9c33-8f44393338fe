package cn.aliyun.ryytn.modules.demand.utils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @title BeanUtils
 * @description 增强对象Copy工具
 * @date 2022/8/19 15:39
 **/
public class BoostBeanUtils {
    /**
     * 自定义函数去重
     *
     * @param keyExtractor
     * @param <T>
     * @return
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>(16);
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 自定义函数去重
     *
     * @param keyExtractor
     * @param <T>
     * @return
     */
    public static <T> Predicate<T> distinctByKeyRetain(Function<? super T, ?> keyExtractor, List<T> retain) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>(16);
        return t -> {
            boolean flg = seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
            if (!flg) {
                //存储被去重的对象
                retain.add(t);
            }
            return flg;
        };
    }

    /**
     * Stream 流中使用类转换功能
     *
     * @param supplier 转化目标对象的构造方法
     * @param <T>      被转换对象类型
     * @param <R>      转换目标类型
     * @return
     */
    public static <T, R> Function<T, R> copyBeanStream(Supplier<R> supplier) {
        return (t) -> {
            R r = supplier.get();
            BeanUtils.copyProperties(t, r);
            return r;
        };
    }

    /**
     * 快速拷贝增强方法
     *
     * @param t        拷贝对象
     * @param supplier 目标对象构造方法
     * @param <T>      拷贝对象类型
     * @param <R>      目标对象类型
     * @return 目标对象
     */
    public static <T, R> R copyProperties(T t, Supplier<R> supplier) {
        if (Objects.isNull(t)) {
            return null;
        }
        R r = supplier.get();
        BeanUtils.copyProperties(t, r);
        return r;
    }

    public static <T> List<T> copyProperties(List<?> sourceList, Class<T> targetClass) {
        return sourceList.stream()
                .map(source -> {
                    try {
                        T target = targetClass.newInstance();
                        BeanUtils.copyProperties(source, target);
                        return target;
                    } catch (InstantiationException | IllegalAccessException e) {
                        throw new RuntimeException("Failed to create instance of target class", e);
                    }
                })
                .collect(Collectors.toList());
    }


    /**
     * 获取非null 属性字段
     *
     * @param source
     * @return {@link String[]}
     * <AUTHOR>
     * @date 2022-10-27 15:46
     * @description 获取非null 属性字段
     */
    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * 增强copy 只拷贝非null属性
     *
     * @param src
     * @param target
     * <AUTHOR>
     * @date 2022-10-27 15:47
     * @description 增强copy 只拷贝非null属性
     */
    public static void copyPropertiesNonNull(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }
}
