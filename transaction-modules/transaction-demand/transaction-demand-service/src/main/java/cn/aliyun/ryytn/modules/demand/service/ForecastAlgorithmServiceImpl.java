package cn.aliyun.ryytn.modules.demand.service;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.modules.demand.api.ForecastAlgorithmService;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastAlgorithmListReqVo;

/**
 * @Description 预测算法接口实现
 * <AUTHOR>
 * @date 2023/10/23 17:38
 */
@Service
public class ForecastAlgorithmServiceImpl implements ForecastAlgorithmService
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 查询预测算法渠道_分仓预测算法列表
     * @param queryForecastAlgorithmListReqVo
     * @return
     * <AUTHOR>
     * @date 2023年10月23日 17:43     */
    @Override
    public DataqResult<?> queryForecastAlgorithmList(QueryForecastAlgorithmListReqVo queryForecastAlgorithmListReqVo) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_ALGORITHM_LIST"));
        Map map = new HashMap<String, Object>();
        map.put("order_by", "algo_name_and_version_name");
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, map, queryForecastAlgorithmListReqVo);

        return dataqResult;
    }
}
