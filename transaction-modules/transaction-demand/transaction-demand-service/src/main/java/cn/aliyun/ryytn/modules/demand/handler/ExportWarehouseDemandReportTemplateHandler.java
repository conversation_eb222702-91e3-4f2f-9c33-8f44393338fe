package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.excel.HideColSheetWriterHandler;
import cn.aliyun.ryytn.common.excel.HideProperty;
import cn.aliyun.ryytn.common.excel.HideRowSheetWriterHandler;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandReportService;
import cn.aliyun.ryytn.modules.demand.dao.WarehouseDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportTemplateDto;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 导出分仓需求提报模板
 * <AUTHOR>
 * @date 2023/12/6 14:33
 */
@Slf4j
@Component("exportWarehouseDemandReportTemplate")
public class ExportWarehouseDemandReportTemplateHandler extends AbstractExportExcelHandler
{
    @Autowired
    private WarehouseDemandReportDao warehouseDemandReportDao;

    @Autowired
    private WarehouseDemandReportService warehouseDemandReportService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 导出分仓需求提报模板
     * @param condition
     * @return byte[]
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 14:35
     */
    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        WarehouseDemandReportDto warehouseDemandReportDto = condition.getCondition().toJavaObject(WarehouseDemandReportDto.class);
        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);

        // 导出模板表格头二维数组
        List<List<String>> headList = new ArrayList<List<String>>();
        // 通过反射获取固定字段表头
        Class clazz = WarehouseDemandReportTemplateDto.class;
        // 遍历所有字段，获取所有ExcelProperty注解
        List<ExcelProperty> excelPropertyList = new ArrayList<>(clazz.getDeclaredFields().length);
        List<Integer> hideCols = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields())
        {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (Objects.isNull(excelProperty))
            {
                continue;
            }
            HideProperty hideProperty = field.getAnnotation(HideProperty.class);
            if (Objects.nonNull(hideProperty) && hideProperty.isHide())
            {
                hideCols.add(excelProperty.index());
            }
            excelPropertyList.add(excelProperty);
        }
        // ExcelProperty注解集合根据index正序排序，取value值转换为List
        List<List<String>> fieldHeadList =
            excelPropertyList.stream().sorted(Comparator.comparing(ExcelProperty::index)).map(item -> {
                return Arrays.asList(item.value());
            }).collect(Collectors.toList());
        headList.addAll(fieldHeadList);

        // 查询缓存周对象
        // 版本首月根据版本号计算，防止导入时间不在当月导致模板数据错误
//        String versionMonthFirstDay = StringUtils.substringBefore(StringUtils.replace(warehouseDemandReportDto.getRollingVersion(), "DP", StringUtils.EMPTY),
//            StringUtils.WEEK_PREFIX_UPPER) + "01";
//
//        // 获取所有周第一天字符串
//        Set<String> itemSet = redisUtils.hKeys(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY);
//        List<String> itemList =
//            itemSet.stream().filter(item -> {
//                return item.compareTo(versionMonthFirstDay) >= 0;
//            }).sorted((o1, o2) -> {
//                return o1.compareTo(o2);
//            }).collect(Collectors.toList());

        // 查询动态表头
        List<String> bizDateValueSet = warehouseDemandReportDao.queryWarehouseDemandReportHeadList(warehouseDemandReportDto);

        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueSet);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(),
            (key1, key2) -> key1));

        List<List<String>> trendsHeadList = new ArrayList<>();
        for (String bizDateValue : bizDateValueSet)
        {
            DataqWeek dataqWeek = dataqWeekMap.get(bizDateValue);
            List<String> list = new ArrayList<>(3);
            list.add(dataqWeek.getMonthLabel());
            list.add(dataqWeek.getWeekLabel());
            list.add(dataqWeek.getFsclWeekRange());
            trendsHeadList.add(list);
        }

        headList.addAll(trendsHeadList);

        // 数据权限
        warehouseDemandReportService.generateDataScope(warehouseDemandReportDto);

        // 查询数据库中的分仓需求提报数据
        List<WarehouseDemandReportTemplateDto> warehouseDemandReportList =
            warehouseDemandReportDao.queryWarehouseDemandReportTemplateList(warehouseDemandReportDto);

        // 由于存在动态字段，无法通过Java类定义电子表格数据，通过Object二维数组实现，字段顺序写死
        List<List<Object>> dataList = new ArrayList<>();
        for (WarehouseDemandReportTemplateDto reportData : warehouseDemandReportList)
        {
            List<Object> rowDataList = new ArrayList<>();
            rowDataList.add(reportData.getLv1CategoryName());
            rowDataList.add(reportData.getLv2CategoryName());
            rowDataList.add(reportData.getLv3CategoryName());
            rowDataList.add(reportData.getSkuCode());
            rowDataList.add(reportData.getSkuName());
            rowDataList.add(reportData.getLv1ChannelName());
            rowDataList.add(reportData.getLv2ChannelName());
            rowDataList.add(reportData.getLv3ChannelName());
            rowDataList.add(reportData.getWarehouseName());
            String year = StringUtils.substring(StringUtils.replace(reportData.getRollingVersion(), "DP", StringUtils.EMPTY), 0, 4);
            rowDataList.add(year);
            rowDataList.add(reportData.getLv1CategoryCode());
            rowDataList.add(reportData.getLv2CategoryCode());
            rowDataList.add(reportData.getLv3CategoryCode());
            rowDataList.add(reportData.getLv1ChannelCode());
            rowDataList.add(reportData.getLv2ChannelCode());
            rowDataList.add(reportData.getLv3ChannelCode());
            rowDataList.add(reportData.getWarehouseCode());
            Map<String, String> dateValueMap = (Map<String, String>) JSON.parseObject(reportData.getData(), Map.class);
            for (List<String> trendsKeys : trendsHeadList)
            {
                // 第三行隐藏表头内容为dataqWeek.getFsclWeekRange()，周第一天到周最后一天，yyyyMMdd-yyyyMMdd
                String weekRange = trendsKeys.get(2);
                String bizTypeValue = StringUtils.substringBefore(weekRange, StringUtils.DATE_SEPARATOR);
                String num = Objects.isNull(dateValueMap.get(bizTypeValue)) ? StringUtils.ZERO : StringUtils.substringBefore(String.valueOf(dateValueMap.get(bizTypeValue)),StringUtils.POINT_SEPARATOR);
                rowDataList.add(StringUtils.isEmpty(num) ? 0d : num);
            }
            dataList.add(rowDataList);
        }
        warehouseDemandReportList.clear();

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream)
                .registerWriteHandler(new HideColSheetWriterHandler(hideCols))
                .registerWriteHandler(new HideRowSheetWriterHandler(2))
                .head(headList)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("分仓需求提报导入模板").doWrite(dataList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }
}
