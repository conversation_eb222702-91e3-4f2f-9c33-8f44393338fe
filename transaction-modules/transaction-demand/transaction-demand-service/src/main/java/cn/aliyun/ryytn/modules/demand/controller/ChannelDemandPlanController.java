package cn.aliyun.ryytn.modules.demand.controller;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.spring.SpringUtil;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.AsyncChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.api.ChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.constant.PeriodTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanPeriodEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanScopeTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectScopeTypeEnum;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemanPlanHistoryDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanConfigReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanDataParamVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanVersionLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanVersionVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandSubPlanGroupVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ConfirmChannelDemandPlanSubPlanGroupVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DeleteDemandPlanReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DemandPlanConfigSkuVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.OfflineDemandPlanReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandForecastResultListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanDataReportListVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanDataSaleTargetVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionGroupListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySkuDeliveryListRspVo;
import cn.aliyun.ryytn.modules.demand.task.SyncDemandPlanVersionDataTaskServiceImpl;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;
import cn.aliyun.ryytn.modules.system.entity.dto.ProductCategoryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 需求计划接口
 * <AUTHOR>
 * @date 2023/10/24 10:04
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/channelDemandPlan")
@Api(tags = "渠道需求计划")
public class ChannelDemandPlanController
{
    @Autowired
    private ChannelDemandPlanService channelDemandPlanService;

    /**
     *
     * @Description 查询渠道列表
     * @param subjectDimensionType
     * @return ResultInfo<Set < ChannelDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月06日 17:25
     */
    @PostMapping("queryChannelList")
    @ResponseBody
    @ApiOperation("查询渠道列表")
    @RequiresPermissions(value = {})
    public ResultInfo<Set<ChannelDto>> queryChannelList(@RequestParam SubjectDimensionTypeEnum subjectDimensionType) throws Exception
    {
        Set<ChannelDto> result = channelDemandPlanService.queryChannelList(subjectDimensionType);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询产品品类列表
     * @param planDimensionType
     * @return ResultInfo<Set < ProductCategoryDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月06日 17:25
     */
    @PostMapping("queryProductCategoryList")
    @ResponseBody
    @ApiOperation("查询产品品类列表")
    @RequiresPermissions(value = {})
    public ResultInfo<Set<ProductCategoryDto>> queryProductCategoryList(@RequestParam PlanDimensionTypeEnum planDimensionType) throws Exception
    {
        Set<ProductCategoryDto> result = channelDemandPlanService.queryProductCategoryList(planDimensionType);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划品类树
     * @param channelDemandPlanDataParamVo
     * @return ResultInfo<Set < ProductCategoryDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月02日 15:34
     */
    @PostMapping("queryChannelDemandPlanCategoryTree")
    @ResponseBody
    @ApiOperation("查询渠道需求计划品类树")
    @RequiresPermissions(value = {})
    public ResultInfo<Set<ProductCategoryDto>> queryChannelDemandPlanCategoryTree(@RequestBody ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getVersionId());

        Set<ProductCategoryDto> result = channelDemandPlanService.queryChannelDemandPlanCategoryTree(channelDemandPlanDataParamVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划列表
     * @param queryChannelDemandPlanListReqVo
     * @return ResultInfo<List < QueryChannelDemandPlanListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月15日 16:06
     */
    @PostMapping("queryChannelDemandPlanList")
    @ResponseBody
    @ApiOperation("查询渠道需求计划列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryChannelDemandPlanListRspVo>> queryChannelDemandPlanList(
        @RequestBody QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanListReqVo);

        List<QueryChannelDemandPlanListRspVo> result = channelDemandPlanService.queryChannelDemandPlanList(queryChannelDemandPlanListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划版本列表
     * @param channelDemandPlanVersionVo
     * @return ResultInfo<List < ChannelDemandPlanVersionVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月15日 16:06
     */
    @PostMapping("queryChannelDemandPlanVersionList")
    @ResponseBody
    @ApiOperation("查询渠道需求计划版本列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<ChannelDemandPlanVersionVo>> queryChannelDemandPlanVersionList(@RequestBody PageCondition<String> condition)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        PageInfo<ChannelDemandPlanVersionVo> result = channelDemandPlanService.queryChannelDemandPlanVersionList(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求子计划清单组列表
     * @param channelDemandSubPlanGroupVo
     * @return ResultInfo<List < ChannelDemandSubPlanGroupVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 15:27
     */
    @PostMapping("queryChannelDemandSubPlanGroupList")
    @ResponseBody
    @ApiOperation("查询渠道需求子计划清单组列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<ChannelDemandSubPlanGroupVo>> queryChannelDemandSubPlanGroupList(
        @RequestBody ChannelDemandSubPlanGroupVo channelDemandSubPlanGroupVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandSubPlanGroupVo);
        ValidateUtil.checkIsNotEmpty(channelDemandSubPlanGroupVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandSubPlanGroupVo.getVersionId());

        List<ChannelDemandSubPlanGroupVo> result = channelDemandPlanService.queryChannelDemandSubPlanGroupList(channelDemandSubPlanGroupVo);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划数据列表
     * @param channelDemandPlanDataParamVo
     * @return ResultInfo<BaseTable < List < QueryChannelDemandPlanVersionListRspVo>>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月16日 18:48
     */
    @PostMapping("queryChannelDemandPlanDataList")
    @ResponseBody
    @ApiOperation("查询渠道需求计划数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<BaseTable<List<QueryChannelDemandPlanVersionListRspVo>>> queryChannelDemandPlanDataList(
        @RequestBody ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getVersionId());

        BaseTable<List<QueryChannelDemandPlanVersionListRspVo>> result = channelDemandPlanService.queryChannelDemandPlanDataList(channelDemandPlanDataParamVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划数据动态表头列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return ResultInfo<List < String>>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    @PostMapping("queryChannelDemandPlanHeadList")
    @ResponseBody
    @ApiOperation("查询渠道需求计划数据动态表头列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryChannelDemandPlanHeadList(@RequestBody QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo.getVersionId());

        List<String> result = channelDemandPlanService.queryChannelDemandPlanHeadList(queryChannelDemandPlanVersionListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划表头下拉列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return ResultInfo<List < QueryChannelDemandPlanVersionGroupListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @PostMapping("queryChannelDemandPlanHeadSelect")
    @ResponseBody
    @ApiOperation("查询渠道需求计划表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryChannelDemandPlanVersionGroupListRspVo>> queryChannelDemandPlanHeadSelect(
        @RequestBody QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo.getGroupColumnList());

        List<QueryChannelDemandPlanVersionGroupListRspVo> result =
            channelDemandPlanService.queryChannelDemandPlanHeadSelect(queryChannelDemandPlanVersionListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划数据分组聚合列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return ResultInfo<List < QueryChannelDemandPlanVersionGroupListRspVo>>
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    @PostMapping("queryChannelDemandPlanGroupList")
    @ResponseBody
    @ApiOperation("查询渠道需求计划数据分组聚合列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryChannelDemandPlanVersionGroupListRspVo>> queryChannelDemandPlanGroupList(
        @RequestBody QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo.getGroupColumnList());

        List<QueryChannelDemandPlanVersionGroupListRspVo> result =
            channelDemandPlanService.queryChannelDemandPlanGroupList(queryChannelDemandPlanVersionListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询渠道需求计划数据列表
     * @param condition
     * @return ResultInfo<PageInfo < QueryChannelDemandPlanVersionGroupListRspVo>>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    @PostMapping("queryChannelDemandPlanDataPage")
    @ResponseBody
    @ApiOperation("分页查询渠道需求计划数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<QueryChannelDemandPlanVersionGroupListRspVo>> queryChannelDemandPlanDataPage(
        @RequestBody PageCondition<QueryChannelDemandPlanVersionListReqVo> condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getVersionId());

        PageInfo<QueryChannelDemandPlanVersionGroupListRspVo> result = channelDemandPlanService.queryChannelDemandPlanDataPage(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划数据汇总
     * @param condition
     * @return ResultInfo<Map < String, Double>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("queryChannelDemandPlanSummary")
    @ResponseBody
    @ApiOperation("查询渠道需求计划数据汇总")
    @RequiresPermissions(value = {})
    public ResultInfo<Map<String, Double>> queryChannelDemandPlanSummary(
        @RequestBody QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandPlanVersionListReqVo.getVersionId());

        Map<String, Double> result = channelDemandPlanService.queryChannelDemandPlanSummary(queryChannelDemandPlanVersionListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划销售目标列表
     * @param channelDemandPlanDataParamVo
     * @return ResultInfo<List < QueryChannelDemandPlanDataSaleTargetVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 10:19
     */
    @PostMapping("queryChannelDemandPlanDataSaleTargetList")
    @ResponseBody
    @ApiOperation("查询渠道需求数据计划销售目标列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryChannelDemandPlanDataSaleTargetVo>> queryChannelDemandPlanDataSaleTargetList(
        @RequestBody ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getGroupId());

        List<QueryChannelDemandPlanDataSaleTargetVo> result = channelDemandPlanService.queryChannelDemandPlanDataSaleTargetList(channelDemandPlanDataParamVo);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划数据需求提报列表
     * @param channelDemandPlanDataParamVo
     * @return ResultInfo<List < QueryChannelDemandPlanDataReportListVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 15:03
     */
    @PostMapping("queryChannelDemandPlanDataReportList")
    @ResponseBody
    @ApiOperation("查询渠道需求计划数据需求提报列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryChannelDemandPlanDataReportListVo>> queryChannelDemandPlanDataReportList(
        @RequestBody ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getGroupId());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getRollingVersion());

        List<QueryChannelDemandPlanDataReportListVo> result = channelDemandPlanService.queryChannelDemandPlanDataReportList(channelDemandPlanDataParamVo);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划数据渠道预测结果列表
     * @param channelDemandPlanDataParamVo
     * @return ResultInfo<List < QueryChannelDemandForecastResultListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 18:44
     */
    @PostMapping("queryChannelDemandPlanDataForecastResultList")
    @ResponseBody
    @ApiOperation("查询渠道需求计划数据渠道预测结果列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryChannelDemandForecastResultListRspVo>> queryChannelDemandPlanDataForecastResultList(
        @RequestBody ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getGroupId());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getPredictionVersion());

        List<QueryChannelDemandForecastResultListRspVo> result =
            channelDemandPlanService.queryChannelDemandPlanDataForecastResultList(channelDemandPlanDataParamVo);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求计划数据渠道出库数量列表
     * @param channelDemandPlanDataParamVo
     * @return ResultInfo<List < QuerySkuDeliveryListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 18:44
     */
    @PostMapping("queryChannelDemandPlanDataObserList")
    @ResponseBody
    @ApiOperation("查询渠道需求计划数据渠道出库数量列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QuerySkuDeliveryListRspVo>> queryChannelDemandPlanDataObserList(
        @RequestBody ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getGroupId());

        List<QuerySkuDeliveryListRspVo> result = channelDemandPlanService.queryChannelDemandPlanDataObserList(channelDemandPlanDataParamVo);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 新增渠道需求计划
     * @param addDemandPlanListReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:01
     */
    @PostMapping("addChannelDemandPlan")
    @ResponseBody
    @ApiOperation("新增渠道需求计划")
    @RequiresPermissions(value = {})
    public ResultInfo<?> addChannelDemandPlan(@RequestBody AddDemandPlanListReqVo addDemandPlanListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(addDemandPlanListReqVo);
        // 计划名称长度小于200
        ValidateUtil.checkStrLength(addDemandPlanListReqVo.getDemandPlanName(), 1, 200, false);
        // 周期粒度
        ValidateUtil.checkIsNotEmpty(addDemandPlanListReqVo.getPeriodType());
        // 计划开始时间
        ValidateUtil.checkTimeFormat(addDemandPlanListReqVo.getStartDate(), DateUtils.YMD_DASH, false, false);
        // 期数必须是数字，必须大于等于1
        ValidateUtil.checkNum(addDemandPlanListReqVo.getPeriods());
        // 计划频率粒度不能为空
        ValidateUtil.checkIsNotEmpty(addDemandPlanListReqVo.getPlanPeriod());
        // 如果计划频率粒度是月，周期粒度不能拆到周
        if (PlanPeriodEnum.PER_MONTH.equals(addDemandPlanListReqVo.getPlanPeriod()) && PeriodTypeEnum.WEEK.equals(addDemandPlanListReqVo.getPeriodType()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
        // 执行日期数必须是数字，必须大于等于1
        ValidateUtil.checkNum(addDemandPlanListReqVo.getExecutionDateNum());
        // 计划主体类型不能为空，当前只支持渠道
        ValidateUtil.checkIsNotEmpty(addDemandPlanListReqVo.getSubjectType());
        // 计划主体纬度不能为空
        ValidateUtil.checkIsNotEmpty(addDemandPlanListReqVo.getSubjectDimensionType());
        // 计划主体范围不能为空
        ValidateUtil.checkIsNotEmpty(addDemandPlanListReqVo.getSubjectScopeType());
        // 计划对象类型不能为空，当前只支持品类
        ValidateUtil.checkIsNotEmpty(addDemandPlanListReqVo.getPlanType());
        // 计划对象纬度不能为空
        ValidateUtil.checkIsNotEmpty(addDemandPlanListReqVo.getPlanDimensionType());
        // 计划对象范围不能为空
        ValidateUtil.checkIsNotEmpty(addDemandPlanListReqVo.getPlanScopeType());
        // 如果主体范围和对象范围存在自定义，子计划组清单不能为空
        if (SubjectScopeTypeEnum.custom.equals(addDemandPlanListReqVo.getSubjectScopeType()) ||
            PlanScopeTypeEnum.custom.equals(addDemandPlanListReqVo.getPlanScopeType()))
        {
            ValidateUtil.checkIsNotEmpty(addDemandPlanListReqVo.getSubPlanGroupList());
        }

        // 新增需求计划
        channelDemandPlanService.addChannelDemandPlan(addDemandPlanListReqVo);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 设置渠道需求计划版本标签
     * @param channelDemandPlanVersionLabelVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:51
     */
    @PostMapping("setChannelDemandPlanVersionLabel")
    @ResponseBody
    @ApiOperation("设置渠道需求计划版本标签")
    @RequiresPermissions(value = {})
    public ResultInfo<?> setChannelDemandPlanVersionLabel(@RequestBody ChannelDemandPlanVersionLabelVo channelDemandPlanVersionLabelVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandPlanVersionLabelVo);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanVersionLabelVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanVersionLabelVo.getVersionId());

        channelDemandPlanService.setChannelDemandPlanVersionLabel(channelDemandPlanVersionLabelVo);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 复制渠道需求计划版本
     * @param channelDemandPlanDataParamVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 10:49
     */
    @PostMapping("duplicateChannelDemandPlanVersion")
    @ResponseBody
    @ApiOperation("复制渠道需求计划版本")
    @RequiresPermissions(value = {})
    public ResultInfo<?> duplicateChannelDemandPlanVersion(@RequestBody ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getVersionId());

        channelDemandPlanService.duplicateChannelDemandPlanVersion(channelDemandPlanDataParamVo);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 修改渠道需求计划数据
     * @param AddDemandPlanConfigReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 17:17
     */
    @PostMapping("updateChannelDemandPlanData")
    @ResponseBody
    @ApiOperation("修改渠道需求计划数据")
    @RequiresPermissions(value = {})
    public ResultInfo<?> updateChannelDemandPlanData(@RequestBody AddDemandPlanConfigReqVo addDemandPlanConfigReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(addDemandPlanConfigReqVo);
        ValidateUtil.checkIsNotEmpty(addDemandPlanConfigReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(addDemandPlanConfigReqVo.getVersionId());
        if (CollectionUtils.isNotEmpty(addDemandPlanConfigReqVo.getPlanList()))
        {
            for (DemandPlanConfigSkuVo demandPlanConfig : addDemandPlanConfigReqVo.getPlanList())
            {
                ValidateUtil.checkIsNotEmpty(demandPlanConfig.getPlanValues());
            }

            channelDemandPlanService.updateChannelDemandPlanData(addDemandPlanConfigReqVo);
        }

        return ResultInfo.success();
    }

    /**
     *
     * @Description 确认渠道需求计划子计划清单组
     * @param confirmChannelDemandPlanSubPlanGroupVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 15:54
     */
    @PostMapping("confirmChannelDemandPlanSubPlanGroup")
    @ResponseBody
    @ApiOperation("确认渠道需求计划子计划清单组")
    @RequiresPermissions(value = {})
    public ResultInfo<?> confirmChannelDemandPlanSubPlanGroup(@RequestBody ConfirmChannelDemandPlanSubPlanGroupVo confirmChannelDemandPlanSubPlanGroupVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(confirmChannelDemandPlanSubPlanGroupVo);
        ValidateUtil.checkIsNotEmpty(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(confirmChannelDemandPlanSubPlanGroupVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(confirmChannelDemandPlanSubPlanGroupVo.getGroupId());

        channelDemandPlanService.confirmChannelDemandPlanSubPlanGroup(confirmChannelDemandPlanSubPlanGroupVo);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 下线渠道需求计划
     * @param offlineDemandPlanReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:11
     */
    @PostMapping("offlineChannelDemandPlan")
    @ResponseBody
    @ApiOperation("下线渠道需求计划")
    @RequiresPermissions(value = {})
    public ResultInfo<?> offlineChannelDemandPlan(@RequestBody OfflineDemandPlanReqVo offlineDemandPlanReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(offlineDemandPlanReqVo);
        ValidateUtil.checkIsNotEmpty(offlineDemandPlanReqVo.getDemandPlanCode());

        channelDemandPlanService.offlineChannelDemandPlan(offlineDemandPlanReqVo);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 删除渠道需求计划
     * @param deleteDemandPlanReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:11
     */
    @PostMapping("deleteChannelDemandPlan")
    @ResponseBody
    @ApiOperation("删除需求计划")
    @RequiresPermissions(value = {})
    public ResultInfo<?> deleteChannelDemandPlan(@RequestBody DeleteDemandPlanReqVo deleteDemandPlanReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(deleteDemandPlanReqVo);
        ValidateUtil.checkIsNotEmpty(deleteDemandPlanReqVo.getDemandPlanCode());

        channelDemandPlanService.deleteChannelDemandPlan(deleteDemandPlanReqVo);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询渠道需求计划数据修改历史
     * @param channelDemandPlanDataParamVo
     * @return ResultInfo<List < ChannelDemanPlanHistoryDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月31日 17:49
     */
    @PostMapping("queryChannelDemandPlanHistoryList")
    @ResponseBody
    @ApiOperation("查询渠道需求计划数据修改历史")
    @RequiresPermissions(value = {})
    public ResultInfo<List<ChannelDemanPlanHistoryDto>> queryChannelDemandPlanHistoryList(
        @RequestBody ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getSkuCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getLv3CategoryCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanDataParamVo.getLv2ChannelCode());

        // 查询渠道需求计划修改历史记录列表
        List<ChannelDemanPlanHistoryDto> result = channelDemandPlanService.queryChannelDemandPlanHistoryList(channelDemandPlanDataParamVo);

        return ResultInfo.success(result);
    }

    @Autowired
    private AsyncChannelDemandPlanService asyncChannelDemandPlanService;

    @PostMapping("syncChannelDemandPlanData")
    @ResponseBody
    @ApiOperation("手动触发同步渠道需求计划共识数据给OMS")
    @RequiresPermissions(value = {})
    public ResultInfo<?> syncChannelDemandPlanData() throws Exception
    {
        TaskService taskService = SpringUtil.getBean(SyncDemandPlanVersionDataTaskServiceImpl.class);
        taskService.process(null);

        return ResultInfo.success();
    }
}
