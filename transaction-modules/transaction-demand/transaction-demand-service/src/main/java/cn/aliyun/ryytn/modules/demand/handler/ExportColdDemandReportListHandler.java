package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.ColdDemandReportService;
import cn.aliyun.ryytn.modules.demand.dao.ColdDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto;

/**
 * @Description 导出低温需求提报数据列表
 * <AUTHOR>
 * @date 2023/11/27 10:28
 */
@Component("exportColdDemandReportList")
public class ExportColdDemandReportListHandler extends AbstractExportExcelHandler
{
    @Autowired
    private ColdDemandReportDao coldDemandReportDao;

    @Autowired
    private ColdDemandReportService coldDemandReportService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 导出低温需求提报数据列表
     * @param condition
     * @return byte[]
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 10:29
     */
    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());
        // 解析参数
        ColdDemandReportDto coldDemandReportDto =
            condition.getCondition().toJavaObject(ColdDemandReportDto.class);
        ValidateUtil.checkIsNotEmpty(coldDemandReportDto.getRollingVersion());
        String tableSuffix = StringUtils.substringBefore(StringUtils.substringAfter(coldDemandReportDto.getRollingVersion(), "DDP"),
            StringUtils.WEEK_PREFIX_UPPER);
        coldDemandReportDto.setTableSuffix(tableSuffix);

        // 数据权限
        coldDemandReportService.generateDataScope(coldDemandReportDto);

        // 查询数据库中的低温需求提报数据
        List<ColdDemandReportDto> ColdDemandReportList = coldDemandReportDao.queryColdDemandReportList(coldDemandReportDto);
        if (CollectionUtils.isNotEmpty(ColdDemandReportList))
        {
            int rowId = 1;
            for (ColdDemandReportDto data : ColdDemandReportList)
            {
                data.setRowId(rowId++);
                data.setWaresName(data.getSkuName());
                data.setDay(String.valueOf(Integer.valueOf(StringUtils.substring(data.getBizDateValue(), 6))));
                data.setMonth(
                    new StringBuilder().append(Integer.valueOf(StringUtils.substring(data.getBizDateValue(), 4, 6))).append(StringUtils.MONTH_UNIT).toString());
            }
        }
        else
        {
            ColdDemandReportList = Collections.emptyList();
        }

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream, ColdDemandReportDto.class).excelType(ExcelTypeEnum.XLSX).sheet("低温需求提报数据")
                .doWrite(ColdDemandReportList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }
}
