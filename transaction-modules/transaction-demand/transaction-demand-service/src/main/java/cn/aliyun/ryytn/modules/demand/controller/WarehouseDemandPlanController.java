package cn.aliyun.ryytn.modules.demand.controller;

import java.util.List;
import java.util.Map;
import java.util.Set;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.mq.MqFactory;
import cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto;
import cn.aliyun.ryytn.modules.distribution.api.DailyWarehouseDemandService;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandPlanService;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandPlanReceiverDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandPlanMarkDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddDemandPlanConfigReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanVersionLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ConfirmChannelDemandPlanSubPlanGroupVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DateLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DemandPlanConfigSkuVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.WarehouseDemandPlanDataParamVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.WarehouseDemandPlanVersionVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.WarehouseDemandSubPlanGroupVo;
import cn.aliyun.ryytn.modules.system.entity.dto.ProductCategoryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 分仓需求计划
 * <AUTHOR>
 * @date 2023/11/27 15:46
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/warehouseDemandPlan")
@Api(tags = "分仓需求计划")
public class WarehouseDemandPlanController
{
    @Autowired
    private WarehouseDemandPlanService warehouseDemandPlanService;
    @Autowired
    private DailyWarehouseDemandService dailyWarehouseDemandService;
    /**
     *
     * @Description 查询分仓需求计划列表
     * @param queryWarehouseDemandPlanListReqVo
     * @return ResultInfo<List < QueryWarehouseDemandPlanListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 16:33
     */
    @PostMapping("queryWarehouseDemandPlanList")
    @ResponseBody
    @ApiOperation("查询分仓需求计划列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryWarehouseDemandPlanListRspVo>> queryWarehouseDemandPlanList(
        @RequestBody QueryWarehouseDemandPlanListReqVo queryWarehouseDemandPlanListReqVo)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanListReqVo);
        List<QueryWarehouseDemandPlanListRspVo> result = warehouseDemandPlanService.queryWarehouseDemandPlanList(queryWarehouseDemandPlanListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询分仓需求计划版本列表
     * @param condition
     * @return ResultInfo<PageInfo < WarehouseDemandPlanVersionVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 16:33
     */
    @PostMapping("queryWarehouseDemandPlanVersionPage")
    @ResponseBody
    @ApiOperation("分页查询分仓需求计划版本列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<WarehouseDemandPlanVersionVo>> queryWarehouseDemandPlanVersionPage(@RequestBody PageCondition<String> condition) throws Exception
    {
        PageInfo<WarehouseDemandPlanVersionVo> result = warehouseDemandPlanService.queryWarehouseDemandPlanVersionPage(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求计划版本列表
     * @param warehouseDemandPlanVersionVo
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 16:33
     */
    @PostMapping("queryWarehouseDemandPlanVersionList")
    @ResponseBody
    @ApiOperation("查询分仓需求计划版本列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryWarehouseDemandPlanVersionList(@RequestBody WarehouseDemandPlanVersionVo warehouseDemandPlanVersionVo) throws Exception
    {
        List<String> result = warehouseDemandPlanService.queryWarehouseDemandPlanVersionList(warehouseDemandPlanVersionVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 设置分仓需求计划版本标签
     * @param channelDemandPlanVersionLabelVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:51
     */
    @PostMapping("setWarehouseDemandPlanVersionLabel")
    @ResponseBody
    @ApiOperation("设置分仓需求计划版本标签")
    @RequiresPermissions(value = {})
    public ResultInfo<?> setWarehouseDemandPlanVersionLabel(@RequestBody ChannelDemandPlanVersionLabelVo channelDemandPlanVersionLabelVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandPlanVersionLabelVo);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanVersionLabelVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanVersionLabelVo.getVersionId());

        warehouseDemandPlanService.setWarehouseDemandPlanVersionLabel(channelDemandPlanVersionLabelVo);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询分仓需求子计划清单组列表
     * @param warehouseDemandSubPlanGroupVo
     * @return ResultInfo<List < WarehouseDemandSubPlanGroupVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 15:27
     */
    @PostMapping("queryWarehouseDemandSubPlanGroupList")
    @ResponseBody
    @ApiOperation("查询分仓需求子计划清单组列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<WarehouseDemandSubPlanGroupVo>> queryWarehouseDemandSubPlanGroupList(
        @RequestBody WarehouseDemandSubPlanGroupVo warehouseDemandSubPlanGroupVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(warehouseDemandSubPlanGroupVo);
        ValidateUtil.checkIsNotEmpty(warehouseDemandSubPlanGroupVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandSubPlanGroupVo.getVersionId());

        List<WarehouseDemandSubPlanGroupVo> result = warehouseDemandPlanService.queryWarehouseDemandSubPlanGroupList(warehouseDemandSubPlanGroupVo);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 确认分仓需求计划子计划清单组
     * @param confirmChannelDemandPlanSubPlanGroupVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 15:54
     */
    @PostMapping("confirmWarehouseDemandPlanSubPlanGroup")
    @ResponseBody
    @ApiOperation("确认分仓需求计划子计划清单组")
    @RequiresPermissions(value = {})
    public ResultInfo<?> confirmWarehouseDemandPlanSubPlanGroup(@RequestBody ConfirmChannelDemandPlanSubPlanGroupVo confirmChannelDemandPlanSubPlanGroupVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(confirmChannelDemandPlanSubPlanGroupVo);
        ValidateUtil.checkIsNotEmpty(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(confirmChannelDemandPlanSubPlanGroupVo.getVersionId());
//        ValidateUtil.checkIsNotEmpty(confirmChannelDemandPlanSubPlanGroupVo.getGroupId());

        warehouseDemandPlanService.confirmWarehouseDemandPlanSubPlanGroup(confirmChannelDemandPlanSubPlanGroupVo);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询分仓需求计划品类树
     * @param warehouseDemandSubPlanGroupVo
     * @return ResultInfo<Set < ProductCategoryDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月02日 15:34
     */
    @PostMapping("queryWarehouseDemandPlanCategoryTree")
    @ResponseBody
    @ApiOperation("查询分仓需求计划品类树")
    @RequiresPermissions(value = {})
    public ResultInfo<Set<ProductCategoryDto>> queryWarehouseDemandPlanCategoryTree(@RequestBody WarehouseDemandSubPlanGroupVo warehouseDemandSubPlanGroupVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(warehouseDemandSubPlanGroupVo);
        ValidateUtil.checkIsNotEmpty(warehouseDemandSubPlanGroupVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandSubPlanGroupVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(warehouseDemandSubPlanGroupVo.getReceiverType());

        Set<ProductCategoryDto> result = warehouseDemandPlanService.queryWarehouseDemandPlanCategoryTree(warehouseDemandSubPlanGroupVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求计划编辑页面时间页签
     * @param warehouseDemandSubPlanGroupVo
     * @return ResultInfo<List < WarehouseDemandReportDateLabelVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 16:01
     */
    @PostMapping("queryWarehouseDemandPlanDateLabelList")
    @ResponseBody
    @ApiOperation("查询分仓需求计划编辑页面时间页签")
    @RequiresPermissions(value = {})
    public ResultInfo<List<DateLabelVo>> queryWarehouseDemandPlanDateLabelList(
        @RequestBody WarehouseDemandSubPlanGroupVo warehouseDemandSubPlanGroupVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(warehouseDemandSubPlanGroupVo);

        List<DateLabelVo> result = warehouseDemandPlanService.queryWarehouseDemandPlanDateLabelList(warehouseDemandSubPlanGroupVo);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求版本编辑数据列表
     * @param warehouseDemandPlanDataParamVo
     * @return ResultInfo<BaseTable < List < QueryWarehouseDemandPlanVersionListRspVo>>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 20:52
     */
    @PostMapping("queryWarehouseDemandPlanVersionDataList")
    @ResponseBody
    @ApiOperation("查询分仓需求版本编辑数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<BaseTable<List<QueryWarehouseDemandPlanVersionListRspVo>>> queryWarehouseDemandPlanVersionDataList(
        @RequestBody WarehouseDemandPlanDataParamVo warehouseDemandPlanDataParamVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(warehouseDemandPlanDataParamVo);
        ValidateUtil.checkIsNotEmpty(warehouseDemandPlanDataParamVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandPlanDataParamVo.getVersionId());

        BaseTable<List<QueryWarehouseDemandPlanVersionListRspVo>> result =
            warehouseDemandPlanService.queryWarehouseDemandPlanVersionDataList(warehouseDemandPlanDataParamVo);

        return ResultInfo.success(result);
    }

    @GetMapping("/generalDay")
    @ResponseBody
    @ApiOperation("生成日销数据")
    @RequiresPermissions(value = {})
    public ResultInfo<?> generalDay(@RequestParam("demandPlanCode") String demandPlanCode,@RequestParam("versionId") String versionId,@RequestParam("rebuild") boolean rebuild) throws Exception
    {
        // 共识触发创建日分仓需求滚动版本
        QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo = new QueryDailyWarehouseDemandListReqVo();
        queryDailyWarehouseDemandListReqVo.setDemandPlanCode(demandPlanCode);
        queryDailyWarehouseDemandListReqVo.setDemandPlanVersion(versionId);
        dailyWarehouseDemandService.addDailyWarehouseDemandList(queryDailyWarehouseDemandListReqVo,rebuild);
        return ResultInfo.success();
    }


    /**
     *
     * @Description 查询分仓需求计划数据动态表头列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return ResultInfo<List < String>>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    @PostMapping("queryWarehouseDemandPlanHeadList")
    @ResponseBody
    @ApiOperation("查询分仓需求计划数据动态表头列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryWarehouseDemandPlanHeadList(
        @RequestBody QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo);
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo.getVersionId());

        List<String> result = warehouseDemandPlanService.queryWarehouseDemandPlanHeadList(queryWarehouseDemandPlanVersionListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求计划表头下拉列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return ResultInfo<List < QueryWarehouseDemandPlanVersionListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @PostMapping("queryWarehouseDemandPlanHeadSelect")
    @ResponseBody
    @ApiOperation("查询分仓需求计划表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryWarehouseDemandPlanVersionListRspVo>> queryWarehouseDemandPlanHeadSelect(
        @RequestBody QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo);
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo.getGroupColumnList());

        List<QueryWarehouseDemandPlanVersionListRspVo> result =
            warehouseDemandPlanService.queryWarehouseDemandPlanHeadSelect(queryWarehouseDemandPlanVersionListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求计划数据分组聚合列表
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return ResultInfo<List < QueryWarehouseDemandPlanVersionListRspVo>>
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    @PostMapping("queryWarehouseDemandPlanGroupList")
    @ResponseBody
    @ApiOperation("查询分仓需求计划数据分组聚合列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryWarehouseDemandPlanVersionListRspVo>> queryWarehouseDemandPlanGroupList(
        @RequestBody QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo);
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo.getVersionId());
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo.getGroupColumnList());

        List<QueryWarehouseDemandPlanVersionListRspVo> result =
            warehouseDemandPlanService.queryWarehouseDemandPlanGroupList(queryWarehouseDemandPlanVersionListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询分仓需求计划数据列表
     * @param condition
     * @return ResultInfo<PageInfo < QueryWarehouseDemandPlanVersionListRspVo>>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    @PostMapping("queryWarehouseDemandPlanDataPage")
    @ResponseBody
    @ApiOperation("分页查询分仓需求计划数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<QueryWarehouseDemandPlanVersionListRspVo>> queryWarehouseDemandPlanDataPage(
        @RequestBody PageCondition<QueryWarehouseDemandPlanVersionListReqVo> condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getVersionId());

        PageInfo<QueryWarehouseDemandPlanVersionListRspVo> result = warehouseDemandPlanService.queryWarehouseDemandPlanDataPage(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求计划数据汇总
     * @param queryWarehouseDemandPlanVersionListReqVo
     * @return ResultInfo<Map < String, Double>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("queryWarehouseDemandPlanSummary")
    @ResponseBody
    @ApiOperation("查询分仓需求计划数据汇总")
    @RequiresPermissions(value = {})
    public ResultInfo<Map<String, Double>> queryWarehouseDemandPlanSummary(
        @RequestBody QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo);
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(queryWarehouseDemandPlanVersionListReqVo.getVersionId());

        Map<String, Double> result = warehouseDemandPlanService.queryWarehouseDemandPlanSummary(queryWarehouseDemandPlanVersionListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 修改分仓需求计划数据
     * @param addDemandPlanConfigReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 15:54
     */
    @PostMapping("updateWarehouseDemandPlanData")
    @ResponseBody
    @ApiOperation("修改分仓需求计划数据")
    @RequiresPermissions(value = {})
    public ResultInfo<?> updateWarehouseDemandPlanData(@RequestBody AddDemandPlanConfigReqVo addDemandPlanConfigReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(addDemandPlanConfigReqVo);
        ValidateUtil.checkIsNotEmpty(addDemandPlanConfigReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(addDemandPlanConfigReqVo.getVersionId());
        if (CollectionUtils.isNotEmpty(addDemandPlanConfigReqVo.getPlanList()))
        {
            for (DemandPlanConfigSkuVo demandPlanConfig : addDemandPlanConfigReqVo.getPlanList())
            {
                ValidateUtil.checkIsNotEmpty(demandPlanConfig.getPlanValues());
            }
            warehouseDemandPlanService.updateWarehouseDemandPlanData(addDemandPlanConfigReqVo);
        }

        WarehouseDemandPlanMarkDto warehouseDemandPlanMarkDto = new WarehouseDemandPlanMarkDto();
        warehouseDemandPlanMarkDto.setDemandPlanCode(addDemandPlanConfigReqVo.getDemandPlanCode());
        warehouseDemandPlanMarkDto.setVersionId(addDemandPlanConfigReqVo.getVersionId());
        warehouseDemandPlanMarkDto.setLv3CategoryCode(addDemandPlanConfigReqVo.getLv3CategoryCode());
        warehouseDemandPlanMarkDto.setBizDateValue(addDemandPlanConfigReqVo.getBizDateValue());
        warehouseDemandPlanMarkDto.setReceiverType(addDemandPlanConfigReqVo.getReceiverType());
        warehouseDemandPlanService.markWarehouseDemandPlanData(warehouseDemandPlanMarkDto);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 标记分仓需求计划数据
     * @param warehouseDemandPlanMarkDto
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 15:54
     */
    @PostMapping("markWarehouseDemandPlanData")
    @ResponseBody
    @ApiOperation("标记分仓需求计划数据")
    @RequiresPermissions(value = {})
    public ResultInfo<?> markWarehouseDemandPlanData(@RequestBody WarehouseDemandPlanMarkDto warehouseDemandPlanMarkDto) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto.getVersionId());
        ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto.getLv3CategoryCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto.getBizDateValue());

        warehouseDemandPlanService.markWarehouseDemandPlanData(warehouseDemandPlanMarkDto);

        return ResultInfo.success();
    }


    /**
     *
     * @Description 一次性标记分仓需求计划数据
     * @param listWarehouseDemandPlanMarkDto
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 15:54
     */
    @PostMapping("onceMarkWarehouseDemandPlanData")
    @ResponseBody
    @ApiOperation("标记分仓需求计划数据")
    @RequiresPermissions(value = {})
    public ResultInfo<?> onceMarkWarehouseDemandPlanData(@RequestBody List<WarehouseDemandPlanMarkDto> listWarehouseDemandPlanMarkDto) throws Exception
    {
        listWarehouseDemandPlanMarkDto.stream().forEach(warehouseDemandPlanMarkDto->{
            // 参数校验
            ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto);
            ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto.getDemandPlanCode());
            ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto.getVersionId());
            ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto.getLv3CategoryCode());
            ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto.getBizDateValue());
            ValidateUtil.checkIsNotEmpty(warehouseDemandPlanMarkDto.getReceiverType());
            try {
                warehouseDemandPlanService.markWarehouseDemandPlanData(warehouseDemandPlanMarkDto);
            } catch (Exception e) {
                log.error("onceMarkWarehouseDemandPlanData save error.",e);
            }
        });


        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询分仓需求计划数据预测结果
     * @param channelDemandPlanReceiverDto
     * @return ResultInfo<List < ChannelDemandPlanReceiverDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 15:54
     */
    @PostMapping("queryWarehouseDemandPlanDataForecastResultList")
    @ResponseBody
    @ApiOperation("查询分仓需求计划数据预测结果")
    @RequiresPermissions(value = {})
    public ResultInfo<List<ChannelDemandPlanReceiverDto>> queryWarehouseDemandPlanDataForecastResultList(
        @RequestBody ChannelDemandPlanReceiverDto channelDemandPlanReceiverDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(channelDemandPlanReceiverDto);
        ValidateUtil.checkIsNotEmpty(channelDemandPlanReceiverDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanReceiverDto.getVersionId());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanReceiverDto.getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(channelDemandPlanReceiverDto.getPredictionVersion());

        List<ChannelDemandPlanReceiverDto> result = warehouseDemandPlanService.queryWarehouseDemandPlanDataForecastResultList(channelDemandPlanReceiverDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求计划提报数据
     * @param warehouseDemandReportDto
     * @return ResultInfo<List < WarehouseDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 15:54
     */
    @PostMapping("queryWarehouseDemandPlanDataReportList")
    @ResponseBody
    @ApiOperation("查询分仓需求计划提报数据")
    @RequiresPermissions(value = {})
    public ResultInfo<List<WarehouseDemandReportDto>> queryWarehouseDemandPlanDataReportList(@RequestBody WarehouseDemandReportDto warehouseDemandReportDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getRollingVersion());
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getLv1CategoryCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getSkuCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getBizDateValue());

        List<WarehouseDemandReportDto> result = warehouseDemandPlanService.queryWarehouseDemandPlanDataReportList(warehouseDemandReportDto);

        return ResultInfo.success(result);
    }
}
