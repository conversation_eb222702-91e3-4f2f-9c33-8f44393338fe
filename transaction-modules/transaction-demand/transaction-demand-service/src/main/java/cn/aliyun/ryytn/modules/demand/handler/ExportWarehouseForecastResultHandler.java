package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqForecastResultDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.ExportWarehouseForecastResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo;

/**
 *
 * @Description 导出分仓需求预测结果（需求变更增加功能）
 * <AUTHOR>
 * @date 2024/2/29 9:56
 */
@Component("exportWarehouseForecastResulList")
public class ExportWarehouseForecastResultHandler extends AbstractExportExcelHandler
{
    @Autowired
    private DataqForecastResultDao dataqForecastResultDao;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        // 解析参数
        QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo =
            condition.getCondition().toJavaObject(QueryForecastWarehouseListReqVo.class);

        // 查询分仓需求预测结果
        List<ExportWarehouseForecastResultRspVo> dataList = dataqForecastResultDao.queryExportWarehouseForecastResultList(queryForecastWarehouseListReqVo);

        if (CollectionUtils.isNotEmpty(dataList))
        {
            // 获取所有周日期数据
            Set<String> targetBizDateSet = dataList.stream().map(ExportWarehouseForecastResultRspVo::getTargetBizDate).distinct().collect(Collectors.toSet());

            // 从缓存中获取所有周数据，方便日期到第几周的转换
            List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, targetBizDateSet);
            List<DataqWeek> dataqWeekList = objectList.stream().map(item -> {
                return (DataqWeek) item;
            }).sorted(Comparator.comparing(DataqWeek::getFsclWeekStart)).collect(Collectors.toList());

            Map<String, DataqWeek> dataqWeekMap =
                dataqWeekList.stream().collect(Collectors.toMap(DataqWeek::getFsclWeekStart, Function.identity(), (key1, key2) -> key1));

            int rowId = 1;
            for (ExportWarehouseForecastResultRspVo data : dataList)
            {
                DataqWeek dataqWeek = dataqWeekMap.get(data.getTargetBizDate());
                data.setRowId(rowId++);
                data.setMonth(dataqWeek.getMonthOfFsclYear() + StringUtils.MONTH_UNIT);
                data.setWeek(StringUtils.WEEK_PREFIX_UPPER + dataqWeek.getWeekOfFsclMonth());
            }
        }

        // 导出excel
        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {

            EasyExcel.write(byteArrayOutputStream, ExportWarehouseForecastResultRspVo.class).excelType(ExcelTypeEnum.XLSX).sheet("分仓需求预测结果")
                .doWrite(dataList);
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            bytes = byteArrayOutputStream.toByteArray();
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }
}
