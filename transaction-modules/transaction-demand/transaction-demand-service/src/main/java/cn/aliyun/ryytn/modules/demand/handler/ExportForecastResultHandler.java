package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqForecastResultDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.ExportChannelForecastMonthResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ExportChannelForecastResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo;

/**
 * @Description 导出预测结果
 * <AUTHOR>
 * @date 2023/11/6 10:29
 */
@Component("exportForecastResulList")
public class ExportForecastResultHandler extends AbstractExportExcelHandler
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private DataqForecastResultDao dataqForecastResultDao;

    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        // 解析参数
        QueryChannelForecastResultReqVo queryChannelForecastResultDetailReqVo =
            condition.getCondition().toJavaObject(QueryChannelForecastResultReqVo.class);

        // 筛选周数据
        queryChannelForecastResultDetailReqVo.setPeriodType("week");
        // dataq接口一次最多返回5W数据，当前生产数据约4W，存在查询丢失数据风险，修改为直接查询数据库
        /**
         String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_LIST"));
         DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelForecastResultDetailReqVo);
         JSONArray jsonArray = (JSONArray) dataqResult.getData();
         List<ExportChannelForecastResultRspVo> dataList = Collections.EMPTY_LIST;
         if (CollectionUtils.isNotEmpty(jsonArray))
         {
         dataList = jsonArray.toJavaList(ExportChannelForecastResultRspVo.class);
         }
         */
        List<ExportChannelForecastResultRspVo> dataList1 = dataqForecastResultDao.queryExportChannelForecastResultList(queryChannelForecastResultDetailReqVo);

        // 从缓存中获取所有周数据，方便日期到第几周的转换
        Map<String, Object> dataqWeekMap = redisUtils.hmget(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY);
        int rowId = 1;
        for (ExportChannelForecastResultRspVo i : dataList1)
        {
            DataqWeek dataqWeek = (DataqWeek) dataqWeekMap.get(i.getTargetBizDate());
            i.setRowId(rowId++);
            i.setYear(dataqWeek.getFsclYear());
            i.setMonth(dataqWeek.getMonthOfFsclYear() + "");
            i.setWeek("W" + dataqWeek.getWeekOfFsclMonth());
            i.setPredictionResult(StringUtils.substringBefore(i.getPredictionResult(), StringUtils.POINT_SEPARATOR));
        }

        // 筛选月数据
        queryChannelForecastResultDetailReqVo.setPeriodType("month");
        // dataq接口一次最多返回5W数据，当前生产数据约4W，存在查询丢失数据风险，修改为直接查询数据库
        /**
         DataqResult<?> dataqMonthResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelForecastResultDetailReqVo);
         JSONArray monthJsonArray = (JSONArray) dataqMonthResult.getData();
         List<ExportChannelForecastMonthResultRspVo> monthdataList = Collections.EMPTY_LIST;
         if (CollectionUtils.isNotEmpty(monthJsonArray))
         {
         monthdataList = monthJsonArray.toJavaList(ExportChannelForecastMonthResultRspVo.class);
         }
         */

        List<ExportChannelForecastResultRspVo> dataList2 = dataqForecastResultDao.queryExportChannelForecastResultList(queryChannelForecastResultDetailReqVo);
        List<ExportChannelForecastMonthResultRspVo> monthdataList = new ArrayList<>(dataList2.size());

        if (CollectionUtils.isNotEmpty(dataList2))
        {
            rowId = 1;
            for (ExportChannelForecastResultRspVo i : dataList2)
            {
                ExportChannelForecastMonthResultRspVo monthData = new ExportChannelForecastMonthResultRspVo();
                BeanUtils.copyProperties(i, monthData);
                DataqWeek dataqWeek = (DataqWeek) dataqWeekMap.get(monthData.getTargetBizDate());
                monthData.setRowId(rowId++);
                monthData.setYear(dataqWeek.getFsclYear());
                monthData.setMonth(dataqWeek.getMonthOfFsclYear() + "");
                monthData.setPredictionResult(StringUtils.substringBefore(monthData.getPredictionResult(), StringUtils.POINT_SEPARATOR));
                monthdataList.add(monthData);
            }
            dataList2.clear();
        }

        // 导出excel
        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream).build();)
        {
            WriteSheet sheet1 = EasyExcel.writerSheet("月预测结果数据").head(ExportChannelForecastMonthResultRspVo.class).build();
            WriteSheet sheet2 = EasyExcel.writerSheet("周预测结果数据").head(ExportChannelForecastResultRspVo.class).build();
            excelWriter.write(monthdataList, sheet1);
            excelWriter.write(dataList1, sheet2);
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            bytes = byteArrayOutputStream.toByteArray();
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }

    private class CustomSheetWriteHandler implements SheetWriteHandler
    {
        public void beforeSheetCreate(SheetWriteHandlerContext context)
        {
            this.beforeSheetCreate(context.getWriteWorkbookHolder(), context.getWriteSheetHolder());
        }

        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder)
        {
        }

        public void afterSheetCreate(SheetWriteHandlerContext context)
        {
            this.afterSheetCreate(context.getWriteWorkbookHolder(), context.getWriteSheetHolder());
        }

        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder)
        {
            SXSSFSheet sheet = (SXSSFSheet) writeSheetHolder.getSheet();
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(1);
            sheet.autoSizeColumn(2);
            sheet.autoSizeColumn(3);
            sheet.autoSizeColumn(4);
        }
    }
}
