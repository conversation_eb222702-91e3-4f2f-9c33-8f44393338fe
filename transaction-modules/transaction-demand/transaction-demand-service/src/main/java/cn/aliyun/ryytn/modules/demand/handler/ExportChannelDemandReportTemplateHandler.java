package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.modules.demand.dataqdao.DataqDeliveryOrderRateDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.*;
import cn.hutool.core.stream.StreamUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.excel.HideColSheetWriterHandler;
import cn.aliyun.ryytn.common.excel.HideProperty;
import cn.aliyun.ryytn.common.excel.HideRowSheetWriterHandler;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.ChannelDemandReportService;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandReportDao;
import cn.aliyun.ryytn.modules.system.api.ChannelService;
import cn.aliyun.ryytn.modules.system.api.ProductService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 导出渠道需求提报模板
 * <AUTHOR>
 * @date 2023/10/18 16:23
 */
@Slf4j
@Component("exportChannelDemandTemplate")
public class ExportChannelDemandReportTemplateHandler extends AbstractExportExcelHandler
{
    @Autowired
    private ProductService productService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private ChannelDemandReportService channelDemandReportService;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private DataqChannelDemandReportDao dataqChannelDemandReportDao;


    private final static String[] monthTotalColumnHeadKeys = new String[]{"已过周实发数量","已过周计划偏差","合计"};

    public class CustomStyleCellWriterHandler implements CellWriteHandler
    {
        private List<Integer> historyLockIndexs;

        public CustomStyleCellWriterHandler(List<Integer> historyLockIndexs)
        {
            this.historyLockIndexs = historyLockIndexs;
        }

        @Override
        public void afterCellDispose(CellWriteHandlerContext context)
        {
            WriteCellData<?> cellData = context.getFirstCellData();
            WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
            writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            if (!context.getHead() && historyLockIndexs.contains(context.getColumnIndex()))
            {
                writeCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            }
            else
            {
                writeCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            }
            writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            writeCellStyle.setBorderLeft(BorderStyle.THIN);
            writeCellStyle.setBorderRight(BorderStyle.THIN);
            writeCellStyle.setBorderTop(BorderStyle.THIN);
            writeCellStyle.setBorderBottom(BorderStyle.THIN);
        }
    }

    /**
     *
     * @Description 导出文件字节数组
     * @param condition
     * @return byte[]
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月27日 14:12
     */
    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo =
            condition.getCondition().toJavaObject(QueryChannelDemandReportListReqVo.class);
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        queryChannelDemandReportListReqVo.setIsModify(0);
        queryChannelDemandReportListReqVo.setDataScope(true);
        queryChannelDemandReportListReqVo.setTableSuffix((StringUtils.isBlank(queryChannelDemandReportListReqVo.getRollingVersion()))?"":queryChannelDemandReportListReqVo.getRollingVersion().substring(3,9));

        // 导出模板表格头二维数组
        List<List<String>> headList = new ArrayList<List<String>>();
        // 通过反射获取固定字段表头
        Class clazz = QueryChannelDemandTemplateDataRspVo.class;
        // 遍历所有字段，获取所有ExcelProperty注解
        List<ExcelProperty> excelPropertyList = new ArrayList<>(clazz.getDeclaredFields().length);
        List<Integer> hideCols = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields())
        {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (Objects.isNull(excelProperty))
            {
                continue;
            }
            HideProperty hideProperty = field.getAnnotation(HideProperty.class);
            if (Objects.nonNull(hideProperty) && hideProperty.isHide())
            {
                hideCols.add(excelProperty.index());
            }
            excelPropertyList.add(excelProperty);
        }
        // ExcelProperty注解集合根据index正序排序，取value值转换为List
        List<List<String>> fieldHeadList =
            excelPropertyList.stream().sorted(Comparator.comparing(ExcelProperty::index)).map(item -> {
                return Arrays.asList(item.value());
            }).collect(Collectors.toList());
        headList.addAll(fieldHeadList);

        // 版本首月根据版本号计算，防止导入时间不在当月导致模板数据错误
//        Date versionDate =
//            DateUtils.parseDate(
//                StringUtils.substringBefore(StringUtils.replace(queryChannelDemandReportListReqVo.getRollingVersion(), "CDP", StringUtils.EMPTY),
//                    StringUtils.WEEK_PREFIX_UPPER), DateUtils.YM);
//
//        // 获取当前月+下三月的周数据，生成动态表头
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(versionDate);
//        List<List<String>> trendsHeadList = new ArrayList<>();
//
//        // 获取所有周第一天字符串
//        Set<String> items = redisUtils.hKeys(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY);
//
//        // 循环当前月+下三月，TreeSet默认根据bizDateValue（周第一天日期）排序
//        Set<String> bizDateValueSet = new TreeSet<>();
//        for (int i = 0; i < 4; i++)
//        {
//            String yearMonth = DateUtils.formatTime(calendar.getTime(), DateUtils.YM);
//            Set<String> temp = items.stream().filter(item -> {
//                return item.startsWith(yearMonth);
//            }).collect(Collectors.toSet());
//            bizDateValueSet.addAll(temp);
//            calendar.add(Calendar.MONTH, 1);
//        }

        // 不根据版本号时间直接计算，按系统已生成数据计算
        List<String> bizDateValueList = dataqChannelDemandReportDao.queryChannelDemandReportHeadList(queryChannelDemandReportListReqVo);
        List<List<String>> trendsHeadList = new ArrayList<>();
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(),
            (key1, key2) -> key1));

        //获取年月周 如:202410W4
        String rollingVersionYMW = StringUtils.replace(queryChannelDemandReportListReqVo.getRollingVersion(), "CDP", StringUtils.EMPTY);
        Object rollingVersionDataqWeekObject = dataqWeekList.stream().filter(x->((DataqWeek)x).getYearMonthWeek().equals(rollingVersionYMW)).findFirst().orElse(null);
        String rollingVersionStartDate = "19010101";
        if(!Objects.isNull(rollingVersionDataqWeekObject)){
            rollingVersionStartDate = ((DataqWeek)rollingVersionDataqWeekObject).getFsclWeekStart();
        }else{
            log.error("rollingVersionDataqWeekObject is null,not found.");
        }
        log.info("rollingVersionStartDate value is :{}",rollingVersionStartDate);

        String preMonthLabel = "";
        int bizDateValueListIndex = 0;
        for (String bizDateValue : bizDateValueList)
        {
            DataqWeek dataqWeek = dataqWeekMap.get(bizDateValue);
            if(StringUtils.isEmpty(preMonthLabel)){
                preMonthLabel = dataqWeek.getMonthLabel();
            }
            if(!dataqWeek.getMonthLabel().equals(preMonthLabel)){
                List<String> listOverWeekDelivery = new ArrayList<>(3);
                listOverWeekDelivery.add(preMonthLabel);
                listOverWeekDelivery.add(preMonthLabel +monthTotalColumnHeadKeys[0]);//"已过周实发数量"
                listOverWeekDelivery.add(preMonthLabel);
                trendsHeadList.add(listOverWeekDelivery);

                List<String> listPlanDiff = new ArrayList<>(3);
                listPlanDiff.add(preMonthLabel);
                listPlanDiff.add(preMonthLabel+monthTotalColumnHeadKeys[1]);//已过周计划偏差
                listPlanDiff.add(preMonthLabel);
                trendsHeadList.add(listPlanDiff);

                List<String> listMonthTotal = new ArrayList<>(3);
                listMonthTotal.add(preMonthLabel);
                listMonthTotal.add(preMonthLabel +monthTotalColumnHeadKeys[2]);//合计
                listMonthTotal.add(preMonthLabel);
                trendsHeadList.add(listMonthTotal);
                preMonthLabel = dataqWeek.getMonthLabel();
            }
            List<String> list = new ArrayList<>(3);
            list.add(dataqWeek.getMonthLabel());
            list.add(dataqWeek.getWeekLabel());
            list.add(dataqWeek.getFsclWeekRange());
            trendsHeadList.add(list);
            if(bizDateValueListIndex == bizDateValueList.size()-1){
                List<String> listOverWeekDelivery = new ArrayList<>(3);
                listOverWeekDelivery.add(preMonthLabel);
                listOverWeekDelivery.add(preMonthLabel +monthTotalColumnHeadKeys[0]);//"已过周实发数量"
                listOverWeekDelivery.add(preMonthLabel);
                trendsHeadList.add(listOverWeekDelivery);

                List<String> listPlanDiff = new ArrayList<>(3);
                listPlanDiff.add(preMonthLabel);
                listPlanDiff.add(preMonthLabel+monthTotalColumnHeadKeys[1]);//已过周计划偏差
                listPlanDiff.add(preMonthLabel);
                trendsHeadList.add(listPlanDiff);

                List<String> listMonthTotal = new ArrayList<>(3);
                listMonthTotal.add(preMonthLabel);
                listMonthTotal.add(preMonthLabel +monthTotalColumnHeadKeys[2]);//合计
                listMonthTotal.add(preMonthLabel);
                trendsHeadList.add(listMonthTotal);
            }
            bizDateValueListIndex++;

        }

        headList.addAll(trendsHeadList);

        // 历史锁定期的表头列索引
        String currentDate = DateUtils.getDate(DateUtils.YMD);
        List<Integer> historyLockIndexs = new ArrayList<>();
        for (int i = 0; i < trendsHeadList.size(); i++)
        {
            String bizTypeValue = StringUtils.substringAfter(trendsHeadList.get(i).get(2), StringUtils.DATE_SEPARATOR);
            // 当前日期已经大于周最后一天日期，则属于历史锁定期列
            if (currentDate.compareTo(bizTypeValue) > 0)
            {
                historyLockIndexs.add(fieldHeadList.size() + i);
            }
            // 动态表头是排过序的，如果出现大于当前时间的周，直接跳出循环，后面不会再有历史锁定期
            else
            {
                break;
            }
        }

        // 由于存在动态字段，无法通过Java类定义电子表格数据，通过Object二维数组实现，字段顺序写死
        List<List<Object>> dataList = new ArrayList<>();

        // 数据权限
        channelDemandReportService.generateDataScope(queryChannelDemandReportListReqVo);

        // dataq接口一次最多返回5W数据，当前生产数据约4W，存在查询丢失数据风险，修改为直接查询数据库
        /**
         // 获取查询动态字段接口地址
         String columnPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_COLUMN"));
         DataqResult<?> columnResult = dataqService.invoke(HttpMethod.POST, columnPath, null, null, queryChannelDemandReportListReqVo);
         List<JSONObject> columnList = (List<JSONObject>) columnResult.getData();
         if (CollectionUtils.isNotEmpty(columnList))
         {
         // 封装动态字段
         queryChannelDemandReportListReqVo.setSelectColumn(columnList.get(0).getString("trendsColumn"));
         // 按照最小粒度周粒度查询
         queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);

         Map<String, Object> param = new HashMap<String, Object>();
         param.put("page_size", 100000);

         // 获取查询表数据接口地址
         String tablePath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_TABLE"));
         DataqResult<?> reportResult = dataqService.invoke(HttpMethod.POST, tablePath, null, param, queryChannelDemandReportListReqVo);
         List<JSONObject> jsonObjectList = (List<JSONObject>) reportResult.getData();

         String year = DateUtils.getDate(DateUtils.Y);
         for (JSONObject jsonObject : jsonObjectList)
         {
         List<Object> rowDataList = new ArrayList<>();
         QueryChannelDemandTemplateDataRspVo queryChannelDemandTemplateDataRspVo =
         jsonObject.toJavaObject(QueryChannelDemandTemplateDataRspVo.class);
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv1CategoryName());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv2CategoryName());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv3CategoryName());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getSkuCode());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getSkuName());
         rowDataList.add(QueryChannelDemandTemplateDataRspVo.status);
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv1ChannelName());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv2ChannelName());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv3ChannelName());
         rowDataList.add(year);
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv1CategoryCode());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv2CategoryCode());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv3CategoryCode());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv1ChannelCode());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv2ChannelCode());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv3ChannelCode());
         rowDataList.add(queryChannelDemandTemplateDataRspVo.getUnit());
         for (List<String> trendsKeys : trendsHeadList)
         {
         // 第三行隐藏表头内容为dataqWeek.getFsclWeekRange()，周第一天到周最后一天，yyyyMMdd-yyyyMMdd
         String weekRange = trendsKeys.get(2);
         String bizTypeValue = StringUtils.substringBefore(weekRange, StringUtils.DATE_SEPARATOR);
         Double num = null;
         if (Objects.nonNull(jsonObject.getJSONObject(bizTypeValue)))
         {
         num = jsonObject.getJSONObject(bizTypeValue).getDouble("orderNum");
         }
         rowDataList.add(Objects.isNull(num) ? 0d : num);
         }
         dataList.add(rowDataList);
         }
         }
         */

        log.info("queryChannelDemandReportDataJsonList params is :{}", JSON.toJSONString(queryChannelDemandReportListReqVo));
        List<QueryChannelDemandReportListRspVo> channelDemandReportDataList =
            dataqChannelDemandReportDao.queryChannelDemandReportDataJsonList(queryChannelDemandReportListReqVo);

//        String skuCodes = channelDemandReportDataList.stream().map(QueryChannelDemandReportListRspVo::getSkuCode).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//        String lv3ChannelCodes = channelDemandReportDataList.stream().map(QueryChannelDemandReportListRspVo::getLv3ChannelCode).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//        List<String> historyWeekStartList = new ArrayList<>();
//        dataqWeekMap.forEach((k,v)->{
//            if (StringUtils.compare(v.getFsclWeekEnd(), currentDate) < 0)
//            {
//                historyWeekStartList.add(v.getFsclWeekStart());
//            }
//        });
//        ChannelDemandRealityDataVo queryChannelDemandRealityDataVo = new ChannelDemandRealityDataVo();
//        queryChannelDemandRealityDataVo.setDimComb("LV3_CHANNEL_CODE+SKU+LV3_CATEGORY_CODE+WEEK");
//        queryChannelDemandRealityDataVo.setBizDates(historyWeekStartList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
//        queryChannelDemandRealityDataVo.setSkuCodes(skuCodes);
//        queryChannelDemandRealityDataVo.setLv3ChannelCodes(lv3ChannelCodes);
//
//        List<ChannelDemandRealityDataVo> historyRealityDataList =dataqDeliveryOrderRateDao.queryHistoryRealityDeliveryOrderByWeek(queryChannelDemandRealityDataVo);
//        Map<ChannelDemandRealityDataVo,ChannelDemandRealityDataVo> realityDataVoMap =  historyRealityDataList.stream().collect(Collectors.toMap(Function.identity(),v->v));




        String year = DateUtils.getDate(DateUtils.Y);
        if (CollectionUtils.isNotEmpty(channelDemandReportDataList))
        {
            for (QueryChannelDemandReportListRspVo queryChannelDemandTemplateDataRspVo : channelDemandReportDataList)
            {
                List<Object> rowDataList = new ArrayList<>();
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv1CategoryName());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv2CategoryName());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv3CategoryName());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getSkuCode());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getSkuName());
                rowDataList.add(QueryChannelDemandTemplateDataRspVo.status);
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv1ChannelName());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv2ChannelName());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv3ChannelName());
                rowDataList.add(year);
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv1CategoryCode());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv2CategoryCode());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv3CategoryCode());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv1ChannelCode());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv2ChannelCode());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getLv3ChannelCode());
                rowDataList.add(queryChannelDemandTemplateDataRspVo.getUnit());
                if (CollectionUtils.isNotEmpty(queryChannelDemandTemplateDataRspVo.getDataList()))
                {
                    queryChannelDemandTemplateDataRspVo.setDataMap(
                        queryChannelDemandTemplateDataRspVo.getDataList().stream().collect(Collectors.toMap(ChannelDemandReportDataVo::getBizDateValue,
                            Function.identity(), (ke1, key2) -> key2)));
                }

                List<WeekDataValue> weekDataValuesList = new ArrayList<>();
                for (List<String> trendsKeys : trendsHeadList)
                {
                    String monthTotalHeadCloumnName = StreamUtil.of(monthTotalColumnHeadKeys).filter(x->trendsKeys.get(1).contains(x)).findFirst().orElse(null);
                    // 第三行隐藏表头内容为dataqWeek.getFsclWeekRange()，周第一天到周最后一天，yyyyMMdd-yyyyMMdd
                    String weekRange = trendsKeys.get(2);
                    String bizTypeValue = StringUtils.substringBefore(weekRange, StringUtils.DATE_SEPARATOR);
                    if(StringUtils.isEmpty(monthTotalHeadCloumnName)){
                        Double num = null;
                        Double realityOrderNum = 0d;
                        if (Objects.nonNull(queryChannelDemandTemplateDataRspVo.getDataMap().get(bizTypeValue)))
                        {
                            num = queryChannelDemandTemplateDataRspVo.getDataMap().get(bizTypeValue).getOrderNum();
                            realityOrderNum = queryChannelDemandTemplateDataRspVo.getDataMap().get(bizTypeValue).getRealityOrderNum();
                        }


                        WeekDataValue weekDataValue = new WeekDataValue();
                        weekDataValue.setWeekStartDate(bizTypeValue);
                        weekDataValue.setWeekEndDate(StringUtils.substringAfter(weekRange, StringUtils.DATE_SEPARATOR));
                        weekDataValue.setPlanValue(num);
                        weekDataValue.setMonthLabel(StringUtils.substringBefore(trendsKeys.get(1),"月")+"月");
                        weekDataValue.setHistoryWeek(rollingVersionStartDate.compareTo(weekDataValue.getWeekEndDate()) > 0?true:false);
                        weekDataValue.setWeekActualValue(realityOrderNum);
                        if(!weekDataValue.isHistoryWeek()){
                            rowDataList.add(Objects.isNull(num) ? 0d : num);
                        }else{
                            rowDataList.add(realityOrderNum);
                        }


//                        ChannelDemandRealityDataVo searchChannelDemandRealityDataVo = new ChannelDemandRealityDataVo();
//                        searchChannelDemandRealityDataVo.setSkuCode(queryChannelDemandTemplateDataRspVo.getSkuCode());
//                        searchChannelDemandRealityDataVo.setLv3ChannelCode(queryChannelDemandTemplateDataRspVo.getLv3ChannelCode());
//                        searchChannelDemandRealityDataVo.setBizWeekDateStart(bizTypeValue);
//                        if(!Objects.isNull(realityDataVoMap.get(searchChannelDemandRealityDataVo))){
//                            weekDataValue.setWeekActualValue(realityDataVoMap.get(searchChannelDemandRealityDataVo).getRealityOrderNum());
//                        }else
//                        {
//                            weekDataValue.setWeekActualValue(0d);
//                        }
                        weekDataValuesList.add(weekDataValue);
                    }else{
                        if(trendsKeys.get(1).indexOf(monthTotalColumnHeadKeys[0]) > 0){
                            //已过周实发数量"
                            Double historyWeekTotalActualValue = 0d;
                            historyWeekTotalActualValue = weekDataValuesList.stream().filter(x->x.isHistoryWeek() && x.getMonthLabel().equals(trendsKeys.get(0))).mapToDouble(x->Objects.isNull(x.getWeekActualValue())?0d:x.getWeekActualValue()).sum();
                            rowDataList.add(historyWeekTotalActualValue);
                        }
                        if(trendsKeys.get(1).indexOf(monthTotalColumnHeadKeys[1]) > 0){
                            //,"已过周计划偏差:= 已过周计划值总和-已过周实际值总和
                            Double historyWeekTotalActualValue = 0d;
                            historyWeekTotalActualValue = weekDataValuesList.stream().filter(x->x.isHistoryWeek() && x.getMonthLabel().equals(trendsKeys.get(0))).mapToDouble(x->Objects.isNull(x.getWeekActualValue())?0d:x.getWeekActualValue()).sum();
                            rowDataList.add(weekDataValuesList.stream().filter(x->x.isHistoryWeek() && x.getMonthLabel().equals(trendsKeys.get(0))).mapToDouble(x->Objects.isNull(x.getPlanValue())?0d:x.getPlanValue()).sum()-historyWeekTotalActualValue);
                        }
                        if(trendsKeys.get(1).indexOf(monthTotalColumnHeadKeys[2]) > 0){
                            //合计=未过周计划值+已过周实际实际值
                            Double historyWeekTotalActualValue = 0d;
                            historyWeekTotalActualValue = weekDataValuesList.stream().filter(x->x.isHistoryWeek() && x.getMonthLabel().equals(trendsKeys.get(0))).mapToDouble(x->Objects.isNull(x.getWeekActualValue())?0d:x.getWeekActualValue()).sum();
                            rowDataList.add(weekDataValuesList.stream().filter(x->!x.isHistoryWeek() && x.getMonthLabel().equals(trendsKeys.get(0))).mapToDouble(x->Objects.isNull(x.getPlanValue())?0d:x.getPlanValue()).sum()+historyWeekTotalActualValue);
                        }
                    }


                }
                dataList.add(rowDataList);
            }
        }

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream)
                .registerWriteHandler(new HideColSheetWriterHandler(hideCols))
                .registerWriteHandler(new CustomStyleCellWriterHandler(historyLockIndexs))
                .registerWriteHandler(new HideRowSheetWriterHandler(2))
                .head(headList)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("渠道需求提报导入模板").doWrite(dataList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }


    public class WeekDataValue{

        /**
         * 一周的开始时间:20240506
         */
        private String weekStartDate;

        /**
         * 一周的结束时间:20240512
         */
        private String weekEndDate;
        /**
         *周实际值
         */
        private Double weekActualValue;

        /**
         * 月标识：5月
         */
        private String monthLabel;

        /**
         * 计划值
         */
        private Double planValue;

        /**
         *是否是历史周
         */
        private boolean historyWeek;

        public boolean isHistoryWeek() {
            return historyWeek;
        }

        public void setHistoryWeek(boolean historyWeek) {
            this.historyWeek = historyWeek;
        }


        public String getWeekStartDate() {
            return weekStartDate;
        }

        public void setWeekStartDate(String weekStartDate) {
            this.weekStartDate = weekStartDate;
        }

        public String getWeekEndDate() {
            return weekEndDate;
        }

        public void setWeekEndDate(String weekEndDate) {
            this.weekEndDate = weekEndDate;
        }

        public Double getWeekActualValue() {
            return weekActualValue;
        }

        public void setWeekActualValue(Double weekActualValue) {
            this.weekActualValue = weekActualValue;
        }

        public Double getPlanValue() {
            return planValue;
        }

        public void setPlanValue(Double planValue) {
            this.planValue = planValue;
        }

        public String getMonthLabel() {
            return monthLabel;
        }

        public void setMonthLabel(String monthLabel) {
            this.monthLabel = monthLabel;
        }

    }
}
