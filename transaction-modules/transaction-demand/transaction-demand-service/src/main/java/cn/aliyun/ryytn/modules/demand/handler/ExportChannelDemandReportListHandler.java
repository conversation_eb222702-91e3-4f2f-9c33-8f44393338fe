package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.util.IOUtils;
import com.github.pagehelper.PageHelper;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListRspVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 导出渠道需求提报数据列表
 * <AUTHOR>
 * @date 2023/10/27 10:28
 */
@Slf4j
@Component("exportChannelDemandReportList")
public class ExportChannelDemandReportListHandler extends AbstractExportExcelHandler
{
    @Autowired
    private DataqChannelDemandReportDao dataqChannelDemandReportDao;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 导出渠道需求提报数据列表
     * @param condition
     * @return byte[]
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月27日 10:29
     */
    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        // 解析参数
        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo =
            condition.getCondition().toJavaObject(QueryChannelDemandReportListReqVo.class);

        // 导出时间粒度必须为周粒度
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        queryChannelDemandReportListReqVo.setIsModify(0);

        List<String> bizDateValueList = dataqChannelDemandReportDao.queryChannelDemandReportHeadList(queryChannelDemandReportListReqVo);
        // 获取指定周的周对象
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(),
            (key1, key2) -> key1));


        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream).excelType(ExcelTypeEnum.XLSX).build();
        try
        {
            // xlsx文件最大不能超过104W行
            int totalRowCount = dataqChannelDemandReportDao.queryChannelDemandReportVersionCount(queryChannelDemandReportListReqVo);
            int rowId = 1;
            Integer pageSize = 1000000;
            if (totalRowCount <= pageSize)
            {
                List<QueryChannelDemandReportListRspVo> dataList =
                    dataqChannelDemandReportDao.queryChannelDemandReportDataList(queryChannelDemandReportListReqVo);
                for (QueryChannelDemandReportListRspVo data : dataList)
                {
                    DataqWeek dataqWeek = dataqWeekMap.get(data.getBizDateValue());
                    data.setRowId(rowId++);
                    data.setWaresName(data.getSkuName());
                    data.setMonth(dataqWeek.getMonthLabel());
                    data.setWeek(StringUtils.WEEK_PREFIX_UPPER + dataqWeek.getWeekOfFsclMonth());
                }
                WriteSheet writeSheet = EasyExcel.writerSheet("渠道需求提报数据").head(QueryChannelDemandReportListRspVo.class).build();
                excelWriter.write(dataList, writeSheet);
                excelWriter.finish();
                dataList.clear();
            }
            else
            {
                Integer writeCount = totalRowCount % pageSize == 0 ? (totalRowCount / pageSize) : (totalRowCount / pageSize + 1);
                for (int i = 1; i <= writeCount; i++)
                {
                    PageHelper.startPage(i, pageSize);
                    List<QueryChannelDemandReportListRspVo> dataList =
                        dataqChannelDemandReportDao.queryChannelDemandReportDataList(queryChannelDemandReportListReqVo);

                    for (QueryChannelDemandReportListRspVo data : dataList)
                    {
                        DataqWeek dataqWeek = dataqWeekMap.get(data.getBizDateValue());
                        data.setRowId(rowId++);
                        data.setWaresName(data.getSkuName());
                        data.setMonth(dataqWeek.getMonthLabel());
                        data.setWeek(StringUtils.WEEK_PREFIX_UPPER + dataqWeek.getWeekOfFsclMonth());
                    }
                    WriteSheet writeSheet = EasyExcel.writerSheet("渠道需求提报数据" + i).head(QueryChannelDemandReportListRspVo.class).build();
                    excelWriter.write(dataList, writeSheet);
                    dataList.clear();
                }
                excelWriter.finish();
            }


            bytes = byteArrayOutputStream.toByteArray();
            byteArrayOutputStream.flush();
        }
        catch (Exception e)
        {
            log.error("export has exception:", e);
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }

    /**
     *
     * @Description 导出渠道需求提报数据列表
     * @param condition
     * @return byte[]
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月27日 10:29
     */
    @Deprecated
    public byte[] exportDataq(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        // 解析参数
        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo =
            condition.getCondition().toJavaObject(QueryChannelDemandReportListReqVo.class);

        // 导出时间粒度必须为周粒度
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        queryChannelDemandReportListReqVo.setIsModify(0);

        int pageNum = 1;
        int pageSize = 5000;
        int skip = pageSize;
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("page_token", pageNum);
        param.put("page_size", pageSize);
        param.put("fetch_all", true);
        param.put("order_by", "id");

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandReportListReqVo);
        int total = dataqResult.getTotal();
        List<QueryChannelDemandReportListRspVo> dataList = new ArrayList<>(total);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            dataList.addAll(jsonArray.toJavaList(QueryChannelDemandReportListRspVo.class));
        }
        while (total > skip)
        {
            try
            {
                param.put("page_token", pageNum + 1);
                dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandReportListReqVo);
                jsonArray = (JSONArray) dataqResult.getData();
                dataList.addAll(jsonArray.toJavaList(QueryChannelDemandReportListRspVo.class));
                skip = skip + pageSize;
            }
            catch (Exception e)
            {
                log.error("exportChannelDemandReportList queryChannelDemandReportList error.pagenum:{},pageSize:{}", pageNum, pageSize);
            }
        }
        param.put("page_token", pageNum + 1);
        dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandReportListReqVo);
        jsonArray = (JSONArray) dataqResult.getData();
        dataList.addAll(jsonArray.toJavaList(QueryChannelDemandReportListRspVo.class));

        Set<String> bizDateValueSet = dataList.stream().map(QueryChannelDemandReportListRspVo::getBizDateValue).collect(Collectors.toSet());

        // 获取指定周的周对象
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueSet);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(),
            (key1, key2) -> key1));

        int rowId = 1;
        for (QueryChannelDemandReportListRspVo data : dataList)
        {
            DataqWeek dataqWeek = dataqWeekMap.get(data.getBizDateValue());
            data.setRowId(rowId++);
            data.setWaresName(data.getSkuName());
            data.setMonth(dataqWeek.getMonthLabel());
            data.setWeek(StringUtils.WEEK_PREFIX_UPPER + dataqWeek.getWeekOfFsclMonth());
        }

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream, QueryChannelDemandReportListRspVo.class).excelType(ExcelTypeEnum.XLSX).sheet("渠道需求提报数据")
                .doWrite(dataList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }
}
