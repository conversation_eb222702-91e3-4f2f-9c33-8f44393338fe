package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.excel.HideColSheetWriterHandler;
import cn.aliyun.ryytn.common.excel.HideProperty;
import cn.aliyun.ryytn.common.excel.HideRowSheetWriterHandler;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.ColdDemandReportService;
import cn.aliyun.ryytn.modules.demand.dao.ColdDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportTemplateDto;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 导出低温需求提报模板
 * <AUTHOR>
 * @date 2023/12/6 14:33
 */
@Slf4j
@Component("exportColdDemandReportTemplate")
public class ExportColdDemandReportTemplateHandler extends AbstractExportExcelHandler
{
    @Autowired
    private ColdDemandReportDao coldDemandReportDao;

    @Autowired
    private ColdDemandReportService coldDemandReportService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 导出低温需求提报模板
     * @param condition
     * @return byte[]
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 14:35
     */
    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        ColdDemandReportDto coldDemandReportDto = condition.getCondition().toJavaObject(ColdDemandReportDto.class);
        String tableSuffix = StringUtils.substringBefore(StringUtils.substringAfter(coldDemandReportDto.getRollingVersion(), "DDP"),
            StringUtils.WEEK_PREFIX_UPPER);
        coldDemandReportDto.setTableSuffix(tableSuffix);

        // 导出模板表格头二维数组
        List<List<String>> headList = new ArrayList<List<String>>();
        // 通过反射获取固定字段表头
        Class clazz = ColdDemandReportTemplateDto.class;
        // 遍历所有字段，获取所有ExcelProperty注解
        List<ExcelProperty> excelPropertyList = new ArrayList<>(clazz.getDeclaredFields().length);
        List<Integer> hideCols = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields())
        {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (Objects.isNull(excelProperty))
            {
                continue;
            }
            HideProperty hideProperty = field.getAnnotation(HideProperty.class);
            if (Objects.nonNull(hideProperty) && hideProperty.isHide())
            {
                hideCols.add(excelProperty.index());
            }
            excelPropertyList.add(excelProperty);
        }
        // ExcelProperty注解集合根据index正序排序，取value值转换为List
        List<List<String>> fieldHeadList =
            excelPropertyList.stream().sorted(Comparator.comparing(ExcelProperty::index)).map(item -> {
                return Arrays.asList(item.value());
            }).collect(Collectors.toList());
        headList.addAll(fieldHeadList);

        // 获取版本日期集合
        List<String> bizDateValueList = coldDemandReportDao.queryColdDemandReportDateList(coldDemandReportDto);

//        Calendar calendar = Calendar.getInstance();
        List<List<String>> trendsHeadList = new ArrayList<>();
        for (String bizDateValue : bizDateValueList)
        {
            Date reportDate = DateUtils.parseDate(bizDateValue, DateUtils.YMD);
            String weekBegin = DateUtils.getWeekBegin(reportDate, DateUtils.MD_SLASH);
            String weekEnd = DateUtils.getWeekEnd(reportDate, DateUtils.MD_SLASH);

            List<String> list = new ArrayList<>(3);
            list.add(new StringBuilder().append(weekBegin).append(StringUtils.TO_SEPARATOR).append(weekEnd).toString());
            list.add(DateUtils.formatTime(reportDate, DateUtils.MD_SLASH));
            list.add(bizDateValue);
            trendsHeadList.add(list);
        }

        headList.addAll(trendsHeadList);

        // 数据权限
        coldDemandReportService.generateDataScope(coldDemandReportDto);

        String year = StringUtils.substring(StringUtils.replace(coldDemandReportDto.getRollingVersion(), "DDP", StringUtils.EMPTY), 0, 4);
        // 查询数据库中的低温需求提报数据
        List<ColdDemandReportTemplateDto> coldDemandReportList = coldDemandReportDao.queryColdDemandReportTemplateList(coldDemandReportDto);

        // 由于存在动态字段，无法通过Java类定义电子表格数据，通过Object二维数组实现，字段顺序写死
        List<List<Object>> dataList = new ArrayList<>(coldDemandReportList.size());
        for (ColdDemandReportTemplateDto reportData : coldDemandReportList)
        {
            List<Object> rowDataList = new ArrayList<>();
            rowDataList.add(reportData.getLv1CategoryName());
            rowDataList.add(reportData.getLv2CategoryName());
            rowDataList.add(reportData.getLv3CategoryName());
            rowDataList.add(reportData.getSkuCode());
            rowDataList.add(reportData.getSkuName());
            rowDataList.add(reportData.getStatus());
            rowDataList.add(reportData.getLv1ChannelName());
            rowDataList.add(reportData.getLv2ChannelName());
            rowDataList.add(reportData.getLv3ChannelName());
            rowDataList.add(year);
            rowDataList.add(reportData.getLv1CategoryCode());
            rowDataList.add(reportData.getLv2CategoryCode());
            rowDataList.add(reportData.getLv3CategoryCode());
            rowDataList.add(reportData.getLv1ChannelCode());
            rowDataList.add(reportData.getLv2ChannelCode());
            rowDataList.add(reportData.getLv3ChannelCode());
            rowDataList.add(reportData.getPlanUnitCnt());
            Map<String, String> dateValueMap = (Map<String, String>) JSON.parseObject(reportData.getData(), Map.class);
            for (List<String> trendsKeys : trendsHeadList)
            {
                // 第三行隐藏表头内容为bizDateValue值，yyyyMMdd
                String bizDateValue = trendsKeys.get(2);
                String num = Objects.isNull(dateValueMap.get(bizDateValue)) ? StringUtils.ZERO : String.valueOf(dateValueMap.get(bizDateValue));
                rowDataList.add(StringUtils.isEmpty(num) ? 0d : num);
            }
            dataList.add(rowDataList);
        }
        coldDemandReportList.clear();

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream)
                .registerWriteHandler(new HideColSheetWriterHandler(hideCols))
                .registerWriteHandler(new HideRowSheetWriterHandler(2))
                .head(headList)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("低温需求提报导入模板").doWrite(dataList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }
}
