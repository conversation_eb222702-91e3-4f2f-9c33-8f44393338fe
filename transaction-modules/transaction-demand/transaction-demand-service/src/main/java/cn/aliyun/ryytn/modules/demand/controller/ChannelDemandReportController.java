package cn.aliyun.ryytn.modules.demand.controller;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Resource;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.AsyncChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.api.ChannelDemandReportService;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportVersionVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportGroupListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportHistoryRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportedChannelIdListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.UpdateChannelDemandReportVo;
import cn.aliyun.ryytn.modules.demand.repository.DataqChannelDemandReportRepository;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 渠道需求提报接口
 * <AUTHOR>
 * @date 2023/10/24 10:10
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/channelDemandReport")
@Api(tags = "渠道需求提报")
public class ChannelDemandReportController
{
    @Autowired
    private ChannelDemandReportService channelDemandReportService;
    @Resource
    private DataqChannelDemandReportRepository repository;

    @Autowired
    private AsyncChannelDemandPlanService asyncChannelDemandPlanService;

    /**
     *
     * @Description 查询渠道需求提报版本列表
     * @param queryChannelDemandReportVersionListReqVo
     * @return ResultInfo<List < ChannelDemandReportVersionVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("queryChannelDemandReportVersionList")
    @ResponseBody
    @ApiOperation("查询渠道需求提报版本列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<ChannelDemandReportVersionVo>> queryChannelDemandReportVersionList(
        @RequestBody ChannelDemandReportVersionVo queryChannelDemandReportVersionListReqVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportVersionListReqVo);

        List<ChannelDemandReportVersionVo> result = channelDemandReportService.queryChannelDemandReportVersionList(queryChannelDemandReportVersionListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求提报版本
     * @param rollingVersion
     * @return ResultInfo<ChannelDemandReportVersionDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("queryChannelDemandReportVersion")
    @ResponseBody
    @ApiOperation("查询渠道需求提报版本列表")
    @RequiresPermissions(value = {})
    public ResultInfo<ChannelDemandReportVersionDto> queryChannelDemandReportVersion(@RequestBody ChannelDemandReportVersionDto channelDemandReportVersionDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandReportVersionDto);

        ChannelDemandReportVersionDto result = channelDemandReportService.queryChannelDemandReportVersion(channelDemandReportVersionDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求提报列表
     * @param queryChannelDemandReportListReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("queryChannelDemandReportList")
    @ResponseBody
    @ApiOperation("查询渠道需求提报列表")
    @RequiresPermissions(value = {})
    public ResultInfo<?> queryChannelDemandReportList(@RequestBody QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo);

        BaseTable<List<QueryChannelDemandReportListRspVo>> result = channelDemandReportService.queryChannelDemandReportList(queryChannelDemandReportListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求提报动态表头
     * @param queryChannelDemandReportListReqVo
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:15
     */
    @PostMapping("queryChannelDemandReportHeadList")
    @ResponseBody
    @ApiOperation("查询渠道需求提报动态表头")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryChannelDemandReportHeadList(@RequestBody QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo);
        if (Objects.isNull(queryChannelDemandReportListReqVo.getBizDateType()))
        {
            queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        }
        List<String> result = Collections.EMPTY_LIST;
        if (StringUtils.isNotBlank(queryChannelDemandReportListReqVo.getRollingVersion()))
        {
            result = channelDemandReportService.queryChannelDemandReportHeadList(queryChannelDemandReportListReqVo);
        }
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求提报表头下拉列表
     * @param queryChannelDemandReportListReqVo
     * @return ResultInfo<List < QueryChannelDemandReportGroupListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @PostMapping("queryChannelDemandReportHeadSelect")
    @ResponseBody
    @ApiOperation("查询渠道需求提报表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryChannelDemandReportGroupListRspVo>> queryChannelDemandReportHeadSelect(
        @RequestBody QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getGroupColumnList());
        if (Objects.isNull(queryChannelDemandReportListReqVo.getBizDateType()))
        {
            queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        }
        List<QueryChannelDemandReportGroupListRspVo> result = Collections.EMPTY_LIST;
        if (StringUtils.isNotBlank(queryChannelDemandReportListReqVo.getRollingVersion()))
        {
            result = channelDemandReportService.queryChannelDemandReportHeadSelect(queryChannelDemandReportListReqVo);
        }

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求提报分组聚合数据列表
     * @param queryChannelDemandReportListReqVo
     * @return ResultInfo<List < QueryChannelDemandReportGroupListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:11
     */
    @PostMapping("queryChannelDemandReportListGroupBy")
    @ResponseBody
    @ApiOperation("查询渠道需求提报分组聚合数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryChannelDemandReportGroupListRspVo>> queryChannelDemandReportListGroupBy(
        @RequestBody QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getGroupColumnList());
        if (Objects.isNull(queryChannelDemandReportListReqVo.getBizDateType()))
        {
            queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        }
        List<QueryChannelDemandReportGroupListRspVo> result = Collections.EMPTY_LIST;
        if (StringUtils.isNotBlank(queryChannelDemandReportListReqVo.getRollingVersion()))
        {
            result = channelDemandReportService.queryChannelDemandReportListGroupBy(queryChannelDemandReportListReqVo);
        }

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询渠道需求提报明细数据列表
     * @param condition
     * @return ResultInfo<PageInfo < QueryChannelDemandReportListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("queryChannelDemandReportDataPage")
    @ResponseBody
    @ApiOperation("分页查询渠道需求提报明细数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<QueryChannelDemandReportListRspVo>> queryChannelDemandReportDataPage(
        @RequestBody PageCondition<QueryChannelDemandReportListReqVo> condition) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());
        if (Objects.isNull(condition.getCondition().getBizDateType()))
        {
            condition.getCondition().setBizDateType(BizDateTypeEnum.WEEK);
        }
        PageInfo<QueryChannelDemandReportListRspVo> result = null;
        if (StringUtils.isNotBlank(condition.getCondition().getRollingVersion()))
        {
            result = channelDemandReportService.queryChannelDemandReportDataPage(condition);
        }
        else
        {
            result = new PageInfo<>();
        }

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求提报数据汇总
     * @param condition
     * @return ResultInfo<Map < String, Double>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("queryChannelDemandReportSummary")
    @ResponseBody
    @ApiOperation("查询渠道需求提报数据汇总")
    @RequiresPermissions(value = {})
    public ResultInfo<Map<String, Double>> queryChannelDemandReportSummary(@RequestBody QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo);
        if (Objects.isNull(queryChannelDemandReportListReqVo.getBizDateType()))
        {
            queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        }

        Map<String, Double> result = Collections.EMPTY_MAP;
        if (StringUtils.isNotBlank(queryChannelDemandReportListReqVo.getRollingVersion()))
        {
            result = channelDemandReportService.queryChannelDemandReportSummary(queryChannelDemandReportListReqVo);
        }
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 修改渠道需求提报版本锁定状态
     * @param channelDemandReportVersionDto
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 14:32
     */
    @PostMapping("updateChannelDemandReportVersionLock")
    @ResponseBody
    @ApiOperation("修改渠道需求提报版本锁定状态")
    @RequiresPermissions(value = {})
    public ResultInfo<?> updateChannelDemandReportVersionLock(@RequestBody ChannelDemandReportVersionDto channelDemandReportVersionDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(channelDemandReportVersionDto);
        ValidateUtil.checkIsNotEmpty(channelDemandReportVersionDto.getRollingVersion());
        ValidateUtil.checkIsNotEmpty(channelDemandReportVersionDto.getIsLocked());
        ValidateUtil.checkNumRange(channelDemandReportVersionDto.getIsLocked(), 0, 1);

        channelDemandReportService.updateChannelDemandReportVersionLock(channelDemandReportVersionDto);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询已提报的渠道编号列表
     * @param queryChannelDemandReportedChannelIdListReqVo
     * @return ResultInfo<Set < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月31日 17:48
     */
    @PostMapping("queryChannelDemandReportedChannelIdList")
    @ResponseBody
    @ApiOperation("查询已提报的渠道编号列表")
    @RequiresPermissions(value = {})
    public ResultInfo<Set<String>> queryChannelDemandReportedChannelIdList(
        @RequestBody QueryChannelDemandReportedChannelIdListReqVo queryChannelDemandReportedChannelIdListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportedChannelIdListReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportedChannelIdListReqVo.getRollingVersion());

        Set<String> result = channelDemandReportService.queryChannelDemandReportedChannelIdList(queryChannelDemandReportedChannelIdListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 修改渠道需求提报数据
     * @param updateChannelDemandReportVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月31日 17:49
     */
    @PostMapping("updateChannelDemandReportData")
    @ResponseBody
    @ApiOperation("修改渠道需求提报数据")
    @RequiresPermissions(value = {})
    public ResultInfo<?> updateChannelDemandReportData(@RequestBody UpdateChannelDemandReportVo updateChannelDemandReportVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(updateChannelDemandReportVo);
        ValidateUtil.checkIsNotEmpty(updateChannelDemandReportVo.getChannelDemandReportDataVoList());
        ValidateUtil.checkIsNotEmpty(updateChannelDemandReportVo.getRollingVersion());

        channelDemandReportService.updateChannelDemandReportData(updateChannelDemandReportVo);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询渠道需求提报数据修改历史
     * @param queryChannelDemandReportListReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月31日 17:49
     */
    @PostMapping("queryChannelDemandReportHistoryList")
    @ResponseBody
    @ApiOperation("查询渠道需求提报数据修改历史")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryChannelDemandReportHistoryRspVo>> queryChannelDemandReportHistoryList(
        @RequestBody QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getRollingVersion());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getSkuCodes());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getLv1CategoryCodes());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getLv2CategoryCodes());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getLv3CategoryCodes());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getLv1ChannelCodes());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getLv2ChannelCodes());
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getLv3ChannelCodes());

        // 查询修改渠道需求提报历史记录列表
        List<QueryChannelDemandReportHistoryRspVo> result = channelDemandReportService.queryChannelDemandReportHistoryList(queryChannelDemandReportListReqVo);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道需求提报版本最后一天日期
     * @param queryChannelDemandReportListReqVo
     * @return
     * <AUTHOR>
     * @date 2024年01月12日 15:19
     */
    @PostMapping("queryChannelDemandReportLastDate")
    @ResponseBody
    @ApiOperation("查询渠道需求提报版本最后一天日期")
    @RequiresPermissions(value = {})
    public ResultInfo<String> queryChannelDemandReportLastDate(@RequestBody QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelDemandReportListReqVo.getRollingVersion());

        String result = channelDemandReportService.queryChannelDemandReportLastDate(queryChannelDemandReportListReqVo);
        return ResultInfo.success(result);
    }

//    @PostMapping("/task/test/sdk")
//    public Boolean test(@RequestBody List<ChannelDemandReportDto> dtos){
//        return repository.saveBatch(dtos);
//    }


//    @GetMapping("syncChannelDemandPlanDataTest")
//    @RequiresPermissions(value = {})
//    public ResultInfo<?> syncChannelDemandPlanDataTest(@RequestParam("demandPlanCode") String demandPlanCode, @RequestParam("versionId") String versionId)
//        throws Exception
//    {
//        ConfirmChannelDemandPlanSubPlanGroupVo confirmChannelDemandPlanSubPlanGroupVo = new ConfirmChannelDemandPlanSubPlanGroupVo();
//        confirmChannelDemandPlanSubPlanGroupVo.setDemandPlanCode(demandPlanCode);
//        confirmChannelDemandPlanSubPlanGroupVo.setVersionId(versionId);
//        asyncChannelDemandPlanService.syncChannelDemandPlanData(confirmChannelDemandPlanSubPlanGroupVo);
//        return ResultInfo.success();
//    }
}
