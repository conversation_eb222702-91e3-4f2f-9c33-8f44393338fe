package cn.aliyun.ryytn.modules.demand.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.DoubleSummaryStatistics;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.aliyun.ryytn.modules.demand.dataqdao.DataqDeliveryOrderRateDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.*;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.aliyun.brain.dataindustry.microapp.MicroAppTaskInstanceOutputVO;
import com.aliyun.brain.dataindustry.microapp.request.ApiRunMicroAppRequest;
import com.aliyun.dataq.dataindustry.DataIndustrySpringServiceContext;
import com.aliyun.dataq.dataindustry.config.Header;
import com.aliyun.dataq.dataindustry.service.MicroAppService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.constants.SchedulerConstant;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.dict.DictUtils;
import cn.aliyun.ryytn.common.utils.mybatis.MybatisUtils;
import cn.aliyun.ryytn.common.utils.page.PageUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.AsyncChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.api.ChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandPlanService;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandReportService;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanPeriodEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import cn.aliyun.ryytn.modules.demand.dao.ChannelDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.dao.SkuLockDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemanPlanHistoryDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto;
import cn.aliyun.ryytn.modules.demand.task.AddChannelDemandPlanVersionTaskServiceImpl;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.dataqdao.DataqFreightPlanDao;
import cn.aliyun.ryytn.modules.scheduler.api.SchedulerService;
import cn.aliyun.ryytn.modules.scheduler.dao.SchedulerDao;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import cn.aliyun.ryytn.modules.system.api.ChannelService;
import cn.aliyun.ryytn.modules.system.api.ProductCategoryService;
import cn.aliyun.ryytn.modules.system.api.ProductService;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;
import cn.aliyun.ryytn.modules.system.entity.dto.ProductCategoryDto;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWeekListReqVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

/**
 * @Description 需求计划实现
 * <AUTHOR>
 * @date 2023/10/24 10:19
 */
@Slf4j
@Service
public class ChannelDemandPlanServiceImpl implements ChannelDemandPlanService
{
    @Autowired
    private CalendarService calendarService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductCategoryService productCategoryService;

    @Autowired
    private WarehouseDemandPlanService warehouseDemandPlanService;

    @Autowired
    private WarehouseDemandReportService warehouseDemandReportService;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private SchedulerService schedulerService;

    @Autowired
    private ChannelDemandPlanDao channelDemandPlanDao;

    @Autowired
    private DataqChannelDemandPlanDao dataqChannelDemandPlanDao;

    @Autowired
    private SchedulerDao schedulerDao;

    @Autowired
    private SkuLockDao skuLockDao;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private DictUtils dictUtils;

    @Autowired
    private MybatisUtils mybatisUtils;

    @Autowired
    private DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Autowired
    private DataqFreightPlanDao dataqFreightPlanDao;

    @Autowired
    private AsyncChannelDemandPlanService asyncChannelDemandPlanService;

    @Resource(name = "dataIndustryContext")
    private DataIndustrySpringServiceContext dataIndustryContext;

    @Autowired
    private DataqDeliveryOrderRateDao dataqDeliveryOrderRateDao;

    @Value("${dataq.scheduler.userId}")
    private String userId;

    @Value("${dataq.scheduler.tenantCode}")
    private String tenantCode;

    @Value("${dataq.scheduler.workspaceCode}")
    private String workspaceCode;

    private static final String MONTH_CRON_TEMPLATE = "0 0 0 {} * ?";

    private static final String WEEK_CRON_TEMPLATE = "0 0 0 ? * {}";

    private static final String PLANDIMENSIONTYPE_ALL_NAME = "全部品类";

    private static final String SUBJECTDIMENSIONTYPE_ALL_NAME = "全部渠道";

    /**
     * 计划数据状态：已提交，页面已确认的数据
     */
    private static final Integer STATUS_SUBMITED = 1;

    /**
     * 计划数据状态：待试算，刚生成滚动版本，没有在页面修改过的数据
     */
    private static final Integer STATUS_TRAIL = -1;

    /**
     * 计划数据状态：生成中，此状态数据还未完成生成
     */
    private static final Integer STATUS_INITING = -2;

    /**
     * 计划数据状态：待提交，在页面修改过的数据（认为是已经试算调整过的，但是没有确认）
     */
    private static final Integer STATUS_NOSUBMIT = 0;

    private static final String TOTAL_KEY = "Total{}";

    /**
     *
     * @Description 查询渠道列表
     * @param subjectDimensionType
     * @return Set<ChannelDto>
     * <AUTHOR>
     * @date 2023年11月06日 17:18
     */
    @Override
    public Set<ChannelDto> queryChannelList(SubjectDimensionTypeEnum subjectDimensionType) throws Exception
    {
        // 查询渠道列表
        List<ChannelDto> channelDtoList = channelService.queryChannelList();

        // 如果是一级渠道，二级/三级渠道赋值为null（阿里后续根据参数是否为空拼接过滤条件）
        if (SubjectDimensionTypeEnum.lv1Channel.equals(subjectDimensionType))
        {
            channelDtoList.forEach(item -> {
                item.setLv2ChannelCode(null);
                item.setLv2ChannelName(null);
                item.setLv3ChannelCode(null);
                item.setLv3ChannelName(null);
            });
        }
        // 如果是二级渠道，三级渠道赋值为null（阿里后续根据参数是否为空拼接过滤条件）
        else if (SubjectDimensionTypeEnum.lv2Channel.equals(subjectDimensionType))
        {
            channelDtoList.forEach(item -> {
                item.setLv3ChannelCode(null);
                item.setLv3ChannelName(null);
            });
        }

        // 去重
        Set<ChannelDto> result = new HashSet<>(channelDtoList);

        return result;
    }

    /**
     *
     * @Description 查询产品品类列表
     * @param planDimensionType
     * @return Set<ProductCategoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月06日 17:29
     */
    @Override
    public Set<ProductCategoryDto> queryProductCategoryList(PlanDimensionTypeEnum planDimensionType) throws Exception
    {
        // 查询产品品类列表
        List<ProductCategoryDto> productCategoryList = productCategoryService.queryProductCategoryList();
        // 如果是一级品类，二级/三级品类赋值为null（阿里后续根据参数是否为空拼接过滤条件）
        if (PlanDimensionTypeEnum.lv1Category.equals(planDimensionType))
        {
            productCategoryList.forEach(item -> {
                item.setLv2CategoryCode(null);
                item.setLv2CategoryName(null);
                item.setLv3CategoryCode(null);
                item.setLv3CategoryName(null);
            });
        }
        // 如果是二级品类，三级品类赋值为null（阿里后续根据参数是否为空拼接过滤条件）
        else if (PlanDimensionTypeEnum.lv2Category.equals(planDimensionType))
        {
            productCategoryList.forEach(item -> {
                item.setLv3CategoryCode(null);
                item.setLv3CategoryName(null);
            });
        }

        // 去重
        Set<ProductCategoryDto> result = new HashSet<>(productCategoryList);

        return result;
    }

    /**
     *
     * @Description 查询渠道需求计划品类树
     * @param channelDemandPlanDataParamVo
     * @return List<ProductCategoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月02日 15:34
     */
    @Override
    public Set<ProductCategoryDto> queryChannelDemandPlanCategoryTree(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception
    {
        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "lv1_category_code,lv1_category_name,lv2_category_code,lv2_category_name,lv3_category_code,lv3_category_name,is_modify");
        param.put("order_by", "lv1_category_code,lv2_category_code,lv3_category_code");

        // 查询dataq接口渠道需求计划版本列表
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setVersionId(channelDemandPlanDataParamVo.getVersionId());
        queryChannelDemandPlanVersionListReqVo.setGroupId(channelDemandPlanDataParamVo.getGroupId());
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }

        // 解析dataq响应，所有版本拆分子计划的平铺数据，还需要对此数据根据子计划清单组编号过滤
        List<ProductCategoryDto> dataList = jsonArray.toJavaList(ProductCategoryDto.class);

        // 查询修改历史记录品类列表
        channelDemandPlanDataParamVo.setLv3CategoryCode(
            dataList.stream().map(ProductCategoryDto::getLv3CategoryCode).distinct().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
        List<ChannelDemanPlanHistoryDto> channelDemandPlanHistoryList = channelDemandPlanDao.queryChannelDemandPlanHistoryList(channelDemandPlanDataParamVo);
        Set<String> hasHistoryLv3CategoryCodeList =
            channelDemandPlanHistoryList.stream().map(ChannelDemanPlanHistoryDto::getLv3CategoryCode).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(hasHistoryLv3CategoryCodeList))
        {
            dataList = dataList.stream().map(item -> {
                item.setChecked(hasHistoryLv3CategoryCodeList.contains(item.getLv3CategoryCode()));
                return item;
            }).collect(Collectors.toList());
        }

        return productCategoryService.createCategoryTree(dataList);
    }

    /**
     *
     * @Description 查询渠道需求计划列表
     * @param queryChannelDemandPlanListReqVo
     * @return List<QueryChannelDemandPlanListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月15日 11:02
     */
    @Override
    public List<QueryChannelDemandPlanListRspVo> queryChannelDemandPlanList(QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo) throws Exception
    {
        List<QueryChannelDemandPlanListRspVo> result = Collections.EMPTY_LIST;

        // 阿里接口入参暂不支持deleted字段
        queryChannelDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.order);
        queryChannelDemandPlanListReqVo.setDeleted(false);

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandPlanListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            result = jsonArray.toJavaList(QueryChannelDemandPlanListRspVo.class);
            // 过滤不展示已删除数据，根据创建时间倒序排序(阿里创建时间为空，未写入，根据自增主键倒序排序)
            result = result.stream().filter(item -> {
                return !item.getDeleted();
            }).sorted(Comparator.comparing(QueryChannelDemandPlanListRspVo::getId).reversed()).collect(Collectors.toList());

            // 计算开始时间
            for (QueryChannelDemandPlanListRspVo item : result)
            {
                String startDate =
                    DateUtils.formatTime(DateUtils.addDays(DateUtils.parseDate(item.getStartDate(), DateUtils.YMD_DASH), item.getExecutionDateNum() - 1),
                        DateUtils.YMD_DASH);

                item.setStartDate(startDate);
            }
        }

        return result;
    }

    /**
     *
     * @Description 查询渠道需求计划版本列表
     * @param condition
     * @return PageInfo<QueryOrderDemandPlanListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月08日 14:16
     */
    @Override
    public PageInfo<ChannelDemandPlanVersionVo> queryChannelDemandPlanVersionList(PageCondition<String> condition) throws Exception
    {
        String demandPlanCode = condition.getCondition();

        StopWatch queryLock = new StopWatch("queryChannelDemandPlanVersionList");
        // 尝试从redis中获取当前页数据，获取到直接return
        Object currPageDataCache =  redisUtils.get(getCacheKey(demandPlanCode, condition.getPageNum()));
        if(currPageDataCache != null)
        {
            return JSONObject.parseObject(currPageDataCache.toString(), PageInfo.class);
        }

        // 查询dataq接口渠道需求计划列表
        QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo = new QueryChannelDemandPlanListReqVo();
        queryChannelDemandPlanListReqVo.setDemandPlanCode(demandPlanCode);
        List<QueryChannelDemandPlanListRspVo> planList = queryChannelDemandPlanList(queryChannelDemandPlanListReqVo);
        if (CollectionUtils.isEmpty(planList))
        {
            return null;
        }
        QueryChannelDemandPlanListRspVo channelDemandPlan = planList.get(0);

        // 查询dataq接口渠道需求计划版本列表
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(demandPlanCode);
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);

        // 特殊参数，分页查询版本号，再根据版本号查询具体数据，最后内存分页
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("aggregation", "label,max,label;plan_date,min,beginDate;plan_date,max,endDate;gmt_create,min,gmt_create");
        paramMap.put("group_by", "version_id");
        paramMap.put("order_by", "gmt_create desc");
        // 返回总数量
        paramMap.put("fetch_all", true);
        log.info("one param:{}",JSONObject.toJSONString(queryChannelDemandPlanVersionListReqVo));
        queryLock.start("oneQuery");
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, paramMap, queryChannelDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }

        // 解析dataq响应，获取对应分页条件的版本号
        List<ChannelDemandPlanVersionVo> versionList = jsonArray.toJavaList(ChannelDemandPlanVersionVo.class);
        queryLock.stop();
        // 封装版本条件，只查询分页条件对应页的版本数据，再进行聚合
        paramMap.clear();
        paramMap.put("aggregation", "*,count,num");
        paramMap.put("group_by", "version_id,\"status\"");

        String versionIds =
            versionList.stream().map(ChannelDemandPlanVersionVo::getVersionId).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        queryChannelDemandPlanVersionListReqVo.setVersionIds(versionIds);
        log.info("twoQuery param:{}",JSONObject.toJSONString(queryChannelDemandPlanVersionListReqVo));
        queryLock.start("twoQuery");
        path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
        dataqResult = dataqService.invoke(HttpMethod.POST, path, null, paramMap, queryChannelDemandPlanVersionListReqVo);
        jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }

        // 解析dataq响应，所有版本按状态聚合，查询各状态的数量
        List<ChannelDemandPlanVersionVo> versionStatusList = jsonArray.toJavaList(ChannelDemandPlanVersionVo.class);
        queryLock.stop();
        Set<String> initingVersionSet =
            versionStatusList.stream().filter(item -> STATUS_INITING.equals(item.getStatus())).map(ChannelDemandPlanVersionVo::getVersionId)
                .collect(Collectors.toSet());

        versionList = versionList.stream().filter(item -> !initingVersionSet.contains(item.getVersionId())).collect(Collectors.toList());
        int total = CollectionUtils.size(versionList);
        queryLock.start("executepage");
        for (ChannelDemandPlanVersionVo channelDemandPlanVersion : versionList)
        {
            String beginDate = channelDemandPlanVersion.getBeginDate();
            String endDate = channelDemandPlanVersion.getEndDate();
            StringBuilder rangeSb = new StringBuilder();
            DataqWeek beginWeek = (DataqWeek) redisUtils.hget(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, beginDate);
            DataqWeek endWeek = (DataqWeek) redisUtils.hget(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, endDate);
            if (Objects.isNull(beginWeek) || Objects.isNull(endWeek))
            {
                rangeSb.append(beginDate).append(StringUtils.TO_SEPARATOR).append(endDate);
            }
            else
            {
                if (PlanPeriodEnum.PER_MONTH.equals(channelDemandPlan.getPlanPeriod()))
                {
                    rangeSb.append(beginWeek.getFsclYearMonth()).append(StringUtils.TO_SEPARATOR).append(endWeek.getFsclYearMonth());
                }
                else
                {
                    rangeSb.append(beginWeek.getMonthOfFsclYear()).append(StringUtils.DATE_SEPARATOR).append(StringUtils.WEEK_PREFIX_UPPER)
                        .append(beginWeek.getWeekOfFsclMonth()).append(StringUtils.TO_SEPARATOR).append(endWeek.getMonthOfFsclYear())
                        .append(StringUtils.DATE_SEPARATOR).append(StringUtils.WEEK_PREFIX_UPPER).append(endWeek.getWeekOfFsclMonth());
                }
            }

            // 过滤版本号下，状态不是已提交的数量
            long count = versionStatusList.stream().filter(item -> {
                return item.getVersionId().equals(channelDemandPlanVersion.getVersionId()) && !STATUS_SUBMITED.equals(item.getStatus());
            }).count();

            channelDemandPlanVersion.setDemandPlanCode(demandPlanCode);
            channelDemandPlanVersion.setVersionRange(rangeSb.toString());
            if (count == 0)
            {
                channelDemandPlanVersion.setStatus(STATUS_SUBMITED);
            }
            else
            {
                channelDemandPlanVersion.setStatus(STATUS_TRAIL);
            }

            // 翻译标签字典
            if (StringUtils.isNotBlank(channelDemandPlanVersion.getLabel()))
            {
                channelDemandPlanVersion.setLabel(dictUtils.codeToName("DEMAND_PLAN_LABEL", channelDemandPlanVersion.getLabel()));
            }
        }

        // 手动分页，阿里dataq是平铺数据，此处必须代码在内存中分页
        PageInfo<ChannelDemandPlanVersionVo> result = PageUtils.init(versionList, condition.getPageNum(), condition.getPageSize(), total);
        queryLock.stop();
        log.info("queryChannelDemandPlanVersionList cost:{}s ",queryLock.getTotalTimeSeconds());
        log.info("queryChannelDemandPlanVersionList cost:{}",queryLock.prettyPrint());
        // 缓存列表当前页数据，key示例： CALENDAR_FAST_CACHE:demandPlanCode:PAGE_NUM_1
        redisUtils.set(getCacheKey(demandPlanCode, condition.getPageNum()), JSONObject.toJSONString(result),60*5);
        return result;
    }

    /**
     * 拼接列表优化缓存的key
     * @return
     */
    private String getCacheKey(String demandPlanCode, Integer pageNum){
        return CommonConstants.CALENDAR_TABLE_DATA_PRE_KEY + demandPlanCode + CommonConstants.CALENDAR_TABLE_DATA_PAGE_KEY + pageNum;
    }

    /**
     *
     * @Description 查询渠道需求子计划清单组列表
     * @param channelDemandSubPlanGroupVo
     * @return List<ChannelDemandSubPlanGroupVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 15:29
     */
    @Override
    public List<ChannelDemandSubPlanGroupVo> queryChannelDemandSubPlanGroupList(ChannelDemandSubPlanGroupVo channelDemandSubPlanGroupVo)
        throws Exception
    {
        String demandPlanCode = channelDemandSubPlanGroupVo.getDemandPlanCode();
        String versionId = channelDemandSubPlanGroupVo.getVersionId();

        // 查询dataq接口渠道需求计划列表
        QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo = new QueryChannelDemandPlanListReqVo();
        queryChannelDemandPlanListReqVo.setDemandPlanCode(demandPlanCode);
        List<QueryChannelDemandPlanListRspVo> planList = queryChannelDemandPlanList(queryChannelDemandPlanListReqVo);
        if (CollectionUtils.isEmpty(planList))
        {
            return null;
        }
        QueryChannelDemandPlanListRspVo channelDemandPlan = planList.get(0);

        // 查询渠道需求计划版本数据，根据分组编号分组，先查询所有子计划组和子计划组的状态
        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "group_id,\"status\",gmt_modify");
        param.put("order_by", "gmt_modify desc");

        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(demandPlanCode);
        queryChannelDemandPlanVersionListReqVo.setVersionId(versionId);
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
        log.info("DATAQ_API_DEMAND_CHANNEL_PLAN_LIST send1 param :{},queryChannelDemandPlanVersionListReqVo:{}", JSONObject.toJSONString(param),JSONObject.toJSONString(queryChannelDemandPlanVersionListReqVo));
        StopWatch stopWatch = new StopWatch("DATAQ_API_DEMAND_CHANNEL_PLAN_LIST_InterfacePlan");
        stopWatch.start("1start callInterface");
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandPlanVersionListReqVo);
        stopWatch.stop();
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }

        // 解析dataq响应，所有版本拆分子计划的平铺数据，还需要对此数据根据版本、子计划清单组编号分别聚合
        List<QueryChannelDemandPlanVersionListRspVo> dataList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);
        Map<Long, QueryChannelDemandPlanVersionListRspVo> groupMap =
            dataList.stream().collect(Collectors.toMap(QueryChannelDemandPlanVersionListRspVo::getGroupId, Function.identity(), (key1, key2) -> key2));

        param.clear();
        dataList.clear();
        List<ChannelDemandSubPlanGroupVo> subPlanGroupList =  Collections.synchronizedList(new ArrayList<ChannelDemandSubPlanGroupVo>(groupMap.size()));
//        List<ChannelDemandSubPlanGroupVo> subPlanGroupList = new ArrayList<>(groupMap.size());
        // 再次查询渠道需求计划版本数据，根据品类、渠道分组
        param.put("group_by",
            "lv1_category_code,lv1_category_name,lv2_category_code,lv2_category_name,lv3_category_code,lv3_category_name,lv1_channel_code,lv1_channel_name,lv2_channel_code,lv2_channel_name");
        ForkJoinPool customPool = new ForkJoinPool(8);
        customPool.submit(() -> {
            groupMap.entrySet().parallelStream().forEach(entry-> {
                long groupId = entry.getValue().getGroupId();
                QueryChannelDemandPlanVersionListRspVo data = entry.getValue();
                int status = Objects.isNull(data.getStatus()) ? STATUS_TRAIL : data.getStatus();
                String gmtModify = data.getGmtModify();
                QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVoSub = new QueryChannelDemandPlanVersionListReqVo();
                queryChannelDemandPlanVersionListReqVoSub.setDemandPlanCode(queryChannelDemandPlanVersionListReqVo.getDemandPlanCode());
                queryChannelDemandPlanVersionListReqVoSub.setVersionId(queryChannelDemandPlanVersionListReqVo.getVersionId());
                queryChannelDemandPlanVersionListReqVoSub.setIsModify(0);
                queryChannelDemandPlanVersionListReqVoSub.setDeleted(0);
                queryChannelDemandPlanVersionListReqVoSub.setGroupId(groupId);
                log.info("DATAQ_API_DEMAND_CHANNEL_PLAN_LIST send2 param :{},queryChannelDemandPlanVersionListReqVo:{}",JSONObject.toJSONString(param),JSONObject.toJSONString(queryChannelDemandPlanVersionListReqVoSub));
//                stopWatch.start("2start callInterface");
                long start0 = System.currentTimeMillis();
                DataqResult<?> dataqResultSub = null;
                try {
                    dataqResultSub = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandPlanVersionListReqVoSub);
                } catch (Exception e) {
                    log.info("request dataQ error.",e);
                }
                long start1 = System.currentTimeMillis();
                log.info("request cost times s:{}",(start1-start0)/1000);
//                stopWatch.stop();
                JSONArray jsonArraySub = (JSONArray) dataqResultSub.getData();
                List<QueryChannelDemandPlanVersionListRspVo> dataListSub = jsonArraySub.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);

                // 根据计划对象范围，封装子计划清单组列表中的计划对象列数据
                String planObject = null;

                // 一级品类，展示所有子计划涉及的一级品类名称，逗号分隔
                if (PlanDimensionTypeEnum.lv1Category.equals(channelDemandPlan.getPlanDimensionType()))
                {
                    planObject = dataListSub.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv1CategoryName).distinct()
                            .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
                }
                // 二级品类，展示所有子计划涉及的一级品类名称-二级品类名称，逗号分隔
                else if (PlanDimensionTypeEnum.lv2Category.equals(channelDemandPlan.getPlanDimensionType()))
                {
                    planObject = dataListSub.stream().map(item -> {
                        return new StringBuilder().append(item.getLv1CategoryName()).append(StringUtils.DATE_SEPARATOR).append(item.getLv2CategoryName())
                                .toString();
                    }).distinct().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
                }
                // 三级品类，展示所有子计划涉及的一级品类名称-二级品类名称-三级品类名称，逗号分隔
                else if (PlanDimensionTypeEnum.lv3Category.equals(channelDemandPlan.getPlanDimensionType()))
                {
                    planObject = dataListSub.stream().map(item -> {
                        return new StringBuilder().append(item.getLv1CategoryName()).append(StringUtils.DATE_SEPARATOR).append(item.getLv2CategoryName())
                                .append(StringUtils.DATE_SEPARATOR).append(item.getLv3CategoryName()).toString();
                    }).distinct().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
                }

                // 根据计划主体范围，封装子计划清单组列表中的计划主体（渠道）列数据
                String planSubject = null;
                // 全部渠道，展示固定字符
                // 一级渠道，展示所有子计划涉及的一级渠道名称，逗号分隔
                if (SubjectDimensionTypeEnum.lv1Channel.equals(channelDemandPlan.getSubjectDimensionType()))
                {
                    planSubject = dataListSub.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv1ChannelName).distinct()
                            .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
                }
                // 二级渠道，展示所有子计划涉及的一级渠道名称-二级渠道名称，逗号分隔
                else if (SubjectDimensionTypeEnum.lv2Channel.equals(channelDemandPlan.getSubjectDimensionType()))
                {
                    planSubject = dataListSub.stream().map(item -> {
                        return new StringBuilder().append(item.getLv1ChannelName()).append(StringUtils.DATE_SEPARATOR).append(item.getLv2ChannelName())
                                .toString();
                    }).distinct().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
                }

                ChannelDemandSubPlanGroupVo subPlanGroup = new ChannelDemandSubPlanGroupVo();
                subPlanGroup.setDemandPlanCode(demandPlanCode);
                subPlanGroup.setVersionId(versionId);
                subPlanGroup.setGroupId(groupId);
                subPlanGroup.setPlanObject(planObject);
                subPlanGroup.setPlanSubject(planSubject);
                subPlanGroup.setStatus(status);
                subPlanGroup.setSubmitTime(gmtModify);

                subPlanGroupList.add(subPlanGroup);
            });
        }).get();
//        for (Map.Entry<Long, QueryChannelDemandPlanVersionListRspVo> entry : groupMap.entrySet())
//        {
//            long groupId = entry.getKey();
//            QueryChannelDemandPlanVersionListRspVo data = entry.getValue();
//            int status = Objects.isNull(data.getStatus()) ? STATUS_TRAIL : data.getStatus();
//            String gmtModify = data.getGmtModify();
//
//            queryChannelDemandPlanVersionListReqVo.setGroupId(groupId);
//            log.info("DATAQ_API_DEMAND_CHANNEL_PLAN_LIST send2 param :{},queryChannelDemandPlanVersionListReqVo:{}",JSONObject.toJSONString(param),JSONObject.toJSONString(queryChannelDemandPlanVersionListReqVo));
//            stopWatch.start("2start callInterface");
//            dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandPlanVersionListReqVo);
//            stopWatch.stop();
//            jsonArray = (JSONArray) dataqResult.getData();
//            dataList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);
//
//            // 根据计划对象范围，封装子计划清单组列表中的计划对象列数据
//            String planObject = null;
//
//            // 一级品类，展示所有子计划涉及的一级品类名称，逗号分隔
//            if (PlanDimensionTypeEnum.lv1Category.equals(channelDemandPlan.getPlanDimensionType()))
//            {
//                planObject = dataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv1CategoryName).distinct()
//                    .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//            }
//            // 二级品类，展示所有子计划涉及的一级品类名称-二级品类名称，逗号分隔
//            else if (PlanDimensionTypeEnum.lv2Category.equals(channelDemandPlan.getPlanDimensionType()))
//            {
//                planObject = dataList.stream().map(item -> {
//                    return new StringBuilder().append(item.getLv1CategoryName()).append(StringUtils.DATE_SEPARATOR).append(item.getLv2CategoryName())
//                        .toString();
//                }).distinct().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//            }
//            // 三级品类，展示所有子计划涉及的一级品类名称-二级品类名称-三级品类名称，逗号分隔
//            else if (PlanDimensionTypeEnum.lv3Category.equals(channelDemandPlan.getPlanDimensionType()))
//            {
//                planObject = dataList.stream().map(item -> {
//                    return new StringBuilder().append(item.getLv1CategoryName()).append(StringUtils.DATE_SEPARATOR).append(item.getLv2CategoryName())
//                        .append(StringUtils.DATE_SEPARATOR).append(item.getLv3CategoryName()).toString();
//                }).distinct().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//            }
//
//            // 根据计划主体范围，封装子计划清单组列表中的计划主体（渠道）列数据
//            String planSubject = null;
//            // 全部渠道，展示固定字符
//            // 一级渠道，展示所有子计划涉及的一级渠道名称，逗号分隔
//            if (SubjectDimensionTypeEnum.lv1Channel.equals(channelDemandPlan.getSubjectDimensionType()))
//            {
//                planSubject = dataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv1ChannelName).distinct()
//                    .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//            }
//            // 二级渠道，展示所有子计划涉及的一级渠道名称-二级渠道名称，逗号分隔
//            else if (SubjectDimensionTypeEnum.lv2Channel.equals(channelDemandPlan.getSubjectDimensionType()))
//            {
//                planSubject = dataList.stream().map(item -> {
//                    return new StringBuilder().append(item.getLv1ChannelName()).append(StringUtils.DATE_SEPARATOR).append(item.getLv2ChannelName())
//                        .toString();
//                }).distinct().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//            }
//
//            ChannelDemandSubPlanGroupVo subPlanGroup = new ChannelDemandSubPlanGroupVo();
//            subPlanGroup.setDemandPlanCode(demandPlanCode);
//            subPlanGroup.setVersionId(versionId);
//            subPlanGroup.setGroupId(groupId);
//            subPlanGroup.setPlanObject(planObject);
//            subPlanGroup.setPlanSubject(planSubject);
//            subPlanGroup.setStatus(status);
//            subPlanGroup.setSubmitTime(gmtModify);
//
//            subPlanGroupList.add(subPlanGroup);
//        }
        log.info("DATAQ_API_DEMAND_CHANNEL_PLAN_LIST_InterfacePlan cost:{}s ",stopWatch.getTotalTimeSeconds());
        log.info("DATAQ_API_DEMAND_CHANNEL_PLAN_LIST_InterfacePlan cost:{}",stopWatch.prettyPrint());


        return subPlanGroupList;
    }


    /**
     *
     * @Description 查询渠道需求计划数据列表
     * @param channelDemandPlanDataParamVo
     * @return BaseTable<List < QueryChannelDemandPlanVersionListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月16日 15:48
     */
    @Override
    public BaseTable<List<QueryChannelDemandPlanVersionListRspVo>> queryChannelDemandPlanDataList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception
    {
        BaseTable<List<QueryChannelDemandPlanVersionListRspVo>> baseTable = new BaseTable();

        QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo = new QueryChannelDemandPlanListReqVo();
        queryChannelDemandPlanListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        queryChannelDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.order);
        List<QueryChannelDemandPlanListRspVo> planList = queryChannelDemandPlanList(queryChannelDemandPlanListReqVo);
        if (CollectionUtils.isEmpty(planList))
        {
            return baseTable;
        }
        QueryChannelDemandPlanListRspVo channelDemandPlan = planList.get(0);

        // 查询dataq接口渠道需求计划版本列表
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setVersionId(channelDemandPlanDataParamVo.getVersionId());
        queryChannelDemandPlanVersionListReqVo.setGroupId(channelDemandPlanDataParamVo.getGroupId());
        queryChannelDemandPlanVersionListReqVo.setLv1CategoryCode(channelDemandPlanDataParamVo.getLv1CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv2CategoryCode(channelDemandPlanDataParamVo.getLv2CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv3CategoryCode(channelDemandPlanDataParamVo.getLv3CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv1ChannelCode(channelDemandPlanDataParamVo.getLv1ChannelCode());
        queryChannelDemandPlanVersionListReqVo.setLv2ChannelCode(channelDemandPlanDataParamVo.getLv2ChannelCode());
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);

        List<QueryChannelDemandPlanVersionListRspVo> dataList =
            dataqChannelDemandPlanDao.queryChannelDemandPlanVersionDataList(queryChannelDemandPlanVersionListReqVo);

        dataList = dataList.stream().filter(item -> {
            return item.getGroupId().equals(channelDemandPlanDataParamVo.getGroupId());
        }).collect(Collectors.toList());

        // 获取所有周数据
        Set<String> planDateSet = dataList.stream().map(item -> {
            return item.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
        }).collect(Collectors.toSet());

        // 获取指定周的周对象
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateSet);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(),
            (key1, key2) -> key1));
        // 封装动态字段列表，格式：yyyy-MM-dd|MM月Ww(dd-dd)，yyyy-MM-dd部分还需用于封装平铺数据
        List<String> keyList = dataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getPlanDate).distinct().sorted((o1, o2) -> {
            return o1.compareTo(o2);
        }).map(item -> {
            String key = item.replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
            DataqWeek dataqWeek = dataqWeekMap.get(key);
            String head = new StringBuilder().append(item).append(StringUtils.STAND_LINE_SEPARATOR).append(dataqWeek.getWeekLabel()).toString();
            return head;
        }).collect(Collectors.toList());

        // 动态表头，格式：MM月Ww(dd-dd)
        List<String> headList = keyList.stream().map(item -> {
            return StringUtils.substringAfterLast(item, StringUtils.STAND_LINE_SEPARATOR);
        }).collect(Collectors.toList());

        // 查询包含当前时间和本次数据的产品锁定期
        List<String> skuCodeList = dataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getSkuCode).distinct().collect(Collectors.toList());
        List<String> lv2ChannelCodeList =
            dataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv2ChannelCode).distinct().collect(Collectors.toList());
        SkuLockVo skuLockVo = new SkuLockVo();
        skuLockVo.setSkuCodes(skuCodeList);
        skuLockVo.setLv2ChannelCodes(lv2ChannelCodeList);
        String currentDay = DateUtils.getDate(DateUtils.YMD);
        skuLockVo.setLockStartDate(currentDay);
        skuLockVo.setLockEndDate(currentDay);
        List<SkuLockVo> skuLockList = skuLockDao.queryLockList(skuLockVo);
        Map<String, List<SkuLockVo>> skuLockMap = Collections.EMPTY_MAP;
        if (CollectionUtils.isNotEmpty(skuLockList))
        {
            skuLockMap = skuLockList.stream().collect(Collectors.groupingBy(SkuLockVo::getSkuChannelCode));
        }

        // 过滤子计划清单组
        Map<QueryChannelDemandPlanVersionListRspVo, List<QueryChannelDemandPlanVersionListRspVo>> map =
            dataList.stream().collect(Collectors.groupingBy(Function.identity()));

        String currentDate = DateUtils.getDate(DateUtils.YMD);
        String currentMonth = DateUtils.getDate(DateUtils.YM);


        String skuCodes = skuCodeList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        String lv2ChannelCodes = dataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv2ChannelCode).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        List<String> historyWeekStartList = new ArrayList<>();
        dataqWeekMap.forEach((k,v)->{
            if (StringUtils.compare(v.getFsclWeekEnd(), currentDate) < 0)
            {
                historyWeekStartList.add(v.getFsclWeekStart());
            }
        });
        ChannelDemandRealityDataVo queryChannelDemandRealityDataVo = new ChannelDemandRealityDataVo();
        queryChannelDemandRealityDataVo.setDimComb("LV2_CHANNEL_CODE+SKU+LV3_CATEGORY_CODE+WEEK");
        queryChannelDemandRealityDataVo.setBizDates(historyWeekStartList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
        queryChannelDemandRealityDataVo.setSkuCodes(skuCodes);
        queryChannelDemandRealityDataVo.setLv2ChannelCodes(lv2ChannelCodes);

        List<ChannelDemandRealityDataVo> historyRealityDataList =dataqDeliveryOrderRateDao.queryHistoryRealityDeliveryOrderByWeek(queryChannelDemandRealityDataVo);
        Map<ChannelDemandRealityDataVo,ChannelDemandRealityDataVo> realityDataVoMap =  historyRealityDataList.stream().collect(Collectors.toMap(Function.identity(),v->v));


        List<QueryChannelDemandPlanVersionListRspVo> list = new ArrayList<>(map.size());
        for (Map.Entry<QueryChannelDemandPlanVersionListRspVo, List<QueryChannelDemandPlanVersionListRspVo>> entry : map.entrySet())
        {
            QueryChannelDemandPlanVersionListRspVo data = entry.getKey();
            if (StringUtils.isEmpty(data.getLv2ChannelCode()))
            {
                data.setLv2ChannelCode(StringUtils.EMPTY);
            }
            if (StringUtils.isEmpty(data.getLv3CategoryCode()))
            {
                data.setLv3CategoryCode(StringUtils.EMPTY);
            }
            String skuChannelCode = new StringBuilder(data.getSkuCode()).append(StringUtils.DATE_SEPARATOR).append(data.getLv2ChannelCode()).toString();
            List<SkuLockVo> lockDateList = skuLockMap.get(skuChannelCode);
            List<QueryChannelDemandPlanVersionListRspVo> planValueList = entry.getValue();
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (String key : keyList)
            {
                String planDate = StringUtils.substringBefore(key, StringUtils.STAND_LINE_SEPARATOR);
                String datakey = StringUtils.substringAfterLast(key, StringUtils.STAND_LINE_SEPARATOR);
                PlanValue planValue = planValueList.stream().filter(item -> {
                    return planDate.equals(item.getPlanDate());
                }).map(item -> {
                    PlanValue temp = new PlanValue();
                    temp.setId(item.getId());
                    temp.setPlanDate(planDate);
                    temp.setPlanValue(item.getPlanValue());
                    temp.setPlanRemark(item.getPlanRemark());
                    temp.setLockFlag(false);
                    String date = StringUtils.replace(planDate, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                    String fsckWeekEnd = dataqWeekMap.get(date).getFsclWeekEnd();
                    String month = dataqWeekMap.get(date).getFsclYearMonth();
                    if (CollectionUtils.isNotEmpty(lockDateList))
                    {
                        long num = lockDateList.stream().filter(lockDate -> {
                            return StringUtils.compare(lockDate.getLockEndDate(), date) >= 0 &&
                                StringUtils.compare(date, lockDate.getLockStartDate()) >= 0;
                        }).count();
                        if (num > 0)
                        {
                            temp.setLockFlag(true);
                        }
                    }
                    // 周粒度计划，当前时间超过周末，自然锁定期，锁定数据不可修改
                    if (PlanPeriodEnum.PER_WEEK.equals(channelDemandPlan.getPlanPeriod()) && StringUtils.compare(currentDate, fsckWeekEnd) > 0)
                    {
                        temp.setLockFlag(true);
                    }
                    // 月粒度计划，当前时间超过月末，自然锁定期，锁定数据不可修改
                    if (PlanPeriodEnum.PER_MONTH.equals(channelDemandPlan.getPlanPeriod()) && StringUtils.compare(currentMonth, month) > 0)
                    {
                        temp.setLockFlag(true);
                    }


                    ChannelDemandRealityDataVo searchChannelDemandRealityDataVo = new ChannelDemandRealityDataVo();
                    searchChannelDemandRealityDataVo.setSkuCode(item.getSkuCode());
                    searchChannelDemandRealityDataVo.setLv2ChannelCode(item.getLv2ChannelCode());
                    searchChannelDemandRealityDataVo.setBizWeekDateStart(item.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY));

                    if(!Objects.isNull(realityDataVoMap.get(searchChannelDemandRealityDataVo))){
                        temp.setWeekActualValue(realityDataVoMap.get(searchChannelDemandRealityDataVo).getRealityOrderNum());
                    }else
                    {
                        temp.setWeekActualValue(0.0);
                    }
                    return temp;
                }).limit(1).collect(Collectors.toList()).get(0);


                data.getDataMap().put(datakey, planValue);
            }
            list.add(data);
        }

        // 根据品类和渠道排序
        list = list.stream().sorted(Comparator.comparing(QueryChannelDemandPlanVersionListRspVo::getLv3CategoryCode)
            .thenComparing(QueryChannelDemandPlanVersionListRspVo::getLv2ChannelCode)).collect(Collectors.toList());

        baseTable.setHeadArray(headList);
        baseTable.setList(list);

        return baseTable;
    }

    /**
     *
     * @Description 查询渠道需求计划数据动态表头列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月20日 14:43
     */
    @Override
    public List<String> queryChannelDemandPlanHeadList(QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo) throws Exception
    {
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);

        List<String> planDateList = dataqChannelDemandPlanDao.queryChannelDemandPlanHeadList(queryChannelDemandPlanVersionListReqVo);
        // 阿里需求计划plan_date格式为yyyy-MM-dd，需要转为yyyyMMdd
        planDateList = planDateList.stream().map(item -> item.replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());

        // 查询日历周缓存
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateList);
        // 封装动态字段列表，格式：MM月Ww(dd-dd)
        List<String> headList =
            objectList.stream().map(item -> (DataqWeek) item).sorted(Comparator.comparing(DataqWeek::getFsclWeekStart)).map(DataqWeek::getWeekLabel)
                .collect(Collectors.toList());

        return headList;
    }

    /**
     *
     * @Description 查询渠道需求计划表头下拉列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionGroupListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @Override
    public List<QueryChannelDemandPlanVersionGroupListRspVo> queryChannelDemandPlanHeadSelect(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo)
        throws Exception
    {
        GroupColumnEnum groupColumnEnum = queryChannelDemandPlanVersionListReqVo.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        queryChannelDemandPlanVersionListReqVo.setGroupColumn(groupColumn);
        queryChannelDemandPlanVersionListReqVo.setSortColumn(sortColumn);
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);

        List<QueryChannelDemandPlanVersionGroupListRspVo> result =
            dataqChannelDemandPlanDao.queryChannelDemandPlanHeadSelect(queryChannelDemandPlanVersionListReqVo);
        return result;
    }

    /**
     *
     * @Description 查询渠道需求计划数据分组聚合列表
     * @param queryChannelDemandPlanVersionListReqVo
     * @return List<QueryChannelDemandPlanVersionGroupListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 11:12
     */
    @Override
    public List<QueryChannelDemandPlanVersionGroupListRspVo> queryChannelDemandPlanGroupList(
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo) throws Exception
    {
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);

        // 分组查询
        List<GroupColumnEnum> groupColumnEnumList = queryChannelDemandPlanVersionListReqVo.getGroupColumnList();
        List<QueryChannelDemandPlanVersionGroupListRspVo> dataList = new ArrayList<>();
        StringBuilder groupColumnSB = new StringBuilder();
        StringBuilder sortColumnSB = new StringBuilder();
        for (GroupColumnEnum groupColumnEnum : groupColumnEnumList)
        {
            String groupColumn = groupColumnSB.append(groupColumnEnum.getColumnName()).toString();
            String sortColumn = sortColumnSB.append(groupColumnEnum.getSortColumn().getColumnName()).toString();
            queryChannelDemandPlanVersionListReqVo.setGroupColumn(groupColumn);
            queryChannelDemandPlanVersionListReqVo.setSortColumn(sortColumn);

            List<QueryChannelDemandPlanVersionGroupListRspVo> list =
                dataqChannelDemandPlanDao.queryChannelDemandPlanDataGroupList(queryChannelDemandPlanVersionListReqVo);
            if (CollectionUtils.isNotEmpty(list))
            {
                dataList.addAll(list);
            }

            groupColumnSB.append(StringUtils.COMMA_SEPARATOR);
            sortColumnSB.append(StringUtils.COMMA_SEPARATOR);
        }

        // 获取所有周数据
        List<String> planDateList = dataqChannelDemandPlanDao.queryChannelDemandPlanHeadList(queryChannelDemandPlanVersionListReqVo);
        planDateList = planDateList.stream().map(item -> StringUtils.replace(item, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());

        // 获取指定周的周对象
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateList);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart, Function.identity(), (key1, key2) -> key1));

        for (QueryChannelDemandPlanVersionGroupListRspVo data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String planDate = StringUtils.replace(planValue.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                DataqWeek dataqWeek = dataqWeekMap.get(planDate);
                data.getDataMap().put(dataqWeek.getWeekLabel(), planValue);
            }
            data.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 分页查询渠道需求计划数据列表
     * @param condition
     * @return PageInfo<QueryChannelDemandPlanVersionGroupListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 15:48
     */
    @Override
    public PageInfo<QueryChannelDemandPlanVersionGroupListRspVo> queryChannelDemandPlanDataPage(PageCondition<QueryChannelDemandPlanVersionListReqVo> condition)
        throws Exception
    {
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = condition.getCondition();
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);

        // dataq接口效率过低，修改为直接读数据库
        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        PageHelper.startPage(pageNum, pageSize);
        List<QueryChannelDemandPlanVersionGroupListRspVo> keyList =
            dataqChannelDemandPlanDao.queryChannelDemandPlanDataKeyList(queryChannelDemandPlanVersionListReqVo);
        PageInfo pageInfo = new PageInfo(keyList);
        if (CollectionUtils.isEmpty(keyList))
        {
            return pageInfo;
        }

        int pageTotal = Integer.parseInt(String.valueOf(pageInfo.getTotal()));

        queryChannelDemandPlanVersionListReqVo.setKeyList(keyList);

        // 获取所有周数据
        List<String> planDateList = dataqChannelDemandPlanDao.queryChannelDemandPlanHeadList(queryChannelDemandPlanVersionListReqVo);
        planDateList = planDateList.stream().map(item -> StringUtils.replace(item, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        List<QueryChannelDemandPlanVersionGroupListRspVo> dataList =
            dataqChannelDemandPlanDao.queryChannelDemandPlanDataJsonList(queryChannelDemandPlanVersionListReqVo);

        String currentDate = DateUtils.getDate(DateUtils.YMD);
        String skuCodes = dataList.stream().map(QueryChannelDemandPlanVersionGroupListRspVo::getSkuCode).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        String lv2ChannelCodes = dataList.stream().map(QueryChannelDemandPlanVersionGroupListRspVo::getLv2ChannelCode).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        List<String> historyWeekStartList = new ArrayList<>();
        dataqWeekMap.forEach((k,v)->{
            if (StringUtils.compare(v.getFsclWeekEnd(), currentDate) < 0)
            {
                historyWeekStartList.add(v.getFsclWeekStart());
            }
        });
        ChannelDemandRealityDataVo queryChannelDemandRealityDataVo = new ChannelDemandRealityDataVo();
        queryChannelDemandRealityDataVo.setDimComb("LV2_CHANNEL_CODE+SKU+LV3_CATEGORY_CODE+WEEK");
        queryChannelDemandRealityDataVo.setBizDates(historyWeekStartList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
        queryChannelDemandRealityDataVo.setSkuCodes(skuCodes);
        queryChannelDemandRealityDataVo.setLv2ChannelCodes(lv2ChannelCodes);

        List<ChannelDemandRealityDataVo> historyRealityDataList =dataqDeliveryOrderRateDao.queryHistoryRealityDeliveryOrderByWeek(queryChannelDemandRealityDataVo);
        Map<ChannelDemandRealityDataVo,ChannelDemandRealityDataVo> realityDataVoMap =  historyRealityDataList.stream().collect(Collectors.toMap(Function.identity(),v->v));


        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (QueryChannelDemandPlanVersionGroupListRspVo data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String planDate = StringUtils.replace(planValue.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                DataqWeek dataqWeek = dataqWeekMap.get(planDate);

                ChannelDemandRealityDataVo searchChannelDemandRealityDataVo = new ChannelDemandRealityDataVo();
                searchChannelDemandRealityDataVo.setSkuCode(data.getSkuCode());
                searchChannelDemandRealityDataVo.setLv2ChannelCode(data.getLv2ChannelCode());
                searchChannelDemandRealityDataVo.setBizWeekDateStart(planDate);

                if(!Objects.isNull(realityDataVoMap.get(searchChannelDemandRealityDataVo))){
                    planValue.setWeekActualValue(realityDataVoMap.get(searchChannelDemandRealityDataVo).getRealityOrderNum());
                }else
                {
                    planValue.setWeekActualValue(0.0);
                }

                planValue.setLockFlag(StringUtils.compare(currentDate,dataqWeek.getFsclWeekEnd()) > 0?true:false);
                data.getDataMap().put(dataqWeek.getWeekLabel(), planValue);
            }
            data.setData(null);
        }

        PageInfo<QueryChannelDemandPlanVersionGroupListRspVo> result = PageUtils.init(dataList, pageNum, pageSize, pageTotal);
        return result;
    }

    /**
     *
     * @Description 查询渠道需求计划数据汇总
     * @param queryChannelDemandPlanVersionListReqVo
     * @return Map<String, Double>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月21日 14:50
     */
    @Override
    public Map<String, Double> queryChannelDemandPlanSummary(QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo) throws Exception
    {
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);

        List<PlanValue> dataList = dataqChannelDemandPlanDao.queryChannelDemandPlanSummary(queryChannelDemandPlanVersionListReqVo);
        if (CollectionUtils.isEmpty(dataList))
        {
            return null;
        }
        List<String> planDateList =
            dataList.stream().map(item -> StringUtils.replace(item.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        Map<String, Double> result = new HashMap<>(dataList.size());
        for (PlanValue data : dataList)
        {
            String planDate = StringUtils.replace(data.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
            DataqWeek dataqWeek = dataqWeekMap.get(planDate);
            result.put(dataqWeek.getWeekLabel(), data.getPlanValue());
        }

        return result;
    }

    /**
     *
     * @Description 查询渠道需求计划数据销售目标列表
     * @param channelDemandPlanDataParamVo
     * @return List<QueryChannelDemandPlanDataSaleTargetVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 16:56
     */
    @Override
    public List<QueryChannelDemandPlanDataSaleTargetVo> queryChannelDemandPlanDataSaleTargetList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception
    {
        // 查询dataq接口渠道需求计划版本列表，根据时间分组，只要版本数据最大时间和最小时间来查询销售目标
        Map<String, Object> param = new HashMap<>();
        param.put("aggregation", "plan_date,min,beginDate;plan_date,max,endDate");
        param.put("group_by", "plan_date");

        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setVersionId(channelDemandPlanDataParamVo.getVersionId());
        queryChannelDemandPlanVersionListReqVo.setGroupId(channelDemandPlanDataParamVo.getGroupId());
        queryChannelDemandPlanVersionListReqVo.setLv1CategoryCodes(channelDemandPlanDataParamVo.getLv1CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv2CategoryCodes(channelDemandPlanDataParamVo.getLv2CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv3CategoryCodes(channelDemandPlanDataParamVo.getLv3CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv1ChannelCodes(channelDemandPlanDataParamVo.getLv1ChannelCode());
        queryChannelDemandPlanVersionListReqVo.setLv2ChannelCodes(channelDemandPlanDataParamVo.getLv2ChannelCode());
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);
        String versionListPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, versionListPath, null, param, queryChannelDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }

        // 获取需要的销售目标最小月和最大月，格式：yyyy-MM，支持跨年
        String startMonth = StringUtils.substringBeforeLast(jsonArray.getJSONObject(0).getString("beginDate"), StringUtils.DATE_SEPARATOR);
        String endMonth = StringUtils.substringBeforeLast(jsonArray.getJSONObject(0).getString("endDate"), StringUtils.DATE_SEPARATOR);

        // 封装查询销售目标条件
        QuerySaleTargetReqVo querySaleTargetReqVo = new QuerySaleTargetReqVo();
        querySaleTargetReqVo.setBizDateType(BizDateTypeEnum.MONTH);
        querySaleTargetReqVo.setLv1CategoryCode(channelDemandPlanDataParamVo.getLv1CategoryCode());
        querySaleTargetReqVo.setLv2CategoryCode(channelDemandPlanDataParamVo.getLv2CategoryCode());
        querySaleTargetReqVo.setLv3CategoryCode(channelDemandPlanDataParamVo.getLv3CategoryCode());
        querySaleTargetReqVo.setLv1ChannelCode(channelDemandPlanDataParamVo.getLv1ChannelCode());
        querySaleTargetReqVo.setLv2ChannelCode(channelDemandPlanDataParamVo.getLv2ChannelCode());
        querySaleTargetReqVo.setStartDate(startMonth);
        querySaleTargetReqVo.setEndDate(endMonth);

        // 调用dataq查询销售目标表格数据接口，传入动态字段数据
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_SALE_TARGET"));
        DataqResult<?> saleTargetResult = dataqService.invoke(HttpMethod.POST, path, null, null, querySaleTargetReqVo);
        JSONArray saleTargetJsonArray = (JSONArray) saleTargetResult.getData();
        if (CollectionUtils.isEmpty(saleTargetJsonArray))
        {
            return null;
        }
        List<QueryChannelDemandPlanDataSaleTargetVo> saleTargetList = saleTargetJsonArray.toJavaList(QueryChannelDemandPlanDataSaleTargetVo.class);

        List<String> headList =
            saleTargetList.stream().map(QueryChannelDemandPlanDataSaleTargetVo::getBizDateValue).distinct().sorted(String::compareTo).collect(
                Collectors.toList());

        // 根据所有业务数据分组
        Map<QueryChannelDemandPlanDataSaleTargetVo, List<QueryChannelDemandPlanDataSaleTargetVo>> map =
            saleTargetList.stream().map(item -> {
                item.setLv3ChannelCode(null);
                return item;
            }).collect(Collectors.groupingBy(Function.identity()));
        List<QueryChannelDemandPlanDataSaleTargetVo> result = new ArrayList<>();
        for (Map.Entry<QueryChannelDemandPlanDataSaleTargetVo, List<QueryChannelDemandPlanDataSaleTargetVo>> entry : map.entrySet())
        {
            QueryChannelDemandPlanDataSaleTargetVo queryChannelDemandPlanDataSaleTargetVo = entry.getKey();
            List<QueryChannelDemandPlanDataSaleTargetVo> list = entry.getValue();
            for (String key : headList)
            {
                DoubleSummaryStatistics s = list.stream().filter(item -> {
                    return key.equals(item.getBizDateValue());
                }).collect(Collectors.summarizingDouble(item -> {
                    return Objects.isNull(item.getOrderNum()) ? 0d : item.getOrderNum();
                }));
                Double orderNum = Objects.isNull(s) ? 0d : s.getSum();
                String month = StringUtils.substringAfter(key, StringUtils.DATE_SEPARATOR);
                String dataKey = StringUtils.format(TOTAL_KEY, month.startsWith(StringUtils.ZERO) ? month.substring(1) : month);
                queryChannelDemandPlanDataSaleTargetVo.getDataMap().put(dataKey, orderNum);
            }
            result.add(queryChannelDemandPlanDataSaleTargetVo);
        }

        return result;
    }

    /**
     *
     * @Description 查询渠道需求计划数据需求提报列表
     * @param channelDemandPlanDataParamVo
     * @return List<QueryChannelDemandPlanDataReportListVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 11:39
     */
    @Override
    public List<QueryChannelDemandPlanDataReportListVo> queryChannelDemandPlanDataReportList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception
    {
        // 查询dataq接口渠道需求计划列表
        QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo = new QueryChannelDemandPlanListReqVo();
        queryChannelDemandPlanListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        List<QueryChannelDemandPlanListRspVo> planList = queryChannelDemandPlanList(queryChannelDemandPlanListReqVo);
        if (CollectionUtils.isEmpty(planList))
        {
            return null;
        }
        QueryChannelDemandPlanListRspVo channelDemandPlan = planList.get(0);

        // 调用平铺接口，获取小时间粒度的数据再聚合
        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "\"bizDateValue\"");
        param.put("aggregation", "\"orderNum\",sum,orderNum");

        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo = new QueryChannelDemandReportListReqVo();
        queryChannelDemandReportListReqVo.setRollingVersion(channelDemandPlanDataParamVo.getRollingVersion());
        queryChannelDemandReportListReqVo.setLv1CategoryCodes(channelDemandPlanDataParamVo.getLv1CategoryCode());
        queryChannelDemandReportListReqVo.setLv2CategoryCodes(channelDemandPlanDataParamVo.getLv2CategoryCode());
        queryChannelDemandReportListReqVo.setLv3CategoryCodes(channelDemandPlanDataParamVo.getLv3CategoryCode());
        queryChannelDemandReportListReqVo.setLv1ChannelCodes(channelDemandPlanDataParamVo.getLv1ChannelCode());
        queryChannelDemandReportListReqVo.setLv2ChannelCodes(channelDemandPlanDataParamVo.getLv2ChannelCode());
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        queryChannelDemandReportListReqVo.setIsModify(0);

        String reportPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_LIST"));
        DataqResult<?> reportResult = dataqService.invoke(HttpMethod.POST, reportPath, null, param, queryChannelDemandReportListReqVo);
        JSONArray reportJsonArray = (JSONArray) reportResult.getData();
        if (CollectionUtils.isEmpty(reportJsonArray))
        {
            return null;
        }
        List<QueryChannelDemandPlanDataReportListVo> reportList = reportJsonArray.toJavaList(QueryChannelDemandPlanDataReportListVo.class);

        List<String> bizDateValueList = reportList.stream().map(QueryChannelDemandPlanDataReportListVo::getBizDateValue).collect(Collectors.toList());
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);
        Map<String, DataqWeek> dataqWeekMap =
            objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart, Function.identity(), (key1, key2) -> key2));

        QueryChannelDemandPlanDataReportListVo report = new QueryChannelDemandPlanDataReportListVo();

        // 需求计划周期粒度为周
        if (PlanPeriodEnum.PER_WEEK.equals(channelDemandPlan.getPlanPeriod()))
        {
            for (QueryChannelDemandPlanDataReportListVo item : reportList)
            {
                DataqWeek dataqWeek = dataqWeekMap.get(item.getBizDateValue());
                String key = dataqWeek.getWeekLabel();
                ChannelDemandReportDataVo reportData = new ChannelDemandReportDataVo();
                reportData.setBizDateValue(item.getBizDateValue());
                reportData.setOrderNum(item.getOrderNum());
                report.getDataMap().put(key, reportData);
            }
        }
        else if (PlanPeriodEnum.PER_MONTH.equals(channelDemandPlan.getPlanPeriod()))
        {
            for (QueryChannelDemandPlanDataReportListVo item : reportList)
            {
                DataqWeek dataqWeek = dataqWeekMap.get(item.getBizDateValue());
                String key = dataqWeek.getWeekLabel();
                if (report.getDataMap().containsKey(key))
                {
                    ChannelDemandReportDataVo reportData = report.getDataMap().get(key);
                    double orderNum1 = reportData.getOrderNum();
                    double orderNum2 = item.getOrderNum();
                    reportData.setOrderNum(new BigDecimal(orderNum1).add(new BigDecimal(orderNum2)).doubleValue());
                }
                else
                {
                    ChannelDemandReportDataVo reportData = new ChannelDemandReportDataVo();
                    reportData.setBizDateValue(item.getBizDateValue());
                    reportData.setOrderNum(item.getOrderNum());
                    report.getDataMap().put(key, reportData);
                }
            }
        }

        List<QueryChannelDemandPlanDataReportListVo> result = new ArrayList<>();
        result.add(report);

        return result;
    }

    /**
     *
     * @Description 查询渠道需求计划数据预测结果列表
     * @param channelDemandPlanDataParamVo
     * @return List<QueryChannelDemandForecastResultListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 17:37
     */
    @Override
    public List<QueryChannelDemandForecastResultListRspVo> queryChannelDemandPlanDataForecastResultList(
        ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception
    {
        // 查询dataq接口渠道需求计划列表
        QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo = new QueryChannelDemandPlanListReqVo();
        queryChannelDemandPlanListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        List<QueryChannelDemandPlanListRspVo> planList = queryChannelDemandPlanList(queryChannelDemandPlanListReqVo);
        if (CollectionUtils.isEmpty(planList))
        {
            return null;
        }
        QueryChannelDemandPlanListRspVo channelDemandPlan = planList.get(0);

        // 查询dataq接口渠道需求计划版本列表，根据时间分组，获取所有版本数据日期字段
        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "plan_date");

        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setVersionId(channelDemandPlanDataParamVo.getVersionId());
        queryChannelDemandPlanVersionListReqVo.setGroupId(channelDemandPlanDataParamVo.getGroupId());
        queryChannelDemandPlanVersionListReqVo.setLv1CategoryCodes(channelDemandPlanDataParamVo.getLv1CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv2CategoryCodes(channelDemandPlanDataParamVo.getLv2CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv3CategoryCodes(channelDemandPlanDataParamVo.getLv3CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv1ChannelCodes(channelDemandPlanDataParamVo.getLv1ChannelCode());
        queryChannelDemandPlanVersionListReqVo.setLv2ChannelCodes(channelDemandPlanDataParamVo.getLv2ChannelCode());
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);
        String versionListPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, versionListPath, null, param, queryChannelDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return null;
        }

        // 解析dataq响应，所有版本拆分子计划的平铺数据，还需要对此数据根据子计划清单组编号过滤
        List<QueryChannelDemandPlanVersionListRspVo> planDataList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);

        // 获取所有周数据
        Set<String> planDateSet = planDataList.stream().map(item -> {
            return item.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
        }).collect(Collectors.toSet());

        // 获取指定周的周对象
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateSet);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(),
            (key1, key2) -> key1));
        // 封装动态表头，格式：yyyy-MM-dd|MM月Ww(dd-dd)
        List<String> headList = planDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getPlanDate).distinct().sorted((o1, o2) -> {
            return o1.compareTo(o2);
        }).map(item -> {
            try
            {
                String key = StringUtils.replace(item, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                DataqWeek dataqWeek = dataqWeekMap.get(key);
                String head = new StringBuilder(item).append(StringUtils.STAND_LINE_SEPARATOR).append(dataqWeek.getWeekLabel()).toString();
                return head;
            }
            catch (Exception e)
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
            }
        }).collect(Collectors.toList());

        // 预测结果默认查询周数据
        String periodType = null;
        if (PlanPeriodEnum.PER_WEEK.equals(channelDemandPlan.getPlanPeriod()))
        {
            periodType = "week";
        }
        else if (PlanPeriodEnum.PER_MONTH.equals(channelDemandPlan.getPlanPeriod()))
        {
            periodType = "month";
        }
        else
        {
            periodType = "week";
        }

        QueryChannelDemandForecastResultListReqVo queryChannelDemandForecastResultListReqVo = new QueryChannelDemandForecastResultListReqVo();
        queryChannelDemandForecastResultListReqVo.setAlgoNameAndVersion(channelDemandPlanDataParamVo.getAlgoNameAndVersion());
        queryChannelDemandForecastResultListReqVo.setPredictionVersion(channelDemandPlanDataParamVo.getPredictionVersion());
        queryChannelDemandForecastResultListReqVo.setPeriodType(periodType);
        queryChannelDemandForecastResultListReqVo.setLv1CategoryCodes(channelDemandPlanDataParamVo.getLv1CategoryCode());
        queryChannelDemandForecastResultListReqVo.setLv2CategoryCodes(channelDemandPlanDataParamVo.getLv2CategoryCode());
        queryChannelDemandForecastResultListReqVo.setLv3CategoryCodes(channelDemandPlanDataParamVo.getLv3CategoryCode());
        queryChannelDemandForecastResultListReqVo.setLv1ChannelCodes(channelDemandPlanDataParamVo.getLv1ChannelCode());
        queryChannelDemandForecastResultListReqVo.setLv2ChannelCodes(channelDemandPlanDataParamVo.getLv2ChannelCode());
        String forecastResultPah = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_LIST"));
        DataqResult<?> forecastResult = dataqService.invoke(HttpMethod.POST, forecastResultPah, null, null, queryChannelDemandForecastResultListReqVo);
        JSONArray forecastResultJsonArray = (JSONArray) forecastResult.getData();
        if (CollectionUtils.isEmpty(forecastResultJsonArray))
        {
            return null;
        }

        List<QueryChannelDemandForecastResultListRspVo> forecastResultList =
            forecastResultJsonArray.toJavaList(QueryChannelDemandForecastResultListRspVo.class);

        // 根据所有业务数据分组，从页面交互看，此处查询到的reportList应该只包含唯一的品类+渠道，分组代码暂时保留
        Map<QueryChannelDemandForecastResultListRspVo, List<QueryChannelDemandForecastResultListRspVo>> map =
            forecastResultList.stream().map(item -> {
                item.setLv3ChannelCode(null);
                return item;
            }).collect(Collectors.groupingBy(Function.identity()));
        List<QueryChannelDemandForecastResultListRspVo> result = new ArrayList<>();
        for (Map.Entry<QueryChannelDemandForecastResultListRspVo, List<QueryChannelDemandForecastResultListRspVo>> entry : map.entrySet())
        {
            QueryChannelDemandForecastResultListRspVo queryChannelDemandForecastResultListRspVo = entry.getKey();
            List<QueryChannelDemandForecastResultListRspVo> list = entry.getValue();
            for (String key : headList)
            {
                Double orderNum = null;
                // 需求计划滚动频率为周
                if (PlanPeriodEnum.PER_WEEK.equals(channelDemandPlan.getPlanPeriod()))
                {
                    // 截取数据时间格式yyyyMMdd，周第一天
                    String planDate = StringUtils.substringBefore(key, StringUtils.STAND_LINE_SEPARATOR).replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                    DoubleSummaryStatistics s = list.stream().filter(item -> {
                        return StringUtils.equals(planDate, item.getTargetBizDate());
                    }).collect(Collectors.summarizingDouble(item -> {
                        return Objects.isNull(item.getPredictionResult()) ? 0d : item.getPredictionResult();
                    }));
                    orderNum = s.getSum();
                }
                // 需求计划滚动频率为月
                else if (PlanPeriodEnum.PER_MONTH.equals(channelDemandPlan.getPlanPeriod()))
                {
                    // 截取数据时间格式yyyyMM
                    String planDate =
                        StringUtils.substringBeforeLast(StringUtils.substringBefore(key, StringUtils.STAND_LINE_SEPARATOR), StringUtils.DATE_SEPARATOR)
                            .replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                    DoubleSummaryStatistics s = list.stream().filter(item -> {
                        return item.getTargetBizDate().startsWith(planDate);
                    }).collect(Collectors.summarizingDouble(item -> {
                        return Objects.isNull(item.getPredictionResult()) ? 0d : item.getPredictionResult();
                    }));
                    orderNum = s.getSum();
                }
                queryChannelDemandForecastResultListRspVo.getDataMap().put(StringUtils.substringAfter(key, StringUtils.STAND_LINE_SEPARATOR), orderNum);
            }
            result.add(queryChannelDemandForecastResultListRspVo);
        }

        return result;
    }

    /**
     *
     * @Description 查询渠道需求计划数据历史同期出库列表
     * @param channelDemandPlanDataParamVo
     * @return List<QuerySkuDeliveryListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 17:33
     */
    @Override
    public List<QuerySkuDeliveryListRspVo> queryChannelDemandPlanDataObserList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception
    {
        // 查询dataq接口渠道需求计划列表
        QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo = new QueryChannelDemandPlanListReqVo();
        queryChannelDemandPlanListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        List<QueryChannelDemandPlanListRspVo> planList = queryChannelDemandPlanList(queryChannelDemandPlanListReqVo);
        if (CollectionUtils.isEmpty(planList))
        {
            return null;
        }
        QueryChannelDemandPlanListRspVo channelDemandPlan = planList.get(0);

        // 查询dataq接口渠道需求计划版本列表，根据时间分组，获取所有版本数据日期字段
        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "plan_date");

        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setVersionId(channelDemandPlanDataParamVo.getVersionId());
        queryChannelDemandPlanVersionListReqVo.setGroupId(channelDemandPlanDataParamVo.getGroupId());
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);
        String versionListPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, versionListPath, null, param, queryChannelDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (Objects.isNull(jsonArray))
        {
            return null;
        }

        // 解析dataq响应，所有版本拆分子计划的平铺数据，还需要对此数据根据子计划清单组编号过滤
        List<QueryChannelDemandPlanVersionListRspVo> planDataList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);

        // 获取所有周数据
        Set<String> bizDateValueSet =
            planDataList.stream().map(item -> {
                return item.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
            }).collect(Collectors.toSet());

        // 获取指定周的周对象
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueSet);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(),
            (key1, key2) -> key1));

        // 封装动态表头，格式：yyyy-MM-dd|MM月Ww(dd-dd)
        List<String> headList = planDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getPlanDate).distinct().sorted((o1, o2) -> {
            return o1.compareTo(o2);
        }).map(item -> {
            String key = item.replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
            DataqWeek dataqWeek = (DataqWeek) dataqWeekMap.get(key);
            String head = new StringBuilder(item).append(StringUtils.STAND_LINE_SEPARATOR).append(dataqWeek.getWeekLabel()).toString();
            return head;
        }).collect(Collectors.toList());

        String startDate =
            headList.stream().map(item -> {
                return StringUtils.substringBefore(item, StringUtils.STAND_LINE_SEPARATOR);
            }).min(Comparator.comparing(String::valueOf)).get();
        String endDate =
            headList.stream().map(item -> {
                return StringUtils.substringBefore(item, StringUtils.STAND_LINE_SEPARATOR);
            }).max(Comparator.comparing(String::valueOf)).get();

        QuerySkuDeliveryListReqVo querySkuDeliveryListReqVo = new QuerySkuDeliveryListReqVo();
        querySkuDeliveryListReqVo.setLv1ChannelCode(channelDemandPlanDataParamVo.getLv1ChannelCode());
        querySkuDeliveryListReqVo.setLv2ChannelCode(channelDemandPlanDataParamVo.getLv2ChannelCode());
        querySkuDeliveryListReqVo.setLv1CategoryCode(channelDemandPlanDataParamVo.getLv1CategoryCode());
        querySkuDeliveryListReqVo.setLv2CategoryCode(channelDemandPlanDataParamVo.getLv2CategoryCode());
        querySkuDeliveryListReqVo.setLv3CategoryCode(channelDemandPlanDataParamVo.getLv3CategoryCode());
        BizDateTypeEnum bizDateType = BizDateTypeEnum.WEEK;
        if (PlanPeriodEnum.PER_MONTH.equals(channelDemandPlan.getPlanPeriod()))
        {
            bizDateType = BizDateTypeEnum.MONTH;
        }
        else if (PlanPeriodEnum.PER_WEEK.equals(channelDemandPlan.getPlanPeriod()))
        {
            bizDateType = BizDateTypeEnum.WEEK;
        }
        querySkuDeliveryListReqVo.setBizDateType(bizDateType);
        querySkuDeliveryListReqVo.setStartDate(startDate);
        querySkuDeliveryListReqVo.setEndDate(endDate);

        String deliverPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_SKU_DELIVERY_LIST"));
        DataqResult<?> deliveryResult = dataqService.invoke(HttpMethod.POST, deliverPath, null, null, querySkuDeliveryListReqVo);
        JSONArray deliveryJsonArray = (JSONArray) deliveryResult.getData();
        if (Objects.isNull(deliveryJsonArray))
        {
            return null;
        }

        List<QuerySkuDeliveryListRspVo> deliveryList = deliveryJsonArray.toJavaList(QuerySkuDeliveryListRspVo.class);

        // 根据所有业务数据分组，从页面交互看，此处查询到的deliveryList应该只包含唯一的品类+渠道，分组代码暂时保留
        Map<QuerySkuDeliveryListRspVo, List<QuerySkuDeliveryListRspVo>> map =
            deliveryList.stream().map(item -> {
                item.setLv3ChannelCode(null);
                return item;
            }).collect(Collectors.groupingBy(Function.identity()));
        List<QuerySkuDeliveryListRspVo> result = new ArrayList<>();
        for (Map.Entry<QuerySkuDeliveryListRspVo, List<QuerySkuDeliveryListRspVo>> entry : map.entrySet())
        {
            QuerySkuDeliveryListRspVo querySkuDeliveryListRspVo = entry.getKey();
            List<QuerySkuDeliveryListRspVo> list = entry.getValue();
            for (String key : headList)
            {
                // 截取数据时间格式yyyyMMdd，周第一天
                String planDate = StringUtils.substringBefore(key, StringUtils.STAND_LINE_SEPARATOR).replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                DoubleSummaryStatistics s = list.stream().filter(item -> {
                    return StringUtils.equals(planDate, item.getBizDateValue());
                }).collect(Collectors.summarizingDouble(item -> {
                    return Objects.isNull(item.getOutboundNum()) ? 0d : item.getOutboundNum();
                }));
                Double orderNum = s.getSum();
                querySkuDeliveryListRspVo.getDataMap()
                    .put(StringUtils.substringAfter(key, StringUtils.STAND_LINE_SEPARATOR), Objects.isNull(orderNum) ? 0d : orderNum);
            }
            result.add(querySkuDeliveryListRspVo);
        }

        return result;
    }

    /**
     *
     * @Description 新增渠道需求计划
     * @param addDemandPlanVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 17:12
     */
    @Override
    public void addChannelDemandPlan(AddDemandPlanListReqVo addDemandPlanListReqVo) throws Exception
    {
        // 查询渠道需求计划名称是否重复
        int num = dataqChannelDemandPlanDao.queryChannelDemandPlanNameExists(addDemandPlanListReqVo);
        if (num > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }

        // 防止前端拆分对象和主体，分组列表传空[]，过滤掉空[]
        if (CollectionUtils.isNotEmpty(addDemandPlanListReqVo.getSubPlanGroupList()))
        {
            addDemandPlanListReqVo.setSubPlanGroupList(addDemandPlanListReqVo.getSubPlanGroupList().stream().filter(item -> {
                return CollectionUtils.isNotEmpty(item);
            }).collect(Collectors.toList()));
        }

        // 开始时间日期格式转换
        Date startDate = DateUtils.parseDate(addDemandPlanListReqVo.getStartDate(), DateUtils.YMD_DASH);

        // 调用dataq接口新增需求计划基础数据
        String demandPlanCode = SeqUtils.getSequenceUid();
        addDemandPlanListReqVo.setDemandPlanCode(demandPlanCode);
        addDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.order);
        addDemandPlanListReqVo.setMarketingYear(addDemandPlanListReqVo.getStartDate().substring(0, 4));
        addDemandPlanListReqVo.setStartDate(DateUtils.formatDate(startDate, DateUtils.YMD));
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_CREATE"));
        dataqService.invoke(HttpMethod.POST, path, null, null, addDemandPlanListReqVo);

        // 新增需求计划的滚动版本创建任务
        SchedulerJob schedulerJob = new SchedulerJob();
        schedulerJob.setJobId(SeqUtils.getSequenceUid());
        schedulerJob.setJobName("createDemandPlanVersion:" + addDemandPlanListReqVo.getDemandPlanName());
        schedulerJob.setJobType(SchedulerConstant.JOB_TYPE_CRON);
        schedulerJob.setStartDate(startDate);
        schedulerJob.setEndDate(null);
        String cron = null;
        if (PlanPeriodEnum.PER_MONTH.equals(addDemandPlanListReqVo.getPlanPeriod()))
        {
            cron = StringUtils.format(MONTH_CRON_TEMPLATE, addDemandPlanListReqVo.getExecutionDateNum());
        }
        else if (PlanPeriodEnum.PER_WEEK.equals(addDemandPlanListReqVo.getPlanPeriod()))
        {
            // Quartz的周， 1，2，3，4，5，6，7分别表示 SUN,MON,TUE,WED,THU,FRI,SAT，与实际业务相差1，需要addDemandPlanListReqVo.getExecutionDateNum() + 1
            cron = StringUtils.format(WEEK_CRON_TEMPLATE,
                addDemandPlanListReqVo.getExecutionDateNum() >= 7 ? 1 : addDemandPlanListReqVo.getExecutionDateNum() + 1);
        }
        schedulerJob.setJobConf(cron);
        schedulerJob.setClassName(AddChannelDemandPlanVersionTaskServiceImpl.class.getName());
        schedulerJob.setParam(JSON.toJSONString(addDemandPlanListReqVo));
        schedulerJob.setServiceId(addDemandPlanListReqVo.getDemandPlanCode());

        schedulerService.addSchedulerJob(schedulerJob);

        // 发送创建分仓需求计划请求给阿里dataq
        warehouseDemandPlanService.addWarehouseDemandPlan(addDemandPlanListReqVo);
    }

    /**
     *
     * @Description 设置渠道需求计划版本标签
     * @param channelDemandPlanVersionLabelVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:56
     */
    @Override
    public void setChannelDemandPlanVersionLabel(ChannelDemandPlanVersionLabelVo channelDemandPlanVersionLabelVo) throws Exception
    {
        // 阿里需要指定渠道需求计划主体为渠道
        channelDemandPlanVersionLabelVo.setSubjectType(SubjectTypeEnum.order);

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_LABEL_SET"));
        dataqService.invoke(HttpMethod.POST, path, null, null, channelDemandPlanVersionLabelVo);
    }

    /**
     *
     * @Description 复制渠道需求计划版本
     * @param channelDemandPlanDataParamVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 10:50
     */
    @Deprecated
    public void duplicateChannelDemandPlanVersionOld(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception
    {
        // 查询需求计划历史版本（只查询版本号），用于生成复制版本
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
        Map<String, Object> param = new HashMap<String, Object>(2);
        param.put("return_fields", "version_id,version_date");
        param.put("group_by", "version_id,version_date");
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        List<QueryChannelDemandPlanVersionListRspVo> versionList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);

        // 生成复制版本号，不管传入的是原始版本，还是原始版本后续的复制版本，截取原始版本号
        String srcVersionId = StringUtils.substringBefore(channelDemandPlanDataParamVo.getVersionId(), StringUtils.DATE_SEPARATOR);
        // 获取对应版本最新的版本号，首先过滤原始版本号及其复制版本号，再获取最新的版本号
        QueryChannelDemandPlanVersionListRspVo latestVersion = versionList.stream().filter(item -> {
            return StringUtils.contains(item.getVersionId(),
                srcVersionId);
        }).max(Comparator.comparingInt(item -> {
            // 如果没有复制过版本，复制版本标签为0
            int index = 0;
            // 如果复制过版本，截取-后的复制版本标签数字
            if (item.getVersionId().contains(StringUtils.DATE_SEPARATOR))
            {
                index = Integer.valueOf(StringUtils.substringAfterLast(item.getVersionId(), StringUtils.DATE_SEPARATOR));
            }
            return index;
        })).get();

        String latestVersionId = latestVersion.getVersionId();

        int index = 1;
        if (latestVersionId.contains(StringUtils.DATE_SEPARATOR))
        {
            index = Integer.valueOf(StringUtils.substringAfter(latestVersionId, StringUtils.DATE_SEPARATOR)) + 1;
        }
        String newVersionId = srcVersionId + StringUtils.DATE_SEPARATOR + index;

        // 调用阿里提供创建版本接口
        String appCode = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_APPCODE_DUPLICATE_CHANNEL_DEMAND_PLAN_VERSION");
        ApiRunMicroAppRequest apiRunMicroAppRequest = new ApiRunMicroAppRequest();
        apiRunMicroAppRequest.setAppCode(appCode);
        Map<String, Object> map = new HashMap<>(2);
        map.put("demandPlanCode", channelDemandPlanDataParamVo.getDemandPlanCode());
        map.put("oldVersionId", channelDemandPlanDataParamVo.getVersionId());
        map.put("newVersionId", newVersionId);
        apiRunMicroAppRequest.setApiParamValues(map);

        log.info("addChannelDemandReportVersion param:{}", JSON.toJSONString(map));

        // 配置header
        Header header = new Header();
        header.setUserId(userId);
        header.setTenantCode(tenantCode);
        header.setWorkspaceCode(workspaceCode);

        // 执行算法调度任务
        MicroAppService service = dataIndustryContext.getService(MicroAppService.class);
        MicroAppTaskInstanceOutputVO execute = service.execute(apiRunMicroAppRequest, header);
    }

    /**
     *
     * @Description 复制渠道需求计划版本，直接操作数据库。因为dataq数据探索任务不支持update操作，无法写入过程状态后再更新为就绪。
     * @param channelDemandPlanDataParamVo
     * @throws Exception
     * <AUTHOR>
     * @date 2024年04月24日 15:43
     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void duplicateChannelDemandPlanVersion(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo) throws Exception
    {
        String lockKey = StringUtils.format(CommonConstants.REDIS_DUPLICATE_CHANNELDEMAND_PLAN_LOCK_KEY, channelDemandPlanDataParamVo.getDemandPlanCode());

        boolean lock = redisUtils.lock(lockKey, 1800L);
        if (!lock)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }

        try
        {
            // 查询需求计划历史版本（只查询版本号），用于生成复制版本
            QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
            queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
            queryChannelDemandPlanVersionListReqVo.setIsModify(0);
            queryChannelDemandPlanVersionListReqVo.setDeleted(0);
            String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
            Map<String, Object> param = new HashMap<String, Object>(2);
            param.put("return_fields", "version_id,version_date");
            param.put("group_by", "version_id,version_date");
            DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandPlanVersionListReqVo);
            JSONArray jsonArray = (JSONArray) dataqResult.getData();
            List<QueryChannelDemandPlanVersionListRspVo> versionList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);

            // 生成复制版本号，不管传入的是原始版本，还是原始版本后续的复制版本，截取原始版本号
            String srcVersionId = StringUtils.substringBefore(channelDemandPlanDataParamVo.getVersionId(), StringUtils.DATE_SEPARATOR);
            // 获取对应版本最新的版本号，首先过滤原始版本号及其复制版本号，再获取最新的版本号
            QueryChannelDemandPlanVersionListRspVo latestVersion = versionList.stream().filter(item -> {
                return StringUtils.contains(item.getVersionId(),
                    srcVersionId);
            }).max(Comparator.comparingInt(item -> {
                // 如果没有复制过版本，复制版本标签为0
                int index = 0;
                // 如果复制过版本，截取-后的复制版本标签数字
                if (item.getVersionId().contains(StringUtils.DATE_SEPARATOR))
                {
                    index = Integer.valueOf(StringUtils.substringAfterLast(item.getVersionId(), StringUtils.DATE_SEPARATOR));
                }
                return index;
            })).get();

            String latestVersionId = latestVersion.getVersionId();

            int index = 1;
            if (latestVersionId.contains(StringUtils.DATE_SEPARATOR))
            {
                index = Integer.valueOf(StringUtils.substringAfter(latestVersionId, StringUtils.DATE_SEPARATOR)) + 1;
            }
            String newVersionId = srcVersionId + StringUtils.DATE_SEPARATOR + index;

            dataqChannelDemandPlanDao.duplicateChannelDemandPlanDuplicateData(channelDemandPlanDataParamVo.getDemandPlanCode(), newVersionId, latestVersionId);
        }
        catch (Exception e)
        {
            throw new ServiceException();
        }
        finally
        {
            redisUtils.unlock(lockKey);
        }
    }

    /**
     *
     * @Description 修改渠道需求计划数据
     * @param addDemandPlanConfigReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 17:21
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void updateChannelDemandPlanData(AddDemandPlanConfigReqVo addDemandPlanConfigReqVo) throws Exception
    {
        // 修改为直接操作数据库
        List<PlanValue> planValueList = new ArrayList<>();
        for (DemandPlanConfigSkuVo demandPlanConfigSkuVo : addDemandPlanConfigReqVo.getPlanList())
        {
            planValueList.addAll(demandPlanConfigSkuVo.getPlanValues());
        }
        Map<String, PlanValue> planValueMap = planValueList.stream().collect(Collectors.toMap(PlanValue::getId, Function.identity(), (key1, key2) -> key2));

        // 使用id作为参数查询旧数据可能导致sql过长，效率很低，修改为使用品类、产品、渠道
        long groupId = addDemandPlanConfigReqVo.getPlanList().get(0).getGroupId();
        String lv2ChannelCodes =
            addDemandPlanConfigReqVo.getPlanList().stream().map(DemandPlanConfigSkuVo::getLv2ChannelCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        String lv3CategoryCodes =
            addDemandPlanConfigReqVo.getPlanList().stream().map(DemandPlanConfigSkuVo::getLv3CategoryCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));

        // 查询修改内容当前的数据
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(addDemandPlanConfigReqVo.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setVersionId(addDemandPlanConfigReqVo.getVersionId());
        queryChannelDemandPlanVersionListReqVo.setGroupId(groupId);
        queryChannelDemandPlanVersionListReqVo.setLv2ChannelCodes(lv2ChannelCodes);
        queryChannelDemandPlanVersionListReqVo.setLv3CategoryCodes(lv3CategoryCodes);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);
        List<ChannelDemanPlanHistoryDto> dataList = dataqChannelDemandPlanDao.queryChannelDemandPlanOldDataList(queryChannelDemandPlanVersionListReqVo);

        if (CollectionUtils.isEmpty(dataList))
        {
            return;
        }

        // 前端可能会筛选数据保存，根据id过滤
        dataList = dataList.stream().filter(item -> {
            return planValueMap.containsKey(item.getId());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataList))
        {
            return;
        }

        String currentAccount = ServiceContextUtils.currentSession().getAccount().getName();
        Date currentDate = new Date();
        for (ChannelDemanPlanHistoryDto history : dataList)
        {
            PlanValue planValue = planValueMap.get(history.getId());
            if (Objects.isNull(history.getOldPlanValue()) || Double.compare(history.getOldPlanValue(), planValue.getPlanValue()) != 0)
            {
                history.setPlanValue(planValue.getPlanValue());
                history.setRemark(planValue.getPlanRemark());
                history.setLastModifier(currentAccount);
                history.setGmtModify(currentDate);
            }
        }
        dataList = dataList.stream().filter(item -> {
            return Objects.nonNull(item.getGmtModify());
        }).collect(Collectors.toList());

//        dataqChannelDemandPlanDao.batchUpdateChannelDemandPlanData(dataList);
//
//        channelDemandPlanDao.batchAddChannelDemandPlanHistory(dataList);
        if (CollectionUtils.isNotEmpty(dataList))
        {
            mybatisUtils.batchUpdateOrInsertFragment(dataList, DataqChannelDemandPlanDao.class, (item, dao) -> dao.updateChannelDemandPlanData(item));

            mybatisUtils.batchUpdateOrInsertFragment(dataList, ChannelDemandPlanDao.class, (item, dao) -> dao.addChannelDemandPlanHistory(item));
        }

//        List<DemandPlanConfigSkuVo> planList = addDemandPlanConfigReqVo.getPlanList();
//        // 阿里需要指定渠道需求计划计划主体为渠道
//        addDemandPlanConfigReqVo.setSubjectType(SubjectTypeEnum.order);
//        addDemandPlanConfigReqVo.setPlanList(null);


//        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_CONFIG_UPDATE"));
//        // 如果数据量过大，需要分片
//        int fragmentSize = 500;
//        if (planList.size() < fragmentSize)
//        {
//            addDemandPlanConfigReqVo.setPlanList(planList);
//            // 发送创建需求计划配置参数（需求计划版本）请求给阿里dataq
//            dataqService.invoke(HttpMethod.POST, path, null, null, addDemandPlanConfigReqVo);
//        }
//        else
//        {
//            // 单例map获取线程池，不需要shutdown
//            ThreadPoolExecutor pool = ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.fragmentThread);
//            int dataSize = planList.size();
//            int threadCount =
//                Math.floorMod(dataSize, fragmentSize) == 0 ? Math.floorDiv(dataSize, fragmentSize) : Math.floorDiv(dataSize, fragmentSize) + 1;
//            CountDownLatch countDownLatch = new CountDownLatch(threadCount);
//            for (Integer i = 0; i < threadCount; i++)
//            {
//                final int start = i * fragmentSize;
//                final int fragmentNum = i;
//                final AddDemandPlanConfigReqVo subData = new AddDemandPlanConfigReqVo();
//                BeanUtils.copyProperties(addDemandPlanConfigReqVo, subData);
//                final List<DemandPlanConfigSkuVo> subPlanList = planList.stream().skip(start).limit(fragmentSize).collect(Collectors.toList());
//                subData.setPlanList(subPlanList);
//                pool.submit(() -> {
//                    try
//                    {
//                        // 发送创建需求计划配置参数（需求计划版本）请求给阿里dataq
//                        DataqResult temp = dataqService.invoke(HttpMethod.POST, path, null, null, subData);
//                        log.info("fragment {} addDemandPlanConfig rsp:{}", fragmentNum, temp);
//                    }
//                    catch (Exception e)
//                    {
//                        log.error("fragment {} addDemandPlanConfig error has error.", fragmentNum, e);
//                    }
//                    finally
//                    {
//                        countDownLatch.countDown();
//                        subPlanList.clear();
//                    }
//                });
//            }
//            countDownLatch.await();
//        }
    }

    /**
     *
     * @Description 确认渠道需求计划子计划清单组
     * @param confirmChannelDemandPlanSubPlanGroupVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 16:27
     */
    @Override
    public void confirmChannelDemandPlanSubPlanGroup(ConfirmChannelDemandPlanSubPlanGroupVo confirmChannelDemandPlanSubPlanGroupVo) throws Exception
    {
        String lockKey =
            StringUtils.format(CommonConstants.REDIS_CONFIRM_CHANNEL_DEMAND_PLAN_LOCK_KEY, confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode(),
                confirmChannelDemandPlanSubPlanGroupVo.getVersionId(), confirmChannelDemandPlanSubPlanGroupVo.getGroupId());
        boolean isLock = redisUtils.lock(lockKey, 1800);
        if (!isLock)
        {
            log.info("confirmChannelDemandPlanSubPlanGroup has processing in another node.");
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }
        try
        {
            // 阿里需要指定渠道需求计划计划主体为渠道
            confirmChannelDemandPlanSubPlanGroupVo.setSubjectType(SubjectTypeEnum.order);

            String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_CONFIRM"));
            dataqService.invoke(HttpMethod.POST, path, null, null, confirmChannelDemandPlanSubPlanGroupVo);

            // 查询dataq接口渠道需求计划版本列表，判断计划+版本所有子计划是否都已提交
            Map<String, Object> param = new HashMap<>();
            param.put("group_by", "\"status\"");

            QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
            queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());
            queryChannelDemandPlanVersionListReqVo.setVersionId(confirmChannelDemandPlanSubPlanGroupVo.getVersionId());
            queryChannelDemandPlanVersionListReqVo.setIsModify(0);
            queryChannelDemandPlanVersionListReqVo.setDeleted(0);
            String versionListPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
            DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, versionListPath, null, param, queryChannelDemandPlanVersionListReqVo);
            JSONArray jsonArray = (JSONArray) dataqResult.getData();
            if (Objects.isNull(jsonArray))
            {
                return;
            }

            // 解析dataq响应，所有版本拆分子计划的平铺数据
            List<QueryChannelDemandPlanVersionListRspVo> dataList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);

            // 过滤是否存在状态不为已提交的数据
            long count = dataList.stream().filter(item -> {
                return !STATUS_SUBMITED.equals(item.getStatus());
            }).count();

            // 如果状态不为已提交的数据数量大于0，则认为版本还未共识，直接退出
            if (count > 0L)
            {
                return;
            }

            // 发生渠道需求计划共识
            // 只获取共识当天所在周+下四周，5*7一定包含，再调用接口通过limit控制
            Date currentDay = new Date();
            String beginDate = DateUtils.formatTime(currentDay, DateUtils.YMD);
            String endDate = DateUtils.formatTime(DateUtils.addDays(currentDay, 5 * 7), DateUtils.YMD);

            // 查询dataq周数据
            QueryWeekListReqVo queryWeekListReqVo = new QueryWeekListReqVo();
            queryWeekListReqVo.setBeginDate(beginDate);
            queryWeekListReqVo.setEndDate(endDate);
            List<DataqWeek> dataqWeekList = calendarService.queryWeekList(queryWeekListReqVo);

            // 排序并仅获取5周数据
            dataqWeekList = dataqWeekList.stream().sorted(Comparator.comparing(DataqWeek::getFsclWeekStart)).limit(5).collect(Collectors.toList());
            beginDate = DateUtils.formatTime(DateUtils.parseDate(dataqWeekList.get(0).getFsclWeekStart(), DateUtils.YMD), DateUtils.YMD_DASH);
            endDate = DateUtils.formatTime(DateUtils.parseDate(dataqWeekList.get(4).getFsclWeekStart(), DateUtils.YMD), DateUtils.YMD_DASH);

            String currentAccount = ServiceContextUtils.currentSession().getAccount().getName();

            WarehouseDemandReportDto warehouseDemandReportDto = new WarehouseDemandReportDto();
            warehouseDemandReportDto.setDemandPlanCode(confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode());
            warehouseDemandReportDto.setRollingVersion(confirmChannelDemandPlanSubPlanGroupVo.getVersionId());
            warehouseDemandReportDto.setBizDateType(BizDateTypeEnum.WEEK);
            warehouseDemandReportDto.setCreator(currentAccount);
            warehouseDemandReportDto.setPlanDateStart(beginDate);
            warehouseDemandReportDto.setPlanDateEnd(endDate);

            // 创建分仓需求计划版本
            warehouseDemandPlanService.addWarehouseDemandPlanVersion(warehouseDemandReportDto);

            // 创建分仓需求提报
            warehouseDemandReportService.addWarehouseDemandReport(warehouseDemandReportDto);

            // 版本共识，将版本数据写认养中间表
            asyncChannelDemandPlanService.syncChannelDemandPlanData(confirmChannelDemandPlanSubPlanGroupVo);
        }
        catch (Exception e)
        {
            throw new ServiceException();
        }
        finally
        {
            redisUtils.unlock(lockKey);
            redisUtils.vagueDel(CommonConstants.CALENDAR_TABLE_DATA_PRE_KEY + confirmChannelDemandPlanSubPlanGroupVo.getDemandPlanCode() + "*");
        }
    }

    /**
     *
     * @Description 下线需求计划
     * @param offlineDemandPlanReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:09
     */
    @Override
    public void offlineChannelDemandPlan(OfflineDemandPlanReqVo offlineDemandPlanReqVo) throws Exception
    {
        // 阿里需要指定渠道需求计划主体为渠道
        offlineDemandPlanReqVo.setSubjectType(SubjectTypeEnum.order);

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_OFFLINE"));
        dataqService.invoke(HttpMethod.POST, path, null, null, offlineDemandPlanReqVo);

        offlineDemandPlanReqVo.setSubjectType(SubjectTypeEnum.warehouse);

        dataqService.invoke(HttpMethod.POST, path, null, null, offlineDemandPlanReqVo);

        // 查询计划对应的滚动版本生成任务，暂停该任务
        SchedulerJob schedulerJob = schedulerDao.querySchedulerJobByServiceId(offlineDemandPlanReqVo.getDemandPlanCode());
        if (Objects.nonNull(schedulerJob))
        {
            schedulerService.pauseSchedulerJob(schedulerJob.getJobId());
        }
    }

    /**
     *
     * @Description 删除需求计划
     * @param deleteDemandPlanReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 14:09
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void deleteChannelDemandPlan(DeleteDemandPlanReqVo deleteDemandPlanReqVo) throws Exception
    {
        // 阿里需要指定渠道需求计划主体为渠道
        deleteDemandPlanReqVo.setSubjectType(SubjectTypeEnum.order);

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_DELETE"));
        dataqService.invoke(HttpMethod.POST, path, null, null, deleteDemandPlanReqVo);

        asyncChannelDemandPlanService.deleteChannelDemandPlanCascade(deleteDemandPlanReqVo);
    }

    /**
     *
     * @Description 查询渠道需求计划变更历史明细
     * @param channelDemandPlanDataParamVo
     * @return List<ChannelDemanPlanHistoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月10日 18:30
     */
    @Override
    public List<ChannelDemanPlanHistoryDto> queryChannelDemandPlanHistoryList(ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo)
        throws Exception
    {

        List<ChannelDemanPlanHistoryDto> historyList = channelDemandPlanDao.queryChannelDemandPlanHistoryList(channelDemandPlanDataParamVo);

        // 获取当前版本涉及所有时间粒度日期对应的周数据
        List<String> planDateList = historyList.stream().map(item -> item.getPlanDate()).distinct().collect(Collectors.toList());

        // 查询时间范围内日历周粒度数据
        Map<String, Object> dataqWeekMap = redisUtils.hmget(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY);

        // 循环封装修改历史数据
        for (ChannelDemanPlanHistoryDto history : historyList)
        {
            DataqWeek dataqWeek =
                (DataqWeek) dataqWeekMap.get(history.getPlanDate().replaceAll(String.valueOf(StringUtils.DATE_SEPARATOR), StringUtils.EMPTY));
            // 时间粒度格式转换为m月Ww
            history.setPlanDateLabel(Objects.isNull(dataqWeek) ? history.getPlanDate() : dataqWeek.getMonthWeekLabel());
        }

        return historyList;
    }


    /**
     *
     * @Description 查询渠道需求计划列表
     * @return JSONArray
     * <AUTHOR>
     * @date 2024年02月29日 14:15
     */
    @Override
    public JSONArray queryChannelDemandPlanList() throws Exception
    {
        QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo = new QueryChannelDemandPlanListReqVo();
        queryChannelDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.order);
        queryChannelDemandPlanListReqVo.setDeleted(false);

        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "demand_plan_code,demand_plan_name");
        param.put("order_by", "demand_plan_code");

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandPlanListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();

        return jsonArray;
    }
}
