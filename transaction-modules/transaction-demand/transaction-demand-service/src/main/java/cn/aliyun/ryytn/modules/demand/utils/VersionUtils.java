package cn.aliyun.ryytn.modules.demand.utils;

import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.utils.string.StringUtils;

/**
 * @Description 版本工具类
 * <AUTHOR>
 * @date 2023/11/7 11:29
 */
public class VersionUtils
{
    private static final String DEMANDPLAN_VERSION_TEMPLATE = "DP{}";

    /**
     *
     * @Description 生成需求计划版本号
     * @param dataqWeek
     * @return String
     * <AUTHOR>
     * @date 2023年11月07日 11:30
     */
    public static String getDemandPlanVersion(DataqWeek dataqWeek)
    {
        return StringUtils.format(DEMANDPLAN_VERSION_TEMPLATE, dataqWeek.getYearMonthWeek());
    }
}
