package cn.aliyun.ryytn.modules.demand.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.ChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.api.DeviationService;
import cn.aliyun.ryytn.modules.demand.api.ForecastAlgorithmService;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqDeliveryOrderRateDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqForecastResultDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.DeliveryOrderDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.DeviationDetailDataVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationDetailReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationDetailRsqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastAlgorithmListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastChannelFilterListReqVo;

/**
 * @Description 偏差对比接口
 * <AUTHOR>
 * @date 2023/11/02 10:24
 */
@Service
public class DeviationServiceImpl implements DeviationService
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private ChannelDemandPlanService channelDemandPlanService;

    @Autowired
    private ForecastAlgorithmService forecastAlgorithmService;

    @Autowired
    private DataqForecastResultDao dataqForecastResultDao;

    @Autowired
    private DataqChannelDemandPlanDao dataqChannelDemandPlanDao;

    @Autowired
    private DataqDeliveryOrderRateDao dataqDeliveryOrderRateDao;

    // 考核版标识
    private static final String LABEL_EXAM = "2";


    /**
     *
     * @Description 查询偏差比对-渠道实际出库数量列表
     * @param deviationChannelListReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月02日 10:10
     */
    @Override
    public BaseTable<List<QueryDeviationListRspVo>> queryDeviationList(QueryDeviationListReqVo deviationChannelListReqVo) throws Exception
    {
        Map<String, Object> returnData = new HashMap<>();
        //根据时间观测点  获取预测目标时间集合
        Date viewTime = deviationChannelListReqVo.getViewTime();
        String targetBizDates = "";
        if (Objects.isNull(viewTime))
        {
            targetBizDates = this.getTargetBizDates(new Date());
        }
        else
        {
            targetBizDates = this.getTargetBizDates(viewTime);
        }

        //封装条件参数
        String[] splitMonth = targetBizDates.split(",");
        List<String> dateList = Arrays.stream(splitMonth).sorted().collect(Collectors.toList());
        int size = dateList.size() - 1;
        String startDate = dateList.get(0);
        String endDate = dateList.get(size);
        String startDateDash = DateUtils.formatTime(DateUtils.parseDate(startDate, DateUtils.YMD), DateUtils.YMD_DASH);
        String endDateDash = DateUtils.formatTime(DateUtils.parseDate(endDate, DateUtils.YMD), DateUtils.YM_DASH) + "-99";
        deviationChannelListReqVo.setStartDate(startDateDash);
        deviationChannelListReqVo.setEndDate(endDateDash);
        StringBuilder groupColumn = new StringBuilder("target_biz_date");
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getLv1ChannelCode()))
        {
            groupColumn.append(",lv1_channel_code");
        }
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getLv2ChannelCode()))
        {
            groupColumn.append(",lv2_channel_code");
        }
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getLv1CategoryCode()))
        {
            groupColumn.append(",lv1_category_code");
        }
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getLv2CategoryCode()))
        {
            groupColumn.append(",lv2_category_code");
        }
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getLv3CategoryCode()))
        {
            groupColumn.append(",lv3_category_code");
        }
        deviationChannelListReqVo.setGroupColumn(groupColumn.toString());

        // 调用dataq接口查询 偏差比对-渠道比对-列表数据提报、需求、实际  http://121.43.102.223/elastic-25-432/ry_retail/ryytn_dev/deviatio/channel-def-list
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEVIATION_CHANNEL_DEF_LIST"));
//        String path = "/elastic-15561-551/znyy_test/ryytn_dev/deviatio/channel-def-list_copy";
        JSONArray data = (JSONArray) dataqService.invoke(HttpMethod.POST, path, null, null, deviationChannelListReqVo).getData();
        List<QueryDeviationListRspVo> channelListRspVos = JSONArray.parseArray(data.toJSONString(), QueryDeviationListRspVo.class);


        // 如果预测参数不为空，则查询预测结果数据
        Map<String, List<QueryDeviationListRspVo>> channelForecastResultMap = Collections.EMPTY_MAP;
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getAlgoNameAndVersion()))
        {
            QueryChannelForecastResultReqVo queryChannelForecastResultReqVo = new QueryChannelForecastResultReqVo();
            queryChannelForecastResultReqVo.setAlgoNameAndVersion(deviationChannelListReqVo.getAlgoNameAndVersion());
            queryChannelForecastResultReqVo.setPredictionVersion(deviationChannelListReqVo.getPredictionVersion());
            queryChannelForecastResultReqVo.setLv1CategoryCode(deviationChannelListReqVo.getLv1CategoryCode());
            queryChannelForecastResultReqVo.setLv2CategoryCode(deviationChannelListReqVo.getLv2CategoryCode());
            queryChannelForecastResultReqVo.setLv3CategoryCode(deviationChannelListReqVo.getLv3CategoryCode());
            queryChannelForecastResultReqVo.setLv1ChannelCode(deviationChannelListReqVo.getLv1ChannelCode());
            queryChannelForecastResultReqVo.setLv2ChannelCode(deviationChannelListReqVo.getLv2ChannelCode());
            queryChannelForecastResultReqVo.setPeriodType("month");
            queryChannelForecastResultReqVo.setStartDate(startDate);
            queryChannelForecastResultReqVo.setEndDate(endDate);

            // 查询渠道预测结果列表，跨多个版本的全量数据
            List<QueryDeviationListRspVo> channelForecastResultList =
                dataqForecastResultDao.queryDeviationChannelForecastResultList(queryChannelForecastResultReqVo);

            // 按时间分组，取最新版本数据
            channelForecastResultMap = channelForecastResultList.stream().collect(Collectors.groupingBy(QueryDeviationListRspVo::getTargetBizDate));
        }

        // 如果需求计划编号参数不为空，则查询需求计划数据
        Map<String, List<QueryChannelDemandPlanVersionListRspVo>> channelDemandPlanMap = Collections.EMPTY_MAP;
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getDemandPlanCode()))
        {
            QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
            queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(deviationChannelListReqVo.getDemandPlanCode());
            queryChannelDemandPlanVersionListReqVo.setLv1CategoryCode(deviationChannelListReqVo.getLv1CategoryCode());
            queryChannelDemandPlanVersionListReqVo.setLv2CategoryCode(deviationChannelListReqVo.getLv2CategoryCode());
            queryChannelDemandPlanVersionListReqVo.setLv3CategoryCode(deviationChannelListReqVo.getLv3CategoryCode());
            queryChannelDemandPlanVersionListReqVo.setLv1ChannelCode(deviationChannelListReqVo.getLv1ChannelCode());
            queryChannelDemandPlanVersionListReqVo.setLv2ChannelCode(deviationChannelListReqVo.getLv2ChannelCode());
            queryChannelDemandPlanVersionListReqVo.setBeginDate(startDateDash);
            queryChannelDemandPlanVersionListReqVo.setEndDate(endDateDash);
            queryChannelDemandPlanVersionListReqVo.setIsModify(0);
            queryChannelDemandPlanVersionListReqVo.setDeleted(0);

            // 查询渠道需求计划列表，跨多个版本、标签的全量数据，优先使用标签为考核版的
            List<QueryChannelDemandPlanVersionListRspVo> channelDemandPlanList =
                dataqChannelDemandPlanDao.queryDeviationChannelDemandPlanList(queryChannelDemandPlanVersionListReqVo);
            channelDemandPlanMap = channelDemandPlanList.stream().collect(Collectors.groupingBy(QueryChannelDemandPlanVersionListRspVo::getPlanDate));
        }

        DeliveryOrderDto deliveryOrderDto = new DeliveryOrderDto();
        deliveryOrderDto.setBizDateType("MONTH");
        deliveryOrderDto.setDimComb(DeliveryOrderDto.DIM_COMB.MONTH.getValue());
        deliveryOrderDto.setBeginDate(StringUtils.replace(startDate, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY));
        deliveryOrderDto.setEndDate(StringUtils.replace(endDate, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY));
        deliveryOrderDto.setLv1CategoryCode(deviationChannelListReqVo.getLv1CategoryCode());
        deliveryOrderDto.setLv2CategoryCode(deviationChannelListReqVo.getLv2CategoryCode());
        deliveryOrderDto.setLv3CategoryCode(deviationChannelListReqVo.getLv3CategoryCode());
        deliveryOrderDto.setLv1ChannelCode(deviationChannelListReqVo.getLv1ChannelCode());
        deliveryOrderDto.setLv2ChannelCode(deviationChannelListReqVo.getLv2ChannelCode());

        List<DeliveryOrderDto> deliveryOrderDtoList = dataqDeliveryOrderRateDao.queryDeliveryOrderGroupByMonth(deliveryOrderDto);
        Map<String, Double> deliveryOrderMap = Collections.EMPTY_MAP;
        if (CollectionUtils.isNotEmpty(deliveryOrderDtoList))
        {
            deliveryOrderMap = deliveryOrderDtoList.stream().collect(Collectors.toMap(DeliveryOrderDto::getBizDateValue, DeliveryOrderDto::getOutboundNum,
                (key1, key2) -> key2));
        }

        // 循环封装
        for (QueryDeviationListRspVo item : channelListRspVos)
        {
            String targetBizDate = item.getTargetBizDate();
            item.setObserValue(deliveryOrderMap.get(targetBizDate));

            List<QueryDeviationListRspVo> forecastResultList = channelForecastResultMap.get(targetBizDate);
            if (CollectionUtils.isNotEmpty(forecastResultList))
            {
                Double predictionResult =
                    forecastResultList.stream().sorted(Comparator.comparing(QueryDeviationListRspVo::getPredictionVersion).reversed())
                        .map(QueryDeviationListRspVo::getPredictionResult).findFirst().get();
                item.setPredictionResult(predictionResult);
            }
            else
            {
                item.setPredictionResult(0d);
            }

            List<QueryChannelDemandPlanVersionListRspVo> demandPlanList = channelDemandPlanMap.get(targetBizDate);
            if (CollectionUtils.isNotEmpty(demandPlanList))
            {
                // 如果有考核版标签版本，优先使用考核版
                List<QueryChannelDemandPlanVersionListRspVo> labelPlanList =
                    demandPlanList.stream().filter(plan -> StringUtils.equals(plan.getLabel(), LABEL_EXAM)).collect(Collectors.toList());
                Double planValue = null;
                if (CollectionUtils.isNotEmpty(labelPlanList))
                {
                    planValue = labelPlanList.stream().sorted(
                        Comparator.comparing(QueryChannelDemandPlanVersionListRspVo::getVersionIdPreffix).reversed()
                            .thenComparing(QueryChannelDemandPlanVersionListRspVo::getVersionIdSuffix).reversed()).findFirst().get().getPlanValue();
                }
                // 没有考核版，使用对应周期时间最新版本
                else
                {
                    planValue = demandPlanList.stream().sorted(
                        Comparator.comparing(QueryChannelDemandPlanVersionListRspVo::getVersionIdPreffix).reversed()
                            .thenComparing(QueryChannelDemandPlanVersionListRspVo::getVersionIdSuffix).reversed()).findFirst().get().getPlanValue();
                }
                item.setDemandPlanResult(planValue);
            }
            else
            {
                item.setDemandPlanResult(0d);
            }
            //处理日期  202301---> 23/01
            targetBizDate = item.getTargetBizDate();
            item.setTargetBizDate(targetBizDate.substring(2, 4) + "/" + targetBizDate.substring(4, 6));
        }

        List<String> months = Arrays.stream(splitMonth).map(item -> item.substring(2, 4) + "/" + item.substring(4, 6)).collect(Collectors.toList());

        Map<String, List<QueryDeviationListRspVo>> collect =
            channelListRspVos.stream().collect(Collectors.groupingBy(QueryDeviationListRspVo::getTargetBizDate));


        BaseTable<List<QueryDeviationListRspVo>> dataBaseTable = new BaseTable<>();

        dataBaseTable.setHeadArray(months);
        List<QueryDeviationListRspVo> fiveList = new ArrayList<>();

        for (int i = 0; i < 5; i++)
        {
            QueryDeviationListRspVo single = new QueryDeviationListRspVo();

            Map<String, Object> singleMap = new HashMap<>();

            Set<String> strings = collect.keySet();

            for (String key : strings)
            {
                singleMap.put(key, collect.get(key).get(0));
            }
            single.setDataMap(singleMap);
            fiveList.add(single);
        }

        dataBaseTable.setList(fiveList);
        //返回结果处理封装
        return dataBaseTable;
    }

    /**
     *
     * @Description 查询偏差比对-渠道实际出库数量列表
     * @param deviationChannelListReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月02日 10:10
     */
    @Deprecated
    public BaseTable<List<QueryDeviationListRspVo>> queryDeviationListOld(QueryDeviationListReqVo deviationChannelListReqVo) throws Exception
    {
        Map<String, Object> returnData = new HashMap<>();
        //根据时间观测点  获取预测目标时间集合
        Date viewTime = deviationChannelListReqVo.getViewTime();
        String targetBizDates = "";
        if (Objects.isNull(viewTime))
        {
            targetBizDates = this.getTargetBizDates(new Date());
        }
        else
        {
            targetBizDates = this.getTargetBizDates(viewTime);
        }

        //封装条件参数
        String[] splitMonth = targetBizDates.split(",");
        List<String> dateList = Arrays.stream(splitMonth).sorted().collect(Collectors.toList());
        String startDate = dateList.get(5).substring(0, 4) + "-" + dateList.get(0).substring(4, 6) + "-" + dateList.get(0).substring(6, 8);
        String endDate = dateList.get(14).substring(0, 4) + "-" + dateList.get(14).substring(4, 6) + "-" + dateList.get(14).substring(6, 8);
        deviationChannelListReqVo.setStartDate(startDate);
        deviationChannelListReqVo.setEndDate(endDate);
        StringBuilder groupColumn = new StringBuilder("target_biz_date");
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getLv1ChannelCode()))
        {
            groupColumn.append(",lv1_channel_code");
        }
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getLv2ChannelCode()))
        {
            groupColumn.append(",lv2_channel_code");
        }
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getLv1CategoryCode()))
        {
            groupColumn.append(",lv1_category_code");
        }
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getLv2CategoryCode()))
        {
            groupColumn.append(",lv2_category_code");
        }
        if (StringUtils.isNotEmpty(deviationChannelListReqVo.getLv3CategoryCode()))
        {
            groupColumn.append(",lv3_category_code");
        }
        deviationChannelListReqVo.setGroupColumn(groupColumn.toString());
        deviationChannelListReqVo.setPeriodType("month");

        // 调用dataq接口查询 偏差比对-渠道比对-列表数据提报、需求、实际  http://121.43.102.223/elastic-25-432/ry_retail/ryytn_dev/deviatio/channel-def-list
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEVIATION_CHANNEL_DEF_LIST"));
        JSONArray data = (JSONArray) dataqService.invoke(HttpMethod.POST, path, null, null, deviationChannelListReqVo).getData();
        List<QueryDeviationListRspVo> channelListRspVos = JSONArray.parseArray(data.toJSONString(), QueryDeviationListRspVo.class);

        if (StringUtils.isEmpty(deviationChannelListReqVo.getPredictionVersion()))
        {
            String version = "";
            //获取最新版本列表信息 prediction_version
            String pathVsersion = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_FILTER_LIST"));
            Map<String, String> param = new HashMap<>();
            param.put("selectColumns", "prediction_version");
            JSONArray versionList = (JSONArray) dataqService.invoke(HttpMethod.POST, pathVsersion, null, null, param).getData();
            List<String> predictionVersion =
                versionList.stream().map(item -> (String) ((JSONObject) item).get("predictionVersion")).sorted().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(predictionVersion))
            {
                version = predictionVersion.get(0);
            }
            deviationChannelListReqVo.setPredictionVersion(version);
        }
        //调用 dataq接口查询 偏差比对_渠道比对_列表数据预测结果
        String pathForecast = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEVIATION_CHANNEL_FORCAST_LIST"));
        JSONArray dataForecast = (JSONArray) dataqService.invoke(HttpMethod.POST, pathForecast, null, null, deviationChannelListReqVo).getData();
        List<QueryDeviationListRspVo> forecastResult = JSONArray.parseArray(dataForecast.toJSONString(), QueryDeviationListRspVo.class);
        Map<String, List<QueryDeviationListRspVo>> collect1 = forecastResult.stream().collect(Collectors.groupingBy(QueryDeviationListRspVo::getTargetBizDate));
        channelListRspVos.stream().forEach(item -> {
            String targetBizDate = item.getTargetBizDate();
            List<QueryDeviationListRspVo> channelListRspVos1 = collect1.get(targetBizDate);
            if (channelListRspVos1 != null && channelListRspVos1.size() > 0)
            {
                item.setPredictionResult(channelListRspVos1.get(0).getPredictionResult());
            }
        });

        //调用 dataq接口查询需求计划
        String pathPlan = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEVIATION_CHANNEL_DEMAND_PLAN_LIST"));
        JSONArray dataPlan = (JSONArray) dataqService.invoke(HttpMethod.POST, pathPlan, null, null, deviationChannelListReqVo).getData();
        List<QueryDeviationListRspVo> planResult = JSONArray.parseArray(dataPlan.toJSONString(), QueryDeviationListRspVo.class);
        Map<String, List<QueryDeviationListRspVo>> planCollect =
            planResult.stream().collect(Collectors.groupingBy(QueryDeviationListRspVo::getTargetBizDate));
        channelListRspVos.stream().forEach(item -> {
            String targetBizDate = item.getTargetBizDate();
            List<QueryDeviationListRspVo> planListRspVos1 = planCollect.get(targetBizDate);
            if (planListRspVos1 != null && planListRspVos1.size() > 0)
            {
                item.setDemandPlanResult(planListRspVos1.get(0).getOrderNum());
            }
        });

        //处理日期  202301---> 23/01
        channelListRspVos.stream().forEach(item -> {
            String targetBizDate = item.getTargetBizDate();
            item.setTargetBizDate(targetBizDate.substring(2, 4) + "/" + targetBizDate.substring(4, 6));
        });
        List<String> months = Arrays.stream(splitMonth).map(item -> item.substring(2, 4) + "/" + item.substring(4, 6)).collect(Collectors.toList());

        Map<String, List<QueryDeviationListRspVo>> collect =
            channelListRspVos.stream().collect(Collectors.groupingBy(QueryDeviationListRspVo::getTargetBizDate));


        BaseTable<List<QueryDeviationListRspVo>> dataBaseTable = new BaseTable<>();

        dataBaseTable.setHeadArray(months);
        List<QueryDeviationListRspVo> fiveList = new ArrayList<>();

        for (int i = 0; i < 5; i++)
        {
            QueryDeviationListRspVo single = new QueryDeviationListRspVo();

            Map<String, Object> singleMap = new HashMap<>();

            Set<String> strings = collect.keySet();

            for (String key : strings)
            {
                singleMap.put(key, collect.get(key).get(0));
            }
            single.setDataMap(singleMap);
            fiveList.add(single);
        }

        dataBaseTable.setList(fiveList);
        //返回结果处理封装
        return dataBaseTable;
    }


    /**
     *
     * @Description 查询偏差比对-下钻详情数据
     * @param deviationDetailReqVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月09日 15:46
     */
    @Override
    public BaseTable<List<QueryDeviationDetailRsqVo>> queryDeviationDetail(QueryDeviationDetailReqVo deviationDetailReqVo) throws Exception
    {
        Map<String, Object> returnData = new HashMap<>();
        List<String> header = new ArrayList<>();
        Date viewTime = deviationDetailReqVo.getViewTime();
        if (Objects.isNull(viewTime))
        {
            viewTime = new Date();
        }
        String viewMonth = DateUtils.formatTime(viewTime, DateUtils.YM);
        // 需要查询观测时间前6个月+后3个月数据
        String startDate = DateUtils.formatDate(DateUtils.addMonths(viewTime, -6), DateUtils.YMD_DASH);
        String endDate = DateUtils.formatDate(DateUtils.addMonths(viewTime, 3), DateUtils.YMD_DASH);
        deviationDetailReqVo.setStartDate(startDate);
        deviationDetailReqVo.setEndDate(endDate);
        //获取列字段数据  偏差比对_渠道比对_详情_列表字段
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEVIATIO_SALES_PLAN_DETAIL_COLUMN"));
        Map<String, String> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        JSONArray data = (JSONArray) dataqService.invoke(HttpMethod.POST, path, null, null, param).getData();
        List<Map> maps = JSONArray.parseArray(data.toJSONString(), Map.class);
        String trendsColumn = "";
        if (maps.size() > 0)
        {
            trendsColumn = (String) maps.get(0).get("trends_column");
        }
        deviationDetailReqVo.setSelectColumn(trendsColumn);

        //封装条件参数
        StringBuilder groupColumn = new StringBuilder("target_biz_date");
        if (StringUtils.isNotEmpty(deviationDetailReqVo.getLv1ChannelCode()))
        {
            groupColumn.append(",lv1_channel_code");
        }
        if (StringUtils.isNotEmpty(deviationDetailReqVo.getLv2ChannelCode()))
        {
            groupColumn.append(",lv2_channel_code");
        }
        if (StringUtils.isNotEmpty(deviationDetailReqVo.getLv1CategoryCode()))
        {
            groupColumn.append(",lv1_category_code");
        }
        if (StringUtils.isNotEmpty(deviationDetailReqVo.getLv2CategoryCode()))
        {
            groupColumn.append(",lv2_category_code");
        }
        if (StringUtils.isNotEmpty(deviationDetailReqVo.getLv3CategoryCode()))
        {
            groupColumn.append(",lv3_category_code");
        }
        deviationDetailReqVo.setGroupColumn(groupColumn.toString());
        //下钻查询接口名
        String path2 = "";
        //下钻数据
        JSONArray data2 = null;
        // 版本号关键字
        String versionKey = "version";
        String labelKey = "label";
        String preffix = "DP";
        //0：销售目标 1：需求预测 2：需求计划
        Integer type = deviationDetailReqVo.getType();
        if (type.equals(0))
        {
            //销售目标下钻
            //获取 偏差比对_渠道比对_销售目标详情_行转列
            path2 = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEVIATIO_SALES_PLAN_DETAIL"));
            deviationDetailReqVo.setGroupColumn(versionKey);
            data2 = (JSONArray) dataqService.invoke(HttpMethod.POST, path2, null, null, deviationDetailReqVo).getData();
        }
        else if (type.equals(1))
        {
            //需求预测下钻
            //获取 偏差比对_渠道比对_需求预测详情_行转列
            path2 = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEVIATIO_FORCAST_DETAIL"));
            deviationDetailReqVo.setGroupColumn(versionKey);
            data2 = (JSONArray) dataqService.invoke(HttpMethod.POST, path2, null, null, deviationDetailReqVo).getData();
        }
        else if (type.equals(2))
        {
            //需求计划下钻
            //获取  偏差比对_渠道比对_需求计划详情_行转列
            path2 = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEVIATIO_DEMAND_PLAN_DETAIL"));
            deviationDetailReqVo.setGroupColumn(versionKey);
            data2 = (JSONArray) dataqService.invoke(HttpMethod.POST, path2, null, null, deviationDetailReqVo).getData();

            // 渠道需求计划支持版本复制功能，需要过滤获取每月最新版本数据
            if (Objects.nonNull(data2))
            {
                Map<String, String> versionLabelMap =
                    data2.stream().map(item -> {
                        return (JSONObject) item;
                    }).filter(item -> {
                        return item.containsKey(versionKey) && StringUtils.isNotBlank(item.getString(versionKey));
                    }).collect(Collectors.toMap(item -> {
                        return item.getString(versionKey);
                    }, item -> {
                        return Optional.ofNullable(item.getString(labelKey)).orElse(StringUtils.EMPTY);
                    }, (key1, key2) -> key2));

                // 普通版月/版本映射
                Map<String, String> monthVersionMap = new HashMap<>();
                // 考核版月/版本映射
                Map<String, String> labelMonthVersionMap = new HashMap<>();
                for (Map.Entry<String, String> entry : versionLabelMap.entrySet())
                {
                    String version = entry.getKey();
                    String label = entry.getValue();
                    String month = StringUtils.substringBefore(StringUtils.substringAfter(version, preffix), StringUtils.WEEK_PREFIX_UPPER);
                    if (LABEL_EXAM.equals(label))
                    {
                        if (labelMonthVersionMap.containsKey(month))
                        {
                            String maxVersion = labelMonthVersionMap.get(month);
                            Integer maxWeek = Integer.valueOf(StringUtils.substringBefore(StringUtils.substringAfter(maxVersion, StringUtils.WEEK_PREFIX_UPPER),
                                StringUtils.DATE_SEPARATOR));
                            Integer maxIndex = StringUtils.contains(version, StringUtils.DATE_SEPARATOR) ?
                                Integer.valueOf(StringUtils.substringAfter(version, StringUtils.DATE_SEPARATOR)) : 0;
                            Integer curWeek = Integer.valueOf(StringUtils.substringBefore(StringUtils.substringAfter(maxVersion, StringUtils.WEEK_PREFIX_UPPER),
                                StringUtils.DATE_SEPARATOR));
                            Integer curIndex = StringUtils.contains(version, StringUtils.DATE_SEPARATOR) ?
                                Integer.valueOf(StringUtils.substringAfter(version, StringUtils.DATE_SEPARATOR)) : 0;
                            if (curWeek > maxWeek || (curWeek.equals(curWeek) && curIndex > maxIndex))
                            {
                                labelMonthVersionMap.put(month, version);
                            }
                        }
                        else
                        {
                            labelMonthVersionMap.put(month, version);
                        }
                    }
                    else
                    {
                        if (monthVersionMap.containsKey(month))
                        {
                            String maxVersion = monthVersionMap.get(month);
                            Integer maxWeek = Integer.valueOf(StringUtils.substringBefore(StringUtils.substringAfter(maxVersion, StringUtils.WEEK_PREFIX_UPPER),
                                StringUtils.DATE_SEPARATOR));
                            Integer maxIndex = StringUtils.contains(version, StringUtils.DATE_SEPARATOR) ?
                                Integer.valueOf(StringUtils.substringAfter(version, StringUtils.DATE_SEPARATOR)) : 0;
                            Integer curWeek = Integer.valueOf(StringUtils.substringBefore(StringUtils.substringAfter(maxVersion, StringUtils.WEEK_PREFIX_UPPER),
                                StringUtils.DATE_SEPARATOR));
                            Integer curIndex = StringUtils.contains(version, StringUtils.DATE_SEPARATOR) ?
                                Integer.valueOf(StringUtils.substringAfter(version, StringUtils.DATE_SEPARATOR)) : 0;
                            if (curWeek > maxWeek || (curWeek.equals(curWeek) && curIndex > maxIndex))
                            {
                                monthVersionMap.put(month, version);
                            }
                        }
                        else
                        {
                            monthVersionMap.put(month, version);
                        }
                    }
                }

                // 过滤每个月最大的版本号
                Set<String> monthSet = new HashSet<>();
                data2 = JSONArray.parseArray(JSON.toJSONString(data2.stream().map(item -> {
                    return (JSONObject) item;
                }).filter(item -> {
                    String version = item.getString(versionKey);
                    if (StringUtils.isBlank(version))
                    {
                        return false;
                    }
                    String month = StringUtils.substringBefore(StringUtils.substringAfter(version, preffix), StringUtils.WEEK_PREFIX_UPPER);
                    if (StringUtils.equals(version, labelMonthVersionMap.get(month)))
                    {
                        monthSet.add(month);
                        return true;
                    }
                    else if (!monthSet.contains(month) && StringUtils.equals(version, monthVersionMap.get(month)))
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }).collect(Collectors.toList())));
            }
        }
        else
        {
            return null;
        }

        DeliveryOrderDto deliveryOrderDto = new DeliveryOrderDto();
        deliveryOrderDto.setBizDateType("MONTH");
        deliveryOrderDto.setDimComb(DeliveryOrderDto.DIM_COMB.MONTH.getValue());
        deliveryOrderDto.setBeginDate(StringUtils.replace(startDate, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY));
        deliveryOrderDto.setEndDate(StringUtils.replace(endDate, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY));
        deliveryOrderDto.setLv1CategoryCode(deviationDetailReqVo.getLv1CategoryCode());
        deliveryOrderDto.setLv2CategoryCode(deviationDetailReqVo.getLv2CategoryCode());
        deliveryOrderDto.setLv3CategoryCode(deviationDetailReqVo.getLv3CategoryCode());
        deliveryOrderDto.setLv1ChannelCode(deviationDetailReqVo.getLv1ChannelCode());
        deliveryOrderDto.setLv2ChannelCode(deviationDetailReqVo.getLv2ChannelCode());

        List<DeliveryOrderDto> deliveryOrderDtoList = dataqDeliveryOrderRateDao.queryDeliveryOrderGroupByMonth(deliveryOrderDto);
        Map<String, Double> deliveryOrderMap = Collections.EMPTY_MAP;
        if (CollectionUtils.isNotEmpty(deliveryOrderDtoList))
        {
            deliveryOrderMap = deliveryOrderDtoList.stream().collect(Collectors.toMap(DeliveryOrderDto::getBizDateValue, DeliveryOrderDto::getOutboundNum,
                (key1, key2) -> key2));
        }

        //处理拼装结果
        BaseTable<List<QueryDeviationDetailRsqVo>> dataBaseTable = new BaseTable<>();

        List<QueryDeviationDetailRsqVo> dataMap = new ArrayList<>();

        List<Map> list = JSONArray.parseArray(data2.toJSONString(), Map.class);
        returnData.put("list", new ArrayList<Map>());
        //处理version 为null的数据 （某个日期的实际值 会存在这个version为null的数据下）
        list = list.stream().filter(item -> item.containsKey(versionKey)).collect(Collectors.toList());
        String viewVersion = null;
        for (Map map : list)
        {
            QueryDeviationDetailRsqVo queryDeviationDetailRsqVo = new QueryDeviationDetailRsqVo();
            queryDeviationDetailRsqVo.setVersion(StringUtils.getValue(map.get(versionKey)));
            Set<Map.Entry<String, Object>> entries = map.entrySet();
            for (Map.Entry<String, Object> entry : entries)
            {
                String key = entry.getKey();
                if (key.equals(versionKey) || key.equals(labelKey))
                {
                    continue;
                }

                String lastMonth = null;
                if (type.equals(2))
                {
                    String versionMonth = StringUtils.substringBefore(StringUtils.substringAfter(queryDeviationDetailRsqVo.getVersion(), preffix),
                        StringUtils.WEEK_PREFIX_UPPER);
                    lastMonth = DateUtils.formatTime(DateUtils.addMonths(DateUtils.parseDate(versionMonth, DateUtils.YM), -1), DateUtils.YM);
                }
                //entry.getValue() 数据结构-> {\"orderNum\":-1,\"obserValue\":-1}
                DeviationDetailDataVo deviationDetailDataVo = JSONObject.parseObject(entry.getValue().toString(), DeviationDetailDataVo.class);
                deviationDetailDataVo.setObserValue(deliveryOrderMap.get(key));
                // 销售目标，版本号中的yyyyMM的列，展示实际值
                if (type.equals(0) && StringUtils.contains(queryDeviationDetailRsqVo.getVersion(), key))
                {
                    deviationDetailDataVo.setShowColumn("obserValue");
                }
                // 渠道预测结果，版本号中的yyyyMM的列，展示实际值
                else if (type.equals(1) && StringUtils.contains(queryDeviationDetailRsqVo.getVersion(), key))
                {
                    deviationDetailDataVo.setShowColumn("obserValue");
                }
                // 渠道需求计划，版本号中的yyyyMM-1月的列，展示实际值
                else if (type.equals(2) && StringUtils.equals(key, lastMonth))
                {
                    deviationDetailDataVo.setShowColumn("obserValue");
                }
                else
                {
                    deviationDetailDataVo.setShowColumn("orderNum");
                }
                header.add(key);
                queryDeviationDetailRsqVo.getDataMap().put(key, deviationDetailDataVo);
            }
            dataMap.add(queryDeviationDetailRsqVo);
            if (StringUtils.contains(queryDeviationDetailRsqVo.getVersion(), viewMonth))
            {
                viewVersion = queryDeviationDetailRsqVo.getVersion();
            }
        }

        header = header.stream().distinct().sorted().collect(Collectors.toList());
        dataBaseTable.setHeadArray(header);
        final String beginVersion = viewVersion;
        dataMap = dataMap.stream().filter(item -> {
                return StringUtils.isBlank(beginVersion) || StringUtils.compare(beginVersion, item.getVersion()) >= 0;
            }).sorted(Comparator.comparing(QueryDeviationDetailRsqVo::getVersion).reversed()).limit(7)
            .sorted(Comparator.comparing(QueryDeviationDetailRsqVo::getVersion)).collect(Collectors.toList());
        dataBaseTable.setList(dataMap);
        return dataBaseTable;
    }

    /**
     * @Description 获取版本下拉框信息
     * @return Map<String, Object>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月23日 14:07
     * */
    @Override
    public Map<String, Object> getVersion() throws Exception
    {
        Map<String, Object> versionMap = new HashMap<>();
        //查询算法版本
        QueryForecastAlgorithmListReqVo queryForecastAlgorithmListReqVo = new QueryForecastAlgorithmListReqVo();
        queryForecastAlgorithmListReqVo.setScene("reseller");
        Object data2 = forecastAlgorithmService.queryForecastAlgorithmList(queryForecastAlgorithmListReqVo).getData();
        List<Map> lists = JSONObject.parseArray(data2.toString(), Map.class);
        String algoNameAndVersion1 = "";
        if (lists.size() > 0)
        {
            algoNameAndVersion1 = lists.get(0).get("algoNameAndVersion").toString();
        }

        //查询预测结果版本
        QueryForecastChannelFilterListReqVo queryForecastChannelFilterListReqVo = new QueryForecastChannelFilterListReqVo();
        queryForecastChannelFilterListReqVo.setAlgoNameAndVersion(algoNameAndVersion1);
        String path1 = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_FILTER_LIST"));
        // 需要查询的字段 prediction_version
        queryForecastChannelFilterListReqVo.setSelectColumns("prediction_version");
        Object data1 = dataqService.invoke(HttpMethod.POST, path1, null, null, queryForecastChannelFilterListReqVo).getData();

        //查询需求计划版本
        List<QueryChannelDemandPlanListRspVo> queryChannelDemandPlanListRspVos =
            channelDemandPlanService.queryChannelDemandPlanList(new QueryChannelDemandPlanListReqVo());
        versionMap.put("demandPlanVersions", queryChannelDemandPlanListRspVos);
        versionMap.put("forecastVersions", data1);

        return versionMap;
    }


    /**
     * @Description 获取预测目标时间集合
     * @param now 当前观测点
     * @return
     */
    public String getTargetBizDates(Date now)
    {
        StringBuilder targetBizDates = new StringBuilder();
        String year_month = DateUtils.formatDate(now, DateUtils.YM_DASH);
        String[] s = year_month.split(StringUtils.DATE_SEPARATOR);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, Integer.valueOf(s[0]));
        calendar.set(Calendar.MONTH, Integer.valueOf(s[1]));
        calendar.add(Calendar.MONTH, -13);
        for (int i = 0; i < 15; i++)
        {
            calendar.add(Calendar.MONTH, 1);
            int month = calendar.get(Calendar.MONTH);
            month++;
            //月份小于10自动补零
            if (month < 10)
            {
                targetBizDates.append(calendar.get(Calendar.YEAR)).append(0).append(month).append("01").append(StringUtils.COMMA_SEPARATOR);
            }
            else
            {
                targetBizDates.append(calendar.get(Calendar.YEAR)).append(month).append("01").append(StringUtils.COMMA_SEPARATOR);
            }
        }
        return targetBizDates.substring(0, targetBizDates.length() - 1);
    }
}
