package cn.aliyun.ryytn.modules.demand.task;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.mybatis.MybatisUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.dao.ColdDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportVersionDto;
import cn.aliyun.ryytn.modules.system.api.ChannelService;
import cn.aliyun.ryytn.modules.system.api.ProductService;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import cn.aliyun.ryytn.modules.system.entity.vo.SkuConditionVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 生成低温需求提报版本定时任务
 * <AUTHOR>
 * @date 2023/12/16 10:51
 */
@Slf4j
@Service
public class AddColdDemandReportVersionTaskServiceImpl implements TaskService
{
    @Autowired
    private ProductService productService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private ColdDemandReportDao coldDemandReportDao;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private MybatisUtils mybatisUtils;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());

            // 获得日期是一周的第几天
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            // 获得日期相对于下周一的偏移量（默认星期一是一周的第二天）
            // 若日期是周日，它的下周一偏移量是1
            int nextMondayOffset = dayOfWeek == 1 ? 1 : 9 - dayOfWeek;

            // 获取下周一
            calendar.add(Calendar.DAY_OF_MONTH, nextMondayOffset);
            Date nextMonday = calendar.getTime();
            String rollingVersion = new StringBuilder("DDP").append(DateUtils.formatTime(nextMonday, DateUtils.YM)).append(StringUtils.WEEK_PREFIX_UPPER)
                    .append(calendar.get(Calendar.WEEK_OF_MONTH)).toString();
            String tableSuffix = StringUtils.substring(rollingVersion, 3, 9);
            List<String> bizDateValueList = new ArrayList<>(28);
            for (int i = 0; i < 28; i++) {
                String bizDateValue = DateUtils.formatTime(DateUtils.addDays(nextMonday, i), DateUtils.YMD);
                bizDateValueList.add(bizDateValue);
            }

            // 如果存在历史版本，自动带入历史版本提报数据
            List<String> versionList = coldDemandReportDao.queryColdDemandReportVersionList();

            // 产品列表
            SkuConditionVo skuConditionVo = new SkuConditionVo();
            List<SkuDto> skuList = productService.querySkuList(skuConditionVo);
            // 过滤只查询低温品类产品，暂时硬编码：产品分类名称为低温白奶
            skuList = skuList.stream().filter(item -> {
                return StringUtils.equals(item.getLv1CategoryName(), "低温白奶");
            }).collect(Collectors.toList());

            // 渠道列表
            List<ChannelDto> channelList = channelService.queryChannelList();

            // 循环封装版本数据
            Date currentTime = new Date();
            Double orderNum = 0d;
            List<ColdDemandReportDto> coldDemandReportList = new ArrayList<>(skuList.size() * channelList.size() * bizDateValueList.size());
            for (SkuDto sku : skuList) {
                for (ChannelDto channel : channelList) {
                    for (String bizDateValue : bizDateValueList) {
                        ColdDemandReportDto coldDemandReport = new ColdDemandReportDto();
                        coldDemandReport.setTableSuffix(tableSuffix);
                        coldDemandReport.setId(SeqUtils.getSequenceUid());
                        coldDemandReport.setRollingVersion(rollingVersion);
                        coldDemandReport.setOrderNum(orderNum);
                        coldDemandReport.setBizDateType(BizDateTypeEnum.DAY);
                        coldDemandReport.setBizDateValue(bizDateValue);
                        coldDemandReport.setSkuCode(sku.getSkuCode());
                        coldDemandReport.setSkuName(sku.getSkuName());
                        coldDemandReport.setLv1CategoryCode(sku.getLv1CategoryCode());
                        coldDemandReport.setLv1CategoryName(sku.getLv1CategoryName());
                        coldDemandReport.setLv2CategoryCode(sku.getLv2CategoryCode());
                        coldDemandReport.setLv2CategoryName(sku.getLv2CategoryName());
                        coldDemandReport.setLv3CategoryCode(sku.getLv3CategoryCode());
                        coldDemandReport.setLv3CategoryName(sku.getLv3CategoryName());
                        coldDemandReport.setLv1ChannelCode(channel.getLv1ChannelCode());
                        coldDemandReport.setLv1ChannelName(channel.getLv1ChannelName());
                        coldDemandReport.setLv2ChannelCode(channel.getLv2ChannelCode());
                        coldDemandReport.setLv2ChannelName(channel.getLv2ChannelName());
                        coldDemandReport.setLv3ChannelCode(channel.getLv3ChannelCode());
                        coldDemandReport.setLv3ChannelName(channel.getLv3ChannelName());
                        coldDemandReport.setReceiverType("to");//因dataQ接口调整,渠道信息不返回channelType,页面receiverType不展示,存储时默认为to
                        coldDemandReport.setUnit(sku.getUnit());
                        coldDemandReport.setRemark(null);
                        coldDemandReport.setExtend(null);
                        coldDemandReport.setIsModify(0);
                        coldDemandReport.setCreator(null);
                        coldDemandReport.setLastModifier(null);
                        coldDemandReport.setGmtCreate(currentTime);
                        coldDemandReport.setGmtModify(currentTime);
                        coldDemandReportList.add(coldDemandReport);
                    }
                }
            }

            int num = coldDemandReportDao.queryColdDemandReportVersionExists(rollingVersion);
            if (num > 0) {
                log.info("{} has exists.", rollingVersion);
                return;
            }

            ColdDemandReportVersionDto coldDemandReportVersionDto = new ColdDemandReportVersionDto();
            coldDemandReportVersionDto.setId(SeqUtils.getSequenceUid());
            coldDemandReportVersionDto.setRollingVersion(rollingVersion);
            coldDemandReportVersionDto.setCreator("定时任务");
            coldDemandReportDao.addColdDemandReportVersion(coldDemandReportVersionDto);

            // 新版本提报数据入库
            mybatisUtils.batchUpdateOrInsertFragment(coldDemandReportList, ColdDemandReportDao.class,
                    (item, coldDemandReportDao) -> coldDemandReportDao.addColdDemandReport(item));

            // 获取最新的版本
            if (CollectionUtils.isNotEmpty(versionList)) {
                String latestRollingVersion = versionList.stream().max(Comparator.comparing(String::valueOf)).get();
                String latestTableSuffix = StringUtils.substring(latestRollingVersion, 3, 9);
                coldDemandReportDao.updateOrderNumFromLatestVersion(rollingVersion, tableSuffix, latestRollingVersion, latestTableSuffix);
            }
        }
        catch(Exception e){
            log.error("error低温需求提报滚动版本执行失败",e);
        }
    }
}
