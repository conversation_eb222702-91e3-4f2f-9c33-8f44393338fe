package cn.aliyun.ryytn.modules.demand.service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.ServiceResult;
import cn.aliyun.ryytn.common.exception.DataqServiceException;
import cn.aliyun.ryytn.common.exception.ExceptionEnums;
import cn.aliyun.ryytn.modules.demand.dataqdao.*;
import cn.aliyun.ryytn.modules.demand.entity.dos.*;
import cn.aliyun.ryytn.modules.demand.entity.dto.MarketActivityDto;
import cn.aliyun.ryytn.modules.demand.utils.BoostBeanUtils;
import com.alibaba.securitysdk.fastjson.JSON;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;

import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.Channel;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.MarketService;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddOrUpdateMarketVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.MarketChannelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.MarketProductVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.MarketRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.MarketActivitySkuVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketChannelListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketChannelListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketConstantReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketSkuListReqVo;
import cn.aliyun.ryytn.modules.system.api.ChannelService;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 促销活动实现接口
 * <AUTHOR>
 * @date 2023/10/24 9:27
 */
@Slf4j
@Service
public class MarketServiceImpl implements MarketService
{
    @Autowired
    private ChannelService channelService;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private DataqCommentsDao dataqCommentsDao;

    @Autowired
    private DataqMarketActivityDao dataqMarketActivityDao;

    @Autowired
    private DataqConstantConfigDao dataqConstantConfigDao;

    @Autowired
    private DataqMarketActivityResellerDao dataqMarketActivityResellerDao;

    @Autowired
    private DataqMarketActivitySkuDao dataqMarketActivitySkuDao;


    @Autowired
    private DataqMarketActivityExpertDao dataqMarketActivityExpertDao;


    @Autowired
    private DataqMarketActivitySkuDetailDao dataqMarketActivitySkuDetailDao;

    /**
     * @Description 查询活动常量配置列表
     * @param  constantTypeVo
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/24 9:30
     */
    @Override
    public Map<Object, List<Map>> queryConstantList(QueryMarketConstantReqVo constantTypeVo) throws Exception
    {
        List<Map<String,Object >> maps = dataqConstantConfigDao.queryMarketConstantConfigList(constantTypeVo);

        Map<Object, List<Map>> constantTypes = maps.stream().collect(Collectors.groupingBy(item -> item.get("constant_type")));

        if (StringUtils.contains(constantTypeVo.getConstantTypes(), "ACTI_AREA"))
        {
            List<Map> provinceList = dataqCommentsDao.queryProvinceList();
            constantTypes.put("ACTI_AREA", provinceList);
        }

        return constantTypes;
    }


    /**
     * @Description 查询活动列表
     * @param  queryMarketListReqVo
     * @return List<MarketRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/24 9:37
     */
    @Override
    public List<MarketRspVo> queryMarketList(QueryMarketListReqVo queryMarketListReqVo) throws Exception
    {
        MarketActivityDto marketActivityDto = new MarketActivityDto();
        //处理活动状态
        String activityStatus = queryMarketListReqVo.getActivityStatus();
        if (StringUtils.isNotEmpty(queryMarketListReqVo.getActiCode())){
            //查询活动详情明细
            marketActivityDto.setActiCode(queryMarketListReqVo.getActiCode());
        }
        if (StringUtils.isNotEmpty(queryMarketListReqVo.getStartDate())){
            queryMarketListReqVo.setStartDate(queryMarketListReqVo.getStartDate()+" 00:00:00");
        }
        if (StringUtils.isNotEmpty(queryMarketListReqVo.getEndDate())){
            queryMarketListReqVo.setEndDate(queryMarketListReqVo.getEndDate()+" 23:59:59");
        }
        marketActivityDto.setEndDate(queryMarketListReqVo.getEndDate());
        marketActivityDto.setStartDate(queryMarketListReqVo.getStartDate());
        marketActivityDto.setActivityStatus(queryMarketListReqVo.getActivityStatus());
        if (StringUtils.isNotEmpty(queryMarketListReqVo.getActiTypes())){
            List<String> activityIds = new ArrayList<>();
            activityIds.add(queryMarketListReqVo.getActiTypes());
            marketActivityDto.setActiTypes(activityIds);
        }
        String currentDate = DateUtils.getDate(DateUtils.YMD_SLASH);
        if ("END".equals(activityStatus))
        {
//            queryMarketListReqVo.setLoseDate(currentDate);
            marketActivityDto.setLoseDate(currentDate);
        }
        else if ("READY".equals(activityStatus))
        {
//            queryMarketListReqVo.setReadyDate(currentDate);
            marketActivityDto.setReadyDate(currentDate);
        }
        else if ("RUNNING".equals(activityStatus))
        {
//            queryMarketListReqVo.setOptDate(currentDate);
            marketActivityDto.setOptDate(currentDate);
        }
//        Map<String, Object> param = new HashMap<>();
//        param.put("order_by", "gmt_create desc");

//        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BUSPARAM_MARKET_LIST"));
//        DataqResult<?> result = dataqService.invoke(HttpMethod.POST, path, null, param, queryMarketListReqVo);
//        JSONArray data = (JSONArray) result.getData();
//
//        //处理数据，根据时间生成活动状态  //now>endDate  结束  END  now<startDate ->READY     否则->RUNNING
//        Date now_d = new Date();
//        List<MarketRspVo> marketRspVos = JSONArray.parseArray(data.toJSONString(), MarketRspVo.class);

        Date now_d = new Date();
        List<MarketRspVo> marketRspVos = dataqMarketActivityDao.queryMarketList(marketActivityDto);
        for (MarketRspVo marketRspVo : marketRspVos)
        {
            String startDate = marketRspVo.getStartDate();
            String endDate = marketRspVo.getEndDate();
            if (StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate))
            {
                Date startDate_d = DateUtils.parseDate(startDate);
                Date endDate_d = DateUtils.parseDate(endDate);
                if (now_d.before(startDate_d))
                {
                    marketRspVo.setActivityStatus("READY");
                }
                else if (now_d.after(endDate_d))
                {
                    marketRspVo.setActivityStatus("END");
                }
                else
                {
                    marketRspVo.setActivityStatus("RUNNING");
                }
            }
        }
        return marketRspVos;
    }

    /**
     * @Description 查询活动渠道树
     * @param  marketChannelReqVo
     * @return List<Channel>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/25 17:55
     */
    @Override
    public List<Channel> queryMarketChannelTree(QueryMarketChannelListReqVo marketChannelReqVo) throws Exception
    {
        //获取渠道数据
//        DataqResult<?> result = this.getMarketChannelList(marketChannelReqVo);
//        JSONArray data = (JSONArray) result.getData();
//
//        //处理数据，构造树结构（现在只有四层）
//        List<QueryMarketChannelListRspVo> marketChannelListRspVos1 = JSONArray.parseArray(data.toJSONString(), QueryMarketChannelListRspVo.class);
        List<QueryMarketChannelListRspVo> marketChannelListRspVos = this.getMarketChannelList(marketChannelReqVo);
        List<Channel> marketChannelTree = this.createMarketChannelTree(marketChannelListRspVos);

        return marketChannelTree;
    }

    /**
     * @Description 查询活动产品列表
     * @param  marketSkuListReqVo
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/26 17:28
     */
    @Override
    public List<MarketActivitySkuVo> queryMarketSkuList(QueryMarketSkuListReqVo marketSkuListReqVo) throws Exception
    {
        List<MarketActivitySkuVo> result = dataqMarketActivitySkuDao.queryMarketActivitySkuList(marketSkuListReqVo);
        for (MarketActivitySkuVo m : result){
            MarketActivitySkuDetailDO marketActivitySkuDetailDO = new MarketActivitySkuDetailDO();
            marketActivitySkuDetailDO.setSkuCode(m.getSkuCode());
            marketActivitySkuDetailDO.setActiCode(m.getActiCode());
            List<MarketActivitySkuDetailDO> mList  = dataqMarketActivitySkuDetailDao.queryMarketActivitySkuDetailList(marketActivitySkuDetailDO);
            m.setSkuDetailList(mList);
        }
        return result;
    }


    /**
     * 查询达人组
     * @param marketActivityExpertDO
     * @return
     * @throws Exception
     */
    @Override
    public List<MarketActivityExpertDO> queryMarketActivityExpertList(MarketActivityExpertDO marketActivityExpertDO) throws Exception
    {
        List<MarketActivityExpertDO> result = dataqMarketActivityExpertDao.queryMarketDisExpertList(marketActivityExpertDO);
        return result;
    }

    /**
     * 新增达人组
     * @param marketActivityExpertDO
     * @return
     * @throws Exception
     */
    @Override
    public Boolean insertExpertGroups(MarketActivityExpertDO marketActivityExpertDO) throws Exception{
        List<MarketActivityExpertDO> marketActivityExpertDOList = new ArrayList<>();
        marketActivityExpertDOList.add(marketActivityExpertDO);
        List<MarketActivityExpertDO> dbExpertList= dataqMarketActivityExpertDao.queryMarketActivityExpertList(marketActivityExpertDO);
        if (dbExpertList.size() > 0){
            return false;
        }
        dataqMarketActivityExpertDao.batchInsertExperts(marketActivityExpertDOList);
        return true;
    }

    /**
     * @Description 查询活动详情
     * @param  actiCode
     * @return Map<String, Object>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/27 11:32
     */
    @Override
    public Map<String, Object> queryMarketActivityDetail(String actiCode) throws Exception
    {

        Map<String, Object> result = new HashMap<>();
        //调用dataq查询活动主数据
        QueryMarketListReqVo marketListReqVo = new QueryMarketListReqVo();
        marketListReqVo.setActiCode(actiCode);
        List<MarketRspVo> marketRspVos = this.queryMarketList(marketListReqVo);
        //产品数据
        QueryMarketSkuListReqVo queryMarketSkuListReqVo = new QueryMarketSkuListReqVo();
        queryMarketSkuListReqVo.setActiCode(actiCode);
//        JSONArray skuList = (JSONArray) this.queryMarketSkuList(queryMarketSkuListReqVo).getData();

        List<MarketActivitySkuVo> marketActivitySkuList = this.queryMarketSkuList(queryMarketSkuListReqVo);
        MarketActivitySkuVo[] skuArray = JSON.parseObject(JSON.toJSONString(marketActivitySkuList), MarketActivitySkuVo[].class);
        JSONArray skuList = new JSONArray(Arrays.asList(skuArray));

        //渠道数据
        QueryMarketChannelListReqVo queryMarketChannelListReqVo = new QueryMarketChannelListReqVo();
        queryMarketChannelListReqVo.setActiCode(actiCode);
//        JSONArray channelList = (JSONArray) this.getMarketChannelList(queryMarketChannelListReqVo).getData();
        List<QueryMarketChannelListRspVo> marketChannelList = this.getMarketChannelList(queryMarketChannelListReqVo);
        JSONArray channelList = JSONArray.parseArray(JSON.toJSONString(marketChannelList));
        //封装结果
        result.put("market", marketRspVos.size() > 0 ? marketRspVos.get(0) : null);
        result.put("skuList", skuList);
        result.put("channelList", channelList);
        return result;
    }

    @Override
    public void auditMarketActivityExpert(AddOrUpdateMarketVo marketVo) throws Exception
    {
        MarketActivityDO marketActivityDO = new MarketActivityDO();
        marketActivityDO.setId(marketVo.getId());
        marketActivityDO.setAuditStatus(marketVo.getAuditStatus());
        dataqMarketActivityDao.updateById(marketActivityDO);
        List<MarketProductVo> skuList = marketVo.getSkuList();
        List<MarketActivitySkuDetailDO> dbInsertSkuDetailList = skuList.stream()
                .flatMap(marketProductVo -> marketProductVo.getSkuDetailList().stream())
                .map(marketActivitySkuDetailDO -> {
                    MarketActivitySkuDetailDO dbSkuDetail = new MarketActivitySkuDetailDO();
                    dbSkuDetail.setActiCode(marketVo.getActiCode());
                    dbSkuDetail.setSkuCode(marketActivitySkuDetailDO.getSkuCode());
                    dbSkuDetail.setAuditStatus(1);
                    dbSkuDetail.setActiSkuDeliveryBeginTime(marketActivitySkuDetailDO.getActiSkuDeliveryBeginTime());
                    dbSkuDetail.setActiSkuDeliveryNumsLimit(marketActivitySkuDetailDO.getActiSkuDeliveryNumsLimit());
                    dbSkuDetail.setActiSkuWeekBeginTime(marketActivitySkuDetailDO.getActiSkuWeekBeginTime());
                    return dbSkuDetail;
                })
                .collect(Collectors.toList());
        // 可以在这里添加逻辑来判断是否需要删除和插入SKU明细
        dataqMarketActivitySkuDetailDao.deleteSkuDetail(marketVo.getActiCode());
        dataqMarketActivitySkuDetailDao.batchInsertSkuDetail(dbInsertSkuDetailList);
    }

    /**
     * @Description 新增修改活动数据
     * @param  marketVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/27 17:00
     */
    @Override
    public void addOrUpdateMarketActivity(AddOrUpdateMarketVo marketVo) throws Exception
    {
        //主数据
        String actiCode = marketVo.getActiCode();
        Boolean isHaveActiCode = true;
        if (StringUtils.isEmpty(actiCode))
        {
            isHaveActiCode = false;
            actiCode = SeqUtils.getRandomUid();
            marketVo.setActiCode(actiCode);
        }
        //处理产品数据
        List<MarketProductVo> skuList = marketVo.getSkuList();
        for (MarketProductVo marketProductVo : skuList)
        {
            marketProductVo.setActiCode(actiCode);
        }
        if (!CommonConstants.TALENT.equals(marketVo.getActiType())) {
            //处理渠道数据
            List<MarketChannelVo> channelList = marketVo.getChannelList();
            List<ChannelDto> channelDtoList = channelService.queryChannelList();
            for (MarketChannelVo marketChannelVo : channelList)
            {
                //前台传的resellerCode   只是渠道的编码  没有意义  用于前端渠道树的显示 勾选
                String resellerCode = marketChannelVo.getResellerCode();
                List<ChannelDto> collect = channelDtoList.stream().distinct()
                        .filter(item -> StringUtils.isNotEmpty(item.getLv3ChannelCode()))
                        .filter(item -> item.getLv3ChannelCode().equals(resellerCode)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect))
                {
                    ChannelDto channelDto = collect.get(0);
                    BeanUtils.copyProperties(marketChannelVo, channelDto);
                    marketChannelVo.setActiCode(actiCode);
                }
            }
            channelList = channelList.stream().filter(item -> StringUtils.isNotEmpty(item.getLv3ChannelName())).collect(Collectors.toList());
            //新增或修改marketActivityReseller表
            List<MarketActivityResellerDO> resellerDOS = BoostBeanUtils.copyProperties(channelList, MarketActivityResellerDO.class);
            addOrUpdateReseller(resellerDOS,actiCode);
        }
        //新增或修改marketActivity表
        MarketActivityDO activityDO = BoostBeanUtils.copyProperties(marketVo, MarketActivityDO::new);
        if (marketVo.getId() == null) {
            dataqMarketActivityDao.addMarketActivity(activityDO);
        } else {
            dataqMarketActivityDao.updateById(activityDO);
        }
        if (CommonConstants.TALENT.equals(marketVo.getActiType())) {
            //新增或删除marketActivityExpert表，更新为启用状态
            addOrUpdateExpert(marketVo);
        }
        if (CommonConstants.TALENT.equals(marketVo.getActiType())) {
            //达人活动，直接清空删除tdm_xqyc_txn_market_activity_sku_detail_di表
            dataqMarketActivitySkuDetailDao.deleteSkuDetail(marketVo.getActiCode());
        }

        //新增或修改marketActivitySku表
        List<MarketActivitySkuDO> skuDOS = BoostBeanUtils.copyProperties(marketVo.getSkuList(), MarketActivitySkuDO.class);
        addOrUpdateSku(skuDOS,actiCode);
    }

    /**
     * 新增达人组和达人id；
     * 更新达人组映射表启用状态；
     * @param marketVo
     * @return
     */
    public Boolean addOrUpdateExpert(AddOrUpdateMarketVo marketVo) {

        String expertId = marketVo.getExpertId();
        String expertName = marketVo.getExpertGroup();
        String[] expertIds = expertId.split(",");
        MarketActivityExpertDO marketActivityExpertDO = new MarketActivityExpertDO();
        // 使用Map来存储数据库中的专家信息，以提高查找效率
        Map<String, MarketActivityExpertDO> dbExpertMap = dataqMarketActivityExpertDao.queryMarketActivityExpertList(marketActivityExpertDO)
                .stream()
                .collect(Collectors.toMap(
                        expert -> expert.getExpertGroup() + expert.getExpertId(),
                        Function.identity()
                ));

        List<MarketActivityExpertDO> dbUpdateExpertList = new ArrayList<>();
        List<MarketActivityExpertDO> dbInsertExpertList = new ArrayList<>();

        for (String expertIdTemp : expertIds) {
            String key = expertName + expertIdTemp;
            MarketActivityExpertDO dbExpertTemp = dbExpertMap.get(key);

            if (dbExpertTemp != null) {
                // 如果存在，则更新
                dbUpdateExpertList.add(dbExpertTemp);
            } else {
                // 如果不存在，则插入
                dbExpertTemp = new MarketActivityExpertDO();
                dbExpertTemp.setExpertGroup(expertName);
                dbExpertTemp.setExpertId(expertIdTemp);
                dbExpertTemp.setRemark1(CommonConstants.STRING_ONE);
                dbInsertExpertList.add(dbExpertTemp);
            }
        }
        dataqMarketActivityExpertDao.batchUpdateActivityExpertList(dbUpdateExpertList);
        if(null != dbInsertExpertList && dbInsertExpertList.size() >0){
            dataqMarketActivityExpertDao.batchInsertExperts(dbInsertExpertList);
        }
        return true;
    }




    public Boolean addOrUpdateSku(List<MarketActivitySkuDO> skuDTOS, String actiCode) {
        String code;
        if (org.apache.commons.lang3.StringUtils.isBlank(actiCode)){
            code = skuDTOS.stream().findFirst()
                    .map(MarketActivitySkuDO::getActiCode)
                    .orElseThrow(() -> new DataqServiceException(ExceptionEnums.MARKET_ACTIVITY_CODE_NULL_ERROR));
        }else {
            code = actiCode;
        }
        Set<Long> updateIds = skuDTOS.stream()
                .map(MarketActivitySkuDO::getId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        List<Long> dbIds = dataqMarketActivitySkuDao.selectIdsByActiCode(code);
        Set<Long> deleteIds = new HashSet<>(dbIds);
        deleteIds.removeAll(updateIds);
        List<MarketActivitySkuDO> saveDOs = skuDTOS.stream()
                .filter(dto -> Objects.isNull(dto.getId()))
                .map(BoostBeanUtils.copyBeanStream(MarketActivitySkuDO::new))
                .collect(Collectors.toList());
        if (!org.springframework.util.CollectionUtils.isEmpty(deleteIds) && Objects.nonNull(MarketActivitySkuDO.builder().deleted(1).build())){
            //先更新删除后的产品deleted设置为1
            List<MarketActivitySkuDO> deleteResellerDOs = deleteIds.stream()
                    .map(id -> {
                        MarketActivitySkuDO marketActivitySkuDO = new MarketActivitySkuDO();
                        marketActivitySkuDO.setId(id);
                        marketActivitySkuDO.setDeleted(1);
                        return marketActivitySkuDO;
                    })
                    .collect(Collectors.toList());
            dataqMarketActivitySkuDao.batchUpdateMarketActivitySku(deleteResellerDOs);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(updateIds)){
            //更新现有的
            List<MarketActivitySkuDO> updateMarketActivitySkuList = skuDTOS.stream()
                    .filter(sku -> sku.getId() != null)
                    .collect(Collectors.toList());
            dataqMarketActivitySkuDao.batchUpdateMarketActivitySku(updateMarketActivitySkuList);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(saveDOs)){
            //插入新的产品
            dataqMarketActivitySkuDao.batchInsert(saveDOs);
        }
        return true;
    }

    public Boolean addOrUpdateReseller(List<MarketActivityResellerDO> resellerDOS, String actiCode) {
        String code;
        if (org.apache.commons.lang3.StringUtils.isBlank(actiCode)){
            code = resellerDOS.stream().findFirst()
                    .map(MarketActivityResellerDO::getActiCode)
                    .orElseThrow(() -> new DataqServiceException(ExceptionEnums.MARKET_ACTIVITY_CODE_NULL_ERROR));
        }else {
            code = actiCode;
        }
        Set<String> initResellerCodes = resellerDOS.stream()
                .map(MarketActivityResellerDO::getResellerCode)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        Set<String> updateResellerCodes = new HashSet<>(initResellerCodes);
        List<String> dbResellerCodes = dataqMarketActivityResellerDao.selectResellerCodesByActiCode(code);
        updateResellerCodes.retainAll(dbResellerCodes); // 保留updateResellerCodes中同时存在于dbResellerCodes中的ResellerCode

        Set<String> newResellerCodes = new HashSet<>(initResellerCodes);//新增
        newResellerCodes.removeAll(updateResellerCodes); // 移除initResellerCodes中存在于dbResellerCodes中的ResellerCode

        dbResellerCodes.removeAll(initResellerCodes); // 删除

        List<String> insertResellerCodes = new ArrayList<>(newResellerCodes); // 将newIds转换为List

        List<MarketActivityResellerDO> saveDOs = resellerDOS.stream()
                .filter(resellerDO -> insertResellerCodes.contains(resellerDO.getResellerCode()))
                .collect(Collectors.toList());

        if (!org.springframework.util.CollectionUtils.isEmpty(dbResellerCodes) && Objects.nonNull(MarketActivityResellerDO.builder().deleted(1).build())){
            //先更新删除后的产品deleted设置为1
            List<MarketActivityResellerDO> deleteResellerDOs = dbResellerCodes.stream()
                    .map(resellerCodeTemp -> {
                        MarketActivityResellerDO marketActivityResellerDO = new MarketActivityResellerDO();
                        marketActivityResellerDO.setActiCode(actiCode);
                        marketActivityResellerDO.setResellerCode(resellerCodeTemp);
                        marketActivityResellerDO.setDeleted(1);
                        return marketActivityResellerDO;
                    })
                    .collect(Collectors.toList());
            dataqMarketActivityResellerDao.batchUpdateByActiCodes(deleteResellerDOs);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(updateResellerCodes)){
            List<MarketActivityResellerDO> updateResellerDOs = updateResellerCodes.stream()
                    .map(resellerCodeTemp -> {
                        MarketActivityResellerDO marketActivityResellerDO = new MarketActivityResellerDO();
                        marketActivityResellerDO.setActiCode(actiCode);
                        marketActivityResellerDO.setResellerCode(resellerCodeTemp);
                        marketActivityResellerDO.setDeleted(0);
                        return marketActivityResellerDO;
                    })
                    .collect(Collectors.toList());
            dataqMarketActivityResellerDao.batchUpdateByActiCodes(updateResellerDOs);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(saveDOs)){
            //插入新的产品
            dataqMarketActivityResellerDao.batchInsert(saveDOs);
        }

        return true;
    }


    /**
     * @Description 删除活动
     * @param  id
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/30 14:14
     */
    @Override
    public ServiceResult deleteMarketActivity(String id) {
        MarketActivityDO marketActivityDO = dataqMarketActivityDao.getById(Long.parseLong(id));
        if (marketActivityDO == null) {
            return new ServiceResult(ExceptionEnums.MARKET_ACTIVITY_ID_NULL_ERROR);
        }

        String actiCode = marketActivityDO.getActiCode();
        int num = dataqMarketActivityDao.updateById(MarketActivityDO.builder().id(Long.parseLong(id)).deleted(1).build());
        if (num <= 0) {
            return new ServiceResult(ExceptionEnums.MARKET_ACTIVITY_OPERATE_ERROR);
        }
        try {
            MarketActivitySkuDO marketActivitySkuDO = new MarketActivitySkuDO();
            marketActivitySkuDO.setDeleted(1);
            marketActivitySkuDO.setActiCode(actiCode);
            dataqMarketActivitySkuDao.updateByActiCode(marketActivitySkuDO);
        } catch (Exception e) {
            log.error("同步删除关联SKU失败", e);
            return new ServiceResult(ExceptionEnums.MARKET_ACTIVITY_SKU_OPERATE_ERROR);
        }
        try {
            MarketActivityResellerDO marketActivityResellerDO = new MarketActivityResellerDO();
            marketActivityResellerDO.setDeleted(1);
            marketActivityResellerDO.setActiCode(actiCode);
            dataqMarketActivityResellerDao.updateByActiCode(marketActivityResellerDO);
        } catch (Exception e) {
            log.error("同步删除关联渠道失败", e);
            return new ServiceResult(ExceptionEnums.MARKET_ACTIVITY_RESELLER_OPERATE_ERROR);
        }

        return new ServiceResult(ExceptionEnums.SUCCESS);
    }

    /**
     * @Description 构造活动渠道列表
     * @param  queryMarketChannelListReqVo
     * @return List<Channel>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/25 17:55
     */
    private List<QueryMarketChannelListRspVo> getMarketChannelList(QueryMarketChannelListReqVo queryMarketChannelListReqVo) throws Exception
    {
        List<QueryMarketChannelListRspVo> result = dataqMarketActivityResellerDao.queryMarketChannelList(queryMarketChannelListReqVo);
//        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BUSPARAM_MARKET_CHANNEL_LIST"));
//        DataqResult<?> result = dataqService.invoke(HttpMethod.POST, path, null, null, queryMarketChannelListReqVo);
        return result;
    }

    /**
     * @Description 构造活动渠道树
     * @param  channelListRspVos
     * @return List<Channel>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/25 17:55
     */
    private List<Channel> createMarketChannelTree(List<QueryMarketChannelListRspVo> channelListRspVos)
    {
        List<Channel> Data = new ArrayList<>();

        //获取根目录  （现阶段只有四层目录）
        List<String> first = channelListRspVos.stream().map(item -> item.getLv1ChannelCode()).distinct().collect(Collectors.toList());
        for (String firstKey : first)
        {
            Channel channel = new Channel();
            List<QueryMarketChannelListRspVo> firstList =
                channelListRspVos.stream().filter(item -> item.getLv1ChannelCode().equals(firstKey)).collect(Collectors.toList());
            QueryMarketChannelListRspVo channelRspVo = firstList.get(0);
            String lv1ChannelName = channelRspVo.getLv1ChannelName();
            channel.setName(lv1ChannelName);
            channel.setId(firstKey);
            channel.setSubList(new ArrayList<>());
            //封装第二层目录，塞到subList中
            List<String> second = firstList.stream().map(item -> item.getLv2ChannelCode()).distinct().collect(Collectors.toList());

            for (String secondKey : second)
            {
                Channel channel2 = new Channel();
                List<QueryMarketChannelListRspVo> secondList = channelListRspVos.stream()
                    .filter(item -> item.getLv1ChannelCode().equals(firstKey))
                    .filter(item -> item.getLv2ChannelCode().equals(secondKey)).collect(Collectors.toList());

                if (secondList.size() < 1)
                {
                    continue;
                }
                QueryMarketChannelListRspVo channelRspVo2 = secondList.get(0);
                String lv1ChannelName2 = channelRspVo2.getLv2ChannelName();
                channel2.setName(lv1ChannelName2);
                channel2.setId(secondKey);
                channel2.setSubList(new ArrayList<>());
                //封装第三层目录，塞到subList中
                List<String> third = channelListRspVos.stream().map(item -> item.getLv3ChannelCode()).distinct().collect(Collectors.toList());

                for (String threeKey : third)
                {
                    Channel channel3 = new Channel();

                    List<QueryMarketChannelListRspVo> thirdList = channelListRspVos.stream()
                        .filter(item -> item.getLv1ChannelCode().equals(firstKey))
                        .filter(item -> item.getLv2ChannelCode().equals(secondKey))
                        .filter(item -> item.getLv3ChannelCode().equals(threeKey)).collect(Collectors.toList());
                    if (thirdList.size() < 1)
                    {
                        continue;
                    }
                    QueryMarketChannelListRspVo channelRspVo3 = thirdList.get(0);
                    channel3.setId(threeKey);
                    channel3.setName(channelRspVo3.getLv3ChannelName());
                    channel3.setSubList(new ArrayList<>());
                    for (QueryMarketChannelListRspVo channelRspVo1 : thirdList)
                    {
                        Channel channel4 = new Channel();
                        channel4.setId(channelRspVo1.getResellerCode());
                        channel4.setName(channelRspVo1.getResellerName());
                        channel3.getSubList().add(channel4);
                    }

                    channel2.getSubList().add(channel3);
                }
                channel.getSubList().add(channel2);
            }
            Data.add(channel);
        }
        return Data;
    }
}
