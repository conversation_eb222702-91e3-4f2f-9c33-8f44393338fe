package cn.aliyun.ryytn.modules.demand.task;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.modules.demand.dao.FreightVersionDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 *
 * @Description 定时刷日分仓调拨计划视图
 * <AUTHOR>
 * @date 2024/9/26 17:06
 */
@Slf4j
@Service
public class RereshAiDaiylWarehouseViewServiceImpl implements TaskService
{

    @Autowired
    private FreightVersionDao freightVersionDao;
    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        log.info(" RereshAiDaiylWarehouseViewServiceImpl recreate view  start===========");
        freightVersionDao.refreshAiDailyWarehouseView();
    }

}
