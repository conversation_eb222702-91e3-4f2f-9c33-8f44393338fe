package cn.aliyun.ryytn.modules.demand.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.ColdDemandReportService;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.dao.ColdDemandReportDao;
import cn.aliyun.ryytn.modules.demand.dao.SkuLockDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ColdDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockVo;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import cn.aliyun.ryytn.modules.system.api.ChannelService;

/**
 * @Description 低温需求提报服务接口实现
 * <AUTHOR>
 * @date 2023/11/27 10:21
 */
@Service
public class ColdDemandReportServiceImpl implements ColdDemandReportService
{
    @Autowired
    private ColdDemandReportDao coldDemandReportDao;

    @Autowired
    private CalendarService calendarService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SkuLockDao skuLockDao;

    /**
     *
     * @Description 查询低温需求提报版本列表
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 16:32
     */
    @Override
    public List<String> queryColdDemandReportVersionList() throws Exception
    {
        return coldDemandReportDao.queryColdDemandReportVersionList();
    }

    /**
     *
     * @Description 查询低温需求提报数据列表
     * @param coldDemandReportDto
     * @return BaseTable<List < ColdDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月30日 20:49
     */
    @Override
    public BaseTable<List<ColdDemandReportDto>> queryColdDemandReportList(ColdDemandReportDto coldDemandReportDto) throws Exception
    {
        BaseTable<List<ColdDemandReportDto>> baseTable = new BaseTable<>();

        String tableSuffix = StringUtils.substringBefore(StringUtils.substringAfter(coldDemandReportDto.getRollingVersion(), "DDP"),
            StringUtils.WEEK_PREFIX_UPPER);
        coldDemandReportDto.setTableSuffix(tableSuffix);

        // 数据权限
        generateDataScope(coldDemandReportDto);

        // 获取低温需求提报时间
        List<String> bizDateValueList = coldDemandReportDao.queryColdDemandReportDateList(coldDemandReportDto);
        // 动态表头
        List<String> headList = new ArrayList<>();
        Map<String, String> headDateMap = new HashMap<>();
        if (BizDateTypeEnum.DAY.equals(coldDemandReportDto.getBizDateType()))
        {
            for (String bizDateValue : bizDateValueList)
            {
                int dayOfWeek = DateUtils.getDayWeek(bizDateValue, DateUtils.YMD);
                int month = Integer.valueOf(StringUtils.substring(bizDateValue, 4, 6));
                int day = Integer.valueOf(StringUtils.substring(bizDateValue, 6));
                String head =
                    new StringBuilder(StringUtils.WEEK_UNIT).append(dayOfWeek).append(month).append(StringUtils.SLASH_SEPARATOR)
                        .append(day).toString();
                headList.add(head);
                headDateMap.put(bizDateValue, head);
            }
        }
        else
        {
            // 获取缓存中低温需求提报时间对应的周对象，低温需求提报周为自然周，不能使用认养的日历周对象缓存
            int weekSize = 7;
            for (int i = 0; i < bizDateValueList.size(); )
            {
                String beginBizDateValue = bizDateValueList.get(i);
                i = i + weekSize;
                String endBizDateValue = bizDateValueList.get(i - 1);
                String beginDay = DateUtils.formatTime(DateUtils.parseDate(beginBizDateValue, DateUtils.YMD), DateUtils.MD_SLASH);
                String endDay = DateUtils.formatTime(DateUtils.parseDate(endBizDateValue, DateUtils.YMD), DateUtils.MD_SLASH);
                // 一周加一个动态表头元素
                String head = beginDay + StringUtils.TO_SEPARATOR + endDay;
                headList.add(head);
            }
            for (int i = 0; i < bizDateValueList.size(); i++)
            {
                String bizDateValue = bizDateValueList.get(i);
                int index = Math.floorDiv(i, weekSize);
                // 每天都写动态表头映射关系
                headDateMap.put(bizDateValue, headList.get(index));
            }
        }

        // 如果上一版本号不为空，则查询上一版本数据
        Map<ColdDemandReportDto, Map<String, Double>> lastVersionMap = Collections.EMPTY_MAP;
        if (StringUtils.isNotBlank(coldDemandReportDto.getLastRollingVersion()))
        {
            ColdDemandReportDto lastVersionCondition = new ColdDemandReportDto();
            BeanUtils.copyProperties(coldDemandReportDto, lastVersionCondition);
            lastVersionCondition.setRollingVersion(coldDemandReportDto.getLastRollingVersion());
            lastVersionCondition.setTableSuffix(StringUtils.substringBefore(StringUtils.substringAfter(lastVersionCondition.getRollingVersion(), "DDP"),
                StringUtils.WEEK_PREFIX_UPPER));
            List<ColdDemandReportDto> lastVersionColdDemandReportList = coldDemandReportDao.queryColdDemandReportDataList(lastVersionCondition);
            if (CollectionUtils.isNotEmpty(lastVersionColdDemandReportList))
            {
                lastVersionMap = lastVersionColdDemandReportList.stream().collect(Collectors.toMap(Function.identity(),
                    item -> {
                        List<ChannelDemandReportDataVo> reportDataList = JSON.parseArray(item.getData(), ChannelDemandReportDataVo.class);
                        Map<String, Double> dataMap = reportDataList.stream().collect(Collectors.toMap(ChannelDemandReportDataVo::getBizDateValue,
                            ChannelDemandReportDataVo::getOrderNum, (key1, key2) -> key2));
                        return dataMap;
                    }, (key1, key2) -> key2));
            }
        }

        // 查询数据库中的低温需求提报数据
        List<ColdDemandReportDto> coldDemandReportList = coldDemandReportDao.queryColdDemandReportDataList(coldDemandReportDto);
        if (CollectionUtils.isEmpty(coldDemandReportList))
        {
            return baseTable;
        }

        // 查询包含当前时间和本次数据的产品锁定期
        List<String> skuCodeList = coldDemandReportList.stream().map(ColdDemandReportDto::getSkuCode).distinct().collect(Collectors.toList());
        List<String> channelCodeList = coldDemandReportList.stream().map(ColdDemandReportDto::getLv2ChannelCode).distinct().collect(Collectors.toList());
        SkuLockVo skuLockVo = new SkuLockVo();
        skuLockVo.setSkuCodes(skuCodeList);
        skuLockVo.setLv2ChannelCodes(channelCodeList);
        String currentDay = DateUtils.getDate(DateUtils.YMD);
        skuLockVo.setLockStartDate(currentDay);
        skuLockVo.setLockEndDate(currentDay);
        List<SkuLockVo> skuLockList = skuLockDao.queryLockList(skuLockVo);
        Map<String, List<SkuLockVo>> skuLockMap = Collections.EMPTY_MAP;
        if (CollectionUtils.isNotEmpty(skuLockList))
        {
            skuLockMap = skuLockList.stream().collect(Collectors.groupingBy(SkuLockVo::getSkuChannelCode));
        }

        String currentDate = DateUtils.getDate(DateUtils.YMD);

        for (ColdDemandReportDto coldDemandReport : coldDemandReportList)
        {
            // 上个版本数据
            Map<String, Double> lastVersionDataMap = Collections.EMPTY_MAP;
            if (lastVersionMap.containsKey(coldDemandReport))
            {
                lastVersionDataMap = lastVersionMap.get(coldDemandReport);
            }

            String skuChannelCode =
                new StringBuilder(coldDemandReport.getSkuCode()).append(StringUtils.DATE_SEPARATOR).append(coldDemandReport.getLv2ChannelCode()).toString();
            List<SkuLockVo> lockDateList = skuLockMap.get(skuChannelCode);

            coldDemandReport.setDataMap(new HashMap<>());
            List<ChannelDemandReportDataVo> reportDataList = JSON.parseArray(coldDemandReport.getData(), ChannelDemandReportDataVo.class);
            for (ChannelDemandReportDataVo reportData : reportDataList)
            {
                Double lastVersionOrderNum = lastVersionDataMap.get(reportData.getBizDateValue());

                String headKey = headDateMap.get(reportData.getBizDateValue());

                // 周粒度，可能会包含
                if (coldDemandReport.getDataMap().containsKey(headKey))
                {
                    ChannelDemandReportDataVo temp = coldDemandReport.getDataMap().get(headKey);
                    BigDecimal bg1 = new BigDecimal(temp.getOrderNum());
                    BigDecimal bg2 = new BigDecimal(reportData.getOrderNum());
                    BigDecimal orderNumBigDecimal = bg1.add(bg2);
                    temp.setOrderNum(orderNumBigDecimal.doubleValue());
                    if (Objects.nonNull(lastVersionOrderNum) && !lastVersionOrderNum.equals(0d))
                    {
                        if (lastVersionOrderNum.equals(0d))
                        {
                            // 如果上个版本是0,这个版本不是0，则偏差率为100%
                            if (!temp.getOrderNum().equals(0d))
                            {
                                temp.setDeviationRadio(1d);
                            }
                            // 如果上个版本是0，这个版本也是0，则偏差率为0
                            else
                            {
                                temp.setDeviationRadio(0d);
                            }
                        }
                        else
                        {
                            BigDecimal tempLastVersionOrderNumBigDecimal = new BigDecimal(Objects.isNull(temp.getLastOrderNum()) ? 0d : temp.getLastOrderNum());
                            BigDecimal lastVersionOrderNumBigDecimal = tempLastVersionOrderNumBigDecimal.add(new BigDecimal(lastVersionOrderNum));
                            temp.setLastOrderNum(lastVersionOrderNumBigDecimal.doubleValue());
                            temp.setDeviationRadio(
                                orderNumBigDecimal.subtract(lastVersionOrderNumBigDecimal).divide(lastVersionOrderNumBigDecimal, 2, RoundingMode.HALF_UP)
                                    .doubleValue());
                        }
                    }

                    coldDemandReport.getDataMap().put(headKey, temp);
                }
                else
                {
                    if (Objects.nonNull(lastVersionOrderNum))
                    {
                        BigDecimal orderNumBigDecimal = new BigDecimal(reportData.getOrderNum());
                        BigDecimal lastVersionOrderNumBigDecimal = new BigDecimal(lastVersionOrderNum);
                        reportData.setLastOrderNum(lastVersionOrderNumBigDecimal.doubleValue());
                        if (lastVersionOrderNum.equals(0d))
                        {
                            // 如果上个版本是0,这个版本不是0，则偏差率为100%
                            if (!reportData.getOrderNum().equals(0d))
                            {
                                reportData.setDeviationRadio(1d);
                            }
                            // 如果上个版本是0，这个版本也是0，则偏差率为0
                            else
                            {
                                reportData.setDeviationRadio(0d);
                            }
                        }
                        else
                        {
                            reportData.setDeviationRadio(
                                orderNumBigDecimal.subtract(lastVersionOrderNumBigDecimal).divide(lastVersionOrderNumBigDecimal, 2, RoundingMode.HALF_UP)
                                    .doubleValue());
                        }
                    }

                    // 产品锁定期
                    if (CollectionUtils.isNotEmpty(lockDateList))
                    {
                        long num = lockDateList.stream().filter(lockDate -> {
                            return StringUtils.compare(lockDate.getLockEndDate(), reportData.getBizDateValue()) >= 0 &&
                                StringUtils.compare(reportData.getBizDateValue(), lockDate.getLockStartDate()) >= 0;
                        }).count();
                        if (num > 0)
                        {
                            reportData.setLockFlag(true);
                        }
                    }
                    // 自然锁定期
                    if (StringUtils.compare(currentDate, reportData.getBizDateValue()) > 0)
                    {
                        reportData.setLockFlag(true);
                    }

                    coldDemandReport.getDataMap().put(headKey, reportData);
                }
            }
            coldDemandReport.setData(null);
        }

        baseTable.setHeadArray(headList);
        baseTable.setList(coldDemandReportList);

        return baseTable;
    }

    /**
     *
     * @Description 修改低温需求提报数据
     * @param dataList
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:16
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateColdDemandReportData(List<ColdDemandReportDto> dataList) throws Exception
    {
        Date currentTime = new Date();
        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        for (ColdDemandReportDto data : dataList)
        {
            data.setIsModify(1);
            data.setRemark(data.getRemark());
            data.setLastModifier(currentAccount.getName());
            data.setGmtModify(currentTime);
        }

        // 批量修改低温需求提报数据
        coldDemandReportDao.batchUpdateColdDemandReport(dataList);
    }

    /**
     *
     * @Description 查询低温需求提报动态表头
     * @param queryChannelDemandReportListReqVo
     * @return List < String>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年3月1日 14:15
     */
    @Override
    public List<String> queryColdDemandReportHeadList(ColdDemandReportDto coldDemandReportDto) throws Exception
    {
        // 临时保存时间粒度，永远查询最小粒度（WEEK），再根据查询条件的时间粒度计算动态字段分组聚合逻辑
        BizDateTypeEnum bizDateType = coldDemandReportDto.getBizDateType();
        coldDemandReportDto.setBizDateType(BizDateTypeEnum.DAY);
        String tableSuffix = StringUtils.substring(coldDemandReportDto.getRollingVersion(), 3, 9);
        coldDemandReportDto.setTableSuffix(tableSuffix);

        List<String> bizDateValueList = coldDemandReportDao.queryColdDemandReportDateList(coldDemandReportDto);

        List<String> headList = new ArrayList<>();
        if (BizDateTypeEnum.DAY.equals(bizDateType))
        {
            for (String bizDateValue : bizDateValueList)
            {
                int dayOfWeek = DateUtils.getDayWeek(bizDateValue, DateUtils.YMD);
                int month = Integer.valueOf(StringUtils.substring(bizDateValue, 4, 6));
                int day = Integer.valueOf(StringUtils.substring(bizDateValue, 6));
                String head =
                    new StringBuilder(StringUtils.WEEK_UNIT).append(dayOfWeek).append(month).append(StringUtils.SLASH_SEPARATOR)
                        .append(day).toString();
                headList.add(head);
            }
        }
        else
        {
            // 获取缓存中低温需求提报时间对应的周对象，低温需求提报周为自然周，不能使用认养的日历周对象缓存
            int weekSize = 7;
            for (int i = 0; i < bizDateValueList.size(); )
            {
                String beginBizDateValue = bizDateValueList.get(i);
                i = i + weekSize;
                String endBizDateValue = bizDateValueList.get(i - 1);
                String beginDay = DateUtils.formatTime(DateUtils.parseDate(beginBizDateValue, DateUtils.YMD), DateUtils.MD_SLASH);
                String endDay = DateUtils.formatTime(DateUtils.parseDate(endBizDateValue, DateUtils.YMD), DateUtils.MD_SLASH);
                // 一周加一个动态表头元素
                String head = beginDay + StringUtils.TO_SEPARATOR + endDay;
                headList.add(head);
            }
        }

        return headList;
    }

    /**
     *
     * @Description 查询低温需求提报表头下拉列表
     * @param coldDemandReportDto
     * @return List<ColdDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月15日 14:58
     */
    @Override
    public List<ColdDemandReportDto> queryColdDemandReportHeadSelect(ColdDemandReportDto coldDemandReportDto) throws Exception
    {
        String tableSuffix = StringUtils.substring(coldDemandReportDto.getRollingVersion(), 3, 9);
        coldDemandReportDto.setTableSuffix(tableSuffix);
        GroupColumnEnum groupColumnEnum = coldDemandReportDto.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        coldDemandReportDto.setGroupColumn(groupColumn);
        coldDemandReportDto.setSortColumn(sortColumn);

        // 数据权限
        generateDataScope(coldDemandReportDto);

        List<ColdDemandReportDto> result = coldDemandReportDao.queryColdDemandReportHeadSelect(coldDemandReportDto);
        return result;
    }

    /**
     *
     * @Description 分组聚合查询低温需求提报列表
     * @param coldDemandReportDto
     * @return List<ColdDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月01日 16:16
     */
    @Override
    public List<ColdDemandReportDto> queryColdDemandReportListGroupBy(ColdDemandReportDto coldDemandReportDto) throws Exception
    {
        // 临时保存时间粒度，永远查询最小粒度（WEEK），再根据查询条件的时间粒度计算动态字段分组聚合逻辑
        BizDateTypeEnum bizDateType = coldDemandReportDto.getBizDateType();
        coldDemandReportDto.setBizDateType(BizDateTypeEnum.DAY);
        String tableSuffix = StringUtils.substring(coldDemandReportDto.getRollingVersion(), 3, 9);
        coldDemandReportDto.setTableSuffix(tableSuffix);

        // 数据权限
        generateDataScope(coldDemandReportDto);

        // 分组查询
        List<GroupColumnEnum> groupColumnEnumList = coldDemandReportDto.getGroupColumnList();
        List<ColdDemandReportDto> dataList = new ArrayList<>();
        StringBuilder groupColumnSB = new StringBuilder();
        StringBuilder sortColumnSB = new StringBuilder();

        for (GroupColumnEnum groupColumnEnum : groupColumnEnumList)
        {
            String groupColumn = groupColumnSB.append(groupColumnEnum.getColumnName()).toString();
            String sortColumn = sortColumnSB.append(groupColumnEnum.getSortColumn().getColumnName()).toString();
            coldDemandReportDto.setGroupColumn(groupColumn);
            coldDemandReportDto.setSortColumn(sortColumn);
            List<ColdDemandReportDto> list = coldDemandReportDao.queryColdDemandReportGroupList(coldDemandReportDto);
            if (CollectionUtils.isNotEmpty(list))
            {
                dataList.addAll(list);
            }

            groupColumnSB.append(StringUtils.COMMA_SEPARATOR);
            sortColumnSB.append(StringUtils.COMMA_SEPARATOR);
        }

        // 获取低温需求提报时间
        List<String> bizDateValueList = coldDemandReportDao.queryColdDemandReportDateList(coldDemandReportDto);
        // 动态表头
        List<String> headList = new ArrayList<>();
        Map<String, String> headDateMap = new HashMap<>();
        if (BizDateTypeEnum.DAY.equals(bizDateType))
        {
            for (String bizDateValue : bizDateValueList)
            {
                int dayOfWeek = DateUtils.getDayWeek(bizDateValue, DateUtils.YMD);
                int month = Integer.valueOf(StringUtils.substring(bizDateValue, 4, 6));
                int day = Integer.valueOf(StringUtils.substring(bizDateValue, 6));
                String head =
                    new StringBuilder(StringUtils.WEEK_UNIT).append(dayOfWeek).append(month).append(StringUtils.SLASH_SEPARATOR)
                        .append(day).toString();
                headList.add(head);
                headDateMap.put(bizDateValue, head);
            }
        }
        else
        {
            // 获取缓存中低温需求提报时间对应的周对象，低温需求提报周为自然周，不能使用认养的日历周对象缓存
            int weekSize = 7;
            for (int i = 0; i < bizDateValueList.size(); )
            {
                String beginBizDateValue = bizDateValueList.get(i);
                i = i + weekSize;
                String endBizDateValue = bizDateValueList.get(i - 1);
                String beginDay = DateUtils.formatTime(DateUtils.parseDate(beginBizDateValue, DateUtils.YMD), DateUtils.MD_SLASH);
                String endDay = DateUtils.formatTime(DateUtils.parseDate(endBizDateValue, DateUtils.YMD), DateUtils.MD_SLASH);
                // 一周加一个动态表头元素
                String head = beginDay + StringUtils.TO_SEPARATOR + endDay;
                headList.add(head);
            }
            for (int i = 0; i < bizDateValueList.size(); i++)
            {
                String bizDateValue = bizDateValueList.get(i);
                int index = Math.floorDiv(i, weekSize);
                // 每天都写动态表头映射关系
                headDateMap.put(bizDateValue, headList.get(index));
            }
        }

        for (ColdDemandReportDto coldDemandReport : dataList)
        {
            coldDemandReport.setDataMap(new HashMap<>());
            List<ChannelDemandReportDataVo> reportDataList = JSON.parseArray(coldDemandReport.getData(), ChannelDemandReportDataVo.class);
            for (ChannelDemandReportDataVo reportData : reportDataList)
            {
                String headKey = headDateMap.get(reportData.getBizDateValue());

                // 周粒度，可能会包含
                if (coldDemandReport.getDataMap().containsKey(headKey))
                {
                    ChannelDemandReportDataVo temp = coldDemandReport.getDataMap().get(headKey);
                    BigDecimal bg1 = new BigDecimal(temp.getOrderNum());
                    BigDecimal bg2 = new BigDecimal(reportData.getOrderNum());
                    BigDecimal orderNumBigDecimal = bg1.add(bg2);
                    temp.setOrderNum(orderNumBigDecimal.doubleValue());
                    coldDemandReport.getDataMap().put(headKey, temp);
                }
                else
                {
                    coldDemandReport.getDataMap().put(headKey, reportData);
                }
            }
            coldDemandReport.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 分页查询低温需求提报列表
     * @param condition
     * @return PageInfo<ColdDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月01日 16:17
     */
    @Override
    public PageInfo<ColdDemandReportDto> queryColdDemandReportDataPage(PageCondition<ColdDemandReportDto> condition) throws Exception
    {
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        ColdDemandReportDto coldDemandReportDto = condition.getCondition();
        // 临时保存时间粒度，永远查询最小粒度（WEEK），再根据查询条件的时间粒度计算动态字段分组聚合逻辑
        BizDateTypeEnum bizDateType = coldDemandReportDto.getBizDateType();
        coldDemandReportDto.setBizDateType(BizDateTypeEnum.DAY);
        String tableSuffix = StringUtils.substring(coldDemandReportDto.getRollingVersion(), 3, 9);
        coldDemandReportDto.setTableSuffix(tableSuffix);

        // 数据权限
        generateDataScope(coldDemandReportDto);

        PageHelper.startPage(pageNum, pageSize);

        List<ColdDemandReportDto> dataList = coldDemandReportDao.queryColdDemandReportDataList(coldDemandReportDto);

        // 查询包含当前时间和本次数据的产品锁定期
        List<String> skuCodeList = dataList.stream().map(ColdDemandReportDto::getSkuCode).distinct().collect(Collectors.toList());
        List<String> channelCodeList = dataList.stream().map(ColdDemandReportDto::getLv2ChannelCode).distinct().collect(Collectors.toList());
        SkuLockVo skuLockVo = new SkuLockVo();
        skuLockVo.setSkuCodes(skuCodeList);
        skuLockVo.setLv2ChannelCodes(channelCodeList);
        String currentDay = DateUtils.getDate(DateUtils.YMD);
        skuLockVo.setLockStartDate(currentDay);
        skuLockVo.setLockEndDate(currentDay);
        List<SkuLockVo> skuLockList = skuLockDao.queryLockList(skuLockVo);
        Map<String, List<SkuLockVo>> skuLockMap = Collections.EMPTY_MAP;
        if (CollectionUtils.isNotEmpty(skuLockList))
        {
            skuLockMap = skuLockList.stream().collect(Collectors.groupingBy(SkuLockVo::getSkuChannelCode));
        }

        // 获取低温需求提报时间
        List<String> bizDateValueList = coldDemandReportDao.queryColdDemandReportDateList(coldDemandReportDto);
        // 动态表头
        List<String> headList = new ArrayList<>();
        Map<String, String> headDateMap = new HashMap<>();
        if (BizDateTypeEnum.DAY.equals(bizDateType))
        {
            for (String bizDateValue : bizDateValueList)
            {
                int dayOfWeek = DateUtils.getDayWeek(bizDateValue, DateUtils.YMD);
                int month = Integer.valueOf(StringUtils.substring(bizDateValue, 4, 6));
                int day = Integer.valueOf(StringUtils.substring(bizDateValue, 6));
                String head =
                    new StringBuilder(StringUtils.WEEK_UNIT).append(dayOfWeek).append(month).append(StringUtils.SLASH_SEPARATOR)
                        .append(day).toString();
                headList.add(head);
                headDateMap.put(bizDateValue, head);
            }
        }
        else
        {
            // 获取缓存中低温需求提报时间对应的周对象，低温需求提报周为自然周，不能使用认养的日历周对象缓存
            int weekSize = 7;
            for (int i = 0; i < bizDateValueList.size(); )
            {
                String beginBizDateValue = bizDateValueList.get(i);
                i = i + weekSize;
                String endBizDateValue = bizDateValueList.get(i - 1);
                String beginDay = DateUtils.formatTime(DateUtils.parseDate(beginBizDateValue, DateUtils.YMD), DateUtils.MD_SLASH);
                String endDay = DateUtils.formatTime(DateUtils.parseDate(endBizDateValue, DateUtils.YMD), DateUtils.MD_SLASH);
                // 一周加一个动态表头元素
                String head = beginDay + StringUtils.TO_SEPARATOR + endDay;
                headList.add(head);
            }
            for (int i = 0; i < bizDateValueList.size(); i++)
            {
                String bizDateValue = bizDateValueList.get(i);
                int index = Math.floorDiv(i, weekSize);
                // 每天都写动态表头映射关系
                headDateMap.put(bizDateValue, headList.get(index));
            }
        }

        String currentDate = DateUtils.getDate(DateUtils.YMD);
        for (ColdDemandReportDto coldDemandReport : dataList)
        {
            String skuChannelCode =
                new StringBuilder(coldDemandReport.getSkuCode()).append(StringUtils.DATE_SEPARATOR).append(coldDemandReport.getLv2ChannelCode()).toString();
            List<SkuLockVo> lockDateList = skuLockMap.get(skuChannelCode);

            coldDemandReport.setDataMap(new HashMap<>());
            List<ChannelDemandReportDataVo> reportDataList = JSON.parseArray(coldDemandReport.getData(), ChannelDemandReportDataVo.class);
            for (ChannelDemandReportDataVo reportData : reportDataList)
            {
                String headKey = headDateMap.get(reportData.getBizDateValue());

                // 周粒度，可能会包含
                if (coldDemandReport.getDataMap().containsKey(headKey))
                {
                    ChannelDemandReportDataVo temp = coldDemandReport.getDataMap().get(headKey);
                    BigDecimal bg1 = new BigDecimal(temp.getOrderNum());
                    BigDecimal bg2 = new BigDecimal(reportData.getOrderNum());
                    BigDecimal orderNumBigDecimal = bg1.add(bg2);
                    temp.setOrderNum(orderNumBigDecimal.doubleValue());
                    coldDemandReport.getDataMap().put(headKey, temp);
                }
                else
                {
                    // 产品锁定期
                    if (CollectionUtils.isNotEmpty(lockDateList))
                    {
                        long num = lockDateList.stream().filter(lockDate -> {
                            return StringUtils.compare(lockDate.getLockEndDate(), reportData.getBizDateValue()) >= 0 &&
                                StringUtils.compare(reportData.getBizDateValue(), lockDate.getLockStartDate()) >= 0;
                        }).count();
                        if (num > 0)
                        {
                            reportData.setLockFlag(true);
                        }
                    }
                    // 自然锁定期
                    if (StringUtils.compare(currentDate, reportData.getBizDateValue()) > 0)
                    {
                        reportData.setLockFlag(true);
                    }

                    coldDemandReport.getDataMap().put(headKey, reportData);
                }
            }
            coldDemandReport.setData(null);
        }

        PageInfo<ColdDemandReportDto> result = new PageInfo<>(dataList);
        return result;
    }

    /**
     *
     * @Description 封装数据权限
     * @param coldDemandReportDto
     * <AUTHOR>
     * @date 2024年02月01日 17:35
     */
    @Override
    public void generateDataScope(ColdDemandReportDto coldDemandReportDto)
    {
        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        List<String> categoryIdList = currentAccount.getCategoryIdList();
        List<String> channelIdList = currentAccount.getChannelIdList();
        String noDataScope = "没有权限";
        coldDemandReportDto.setCategoryCodes(
            CollectionUtils.isEmpty(categoryIdList) ? noDataScope : categoryIdList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
        coldDemandReportDto.setChannelCodes(
            CollectionUtils.isEmpty(channelIdList) ? noDataScope : channelIdList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
    }
}
