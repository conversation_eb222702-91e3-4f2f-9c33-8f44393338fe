package cn.aliyun.ryytn.modules.demand.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.common.utils.page.PageUtils;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqDeliveryOrderRateDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.CharsetKit;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.ChannelDemandReportService;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.dao.ChannelDemandReportDao;
import cn.aliyun.ryytn.modules.demand.dao.SkuLockDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWeekListReqVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 渠道需求提报接口实现类
 * <AUTHOR>
 * @date 2023/10/24 10:24
 */
@Slf4j
@Service
public class ChannelDemandReportServiceImpl implements ChannelDemandReportService
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private CalendarService calendarService;

    @Autowired
    private DataqChannelDemandReportDao dataqChannelDemandReportDao;

    @Autowired
    private ChannelDemandReportDao channelDemandReportDao;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SkuLockDao skuLockDao;


    /**
     *
     * @Description 查询渠道需求提报版本列表
     * @param queryChannelDemandReportVersionListReqVo
     * @return List<ChannelDemandReportVersionVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:36
     */
    @Override
    public List<ChannelDemandReportVersionVo> queryChannelDemandReportVersionList(ChannelDemandReportVersionVo channelDemandReportVersionVo)
        throws Exception
    {
//        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_VERSION_LIST"));
//        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, channelDemandReportVersionVo);
//        JSONArray jsonArray = (JSONArray) dataqResult.getData();
//        if (CollectionUtils.isEmpty(jsonArray))
//        {
//            return null;
//        }
//
//        List<ChannelDemandReportVersionVo> result = jsonArray.toJavaList(ChannelDemandReportVersionVo.class);
//
//        result = result.stream().sorted(Comparator.comparing(ChannelDemandReportVersionVo::getRollingVersion).reversed()).collect(Collectors.toList());

        List<ChannelDemandReportVersionVo> result = channelDemandReportDao.queryChannelDemandReportVersionList(channelDemandReportVersionVo);

        if (CollectionUtils.isNotEmpty(result))
        {
            // 将当前周的版本，设置为默认展示版本
            QueryWeekListReqVo queryWeekListReqVo = new QueryWeekListReqVo();
            queryWeekListReqVo.setClearDate(DateUtils.getDate(DateUtils.YMD));
            List<DataqWeek> dataqWeekList = calendarService.queryWeekList(queryWeekListReqVo);
            if (CollectionUtils.isNotEmpty(dataqWeekList))
            {
                DataqWeek dataqWeek = dataqWeekList.get(0);
                result.forEach(item -> {
                    if (StringUtils.contains(item.getRollingVersion(), dataqWeek.getYearMonthWeek()))
                    {
                        item.setDefaultFlag(true);
                    }
                });
            }
        }

        return result;
    }

    /**
     *
     * @Description 查询渠道需求提报版本
     * @param channelDemandReportVersionDto
     * @return ChannelDemandReportVersionDto
     * @throws Exception
     * <AUTHOR>
     * @date 2024年02月20日 16:33
     */
    @Override
    public ChannelDemandReportVersionDto queryChannelDemandReportVersion(ChannelDemandReportVersionDto channelDemandReportVersionDto)
        throws Exception
    {
        ChannelDemandReportVersionDto result = channelDemandReportDao.queryChannelDemandReportVersion(channelDemandReportVersionDto.getRollingVersion());
        return result;
    }

    /**
     *
     * @Description 查询渠道需求提报数据列表
     * @param queryChannelDemandReportListReqVo
     * @return BaseTable<List < QueryChannelDemandReportListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月15日 14:13
     */
    @Override
    public BaseTable<List<QueryChannelDemandReportListRspVo>> queryChannelDemandReportList(
        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception
    {
        BaseTable<List<QueryChannelDemandReportListRspVo>> baseTable = new BaseTable();

        // 获取查询表数据接口地址
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_JSON"));
        DataqResult<?> reportResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandReportListReqVo);
        JSONArray jsonArray = (JSONArray) reportResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return baseTable;
        }
        List<QueryChannelDemandReportListRspVo> dataList = jsonArray.toJavaList(QueryChannelDemandReportListRspVo.class);

        // 如果上一版本入参不为空，获取上一版本提报数据
        Map<QueryChannelDemandReportListRspVo, List<ChannelDemandReportDataVo>> lastVersionReportMap = new HashMap<>();
        if (StringUtils.isNotEmpty(queryChannelDemandReportListReqVo.getLastRollingVersion()))
        {
            // 将版本字段值修改为上一版本
            queryChannelDemandReportListReqVo.setRollingVersion(queryChannelDemandReportListReqVo.getLastRollingVersion());
            // 查询上一版本提报数据
            DataqResult<?> lastVersionReportResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandReportListReqVo);
            jsonArray = (JSONArray) lastVersionReportResult.getData();
            if (CollectionUtils.isNotEmpty(jsonArray))
            {
                // 上一版本提报数据转换为Map，key：QueryChannelDemandReportListRspVo对象，后续根据equals和hashCode直接获取对应动态字段，value：动态字段值Map
                List<QueryChannelDemandReportListRspVo> lastVersionReportList = (List<QueryChannelDemandReportListRspVo>) lastVersionReportResult.getData();
                lastVersionReportMap = lastVersionReportList.stream().collect(Collectors.toMap(Function.identity(),
                    QueryChannelDemandReportListRspVo::getDataList, (key1, key2) -> key1));
            }
        }

        // 解析动态字段key，封装表头
        List<String> trendsKeySet = dataList.get(0).getDataList().stream().map(ChannelDemandReportDataVo::getBizDateValue).collect(Collectors.toList());
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, trendsKeySet);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        List<String> headList = new ArrayList<>();
        // dataq返回动态字段格式：yyyyMMdd
        // 业务返回周粒度动态字段格式：12月W1(01-03)
        if (BizDateTypeEnum.WEEK.equals(queryChannelDemandReportListReqVo.getBizDateType()))
        {
            for (String trendsKey : trendsKeySet)
            {
                DataqWeek dataqWeek = dataqWeekMap.get(trendsKey);
                headList.add(dataqWeek.getWeekLabel());
            }
        }
        // 业务返回月粒度动态字段格式：12月
        else if (BizDateTypeEnum.MONTH.equals(queryChannelDemandReportListReqVo.getBizDateType()))
        {
            for (String trendsKey : trendsKeySet)
            {
                DataqWeek dataqWeek = dataqWeekMap.get(trendsKey);
                headList.add(dataqWeek.getMonthLabel());
            }
            headList = headList.stream().distinct().collect(Collectors.toList());
        }
        else
        {
            return null;
        }

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (QueryChannelDemandReportListRspVo queryChannelDemandReportListRspVo : dataList)
        {
            // 获取上一版提报数据
            List<ChannelDemandReportDataVo> lastVesionDataList = lastVersionReportMap.get(queryChannelDemandReportListRspVo);
            Map<String, Double> lastVersionData = Collections.EMPTY_MAP;
            if (CollectionUtils.isNotEmpty(lastVesionDataList))
            {
                lastVersionData = lastVesionDataList.stream()
                    .collect(Collectors.toMap(ChannelDemandReportDataVo::getBizDateValue, ChannelDemandReportDataVo::getOrderNum, (key1, key2) -> key1));
            }

            double total = 0.0d;
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (ChannelDemandReportDataVo channelDemandReportDataVo : queryChannelDemandReportListRspVo.getDataList())
            {
                String key = channelDemandReportDataVo.getBizDateValue();
                // 获取上一版本对应动态字段的数值，上一版本可能没有此动态字段，则为空
                Double lastVersionOrderNum = lastVersionData.get(key);
                channelDemandReportDataVo.setLastOrderNum(lastVersionOrderNum);
                if (Objects.isNull(lastVersionOrderNum))
                {
                    channelDemandReportDataVo.setDeviationRadio(1d);
                }
                else
                {
                    BigDecimal orderNum = new BigDecimal(channelDemandReportDataVo.getOrderNum());
                    BigDecimal lastOrderNum = new BigDecimal(channelDemandReportDataVo.getLastOrderNum());
                    channelDemandReportDataVo.setDeviationRadio(orderNum.subtract(lastOrderNum).divide(lastOrderNum, 2, RoundingMode.HALF_UP).doubleValue());
                }

                // 周粒度直接加入dataMap
                if (BizDateTypeEnum.WEEK.equals(queryChannelDemandReportListReqVo.getBizDateType()))
                {
                    String head = dataqWeekMap.get(key).getWeekLabel();
                    queryChannelDemandReportListRspVo.getDataMap().put(head, channelDemandReportDataVo);
                }
                // 月粒度按月求和
                else
                {
                    String head = dataqWeekMap.get(key).getMonthLabel();
                    if (queryChannelDemandReportListRspVo.getDataMap().containsKey(head))
                    {
                        ChannelDemandReportDataVo tmpChannelDemandReportDataVo = queryChannelDemandReportListRspVo.getDataMap().get(head);
                        BigDecimal monthTotal = new BigDecimal(tmpChannelDemandReportDataVo.getOrderNum());
                        BigDecimal orderNum = new BigDecimal(channelDemandReportDataVo.getOrderNum());
                        tmpChannelDemandReportDataVo.setOrderNum(monthTotal.add(orderNum).doubleValue());
                        queryChannelDemandReportListRspVo.getDataMap().put(head, tmpChannelDemandReportDataVo);
                    }
                    else
                    {
                        queryChannelDemandReportListRspVo.getDataMap().put(head, channelDemandReportDataVo);
                    }
                }

                // 计算总数
                Double currentValue = channelDemandReportDataVo.getOrderNum();
                total += currentValue;
            }

            queryChannelDemandReportListRspVo.setTotal(total);
            queryChannelDemandReportListRspVo.setDataList(null);
        }
        baseTable.setHeadArray(headList);
        baseTable.setList(dataList);

        return baseTable;
    }

    /**
     *
     * @Description 查询渠道需求提报动态表头
     * @param queryChannelDemandReportListReqVo
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:16
     */
    @Override
    public List<String> queryChannelDemandReportHeadList(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception
    {
        // 临时保存时间粒度，永远查询最小粒度（WEEK），再根据查询条件的时间粒度计算动态字段分组聚合逻辑
        BizDateTypeEnum bizDateType = queryChannelDemandReportListReqVo.getBizDateType();
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        queryChannelDemandReportListReqVo.setIsModify(0);

        List<String> bizDateValueList = dataqChannelDemandReportDao.queryChannelDemandReportHeadList(queryChannelDemandReportListReqVo);
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        List<String> headList = new ArrayList<>();
        // dataq返回动态字段格式：yyyyMMdd
        // 业务返回周粒度动态字段格式：12月W1(01-03)
        if (BizDateTypeEnum.WEEK.equals(bizDateType))
        {
            for (String bizDateValue : bizDateValueList)
            {
                DataqWeek dataqWeek = dataqWeekMap.get(bizDateValue);
                headList.add(dataqWeek.getWeekLabel());
            }
        }
        // 业务返回月粒度动态字段格式：12月
        else if (BizDateTypeEnum.MONTH.equals(bizDateType))
        {
            for (String bizDateValue : bizDateValueList)
            {
                DataqWeek dataqWeek = dataqWeekMap.get(bizDateValue);
                if (!headList.contains(dataqWeek.getMonthLabel()))
                {
                    headList.add(dataqWeek.getMonthLabel());
                }
            }
        }
        else
        {
            return null;
        }

        return headList;
    }

    /**
     *
     * @Description 查询渠道需求提报表头下拉列表
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportGroupListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @Override
    public List<QueryChannelDemandReportGroupListRspVo> queryChannelDemandReportHeadSelect(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
        throws Exception
    {
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        GroupColumnEnum groupColumnEnum = queryChannelDemandReportListReqVo.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        queryChannelDemandReportListReqVo.setGroupColumn(groupColumn);
        queryChannelDemandReportListReqVo.setSortColumn(sortColumn);
        queryChannelDemandReportListReqVo.setIsModify(0);

        // 数据权限
        generateDataScope(queryChannelDemandReportListReqVo);


        List<QueryChannelDemandReportGroupListRspVo> result = dataqChannelDemandReportDao.queryChannelDemandReportHeadSelect(queryChannelDemandReportListReqVo);

        return result;
    }

    /**
     *
     * @Description 封装数据权限
     * @param queryChannelDemandReportListReqVo
     * <AUTHOR>
     * @date 2024年02月01日 17:35
     */
    @Override
    public void generateDataScope(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
    {
        if (Objects.isNull(queryChannelDemandReportListReqVo.getDataScope()) || !queryChannelDemandReportListReqVo.getDataScope())
        {
            return;
        }

        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        List<String> categoryIdList = currentAccount.getCategoryIdList();
        List<String> channelIdList = currentAccount.getChannelIdList();
        String noDataScope = "没有权限";
        queryChannelDemandReportListReqVo.setCategoryCodes(CollectionUtils.isEmpty(categoryIdList) ? noDataScope :
            categoryIdList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
        queryChannelDemandReportListReqVo.setChannelCodes(CollectionUtils.isEmpty(channelIdList) ? noDataScope :
            channelIdList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));

    }

    /**
     *
     * @Description 查询渠道需求提报分组聚合数据列表
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportGroupListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @Override
    public List<QueryChannelDemandReportGroupListRspVo> queryChannelDemandReportListGroupBy(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
        throws Exception
    {
        // 临时保存时间粒度，永远查询最小粒度（WEEK），再根据查询条件的时间粒度计算动态字段分组聚合逻辑
        BizDateTypeEnum bizDateType = queryChannelDemandReportListReqVo.getBizDateType();
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        queryChannelDemandReportListReqVo.setIsModify(0);

        // 数据权限
        generateDataScope(queryChannelDemandReportListReqVo);

        // 分组查询
        List<GroupColumnEnum> groupColumnEnumList = queryChannelDemandReportListReqVo.getGroupColumnList();
        List<QueryChannelDemandReportGroupListRspVo> dataList = new ArrayList<>();
        StringBuilder groupColumnSB = new StringBuilder();
        StringBuilder sortColumnSB = new StringBuilder();
        for (GroupColumnEnum groupColumnEnum : groupColumnEnumList)
        {
            String groupColumn = groupColumnSB.append(groupColumnEnum.getColumnName()).toString();
            String sortColumn = sortColumnSB.append(groupColumnEnum.getSortColumn().getColumnName()).toString();
            queryChannelDemandReportListReqVo.setGroupColumn(groupColumn);
            queryChannelDemandReportListReqVo.setSortColumn(sortColumn);
            List<QueryChannelDemandReportGroupListRspVo> list =
                dataqChannelDemandReportDao.queryChannelDemandReportGroupList(queryChannelDemandReportListReqVo);
            if (CollectionUtils.isNotEmpty(list))
            {
                dataList.addAll(list);
            }

            groupColumnSB.append(StringUtils.COMMA_SEPARATOR);
            sortColumnSB.append(StringUtils.COMMA_SEPARATOR);
        }

        // 解析动态字段key，封装表头
        List<String> bizDateValueList = dataqChannelDemandReportDao.queryChannelDemandReportHeadList(queryChannelDemandReportListReqVo);
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (QueryChannelDemandReportGroupListRspVo queryChannelDemandReportGroupListRspVo : dataList)
        {
            List<ChannelDemandReportDataVo> reportDataList = JSON.parseArray(queryChannelDemandReportGroupListRspVo.getData(), ChannelDemandReportDataVo.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (ChannelDemandReportDataVo channelDemandReportDataVo : reportDataList)
            {
                String key = channelDemandReportDataVo.getBizDateValue();

                // 周粒度直接加入dataMap
                if (BizDateTypeEnum.WEEK.equals(bizDateType))
                {
                    String head = dataqWeekMap.get(key).getWeekLabel();
                    queryChannelDemandReportGroupListRspVo.getDataMap().put(head, channelDemandReportDataVo);
                }
                // 月粒度按月求和
                else
                {
                    String head = dataqWeekMap.get(key).getMonthLabel();
                    if (queryChannelDemandReportGroupListRspVo.getDataMap().containsKey(head))
                    {
                        ChannelDemandReportDataVo tmpChannelDemandReportDataVo = queryChannelDemandReportGroupListRspVo.getDataMap().get(head);
                        BigDecimal monthTotal = new BigDecimal(queryChannelDemandReportGroupListRspVo.getDataMap().get(head).getOrderNum());
                        BigDecimal orderNum = new BigDecimal(channelDemandReportDataVo.getOrderNum());
                        Double value = monthTotal.add(orderNum).doubleValue();
                        tmpChannelDemandReportDataVo.setOrderNum(value);
                        queryChannelDemandReportGroupListRspVo.getDataMap().put(head, tmpChannelDemandReportDataVo);
                    }
                    else
                    {
                        queryChannelDemandReportGroupListRspVo.getDataMap().put(head, channelDemandReportDataVo);
                    }
                }
            }
            queryChannelDemandReportGroupListRspVo.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 分页查询渠道需求提报明细数据列表
     * @param condition
     * @return PageInfo<QueryChannelDemandReportListRspVo>
     * <AUTHOR>
     * @date 2023年12月20日 14:28
     */
    @Override
    public PageInfo<QueryChannelDemandReportListRspVo> queryChannelDemandReportDataPage(PageCondition<QueryChannelDemandReportListReqVo> condition)
        throws Exception
    {

        long start0 = System.currentTimeMillis();
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo = condition.getCondition();
        // 临时保存时间粒度，永远查询最小粒度（WEEK），再根据查询条件的时间粒度计算动态字段分组聚合逻辑
        BizDateTypeEnum bizDateType = queryChannelDemandReportListReqVo.getBizDateType();
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        queryChannelDemandReportListReqVo.setIsModify(0);
        queryChannelDemandReportListReqVo.setTableSuffix((StringUtils.isBlank(queryChannelDemandReportListReqVo.getRollingVersion()))?"":queryChannelDemandReportListReqVo.getRollingVersion().substring(3,9));

        // 数据权限
        generateDataScope(queryChannelDemandReportListReqVo);

        // dataq接口效率过低，修改为直接读数据库
        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        PageHelper.startPage(pageNum, pageSize);
        // fix: 1556 增加偏差率筛选条件
        List<QueryChannelDemandReportListRspVo> keyList = null;

        keyList = dataqChannelDemandReportDao.queryChannelDemandReportDataKeyList(queryChannelDemandReportListReqVo);
        PageInfo pageInfo = new PageInfo(keyList);
        if (CollectionUtils.isEmpty(keyList))
        {
            return pageInfo;
        }

        int pageTotal = Integer.parseInt(String.valueOf(pageInfo.getTotal()));

        queryChannelDemandReportListReqVo.setKeyList(keyList);

        PageHelper.clearPage();
        long start1 = System.currentTimeMillis();
        log.info("耗时1:{}s",(start1-start0)/1000);
        List<QueryChannelDemandReportListRspVo> dataList = dataqChannelDemandReportDao.queryChannelDemandReportDataJsonList(queryChannelDemandReportListReqVo);
        long start12 = System.currentTimeMillis();
        log.info("耗时12:{}s",(start12-start1)/1000);
        // 解析动态字段key，封装表头
        List<String> bizDateValueList = dataqChannelDemandReportDao.queryChannelDemandReportHeadList(queryChannelDemandReportListReqVo);
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);
        long start2 = System.currentTimeMillis();
        log.info("耗时2:{}s",(start2-start1)/1000);
        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));
        long start3 = System.currentTimeMillis();
        log.info("耗时3:{}s",(start3-start2)/1000);
        // fix: 1556 增加偏差率筛选条件，上一版本数据在当前版本记录中冗余保存了，直接查询
//        // 如果上一版本入参不为空，获取上一版本提报数据
//        Map<QueryChannelDemandReportListRspVo, List<ChannelDemandReportDataVo>> lastVersionReportMap = new HashMap<>();
//        if (StringUtils.isNotEmpty(queryChannelDemandReportListReqVo.getLastRollingVersion()))
//        {
//            // 将版本字段值修改为上一版本
//            queryChannelDemandReportListReqVo.setRollingVersion(queryChannelDemandReportListReqVo.getLastRollingVersion());
//            List<QueryChannelDemandReportListRspVo> lastVersionReportList =
//                dataqChannelDemandReportDao.queryChannelDemandReportDataJsonList(queryChannelDemandReportListReqVo);
//            if (CollectionUtils.isNotEmpty(lastVersionReportList))
//            {
//                lastVersionReportMap = lastVersionReportList.stream().collect(Collectors.toMap(Function.identity(),
//                    QueryChannelDemandReportListRspVo::getDataList, (key1, key2) -> key1));
//            }
//        }

        // 查询包含当前时间和本次数据的产品锁定期
        List<String> skuCodeList = dataList.stream().map(QueryChannelDemandReportListRspVo::getSkuCode).distinct().collect(Collectors.toList());
        List<String> channelCodeList = dataList.stream().map(QueryChannelDemandReportListRspVo::getLv2ChannelCode).distinct().collect(Collectors.toList());
        SkuLockVo skuLockVo = new SkuLockVo();
        skuLockVo.setSkuCodes(skuCodeList);
        skuLockVo.setLv2ChannelCodes(channelCodeList);
        String currentDay = DateUtils.getDate(DateUtils.YMD);
        skuLockVo.setLockStartDate(currentDay);
        skuLockVo.setLockEndDate(currentDay);
        List<SkuLockVo> skuLockList = skuLockDao.queryLockList(skuLockVo);
        Map<String, List<SkuLockVo>> skuLockMap = Collections.EMPTY_MAP;
        if (CollectionUtils.isNotEmpty(skuLockList))
        {
            skuLockMap = skuLockList.stream().collect(Collectors.groupingBy(SkuLockVo::getSkuChannelCode));
        }

        String currentDate = DateUtils.getDate(DateUtils.YMD);

//
//        String skuCodes = dataList.stream().map(QueryChannelDemandReportListRspVo::getSkuCode).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//        String lv3ChannelCodes = dataList.stream().map(QueryChannelDemandReportListRspVo::getLv3ChannelCode).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
//        List<String> historyWeekStartList = new ArrayList<>();
//        dataqWeekMap.forEach((k,v)->{
//            if (StringUtils.compare(v.getFsclWeekEnd(), currentDate) < 0)
//            {
//                historyWeekStartList.add(v.getFsclWeekStart());
//            }
//        });
//        ChannelDemandRealityDataVo queryChannelDemandRealityDataVo = new ChannelDemandRealityDataVo();
//        queryChannelDemandRealityDataVo.setDimComb("LV3_CHANNEL_CODE+SKU+LV3_CATEGORY_CODE+WEEK");
//        queryChannelDemandRealityDataVo.setBizDates(historyWeekStartList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
//        queryChannelDemandRealityDataVo.setSkuCodes(skuCodes);
//        queryChannelDemandRealityDataVo.setLv3ChannelCodes(lv3ChannelCodes);
//
//        List<ChannelDemandRealityDataVo> historyRealityDataList =dataqDeliveryOrderRateDao.queryHistoryRealityDeliveryOrderByWeek(queryChannelDemandRealityDataVo);
//        Map<ChannelDemandRealityDataVo,ChannelDemandRealityDataVo> realityDataVoMap =  historyRealityDataList.stream().collect(Collectors.toMap(Function.identity(),v->v));

        long start4 = System.currentTimeMillis();
        log.info("耗时4:{}s",(start4-start3)/1000);
        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (QueryChannelDemandReportListRspVo queryChannelDemandReportListRspVo : dataList)
        {
            // fix: 1556 增加偏差率筛选条件，上一版本数据在当前版本记录中冗余保存了，直接查询
            // 获取上一版提报数据
//            List<ChannelDemandReportDataVo> lastVesionDataList = lastVersionReportMap.get(queryChannelDemandReportListRspVo);
//            Map<String, Double> lastVersionData = Collections.EMPTY_MAP;
//            if (CollectionUtils.isNotEmpty(lastVesionDataList))
//            {
//                lastVersionData = lastVesionDataList.stream()
//                    .collect(Collectors.toMap(ChannelDemandReportDataVo::getBizDateValue, ChannelDemandReportDataVo::getOrderNum, (key1, key2) -> key1));
//            }

            String skuChannelCode =
                new StringBuilder(queryChannelDemandReportListRspVo.getSkuCode()).append(StringUtils.DATE_SEPARATOR)
                    .append(queryChannelDemandReportListRspVo.getLv2ChannelCode()).toString();
            List<SkuLockVo> lockDateList = skuLockMap.get(skuChannelCode);

            double total = 0.0d;
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (ChannelDemandReportDataVo channelDemandReportDataVo : queryChannelDemandReportListRspVo.getDataList())
            {
                String key = channelDemandReportDataVo.getBizDateValue();

                // 获取时间对应周对象
                DataqWeek dataqWeek = dataqWeekMap.get(key);

                // fix: 1556 增加偏差率筛选条件，上一版本数据在当前版本记录中冗余保存了，直接查询
                // 获取上一版本对应动态字段的数值，上一版本可能没有此动态字段，则为空
//                Double lastVersionOrderNum = lastVersionData.get(key);
//                channelDemandReportDataVo.setLastOrderNum(lastVersionOrderNum);
//                if (Objects.nonNull(lastVersionOrderNum))
//                {
//                    // 上一版本数据如果为0，偏差率固定为100%
//                    if (lastVersionOrderNum.equals(0d))
//                    {
//                        if (channelDemandReportDataVo.getOrderNum().equals(0d))
//                        {
//                            channelDemandReportDataVo.setDeviationRadio(0d);
//                        }
//                        else
//                        {
//                            channelDemandReportDataVo.setDeviationRadio(1d);
//                        }
//                    }
//                    else
//                    {
//                        BigDecimal orderNum = new BigDecimal(channelDemandReportDataVo.getOrderNum());
//                        BigDecimal lastOrderNum = new BigDecimal(channelDemandReportDataVo.getLastOrderNum());
//                        channelDemandReportDataVo.setDeviationRadio(orderNum.subtract(lastOrderNum).divide(lastOrderNum, 2,
//                            RoundingMode.HALF_UP).doubleValue());
//                    }
//                }

                // 周粒度直接加入dataMap
                if (BizDateTypeEnum.WEEK.equals(bizDateType))
                {
                    // 产品锁定期
                    if (CollectionUtils.isNotEmpty(lockDateList))
                    {
                        long num = lockDateList.stream().filter(lockDate -> {
                            return StringUtils.compare(lockDate.getLockEndDate(), dataqWeek.getFsclWeekEnd()) >= 0 &&
                                StringUtils.compare(dataqWeek.getFsclWeekStart(), lockDate.getLockStartDate()) >= 0;
                        }).count();
                        if (num > 0)
                        {
                            channelDemandReportDataVo.setLockFlag(true);
                        }
                    }
                    // 自然锁定期
                    if (StringUtils.compare(currentDate, dataqWeek.getFsclWeekEnd()) > 0)
                    {
                        channelDemandReportDataVo.setLockFlag(true);
                    }

                    String head = dataqWeekMap.get(key).getWeekLabel();


//                    ChannelDemandRealityDataVo searchChannelDemandRealityDataVo = new ChannelDemandRealityDataVo();
//                    searchChannelDemandRealityDataVo.setSkuCode(queryChannelDemandReportListRspVo.getSkuCode());
//                    searchChannelDemandRealityDataVo.setLv3ChannelCode(queryChannelDemandReportListRspVo.getLv3ChannelCode());
//                    searchChannelDemandRealityDataVo.setBizWeekDateStart(key);
//
//                    if(!Objects.isNull(realityDataVoMap.get(searchChannelDemandRealityDataVo))){
//                        channelDemandReportDataVo.setRealityOrderNum(realityDataVoMap.get(searchChannelDemandRealityDataVo).getRealityOrderNum());
//                    }else
//                    {
//                        channelDemandReportDataVo.setRealityOrderNum(0d);
//                    }
                    queryChannelDemandReportListRspVo.getDataMap().put(head, channelDemandReportDataVo);
                }
                // 月粒度按月求和
                else
                {
                    String head = dataqWeekMap.get(key).getMonthLabel();
                    if (queryChannelDemandReportListRspVo.getDataMap().containsKey(head))
                    {
                        ChannelDemandReportDataVo tmpChannelDemandReportDataVo = queryChannelDemandReportListRspVo.getDataMap().get(head);
                        BigDecimal monthTotal = new BigDecimal(tmpChannelDemandReportDataVo.getOrderNum());
//                        BigDecimal monthRealityOrderNum = new BigDecimal(tmpChannelDemandReportDataVo.getRealityOrderNum());
//                        BigDecimal realityOrderNum = new BigDecimal(channelDemandReportDataVo.getRealityOrderNum());
                        BigDecimal orderNum = new BigDecimal(channelDemandReportDataVo.getOrderNum());
                        tmpChannelDemandReportDataVo.setOrderNum(monthTotal.add(orderNum).doubleValue());
//                        tmpChannelDemandReportDataVo.setRealityOrderNum(monthRealityOrderNum.add(realityOrderNum).doubleValue());
                        queryChannelDemandReportListRspVo.getDataMap().put(head, tmpChannelDemandReportDataVo);
                    }
                    else
                    {
                        queryChannelDemandReportListRspVo.getDataMap().put(head, channelDemandReportDataVo);
                    }
                }

                // 计算总数
                Double currentValue = channelDemandReportDataVo.getOrderNum();
                total += currentValue;
            }

            queryChannelDemandReportListRspVo.setTotal(total);
            queryChannelDemandReportListRspVo.setDataList(null);
            queryChannelDemandReportListRspVo.setData(null);
        }
        long start5 = System.currentTimeMillis();
        log.info("耗时5:{}s",(start5-start4)/1000);
        PageInfo<QueryChannelDemandReportListRspVo> result = PageUtils.init(dataList, pageNum, pageSize, pageTotal);
//        PageInfo<QueryChannelDemandReportListRspVo> result = new PageInfo(dataList);
        return result;
    }

    /**
     *
     * @Description 查询渠道需求提报数据汇总
     * @param queryChannelDemandReportListReqVo
     * @return Map<String, Double>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月21日 14:50
     */
    @Override
    public Map<String, Double> queryChannelDemandReportSummary(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception
    {
        queryChannelDemandReportListReqVo.setIsModify(0);

        // 数据权限
        generateDataScope(queryChannelDemandReportListReqVo);

        List<ChannelDemandReportDataVo> dataList = dataqChannelDemandReportDao.queryChannelDemandReportSummary(queryChannelDemandReportListReqVo);
        if (CollectionUtils.isEmpty(dataList))
        {
            return null;
        }
        List<String> bizDateValueList = dataList.stream().map(ChannelDemandReportDataVo::getBizDateValue).collect(Collectors.toList());
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        Map<String, Double> result = new HashMap<>();
        for (ChannelDemandReportDataVo data : dataList)
        {
            DataqWeek dataqWeek = dataqWeekMap.get(data.getBizDateValue());
            // 周粒度直接加入dataMap
            if (BizDateTypeEnum.WEEK.equals(queryChannelDemandReportListReqVo.getBizDateType()))
            {
                String head = dataqWeek.getWeekLabel();
                result.put(head, data.getOrderNum());
            }
            // 月粒度按月求和
            else if (BizDateTypeEnum.MONTH.equals(queryChannelDemandReportListReqVo.getBizDateType()))
            {
                String head = dataqWeek.getMonthLabel();
                if (result.containsKey(head))
                {
                    BigDecimal monthTotal = new BigDecimal(result.get(head));
                    BigDecimal orderNum = new BigDecimal(data.getOrderNum());
                    Double value = monthTotal.add(orderNum).doubleValue();
                    result.put(head, value);
                }
                else
                {
                    result.put(head, data.getOrderNum());
                }
            }
        }

        return result;
    }

    /**
     *
     * @Description 查询渠道需求提报列表，全量查询平铺数据，数据量大的时候存在性能问题，已废弃。
     * @param queryChannelDemandReportListReqVo
     * @return BaseTable<List < QueryChannelDemandReportListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:36
     */
//    @Deprecated
//    public BaseTable<List<QueryChannelDemandReportListRspVo>> queryChannelDemandReportListAll(
//        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
//        throws Exception
//    {
//        // 调用平铺接口，获取小时间粒度的数据再聚合
//        BizDateTypeEnum srcBizDateType = queryChannelDemandReportListReqVo.getBizDateType();
//        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
//        queryChannelDemandReportListReqVo.setIsModify(0);
//
//        BaseTable<List<QueryChannelDemandReportListRspVo>> baseTable = new BaseTable();
//        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_LIST"));
//        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandReportListReqVo);
//        JSONArray jsonArray = (JSONArray) dataqResult.getData();
//        if (CollectionUtils.isEmpty(jsonArray))
//        {
//            return baseTable;
//        }
//
//        // 如果上一版本入参不为空，获取上一版本提报数据
//        Map<QueryChannelDemandReportListRspVo, List<QueryChannelDemandReportListRspVo>> lastVersionReportMap = Collections.EMPTY_MAP;
//        if (StringUtils.isNotEmpty(queryChannelDemandReportListReqVo.getLastRollingVersion()))
//        {
//            // 将版本字段值修改为上一版本
//            queryChannelDemandReportListReqVo.setRollingVersion(queryChannelDemandReportListReqVo.getLastRollingVersion());
//            // 查询上一版本动态字段
//            DataqResult<?> lastVersionResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandReportListReqVo);
//            JSONArray lastVersionJsonArray = (JSONArray) lastVersionResult.getData();
//            if (CollectionUtils.isNotEmpty(lastVersionJsonArray))
//            {
//                List<QueryChannelDemandReportListRspVo> lastVersionReportList = lastVersionJsonArray.toJavaList(QueryChannelDemandReportListRspVo.class);
//                lastVersionReportMap = lastVersionReportList.stream().collect(Collectors.groupingBy(Function.identity()));
//            }
//        }
//        queryChannelDemandReportListReqVo.setBizDateType(srcBizDateType);
//
//        // 遍历平铺的数据，并根据时间粒度和动态数据封装表头
//        List<QueryChannelDemandReportListRspVo> reportList = jsonArray.toJavaList(QueryChannelDemandReportListRspVo.class);
//
//        // 获取所有周数据
//        Set<String> bizDateValueSet = reportList.stream().map(QueryChannelDemandReportListRspVo::getBizDateValue).collect(Collectors.toSet());
//
//        // 获取指定周的周对象
//        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueSet);
//        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
//            return (DataqWeek) item;
//        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
//            Function.identity(),
//            (key1, key2) -> key1));
//
//        // 数据根据时间排序
//        for (QueryChannelDemandReportListRspVo report : reportList)
//        {
//            DataqWeek dataqWeek = (DataqWeek) dataqWeekMap.get(report.getBizDateValue());
//            // 月时间粒度，表头：XX月
//            if (BizDateTypeEnum.MONTH.equals(queryChannelDemandReportListReqVo.getBizDateType()))
//            {
//                report.setHead(dataqWeek.getMonthLabel());
//            }
//            // 周时间粒度：表头：XX月W1(1-5)
//            else if (BizDateTypeEnum.WEEK.equals(queryChannelDemandReportListReqVo.getBizDateType()))
//            {
//                report.setHead(dataqWeek.getWeekLabel());
//            }
//            else
//            {
//                throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
//            }
//        }
//
//        List<String> headList =
//            reportList.stream().sorted(Comparator.comparing(QueryChannelDemandReportListRspVo::getBizDateValue)).map(QueryChannelDemandReportListRspVo::getHead)
//                .distinct().collect(Collectors.toList());
//
//        // 当前版本平铺数据列表转map，KEY：固定字段，Value：固定字段对应动态字段的数据列表
//        Map<QueryChannelDemandReportListRspVo, List<QueryChannelDemandReportListRspVo>> reportMap =
//            reportList.stream().collect(Collectors.groupingBy(Function.identity()));
//
//        // 循环遍历map，封装数据
//        List<QueryChannelDemandReportListRspVo> result = new ArrayList<>(reportMap.size());
//        for (Map.Entry<QueryChannelDemandReportListRspVo, List<QueryChannelDemandReportListRspVo>> entry : reportMap.entrySet())
//        {
//            QueryChannelDemandReportListRspVo queryChannelDemandReportListRspVo = entry.getKey();
//            List<QueryChannelDemandReportListRspVo> dataList = entry.getValue();
//            // 获取固定数据对应上一版本的动态数据列表
//            List<QueryChannelDemandReportListRspVo> lastVersionDataList = lastVersionReportMap.get(queryChannelDemandReportListRspVo);
//            Map<String, Double> lastVersionDataMap = Collections.EMPTY_MAP;
//            if (CollectionUtils.isNotEmpty(lastVersionDataList))
//            {
//                // 上一版本动态数据列表转Map，key：动态时间，value：提报数据值
//                lastVersionDataMap = lastVersionDataList.stream().collect(
//                    Collectors.toMap(QueryChannelDemandReportListRspVo::getBizDateValue, QueryChannelDemandReportListRspVo::getOrderNum, (k1, k2) -> k1));
//            }
//            // 遍历当前版本动态数据，封装dataMap与动态表头对应。同时获取动态时间对应上一版本提报数据值。
//            for (QueryChannelDemandReportListRspVo data : dataList)
//            {
//                Double lastVersionOrderNum = lastVersionDataMap.get(data.getBizDateValue());
//                ChannelDemandReportDataVo channelDemandReportDataVo = new ChannelDemandReportDataVo();
//                channelDemandReportDataVo.setId(data.getId());
//                channelDemandReportDataVo.setBizDateValue(data.getBizDateValue());
//                channelDemandReportDataVo.setOrderNum(data.getOrderNum());
//                channelDemandReportDataVo.setLastOrderNum(lastVersionOrderNum);
//                if (Objects.isNull(lastVersionOrderNum))
//                {
//                    channelDemandReportDataVo.setDeviationRadio(null);
//                }
//                else
//                {
//                    BigDecimal orderNum = new BigDecimal(data.getOrderNum());
//                    BigDecimal lastOrderNum = new BigDecimal(lastVersionOrderNum);
//                    channelDemandReportDataVo.setDeviationRadio(BigDecimal.ZERO.equals(lastOrderNum) ? 0d :
//                        orderNum.subtract(lastOrderNum).divide(lastOrderNum, 2, RoundingMode.HALF_UP).doubleValue());
//                }
//                channelDemandReportDataVo.setRemark(null);
//
//                queryChannelDemandReportListRspVo.getDataMap().put(data.getHead(), channelDemandReportDataVo);
//            }
//            result.add(queryChannelDemandReportListRspVo);
//        }
//
//        // 过滤当前账号有权限的数据
//        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
//        if (!currentAccount.isAdmin())
//        {
//            result = result.stream().filter(item -> {
//                boolean authFlag = false;
//                if (currentAccount.getChannelIdList().contains(item.getLv3ChannelCode()) &&
//                    currentAccount.getCategoryIdList().contains(item.getLv3CategoryCode()))
//                {
//                    authFlag = true;
//                }
//                return authFlag;
//            }).collect(Collectors.toList());
//        }
//
//        baseTable.setHeadArray(headList);
//        baseTable.setList(result);
//
//        return baseTable;
//    }

    /**
     *
     * @Description 查询渠道需求提报数据列表（行转列）
     * @param queryChannelDemandReportListReqVo
     * @return BaseTable<List < QueryChannelDemandReportListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月05日 17:27
     */
//    public BaseTable<List<QueryChannelDemandReportListRspVo>> queryChannelDemandReportListColumnTable(
//        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception
//    {
//        BaseTable<List<QueryChannelDemandReportListRspVo>> baseTable = new BaseTable();
//        // 获取查询动态字段接口地址
//        String columnPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_COLUMN"));
//        DataqResult<?> columnResult = dataqService.invoke(HttpMethod.POST, columnPath, null, null, queryChannelDemandReportListReqVo);
//        List<JSONObject> columnList = (List<JSONObject>) columnResult.getData();
//        if (CollectionUtils.isEmpty(columnList))
//        {
//            return baseTable;
//        }
//
//        // 封装动态字段
//        queryChannelDemandReportListReqVo.setSelectColumn(columnList.get(0).getString("trendsColumn"));
//
//        // 获取查询表数据接口地址
//        String tablePath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_TABLE"));
//        DataqResult<?> reportResult = dataqService.invoke(HttpMethod.POST, tablePath, null, null, queryChannelDemandReportListReqVo);
//        List<JSONObject> reportList = (List<JSONObject>) reportResult.getData();
//        if (CollectionUtils.isEmpty(reportList))
//        {
//            return baseTable;
//        }
//
//        // 如果上一版本入参不为空，获取上一版本提报数据
//        Map<QueryChannelDemandReportListRspVo, JSONObject> lastVersionReportMap = new HashMap<>();
//        if (StringUtils.isNotEmpty(queryChannelDemandReportListReqVo.getLastRollingVersion()))
//        {
//            // 将版本字段值修改为上一版本
//            queryChannelDemandReportListReqVo.setRollingVersion(queryChannelDemandReportListReqVo.getLastRollingVersion());
//            // 查询上一版本动态字段
//            DataqResult<?> lastVersionColumnResult = dataqService.invoke(HttpMethod.POST, columnPath, null, null, queryChannelDemandReportListReqVo);
//            List<JSONObject> lastVersionColumnList = (List<JSONObject>) lastVersionColumnResult.getData();
//            if (CollectionUtils.isNotEmpty(lastVersionColumnList))
//            {
//                // 查询上一版本提报数据
//                DataqResult<?> lastVersionReportResult = dataqService.invoke(HttpMethod.POST, tablePath, null, null, queryChannelDemandReportListReqVo);
//                List<JSONObject> lastVersionReportList = (List<JSONObject>) lastVersionReportResult.getData();
//                if (CollectionUtils.isNotEmpty(lastVersionReportList))
//                {
//                    // 上一版本提报数据转换为Map，key：不包含动态字段的QueryChannelDemandReportListRspVo对象，后续根据equals和hashCode直接获取对应动态字段，value：包含动态字段的JSONObject
//                    for (JSONObject jsonObject : lastVersionReportList)
//                    {
//                        lastVersionReportMap.put(jsonObject.toJavaObject(QueryChannelDemandReportListRspVo.class), jsonObject);
//                    }
//                }
//            }
//        }
//
//        // 解析动态字段key，封装表头
//        // 根据时间粒度类型，拼装时间动态字段正则表达式
//        String keyRegx = "^\\d{8}$";
//
//        // 遍历响应数据所有key，过滤时间动态字段，并根据字段正序排序，排过序的字段，会作为后面循环查询动态字段value的索引
//        // 兼容阿里数据问题，导致不同元素动态字段不一致，遍历所有元素获取所有动态字段
//        Set<String> allKeySet = new HashSet<>();
//        reportList.forEach(item -> {
//                allKeySet.addAll(item.keySet());
//            }
//        );
//        Set<String> trendsKeySet = allKeySet.stream().filter(item -> {
//            return Pattern.matches(keyRegx, item);
//        }).collect(Collectors.toSet());
//        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, trendsKeySet);
//
//        // 查询所有动态字段对应的日历周对象
//        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
//            Function.identity(), (key1, key2) -> key1));
//
//        List<String> headList = new ArrayList<>();
//        // dataq返回动态字段格式：yyyyMMdd
//        // 业务返回周粒度动态字段格式：12月W1(01-03)
//        if (BizDateTypeEnum.WEEK.equals(queryChannelDemandReportListReqVo.getBizDateType()))
//        {
//            for (String trendsKey : trendsKeySet)
//            {
//                DataqWeek dataqWeek = dataqWeekMap.get(trendsKey);
//                headList.add(dataqWeek.getWeekLabel());
//            }
//        }
//        // 业务返回月粒度动态字段格式：12月
//        else if (BizDateTypeEnum.MONTH.equals(queryChannelDemandReportListReqVo.getBizDateType()))
//        {
//            for (String trendsKey : trendsKeySet)
//            {
//                DataqWeek dataqWeek = dataqWeekMap.get(trendsKey);
//                headList.add(dataqWeek.getMonthLabel());
//            }
//        }
//        else
//        {
//            return null;
//        }
//
//        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
//        List<QueryChannelDemandReportListRspVo> dataList = new ArrayList<>();
//        for (JSONObject jsonObject : reportList)
//        {
//            // 当前版本提报数据
//            QueryChannelDemandReportListRspVo queryChannelDemandReportListRspVo = jsonObject.toJavaObject(QueryChannelDemandReportListRspVo.class);
//
//            // 获取上一版提报数据
//            JSONObject lastVersionJsonObject = lastVersionReportMap.get(queryChannelDemandReportListRspVo);
//
//            double total = 0.0d;
//            // 每条数据都按照动态字段列表的顺序，动态获取字段value
//            List<ChannelDemandReportDataVo> trendsDataList = new ArrayList<>(trendsKeySet.size());
//            for (String key : trendsKeySet)
//            {
//                ChannelDemandReportDataVo channelDemandReportDataVo = null;
//                if (Objects.nonNull(jsonObject.getJSONObject(key)))
//                {
//                    channelDemandReportDataVo = jsonObject.getJSONObject(key).toJavaObject(ChannelDemandReportDataVo.class);
//                }
//                // 正常场景不会走到else，此处else用于兼容阿里数据如果出现时间粒度不连续的错误情况
//                else
//                {
//                    channelDemandReportDataVo = new ChannelDemandReportDataVo();
//                    channelDemandReportDataVo.setId(StringUtils.EMPTY);
//                    channelDemandReportDataVo.setOrderNum(0.0d);
//                }
//                if (Objects.isNull(channelDemandReportDataVo.getOrderNum()))
//                {
//                    channelDemandReportDataVo.setOrderNum(0.0d);
//                }
//
//                // 获取上一版本对应动态字段的数值，上一版本可能没有此动态字段，则为空
//                if (Objects.nonNull(lastVersionJsonObject))
//                {
//                    JSONObject lastVersionKeyObject = lastVersionJsonObject.getJSONObject(key);
//                    if (Objects.nonNull(lastVersionKeyObject))
//                    {
//                        ChannelDemandReportDataVo lastVersionChannelDemandReportDataVo = lastVersionKeyObject.toJavaObject(ChannelDemandReportDataVo.class);
//                        if (Objects.isNull(lastVersionChannelDemandReportDataVo.getOrderNum()))
//                        {
//                            lastVersionChannelDemandReportDataVo.setOrderNum(0.0d);
//                        }
//                        channelDemandReportDataVo.setLastOrderNum(lastVersionChannelDemandReportDataVo.getOrderNum());
//                        BigDecimal orderNum = new BigDecimal(channelDemandReportDataVo.getOrderNum());
//                        BigDecimal lastOrderNum = new BigDecimal(channelDemandReportDataVo.getLastOrderNum());
//                        channelDemandReportDataVo.setDeviationRadio(orderNum.divide(lastOrderNum, 2, RoundingMode.HALF_UP).doubleValue());
//                    }
//                }
//
//                channelDemandReportDataVo.setBizDateValue(key);
//                trendsDataList.add(channelDemandReportDataVo);
//
//                // 周粒度直接加入dataMap
//                if (BizDateTypeEnum.WEEK.equals(queryChannelDemandReportListReqVo.getBizDateType()))
//                {
//                    String head = dataqWeekMap.get(key).getWeekLabel();
//                    queryChannelDemandReportListRspVo.getDataMap().put(head, channelDemandReportDataVo);
//                }
//                // 月粒度按月求和
//                else
//                {
//                    String head = dataqWeekMap.get(key).getMonthLabel();
//                    if (queryChannelDemandReportListRspVo.getDataMap().containsKey(head))
//                    {
//                        ChannelDemandReportDataVo tmpChannelDemandReportDataVo = queryChannelDemandReportListRspVo.getDataMap().get(head);
//                        BigDecimal monthTotal = new BigDecimal(tmpChannelDemandReportDataVo.getOrderNum());
//                        BigDecimal orderNum = new BigDecimal(channelDemandReportDataVo.getLastOrderNum());
//                        tmpChannelDemandReportDataVo.setOrderNum(monthTotal.add(orderNum).doubleValue());
//                        queryChannelDemandReportListRspVo.getDataMap().put(head, tmpChannelDemandReportDataVo);
//                    }
//                    else
//                    {
//                        queryChannelDemandReportListRspVo.getDataMap().put(head, channelDemandReportDataVo);
//                    }
//                }
//
//                // 计算总数
//                Double currentValue = channelDemandReportDataVo.getOrderNum();
//                total += currentValue;
//            }
//
//            queryChannelDemandReportListRspVo.setTotal(total);
//            dataList.add(queryChannelDemandReportListRspVo);
//        }
//        baseTable.setHeadArray(headList);
//        baseTable.setList(dataList);
//
//        return baseTable;
//    }

    /**
     *
     * @Description 修改渠道需求提报版本锁定状态
     * @param channelDemandReportVersionDto
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月26日 14:22
     */
    @Override
    public void updateChannelDemandReportVersionLock(ChannelDemandReportVersionDto channelDemandReportVersionDto)
        throws Exception
    {
        channelDemandReportDao.updateChannelDemandReportVersionLock(channelDemandReportVersionDto);
    }

    /**
     *
     * @Description 查询渠道需求提报已提报的渠道编号列表
     * @param queryChannelDemandReportedChannelIdListReqVo
     * @return Set<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月27日 9:43
     */
    @Override
    public Set<String> queryChannelDemandReportedChannelIdList(QueryChannelDemandReportedChannelIdListReqVo queryChannelDemandReportedChannelIdListReqVo)
        throws Exception
    {
//        Map<String, Object> param = new HashMap<>();
//        param.put("group_by", "\"lv3ChannelCode\"");
//
//        queryChannelDemandReportedChannelIdListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
//        queryChannelDemandReportedChannelIdListReqVo.setIsModify(1);
//
//        // 获取查询渠道需求列表接口地址，由于本接口仅需要获取已经提报的渠道数据，所以不需要行转列，使用平铺的简单接口来获取
//        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_LIST"));
//        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelDemandReportedChannelIdListReqVo);
//        JSONArray jsonArray = (JSONArray) dataqResult.getData();
//        List<QueryChannelDemandReportListRspVo> dataList = jsonArray.toJavaList(QueryChannelDemandReportListRspVo.class);
//
//        // 遍历数据列表，提取渠道编号信息
//        Set<String> channelIdSet = new HashSet<String>();
//        if (CollectionUtils.isNotEmpty(dataList))
//        {
//            Set<String> lv3ChannelCodeSet = dataList.stream().map(QueryChannelDemandReportListRspVo::getLv3ChannelCode).distinct().collect(Collectors.toSet());
//            channelIdSet.addAll(lv3ChannelCodeSet);
//            lv3ChannelCodeSet.clear();
//        }
        Set<String> channelIdSet = null;

        String lv3ChannelCodes =
            channelDemandReportDao.queryChannelDemandReportVersionLv3ChannelCodes(queryChannelDemandReportedChannelIdListReqVo.getRollingVersion());
        if (StringUtils.isBlank(lv3ChannelCodes))
        {
            channelIdSet = Collections.EMPTY_SET;
        }
        else
        {
            channelIdSet = Arrays.stream(lv3ChannelCodes.split(StringUtils.COMMA_SEPARATOR)).collect(Collectors.toSet());
        }

        return channelIdSet;
    }

    /**
     *
     * @Description 修改渠道需求提报数据
     * @param updateChannelDemandReportVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月31日 17:51
     */
    @Override
    public void updateChannelDemandReportData(UpdateChannelDemandReportVo updateChannelDemandReportVo) throws Exception
    {
        List<ChannelDemandReportDataVo> channelDemandReportDataVoList = updateChannelDemandReportVo.getChannelDemandReportDataVoList();

        // 校验渠道需求提报时间是否已过
        String rollingVersion = dataqChannelDemandReportDao.queryChannelDemandReportVersionById(channelDemandReportDataVoList.get(0).getId());
        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo = new QueryChannelDemandReportListReqVo();
        queryChannelDemandReportListReqVo.setRollingVersion(rollingVersion);
        String lastDate = queryChannelDemandReportLastDate(queryChannelDemandReportListReqVo);
        String currentDate = DateUtils.getDate(DateUtils.YMD);
        if (StringUtils.isNotEmpty(lastDate) && currentDate.compareTo(lastDate) > 0)
        {
            throw new ServiceException("渠道需求提报版本日期已结束");
        }

        // 设置修改人账号信息，放在dataq接口请求头中
        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        Map<String, Object> head = new HashMap<String, Object>();
        head.put("XXX-USER-TOKEN", URLEncoder.encode(currentAccount.getName(), CharsetKit.UTF_8));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", channelDemandReportDataVoList);

        // 获取查询渠道需求提报导入模板动态字段接口地址
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_UPDATE"));
        dataqService.invoke(HttpMethod.POST, path, head, null, jsonObject);

        // 由于领导要求部分功能归入阿里产品，阿里渠道需求提报表结构设计编辑操作为修改原始数据为已修改，同时插入新数据。
        // 阿里接口只写入数据发生变化的数据。
        // 所以此处需要单独保存已编辑三级渠道编号，否则编辑操作没有发生修改数据值的情况，看不到已编辑的渠道
        ChannelDemandReportVersionDto channelDemandReportVersionDto = new ChannelDemandReportVersionDto();
        channelDemandReportVersionDto.setRollingVersion(updateChannelDemandReportVo.getRollingVersion());
        channelDemandReportVersionDto.setLv3ChannelCodes(updateChannelDemandReportVo.getLv3ChannelCode());
        channelDemandReportDao.appendChannelDemandReportVersionLv3ChannelCodes(channelDemandReportVersionDto);
    }

    /**
     *
     * @Description 查询渠道需求提报数据修改历史
     * @param queryChannelDemandReportListReqVo
     * @return List<QueryChannelDemandReportHistoryRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月02日 16:23
     */
    @Override
    public List<QueryChannelDemandReportHistoryRspVo> queryChannelDemandReportHistoryList(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo)
        throws Exception
    {
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        queryChannelDemandReportListReqVo.setIsModify(null);

        // 查询渠道需求提报修改历史记录
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandReportListReqVo);
        if (Objects.isNull(dataqResult.getData()))
        {
            return null;
        }
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        List<QueryChannelDemandReportListRspVo> list = jsonArray.toJavaList(QueryChannelDemandReportListRspVo.class);

        // 获取当前版本涉及所有时间粒度日期对应的周数据
        String minBizDateValue = list.stream().map(item -> item.getBizDateValue()).distinct().min((o1, o2) -> {
            return o1.compareTo(o2);
        }).get();
        String maxBizDateValue = list.stream().map(item -> item.getBizDateValue()).distinct().max((o1, o2) -> {
            return o1.compareTo(o2);
        }).get();

        // 所有提报数据根据时间粒度值分组
        Map<String, List<QueryChannelDemandReportListRspVo>> map =
            list.stream().collect(Collectors.groupingBy(QueryChannelDemandReportListRspVo::getBizDateValue));
        list.clear();

        // 查询时间范围内日历周粒度数据
        QueryWeekListReqVo queryWeekListReqVo = new QueryWeekListReqVo();
        queryWeekListReqVo.setBeginDate(minBizDateValue);
        queryWeekListReqVo.setEndDate(maxBizDateValue);
        List<DataqWeek> dataqWeekList = calendarService.queryWeekList(queryWeekListReqVo);
        Map<String, DataqWeek> dataqWeekMap = null;
        if (CollectionUtils.isNotEmpty(dataqWeekList))
        {
            dataqWeekMap = dataqWeekList.stream().collect(Collectors.toMap(key -> key.getFsclWeekStart(), Function.identity(), (k1, k2) -> k1));
        }
        else
        {
            dataqWeekMap = new HashMap<>();
        }

        // 循环封装修改历史数据
        List<QueryChannelDemandReportHistoryRspVo> result = new ArrayList<>();
        for (String bizDateValue : map.keySet())
        {
            List<QueryChannelDemandReportListRspVo> subList = map.get(bizDateValue);
            subList = subList.stream().sorted(Comparator.comparing(QueryChannelDemandReportListRspVo::getGmtCreate)).collect(Collectors.toList());
            for (int i = 1; i < subList.size(); i++)
            {
                QueryChannelDemandReportHistoryRspVo queryChannelDemandReportHistoryRspVo = new QueryChannelDemandReportHistoryRspVo();
                DataqWeek dataqWeek = dataqWeekMap.get(bizDateValue.replaceAll(String.valueOf(StringUtils.DATE_SEPARATOR), StringUtils.EMPTY));
                // 时间粒度格式转换为m月Ww
                queryChannelDemandReportHistoryRspVo.setBizDateValue(Objects.isNull(dataqWeek) ? bizDateValue : dataqWeek.getMonthWeekLabel());
                // 修改前的值
                queryChannelDemandReportHistoryRspVo.setOldOrderNum(subList.get(i - 1).getOrderNum());
                // 修改后的值
                queryChannelDemandReportHistoryRspVo.setNewOrderNum(subList.get(i).getOrderNum());
                // 修改时间
                queryChannelDemandReportHistoryRspVo.setUpdatedTime(subList.get(i).getGmtModify());
                // 修改人
                queryChannelDemandReportHistoryRspVo.setUpdatedBy(subList.get(i).getLastModifier());
                // 备注
                queryChannelDemandReportHistoryRspVo.setRemark(subList.get(i).getExtend());
                result.add(queryChannelDemandReportHistoryRspVo);
            }
        }

        return result.stream().sorted(Comparator.comparing(QueryChannelDemandReportHistoryRspVo::getUpdatedTime).reversed()).collect(Collectors.toList());
    }


    /**
     *
     * @Description 查询渠道需求提报版本最后一天日期
     * @param queryChannelDemandReportListReqVo
     * @return String
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月12日 15:21
     */
    @Override
    public String queryChannelDemandReportLastDate(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) throws Exception
    {
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        queryChannelDemandReportListReqVo.setIsModify(0);

        List<String> bizDateValueList = dataqChannelDemandReportDao.queryChannelDemandReportHeadList(queryChannelDemandReportListReqVo);
        if (CollectionUtils.isEmpty(bizDateValueList))
        {
            return null;
        }
        String maxBizDateValue = bizDateValueList.stream().max(Comparator.comparing(String::valueOf)).get();

        DataqWeek dataqWeek = (DataqWeek) redisUtils.hget(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, maxBizDateValue);
        if (Objects.isNull(dataqWeek))
        {
            return maxBizDateValue;
        }
        return dataqWeek.getFsclWeekEnd();
    }

    /**
     * 查询周期数据认养周时间列表，按照升序排列
     * @param queryChannelDemandReportListReqVo
     * @return
     */
    @Override
    public List<String> queryChannelDemandReportDateByOrder(QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo) {
        queryChannelDemandReportListReqVo.setBizDateType(BizDateTypeEnum.WEEK);
        queryChannelDemandReportListReqVo.setIsModify(0);
        List<String> bizDateValueList = dataqChannelDemandReportDao.queryChannelDemandReportHeadList(queryChannelDemandReportListReqVo);
        return bizDateValueList;
    }
}
