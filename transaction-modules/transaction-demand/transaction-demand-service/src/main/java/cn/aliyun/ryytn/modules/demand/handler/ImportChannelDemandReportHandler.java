package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.excel.AbstractImportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.excel.ExcelData;
import cn.aliyun.ryytn.common.excel.ExcelResult;
import cn.aliyun.ryytn.common.excel.ImportResultFillHander;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.resource.I18nUtils;
import cn.aliyun.ryytn.common.utils.string.CharsetKit;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.ChannelDemandReportService;
import cn.aliyun.ryytn.modules.demand.dao.ChannelDemandReportDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.ChannelDemandReportVersionDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ImportChannelDemandReportVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo;
import cn.aliyun.ryytn.modules.demand.repository.DataqChannelDemandReportRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @Description 导入渠道需求提报
 * @date 2023/10/19 14:46
 */
@Slf4j
@Component("importChannelDemandReport")
public class ImportChannelDemandReportHandler extends AbstractImportExcelHandler<ImportChannelDemandReportVo>
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private ChannelDemandReportService channelDemandReportService;

    @Autowired
    private RedisUtils redisUtils;

    @Resource
    private DataqChannelDemandReportDao dataqChannelDemandReportDao;

    @Autowired
    private ChannelDemandReportDao channelDemandReportDao;

    public static final Integer headRowNum = 3;

    private static final Integer LOCK = 1;

    @Resource
    private DataqChannelDemandReportRepository repository;

    public static final String rollingVersionKey = "rollingVersion";
    /**
     * 月统计列
     */
    private final static String monthTotalColumnHeadKey = "月";

    //    private static final String[] headerKeys =new String[]{"产品分类","产品大类","产品小类","产品编码","产品简称","产品状态","一级渠道","二级渠道","三级渠道","起始年份","产品分类编码","产品大类编码","产品小类编码","一级渠道编码","二级渠道编码","三级渠道编码","单位"};
    private static  String[] headerKeys = null;
    static {
        Map<Integer, String> treeMap = new TreeMap<>();
        Field[] fds = ImportChannelDemandReportVo.class.getDeclaredFields();
        for(int i = 0; i < fds.length;i++){
            if (fds[i].isAnnotationPresent(ExcelProperty.class)) {
                ExcelProperty ep = fds[i].getDeclaredAnnotation(ExcelProperty.class);
                if(null != ep){
                    treeMap.put(ep.index(),ep.value()[0]);
                }
            }
        }
        headerKeys= new String[treeMap.size()];
        for (Map.Entry<Integer, String> entry : treeMap.entrySet()) {
            headerKeys[entry.getKey()] = entry.getValue();
        }
    }

    /**
     * @param file
     * @param condition
     * @return List<?>
     * @Description 导入电子表格
     * <AUTHOR>
     * @date 2023年10月19日 15:01
     */
    @Override
    public ExcelResult<ExcelData> importExcel(File file, ExcelCondition condition) throws Exception
    {
        Set<String> excelSkuCodes = new HashSet<>();
        if (!condition.getCondition().containsKey(rollingVersionKey))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }

        // 校验渠道需求提报时间是否已过
        String rollingVersion = condition.getCondition().getString(rollingVersionKey);

        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo = new QueryChannelDemandReportListReqVo();
        queryChannelDemandReportListReqVo.setRollingVersion(rollingVersion);
        queryChannelDemandReportListReqVo.setTableSuffix((StringUtils.isBlank(rollingVersion))?"":rollingVersion.substring(3,9));
        String lastDate = channelDemandReportService.queryChannelDemandReportLastDate(queryChannelDemandReportListReqVo);
        String currentDate = DateUtils.getDate(DateUtils.YMD);
        if (StringUtils.isNotEmpty(lastDate) && currentDate.compareTo(lastDate) > 0)
        {
            throw new ServiceException("渠道需求提报版本日期已结束");
        }

        ChannelDemandReportVersionDto channelDemandReportVersionDto = new ChannelDemandReportVersionDto();
        channelDemandReportVersionDto.setRollingVersion(rollingVersion);
        channelDemandReportVersionDto = channelDemandReportService.queryChannelDemandReportVersion(channelDemandReportVersionDto);
        if (LOCK.equals(channelDemandReportVersionDto.getIsLocked()))
        {
            throw new ServiceException("提报版本已锁定");
        }

        // 分布式锁，由于阿里提报数据表存储修改历史的全部数据，仅通过is_modify字段标识当前有效数据，如果不加锁，并发场景下可能导致同样的业务数据存在多条is_modify是0的数据，造成业务异常
        String key = StringUtils.format(CommonConstants.REDIS_IMPORT_CHANNEL_DEMAND_REPORT_LOCK_KEY, rollingVersion);

        CustomChannelCodeReadListener listenerChannel = new CustomChannelCodeReadListener(condition);
        InputStream inputStreamChannel = new FileInputStream(file);
        EasyExcel.read(inputStreamChannel, ImportChannelDemandReportVo.class, listenerChannel).autoCloseStream(false).headRowNumber(headRowNum).sheet().doRead();

        CustomReadListener listener = null;
        if (redisUtils.lock(key, 3600L))
        {
            InputStream inputStream1 = new FileInputStream(file);
            InputStream inputStream2 = new FileInputStream(file);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            try
            {
                StopWatch excelClock = new StopWatch("excelRead");
                excelClock.start("readDbRecord");
                listener = new CustomReadListener(condition, listenerChannel.skuCodes);
                excelClock.stop();
                excelClock.start("read");
                EasyExcel.read(inputStream1, ImportChannelDemandReportVo.class, listener).autoCloseStream(false).headRowNumber(headRowNum).sheet().doRead();
                excelClock.stop();

                excelClock.start("write");
                EasyExcel.write(byteArrayOutputStream)
                        .withTemplate(inputStream2)
                        .autoCloseStream(false)
                        .registerWriteHandler(new ImportResultFillHander(listener.excelResult.getFailedList(), headRowNum))
                        .needHead(false)
                        .sheet()
                        .doWrite(Collections.emptyList());
                excelClock.stop();
                log.info("excelReadandWrite cost:{}s ",excelClock.getTotalTimeSeconds());
                log.info("excelReadandWrite cost:{}",excelClock.prettyPrint());
                listener.excelResult.setBytes(byteArrayOutputStream.toByteArray());
                // 失败数量限制100，防止集合过大浏览器崩溃
                listener.excelResult.setFailedList(listener.excelResult.getFailedList().stream().limit(100).collect(Collectors.toList()));
            }
            catch (Exception e)
            {
                throw e;
            }
            finally
            {
                IOUtils.closeQuietly(inputStream1);
                IOUtils.closeQuietly(inputStream2);
                IOUtils.closeQuietly(byteArrayOutputStream);
                redisUtils.unlock(key);
            }
        }
        else
        {
            log.info("提报版本已有人锁定,当前版本正在执行其他导入任务，请稍后再试");
            throw new ServiceException("当前版本正在执行其他导入任务，请稍后再试。");
        }

        listener.channelDemandReportMap.clear();
        return listener.excelResult;
    }

    /**
     * <AUTHOR>
     * @Description 自定义读监听器
     * @date 2023/10/30 16:34
     */
    private class CustomReadListener implements ReadListener<ImportChannelDemandReportVo>
    {
        /**
         * 每隔10条存储数据库
         * 由于导入电子表格提报数据一行包含N个动态时间字段数据
         * 此处批量处理10条，最终会给dataq发送10*动态时间数条提报数据记录
         */
        private static final int BATCH_COUNT = 100;

        /**
         * 当前日，格式：yyyyMMdd
         */
        private final String currentDay = DateUtils.getDate(DateUtils.YMD);

        /**
         * 固定字段最后一列索引
         */
        private Integer fieldLastCol;

        /**
         * 动态列最后一列索引
         */
        private Integer headLastCol;

        /**
         * 列和参数key映射关系，Key：电子表格列索引，Value：调用dataq参数的key
         */
        private final Map<Integer, String> columnWeekDate;

        /**
         * 缓存的数据
         */
        private List<ImportChannelDemandReportVo> cachedDataList;

        /**
         * 导入参数
         */
        private final ExcelCondition condition;

        /**
         * 导入结果
         */
        private final ExcelResult<ExcelData> excelResult;

        private Map<ChannelDemandReportDto, ChannelDemandReportDto> channelDemandReportMap;

        private Account currentAccount;

        public int count = 0;

        private String extend = "文件导入";

        private static final String SQL_SELECT_DATE_TEMPLATE_ORDER_NUM = " max(case when biz_date_value = '%s' then order_num else 0 end) as \"%s\", ";

        private static final String SQL_SELECT_DATE_TEMPLATE_PREVIOUS_ORDER_NUM = " max(case when biz_date_value = '%s' then previous_order_num else 0 end) as \"%s_PRE\", ";

        private static final String SQL_SELECT_DATE_TEMPLATE_PREVIOUS_ID= " max(case when biz_date_value = '%s' then id else -1 end) as \"%s_ID\", ";


        public CustomReadListener(ExcelCondition condition,Set<String> skuCodeSet)
        {

            this.currentAccount = condition.getSession().getAccount();
            StopWatch customReadListenerClock = new StopWatch("customReadListenerSW");
            // 通过反射获取固定字段表头
            customReadListenerClock.start("col_jiexi");
            Class clazz = ImportChannelDemandReportVo.class;
            // 遍历所有字段，获取所有ExcelProperty注解
            this.fieldLastCol = 0;
            for (Field field : clazz.getDeclaredFields())
            {
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                if (Objects.isNull(excelProperty))
                {
                    continue;
                }
                if (excelProperty.index() > this.fieldLastCol)
                {
                    this.fieldLastCol = excelProperty.index();
                }
            }
            customReadListenerClock.stop();

            this.columnWeekDate = new HashMap<>();
            this.cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            this.condition = condition;
            this.excelResult = new ExcelResult<>();
            this.excelResult.setSuccess(0L);
            this.excelResult.setFailed(0L);
            this.excelResult.setFailedList(new ArrayList<ExcelData>());
            customReadListenerClock.start("channelDemandReportList");

            String  lv3CategoryCodes = "";
            if(null != currentAccount.getCategoryIdList() && currentAccount.getCategoryIdList().size() >0){
                lv3CategoryCodes = String.join(",", currentAccount.getCategoryIdList());
            }
            log.info("lv3CategoryCodes content is {}",lv3CategoryCodes);

            String skuCodes = "";
            if(null != skuCodeSet &&skuCodeSet.size() >0){
                skuCodes =  String.join(",", skuCodeSet);
            }
            log.info("skuCodes content is {}",skuCodes);
            QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVoForDateList = new QueryChannelDemandReportListReqVo();
            queryChannelDemandReportListReqVoForDateList.setRollingVersion(condition.getCondition().getString(rollingVersionKey));
            queryChannelDemandReportListReqVoForDateList.setTableSuffix(condition.getCondition().getString(rollingVersionKey).substring(3,9));
            queryChannelDemandReportListReqVoForDateList.setBizDateType(BizDateTypeEnum.WEEK);
            List<String> bizDateList = channelDemandReportService.queryChannelDemandReportDateByOrder(queryChannelDemandReportListReqVoForDateList);
            if(Objects.isNull(bizDateList) || bizDateList.isEmpty()){
                throw new ServiceException("提报数据版本数据日期集合获取异常");
            }
            log.info("query bizDateList size is:{}", bizDateList.size());
            String dynamicsSql = "";
            for(int i = 0 ; i < bizDateList.size();i++){
                dynamicsSql += String.format(SQL_SELECT_DATE_TEMPLATE_ORDER_NUM,bizDateList.get(i),bizDateList.get(i));
                dynamicsSql += String.format(SQL_SELECT_DATE_TEMPLATE_PREVIOUS_ORDER_NUM,bizDateList.get(i),bizDateList.get(i));
                dynamicsSql += String.format(SQL_SELECT_DATE_TEMPLATE_PREVIOUS_ID,bizDateList.get(i),bizDateList.get(i));
            }
            log.info("dynamicsSql is :{}",dynamicsSql);
            //List<ChannelDemandReportDto> channelDemandReportList =
            //        dataqChannelDemandReportDao.queryDuplicate(null, condition.getCondition().getString(rollingVersionKey), BizDateTypeEnum.WEEK.name(),lv3CategoryCodes,skuCodes,condition.getCondition().getString(rollingVersionKey).substring(3,9));
            List<Map<String,Object>> channelDemandReportList = dataqChannelDemandReportDao.queryDuplicateOptimize(condition.getCondition().getString(rollingVersionKey), BizDateTypeEnum.WEEK.name(),lv3CategoryCodes,skuCodes,condition.getCondition().getString(rollingVersionKey).substring(3,9),dynamicsSql);
            log.info("channelDemandReportList queryDuplicateOptimize recordSize:{}",(null == channelDemandReportList)?0:channelDemandReportList.size());
            customReadListenerClock.stop();
            if (CollectionUtils.isEmpty(channelDemandReportList))
            {
                throw new ServiceException("提报版本已锁定");
            }
            customReadListenerClock.start("channelDemandReportMap");

            this.channelDemandReportMap =
                    channelDemandReportList.stream().parallel().map(r->{
                        ChannelDemandReportDto channelDemandReportDto =  new ChannelDemandReportDto();
                        channelDemandReportDto.setBizDateType(BizDateTypeEnum.WEEK.name());
                        channelDemandReportDto.setSkuCode(r.get("sku_code").toString());
                        channelDemandReportDto.setRollingVersion(condition.getCondition().getString(rollingVersionKey));
                        channelDemandReportDto.setLv1CategoryCode(r.get("lv1_category_code").toString());
                        channelDemandReportDto.setLv2CategoryCode(r.get("lv2_category_code").toString());
                        channelDemandReportDto.setLv3CategoryCode(r.get("lv3_category_code").toString());
                        channelDemandReportDto.setLv1ChannelCode(r.get("lv1_channel_code").toString());
                        channelDemandReportDto.setLv2ChannelCode(r.get("lv2_channel_code").toString());
                        channelDemandReportDto.setLv3ChannelCode(null == r.get("lv3_channel_code")?"":r.get("lv3_channel_code").toString());
                        channelDemandReportDto.setValues(r);
                        return channelDemandReportDto;
                    }).collect(Collectors.toMap(key -> key, value -> value, (o1, o2) -> o1));
            customReadListenerClock.stop();
            log.info("customReadListenerSW cost:{}s ",customReadListenerClock.getTotalTimeSeconds());
            log.info("customReadListenerSW cost:{}",customReadListenerClock.prettyPrint());
//            this.currentAccount = condition.getSession().getAccount();
        }

        /**
         * @param headMap
         * @param context
         * @Description 读取表头数据，根据表头封装监听类成员变量
         * <AUTHOR>
         * @date 2023年10月30日 16:45
         */
        @Override
        public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context)
        {

            StopWatch excelHeaderReadClock = new StopWatch("excelHeaderRead");
            excelHeaderReadClock.start("read");
            // 只读取隐藏的表头列，隐藏列中动态表头的值作为dataq批量导入提报数据动态参数的key
            if (context.readRowHolder().getRowIndex() == headRowNum - 1)
            {
                // 封装动态表头数据，固定字段最后一列后的所有列，都是动态列
                for (Integer i : headMap.keySet())
                {
                    if (i > fieldLastCol)
                    {
                        this.columnWeekDate.put(i, headMap.get(i).getStringValue());
                    }
                }
                this.headLastCol = headMap.keySet().stream().max((x, y) -> Integer.compare(x, y)).get();
            }
            excelHeaderReadClock.stop();
            log.info("excelHeaderRead cost:{}s ",excelHeaderReadClock.getTotalTimeSeconds());
            log.info("excelHeaderRead cost:{}",excelHeaderReadClock.prettyPrint());
        }

        /**
         * @param importChannelDemandVo
         * @param analysisContext
         * @Description 读取一条数据
         * <AUTHOR>
         * @date 2023年10月19日 15:06
         */
        @Override
        public void invoke(ImportChannelDemandReportVo importChannelDemandReportVo, AnalysisContext context)
        {
//            StopWatch dataProcessClock1 = new StopWatch("dataProcess");
            // 校验数据，校验失败的数据记录失败列表和失败原因
            try
            {
//                dataProcessClock.start("checkData");
                // 设置行号
                importChannelDemandReportVo.setRowIndex(context.readRowHolder().getRowIndex());

                checkData(importChannelDemandReportVo);
//                dataProcessClock.stop();

//                dataProcessClock.start("dataPreDeal");
                Map<Integer, Cell> cellMap = context.readRowHolder().getCellMap();
                for (Integer i : cellMap.keySet())
                {
                    if (i > fieldLastCol && i <= headLastCol)
                    {
                        CellData<String> cellData = (CellData) cellMap.get(i);
                        String value = null;
                        if (CellDataTypeEnum.NUMBER.equals(cellData.getType()))
                        {
                            value = StringUtils.substringBefore(StringUtils.getValue(cellData.getNumberValue()), StringUtils.POINT_SEPARATOR);
                        }
                        else if (CellDataTypeEnum.STRING.equals(cellData.getType()))
                        {
                            value = StringUtils.substringBefore(cellData.getStringValue(), StringUtils.POINT_SEPARATOR);
                        }
                        else
                        {
                            value = "0";
                        }
                        value = StringUtils.replace(value, StringUtils.SPACE, StringUtils.EMPTY);
                        if (StringUtils.isBlank(value))
                        {
                            value = "0";
                        }
                        if(!(this.columnWeekDate.get(i).indexOf(monthTotalColumnHeadKey) > 0)){//剔除掉如：“5月已过周实发数量、5月已过周计划偏差、5月合计”列
                            if (!value.matches(CommonConstants.PLAN_VALUE_PATTERN))
                            {
                                throw new ServiceException("提报数据必须为非负整数");
                            }
                            importChannelDemandReportVo.getWeekDateValue().put(this.columnWeekDate.get(i), Double.valueOf(value));
                        }
                    }
                }
//                dataProcessClock.stop();
            }
            catch (ServiceException e)
            {
                log.error("CustomReadListener invoke has exception:{}", e);
                this.excelResult.setFailed(this.excelResult.getFailed() + 1);
                importChannelDemandReportVo.setResult(StringUtils.isEmpty(e.getErrorMessage()) ? I18nUtils.getValue(e.errorCode) : e.getErrorMessage());
                this.excelResult.getFailedList().add(importChannelDemandReportVo);
                return;
            }
            catch (Exception e)
            {
                log.error("CustomReadListener invoke has exception:{}", e);
                importChannelDemandReportVo.setResult(I18nUtils.getValue(ErrorCodeConstants.FAIL_OTHER));
                this.excelResult.setFailed(this.excelResult.getFailed() + 1);
                this.excelResult.getFailedList().add(importChannelDemandReportVo);
                return;
            }

            // 封装导入表格外的导入参数
            importChannelDemandReportVo.setRollingVersion(this.condition.getCondition().getString("rollingVersion"));

            cachedDataList.add(importChannelDemandReportVo);
            // 达到BATCH_COUNT了，批量导入，防止数据几万条数据在内存，容易OOM
            if (cachedDataList.size() >= BATCH_COUNT)
            {
                try
                {
//                    dataProcessClock.start("startDataSave for one batch");
                    saveDataIntoDb(cachedDataList);
//                    dataProcessClock.stop();
                    cachedDataList.forEach(item -> {
                        item.setResult(I18nUtils.getValue(ErrorCodeConstants.SUCCESS));
                    });
                    this.excelResult.setSuccess(this.excelResult.getSuccess() + cachedDataList.size());
                }
                catch (ServiceException e)
                {
                    log.error("CustomReadListener invoke has exception:{}", e);
                    cachedDataList.forEach(item -> {
                        item.setResult(StringUtils.isBlank(e.errorMessage) ? I18nUtils.getValue(e.errorCode) : e.errorMessage);
                    });
                    this.excelResult.setFailed(this.excelResult.getFailed() + cachedDataList.size());
                    this.excelResult.getFailedList().addAll(cachedDataList);
                }
                catch (Exception e)
                {
                    log.error("CustomReadListener invoke has exception:{}", e);
                    cachedDataList.forEach(item -> {
                        item.setResult(I18nUtils.getValue(ErrorCodeConstants.FAIL_OTHER));
                    });
                    this.excelResult.setFailed(this.excelResult.getFailed() + cachedDataList.size());
                    this.excelResult.getFailedList().addAll(cachedDataList);
                }
                finally
                {
                    this.cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
                }
            }
//            log.info("dataProcessClock cost:{}s ",dataProcessClock.getTotalTimeSeconds());
//            log.info("dataProcessClock cost:{}",dataProcessClock.prettyPrint());
        }

        /**
         * @param analysisContext
         * @Description 读取数据结束
         * <AUTHOR>
         * @date 2023年10月19日 15:06
         */
        @Override
        public void doAfterAllAnalysed(AnalysisContext context)
        {
            if (CollectionUtils.isEmpty(cachedDataList))
            {
                return;
            }

            try
            {
                // 保存最后遗留的零头数据
                saveDataIntoDb(cachedDataList);
                cachedDataList.forEach(item -> {
                    item.setResult(I18nUtils.getValue(ErrorCodeConstants.SUCCESS));
                });
                this.excelResult.setSuccess(this.excelResult.getSuccess() + cachedDataList.size());
            }
            catch (ServiceException e)
            {
                log.error("CustomReadListener doAfterAllAnalysed has exception:{}", e);
                cachedDataList.forEach(item -> {
                    item.setResult(StringUtils.isBlank(e.errorMessage) ? I18nUtils.getValue(e.errorCode) : e.errorMessage);
                });
                this.excelResult.setFailed(this.excelResult.getFailed() + cachedDataList.size());
                this.excelResult.getFailedList().addAll(cachedDataList);
            }
            catch (Exception e)
            {
                log.error("CustomReadListener doAfterAllAnalysed has exception:{}", e);
                cachedDataList.forEach(item -> {
                    item.setResult(I18nUtils.getValue(ErrorCodeConstants.FAIL_OTHER));
                });
                this.excelResult.setFailed(this.excelResult.getFailed() + cachedDataList.size());
                this.excelResult.getFailedList().addAll(cachedDataList);
            }
        }

        /**
         * @param importChannelDemandVo
         * @throws ServiceException
         * @Description 校验数据
         * <AUTHOR>
         * @date 2023年10月31日 10:07
         */
        private void checkData(ImportChannelDemandReportVo importChannelDemandReportVo) throws ServiceException
        {
            // 如果不是超级管理员，则校验导入渠道和品类的权限
            if (this.currentAccount.isAdmin())
            {
                return;
            }

            // 当前登录账号有权限的渠道编号
            Set<String> accountChannelIdSet = new HashSet<String>(this.currentAccount.getChannelIdList());
            // 渠道没有权限，则该条数据导入失败
            if (!accountChannelIdSet.contains(importChannelDemandReportVo.getLv1ChannelCode()) &&
                    !accountChannelIdSet.contains(importChannelDemandReportVo.getLv2ChannelCode()) &&
                    !accountChannelIdSet.contains(importChannelDemandReportVo.getLv3ChannelCode()))
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_NOAUTH_ERROR);
            }

            // 当前登录账号有权限的品类编号
            Set<String> accountCategoryIdSet = new HashSet<String>(this.currentAccount.getCategoryIdList());
            // 品类没有权限，则该条数据导入失败
            if (!accountCategoryIdSet.contains(importChannelDemandReportVo.getLv1CategoryCode()) &&
                    !accountCategoryIdSet.contains(importChannelDemandReportVo.getLv2CategoryCode()) &&
                    !accountCategoryIdSet.contains(importChannelDemandReportVo.getLv3CategoryCode()))
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_NOAUTH_ERROR);
            }
        }

        /**
         * @param cachedDataList
         * @Description 保存数据
         * <AUTHOR>
         * @date 2023年10月31日 10:43
         */
        @Deprecated
        private void saveData(List<ImportChannelDemandReportVo> cachedDataList) throws Exception
        {
            List<ImportChannelDemandReportVo> dataList = new ArrayList<>();
            for (ImportChannelDemandReportVo importChannelDemandReportVo : cachedDataList)
            {
                for (String weekDate : importChannelDemandReportVo.getWeekDateValue().keySet())
                {
                    String weekFirstDay = StringUtils.substringBefore(weekDate, StringUtils.DATE_SEPARATOR);
                    String weekLastDay = StringUtils.substringAfter(weekDate, StringUtils.DATE_SEPARATOR);
                    // 如果当前日期已经超过周最后一天日期，则提报数据属于历史锁定期，忽略不做提报
                    if (currentDay.compareTo(weekLastDay) > 0)
                    {
                        continue;
                    }

                    ImportChannelDemandReportVo data = new ImportChannelDemandReportVo();
                    BeanUtils.copyProperties(importChannelDemandReportVo, data);
                    // 渠道需求提报周粒度时间值为周第一天日期
                    data.setBizDateValue(weekFirstDay);
                    // 渠道需求提报数据
                    data.setOrderNum(Double.valueOf(importChannelDemandReportVo.getWeekDateValue().get(weekDate)));
                    data.setExtend(extend);
                    dataList.add(data);
                }
            }

            Map<String, List<ImportChannelDemandReportVo>> map = new HashMap<>();
            map.put("data", dataList);
            count += dataList.size();

            // 设置修改人账号信息，放在dataq接口请求头中
            Account currentAccount = condition.getSession().getAccount();
            Map<String, Object> head = new HashMap<String, Object>();
            head.put("XXX-USER-TOKEN", URLEncoder.encode(currentAccount.getName(), CharsetKit.UTF_8));

            // 调用dataq服务批量导入渠道需求提报数据
            String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_IMPORT"));
            dataqService.invoke(HttpMethod.POST, path, head, null, map);
        }

        /**
         * @param cachedDataList
         * @Description 不通过接口，直接保存数据到数据库
         * <AUTHOR>
         * @date 2023年10月31日 10:43
         */
        private void saveDataIntoDb(List<ImportChannelDemandReportVo> cachedDataList) throws Exception
        {
            log.info("Enter saveDataIntoDb,cachedDataList size is:{}", cachedDataList.size());
//            StopWatch saveDataIntoDbClock1 = new StopWatch("saveDataIntoDb");
            String rollingVersion = condition.getCondition().getString(rollingVersionKey);
//            saveDataIntoDbClock.start("queryChannelDemandReportVersionLv3ChannelCodes");
            // 查询提报版本已编辑三级渠道编号
            String lv3ChannelCodes =
                    channelDemandReportDao.queryChannelDemandReportVersionLv3ChannelCodes(rollingVersion);
//            saveDataIntoDbClock.stop();

            Account currentAccount = condition.getSession().getAccount();
            List<ChannelDemandReportDto> dataList = new ArrayList<>();
//            saveDataIntoDbClock.start("datasavebeforeprocess");
            for (ImportChannelDemandReportVo importChannelDemandReportVo : cachedDataList)
            {
                for (String weekDate : importChannelDemandReportVo.getWeekDateValue().keySet())
                {
                    String weekFirstDay = StringUtils.substringBefore(weekDate, StringUtils.DATE_SEPARATOR);
                    String weekLastDay = StringUtils.substringAfter(weekDate, StringUtils.DATE_SEPARATOR);
                    // 如果当前日期已经超过周最后一天日期，则提报数据属于历史锁定期，忽略不做提报
                    if (currentDay.compareTo(weekLastDay) > 0)
                    {
                        continue;
                    }
                    ChannelDemandReportDto data = new ChannelDemandReportDto();
                    BeanUtils.copyProperties(importChannelDemandReportVo, data);
                    // 渠道需求提报周粒度时间值为周第一天日期
                    data.setBizDateType(BizDateTypeEnum.WEEK.name());
                    data.setBizDateValue(weekFirstDay);
                    // 渠道需求提报数据
                    data.setOrderNum(importChannelDemandReportVo.getWeekDateValue().get(weekDate));
                    data.setExtend(extend);
                    data.setCreator(currentAccount.getName());
                    data.setLastModifier(currentAccount.getName());
                    dataList.add(data);
                }

                // 如果三级渠道未编辑过，则追加
                if (StringUtils.isBlank(lv3ChannelCodes))
                {
                    lv3ChannelCodes = importChannelDemandReportVo.getLv3ChannelCode();
                }
                else if (!StringUtils.contains(lv3ChannelCodes, importChannelDemandReportVo.getLv3ChannelCode()))
                {
                    lv3ChannelCodes = lv3ChannelCodes + StringUtils.COMMA_SEPARATOR + importChannelDemandReportVo.getLv3ChannelCode();
                }
            }
//            saveDataIntoDbClock.stop();
//            saveDataIntoDbClock.start("saveBatch");
            repository.saveBatch(dataList, this.channelDemandReportMap);
//            saveDataIntoDbClock.stop();

//            saveDataIntoDbClock.start("updateChannelDemandReportVersionLv3ChannelCodes");
            // 由于领导要求部分功能归入阿里产品，阿里渠道需求提报表结构设计编辑操作为修改原始数据为已修改，同时插入新数据。
            // 阿里接口只写入数据发生变化的数据。
            // 所以此处需要单独保存已编辑三级渠道编号，否则编辑操作没有发生修改数据值的情况，看不到已编辑的渠道
            ChannelDemandReportVersionDto channelDemandReportVersionDto = new ChannelDemandReportVersionDto();
            channelDemandReportVersionDto.setRollingVersion(rollingVersion);
            channelDemandReportVersionDto.setLv3ChannelCodes(lv3ChannelCodes);
            channelDemandReportDao.updateChannelDemandReportVersionLv3ChannelCodes(channelDemandReportVersionDto);
//            saveDataIntoDbClock.stop();
//            log.info("saveDataIntoDbClock cost:{}s ",saveDataIntoDbClock.getTotalTimeSeconds());
//            log.info("saveDataIntoDbClock cost:{},",saveDataIntoDbClock.prettyPrint());
        }
    }



    /**
     * <AUTHOR>
     * @Description 自定义读监听器
     * @date 2023/10/30 16:34
     */
    private class CustomChannelCodeReadListener implements ReadListener<ImportChannelDemandReportVo>
    {


        /**
         * 当前日，格式：yyyyMMdd
         */
        private final String currentDay = DateUtils.getDate(DateUtils.YMD);

        /**
         * 固定字段最后一列索引
         */
        private Integer fieldLastCol;

        /**
         * 动态列最后一列索引
         */
        private Integer headLastCol;

        /**
         * 列和参数key映射关系，Key：电子表格列索引，Value：调用dataq参数的key
         */
        private final Map<Integer, String> columnWeekDate;

        /**
         * 缓存的数据
         */
        private List<ImportChannelDemandReportVo> cachedDataList;

        /**
         * 导入参数
         */
        private final ExcelCondition condition;

        /**
         * 导入结果
         */
        private final ExcelResult<ExcelData> excelResult;

        private Map<ChannelDemandReportDto, Map<Long, Double>> channelDemandReportMap;

        private Account currentAccount;

        public int count = 0;

        private String extend = "文件导入";

        public Set<String> skuCodes = new HashSet<>();

        public CustomChannelCodeReadListener(ExcelCondition condition)
        {
            Class clazz = ImportChannelDemandReportVo.class;
            // 遍历所有字段，获取所有ExcelProperty注解
            this.fieldLastCol = 0;
            for (Field field : clazz.getDeclaredFields())
            {
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                if (Objects.isNull(excelProperty))
                {
                    continue;
                }
                if (excelProperty.index() > this.fieldLastCol)
                {
                    this.fieldLastCol = excelProperty.index();
                }
            }
            this.columnWeekDate = new HashMap<>();
            this.condition = condition;
            this.excelResult = new ExcelResult<>();
            this.excelResult.setSuccess(0L);
            this.excelResult.setFailed(0L);
            this.excelResult.setFailedList(new ArrayList<ExcelData>());
            //            this.currentAccount = condition.getSession().getAccount();
        }

        /**
         * @param headMap
         * @param context
         * @Description 读取表头数据，根据表头封装监听类成员变量
         * <AUTHOR>
         * @date 2023年10月30日 16:45
         */
        @Override
        public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context)
        {

            StopWatch excelHeaderReadClock = new StopWatch("excelHeaderChannelRead");
            excelHeaderReadClock.start("readchannel");
            for(int i = 0; i <headerKeys.length;i++){
                String v = (null == headMap.get(i).getStringValue())?"":headMap.get(i).getStringValue();
                if(!v.equals(headerKeys[i])){
                    throw new ServiceException("导入数据文件表头错误");
                }
            }
            // 只读取隐藏的表头列，隐藏列中动态表头的值作为dataq批量导入提报数据动态参数的key
            if (context.readRowHolder().getRowIndex() == headRowNum - 1)
            {
                // 封装动态表头数据，固定字段最后一列后的所有列，都是动态列
                for (Integer i : headMap.keySet())
                {
                    if (i > fieldLastCol)
                    {
                        this.columnWeekDate.put(i, headMap.get(i).getStringValue());
                    }
                }
                this.headLastCol = headMap.keySet().stream().max((x, y) -> Integer.compare(x, y)).get();
            }
            excelHeaderReadClock.stop();
            log.info("excelHeaderRead cost:{}s ",excelHeaderReadClock.getTotalTimeSeconds());
            log.info("excelHeaderRead cost:{}",excelHeaderReadClock.prettyPrint());
        }

        /**
         * @param importChannelDemandVo
         * @param analysisContext
         * @Description 读取一条数据
         * <AUTHOR>
         * @date 2023年10月19日 15:06
         */
        @Override
        public void invoke(ImportChannelDemandReportVo importChannelDemandReportVo, AnalysisContext context)
        {
//            StopWatch dataProcessClock = new StopWatch("excelChanneldataProcess");
            // 校验数据，校验失败的数据记录失败列表和失败原因
            try
            {
//                dataProcessClock.start("checkData");
                // 设置行号
                importChannelDemandReportVo.setRowIndex(context.readRowHolder().getRowIndex());

//                checkData(importChannelDemandReportVo);
//                dataProcessClock.stop();

//                dataProcessClock.start("dataPreDeal");
//                Map<Integer, Cell> cellMap = context.readRowHolder().getCellMap();
//                for (Integer i : cellMap.keySet())
//                {
//                    if (i > fieldLastCol && i <= headLastCol)
//                    {
//                        CellData<String> cellData = (CellData) cellMap.get(i);
//                        String value = null;
//                        if (CellDataTypeEnum.NUMBER.equals(cellData.getType()))
//                        {
//                            value = StringUtils.substringBefore(StringUtils.getValue(cellData.getNumberValue()), StringUtils.POINT_SEPARATOR);
//                        }
//                        else if (CellDataTypeEnum.STRING.equals(cellData.getType()))
//                        {
//                            value = StringUtils.substringBefore(cellData.getStringValue(), StringUtils.POINT_SEPARATOR);
//                        }
//                        else
//                        {
//                            value = "0";
//                        }
//                        value = StringUtils.replace(value, StringUtils.SPACE, StringUtils.EMPTY);
//                        if (StringUtils.isBlank(value))
//                        {
//                            value = "0";
//                        }
//                        if(!(this.columnWeekDate.get(i).indexOf(monthTotalColumnHeadKey) > 0)){//剔除掉如：“5月已过周实发数量、5月已过周计划偏差、5月合计”列
//                            if (!value.matches(CommonConstants.PLAN_VALUE_PATTERN))
//                            {
//                                throw new ServiceException("提报数据必须为非负整数");
//                            }
//                            importChannelDemandReportVo.getWeekDateValue().put(this.columnWeekDate.get(i), Double.valueOf(value));
//                        }
//                    }
//                }
//                dataProcessClock.stop();
            }
            catch (ServiceException e)
            {
//                log.error("CustomReadListener invoke has exception:{}", e);
//                this.excelResult.setFailed(this.excelResult.getFailed() + 1);
//                importChannelDemandReportVo.setResult(StringUtils.isEmpty(e.getErrorMessage()) ? I18nUtils.getValue(e.errorCode) : e.getErrorMessage());
//                this.excelResult.getFailedList().add(importChannelDemandReportVo);
                return;
            }
            catch (Exception e)
            {
//                log.error("CustomReadListener invoke has exception:{}", e);
//                importChannelDemandReportVo.setResult(I18nUtils.getValue(ErrorCodeConstants.FAIL_OTHER));
//                this.excelResult.setFailed(this.excelResult.getFailed() + 1);
//                this.excelResult.getFailedList().add(importChannelDemandReportVo);
                return;
            }

            // 封装导入表格外的导入参数
            importChannelDemandReportVo.setRollingVersion(this.condition.getCondition().getString("rollingVersion"));

            skuCodes.add(importChannelDemandReportVo.getSkuCode());
//
//            log.info("dataProcessClock cost:{}s ",dataProcessClock.getTotalTimeSeconds());
//            log.info("dataProcessClock cost:{}",dataProcessClock.prettyPrint());
        }

        /**
         * @param analysisContext
         * @Description 读取数据结束
         * <AUTHOR>
         * @date 2023年10月19日 15:06
         */
        @Override
        public void doAfterAllAnalysed(AnalysisContext context)
        {
//            if (CollectionUtils.isEmpty(cachedDataList))
//            {
//                return;
//            }
//
//            try
//            {
//                // 保存最后遗留的零头数据
//                saveDataIntoDb(cachedDataList);
//                cachedDataList.forEach(item -> {
//                    item.setResult(I18nUtils.getValue(ErrorCodeConstants.SUCCESS));
//                });
//                this.excelResult.setSuccess(this.excelResult.getSuccess() + cachedDataList.size());
//            }
//            catch (ServiceException e)
//            {
//                log.error("CustomReadListener doAfterAllAnalysed has exception:{}", e);
//                cachedDataList.forEach(item -> {
//                    item.setResult(StringUtils.isBlank(e.errorMessage) ? I18nUtils.getValue(e.errorCode) : e.errorMessage);
//                });
//                this.excelResult.setFailed(this.excelResult.getFailed() + cachedDataList.size());
//                this.excelResult.getFailedList().addAll(cachedDataList);
//            }
//            catch (Exception e)
//            {
//                log.error("CustomReadListener doAfterAllAnalysed has exception:{}", e);
//                cachedDataList.forEach(item -> {
//                    item.setResult(I18nUtils.getValue(ErrorCodeConstants.FAIL_OTHER));
//                });
//                this.excelResult.setFailed(this.excelResult.getFailed() + cachedDataList.size());
//                this.excelResult.getFailedList().addAll(cachedDataList);
//            }
        }



    }
}
