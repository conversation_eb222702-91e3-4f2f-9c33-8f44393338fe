package cn.aliyun.ryytn.modules.demand.service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.page.PageUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.ForecastResultService;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqForecastResultDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDetailChart;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelForecastDateResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelForecastResultBaseTable;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultDetailReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultDetailRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultDetaildataqResultVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastChannelFilterListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastChannelListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseFilterListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseForecastResultRspVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 预测结果接口实现类
 * <AUTHOR>
 * @date 2023/10/23 17:59
 */
@Service
@Slf4j
public class ForecastResultServiceImpl implements ForecastResultService
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private DataqForecastResultDao dataqForecastResultDao;

    /**
     * @Description 查询渠道预测结果筛选条件
     * @param queryForecastChannelFilterListReqVo
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 18:11
     */
    @Override
    public DataqResult<?> queryChannelForecastResultFilterList(QueryForecastChannelFilterListReqVo queryForecastChannelFilterListReqVo) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_FILTER_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryForecastChannelFilterListReqVo);

        return dataqResult;
    }

    /**
     *
     * @Description 分页查询渠道预测结果
     * @param condition
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 18:20
     */
    @Override
    public DataqResult<PageInfo<?>> pageChannelForecastResultList(PageCondition<QueryForecastChannelListReqVo> condition) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_FILTER_LIST"));
        Map<String, Object> map = new HashMap<>();
        map.put("group_by", "prediction_version");
        // 聚合字段 预测周期最小值，预测周期最大值
        map.put("aggregation", "target_biz_date,MAX,originalMaxTargetBizDate;target_biz_date,MIN,originalMinTargetBizDate");
        map.put("order_by", "originalMinTargetBizDate");
        map.put("page_size", condition.getPageSize());
        map.put("page_token", condition.getPageNum());
        map.put("fetch_all", true);
        condition.getCondition().setSelectColumns("target_biz_date,prediction_version");
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, map, condition.getCondition());
        // 将json转换成list
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            DataqResult<PageInfo<?>> pageInfoDataqResult = new DataqResult<>();
            pageInfoDataqResult.setData(null);
            return pageInfoDataqResult;
        }
        List<QueryChannelForecastResultRspVo> dataqResultList = jsonArray.toJavaList(QueryChannelForecastResultRspVo.class);

        for (QueryChannelForecastResultRspVo i : dataqResultList)
        {
            // 将20230101格式日期转换成2023/01
            i.setMinTargetBizDate(new String(convertDate(i.getOriginalMinTargetBizDate())));
            i.setMaxTargetBizDate(new String(convertDate(i.getOriginalMaxTargetBizDate())));
        }

        // 封装分页信息
        PageInfo pageInfo = PageUtils.init(dataqResultList, condition.getPageNum(), condition.getPageSize(), Integer.valueOf(dataqResult.getTotal()));
        DataqResult<PageInfo<?>> pageInfoDataqResult = new DataqResult<>();
        pageInfoDataqResult.setData(pageInfo);

        return pageInfoDataqResult;
    }

    private String convertDate(String targetBizDate)
    {
        String year = targetBizDate.substring(0, 4);
        String month = targetBizDate.substring(4, 6);
        return year + "/" + month;
    }

    /**
     *
     * @Description 查询分仓预测结果过滤条件
     * @param queryForecastWarehouseFilterListReqVo
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 9:31
     */
    @Override
    public DataqResult<?> queryWarehouseForecastResultFilterList(QueryForecastWarehouseFilterListReqVo queryForecastWarehouseFilterListReqVo) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_WAREHOUSE_FILTER_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryForecastWarehouseFilterListReqVo);

        return dataqResult;
    }

    /**
     *
     * @Description 分页查询分仓预测结果
     * @param condition
     * @return DataqResult<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 9:31
     */
    @Override
    public DataqResult<PageInfo<?>> pageWarehouseForecastResultList(PageCondition<QueryForecastWarehouseListReqVo> condition) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_WAREHOUSE_LIST"));
        Map<String, Object> map = new HashMap<>();
        map.put("group_by", "prediction_version");
        // 聚合字段 预测周期最小值，预测周期最大值
        map.put("aggregation", "target_biz_date,MAX,originalMaxTargetBizDate;target_biz_date,MIN,originalMinTargetBizDate");
        map.put("order_by", "originalMinTargetBizDate");
        map.put("page_size", condition.getPageSize());
        map.put("page_token", condition.getPageNum());
        map.put("fetch_all", true);
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, map, condition.getCondition());
        // 将json转换成list
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            DataqResult<PageInfo<?>> pageInfoDataqResult = new DataqResult<>();
            pageInfoDataqResult.setData(null);
            return pageInfoDataqResult;
        }
        List<QueryChannelForecastResultRspVo> dataqResultList = jsonArray.toJavaList(QueryChannelForecastResultRspVo.class);

        for (QueryChannelForecastResultRspVo i : dataqResultList)
        {
            // 将20230101格式日期转换成2023/01
            i.setMinTargetBizDate(new String(convertDate(i.getOriginalMinTargetBizDate())));
            i.setMaxTargetBizDate(new String(convertDate(i.getOriginalMaxTargetBizDate())));
        }

        // 封装分页信息
        PageInfo pageInfo = PageUtils.init(dataqResultList, condition.getPageNum(), condition.getPageSize(), Integer.valueOf(dataqResult.getTotal()));
        DataqResult<PageInfo<?>> pageInfoDataqResult = new DataqResult<>();
        pageInfoDataqResult.setData(pageInfo);

        return pageInfoDataqResult;
    }

    /**
     *
     * @Description 查询渠道预测结果
     * @param queryChannelForecastResultReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月01日 15:28     */
    @Override
    public ChannelForecastResultBaseTable<?> queryChannelForecastResult(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo) throws Exception
    {
        ChannelForecastResultBaseTable<List<QueryForecastResultRspVo>> baseTable = new ChannelForecastResultBaseTable();
        // 查询动态字段信息
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_COLUMN"));
        DataqResult<?> columnResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelForecastResultReqVo);
        List<JSONObject> columnList = (List<JSONObject>) columnResult.getData();
        if (CollectionUtils.isEmpty(columnList))
        {
            return baseTable;
        }

        // 根据动态字段查询数据
        String path2 = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL"));
        queryChannelForecastResultReqVo.setSelectColumn(columnList.get(0).getString("trends_column"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path2, null, null, queryChannelForecastResultReqVo);
        List<JSONObject> forecastResultList = (List<JSONObject>) dataqResult.getData();
        if (CollectionUtils.isEmpty(forecastResultList))
        {
            return baseTable;
        }

        // 遍历响应数据所有key，过滤时间动态字段，并根据字段正序排序
        List<String> headList = forecastResultList.get(0).keySet().stream().filter(item -> {
            return Pattern.matches("^\\d{2}/\\d{2}$", item);
        }).sorted((o1, o2) -> {
            return o1.compareTo(o2);
        }).collect(Collectors.toList());
        baseTable.setHeadArray(headList);

        // 解析dataq响应，并转换动态字段格式封装为固定字段
        List<QueryForecastResultRspVo> dataList = new ArrayList<>();
        for (JSONObject jsonObject : forecastResultList)
        {
            QueryForecastResultRspVo queryForecastResultRspVo = jsonObject.toJavaObject(QueryForecastResultRspVo.class);

            for (String key : headList)
            {
                Double value = Objects.isNull(jsonObject.getDouble(key)) ? 0d : jsonObject.getDouble(key);
                queryForecastResultRspVo.getDataMap().put(key, value);
            }
            dataList.add(queryForecastResultRspVo);
        }
        // 按照产品分类 产品大类 产品小类 一级渠道 二级渠道 升序排序
        dataList = dataList.stream().
            sorted(Comparator.comparing(QueryForecastResultRspVo::getLv1CategoryName, Comparator.nullsLast(String::compareTo)).
                thenComparing(QueryForecastResultRspVo::getLv2CategoryName, Comparator.nullsLast(String::compareTo)).
                thenComparing(QueryForecastResultRspVo::getLv3CategoryName, Comparator.nullsLast(String::compareTo)).
                thenComparing(QueryForecastResultRspVo::getLv1ChannelName, Comparator.nullsLast(String::compareTo)).
                thenComparing(QueryForecastResultRspVo::getLv2ChannelName, Comparator.nullsLast(String::compareTo))).
            collect(Collectors.toList());

        baseTable.setList(dataList);

        // 查询周期起始和结束具体时间
        String path3 = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_FILTER_LIST"));
        Map<String, Object> queryDateMap = new HashMap<>();
        queryDateMap.put("aggregation", "target_biz_date,MAX,endDate;target_biz_date,MIN,startDate");
        queryChannelForecastResultReqVo.setSelectColumns("target_biz_date");
        DataqResult<?> dateDataqResult = dataqService.invoke(HttpMethod.POST, path3, null, queryDateMap, queryChannelForecastResultReqVo);
        JSONArray jsonArray = (JSONArray) dateDataqResult.getData();
        if (!CollectionUtils.isEmpty(jsonArray))
        {
            JSONObject jsonObject = jsonArray.getJSONObject(0);
            ChannelForecastDateResultRspVo channelForecastDateResultRspVo = jsonObject.toJavaObject(ChannelForecastDateResultRspVo.class);
            String start = channelForecastDateResultRspVo.getStartDate();
            String end = channelForecastDateResultRspVo.getEndDate();
            baseTable.setStartDate(start.substring(0, 4) + "-" + start.substring(4, 6) + "-" + start.substring(6, 8));
            baseTable.setEndDate(end.substring(0, 4) + "-" + end.substring(4, 6) + "-" + end.substring(6, 8));
        }

        return baseTable;
    }

    /**
     *
     * @Description 查询分仓预测结果动态表头
     * @param queryChannelForecastResultReqVo
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 11:26
     */
    @Override
    public List<String> queryChannelForecastResultHeadList(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo) throws Exception
    {
        queryChannelForecastResultReqVo.setPeriodType("month");
        List<String> targetBizDateList = dataqForecastResultDao.queryChannelForecastResultHeadList(queryChannelForecastResultReqVo);
        List<String> headList = new ArrayList<>(targetBizDateList.size());
        for (String targetBizDate : targetBizDateList)
        {
            targetBizDate = DateUtils.formatTime(DateUtils.parseDate(targetBizDate, DateUtils.YMD), DateUtils.YM_SLASH_YY);
            headList.add(targetBizDate);
        }

        return headList;
    }

    /**
     *
     * @Description 查询渠道预测结果表头筛选条件
     * @param queryChannelForecastResultReqVo
     * @return List<QueryForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月29日 14:22
     */
    @Override
    public List<QueryForecastResultRspVo> queryChannelForecastResultHeadSelect(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo) throws Exception
    {
        queryChannelForecastResultReqVo.setPeriodType("month");
        GroupColumnEnum groupColumnEnum = queryChannelForecastResultReqVo.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        queryChannelForecastResultReqVo.setGroupColumn(groupColumn);
        queryChannelForecastResultReqVo.setSortColumn(sortColumn);

        List<QueryForecastResultRspVo> result = dataqForecastResultDao.queryChannelForecastResultHeadSelect(queryChannelForecastResultReqVo);
        return result;
    }

    /**
     *
     * @Description 分组聚合查询渠道预测结果列表
     * @param queryChannelForecastResultReqVo
     * @return List<QueryForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月09日 11:21
     */
    @Override
    public List<QueryForecastResultRspVo> queryChannelForecastResultGroupBy(QueryChannelForecastResultReqVo queryChannelForecastResultReqVo) throws Exception
    {
        // 分组查询
        queryChannelForecastResultReqVo.setPeriodType("month");
        List<GroupColumnEnum> groupColumnEnumList = queryChannelForecastResultReqVo.getGroupColumnList();
        List<QueryForecastResultRspVo> dataList = new ArrayList<>();
        StringBuilder groupColumnSB = new StringBuilder();
        StringBuilder sortColumnSB = new StringBuilder();
        for (GroupColumnEnum groupColumnEnum : groupColumnEnumList)
        {
            String groupColumn = groupColumnSB.append(groupColumnEnum.getColumnName()).toString();
            String sortColumn = sortColumnSB.append(groupColumnEnum.getSortColumn().getColumnName()).toString();
            queryChannelForecastResultReqVo.setGroupColumn(groupColumn);
            queryChannelForecastResultReqVo.setSortColumn(sortColumn);
            List<QueryForecastResultRspVo> list =
                dataqForecastResultDao.queryChannelForecastResultGroupBy(queryChannelForecastResultReqVo);
            if (CollectionUtils.isNotEmpty(list))
            {
                dataList.addAll(list);
            }

            groupColumnSB.append(StringUtils.COMMA_SEPARATOR);
            sortColumnSB.append(StringUtils.COMMA_SEPARATOR);
        }

        // 解析动态字段key，封装表头
        List<String> targetBizDateList = dataqForecastResultDao.queryChannelForecastResultHeadList(queryChannelForecastResultReqVo);

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (QueryForecastResultRspVo queryForecastResultRspVo : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(queryForecastResultRspVo.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String planDate = planValue.getPlanDate();
                String key = DateUtils.formatTime(DateUtils.parseDate(planDate, DateUtils.YMD), DateUtils.YM_SLASH_YY);
                queryForecastResultRspVo.getDataMap().put(key, planValue.getPlanValue());
            }
            queryForecastResultRspVo.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 分页查询分仓预测结果
     * @param condition
     * @return PageInfo<QueryWarehouseForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 11:26
     */
    @Override
    public PageInfo<QueryForecastResultRspVo> queryChannelForecastResultPage(PageCondition<QueryChannelForecastResultReqVo> condition)
        throws Exception
    {
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        QueryChannelForecastResultReqVo queryChannelForecastResultReqVo = condition.getCondition();
        queryChannelForecastResultReqVo.setPeriodType("month");

        // dataq接口效率过低，修改为直接读数据库
        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        PageHelper.startPage(pageNum, pageSize);
        List<QueryForecastResultRspVo> keyList =
            dataqForecastResultDao.queryChannelForecastResultDataKeyList(queryChannelForecastResultReqVo);
        PageInfo pageInfo = new PageInfo(keyList);
        if (CollectionUtils.isEmpty(keyList))
        {
            return pageInfo;
        }

        int pageTotal = Integer.parseInt(String.valueOf(pageInfo.getTotal()));

        queryChannelForecastResultReqVo.setKeyList(keyList);

        // 获取所有周数据
        List<String> targetBizDateList = dataqForecastResultDao.queryChannelForecastResultHeadList(queryChannelForecastResultReqVo);

        List<QueryForecastResultRspVo> dataList =
            dataqForecastResultDao.queryChannelForecastResultDataJsonList(queryChannelForecastResultReqVo);

        // 转换动态字段格式封装为固定字段
        for (QueryForecastResultRspVo data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String planDate = planValue.getPlanDate();
                String key = DateUtils.formatTime(DateUtils.parseDate(planDate, DateUtils.YMD), DateUtils.YM_SLASH_YY);
                data.getDataMap().put(key, planValue.getPlanValue());
            }
            data.setData(null);
        }

        PageInfo<QueryForecastResultRspVo> result = PageUtils.init(dataList, pageNum, pageSize, pageTotal);
        return result;
    }

    /**
     *
     * @Description 查询渠道预测结果下拉列表
     * @param queryForecastChannelListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月07日 10:21     */
    @Override
    public DataqResult<?> queryChannelForecastResultList(QueryForecastChannelListReqVo queryForecastChannelListReqVo) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_FILTER_LIST"));
        queryForecastChannelListReqVo.setSelectColumns("prediction_version");
        Map<String, Object> map = new HashMap<>();
        map.put("order_by", "prediction_version desc");
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, map, queryForecastChannelListReqVo);

        return dataqResult;
    }

    /**
     *
     * @Description 查询渠道预测结果详情
     * @param queryChannelForecastResultDetailReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月08日 10:20     */
    @Override
    public QueryChannelForecastResultDetailRspVo queryChannelForecastResultDetail(QueryChannelForecastResultDetailReqVo queryChannelForecastResultDetailReqVo)
        throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_DETAIL"));
        Map<String, Object> map = new HashMap<>();
        map.put("order_by", "targetBizDate");
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, map, queryChannelForecastResultDetailReqVo);

        // 解析dataq响应
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        List<QueryChannelForecastResultDetaildataqResultVo> dataqResultList = jsonArray.toJavaList(QueryChannelForecastResultDetaildataqResultVo.class);

        // 重新封装响应
        QueryChannelForecastResultDetailRspVo queryChannelForecastResultDetailRspVo = new QueryChannelForecastResultDetailRspVo();
        repackageChannelForecastResultDetailResponse(queryChannelForecastResultDetailRspVo, dataqResultList, queryChannelForecastResultDetailReqVo);

        return queryChannelForecastResultDetailRspVo;
    }

    /**
     *
     * @Description 查询渠道预测结果报表
     * @param queryChannelForecastResultDetailReqVo
     * @return QueryChannelForecastResultDetailRspVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月28日 15:47
     */
    @Override
    public QueryChannelForecastResultDetailRspVo queryChannelForecastResultDetailReport(
        QueryChannelForecastResultDetailReqVo queryChannelForecastResultDetailReqVo) throws Exception
    {
        // 计算报表时间周期：当前渠道预测结果版本的时间范围+当前日期所在自然年(最多23个月)
        QueryChannelForecastResultReqVo queryChannelForecastResultReqVo = new QueryChannelForecastResultReqVo();
        queryChannelForecastResultReqVo.setAlgoNameAndVersion(queryChannelForecastResultDetailReqVo.getAlgoVersion());
        queryChannelForecastResultReqVo.setPredictionVersion(queryChannelForecastResultDetailReqVo.getPredictionVersion());
        queryChannelForecastResultReqVo.setPeriodType("month");
        List<String> targetBizDateList = dataqForecastResultDao.queryChannelForecastResultHeadList(queryChannelForecastResultReqVo);
        TreeSet<String> treeSet = new TreeSet<>(targetBizDateList);

        String currentYear = DateUtils.getDate(DateUtils.Y);
        String day = "01";
        String format = "%02d";
        for (int i = 1; i <= 12; i++)
        {
            treeSet.add(currentYear + String.format(format, i) + day);
        }
        List<String> dateList = treeSet.stream().limit(23).collect(Collectors.toList());

        String beginDate = dateList.get(0);
        String endDate = dateList.get(dateList.size() - 1);
        queryChannelForecastResultDetailReqVo.setStartDate(DateUtils.formatDate(DateUtils.parseDate(beginDate, DateUtils.YMD), DateUtils.YMD_DASH));
        queryChannelForecastResultDetailReqVo.setEndDate(DateUtils.formatDate(DateUtils.parseDate(endDate, DateUtils.YMD), DateUtils.YMD_DASH));


        if (StringUtils.isEmpty(queryChannelForecastResultDetailReqVo.getGroupColumn()))
        {
            String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_CHANNEL_DETAIL"));
            Map<String, Object> param = new HashMap<>();
            param.put("order_by", "targetbizdate");
            DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryChannelForecastResultDetailReqVo);

            // 解析dataq响应
            JSONArray jsonArray = (JSONArray) dataqResult.getData();
            List<QueryChannelForecastResultDetaildataqResultVo> dataqResultList = jsonArray.toJavaList(QueryChannelForecastResultDetaildataqResultVo.class);

            Map<String, QueryChannelForecastResultDetaildataqResultVo> map =
                dataqResultList.stream().collect(Collectors.toMap(QueryChannelForecastResultDetaildataqResultVo::getTargetbizdate, Function.identity(),
                    (key1, key2) -> key2));

            Set<String> targetBizDateSet = new HashSet<>();
            List<ChannelDetailChart> chartList = new ArrayList<>(map.keySet().size());
            for (QueryChannelForecastResultDetaildataqResultVo item : dataqResultList)
            {
                if (targetBizDateSet.contains(item.getTargetbizdate()))
                {
                    continue;
                }
                ChannelDetailChart channelDetailChart = new ChannelDetailChart();
                channelDetailChart.setTargetBizDate(item.getTargetbizdate());
                channelDetailChart.setPlanOrderNum(Objects.isNull(item.getPlanordernum()) ? StringUtils.ZERO : item.getPlanordernum());
                channelDetailChart.setPredictionResult(Objects.isNull(item.getPredictionresult()) ? StringUtils.ZERO : item.getPredictionresult());
                channelDetailChart.setReportingOrderNum(Objects.isNull(item.getReportingordernum()) ? StringUtils.ZERO : item.getReportingordernum());
                channelDetailChart.setObserValue(Objects.isNull(item.getObservalue()) ? StringUtils.ZERO : item.getObservalue());
                channelDetailChart.setOldObserValue(Objects.isNull(item.getOldobservalue()) ? StringUtils.ZERO : item.getOldobservalue());
                chartList.add(channelDetailChart);
                targetBizDateSet.add(item.getTargetbizdate());
            }
            targetBizDateSet.clear();

            // 重新封装响应
            QueryChannelForecastResultDetaildataqResultVo queryChannelForecastResultDetaildataqResultVo = dataqResultList.get(0);
            QueryChannelForecastResultDetailRspVo queryChannelForecastResultDetailRspVo = new QueryChannelForecastResultDetailRspVo();
            queryChannelForecastResultDetailRspVo.setLv1ChannelCode(queryChannelForecastResultDetaildataqResultVo.getLv1channelcode());
            queryChannelForecastResultDetailRspVo.setLv1ChannelName(queryChannelForecastResultDetaildataqResultVo.getLv1channelname());
            queryChannelForecastResultDetailRspVo.setLv2ChannelCode(queryChannelForecastResultDetaildataqResultVo.getLv2channelcode());
            queryChannelForecastResultDetailRspVo.setLv2ChannelName(queryChannelForecastResultDetaildataqResultVo.getLv2channelname());
            queryChannelForecastResultDetailRspVo.setLv1CategoryCode(queryChannelForecastResultDetaildataqResultVo.getLv1categorycode());
            queryChannelForecastResultDetailRspVo.setLv1CategoryName(queryChannelForecastResultDetaildataqResultVo.getLv1categoryname());
            queryChannelForecastResultDetailRspVo.setLv2CategoryCode(queryChannelForecastResultDetaildataqResultVo.getLv2categorycode());
            queryChannelForecastResultDetailRspVo.setLv2CategoryName(queryChannelForecastResultDetaildataqResultVo.getLv2categoryname());
            queryChannelForecastResultDetailRspVo.setLv3CategoryCode(queryChannelForecastResultDetaildataqResultVo.getLv3categorycode());
            queryChannelForecastResultDetailRspVo.setLv3CategoryName(queryChannelForecastResultDetaildataqResultVo.getLv3categoryname());
            queryChannelForecastResultDetailRspVo.setDataUnit(queryChannelForecastResultDetaildataqResultVo.getDataunit());
            queryChannelForecastResultDetailRspVo.setPeriodType("MONTH");
            queryChannelForecastResultDetailRspVo.setAlgoVersion(queryChannelForecastResultDetailReqVo.getAlgoVersion());
            queryChannelForecastResultDetailRspVo.setStartDate(DateUtils.formatDate(DateUtils.parseDate(beginDate, DateUtils.YMD), DateUtils.YM_SLASH));
            queryChannelForecastResultDetailRspVo.setEndDate(DateUtils.formatDate(DateUtils.parseDate(endDate, DateUtils.YMD), DateUtils.YM_SLASH));
            queryChannelForecastResultDetailRspVo.setChartList(chartList);

            return queryChannelForecastResultDetailRspVo;
        }
        else
        {
            queryChannelForecastResultDetailReqVo.setSelectColumn(queryChannelForecastResultDetailReqVo.getGroupColumn());

            String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_GROUPING_DEMAND_FORECAST_CHANNEL_DETAIL"));
            DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelForecastResultDetailReqVo);

            // 解析dataq响应
            JSONArray jsonArray = (JSONArray) dataqResult.getData();
            List<QueryChannelForecastResultDetaildataqResultVo> dataqResultList = jsonArray.toJavaList(QueryChannelForecastResultDetaildataqResultVo.class);

            Map<String, QueryChannelForecastResultDetaildataqResultVo> map =
                dataqResultList.stream().collect(Collectors.toMap(QueryChannelForecastResultDetaildataqResultVo::getTargetbizdate, Function.identity(),
                    (key1, key2) -> key2));

            Set<String> targetBizDateSet = new HashSet<>();
            List<ChannelDetailChart> chartList = new ArrayList<>(map.keySet().size());
            for (QueryChannelForecastResultDetaildataqResultVo item : dataqResultList)
            {
                if (targetBizDateSet.contains(item.getTargetbizdate()))
                {
                    continue;
                }
                ChannelDetailChart channelDetailChart = new ChannelDetailChart();
                channelDetailChart.setTargetBizDate(item.getTargetbizdate());
                channelDetailChart.setPlanOrderNum(Objects.isNull(item.getPlanordernum()) ? StringUtils.ZERO : item.getPlanordernum());
                channelDetailChart.setPredictionResult(Objects.isNull(item.getPredictionresult()) ? StringUtils.ZERO : item.getPredictionresult());
                channelDetailChart.setReportingOrderNum(Objects.isNull(item.getReportingordernum()) ? StringUtils.ZERO : item.getReportingordernum());
                channelDetailChart.setObserValue(Objects.isNull(item.getObservalue()) ? StringUtils.ZERO : item.getObservalue());
                channelDetailChart.setOldObserValue(Objects.isNull(item.getOldobservalue()) ? StringUtils.ZERO : item.getOldobservalue());
                chartList.add(channelDetailChart);
                targetBizDateSet.add(item.getTargetbizdate());
            }
            targetBizDateSet.clear();

            // 重新封装响应
            QueryChannelForecastResultDetailRspVo queryChannelForecastResultDetailRspVo = new QueryChannelForecastResultDetailRspVo();
            queryChannelForecastResultDetailRspVo.setChartList(chartList);
            queryChannelForecastResultDetailRspVo.setDataUnit("提/罐");
            queryChannelForecastResultDetailRspVo.setLv1ChannelCode(queryChannelForecastResultDetailReqVo.getLv1ChannelCode());
            queryChannelForecastResultDetailRspVo.setLv1ChannelName(queryChannelForecastResultDetailReqVo.getLv1ChannelName());
            queryChannelForecastResultDetailRspVo.setLv2ChannelCode(queryChannelForecastResultDetailReqVo.getLv2ChannelCode());
            queryChannelForecastResultDetailRspVo.setLv2ChannelName(queryChannelForecastResultDetailReqVo.getLv2ChannelName());
            queryChannelForecastResultDetailRspVo.setLv1CategoryCode(queryChannelForecastResultDetailReqVo.getLv1CategoryCode());
            queryChannelForecastResultDetailRspVo.setLv1CategoryName(queryChannelForecastResultDetailReqVo.getLv1CategoryName());
            queryChannelForecastResultDetailRspVo.setLv2CategoryCode(queryChannelForecastResultDetailReqVo.getLv2CategoryCode());
            queryChannelForecastResultDetailRspVo.setLv2CategoryName(queryChannelForecastResultDetailReqVo.getLv2CategoryName());
            queryChannelForecastResultDetailRspVo.setLv3CategoryCode(queryChannelForecastResultDetailReqVo.getLv3CategoryCode());
            queryChannelForecastResultDetailRspVo.setLv3CategoryName(queryChannelForecastResultDetailReqVo.getLv3CategoryName());
            queryChannelForecastResultDetailRspVo.setPeriodType("MONTH");
            queryChannelForecastResultDetailRspVo.setAlgoVersion(queryChannelForecastResultDetailReqVo.getAlgoVersion());
            queryChannelForecastResultDetailRspVo.setStartDate(DateUtils.formatDate(DateUtils.parseDate(beginDate, DateUtils.YMD), DateUtils.YM_SLASH));
            queryChannelForecastResultDetailRspVo.setEndDate(DateUtils.formatDate(DateUtils.parseDate(endDate, DateUtils.YMD), DateUtils.YM_SLASH));

            return queryChannelForecastResultDetailRspVo;
        }
    }

    /**
     *
     * @Description 查询分仓预测结果下拉列表
     * @param queryForecastWarehouseListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月09日 15:09     */
    @Override
    public DataqResult<?> queryWarehouseForecastResultList(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_WAREHOUSE_LIST"));
        Map<String, Object> map = new HashMap<>();
        map.put("group_by", "prediction_version");
        // 聚合字段 预测周期最小值，预测周期最大值
        map.put("order_by", "prediction_version desc");
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, map, queryForecastWarehouseListReqVo);

        return dataqResult;
    }

    /**
     *
     * @Description 查询分仓预测结果
     * @param queryForecastWarehouseListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月10日 9:21     */
    @Override
    public BaseTable<?> queryWarehouseForecastResult(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo) throws Exception
    {
        BaseTable<List<QueryWarehouseForecastResultRspVo>> baseTable = new BaseTable();
        // 查询动态字段信息
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_WAREHOUSE_COLUMN"));
        DataqResult<?> columnResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryForecastWarehouseListReqVo);
        List<JSONObject> columnList = (List<JSONObject>) columnResult.getData();
        if (CollectionUtils.isEmpty(columnList))
        {
            return baseTable;
        }

        // 根据动态字段查询数据
        String path2 = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_FORECAST_WAREHOUSE"));
        queryForecastWarehouseListReqVo.setSelectColumn(columnList.get(0).getString("trends_column"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path2, null, null, queryForecastWarehouseListReqVo);
        List<JSONObject> forecastResultList = (List<JSONObject>) dataqResult.getData();
        if (CollectionUtils.isEmpty(forecastResultList))
        {
            return baseTable;
        }

        // 遍历响应数据所有key，过滤时间动态字段，并根据字段正序排序
        List<String> rawHeadList = forecastResultList.get(0).keySet().stream().filter(item -> {
            // y2024m02w03_12_18  结构示例
            return Pattern.matches("^y\\d{4}m\\d{2}w\\d{2}_\\d{2}_\\d{2}$", item);
        }).sorted((o1, o2) -> {
            return o1.compareTo(o2);
        }).collect(Collectors.toList());

        // 转换日期  例如y2024m02w03_12_18    ->     02W312-18
        List<String> headList = getHeadList(rawHeadList);
        baseTable.setHeadArray(headList);

        // 解析dataq响应，并转换动态字段格式封装为固定字段
        List<QueryWarehouseForecastResultRspVo> dataList = new ArrayList<>();

        for (JSONObject jsonObject : forecastResultList)
        {
            QueryWarehouseForecastResultRspVo queryWarehouseForecastResultRspVo = jsonObject.toJavaObject(QueryWarehouseForecastResultRspVo.class);

            for (String key : rawHeadList)
            {
                Double value = Objects.isNull(jsonObject.getDouble(key)) ? 0d : jsonObject.getDouble(key);
                queryWarehouseForecastResultRspVo.getDataMap().put(key, value);
            }
            dataList.add(queryWarehouseForecastResultRspVo);
        }

        // 按照产品分类 产品大类 产品小类 升序排序
        dataList = dataList.stream().
            sorted(Comparator.comparing(QueryWarehouseForecastResultRspVo::getLv1CategoryName, Comparator.nullsLast(String::compareTo)).
                thenComparing(QueryWarehouseForecastResultRspVo::getLv2CategoryName, Comparator.nullsLast(String::compareTo)).
                thenComparing(QueryWarehouseForecastResultRspVo::getLv3CategoryName, Comparator.nullsLast(String::compareTo))).
            collect(Collectors.toList());
        baseTable.setList(dataList);

        return baseTable;
    }

    /**
     *
     * @Description 查询分仓预测结果动态表头
     * @param queryForecastWarehouseListReqVo
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 11:26
     */
    @Override
    public List<String> queryWarehouseForecastResultHeadList(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo) throws Exception
    {
        List<String> targetBizDateList = dataqForecastResultDao.queryWarehouseForecastResultHeadList(queryForecastWarehouseListReqVo);

        // 查询日历周缓存
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, targetBizDateList);
        if (CollectionUtils.isEmpty(objectList))
        {
            return null;
        }
        // 封装动态字段列表，格式：MM月Ww(dd-dd)
        List<String> headList =
            objectList.stream().map(item -> (DataqWeek) item).sorted(Comparator.comparing(DataqWeek::getFsclWeekStart)).map(DataqWeek::getWeekLabel)
                .collect(Collectors.toList());

        return headList;
    }

    /**
     *
     * @Description 查询分仓预测结果表头下拉列表
     * @param queryForecastWarehouseListReqVo
     * @return List<QueryWarehouseForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @Override
    public List<QueryWarehouseForecastResultRspVo> queryWarehouseDemandReportHeadSelect(QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo)
    {
        GroupColumnEnum groupColumnEnum = queryForecastWarehouseListReqVo.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        queryForecastWarehouseListReqVo.setGroupColumn(groupColumn);
        queryForecastWarehouseListReqVo.setSortColumn(sortColumn);

        List<QueryWarehouseForecastResultRspVo> result = dataqForecastResultDao.queryWarehouseForecastResultHeadSelect(queryForecastWarehouseListReqVo);
        return result;
    }

    /**
     *
     * @Description 分页查询分仓预测结果
     * @param condition
     * @return PageInfo<QueryWarehouseForecastResultRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 11:26
     */
    @Override
    public PageInfo<QueryWarehouseForecastResultRspVo> queryWarehouseForecastResultPage(PageCondition<QueryForecastWarehouseListReqVo> condition)
        throws Exception
    {
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo = condition.getCondition();

        // dataq接口效率过低，修改为直接读数据库
        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        PageHelper.startPage(pageNum, pageSize);
        List<QueryWarehouseForecastResultRspVo> keyList =
            dataqForecastResultDao.queryWarehouseForecastResultDataKeyList(queryForecastWarehouseListReqVo);
        PageInfo pageInfo = new PageInfo(keyList);
        if (CollectionUtils.isEmpty(keyList))
        {
            return pageInfo;
        }

        int pageTotal = Integer.parseInt(String.valueOf(pageInfo.getTotal()));

        queryForecastWarehouseListReqVo.setKeyList(keyList);

        // 获取所有周数据
        List<String> targetBizDateList = dataqForecastResultDao.queryWarehouseForecastResultHeadList(queryForecastWarehouseListReqVo);
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, targetBizDateList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        List<QueryWarehouseForecastResultRspVo> dataList =
            dataqForecastResultDao.queryWarehouseForecastResultDataJsonList(queryForecastWarehouseListReqVo);

        // 转换动态字段格式封装为固定字段
        for (QueryWarehouseForecastResultRspVo data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String planDate = planValue.getPlanDate();
                DataqWeek dataqWeek = dataqWeekMap.get(planDate);
                data.getDataMap().put(dataqWeek.getWeekLabel(), planValue.getPlanValue());
            }
            data.setData(null);
        }

        PageInfo<QueryWarehouseForecastResultRspVo> result = PageUtils.init(dataList, pageNum, pageSize, pageTotal);
        return result;
    }

    private static List<String> getHeadList(List<String> rawHeadList)
    {
        List<String> headList = new ArrayList<>();
        for (String i : rawHeadList)
        {
            // 处理日期   类似 09处理成9,01处理成1
            char zero = 48;
            Object start, end;
            if (zero == i.charAt(12))
            {
                start = i.charAt(13);
            }
            else
            {
                start = i.substring(12, 14);
            }

            if (zero == i.charAt(15))
            {
                end = i.charAt(16);
            }
            else
            {
                end = i.substring(15, 17);
            }
            headList.add(i.substring(6, 8) + "W" + i.charAt(10) + start + "-" + end);

        }
        return headList;
    }

    private void repackageChannelForecastResultDetailResponse(QueryChannelForecastResultDetailRspVo rsp,
        List<QueryChannelForecastResultDetaildataqResultVo> dataqResultList, QueryChannelForecastResultDetailReqVo req)
    {
        if (!CollectionUtils.isEmpty(dataqResultList))
        {
            if (ObjectUtils.isNotEmpty(dataqResultList.get(0)))
            {
                QueryChannelForecastResultDetaildataqResultVo first = dataqResultList.get(0);
                rsp.setLv1ChannelCode(first.getLv2channelcode());
                rsp.setLv1ChannelName(first.getLv1channelname());
                rsp.setLv2ChannelCode(first.getLv2channelcode());
                rsp.setLv2ChannelName(first.getLv2channelname());
                rsp.setLv1CategoryCode(first.getLv1categorycode());
                rsp.setLv1CategoryName(first.getLv1categoryname());
                rsp.setLv2CategoryCode(first.getLv2categorycode());
                rsp.setLv2CategoryName(first.getLv2categoryname());
                rsp.setLv3CategoryCode(first.getLv3categorycode());
                rsp.setLv3CategoryName(first.getLv3categoryname());
                rsp.setDataUnit(first.getDataunit());
                rsp.setPeriodType(first.getPeriodtype());
                rsp.setAlgoVersion(req.getAlgoVersion());
                // 将2023-01-01 转换成 2023/01
                if (StringUtils.isNotEmpty(req.getStartDate()))
                {
                    rsp.setStartDate(req.getStartDate().substring(0, 4) + "/" + req.getStartDate().substring(5, 7));
                    rsp.setEndDate(req.getEndDate().substring(0, 4) + "/" + req.getEndDate().substring(5, 7));
                }
                rsp.setChannelChartTargetBizDateList(new LinkedList<>());
                rsp.setChannelChartPredictionResultList(new ArrayList<>());
                rsp.setChannelChartOldObserValueList(new ArrayList<>());
                rsp.setChannelChartObserValueList(new ArrayList<>());
                rsp.setChannelChartReportingOrderNumList(new ArrayList<>());
                rsp.setChannelChartPlanOrderNumList(new ArrayList<>());
                for (QueryChannelForecastResultDetaildataqResultVo i : dataqResultList)
                {
                    // 202301 转换成 2023/01
                    if (StringUtils.isNotEmpty(i.getTargetbizdate()))
                    {
                        rsp.getChannelChartTargetBizDateList().add(i.getTargetbizdate().substring(0, 4) + "/" + i.getTargetbizdate().substring(4, 6));
                    }
                    rsp.getChannelChartPredictionResultList().add(Objects.isNull(i.getPredictionresult()) ? StringUtils.ZERO : i.getPredictionresult());
                    rsp.getChannelChartOldObserValueList().add(Objects.isNull(i.getOldobservalue()) ? StringUtils.ZERO : i.getOldobservalue());
                    rsp.getChannelChartObserValueList().add(Objects.isNull(i.getObservalue()) ? StringUtils.ZERO : i.getObservalue());
                    rsp.getChannelChartReportingOrderNumList().add(Objects.isNull(i.getReportingordernum()) ? StringUtils.ZERO : i.getReportingordernum());
                    rsp.getChannelChartPlanOrderNumList().add(Objects.isNull(i.getPlanordernum()) ? StringUtils.ZERO : i.getPlanordernum());
                }
            }
        }
    }
}
