package cn.aliyun.ryytn.modules.demand.task;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.modules.distribution.api.InventoryStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 *
 * @Description 定时刷新库策略视图数据
 * <AUTHOR>
 * @date 2024/9/26 17:06
 */
@Slf4j
@Service
public class RereshInventoryStrategyViewServiceImpl implements TaskService
{

    @Autowired
    private InventoryStrategyService inventoryStrategyService;
    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        log.info(" RereshInventoryStrategyViewServiceImpl data start===========");
        inventoryStrategyService.rereshInventoryStrategyData2Table();
    }
}
