package cn.aliyun.ryytn.modules.demand.controller;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandReportService;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.DateLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 分仓需求提报
 * <AUTHOR>
 * @date 2023/11/27 15:46
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/warehouseDemandReport")
@Api(tags = "分仓需求提报")
public class WarehouseDemandReportController
{
    @Autowired
    private WarehouseDemandReportService warehouseDemandReportService;

    /**
     *
     * @Description 查询分仓需求提报对应渠道需求计划列表
     * @return ResultInfo<List < WarehouseDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 11:54
     */
    @PostMapping("queryWarehouseDemandReportPlanList")
    @ResponseBody
    @ApiOperation("查询分仓需求提报对应渠道需求计划列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<WarehouseDemandReportDto>> queryWarehouseDemandReportPlanList() throws Exception
    {
        List<WarehouseDemandReportDto> result = warehouseDemandReportService.queryWarehouseDemandReportPlanList();

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求提报版本列表
     * @param warehouseDemandReportDto
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 16:33
     */
    @PostMapping("queryWarehouseDemandReportVersionList")
    @ResponseBody
    @ApiOperation("查询分仓需求提报版本列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryWarehouseDemandReportVersionList(@RequestBody WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getDemandPlanCode());
        List<String> result = warehouseDemandReportService.queryWarehouseDemandReportVersionList(warehouseDemandReportDto.getDemandPlanCode());

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求提报列表
     * @param warehouseDemandReportDto
     * @return ResultInfo<BaseTable < List < WarehouseDemandReportDto>>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月30日 20:52
     */
    @PostMapping("queryWarehouseDemandReportList")
    @ResponseBody
    @ApiOperation("查询分仓需求提报列表")
    @RequiresPermissions(value = {})
    public ResultInfo<BaseTable<List<WarehouseDemandReportDto>>> queryWarehouseDemandReportList(@RequestBody WarehouseDemandReportDto warehouseDemandReportDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getRollingVersion());

        BaseTable<List<WarehouseDemandReportDto>> result = warehouseDemandReportService.queryWarehouseDemandReportList(warehouseDemandReportDto);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求提报编辑页面时间页签
     * @param warehouseDemandReportDto
     * @return ResultInfo<List < WarehouseDemandReportDateLabelVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 16:01
     */
    @PostMapping("queryWarehouseDemandReportDateLabelList")
    @ResponseBody
    @ApiOperation("查询分仓需求提报编辑页面时间页签")
    @RequiresPermissions(value = {})
    public ResultInfo<List<DateLabelVo>> queryWarehouseDemandReportDateLabelList(
        @RequestBody WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);

        List<DateLabelVo> result = warehouseDemandReportService.queryWarehouseDemandReportDateLabelList(warehouseDemandReportDto);

        return ResultInfo.success(result);
    }


    /**
     *
     * @Description 查询分仓需求提报编辑数据列表
     * @param warehouseDemandReportDto
     * @return ResultInfo<BaseTable < List < WarehouseDemandReportDto>>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 20:52
     */
    @PostMapping("queryWarehouseDemandReportDataList")
    @ResponseBody
    @ApiOperation("查询分仓需求提报编辑数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<BaseTable<List<WarehouseDemandReportDto>>> queryWarehouseDemandReportDataList(
        @RequestBody WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getRollingVersion());

        BaseTable<List<WarehouseDemandReportDto>> result = warehouseDemandReportService.queryWarehouseDemandReportDataList(warehouseDemandReportDto);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求提报动态表头
     * @param warehouseDemandReportDto
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:15
     */
    @PostMapping("queryWarehouseDemandReportHeadList")
    @ResponseBody
    @ApiOperation("查询分仓需求提报动态表头")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryWarehouseDemandReportHeadList(@RequestBody WarehouseDemandReportDto warehouseDemandReportDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getDemandPlanCode());

        List<String> result = Collections.EMPTY_LIST;
        if (StringUtils.isNotBlank(warehouseDemandReportDto.getRollingVersion()))
        {
            result = warehouseDemandReportService.queryWarehouseDemandReportHeadList(warehouseDemandReportDto);
        }
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求提报表头下拉列表
     * @param warehouseDemandReportDto
     * @return ResultInfo<List < WarehouseDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @PostMapping("queryWarehouseDemandReportHeadSelect")
    @ResponseBody
    @ApiOperation("查询分仓需求提报表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<WarehouseDemandReportDto>> queryWarehouseDemandReportHeadSelect(
        @RequestBody WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getGroupColumnList());

        List<WarehouseDemandReportDto> result = Collections.EMPTY_LIST;
        if (StringUtils.isNotBlank(warehouseDemandReportDto.getRollingVersion()))
        {
            result = warehouseDemandReportService.queryWarehouseDemandReportHeadSelect(warehouseDemandReportDto);
        }

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求提报分组聚合数据列表
     * @param warehouseDemandReportDto
     * @return ResultInfo<List < WarehouseDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:11
     */
    @PostMapping("queryWarehouseDemandReportListGroupBy")
    @ResponseBody
    @ApiOperation("查询分仓需求提报分组聚合数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<WarehouseDemandReportDto>> queryWarehouseDemandReportListGroupBy(@RequestBody WarehouseDemandReportDto warehouseDemandReportDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getGroupColumnList());

        List<WarehouseDemandReportDto> result = Collections.EMPTY_LIST;
        if (StringUtils.isNotBlank(warehouseDemandReportDto.getRollingVersion()))
        {
            result = warehouseDemandReportService.queryWarehouseDemandReportListGroupBy(warehouseDemandReportDto);
        }

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询分仓需求提报明细数据列表
     * @param condition
     * @return ResultInfo<PageInfo < WarehouseDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("queryWarehouseDemandReportDataPage")
    @ResponseBody
    @ApiOperation("分页查询分仓需求提报明细数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<WarehouseDemandReportDto>> queryWarehouseDemandReportDataPage(
        @RequestBody PageCondition<WarehouseDemandReportDto> condition) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getDemandPlanCode());

        PageInfo<WarehouseDemandReportDto> result = null;
        if (StringUtils.isNotBlank(condition.getCondition().getRollingVersion()))
        {
            result = warehouseDemandReportService.queryWarehouseDemandReportDataPage(condition);
        }
        else
        {
            result = new PageInfo<>();
        }

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求提报数据汇总
     * @param warehouseDemandReportDto
     * @return ResultInfo<Map < String, Double>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("queryWarehouseDemandReportSummary")
    @ResponseBody
    @ApiOperation("查询分仓需求提报数据汇总")
    @RequiresPermissions(value = {})
    public ResultInfo<Map<String, Double>> queryWarehouseDemandReportSummary(@RequestBody WarehouseDemandReportDto warehouseDemandReportDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getDemandPlanCode());

        Map<String, Double> result = Collections.EMPTY_MAP;
        if (StringUtils.isNotBlank(warehouseDemandReportDto.getRollingVersion()))
        {
            result = warehouseDemandReportService.queryWarehouseDemandReportSummary(warehouseDemandReportDto);
        }
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 修改分仓需求提报数据
     * @param dataList
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:15
     */
    @PostMapping("updateWarehouseDemandReportData")
    @ResponseBody
    @ApiOperation("修改分仓需求提报数据")
    @RequiresPermissions(value = {})
    public ResultInfo<?> updateWarehouseDemandReportData(@RequestBody List<WarehouseDemandReportDto> dataList) throws Exception
    {
        if (CollectionUtils.isNotEmpty(dataList))
        {
            warehouseDemandReportService.updateWarehouseDemandReportData(dataList);
        }

        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询分仓需求提报数据修改记录
     * @param warehouseDemandReportDto
     * @return ResultInfo<List < WarehouseDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:49
     */
    @PostMapping("queryWarehouseDemandReportHistoryList")
    @ResponseBody
    @ApiOperation("查询分仓需求提报数据修改记录")
    @RequiresPermissions(value = {})
    public ResultInfo<List<WarehouseDemandReportDto>> queryWarehouseDemandReportHistoryList(@RequestBody WarehouseDemandReportDto warehouseDemandReportDto)
        throws Exception
    {
        List<WarehouseDemandReportDto> result = warehouseDemandReportService.queryWarehouseDemandReportHistoryList(warehouseDemandReportDto);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓需求提报数据发布状态
     * @param warehouseDemandReportDto
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:49
     */
    @PostMapping("queryWarehouseDemandReportPublishStatus")
    @ResponseBody
    @ApiOperation("查询分仓需求提报数据发布状态")
    @RequiresPermissions(value = {})
    public ResultInfo<?> queryWarehouseDemandReportPublishStatus(@RequestBody WarehouseDemandReportDto warehouseDemandReportDto)
        throws Exception
    {
        int result = warehouseDemandReportService.queryWarehouseDemandReportPublishStatus(warehouseDemandReportDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 发布分仓需求提报数据，需要出发调用阿里接口生成分仓需求提报计划滚动版本
     * @param warehouseDemandReportDto
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:49
     */
    @PostMapping("publishWarehouseDemandReport")
    @ResponseBody
    @ApiOperation("发布分仓需求提报数据")
    @RequiresPermissions(value = {})
    public ResultInfo<?> publishWarehouseDemandReport(@RequestBody WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getRollingVersion());

        warehouseDemandReportService.publishWarehouseDemandReport(warehouseDemandReportDto);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询渠道需求计划共识版汇总数据
     * @param warehouseDemandReportDto
     * @return ResultInfo<List < PlanValue> >
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:49
     */
    @PostMapping("queryChannelDemandPlanConfirmSum")
    @ResponseBody
    @ApiOperation("查询渠道需求计划共识版汇总数据")
    @RequiresPermissions(value = {})
    public ResultInfo<List<PlanValue>> queryChannelDemandPlanConfirmSum(@RequestBody WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto);
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(warehouseDemandReportDto.getRollingVersion());

        List<PlanValue> sum = warehouseDemandReportService.queryChannelDemandPlanConfirmSum(warehouseDemandReportDto);
        return ResultInfo.success(sum);
    }
}
