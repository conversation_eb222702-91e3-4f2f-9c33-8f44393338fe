package cn.aliyun.ryytn.modules.demand.controller;

import java.util.List;
import java.util.Map;


import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.ServiceResult;
import cn.aliyun.ryytn.modules.demand.entity.dos.MarketActivityExpertDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.Channel;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.MarketService;
import cn.aliyun.ryytn.modules.demand.entity.vo.MarketActivitySkuVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.AddOrUpdateMarketVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.MarketRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketChannelListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketConstantReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryMarketSkuListReqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 促销活动
 * <AUTHOR>
 * @date 2023/10/24 9:26
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/market")
@Api(tags = "促销活动")
public class MarketController
{
    @Autowired
    private MarketService marketService;

    /**
     * @Description 查询活动常量配置列表
     * @return ResultInfo<DataqResult < ?>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/23 14:58
     */
    @PostMapping("queryConstantList")
    @ResponseBody
    @ApiOperation("查询活动常量配置列表")
//    @RequiresPermissions(value = {"demand:market:query"})
    public ResultInfo<?> queryConstantList(@RequestBody QueryMarketConstantReqVo constantTypeVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(constantTypeVo);

        Map<Object, List<Map>> objectListMap = marketService.queryConstantList(constantTypeVo);
        return ResultInfo.success(objectListMap);
    }

    /**
     * @Description 查询活动列表
     * @param  queryMarketListReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/23 14:58
     */
    @PostMapping("queryMarketList")
    @ResponseBody
    @ApiOperation("查询活动列表")
//    @RequiresPermissions(value = {"demand:market:query"})
    public ResultInfo<List<MarketRspVo>> queryMarketList(@RequestBody QueryMarketListReqVo queryMarketListReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryMarketListReqVo);
        List<MarketRspVo> marketRspVos = marketService.queryMarketList(queryMarketListReqVo);
        return ResultInfo.success(marketRspVos);
    }

    /**
     * @Description 查询活动渠道树
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/23 14:58
     */
    @PostMapping("queryMarketChannelTree")
    @ResponseBody
    @ApiOperation("查询活动渠道树")
//    @RequiresPermissions(value = {})
    public ResultInfo<?> queryMarketChannelTree(@RequestBody QueryMarketChannelListReqVo marketChannelReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(marketChannelReqVo);
        List<Channel> result = marketService.queryMarketChannelTree(marketChannelReqVo);
        return ResultInfo.success(result);
    }

    /**
     * @Description 查询活动产品列表
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/26 17:25
     */
    @PostMapping("queryMarketSkuList")
    @ResponseBody
    @ApiOperation("查询活动产品列表")
//    @RequiresPermissions(value = {})
    public ResultInfo<?> queryMarketSkuList(@RequestBody QueryMarketSkuListReqVo marketSkuListReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(marketSkuListReqVo);
        List<MarketActivitySkuVo> result = marketService.queryMarketSkuList(marketSkuListReqVo);
        return ResultInfo.success(result);
    }

    /**
     * @Description 查询促销活动详情
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/27 11:25
     */
    @PostMapping("queryMarketActivityDetail")
    @ResponseBody
    @ApiOperation("查询促销活动详情")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> queryMarketActivityDetail(@RequestBody String actiCode) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(actiCode);
        Map<String, Object> stringObjectMap = marketService.queryMarketActivityDetail(actiCode);
        return ResultInfo.success(stringObjectMap);
    }




    @PostMapping("queryExpertGroups")
    @ResponseBody
    @ApiOperation("查询达人组")
    public ResultInfo<?> queryExpertGroups(@RequestBody MarketActivityExpertDO marketActivityExpertDO) throws Exception
    {
        List<MarketActivityExpertDO> marketActivityExpertDOList = marketService.queryMarketActivityExpertList(marketActivityExpertDO);
        return ResultInfo.success(marketActivityExpertDOList);
    }


    @PostMapping("insertExpertGroups")
    @ResponseBody
    @ApiOperation("新增达人组")
    public ResultInfo<?> insertExpertGroups(@RequestBody MarketActivityExpertDO marketActivityExpertDO) throws Exception
    {
        Boolean result = marketService.insertExpertGroups(marketActivityExpertDO);
        if (result) {
            return ResultInfo.success();
        }else {
            return ResultInfo.fail(-1,"达人组已存在");
        }
    }

    /**
     * @Description 活动新增或修改
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/27 14:11
     */
    @PostMapping("addOrUpdateMarketActivity")
    @ResponseBody
    @ApiOperation("活动新增或修改")
//    @RequiresPermissions(value = {""})market-activity/add-or-update
    public ResultInfo<?> addOrUpdateMarketActivity(@RequestBody AddOrUpdateMarketVo marketVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(marketVo);
        ValidateUtil.checkIsNotEmpty(marketVo.getSkuList());
        if (!CommonConstants.TALENT.equals(marketVo.getActiType())) {
            ValidateUtil.checkIsNotEmpty(marketVo.getChannelList());
        }
        marketService.addOrUpdateMarketActivity(marketVo);
        return ResultInfo.success();
    }


    /**
     * @Description 达人活动审核
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/27 14:11
     */
    @PostMapping("auditMarketActivityExpert")
    @ResponseBody
    @ApiOperation("达人活动审核")
    public ResultInfo<?> auditMarketActivityExpert(@RequestBody AddOrUpdateMarketVo marketVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(marketVo.getId());
        ValidateUtil.checkIsNotEmpty(marketVo.getActiCode());
        ValidateUtil.checkIsNotEmpty(marketVo.getAuditStatus());
        ValidateUtil.checkIsNotEmpty(marketVo.getSkuList());
        marketService.auditMarketActivityExpert(marketVo);
        return ResultInfo.success();
    }



    /**
     * @Description 删除活动
     * @param  id
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/27 14:11
     */
    @PostMapping("deleteMarketActivity")
    @ResponseBody
    @ApiOperation("删除活动")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> deleteMarketActivity(@RequestBody String id) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(id);
        ServiceResult result = marketService.deleteMarketActivity(id);
        if (result.getCode() == 0) {
            return ResultInfo.success();
        } else {
            return ResultInfo.fail(result.getCode(),result.getMessage());
        }
    }

}
