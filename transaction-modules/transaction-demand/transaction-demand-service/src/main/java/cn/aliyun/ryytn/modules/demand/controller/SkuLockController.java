package cn.aliyun.ryytn.modules.demand.controller;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.SkuLockService;
import cn.aliyun.ryytn.modules.demand.entity.dto.SkuLockDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySkuReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.SkuLockRspVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 产品锁定期接口
 * <AUTHOR>
 * @date 2023/10/26 14:13
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/skuLock")
@Api(tags = "产品锁定期")
public class SkuLockController
{
    @Autowired
    private SkuLockService skuLockService;

    @PostMapping("pageSkuLock")
    @ResponseBody
    @ApiOperation("分页查询产品锁定期列表")
    //@RequiresPermissions(value = {"demand:sukLock:query"})
    public ResultInfo<PageInfo<SkuLockRspVo>> pageSkuLockList(@RequestBody PageCondition<SkuLockDto> condition) throws Exception
    {
        if (Objects.isNull(condition))
        {
            condition = new PageCondition<>();
        }

        PageInfo<SkuLockRspVo> page = skuLockService.pageSkuLockList(condition);
        return ResultInfo.success(page);
    }

    @PostMapping("querySku")
    @ResponseBody
    @ApiOperation("查询产品详情")
    //@RequiresPermissions(value = {"demand:sukLock:query"})
    public ResultInfo<?> querySku(@RequestBody QuerySkuReqVo querySku) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(querySku);

        DataqResult<?> result = skuLockService.querySku(querySku);
        return ResultInfo.success(result.getData());
    }

    @PostMapping("addSkuLock")
    @ResponseBody
    @ApiOperation("新增产品锁定期")
    //@RequiresPermissions(value = {"demand:sukLock:add"})
    public ResultInfo<?> addSkuLock(@RequestBody SkuLockDto skuLockDto) throws Exception
    {
        // 参数校验
        ValidateSkuLockDto(skuLockDto);

        skuLockService.addSkuLock(skuLockDto);
        return ResultInfo.success();
    }

    private void ValidateSkuLockDto(SkuLockDto skuLockDto)
    {
        ValidateUtil.checkIsNotEmpty(skuLockDto);
        ValidateUtil.checkIsNotEmpty(skuLockDto.getSkuCode());
        ValidateUtil.checkIsNotEmpty(skuLockDto.getChannelList());
        ValidateUtil.checkIsNotEmpty(skuLockDto.getLockStartWeek());
        ValidateUtil.checkIsNotEmpty(skuLockDto.getLockEndWeek());
        ValidateUtil.checkIsNotEmpty(skuLockDto.getLockStartDate());
        ValidateUtil.checkIsNotEmpty(skuLockDto.getLockEndDate());
    }

    @PostMapping("deleteSkuLock")
    @ResponseBody
    @ApiOperation("删除产品锁定期")
    //@RequiresPermissions(value = {"demand:sukLock:delete"})
    public ResultInfo<?> deleteSkuLock(@RequestBody String id) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(id);

        skuLockService.deleteSkuLock(id);
        return ResultInfo.success();
    }

}
