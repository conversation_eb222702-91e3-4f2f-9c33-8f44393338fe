package cn.aliyun.ryytn.modules.demand.controller;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.SaleTargetService;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QuerySaleTargetRspVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 查询销售目标列表
 * <AUTHOR>
 * @date 2023/10/23 11:19
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/saleTarget")
@Api(tags = "销售目标")
public class SaleTargetController
{
    @Autowired
    private SaleTargetService saleTargetService;

    /**
     *
     * @Description 查询销售目标列表
     * @param querySaleTargetReqVo
     * @return ResultInfo<BaseTable < List < QuerySaleTargetRspVo>>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 16:27
     */
    @PostMapping("querySaleTargetList")
    @ResponseBody
    @ApiOperation("查询销售目标")
    @RequiresPermissions(value = {"demand:config:saleTarget:query"})
    public ResultInfo<BaseTable<List<QuerySaleTargetRspVo>>> querySaleTargetList(@RequestBody QuerySaleTargetReqVo querySaleTargetReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(querySaleTargetReqVo);
        ValidateUtil.checkIsNotEmpty(querySaleTargetReqVo.getBizDateType());
        ValidateUtil.checkIsNotEmpty(querySaleTargetReqVo.getFsclYear());

        BaseTable<List<QuerySaleTargetRspVo>> result = saleTargetService.querySaleTargetList(querySaleTargetReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询销售目标动态表头
     * @param QuerySaleTargetReqVo
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:15
     */
    @PostMapping("querySaleTargetHeadList")
    @ResponseBody
    @ApiOperation("查询销售目标动态表头")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> querySaleTargetHeadList(@RequestBody QuerySaleTargetReqVo querySaleTargetReqVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(querySaleTargetReqVo);
        ValidateUtil.checkIsNotEmpty(querySaleTargetReqVo.getFsclYear());
        if (Objects.isNull(querySaleTargetReqVo.getBizDateType()))
        {
            querySaleTargetReqVo.setBizDateType(BizDateTypeEnum.MONTH);
        }

        List<String> result = saleTargetService.querySaleTargetHeadList(querySaleTargetReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询销售目标表头下拉列表
     * @param QuerySaleTargetReqVo
     * @return ResultInfo<List < QuerySaleTargetRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @PostMapping("querySaleTargetHeadSelect")
    @ResponseBody
    @ApiOperation("查询销售目标表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QuerySaleTargetRspVo>> querySaleTargetHeadSelect(
        @RequestBody QuerySaleTargetReqVo querySaleTargetReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(querySaleTargetReqVo);
        ValidateUtil.checkIsNotEmpty(querySaleTargetReqVo.getFsclYear());
        ValidateUtil.checkIsNotEmpty(querySaleTargetReqVo.getGroupColumnList());
        if (Objects.isNull(querySaleTargetReqVo.getBizDateType()))
        {
            querySaleTargetReqVo.setBizDateType(BizDateTypeEnum.MONTH);
        }

        List<QuerySaleTargetRspVo> result = saleTargetService.querySaleTargetHeadSelect(querySaleTargetReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询销售目标分组聚合数据列表
     * @param QuerySaleTargetReqVo
     * @return ResultInfo<List < QuerySaleTargetRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:11
     */
    @PostMapping("querySaleTargetListGroupBy")
    @ResponseBody
    @ApiOperation("查询销售目标分组聚合数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QuerySaleTargetRspVo>> querySaleTargetListGroupBy(
        @RequestBody QuerySaleTargetReqVo QuerySaleTargetReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(QuerySaleTargetReqVo);
        ValidateUtil.checkIsNotEmpty(QuerySaleTargetReqVo.getFsclYear());
        ValidateUtil.checkIsNotEmpty(QuerySaleTargetReqVo.getGroupColumnList());
        if (Objects.isNull(QuerySaleTargetReqVo.getBizDateType()))
        {
            QuerySaleTargetReqVo.setBizDateType(BizDateTypeEnum.MONTH);
        }

        List<QuerySaleTargetRspVo> result =
            saleTargetService.querySaleTargetListGroupBy(QuerySaleTargetReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询销售目标明细数据列表
     * @param condition
     * @return ResultInfo<PageInfo < QuerySaleTargetRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("querySaleTargetDataPage")
    @ResponseBody
    @ApiOperation("分页查询销售目标明细数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<QuerySaleTargetRspVo>> querySaleTargetDataPage(
        @RequestBody PageCondition<QuerySaleTargetReqVo> condition) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getFsclYear());
        if (Objects.isNull(condition.getCondition().getBizDateType()))
        {
            condition.getCondition().setBizDateType(BizDateTypeEnum.MONTH);
        }

        PageInfo<QuerySaleTargetRspVo> result = saleTargetService.querySaleTargetDataPage(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询销售目标数据汇总
     * @param condition
     * @return ResultInfo<Map < String, Double>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:32
     */
    @PostMapping("querySaleTargetSummary")
    @ResponseBody
    @ApiOperation("查询销售目标数据汇总")
    @RequiresPermissions(value = {})
    public ResultInfo<Map<String, Double>> querySaleTargetSummary(@RequestBody QuerySaleTargetReqVo QuerySaleTargetReqVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(QuerySaleTargetReqVo);
        ValidateUtil.checkIsNotEmpty(QuerySaleTargetReqVo.getFsclYear());
        if (Objects.isNull(QuerySaleTargetReqVo.getBizDateType()))
        {
            QuerySaleTargetReqVo.setBizDateType(BizDateTypeEnum.MONTH);
        }

        Map<String, Double> result = saleTargetService.querySaleTargetSummary(QuerySaleTargetReqVo);
        return ResultInfo.success(result);
    }
}
