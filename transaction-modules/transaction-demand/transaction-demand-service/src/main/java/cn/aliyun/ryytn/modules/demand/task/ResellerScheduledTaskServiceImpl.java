package cn.aliyun.ryytn.modules.demand.task;

import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.aliyun.brain.dataindustry.microapp.MicroAppTaskInstanceOutputVO;
import com.aliyun.brain.dataindustry.microapp.request.ApiRunMicroAppRequest;
import com.aliyun.dataq.dataindustry.DataIndustrySpringServiceContext;
import com.aliyun.dataq.dataindustry.config.Header;
import com.aliyun.dataq.dataindustry.service.MicroAppService;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 渠道需求预测定时任务
 * <AUTHOR>
 * @date 2023/11/30 10:31
 */
@Slf4j
@Service
public class ResellerScheduledTaskServiceImpl implements TaskService
{
    @Resource(name = "dataIndustryContext")
    private DataIndustrySpringServiceContext dataIndustryContext;

    private static final String APPCODE = "APP123123";

    @Value("${dataq.scheduler.userId}")
    private String userId;

    @Value("${dataq.scheduler.tenantCode}")
    private String tenantCode;

    @Value("${dataq.scheduler.workspaceCode}")
    private String workspaceCode;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        // 配置算法调度入参
        ApiRunMicroAppRequest apiRunMicroAppRequest = new ApiRunMicroAppRequest();
        apiRunMicroAppRequest.setAppCode(APPCODE);
        Map<String, Object> map = new HashMap<>();
        map.put("param1", "value1");
        map.put("param2", "value2");
        apiRunMicroAppRequest.setApiParamValues(map);
        log.info("Enter ResellerScheduledTaskServiceImpl.apiRunMicroAppRequest:" + apiRunMicroAppRequest);

        // 配置header
        Header header = new Header();
        header.setUserId(userId);
        header.setTenantCode(tenantCode);
        header.setWorkspaceCode(workspaceCode);

        // 执行算法调度任务
        MicroAppService service = dataIndustryContext.getService(MicroAppService.class);
        MicroAppTaskInstanceOutputVO execute = service.execute(apiRunMicroAppRequest, header);
        log.info("Exit ResellerScheduledTaskServiceImpl.execute:" + execute);
    }
}
