package cn.aliyun.ryytn.modules.demand.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.ForecastAlgorithmService;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastAlgorithmListReqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 预测算法接口
 * <AUTHOR>
 * @date 2023/10/23 17:21
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/forecastAlgorithm")
@Api(tags = "预测算法接口")
public class ForecastAlgorithmController
{
    @Autowired
    private ForecastAlgorithmService forecastAlgorithmService;

    @PostMapping("queryForecastAlgorithmList")
    @ResponseBody
    @ApiOperation("查询渠道_分仓预测算法列表列表")
    //@RequiresPermissions(value = {"demand:forecastAlgorithm:query"})
    public ResultInfo<?> queryForecastAlgorithmList(@RequestBody QueryForecastAlgorithmListReqVo queryForecastAlgorithmListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryForecastAlgorithmListReqVo);

        // 调用dataq
        DataqResult<?> result = forecastAlgorithmService.queryForecastAlgorithmList(queryForecastAlgorithmListReqVo);
        return ResultInfo.success(result.getData());
    }
}
