package cn.aliyun.ryytn.modules.demand.task;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.modules.demand.dao.WarehouseDemandReportDao;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 创建分仓需求提报分区表任务
 * <AUTHOR>
 * @date 2023/12/8 18:26
 */
@Slf4j
@Service
public class CreateWarehouseDemandReportPartitionTaskServiceImpl implements TaskService
{
    @Autowired
    private WarehouseDemandReportDao warehouseDemandReportDao;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        // 明年
        String nextYear = DateUtils.formatTime(DateUtils.addYears(new Date(), 1), DateUtils.Y);
        List<String> partitionList = new ArrayList<>(12);
        for (int i = 1; i <= 12; i++)
        {
            partitionList.add(nextYear + String.format("%02d", i));
        }
        warehouseDemandReportDao.createPartitionTable(partitionList);
    }
}
