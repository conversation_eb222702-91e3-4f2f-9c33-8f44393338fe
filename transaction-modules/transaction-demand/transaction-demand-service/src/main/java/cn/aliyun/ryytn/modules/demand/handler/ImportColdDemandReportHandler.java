package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.excel.AbstractImportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.excel.ExcelData;
import cn.aliyun.ryytn.common.excel.ExcelResult;
import cn.aliyun.ryytn.common.excel.ImportResultFillHander;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.resource.I18nUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.dao.ColdDemandReportDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.ImportColdDemandReportVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 导入低温需求提报
 * <AUTHOR>
 * @date 2023/10/19 14:46
 */
@Slf4j
@Component("importColdDemandReport")
public class ImportColdDemandReportHandler extends AbstractImportExcelHandler<ImportColdDemandReportVo>
{
    @Autowired
    private ColdDemandReportDao coldDemandReportDao;

    public static final Integer headRowNum = 3;

    /**
     *
     * @Description 导入电子表格
     * @param file
     * @param condition
     * @return List<?>
     * <AUTHOR>
     * @date 2023年10月19日 15:01
     */
    @Override
    public ExcelResult<ExcelData> importExcel(File file, ExcelCondition condition) throws Exception
    {
        if (!condition.getCondition().containsKey("rollingVersion"))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
        CustomReadListener listener = new CustomReadListener(condition);

        InputStream inputStream1 = new FileInputStream(file);
        InputStream inputStream2 = new FileInputStream(file);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.read(inputStream1, ImportColdDemandReportVo.class, listener).headRowNumber(headRowNum).sheet().doRead();

            EasyExcel.write(byteArrayOutputStream)
                .withTemplate(inputStream2)
                .autoCloseStream(false)
                .registerWriteHandler(new ImportResultFillHander(listener.excelResult.getFailedList(), headRowNum))
                .needHead(false)
                .sheet()
                .doWrite(Collections.emptyList());

            listener.excelResult.setBytes(byteArrayOutputStream.toByteArray());
            // 失败数量限制100，防止集合过大浏览器崩溃
            listener.excelResult.setFailedList(listener.excelResult.getFailedList().stream().limit(100).collect(Collectors.toList()));
        }
        catch (Exception e)
        {
            throw e;
        }
        finally
        {
            IOUtils.closeQuietly(inputStream1);
            IOUtils.closeQuietly(inputStream2);
            IOUtils.closeQuietly(byteArrayOutputStream);
        }
        return listener.excelResult;
    }

    /**
     *
     * @Description 自定义读监听器
     * <AUTHOR>
     * @date 2023/10/30 16:34
     */
    private class CustomReadListener implements ReadListener<ImportColdDemandReportVo>
    {

        /**
         * 每隔200条存储数据库
         */
        private static final int BATCH_COUNT = 200;

        /**
         * 当前日，格式：yyyyMMdd
         */
        private final String currentDay = DateUtils.getDate(DateUtils.YMD);

        /**
         * 固定字段最后一列索引
         */
        private Integer fieldLastCol;

        /**
         * 动态列最后一列索引
         */
        private Integer headLastCol;

        /**
         * 列和参数key映射关系，Key：电子表格列索引，Value：调用dataq参数的key
         */
        private final Map<Integer, String> columnWeekDate;

        /**
         * 缓存的数据
         */
        private List<ImportColdDemandReportVo> cachedDataList;

        /**
         * 导入参数
         */
        private final ExcelCondition condition;

        /**
         * 导入结果
         */
        private final ExcelResult<ExcelData> excelResult;

        private Account currentAccount;

        public int count = 0;

        private String remark = "文件导入";

        private String tableSuffix;

        public CustomReadListener(ExcelCondition condition)
        {
            // 通过反射获取固定字段表头
            Class clazz = ImportColdDemandReportVo.class;
            // 遍历所有字段，获取所有ExcelProperty注解
            this.fieldLastCol = 0;
            for (Field field : clazz.getDeclaredFields())
            {
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                if (Objects.isNull(excelProperty))
                {
                    continue;
                }
                if (excelProperty.index() > this.fieldLastCol)
                {
                    this.fieldLastCol = excelProperty.index();
                }
            }
            this.columnWeekDate = new HashMap<>();
            this.cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            this.condition = condition;
            this.excelResult = new ExcelResult<>();
            this.excelResult.setSuccess(0L);
            this.excelResult.setFailed(0L);
            this.excelResult.setFailedList(new ArrayList<ExcelData>());
            String rollingVersion = condition.getCondition().getString("rollingVersion");
            this.tableSuffix = StringUtils.substring(rollingVersion, 3, 9);
            this.currentAccount = condition.getSession().getAccount();
        }

        /**
         *
         * @Description 读取表头数据，根据表头封装监听类成员变量
         * @param headMap
         * @param context
         * <AUTHOR>
         * @date 2023年10月30日 16:45
         */
        @Override
        public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context)
        {
            // 只读取隐藏的表头列，隐藏列中动态表头的值作为dataq批量导入提报数据动态参数的key
            if (context.readRowHolder().getRowIndex() == headRowNum - 1)
            {
                // 封装动态表头数据，固定字段最后一列后的所有列，都是动态列
                for (Integer i : headMap.keySet())
                {
                    if (i > fieldLastCol)
                    {
                        this.columnWeekDate.put(i, headMap.get(i).getStringValue());
                    }
                }
                this.headLastCol = headMap.keySet().stream().max((x, y) -> Integer.compare(x, y)).get();
            }
        }

        /**
         *
         * @Description 读取一条数据
         * @param importChannelDemandVo
         * @param analysisContext
         * <AUTHOR>
         * @date 2023年10月19日 15:06
         */
        @Override
        public void invoke(ImportColdDemandReportVo importColdDemandReportVo, AnalysisContext context)
        {
            // 校验数据，校验失败的数据记录失败列表和失败原因
            try
            {
                // 设置行号
                importColdDemandReportVo.setRowIndex(context.readRowHolder().getRowIndex());

                checkData(importColdDemandReportVo);
                importColdDemandReportVo.setDataMap(new HashMap<>());
                Map<Integer, Cell> cellMap = context.readRowHolder().getCellMap();
                for (Integer i : cellMap.keySet())
                {
                    if (i > fieldLastCol && i <= headLastCol)
                    {
                        CellData<String> cellData = (CellData) cellMap.get(i);
                        String value = null;
                        if (CellDataTypeEnum.NUMBER.equals(cellData.getType()))
                        {
                            value = StringUtils.substringBefore(StringUtils.getValue(cellData.getNumberValue()), StringUtils.POINT_SEPARATOR);
                        }
                        else if (CellDataTypeEnum.STRING.equals(cellData.getType()))
                        {
                            value = StringUtils.substringBefore(cellData.getStringValue(), StringUtils.POINT_SEPARATOR);
                        }
                        else
                        {
                            value = "0";
                        }
                        value = StringUtils.replace(value, StringUtils.SPACE, StringUtils.EMPTY);
                        if (StringUtils.isBlank(value))
                        {
                            value = "0";
                        }
                        if (!value.matches(CommonConstants.PLAN_VALUE_PATTERN))
                        {
                            throw new ServiceException("提报数据必须为非负整数");
                        }
                        importColdDemandReportVo.getDataMap().put(this.columnWeekDate.get(i), Double.valueOf(value));
                    }
                }

                // 封装导入表格外的导入参数
                importColdDemandReportVo.setRollingVersion(this.condition.getCondition().getString("rollingVersion"));

                // 计算导入提报数量必须是系数的倍数
                if (Objects.isNull(importColdDemandReportVo.getOrderNum()))
                {
                    importColdDemandReportVo.setOrderNum(0d);
                }
                if (Objects.isNull(importColdDemandReportVo.getPlanUnitCnt()))
                {
                    importColdDemandReportVo.setPlanUnitCnt(1);
                }
                if (Math.floorMod(importColdDemandReportVo.getOrderNum().intValue(), importColdDemandReportVo.getPlanUnitCnt()) != 0)
                {
                    BigDecimal importOrderNum = new BigDecimal(importColdDemandReportVo.getOrderNum());
                    BigDecimal planUnitCnt = new BigDecimal(importColdDemandReportVo.getPlanUnitCnt());
                    Double orderNum = importOrderNum.divide(planUnitCnt, 0, RoundingMode.UP).multiply(planUnitCnt).doubleValue();
                    importColdDemandReportVo.setOrderNum(orderNum);
                }
            }
            catch (ServiceException e)
            {
                log.error("CustomReadListener invoke has exception:{}", e);
                this.excelResult.setFailed(this.excelResult.getFailed() + 1);
                importColdDemandReportVo.setResult(StringUtils.isEmpty(e.getErrorMessage()) ? I18nUtils.getValue(e.errorCode) : e.getErrorMessage());
                if (this.excelResult.getFailedList().size() < 100)
                {
                    this.excelResult.getFailedList().add(importColdDemandReportVo);
                }
                return;
            }
            catch (Exception e)
            {
                log.error("CustomReadListener invoke has exception:{}", e);
                importColdDemandReportVo.setResult(I18nUtils.getValue(ErrorCodeConstants.FAIL_OTHER));
                this.excelResult.setFailed(this.excelResult.getFailed() + 1);
                if (this.excelResult.getFailedList().size() < 100)
                {
                    this.excelResult.getFailedList().add(importColdDemandReportVo);
                }
                return;
            }

            // 封装导入表格外的导入参数
            importColdDemandReportVo.setRollingVersion(this.condition.getCondition().getString("rollingVersion"));

            cachedDataList.add(importColdDemandReportVo);
            // 达到BATCH_COUNT了，批量导入，防止数据几万条数据在内存，容易OOM
            if (cachedDataList.size() >= BATCH_COUNT)
            {
                try
                {
                    saveData(cachedDataList);
                    cachedDataList.forEach(item -> {
                        item.setResult(I18nUtils.getValue(ErrorCodeConstants.SUCCESS));
                    });
                    this.excelResult.setSuccess(this.excelResult.getSuccess() + cachedDataList.size());
                }
                catch (ServiceException e)
                {
                    log.error("CustomReadListener doAfterAllAnalysed has exception:{}", e);
                    cachedDataList.forEach(item -> {
                        item.setResult(StringUtils.isEmpty(e.getErrorMessage()) ? I18nUtils.getValue(e.errorCode) : e.getErrorMessage());
                    });
                    this.excelResult.setFailed(this.excelResult.getFailed() + cachedDataList.size());
                    if (this.excelResult.getFailedList().size() < 100)
                    {
                        this.excelResult.getFailedList().addAll(cachedDataList);
                    }
                }
                catch (Exception e)
                {
                    log.error("CustomReadListener doAfterAllAnalysed has exception:{}", e);
                    cachedDataList.forEach(item -> {
                        item.setResult(I18nUtils.getValue(ErrorCodeConstants.FAIL_OTHER));
                    });
                    this.excelResult.setFailed(this.excelResult.getFailed() + cachedDataList.size());
                    if (this.excelResult.getFailedList().size() < 100)
                    {
                        this.excelResult.getFailedList().addAll(cachedDataList);
                    }
                }
                finally
                {
                    this.cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
                }
            }
        }

        /**
         *
         * @Description 读取数据结束
         * @param analysisContext
         * <AUTHOR>
         * @date 2023年10月19日 15:06
         */
        @Override
        public void doAfterAllAnalysed(AnalysisContext context)
        {
            if (CollectionUtils.isEmpty(cachedDataList))
            {
                return;
            }

            try
            {
                // 保存最后遗留的零头数据
                saveData(cachedDataList);
                cachedDataList.forEach(item -> {
                    item.setResult(I18nUtils.getValue(ErrorCodeConstants.SUCCESS));
                });
                this.excelResult.setSuccess(this.excelResult.getSuccess() + cachedDataList.size());
            }
            catch (ServiceException e)
            {
                log.error("CustomReadListener doAfterAllAnalysed has exception:{}", e);
                cachedDataList.forEach(item -> {
                    item.setResult(StringUtils.isEmpty(e.getErrorMessage()) ? I18nUtils.getValue(e.errorCode) : e.getErrorMessage());
                });
                this.excelResult.setFailed(this.excelResult.getFailed() + cachedDataList.size());
                if (this.excelResult.getFailedList().size() < 100)
                {
                    this.excelResult.getFailedList().addAll(cachedDataList);
                }
            }
            catch (Exception e)
            {
                log.error("CustomReadListener doAfterAllAnalysed has exception:{}", e);
                cachedDataList.forEach(item -> {
                    item.setResult(I18nUtils.getValue(ErrorCodeConstants.FAIL_OTHER));
                });
                this.excelResult.setFailed(this.excelResult.getFailed() + cachedDataList.size());
                if (this.excelResult.getFailedList().size() < 100)
                {
                    this.excelResult.getFailedList().addAll(cachedDataList);
                }
            }
        }

        /**
         *
         * @Description 校验数据
         * @param importChannelDemandVo
         * @throws ServiceException
         * <AUTHOR>
         * @date 2023年10月31日 10:07
         */
        private void checkData(ImportColdDemandReportVo importColdDemandReportVo) throws ServiceException
        {
            // 如果不是超级管理员，则校验导入渠道和品类的权限
            if (this.currentAccount.isAdmin())
            {
                return;
            }

            // 当前登录账号有权限的渠道编号
            Set<String> accountChannelIdSet = new HashSet<String>(this.currentAccount.getChannelIdList());
            // 渠道没有权限，则该条数据导入失败
            if (!accountChannelIdSet.contains(importColdDemandReportVo.getLv1ChannelCode()) &&
                !accountChannelIdSet.contains(importColdDemandReportVo.getLv2ChannelCode()) &&
                !accountChannelIdSet.contains(importColdDemandReportVo.getLv3ChannelCode()))
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_NOAUTH_ERROR);
            }

            // 当前登录账号有权限的品类编号
            Set<String> accountCategoryIdSet = new HashSet<String>(this.currentAccount.getCategoryIdList());
            // 品类没有权限，则该条数据导入失败
            if (!accountCategoryIdSet.contains(importColdDemandReportVo.getLv1CategoryCode()) &&
                !accountCategoryIdSet.contains(importColdDemandReportVo.getLv2CategoryCode()) &&
                !accountCategoryIdSet.contains(importColdDemandReportVo.getLv3CategoryCode()))
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_NOAUTH_ERROR);
            }
        }

        /**
         *
         * @Description 保存数据
         * @param cachedDataList
         * <AUTHOR>
         * @date 2023年10月31日 10:43
         */
        private void saveData(List<ImportColdDemandReportVo> cachedDataList) throws Exception
        {
            Account currentAccount = condition.getSession().getAccount();
            Date currentDate = new Date();
            List<ImportColdDemandReportVo> dataList = new ArrayList<>();
            for (ImportColdDemandReportVo importColdDemandReportVo : cachedDataList)
            {
                for (String bizDateValue : importColdDemandReportVo.getDataMap().keySet())
                {
                    ImportColdDemandReportVo data = new ImportColdDemandReportVo();
                    BeanUtils.copyProperties(importColdDemandReportVo, data);
                    data.setTableSuffix(this.tableSuffix);
                    // 渠道需求提报周粒度时间值为周第一天日期
                    data.setBizDateValue(bizDateValue);
                    // 渠道需求提报数据
                    data.setOrderNum(Double.valueOf(importColdDemandReportVo.getDataMap().get(bizDateValue)));
                    data.setLastModifier(currentAccount.getName());
                    data.setGmtModify(currentDate);
                    data.setRemark(remark);
                    dataList.add(data);
                }
            }

            // 批量保存数据库
            coldDemandReportDao.importColdDemandReport(dataList);
        }
    }
}
