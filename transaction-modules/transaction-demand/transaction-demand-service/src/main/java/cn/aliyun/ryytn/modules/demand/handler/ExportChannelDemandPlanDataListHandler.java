package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandPlanDataParamVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListRspVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 导出渠道需求计划数据列表
 * <AUTHOR>
 * @date 2023/11/17 10:28
 */
@Slf4j
@Component("exportChannelDemandPlanDataList")
public class ExportChannelDemandPlanDataListHandler extends AbstractExportExcelHandler
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private DataqChannelDemandPlanDao dataqChannelDemandPlanDao;

    @Autowired
    private RedisUtils redisUtils;

    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        // 解析参数
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo =
            condition.getCondition().toJavaObject(QueryChannelDemandPlanVersionListReqVo.class);
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);



        List<QueryChannelDemandPlanVersionListRspVo> dataList =
            dataqChannelDemandPlanDao.queryChannelDemandPlanVersionList(queryChannelDemandPlanVersionListReqVo);

        Set<String> planDateSet = dataList.stream().map(item -> {
            return item.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
        }).collect(Collectors.toSet());

        // 获取指定周的周对象
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateSet);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(),
            (key1, key2) -> key1));

        //获取年月周 如:202410W4
        String rollingVersionYMW = StringUtils.replace(queryChannelDemandPlanVersionListReqVo.getVersionId(), "DP", StringUtils.EMPTY);
        Object rollingVersionDataqWeekObject = dataqWeekList.stream().filter(x->((DataqWeek)x).getYearMonthWeek().equals(rollingVersionYMW)).findFirst().orElse(null);
        String rollingVersionStartDate = "19010101";
        if(!Objects.isNull(rollingVersionDataqWeekObject)){
            rollingVersionStartDate = ((DataqWeek)rollingVersionDataqWeekObject).getFsclWeekStart();
        }else{
            log.error("ExportChannelDemandPlanDataListHandler rollingVersionDataqWeekObject is null,not found.");
        }
        log.info("ExportChannelDemandPlanDataListHandler rollingVersionStartDate value is :{}",rollingVersionStartDate);

        int rowId = 1;
        for (QueryChannelDemandPlanVersionListRspVo data : dataList)
        {
            String key = data.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
            DataqWeek dataqWeek = dataqWeekMap.get(key);
            data.setRowId(rowId++);
            data.setMonth(dataqWeek.getMonthLabel());
            data.setWeek(StringUtils.WEEK_PREFIX_UPPER + dataqWeek.getWeekOfFsclMonth());
            if (Objects.isNull(data.getPlanValue()))
            {
                data.setPlanValue(0d);
            }
            if(rollingVersionStartDate.compareTo(key) > 0){
                if (Objects.isNull(data.getOrderNumReal()))
                {
                    data.setOrderNum(0d);
                }else{
                    data.setOrderNum(data.getOrderNumReal());
                }
            }else{
                if (Objects.isNull(data.getOrderNum()))
                {
                    data.setOrderNum(0d);
                }
            }

        }

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream, QueryChannelDemandPlanVersionListRspVo.class).excelType(ExcelTypeEnum.XLSX).sheet("渠道需求计划数据")
                .doWrite(dataList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }

    /**
     *
     * @Description 导出渠道需求计划数据列表
     * @param condition
     * @return byte[]
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月17日 10:29
     */
    @Deprecated
    public byte[] exportOld(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        // 解析参数
        ChannelDemandPlanDataParamVo channelDemandPlanDataParamVo =
            condition.getCondition().toJavaObject(ChannelDemandPlanDataParamVo.class);

        List<QueryChannelDemandPlanVersionListRspVo> dataList = Collections.EMPTY_LIST;

        // 查询dataq接口渠道需求计划版本列表
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(channelDemandPlanDataParamVo.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setVersionId(channelDemandPlanDataParamVo.getVersionId());
        queryChannelDemandPlanVersionListReqVo.setGroupId(channelDemandPlanDataParamVo.getGroupId());
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandPlanVersionListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();

        // 解析dataq响应，所有版本拆分子计划的平铺数据，还需要对此数据根据子计划清单组编号过滤
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            dataList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);
            dataList = dataList.stream().sorted(
                    Comparator.comparing(QueryChannelDemandPlanVersionListRspVo::getLv3CategoryCode)
                        .thenComparing(QueryChannelDemandPlanVersionListRspVo::getLv2ChannelCode)
                        .thenComparing(QueryChannelDemandPlanVersionListRspVo::getPlanDate))
                .collect(Collectors.toList());

            Set<String> planDateSet = dataList.stream().map(item -> {
                return item.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
            }).collect(Collectors.toSet());

            // 获取指定周的周对象
            List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateSet);
            Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
                return (DataqWeek) item;
            }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
                Function.identity(),
                (key1, key2) -> key1));

            int rowId = 1;
            for (QueryChannelDemandPlanVersionListRspVo data : dataList)
            {
                String key = data.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                DataqWeek dataqWeek = dataqWeekMap.get(key);
                data.setRowId(rowId++);
                data.setMonth(dataqWeek.getMonthLabel());
                data.setWeek(StringUtils.WEEK_PREFIX_UPPER + dataqWeek.getWeekOfFsclMonth());
                if (Objects.isNull(data.getPlanValue()))
                {
                    data.setPlanValue(0d);
                }
            }
        }

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream, QueryChannelDemandReportListRspVo.class).excelType(ExcelTypeEnum.XLSX).sheet("渠道需求提报数据")
                .doWrite(dataList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }
}
