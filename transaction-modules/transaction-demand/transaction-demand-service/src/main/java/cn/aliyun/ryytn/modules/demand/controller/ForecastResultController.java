package cn.aliyun.ryytn.modules.demand.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.ForecastResultService;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelForecastResultBaseTable;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultDetailReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultDetailRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelForecastResultReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastChannelFilterListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastChannelListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastResultRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseFilterListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryForecastWarehouseListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseForecastResultRspVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 预测结果接口
 * <AUTHOR>
 * @date 2023/10/23 17:58
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/forecastResult")
@Api(tags = "预测结果")
public class ForecastResultController
{
    @Autowired
    private ForecastResultService forecastResultService;

    /**
     *
     * @Description 查询渠道需求结果筛选条件
     * @param queryForecastChannelFilterListReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 9:13
     */
    @PostMapping("queryChannelForecastResultFilterList")
    @ResponseBody
    @ApiOperation("渠道需求结果筛选条件")
    @RequiresPermissions(value = {"demand:forecastResult:channel:query"})
    public ResultInfo<?> queryChannelForecastResultFilterList(@RequestBody QueryForecastChannelFilterListReqVo queryForecastChannelFilterListReqVo)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryForecastChannelFilterListReqVo);

        DataqResult<?> result = forecastResultService.queryChannelForecastResultFilterList(queryForecastChannelFilterListReqVo);
        return ResultInfo.success(result.getData());
    }

    /**
     *
     * @Description 分页查询渠道预测结果
     * @param condition
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 9:14
     */
    @PostMapping("pageChannelForecastResultList")
    @ResponseBody
    @ApiOperation("分页查询渠道预测结果")
    //@RequiresPermissions(value = {"demand:forecastResult:channel:query"})
    public ResultInfo<PageInfo<?>> pageChannelForecastResultList(@RequestBody PageCondition<QueryForecastChannelListReqVo> condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getPageNum());
        ValidateUtil.checkIsNotEmpty(condition.getPageSize());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getAlgoNameAndVersion());

        DataqResult<PageInfo<?>> result = forecastResultService.pageChannelForecastResultList(condition);
        return ResultInfo.success(result.getData());
    }

    @PostMapping("queryWarehouseForecastResultList")
    @ResponseBody
    @ApiOperation("查询分仓预测结果下拉列表")
    //@RequiresPermissions(value = {"demand:forecastResult:warehouse:query"})
    public ResultInfo<?> queryWarehouseForecastResultList(@RequestBody QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo);
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo.getAlgoNameAndVersion());

        DataqResult<?> result = forecastResultService.queryWarehouseForecastResultList(queryForecastWarehouseListReqVo);

        return ResultInfo.success(result.getData());
    }

    /**
     *
     * @Description 查询渠道预测结果下拉列表
     * @param queryForecastChannelListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月07日 9:37     */
    @PostMapping("queryChannelForecastResultList")
    @ResponseBody
    @ApiOperation("查询渠道预测结果下拉列表")
    //@RequiresPermissions(value = {"demand:forecastResult:channel:query"})
    public ResultInfo<?> queryChannelForecastResultList(@RequestBody QueryForecastChannelListReqVo queryForecastChannelListReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryForecastChannelListReqVo);
        ValidateUtil.checkIsNotEmpty(queryForecastChannelListReqVo.getAlgoNameAndVersion());

        DataqResult<?> result = forecastResultService.queryChannelForecastResultList(queryForecastChannelListReqVo);

        return ResultInfo.success(result.getData());
    }

    /**
     *
     * @Description 查询分仓需求结果筛选条件
     * @param queryForecastWarehouseFilterListReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 9:24
     */
    @PostMapping("queryWarehouseForecastResultFilterList")
    @ResponseBody
    @ApiOperation("查询分仓需求结果筛选条件")
    @RequiresPermissions(value = {"demand:forecastResult:warehouse:query"})
    public ResultInfo<?> queryWarehouseForecastResultFilterList(@RequestBody QueryForecastWarehouseFilterListReqVo queryForecastWarehouseFilterListReqVo)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseFilterListReqVo);

        DataqResult<?> result = forecastResultService.queryWarehouseForecastResultFilterList(queryForecastWarehouseFilterListReqVo);
        return ResultInfo.success(result.getData());
    }

    /**
     *
     * @Description 分页查询分仓预测结果
     * @param condition
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 9:24
     */
    @PostMapping("pageWarehouseForecastResultList")
    @ResponseBody
    @ApiOperation("分页查询分仓预测结果")
    //@RequiresPermissions(value = {"demand:forecastResult:warehouse:query"})
    public ResultInfo<PageInfo<?>> pageWarehouseForecastResultList(@RequestBody PageCondition<QueryForecastWarehouseListReqVo> condition)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getPageNum());
        ValidateUtil.checkIsNotEmpty(condition.getPageSize());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getAlgoNameAndVersion());

        DataqResult<PageInfo<?>> result = forecastResultService.pageWarehouseForecastResultList(condition);
        return ResultInfo.success(result.getData());
    }

    /**
     *
     * @Description 查询渠道预测结果
     * @param queryChannelForecastResultReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月01日 15:21     */
    @PostMapping("queryChannelForecastResult")
    @ResponseBody
    @ApiOperation("查询渠道预测结果")
    //@RequiresPermissions(value = {"demand:forecastResult:channel:query"})
    public ResultInfo<?> queryChannelForecastResult(@RequestBody QueryChannelForecastResultReqVo queryChannelForecastResultReqVo)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo.getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo.getPredictionVersion());

        ChannelForecastResultBaseTable<?> result = forecastResultService.queryChannelForecastResult(queryChannelForecastResultReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道预测结果动态表头
     * @param queryChannelForecastResultReqVo
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 9:18
     */
    @PostMapping("queryChannelForecastResultHeadList")
    @ResponseBody
    @ApiOperation("查询渠道预测结果动态表头")
    public ResultInfo<List<String>> queryChannelForecastResultHeadList(@RequestBody QueryChannelForecastResultReqVo queryChannelForecastResultReqVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo.getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo.getPredictionVersion());

        List<String> result = forecastResultService.queryChannelForecastResultHeadList(queryChannelForecastResultReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道预测结果表头筛选条件
     * @param queryChannelForecastResultReqVo
     * @return ResultInfo<List < QueryForecastResultRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月29日 14:22
     */
    @PostMapping("queryChannelForecastResultHeadSelect")
    @ResponseBody
    @ApiOperation("查询渠道预测结果表头筛选条件")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryForecastResultRspVo>> queryChannelForecastResultHeadSelect(
        @RequestBody QueryChannelForecastResultReqVo queryChannelForecastResultReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo.getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo.getPredictionVersion());

        List<QueryForecastResultRspVo> result = forecastResultService.queryChannelForecastResultHeadSelect(queryChannelForecastResultReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分组聚合查询渠道预测结果列表
     * @param queryChannelDemandReportListReqVo
     * @return ResultInfo<List < QueryForecastResultRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月09日 11:14
     */
    @PostMapping("queryChannelForecastResultGroupBy")
    @ResponseBody
    @ApiOperation("分组聚合查询渠道预测结果列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryForecastResultRspVo>> queryChannelForecastResultGroupBy(
        @RequestBody QueryChannelForecastResultReqVo queryChannelForecastResultReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo);
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo.getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo.getPredictionVersion());
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultReqVo.getGroupColumnList());

        List<QueryForecastResultRspVo> result = forecastResultService.queryChannelForecastResultGroupBy(queryChannelForecastResultReqVo);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询渠道预测结果
     * @param condition
     * @return ResultInfo<PageInfo < QueryForecastResultRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 9:18
     */
    @PostMapping("queryChannelForecastResultPage")
    @ResponseBody
    @ApiOperation("分页查询渠道预测结果")
    public ResultInfo<PageInfo<QueryForecastResultRspVo>> queryChannelForecastResultPage(@RequestBody PageCondition<QueryChannelForecastResultReqVo> condition)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getPredictionVersion());

        PageInfo<QueryForecastResultRspVo> result = forecastResultService.queryChannelForecastResultPage(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓预测结果
     * @param queryForecastWarehouseListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月10日 9:18     */
    @PostMapping("queryWarehouseForecastResult")
    @ResponseBody
    @ApiOperation("查询分仓预测结果")
    public ResultInfo<?> queryWarehouseForecastResult(@RequestBody QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo);
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo.getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo.getPredictionVersion());

        BaseTable<?> result = forecastResultService.queryWarehouseForecastResult(queryForecastWarehouseListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道预测结果详情
     * @param queryChannelForecastResultDetailReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月08日 9:58     */
    @PostMapping("queryChannelForecastResultDetail")
    @ResponseBody
    @ApiOperation("查询渠道预测结果详情")
    public ResultInfo<QueryChannelForecastResultDetailRspVo> queryChannelForecastResultDetail(
        @RequestBody QueryChannelForecastResultDetailReqVo queryChannelForecastResultDetailReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultDetailReqVo);

        QueryChannelForecastResultDetailRspVo result = forecastResultService.queryChannelForecastResultDetail(queryChannelForecastResultDetailReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询渠道预测结果详情报表
     * @param queryChannelForecastResultDetailReqVo
     * @return ResultInfo<QueryChannelForecastResultDetailRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月08日 9:58
     */
    @PostMapping("queryChannelForecastResultDetailReport")
    @ResponseBody
    @ApiOperation("查询渠道预测结果详情报表")
    public ResultInfo<QueryChannelForecastResultDetailRspVo> queryChannelForecastResultDetailReport(
        @RequestBody QueryChannelForecastResultDetailReqVo queryChannelForecastResultDetailReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryChannelForecastResultDetailReqVo);

        QueryChannelForecastResultDetailRspVo result = forecastResultService.queryChannelForecastResultDetailReport(queryChannelForecastResultDetailReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓预测结果动态表头
     * @param queryForecastWarehouseListReqVo
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 9:18
     */
    @PostMapping("queryWarehouseForecastResultHeadList")
    @ResponseBody
    @ApiOperation("查询分仓预测结果动态表头")
    public ResultInfo<List<String>> queryWarehouseForecastResultHeadList(@RequestBody QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo);
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo.getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo.getPredictionVersion());

        List<String> result = forecastResultService.queryWarehouseForecastResultHeadList(queryForecastWarehouseListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询分仓预测结果表头下拉列表
     * @param queryForecastWarehouseListReqVo
     * @return ResultInfo<List < QueryWarehouseForecastResultRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @PostMapping("queryWarehouseForecastResultHeadSelect")
    @ResponseBody
    @ApiOperation("查询分仓预测结果表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<QueryWarehouseForecastResultRspVo>> queryWarehouseForecastResultHeadSelect(
        @RequestBody QueryForecastWarehouseListReqVo queryForecastWarehouseListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo);
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo.getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo.getPredictionVersion());
        ValidateUtil.checkIsNotEmpty(queryForecastWarehouseListReqVo.getGroupColumnList());

        List<QueryWarehouseForecastResultRspVo> result = forecastResultService.queryWarehouseDemandReportHeadSelect(queryForecastWarehouseListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询分仓预测结果
     * @param condition
     * @return ResultInfo<PageInfo < QueryWarehouseForecastResultRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 9:18
     */
    @PostMapping("queryWarehouseForecastResultPage")
    @ResponseBody
    @ApiOperation("分页查询分仓预测结果")
    public ResultInfo<PageInfo<QueryWarehouseForecastResultRspVo>> queryWarehouseForecastResultPage(
        @RequestBody PageCondition<QueryForecastWarehouseListReqVo> condition) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getAlgoNameAndVersion());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getPredictionVersion());

        PageInfo<QueryWarehouseForecastResultRspVo> result = forecastResultService.queryWarehouseForecastResultPage(condition);
        return ResultInfo.success(result);
    }
}
