package cn.aliyun.ryytn.modules.demand.controller;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.mq.MqFactory;
import cn.aliyun.ryytn.modules.demand.entity.dto.OmsFileRecordDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;


/**
 * @Description oms同步文件导出
 * <AUTHOR>
 * @date 2024/7/10 10:24
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/omsChannelDemand")
@Api(tags = "oms同步文件导出")
public class OmsFileExportController
{

    @GetMapping("/exportFile")
    @ResponseBody
    @ApiOperation("生成同步文件手工")
    @RequiresPermissions(value = {})
    public ResultInfo<?> exportFile(@RequestParam("demandPlanCode") String demandPlanCode,@RequestParam("versionId") String versionId) throws Exception
    {
        OmsFileRecordDto omsFileRecordDto = new OmsFileRecordDto();
        omsFileRecordDto.setDemandPlanCode(demandPlanCode);
        omsFileRecordDto.setVersionId(versionId);
        MqFactory.newProducerService().produce(CommonConstants.TOPIC_OMS_FILE, omsFileRecordDto);
        return ResultInfo.success();
    }
}
