package cn.aliyun.ryytn.modules.demand.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.brain.dataindustry.common.enums.TaskStatus;
import com.aliyun.brain.dataindustry.microapp.MicroAppTaskInstanceOutputVO;
import com.aliyun.brain.dataindustry.microapp.request.ApiRunMicroAppRequest;
import com.aliyun.dataq.dataindustry.DataIndustrySpringServiceContext;
import com.aliyun.dataq.dataindustry.config.Header;
import com.aliyun.dataq.dataindustry.service.MicroAppService;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.DataqTask;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.mybatis.MybatisUtils;
import cn.aliyun.ryytn.common.utils.page.PageUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.ChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandReportService;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.constant.PlanDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectDimensionTypeEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import cn.aliyun.ryytn.modules.demand.dao.WarehouseDemandReportDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqWarehouseDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqWarehouseDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDemandReportDto;
import cn.aliyun.ryytn.modules.demand.entity.dto.WarehouseDto;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportDataVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.ChannelDemandReportRatioVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.DateLabelVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanVersionListRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandReportListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandReportRateRspVo;
import cn.aliyun.ryytn.modules.scheduler.dao.DataqTaskDao;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import cn.aliyun.ryytn.modules.system.api.ChannelService;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWeekListReqVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 分仓需求提报服务接口实现
 * <AUTHOR>
 * @date 2023/11/27 10:21
 */
@Slf4j
@Service
public class WarehouseDemandReportServiceImpl implements WarehouseDemandReportService
{
    @Autowired
    private WarehouseDemandReportDao warehouseDemandReportDao;

    @Autowired
    private DataqChannelDemandPlanDao dataqChannelDemandPlanDao;

    @Autowired
    private DataqWarehouseDemandPlanDao dataqWarehouseDemandPlanDao;

    @Autowired
    private CalendarService calendarService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private DataqWarehouseDao dataqWarehouseDao;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private MybatisUtils mybatisUtils;

    @Resource(name = "dataIndustryContext")
    private DataIndustrySpringServiceContext dataIndustryContext;

    @Value("${dataq.scheduler.userId}")
    private String userId;

    @Value("${dataq.scheduler.tenantCode}")
    private String tenantCode;

    @Value("${dataq.scheduler.workspaceCode}")
    private String workspaceCode;

    @Value("${datax.csEngine.code}")
    private String dataxCsEngineCode;

    @Value("${datax.table.schema}")
    private String dataxTableSchame;

    private static final String WAREHOUSEREPORT_TABLE_NAME = "t_ryytn_warehouse_demand_report_{}";

    @Autowired
    private DataqTaskDao dataqTaskDao;

    @Lazy
    @Autowired
    private ChannelDemandPlanService channelDemandPlanService;

    /**
     *
     * @Description 新增分仓需求提报，渠道需求计划版本共识操作（所有子计划全部确认提交）触发
     * 数据量过大，异步处理，暂不考虑事务
     * @param warehouseDemandReportDto
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 17:32
     */
    @Override
    public void addWarehouseDemandReport(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        // 分布式锁，dataq任务定时补偿机制里检查任务完成情况并视情况释放锁
        String lockKey =
            StringUtils.format(CommonConstants.REDIS_ADD_WAREHOUSE_DEMAND_REPORT_VERSION_LOCK_KEY, warehouseDemandReportDto.getDemandPlanCode(),
                warehouseDemandReportDto.getRollingVersion());
        boolean isLock = redisUtils.lock(lockKey, 1800);
        if (!isLock)
        {
            log.info("addWarehouseDemandReport has processing in another node.");
            return;
        }
        // 对应需求提报版本数据，由于渠道需求计划时间粒度如果为月，计划数据的planDate是精确到月的，版本号也是到月的，无法和渠道需求提报数据对应
        // 经过产品李伟亮确认：虽然当前入口可以创建时间粒度为月的渠道需求计划，但是后续使用过程中，会从使用流程角度控制渠道需求计划时间粒度都是按周的
        // 此处全部按时间粒度为周处理，仅保证出现月的数据时不报错即可，按月数据不做匹配
        if (BizDateTypeEnum.MONTH.equals(warehouseDemandReportDto.getBizDateType()))
        {
            return;
        }

        // 如果分仓需求提报版本已存在，则直接返回
        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);
        int num = warehouseDemandReportDao.queryWarehouseDemandReportVersionExists(warehouseDemandReportDto);
        if (num > 0)
        {
            return;
        }

        // 阿里数据探索批量写分仓需求提报数据是分片异步写入，可能存在未完全写完的数据被页面操作，但是阿里数据探索暂不支持入库中间状态后再原子操作修改状态为就绪。
        // 此处写入版本的操作从业务代码中删除，放在数据探索任务中，确保页面能查询到的数据是数据探索任务执行完成的数据。
        /**
         // 查询渠道需求计划名称
         String demandPlanName = dataqChannelDemandPlanDao.queryChannelDemandPlanName(warehouseDemandReportDto.getDemandPlanCode());

         // 新增分仓需求提报版本
         WarehouseDemandReportVersionDto warehouseDemandReportVersion = new WarehouseDemandReportVersionDto();
         warehouseDemandReportVersion.setDemandPlanCode(warehouseDemandReportDto.getDemandPlanCode());
         warehouseDemandReportVersion.setName(demandPlanName);
         warehouseDemandReportVersion.setRollingVersion(warehouseDemandReportDto.getRollingVersion());
         warehouseDemandReportDao.addWarehouseDemandReportVersion(warehouseDemandReportVersion);
         */

        // 调用阿里数据探索服务，直接通过sql插入分仓需求提报数据
        String appCode = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_APPCODE_ADD_WAREHOUSE_DEMAND_REPORT_VERSION");

        ApiRunMicroAppRequest apiRunMicroAppRequest = new ApiRunMicroAppRequest();
        apiRunMicroAppRequest.setAppCode(appCode);
        Map<String, Object> map = new HashMap<>();
        map.put("demandPlanCode", warehouseDemandReportDto.getDemandPlanCode());
        map.put("versionId", warehouseDemandReportDto.getRollingVersion());
        map.put("planDateStart", warehouseDemandReportDto.getPlanDateStart());
        map.put("planDateEnd", warehouseDemandReportDto.getPlanDateEnd());
        map.put("creator", warehouseDemandReportDto.getCreator());
        Map<String, Object> warehouseTable = new HashMap<>();
        warehouseTable.put("csEngineCode", dataxCsEngineCode);
        warehouseTable.put("tableName", StringUtils.format(WAREHOUSEREPORT_TABLE_NAME, tableSuffix));
        warehouseTable.put("tableSchema", dataxTableSchame);
        map.put("warehouseTable", warehouseTable);
        apiRunMicroAppRequest.setApiParamValues(map);

        log.info("addWarehouseDemandReport param:{}", JSON.toJSONString(map));

        // 配置header
        Header header = new Header();
        header.setUserId(userId);
        header.setTenantCode(tenantCode);
        header.setWorkspaceCode(workspaceCode);

        // 执行算法调度任务
        MicroAppService service = dataIndustryContext.getService(MicroAppService.class);
        MicroAppTaskInstanceOutputVO execute = null;
        try
        {
            execute = service.execute(apiRunMicroAppRequest, header);
        }
        catch (Exception e)
        {
            log.error("addWarehouseDemandReport has exception:", e);
        }
        log.info("addWarehouseDemandReport.execute:{}", execute);

        if (Objects.isNull(execute))
        {
            execute = new MicroAppTaskInstanceOutputVO();
            Date currentTime = new Date();
            execute.setStatus(TaskStatus.FAILED);
            execute.setStartTime(currentTime);
            execute.setEndTime(currentTime);
        }

        // 新增阿里任务调度记录数据
        DataqTask dataqTask = new DataqTask();
        dataqTask.setTaskId(execute.getTaskInstanceId());
        dataqTask.setJobId("-1");
        dataqTask.setAppCode(appCode);
        dataqTask.setParam(JSON.toJSONString(map));
        dataqTask.setStatus(execute.getStatus().name());
        dataqTask.setReloadId(0L);
        dataqTask.setStartTime(execute.getStartTime());
        dataqTask.setEndTime(execute.getEndTime());
        dataqTask.setLockKey(lockKey);
        dataqTaskDao.addDataqTask(dataqTask);
    }

    /**
     *
     * @Description 新增分仓需求提报，渠道需求计划版本共识操作（所有子计划全部确认提交）触发
     * 废弃方法，废弃原因：
     * 元旦前应付客户演示，产品暂时要求平均拆分，后需要改回为按照提报数据比例+历史订单比例拆分
     * @param warehouseDemandReportDto
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月27日 17:32
     */
    @Async
    @Deprecated
    public void addWarehouseDemandReportLocalDB(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        // 对应需求提报版本数据，由于渠道需求计划时间粒度如果为月，计划数据的planDate是精确到月的，版本号也是到月的，无法和渠道需求提报数据对应
        // 经过产品李伟亮确认：虽然当前入口可以创建时间粒度为月的渠道需求计划，但是后续使用过程中，会从使用流程角度控制渠道需求计划时间粒度都是按周的
        // 此处全部按时间粒度为周处理，仅保证出现月的数据时不报错即可，按月数据不做匹配
        if (BizDateTypeEnum.MONTH.equals(warehouseDemandReportDto.getBizDateType()))
        {
            return;
        }

        // 如果分仓需求提报版本已存在，则直接返回
        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);
        int num = warehouseDemandReportDao.queryWarehouseDemandReportVersionExists(warehouseDemandReportDto);
        if (num > 0)
        {
            return;
        }

        // 根据需求计划编号查询需求计划，获取需求计划名称
        QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo = new QueryChannelDemandPlanListReqVo();
        queryChannelDemandPlanListReqVo.setDemandPlanCode(warehouseDemandReportDto.getDemandPlanCode());
        queryChannelDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.order);
        queryChannelDemandPlanListReqVo.setDeleted(false);
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandPlanListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return;
        }
        List<QueryChannelDemandPlanListRspVo> channelDemandPlanList = jsonArray.toJavaList(QueryChannelDemandPlanListRspVo.class);
        QueryChannelDemandPlanListRspVo channelDemandPlan = channelDemandPlanList.get(0);
        String demandPlanName = channelDemandPlan.getDemandPlanName();

        // 只获取共识当天所在周+下四周，5*7一定包含，再调用接口通过limit控制
        Date currentDay = new Date();
        String beginDate = DateUtils.formatTime(currentDay, DateUtils.YMD);
        String endDate = DateUtils.formatTime(DateUtils.addDays(currentDay, 5 * 7), DateUtils.YMD);

        // 查询dataq周数据
        QueryWeekListReqVo queryWeekListReqVo = new QueryWeekListReqVo();
        queryWeekListReqVo.setBeginDate(beginDate);
        queryWeekListReqVo.setEndDate(endDate);
        List<DataqWeek> dataqWeekList = calendarService.queryWeekList(queryWeekListReqVo);

        // 排序并仅获取5周数据
        dataqWeekList = dataqWeekList.stream().sorted(Comparator.comparing(DataqWeek::getFsclWeekStart)).limit(5).collect(Collectors.toList());
        beginDate = DateUtils.formatTime(DateUtils.parseDate(dataqWeekList.get(0).getFsclWeekStart(), DateUtils.YMD), DateUtils.YMD_DASH);
        endDate = DateUtils.formatTime(DateUtils.parseDate(dataqWeekList.get(4).getFsclWeekStart(), DateUtils.YMD), DateUtils.YMD_DASH);

        // 查询dataq接口渠道需求计划版本列表
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(warehouseDemandReportDto.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setVersionId(warehouseDemandReportDto.getRollingVersion());
        queryChannelDemandPlanVersionListReqVo.setBeginDate(beginDate);
        queryChannelDemandPlanVersionListReqVo.setEndDate(endDate);
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);

        // 查询dataq接口json反序列化性能太差，修改为查询数据库
        /**
         path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
         dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandPlanVersionListReqVo);
         jsonArray = (JSONArray) dataqResult.getData();
         if (CollectionUtils.isEmpty(jsonArray))
         {
         throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
         }

         // 渠道需求计划数据
         List<QueryChannelDemandPlanVersionListRspVo> channelDemandPlanDataList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);
         */
        List<QueryChannelDemandPlanVersionListRspVo> channelDemandPlanDataList =
            dataqChannelDemandPlanDao.queryChannelDemandPlanVersionList(queryChannelDemandPlanVersionListReqVo);

        // 查询渠道列表，获取每个二级渠道下的三级渠道列表
        List<ChannelDto> allChannelList = channelService.queryChannelList();
        Map<String, List<ChannelDto>> lv2ChannelLv3ChannelMap = allChannelList.stream().collect(Collectors.groupingBy(ChannelDto::getLv2ChannelCode));

        // 查询所有仓库数据
        List<WarehouseDto> allWarehouseList = dataqWarehouseDao.queryRdcPhysicWarehouseList();
        // 仓库为空直接退出
        if (CollectionUtils.isEmpty(allWarehouseList))
        {
            log.error("addWarehouseDemandReport exit error whith allWarehouseList is empty");
            return;
        }

        BigDecimal warehouseNum = new BigDecimal(allWarehouseList.size());

        String currentAccount = warehouseDemandReportDto.getCreator();
        Date currentTime = new Date();

        final int BATCH_SIZE = 100000;
        List<WarehouseDemandReportDto> warehouseDemandReportDtoList = new ArrayList<>(BATCH_SIZE);
        // 遍历所有渠道需求计划数据
        for (QueryChannelDemandPlanVersionListRspVo channelDemandPlanData : channelDemandPlanDataList)
        {
            String lv2ChannelCode = channelDemandPlanData.getLv2ChannelCode();
            // 遍历渠道需求计划二级渠道下所有三级渠道
            List<ChannelDto> lv3ChannelList = lv2ChannelLv3ChannelMap.get(lv2ChannelCode);
            // 如果渠道需求计划二级渠道下不存在三级渠道，直接跳过
            if (CollectionUtils.isEmpty(lv3ChannelList))
            {
                continue;
            }
            if (Objects.isNull(channelDemandPlanData.getPlanValue()))
            {
                channelDemandPlanData.setPlanValue(0d);
            }
            BigDecimal planValue = BigDecimal.valueOf(channelDemandPlanData.getPlanValue());
            BigDecimal lv3ChannelNum = BigDecimal.valueOf(lv3ChannelList.size());
            // 应付客户演示，直接按照拆分平均计算数量
            Double orderNum = planValue.divide(lv3ChannelNum, 2, RoundingMode.HALF_UP).divide(warehouseNum, 2, RoundingMode.HALF_UP).doubleValue();
            for (ChannelDto lv3Channel : lv3ChannelList)
            {
                // 遍历全部仓库
                for (WarehouseDto warehouse : allWarehouseList)
                {
                    WarehouseDemandReportDto warehouseDemandReport = new WarehouseDemandReportDto();
                    warehouseDemandReport.setTableSuffix(tableSuffix);
                    warehouseDemandReport.setId(SeqUtils.getSequenceUid());
                    warehouseDemandReport.setDemandPlanCode(warehouseDemandReportDto.getDemandPlanCode());
                    warehouseDemandReport.setName(demandPlanName);
                    warehouseDemandReport.setRollingVersion(warehouseDemandReportDto.getRollingVersion());
                    warehouseDemandReport.setOrderNum(orderNum);
                    warehouseDemandReport.setBizDateType(warehouseDemandReportDto.getBizDateType());
                    warehouseDemandReport.setBizDateValue(
                        StringUtils.replace(channelDemandPlanData.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY));
                    warehouseDemandReport.setSkuCode(channelDemandPlanData.getSkuCode());
                    warehouseDemandReport.setSkuName(channelDemandPlanData.getSkuName());
                    warehouseDemandReport.setLv1CategoryCode(channelDemandPlanData.getLv1CategoryCode());
                    warehouseDemandReport.setLv1CategoryName(channelDemandPlanData.getLv1CategoryName());
                    warehouseDemandReport.setLv2CategoryCode(channelDemandPlanData.getLv2CategoryCode());
                    warehouseDemandReport.setLv2CategoryName(channelDemandPlanData.getLv2CategoryName());
                    warehouseDemandReport.setLv3CategoryCode(channelDemandPlanData.getLv3CategoryCode());
                    warehouseDemandReport.setLv3CategoryName(channelDemandPlanData.getLv3CategoryName());
                    warehouseDemandReport.setLv1ChannelCode(channelDemandPlanData.getLv1ChannelCode());
                    warehouseDemandReport.setLv1ChannelName(channelDemandPlanData.getLv1ChannelName());
                    warehouseDemandReport.setLv2ChannelCode(channelDemandPlanData.getLv2ChannelCode());
                    warehouseDemandReport.setLv2ChannelName(channelDemandPlanData.getLv2ChannelName());
                    warehouseDemandReport.setUnit(null);
                    warehouseDemandReport.setRemark(null);
                    warehouseDemandReport.setExtend(null);
                    warehouseDemandReport.setIsModify(0);
                    warehouseDemandReport.setCreator(currentAccount);
                    warehouseDemandReport.setLastModifier(currentAccount);
                    warehouseDemandReport.setGmtCreate(currentTime);
                    warehouseDemandReport.setGmtModify(currentTime);
                    warehouseDemandReport.setLv3ChannelCode(lv3Channel.getLv3ChannelCode());
                    warehouseDemandReport.setLv3ChannelName(lv3Channel.getLv3ChannelName());
                    warehouseDemandReport.setReceiverType("to");////因dataQ接口调整,渠道信息不返回channelType,该方法被丢弃,修改为to
                    warehouseDemandReport.setWarehouseCode(warehouse.getBizWarehouseCode());
                    warehouseDemandReport.setWarehouseName(warehouse.getBizWarehouseName());
                    warehouseDemandReportDtoList.add(warehouseDemandReport);
                    if (warehouseDemandReportDtoList.size() >= BATCH_SIZE)
                    {
                        // 新增分仓需求提报数据，批量提交
                        mybatisUtils.batchUpdateOrInsert(warehouseDemandReportDtoList, WarehouseDemandReportDao.class,
                            (item, warehouseDemandReportDao) -> warehouseDemandReportDao.batchAddWarehouseDemandReport(item));

                        // 如果数据量过大，需要分片
//                        int fragmentSize = 1000;
//                        if (warehouseDemandReportDtoList.size() < fragmentSize)
//                        {
//                            warehouseDemandReportDao.addWarehouseDemandReport(warehouseDemandReportDtoList);
//                        }
//                        else
//                        {
//                            // 单例map获取线程池，不需要shutdown
//                            ThreadPoolExecutor pool =
//                                ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.fragmentThread);
//                            int dataSize = warehouseDemandReportDtoList.size();
//                            int threadCount =
//                                Math.floorMod(dataSize, fragmentSize) == 0 ? Math.floorDiv(dataSize, fragmentSize) : Math.floorDiv(dataSize, fragmentSize) + 1;
//                            CountDownLatch countDownLatch = new CountDownLatch(threadCount);
//                            for (Integer i = 0; i < threadCount; i++)
//                            {
//                                final int start = i * fragmentSize;
//                                final List<WarehouseDemandReportDto> subData =
//                                    warehouseDemandReportDtoList.stream().skip(start).limit(fragmentSize).collect(Collectors.toList());
//                                pool.submit(() -> {
//                                    try
//                                    {
//                                        warehouseDemandReportDao.addWarehouseDemandReport(subData);
//                                    }
//                                    catch (Exception e)
//                                    {
//                                        log.error("addWarehouseDemandReport has exception.", e);
//                                    }
//                                    finally
//                                    {
//                                        countDownLatch.countDown();
//                                        subData.clear();
//                                    }
//                                });
//                            }
//                            countDownLatch.await();
//                        }
                        warehouseDemandReportDtoList.clear();
                    }
                }
            }
        }
    }

    @Deprecated
    @Async
    public void addWarehouseDemandReportOld(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        // 对应需求提报版本数据，由于渠道需求计划时间粒度如果为月，计划数据的planDate是精确到月的，版本号也是到月的，无法和渠道需求提报数据对应
        // 经过产品李伟亮确认：虽然当前入口可以创建时间粒度为月的渠道需求计划，但是后续使用过程中，会从使用流程角度控制渠道需求计划时间粒度都是按周的
        // 此处全部按时间粒度为周处理，仅保证出现月的数据时不报错即可，按月数据不做匹配
        if (BizDateTypeEnum.MONTH.equals(warehouseDemandReportDto.getBizDateType()))
        {
            return;
        }

        // 根据需求计划编号查询需求计划，获取需求计划名称
        QueryChannelDemandPlanListReqVo queryChannelDemandPlanListReqVo = new QueryChannelDemandPlanListReqVo();
        queryChannelDemandPlanListReqVo.setDemandPlanCode(warehouseDemandReportDto.getDemandPlanCode());
        queryChannelDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.order);
        queryChannelDemandPlanListReqVo.setDeleted(false);
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandPlanListReqVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            return;
        }
        List<QueryChannelDemandPlanListRspVo> channelDemandPlanList = jsonArray.toJavaList(QueryChannelDemandPlanListRspVo.class);
        QueryChannelDemandPlanListRspVo channelDemandPlan = channelDemandPlanList.get(0);
        String demandPlanName = channelDemandPlan.getDemandPlanName();

        // 只获取共识当天所在周+下四周，5*7一定包含，再调用接口通过limit控制
        Date currentDay = new Date();
        String beginDate = DateUtils.formatTime(currentDay, DateUtils.YMD);
        String endDate = DateUtils.formatTime(DateUtils.addDays(currentDay, 5 * 7), DateUtils.YMD_DASH);

        // 查询dataq周数据
        QueryWeekListReqVo queryWeekListReqVo = new QueryWeekListReqVo();
        queryWeekListReqVo.setBeginDate(beginDate);
        queryWeekListReqVo.setEndDate(endDate);
        List<DataqWeek> dataqWeekList = calendarService.queryWeekList(queryWeekListReqVo);

        // 排序并仅获取5周数据
        dataqWeekList = dataqWeekList.stream().sorted(Comparator.comparing(DataqWeek::getFsclWeekStart)).limit(5).collect(Collectors.toList());
        beginDate = dataqWeekList.stream().min(Comparator.comparing(DataqWeek::getFsclWeekStart)).get().getFsclWeekStart();
        endDate = dataqWeekList.stream().max(Comparator.comparing(DataqWeek::getFsclWeekStart)).get().getFsclWeekStart();

        // 查询dataq接口渠道需求计划版本列表
        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(warehouseDemandReportDto.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setVersionId(warehouseDemandReportDto.getRollingVersion());
        queryChannelDemandPlanVersionListReqVo.setBeginDate(beginDate);
        queryChannelDemandPlanVersionListReqVo.setEndDate(endDate);
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);

        path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_CHANNEL_PLAN_LIST"));
        dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandPlanVersionListReqVo);
        jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isEmpty(jsonArray))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }

        // 渠道需求计划数据
        List<QueryChannelDemandPlanVersionListRspVo> channelDemandPlanDataList = jsonArray.toJavaList(QueryChannelDemandPlanVersionListRspVo.class);

        // 封装根据渠道需求计划查询对应渠道需求提报数据条件
        String lv1ChannelCodes = null;
        String lv2ChannelCodes = null;
        String lv3ChannelCodes = null;
        // 计划主体：一级渠道
        if (SubjectDimensionTypeEnum.lv1Channel.equals(channelDemandPlan.getSubjectDimensionType()))
        {
            lv1ChannelCodes = channelDemandPlanDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv1ChannelCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        }
        // 计划主体：二级渠道
        else
        {
            lv1ChannelCodes = channelDemandPlanDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv1ChannelCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
            lv2ChannelCodes = channelDemandPlanDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv2ChannelCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        }

        String lv1CategoryCodes = null;
        String lv2CategoryCodes = null;
        String lv3CategoryCodes = null;
        // 计划对象：一级品类
        if (PlanDimensionTypeEnum.lv1Category.equals(channelDemandPlan.getPlanDimensionType()))
        {
            lv1CategoryCodes = channelDemandPlanDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv1CategoryCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        }
        // 计划对象：二级品类
        else if (PlanDimensionTypeEnum.lv1Category.equals(channelDemandPlan.getPlanDimensionType()))
        {
            lv1CategoryCodes = channelDemandPlanDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv1CategoryCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
            lv2CategoryCodes = channelDemandPlanDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv2CategoryCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        }
        // 计划对象：三级品类
        else
        {
            lv1CategoryCodes = channelDemandPlanDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv1CategoryCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
            lv2CategoryCodes = channelDemandPlanDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv2CategoryCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
            lv3CategoryCodes = channelDemandPlanDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getLv3CategoryCode).distinct()
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        }
        String skuCodes = channelDemandPlanDataList.stream().map(QueryChannelDemandPlanVersionListRspVo::getSkuCode).distinct()
            .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));

        // 根据渠道需求计划版本，获取渠道需求提报版本
        // 需求计划版本格式：DPyyyyMMWw或者DPyyyyMMWw-n
        // 需求提报版本格式：CDPyyyyMMWw
        String rollingVersion =
            StringUtils.replace(StringUtils.substringBefore(warehouseDemandReportDto.getRollingVersion(), StringUtils.DATE_SEPARATOR), "DP", "CDP");

        // 查询渠道需求提报数据，用于计算三级渠道比例
        QueryChannelDemandReportListReqVo queryChannelDemandReportListReqVo = new QueryChannelDemandReportListReqVo();
        queryChannelDemandReportListReqVo.setRollingVersion(rollingVersion);
        queryChannelDemandReportListReqVo.setLv1ChannelCodes(lv1ChannelCodes);
        queryChannelDemandReportListReqVo.setLv2ChannelCodes(lv2ChannelCodes);
        queryChannelDemandReportListReqVo.setLv3ChannelCodes(lv3ChannelCodes);
        queryChannelDemandReportListReqVo.setLv1CategoryCodes(lv1CategoryCodes);
        queryChannelDemandReportListReqVo.setLv2CategoryCodes(lv2CategoryCodes);
        queryChannelDemandReportListReqVo.setLv3CategoryCodes(lv3CategoryCodes);
        queryChannelDemandReportListReqVo.setSkuCodes(skuCodes);
        queryChannelDemandReportListReqVo.setBizDateType(warehouseDemandReportDto.getBizDateType());
        queryChannelDemandReportListReqVo.setBeginDate(beginDate);
        queryChannelDemandReportListReqVo.setEndDate(endDate);
        queryChannelDemandReportListReqVo.setIsModify(0);

        path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CHANNEL_DEMAND_REPORT_RATIO"));
        dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryChannelDemandReportListReqVo);
        jsonArray = (JSONArray) dataqResult.getData();
        // 如果查询不到渠道需求提报数据，后续循环拆分无法拆分
        Map<ChannelDemandReportRatioVo, ChannelDemandReportRatioVo> channelDemandReportRatioMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            List<ChannelDemandReportRatioVo> channelDemandReportRatioList = jsonArray.toJavaList(ChannelDemandReportRatioVo.class);
            for (ChannelDemandReportRatioVo channelDemandReportRatio : channelDemandReportRatioList)
            {
                Map<String, Double> lv3ChannelData = channelDemandReportRatio.getLv3ChannelData();
                BigDecimal total = new BigDecimal(channelDemandReportRatio.getTotalNum());
                for (String lv3ChannelCode : lv3ChannelData.keySet())
                {
                    Double lv3OrderNum = lv3ChannelData.get(lv3ChannelCode);
                    Double ratio = 1d;
                    if (!BigDecimal.ZERO.equals(total))
                    {
                        ratio = new BigDecimal(lv3OrderNum).divide(total, 2, RoundingMode.HALF_UP).doubleValue();
                    }
                    lv3ChannelData.put(lv3ChannelCode, ratio);
                }
            }

            channelDemandReportRatioMap =
                channelDemandReportRatioList.stream().collect(Collectors.toMap(Function.identity(), Function.identity(), (key1, key2) -> key1));
        }

        // 查询渠道列表，下面封装分仓需求提报数据，需要根据三级渠道编号映射三级渠道名称
        List<ChannelDto> channelList = channelService.queryChannelList();
        Map<String, String> channelMap = channelList.stream().collect(Collectors.toMap(ChannelDto::getLv3ChannelCode, ChannelDto::getLv3ChannelName,
            (key1, key2) -> key1));

        // 查询仓库比例
        JSONObject queryWarehouseRate = new JSONObject();
        queryWarehouseRate.put("lv1ChannelCodes", lv1ChannelCodes);
        queryWarehouseRate.put("lv2ChannelCodes", lv2ChannelCodes);
        queryWarehouseRate.put("lv3ChannelCodes", lv3ChannelCodes);
        queryWarehouseRate.put("lv1CategoryCodes", lv1CategoryCodes);
        queryWarehouseRate.put("lv2CategoryCodes", lv2CategoryCodes);
        queryWarehouseRate.put("lv3CategoryCodes", lv3CategoryCodes);
        queryWarehouseRate.put("skuCodes", skuCodes);
        path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BASEBUS_DEMAND_REPORT_RATE"));
        dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryWarehouseRate);
        jsonArray = (JSONArray) dataqResult.getData();
        // 如果查询不到仓库比例数据，后续循环拆分无法拆分
        Map<QueryWarehouseDemandReportRateRspVo, List<QueryWarehouseDemandReportRateRspVo>> warehouseRateMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            List<QueryWarehouseDemandReportRateRspVo> warehouseRateList = jsonArray.toJavaList(QueryWarehouseDemandReportRateRspVo.class);
            warehouseRateMap = warehouseRateList.stream().collect(Collectors.groupingBy(Function.identity()));
        }

        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        Date currentTime = new Date();

        List<WarehouseDemandReportDto> warehouseDemandReportDtoList = new ArrayList<>();
        // 遍历所有渠道需求计划数据
        for (QueryChannelDemandPlanVersionListRspVo channelDemandPlanData : channelDemandPlanDataList)
        {
            if (Objects.isNull(channelDemandPlanData.getPlanValue()))
            {
                channelDemandPlanData.setPlanValue(0d);
            }
            BigDecimal planValue = new BigDecimal(channelDemandPlanData.getPlanValue());
            String bizDateValue = channelDemandPlanData.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
            ChannelDemandReportRatioVo reportCondition = new ChannelDemandReportRatioVo();
            reportCondition.setSkuCode(channelDemandPlanData.getSkuCode());
            reportCondition.setLv1CategoryCode(channelDemandPlanData.getLv1CategoryCode());
            reportCondition.setLv2CategoryCode(channelDemandPlanData.getLv2CategoryCode());
            reportCondition.setLv3CategoryCode(channelDemandPlanData.getLv3CategoryCode());
            reportCondition.setLv1ChannelCode(channelDemandPlanData.getLv1ChannelCode());
            reportCondition.setLv2ChannelCode(channelDemandPlanData.getLv2ChannelCode());
            reportCondition.setBizDateValue(bizDateValue);
            ChannelDemandReportRatioVo lv3ChannelRatio = channelDemandReportRatioMap.get(reportCondition);
            // 该周没有对应二级渠道下三级渠道的提报数据
            if (Objects.isNull(lv3ChannelRatio) || Objects.isNull(lv3ChannelRatio.getLv3ChannelData()))
            {
                continue;
            }
            Double channelDemandReportLv2OrderNum = lv3ChannelRatio.getTotalNum();
            Double warehouseDemandReportLv2OrderNum = 0d;
            List<WarehouseDemandReportDto> lv2WarehouseDemandReportList = new ArrayList<>();
            // 计算所有二级渠道下各个三级渠道提报数据比例
            for (Map.Entry<String, Double> entry : lv3ChannelRatio.getLv3ChannelData().entrySet())
            {
                String lv3ChannelCode = entry.getKey();
                QueryWarehouseDemandReportRateRspVo warehouseRateCondition = new QueryWarehouseDemandReportRateRspVo();
                warehouseRateCondition.setSkuCode(channelDemandPlanData.getSkuCode());
                warehouseRateCondition.setLv1CategoryCode(channelDemandPlanData.getLv1CategoryCode());
                warehouseRateCondition.setLv2CategoryCode(channelDemandPlanData.getLv2CategoryCode());
                warehouseRateCondition.setLv3CategoryCode(channelDemandPlanData.getLv3CategoryCode());
                warehouseRateCondition.setLv1ChannelCode(channelDemandPlanData.getLv1ChannelCode());
                warehouseRateCondition.setLv2ChannelCode(channelDemandPlanData.getLv2ChannelCode());
                warehouseRateCondition.setLv3ChannelCode(lv3ChannelCode);
                List<QueryWarehouseDemandReportRateRspVo> warehouseRates = warehouseRateMap.get(warehouseRateCondition);
                if (CollectionUtils.isEmpty(warehouseRates))
                {
                    continue;
                }
                String lv3ChannelName = channelMap.get(lv3ChannelCode);
                BigDecimal lv3ChannelRate = new BigDecimal(entry.getValue());
                // 计算所有三级渠道对应所有仓库提报数据比例
                for (QueryWarehouseDemandReportRateRspVo warehouseRateItem : warehouseRates)
                {
                    BigDecimal warehouseRate = new BigDecimal(Objects.isNull(warehouseRateItem.getOutboundRate()) ? 1d : warehouseRateItem.getOutboundRate());
                    Double orderNum = planValue.multiply(lv3ChannelRate).multiply(warehouseRate).setScale(2, RoundingMode.UP).doubleValue();
                    WarehouseDemandReportDto warehouseDemandReport = new WarehouseDemandReportDto();
                    warehouseDemandReport.setId(SeqUtils.getSequenceUid());
                    warehouseDemandReport.setDemandPlanCode(warehouseDemandReportDto.getDemandPlanCode());
                    warehouseDemandReport.setName(demandPlanName);
                    warehouseDemandReport.setRollingVersion(warehouseDemandReportDto.getRollingVersion());
                    warehouseDemandReport.setOrderNum(orderNum);
                    warehouseDemandReport.setBizDateType(warehouseDemandReportDto.getBizDateType());
                    warehouseDemandReport.setBizDateValue(bizDateValue);
                    warehouseDemandReport.setSkuCode(channelDemandPlanData.getSkuCode());
                    warehouseDemandReport.setSkuName(channelDemandPlanData.getSkuName());
                    warehouseDemandReport.setLv1CategoryCode(channelDemandPlanData.getLv1CategoryCode());
                    warehouseDemandReport.setLv1CategoryName(channelDemandPlanData.getLv1CategoryName());
                    warehouseDemandReport.setLv2CategoryCode(channelDemandPlanData.getLv2CategoryCode());
                    warehouseDemandReport.setLv2CategoryName(channelDemandPlanData.getLv2CategoryName());
                    warehouseDemandReport.setLv3CategoryCode(channelDemandPlanData.getLv3CategoryCode());
                    warehouseDemandReport.setLv3CategoryName(channelDemandPlanData.getLv3CategoryName());
                    warehouseDemandReport.setLv1ChannelCode(channelDemandPlanData.getLv1ChannelCode());
                    warehouseDemandReport.setLv1ChannelName(channelDemandPlanData.getLv1ChannelName());
                    warehouseDemandReport.setLv2ChannelCode(channelDemandPlanData.getLv2ChannelCode());
                    warehouseDemandReport.setLv2ChannelName(channelDemandPlanData.getLv2ChannelName());
                    warehouseDemandReport.setLv3ChannelCode(lv3ChannelCode);
                    warehouseDemandReport.setLv3ChannelName(lv3ChannelName);
                    warehouseDemandReport.setReceiverType(warehouseRateItem.getChannelType());
                    warehouseDemandReport.setWarehouseCode(warehouseRateItem.getWarehouseCode());
                    warehouseDemandReport.setWarehouseName(warehouseRateItem.getWarehouseName());
                    warehouseDemandReport.setUnit(null);
                    warehouseDemandReport.setRemark(null);
                    warehouseDemandReport.setExtend(null);
                    warehouseDemandReport.setIsModify(0);
                    warehouseDemandReport.setCreator(currentAccount.getName());
                    warehouseDemandReport.setLastModifier(currentAccount.getName());
                    warehouseDemandReport.setGmtCreate(currentTime);
                    warehouseDemandReport.setGmtModify(currentTime);
                    lv2WarehouseDemandReportList.add(warehouseDemandReport);
                    warehouseDemandReportLv2OrderNum += orderNum;
                }
            }
            // 首次加载时，二级渠道分仓需求数量汇总与二级渠道渠道需求提报数量汇总不一致时，对二级渠道下全部三级渠道的分仓需求提报明细数据进行异常行颜色的显示提醒；
            // 当三级渠道分仓需求提报数据被调整后，异常行颜色消失；
            double deviationRadio = channelDemandReportLv2OrderNum.equals(warehouseDemandReportLv2OrderNum) ? 0d : 1d;
            lv2WarehouseDemandReportList.forEach(item -> {
                item.setDeviationRadio(deviationRadio);
            });
            warehouseDemandReportDtoList.addAll(lv2WarehouseDemandReportList);
            lv2WarehouseDemandReportList.clear();
        }

        // 新增分仓需求提报数据，批量提交
        mybatisUtils.batchUpdateOrInsert(warehouseDemandReportDtoList, WarehouseDemandReportDao.class,
            (item, warehouseDemandReportDao) -> warehouseDemandReportDao.batchAddWarehouseDemandReport(item));
    }

    /**
     *
     * @Description 查询分仓需求提报对应渠道需求计划列表
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 11:55
     */
    @Override
    public List<WarehouseDemandReportDto> queryWarehouseDemandReportPlanList() throws Exception
    {
        // fix: 1716 产品李伟亮要求直接查询渠道需求计划，可以没有版本数据，但是只要有计划，就要有后续下游数据的计划
//        return warehouseDemandReportDao.queryWarehouseDemandReportPlanList();
        JSONArray jsonArray = channelDemandPlanService.queryChannelDemandPlanList();
        List<WarehouseDemandReportDto> result = Collections.EMPTY_LIST;
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            result = jsonArray.toJavaList(WarehouseDemandReportDto.class);
        }
        return result;
    }

    /**
     *
     * @Description 查询分仓需求提报版本列表
     * @param demandPlanCode
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月06日 16:32
     */
    @Override
    public List<String> queryWarehouseDemandReportVersionList(String demandPlanCode) throws Exception
    {
        return warehouseDemandReportDao.queryWarehouseDemandReportVersionList(demandPlanCode);
    }

    /**
     *
     * @Description 查询分仓需求提报数据列表
     * @param warehouseDemandReportDto
     * @return BaseTable<List < WarehouseDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月30日 20:49
     */
    @Override
    public BaseTable<List<WarehouseDemandReportDto>> queryWarehouseDemandReportList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        BaseTable<List<WarehouseDemandReportDto>> baseTable = new BaseTable<>();

        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);
        // 获取分仓需求提报时间
        List<String> bizDateValueSet = warehouseDemandReportDao.queryWarehouseDemandReportDateList(warehouseDemandReportDto);

        // 获取缓存中分仓需求提报时间对应的周对象
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueSet);
        List<DataqWeek> dataqWeekList = objectList.stream().map(item -> {
            return (DataqWeek) item;
        }).sorted(Comparator.comparing(DataqWeek::getFsclWeekStart)).collect(Collectors.toList());

        // 动态表头
        List<String> headList =
            dataqWeekList.stream().sorted(Comparator.comparing(DataqWeek::getFsclWeekStart)).map(DataqWeek::getWeekLabel).collect(Collectors.toList());

        // 查询数据库中的分仓需求提报数据
        List<WarehouseDemandReportDto> warehouseDemandReportList = warehouseDemandReportDao.queryWarehouseDemandReportList(warehouseDemandReportDto);

        if (CollectionUtils.isEmpty(warehouseDemandReportList))
        {
            return baseTable;
        }

        // 封装固定字段+动态数据
        Map<WarehouseDemandReportDto, List<WarehouseDemandReportDto>> warehouseDemandReportMap =
            warehouseDemandReportList.stream().collect(Collectors.groupingBy(Function.identity()));

        List<WarehouseDemandReportDto> reportList = new ArrayList<>(warehouseDemandReportMap.size());
        for (Map.Entry<WarehouseDemandReportDto, List<WarehouseDemandReportDto>> entry : warehouseDemandReportMap.entrySet())
        {
            WarehouseDemandReportDto warehouseDemandReport = entry.getKey();
            warehouseDemandReport.setDataMap(new HashMap<>());
            List<WarehouseDemandReportDto> list = entry.getValue();
            Map<String, WarehouseDemandReportDto> map =
                list.stream().collect(Collectors.toMap(WarehouseDemandReportDto::getBizDateValue, Function.identity(), (key1,
                    key2) -> key1));
            long count = list.stream().filter(item -> {
                return item.getDeviationRadio() > 0d;
            }).count();
            Double deviationRadio = count > 0 ? 1d : 0d;
            for (DataqWeek dataqWeek : dataqWeekList)
            {
                WarehouseDemandReportDto report = map.get(dataqWeek.getFsclWeekStart());
                String id = null;
                Double orderNum = 0d;
                if (Objects.nonNull(report))
                {
                    id = report.getId();
                    orderNum = report.getOrderNum();
                }
                ChannelDemandReportDataVo channelDemandReportDataVo = new ChannelDemandReportDataVo();
                channelDemandReportDataVo.setId(id);
                channelDemandReportDataVo.setDeviationRadio(deviationRadio);
                channelDemandReportDataVo.setOrderNum(orderNum);
                channelDemandReportDataVo.setLastOrderNum(null);
                warehouseDemandReport.getDataMap().put(dataqWeek.getWeekLabel(), channelDemandReportDataVo);
            }

            reportList.add(warehouseDemandReport);
        }

        baseTable.setHeadArray(headList);
        baseTable.setList(reportList);

        return baseTable;
    }

    /**
     *
     * @Description 查询分仓需求提报编辑页面时间页签
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDateLabelVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 15:55
     */
    @Override
    public List<DateLabelVo> queryWarehouseDemandReportDateLabelList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);
        List<String> bizDateValueList = warehouseDemandReportDao.queryWarehouseDemandReportDateList(warehouseDemandReportDto);
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);
        if (CollectionUtils.isEmpty(objectList))
        {
            return null;
        }

        List<DateLabelVo> result = objectList.stream().map(item -> {
            DataqWeek dataqWeek = (DataqWeek) item;
            DateLabelVo dateLabel = new DateLabelVo();
            dateLabel.setBizDateLabel(dataqWeek.getWeekLabel());
            dateLabel.setBizDateValue(dataqWeek.getFsclWeekStart());
            return dateLabel;
        }).sorted(Comparator.comparing(DateLabelVo::getBizDateValue)).collect(Collectors.toList());

        return result;
    }

    /**
     *
     * @Description 查询分仓需求提报数据列表
     * @param warehouseDemandReportDto
     * @return BaseTable<List < WarehouseDemandReportDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月30日 20:49
     */
    @Override
    public BaseTable<List<WarehouseDemandReportDto>> queryWarehouseDemandReportDataList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        BaseTable<List<WarehouseDemandReportDto>> baseTable = new BaseTable<>();
        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);
        // 查询数据库中的分仓需求提报数据
        List<WarehouseDemandReportDto> warehouseDemandReportDataList = warehouseDemandReportDao.queryWarehouseDemandReportList(warehouseDemandReportDto);
        if (CollectionUtils.isEmpty(warehouseDemandReportDataList))
        {
            return baseTable;
        }
        List<String> headList =
            warehouseDemandReportDataList.stream().map(WarehouseDemandReportDto::getWarehouseName).distinct().sorted(Comparator.comparing(String::valueOf))
                .collect(Collectors.toList());

        Map<String, List<WarehouseDemandReportDto>> map = warehouseDemandReportDataList.stream().collect(Collectors.groupingBy(item -> {
            return new StringBuilder().append(item.getLv3ChannelCode()).append(StringUtils.DATE_SEPARATOR).append(item.getLv3ChannelName()).toString();
        }));

        List<WarehouseDemandReportDto> dataList = new ArrayList<>(map.size());
        for (Map.Entry<String, List<WarehouseDemandReportDto>> entry : map.entrySet())
        {
            List<WarehouseDemandReportDto> warehouseDataList = entry.getValue();
            WarehouseDemandReportDto warehouseData = new WarehouseDemandReportDto();
            BeanUtils.copyProperties(warehouseDataList.get(0), warehouseData);
            warehouseData.setDataMap(new HashMap<>());
            for (WarehouseDemandReportDto item : warehouseDataList)
            {
                ChannelDemandReportDataVo data = new ChannelDemandReportDataVo();
                data.setId(item.getId());
                data.setOrderNum(item.getOrderNum());
                warehouseData.getDataMap().put(item.getWarehouseName(), data);
            }
            dataList.add(warehouseData);
        }

        dataList = dataList.stream()
            .sorted(Comparator.comparing(WarehouseDemandReportDto::getLv1CategoryCode).thenComparing(WarehouseDemandReportDto::getLv2CategoryCode)
                .thenComparing(WarehouseDemandReportDto::getLv3CategoryCode).thenComparing(WarehouseDemandReportDto::getSkuCode)
                .thenComparing(WarehouseDemandReportDto::getLv1ChannelCode).thenComparing(WarehouseDemandReportDto::getLv2ChannelCode)
                .thenComparing(WarehouseDemandReportDto::getLv3ChannelCode).thenComparing(WarehouseDemandReportDto::getWarehouseCode)).collect(
                Collectors.toList());

        baseTable.setHeadArray(headList);
        baseTable.setList(dataList);

        return baseTable;
    }

    /**
     *
     * @Description 查询分仓需求提报动态表头
     * @param warehouseDemandReportDto
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 10:44
     */
    @Override
    public List<String> queryWarehouseDemandReportHeadList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);
        List<String> bizDateValueList = warehouseDemandReportDao.queryWarehouseDemandReportHeadList(warehouseDemandReportDto);
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        List<String> headList = new ArrayList<>();
        // dataq返回动态字段格式：yyyyMMdd
        // 业务返回周粒度动态字段格式：12月W1(01-03)
        for (String bizDateValue : bizDateValueList)
        {
            DataqWeek dataqWeek = dataqWeekMap.get(bizDateValue);
            headList.add(dataqWeek.getWeekLabel());
        }
        return headList;
    }

    /**
     *
     * @Description 查询分仓需求提报表头下拉列表
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @Override
    public List<WarehouseDemandReportDto> queryWarehouseDemandReportHeadSelect(WarehouseDemandReportDto warehouseDemandReportDto)
    {
        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);
        GroupColumnEnum groupColumnEnum = warehouseDemandReportDto.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        warehouseDemandReportDto.setGroupColumn(groupColumn);
        warehouseDemandReportDto.setSortColumn(sortColumn);

        // 数据权限
        generateDataScope(warehouseDemandReportDto);

        List<WarehouseDemandReportDto> result = warehouseDemandReportDao.queryWarehouseDemandReportHeadSelect(warehouseDemandReportDto);
        return result;
    }

    /**
     *
     * @Description 封装数据权限
     * @param warehouseDemandReportDto
     * <AUTHOR>
     * @date 2024年02月01日 17:35
     */
    @Override
    public void generateDataScope(WarehouseDemandReportDto warehouseDemandReportDto)
    {
        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        if (Objects.isNull(currentAccount))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_NEEDLOGIN_ERROR);
        }
        List<String> categoryIdList = currentAccount.getCategoryIdList();
        List<String> channelIdList = currentAccount.getChannelIdList();
        List<String> warehouseIdList = currentAccount.getDepositoryIdList();
        String noDataScope = "没有权限";
        warehouseDemandReportDto.setCategoryCodes(
            CollectionUtils.isEmpty(categoryIdList) ? noDataScope : categoryIdList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
        warehouseDemandReportDto.setChannelCodes(CollectionUtils.isEmpty(channelIdList) ? noDataScope :
            channelIdList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));

        if (StringUtils.isBlank(warehouseDemandReportDto.getWarehouseCodes()))
        {
            warehouseDemandReportDto.setWarehouseCodes(
                CollectionUtils.isEmpty(warehouseIdList) ? noDataScope : warehouseIdList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
        }
        else
        {
            List<String> warehouseCodeList = Arrays.asList(StringUtils.split(warehouseDemandReportDto.getWarehouseCodes(), StringUtils.COMMA_SEPARATOR));
            warehouseIdList = (List<String>) CollectionUtils.retainAll(warehouseCodeList, warehouseIdList);
            warehouseDemandReportDto.setWarehouseCodes(
                CollectionUtils.isEmpty(warehouseIdList) ? noDataScope : warehouseIdList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
        }
    }

    /**
     *
     * @Description 查询分仓需求提报分组聚合数据
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 10:44
     */
    @Override
    public List<WarehouseDemandReportDto> queryWarehouseDemandReportListGroupBy(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);

        // 数据权限
        generateDataScope(warehouseDemandReportDto);

        // 分组查询
        List<GroupColumnEnum> groupColumnEnumList = warehouseDemandReportDto.getGroupColumnList();
        List<WarehouseDemandReportDto> dataList = new ArrayList<>();
        StringBuilder groupColumnSB = new StringBuilder();
        StringBuilder sortColumnSB = new StringBuilder();

        for (GroupColumnEnum groupColumnEnum : groupColumnEnumList)
        {
            String groupColumn = groupColumnSB.append(groupColumnEnum.getColumnName()).toString();
            String sortColumn = sortColumnSB.append(groupColumnEnum.getSortColumn().getColumnName()).toString();
            warehouseDemandReportDto.setGroupColumn(groupColumn);
            warehouseDemandReportDto.setSortColumn(sortColumn);
            List<WarehouseDemandReportDto> list =
                warehouseDemandReportDao.queryWarehouseDemandReportGroupList(warehouseDemandReportDto);
            if (CollectionUtils.isNotEmpty(list))
            {
                dataList.addAll(list);
            }

            groupColumnSB.append(StringUtils.COMMA_SEPARATOR);
            sortColumnSB.append(StringUtils.COMMA_SEPARATOR);
        }

        // 解析动态字段key，封装表头
        List<String> bizDateValueList = warehouseDemandReportDao.queryWarehouseDemandReportHeadList(warehouseDemandReportDto);
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (WarehouseDemandReportDto warehouseDemandReport : dataList)
        {
            List<ChannelDemandReportDataVo> reportDataList = JSON.parseArray(warehouseDemandReport.getData(), ChannelDemandReportDataVo.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (ChannelDemandReportDataVo channelDemandReportDataVo : reportDataList)
            {
                String key = channelDemandReportDataVo.getBizDateValue();
                String head = dataqWeekMap.get(key).getWeekLabel();
                warehouseDemandReport.getDataMap().put(head, channelDemandReportDataVo);
            }
            warehouseDemandReport.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 分页查询分仓需求提报数据
     * @param condition
     * @return PageInfo<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 10:44
     */
    @Override
    public PageInfo<WarehouseDemandReportDto> queryWarehouseDemandReportDataPage(PageCondition<WarehouseDemandReportDto> condition) throws Exception
    {
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        WarehouseDemandReportDto warehouseDemandReportDto = condition.getCondition();

        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);

        // 数据权限
        generateDataScope(warehouseDemandReportDto);

        long start0 = System.currentTimeMillis();
        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        // 分页查询，count操作耗时较大，不查询count，通过list.size()获取数量手动实例化pageInfo
        List<WarehouseDemandReportDto> keyList = warehouseDemandReportDao.queryWarehouseDemandReportDataKeyList(warehouseDemandReportDto);
        long start1 = System.currentTimeMillis();
        log.info("耗时1:{}s",(start1-start0)/1000);
        if (CollectionUtils.isEmpty(keyList))
        {
            return new PageInfo();
        }
        List<WarehouseDemandReportDto> subKeyList =
            keyList.stream().skip(Math.multiplyExact(pageNum - 1, pageSize)).limit(pageSize).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subKeyList))
        {
            return new PageInfo();
        }
        int pageTotal = keyList.size();
        warehouseDemandReportDto.setKeyList(subKeyList);

        List<WarehouseDemandReportDto> dataList = warehouseDemandReportDao.queryWarehouseDemandReportDataJsonList(warehouseDemandReportDto);
        long start2 = System.currentTimeMillis();
        log.info("耗时2:{}s",(start2-start1)/1000);
        // 解析动态字段key，封装表头
        List<String> bizDateValueList = warehouseDemandReportDao.queryWarehouseDemandReportHeadList(warehouseDemandReportDto);
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);
        long start3 = System.currentTimeMillis();
        log.info("耗时3:{}s",(start3-start2)/1000);
        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (WarehouseDemandReportDto warehouseDemandReport : dataList)
        {
            List<ChannelDemandReportDataVo> reportDataList = JSON.parseArray(warehouseDemandReport.getData(), ChannelDemandReportDataVo.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (ChannelDemandReportDataVo channelDemandReportDataVo : reportDataList)
            {
                String key = channelDemandReportDataVo.getBizDateValue();
                String head = dataqWeekMap.get(key).getWeekLabel();
                warehouseDemandReport.getDataMap().put(head, channelDemandReportDataVo);
            }
            warehouseDemandReport.setData(null);
        }

        PageInfo<WarehouseDemandReportDto> result = PageUtils.init(dataList, pageNum, pageSize, pageTotal);
        return result;
    }

    /**
     *
     * @Description 查询分仓需求提报汇总
     * @param warehouseDemandReportDto
     * @return Map<String, Double>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 10:44
     */
    @Override
    public Map<String, Double> queryWarehouseDemandReportSummary(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        String tableSuffix = StringUtils.substring(warehouseDemandReportDto.getRollingVersion(), 2, 8);
        warehouseDemandReportDto.setTableSuffix(tableSuffix);
        // 数据权限
        generateDataScope(warehouseDemandReportDto);

        List<ChannelDemandReportDataVo> dataList = warehouseDemandReportDao.queryWarehouseDemandReportSummary(warehouseDemandReportDto);
        if (CollectionUtils.isEmpty(dataList))
        {
            return null;
        }
        List<String> bizDateValueList = dataList.stream().map(ChannelDemandReportDataVo::getBizDateValue).collect(Collectors.toList());
        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, bizDateValueList);

        // 查询所有动态字段对应的日历周对象
        Map<String, DataqWeek> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(), (key1, key2) -> key1));

        Map<String, Double> result = new HashMap<>();
        for (ChannelDemandReportDataVo data : dataList)
        {
            DataqWeek dataqWeek = dataqWeekMap.get(data.getBizDateValue());
            result.put(dataqWeek.getWeekLabel(), data.getOrderNum());
        }

        return result;
    }

    /**
     *
     * @Description 修改分仓需求提报数据
     * @param dataList
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:16
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWarehouseDemandReportData(List<WarehouseDemandReportDto> dataList) throws Exception
    {
        Map<String, WarehouseDemandReportDto> map = dataList.stream().collect(Collectors.toMap(WarehouseDemandReportDto::getId, Function.identity(),
            (key1, key2) -> key1));
        List<String> ids = new ArrayList<>(map.keySet());
        List<WarehouseDemandReportDto> newDataList = warehouseDemandReportDao.queryWarehouseDemandReportDataByIds(ids);
        if (CollectionUtils.isEmpty(newDataList))
        {
            return;
        }

        Date currentTime = new Date();
        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        for (WarehouseDemandReportDto newData : newDataList)
        {
            WarehouseDemandReportDto data = map.get(newData.getId());
            if (Objects.isNull(data))
            {
                continue;
            }
            newData.setOldOrderNum(newData.getOrderNum());
            newData.setOrderNum(data.getOrderNum());
            newData.setIsModify(1);
            newData.setRemark(data.getRemark());
            newData.setLastModifier(currentAccount.getName());
            newData.setGmtModify(currentTime);
        }

        // 批量修改分仓需求提报数据
        warehouseDemandReportDao.batchUpdateWarehouseDemandReport(newDataList);

        // 新增分仓需求提报数据修改记录
        warehouseDemandReportDao.addWarehouseDemandReportHistory(newDataList);
    }

    /**
     *
     * @Description 查询分仓需求提报数据修改记录
     * @param warehouseDemandReportDto
     * @return List<WarehouseDemandReportDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月09日 17:50
     */
    @Override
    public List<WarehouseDemandReportDto> queryWarehouseDemandReportHistoryList(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        List<WarehouseDemandReportDto> result = warehouseDemandReportDao.queryWarehouseDemandReportHistoryList(warehouseDemandReportDto);

        return result;
    }

    /**
     *
     * @Description 查询分仓需求提报数据发布状态
     * @param warehouseDemandReportDto
     * @return int
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月11日 15:30
     */
    @Override
    public int queryWarehouseDemandReportPublishStatus(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        int result = dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanVersionCount(warehouseDemandReportDto);

        return result;
    }

    /**
     *
     * @Description 发布分仓需求提报数据，需要出发调用阿里接口生成分仓需求提报计划滚动版本
     * @param warehouseDemandReportDto
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月11日 15:30
     */
    @Override
    public void publishWarehouseDemandReport(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        // 20240407需求变更，渠道需求共识拆分分仓需求计划增加另一条链路，分仓需求计划不再通过分仓需求提报生成
        // 修改为在渠道需求计划共识时触发调用阿里数据探索任务生成分仓需求计划数据，状态为-2（编辑中），此处发布触发将-2的数据修改为-1
        // 此处通过发布分仓需求提报版本生成分仓需求计划的代码注释掉
        dataqWarehouseDemandPlanDao.updateWarehouseDemandPlanStatus(warehouseDemandReportDto);
        /**
         String lockKey =
         StringUtils.format(CommonConstants.REDIS_PUBLISH_WAREHOUSE_DEMAND_REPORT_LOCK_KEY, warehouseDemandReportDto.getDemandPlanCode(),
         warehouseDemandReportDto.getRollingVersion());
         boolean isLock = redisUtils.lock(lockKey, 900);
         if (!isLock)
         {
         log.info("publishWarehouseDemandReport has processing in another node.");
         throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
         }

         try
         {
         // 校验是否已经存在分仓需求提报计划
         int num = dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanVersionCount(warehouseDemandReportDto);
         if (num > 0)
         {
         throw new ServiceException("分仓需求提报计划已存在");
         }

         JSONObject jsonObject = new JSONObject();
         jsonObject.put("demandPlanCode", warehouseDemandReportDto.getDemandPlanCode());
         jsonObject.put("versionId", warehouseDemandReportDto.getRollingVersion());
         jsonObject.put("subjectType", SubjectTypeEnum.warehouse);

         // 调用阿里接口创建分仓需求提报计划滚动版本
         String addDemandPlanConfigPath = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_CONFIG_CREATE"));
         dataqService.invoke(HttpMethod.POST, addDemandPlanConfigPath, null, null, jsonObject);
         }
         catch (Exception e)
         {
         throw new ServiceException();
         }
         // 当前dataq无法在异步任务完成时回调业务侧，如果这里解锁，dataq没有生成数据，页面判断状态为未发布可以继续点击发布按钮，会产生数据重复问题。
         // 这里不做解锁操作，自然15分钟超时解锁，因为正常场景下，同个计划同个版本只会发布一次。如果发布失败，未生成分仓计划版本数据，则15分钟后自然解锁可以点击按钮。
         //        finally
         //        {
         //            redisUtils.unlock(lockKey);
         //        }
         **/
    }

    /**
     *
     * @Description 删除分仓需求提报数据
     * @param demandPlanCode
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月12日 11:14
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void deleteWarehouseDemandReport(String demandPlanCode) throws Exception
    {
        warehouseDemandReportDao.deleteWarehouseDemandReportVersion(demandPlanCode);
        warehouseDemandReportDao.deleteWarehouseDemandReport(demandPlanCode);
    }


    /**
     *
     * @Description 查询渠道需求计划共识版汇总数据
     * @param warehouseDemandReportDto
     * @return List<PlanValue>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月18日 15:34
     */
    @Override
    public List<PlanValue> queryChannelDemandPlanConfirmSum(WarehouseDemandReportDto warehouseDemandReportDto) throws Exception
    {
        String planDate = null;
        if (StringUtils.isNotBlank(warehouseDemandReportDto.getBizDateValue()))
        {
            planDate = DateUtils.formatTime(DateUtils.parseDate(warehouseDemandReportDto.getBizDateValue(), DateUtils.YMD), DateUtils.YMD_DASH);
        }

        QueryChannelDemandPlanVersionListReqVo queryChannelDemandPlanVersionListReqVo = new QueryChannelDemandPlanVersionListReqVo();
        queryChannelDemandPlanVersionListReqVo.setDemandPlanCode(warehouseDemandReportDto.getDemandPlanCode());
        queryChannelDemandPlanVersionListReqVo.setVersionId(warehouseDemandReportDto.getRollingVersion());
        queryChannelDemandPlanVersionListReqVo.setSkuCode(warehouseDemandReportDto.getSkuCode());
        queryChannelDemandPlanVersionListReqVo.setSkuCodes(warehouseDemandReportDto.getSkuCodes());
        queryChannelDemandPlanVersionListReqVo.setLv1CategoryCode(warehouseDemandReportDto.getLv1CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv1CategoryCodes(warehouseDemandReportDto.getLv1CategoryCodes());
        queryChannelDemandPlanVersionListReqVo.setLv2CategoryCode(warehouseDemandReportDto.getLv2CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv2CategoryCodes(warehouseDemandReportDto.getLv2CategoryCodes());
        queryChannelDemandPlanVersionListReqVo.setLv3CategoryCode(warehouseDemandReportDto.getLv3CategoryCode());
        queryChannelDemandPlanVersionListReqVo.setLv3CategoryCodes(warehouseDemandReportDto.getLv3CategoryCodes());
        queryChannelDemandPlanVersionListReqVo.setLv1ChannelCode(warehouseDemandReportDto.getLv1ChannelCode());
        queryChannelDemandPlanVersionListReqVo.setLv1ChannelCodes(warehouseDemandReportDto.getLv1ChannelCodes());
        queryChannelDemandPlanVersionListReqVo.setLv2ChannelCode(warehouseDemandReportDto.getLv2ChannelCode());
        queryChannelDemandPlanVersionListReqVo.setLv2ChannelCodes(warehouseDemandReportDto.getLv2ChannelCodes());
        queryChannelDemandPlanVersionListReqVo.setLv3ChannelCode(warehouseDemandReportDto.getLv3ChannelCode());
        queryChannelDemandPlanVersionListReqVo.setLv3ChannelCodes(warehouseDemandReportDto.getLv3ChannelCodes());
        queryChannelDemandPlanVersionListReqVo.setBeginDate(planDate);
        queryChannelDemandPlanVersionListReqVo.setEndDate(planDate);
        queryChannelDemandPlanVersionListReqVo.setIsModify(0);
        queryChannelDemandPlanVersionListReqVo.setDeleted(0);

        List<PlanValue> result = dataqChannelDemandPlanDao.queryChannelDemandPlanSummary(queryChannelDemandPlanVersionListReqVo);

        return result;
    }
}
