package cn.aliyun.ryytn.modules.demand.handler;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqWarehouseDemandPlanDao;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryWarehouseDemandPlanVersionListRspVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 导出分仓需求计划数据列表
 * <AUTHOR>
 * @date 2023/11/17 10:28
 */
@Slf4j
@Component("exportWarehouseDemandPlanDataList")
public class ExportWarehouseDemandPlanDataListHandler extends AbstractExportExcelHandler
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private DataqWarehouseDemandPlanDao dataqWarehouseDemandPlanDao;

    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        // 解析参数
        QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo =
            condition.getCondition().toJavaObject(QueryWarehouseDemandPlanVersionListReqVo.class);

        // 查询dataq接口渠道需求计划版本列表
        queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
        queryWarehouseDemandPlanVersionListReqVo.setDeleted(0);

        List<QueryWarehouseDemandPlanVersionListRspVo> dataList =
            dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanList(queryWarehouseDemandPlanVersionListReqVo);
//        int pageNum = 1;
//        int pageSize = 5000;
//        int skip = pageSize;
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("page_token", pageNum);
//        param.put("page_size", pageSize);
//        param.put("fetch_all", true);
//        param.put("order_by", "warehouse_code,lv1_category_code,lv2_category_code,lv3_category_code,sku_code,plan_date");
//
//        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST"));
//        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryWarehouseDemandPlanVersionListReqVo);
//        int total = dataqResult.getTotal();
//        List<QueryWarehouseDemandPlanVersionListRspVo> dataList = new ArrayList<>(total);
//        JSONArray jsonArray = (JSONArray) dataqResult.getData();
//        if (CollectionUtils.isNotEmpty(jsonArray))
//        {
//            dataList.addAll(jsonArray.toJavaList(QueryWarehouseDemandPlanVersionListRspVo.class));
//        }
//        while (total > skip)
//        {
//            try
//            {
//                param.put("page_token", pageNum + 1);
//                dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, queryWarehouseDemandPlanVersionListReqVo);
//                jsonArray = (JSONArray) dataqResult.getData();
//                dataList.addAll(jsonArray.toJavaList(QueryWarehouseDemandPlanVersionListRspVo.class));
//                skip = skip + pageSize;
//            }
//            catch (Exception e)
//            {
//                log.error("exportChannelDemandPlanDataList queryChannelDemandPlanVersionList error.pagenum:{},pageSize:{}", pageNum, pageSize);
//            }
//        }

        Set<String> planDateSet = dataList.stream().map(item -> {
            return item.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
        }).collect(Collectors.toSet());

        // 获取指定周的周对象
        List<Object> dataqWeekList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, planDateSet);
        Map<String, DataqWeek> dataqWeekMap = dataqWeekList.stream().map(item -> {
            return (DataqWeek) item;
        }).collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
            Function.identity(),
            (key1, key2) -> key1));

        int rowId = 1;
        for (QueryWarehouseDemandPlanVersionListRspVo data : dataList)
        {
            String key = data.getPlanDate().replace(StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
            DataqWeek dataqWeek = dataqWeekMap.get(key);
            data.setRowId(rowId++);
            data.setWareName(data.getSkuName());
            data.setMonth(dataqWeek.getMonthLabel());
            data.setWeek(StringUtils.WEEK_PREFIX_UPPER + dataqWeek.getWeekOfFsclMonth());
            if (Objects.isNull(data.getPlanValue()))
            {
                data.setPlanValue(0d);
            }
        }

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream, QueryWarehouseDemandPlanVersionListRspVo.class).excelType(ExcelTypeEnum.XLSX).sheet("分仓需求计划数据")
                .doWrite(dataList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }
}
