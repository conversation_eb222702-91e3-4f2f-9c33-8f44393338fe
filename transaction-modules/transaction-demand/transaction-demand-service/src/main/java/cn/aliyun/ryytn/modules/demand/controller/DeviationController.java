package cn.aliyun.ryytn.modules.demand.controller;

import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.DeviationService;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationDetailReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationDetailRsqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryDeviationListRspVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 偏差对比接口
 * <AUTHOR>
 * @date 2023/11/02 10:10
 */
@Slf4j
@RestController
@RequestMapping("/api/demand/deviation")
@Api(tags = "偏差对比接口")
public class DeviationController
{
    @Autowired
    private DeviationService deviationService;

    /**
     *
     * @Description 查询偏差比对-列表数据
     * @param deviationChannelListReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月02日 10:23
     */
    @PostMapping("queryDeviationList")
    @ResponseBody
    @ApiOperation("查询偏差比对-列表数据")
//    @RequiresPermissions(value = {})
    public ResultInfo<BaseTable<List<QueryDeviationListRspVo>>> queryDeviationList(@RequestBody QueryDeviationListReqVo deviationChannelListReqVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(deviationChannelListReqVo);

        BaseTable<List<QueryDeviationListRspVo>> result = deviationService.queryDeviationList(deviationChannelListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询偏差比对-下钻详情数据
     * @param deviationDetailReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月02日 10:23
     */
    @PostMapping("queryDeviationDetail")
    @ResponseBody
    @ApiOperation("查询偏差比对-下钻详情数据")
//    @RequiresPermissions(value = {})
    public ResultInfo<BaseTable<List<QueryDeviationDetailRsqVo>>> queryDeviationDetail(@RequestBody QueryDeviationDetailReqVo deviationDetailReqVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(deviationDetailReqVo);
        ValidateUtil.checkIsNotEmpty(deviationDetailReqVo.getType());

        return ResultInfo.success(deviationService.queryDeviationDetail(deviationDetailReqVo));
    }

    /**
     *
     * @Description 查询偏差比对-版本下拉数据
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月02日 10:23
     */
    @PostMapping("getVersion")
    @ResponseBody
    @ApiOperation("查询偏差比对-版本下拉数据")
    public ResultInfo<?> getVersion() throws Exception
    {

        return ResultInfo.success(deviationService.getVersion());
    }


}
