package cn.aliyun.ryytn.modules.demand.task;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.modules.demand.dao.FreightVersionDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 *
 * @Description 定时刷新调拨计划版本数据
 * <AUTHOR>
 * @date 2024/9/26 17:06
 */
@Slf4j
@Service
public class RereshAiFreightPlanVersionServiceImpl implements TaskService
{

    @Autowired
    private FreightVersionDao freightVersionDao;
    @Override
    @Transactional
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        log.info(" RereshAiFreightPlanVersionServiceImpl data start===========");
        freightVersionDao.deleteTempTable();
        freightVersionDao.insertTempTable();
        freightVersionDao.deleteVersionTable();
        freightVersionDao.insertVersionTable();
    }

}
