<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.scheduler.dao.SchedulerDao">
	<select id="queryAllSchedulerJob" resultType="cn.aliyun.ryytn.common.entity.SchedulerJob">
		SELECT 
		  job_id,
		  job_name,
		  job_type,
		  start_date,
		  end_date,
		  job_conf,
		  class_name,
		  param,
		  service_id,
		  misfire_policy,
		  concurrent,
		  status,
		  description,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_job 
	   WHERE status &lt;&gt; 3
	</select>
	<select id="querySchedulerJobList" parameterType="cn.aliyun.ryytn.common.entity.SchedulerJob" resultType="cn.aliyun.ryytn.common.entity.SchedulerJob">
		SELECT 
		  job_id,
		  job_name,
		  job_type,
		  start_date,
		  end_date,
		  job_conf,
		  class_name,
		  param,
		  service_id,
		  misfire_policy,
		  concurrent,
		  status,
		  description,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_job 
	   WHERE 1=1
	   <if test="jobName!=null and jobName!=''">
	     AND strpos(job_name,#{jobName}) &gt; 0
	   </if>
	   <if test="status!=null and status!=''">
	     AND status = #{status}
	   </if>
	</select>
	<select id="querySchedulerJobDetial" parameterType="java.lang.String" resultType="cn.aliyun.ryytn.common.entity.SchedulerJob">
		SELECT 
		  job_id,
		  job_name,
		  job_type,
		  start_date,
		  end_date,
		  job_conf,
		  class_name,
		  param,
		  service_id,
		  misfire_policy,
		  concurrent,
		  status,
		  description,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_job 
	   WHERE job_id = #{jobId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>
	<delete id="deleteSchedulerJob" parameterType="java.lang.String">
		DELETE 
		FROM
		  t_ryytn_job 
		WHERE job_id =#{jobId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>
	<update id="updateSchedulerJobStatus" parameterType="cn.aliyun.ryytn.common.entity.SchedulerJob">
		UPDATE 
		  t_ryytn_job 
		SET
		  status = #{status},
		  updated_by = #{updatedBy},
		  updated_time = now()
		WHERE job_id = #{jobId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>
	<insert id="addSchedulerJob" parameterType="cn.aliyun.ryytn.common.entity.SchedulerJob">
		INSERT INTO t_ryytn_job (
		  job_id,
		  job_name,
		  job_type,
		  start_date,
		  end_date,
		  job_conf,
		  class_name,
		  param,
		  service_id,
		  misfire_policy,
		  concurrent,
		  status,
		  description,
		  created_by,
		  created_time
		) 
		VALUES
		  (
		    #{jobId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
		    #{jobName},
		    #{jobType},
		    #{startDate},
			#{endDate},
		    #{jobConf},
			#{className},
			#{param},
		    #{serviceId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
		    #{misfirePolicy},
		    #{concurrent,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler},
		    #{status},
		    #{description},
		    #{createdBy},
		    now()
		  ) 
	</insert>

	<select id="querySchedulerJobByServiceId" parameterType="java.lang.String" resultType="cn.aliyun.ryytn.common.entity.SchedulerJob">
		select job_id,
		  job_name,
		  job_type,
		  start_date,
		  end_date,
		  job_conf,
		  class_name,
		  param,
		  service_id,
		  misfire_policy,
		  concurrent,
		  status,
		  description,
		  created_by,
		  created_time
		from t_ryytn_job
		where service_id = #{serviceId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>
</mapper>