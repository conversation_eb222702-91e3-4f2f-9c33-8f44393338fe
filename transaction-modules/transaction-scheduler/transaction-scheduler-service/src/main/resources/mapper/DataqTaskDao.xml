<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.scheduler.dao.DataqTaskDao">
    <insert id="addDataqTask" parameterType="cn.aliyun.ryytn.common.entity.DataqTask">
        insert into t_ryytn_dataq_task
        (
            task_id,
            job_id,
            app_code,
            param,
            lock_key,
            status,
            reload_id,
            start_time,
            end_time
            )
        values
        (
            #{taskId},
            #{jobId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{appCode},
            #{param},
            #{lockKey},
            #{status},
            #{reloadId},
            #{startTime},
            #{endTime}
        )
    </insert>

    <select id="queryDataqTaskListNotCompleted" parameterType="java.util.List" resultType="cn.aliyun.ryytn.common.entity.DataqTask">
        select
            id,
            task_id,
            job_id,
            app_code,
            param,
            lock_key,
            status,
            reload_id,
            start_time,
            end_time
        from
            t_ryytn_dataq_task
        where 1=1
        and status in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryDataqTaskListNeedReload" parameterType="java.util.List" resultType="cn.aliyun.ryytn.common.entity.DataqTask">
        select
            id,
            task_id,
            job_id,
            app_code,
            param,
            lock_key,
            status,
            reload_id,
            start_time,
            end_time
        from
            t_ryytn_dataq_task
        where 1=1
        <!-- 只查询补偿次数小于等于5次的数据 -->
        and reload_id &lt;= 5
          and status in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateDataqTask" parameterType="cn.aliyun.ryytn.common.entity.DataqTask">
        update
            t_ryytn_dataq_task
        set
            task_id = #{taskId},
            status = #{status},
            end_time = #{endTime},
            reload_id = #{reloadId}
        where
            id = #{id}
    </update>

    <delete id="deleteHistoryDataqTask" parameterType="java.util.Date">
        delete
        from
            t_ryytn_dataq_task
        where end_time &lt; #{expiredTime}
          and ((status in ('SKIP','STOPPED','FAILED') and reload_id is not null) or status = 'SUCCESS')
    </delete>
</mapper>