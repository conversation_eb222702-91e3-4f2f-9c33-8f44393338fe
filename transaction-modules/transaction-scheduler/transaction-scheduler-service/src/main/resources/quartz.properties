# Default Properties file for use by StdSchedulerFactory
# to create a Quartz Scheduler Instance, if a different
# properties file is not explicitly specified.
#
# ===========================================================================
# Configure Main Scheduler Properties \u8C03\u5EA6\u5668\u5C5E\u6027
# ===========================================================================
org.quartz.scheduler.instanceName:DefaultQuartzScheduler
org.quartz.scheduler.instanceid:AUTO
org.quartz.scheduler.rmi.export:false
org.quartz.scheduler.rmi.proxy:false
org.quartz.scheduler.wrapJobExecutionInUserTransaction:false
# ===========================================================================
# Configure ThreadPool \u7EBF\u7A0B\u6C60\u5C5E\u6027
# ===========================================================================
#\u7EBF\u7A0B\u6C60\u7684\u5B9E\u73B0\u7C7B\uFF08\u4E00\u822C\u4F7F\u7528SimpleThreadPool\u5373\u53EF\u6EE1\u8DB3\u51E0\u4E4E\u6240\u6709\u7528\u6237\u7684\u9700\u6C42\uFF09
org.quartz.threadPool.class:org.quartz.simpl.SimpleThreadPool
#\u6307\u5B9A\u7EBF\u7A0B\u6570\uFF0C\u81F3\u5C11\u4E3A1\uFF08\u65E0\u9ED8\u8BA4\u503C\uFF09(\u4E00\u822C\u8BBE\u7F6E\u4E3A1-100\u76F4\u63A5\u7684\u6574\u6570\u5408\u9002)
org.quartz.threadPool.threadCount:10
#\u8BBE\u7F6E\u7EBF\u7A0B\u7684\u4F18\u5148\u7EA7\uFF08\u6700\u5927\u4E3Ajava.lang.Thread.MAX_PRIORITY 10\uFF0C\u6700\u5C0F\u4E3AThread.MIN_PRIORITY 1\uFF0C\u9ED8\u8BA4\u4E3A5\uFF09
org.quartz.threadPool.threadPriority:5
#\u8BBE\u7F6ESimpleThreadPool\u7684\u4E00\u4E9B\u5C5E\u6027
#\u8BBE\u7F6E\u662F\u5426\u4E3A\u5B88\u62A4\u7EBF\u7A0B
#org.quartz.threadpool.makethreadsdaemons = false
#org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread: true
#org.quartz.threadpool.threadsinheritgroupofinitializingthread=false
#\u7EBF\u7A0B\u524D\u7F00\u9ED8\u8BA4\u503C\u662F\uFF1A[Scheduler Name]_Worker
#org.quartz.threadpool.threadnameprefix=swhJobThead;
# \u914D\u7F6E\u5168\u5C40\u76D1\u542C(TriggerListener,JobListener) \u5219\u5E94\u7528\u7A0B\u5E8F\u53EF\u4EE5\u63A5\u6536\u548C\u6267\u884C \u9884\u5B9A\u7684\u4E8B\u4EF6\u901A\u77E5
# ===========================================================================
# Configuring a Global TriggerListener \u914D\u7F6E\u5168\u5C40\u7684Trigger\u76D1\u542C\u5668
# MyTriggerListenerClass \u7C7B\u5FC5\u987B\u6709\u4E00\u4E2A\u65E0\u53C2\u6570\u7684\u6784\u9020\u51FD\u6570\uFF0C\u548C \u5C5E\u6027\u7684set\u65B9\u6CD5\uFF0C\u76EE\u524D2.2.x\u53EA\u652F\u6301\u539F\u59CB\u6570\u636E\u7C7B\u578B\u7684\u503C\uFF08\u5305\u62EC\u5B57\u7B26\u4E32\uFF09
# ===========================================================================
#org.quartz.triggerListener.NAME.class = com.swh.MyTriggerListenerClass
#org.quartz.triggerListener.NAME.propName = propValue
#org.quartz.triggerListener.NAME.prop2Name = prop2Value
# ===========================================================================
# Configuring a Global JobListener \u914D\u7F6E\u5168\u5C40\u7684Job\u76D1\u542C\u5668
# MyJobListenerClass \u7C7B\u5FC5\u987B\u6709\u4E00\u4E2A\u65E0\u53C2\u6570\u7684\u6784\u9020\u51FD\u6570\uFF0C\u548C \u5C5E\u6027\u7684set\u65B9\u6CD5\uFF0C\u76EE\u524D2.2.x\u53EA\u652F\u6301\u539F\u59CB\u6570\u636E\u7C7B\u578B\u7684\u503C\uFF08\u5305\u62EC\u5B57\u7B26\u4E32\uFF09
# ===========================================================================
#org.quartz.jobListener.NAME.class = com.swh.MyJobListenerClass
#org.quartz.jobListener.NAME.propName = propValue
#org.quartz.jobListener.NAME.prop2Name = prop2Value
# ===========================================================================
# Configure JobStore \u5B58\u50A8\u8C03\u5EA6\u4FE1\u606F\uFF08\u5DE5\u4F5C\uFF0C\u89E6\u53D1\u5668\u548C\u65E5\u5386\u7B49\uFF09
# ===========================================================================
# \u4FE1\u606F\u4FDD\u5B58\u65F6\u95F4 \u9ED8\u8BA4\u503C60\u79D2
org.quartz.jobStore.misfireThreshold:60000
#\u4FDD\u5B58job\u548CTrigger\u7684\u72B6\u6001\u4FE1\u606F\u5230\u5185\u5B58\u4E2D\u7684\u7C7B
org.quartz.jobStore.class:org.quartz.simpl.RAMJobStore
#\u6570\u636E\u5E93\u8FDE\u63A5\u9A71\u52A8
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
org.quartz.jobStore.txIsolationLevelSerializable=false
# ===========================================================================
# Configure SchedulerPlugins \u63D2\u4EF6\u5C5E\u6027 \u914D\u7F6E
# ===========================================================================
# \u81EA\u5B9A\u4E49\u63D2\u4EF6
#org.quartz.plugin.NAME.class = com.swh.MyPluginClass
#org.quartz.plugin.NAME.propName = propValue
#org.quartz.plugin.NAME.prop2Name = prop2Value
#\u914D\u7F6Etrigger\u6267\u884C\u5386\u53F2\u65E5\u5FD7\uFF08\u53EF\u4EE5\u770B\u5230\u7C7B\u7684\u6587\u6863\u548C\u53C2\u6570\u5217\u8868\uFF09
org.quartz.plugin.triggHistory.class=org.quartz.plugins.history.LoggingTriggerHistoryPlugin  
org.quartz.plugin.triggHistory.triggerFiredMessage=Trigger {1}.{0} fired job {6}.{5} at\: {4, date, HH\:mm\:ss MM/dd/yyyy}  
org.quartz.plugin.triggHistory.triggerCompleteMessage=Trigger {1}.{0} completed firing job {6}.{5} at {4, date, HH\:mm\:ss MM/dd/yyyy} with resulting trigger instruction code\: {9}  
#\u914D\u7F6Ejob\u8C03\u5EA6\u63D2\u4EF6  quartz_jobs(jobs and triggers\u5185\u5BB9)\u7684XML\u6587\u6863
#\u52A0\u8F7D Job \u548C Trigger \u4FE1\u606F\u7684\u7C7B   \uFF081.8\u4E4B\u524D\u7528\uFF1Aorg.quartz.plugins.xml.JobInitializationPlugin\uFF09
org.quartz.plugin.jobInitializer.class=org.quartz.plugins.xml.XMLSchedulingDataProcessorPlugin
#\u6307\u5B9A\u5B58\u653E\u8C03\u5EA6\u5668(Job \u548C Trigger)\u4FE1\u606F\u7684xml\u6587\u4EF6\uFF0C\u9ED8\u8BA4\u662Fclasspath\u4E0Bquartz_jobs.xml
org.quartz.plugin.jobInitializer.fileNames=my_quartz_job2.xml  
#org.quartz.plugin.jobInitializer.overWriteExistingJobs = false
org.quartz.plugin.jobInitializer.failOnFileNotFound=true  
#\u81EA\u52A8\u626B\u63CF\u4EFB\u52A1\u5355\u5E76\u53D1\u73B0\u6539\u52A8\u7684\u65F6\u95F4\u95F4\u9694,\u5355\u4F4D\u4E3A\u79D2
org.quartz.plugin.jobInitializer.scanInterval=10
#\u8986\u76D6\u4EFB\u52A1\u8C03\u5EA6\u5668\u4E2D\u540C\u540D\u7684jobDetail,\u907F\u514D\u53EA\u4FEE\u6539\u4E86CronExpression\u6240\u9020\u6210\u7684\u4E0D\u80FD\u91CD\u65B0\u751F\u6548\u60C5\u51B5
org.quartz.plugin.jobInitializer.wrapInUserTransaction=false
# ===========================================================================
# Sample configuration of ShutdownHookPlugin  ShutdownHookPlugin\u63D2\u4EF6\u7684\u914D\u7F6E\u6837\u4F8B\u2039
# ===========================================================================
#org.quartz.plugin.shutdownhook.class = \org.quartz.plugins.management.ShutdownHookPlugin
#org.quartz.plugin.shutdownhook.cleanShutdown = true
#
# Configure RMI Settings \u8FDC\u7A0B\u670D\u52A1\u8C03\u7528\u914D\u7F6E
#
#\u5982\u679C\u4F60\u60F3quartz-scheduler\u51FA\u53E3\u672C\u8EAB\u901A\u8FC7RMI\u4F5C\u4E3A\u670D\u52A1\u5668\uFF0C\u7136\u540E\u8BBE\u7F6E\u201C\u51FA\u53E3\u201D\u6807\u5FD7true(\u9ED8\u8BA4\u503C\u4E3Afalse)\u3002
#org.quartz.scheduler.rmi.export = false
#\u4E3B\u673A\u4E0Armi\u6CE8\u518C\u8868(\u9ED8\u8BA4\u503Clocalhost)
#org.quartz.scheduler.rmi.registryhost = localhost
#\u6CE8\u518C\u76D1\u542C\u7AEF\u53E3\u53F7\uFF08\u9ED8\u8BA4\u503C1099\uFF09
#org.quartz.scheduler.rmi.registryport = 1099
#\u521B\u5EFArmi\u6CE8\u518C\uFF0Cfalse/never\uFF1A\u5982\u679C\u4F60\u5DF2\u7ECF\u6709\u4E00\u4E2A\u5728\u8FD0\u884C\u6216\u4E0D\u60F3\u8FDB\u884C\u521B\u5EFA\u6CE8\u518C
# true/as_needed:\u7B2C\u4E00\u6B21\u5C1D\u8BD5\u4F7F\u7528\u73B0\u6709\u7684\u6CE8\u518C\uFF0C\u7136\u540E\u518D\u56DE\u6765\u8FDB\u884C\u521B\u5EFA
# always:\u5148\u8FDB\u884C\u521B\u5EFA\u4E00\u4E2A\u6CE8\u518C\uFF0C\u7136\u540E\u518D\u4F7F\u7528\u56DE\u6765\u4F7F\u7528\u6CE8\u518C
#org.quartz.scheduler.rmi.createregistry = never
#Quartz Scheduler\u670D\u52A1\u7AEF\u7AEF\u53E3\uFF0C\u9ED8\u8BA4\u662F\u968F\u673A\u5206\u914DRMI\u6CE8\u518C\u8868
#org.quartz.scheduler.rmi.serverport = 1098
#true:\u94FE\u63A5\u8FDC\u7A0B\u670D\u52A1\u8C03\u5EA6(\u5BA2\u6237\u7AEF),\u8FD9\u4E2A\u4E5F\u8981\u6307\u5B9Aregistryhost\u548Cregistryport\uFF0C\u9ED8\u8BA4\u4E3Afalse
# \u5982\u679Cexport\u548Cproxy\u540C\u65F6\u6307\u5B9A\u4E3Atrue\uFF0C\u5219export\u7684\u8BBE\u7F6E\u5C06\u88AB\u5FFD\u7565
#org.quartz.scheduler.rmi.proxy = false
