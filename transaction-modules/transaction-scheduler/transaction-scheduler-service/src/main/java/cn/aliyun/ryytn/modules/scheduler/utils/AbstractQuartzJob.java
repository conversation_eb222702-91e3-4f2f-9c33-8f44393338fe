package cn.aliyun.ryytn.modules.scheduler.utils;

import com.yomahub.tlog.core.rpc.TLogLabelBean;
import com.yomahub.tlog.core.rpc.TLogRPCHandler;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import cn.aliyun.ryytn.common.constants.SchedulerConstant;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import lombok.extern.slf4j.Slf4j;

/**
 * 抽象quartz调用
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractQuartzJob implements Job
{
    private TLogRPCHandler tLogRPCHandler = new TLogRPCHandler();
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException
    {
        SchedulerJob schedulerJob = new SchedulerJob();
        BeanUtils.copyBeanProp(schedulerJob, context.getMergedJobDataMap().get(SchedulerConstant.TASK_PROPERTIES));
        try
        {
            this.tLogRPCHandler.processProviderSide(new TLogLabelBean());
            before(context, schedulerJob);
            if (schedulerJob != null)
            {
                doExecute(context, schedulerJob);
            }
            after(context, schedulerJob, null);
        }
        catch (Exception e)
        {
            log.error("execute has exception:", e);
            after(context, schedulerJob, e);
        }finally {
            this.tLogRPCHandler.cleanThreadLocal();
        }
    }

    /**
     * 执行前
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     */
    protected abstract void before(JobExecutionContext context, SchedulerJob schedulerJob);

    /**
     * 执行后
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     */
    protected abstract void after(JobExecutionContext context, SchedulerJob schedulerJob, Exception e);

    /**
     * 执行方法，由子类重载
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     * @throws Exception 执行过程中的异常
     */
    protected abstract void doExecute(JobExecutionContext context, SchedulerJob schedulerJob) throws Exception;
}
