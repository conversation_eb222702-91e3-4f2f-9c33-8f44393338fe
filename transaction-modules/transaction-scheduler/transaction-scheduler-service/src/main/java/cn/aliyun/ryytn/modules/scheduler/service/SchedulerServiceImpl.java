package cn.aliyun.ryytn.modules.scheduler.service;

import java.util.List;

import org.quartz.JobDataMap;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.constants.SchedulerConstant;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.scheduler.api.SchedulerService;
import cn.aliyun.ryytn.modules.scheduler.dao.SchedulerDao;
import cn.aliyun.ryytn.modules.scheduler.utils.CronUtils;
import cn.aliyun.ryytn.modules.scheduler.utils.SchedulerUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 定时任务调度信息 服务层
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SchedulerServiceImpl implements SchedulerService, ApplicationListener<ContextRefreshedEvent>
{
    @Autowired
    private Scheduler scheduler;

    @Autowired
    private SchedulerDao schedulerDao;

    /**
     *
     * @Description 查询调度任务列表
     * @param schedulerJob
     * @return PageInfo<SchedulerJob>
     * @throws Exception
     * <AUTHOR>
     * @date 2022年5月24日 下午7:49:11
     */
    @Override
    public PageInfo<SchedulerJob> querySchedulerJobList(PageCondition<SchedulerJob> condition)
    {
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());

        List<SchedulerJob> schedulerJobList = schedulerDao.querySchedulerJobList(condition.getCondition());

        return new PageInfo<SchedulerJob>(schedulerJobList);
    }

    /**
     * 通过调度任务ID查询调度信息
     *
     * @param jobId 调度任务ID
     * @return 调度任务对象信息
     */
    @Override
    public SchedulerJob querySchedulerJobDetail(String jobId)
    {
        return schedulerDao.querySchedulerJobDetial(jobId);
    }

    /**
     * 暂停任务
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pauseSchedulerJob(String jobId) throws Exception
    {
        SchedulerJob schedulerJob = schedulerDao.querySchedulerJobDetial(jobId);
        if (null == schedulerJob)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }
        schedulerJob.setStatus(SchedulerConstant.StatusEnum.PAUSE.getValue());
        schedulerJob.setUpdatedBy("admin");
        schedulerJob.setUpdatedTime(DateUtils.getDate(DateUtils.YMDHMS_STD));
        schedulerDao.updateSchedulerJobStatus(schedulerJob);

        // 判断是否存在
        JobKey jobKey = SchedulerUtils.getJobKey(jobId, SchedulerConstant.JOB_GROUP_NAME);
        if (scheduler.checkExists(jobKey))
        {
            scheduler.pauseJob(jobKey);
        }
    }

    /**
     * 恢复任务
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resumeSchedulerJob(String jobId) throws Exception
    {
        SchedulerJob schedulerJob = schedulerDao.querySchedulerJobDetial(jobId);
        if (null == schedulerJob)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }
        schedulerJob.setStatus(SchedulerConstant.StatusEnum.NORMAL.getValue());
        schedulerDao.updateSchedulerJobStatus(schedulerJob);

        // 判断是否存在
        JobKey jobKey = SchedulerUtils.getJobKey(jobId, SchedulerConstant.JOB_GROUP_NAME);
        if (scheduler.checkExists(jobKey))
        {
            scheduler.resumeJob(jobKey);
        }
    }

    /**
     * 删除任务后，所对应的trigger也将被删除
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSchedulerJob(String jobId) throws Exception
    {
        SchedulerJob schedulerJob = schedulerDao.querySchedulerJobDetial(jobId);
        if (null == schedulerJob)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }
        schedulerDao.deleteSchedulerJob(jobId);
        // 判断是否存在
        JobKey jobKey = SchedulerUtils.getJobKey(jobId, SchedulerConstant.JOB_GROUP_NAME);
        if (scheduler.checkExists(jobKey))
        {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
    }

    /**
     * 立即运行任务
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void triggerSchedulerJob(String jobId) throws Exception
    {
        SchedulerJob schedulerJob = schedulerDao.querySchedulerJobDetial(jobId);
        if (!SchedulerConstant.StatusEnum.NORMAL.getValue().equals(schedulerJob.getStatus()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }
        // 参数
        JobDataMap dataMap = new JobDataMap();
        dataMap.put(SchedulerConstant.TASK_PROPERTIES, schedulerJob);
        scheduler.triggerJob(SchedulerUtils.getJobKey(jobId, SchedulerConstant.JOB_GROUP_NAME), dataMap);
    }

    /**
     * 新增任务
     *
     * @param job 调度信息 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSchedulerJob(SchedulerJob schedulerJob) throws Exception
    {
        if (StringUtils.isEmpty(schedulerJob.getJobId()))
        {
            schedulerJob.setJobId(SeqUtils.getSequenceUid());
        }
        schedulerJob.setStatus(SchedulerConstant.StatusEnum.NORMAL.getValue());

        // jobId主键约束，用于控制分布式部署调度服务时消费MQ幂等性
        schedulerDao.addSchedulerJob(schedulerJob);
        // 判断是否存在
        JobKey jobKey = SchedulerUtils.getJobKey(schedulerJob.getJobId(), SchedulerConstant.JOB_GROUP_NAME);
        if (scheduler.checkExists(jobKey))
        {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
        SchedulerUtils.createScheduleJob(scheduler, schedulerJob);
    }

    /**
     * 更新任务
     *
     * @param job 任务对象
     * @param jobGroup 任务组名
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSchedulerJob(SchedulerJob schedulerJob) throws Exception
    {
        // 先删除，在新增，否则无法支持修改任务类型场景，因为修改为一次性实时任务时，是不记录edu_common_job表的
        schedulerDao.deleteSchedulerJob(schedulerJob.getJobId());

        schedulerDao.addSchedulerJob(schedulerJob);

        String jobId = schedulerJob.getJobId();
        // 判断是否存在
        JobKey jobKey = SchedulerUtils.getJobKey(jobId, SchedulerConstant.JOB_GROUP_NAME);
        if (scheduler.checkExists(jobKey))
        {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
        SchedulerUtils.createScheduleJob(scheduler, schedulerJob);
    }

    /**
     * 校验cron表达式是否有效
     *
     * @param cronExpression 表达式
     * @return 结果
     */
    @Override
    public boolean checkCronExpressionIsValid(String cronExpression)
    {
        return CronUtils.isValid(cronExpression);
    }


    /**
     *
     * @Description 启动加载数据库调度任务，并启动调度
     * @throws SchedulerException
     * @throws TaskException
     * <AUTHOR>
     * @date 2023年11月24日 下午7:49:49
     */
    public void init() throws Exception
    {
        try
        {
            // 加载数据库调度任务
            // scheduler.clear();
            List<SchedulerJob> schedulerJobList = schedulerDao.queryAllSchedulerJob();
            for (SchedulerJob schedulerJob : schedulerJobList)
            {
                SchedulerUtils.createScheduleJob(scheduler, schedulerJob);
            }
        }
        catch (Exception e)
        {
            log.error("Scheduler init has exception:", e);
            throw e;
        }
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event)
    {
        try
        {
            init();
        }
        catch (Exception e)
        {
            log.error("onApplicationEvent init scheduler job has exceptoin:", e);
        }
    }
}