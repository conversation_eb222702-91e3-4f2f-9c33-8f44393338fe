package cn.aliyun.ryytn.modules.scheduler.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.scheduler.api.SchedulerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 *
 * @Description 调度任务管理控制层
 * <AUTHOR>
 * @date 2022年5月24日 下午7:58:13
 */
@RestController
@RequestMapping("/api/scheduler")
@Api(tags = "调度任务管理")
public class SchedulerController
{
    @Autowired
    private SchedulerService schedulerService;

    /**
     *
     * @Description 查询调度任务列表
     * @param schedulerJob
     * @return PageInfo<SchedulerJob>
     * @throws TaskException
     * <AUTHOR>
     * @date 2022年5月24日 下午7:49:11
     */
    @PostMapping("/querySchedulerJobPage")
    @ResponseBody
    @ApiOperation("分页查询调度任务列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<SchedulerJob>> querySchedulerJobList(@RequestBody PageCondition<SchedulerJob> condition) throws Exception
    {
        PageInfo<SchedulerJob> schedulerJobList = schedulerService.querySchedulerJobList(condition);
        return ResultInfo.success(schedulerJobList);
    }

    /**
     *
     * @Description 查询调度任务详情
     * @param jobId
     * @return SchedulerJob
     * @throws TaskException
     * <AUTHOR>
     * @date 2022年5月24日 下午7:51:30
     */
    @GetMapping("/querySchedulerJobDetail")
    @ResponseBody
    @ApiOperation("查询调度任务详情")
    @RequiresPermissions(value = {})
    public ResultInfo<SchedulerJob> querySchedulerJobDetail(@RequestParam("jobId") String jobId) throws Exception
    {
        SchedulerJob schedulerJob = schedulerService.querySchedulerJobDetail(jobId);
        return ResultInfo.success(schedulerJob);
    }

    /**
     *
     * @Description 暂停调度任务
     * @param jobId
     * @throws TaskException
     * <AUTHOR>
     * @date 2022年5月24日 下午7:51:51
     */
    @GetMapping("/pauseSchedulerJob")
    @ResponseBody
    @ApiOperation("暂定调度任务")
    @RequiresPermissions(value = {})
    public ResultInfo<?> pauseSchedulerJob(@RequestParam("jobId") String jobId) throws Exception
    {
        schedulerService.pauseSchedulerJob(jobId);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 恢复调度任务
     * @param jobId
     * @throws TaskException
     * <AUTHOR>
     * @date 2022年5月24日 下午7:52:05
     */
    @GetMapping("/resumeSchedulerJob")
    @ResponseBody
    @ApiOperation("恢复调度任务")
    @RequiresPermissions(value = {})
    public ResultInfo<?> resumeSchedulerJob(@RequestParam("jobId") String jobId) throws Exception
    {
        schedulerService.resumeSchedulerJob(jobId);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 删除调度任务
     * @param jobId
     * @throws TaskException
     * <AUTHOR>
     * @date 2022年5月24日 下午7:52:17
     */
    @GetMapping("/deleteSchedulerJob")
    @ResponseBody
    @ApiOperation("删除调度任务")
    @RequiresPermissions(value = {})
    public ResultInfo<?> deleteSchedulerJob(@RequestParam("jobId") String jobId) throws Exception
    {
        schedulerService.deleteSchedulerJob(jobId);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 执行调度任务
     * @param jobId
     * @throws TaskException
     * <AUTHOR>
     * @date 2022年5月24日 下午7:52:29
     */
    @GetMapping("/triggerSchedulerJob")
    @ResponseBody
    @ApiOperation("执行调度任务")
    @RequiresPermissions(value = {})
    public ResultInfo<?> triggerSchedulerJob(@RequestParam("jobId") String jobId) throws Exception
    {
        schedulerService.triggerSchedulerJob(jobId);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 新增调度任务
     * @param schedulerJob
     * @throws TaskException
     * <AUTHOR>
     * @date 2022年5月24日 下午7:52:47
     */
    @PostMapping("/addSchedulerJob")
    @ResponseBody
    @ApiOperation("新增调度任务")
    @RequiresPermissions(value = {})
    public ResultInfo<?> addSchedulerJob(@RequestBody SchedulerJob schedulerJob) throws Exception
    {
        // http请求创建任务不会带jobId，由调度服务自行生成，增加前缀用于区分，防止业务生成jobId与调度服务自行生成jobId重复
        String jobId = SeqUtils.getSequenceUid();
        schedulerJob.setJobId(jobId);
        schedulerService.addSchedulerJob(schedulerJob);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 修改调度任务
     * @param schedulerJob
     * @throws TaskException
     * <AUTHOR>
     * @date 2022年5月24日 下午7:52:59
     */
    @PostMapping("/updateSchedulerJob")
    @ResponseBody
    @ApiOperation("修改调度任务")
    @RequiresPermissions(value = {})
    public ResultInfo<?> updateSchedulerJob(@RequestBody SchedulerJob schedulerJob) throws Exception
    {
        schedulerService.updateSchedulerJob(schedulerJob);
        return ResultInfo.success();
    }
}
