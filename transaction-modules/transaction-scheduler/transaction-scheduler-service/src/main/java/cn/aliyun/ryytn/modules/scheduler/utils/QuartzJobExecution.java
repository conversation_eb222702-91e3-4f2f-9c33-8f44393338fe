package cn.aliyun.ryytn.modules.scheduler.utils;

import javax.annotation.PostConstruct;

import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.constants.SchedulerConstant;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.spring.SpringUtil;
import cn.aliyun.ryytn.modules.scheduler.dao.SchedulerDao;
import lombok.extern.slf4j.Slf4j;

/**
 * 定时任务处理（允许并发执行）
 *
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class QuartzJobExecution extends AbstractQuartzJob
{
    @Autowired
    private Scheduler scheduler;

    @Autowired
    private SchedulerDao schedulerDao;

    private static Scheduler schedulers;

    private static SchedulerDao schedulerDaos;

    /**
     * 线程本地变量
     */
    private static ThreadLocal<String> threadLocal = new ThreadLocal<String>();

    // 通过@PostConstruct实现初始化bean之前进行的操作
    @PostConstruct
    public void init()
    {
        schedulers = this.scheduler;
        schedulerDaos = this.schedulerDao;
    }

    @Override
    protected void doExecute(JobExecutionContext context, SchedulerJob schedulerJob) throws Exception
    {
        Class<TaskService> clazz = (Class<TaskService>) this.getClass().getClassLoader().loadClass(schedulerJob.getClassName());
        TaskService schedulerJobService = SpringUtil.getBean(clazz);
        schedulerJobService.process(schedulerJob);
    }

    /**
     * 执行前
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     */
    @Override
    protected void before(JobExecutionContext context, SchedulerJob schedulerJob)
    {
        threadLocal.set(DateUtils.getDate(DateUtils.YMDHMS_STD_MS));
    }

    /**
     * 执行后
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     */
    @Override
    protected void after(JobExecutionContext context, SchedulerJob schedulerJob, Exception e)
    {
        String startTime = threadLocal.get();
        String endTime = DateUtils.getDate(DateUtils.YMDHMS_STD_MS);
        threadLocal.remove();

        // 如果执行过程发生异常，则不再继续执行后续处理
        if (null != e)
        {
            return;
        }
        try
        {
            // 延时任务属于一次性任务，执行完成后，将任务状态修改为已结束，并从schedule中删除
            if (SchedulerConstant.JOB_TYPE_ONCE.equals(schedulerJob.getJobType()))
            {
                schedulerJob.setStatus(SchedulerConstant.StatusEnum.FINISHED.getValue());
                schedulerDaos.updateSchedulerJobStatus(schedulerJob);

                // 判断是否存在
                JobKey jobKey = SchedulerUtils.getJobKey(schedulerJob.getJobId(), SchedulerConstant.JOB_GROUP_NAME);
                if (schedulers.checkExists(jobKey))
                {
                    // 防止创建时存在数据问题 先移除，然后在执行创建操作
                    schedulers.deleteJob(jobKey);
                }
            }
        }
        catch (Exception e1)
        {
            log.error("after has exception:", e);
        }
    }
}
