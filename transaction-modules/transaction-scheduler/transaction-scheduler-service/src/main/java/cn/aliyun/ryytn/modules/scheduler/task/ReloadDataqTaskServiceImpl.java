package cn.aliyun.ryytn.modules.scheduler.task;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.aliyun.brain.dataindustry.microapp.MicroAppTaskInstanceOutputVO;
import com.aliyun.brain.dataindustry.microapp.request.ApiRunMicroAppRequest;
import com.aliyun.brain.dataindustry.microapp.request.MicroAppInstantIdRequest;
import com.aliyun.dataq.dataindustry.DataIndustrySpringServiceContext;
import com.aliyun.dataq.dataindustry.config.Header;
import com.aliyun.dataq.dataindustry.service.MicroAppService;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.DataqTask;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.scheduler.dao.DataqTaskDao;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 重载dataq任务调度任务执行实现类
 * <AUTHOR>
 * @date 2023/12/7 17:07
 */
@Slf4j
@Service
public class ReloadDataqTaskServiceImpl implements TaskService
{
    @Autowired
    private DataqTaskDao dataqTaskDao;

    @Autowired
    private RedisUtils redisUtils;

    @Resource(name = "dataIndustryContext")
    private DataIndustrySpringServiceContext dataIndustryContext;

    @Value("${dataq.scheduler.userId}")
    private String userId;

    @Value("${dataq.scheduler.tenantCode}")
    private String tenantCode;

    @Value("${dataq.scheduler.workspaceCode}")
    private String workspaceCode;

    // 此方法不做事务控制
    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        log.info("Enter reload dataq task.");

        // 清理已过期历史任务,默认一年前
        Date expiredTime = DateUtils.addDays(new Date(), -365);
        dataqTaskDao.deleteHistoryDataqTask(expiredTime);

        List<String> notCompletedStatusList = Arrays.asList(CommonConstants.TASK_STATUS_NOT_COMPLETED);
        List<String> failStatusList = Arrays.asList(CommonConstants.TASK_STATUS_NEED_RELOAD);

        long timeoutTime = DateUtils.addDays(new Date(), -1).getTime();

        // 配置header
        Header header = new Header();
        header.setUserId(userId);
        header.setTenantCode(tenantCode);
        header.setWorkspaceCode(workspaceCode);
        MicroAppService service = dataIndustryContext.getService(MicroAppService.class);

        // 查询所有未完成的任务
        List<DataqTask> notCompletedDataqTaskList = dataqTaskDao.queryDataqTaskListNotCompleted(notCompletedStatusList);
        if (CollectionUtils.isNotEmpty(notCompletedDataqTaskList))
        {
            for (DataqTask dataqTask : notCompletedDataqTaskList)
            {
                if (timeoutTime > dataqTask.getStartTime().getTime())
                {
                    dataqTask.setStatus(CommonConstants.TASK_STATUS_TIMEOUT);
                    dataqTaskDao.updateDataqTask(dataqTask);
                }
                else
                {
                    // 查询dataq任务状态
                    MicroAppInstantIdRequest microAppInstantIdRequest = new MicroAppInstantIdRequest();
                    microAppInstantIdRequest.setAppCode(dataqTask.getAppCode());
                    microAppInstantIdRequest.setTaskInstanceId(dataqTask.getTaskId());
                    MicroAppTaskInstanceOutputVO execute = service.instanceById(microAppInstantIdRequest, header);
                    // 如果状态发生变化，更新数据库任务状态
                    if (!Objects.equals(execute.getStatus().name(), dataqTask.getStatus()))
                    {
                        dataqTask.setStatus(execute.getStatus().name());
                        dataqTask.setEndTime(execute.getEndTime());
                        dataqTaskDao.updateDataqTask(dataqTask);
                    }
                }

                // 如果状态为已完成并且任务存在锁，则解锁
                if ((failStatusList.contains(dataqTask.getStatus()) || CommonConstants.TASK_STATUS_SUCCESS.equals(dataqTask.getStatus()) ||
                    CommonConstants.TASK_STATUS_TIMEOUT.equals(dataqTask.getStatus())) &&
                    StringUtils.isNotBlank(dataqTask.getLockKey()))
                {
                    redisUtils.unlock(dataqTask.getLockKey());
                }
            }
        }

        // 查询所有失败任务，需要补偿
        List<DataqTask> needReloadDataqTaskList = dataqTaskDao.queryDataqTaskListNeedReload(failStatusList);
        if (CollectionUtils.isNotEmpty(needReloadDataqTaskList))
        {
            for (DataqTask dataqTask : needReloadDataqTaskList)
            {
                // 如果任务有锁，先获取锁，获取不到锁continue
                if (StringUtils.isNotBlank(dataqTask.getLockKey()))
                {
                    boolean isLock = redisUtils.lock(dataqTask.getLockKey(), 1800);
                    if (!isLock)
                    {
                        continue;
                    }
                }
                try
                {
                    Map<String, Object> map = JSON.parseObject(dataqTask.getParam(), Map.class);
                    ApiRunMicroAppRequest apiRunMicroAppRequest = new ApiRunMicroAppRequest();
                    apiRunMicroAppRequest.setAppCode(dataqTask.getAppCode());
                    apiRunMicroAppRequest.setApiParamValues(map);

                    log.info("reloadDataqTask param:{}", JSON.toJSONString(map));

                    // 执行算法调度任务
                    MicroAppTaskInstanceOutputVO execute = service.execute(apiRunMicroAppRequest, header);
                    log.info("reloadDataqTask.execute:" + execute);

                    if (Objects.nonNull(execute))
                    {
                        dataqTask.setTaskId(execute.getTaskInstanceId());
                        dataqTask.setStatus(execute.getStatus().name());
                        dataqTask.setEndTime(execute.getEndTime());
                        dataqTask.setReloadId(dataqTask.getReloadId() + 1);
                        // 修改原任务状态
                        dataqTaskDao.updateDataqTask(dataqTask);

                        // 如果状态为已完成并且任务存在锁，则解锁
                        if ((failStatusList.contains(execute.getStatus().name()) || "SUCCESS".equals(execute.getStatus().name())) &&
                            StringUtils.isNotBlank(dataqTask.getLockKey()))
                        {
                            redisUtils.unlock(dataqTask.getLockKey());
                        }
                    }
                }
                catch (Exception e)
                {
                    log.error("reload dataq task has exception.", e);
                }
            }
        }
    }
}
