package cn.aliyun.ryytn.modules.scheduler.utils;

import java.util.Objects;

import org.quartz.CronScheduleBuilder;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.ScheduleBuilder;
import org.quartz.Scheduler;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.constants.SchedulerConstant;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.exception.ServiceException;

/**
 * 定时任务工具类
 *
 * <AUTHOR>
 *
 */
public class SchedulerUtils
{
    /**
     * 得到quartz任务类
     *
     * @param sysJob 执行计划
     * @return 具体执行任务类
     */
    private static Class<? extends Job> getQuartzJobClass(SchedulerJob sysJob)
    {
        boolean isConcurrent = "0".equals(sysJob.getConcurrent());
        return isConcurrent ? QuartzJobExecution.class : QuartzDisallowConcurrentExecution.class;
    }

    /**
     * 构建任务触发对象
     */
    public static TriggerKey getTriggerKey(String jobId, String jobGroup)
    {
        return TriggerKey.triggerKey(SchedulerConstant.JOB_KEY_PREFIX + jobId, jobGroup);
    }

    /**
     * 构建任务键对象
     */
    public static JobKey getJobKey(String jobId, String jobGroup)
    {
        return JobKey.jobKey(SchedulerConstant.JOB_KEY_PREFIX + jobId, jobGroup);
    }

    /**
     * 创建定时任务
     */
    @SuppressWarnings("unchecked")
    public static void createScheduleJob(Scheduler scheduler, SchedulerJob schedulerJob) throws Exception
    {
        Class<? extends Job> jobClass = getQuartzJobClass(schedulerJob);
        // 构建job信息
        String jobId = schedulerJob.getJobId();
        String jobGroup = SchedulerConstant.JOB_GROUP_NAME;
        JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(getJobKey(jobId, jobGroup)).build();

        ScheduleBuilder scheduleBuilder = null;

        // 按新的cronExpression表达式构建一个新的trigger
        Trigger trigger = null;
        if (SchedulerConstant.JOB_TYPE_CRON.equals(schedulerJob.getJobType()))
        {
            // 表达式调度构建器
            scheduleBuilder = CronScheduleBuilder.cronSchedule(schedulerJob.getJobConf()).withMisfireHandlingInstructionDoNothing();
            trigger =
                TriggerBuilder.newTrigger().withIdentity(getTriggerKey(jobId, jobGroup)).withSchedule(scheduleBuilder).build();
        }
        else if (SchedulerConstant.JOB_TYPE_CYCLE.equals(schedulerJob.getJobType()))
        {
            // 简单调度构建器，永久循环，间隔时间取schedulerJob.jobConf
            scheduleBuilder =
                SimpleScheduleBuilder.repeatSecondlyForever(Integer.valueOf(schedulerJob.getJobConf())).withMisfireHandlingInstructionIgnoreMisfires();
            trigger =
                TriggerBuilder.newTrigger().withIdentity(getTriggerKey(jobId, jobGroup)).withSchedule(scheduleBuilder).startAt(schedulerJob.getStartDate())
                    .endAt(schedulerJob.getEndDate()).build();
        }
        else if (SchedulerConstant.JOB_TYPE_ONCE.equals(schedulerJob.getJobType()))
        {
            // 简单调度构建器，只循环一次
            scheduleBuilder = SimpleScheduleBuilder.simpleSchedule().withRepeatCount(1).withIntervalInSeconds(1).withMisfireHandlingInstructionFireNow();
            TriggerBuilder triggerBuiler =
                TriggerBuilder.newTrigger().withIdentity(getTriggerKey(jobId, jobGroup)).withSchedule(scheduleBuilder).endAt(schedulerJob.getEndDate());
            if (Objects.isNull(schedulerJob.getStartDate()))
            {
                triggerBuiler.startNow();
            }
            else
            {
                triggerBuiler.startAt(schedulerJob.getStartDate());
            }
            trigger = triggerBuiler.build();
        }
        else
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }

        // 放入参数，运行时的方法可以获取
        jobDetail.getJobDataMap().put(SchedulerConstant.TASK_PROPERTIES, schedulerJob);

        // 判断是否存在
        if (!scheduler.checkExists(getJobKey(jobId, jobGroup)))
        {
            scheduler.scheduleJob(jobDetail, trigger);
        }

        // 暂停任务
        if (schedulerJob.getStatus().equals(SchedulerConstant.StatusEnum.PAUSE.getValue()))
        {
            scheduler.pauseJob(SchedulerUtils.getJobKey(jobId, jobGroup));
        }
    }

    /**
     * 设置定时任务策略
     * 暂不支持自定义，全部使用默认策略
     */
    public static ScheduleBuilder handleScheduleMisfirePolicy(SchedulerJob schedulerJob, ScheduleBuilder cb)
        throws Exception
    {
        return cb;
    }
}