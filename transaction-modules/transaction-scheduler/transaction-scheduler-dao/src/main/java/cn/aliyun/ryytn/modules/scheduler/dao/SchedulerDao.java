package cn.aliyun.ryytn.modules.scheduler.dao;

import java.util.List;

import cn.aliyun.ryytn.common.entity.SchedulerJob;


/**
 *
 * @Description 调度任务管理Dao层接口
 * <AUTHOR>
 * @date 2022年5月24日 上午11:46:22
 */
public interface SchedulerDao
{
    /**
     *
     * @Description 查询所有调度任务
     * @return List<SchedulerJob>
     * <AUTHOR>
     * @date 2022年5月24日 下午7:00:54
     */
    List<SchedulerJob> queryAllSchedulerJob();

    /**
     *
     * @Description 查询调度任务列表
     * @param schedulerJob
     * @return List<SchedulerJob>
     * <AUTHOR>
     * @date 2022年5月24日 上午11:47:21
     */
    List<SchedulerJob> querySchedulerJobList(SchedulerJob schedulerJob);

    /**
     *
     * @Description 查询调度任务详情
     * @param schedulerJob
     * @return SchedulerJob
     * <AUTHOR>
     * @date 2022年5月24日 上午11:47:21
     */
    SchedulerJob querySchedulerJobDetial(String jobId);

    /**
     *
     * @Description 删除调度任务
     * @param jobId
     * <AUTHOR>
     * @date 2022年5月24日 上午11:47:21
     */
    void deleteSchedulerJob(String jobId);

    /**
     *
     * @Description 修改调度任务
     * @param schedulerJob
     * <AUTHOR>
     * @date 2022年5月24日 上午11:47:21
     */
    void updateSchedulerJobStatus(SchedulerJob schedulerJob);

    /**
     *
     * @Description 新增调度任务
     * @param schedulerJob
     * <AUTHOR>
     * @date 2022年5月24日 上午11:47:21
     */
    void addSchedulerJob(SchedulerJob schedulerJob);

    /**
     *
     * @Description 根据业务编号查询任务
     * @param serviceId
     * @return SchedulerJob
     * <AUTHOR>
     * @date 2023年12月13日 14:19
     */
    SchedulerJob querySchedulerJobByServiceId(String serviceId);
}
