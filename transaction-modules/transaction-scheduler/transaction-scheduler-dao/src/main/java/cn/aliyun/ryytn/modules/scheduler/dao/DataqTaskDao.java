package cn.aliyun.ryytn.modules.scheduler.dao;

import java.util.Date;
import java.util.List;

import cn.aliyun.ryytn.common.entity.DataqTask;

/**
 * @Description 阿里dataq任务执行记录表
 * <AUTHOR>
 * @date 2023/12/7 16:28
 */
public interface DataqTaskDao
{
    /**
     *
     * @Description 新增dataq任务执行记录
     * @param dataqTask
     * <AUTHOR>
     * @date 2023年12月07日 16:38
     */
    void addDataqTask(DataqTask dataqTask);

    /**
     *
     * @Description 查询dataq任务未完成记录
     * @param statusList
     * @return List<DataqTask>
     * <AUTHOR>
     * @date 2023年12月07日 16:38
     */
    List<DataqTask> queryDataqTaskListNotCompleted(List<String> statusList);

    /**
     *
     * @Description 查询dataq任务需要补偿的记录
     * @param statusList
     * @return List<DataqTask>
     * <AUTHOR>
     * @date 2023年12月07日 16:38
     */
    List<DataqTask> queryDataqTaskListNeedReload(List<String> statusList);

    /**
     *
     * @Description 修改dataq任务执行记录
     * @param dataqTask
     * <AUTHOR>
     * @date 2023年12月07日 16:39
     */
    void updateDataqTask(DataqTask dataqTask);

    /**
     *
     * @Description 删除历史dataq任务执行记录
     * @param expiredTime
     * <AUTHOR>
     * @date 2023年12月07日 16:39
     */
    void deleteHistoryDataqTask(Date expiredTime);
}
