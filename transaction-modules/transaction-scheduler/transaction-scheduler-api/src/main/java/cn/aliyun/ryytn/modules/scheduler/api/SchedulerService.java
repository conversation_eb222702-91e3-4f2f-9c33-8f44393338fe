package cn.aliyun.ryytn.modules.scheduler.api;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.SchedulerJob;

/**
 *
 * @Description 调度任务管理接口
 * <AUTHOR>
 * @date 2022年5月24日 下午7:48:57
 */
public interface SchedulerService
{

    /**
     *
     * @Description 查询调度任务列表
     * @param schedulerJob
     * @return PageInfo<SchedulerJob>
     * @throws Exception
     * <AUTHOR>
     * @date 2022年5月24日 下午7:49:11
     */
    public PageInfo<SchedulerJob> querySchedulerJobList(PageCondition<SchedulerJob> condition) throws Exception;

    /**
     *
     * @Description 查询调度任务详情
     * @param jobId
     * @return SchedulerJob
     * @throws Exception
     * <AUTHOR>
     * @date 2022年5月24日 下午7:51:30
     */
    public SchedulerJob querySchedulerJobDetail(String jobId) throws Exception;

    /**
     *
     * @Description 暂停调度任务
     * @param jobId
     * @throws Exception
     * <AUTHOR>
     * @date 2022年5月24日 下午7:51:51
     */
    public void pauseSchedulerJob(String jobId) throws Exception;

    /**
     *
     * @Description 恢复调度任务
     * @param jobId
     * @throws Exception
     * <AUTHOR>
     * @date 2022年5月24日 下午7:52:05
     */
    public void resumeSchedulerJob(String jobId) throws Exception;

    /**
     *
     * @Description 删除调度任务
     * @param jobId
     * @throws Exception
     * <AUTHOR>
     * @date 2022年5月24日 下午7:52:17
     */
    public void deleteSchedulerJob(String jobId) throws Exception;

    /**
     *
     * @Description 执行调度任务
     * @param jobId
     * @throws Exception
     * <AUTHOR>
     * @date 2022年5月24日 下午7:52:29
     */
    public void triggerSchedulerJob(String jobId) throws Exception;

    /**
     *
     * @Description 新增调度任务
     * @param schedulerJob
     * @throws Exception
     * <AUTHOR>
     * @date 2022年5月24日 下午7:52:47
     */
    public void addSchedulerJob(SchedulerJob schedulerJob) throws Exception;

    /**
     *
     * @Description 修改调度任务
     * @param schedulerJob
     * @throws Exception
     * <AUTHOR>
     * @date 2022年5月24日 下午7:52:59
     */
    public void updateSchedulerJob(SchedulerJob schedulerJob) throws Exception;

    /**
     *
     * @Description 校验表达式是否有效
     * @param cronExpression
     * @return boolean
     * <AUTHOR>
     * @date 2022年5月24日 下午7:53:16
     */
    public boolean checkCronExpressionIsValid(String cronExpression);
}