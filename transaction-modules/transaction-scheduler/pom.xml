<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.aliyun.ryytn</groupId>
		<artifactId>transaction-modules</artifactId>
		<version>1.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>transaction-scheduler</artifactId>
    <packaging>pom</packaging>
	
	<dependencies>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>
	</dependencies>
	
	<modules>
	  <module>transaction-scheduler-api</module>
	  <module>transaction-scheduler-dao</module>
	  <module>transaction-scheduler-entity</module>
	  <module>transaction-scheduler-service</module>
	</modules>
</project>
