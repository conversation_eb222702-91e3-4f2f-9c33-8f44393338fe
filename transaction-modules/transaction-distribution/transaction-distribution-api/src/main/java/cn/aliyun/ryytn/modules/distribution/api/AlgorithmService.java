package cn.aliyun.ryytn.modules.distribution.api;

import java.util.List;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PageAlgoVersionReqVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PageAlgoVersionRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryAlgorithmListRspVo;

/**
 * @Description 算法管理接口
 * <AUTHOR>
 * @date 2023/11/21 16:27
 */
public interface AlgorithmService
{
    List<QueryAlgorithmListRspVo> queryAlgorithmList() throws Exception;

    PageInfo<PageAlgoVersionRspVo> queryAlgoVersion(PageCondition<PageAlgoVersionReqVo> condition) throws Exception;
}
