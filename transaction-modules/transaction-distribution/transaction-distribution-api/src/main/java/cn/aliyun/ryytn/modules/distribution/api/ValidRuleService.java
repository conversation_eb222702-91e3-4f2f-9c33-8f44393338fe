package cn.aliyun.ryytn.modules.distribution.api;

import java.util.List;

import cn.aliyun.ryytn.modules.distribution.entity.dto.ValidRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleVo;

/**
 * @Description 效期分档规则api
 * <AUTHOR>
 * @date 2023/11/14 11:06
 */
public interface ValidRuleService
{

    /**
     *
     * @Description 查询效期分档规则列表数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 15:34
     */
    List<ValidRuleDto> queryValidRuleList(ValidRuleDto validRuleDto) throws Exception;

    /**
     *
     * @Description 查询效期分档规则详情
     * @param  id
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 15:35
     */
    ValidRuleVo queryValidRuleDetail(String id) throws Exception;


    /**
     *
     * @Description 查询效期分档规则详情
     * @param  id
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 15:35
     */
    void deleteValidRule(String id) throws Exception;

    /**
     *
     * @Description 新增效期分档规则
     * @param  validRuleVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 15:35
     */
    void addOrUpdateValidRule(ValidRuleVo validRuleVo) throws Exception;

}
