package cn.aliyun.ryytn.modules.distribution.api;

import java.util.List;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.distribution.entity.dto.GraphKcclConfigDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyConfDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo;

/**
 * @Description 库存策略配置api
 * <AUTHOR>
 * @date 2023/11/20 20:15
 */
public interface InventoryStrategyService
{

    /**
     *
     * @Description 查询库存策略配置列表数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/20 20:15
     */
    List<InventoryStrategyConfDto> queryInventoryStrategyConfList() throws Exception;


    /**
     *
     * @Description 修改库存策略配置
     * @param  inventoryStrategyConfDtos
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/21 09:05
     */
    void updateInventoryStrategyConf(List<InventoryStrategyConfDto> inventoryStrategyConfDtos) throws Exception;

    /**
     * 新增、修改、删除日均销量(实际)
     * @param inventoryStrategyVo
     * @throws Exception
     */
    void inventoryStrategyRealityManage(InventoryStrategyVo inventoryStrategyVo) throws Exception;

    /**
     *
     * @Description 查询库存策略数据
     * @param  graphKcclConfig
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/22 11:12
     */
    List<GraphKcclConfigDto> queryInventoryStrategyDetail(GraphKcclConfigDto graphKcclConfig) throws Exception;

    /**
     *
     * @Description 修改库存策略数据
     * @param  graphKcclConfigList
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/22 10:16
     */
    void updateInventoryStrategy(List<GraphKcclConfigDto> graphKcclConfigList) throws Exception;


    /**
     *
     * @Description 查询库存策略列表
     * @param  condition
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/20 20:15
     */
    PageInfo<InventoryStrategyVo> queryInventoryStrategyList(PageCondition<InventoryStrategyConditionVo> condition) throws Exception;

    /**
     *
     * @Description 查询库存策略表头下拉列表
     * @param inventoryStrategyVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月03日 17:25     */
    List<InventoryStrategyVo> queryInventoryStrategyHeadSelect(InventoryStrategyVo inventoryStrategyVo) throws Exception;

    /**
     *
     * @Description 分页查询库存策略视图数据列表
     * @param condition
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月04日 16:26     */
    PageInfo<InventoryStrategyVo> pageInventoryStrategyView(PageCondition<InventoryStrategyVo> condition) throws Exception;


    /**
     * 刷新库存策略视图数据到表中
     */
    void rereshInventoryStrategyData2Table();
}
