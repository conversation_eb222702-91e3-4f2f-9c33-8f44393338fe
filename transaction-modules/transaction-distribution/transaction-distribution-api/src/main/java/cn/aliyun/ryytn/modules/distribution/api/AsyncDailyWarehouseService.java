package cn.aliyun.ryytn.modules.distribution.api;

import cn.aliyun.ryytn.modules.distribution.entity.vo.GenerateDailyWarehouseAiplanDemandVo;

/**
 * @Description 日分仓异步接口
 * <AUTHOR>
 * @date 2024/3/23 19:06
 */
public interface AsyncDailyWarehouseService
{
    /**
     *
     * @Description 异步日分仓编辑确认生成日分仓调拨
     * @param generateDailyWarehouseAiplanDemandVo
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月23日 19:07
     */
    @Deprecated
    void generateDailyWarehouseAiplanDemand(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo, String lockKey) throws Exception;
}
