package cn.aliyun.ryytn.modules.distribution.api;

import java.util.List;

import cn.aliyun.ryytn.modules.distribution.entity.dto.AdjustableDaysRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryAdjustableDaysRuleListRspVo;

/**
 * @Description 可调天数规则接口
 * <AUTHOR>
 * @date 2023/11/16 10:36
 */
public interface AdjustableDaysRuleService
{
    void addAdjustableDaysRule(AdjustableDaysRuleDto adjustableDaysRuleDto) throws Exception;

    List<QueryAdjustableDaysRuleListRspVo> queryAdjustableDaysRuleList() throws Exception;

    AdjustableDaysRuleDto queryAdjustableDaysRuleDetail(String id) throws Exception;

    void deleteAdjustableDaysRule(String id) throws Exception;
}
