package cn.aliyun.ryytn.modules.distribution.api;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.dto.AbcTypeDto;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.DataqTask;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryInferenceDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.StockFluctuateDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseStockAnalyDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseAiplanDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo;

/**
 * @Description 调拨计划接口
 * <AUTHOR>
 * @date 2023/12/26 14:15
 */
public interface FreightPlanService
{
    /**
     *
     * @Description 查询调拨计划列表
     * @param freightPlanDto
     * @return List<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<FreightPlanDto> queryFreightPlanList(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 查询调拨计划版本列表
     * @param freightPlanDto
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月26日 19:50
     */
    List<String> queryFreightPlanVersionList(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 查询调拨计划动态表头
     * @param FreightPlanDto
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:16
     */
    List<String> queryFreightPlanHeadList(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 查询调拨计划表头下拉列表
     * @param FreightPlanDto
     * @return List<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    List<FreightPlanDto> queryFreightPlanHeadSelect(FreightPlanDto freightPlanDto)
        throws Exception;

    /**
     *
     * @Description 查询调拨计划分组聚合数据列表
     * @param FreightPlanDto
     * @return List<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    List<FreightPlanDto> queryFreightPlanListGroupBy(FreightPlanDto freightPlanDto)
        throws Exception;

    /**
     *
     * @Description 分页查询调拨计划明细数据列表
     * @param condition
     * @return PageInfo<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月20日 14:28
     */
    PageInfo<FreightPlanDto> queryFreightPlanDataPage(PageCondition<FreightPlanDto> condition) throws Exception;

    /**
     *
     * @Description 查询调拨计划日分仓需求列表
     * @param freightPlanDto
     * @return List<DailyWarehouseDemandRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 19:47
     */
    List<DailyWarehouseDemandRspVo> queryFreightPlanDailyWarehouseDemandList(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 查询调拨计划日分仓调拨需求列表
     * @param freightPlanDto
     * @return List<DailyWarehouseAiplanDemandRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 19:47
     */
    List<DailyWarehouseAiplanDemandRspVo> queryFreightPlanDailyWarehouseAiPlanList(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 查询调拨计划指定分组逻辑的建议合计数量
     * @param FreightPlanDto
     * @return List<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    List<FreightPlanDto> queryAdvFreightPlanListGroupBySum(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 查询实际保存调拨计划指定分组逻辑的合计数量
     * @param FreightPlanDto
     * @return List<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    List<FreightPlanDto> queryActFreightPlanListGroupBySum(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 查询调拨计划多仓比对列表
     * @param FreightPlanDto
     * @return List<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    List<FreightPlanDto> queryFreightPlanWarehouseContrastList(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 询指定产品/仓库的库存数据
     * @param freightPlanDto
     * @return List<InventoryStrategyVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月02日 19:45
     */
    List<InventoryStrategyVo> queryFreightPlanInventoryList(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 保存调拨计划数据
     * 需求变更，展示数据粒度从生产批次修改为效期，编辑保存功能粒度依然为生产频次
     * 上线时间过紧来不及从数据加工开始重新实现，接口当前废弃不可用
     * @param freightPlanDtoList
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月04日 13:54
     */
    @Deprecated
    void saveFreightPlanData(List<FreightPlanDto> freightPlanDtoList) throws Exception;

    /**
     *
     * @Description 查询生产调入列表
     * @param freightPlanDto
     * @return List<WarehouseStockAnalyDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月05日 17:20
     */
    List<WarehouseStockAnalyDto> queryProductionFreightList(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 查询在途库存列表
     * @param freightPlanDto
     * @return List<WarehouseStockAnalyDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月05日 17:20
     */
    List<WarehouseStockAnalyDto> queryOnTransInventoryList(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 查询库存列表
     * @param stockFluctuateDto
     * @return List<StockFluctuateDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月19日 13:57
     */
    List<StockFluctuateDto> queryStockFluctuateList(StockFluctuateDto stockFluctuateDto) throws Exception;

    /**
     *
     * @Description 查询库存推演数据
     * @param inventoryInferenceDto
     * @return InventoryInferenceDto
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月08日 10:56
     */
    @Deprecated
    InventoryInferenceDto queryInventoryInference(InventoryInferenceDto inventoryInferenceDto) throws Exception;

    /**
     *
     * @Description 查询多仓库比对库存推演数据
     * @param inventoryInferenceDto
     * @return List<InventoryInferenceDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月08日 14:07
     */
    @Deprecated
    List<InventoryInferenceDto> queryInventoryInferenceWarehouseContrast(InventoryInferenceDto inventoryInferenceDto)
        throws Exception;

    /**
     *
     * @Description 多仓比对未来可供应天数
     * @param freightPlanDto
     * @return List<StockFluctuateDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月20日 10:16
     */
    List<StockFluctuateDto> queryFreightDaysWarehouseContrast(FreightPlanDto freightPlanDto) throws Exception;

    /**
     *
     * @Description 生成调拨计划
     * @param soWtDemandDto
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月12日 11:38
     */
    void generateFreightPlan(SoWtDemandDto soWtDemandDto) throws Exception;

    /**
     *
     * @Description 查询仓容
     * @param inventoryInferenceDto
     * @return Long
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月19日 17:18
     */
    Long queryWarehouseCapacity(InventoryInferenceDto inventoryInferenceDto) throws Exception;

    /**
     *
     * @Description 查询调拨任务详情
     * @param demandPlanCode
     * @return DataqTask
     * @throws Exception
     * <AUTHOR>
     * @date 2024年04月24日 14:04
     */
    DataqTask queryFreightTaskDetail(String demandPlanCode) throws Exception;

    /**
     * 返回abctype类型
     * @param itemId
     * @return
     */
    AbcTypeDto getAbcType(String itemId);
}
