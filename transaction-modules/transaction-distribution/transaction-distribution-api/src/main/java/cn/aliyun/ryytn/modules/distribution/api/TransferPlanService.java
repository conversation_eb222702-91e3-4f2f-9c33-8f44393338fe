package cn.aliyun.ryytn.modules.distribution.api;

import java.util.List;
import java.util.Map;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.modules.distribution.entity.vo.EditInventoryInferenceVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryInferenceConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryInferenceVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SaveTransferPlanVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SkuWarehouseCompareConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SkuWarehouseCompareVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.TransferPlanCondition;
import cn.aliyun.ryytn.modules.distribution.entity.vo.TransferPlanVo;

/**
 * @Description 调拨计划api
 * <AUTHOR>
 * @date 2023/11/24 15:38
 */
public interface TransferPlanService
{

    /**
     *
     * @Description 查询调拨计划列表数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/24 15:38
     */
    BaseTable<List<TransferPlanVo>> queryTransferPlanList(TransferPlanCondition transferPlanCondition) throws Exception;

    /**
     *
     * @Description 查询产品多仓比对列表数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/28 11:04
     */
    BaseTable<List<SkuWarehouseCompareVo>> querySkuWarehouseCompareList(SkuWarehouseCompareConditionVo skuWarehouseCompareConditionVo) throws Exception;

    /**
     *
     * @Description 查询库存推演数据
     * @param  condition
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/28 11:04
     */
    BaseTable<List<InventoryInferenceVo>> queryInventoryInference(InventoryInferenceConditionVo condition) throws Exception;


    /**
     *
     * @Description 编辑库存推演数据
     * @param  condition
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/28 11:04
     */
    BaseTable<List<EditInventoryInferenceVo>> editInventoryInference(Object condition) throws Exception;


    /**
     *
     * @Description 查询下拉框数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/12/12 17:28
     */
    Map<String, Object> queryComboBoxData() throws Exception;

    void saveTransferPlanData(List<SaveTransferPlanVo> saveTransferPlanVos) throws Exception;


}
