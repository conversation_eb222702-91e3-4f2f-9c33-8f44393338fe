package cn.aliyun.ryytn.modules.distribution.api;

import java.util.List;

import cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PhysicWarehouseVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleVo;

/**
 * @Description 仓能力规则api
 * <AUTHOR>
 * @date 2023/11/18 19:12
 */
public interface WarehouseRuleService
{


    /**
     *
     * @Description 查询仓能力规则列表数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/19 17:15
     */
    List<WarehouseRuleDto> queryWarehouseRuleList(WarehouseRuleDto warehouseRuleDto) throws Exception;

    /**
     *
     * @Description 查询仓能力规则详情
     * @param  id
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/19 17:15
     */
    WarehouseRuleVo queryWarehouseRuleDetail(String id) throws Exception;


    /**
     *
     * @Description 查询仓能力规则详情
     * @param  id
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/19 17:15
     */
    void deleteWarehouseRule(String id) throws Exception;

    /**
     *
     * @Description 新增仓能力规则
     * @param  warehouseRuleVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/19 17:15
     */
    void addOrUpdateWarehouseRule(WarehouseRuleVo warehouseRuleVo) throws Exception;


    /**
     * 更新库存能力状态
     */
    void updateStockCapacityStatus();

    List<PhysicWarehouseVo> queryWarehouseRuleWarehouse();

    List<PhysicWarehouseVo> queryAdjustableDaysRuleWarehouse();
}
