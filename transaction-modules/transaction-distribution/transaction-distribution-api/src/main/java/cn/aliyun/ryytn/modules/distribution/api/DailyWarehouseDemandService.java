package cn.aliyun.ryytn.modules.distribution.api;

import java.util.List;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandBaseTable;
import cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseAiplanDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.GenerateDailyWarehouseAiplanDemandVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo;

/**
 * @Description 日分仓需求接口
 * <AUTHOR>
 * @date 2023/12/4 14:09
 */
public interface DailyWarehouseDemandService
{
    /**
     *
     * @Description 查询日分仓需求计划列表
     * @return List<DailyWarehouseDemandDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:19
     */
    List<DailyWarehouseDemandDto> queryDailyWarehouseDemandPlanList() throws Exception;

    /**
     *
     * @Description 查询日分仓需求版本列表
     * @param dailyWarehouseDemandDto
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:19
     */
    List<String> queryDailyWarehouseDemandVersionList(DailyWarehouseDemandDto dailyWarehouseDemandDto) throws Exception;

    DailyWarehouseDemandBaseTable<List<DailyWarehouseDemandRspVo>> queryDailyWarehouseDemandList(
        QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception;

    void addDailyWarehouseDemandList(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo,boolean rebuild) throws Exception;

    /**
     *
     * @Description 保存日分仓编辑（分页保存）
     * @param generateDailyWarehouseAiplanDemandVo
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月26日 11:09
     */
    void saveDailyWarehouseDemand(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo) throws Exception;

    void generateDailyWarehouseAiplanDemand(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo) throws Exception;

    DailyWarehouseDemandBaseTable<List<DailyWarehouseAiplanDemandDto>> queryDailyWarehouseAiplanDemandList(
        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto) throws Exception;

    /**
     *
     * @Description 查询日分仓调拨需求计划下拉列表
     * @return List<DailyWarehouseAiplanDemandDto>
     * <AUTHOR>
     * @date 2024年01月17日 17:11
     */
    List<DailyWarehouseAiplanDemandDto> queryDailyWarehouseAiplanDemandPlanList() throws Exception;

    /**
     *
     * @Description 查询日分仓调拨需求计划版本下拉列表
     * @param dailyWarehouseAiplanDemandDto
     * @return List<String>
     * <AUTHOR>
     * @date 2024年01月17日 17:11
     */
    List<String> queryDailyWarehouseAiplanDemandPlanVersionList(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto) throws Exception;

    List<String> queryAiPlanDemandVersion(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception;

    /**
     *
     * @Description 查询日分仓调拨需求动态表头
     * @param dailyWarehouseAiplanDemandDto
     * @return
     * <AUTHOR>
     * @date 2024年01月03日 10:06     */
    List<String> queryDailyWarehouseAiplanDemandHeadList(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto) throws Exception;

    /**
     *
     * @Description 查询日分仓调拨需求表头下拉列表
     * @param dailyWarehouseAiplanDemandDto
     * @return List<DailyWarehouseAiplanDemandDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月03日 10:14     */
    List<DailyWarehouseAiplanDemandDto> queryDailyWarehouseDemandAiplanHeadSelect(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto) throws Exception;

    /**
     *
     * @Description 分组聚合查询日分仓需求编辑列表
     * @param dailyWarehouseAiplanDemandDto
     * @return List<DailyWarehouseAiplanDemandRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 17:09
     */
    List<DailyWarehouseAiplanDemandRspVo> queryDailyWarehouseAiplanDemandListGroupBy(
        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto) throws Exception;

    /**
     *
     * @Description 分页查询日分仓调拨需求数据列表
     * @param condition
     * @return
     * <AUTHOR>
     * @date 2024年01月03日 15:02     */
    PageInfo<DailyWarehouseAiplanDemandDto> pageDailyWarehouseAiplanDemandList(PageCondition<DailyWarehouseAiplanDemandDto> condition)
        throws Exception;

    /**
     *
     * @Description 查询日分仓需求动态表头
     * @param queryDailyWarehouseDemandListReqVo
     * @return
     * <AUTHOR>
     * @date 2024年01月03日 16:18     */
    List<String> queryDailyWarehouseDemandHeadList(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception;

    /**
     *
     * @Description 查询日分仓需求计划表头下拉列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return List<DailyWarehouseDemandDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:40
     */
    List<DailyWarehouseDemandDto> queryDailyWarehouseDemandHeadSelect(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception;

    /**
     *
     * @Description 分组聚合查询日分仓需求编辑列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return List<DailyWarehouseDemandRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 17:09
     */
    List<DailyWarehouseDemandRspVo> queryDailyWarehouseDemandListGroupBy(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo)
        throws Exception;

    /**
     *
     * @Description 分页查询日分仓需求编辑列表
     * @param condition
     * @return PageInfo<DailyWarehouseDemandRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 17:04
     */
    PageInfo<DailyWarehouseDemandRspVo> pageDailyWarehouseDemandList(PageCondition<QueryDailyWarehouseDemandListReqVo> condition)
        throws Exception;

    /**
     *
     * @Description 日分仓调拨需求版本是否在生成中
     * @param generateDailyWarehouseAiplanDemandVo
     * @return Boolean
     * @throws Exception
     * <AUTHOR>
     * @date 2024年04月12日 11:35
     */
    Boolean isDailyWarehouseAiplanProcessing(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo) throws Exception;
}
