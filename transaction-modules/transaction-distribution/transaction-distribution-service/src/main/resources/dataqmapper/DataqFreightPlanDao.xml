<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.distribution.dataqdao.DataqFreightPlanDao">
    <select id="queryFreightPlanList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto">
        select distinct demand_plan_code,demand_plan_name from tdm_kcjh_txn_freight_qty_di
        order by demand_plan_code
    </select>

    <select id="queryFreightPlanVersionList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto" resultType="java.lang.String">
        select  prediction_version from cdop_sys.t_ryytn_freight_version_list
		where 1=1
		<if test="demandPlanCode != null and demandPlanCode != ''">
			and demand_plan_code = #{demandPlanCode,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</if>
        order by prediction_version desc
    </select>

    <select id="queryFreightPlanHeadList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto" resultType="java.lang.String">
        select distinct
		<choose>
			<when test="cdcFlag">
				departure_date
			</when>
			<otherwise>
				arrival_date
			</otherwise>
		</choose>
		from tdm_kcjh_txn_freight_qty_di
		<include refid="where_sql"/>
        order by
		<choose>
			<when test="cdcFlag">
				departure_date
			</when>
			<otherwise>
				arrival_date
			</otherwise>
		</choose>
    </select>

	<select id="queryFreightPlanHeadSelect" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto">
		select distinct ${groupColumn}
		from tdm_kcjh_txn_freight_qty_di
		<include refid="where_sql"/>
		order by ${sortColumn}
	</select>

	<select id="queryFreightPlanGroupList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto">
		SELECT
		json_agg("data") AS "data",
		MAX(task_detail_id) as task_detail_id,
		shipping_type_group_id,
		shipping_type_group_name,
		departure_valid_name,
		${groupColumn}
		FROM (
			(
			SELECT
			json_build_object('planValue', SUM(act_qty::numeric), 'planDate',
				<choose>
					<when test="cdcFlag">
						departure_date
					</when>
					<otherwise>
						arrival_date
					</otherwise>
				</choose>
			) AS "data",
			SUM(act_qty::numeric) as "total",
			MAX(task_detail_id) as task_detail_id,
			shipping_type_group_id,
			shipping_type_group_name,
			departure_valid_name,
			${groupColumn}
			FROM tdm_kcjh_txn_freight_qty_di
			<include refid="where_sql"/>
			GROUP BY
			shipping_type_group_id,
			shipping_type_group_name,
			departure_valid_name,
			${groupColumn},
			<choose>
				<when test="cdcFlag">
					departure_date
				</when>
				<otherwise>
					arrival_date
				</otherwise>
			</choose>
			)
		) agg
		GROUP BY
		shipping_type_group_id,
		shipping_type_group_name,
		departure_valid_name,
		${groupColumn}
		ORDER BY ${sortColumn}
	</select>

	<select id="queryAdvFreightPlanGroupList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto">
		SELECT
		json_agg("data") AS "data",
		MAX(task_detail_id) as task_detail_id,
		shipping_type_group_id,
		shipping_type_group_name,
		departure_valid_name,
		${groupColumn}
		FROM (
			(
			SELECT
			json_build_object('planValue', SUM(qty::numeric), 'planDate',
				<choose>
					<when test="cdcFlag">
						departure_date
					</when>
					<otherwise>
						arrival_date
					</otherwise>
				</choose>
			) AS "data",
			SUM(qty::numeric) as "total",
			MAX(task_detail_id) as task_detail_id,
			shipping_type_group_id,
			shipping_type_group_name,
			departure_valid_name,
			${groupColumn}
			FROM tdm_kcjh_txn_freight_qty_di
			<include refid="where_sql"/>
			GROUP BY
			shipping_type_group_id,
			shipping_type_group_name,
			departure_valid_name,
			${groupColumn},
			<choose>
				<when test="cdcFlag">
					departure_date
				</when>
				<otherwise>
					arrival_date
				</otherwise>
			</choose>
			)
		) agg
		GROUP BY
		shipping_type_group_id,
		shipping_type_group_name,
		departure_valid_name,
		 ${groupColumn}
		ORDER BY ${sortColumn}
	</select>

	<select id="queryFreightPlanWarehouseContrastList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto">
		SELECT
		json_agg("data") AS "data",
		biz_warehouse_code as "endPointId",
		biz_warehouse_name as "endPointName"
		FROM (SELECT
			json_build_object('planValue', SUM(a.qty::numeric), 'planDate',a.arrival_date) AS "data",
			b.biz_warehouse_code,
			b.biz_warehouse_name,
			SUM(qty::numeric) as "total"
			FROM tdm_kcjh_txn_freight_qty_di a
			left join dim_bas_warehouse_info_df b
			on a.end_point_id=b.warehouse_code
			<include refid="where_sql"/>
			group by b.biz_warehouse_code,b.biz_warehouse_name,a.arrival_date
		) agg
			group by biz_warehouse_code,biz_warehouse_name
			order by biz_warehouse_code,biz_warehouse_name
	</select>

	<select id="queryFreightPlanDataKeyList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto">
		select distinct
			item_id,
			start_point_id,
			start_physical_point_id,
			end_point_id,
			end_physical_point_id,
			shipping_type_group_id
		from tdm_kcjh_txn_freight_qty_di
		<include refid="where_sql"/>
		ORDER BY item_id, start_point_id, end_point_id, shipping_type_group_id
	</select>

	<select id="queryFreightPlanDataJsonList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto">
		SELECT
		json_agg(json_build_object('planValue', act_qty::varchar, 'planDate',
		<choose>
			<when test="cdcFlag">
				departure_date
			</when>
			<otherwise>
				arrival_date
			</otherwise>
		</choose>
		)) AS "data",
		p.item_id,
		MAX(p.item_name) as item_name,
		p.start_point_id,
		MAX(p.start_point_name) as start_point_name,
		p.start_physical_point_id,
		MAX(p.start_physical_point_name) as start_physical_point_name,
		p.end_point_id,
		MAX(p.end_point_name) as end_point_name,
		p.end_physical_point_id,
		MAX(p.end_physical_point_name) as end_physical_point_name,
		<choose>
			<when test="cdcFlag">
				p.departure_valid_name,
			</when>
			<otherwise>
				p.arrival_date,
				p.arrival_valid_name,
			</otherwise>
		</choose>
		p.shipping_type_group_id,
		MAX(p.shipping_type_group_name) as shipping_type_group_name,
		SUM(p.act_qty) as act_qty,
		MAX(p.task_detail_id) as task_detail_id,
		min(abc.abc_type) as abc_type
		FROM tdm_kcjh_txn_freight_qty_di p left join (select  sku_code,min(abc_type) as abc_type
		from
		tdm_xqyc_sku_abc_df   group by sku_code) abc on p.item_id = abc.sku_code
		<include refid="where_sql_p"/>
		and 1=1
		and p.act_qty >0
		GROUP BY item_id, start_point_id,start_physical_point_id, end_point_id,end_physical_point_id, shipping_type_group_id,
		<choose>
			<when test="cdcFlag">
				departure_valid_name
			</when>
			<otherwise>
				arrival_date,
				arrival_valid_name
			</otherwise>
		</choose>
		ORDER BY item_id, start_point_id, end_point_id,
		<choose>
			<when test="cdcFlag">
				departure_valid_name
			</when>
			<otherwise>
				arrival_valid_name
			</otherwise>
		</choose>
		, shipping_type_group_id
	</select>

	<select id="queryFreightPlanDataList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto">
		SELECT
			SUM(act_qty::numeric) AS act_qty,
			MAX(task_detail_id) as task_detail_id,
			MAX (arrival_date) AS arrival_date,
			MIN(production_date) as production_date_start,
			MAX(production_date) as production_date_end,
			${groupColumn}
		FROM tdm_kcjh_txn_freight_qty_di
		<include refid="where_sql"/>
		GROUP BY ${groupColumn}
        ORDER BY ${sortColumn}
	</select>
	<sql id="where_sql_p">
		WHERE 1 = 1
		<if test="demandPlanCode != null and demandPlanCode != '' ">
			AND p.demand_plan_code = #{demandPlanCode,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</if>
		<if test="predictionVersion != null and predictionVersion != ''">
			AND p.prediction_version = #{predictionVersion}
		</if>
		AND deleted = '0'
		<if test="itemIds != null and itemIds != ''">
			AND p.item_id = ANY(STRING_TO_ARRAY(#{itemIds},','))
		</if>
		<if test="itemId != null and itemId != ''">
			AND p.item_id = #{itemId}
		</if>
		<if test="startPointIds != null and startPointIds != ''">
			AND p.start_point_id = ANY(STRING_TO_ARRAY(#{startPointIds},','))
		</if>
		<if test="startPointId != null and startPointId != ''">
			AND p.start_point_id = #{startPointId}
		</if>
		<if test="startPhysicalPointIds != null and startPhysicalPointIds != ''">
			AND p.start_physical_point_id = ANY(STRING_TO_ARRAY(#{startPhysicalPointIds},','))
		</if>
		<if test="startPhysicalPointId != null and startPhysicalPointId != ''">
			AND p.start_physical_point_id = #{startPhysicalPointId}
		</if>
		<if test="endPointIds != null and endPointIds != ''">
			AND p.end_point_id = ANY(STRING_TO_ARRAY(#{endPointIds},','))
		</if>
		<if test="endPointId != null and endPointId != ''">
			AND p.end_point_id = #{endPointId}
		</if>
		<if test="endPhysicalPointIds != null and endPhysicalPointIds != ''">
			AND p.end_physical_point_id = ANY(STRING_TO_ARRAY(#{endPhysicalPointIds},','))
		</if>
		<if test="endPhysicalPointId != null and endPhysicalPointId != ''">
			AND p.end_physical_point_id = #{endPhysicalPointId}
		</if>
		<if test="shippingTypeGroupIds != null and shippingTypeGroupIds != ''">
			AND p.shipping_type_group_id = ANY(STRING_TO_ARRAY(#{shippingTypeGroupIds},','))
		</if>
		<if test="shippingTypeGroupId != null and shippingTypeGroupId != ''">
			AND p.shipping_type_group_id = #{shippingTypeGroupId}
		</if>
		<choose>
			<when test="cdcFlag">
				<if test="departureValidName != null and departureValidName != ''">
					AND p.departure_valid_name = #{departureValidName}
				</if>
				<if test="departureValidNames != null and departureValidNames != ''">
					AND p.departure_valid_name = ANY(STRING_TO_ARRAY(#{departureValidNames},','))
				</if>
			</when>
			<otherwise>
				<if test="arrivalValidName != null and arrivalValidName != ''">
					AND p.arrival_valid_name = #{arrivalValidName}
				</if>
				<if test="arrivalValidNames != null and arrivalValidNames != ''">
					AND p.arrival_valid_name = ANY(STRING_TO_ARRAY(#{arrivalValidNames},','))
				</if>
			</otherwise>
		</choose>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
				<if test="item.itemId != null and item.itemId != ''">
					AND p.item_id = #{item.itemId}
				</if>
				<if test="item.startPointId != null and item.startPointId != ''">
					AND p.start_point_id = #{item.startPointId}
				</if>
				<if test="item.endPointId != null and item.endPointId != ''">
					AND p.end_point_id = #{item.endPointId}
				</if>
				<if test="item.shippingTypeGroupId != null and item.shippingTypeGroupId != ''">
					AND p.shipping_type_group_id = #{item.shippingTypeGroupId}
				</if>
				)
			</foreach>
		</if>
	</sql>
	<sql id="where_sql">
		WHERE 1 = 1
		<if test="demandPlanCode != null and demandPlanCode != '' ">
			AND demand_plan_code = #{demandPlanCode,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</if>
		<if test="predictionVersion != null and predictionVersion != ''">
			AND prediction_version = #{predictionVersion}
		</if>
            AND deleted = '0'
		<if test="itemIds != null and itemIds != ''">
			AND item_id = ANY(STRING_TO_ARRAY(#{itemIds},','))
		</if>
		<if test="itemId != null and itemId != ''">
			AND item_id = #{itemId}
		</if>
        <if test="startPointIds != null and startPointIds != ''">
            AND start_point_id = ANY(STRING_TO_ARRAY(#{startPointIds},','))
        </if>
        <if test="startPointId != null and startPointId != ''">
            AND start_point_id = #{startPointId}
        </if>
        <if test="startPhysicalPointIds != null and startPhysicalPointIds != ''">
            AND start_physical_point_id = ANY(STRING_TO_ARRAY(#{startPhysicalPointIds},','))
        </if>
        <if test="startPhysicalPointId != null and startPhysicalPointId != ''">
            AND start_physical_point_id = #{startPhysicalPointId}
        </if>
        <if test="endPointIds != null and endPointIds != ''">
            AND end_point_id = ANY(STRING_TO_ARRAY(#{endPointIds},','))
        </if>
        <if test="endPointId != null and endPointId != ''">
            AND end_point_id = #{endPointId}
        </if>
        <if test="endPhysicalPointIds != null and endPhysicalPointIds != ''">
            AND end_physical_point_id = ANY(STRING_TO_ARRAY(#{endPhysicalPointIds},','))
        </if>
        <if test="endPhysicalPointId != null and endPhysicalPointId != ''">
            AND end_physical_point_id = #{endPhysicalPointId}
        </if>
        <if test="shippingTypeGroupIds != null and shippingTypeGroupIds != ''">
            AND shipping_type_group_id = ANY(STRING_TO_ARRAY(#{shippingTypeGroupIds},','))
        </if>
        <if test="shippingTypeGroupId != null and shippingTypeGroupId != ''">
            AND shipping_type_group_id = #{shippingTypeGroupId}
        </if>
		<choose>
			<when test="cdcFlag">
				<if test="departureValidName != null and departureValidName != ''">
					AND departure_valid_name = #{departureValidName}
				</if>
				<if test="departureValidNames != null and departureValidNames != ''">
					AND departure_valid_name = ANY(STRING_TO_ARRAY(#{departureValidNames},','))
				</if>
			</when>
			<otherwise>
				<if test="arrivalValidName != null and arrivalValidName != ''">
					AND arrival_valid_name = #{arrivalValidName}
				</if>
				<if test="arrivalValidNames != null and arrivalValidNames != ''">
					AND arrival_valid_name = ANY(STRING_TO_ARRAY(#{arrivalValidNames},','))
				</if>
			</otherwise>
		</choose>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
				<if test="item.itemId != null and item.itemId != ''">
					AND item_id = #{item.itemId}
				</if>
				<if test="item.startPointId != null and item.startPointId != ''">
					AND start_point_id = #{item.startPointId}
				</if>
				<if test="item.endPointId != null and item.endPointId != ''">
					AND end_point_id = #{item.endPointId}
				</if>
				<if test="item.shippingTypeGroupId != null and item.shippingTypeGroupId != ''">
					AND shipping_type_group_id = #{item.shippingTypeGroupId}
				</if>
				)
			</foreach>
		</if>
	</sql>

	<select id="queryFreightPlanVersion" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto">
		select prediction_version, algo_name_and_version, demand_plan_code, demand_plan_name, version_id,task_detail_id
		from tdm_kcjh_txn_freight_qty_di
		where demand_plan_code = #{demandPlanCode,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		  and prediction_version = #{predictionVersion}
		LIMIT 1
	</select>

	<update id="saveFreightPlanData" parameterType="java.util.List">
		<foreach collection="list" item="item" separator=";">
			update tdm_kcjh_txn_freight_qty_di
			   set act_qty = #{item.actQty}
			 where demand_plan_code=#{item.demandPlanCode,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
			   and prediction_version=#{item.predictionVersion}
			   and item_id=#{item.itemId}
			   and start_point_id=#{item.startPointId}
			   and end_point_id=#{item.endPointId}
			   and start_physical_point_id=#{item.startPhysicalPointId}
			   and shipping_type_group_id=#{item.shippingTypeGroupId}
			   and arrival_date=#{item.arrivalDate}
		</foreach>
	</update>

	<select id="queryProductionFreightList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseStockAnalyDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseStockAnalyDto">
		SELECT
        	json_agg(json_build_object('planValue', production_num::varchar, 'planDate',in_warehouse_date)) AS "data"
		FROM tdm_txn_warehouse_stock_analy_df
		WHERE 1=1
		  AND biz_date_type='DAY'
		<if test="dateList != null and dateList.size>0">
			AND in_warehouse_date IN
			<foreach collection="dateList" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="skuCode != null and skuCode != ''">
			AND sku_code = #{skuCode}
		</if>
		<if test="warehouseCode != null and warehouseCode != ''">
			AND warehouse_code = #{warehouseCode}
		</if>
		<if test="validName != null and validName != ''">
			AND valid_name = #{validName}
		</if>
		GROUP BY in_warehouse_date
	</select>

	<select id="queryOnTransInventoryList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseStockAnalyDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseStockAnalyDto">
		SELECT
        	json_agg(json_build_object('planValue', on_transit_num::varchar, 'planDate',in_warehouse_date)) AS "data"
		<if test="groupColumn != null and groupColumn != ''">
			,${groupColumn}
		</if>
		FROM tdm_txn_warehouse_stock_analy_df
		WHERE 1=1
		  AND lv1_type_code != '1967276667'
		  AND biz_date_type='DAY'
		<if test="dateList != null and dateList.size>0">
			AND in_warehouse_date IN
			<foreach collection="dateList" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="skuCode != null and skuCode != ''">
			AND sku_code = #{skuCode}
		</if>
		<if test="warehouseCode != null and warehouseCode != ''">
			AND warehouse_code = #{warehouseCode}
		</if>
		<if test="validName != null and validName != ''">
			AND valid_name = #{validName}
		</if>
		GROUP BY in_warehouse_date
		<if test="groupColumn != null and groupColumn != ''">
			,${groupColumn}
		</if>
	</select>

	<select id="queryInventoryInferenceList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryInferenceDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryInferenceDto">
		SELECT
		<choose>
			<when test="skuCode != null and skuCode != ''">
				a.sku_code,
			</when>
			<otherwise>
				'' AS "skuCode",
			</otherwise>
		</choose>
		MAX(a.sku_name) as "skuName",
		a.warehouse_code,
		MAX(a.warehouse_name) as "warehouseName",
		MAX(a.ds) AS "ds",
		SUM(a.oi_0) AS "oi0",
		SUM(a.ei_0) AS "ei0",
		SUM(a.oi_1) AS "oi1",
		SUM(a.ei_1) AS "ei1",
		SUM(a.oi_2) AS "oi2",
		SUM(a.ei_2) AS "ei2",
		SUM(a.oi_3) AS "oi3",
		SUM(a.ei_3) AS "ei3",
		SUM(a.oi_4) AS "oi4",
		SUM(a.ei_4) AS "ei4",
		SUM(a.oi_5) AS "oi5",
		SUM(a.ei_5) AS "ei5",
		SUM(a.oi_6) AS "oi6",
		SUM(a.ei_6) AS "ei6",
		SUM(a.oi_7) AS "oi7",
		SUM(a.ei_7) AS "ei7",
		SUM(a.oi_8) AS "oi8",
		SUM(a.ei_8) AS "ei8",
		SUM(a.oi_9) AS "oi9",
		SUM(a.ei_9) AS "ei9",
		SUM(a.oi_10) AS "oi10",
		SUM(a.ei_10) AS "ei10",
		SUM(a.oi_11) AS "oi11",
		SUM(a.ei_11) AS "ei11",
		SUM(a.oi_12) AS "oi12",
		SUM(a.ei_12) AS "ei12",
		SUM(a.oi_13) AS "oi13",
		SUM(a.ei_13) AS "ei13"
		FROM v_inventory_inference a
		WHERE 1=1
		<if test="skuCode != null and skuCode != ''">
		  AND a.sku_code = #{skuCode}
		</if>
		<if test="warehouseCode != null and warehouseCode != ''">
		  AND a.warehouse_code = #{warehouseCode}
		</if>
		<choose>
			<when test="skuCode != null and skuCode != ''">
				GROUP BY a.sku_code,a.warehouse_code
				ORDER BY a.sku_code,a.warehouse_code
			</when>
			<otherwise>
				GROUP BY a.warehouse_code
				ORDER BY a.warehouse_code
			</otherwise>
		</choose>
	</select>

	<select id="queryWarehouseAvgAllocation" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryInferenceDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryInferenceDto">
		select
			<choose>
				<when test="skuCode != null and skuCode != ''">
					a.sku_code,
				</when>
				<otherwise>
					'' AS "skuCode",
				</otherwise>
			</choose>
			a.warehouse_code,
			coalesce(sum(a.avg_allocation), 0) as "avgAllocation"
		from
		v_daily_warehouse_aiplan_demand_avg_allocation a
		where 1=1
		<if test="skuCode != null and skuCode != ''">
		  AND a.sku_code = #{skuCode}
		</if>
		<if test="warehouseCode != null and warehouseCode != ''">
		  AND a.warehouse_code = #{warehouseCode}
		</if>
		<choose>
			<when test="skuCode != null and skuCode != ''">
				GROUP BY a.sku_code,a.warehouse_code
				ORDER BY a.sku_code,a.warehouse_code
			</when>
			<otherwise>
				GROUP BY a.warehouse_code
				ORDER BY a.warehouse_code
			</otherwise>
		</choose>
	</select>

	<delete id="deleteFreightPlan" parameterType="java.lang.String">
		delete from tdm_kcjh_txn_freight_qty_di where demand_plan_code=#{demandPlanCode,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<select id="queryStockFluctuateList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.StockFluctuateDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.StockFluctuateDto">
		select json_agg(t."data") as data,t.physical_point_id,t.physical_point_name
		from
		(
			select json_build_object('stockDate',date,'availableNum',sum(qty),'tailNum',sum(tail_qty)) as "data",
			date,physical_point_id,physical_point_name
			from tdm_kcjh_txn_stock_fluctuate_di
			<include refid="stock_fluctuate_where_sql"/>
			group by date,physical_point_id,physical_point_name
		) t left join dim_bas_warehouse_info_df wh on t.physical_point_id =  wh.biz_warehouse_code
		where  wh.lv1_type_code != '1967276667'
		group by t.physical_point_id,t.physical_point_name
	</select>

	<select id="queryStockFluctuateListGroupBy" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.StockFluctuateDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.StockFluctuateDto">
		select
		sum(qty) as qty,
		sum(tail_qty) as tail_qty,
		${groupColumn}
		from tdm_kcjh_txn_stock_fluctuate_di
		<include refid="stock_fluctuate_where_sql"/>
		group by ${groupColumn}
	</select>

	<sql id="stock_fluctuate_where_sql">
		where 1=1
		<if test="taskDetailId != null and taskDetailId != ''">
			and task_detail_id = #{taskDetailId}
		</if>
		<if test="itemId != null and itemId != ''">
			and item_id = #{itemId}
		</if>
		<if test="itemIds != null and itemIds != ''">
			and item_id = any(string_to_array(#{itemIds},','))
		</if>
		<if test="stockPointId != null and stockPointId != ''">
			and stock_point_id = #{stockPointId}
		</if>
		<if test="stockPointIds != null and stockPointIds != ''">
			and stock_point_id = any(string_to_array(#{stockPointIds},','))
		</if>
		<if test="physicalPointId != null and physicalPointId != ''">
			and physical_point_id = #{physicalPointId}
		</if>
		<if test="physicalPointIds != null and physicalPointIds != ''">
			and physical_point_id = any(string_to_array(#{physicalPointIds},','))
		</if>
		<if test="validName != null and validName != ''">
			and valid_name = #{validName}
		</if>
	</sql>
</mapper>