<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.distribution.dataqdao.DataqSkuAbcDao">

	<select id="querySkuAbcList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.SkuAbcDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.SkuAbcDto">
		select
			sku_code,
			sku_name,
			source_order_type,
			abc_type,
			ds
		from
			tdm_xqyc_sku_abc_df
		WHERE 1 = 1
		<if test="skuCode != null and skuCode != ''">
			AND sku_code = #{skuCode}
		</if>
		<if test="sourceOrderType != null and sourceOrderType != ''">
			AND source_order_type = #{sourceOrderType}
		</if>
	</select>
</mapper>