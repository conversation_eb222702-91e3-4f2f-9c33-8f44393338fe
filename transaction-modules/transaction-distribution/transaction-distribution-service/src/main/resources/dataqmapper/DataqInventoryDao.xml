<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.distribution.dataqdao.DataqInventoryDao">
	<select id="queryInventoryList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyConditionVo"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo">
		select
		json_agg(
				json_build_object(
				'nowDate', now_date,
				'safetyDays', safety_days,
				'safetyQty',safety_qty,
				'targetDays',target_days,
				'targetQty',target_qty
				)) AS "data"
			, warehouse_code, warehouse_name
		from
		<foreach collection="nowDateList" item="item" open="(" close=") tab" separator="union all">
			SELECT id, sku_code, sku_name, warehouse_code, warehouse_name
				, safety_days_adv, safety_qty_adv, target_days_adv, target_qty_adv, safety_days
				, safety_days * das_num AS "safety_qty", turnover_days
				, turnover_days * das_num AS "das_qty", qc_days
				, qc_days * das_num AS "qc_qty", target_days
				, target_days * das_num AS "target_qty", das_num
				, #{item} as "now_date"
			FROM
				(
			SELECT sw.sku_code, sw.sku_name, sw.warehouse_code, sw.warehouse_name
				, CASE
					WHEN c1.safety_days IS NOT NULL THEN c1.safety_days
					WHEN c2.safety_days IS NOT NULL THEN c2.safety_days
					WHEN a.safety_days IS NOT NULL THEN a.safety_days
					ELSE 0
				END AS "safety_days"
				, CASE
					WHEN c1.turnover_days IS NOT NULL THEN c1.turnover_days
					WHEN c2.turnover_days IS NOT NULL THEN c2.turnover_days
					ELSE 7
				END AS "turnover_days", 11 AS "qc_days"
				, CASE
					WHEN c1.target_days IS NOT NULL THEN c1.target_days + 11
					WHEN c2.target_days IS NOT NULL THEN c2.target_days + 11
					WHEN a.safety_days IS NOT NULL THEN a.safety_days + 7 + 11
					ELSE 0 + 7 + 11
				END AS "target_days"
				, CASE
					WHEN c1.id IS NOT NULL THEN c1.id
					WHEN c2.id IS NOT NULL THEN c2.id
					ELSE NULL
				END AS "id"
				, CASE
					WHEN sd.das_num IS NOT NULL THEN sd.das_num
					ELSE 0
				END AS das_num, a.target_qty AS "target_qty_adv", a.target_days AS "target_days_adv", a.safety_qty AS "safety_qty_adv", a.safety_days AS "safety_days_adv"
				FROM (
					(SELECT biz_warehouse_code AS warehouse_code, biz_warehouse_name AS warehouse_name, sku_code, sku_name
						FROM tdm_rltn_warehouse_sku_df
						WHERE 1 = 1
						<if test="skuCodes != null and skuCodes != ''">
							AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
						</if>
						<if test="warehouseCodes != null and warehouseCodes != ''">
							AND biz_warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
						</if>
					) sw
					LEFT JOIN
					(SELECT item_id AS sku_code, item_name, stock_point_id AS warehouse_code, stock_point_name AS warehouse_name, safety_qty
						, safety_days, target_qty, target_days
					FROM tdm_kcjh_txn_graph_kccl_di
					WHERE 1 = 1
					<if test="skuCodes != null and skuCodes != ''">
						AND item_id = ANY(STRING_TO_ARRAY(#{skuCodes},','))
					</if>
					<if test="warehouseCodes != null and warehouseCodes != ''">
						AND stock_point_id = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
					</if>
					) a
					ON sw.sku_code = a.sku_code
					AND sw.warehouse_code = a.warehouse_code
					LEFT JOIN (
						SELECT id, sku_code, warehouse_code, safety_days, turnover_days
							, safety_days + turnover_days AS target_days
						FROM tdm_kcjh_txn_graph_kccl_config_di
						WHERE 1 = 1
						<if test="skuCodes != null and skuCodes != ''">
							AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
						</if>
						<if test="warehouseCodes != null and warehouseCodes != ''">
							AND warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
						</if>
							AND eff_end_time &gt;= #{item}::date
							AND eff_start_time &lt;= #{item}::date
							AND is_custom = 1
					) c1
					ON sw.sku_code = c1.sku_code
						AND sw.warehouse_code = c1.warehouse_code
					LEFT JOIN (
						SELECT id, sku_code, warehouse_code, safety_days, turnover_days
							, safety_days + turnover_days AS target_days
						FROM tdm_kcjh_txn_graph_kccl_config_di
						WHERE 1 = 1
						<if test="skuCodes != null and skuCodes != ''">
							AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
						</if>
						<if test="warehouseCodes != null and warehouseCodes != ''">
							AND warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
						</if>
							AND is_custom = 0
					) c2
					ON sw.sku_code = c2.sku_code
						AND sw.warehouse_code = c2.warehouse_code
					LEFT JOIN (
						SELECT sku_code, warehouse_code, das_num
						FROM tdm_kcjh_txn_graph_daily_sales_di
						WHERE 1 = 1
						<if test="skuCodes != null and skuCodes != ''">
							AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
						</if>
						<if test="warehouseCodes != null and warehouseCodes != ''">
							AND warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
						</if>
					) sd
					ON sd.sku_code = sw.sku_code
						AND sd.warehouse_code = sw.warehouse_code)
			) t
		</foreach>
			GROUP BY warehouse_code, warehouse_name
			ORDER BY warehouse_code
	</select>

	<select id="querySalesVolumeList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.SalesVolume"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.SalesVolume">
		select
			biz_date_value as "bizDateValue",
			sku_code as "skuCode",
			sku_name as "skuName",
			biz_warehouse_code as "warehouseCode",
			biz_warehouse_name as "warehouseName",
			outbound_num as "outboundNum",
			num_unit as "numUnit"
		from
			tdm_kcjh_txn_delivery_order_df
		where
			1 = 1
		<if test="bizDateType != null and bizDateType != ''">
			and biz_date_type = #{bizDateType}
		</if>
		<if test="skuCode != null and skuCode != ''">
			and sku_code = #{skuCode}
		</if>
		<if test="warehouseCode != null and warehouseCode != ''">
			and warehouse_code = #{warehouseCode}
		</if>
		<if test="skuCodes != null and skuCodes != ''">
			and sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="warehouseCodes != null and warehouseCodes != ''">
			and warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
		</if>
		<if test="startDate != null and startDate != ''">
			and biz_date_value &gt;= replace(#{startDate}, '-', '')
		</if>
		<if test="endDate != null and endDate != ''">
			and biz_date_value &lt;= replace(#{endDate}, '-', '')
		</if>
	</select>

	<delete id="deleteGraphKcclConfig" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.GraphKcclConfigDto">
		delete from tdm_kcjh_txn_graph_kccl_config_di
		where sku_code = #{skuCode}
		  and warehouse_code = #{warehouseCode}
	</delete>

	<insert id="addGraphKcclConfigList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.GraphKcclConfigDto">
		insert into tdm_kcjh_txn_graph_kccl_config_di
		(
			creator,
			last_modifier,
			gmt_create,
			gmt_modify,
			sku_code,
			warehouse_code,
			safety_days,
			turnover_days,
			remark,
			eff_start_time,
			eff_end_time,
			is_custom
		)
		values
		<foreach collection="list" item="item" separator=",">
			(
				#{item.creator},
				#{item.lastModifier},
				#{item.gmtCreate},
				#{item.gmtModify},
				#{item.skuCode},
				#{item.warehouseCode},
				#{item.safetyDays},
				#{item.turnoverDays},
				#{item.remark},
				<choose>
					<when test="item.effStartTime != null and item.effStartTime != ''">
						to_date(#{item.effStartTime},'yyyy-MM'),
					</when>
					<otherwise>
						to_date('1970-01-01','yyyy-MM'),
					</otherwise>
				</choose>
				<choose>
					<when test="item.effEndTime != null and item.effEndTime != ''">
						to_date(#{item.effEndTime},'yyyy-MM'),
					</when>
					<otherwise>
						to_date('2099-12-31','yyyy-MM'),
					</otherwise>
				</choose>
				#{item.isCustom}
			)
		</foreach>
	</insert>

	<select id="queryGraphKcclConfigDetail" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.GraphKcclConfigDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.GraphKcclConfigDto">
		select
			id,
			creator,
			last_modifier,
			gmt_create,
			gmt_modify,
			sku_code,
			warehouse_code,
			safety_days,
			turnover_days,
			remark,
			eff_start_time,
			eff_end_time,
			is_custom
		from
			tdm_kcjh_txn_graph_kccl_config_di
		where sku_code = #{skuCode}
		  and warehouse_code = #{warehouseCode}
	</select>
</mapper>