<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao">

    <update id="createDailyWarehousePartitionTable" parameterType="java.util.List">
        <foreach collection="partitionList" item="item" separator=";">
			create table if not exists t_ryytn_daily_warehouse_demand_${item} (like t_ryytn_daily_warehouse_demand including all, CHECK (substr(demand_plan_version,3,6)='${item}')) inherits
			(t_ryytn_daily_warehouse_demand);
			CREATE OR REPLACE RULE rule_insert_daily_warehouse_demand_${item} AS
			ON INSERT TO t_ryytn_daily_warehouse_demand WHERE ( substr(demand_plan_version,3,6)='${item}')
			DO INSTEAD INSERT INTO t_ryytn_daily_warehouse_demand_${item} VALUES (NEW.*);
		</foreach>
    </update>

    <update id="createDailyWarehouseAiplanPartitionTable" parameterType="java.util.List">
        <foreach collection="partitionList" item="item" separator=";">
			create table if not exists t_ryytn_daily_warehouse_aiplan_demand_${item} (like t_ryytn_daily_warehouse_aiplan_demand including all, CHECK
            (substr(demand_plan_version,3,6)='${item}')) inherits
			(t_ryytn_daily_warehouse_aiplan_demand);
			CREATE OR REPLACE RULE rule_insert_daily_warehouse_aiplan_demand_${item} AS
			ON INSERT TO t_ryytn_daily_warehouse_aiplan_demand WHERE ( substr(demand_plan_version,3,6)='${item}')
			DO INSTEAD INSERT INTO t_ryytn_daily_warehouse_aiplan_demand_${item} VALUES (NEW.*);
		</foreach>
    </update>

    <select id="queryDailyWarehouseDemandPlanExists" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo"
            resultType="java.lang.Integer">
        select count(1)
        from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_demand
            </otherwise>
        </choose>
        WHERE 1 = 1
            AND demand_plan_code = #{demandPlanCode}
            AND demand_plan_version = #{demandPlanVersion}
    </select>
    
    <insert id="batchAddDailyWarehouseDemandList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto">
        INSERT INTO
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_demand
            </otherwise>
        </choose>
            (
            id,
            demand_plan_name,
            demand_plan_version,
            sku_code,
            sku_name,
            distribute_type,
            warehouse_code,
            warehouse_name,
            lv1_category_code,
            lv1_category_name,
            lv2_category_code,
            lv2_category_name,
            lv3_category_code,
            lv3_category_name,
            date_recorded,
            date_value,
            week_recorded,
            week_raw_value,
            week_actual_value,
            demand_plan_code,
            status,
            plan_data_type
            )
        VALUES
            (
            #{id},
            #{demandPlanName},
            #{demandPlanVersion},
            #{skuCode},
            #{skuName},
            #{distributeType},
            #{warehouseCode},
            #{warehouseName},
            #{lv1CategoryCode},
            #{lv1CategoryName},
            #{lv2CategoryCode},
            #{lv2CategoryName},
            #{lv3CategoryCode},
            #{lv3CategoryName},
            #{dateRecorded},
            #{dateValue},
            #{weekRecorded},
            #{weekRawValue},
            #{weekActualValue},
            #{demandPlanCode},
            #{status},
            #{planDataType}
            )
    </insert>

    <insert id="addDailyWarehouseDemandList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto">
        INSERT INTO t_ryytn_daily_warehouse_demand
            (
            id,
            demand_plan_name,
            demand_plan_version,
            sku_code,
            sku_name,
            distribute_type,
            warehouse_code,
            warehouse_name,
            lv1_category_code,
            lv1_category_name,
            lv2_category_code,
            lv2_category_name,
            lv3_category_code,
            lv3_category_name,
            date_recorded,
            date_value,
            week_recorded,
            week_raw_value,
            week_actual_value,
            demand_plan_code,
            status,
            plan_data_type
            )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.demandPlanName},
            #{item.demandPlanVersion},
            #{item.skuCode},
            #{item.skuName},
            #{item.distributeType},
            #{item.warehouseCode},
            #{item.warehouseName},
            #{item.lv1CategoryCode},
            #{item.lv1CategoryName},
            #{item.lv2CategoryCode},
            #{item.lv2CategoryName},
            #{item.lv3CategoryCode},
            #{item.lv3CategoryName},
            #{item.dateRecorded},
            #{item.dateValue},
            #{item.weekRecorded},
            #{item.weekRawValue},
            #{item.weekActualValue},
            #{item.demandPlanCode},
            #{item.status},
            #{item.planDataType}
            )
        </foreach>
    </insert>

    <select id="queryDailyWarehouseDemandPlanList" resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto">
        select distinct demand_plan_code,demand_plan_name from t_ryytn_daily_warehouse_demand
        where status = 1
        order by demand_plan_code desc
    </select>

    <select id="queryRefreshDailyDemandPlanList" resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto">
        select
        d.demand_plan_code,
        max(d.demand_plan_version) as demand_plan_version
        from
        (
            select
            demand_plan_code,
            demand_plan_version,
            min(date_recorded) as startDate,
            max(date_recorded) as endDate
            from
            t_ryytn_daily_warehouse_demand
            where demand_plan_code  in (
            SELECT demand_plan_code
            FROM cdop_biz.tdm_xqyc_txn_demand_config_info_di
            WHERE status <![CDATA[<>]]>-1)
            group by
            demand_plan_code,
            demand_plan_version
            having
            max(date_recorded) <![CDATA[>=]]> to_char(now(),
            'yyyyMMdd')
        ) d group by d.demand_plan_code
    </select>



    <select id="queryDailyWarehouseDemandVersionList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto"
            resultType="java.lang.String">
        select distinct demand_plan_version from t_ryytn_daily_warehouse_demand
        where demand_plan_code = #{demandPlanCode}
          and status = 1
        order by demand_plan_version desc
    </select>

    <select id="queryDailyWarehouseDemandList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto">
        SELECT
            id,
            demand_plan_code,
            demand_plan_name,
            demand_plan_version,
            sku_code,
            sku_name,
            distribute_type,
            warehouse_code,
            warehouse_name,
            lv1_category_code,
            lv1_category_name,
            lv2_category_code,
            lv2_category_name,
            lv3_category_code,
            lv3_category_name,
            date_recorded,
            date_value,
            week_recorded,
            week_raw_value,
            week_actual_value,
            plan_data_type,
            decode(plan_data_type,0,'日销计划量',1,'活动计划量','') as plan_data_type_name
        FROM
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_demand
            </otherwise>
        </choose>
		<include refid="daily_warehouse_demand_where_sql"/>
        ORDER BY
            lv1_category_name,lv2_category_name,lv3_category_name,sku_code,warehouse_name,distribute_type
    </select>

    <select id="queryValidRuleByProduct" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.SkuValidRuleVo">
        SELECT a.distribute_type ,b."name",b.ratio , c.sku_code , b.end_day
        FROM t_ryytn_distribute_plan_valid_rule  a
        left join t_ryytn_distribute_plan_valid_rule_range b
        on a.id = b.rule_id
        left join t_ryytn_distribute_plan_valid_rule_range_product c
        on a.id = c.rule_id
        where a.range_type = 0
          and a.is_default = 1
          and ( (a.start_time <![CDATA[<=]]> current_date and a.end_time <![CDATA[>=]]> current_date) or a.forever_flag = 1)
    </select>

    <select id="queryValidRuleByCategory" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.SkuValidRuleVo">
        SELECT a.distribute_type ,b."name",b.ratio , c.category_code , b.end_day
        FROM t_ryytn_distribute_plan_valid_rule  a
        left join t_ryytn_distribute_plan_valid_rule_range b
        on a.id = b.rule_id
        left join t_ryytn_distribute_plan_valid_rule_range_category c
        on a.id = c.rule_id
        where a.range_type = 1
          and a.is_default = 1
          and ( (a.start_time <![CDATA[<=]]> current_date and a.end_time <![CDATA[>=]]> current_date) or a.forever_flag = 1)
    </select>

    <select id="queryValidGeneralRule" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.SkuValidRuleVo">
        SELECT a.distribute_type ,b."name" ,b.ratio , b.end_day
        FROM t_ryytn_distribute_plan_valid_rule  a
        left join t_ryytn_distribute_plan_valid_rule_range b
        on a.id = b.rule_id
        where a.is_default = 0
          and ( (a.start_time <![CDATA[<=]]> current_date and a.end_time <![CDATA[>=]]> current_date) or a.forever_flag = 1)
    </select>

    <insert id="batchAddDailyWarehouseAiplanDemandList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto">
        INSERT INTO
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_aiplan_demand
            </otherwise>
        </choose>
            (id,
            demand_plan_code,
            demand_plan_name,
            demand_plan_version,
            aiplan_demand_version,
            sku_code,
            sku_name,
            distribute_type,
            warehouse_code,
            warehouse_name,
            lv1_category_code,
            lv1_category_name,
            lv2_category_code,
            lv2_category_name,
            lv3_category_code,
            lv3_category_name,
            validity_period,
            date_recorded,
            date_value,
            status)
        VALUES
            (#{id},
            #{demandPlanCode},
            #{demandPlanName},
            #{demandPlanVersion},
            #{aiplanDemandVersion},
            #{skuCode},
            #{skuName},
            #{distributeType},
            #{warehouseCode},
            #{warehouseName},
            #{lv1CategoryCode},
            #{lv1CategoryName},
            #{lv2CategoryCode},
            #{lv2CategoryName},
            #{lv3CategoryCode},
            #{lv3CategoryName},
            #{validityPeriod},
            #{dateRecorded},
            #{dateValue},
            #{status})
    </insert>

    <insert id="addDailyWarehouseAiplanDemandList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto">
        INSERT INTO t_ryytn_daily_warehouse_aiplan_demand
            (id,
            demand_plan_code,
            demand_plan_name,
            demand_plan_version,
            aiplan_demand_version,
            sku_code,
            sku_name,
            distribute_type,
            warehouse_code,
            warehouse_name,
            lv1_category_code,
            lv1_category_name,
            lv2_category_code,
            lv2_category_name,
            lv3_category_code,
            lv3_category_name,
            validity_period,
            date_recorded,
            date_value,
            status)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
            #{item.demandPlanCode},
            #{item.demandPlanName},
            #{item.demandPlanVersion},
            #{item.aiplanDemandVersion},
            #{item.skuCode},
            #{item.skuName},
            #{item.distributeType},
            #{item.warehouseCode},
            #{item.warehouseName},
            #{item.lv1CategoryCode},
            #{item.lv1CategoryName},
            #{item.lv2CategoryCode},
            #{item.lv2CategoryName},
            #{item.lv3CategoryCode},
            #{item.lv3CategoryName},
            #{item.validityPeriod},
            #{item.dateRecorded},
            #{item.dateValue},
            #{item.status})
        </foreach>
    </insert>

    <select id="queryDailyWarehouseAiplanDemandVersionList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.GenerateDailyWarehouseAiplanDemandVo"
            resultType="java.lang.String">
        select distinct aiplan_demand_version  from t_ryytn_daily_warehouse_aiplan_demand
        where demand_plan_code = #{demandPlanCode}
          and demand_plan_version = #{demandPlanVersion}
          and aiplan_demand_version like concat(#{aiplanDemandVersion},'%')
    </select>

    <select id="countDailyWarehouseAiplanDemandVersion" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.GenerateDailyWarehouseAiplanDemandVo"
            resultType="int">
        select count(distinct aiplan_demand_version)  from t_ryytn_daily_warehouse_aiplan_demand
        where demand_plan_code = #{demandPlanCode}
          and demand_plan_version = #{demandPlanVersion}
    </select>

    <select id="queryDailyWarehouseAiplanDemandList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto">
        SELECT
            id,
            demand_plan_code,
            demand_plan_name,
            demand_plan_version,
            aiplan_demand_version,
            sku_code, sku_name,
            distribute_type,
            warehouse_code,
            warehouse_name,
            lv1_category_code,
            lv1_category_name,
            lv2_category_code,
            lv2_category_name,
            lv3_category_code,
            lv3_category_name,
            validity_period,
            date_recorded,
            date_value
        FROM
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_aiplan_demand
            </otherwise>
        </choose>
        <include refid="daily_warehouse_aiplan_where_sql"/>
        order by sku_code,warehouse_code,date_recorded
    </select>

    <select id="queryDailyWarehouseAiplanDemandPlanList" resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto">
        SELECT distinct demand_plan_code, demand_plan_name FROM t_ryytn_daily_warehouse_aiplan_demand
        WHERE 1=1
          AND status = 1
        order by demand_plan_code desc
    </select>
    
    <select id="queryDailyWarehouseAiplanDemandPlanVersionList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto"
            resultType="java.lang.String">
        SELECT distinct demand_plan_version
        FROM t_ryytn_daily_warehouse_aiplan_demand
        where demand_plan_code = #{demandPlanCode}
          and status = 1
    </select>

    <select id="queryAiPlanDemandVersion" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo"
            resultType="java.lang.String">
        SELECT distinct aiplan_demand_version
        FROM t_ryytn_daily_warehouse_aiplan_demand
        WHERE demand_plan_code = #{demandPlanCode}
          AND demand_plan_version = #{demandPlanVersion}
          AND status = 1
    </select>

    <insert id="batchAddAiplanAlgoList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto">
        INSERT INTO so_wt_demand
            (
            item_id,
            item_name,
            stock_point_id,
            stock_point_name,
            expiry_limit,
            expected_delivery_date,
            qty,
            demand_plan_code,
            demand_plan_name,
            version_id
            )
        VALUES
            (
            #{itemId},
            #{itemName},
            #{stockPointId},
            #{stockPointName},
            #{expiryLimit},
            #{expectedDeliveryDate},
            #{qty},
            #{demandPlanCode,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{demandPlanName},
            #{versionId}
            )
    </insert>

    <insert id="addAiplanAlgoList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto">
        INSERT INTO so_wt_demand
            (
            item_id,
            item_name,
            stock_point_id,
            stock_point_name,
            expiry_limit,
            expected_delivery_date,
            qty,
            demand_plan_code,
            demand_plan_name,
            version_id
            )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.itemId},
            #{item.itemName},
            #{item.stockPointId},
            #{item.stockPointName},
            #{item.expiryLimit},
            #{item.expectedDeliveryDate},
            #{item.qty},
            #{item.demandPlanCode,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{item.demandPlanName},
            #{item.versionId}
            )
        </foreach>
    </insert>

    <select id="queryDailyWarehouseDemandListById" parameterType="java.lang.String"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto">
        SELECT
            id,
            demand_plan_code,
            demand_plan_name,
            demand_plan_version,
            sku_code,
            sku_name,
            distribute_type,
            warehouse_code,
            warehouse_name,
            lv1_category_code,
            lv1_category_name,
            lv2_category_code,
            lv2_category_name,
            lv3_category_code,
            lv3_category_name,
            date_recorded,
            date_value,
            week_recorded,
            week_raw_value,
            week_actual_value
        FROM t_ryytn_daily_warehouse_demand
        WHERE id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="batchUpdateDailyWarehouseWeekData" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto">
        update t_ryytn_daily_warehouse_demand_${subDemandPlanVersion} set week_actual_value=(week_actual_value+#{dateValue})
        where demand_plan_code = #{demandPlanCode}
        and demand_plan_version = #{demandPlanVersion}
        and sku_code = #{skuCode}
        and distribute_type = #{distributeType}
        and warehouse_code = #{warehouseCode}
        and lv1_category_code = #{lv1CategoryCode}
        and lv2_category_code = #{lv2CategoryCode}
        and lv3_category_code = #{lv3CategoryCode}
        and week_recorded = #{weekRecorded}
    </update>

    <update id="updateDailyWarehouseWeekData" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto">
        <foreach collection="list" item="item" separator=";">
            update t_ryytn_daily_warehouse_demand_${item.subDemandPlanVersion} set week_actual_value=(week_actual_value+#{item.dateValue})
            where demand_plan_code = #{item.demandPlanCode}
            and demand_plan_version = #{item.demandPlanVersion}
            and sku_code = #{item.skuCode}
            and distribute_type = #{item.distributeType}
            and warehouse_code = #{item.warehouseCode}
            and lv1_category_code = #{item.lv1CategoryCode}
            and lv2_category_code = #{item.lv2CategoryCode}
            and lv3_category_code = #{item.lv3CategoryCode}
            and week_recorded = #{item.weekRecorded}
        </foreach>
    </update>

    <update id="batchUpdateDailyWarehouseDateData" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseDemandDateVo">
        update t_ryytn_daily_warehouse_demand_${subDemandPlanVersion}
        set date_value = #{dateValue}
        where id = #{id}
    </update>

    <update id="updateDailyWarehouseDateData" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseDemandDateVo">
        <foreach collection="dailyWarehouseDemandDateVos" item="item" separator=";">
            update t_ryytn_daily_warehouse_demand_${subDemandPlanVersion}
            set date_value = #{item.dateValue}
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteDailyWarehouseDemand" parameterType="java.lang.String">
        delete from t_ryytn_daily_warehouse_demand where demand_plan_code=#{demandPlanCode}
    </delete>

    <delete id="deleteDailyWarehouseAiPlanDemand">
        delete from t_ryytn_daily_warehouse_aiplan_demand
        where demand_plan_code=#{demandPlanCode}
    </delete>

    <delete id="deleteAiplanAlgoList" parameterType="java.lang.String">
        delete from so_wt_demand
        where demand_plan_code=#{demandPlanCode,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <select id="querySoWtDemandVersion" resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto">
        select distinct demand_plan_code,demand_plan_name,version_id from so_wt_demand
    </select>

    <select id="querySoWtDemandList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto">
        select distinct demand_plan_code,demand_plan_name,version_id from so_wt_demand
        where 1=1
        <if test="demandPlanCode != null and demandPlanCode != ''">
            and demand_plan_code = #{demandPlanCode,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </if>
        order by version_id desc
    </select>

	<select id="queryDailyWarehouseDemandListGroupBy" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo">
		SELECT
		json_agg("data") AS "data",
		${groupColumn}
		FROM (
			(
			SELECT json_build_object('planValue', SUM(date_value), 'planDate', date_recorded) AS "data"
				, ${groupColumn}
			FROM
            <choose>
                <when test="tableSuffix != null and tableSuffix != ''">
                    t_ryytn_daily_warehouse_demand_${tableSuffix}
                </when>
                <otherwise>
                    t_ryytn_daily_warehouse_demand
                </otherwise>
            </choose>
			<include refid="daily_warehouse_demand_where_sql"/>
			GROUP BY ${groupColumn}, date_recorded
			)
		) agg
		GROUP BY ${groupColumn}
		ORDER BY ${sortColumn}
	</select>

    <select id="queryDailyWarehouseDemandDataKeyList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo">
        select distinct
			lv1_category_code,lv2_category_code,lv3_category_code,sku_code, warehouse_code, distribute_type
		from
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_daily_warehouse_demand_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_daily_warehouse_demand
			</otherwise>
		</choose>
		<include refid="daily_warehouse_demand_where_sql"/>
		ORDER BY lv1_category_code,lv2_category_code,lv3_category_code,sku_code, warehouse_code, distribute_type
    </select>

	<select id="queryDailyWarehouseDemandDataJsonList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo"
            resultType="cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo">
		SELECT
        	json_agg(json_build_object('id', id::varchar, 'planValue', date_value::varchar , 'planDate',
            date_recorded,'planWeek',week_recorded,'weekRawValue',week_raw_value,'weekActualValue',week_actual_value)) AS "data",
			sku_code AS "skuCode",
			MAX(sku_name) AS "skuName",
			lv1_category_code AS "lv1CategoryCode",
			MAX(lv1_category_name) AS "lv1CategoryName",
			lv2_category_code AS "lv2CategoryCode",
			MAX(lv2_category_name) AS "lv2CategoryName",
			lv3_category_code AS "lv3CategoryCode",
			MAX(lv3_category_name) AS "lv3CategoryName",
			distribute_type AS "distributeType",
			warehouse_code AS "warehouseCode",
			MAX(warehouse_name) AS "warehouseName",
            MAX(demand_plan_code) AS "demandPlanCode",
            MAX(demand_plan_name) AS "demandPlanName",
            MAX(demand_plan_version) AS "demandPlanVersion",
            plan_data_type as "planDataType"
		FROM
		<choose>
			<when test="tableSuffix != null and tableSuffix != ''">
				t_ryytn_daily_warehouse_demand_${tableSuffix}
			</when>
			<otherwise>
				t_ryytn_daily_warehouse_demand
			</otherwise>
		</choose>
		<include refid="daily_warehouse_demand_where_sql"/>
		GROUP BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, distribute_type, plan_data_type,warehouse_code
		ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, distribute_type, plan_data_type,warehouse_code
	</select>

	<sql id="daily_warehouse_demand_where_sql">
        WHERE 1 = 1
            AND demand_plan_code = #{demandPlanCode}
            AND demand_plan_version = #{demandPlanVersion}
            AND status = 1
		<if test="skuCodes != null and skuCodes != ''">
			AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="skuCode != null and skuCode != ''">
			AND sku_code = #{skuCode}
		</if>
        <if test="warehouseCodes != null and warehouseCodes != ''">
            AND warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND warehouse_code = #{warehouseCode}
        </if>
        <if test="distributeTypes != null and distributeTypes != ''">
            AND distribute_type::varchar = ANY(STRING_TO_ARRAY(#{distributeTypes},','))
        </if>
        <if test="distributeType != null and distributeType != ''">
            AND distribute_type = #{distributeType}
        </if>
        <if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
            AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
        </if>
        <if test="lv1CategoryCode != null and lv1CategoryCode != ''">
            AND lv1_category_code = #{lv1CategoryCode}
        </if>
        <if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
            AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
        </if>
        <if test="lv2CategoryCode != null and lv2CategoryCode != ''">
            AND lv2_category_code = #{lv2CategoryCode}
        </if>
        <if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
            AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
        </if>
        <if test="planDataType != null and planDataType != ''">
            and plan_data_type = #{planDataType}
        </if>
        <if test="lv3CategoryCode != null and lv3CategoryCode != ''">
            AND lv3_category_code = #{lv3CategoryCode}
        </if>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
                <if test="item.skuCode != null and item.skuCode != ''">
                    AND sku_code = #{item.skuCode}
                </if>
                <if test="item.warehouseCode != null and item.warehouseCode != ''">
                    AND warehouse_code = #{item.warehouseCode}
                </if>
                <if test="item.distributeType != null and item.distributeType != ''">
                    AND distribute_type = #{item.distributeType}
                </if>
				)
			</foreach>
		</if>
	</sql>

	<select id="queryDailyWarehouseAiplanDemandListGroupBy"
            parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseAiplanDemandRspVo">
		SELECT
		json_agg("data") AS "data",
		${groupColumn}
		FROM (
			(
			SELECT json_build_object('planValue', SUM(date_value), 'planDate', date_recorded) AS "data"
				, ${groupColumn}
			FROM
            <choose>
                <when test="tableSuffix != null and tableSuffix != ''">
                    t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
                </when>
                <otherwise>
                    t_ryytn_daily_warehouse_aiplan_demand
                </otherwise>
            </choose>
			<include refid="daily_warehouse_aiplan_where_sql"/>
			GROUP BY ${groupColumn}, date_recorded
			)
		) agg
		GROUP BY ${groupColumn}
		ORDER BY ${sortColumn}
	</select>

    <select id="queryDailyWarehouseAiplanDemandDataKeyList"
            parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto">
        select distinct
			lv1_category_code,lv2_category_code,lv3_category_code,sku_code, warehouse_code, distribute_type,validity_period
		from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_aiplan_demand
            </otherwise>
        </choose>
		<include refid="daily_warehouse_aiplan_where_sql"/>
		ORDER BY lv1_category_code,lv2_category_code,lv3_category_code,sku_code, warehouse_code, distribute_type,validity_period
    </select>

    <select id="queryDailyWarehouseAiplanDemandHeadList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto"
            resultType="java.lang.String">
        select distinct date_recorded
        from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_aiplan_demand
            </otherwise>
        </choose>
		<include refid="daily_warehouse_aiplan_where_sql"/>
		order by date_recorded
    </select>

    <select id="queryDailyWarehouseDemandAiplanHeadSelect" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto">
        select distinct ${groupColumn}
		from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_aiplan_demand
            </otherwise>
        </choose>
		<include refid="daily_warehouse_aiplan_where_sql"/>
		order by ${sortColumn}
    </select>

	<select id="queryDailyWarehouseAiplanDemandDataJsonList"
            parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto">
		SELECT
        	json_agg(json_build_object('id', id::varchar, 'planValue', date_value::varchar , 'planDate', date_recorded)) AS "data",
			sku_code AS "skuCode",
			MAX(sku_name) AS "skuName",
			lv1_category_code AS "lv1CategoryCode",
			MAX(lv1_category_name) AS "lv1CategoryName",
			lv2_category_code AS "lv2CategoryCode",
			MAX(lv2_category_name) AS "lv2CategoryName",
			lv3_category_code AS "lv3CategoryCode",
			MAX(lv3_category_name) AS "lv3CategoryName",
			distribute_type AS "distributeType",
			warehouse_code AS "warehouseCode",
			MAX(warehouse_name) AS "warehouseName",
            validity_period AS "validityPeriod"
		FROM
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_aiplan_demand
            </otherwise>
        </choose>
		<include refid="daily_warehouse_aiplan_where_sql"/>
		GROUP BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, distribute_type, warehouse_code,validity_period
		ORDER BY lv1_category_code, lv2_category_code, lv3_category_code, sku_code, distribute_type, warehouse_code,validity_period
	</select>

	<sql id="daily_warehouse_aiplan_where_sql">
        WHERE 1 = 1
          and demand_plan_code = #{demandPlanCode}
          and demand_plan_version = #{demandPlanVersion}
          and aiplan_demand_version = #{aiplanDemandVersion}
          and status = 1
		<if test="skuCodes != null and skuCodes != ''">
			AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="skuCode != null and skuCode != ''">
			AND sku_code = #{skuCode}
		</if>
        <if test="warehouseCodes != null and warehouseCodes != ''">
            AND warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND warehouse_code = #{warehouseCode}
        </if>
        <if test="distributeTypes != null and distributeTypes != ''">
            AND distribute_type::varchar = ANY(STRING_TO_ARRAY(#{distributeTypes},','))
        </if>
        <if test="distributeType != null and distributeType != ''">
            AND distribute_type = #{distributeType}
        </if>
        <if test="validityPeriods != null and validityPeriods != ''">
            AND validity_period = ANY(STRING_TO_ARRAY(#{validityPeriods},','))
        </if>
        <if test="validityPeriod != null and validityPeriod != ''">
            AND validity_period = #{validityPeriod}
        </if>
        <if test="lv1CategoryCodes != null and lv1CategoryCodes != ''">
            AND lv1_category_code = ANY(STRING_TO_ARRAY(#{lv1CategoryCodes},','))
        </if>
        <if test="lv1CategoryCode != null and lv1CategoryCode != ''">
            AND lv1_category_code = #{lv1CategoryCode}
        </if>
        <if test="lv2CategoryCodes != null and lv2CategoryCodes != ''">
            AND lv2_category_code = ANY(STRING_TO_ARRAY(#{lv2CategoryCodes},','))
        </if>
        <if test="lv2CategoryCode != null and lv2CategoryCode != ''">
            AND lv2_category_code = #{lv2CategoryCode}
        </if>
        <if test="lv3CategoryCodes != null and lv3CategoryCodes != ''">
            AND lv3_category_code = ANY(STRING_TO_ARRAY(#{lv3CategoryCodes},','))
        </if>
        <if test="lv3CategoryCode != null and lv3CategoryCode != ''">
            AND lv3_category_code = #{lv3CategoryCode}
        </if>
        <if test="beginDate != null and beginDate != ''">
            AND date_recorded &gt;= #{beginDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND date_recorded &lt;= #{endDate}
        </if>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
                <if test="item.skuCode != null and item.skuCode != ''">
                    AND sku_code = #{item.skuCode}
                </if>
                <if test="item.warehouseCode != null and item.warehouseCode != ''">
                    AND warehouse_code = #{item.warehouseCode}
                </if>
                <if test="item.distributeType != null and item.distributeType != ''">
                    AND distribute_type = #{item.distributeType}
                </if>
                <if test="item.validityPeriod != null and item.validityPeriod != ''">
                    AND validity_period = #{item.validityPeriod}
                </if>
                <if test="item.lv1CategoryCode != null and item.lv1CategoryCode != ''">
                    AND lv1_category_code = #{item.lv1CategoryCode}
                </if>
                <if test="item.lv2CategoryCode != null and item.lv2CategoryCode != ''">
                    AND lv2_category_code = #{item.lv2CategoryCode}
                </if>
                <if test="item.lv3CategoryCode != null and item.lv3CategoryCode != ''">
                    AND lv3_category_code = #{item.lv3CategoryCode}
                </if>
				)
			</foreach>
		</if>
	</sql>

    <select id="queryDailyWarehouseDemandHeadList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto">
        select distinct date_recorded,week_recorded
        from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_demand
            </otherwise>
        </choose>
		<include refid="daily_warehouse_demand_where_sql"/>
		order by date_recorded,week_recorded
    </select>

    <select id="queryDailyWarehouseDemandHeadSelect" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto">
        select distinct ${groupColumn}
		from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_demand
            </otherwise>
        </choose>
		<include refid="daily_warehouse_demand_where_sql"/>
		order by ${sortColumn}
    </select>

    <select id="querySumGroupByWarehouse" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto">
        select warehouse_code,sum(date_value) as date_value
        from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_aiplan_demand
            </otherwise>
        </choose>
        <include refid="daily_warehouse_aiplan_where_sql"/>
        group by warehouse_code
    </select>

    <select id="querySumGroupByWarehouseProductionSku" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto">
        select
        demand.warehouse_code,
        sku.production_code as sku_code,
        sum(demand.date_value) as date_value
        from
        (
            select
            warehouse_code,
            warehouse_name,
            sku_code,
            date_value
            from
            <choose>
                <when test="tableSuffix != null and tableSuffix != ''">
                    t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
                </when>
                <otherwise>
                    t_ryytn_daily_warehouse_aiplan_demand
                </otherwise>
            </choose>
            <include refid="daily_warehouse_aiplan_where_sql"/>
        )
        demand
        left join cdop_biz.tdm_kcjh_rltn_product_sale_sku_df sku
        on demand.sku_code=sku.sku_code
        group by demand.warehouse_code,sku.production_code
    </select>

    <delete id="deleteDailyWarehouseDemandIniting" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo">
        delete from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_demand
            </otherwise>
        </choose>
        WHERE 1 = 1
          AND demand_plan_code = #{demandPlanCode}
          AND demand_plan_version = #{demandPlanVersion}
          AND status = 2
    </delete>

    <select id="queryDailyWareHouseDateRange" resultType="map">
        select
        max(date_recorded) as endDate,
        min(date_recorded) as startDate
        from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_demand
            </otherwise>
        </choose>
        WHERE  demand_plan_code = #{demandPlanCode}
        AND demand_plan_version = #{demandPlanVersion}
    </select>


    <delete id="deleteDailyWarehouse" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo">
        delete from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_demand
            </otherwise>
        </choose>
        WHERE  demand_plan_code = #{demandPlanCode}
        AND demand_plan_version = #{demandPlanVersion}
    </delete>

    <update id="udpateDailyWarehouseDemandNormal" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo">
        update
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_demand
            </otherwise>
        </choose>
        set status = 1
        WHERE 1 = 1
          AND demand_plan_code = #{demandPlanCode}
          AND demand_plan_version = #{demandPlanVersion}
          AND status = 2
    </update>

    <delete id="deleteDailyWarehouseAiplanDemandIniting" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.GenerateDailyWarehouseAiplanDemandVo">
        delete from
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_aiplan_demand
            </otherwise>
        </choose>
        where demand_plan_code=#{demandPlanCode}
          and demand_plan_version=#{demandPlanVersion}
          and aiplan_demand_version =#{aiplanDemandVersion}
          and status = 2
    </delete>

    <update id="udpateDailyWarehouseAiplanDemandNormal" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.GenerateDailyWarehouseAiplanDemandVo">
        update
        <choose>
            <when test="tableSuffix != null and tableSuffix != ''">
                t_ryytn_daily_warehouse_aiplan_demand_${tableSuffix}
            </when>
            <otherwise>
                t_ryytn_daily_warehouse_aiplan_demand
            </otherwise>
        </choose>
        set status = 1
        WHERE 1 = 1
          AND demand_plan_code = #{demandPlanCode}
          AND demand_plan_version = #{demandPlanVersion}
          and aiplan_demand_version =#{aiplanDemandVersion}
          AND status = 2
    </update>
</mapper>