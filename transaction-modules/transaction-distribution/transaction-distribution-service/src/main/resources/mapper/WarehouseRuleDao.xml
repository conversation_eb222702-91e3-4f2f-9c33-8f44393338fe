<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.distribution.dao.WarehouseRuleDao">

	<select id="countWarehouseRuleName" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleVo" resultType="java.lang.Integer">
        select count(*) from t_ryytn_distribute_plan_warehouse_rule WHERE name=#{name}
        <if test="id != null and id != ''">
			AND id != #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</if>
	</select>

    <!--查询仓能力列表-->
    <select id="queryWarehoueRuleList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseRuleDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseRuleDto">
		SELECT
		  warehouse.id,
		  warehouse.name,
		  warehouse.range_type as rangeType,
		  warehouse.start_time as startTime,
		  warehouse.end_time as endTime,
		  warehouse.forever_flag as foreverFlag,
		  warehouse.created_by as createdBy,
		  warehouse.created_time as createdTime,
		  warehouse.updated_by as updatedBy,
		  warehouse.updated_time as updatedTime,
		  warehouse.is_default as isDefault,
		CASE WHEN start_time <![CDATA[>]]> date(now()) THEN 0
		     WHEN end_time  <![CDATA[<]]> date(now()) THEN 2
		     ELSE 1
		END  AS status,
    	  categoryList.categoryNames,
    	  skuList.skuNames
		FROM
		  t_ryytn_distribute_plan_warehouse_rule warehouse
		   LEFT JOIN (SELECT rule_id,string_agg(category_name,',') categoryNames
		              FROM t_ryytn_distribute_plan_warehouse_rule_capacity_category
		              GROUP BY  rule_id) as categoryList
			 ON warehouse.id=categoryList.rule_id
  		   LEFT JOIN (SELECT rule_id,string_agg(sku_name,',') skuNames
  		              FROM t_ryytn_distribute_plan_warehouse_rule_capacity_product
  		              GROUP BY  rule_id ) as skuList
  		     ON warehouse.id=skuList.rule_id
		<where>
	     <if test="name!=null and name!=''">
	     	AND name like concat('%',#{name},'%')
	     </if>
		</where>
	ORDER BY created_time desc
	</select>

    <!--仓能力规则-->
    <resultMap id="warehouseRuleDetail" type="cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleVo">
		<id property="id" column="id"></id>
		<result column="name" property="name"></result>
		<result column="range_type" property="rangeType"></result>
		<result column="start_time" property="startTime"></result>
		<result column="end_time" property="endTime"></result>
		<result column="forever_flag" property="foreverFlag"></result>
		<result column="created_by" property="createdBy"></result>
		<result column="created_time" property="createdTime"></result>
		<result column="updated_by" property="updatedBy"></result>
		<result column="updated_time" property="updatedTime"></result>
		<result column="is_default" property="isDefault"></result>
		<collection property="capacityList" column="id" javaType="ArrayList"
                    ofType="cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleCapacity" select="selectWarehouseRuleCapacityListById">
			<id column="id" property="id"/>
			<result column="rule_id" property="ruleId"></result>
			<result column="warehouse_code" property="warehouseCode"></result>
			<result column="warehouse_name" property="warehouseName"></result>
			<result column="capacity" property="capacity"></result>
			<result column="capacity_flag" property="capacityFlag"></result>
			<result column="delivery_limit" property="deliveryLimit"></result>
			<result column="delivery_limit_flag" property="deliveryLimitFlag"></result>
			<result column="delivery_unlimit_flag" property="deliveryUnLimitFlag"></result>
			<result column="shipment_limit" property="shipmentLimit"></result>
			<result column="shipment_limit_flag" property="shipmentLimitFlag"></result>
			<result column="shipment_unlimit_flag" property="shipmentUnLimitFlag"></result>
		</collection>
		<collection property="productList" column="id" javaType="ArrayList"
                    ofType="cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct" select="selectWarehouseRuleProductListById">
			<id column="id" property="id"/>
			<result column="rule_id" property="ruleId"></result>
			<result column="sku_code" property="skuCode"></result>
			<result column="sku_name" property="skuName"></result>
		</collection>
		<collection property="categoryList" column="id" javaType="ArrayList"
                    ofType="cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory" select="selectWarehouseRuleCategoryListById">
			<id column="id" property="id"/>
			<result column="rule_id" property="ruleId"></result>
			<result column="category_code" property="categoryCode"></result>
			<result column="category_name" property="categoryName"></result>
			<result column="level" property="level"></result>
			<result column="sku_codes" property="skuCodes"></result>
		</collection>
	</resultMap>
    <!--查询仓能力详情-->
    <select id="queryWarehouseRuleDetail" resultMap="warehouseRuleDetail">
		select
			rule.id,
            rule.name,
            rule.range_type,
            rule.start_time,
            rule.end_time,
            rule.forever_flag,
            rule.created_by,
            rule.created_time,
            rule.updated_by,
            rule.updated_time,
            rule.id rule_id,
		    rule.is_default
        from t_ryytn_distribute_plan_warehouse_rule rule
        where rule.id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>

	<select id="selectWarehouseRuleCapacityListById" parameterType="java.lang.String"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleCapacity">
		select
			range.id,
			warehouse.biz_warehouse_code as warehouse_code,
		    warehouse.biz_warehouse_name as warehouse_name,
		    range.rule_id,
            range.capacity,
            range.capacity_flag,
            range.delivery_limit,
            range.delivery_limit_flag,
            range.delivery_unlimit_flag,
            range.shipment_limit,
            range.shipment_limit_flag,
            range.shipment_unlimit_flag
		from (
			SELECT DISTINCT biz_warehouse_code,biz_warehouse_name
			FROM cdop_biz.dim_bas_warehouse_info_df
			WHERE STATUS = '1'
			AND biz_warehouse_code IS NOT NULL
		) warehouse
        left join t_ryytn_distribute_plan_warehouse_rule_capacity range
			on warehouse.biz_warehouse_code=range.warehouse_code
		where range.rule_id is null
		   or range.rule_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		order by warehouse_code
	</select>

	<select id="selectWarehouseRuleProductListById" parameterType="java.lang.String" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct">
		select
			id,
			rule_id,
			sku_code,
			sku_name
		from
			t_ryytn_distribute_plan_warehouse_rule_capacity_product
		where rule_id=#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		order by sku_code
	</select>

	<select id="selectWarehouseRuleCategoryListById" parameterType="java.lang.String" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory">
		select
			id,
			rule_id,
			category_code,
			category_name,
			"level",
			sku_codes
		from
			t_ryytn_distribute_plan_warehouse_rule_capacity_category
		where rule_id=#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		order by category_code
	</select>

	<delete id="deleteWarehouseRuleById">
		delete
        from t_ryytn_distribute_plan_warehouse_rule
        where id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<delete id="deleteWarehouseRuleRangeByRuleId">
		delete
        from t_ryytn_distribute_plan_warehouse_rule_capacity
        where rule_id = #{ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<delete id="deleteWarehouseRuleCategoryByRuleId">
		delete
        from t_ryytn_distribute_plan_warehouse_rule_capacity_category
        where rule_id = #{ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<delete id="deleteWarehouseRuleProductByRuleId">
		delete
        from t_ryytn_distribute_plan_warehouse_rule_capacity_product
        where rule_id = #{ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<select id="querySkuCodeList" resultType="map">
		select skucodeList.skucodes,
               warehouseList.warehousecodes
        from (select string_agg(product.sku_code, ',') skucodes,
                     product.rule_id
              from t_ryytn_distribute_plan_warehouse_rule_capacity_product product
                       left join t_ryytn_distribute_plan_warehouse_rule rule on
                  product.rule_id = rule.id
              where  rule.end_time <![CDATA[>=]]> date(now())
              group by product.rule_id) skucodeList
                 left join (select string_agg(warehouse_code, ',') warehousecodes,
                                   rule_id
                            from t_ryytn_distribute_plan_warehouse_rule_capacity
                            group by rule_id) warehouseList on
            warehouseList.rule_id = skucodeList.rule_id
	 </select>

	<select id="queryCategorySkuCodeList" resultType="map">
		select category.sku_codes skucodes, warehouse.warehouse_codes warehousecodes
        from t_ryytn_distribute_plan_warehouse_rule_capacity_category category
                 left join t_ryytn_distribute_plan_warehouse_rule rule on category.rule_id = rule.id
                 left join (select string_agg(warehouse_code, ',') warehouse_codes,
                                   rule_id
                            from t_ryytn_distribute_plan_warehouse_rule_capacity
                            group by rule_id) warehouse on warehouse.rule_id = rule.id
        where  rule.end_time <![CDATA[>=]]> date(now())
	</select>

	<update id="updateWarehouseRule" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleVo">
		UPDATE
            t_ryytn_distribute_plan_warehouse_rule
        SET name            = #{name},
            range_type      = #{rangeType},
            start_time      = #{startTime},
            end_time        = #{endTime},
            forever_flag    = #{foreverFlag},
            updated_by      = #{updatedBy},
            updated_time    = #{updatedTime}
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<insert id="insertWarehouseRule" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleVo">
		insert into t_ryytn_distribute_plan_warehouse_rule
        (id,
         name,
         range_type,
         start_time,
         end_time,
         forever_flag,
         created_by,
         created_time)
        VALUES (#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{name},
                #{rangeType},
                #{startTime},
                #{endTime},
                #{foreverFlag},
                #{createdBy},
                #{createdTime})
	</insert>

	<insert id="insertWarehouseRuleRangeList" parameterType="java.util.List">
		INSERT INTO t_ryytn_distribute_plan_warehouse_rule_capacity (
		  id,
		  rule_id,
		  warehouse_code,
		  warehouse_name,
		  capacity,
		  capacity_flag,
		  delivery_limit,
		  delivery_limit_flag,
		  delivery_unlimit_flag,
		  shipment_limit,
		  shipment_limit_flag,
		  shipment_unlimit_flag
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.warehouseCode},
			#{item.warehouseName},
			#{item.capacity},
			#{item.capacityFlag},
			#{item.deliveryLimit},
			#{item.deliveryLimitFlag},
			#{item.deliveryUnLimitFlag},
			#{item.shipmentLimit},
			#{item.shipmentLimitFlag},
			#{item.shipmentUnLimitFlag}
		  )
		</foreach>

	</insert>


	<insert id="insertWarehouseRuleProductList" parameterType="java.util.List">
		INSERT INTO t_ryytn_distribute_plan_warehouse_rule_capacity_product (
		  id,
		  rule_id,
		  sku_code,
		  sku_name
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.skuCode},
			#{item.skuName}
		  )
		</foreach>
	</insert>

	<insert id="insertWarehouseRuleCategoryList" parameterType="java.util.List">
		INSERT INTO t_ryytn_distribute_plan_warehouse_rule_capacity_category (
		  id,
		  rule_id,
		  category_code,
		  category_name,
		  level,
		  sku_codes
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.categoryCode},
			#{item.categoryName},
			#{item.level},
			#{item.skuCodes}
		  )
		</foreach>
	</insert>

	<delete id="deleteStockCapacity">
		delete
        from so_wt_stock_capacity
        where rule_id = #{ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>
	<insert id="insertStockCapacity" parameterType="java.util.List">
		INSERT INTO so_wt_stock_capacity (
				  rule_id,
				  stock_point_id,
				  stock_point_name,
				  item_id,
				  item_name,
				  group_id,
				  type,
		          capacity,
		          remark,
		          status
				)
				VALUES
				<foreach collection="list" item="item" separator=",">
				  (
					#{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
					#{item.stockPointId},
					#{item.stockPointName},
					#{item.itemId},
					#{item.itemName},
					#{item.groupId},
					#{item.type},
					#{item.capacity},
					#{item.remark},
					#{item.status}
				  )
				</foreach>

	</insert>

	<update id="updateStockCapacityStatus">
		update so_wt_stock_capacity set status = 0 where rule_id
          in (
          select rule.id from t_ryytn_distribute_plan_warehouse_rule  rule
          where rule.start_time<![CDATA[>]]>now() or rule.end_time<![CDATA[<]]>now()
			);
		update so_wt_stock_capacity set status = 1 where rule_id
			 in (
			 select rule.id from t_ryytn_distribute_plan_warehouse_rule  rule
			 where rule.start_time<![CDATA[<]]>now() and rule.end_time<![CDATA[>]]>now()
			 );
	</update>

	<select id="queryCapacityByCode" parameterType="String" resultType="Double">
		select capacity from so_wt_stock_capacity where  type= 1 and status = 1
	    <if test="warehouseCode!=null and warehouseCode!=''">
		and stock_point_id =#{warehouseCode}
		</if>
	    <if test="skuCode!=null and skuCode!=''">
		and item_id =#{skuCode}
		</if>
		<if test="skuCode==null or skuCode==''">
		and (item_id ='' or item_id is null)
		</if>
		limit 1
	</select>

	<delete id="deleteSoWtStockCapacity">
		delete from so_wt_stock_capacity where 1=1
	</delete>

    <select id="queryWarehouseCapacityByProduct" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseCapacityVo">
		select a.id,b.warehouse_code ,b.warehouse_name ,b.capacity ,b.capacity_flag ,b.delivery_limit ,b.delivery_limit_flag ,
			b.delivery_unlimit_flag,b.shipment_limit ,b.shipment_limit_flag ,b.shipment_unlimit_flag ,
			c.sku_code ,c.sku_name
		from t_ryytn_distribute_plan_warehouse_rule a
		left join t_ryytn_distribute_plan_warehouse_rule_capacity b
			on a.id = b.rule_id
		left join t_ryytn_distribute_plan_warehouse_rule_capacity_product c
			on a.id = c.rule_id
		where a.range_type =0 and (a.forever_flag=1 or a.start_time <![CDATA[<=]]> date(now()) and a.end_time <![CDATA[>=]]> date(now()))
	</select>

	<select id="queryWarehouseCapacityByCategory" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseCapacityVo">
		select
			a.id,
			b.warehouse_code ,
			max(b.warehouse_name) as warehouse_name,
			max(b.capacity) as capacity ,
			max(b.capacity_flag) as capacity_flag ,
			max(b.delivery_limit) as delivery_limit ,
			max(b.delivery_limit_flag) as delivery_limit_flag ,
			max(b.delivery_unlimit_flag) as delivery_unlimit_flag,
			max(b.shipment_limit) as shipment_limit ,
			max(b.shipment_limit_flag) as shipment_limit_flag ,
			max(b.shipment_unlimit_flag) as shipment_unlimit_flag ,
			string_agg(c.sku_codes,',') as sku_codes
		from t_ryytn_distribute_plan_warehouse_rule a
		left join t_ryytn_distribute_plan_warehouse_rule_capacity b
			on a.id = b.rule_id
		left join t_ryytn_distribute_plan_warehouse_rule_capacity_category c
			on a.id = c.rule_id
		where a.range_type =1 and (a.forever_flag=1 or a.start_time <![CDATA[<=]]> date(now()) and a.end_time <![CDATA[>=]]> date(now()))
		group by a.id,b.warehouse_code
	</select>

	<select id="queryWarehouseCapacityByGeneralRule" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseCapacityVo">
		select a.id,b.warehouse_code ,b.warehouse_name ,b.capacity ,b.capacity_flag ,b.delivery_limit ,b.delivery_limit_flag ,
			b.delivery_unlimit_flag,b.shipment_limit ,b.shipment_limit_flag ,b.shipment_unlimit_flag
		from t_ryytn_distribute_plan_warehouse_rule a
		left join t_ryytn_distribute_plan_warehouse_rule_capacity b
			on a.id = b.rule_id
		where a.range_type =2 and (a.forever_flag=1 or a.start_time <![CDATA[<=]]> date(now()) and a.end_time <![CDATA[>=]]> date(now()))
	</select>

	<insert id="addSoWtStockCapacity" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtStockCapacityDto">
		INSERT INTO so_wt_stock_capacity
			(rule_id,
			stock_point_id,
			stock_point_name,
			item_id,
			item_name,
			"group_id",
			"type",
			capacity,
			remark,
			status)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(#{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.stockPointId},
			#{item.stockPointName},
			#{item.itemId},
			#{item.itemName},
			#{item.groupId},
			#{item.type},
			#{item.capacity},
			#{item.remark},
			#{item.status})
		</foreach>
	</insert>

	<select id="queryAdjustableDaysRuleWarehouse" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.PhysicWarehouseVo">
-- 		SELECT
-- 		biz_warehouse_code as warehouseCode,
-- 		biz_warehouse_name as warehouseName
-- 		FROM cdop_biz.v_warehouse_cdc_factory_list
 	SELECT dim_bas_warehouse_info_df.biz_warehouse_code as warehouseCode,
    	dim_bas_warehouse_info_df.biz_warehouse_name as warehouseName
  	FROM cdop_biz.dim_bas_warehouse_info_df
  		WHERE dim_bas_warehouse_info_df.status::text = '1'::text AND (dim_bas_warehouse_info_df.lv1_type_code::text = ANY (ARRAY['927007654'::character varying::text, '1154980266'::character varying::text,'1967276667'::character varying::text]))
  	GROUP BY dim_bas_warehouse_info_df.biz_warehouse_code, dim_bas_warehouse_info_df.biz_warehouse_name

	</select>

	<select id="queryWarehouseRuleWarehouse" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.PhysicWarehouseVo">
		SELECT
		biz_warehouse_code as warehouseCode,
		biz_warehouse_name as warehouseName
		FROM cdop_biz.v_warehouse_cdc_rdc_factory_list
	</select>

	<select id="queryCapacityBySku" resultType="java.lang.Long">
		SELECT b.capacity
		FROM t_ryytn_distribute_plan_warehouse_rule  a
		left join t_ryytn_distribute_plan_warehouse_rule_capacity b
		on a.id = b.rule_id
		left join t_ryytn_distribute_plan_warehouse_rule_capacity_product c
		on a.id = c.rule_id
		where a.is_default=1
		and ( (a.start_time &lt;= current_date and a.end_time &gt;= current_date) or a.forever_flag = 1)
		and c.sku_code = #{skuCode}
		and b.warehouse_code = #{warehouseCode}
		order by b.rule_id
		limit 1
	</select>

	<select id="queryCapacityByCategory" resultType="java.lang.Long">
		SELECT b.capacity
		FROM t_ryytn_distribute_plan_warehouse_rule  a
		left join t_ryytn_distribute_plan_warehouse_rule_capacity b
		on a.id = b.rule_id
		left join t_ryytn_distribute_plan_warehouse_rule_capacity_category c
		on a.id = c.rule_id
		where a.is_default=1
		and ( (a.start_time &lt;= current_date and a.end_time &gt;= current_date) or a.forever_flag = 1)
		and c.category_code = #{lv3CategoryCode}
		and b.warehouse_code = #{warehouseCode}
		order by b.rule_id
		limit 1
	</select>

	<select id="queryDefaultCapacity" parameterType="java.lang.String" resultType="java.lang.Long">
		SELECT b.capacity
		FROM t_ryytn_distribute_plan_warehouse_rule  a
		left join t_ryytn_distribute_plan_warehouse_rule_capacity b
		on a.id = b.rule_id
		where a.is_default = 0
		and b.warehouse_code = #{warehouseCode}
		limit 1
	</select>
</mapper>