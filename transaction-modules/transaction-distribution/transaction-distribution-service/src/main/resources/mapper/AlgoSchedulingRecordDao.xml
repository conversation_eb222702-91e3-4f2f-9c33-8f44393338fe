<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.distribution.dao.AlgoSchedulingRecordDao">
    <insert id="addAlgoSchedulingRecord" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.AlgoSchedulingRecordDto">
        INSERT INTO t_ryytn_algo_scheduling_record
        (id, algo_name_and_version, start_time, end_time, running_status, scene)
        VALUES(#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}, #{algoNameAndVersion}, #{startTime}, #{endTime}, #{runningStatus}, #{scene});
    </insert>

	<select id="queryAlgoSchedulingRecord" resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.AlgoSchedulingRecordDto">
		SELECT id, algo_name_and_version, start_time, end_time, running_status, scene
		FROM t_ryytn_algo_scheduling_record
		WHERE running_status = 5 and scene in (2,3)
	</select>

    <select id="queryAlgoVersion" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.PageAlgoVersionReqVo"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.PageAlgoVersionRspVo">
        SELECT start_time
		FROM t_ryytn_algo_scheduling_record
		WHERE running_status = 5 and
        algo_name_and_version = #{algoNameAndVersion}
    </select>
</mapper>