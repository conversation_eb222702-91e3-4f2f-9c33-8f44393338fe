<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.distribution.dao.ValidRuleDao">

	<select id="countValidRuleName" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleVo" resultType="java.lang.Integer">
        select count(*) from t_ryytn_distribute_plan_valid_rule WHERE name=#{name}
		and distribute_type = #{distributeType}
        <if test="id != null and id != ''">
			AND id != #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</if>
	</select>


    <!--效期分档规则-->
    <resultMap id="validRuleDetail" type="cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleVo">
		<id property="id" column="id"></id>
		<result property="name" column="name"></result>
		<result column="range_type" property="rangeType"></result>
		<result column="start_time" property="startTime"></result>
		<result column="end_time" property="endTime"></result>
		<result column="forever_flag" property="foreverFlag"></result>
		<result column="distribute_type" property="distributeType"></result>
		<result column="created_by" property="createdBy"></result>
		<result column="created_time" property="createdTime"></result>
		<result column="updated_by" property="updatedBy"></result>
		<result column="updated_time" property="updatedTime"></result>
		<result column="is_default" property="isDefault"></result>
		<collection property="rangeList"
                    ofType="cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleRange">
					<id column="rId" property="id"/>
					<result column="rule_id" property="ruleId"></result>
					<result column="name_r" property="name"></result>
					<result column="start_day" property="startDay"></result>
					<result column="end_day" property="endDay"></result>
					<result column="ratio" property="ratio"></result>
		</collection>
		<collection property="productList"
                    ofType="cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct">
					<id column="pId" property="id"/>
					<result column="rule_id" property="ruleId"></result>
					<result column="sku_code" property="skuCode"></result>
					<result column="sku_name" property="skuName"></result>

		</collection>
		<collection property="categoryList"
                    ofType="cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory">
					<id column="cId" property="id"/>
					<result column="rule_id" property="ruleId"></result>
					<result column="category_code" property="categoryCode"></result>
					<result column="category_name" property="categoryName"></result>
					<result column="level" property="level"></result>
		</collection>
	</resultMap>

    <!--查询效期归档列表-->
    <select id="queryValidRuleList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.ValidRuleDto"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.ValidRuleDto">
		SELECT
		  valid.id,
		  valid.name,
		  valid.range_type as rangeType,
		  valid.start_time as startTime,
		  valid.end_time as endTime,
		  valid.forever_flag as foreverFlag,
		  valid.distribute_type as distributeType,
		  valid.created_by as createdBy,
		  valid.created_time as createdTime,
		  valid.updated_by as updatedBy,
		  valid.updated_time as updatedTime,
		  valid.is_default as isDefault,
		CASE WHEN start_time <![CDATA[>]]> date(now()) THEN 0
		     WHEN end_time  <![CDATA[<]]> date(now()) THEN 2
		     ELSE 1
		END  AS status,
    	  categoryList.categoryNames,
    	  skuList.skuNames
		FROM
		  t_ryytn_distribute_plan_valid_rule valid
		   LEFT JOIN (SELECT rule_id,string_agg(category_name,',') categoryNames
		              FROM t_ryytn_distribute_plan_valid_rule_range_category
		              GROUP BY  rule_id) as categoryList
			 ON valid.id=categoryList.rule_id
  		   LEFT JOIN (SELECT rule_id,string_agg(sku_name,',') skuNames
  		              FROM t_ryytn_distribute_plan_valid_rule_range_product
  		              GROUP BY  rule_id ) as skuList
  		     ON valid.id=skuList.rule_id
		<where>
	     <if test="distributeType!=null">
	     	AND valid.distribute_type = #{distributeType}
	     </if>
	     <if test="name!=null and name!=''">
	     	AND valid like concat('%',#{name},'%')
	     </if>
		</where>
	ORDER BY created_time desc
	</select>

    <!--查询效期归档详情-->
    <select id="queryValidRuleDetail" resultMap="validRuleDetail">
		select valid.id,
			   valid.name,
			   valid.range_type ,
			   valid.start_time ,
			   valid.end_time ,
			   valid.forever_flag ,
			   valid.distribute_type,
			   valid.created_by ,
			   valid.created_time,
			   valid.updated_by ,
			   valid.updated_time,
			   valid.id rule_id,
		       valid.is_default,
			   range.*,
			   range.name name_r,
			   range.id rId,
			   product.*,
			   product.id pId,
			   category.*,
			   category.id cId
        from t_ryytn_distribute_plan_valid_rule valid

        left join t_ryytn_distribute_plan_valid_rule_range range  on valid.id=range.rule_id
		left   JOIN  t_ryytn_distribute_plan_valid_rule_range_product product on valid.id=product.rule_id
		left JOIN  t_ryytn_distribute_plan_valid_rule_range_category category on valid.id=category.rule_id

        where valid.id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>

	<delete id="deleteValidRuleById">
		delete
        from t_ryytn_distribute_plan_valid_rule
        where id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<delete id="deleteValidRuleRangeByRuleId">
		delete
        from t_ryytn_distribute_plan_valid_rule_range
        where rule_id = #{ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<delete id="deleteValidRuleCategoryByRuleId">
		delete
        from t_ryytn_distribute_plan_valid_rule_range_category
        where rule_id = #{ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<delete id="deleteValidRuleProductByRuleId">
		delete
        from t_ryytn_distribute_plan_valid_rule_range_product
        where rule_id = #{ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<select id="checkSkuIfExist" resultType="Integer">
		select  count(product.id) from t_ryytn_distribute_plan_valid_rule_range_product product
			left join t_ryytn_distribute_plan_valid_rule rule on  product.rule_id=rule.id
		where  (rule.end_time <![CDATA[>=]]> date(now()) or rule.forever_flag=1) and rule.distribute_type = #{distributeType}
			and
			sku_code   in
			<foreach collection="skuList" item="item" open="(" close=")" separator=",">
			#{item}
			</foreach>
	 </select>

	<select id="queryCategorySkuCodeList" resultType="java.lang.String">
		select category.sku_codes from t_ryytn_distribute_plan_valid_rule_range_category category
			left join t_ryytn_distribute_plan_valid_rule rule on  category.rule_id=rule.id
		where  ((rule.start_time <![CDATA[<=]]> date(now()) and rule.end_time <![CDATA[>=]]> date(now())) or rule.forever_flag=1) and rule.distribute_type = #{distributeType}
	</select>

	<update id="updateValidRule" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleVo">
		UPDATE
			t_ryytn_distribute_plan_valid_rule
		SET
			name = #{name},
			range_type = #{rangeType},
			start_time = #{startTime},
			end_time = #{endTime},
			forever_flag = #{foreverFlag},
			distribute_type = #{distributeType},
			updated_by = #{updatedBy},
			updated_time = #{updatedTime}
		WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<insert id="insertValidRule" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleVo">
		insert into t_ryytn_distribute_plan_valid_rule
        (id,
         name,
		 range_type,
         start_time,
         end_time,
         forever_flag,
         distribute_type,
         created_by,
         created_time)
        VALUES (#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{name},
                #{rangeType},
                #{startTime},
                #{endTime},
                #{foreverFlag},
                #{distributeType},
                #{createdBy},
                #{createdTime})
	</insert>

	<insert id="insertValidRuleRangeList" parameterType="java.util.List">
		INSERT INTO t_ryytn_distribute_plan_valid_rule_range (
		  id,
		  rule_id,
		  name,
		  start_day,
		  end_day,
		  ratio
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.name},
			#{item.startDay},
			#{item.endDay},
			#{item.ratio}
		  )
		</foreach>

	</insert>


	<insert id="insertValidRuleProductList" parameterType="java.util.List">
		INSERT INTO t_ryytn_distribute_plan_valid_rule_range_product (
		  id,
		  rule_id,
		  sku_code,
		  sku_name
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.skuCode},
			#{item.skuName}
		  )
		</foreach>
	</insert>

	<insert id="insertValidRuleCategoryList" parameterType="java.util.List">
		INSERT INTO t_ryytn_distribute_plan_valid_rule_range_category (
		  id,
		  rule_id,
		  category_code,
		  category_name,
		  level,
		  sku_codes
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.categoryCode},
			#{item.categoryName},
			#{item.level},
			#{item.skuCodes}
		  )
		</foreach>
	</insert>



	<select id="queryValidRuleName" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto" resultType="java.lang.String">
		select
			distinct b.name
		from
			t_ryytn_distribute_plan_valid_rule a
		left join t_ryytn_distribute_plan_valid_rule_range b
		 on
			a.id = b.rule_id
		left join t_ryytn_distribute_plan_valid_rule_range_category c
		 on
			a.id = c.rule_id
		where
			(a.forever_flag = 1
				or start_time &lt;= now()
				and end_time &gt;= now())
			and b.start_day &lt;= #{dayNum}
			and b.end_day &gt;= #{dayNum}
			and (#{itemId} = any(STRING_TO_ARRAY(c.sku_codes, ','))
				or #{itemId} = d.sku_code)
		limit 1
	</select>
</mapper>