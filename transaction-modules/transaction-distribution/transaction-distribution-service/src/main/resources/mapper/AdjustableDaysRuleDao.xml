<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.distribution.dao.AdjustableDaysRuleDao">

    <select id="countAdjustableDaysRuleName" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.AdjustableDaysRuleDto"
            resultType="java.lang.Integer">
        select count(*) from t_ryytn_adjustable_days_rule WHERE name=#{name}
        <if test="id != null and id != ''">
			AND id != #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</if>
    </select>

    <insert id="addAdjustableDaysRule" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.AdjustableDaysRuleDto">
        insert into t_ryytn_adjustable_days_rule(
        id,
        name,
        range_type,
        start_time,
        end_time,
        forever_flag,
        created_by
        )
        values(
        #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
        #{name},
        #{rangeType},
        #{startTime},
        #{endTime},
        #{foreverFlag},
        #{createdBy}
        )
    </insert>

    <insert id="addAdjustableDaysRuleRange" parameterType="java.util.List">
        insert into t_ryytn_adjustable_days_rule_range(
        id,
        rule_id,
        warehouse_code,
        warehouse_name,
        adjustable_days
        )
        values
        <foreach collection="list" item="item" separator=",">
        (
        #{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
        #{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
        #{item.warehouseCode},
        #{item.warehouseName},
        #{item.adjustableDays}
        )
        </foreach>
    </insert>

     <insert id="addAdjustableDaysRuleRangeProduct" parameterType="java.util.List">
        insert into t_ryytn_adjustable_days_rule_range_product(
        id,
        rule_id,
        sku_code,
        sku_name
        )
         values
        <foreach collection="list" item="item" separator=",">
        (
        #{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
        #{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
        #{item.skuCode},
        #{item.skuName}
        )
        </foreach>
    </insert>
    
    <insert id="addAdjustableDaysRuleRangeCategory" parameterType="java.util.List">
        insert into t_ryytn_adjustable_days_rule_range_Category(
        id,
        rule_id,
        category_code,
        category_name,
        level,
        sku_codes
        )
        values
        <foreach collection="list" item="item" separator=",">
        (
        #{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
        #{item.ruleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
        #{item.categoryCode},
        #{item.categoryName},
        #{item.level},
        #{item.skuCodes}
        )
        </foreach>
    </insert>

    <select id="queryAdjustableDaysRuleList" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.QueryAdjustableDaysRuleListRspVo">
        SELECT
            a.id,
            a.name,
            a.range_type,
            a.start_time,
            a.end_time,
            a.forever_flag,
            a.is_default,
            string_agg(b.sku_name,',') as productRange,
            string_agg(c.category_name,',') as categoryRange
        FROM
            t_ryytn_adjustable_days_rule a
        LEFT JOIN
            t_ryytn_adjustable_days_rule_range_product b
            on a.id = b.rule_id
        LEFT JOIN
            t_ryytn_adjustable_days_rule_range_category c
            on a.id = c.rule_id
        GROUP BY a.id
        ORDER BY a.created_time desc
    </select>

    <resultMap id="AdjustableDaysRuleDtoMap" type="cn.aliyun.ryytn.modules.distribution.entity.dto.AdjustableDaysRuleDto">
		<id property="id" column="id"></id>
        <result column="name" property="name"></result>
		<result column="range_type" property="rangeType"></result>
		<result column="start_time" property="startTime"></result>
		<result column="end_time" property="endTime"></result>
		<result column="forever_flag" property="foreverFlag"></result>
        <result column="is_default" property="isDefault"></result>
		<collection property="adjustableDaysRuleRangeList"
                    ofType="cn.aliyun.ryytn.modules.distribution.entity.dto.AdjustableDaysRuleRangeDto">
					<id column="rId" property="id"/>
					<result column="rule_id" property="ruleId"></result>
					<result column="warehouse_code" property="warehouseCode"></result>
					<result column="warehouse_name" property="warehouseName"></result>
					<result column="adjustable_days" property="adjustableDays"></result>
		</collection>
		<collection property="productList"
                    ofType="cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct">
					<id column="pId" property="id"/>
					<result column="rule_id" property="ruleId"></result>
					<result column="sku_code" property="skuCode"></result>
					<result column="sku_name" property="skuName"></result>

		</collection>
		<collection property="categoryList"
                    ofType="cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory">
					<id column="cId" property="id"/>
					<result column="rule_id" property="ruleId"></result>
					<result column="category_code" property="categoryCode"></result>
					<result column="category_name" property="categoryName"></result>
					<result column="level" property="level"></result>
		</collection>
	</resultMap>

    <select id="queryAdjustableDaysRuleDetail" parameterType="java.lang.String" resultMap="AdjustableDaysRuleDtoMap">
        SELECT
            a.id,
            a.name,
            a.range_type,
            a.start_time,
            a.end_time,
            a.forever_flag,
            a.is_default,
            b.*,
            b.id pId,
            c.*,
            c.id cId,
            d.*,
            d.id rId
        FROM
            t_ryytn_adjustable_days_rule a
        LEFT JOIN
            t_ryytn_adjustable_days_rule_range_product b
            on a.id = b.rule_id
        LEFT JOIN
            t_ryytn_adjustable_days_rule_range_category c
            on a.id = c.rule_id
        LEFT JOIN
            t_ryytn_adjustable_days_rule_range d
            on a.id = d.rule_id
        WHERE a.id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        ORDER BY d.adjustable_days
    </select>

    <delete id="deleteAdjustableDaysRule" parameterType="java.lang.String">
        DELETE FROM t_ryytn_adjustable_days_rule
        WHERE id=#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <delete id="deleteAdjustableDaysRuleRange" parameterType="java.lang.String">
        DELETE FROM t_ryytn_adjustable_days_rule_range
        WHERE rule_id=#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <delete id="deleteAdjustableDaysRuleRangeCategory" parameterType="java.lang.String">
        DELETE FROM t_ryytn_adjustable_days_rule_range_category
        WHERE rule_id=#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <delete id="deleteAdjustableDaysRuleRangeProduct" parameterType="java.lang.String">
        DELETE FROM t_ryytn_adjustable_days_rule_range_product
        WHERE rule_id=#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <update id="updateAdjustableDaysRule" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.AdjustableDaysRuleDto">
        UPDATE t_ryytn_adjustable_days_rule
        SET name=#{name},
        range_type=#{rangeType},
        start_time=#{startTime},
        end_time=#{endTime},
        forever_flag=#{foreverFlag},
        updated_by=#{updatedBy},
        updated_time=#{updatedTime}
        WHERE id=#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler};
    </update>
    
    <select id="checkSkuIfExist" parameterType="list" resultType="Integer">
		select  count(product.id) from t_ryytn_adjustable_days_rule_range_product product
			left join t_ryytn_adjustable_days_rule rule on  product.rule_id=rule.id
		where rule.start_time <![CDATA[>=]]>date(now()) and rule.end_time <![CDATA[<]]> now() and
			sku_code   in
			<foreach collection="skuCodeList" item="item" open="(" close=")" separator=",">
			#{item}
			</foreach>
    </select>
    
    <select id="queryCategorySkuCodeList" resultType="string">
		select category.sku_codes from t_ryytn_adjustable_days_rule_range_category category
			left join t_ryytn_adjustable_days_rule rule on  category.rule_id=rule.id
		where rule.start_time <![CDATA[>=]]>date(now()) and rule.end_time <![CDATA[<]]> now()
	</select>

    <select id="countAdjustableDaysGeneralRule" resultType="int">
        select count(*) from t_ryytn_adjustable_days_rule
        where range_type = 2
    </select>
</mapper>