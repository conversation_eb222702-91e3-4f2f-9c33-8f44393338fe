<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.distribution.dao.InventoryStrategyDao">


    <!--查询库存策略配置列表-->
    <select id="queryInventoryStrategyConfList" resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyConfDto">
		select id,
               config_name  as configName,
               config_value as configValue,
               remark,
               created_by   as createdBy,
               created_time as createdTime,
               updated_by   as updatedBy,
               updated_time as updatedTime
        from t_ryytn_distribute_plan_inventory_strategy_conf
        ORDER BY created_time
	</select>
    <!--查询库存策略配置列表-->
    <select id="queryInventoryStrategyList" resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyDto"
            parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyDto">
		select id,
               warehouse_code  as warehouseCode,
               warehouse_name as warehouseName,
               warehouse_type as warehouseType,
               sku_code   as skuCode,
               sku_name as skuName,
               sku_name_simple   as skuNameSimple,
               inventory_safe_day as inventorySafeDay,
               inventory_safe_amount as inventorySafeAmount,
               inventory_safe_amount_adv as inventorySafeAmountAdv,
               inventory_turnover_day as inventoryTurnoverDay,
               inventory_turnover_amount as inventoryTurnoverAmount,
               inventory_target_day_adv    as inventoryTargetDayAdv,
               inventory_target_amount_adv as inventoryTargetAmountAdv,
               inventory_target_day as inventoryTargetDay,
               inventory_target_amount as inventoryTargetAmount,
               sales_daily as salesDaily,
               special_strategy_flag as specialStrategyFlag,
               inventory_safe_day_spec as inventorySafeDaySpec,
               inventory_turnover_day_spec as inventoryTurnoverDaySpec,
               start_time_strategy           as startTimeStrategy,
			   end_time_strategy             as endTimeStrategy,
			   created_by                    as createdBy,
			   created_time                  as createdTime,
			   updated_by                    as updatedBy,
			   updated_time                  as updatedTime
        from t_ryytn_distribute_plan_inventory_strategy
		<where>
			<if test="warehouseName!=null and warehouseName!=''">
				and warehouse_name like concat('%',#{warehouseName},'%')
			</if>
			<if test="skuCode!=null and skuCode!=''">
				and sku_code = #{skuCode}
			</if>
			<if test="skuNameSimple!=null and skuNameSimple!=''">
				and sku_name_simple like concat('%',#{skuNameSimple},'%')
			</if>
		</where>
	</select>


	<update id="updateInventoryStrategyConf" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyConfDto">
		UPDATE
            t_ryytn_distribute_plan_inventory_strategy_conf
        SET config_value = #{configValue},
            updated_by   = #{updatedBy},
            updated_time = #{updatedTime}
        WHERE config_name = #{configName}
	</update>

	<delete id="deleteInventoryStrategy">
		delete
        from t_ryytn_distribute_plan_inventory_strategy
        where 1 = 1
	</delete>

	<select id="queryInventoryStrategyDetail" resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyDto">
		select id,
               warehouse_code                as warehouseCode,
               warehouse_name                as warehouseName,
               warehouse_type                as warehouseType,
               sku_code                      as skuCode,
               sku_name                      as skuName,
               sku_name_simple               as skuNameSimple,
               inventory_safe_day            as inventorySafeDay,
               inventory_safe_amount         as inventorySafeAmount,
               inventory_safe_amount_adv     as inventorySafeAmountAdv,
               inventory_turnover_day        as inventoryTurnoverDay,
               inventory_turnover_amount     as inventoryTurnoverAmount,
               inventory_target_day_adv    as inventoryTargetDayAdv,
               inventory_target_amount_adv as inventoryTargetAmountAdv,
               inventory_target_day          as inventoryTargetDay,
               inventory_target_amount       as inventoryTargetAmount,
               sales_daily                   as salesDaily,
               special_strategy_flag         as specialStrategyFlag,
               inventory_safe_day_spec       as inventorySafeDaySpec,
               inventory_turnover_day_spec   as inventoryTurnoverDaySpec,
               start_time_strategy           as startTimeStrategy,
               end_time_strategy             as endTimeStrategy,
               created_by                    as createdBy,
               created_time                  as createdTime,
               updated_by                    as updatedBy,
               updated_time                  as updatedTime
        from t_ryytn_distribute_plan_inventory_strategy
        where id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>

	<update id="updateInventoryStrategy" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyDto">
        update t_ryytn_distribute_plan_inventory_strategy
        set inventory_safe_day          =#{inventorySafeDay},
            inventory_turnover_day      =#{inventoryTurnoverDay},
            special_strategy_flag       =#{specialStrategyFlag},
            inventory_safe_day_spec     =#{inventorySafeDaySpec},
            inventory_turnover_day_spec =#{inventoryTurnoverDaySpec},
            start_time_strategy         =#{startTimeStrategy},
            end_time_strategy           =#{endTimeStrategy},
            updated_by                  =#{updatedBy},
            updated_time                =#{updatedTime}
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>


	<insert id="insertStrategyReality" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyRealityDto">
		INSERT INTO t_ryytn_distribute_plan_inventory_strategy_reality (warehouse_code, sku_code, sales_daily_reality,created_by)
		VALUES
			(#{warehouseCode},
			#{skuCode},
			#{salesDailyReality},
			#{createdBy})
	</insert>

	<select id="queryStrategyReality" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyRealityDto">
		select id,warehouse_code, sku_code, sales_daily_reality,created_by,created_time,updated_by,updated_time
		from t_ryytn_distribute_plan_inventory_strategy_reality
		where warehouse_code = #{warehouseCode} and sku_code = #{skuCode}
	</select>

	<update id="updateStrategyReality" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyRealityDto">
		UPDATE t_ryytn_distribute_plan_inventory_strategy_reality
		<trim prefix="SET" suffixOverrides=",">
			<if test="salesDailyReality != null and salesDailyReality !='' ">sales_daily_reality = #{salesDailyReality},</if>
			<if test="updatedBy != null and updatedBy != '' ">updated_by = #{updatedBy},</if>
			<if test="updatedTime != null and updatedTime != '' ">updated_time = to_timestamp(#{updatedTime}, 'YYYY-MM-DD HH24:MI:SS')</if>
		</trim>
		WHERE warehouse_code = #{warehouseCode} and sku_code = #{skuCode}
	</update>

	<delete id="deleteStrategyReality" parameterType="cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyRealityDto">
		delete from t_ryytn_distribute_plan_inventory_strategy_reality
		WHERE warehouse_code = #{warehouseCode} and sku_code = #{skuCode}
	</delete>


	<insert id="insertInventoryStrategyList" parameterType="java.util.List">
		INSERT INTO t_ryytn_distribute_plan_inventory_strategy (
		  id,
		  warehouse_code,
		  warehouse_name,
		  warehouse_type,
		  sku_code,
		  sku_name,
		  sku_name_simple,
		  inventory_safe_day,
		  inventory_safe_amount,
		  inventory_safe_amount_adv,
		  inventory_turnover_day,
		  inventory_turnover_amount,
		  inventory_target_day_adv,
		  inventory_target_amount_adv,
		  inventory_target_day,
		  inventory_target_amount,
		  sales_daily,
		  special_strategy_flag,
		  inventory_safe_day_spec,
		  inventory_turnover_day_spec,
		  start_time_strategy,
		  end_time_strategy,
		  created_by,
		  created_time
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.id},
			#{item.warehouseCode},
			#{item.warehouseName},
			#{item.warehouseType},
			#{item.skuCode},
			#{item.skuName},
			#{item.skuNameSimple},
			#{item.inventorySafeDay},
			#{item.inventorySafeAmount},
			#{item.inventorySafeAmountAdv},
			#{item.inventoryTurnoverDay},
			#{item.inventoryTurnoverAmount},
			#{item.inventoryTargetDayAdv},
			#{item.inventoryTargetAmountAdv},
			#{item.inventoryTargetDay},
			#{item.inventoryTargetAmount},
			#{item.salesDaily},
			#{item.specialStrategyFlag},
			#{item.inventorySafeDaySpec},
			#{item.inventoryTurnoverDaySpec},
			#{item.startTimeStrategy},
			#{item.endTimeStrategy},
			#{item.createdBy},
			#{item.createdTime}
		  )
		</foreach>

	</insert>

	<delete id="deleteAllServiceLevel">
		delete
        from so_ss_service_level
        where 1 = 1
	</delete>

	<delete id="deleteAllAverageDemand">
		delete
        from so_ss_average_demand
        where 1 = 1
	</delete>

	<delete id="deleteAllSafetyStock">
		delete
        from so_wt_safety_stock
        where 1 = 1
	</delete>

	<insert id="insertServiceLevelList" parameterType="java.util.List">
		INSERT INTO so_ss_service_level (
		  stock_point_id,
		  stock_point_name,
		  item_id,
		  item_name,
		  service_level,
		  remark,
		  status
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.stockPointId},
			#{item.stockPointName},
			#{item.itemId},
			#{item.itemName},
			#{item.serviceLevel},
			#{item.remark},
			#{item.status}
		  )
		</foreach>
	</insert>
	<insert id="insertAverageDemandList" parameterType="java.util.List">
		INSERT INTO so_ss_average_demand (
		  stock_point_id,
		  stock_point_name,
		  item_id,
		  item_name,
		  average_qty,
		  remark,
		  status
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.stockPointId},
			#{item.stockPointName},
			#{item.itemId},
			#{item.itemName},
			#{item.averageQty},
			#{item.remark},
			#{item.status}
		  )
		</foreach>
	</insert>

	<insert id="insertSafetyStockList" parameterType="java.util.List">
		INSERT INTO so_wt_safety_stock (
		  item_id,
		  item_name,
		  stock_point_id,
		  stock_point_name,
		  safety_stock,
		  target_stock,
		  remark,
		  status
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.itemId},
			#{item.itemName},
			#{item.stockPointId},
			#{item.stockPointName},
			#{item.safetyStock},
			#{item.targetStock},
			#{item.remark},
			#{item.status}
		  )
		</foreach>
	</insert>

	<select id="selectDailySalesView" resultType="cn.aliyun.ryytn.modules.distribution.entity.dto.DailySalesView">
<!--		select * from cdop_biz.v_average_daily_sales-->
        SELECT _b.biz_warehouse_code as warehouse_code,
			max(_b.biz_warehouse_name) AS warehouse_name,
			_b.sku_code,
			max(_b.sku_name) AS sku_name,
			sum(_b.outbound_num)::numeric(20,0) AS outbound_num
		   FROM ( SELECT tdm_kcjh_txn_delivery_order_df.biz_warehouse_code,
					tdm_kcjh_txn_delivery_order_df.sku_code,
					max(tdm_kcjh_txn_delivery_order_df.biz_warehouse_name::text) AS biz_warehouse_name,
					max(tdm_kcjh_txn_delivery_order_df.sku_name::text) AS sku_name,
					sum(tdm_kcjh_txn_delivery_order_df.outbound_num::double precision) / date_part('day'::text, date_trunc('month'::text, 'now'::text::date - '1 mon'::interval) - date_trunc('month'::text, 'now'::text::date - '2 mons'::interval)) * (( SELECT t_ryytn_distribute_plan_inventory_strategy_conf.config_value::numeric(6,4) / 100::numeric
						   FROM cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf
						  WHERE t_ryytn_distribute_plan_inventory_strategy_conf.config_name::text = 'OUTBOUND_RATIO_2'::text
						 LIMIT 1))::double precision AS outbound_num
				   FROM cdop_biz.tdm_kcjh_txn_delivery_order_df
				  WHERE to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &gt;= date_trunc('month'::text, 'now'::text::date - '2
		mons'::interval) AND to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &lt;= date_trunc('month'::text, 'now'::text::date -
		'1 mon'::interval)
				  GROUP BY tdm_kcjh_txn_delivery_order_df.biz_warehouse_code, tdm_kcjh_txn_delivery_order_df.sku_code
				UNION ALL
				 SELECT tdm_kcjh_txn_delivery_order_df.biz_warehouse_code,
					tdm_kcjh_txn_delivery_order_df.sku_code,
					max(tdm_kcjh_txn_delivery_order_df.biz_warehouse_name::text) AS biz_warehouse_name,
					max(tdm_kcjh_txn_delivery_order_df.sku_name::text) AS sku_name,
					sum(tdm_kcjh_txn_delivery_order_df.outbound_num::double precision) / date_part('day'::text, date_trunc('month'::text, 'now'::text::date::timestamp with time zone) - date_trunc('month'::text, 'now'::text::date - '1 mon'::interval)::timestamp with time zone) * (( SELECT t_ryytn_distribute_plan_inventory_strategy_conf.config_value::numeric(6,4) / 100::numeric
						   FROM cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf
						  WHERE t_ryytn_distribute_plan_inventory_strategy_conf.config_name::text = 'OUTBOUND_RATIO_1'::text
						 LIMIT 1))::double precision AS outbound_num
				   FROM cdop_biz.tdm_kcjh_txn_delivery_order_df
				  WHERE to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &gt;= date_trunc('month'::text, 'now'::text::date - '1
		mon'::interval) AND to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &lt;= date_trunc('month'::text,
		'now'::text::date::timestamp with time zone)
				  GROUP BY tdm_kcjh_txn_delivery_order_df.biz_warehouse_code, tdm_kcjh_txn_delivery_order_df.sku_code
				UNION ALL
				 SELECT tdm_kcjh_txn_delivery_order_df.biz_warehouse_code,
					tdm_kcjh_txn_delivery_order_df.sku_code,
					max(tdm_kcjh_txn_delivery_order_df.biz_warehouse_name::text) AS biz_warehouse_name,
					max(tdm_kcjh_txn_delivery_order_df.sku_name::text) AS sku_name,
					sum(tdm_kcjh_txn_delivery_order_df.outbound_num::double precision) / (date_part('day'::text, date_trunc('month'::text, 'now'::text::date::timestamp with time zone) - date_trunc('month'::text, 'now'::text::date - '00:00:00'::interval)::timestamp with time zone) + 1::double precision) * (( SELECT t_ryytn_distribute_plan_inventory_strategy_conf.config_value::numeric(6,4) / 100::numeric
						   FROM cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf
						  WHERE t_ryytn_distribute_plan_inventory_strategy_conf.config_name::text = 'OUTBOUND_RATIO'::text
						 LIMIT 1))::double precision AS outbound_num
				   FROM cdop_biz.tdm_kcjh_txn_delivery_order_df
				  WHERE to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &gt;= date_trunc('month'::text, 'now'::text::date -
		'00:00:00'::interval) AND to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &lt;= date_trunc('day'::text,
		'now'::text::date::timestamp with time zone)
				  GROUP BY tdm_kcjh_txn_delivery_order_df.biz_warehouse_code, tdm_kcjh_txn_delivery_order_df.sku_code
				UNION ALL
				 SELECT warehouse.biz_warehouse_code,
					plan_warehouse.sku_code,
					max(warehouse.biz_warehouse_name::text) AS biz_warehouse_name,
					max(plan_warehouse.sku_name::text) AS sku_name,
					sum(plan_warehouse.date_value::double precision) / date_part('day'::text, date_trunc('month'::text, 'now'::text::date + '1 mon'::interval)::timestamp with time zone - date_trunc('month'::text, 'now'::text::date::timestamp with time zone)) * (( SELECT t_ryytn_distribute_plan_inventory_strategy_conf.config_value::numeric(6,4) / 100::numeric
						   FROM cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf
						  WHERE t_ryytn_distribute_plan_inventory_strategy_conf.config_name::text = 'DAILY_WAREHOUSE_DEMAND_RATIO'::text
						 LIMIT 1))::double precision AS outbound_num
				   FROM cdop_sys.t_ryytn_daily_warehouse_demand plan_warehouse
					 LEFT JOIN cdop_biz.dim_bas_warehouse_info_df warehouse ON warehouse.warehouse_code::text = plan_warehouse.warehouse_code::text
				  WHERE to_date(plan_warehouse.date_recorded, 'yyyyMMdd'::text) &gt;= NOW()
				  GROUP BY warehouse.biz_warehouse_code, plan_warehouse.sku_code) _b
		  WHERE _b.biz_warehouse_code IS NOT NULL
		  GROUP BY _b.biz_warehouse_code, _b.sku_code
	</select>

	<select id="queryInventoryStrategyHeadSelect" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo">
		select distinct ${groupColumn}
		from cdop_biz.v_inventory_strategy
		<include refid="where_sql"/>
		order by ${sortColumn}
	</select>

	<select id="queryInventoryStrategyViewDataKeyList" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo">
		select distinct
			sku_code,
			sku_name,
			warehouse_name
		from cdop_biz.v_inventory_strategy
		<include refid="where_sql"/>
		ORDER BY warehouse_name, sku_code, sku_name
	</select>

	<select id="pageInventoryStrategyViewFromTable" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo"
			resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo">
		SELECT
		s.id,
		s.sku_code,
		s.sku_name,
		s.warehouse_code,
		s.warehouse_name,
		s.safety_days_adv,
		s.safety_qty_adv,
		s.target_days_adv,
		s.target_qty_adv,
		s.lead_qty_adv,
		s.lead_days_adv,
		s.safety_days,
		s.safety_qty,
		s.turnover_days,
		s.das_qty as turnover_qty,
		s.qc_days,
		s.qc_qty,
		s.target_days,
		s.target_qty,
		s.abc_type,
		ceil(COALESCE(df.sales_daily_reality::numeric, COALESCE(s.das_num_advice, 0::numeric))) AS das_num,
		COALESCE(df.sales_daily_reality::bigint, - 1::bigint) AS day_num_define,
		s.outbound_num_m0,
		s.outbound_num_m1,
		s.outbound_num_m2,
		s.outbound_num_aiplan,
		s.das_num_advice
		FROM cdop_sys.t_ryytn_inventory_strategy s left join cdop_sys.t_ryytn_distribute_plan_inventory_strategy_reality df ON s.warehouse_code::text = df.warehouse_code::text AND df.sku_code::text =  s.sku_code::text
		<include refid="where_sql_s"/>
		and s.data_create_time = (select max(data_create_time) from cdop_sys.t_ryytn_inventory_strategy)
		<choose>
			<when test="sortColumn != null and sortColumn != ''">
				ORDER BY s.${sortColumn}
			</when>
			<otherwise>
				ORDER BY s.sku_code,s.warehouse_code
			</otherwise>
		</choose>
	</select>


	<select id="pageInventoryStrategyView" parameterType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo"
            resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo">
		SELECT
			id,
			sku_code,
			sku_name,
			warehouse_code,
			warehouse_name,
			safety_days_adv,
			safety_qty_adv,
			target_days_adv,
			target_qty_adv,
			lead_qty_adv,
			lead_days_adv,
			safety_days,
			safety_qty,
			turnover_days,
			das_qty as turnover_qty,
			qc_days,
			qc_qty,
			target_days,
			target_qty,
			das_num,
			abc_type,
			day_num_define,
			outbound_num_m0,
			outbound_num_m1,
			outbound_num_m2,
			outbound_num_aiplan,
			das_num_advice
		FROM cdop_biz.v_inventory_strategy
		<include refid="where_sql"/>
		<choose>
			<when test="sortColumn != null and sortColumn != ''">
				ORDER BY ${sortColumn}
			</when>
			<otherwise>
				ORDER BY sku_code,warehouse_code
			</otherwise>
		</choose>
	</select>
	<sql id="where_sql_s">
		WHERE 1 = 1
		<if test="skuCodes != null and skuCodes != ''">
			AND s.sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="skuCode != null and skuCode != ''">
			AND s.sku_code = #{skuCode}
		</if>
		<if test="warehouseCodes != null and warehouseCodes != ''">
			AND s.warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
		</if>
		<if test="warehouseCode != null and warehouseCode != ''">
			AND s.warehouse_code = #{warehouseCode}
		</if>
		<if test="warehouseNames != null and warehouseNames != ''">
			AND s.warehouse_name = ANY(STRING_TO_ARRAY(#{warehouseNames},','))
		</if>
		<if test="warehouseName != null and warehouseName != ''">
			AND s.warehouse_name = #{warehouseName}
		</if>
		<if test="skuName != null and skuName != ''">
			AND s.sku_name = ANY(STRING_TO_ARRAY(#{skuName},','))
		</if>
		<if test="skuNames != null and skuNames != ''">
			AND s.sku_name = #{skuNames}
		</if>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
				<if test="skuCode != null and skuCode != ''">
					AND s.sku_code = #{skuCode}
				</if>
				<if test="skuName != null and skuName != ''">
					AND s.sku_name = #{skuName}
				</if>
				<if test="warehouseCode != null and warehouseCode != ''">
					AND s.warehouse_code = #{warehouseCode}
				</if>
				)
			</foreach>
		</if>
	</sql>
	<sql id="where_sql">
        WHERE 1 = 1
		<if test="skuCodes != null and skuCodes != ''">
			AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="skuCode != null and skuCode != ''">
			AND sku_code = #{skuCode}
		</if>
        <if test="warehouseCodes != null and warehouseCodes != ''">
            AND warehouse_code = ANY(STRING_TO_ARRAY(#{warehouseCodes},','))
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND warehouse_code = #{warehouseCode}
        </if>
		<if test="warehouseNames != null and warehouseNames != ''">
            AND warehouse_name = ANY(STRING_TO_ARRAY(#{warehouseNames},','))
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND warehouse_name = #{warehouseName}
        </if>
        <if test="skuName != null and skuName != ''">
            AND sku_name = ANY(STRING_TO_ARRAY(#{skuName},','))
        </if>
        <if test="skuNames != null and skuNames != ''">
            AND sku_name = #{skuNames}
        </if>
		<if test="keyList != null and keyList.size() > 0">
			AND
			<foreach collection="keyList" item="item" open="(" close=")" separator="OR">
				(1=1
                <if test="skuCode != null and skuCode != ''">
                    AND sku_code = #{skuCode}
                </if>
				<if test="skuName != null and skuName != ''">
                    AND sku_name = #{skuName}
                </if>
                <if test="warehouseCode != null and warehouseCode != ''">
                    AND warehouse_code = #{warehouseCode}
                </if>
				)
			</foreach>
		</if>
	</sql>

	<select id="queryDasNumList" parameterType="java.lang.String" resultType="cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo">
		 SELECT _b.biz_warehouse_code as warehouse_code,
			max(_b.biz_warehouse_name) AS warehouse_name,
			_b.sku_code,
			max(_b.sku_name) AS sku_name,
			sum(_b.outbound_num)::numeric(20,0) AS das_num
		   FROM ( SELECT tdm_kcjh_txn_delivery_order_df.biz_warehouse_code,
					tdm_kcjh_txn_delivery_order_df.sku_code,
					max(tdm_kcjh_txn_delivery_order_df.biz_warehouse_name::text) AS biz_warehouse_name,
					max(tdm_kcjh_txn_delivery_order_df.sku_name::text) AS sku_name,
					sum(tdm_kcjh_txn_delivery_order_df.outbound_num::double precision) / date_part('day'::text, date_trunc('month'::text, 'now'::text::date - '1 mon'::interval) - date_trunc('month'::text, 'now'::text::date - '2 mons'::interval)) * (( SELECT t_ryytn_distribute_plan_inventory_strategy_conf.config_value::numeric(6,4) / 100::numeric
						   FROM cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf
						  WHERE t_ryytn_distribute_plan_inventory_strategy_conf.config_name::text = 'OUTBOUND_RATIO_2'::text
						 LIMIT 1))::double precision AS outbound_num
				   FROM cdop_biz.tdm_kcjh_txn_delivery_order_df
				  WHERE to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &gt;= date_trunc('month'::text, 'now'::text::date - '2
		mons'::interval) AND to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &lt;= date_trunc('month'::text, 'now'::text::date -
		'1 mon'::interval)
				  GROUP BY tdm_kcjh_txn_delivery_order_df.biz_warehouse_code, tdm_kcjh_txn_delivery_order_df.sku_code
				UNION ALL
				 SELECT tdm_kcjh_txn_delivery_order_df.biz_warehouse_code,
					tdm_kcjh_txn_delivery_order_df.sku_code,
					max(tdm_kcjh_txn_delivery_order_df.biz_warehouse_name::text) AS biz_warehouse_name,
					max(tdm_kcjh_txn_delivery_order_df.sku_name::text) AS sku_name,
					sum(tdm_kcjh_txn_delivery_order_df.outbound_num::double precision) / date_part('day'::text, date_trunc('month'::text, 'now'::text::date::timestamp with time zone) - date_trunc('month'::text, 'now'::text::date - '1 mon'::interval)::timestamp with time zone) * (( SELECT t_ryytn_distribute_plan_inventory_strategy_conf.config_value::numeric(6,4) / 100::numeric
						   FROM cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf
						  WHERE t_ryytn_distribute_plan_inventory_strategy_conf.config_name::text = 'OUTBOUND_RATIO_1'::text
						 LIMIT 1))::double precision AS outbound_num
				   FROM cdop_biz.tdm_kcjh_txn_delivery_order_df
				  WHERE to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &gt;= date_trunc('month'::text, 'now'::text::date - '1
		mon'::interval) AND to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &lt;= date_trunc('month'::text,
		'now'::text::date::timestamp with time zone)
				  GROUP BY tdm_kcjh_txn_delivery_order_df.biz_warehouse_code, tdm_kcjh_txn_delivery_order_df.sku_code
				UNION ALL
				 SELECT tdm_kcjh_txn_delivery_order_df.biz_warehouse_code,
					tdm_kcjh_txn_delivery_order_df.sku_code,
					max(tdm_kcjh_txn_delivery_order_df.biz_warehouse_name::text) AS biz_warehouse_name,
					max(tdm_kcjh_txn_delivery_order_df.sku_name::text) AS sku_name,
					sum(tdm_kcjh_txn_delivery_order_df.outbound_num::double precision) / (date_part('day'::text, date_trunc('month'::text, 'now'::text::date::timestamp with time zone) - date_trunc('month'::text, 'now'::text::date - '00:00:00'::interval)::timestamp with time zone) + 1::double precision) * (( SELECT t_ryytn_distribute_plan_inventory_strategy_conf.config_value::numeric(6,4) / 100::numeric
						   FROM cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf
						  WHERE t_ryytn_distribute_plan_inventory_strategy_conf.config_name::text = 'OUTBOUND_RATIO'::text
						 LIMIT 1))::double precision AS outbound_num
				   FROM cdop_biz.tdm_kcjh_txn_delivery_order_df
				  WHERE to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &gt;= date_trunc('month'::text, 'now'::text::date -
		'00:00:00'::interval) AND to_date(tdm_kcjh_txn_delivery_order_df.biz_date_value::text, 'yyyyMMdd'::text) &lt;= date_trunc('day'::text,
		'now'::text::date::timestamp with time zone)
				  GROUP BY tdm_kcjh_txn_delivery_order_df.biz_warehouse_code, tdm_kcjh_txn_delivery_order_df.sku_code
				UNION ALL
				 SELECT warehouse.biz_warehouse_code,
					plan_warehouse.sku_code,
					max(warehouse.biz_warehouse_name::text) AS biz_warehouse_name,
					max(plan_warehouse.sku_name::text) AS sku_name,
					sum(plan_warehouse.date_value::double precision) / date_part('day'::text, date_trunc('month'::text, 'now'::text::date + '1 mon'::interval)::timestamp with time zone - date_trunc('month'::text, 'now'::text::date::timestamp with time zone)) * (( SELECT t_ryytn_distribute_plan_inventory_strategy_conf.config_value::numeric(6,4) / 100::numeric
						   FROM cdop_sys.t_ryytn_distribute_plan_inventory_strategy_conf
						  WHERE t_ryytn_distribute_plan_inventory_strategy_conf.config_name::text = 'DAILY_WAREHOUSE_DEMAND_RATIO'::text
						 LIMIT 1))::double precision AS outbound_num
				   FROM
					<choose>
						<when test="month != null and month != ''">
							cdop_sys.t_ryytn_daily_warehouse_demand_${month}
						</when>
						<otherwise>
							cdop_sys.t_ryytn_daily_warehouse_demand
						</otherwise>
					</choose>
					 plan_warehouse
					 LEFT JOIN cdop_biz.dim_bas_warehouse_info_df warehouse ON warehouse.warehouse_code::text = plan_warehouse.warehouse_code::text
				  WHERE to_date(plan_warehouse.date_recorded, 'yyyyMMdd'::text) &gt;= NOW()
				  GROUP BY warehouse.biz_warehouse_code, plan_warehouse.sku_code) _b
		  WHERE _b.biz_warehouse_code IS NOT NULL
		  GROUP BY _b.biz_warehouse_code, _b.sku_code
	</select>



	<delete id="deleteTempInventoryStrategyThird">
        delete from cdop_sys.t_ryytn_inventory_strategy_third
    </delete>

	<insert id="insertTempInventoryStrategyThird">
          insert into  cdop_sys.t_ryytn_inventory_strategy_third(id,
				sku_code,
				sku_name,
				warehouse_code,
				warehouse_name,
				safety_days_adv,
				safety_qty_adv,
				target_days_adv,
				target_qty_adv,
				lead_qty_adv,
				lead_days_adv,
				safety_days,
				safety_qty,
				turnover_days,
				das_qty,
				qc_days,
				qc_qty,
				target_days,
				target_qty,
				das_num,
				status,
				day_num_define,
				outbound_num_m0,
				outbound_num_m1,
				outbound_num_m2,
				outbound_num_aiplan,
				abc_type,
				das_num_advice,
				data_create_time)
			select  id,
				sku_code,
				sku_name,
				warehouse_code,
				warehouse_name,
				safety_days_adv,
				safety_qty_adv,
				target_days_adv,
				target_qty_adv,
				lead_qty_adv,
				lead_days_adv,
				safety_days,
				safety_qty,
				turnover_days,
				das_qty,
				qc_days,
				qc_qty,
				target_days,
				target_qty,
				das_num,
				status,
				day_num_define,
				outbound_num_m0,
				outbound_num_m1,
				outbound_num_m2,
				outbound_num_aiplan,
				abc_type,
				das_num_advice,to_char(now(),'yyyyMMddHH24MIss') as data_create_time  from cdop_biz.v_inventory_strategy
    </insert>
	<insert id="insertTempInventoryStrategy">
             insert into  cdop_sys.t_ryytn_inventory_strategy
             select * from cdop_sys.t_ryytn_inventory_strategy_third
    </insert>
</mapper>
