package cn.aliyun.ryytn.modules.distribution.task;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 创建日分仓需求分区表任务
 * <AUTHOR>
 * @date 2023/12/11 9:37
 */
@Slf4j
@Service
public class CreateDailyWarehousePartitionTaskServiceImpl implements TaskService
{
    @Autowired
    private DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        // 明年
        String nextYear = DateUtils.formatTime(DateUtils.addYears(new Date(), 1), DateUtils.Y);
        List<String> partitionList = new ArrayList<>(12);
        for (int i = 1; i <= 12; i++)
        {
            partitionList.add(nextYear + String.format("%02d", i));
        }
        dailyWarehouseDemandDao.createDailyWarehousePartitionTable(partitionList);
    }
}
