package cn.aliyun.ryytn.modules.distribution.service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.modules.distribution.entity.dto.DailySalesView;
import cn.aliyun.ryytn.modules.distribution.entity.dto.GraphKcclConfigDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyConfDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyRealityDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoSsAverageDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoSsServiceLevelDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtSafetyStockDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseSkuDto;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.security.SecurityUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.distribution.api.InventoryStrategyService;
import cn.aliyun.ryytn.modules.distribution.dao.InventoryStrategyDao;
import cn.aliyun.ryytn.modules.distribution.dataqdao.DataqInventoryDao;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SalesVolume;
import cn.aliyun.ryytn.modules.system.api.WarehouseService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 效期分档规则接口
 * <AUTHOR>
 * @date 2023/11/20 20:25
 */
@Slf4j
@Service
public class InventoryStrategyServiceImpl implements InventoryStrategyService
{

    @Autowired
    private InventoryStrategyDao inventoryStrategyDao;


    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private DataqInventoryDao dataqInventoryDao;

    /**
     *
     * @Description 查询库存策略配置列表数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/20 20:25
     */
    @Override
    public List<InventoryStrategyConfDto> queryInventoryStrategyConfList() throws Exception
    {
        return inventoryStrategyDao.queryInventoryStrategyConfList();
    }

    /**
     *
     * @Description 更新库存策略配置
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/20 09:06
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInventoryStrategyConf(List<InventoryStrategyConfDto> inventoryStrategyConfDtos) throws Exception
    {

        //修改配置信息
        for (InventoryStrategyConfDto inventoryStrategyConfDto : inventoryStrategyConfDtos)
        {
            inventoryStrategyConfDto.setUpdatedBy("update");
            inventoryStrategyConfDto.setUpdatedTime(new Date());
            inventoryStrategyDao.updateInventoryStrategyConf(inventoryStrategyConfDto);
        }

        //报错为了演示暂时注释掉
        //更新算法中间表数据
        //更新日均需求数据
        // 2024/03/11：与邬练和杨金成确认，前期申晓新与他们沟通直接使用申晓新创建的视图，
        // this.updateDailySales();
        //更新安全库存（so_wt_safety_stock） 表数据
        // 2024/03/11：与邬练和杨金成确认，前期申晓新与他们沟通不再使用cdop_sys的so_wt_safety_stock表数据，而直接使用申晓新创建的视图，
        // 排查发现确实没有视图使用so_wt_safety_stock表，此处代码注释。
        // this.updateSafetyStockData();
        //更新服务水平（so_ss_service_level） 和  日均需求（so_ss_average_demand） 表数据
        // 2024/03/11：与邬练和杨金成确认，前期申晓新与他们沟通不再使用cdop_sys的so_ss_service_level,so_ss_average_demand表数据，而直接使用申晓新创建的视图，
        // 排查发现确实没有视图使用so_ss_service_level,so_ss_average_demand表，此处代码注释。
        // this.insetInventoryStrategyData();
    }

    @Override
    public void inventoryStrategyRealityManage(InventoryStrategyVo inventoryStrategyVo) throws Exception {
        InventoryStrategyRealityDto inventoryStrategyRealityDto = new InventoryStrategyRealityDto();
        inventoryStrategyRealityDto.setSalesDailyReality(inventoryStrategyVo.getSalesDailyReality());
        inventoryStrategyRealityDto.setSkuCode(inventoryStrategyVo.getSkuCode());
        inventoryStrategyRealityDto.setWarehouseCode(inventoryStrategyVo.getWarehouseCode());
        if (CommonConstants.ADD.equals(inventoryStrategyVo.getOperateType())) {
            inventoryStrategyRealityDto.setCreatedBy(inventoryStrategyVo.getCreatedBy());
            inventoryStrategyDao.insertStrategyReality(inventoryStrategyRealityDto);
        } else if (CommonConstants.UPDATE.equals(inventoryStrategyVo.getOperateType())) {
            inventoryStrategyRealityDto.setUpdatedBy(inventoryStrategyVo.getUpdatedBy());
            inventoryStrategyRealityDto.setUpdatedTime(DateUtils.formatTime(new Date(),DateUtils.YMDHMS_STD_MS));
            inventoryStrategyDao.updateStrategyReality(inventoryStrategyRealityDto);
        } else if (CommonConstants.DELETE.equals(inventoryStrategyVo.getOperateType())) {
            inventoryStrategyDao.deleteStrategyReality(inventoryStrategyRealityDto);
        } else {
            log.info("操作类型错误：inventoryStrategyRealityManage");
        }
    }


    @Override
    public List<GraphKcclConfigDto> queryInventoryStrategyDetail(GraphKcclConfigDto graphKcclConfig) throws Exception
    {
        List<GraphKcclConfigDto> result = dataqInventoryDao.queryGraphKcclConfigDetail(graphKcclConfig);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInventoryStrategy(List<GraphKcclConfigDto> graphKcclConfigList) throws Exception
    {
        String currentAccount = ServiceContextUtils.currentSession().getAccount().getName();
        Date currentTime = new Date();
        for (GraphKcclConfigDto item : graphKcclConfigList)
        {
            if (Objects.isNull(item.getSafetyDays()))
            {
                item.setSafetyDays(0);
            }
            if (Objects.isNull(item.getTurnoverDays()))
            {
                item.setTurnoverDays(0);
            }
            if (Objects.isNull(item.getIsCustom()))
            {
                item.setIsCustom(0);
            }
            item.setCreator(currentAccount);
            item.setLastModifier(currentAccount);
            item.setGmtCreate(currentTime);
            item.setGmtModify(currentTime);
        }
        GraphKcclConfigDto graphKcclConfigDto = graphKcclConfigList.get(0);

        // 删除原来的配置数据
        dataqInventoryDao.deleteGraphKcclConfig(graphKcclConfigDto);

        // 新增本次修改的配置数据
        dataqInventoryDao.addGraphKcclConfigList(graphKcclConfigList);
    }


    /**
     *
     * @Description 查询库存策略数据列表
     * @param  condition
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/20 20:25
     */
    @Override
    public PageInfo<InventoryStrategyVo> queryInventoryStrategyList(PageCondition<InventoryStrategyConditionVo> condition) throws Exception
    {

        InventoryStrategyConditionVo condition1 = condition.getCondition();
        Map<String, Object> body = new HashMap<>();
        body.put("nowDate", DateUtils.formatDate(new Date(), DateUtils.YMD_DASH));
        body.put("skuCodes", condition1.getSkuCodes());
        body.put("warehouseCodes", condition1.getWarehouseCodes());
        body.put("skuNames", condition1.getSkuNames());
        Map<String, Object> param = new HashMap<>();
        param.put("page_token", condition.getPageNum());
        param.put("fetch_all", true);
        param.put("order_by", "warehouse_code,sku_code");
        param.put("page_size", condition.getPageSize());

        String pathPage = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_INVSTRAT_PAGE"));
        DataqResult<?> invoke = dataqService.invoke(HttpMethod.POST, pathPage, null, param, body);
        Integer total = Integer.valueOf(invoke.getTotal());
        JSONArray pageResult = (JSONArray) invoke.getData();
        List<InventoryStrategyVo> inventoryStrategyVos = new ArrayList<>();
        if (Objects.nonNull(pageResult))
        {
            inventoryStrategyVos = JSONArray.parseArray(pageResult.toJSONString(), InventoryStrategyVo.class);
        }
        // 临时查询日均销量视图
        List<DailySalesView> dailySalesViewList = inventoryStrategyDao.selectDailySalesView();
        Map<DailySalesView, List<DailySalesView>> collect = dailySalesViewList.stream().collect(Collectors.groupingBy(Function.identity()));
        for (InventoryStrategyVo vo : inventoryStrategyVos)
        {
            DailySalesView view = new DailySalesView();
            view.setBizWarehouseCode(vo.getWarehouseCode());
            view.setSkuCode(vo.getSkuCode());
            List<DailySalesView> list = collect.get(view);
            if (CollectionUtils.isNotEmpty(list))
            {
                vo.setDasNum(list.get(0).getOutboundNum());
            }
            else
            {
                vo.setDasNum(0d);
            }
            vo.setSafetyQty(vo.getDasNum() * vo.getSafetyDays());
            vo.setDasQty(vo.getDasNum() * vo.getTurnoverDays());
            vo.setQcQty(vo.getDasNum() * vo.getQcDays());
            vo.setTargetQty(vo.getDasNum() * vo.getTargetDays());
        }

        PageInfo<InventoryStrategyVo> inventoryStrategyVoPageInfo = new PageInfo<>(inventoryStrategyVos);
        inventoryStrategyVoPageInfo.setTotal(total);
        return inventoryStrategyVoPageInfo;
    }

    /**
     *
     * @Description 查询库存策略表头下拉列表
     * @param inventoryStrategyVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月04日 16:35     */
    @Override
    public List<InventoryStrategyVo> queryInventoryStrategyHeadSelect(InventoryStrategyVo inventoryStrategyVo) throws Exception
    {
        GroupColumnEnum groupColumnEnum = inventoryStrategyVo.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        inventoryStrategyVo.setGroupColumn(groupColumn);
        inventoryStrategyVo.setSortColumn(sortColumn);

        return inventoryStrategyDao.queryInventoryStrategyHeadSelect(inventoryStrategyVo);
    }

    /**
     *
     * @Description 分页查询库存策略视图数据列表
     * @param condition
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月04日 16:36     */
    @Override
    public PageInfo<InventoryStrategyVo> pageInventoryStrategyView(PageCondition<InventoryStrategyVo> condition) throws Exception
    {
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        InventoryStrategyVo inventoryStrategyVo = condition.getCondition();

        // 防止sql注入
        if (Objects.nonNull(inventoryStrategyVo) && StringUtils.isNotBlank(inventoryStrategyVo.getSortColumn()))
        {
            inventoryStrategyVo.setSortColumn(SecurityUtil.escapeSql(inventoryStrategyVo.getSortColumn()));
        }

        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        PageHelper.startPage(pageNum, pageSize);

        List<InventoryStrategyVo> dataList = inventoryStrategyDao.pageInventoryStrategyViewFromTable(inventoryStrategyVo);

        return new PageInfo<InventoryStrategyVo>(dataList);
    }

    /**
     * 刷新库存策略数据到表中
     */
    @Override
    @Transactional
    public void rereshInventoryStrategyData2Table() {
        inventoryStrategyDao.deleteTempInventoryStrategyThird();
        inventoryStrategyDao.insertTempInventoryStrategyThird();
        inventoryStrategyDao.insertTempInventoryStrategy();
    }

    /**
     * 更新算法中间表数据（服务水平，日均需求）
     * @throws Exception
     */
    @Deprecated
    public void insetInventoryStrategyData() throws Exception
    {
        //删除现有数据 服务水平、日均需求
        inventoryStrategyDao.deleteAllServiceLevel();
        inventoryStrategyDao.deleteAllAverageDemand();
        List<SoSsServiceLevelDto> serviceLevelDtos = new ArrayList<>();
        List<SoSsAverageDemandDto> averageDemandDtos = new ArrayList<>();
        List<InventoryStrategyDto> inventoryStrategyDtos = new ArrayList<>();
        //从dataq获取仓库 +sku 关系数据
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BASEBUS_SKU_WAREHOUSE_LIST"));
        Map<String, Object> body = new HashMap<>();
        body.put("status", 1);
        body.put("nowDate", DateUtils.getDate(DateUtils.YMD_DASH));
        JSONArray warehouseJson = (JSONArray) dataqService.invoke(HttpMethod.POST, path, null, null, body).getData();
        List<WarehouseSkuDto> warehouseSkuDtoList = JSONArray.parseArray(warehouseJson.toJSONString(), WarehouseSkuDto.class);

        //获取仓库+sku的 日均销量
        //获取配置信息
        Map<String, Object> config = this.getConfig();
        //获取服务水平  阿里默认0.95
        Double serviceLevel = Double.valueOf(config.get("SERVICE_LEVEL").toString());

        //获取库存策略的日均需求
        PageCondition<InventoryStrategyConditionVo> condition = new PageCondition<>();
        condition.setCondition(new InventoryStrategyConditionVo());
        condition.setPageNum(1);
        condition.setPageSize(99999);
        PageInfo<InventoryStrategyVo> inventoryStrategyVoPageInfo = this.queryInventoryStrategyList(condition);
        //根据sku+warehouse分组
        Map<String, List<InventoryStrategyVo>> sku_warehouseMap =
            inventoryStrategyVoPageInfo.getList().stream().collect(Collectors.groupingBy(item -> item.getSkuCode() + "_" + item.getWarehouseCode()));

        for (WarehouseSkuDto warehouseSkuDto : warehouseSkuDtoList)
        {
            String warehouseCode = warehouseSkuDto.getWarehouseCode();
            String warehouseName = warehouseSkuDto.getWarehouseName();
            String skuCode = warehouseSkuDto.getSkuCode();
            String skuName = warehouseSkuDto.getSkuName();
            Double salesDaily = 0.0;
            //获取日均出库量 从库存策略获取
            List<InventoryStrategyVo> inventoryStrategyVos = sku_warehouseMap.get(skuCode + "_" + warehouseCode);
            if (Objects.nonNull(inventoryStrategyVos) && inventoryStrategyVos.size() > 0)
            {
                salesDaily = inventoryStrategyVos.get(0).getDasNum();
            }
            SoSsServiceLevelDto serviceLevelDto = new SoSsServiceLevelDto();
            serviceLevelDto.setStockPointId(warehouseCode);
            serviceLevelDto.setStockPointName(warehouseName);
            serviceLevelDto.setItemId(skuCode);
            serviceLevelDto.setItemName(skuName);
            serviceLevelDto.setServiceLevel(serviceLevel);
            serviceLevelDto.setStatus(1);
            serviceLevelDtos.add(serviceLevelDto);

            SoSsAverageDemandDto averageDemandDto = new SoSsAverageDemandDto();
            averageDemandDto.setStockPointId(warehouseCode);
            averageDemandDto.setStockPointName(warehouseName);
            averageDemandDto.setItemId(skuCode);
            averageDemandDto.setItemName(skuName);

            averageDemandDto.setAverageQty(salesDaily);
            averageDemandDto.setStatus(1);
            averageDemandDtos.add(averageDemandDto);
            InventoryStrategyDto inventoryStrategyDto = new InventoryStrategyDto();
            inventoryStrategyDto.setId(SeqUtils.getSequenceUid());
            inventoryStrategyDto.setSkuCode(skuCode);
            inventoryStrategyDto.setSkuName(skuName);
            inventoryStrategyDto.setWarehouseCode(warehouseCode);
            inventoryStrategyDto.setWarehouseName(warehouseName);
            inventoryStrategyDto.setCreatedTime(new Date());
            inventoryStrategyDto.setCreatedBy("create");
            inventoryStrategyDtos.add(inventoryStrategyDto);
        }
        //处理安全库存这张表

        // 批次最大保存数量
        int batchNum = 1000;
        // 总数据量
        int insertLength = serviceLevelDtos.size();
        int i = 0;
        while (insertLength > batchNum)
        {
            inventoryStrategyDao.insertServiceLevelList(serviceLevelDtos.subList(i, i + batchNum));
            inventoryStrategyDao.insertAverageDemandList(averageDemandDtos.subList(i, i + batchNum));
            i = i + batchNum;
            insertLength = insertLength - batchNum;
        }
        // 保存首次或最后一次数据量不足"批次最大保存数量"的数据
        if (insertLength > 0)
        {
            inventoryStrategyDao.insertServiceLevelList(serviceLevelDtos.subList(i, i + insertLength));
            inventoryStrategyDao.insertAverageDemandList(averageDemandDtos.subList(i, i + insertLength));
        }
    }

    /**
     * 更新算法中间表数据 安全库存
     * @throws Exception
     */
    public void updateSafetyStockData() throws Exception
    {

        //先删除历史数据
        inventoryStrategyDao.deleteAllSafetyStock();
        //查库存策略所有数据
        PageCondition<InventoryStrategyConditionVo> condition = new PageCondition<>();
        condition.setCondition(new InventoryStrategyConditionVo());
        condition.setPageNum(1);
        condition.setPageSize(99999);
        PageInfo<InventoryStrategyVo> inventoryStrategyVoPageInfo = this.queryInventoryStrategyList(condition);
        List<SoWtSafetyStockDto> safetyStockDtos = new ArrayList<>();
        List<InventoryStrategyVo> list = inventoryStrategyVoPageInfo.getList();

        //查询生产sku与销售sku映射关系
        String pathPage = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BASEBUS_SKU_PRODUCT_TABLE"));
        Map<String, Object> param = new HashMap<>();
        param.put("return_fields", "production_code,sku_code,production_name");

        Map<String, Object> body = new HashMap<>();
        body.put("status", 1);
        Object data = dataqService.invoke(HttpMethod.POST, pathPage, null, param, body).getData();

        List<Map> maps = JSONArray.parseArray(data.toString(), Map.class);
        Map<String, List<Map>> skuCodeMapList = maps.stream().collect(Collectors.groupingBy(item -> item.get("skuCode").toString()));
        List<String> xsSkuCode = maps.stream().map(item -> item.get("skuCode").toString()).collect(Collectors.toList());

        //将销售编码转化为生产编码
        list.stream().forEach(item -> {
            String skuCode = item.getSkuCode();
            if (xsSkuCode.contains(skuCode))
            {
                Map map = skuCodeMapList.get(skuCode).get(0);
                item.setSkuCode(map.get("productionCode").toString());
                item.setSkuName(map.get("productionName").toString());
            }
        });

        //根据产品编码和仓库编码分组
        Map<String, List<InventoryStrategyVo>> warehouseCodeMap =
            list.stream().collect(Collectors.groupingBy(item -> item.getWarehouseCode() + "_" + item.getSkuCode()));
        Set<Map.Entry<String, List<InventoryStrategyVo>>> entries = warehouseCodeMap.entrySet();
        for (Map.Entry<String, List<InventoryStrategyVo>> entry : entries)
        {
            List<InventoryStrategyVo> value = entry.getValue();
            InventoryStrategyVo oneData = value.get(0);
            SoWtSafetyStockDto stockDto = new SoWtSafetyStockDto();
            stockDto.setStockPointId(oneData.getWarehouseCode());
            stockDto.setStockPointName(oneData.getWarehouseName());
            stockDto.setItemId(oneData.getSkuCode());
            stockDto.setItemName(oneData.getSkuName());
            stockDto.setStatus(1);
            long count1 = value.stream().filter(item -> item.getSafetyQty() != null).count();
            if (count1 > 0)
            {
                double safetyQtySum = value.stream().mapToDouble(InventoryStrategyVo::getSafetyQty).sum();
                stockDto.setSafetyStock(safetyQtySum);
            }
            long count = value.stream().filter(item -> item.getTargetQty() != null).count();
            if (count > 0)
            {
                double targetQtySum = value.stream().filter(item -> item.getTargetQty() != null).mapToDouble(InventoryStrategyVo::getTargetQty).sum();
                stockDto.setTargetStock(targetQtySum);
            }
            safetyStockDtos.add(stockDto);
        }
        int batchNum = 1000;
        // 总数据量
        int insertLength = safetyStockDtos.size();
        int i = 0;
        while (insertLength > batchNum)
        {
            inventoryStrategyDao.insertSafetyStockList(safetyStockDtos.subList(i, i + batchNum));
            i = i + batchNum;
            insertLength = insertLength - batchNum;
        }
        // 保存首次或最后一次数据量不足"批次最大保存数量"的数据
        if (insertLength > 0)
        {
            inventoryStrategyDao.insertSafetyStockList(safetyStockDtos.subList(i, i + insertLength));
        }
    }


    /**
     * 获取查询条件开始结束时间
     * @param day  天数
     * @param type 类型： 过去/未来
     * @return
     */
    public String getStartAndEndDay(Integer day, String type)
    {
        Date now = new Date();
        Calendar instance = Calendar.getInstance();
        if (type.equals("过去"))
        {
            instance.add(Calendar.DATE, -day);
        }
        else
        {
            instance.add(Calendar.DATE, day);
        }
        Date time = instance.getTime();
        String startTime = DateUtils.formatDate(time, DateUtils.YMD_DASH);
        String nowTime = DateUtils.formatDate(now, DateUtils.YMD_DASH);
        return startTime + "_" + nowTime;
    }

    /**
     * 获取上几月份的开始时间和结束时间
     */
    public String getMonthStartAndEndTime(Integer lastMonth)
    {
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.MONTH, -lastMonth + 1);
        int minimum = instance.getActualMinimum(Calendar.DATE);
        instance.set(Calendar.DAY_OF_MONTH, minimum);
        Date startTime = instance.getTime();
        String startDate = DateUtils.formatDate(startTime, DateUtils.YMD_DASH);
        int maximum = instance.getActualMaximum(Calendar.DATE);
        instance.set(Calendar.DAY_OF_MONTH, maximum);
        Date endTime = instance.getTime();
        String endDate = DateUtils.formatDate(endTime, DateUtils.YMD_DASH);
        return startDate + "_" + endDate;
    }

    /**
     *
     * 查询过去几个月的日均销量
     * @param   skuCodes：产品编码集合
     * @return
     * @throws Exception
     */
    public Map<String, List<SalesVolume>> getLastData(String skuCodes) throws Exception
    {
        Map<String, List<SalesVolume>> DATA = new HashMap<>();
        //查询dataq条件参数
//        Map<String, Object> body = new HashMap<>();
//        body.put("bizDateType", "DAY");
        //日均销量天数
        String startAndEndDay1 = getMonthStartAndEndTime(1);
        String startAndEndDay2 = getMonthStartAndEndTime(2);
        String startAndEndDay3 = getMonthStartAndEndTime(3);
        String[] s1 = startAndEndDay1.split("_");
        String[] s2 = startAndEndDay2.split("_");
        String[] s3 = startAndEndDay3.split("_");
//        body.put("start_date", s1[0]);
//        body.put("end_date", s1[1]);
//        if (StringUtils.isNotEmpty(skuCodes))
//        {
//            body.put("skuCodes", skuCodes);
//        }
        SalesVolume salesVolume = new SalesVolume();
        salesVolume.setBizDateType("DAY");
        salesVolume.setStartDate(s1[0]);
        salesVolume.setEndDate(s1[1]);
        List<SalesVolume> salesVolumes1 = dataqInventoryDao.querySalesVolumeList(salesVolume);
        DATA.put("oneLastMonth", salesVolumes1);
        salesVolume.setStartDate(s2[0]);
        salesVolume.setEndDate(s2[1]);
        List<SalesVolume> salesVolumes2 = dataqInventoryDao.querySalesVolumeList(salesVolume);
        DATA.put("threeLastMonth", salesVolumes2);
        salesVolume.setStartDate(s3[0]);
        salesVolume.setEndDate(s3[1]);
        List<SalesVolume> salesVolumes3 = dataqInventoryDao.querySalesVolumeList(salesVolume);
        DATA.put("twoLastMonth", salesVolumes3);
        // dataq接口一次最多返回5W数据，当前生产数据约4W，存在查询丢失数据风险，修改为直接查询数据库
        /**
         //条件参数
         String pathLast = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_INVSTRAT_WAREHOUSE_SALES_VOLUME"));
         JSONArray lastJson1 = (JSONArray) dataqService.invoke(HttpMethod.POST, pathLast, null, null, body).getData();
         List<SalesVolume> salesVolumes1 = new ArrayList<>();
         if (Objects.nonNull(lastJson1))
         {
         salesVolumes1 = JSONArray.parseArray(lastJson1.toJSONString(), SalesVolume.class);
         }


         DATA.put("oneLastMonth", salesVolumes1);

         body.put("start_date", s2[0]);
         body.put("end_date", s2[1]);
         JSONArray lastJson2 = (JSONArray) dataqService.invoke(HttpMethod.POST, pathLast, null, null, body).getData();
         List<SalesVolume> salesVolumes2 = new ArrayList<>();
         if (Objects.nonNull(lastJson1))
         {
         salesVolumes2 = JSONArray.parseArray(lastJson2.toJSONString(), SalesVolume.class);
         }
         DATA.put("twoLastMonth", salesVolumes2);

         body.put("start_date", s3[0]);
         body.put("end_date", s3[1]);
         JSONArray lastJson3 = (JSONArray) dataqService.invoke(HttpMethod.POST, pathLast, null, null, body).getData();
         List<SalesVolume> salesVolumes3 = new ArrayList<>();
         if (Objects.nonNull(lastJson1))
         {
         salesVolumes3 = JSONArray.parseArray(lastJson3.toJSONString(), SalesVolume.class);
         }
         DATA.put("threeLastMonth", salesVolumes3);
         */
        return DATA;

    }

    /**
     * 获取库存策略配置信息
     * @return Map<String, Object>
     */
    public Map<String, Object> getConfig()
    {

        Map<String, Object> configList = new HashMap<>();

        List<InventoryStrategyConfDto> inventoryStrategyConfDtos = inventoryStrategyDao.queryInventoryStrategyConfList();
        //M-3月出库量占比
        Integer OUTBOUND_RATIO_3 = 0;

        //M-2月出库量占比
        Integer OUTBOUND_RATIO_2 = 0;

        //M-1月出库量占比
        Integer OUTBOUND_RATIO_1 = 0;

        //分仓需求计划量占比
        Integer PLAN_DEMAND_RATIO = 0;

        //服务水平
        Double SERVICE_LEVEL = 0.0;

        for (InventoryStrategyConfDto inventoryStrategyConfDto : inventoryStrategyConfDtos)
        {
            String configName = inventoryStrategyConfDto.getConfigName();
            String configValue = inventoryStrategyConfDto.getConfigValue();
            switch (configName)
            {
                case "OUTBOUND_RATIO_3":
                    OUTBOUND_RATIO_3 = Integer.valueOf(configValue);
                    configList.put("OUTBOUND_RATIO_3", OUTBOUND_RATIO_3);
                    break;
                case "OUTBOUND_RATIO_2":
                    OUTBOUND_RATIO_2 = Integer.valueOf(configValue);
                    configList.put("OUTBOUND_RATIO_2", OUTBOUND_RATIO_2);
                    break;
                case "OUTBOUND_RATIO_1":
                    OUTBOUND_RATIO_1 = Integer.valueOf(configValue);
                    configList.put("OUTBOUND_RATIO_1", OUTBOUND_RATIO_1);
                    break;
                case "PLAN_DEMAND_RATIO":
                    PLAN_DEMAND_RATIO = Integer.valueOf(configValue);
                    configList.put("PLAN_DEMAND_RATIO", PLAN_DEMAND_RATIO);
                    break;
                case "SERVICE_LEVEL":
                    SERVICE_LEVEL = Double.valueOf(configValue);
                    configList.put("SERVICE_LEVEL", SERVICE_LEVEL);
                    break;
            }
        }
        return configList;
    }


    /**
     * 更新日均销量数据,并调用dataq接口修改
     * 20240426 由于申晓新与阿里沟通，算法使用他提供的视图，此处不再需要同步更新数据
     * 另外说明：分仓需求计划量占比PLAN_DEMAND_RATIO参数被废弃，视图里计算日均销量使用的是当月日均出库量占比OUTBOUND_RATIO参数
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void updateDailySales() throws Exception
    {
        //获取配置信息
        Map<String, Object> config = this.getConfig();
        //M-3月 该月占比+M-2月日均出库量*该月占比+M-1月日均出库量*该月占比+本月日均分仓需求计划量*该月占比
        Integer OUTBOUND_RATIO_3 = Integer.valueOf(config.get("OUTBOUND_RATIO_3").toString());
        //M-2月出库量占比
        Integer OUTBOUND_RATIO_2 = Integer.valueOf(config.get("OUTBOUND_RATIO_2").toString());
        //M-1月出库量占比
        Integer OUTBOUND_RATIO_1 = Integer.valueOf(config.get("OUTBOUND_RATIO_1").toString());
        //分仓需求计划量占比
        //本月日均分仓需求计划该月占比
        Integer PLAN_DEMAND_RATIO = Integer.valueOf(config.get("PLAN_DEMAND_RATIO").toString());

        //日均销量
        //必填，正整数，全部占比合计必须等于100%  Todo 需求变更了
        //依次M-3月出库量占比、M-2月出库量占比、M-1月出库量占比以及本月分仓需求计划量占比用于计算日均销量时的取数范围；
        // 日均销量=M-3月日均出库量*该月占比+M-2月日均出库量*该月占比+M-1月日均出库量*该月占比+本月日均分仓需求计划量*该月占比
        //日均销量取数时应获取生产编码关系规则中关联的全部产品；
        //M-3月、M-2月、M-1月取当月+仓库+SKU的全部出库量的日平均值(发生天数以实际为准，未发生的不计入)
        //本月分仓需求计划优先取当月第一个已共识完成版本的分仓需求量的日平均值用于计算日均销量，
        // 若当月没有已共识完成版本则取上一个月最后一个已共识完成版本；并且日平均值的计算仅计算当月全部日期的需求数量的平均值；

        //安全库存天数	安全库存数量(算法输出)/日均销量
        //周转库存天数	默认为7天，可通过库存策略刷新功能进行修改
        //质检库存天数	默认为11天
        //周转库存数量	周转库存天数*日均销量
        //质检库存数量	质检库存天数*日均销量
        Double outboundNum1 = 0.0;
        Double outboundNum2 = 0.0;
        Double outboundNum3 = 0.0;


        PageCondition<InventoryStrategyConditionVo> condition = new PageCondition<>();
        condition.setCondition(new InventoryStrategyConditionVo());
        condition.setPageNum(1);
        condition.setPageSize(999999);
        List<InventoryStrategyVo> inventoryStrategyVoList = this.queryInventoryStrategyList(condition).getList();

        //获取日均出库量  M-1月取当月+仓库+SKU的全部出库量的日平均值
        //获取日均出库量  M-2月取当月+仓库+SKU的全部出库量的日平均值
        //获取日均出库量  M-3月取当月+仓库+SKU的全部出库量的日平均值
        String skuCodes = inventoryStrategyVoList.stream().map(item -> item.getSkuCode()).collect(Collectors.joining(","));

        Map<String, List<SalesVolume>> lastData = this.getLastData(skuCodes);

        for (InventoryStrategyVo inventoryStrategyVo : inventoryStrategyVoList)
        {
            String warehouseCode = inventoryStrategyVo.getWarehouseCode();
            String skuCode = inventoryStrategyVo.getSkuCode();

            //M-1
            List<SalesVolume> collect1 = lastData.get("oneLastMonth").stream().filter(item -> item.getSkuCode().equals(skuCode))
                .filter(item -> item.getWarehouseCode().equals(warehouseCode)).collect(Collectors.toList());
            if (collect1.size() > 0)
            {
                outboundNum1 = collect1.get(0).getOutboundNum();
            }
            else
            {
                outboundNum1 = 0.0;
            }
            //M-2
            List<SalesVolume> collect2 = lastData.get("twoLastMonth").stream().filter(item -> item.getSkuCode().equals(skuCode))
                .filter(item -> item.getWarehouseCode().equals(warehouseCode)).collect(Collectors.toList());
            if (collect2.size() > 0)
            {
                outboundNum2 = collect2.get(0).getOutboundNum();
            }
            else
            {
                outboundNum2 = 0.0;
            }
            //M-3
            List<SalesVolume> collect3 = lastData.get("threeLastMonth").stream().filter(item -> item.getSkuCode().equals(skuCode))
                .filter(item -> item.getWarehouseCode().equals(warehouseCode)).collect(Collectors.toList());
            if (collect3.size() > 0)
            {
                outboundNum3 = collect3.get(0).getOutboundNum();
            }
            else
            {
                outboundNum3 = 0.0;
            }
            //todo  获取本月分仓需求计划优先取当月第一个已共识完成版本的分仓需求量的日平均值用于计算日均销量，
            // 若当月没有已共识完成版本则取上一个月最后一个已共识完成版本；并且日平均值的计算仅计算当月全部日期的需求数量的平均值
            Double demandPlan = 0.0;

            double salesDaily =
                outboundNum1 * OUTBOUND_RATIO_1 + outboundNum2 * OUTBOUND_RATIO_2 + outboundNum3 * OUTBOUND_RATIO_3 + PLAN_DEMAND_RATIO * demandPlan;
            inventoryStrategyVo.setDasNum(salesDaily);

        }

        //调用dataq接口清除日均销量的数据
        //http://47.98.52.48/elastic-15561-551/znyy_test/ryytn_dev/inv-strat/daily-sales/clean
        String pathDelete = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_INVSTRAT_DAILY_SALES_CLEAN"));
        dataqService.invoke(HttpMethod.POST, pathDelete, null, null, null);

        //调用dataq接口更新日均销量
        List<Map> saveSalesDaily = new ArrayList<>();
        inventoryStrategyVoList.stream()
            .filter(item -> item.getDasNum() != null)
            .filter(item -> item.getDasNum() > 0.0).forEach(item -> {
                Map<String, Object> single = new HashMap<>();
                single.put("skuCode", item.getSkuCode());
                single.put("warehouseCode", item.getWarehouseCode());
                single.put("dasNum", item.getDasNum());
                saveSalesDaily.add(single);
            });
        String pathSave = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_INVSTRAT_DAILY_SALES_SAVE_BATCH"));

        //根据数据条数，分批调用
        int batchNum = 2000;

        Map<String, Object> body = new HashMap<>();

        if (saveSalesDaily.size() > 2000)
        {
            // 总数据量
            int insertLength = saveSalesDaily.size();
            int i = 0;
            while (insertLength > batchNum)
            {
                body.put("data", saveSalesDaily.subList(i, i + batchNum));
                dataqService.invoke(HttpMethod.POST, pathSave, null, null, body);
                i = i + batchNum;
                insertLength = insertLength - batchNum;
            }
            // 保存首次或最后一次数据量不足"批次最大保存数量"的数据
            if (insertLength > 0)
            {
                body.put("data", saveSalesDaily.subList(i, i + insertLength));
                dataqService.invoke(HttpMethod.POST, pathSave, null, null, body);
            }
        }
        else
        {
            body.put("data", saveSalesDaily);
            dataqService.invoke(HttpMethod.POST, pathSave, null, null, body);
        }

    }
}
