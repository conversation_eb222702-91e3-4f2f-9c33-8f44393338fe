package cn.aliyun.ryytn.modules.distribution.service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.excel.util.StringUtils;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.distribution.api.WarehouseRuleService;
import cn.aliyun.ryytn.modules.distribution.dao.WarehouseRuleDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PhysicWarehouseVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct;
import cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleCapacity;
import cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleVo;
import cn.aliyun.ryytn.modules.scheduler.api.SchedulerService;
import cn.aliyun.ryytn.modules.system.api.ProductService;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 仓能力规则接口
 * <AUTHOR>
 * @date 2023/11/18 19:16
 */
@Slf4j
@Service
public class WarehouseRuleServiceImpl implements WarehouseRuleService
{

    @Autowired
    private WarehouseRuleDao warehouseRuleDao;

    @Autowired
    private ProductService productService;

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SchedulerService schedulerService;

    /**
     *
     * @Description 查询仓能力规则列表数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/19 17:28
     */
    @Override
    public List<WarehouseRuleDto> queryWarehouseRuleList(WarehouseRuleDto warehouseRuleDto) throws Exception
    {
        List<WarehouseRuleDto> warehouseRuleDtos = warehouseRuleDao.queryWarehoueRuleList(warehouseRuleDto);
        warehouseRuleDtos = warehouseRuleDtos.stream().sorted((a, b) -> {
            //先根据效期 1 已生效  0 未生效  2 已过期  再根据创建时间倒序
            if (a.getStatus() == 1)
            {
                return b.getStatus() == 1 ? 1 : -1;
            }
            else if (b.getStatus() == 1)
            {
                return a.getStatus() == 1 ? -1 : 1;
            }
            else
            {
                return a.getStatus() - b.getStatus();
            }
        }).collect(Collectors.toList());
        return warehouseRuleDtos;
    }

    /**
     *
     * @Description 查询仓能力规则详情数据
     * @param  id
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/19 17:28
     */
    @Override
    public WarehouseRuleVo queryWarehouseRuleDetail(String id) throws Exception
    {
        //查主表以及关联子表数据
        WarehouseRuleVo warehouseRuleVo = warehouseRuleDao.queryWarehouseRuleDetail(id);
        List<WarehouseRuleCapacity> collect =
            warehouseRuleVo.getCapacityList().stream().sorted(Comparator.comparing(WarehouseRuleCapacity::getWarehouseCode)).collect(Collectors.toList());
        warehouseRuleVo.setCapacityList(collect);
        List<RuleCategory> categoryList = warehouseRuleVo.getCategoryList();
        if (categoryList != null && categoryList.size() > 0)
        {
            if (categoryList.get(0).getId() == null)
            {
                warehouseRuleVo.setCategoryList(null);
            }
        }

        List<RuleProduct> productList = warehouseRuleVo.getProductList();
        if (productList != null && productList.size() > 0)
        {
            if (productList.get(0).getId() == null)
            {
                warehouseRuleVo.setProductList(null);
            }
        }
        return warehouseRuleVo;
    }

    /**
     *
     * @Description 删除仓能力规则数据
     * @param  id
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/19 17:28
     */
    @Override
    public void deleteWarehouseRule(String id) throws Exception
    {
        //删除表数据
        warehouseRuleDao.deleteWarehouseRuleById(id);
        warehouseRuleDao.deleteWarehouseRuleRangeByRuleId(id);
        warehouseRuleDao.deleteWarehouseRuleCategoryByRuleId(id);
        warehouseRuleDao.deleteWarehouseRuleProductByRuleId(id);

    }

    /**
     *
     * @Description 新增、编辑仓能力规则
     * @param  warehouseRuleVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/19 17:28
     */
    @Override
    public void addOrUpdateWarehouseRule(WarehouseRuleVo warehouseRuleVo) throws Exception
    {
        int num = warehouseRuleDao.countWarehouseRuleName(warehouseRuleVo);
        if (num > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }

        //删除关联表，新增
        this.packSkuCodesField(warehouseRuleVo);
        String id = warehouseRuleVo.getId();
        //二、入自己的表
        if (id != null)
        {
            //修改
            warehouseRuleDao.deleteWarehouseRuleRangeByRuleId(id);
            warehouseRuleDao.deleteWarehouseRuleProductByRuleId(id);
            warehouseRuleDao.deleteWarehouseRuleCategoryByRuleId(id);
            warehouseRuleDao.deleteStockCapacity(id);
            warehouseRuleVo.setUpdatedBy("修改");
            warehouseRuleVo.setUpdatedTime(new Date());
            if (warehouseRuleVo.getForeverFlag() == 1)
            {
                warehouseRuleVo.setStartTime(null);
                warehouseRuleVo.setEndTime(null);
            }
            warehouseRuleDao.updateWarehouseRule(warehouseRuleVo);
        }
        else
        {
            //新增
            //添加控制 通用规则只能有一个
//            if (warehouseRuleVo.getName().equals("通用规则") || warehouseRuleVo.getRangeType() == 2)
//            {
//                List<WarehouseRuleDto> warehouseRuleDtos = this.queryWarehouseRuleList(null);
//                if (!CollectionUtils.isEmpty(warehouseRuleDtos))
//                {
//                    List<WarehouseRuleDto> specialList =
//                        warehouseRuleDtos.stream().filter(item -> item.getName().equals("通用规则") || item.getRangeType() == 2).collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(specialList))
//                    {
//                        throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
//                    }
//                }
//            }

            id = SeqUtils.getSequenceUid();
            warehouseRuleVo.setId(id);
            warehouseRuleVo.setCreatedBy("新增");
            warehouseRuleVo.setCreatedTime(new Date());
            warehouseRuleDao.insertWarehouseRule(warehouseRuleVo);
        }

        List<WarehouseRuleCapacity> warehouseRuleCapacityList = warehouseRuleVo.getCapacityList();
        for (WarehouseRuleCapacity warehouseRuleCapacity : warehouseRuleCapacityList)
        {
            warehouseRuleCapacity.setId(SeqUtils.getSequenceUid());
            warehouseRuleCapacity.setRuleId(id);
        }
        warehouseRuleDao.insertWarehouseRuleRangeList(warehouseRuleCapacityList);

        List<RuleCategory> categoryList = warehouseRuleVo.getCategoryList();
        if (categoryList != null && categoryList.size() > 0)
        {
            for (RuleCategory ruleCategory : categoryList)
            {
                ruleCategory.setId(SeqUtils.getSequenceUid());
                ruleCategory.setRuleId(id);
            }
            warehouseRuleDao.insertWarehouseRuleCategoryList(categoryList);
        }

        List<RuleProduct> productList = warehouseRuleVo.getProductList();
        if (productList != null && productList.size() > 0)
        {

            for (RuleProduct ruleProduct : productList)
            {
                ruleProduct.setId(SeqUtils.getSequenceUid());
                ruleProduct.setRuleId(id);
            }
            warehouseRuleDao.insertWarehouseRuleProductList(productList);
        }

        // 触发同步阿里算法仓容中间表定时任务
        schedulerService.triggerSchedulerJob(CommonConstants.REFRESH_SO_WT_STOCK_CAPACITY_JOBID);
    }

    /**
     * 更新库存能力状态
     */
    @Override
    public void updateStockCapacityStatus()
    {
        warehouseRuleDao.updateStockCapacityStatus();
    }

    /**
     *
     * @Description 查询仓能力规则仓库
     * @return
     * <AUTHOR>
     * @date 2024年01月02日 15:23     */
    @Override
    public List<PhysicWarehouseVo> queryWarehouseRuleWarehouse()
    {
        return warehouseRuleDao.queryWarehouseRuleWarehouse();
    }

    @Override
    public List<PhysicWarehouseVo> queryAdjustableDaysRuleWarehouse()
    {
        return warehouseRuleDao.queryAdjustableDaysRuleWarehouse();
    }

    public void packSkuCodesField(WarehouseRuleVo warehouseRuleVo) throws Exception
    {
        List<RuleCategory> categoryList = warehouseRuleVo.getCategoryList();
        //查询基础产品数据
        List<SkuDto> produvctVoList = productService.querySkuList(null);
        //根据等级赋值 SkuCodes 字段
        if (categoryList == null || categoryList.size() < 1)
        {
            return;
        }
        for (RuleCategory ruleCategory : categoryList)
        {
            Integer level = ruleCategory.getLevel();
            String categoryCode = ruleCategory.getCategoryCode();
            String skuCodes = "";
            switch (level)
            {
                case 1:
                    skuCodes =
                        produvctVoList.stream().filter(item -> StringUtils.equals(categoryCode, item.getLv1CategoryCode())).map(item -> item.getSkuCode())
                            .distinct().collect(Collectors.joining(","));
                    break;
                case 2:
                    skuCodes =
                        produvctVoList.stream().filter(item -> StringUtils.equals(categoryCode, item.getLv2CategoryCode())).map(item -> item.getSkuCode())
                            .distinct().collect(Collectors.joining(","));
                    break;
                case 3:
                    skuCodes =
                        produvctVoList.stream().filter(item -> StringUtils.equals(categoryCode, item.getLv3CategoryCode())).map(item -> item.getSkuCode())
                            .distinct().collect(Collectors.joining(","));
                    break;
            }
            ruleCategory.setSkuCodes(skuCodes);
        }

    }

}
