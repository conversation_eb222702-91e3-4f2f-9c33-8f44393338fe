package cn.aliyun.ryytn.modules.distribution.task;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.modules.distribution.api.DailyWarehouseDemandService;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 日分仓数据重新刷任务
 * <AUTHOR>
 * @date 2024/08/28 9:37
 */
@Slf4j
@Service
public class DailyWarehouseRefreshTaskServiceImpl  implements TaskService{


    @Autowired
    private DailyWarehouseDemandDao dailyWarehouseDemandDao;
    @Autowired
    private DailyWarehouseDemandService dailyWarehouseDemandService;
    @Override
    public void process(SchedulerJob schedulerJob) throws Exception {
        try{

            List<DailyWarehouseDemandDto> listTask =  dailyWarehouseDemandDao.queryRefreshDailyDemandPlanList();
            for(DailyWarehouseDemandDto t : listTask){
                QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo = new QueryDailyWarehouseDemandListReqVo();
                queryDailyWarehouseDemandListReqVo.setDemandPlanCode(t.getDemandPlanCode());
                queryDailyWarehouseDemandListReqVo.setDemandPlanVersion(t.getDemandPlanVersion());
                dailyWarehouseDemandService.addDailyWarehouseDemandList(queryDailyWarehouseDemandListReqVo,true);
            }
        }catch(Exception e){
            log.error("error日分仓需求计划刷新失败",e);
        }
    }
}
