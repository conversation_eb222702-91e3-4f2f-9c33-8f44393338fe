package cn.aliyun.ryytn.modules.distribution.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.distribution.api.InventoryStrategyService;
import cn.aliyun.ryytn.modules.distribution.entity.dto.GraphKcclConfigDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyConfDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 库存策略
 * <AUTHOR>
 * @date 2023/11/20 20:27
 */
@Slf4j
@RestController
@RequestMapping("/api/distribution/inventoryStrategy")
@Api(tags = "库存策略")
public class InventoryStrategyController
{

    /**
     * 库存策略接口
     */
    @Autowired
    private InventoryStrategyService inventoryStrategyService;

    /**
     *
     * @Description 查询库存策略配置列表
     * @return ResultInfo<List < InventoryStrategyConfDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/20 20:27
     */
    @PostMapping("queryInventoryStrategyConfList")
    @ResponseBody
    @ApiOperation("查询库存策略配置列表")
//    @RequiresPermissions({})
    public ResultInfo<List<InventoryStrategyConfDto>> queryInventoryStrategyConfList() throws Exception
    {
        return ResultInfo.success(inventoryStrategyService.queryInventoryStrategyConfList());
    }

    /**
     *
     * @Description 更新库存策略配置
     * @return ResultInfo<List < InventoryStrategyConfDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/20 20:27
     */
    @PostMapping("updateInventoryStrategyConf")
    @ResponseBody
    @ApiOperation("更新库存策略配置")
    @Transactional(rollbackFor = Exception.class)
//    @RequiresPermissions({})
    public ResultInfo<?> updateInventoryStrategyConf(@RequestBody List<InventoryStrategyConfDto> inventoryStrategyConfDtos) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(inventoryStrategyConfDtos);

        inventoryStrategyService.updateInventoryStrategyConf(inventoryStrategyConfDtos);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 分页查询库存策略数据列表
     * @return ResultInfo<PageInfo < InventoryStrategyDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/20 20:27
     */
    @PostMapping("queryInventoryStrategyList")
    @ResponseBody
    @ApiOperation("分页查询库存策略数据列表")
//    @RequiresPermissions({})
    public ResultInfo<PageInfo<InventoryStrategyVo>> queryInventoryStrategyList(@RequestBody PageCondition<InventoryStrategyConditionVo> condition)
        throws Exception
    {

        ValidateUtil.checkIsNotEmpty(condition);

        return ResultInfo.success(inventoryStrategyService.queryInventoryStrategyList(condition));
    }

    /**
     *
     * @Description 查询库存策略数据详情
     * @param graphKcclConfig
     * @return ResultInfo <InventoryStrategyDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/22 14:05
     */
    @PostMapping("queryInventoryStrategyDetail")
    @ResponseBody
    @ApiOperation("查询库存策略数据详情")
    @RequiresPermissions({})
    public ResultInfo<List<GraphKcclConfigDto>> queryInventoryStrategyDetail(@RequestBody GraphKcclConfigDto graphKcclConfig) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(graphKcclConfig);
        ValidateUtil.checkIsNotEmpty(graphKcclConfig.getSkuCode());
        ValidateUtil.checkIsNotEmpty(graphKcclConfig.getWarehouseCode());

        return ResultInfo.success(inventoryStrategyService.queryInventoryStrategyDetail(graphKcclConfig));
    }

    /**
     *
     * @Description 更新库存策略数据
     * @param graphKcclConfigList
     * @return ResultInfo <?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/22 14:05
     */
    @PostMapping("updateInventoryStrategy")
    @ResponseBody
    @ApiOperation("更新库存策略数据")
//    @RequiresPermissions({})
    public ResultInfo<?> updateInventoryStrategy(@RequestBody List<GraphKcclConfigDto> graphKcclConfigList) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(graphKcclConfigList);
        Map<Integer, List<GraphKcclConfigDto>> map = graphKcclConfigList.stream().collect(Collectors.groupingBy(GraphKcclConfigDto::getIsCustom));
        // 非特殊参数的数据有且仅有1个
        if (!map.containsKey(0) || map.get(0).size() != 1)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }
        // 特殊参数的数据要没有，要么只有1个
        if (map.containsKey(1) && map.get(1).size() != 1)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }

        inventoryStrategyService.updateInventoryStrategy(graphKcclConfigList);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询库存策略表头下拉列表
     * @param inventoryStrategyVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月03日 17:24     */
    @PostMapping("queryInventoryStrategyHeadSelect")
    @ResponseBody
    @ApiOperation("查询库存策略表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<InventoryStrategyVo>> queryInventoryStrategyHeadSelect(
        @RequestBody InventoryStrategyVo inventoryStrategyVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(inventoryStrategyVo);

        List<InventoryStrategyVo> result = inventoryStrategyService.queryInventoryStrategyHeadSelect(inventoryStrategyVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @param inventoryStrategyVo
     * @return
     * @throws Exception
     */
    @PostMapping("inventoryStrategyRealityManage")
    @ResponseBody
    @ApiOperation("日均销量(实际)")
    public ResultInfo<?> inventoryStrategyRealityManage(@RequestBody InventoryStrategyVo inventoryStrategyVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(inventoryStrategyVo.getOperateType());
        ValidateUtil.checkIsNotEmpty(inventoryStrategyVo.getSkuCode());
        ValidateUtil.checkIsNotEmpty(inventoryStrategyVo.getWarehouseCode());
        if (!CommonConstants.DELETE.equals(inventoryStrategyVo.getOperateType())) {
            ValidateUtil.checkIsNotEmpty(inventoryStrategyVo.getSalesDailyReality());
        }
        inventoryStrategyService.inventoryStrategyRealityManage(inventoryStrategyVo);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 分页查询库存策略视图数据列表
     * @param condition
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月04日 16:25     */
    @PostMapping("pageInventoryStrategyView")
    @ResponseBody
    @ApiOperation("分页查询库存策略视图数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<InventoryStrategyVo>> pageInventoryStrategyView(
        @RequestBody PageCondition<InventoryStrategyVo> condition) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());

        PageInfo<InventoryStrategyVo> result = inventoryStrategyService.pageInventoryStrategyView(condition);
        return ResultInfo.success(result);
    }

}
