package cn.aliyun.ryytn.modules.distribution.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.modules.demand.entity.vo.*;
import cn.aliyun.ryytn.modules.distribution.api.SkuAbcTypeService;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.concurrent.ThreadPoolExecutorUtils;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.mybatis.MybatisUtils;
import cn.aliyun.ryytn.common.utils.page.PageUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandPlanService;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.constant.SubjectTypeEnum;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqWarehouseDemandPlanDao;
import cn.aliyun.ryytn.modules.distribution.api.AsyncDailyWarehouseService;
import cn.aliyun.ryytn.modules.distribution.api.DailyWarehouseDemandService;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseAiplanDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseDemandDateVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.GenerateDailyWarehouseAiplanDemandVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SkuValidRuleVo;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWeekListReqVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 日分仓需求实现类
 * <AUTHOR>
 * @date 2023/12/4 14:19
 */
@Slf4j
@Service
public class DailyWarehouseDemandServiceImpl implements DailyWarehouseDemandService
{
    @Autowired
    DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    DataqService dataqService;

    @Autowired
    SkuAbcTypeService skuAbcTypeService;

    @Autowired
    CalendarService calendarService;

    @Autowired
    private MybatisUtils mybatisUtils;

    @Autowired
    private WarehouseDemandPlanService warehouseDemandPlanService;

    @Autowired
    private AsyncDailyWarehouseService asyncDailyWarehouseService;

    @Autowired
    private DataqWarehouseDemandPlanDao dataqWarehouseDemandPlanDao;

    private static final String TOB = "TOB";

    private static final String TOC = "TOC";

    private static final Integer TOBCode = 0;

    private static final Integer TOCCode = 1;

    private static final String WEEK_ACT_DATA_HEAD = "周数据（更新）";

    private static final String WEEK_RAW_DATA_HEAD = "周数据（原始）";

    /**
     * 生成中数据，防止数据还没有完全创建完，就在新页面查询到不完整的数据
     */
    private static Integer STATUS_INITING = 2;

    /**
     * 正常状态数据
     */
    private static Integer STATUS_NORMAL = 1;

    /**
     *
     * @Description 查询日分仓需求计划列表
     * @return List<DailyWarehouseDemandDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:19
     */
    @Override
    public List<DailyWarehouseDemandDto> queryDailyWarehouseDemandPlanList() throws Exception
    {
        // fix: 1716 产品李伟亮要求直接查询渠道需求计划，可以没有版本数据，但是只要有计划，就要有后续下游数据的计划
//        List<DailyWarehouseDemandDto> result = dailyWarehouseDemandDao.queryDailyWarehouseDemandPlanList();
        JSONArray jsonArray = warehouseDemandPlanService.queryWarehouseDemandPlanList();
        List<DailyWarehouseDemandDto> result = Collections.EMPTY_LIST;
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            result = jsonArray.toJavaList(DailyWarehouseDemandDto.class);
        }
        return result;
    }

    /**
     *
     * @Description 查询日分仓需求版本列表
     * @param dailyWarehouseDemandDto
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:19
     */
    @Override
    public List<String> queryDailyWarehouseDemandVersionList(DailyWarehouseDemandDto dailyWarehouseDemandDto) throws Exception
    {
        List<String> result = dailyWarehouseDemandDao.queryDailyWarehouseDemandVersionList(dailyWarehouseDemandDto);
        if (CollectionUtils.isNotEmpty(result))
        {
            result = result.stream().sorted(Comparator.comparing(item -> StringUtils.substringBefore((String) item,
                StringUtils.DATE_SEPARATOR)).thenComparing(item -> {
                String index = StringUtils.substringAfter((String) item, StringUtils.DATE_SEPARATOR);
                return StringUtils.isBlank(index) ? StringUtils.EMPTY : index;
            }).reversed()).collect(Collectors.toList());
        }
        return result;
    }


    /**
     *
     * @Description 查询日分仓需求列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月11日 16:48     */
    @Override
    public DailyWarehouseDemandBaseTable<List<DailyWarehouseDemandRspVo>> queryDailyWarehouseDemandList(
        QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception
    {
        // 响应数据结构
        DailyWarehouseDemandBaseTable<List<DailyWarehouseDemandRspVo>> baseTable = new DailyWarehouseDemandBaseTable<>();
        queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));

        List<String> headList = queryDailyWarehouseDemandHeadList(queryDailyWarehouseDemandListReqVo);
        baseTable.setHeadArray(headList);

        List<DailyWarehouseDemandRspVo> dataList = dailyWarehouseDemandDao.queryDailyWarehouseDemandDataJsonList(queryDailyWarehouseDemandListReqVo);

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (DailyWarehouseDemandRspVo dailyWarehouseDemandRspVo : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(dailyWarehouseDemandRspVo.getData(), PlanValue.class);
            Map<String, PlanValue> planValueMap =
                planValueList.stream().collect(Collectors.toMap(PlanValue::getPlanDate, Function.identity(), (key1, key2) -> key2));

            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (String head : headList)
            {
                PlanValue planValue = null;
                if (StringUtils.endsWith(head, WEEK_RAW_DATA_HEAD))
                {
                    String key = StringUtils.replace(head, WEEK_RAW_DATA_HEAD, StringUtils.EMPTY);
                    planValue = new PlanValue();
                    planValue.setPlanValue(planValueMap.get(key).getWeekRawValue());
                }
                else if (StringUtils.endsWith(head, WEEK_ACT_DATA_HEAD))
                {
                    String key = StringUtils.replace(head, WEEK_ACT_DATA_HEAD, StringUtils.EMPTY);
                    planValue = new PlanValue();
                    planValue.setPlanValue(planValueMap.get(key).getWeekActualValue());
                }
                else
                {
                    planValue = planValueMap.get(head);
                }
                dailyWarehouseDemandRspVo.getDataMap().put(head, planValue);
            }
            dailyWarehouseDemandRspVo.setData(null);
        }

        baseTable.setList(dataList);

        return baseTable;
    }

    /**
     *
     * @Description 新增日分仓需求
     * @param queryDailyWarehouseDemandListReqVo
     * @param rebuild 重新生成
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月11日 16:48     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void addDailyWarehouseDemandList(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo,boolean rebuild) throws Exception
    {
        log.info("start addDailyWarehouseDemandList param:{}==,rebuild{}", JSONObject.toJSONString(queryDailyWarehouseDemandListReqVo),String.valueOf(rebuild));
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo);

        String lockKey = StringUtils.format(CommonConstants.REDIS_GEN_DAILY_DEMAND_LOCK_KEY, queryDailyWarehouseDemandListReqVo.getDemandPlanCode(),
            queryDailyWarehouseDemandListReqVo.getDemandPlanVersion());
        boolean isLock = redisUtils.lock(lockKey, 3600);
        if (!isLock)
        {
            log.info("addDailyWarehouseDemandList has processing in another node.");
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }

        try
        {
            String tableSuffix = StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8);
            queryDailyWarehouseDemandListReqVo.setTableSuffix(tableSuffix);

            // 删除可能出现的因为异常、重启、故障等原因导致线程中断而产生的生成中的数据
            dailyWarehouseDemandDao.deleteDailyWarehouseDemandIniting(queryDailyWarehouseDemandListReqVo);

            // 如果日分仓需求已存在，则直接返回
            int num = dailyWarehouseDemandDao.queryDailyWarehouseDemandPlanExists(queryDailyWarehouseDemandListReqVo);
            if (num > 0 && !rebuild)
            {
                log.error("dailyWarehouseDemand has exists.");
                return;
            }

            // 获取需求计划Code对应的名称
            String path2 = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_PLAN_LIST"));
            QueryWarehouseDemandPlanListReqVo queryWarehouseDemandPlanListReqVo = new QueryWarehouseDemandPlanListReqVo();
            queryWarehouseDemandPlanListReqVo.setSubjectType(SubjectTypeEnum.warehouse);
            DataqResult<?> demandPlanResult = dataqService.invoke(HttpMethod.POST, path2, null, null, queryWarehouseDemandPlanListReqVo);
            JSONArray jsonArray2 = (JSONArray) demandPlanResult.getData();
            List<QueryDailyWarehouseDemandListReqVo> demandPlanVos = jsonArray2.toJavaList(QueryDailyWarehouseDemandListReqVo.class);
            Map<String, String> map = demandPlanVos.stream().
                collect(Collectors.toMap(QueryDailyWarehouseDemandListReqVo::getDemandPlanCode, QueryDailyWarehouseDemandListReqVo::getDemandPlanName));

            // 分仓需求计划 共识日第二天+所在周剩余日期+下4周全部日期
            // 框定查询周数据范围
            String nowDay = DateUtils.formatTime(new Date(), DateUtils.YMD);
            String startDay = DateUtils.formatTime(DateUtils.addDays(new Date(), 1), DateUtils.YMD);
            String endDay = DateUtils.formatTime(DateUtils.addDays(new Date(), 40), DateUtils.YMD);
            if(rebuild){
//                startDay ="20240408";
//                endDay = "20240501";//listMap.get(0).get("enddate");
                List<Map<String,String>> listMap = dailyWarehouseDemandDao.queryDailyWareHouseDateRange(queryDailyWarehouseDemandListReqVo);
                if(null == listMap.get(0) || StringUtils.isEmpty(listMap.get(0).get("startdate"))){
                   log.error("get history dailywarehouseDateRange is null.demand_code{},version:{}",queryDailyWarehouseDemandListReqVo.getDemandPlanCode(),queryDailyWarehouseDemandListReqVo.getDemandPlanVersion());
                   return;
                }
                 startDay =listMap.get(0).get("startdate");
                 endDay = listMap.get(0).get("enddate");
            }

            log.info("general daily warehouse startDay{},endDay{}",startDay,endDay);
            if(rebuild){
                dailyWarehouseDemandDao.deleteDailyWarehouse(queryDailyWarehouseDemandListReqVo);
            }
            QueryWeekListReqVo queryWeekListReqVo = new QueryWeekListReqVo();
            queryWeekListReqVo.setBeginDate(startDay);
            queryWeekListReqVo.setEndDate(endDay);
            List<DataqWeek> dataqWeekList = calendarService.queryWeekList(queryWeekListReqVo);
            Map<String, DataqWeek> dataqWeekMap =
                dataqWeekList.stream().sorted((o1, o2) -> o1.getFsclWeekStart().compareTo(o2.getFsclWeekStart())).limit(5)
                    .collect(Collectors.toMap(DataqWeek::getFsclWeekStart,
                        Function.identity(), (key1, key2) -> key1));
            String beginDate = dataqWeekMap.keySet().stream().min(Comparator.comparing(String::valueOf)).get();
            String endDate = dataqWeekMap.keySet().stream().max(Comparator.comparing(String::valueOf)).get();


            // dataq天坑，接口只能查询返回最多50000条数据，并且不支持扩容。
            // 此处查询平铺的分仓需求计划数据，数据量远超5W，导致查询数据丢失，后续生成数据也丢失。
            // 修改为直接查询数据库。
            // 根据需求计划Code和版本查询分仓需求计划数据
            QueryWarehouseDemandPlanVersionListReqVo queryWarehouseDemandPlanVersionListReqVo = new QueryWarehouseDemandPlanVersionListReqVo();
            queryWarehouseDemandPlanVersionListReqVo.setDemandPlanCode(queryDailyWarehouseDemandListReqVo.getDemandPlanCode());
            queryWarehouseDemandPlanVersionListReqVo.setVersionId(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion());
            queryWarehouseDemandPlanVersionListReqVo.setBeginDate(beginDate);
            queryWarehouseDemandPlanVersionListReqVo.setEndDate(endDate);
            queryWarehouseDemandPlanVersionListReqVo.setIsModify(0);
//        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DEMAND_WAREHOUSE_PLAN_LIST"));
//        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryWarehouseDemandPlanVersionListReqVo);
//        JSONArray jsonArray = (JSONArray) dataqResult.getData();
//        List<QueryWarehouseDemandPlanVersionListRspVo> warehouseDemandPlanVersionDataList =
//            jsonArray.toJavaList(QueryWarehouseDemandPlanVersionListRspVo.class);

            List<QueryWarehouseDemandPlanVersionListRspVo> warehouseDemandPlanVersionDataList =
                dataqWarehouseDemandPlanDao.queryWarehouseDemandPlanList(queryWarehouseDemandPlanVersionListReqVo);
            if (CollectionUtils.isEmpty(warehouseDemandPlanVersionDataList))
            {
                log.warn("warehouseDemandPlanVersionDataList is null");
                return;
            }
            warehouseDemandPlanVersionDataList =
                warehouseDemandPlanVersionDataList.stream().filter(item -> dataqWeekMap.containsKey(StringUtils.replace(item.getPlanDate(),
                    StringUtils.DATE_SEPARATOR, StringUtils.EMPTY))).collect(Collectors.toList());

            log.info("warehouseDemandPlanVersionDataList size is :{}",warehouseDemandPlanVersionDataList.size());
            // 封装周日期，需求计划名称，渠道类型 并将数据转换成dto数据
            List<DailyWarehouseDemandDto> addList = new ArrayList<>();
            for (QueryWarehouseDemandPlanVersionListRspVo warehouseDemandPlanVersionData : warehouseDemandPlanVersionDataList)
            {
                List<DayAcutalDeliveryVo>  listActualDeliveryDays = warehouseDemandPlanVersionData.parseData();
                String demandPlanName = map.get(warehouseDemandPlanVersionData.getDemandPlanCode());
                String weekRecorded = StringUtils.replace(warehouseDemandPlanVersionData.getPlanDate(), StringUtils.DATE_SEPARATOR, StringUtils.EMPTY);
                Integer distributeType = null;
                if (TOB.equalsIgnoreCase(warehouseDemandPlanVersionData.getReceiverType()))
                {
                    distributeType = TOBCode;
                }
                else if (TOC.equalsIgnoreCase(warehouseDemandPlanVersionData.getReceiverType()))
                {
                    distributeType = TOCCode;
                }

                DataqWeek dataqWeek = dataqWeekMap.get(weekRecorded);
                List<DayAcutalDeliveryVo> listActualDeliveryDaysOfWeek = listActualDeliveryDays.stream().filter(x->StringUtils.compare(x.getBizDateValue(), dataqWeek.getFsclWeekStart()) >= 0 && StringUtils.compare(x.getBizDateValue(), dataqWeek.getFsclWeekEnd()) <= 0)
                        .collect(Collectors.toList());
                Map<String, BigDecimal> actualDeliveryDaysOfWeekMap = listActualDeliveryDaysOfWeek.stream().collect(Collectors.toMap(DayAcutalDeliveryVo::getBizDateValue,DayAcutalDeliveryVo::getOutboundNum));
//                日分仓需求编辑，每日查看和管理时已过日期显示灰色并显示当日实际出货量（做过仓还原之后的出货量），
//                当周剩余未过日期按（分仓周计划量-当周出货量）平均拆分至剩余各日，同时小数点部分补全至最后一天。
//                若当周分仓周计划量-当周出货量<0，则剩余各日计划量均为零；若整周都是已过周，则直接显示当周各日实际出货量。
                if(StringUtils.compare(nowDay, dataqWeek.getFsclWeekEnd()) > 0){
                    //所有周都是已过周
                    for (String day : dataqWeek.getDayList())
                    {
                        double dayValue = 0d;
                        DailyWarehouseDemandDto dailyWarehouseDemandDto = new DailyWarehouseDemandDto();
                        // 封装日数据与周数据
                        dailyWarehouseDemandDto.setId(SeqUtils.getSequenceUid());
                        dailyWarehouseDemandDto.setDemandPlanCode(warehouseDemandPlanVersionData.getDemandPlanCode());
                        dailyWarehouseDemandDto.setDemandPlanName(demandPlanName);
                        dailyWarehouseDemandDto.setDemandPlanVersion(warehouseDemandPlanVersionData.getVersionId());
                        dailyWarehouseDemandDto.setSkuCode(warehouseDemandPlanVersionData.getSkuCode());
                        dailyWarehouseDemandDto.setSkuName(warehouseDemandPlanVersionData.getSkuName());
                        dailyWarehouseDemandDto.setDistributeType(distributeType);
                        dailyWarehouseDemandDto.setWarehouseCode(warehouseDemandPlanVersionData.getWarehouseCode());
                        dailyWarehouseDemandDto.setWarehouseName(warehouseDemandPlanVersionData.getWarehouseName());
                        dailyWarehouseDemandDto.setLv1CategoryCode(warehouseDemandPlanVersionData.getLv1CategoryCode());
                        dailyWarehouseDemandDto.setLv1CategoryName(warehouseDemandPlanVersionData.getLv1CategoryName());
                        dailyWarehouseDemandDto.setLv2CategoryCode(warehouseDemandPlanVersionData.getLv2CategoryCode());
                        dailyWarehouseDemandDto.setLv2CategoryName(warehouseDemandPlanVersionData.getLv2CategoryName());
                        dailyWarehouseDemandDto.setLv3CategoryCode(warehouseDemandPlanVersionData.getLv3CategoryCode());
                        dailyWarehouseDemandDto.setLv3CategoryName(warehouseDemandPlanVersionData.getLv3CategoryName());
                        dailyWarehouseDemandDto.setDateRecorded(day);
                        dailyWarehouseDemandDto.setPlanDataType(warehouseDemandPlanVersionData.getPlanDataType());
                        BigDecimal planValue = new BigDecimal(Objects.isNull(warehouseDemandPlanVersionData.getPlanValue()) ? 0d :
                                warehouseDemandPlanVersionData.getPlanValue());
                        BigDecimal actualNum = actualDeliveryDaysOfWeekMap.get(day);
                        if(null == actualNum){
                            dayValue = 0d;
                        }else{
                            dayValue = actualNum.doubleValue();
                        }
//                        BigDecimal dayNum = new BigDecimal(dataqWeek.getDayList().size());
//                        Double avg = planValue.divide(dayNum, 0, RoundingMode.HALF_UP).doubleValue();
                        dailyWarehouseDemandDto.setDateValue(dayValue);
                        dailyWarehouseDemandDto.setWeekRecorded(dataqWeek.getFsclWeekStart());
                        dailyWarehouseDemandDto.setWeekRawValue(planValue.doubleValue());
                        dailyWarehouseDemandDto.setWeekActualValue(planValue.doubleValue());
                        dailyWarehouseDemandDto.setStatus(STATUS_INITING);
                        dailyWarehouseDemandDto.setTableSuffix(tableSuffix);
                        addList.add(dailyWarehouseDemandDto);
                    }
                }else{
                    //所有周都是未过周;前半是已过周,后半是未过周
                    int pointNum = 0;//标记第几个
                    for(int i = 0; i < dataqWeek.getDayList().size();i++){
                        String day =  dataqWeek.getDayList().get(i);
                        if(day.equals(nowDay)){
                            pointNum = i+1;
                            break;
                        }
                    }
                    Double historyDeliverNum = 0d; //记录实发的总计数据值
                    Double planValueSum = 0d;//记录计划已经计算总计的值
                    for (int i = 0 ; i < dataqWeek.getDayList().size();i++)
                    {
                        String day = dataqWeek.getDayList().get(i);
                        double dayValue = 0d;
                        if(i < pointNum){
                            //取历史实发
                            BigDecimal actualNum = actualDeliveryDaysOfWeekMap.get(day);
                            if(null == actualNum){
                                dayValue = 0d;
                            }else{
                                dayValue = actualNum.doubleValue();
                            }
                            historyDeliverNum  = historyDeliverNum +dayValue;
                        }else{
                            //取平均值
                            BigDecimal planValue = new BigDecimal(Objects.isNull(warehouseDemandPlanVersionData.getPlanValue()) ? 0d :
                                    warehouseDemandPlanVersionData.getPlanValue());

                            if((planValue.doubleValue() - historyDeliverNum) <= 0){
                                //如果计划-实发 <=0 ,则数值取0;
                                dayValue = 0d;
                            }else{
                                BigDecimal dayNum = new BigDecimal(dataqWeek.getDayList().size()-pointNum);
                                Double avg = new BigDecimal(planValue.doubleValue()-historyDeliverNum).divide(dayNum, 0, RoundingMode.HALF_UP).doubleValue();
                                if(i == (dataqWeek.getDayList().size()-1)){
                                    dayValue = planValue.doubleValue() - planValueSum;
                                }else{
                                    dayValue = avg;
                                    planValueSum = planValueSum + avg;
                                }
                            }
                        }

                        if (StringUtils.compare(day, startDay) < 0)
                        {//如果是前半周和当前日期+1比较,如果在之前,只计算不增加数据;
                            continue;
                        }
                        DailyWarehouseDemandDto dailyWarehouseDemandDto = new DailyWarehouseDemandDto();
                        // 封装日数据与周数据
                        dailyWarehouseDemandDto.setId(SeqUtils.getSequenceUid());
                        dailyWarehouseDemandDto.setDemandPlanCode(warehouseDemandPlanVersionData.getDemandPlanCode());
                        dailyWarehouseDemandDto.setDemandPlanName(demandPlanName);
                        dailyWarehouseDemandDto.setDemandPlanVersion(warehouseDemandPlanVersionData.getVersionId());
                        dailyWarehouseDemandDto.setSkuCode(warehouseDemandPlanVersionData.getSkuCode());
                        dailyWarehouseDemandDto.setSkuName(warehouseDemandPlanVersionData.getSkuName());
                        dailyWarehouseDemandDto.setDistributeType(distributeType);
                        dailyWarehouseDemandDto.setWarehouseCode(warehouseDemandPlanVersionData.getWarehouseCode());
                        dailyWarehouseDemandDto.setWarehouseName(warehouseDemandPlanVersionData.getWarehouseName());
                        dailyWarehouseDemandDto.setLv1CategoryCode(warehouseDemandPlanVersionData.getLv1CategoryCode());
                        dailyWarehouseDemandDto.setLv1CategoryName(warehouseDemandPlanVersionData.getLv1CategoryName());
                        dailyWarehouseDemandDto.setLv2CategoryCode(warehouseDemandPlanVersionData.getLv2CategoryCode());
                        dailyWarehouseDemandDto.setLv2CategoryName(warehouseDemandPlanVersionData.getLv2CategoryName());
                        dailyWarehouseDemandDto.setLv3CategoryCode(warehouseDemandPlanVersionData.getLv3CategoryCode());
                        dailyWarehouseDemandDto.setLv3CategoryName(warehouseDemandPlanVersionData.getLv3CategoryName());
                        dailyWarehouseDemandDto.setDateRecorded(day);
                        dailyWarehouseDemandDto.setPlanDataType(warehouseDemandPlanVersionData.getPlanDataType());
                        BigDecimal planValue = new BigDecimal(Objects.isNull(warehouseDemandPlanVersionData.getPlanValue()) ? 0d :
                                warehouseDemandPlanVersionData.getPlanValue());
//                        BigDecimal dayNum = new BigDecimal(dataqWeek.getDayList().size());
//                        Double avg = planValue.divide(dayNum, 0, RoundingMode.HALF_UP).doubleValue();
                        dailyWarehouseDemandDto.setDateValue(dayValue);
                        dailyWarehouseDemandDto.setWeekRecorded(dataqWeek.getFsclWeekStart());
                        dailyWarehouseDemandDto.setWeekRawValue(planValue.doubleValue());
                        dailyWarehouseDemandDto.setWeekActualValue(planValue.doubleValue());
                        dailyWarehouseDemandDto.setStatus(STATUS_INITING);
                        dailyWarehouseDemandDto.setTableSuffix(tableSuffix);
                        addList.add(dailyWarehouseDemandDto);
                    }
                }
            }

            log.info("save dailywarehouse data size :{}",addList.size());
            // 如果数据量过大，需要分片
            int fragmentSize = 100000;
            if (addList.size() < fragmentSize)
            {
                if (CollectionUtils.isNotEmpty(addList))
                {
                    addDailyWarehouseDemandList(addList);
                }
            }
            else
            {
                // 单例map获取线程池，不需要shutdown
                ThreadPoolExecutor pool = ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.fragmentThread);
                int dataSize = addList.size();
                int threadCount =
                    Math.floorMod(dataSize, fragmentSize) == 0 ? Math.floorDiv(dataSize, fragmentSize) : Math.floorDiv(dataSize, fragmentSize) + 1;
                CountDownLatch countDownLatch = new CountDownLatch(threadCount);
                for (int i = 0; i < threadCount; i++)
                {
                    final int start = i * fragmentSize;
                    final int fragmentNum = i;
                    final List<DailyWarehouseDemandDto> subList = addList.stream().skip(start).limit(fragmentSize).collect(Collectors.toList());
                    pool.submit(() -> {
                        try
                        {
                            addDailyWarehouseDemandList(subList);
                            log.info("fragment {} addDailyWarehouseDemandList", fragmentNum);
                        }
                        catch (Exception e)
                        {
                            log.error("日分仓数据生成失败,分片数据保存失败，fragment {} addDailyWarehouseDemandList error has error.", fragmentNum, e);
                        }
                        finally
                        {
                            countDownLatch.countDown();
                            subList.clear();
                        }
                    });
                }
                countDownLatch.await();
            }

            // 修改入库的日分仓编辑数据状态为正常
            dailyWarehouseDemandDao.udpateDailyWarehouseDemandNormal(queryDailyWarehouseDemandListReqVo);
        }
        catch (Exception e)
        {
            log.error("日分仓数据生成失败:{}", e);
        }
        finally
        {
            redisUtils.unlock(lockKey);
        }
    }


    /**
     * 单次多条插入
     * @param subList
     * @return
     */

    private void addDailyWarehouseDemandList(List<DailyWarehouseDemandDto> subList) throws Exception{
        int pageSize = 256;
        int dataSize = subList.size();
        if(dataSize > pageSize){
            int pageNum = Math.floorMod(dataSize, pageSize) == 0 ? Math.floorDiv(dataSize, pageSize) : Math.floorDiv(dataSize, pageSize) + 1;
            for (int i = 0; i < pageNum; i++){
                int start = i * pageSize;
                List<DailyWarehouseDemandDto> onceList = subList.stream().skip(start).limit(pageSize).collect(Collectors.toList());
                dailyWarehouseDemandDao.addDailyWarehouseDemandList(onceList);
            }

        }else{
            dailyWarehouseDemandDao.addDailyWarehouseDemandList(subList);
        }
    }

    /**
     *
     * @Description 保存日分仓编辑（分页保存）
     * @param generateDailyWarehouseAiplanDemandVo
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月26日 11:09
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDailyWarehouseDemand(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo) throws Exception
    {
        /*
         * 1.修改原始数据
         * 2.生成日分仓调拨需求数据
         * 3.维护算法中间表
         */
        String subDemandPlanVersion = generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion().substring(2, 8);
        generateDailyWarehouseAiplanDemandVo.setSubDemandPlanVersion(subDemandPlanVersion);

        List<DailyWarehouseDemandDateVo> dailyWarehouseDemandDateVos = generateDailyWarehouseAiplanDemandVo.getDailyWarehouseDemandDateVos();

        if (CollectionUtils.isNotEmpty(dailyWarehouseDemandDateVos))
        {
            for (DailyWarehouseDemandDateVo dto : dailyWarehouseDemandDateVos)
            {
                dto.setSubDemandPlanVersion(subDemandPlanVersion);
            }

            // 原来研发表结构设计为新增的时候写入weekActualValue，每次修改同步更新，现在修改为查询的时候计算，修改不再更新weekActualValue字段
            mybatisUtils.batchUpdateOrInsertFragment(dailyWarehouseDemandDateVos, DailyWarehouseDemandDao.class,
                (item, dailyWarehouseDemandDao) -> dailyWarehouseDemandDao.batchUpdateDailyWarehouseDateData(item));
        }

        /**
         if (CollectionUtils.isNotEmpty(dailyWarehouseDemandDateVos))
         {
         // dateValue来记录更新的值
         List<String> idList = dailyWarehouseDemandDateVos.stream().map(DailyWarehouseDemandDateVo::getId).collect(Collectors.toList());
         List<DailyWarehouseDemandDto> dailyWarehouseDemandDtoList = dailyWarehouseDemandDao.queryDailyWarehouseDemandListById(idList);
         for (DailyWarehouseDemandDto dto : dailyWarehouseDemandDtoList)
         {
         dto.setSubDemandPlanVersion(subDemandPlanVersion);
         for (DailyWarehouseDemandDateVo vo : dailyWarehouseDemandDateVos)
         {
         vo.setSubDemandPlanVersion(subDemandPlanVersion);
         if (dto.getId().equals(vo.getId()))
         {
         dto.setDateValue(vo.getDateValue() - dto.getDateValue());
         }
         }
         }

         mybatisUtils.batchUpdateOrInsertFragment(dailyWarehouseDemandDateVos, DailyWarehouseDemandDao.class,
         (item, dailyWarehouseDemandDao) -> dailyWarehouseDemandDao.batchUpdateDailyWarehouseDateData(item));
         mybatisUtils.batchUpdateOrInsertFragment(dailyWarehouseDemandDtoList, DailyWarehouseDemandDao.class,
         (item, dailyWarehouseDemandDao) -> dailyWarehouseDemandDao.batchUpdateDailyWarehouseWeekData(item));
         }
         */
    }

    /**
     *
     * @Description 生成日分仓调拨需求
     * @param generateDailyWarehouseAiplanDemandVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月14日 16:06
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateDailyWarehouseAiplanDemand(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo) throws Exception
    {
        // 获取分布式锁，锁业务:计划编号:版本号
        String lockKey = StringUtils.format(CommonConstants.REDIS_GEN_DAILY_AIPLAN_LOCK_KEY, generateDailyWarehouseAiplanDemandVo.getDemandPlanCode(),
            generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion());
        boolean isLock = redisUtils.lock(lockKey, 3600);
        if (!isLock)
        {
            log.info("generateDailyWarehouseAiplanDemand has processing in another node.");
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }

        try
        {
            String tableSuffix = StringUtils.substring(generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion(), 2, 8);
            generateDailyWarehouseAiplanDemandVo.setTableSuffix(tableSuffix);

            /*
             * 1.修改原始数据
             * 2.生成日分仓调拨需求数据
             * 3.维护算法中间表
             */
            String subDemandPlanVersion = generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion().substring(2, 8);
            generateDailyWarehouseAiplanDemandVo.setSubDemandPlanVersion(subDemandPlanVersion);

            // 查询所有效期规则
            List<SkuValidRuleVo> productRules = dailyWarehouseDemandDao.queryValidRuleByProduct();
            List<SkuValidRuleVo> categoryRules = dailyWarehouseDemandDao.queryValidRuleByCategory();
            List<SkuValidRuleVo> generalRules = dailyWarehouseDemandDao.queryValidGeneralRule();

            Map<String, List<SkuValidRuleVo>> productRuleMap = Collections.EMPTY_MAP;
            if (CollectionUtils.isNotEmpty(productRules))
            {
                productRuleMap =
                    productRules.stream().collect(Collectors.groupingBy(
                        item -> new StringBuilder(item.getSkuCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR).append(item.getDistributeType()).toString()));
            }

            Map<String, List<SkuValidRuleVo>> categoryRuleMap = Collections.EMPTY_MAP;
            if (CollectionUtils.isNotEmpty(categoryRules))
            {
                categoryRuleMap = categoryRules.stream().collect(Collectors.groupingBy(
                    item -> new StringBuilder(item.getCategoryCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR).append(item.getDistributeType()).toString()));
            }

            Map<Integer, List<SkuValidRuleVo>> generalRuleMap = Collections.EMPTY_MAP;
            if (CollectionUtils.isNotEmpty(generalRules))
            {
                generalRuleMap = generalRules.stream().collect(Collectors.groupingBy(SkuValidRuleVo::getDistributeType));
            }

            // 获取版本号
            String aiplanDemandVersion = DateUtils.getDate(DateUtils.YMD);
            generateDailyWarehouseAiplanDemandVo.setAiplanDemandVersion(aiplanDemandVersion);
            List<String> aiplanDemandVersionList = dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandVersionList(generateDailyWarehouseAiplanDemandVo);
            if (CollectionUtils.isNotEmpty(aiplanDemandVersionList))
            {
                int index = aiplanDemandVersionList.stream().map(item -> {
                    return StringUtils.contains(item, StringUtils.DATE_SEPARATOR) ?
                        Integer.valueOf(StringUtils.substringAfterLast(item,
                            StringUtils.DATE_SEPARATOR)) : 0;
                }).max(Comparator.comparingInt(Integer::intValue)).get();
                index = index + 1;

                aiplanDemandVersion = aiplanDemandVersion + "-" + index;
            }
            generateDailyWarehouseAiplanDemandVo.setAiplanDemandVersion(aiplanDemandVersion);

            // 删除可能出现的因为异常、重启、故障等原因导致线程中断而产生的生成中的数据
            dailyWarehouseDemandDao.deleteDailyWarehouseAiplanDemandIniting(generateDailyWarehouseAiplanDemandVo);

            // 查询日分仓需求数据
            QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo = new QueryDailyWarehouseDemandListReqVo();
            queryDailyWarehouseDemandListReqVo.setDemandPlanCode(generateDailyWarehouseAiplanDemandVo.getDemandPlanCode());
            queryDailyWarehouseDemandListReqVo.setDemandPlanVersion(generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion());
//        DailyWarehouseDemandBaseTable<List<DailyWarehouseDemandRspVo>> listDailyWarehouseDemandBaseTable =
//            this.queryDailyWarehouseDemandListWithoutWeekDate(queryDailyWarehouseDemandListReqVo);
//        List<DailyWarehouseDemandRspVo> dailyWarehouseDemandRspVoList = listDailyWarehouseDemandBaseTable.getList();
            queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));
            // 查询动态字段信息
            List<DailyWarehouseDemandRspVo> dailyWarehouseDemandRspVoList =
                dailyWarehouseDemandDao.queryDailyWarehouseDemandDataJsonList(queryDailyWarehouseDemandListReqVo);

            BigDecimal percent = new BigDecimal(100);

            // 新增的list
            List<DailyWarehouseAiplanDemandDto> addList = new ArrayList<>();
            for (DailyWarehouseDemandRspVo dailyWarehouseDemandRspVo : dailyWarehouseDemandRspVoList)
            {
                List<PlanValue> planValueList = JSON.parseArray(dailyWarehouseDemandRspVo.getData(), PlanValue.class);
                dailyWarehouseDemandRspVo.setData(null);

                String productRuleKey = new StringBuilder(dailyWarehouseDemandRspVo.getSkuCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR)
                    .append(dailyWarehouseDemandRspVo.getDistributeType()).toString();
                String categoryRuleKey = new StringBuilder(dailyWarehouseDemandRspVo.getLv3CategoryCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR)
                    .append(dailyWarehouseDemandRspVo.getDistributeType()).toString();

                List<SkuValidRuleVo> validRuleList = Collections.EMPTY_LIST;
                // 命中产品效期规则
                if (productRuleMap.containsKey(productRuleKey))
                {
                    validRuleList = productRuleMap.get(productRuleKey);
                }
                // 命中品类效期规则
                else if (categoryRuleMap.containsKey(categoryRuleKey))
                {
                    validRuleList = categoryRuleMap.get(categoryRuleKey);
                }
                // 命中通用效期规则
                else if (generalRuleMap.containsKey(dailyWarehouseDemandRspVo.getDistributeType()))
                {
                    validRuleList = generalRuleMap.get(dailyWarehouseDemandRspVo.getDistributeType());
                }
                // 未配置效期规则
                else
                {
                    continue;
                }

                for (PlanValue planValue : planValueList)
                {
                    for (SkuValidRuleVo skuValidRuleVo : validRuleList)
                    {
                        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto = new DailyWarehouseAiplanDemandDto();
//                        BeanUtils.copyProperties(dailyWarehouseDemandRspVo, dailyWarehouseAiplanDemandDto);
                        dailyWarehouseAiplanDemandDto.setId(SeqUtils.getSequenceUid());
                        dailyWarehouseAiplanDemandDto.setDemandPlanCode(dailyWarehouseDemandRspVo.getDemandPlanCode());
                        dailyWarehouseAiplanDemandDto.setDemandPlanName(dailyWarehouseDemandRspVo.getDemandPlanName());
                        dailyWarehouseAiplanDemandDto.setDemandPlanVersion(dailyWarehouseDemandRspVo.getDemandPlanVersion());
                        dailyWarehouseAiplanDemandDto.setAiplanDemandVersion(aiplanDemandVersion);
                        dailyWarehouseAiplanDemandDto.setSkuCode(dailyWarehouseDemandRspVo.getSkuCode());
                        dailyWarehouseAiplanDemandDto.setSkuName(dailyWarehouseDemandRspVo.getSkuName());
                        dailyWarehouseAiplanDemandDto.setDistributeType(dailyWarehouseDemandRspVo.getDistributeType());
                        dailyWarehouseAiplanDemandDto.setWarehouseCode(dailyWarehouseDemandRspVo.getWarehouseCode());
                        dailyWarehouseAiplanDemandDto.setWarehouseName(dailyWarehouseDemandRspVo.getWarehouseName());
                        dailyWarehouseAiplanDemandDto.setLv1CategoryCode(dailyWarehouseDemandRspVo.getLv1CategoryCode());
                        dailyWarehouseAiplanDemandDto.setLv1CategoryName(dailyWarehouseDemandRspVo.getLv1CategoryName());
                        dailyWarehouseAiplanDemandDto.setLv2CategoryCode(dailyWarehouseDemandRspVo.getLv2CategoryCode());
                        dailyWarehouseAiplanDemandDto.setLv2CategoryName(dailyWarehouseDemandRspVo.getLv2CategoryName());
                        dailyWarehouseAiplanDemandDto.setLv3CategoryCode(dailyWarehouseDemandRspVo.getLv3CategoryCode());
                        dailyWarehouseAiplanDemandDto.setLv3CategoryName(dailyWarehouseDemandRspVo.getLv3CategoryName());
                        dailyWarehouseAiplanDemandDto.setValidityPeriod(skuValidRuleVo.getName());
                        dailyWarehouseAiplanDemandDto.setDateRecorded(planValue.getPlanDate());
                        double dateValue = planValue.getPlanValue();
                        BigDecimal dateValueBg = new BigDecimal(dateValue);
                        BigDecimal ratioBg = new BigDecimal(skuValidRuleVo.getRatio());
                        dailyWarehouseAiplanDemandDto.setDateValue(dateValueBg.multiply(ratioBg).divide(percent, 0, RoundingMode.HALF_UP).doubleValue());
                        dailyWarehouseAiplanDemandDto.setEndDay(skuValidRuleVo.getEndDay());
                        dailyWarehouseAiplanDemandDto.setTableSuffix(subDemandPlanVersion);
                        dailyWarehouseAiplanDemandDto.setStatus(STATUS_INITING);

                        addList.add(dailyWarehouseAiplanDemandDto);
                    }
                }
            }

            mybatisUtils.batchUpdateOrInsertFragment(addList, DailyWarehouseDemandDao.class,
                (item, dailyWarehouseDemandDao) -> dailyWarehouseDemandDao.batchAddDailyWarehouseAiplanDemandList(item));

            // 修改入库的日分仓调拨数据状态为正常
            dailyWarehouseDemandDao.udpateDailyWarehouseAiplanDemandNormal(generateDailyWarehouseAiplanDemandVo);

            // 将tob toc数据合并到一起
            Map<DailyWarehouseAiplanDemandDto, List<DailyWarehouseAiplanDemandDto>> map = addList.stream().
                collect(Collectors.groupingBy(Function.identity()));

            List<SoWtDemandDto> soWtDemandDtoList = new ArrayList<>();

            map.forEach((key, value) -> {
                DailyWarehouseAiplanDemandDto dto1 = value.get(0);
                SoWtDemandDto soWtDemandDto = new SoWtDemandDto();
                soWtDemandDto.setItemId(dto1.getSkuCode());
                soWtDemandDto.setItemName(dto1.getSkuName());
                soWtDemandDto.setStockPointId(dto1.getWarehouseCode());
                soWtDemandDto.setStockPointName(dto1.getWarehouseName());
                soWtDemandDto.setExpiryLimit(dto1.getEndDay());
                soWtDemandDto.setDemandPlanCode((dto1.getDemandPlanCode()));
                soWtDemandDto.setDemandPlanName(dto1.getDemandPlanName());
                soWtDemandDto.setVersionId(dto1.getDemandPlanVersion() + "," + dto1.getAiplanDemandVersion());
                String date = dto1.getDateRecorded();
                Date expectedDeliveryDate = new Date(date.substring(0, 4) + "/" + date.substring(4, 6) + "/" + date.substring(6, 8));
                soWtDemandDto.setExpectedDeliveryDate(expectedDeliveryDate);
                if (value.size() == 1)
                {
                    soWtDemandDto.setQty(Double.valueOf(dto1.getDateValue()));
                }
                else
                {
                    Double qty = value.stream().collect(Collectors.summarizingDouble(item -> {
                        return Objects.isNull(item.getDateValue()) ? 0d : item.getDateValue();
                    })).getSum();
                    soWtDemandDto.setQty(qty);
                }

                soWtDemandDtoList.add(soWtDemandDto);
            });

            // 删除原有该demandPlanCode下数据 更新提供给算法的中间表
            // 如果数据量过大需要分片
            dailyWarehouseDemandDao.deleteAiplanAlgoList(generateDailyWarehouseAiplanDemandVo.getDemandPlanCode());

            mybatisUtils.batchUpdateOrInsertFragment(soWtDemandDtoList, DailyWarehouseDemandDao.class,
                (item, dailyWarehouseDemandDao) -> dailyWarehouseDemandDao.batchAddAiplanAlgoList(item));
        }
        catch (Exception e)
        {
            log.error("generateDailyWarehouseAiplanDemand has exception:{}", e);
        }
        finally
        {
            redisUtils.unlock(lockKey);
        }
    }

    /**
     *
     * @Description 生成日分仓调拨需求
     * @param generateDailyWarehouseAiplanDemandVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月14日 16:06     */
//    @Override
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void generateDailyWarehouseAiplanDemandOld(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo) throws Exception
    {
        // 获取分布式锁，锁业务:计划编号:版本号
        String lockKey = StringUtils.format(CommonConstants.REDIS_GEN_DAILY_AIPLAN_LOCK_KEY, generateDailyWarehouseAiplanDemandVo.getDemandPlanCode(),
            generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion());
        boolean isLock = redisUtils.lock(lockKey, 3600);
        if (!isLock)
        {
            log.info("generateDailyWarehouseAiplanDemand has processing in another node.");
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }

        try
        {

            /*
             * 1.修改原始数据
             * 2.生成日分仓调拨需求数据
             * 3.维护算法中间表
             */
            generateDailyWarehouseAiplanDemandVo.setSubDemandPlanVersion(
                generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion().substring(2, 8));

            List<DailyWarehouseDemandDateVo> dailyWarehouseDemandDateVos = generateDailyWarehouseAiplanDemandVo.getDailyWarehouseDemandDateVos();
            if (CollectionUtils.isNotEmpty(dailyWarehouseDemandDateVos))
            {
                // dateValue来记录更新的值
                List<String> idList = dailyWarehouseDemandDateVos.stream().map(DailyWarehouseDemandDateVo::getId).collect(Collectors.toList());
                List<DailyWarehouseDemandDto> dailyWarehouseDemandDtoList = dailyWarehouseDemandDao.queryDailyWarehouseDemandListById(idList);
                for (DailyWarehouseDemandDto dto : dailyWarehouseDemandDtoList)
                {
                    dto.setSubDemandPlanVersion(dto.getDemandPlanVersion().substring(2, 8));
                    for (DailyWarehouseDemandDateVo vo : dailyWarehouseDemandDateVos)
                    {
                        if (dto.getId().equals(vo.getId()))
                        {
                            dto.setDateValue(vo.getDateValue() - dto.getDateValue());
                        }
                    }
                }

                dailyWarehouseDemandDao.updateDailyWarehouseWeekData(dailyWarehouseDemandDtoList);
                dailyWarehouseDemandDao.updateDailyWarehouseDateData(generateDailyWarehouseAiplanDemandVo);
            }

            // 查询所有效期规则
            List<SkuValidRuleVo> productRules = dailyWarehouseDemandDao.queryValidRuleByProduct();
            List<SkuValidRuleVo> categoryRules = dailyWarehouseDemandDao.queryValidRuleByCategory();
            List<SkuValidRuleVo> generalRules = dailyWarehouseDemandDao.queryValidGeneralRule();

            Map<String, List<SkuValidRuleVo>> productRuleMap = Collections.EMPTY_MAP;
            if (CollectionUtils.isNotEmpty(productRules))
            {
                productRuleMap =
                    productRules.stream().collect(Collectors.groupingBy(
                        item -> new StringBuilder(item.getSkuCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR).append(item.getDistributeType()).toString()));
            }

            Map<String, List<SkuValidRuleVo>> categoryRuleMap = Collections.EMPTY_MAP;
            if (CollectionUtils.isNotEmpty(categoryRules))
            {
                categoryRuleMap = categoryRules.stream().collect(Collectors.groupingBy(
                    item -> new StringBuilder(item.getCategoryCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR).append(item.getDistributeType()).toString()));
            }

            Map<Integer, List<SkuValidRuleVo>> generalRuleMap = Collections.EMPTY_MAP;
            if (CollectionUtils.isNotEmpty(generalRules))
            {
                generalRuleMap = generalRules.stream().collect(Collectors.groupingBy(SkuValidRuleVo::getDistributeType));
            }

            // 获取版本号
            String aiplanDemandVersion = null;
            int num = dailyWarehouseDemandDao.countDailyWarehouseAiplanDemandVersion(generateDailyWarehouseAiplanDemandVo);
            Date now = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            if (num == 0)
            {
                aiplanDemandVersion = sdf.format(now);
            }
            else
            {
                aiplanDemandVersion = sdf.format(now) + "-" + num;
            }

            // 查询日分仓需求数据
            QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo = new QueryDailyWarehouseDemandListReqVo();
            queryDailyWarehouseDemandListReqVo.setDemandPlanCode(generateDailyWarehouseAiplanDemandVo.getDemandPlanCode());
            queryDailyWarehouseDemandListReqVo.setDemandPlanVersion(generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion());
//        DailyWarehouseDemandBaseTable<List<DailyWarehouseDemandRspVo>> listDailyWarehouseDemandBaseTable =
//            this.queryDailyWarehouseDemandListWithoutWeekDate(queryDailyWarehouseDemandListReqVo);
//        List<DailyWarehouseDemandRspVo> dailyWarehouseDemandRspVoList = listDailyWarehouseDemandBaseTable.getList();
            queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));
            // 查询动态字段信息
            List<DailyWarehouseDemandRspVo> dailyWarehouseDemandRspVoList =
                dailyWarehouseDemandDao.queryDailyWarehouseDemandDataJsonList(queryDailyWarehouseDemandListReqVo);

            // 新增的list
            List<DailyWarehouseAiplanDemandDto> addList = new ArrayList<>();
            for (DailyWarehouseDemandRspVo dailyWarehouseDemandRspVo : dailyWarehouseDemandRspVoList)
            {
                List<PlanValue> planValueList = JSON.parseArray(dailyWarehouseDemandRspVo.getData(), PlanValue.class);
                Map<String, PlanValue> planValueMap =
                    planValueList.stream().collect(Collectors.toMap(PlanValue::getPlanDate, Function.identity(), (key1, key2) -> key2));
                dailyWarehouseDemandRspVo.setData(null);
                Set<String> keys = planValueMap.keySet();

                String productRuleKey = new StringBuilder(dailyWarehouseDemandRspVo.getSkuCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR)
                    .append(dailyWarehouseDemandRspVo.getDistributeType()).toString();
                String categoryRuleKey = new StringBuilder(dailyWarehouseDemandRspVo.getLv3CategoryCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR)
                    .append(dailyWarehouseDemandRspVo.getDistributeType()).toString();

                List<SkuValidRuleVo> validRuleList = Collections.EMPTY_LIST;
                // 命中产品效期规则
                if (productRuleMap.containsKey(productRuleKey))
                {
                    validRuleList = productRuleMap.get(productRuleKey);
                }
                // 命中品类效期规则
                else if (categoryRuleMap.containsKey(categoryRuleKey))
                {
                    validRuleList = categoryRuleMap.get(categoryRuleKey);
                }
                // 命中通用效期规则
                else if (generalRuleMap.containsKey(dailyWarehouseDemandRspVo.getDistributeType()))
                {
                    validRuleList = generalRuleMap.get(dailyWarehouseDemandRspVo.getDistributeType());
                }
                // 未配置效期规则
                else
                {
                    continue;
                }

                for (String key : keys)
                {
                    for (SkuValidRuleVo skuValidRuleVo : validRuleList)
                    {
                        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto = new DailyWarehouseAiplanDemandDto();
                        BeanUtils.copyProperties(dailyWarehouseDemandRspVo, dailyWarehouseAiplanDemandDto);
                        dailyWarehouseAiplanDemandDto.setId(SeqUtils.getSequenceUid());
                        dailyWarehouseAiplanDemandDto.setAiplanDemandVersion(aiplanDemandVersion);

                        dailyWarehouseAiplanDemandDto.setValidityPeriod(skuValidRuleVo.getName());
                        dailyWarehouseAiplanDemandDto.setDateRecorded(key);
                        double dateValue = planValueMap.get(key).getPlanValue();
                        dailyWarehouseAiplanDemandDto.setDateValue(dateValue * skuValidRuleVo.getRatio() / 100);
                        dailyWarehouseAiplanDemandDto.setEndDay(skuValidRuleVo.getEndDay());

                        addList.add(dailyWarehouseAiplanDemandDto);
                    }
                }
            }

            // 生成日分仓调拨需求
            // 如果数据量过大，需要分片
            int fragmentSize = 100;
            if (addList.size() < fragmentSize)
            {
                if (CollectionUtils.isNotEmpty(addList))
                {
                    dailyWarehouseDemandDao.addDailyWarehouseAiplanDemandList(addList);
                }
            }
            else
            {
                // 单例map获取线程池，不需要shutdown
                ThreadPoolExecutor pool = ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.fragmentThread);
                int dataSize = addList.size();
                int threadCount =
                    Math.floorMod(dataSize, fragmentSize) == 0 ? Math.floorDiv(dataSize, fragmentSize) : Math.floorDiv(dataSize, fragmentSize) + 1;
                CountDownLatch countDownLatch = new CountDownLatch(threadCount);
                for (int i = 0; i < threadCount; i++)
                {
                    final int start = i * fragmentSize;
                    final int fragmentNum = i;
                    final List<DailyWarehouseAiplanDemandDto> subList = addList.stream().skip(start).limit(fragmentSize).collect(Collectors.toList());
                    pool.submit(() -> {
                        try
                        {
                            dailyWarehouseDemandDao.addDailyWarehouseAiplanDemandList(subList);
                            log.info("fragment {} addDailyWarehouseAiplanDemandList", fragmentNum);
                        }
                        catch (Exception e)
                        {
                            log.error("fragment {} addDailyWarehouseAiplanDemandList error has error.", fragmentNum, e);
                        }
                        finally
                        {
                            countDownLatch.countDown();
                            subList.clear();
                        }
                    });
                }
                countDownLatch.await();
            }

            // 将tob toc数据合并到一起
            Map<DailyWarehouseAiplanDemandDto, List<DailyWarehouseAiplanDemandDto>> map = addList.stream().
                collect(Collectors.groupingBy(Function.identity()));

            List<SoWtDemandDto> soWtDemandDtoList = new ArrayList<>();

            map.forEach((key, value) -> {
                DailyWarehouseAiplanDemandDto dto1 = value.get(0);
                SoWtDemandDto soWtDemandDto = new SoWtDemandDto();
                soWtDemandDto.setItemId(dto1.getSkuCode());
                soWtDemandDto.setItemName(dto1.getSkuName());
                soWtDemandDto.setStockPointId(dto1.getWarehouseCode());
                soWtDemandDto.setStockPointName(dto1.getWarehouseName());
                soWtDemandDto.setExpiryLimit(dto1.getEndDay());
                soWtDemandDto.setDemandPlanCode((dto1.getDemandPlanCode()));
                soWtDemandDto.setDemandPlanName(dto1.getDemandPlanName());
                soWtDemandDto.setVersionId(dto1.getDemandPlanVersion() + "," + dto1.getAiplanDemandVersion());
                String date = dto1.getDateRecorded();
                Date expectedDeliveryDate = new Date(date.substring(0, 4) + "/" + date.substring(4, 6) + "/" + date.substring(6, 8));
                soWtDemandDto.setExpectedDeliveryDate(expectedDeliveryDate);
                if (value.size() == 1)
                {
                    soWtDemandDto.setQty(Double.valueOf(dto1.getDateValue()));
                }
                else
                {
                    Double qty = value.stream().collect(Collectors.summarizingDouble(item -> {
                        return Objects.isNull(item.getDateValue()) ? 0d : item.getDateValue();
                    })).getSum();
                    soWtDemandDto.setQty(qty);
                }

                soWtDemandDtoList.add(soWtDemandDto);
            });

            // 删除原有该demandPlanCode下数据 更新提供给算法的中间表
            // 如果数据量过大需要分片
            dailyWarehouseDemandDao.deleteAiplanAlgoList(generateDailyWarehouseAiplanDemandVo.getDemandPlanCode());
            if (soWtDemandDtoList.size() < fragmentSize)
            {
                if (CollectionUtils.isNotEmpty(soWtDemandDtoList))
                {
                    dailyWarehouseDemandDao.addAiplanAlgoList(soWtDemandDtoList);
                }
            }
            else
            {
                // 单例map获取线程池，不需要shutdown
                ThreadPoolExecutor pool = ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.fragmentThread);
                int dataSize = soWtDemandDtoList.size();
                int threadCount =
                    Math.floorMod(dataSize, fragmentSize) == 0 ? Math.floorDiv(dataSize, fragmentSize) : Math.floorDiv(dataSize, fragmentSize) + 1;
                CountDownLatch countDownLatch = new CountDownLatch(threadCount);
                for (int i = 0; i < threadCount; i++)
                {
                    final int start = i * fragmentSize;
                    final int fragmentNum = i;
                    final List<SoWtDemandDto> subList = soWtDemandDtoList.stream().skip(start).limit(fragmentSize).collect(Collectors.toList());
                    pool.submit(() -> {
                        try
                        {
                            dailyWarehouseDemandDao.addAiplanAlgoList(subList);
                            log.info("fragment {} addAiplanAlgoList", fragmentNum);
                        }
                        catch (Exception e)
                        {
                            log.error("fragment {} addAiplanAlgoList error has error.", fragmentNum, e);
                        }
                        finally
                        {
                            countDownLatch.countDown();
                            subList.clear();
                        }
                    });
                }
                countDownLatch.await();
            }
        }
        catch (Exception e)
        {
            throw new ServiceException();
        }
        finally
        {
            redisUtils.unlock(lockKey);
        }
    }

    @Deprecated
    private DailyWarehouseDemandBaseTable<List<DailyWarehouseDemandRspVo>> queryDailyWarehouseDemandListWithoutWeekDate(
        QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo)
    {
        // 响应数据结构
        DailyWarehouseDemandBaseTable<List<DailyWarehouseDemandRspVo>> baseTable = new DailyWarehouseDemandBaseTable<>();
        queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));
        // 查询动态字段信息
        List<DailyWarehouseDemandDto> dailyWarehouseDemandDtoList = dailyWarehouseDemandDao.queryDailyWarehouseDemandList(queryDailyWarehouseDemandListReqVo);

        // 将原始结果转换成响应数据结构
        List<String> head = new ArrayList<>();
        for (DailyWarehouseDemandDto i : dailyWarehouseDemandDtoList)
        {
            head.add(i.getDateRecorded());
        }

        // 去重排序
        head = head.stream().distinct().sorted().collect(Collectors.toList());

        baseTable.setHeadArray(head);

        // 封装列表数据
        List<DailyWarehouseDemandRspVo> dataList = new ArrayList<>();
        for (DailyWarehouseDemandDto i : dailyWarehouseDemandDtoList)
        {
            DailyWarehouseDemandRspVo newVo = new DailyWarehouseDemandRspVo();
            BeanUtils.copyProperties(i, newVo);
            DailyWarehouseDemandRspVo data = null;
            // 如果已经有该条数据，则新增dataMap即可，若没有新增一条新数据
            for (DailyWarehouseDemandRspVo vo : dataList)
            {
                if (newVo.equals(vo))
                {
                    PlanValue planValue = new PlanValue();
                    planValue.setId(i.getId());
                    planValue.setPlanDate(i.getDateRecorded());
                    planValue.setPlanValue(i.getDateValue());
                    data = vo;
                    data.getDataMap().put(i.getDateRecorded(), planValue);
                    break;
                }
            }
            if (ObjectUtils.isEmpty(data))
            {
                PlanValue planValue = new PlanValue();
                planValue.setId(i.getId());
                planValue.setPlanDate(i.getDateRecorded());
                planValue.setPlanValue(i.getDateValue());
                data = newVo;
                data.getDataMap().put(i.getDateRecorded(), planValue);
                dataList.add(data);
            }
        }

        baseTable.setList(dataList);

        return baseTable;
    }

    /**
     *
     * @Description 查询日分仓调拨需求
     * @param dailyWarehouseAiplanDemandDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月18日 15:44     */
    @Override
    public DailyWarehouseDemandBaseTable<List<DailyWarehouseAiplanDemandDto>> queryDailyWarehouseAiplanDemandList(
        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto) throws Exception
    {
        // 响应数据结构
        DailyWarehouseDemandBaseTable<List<DailyWarehouseAiplanDemandDto>> baseTable = new DailyWarehouseDemandBaseTable<>();

        // 将原始结果转换成响应数据结构
        // 动态表头
        dailyWarehouseAiplanDemandDto.setTableSuffix(StringUtils.substring(dailyWarehouseAiplanDemandDto.getDemandPlanVersion(), 2, 8));
        List<String> head = queryDailyWarehouseAiplanDemandHeadList(dailyWarehouseAiplanDemandDto);
        baseTable.setHeadArray(head);

        List<DailyWarehouseAiplanDemandDto> dataList =
            dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandDataJsonList(dailyWarehouseAiplanDemandDto);

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (DailyWarehouseAiplanDemandDto item : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(item.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key =
                    DateUtils.formatTime(DateUtils.parseDate(StringUtils.substringBefore(planValue.getPlanDate(), StringUtils.SPACE), DateUtils.YMD),
                        DateUtils.MD_SLASH);
                item.getDataMap().put(key, planValue);
            }
            item.setData(null);
        }

        baseTable.setList(dataList);

        return baseTable;
    }

    /**
     *
     * @Description 查询日分仓调拨需求计划下拉列表
     * @return List<DailyWarehouseAiplanDemandDto>
     * <AUTHOR>
     * @date 2024年01月17日 17:11
     */
    @Override
    public List<DailyWarehouseAiplanDemandDto> queryDailyWarehouseAiplanDemandPlanList() throws Exception
    {
        // fix: 1716 产品李伟亮要求直接查询渠道需求计划，可以没有版本数据，但是只要有计划，就要有后续下游数据的计划
//        return dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandPlanList();
        JSONArray jsonArray = warehouseDemandPlanService.queryWarehouseDemandPlanList();
        List<DailyWarehouseAiplanDemandDto> result = Collections.EMPTY_LIST;
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            result = jsonArray.toJavaList(DailyWarehouseAiplanDemandDto.class);
        }
        return result;
    }

    /**
     *
     * @Description 查询日分仓调拨需求计划版本下拉列表
     * @param dailyWarehouseAiplanDemandDto
     * @return List<String>
     * <AUTHOR>
     * @date 2024年01月17日 17:11
     */
    @Override
    public List<String> queryDailyWarehouseAiplanDemandPlanVersionList(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto)
    {
        List<String> demandPlanVersionList = dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandPlanVersionList(dailyWarehouseAiplanDemandDto);
        if (CollectionUtils.isNotEmpty(demandPlanVersionList))
        {
            demandPlanVersionList = demandPlanVersionList.stream().sorted(Comparator.comparing(item -> StringUtils.substringBefore((String) item,
                StringUtils.DATE_SEPARATOR)).thenComparing(item -> {
                String index = StringUtils.substringAfter((String) item, StringUtils.DATE_SEPARATOR);
                return StringUtils.isBlank(index) ? StringUtils.EMPTY : index;
            }).reversed()).collect(Collectors.toList());
        }
        return demandPlanVersionList;
    }

    /**
     *
     * @Description 查询日分仓调拨需求下拉列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月18日 16:44     */
    @Override
    public List<String> queryAiPlanDemandVersion(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception
    {
        List<String> aiPlanDemandVersionList = dailyWarehouseDemandDao.queryAiPlanDemandVersion(queryDailyWarehouseDemandListReqVo);
        if (CollectionUtils.isNotEmpty(aiPlanDemandVersionList))
        {
            aiPlanDemandVersionList = aiPlanDemandVersionList.stream().sorted(Comparator.comparing(item -> StringUtils.substringBefore((String) item,
                StringUtils.DATE_SEPARATOR)).thenComparing(item -> {
                String index = StringUtils.substringAfter((String) item, StringUtils.DATE_SEPARATOR);
                return StringUtils.isBlank(index) ? StringUtils.EMPTY : index;
            }).reversed()).collect(Collectors.toList());
        }
        return aiPlanDemandVersionList;
    }

    /**
     *
     * @Description 查询日分仓调拨需求动态表头
     * @param dailyWarehouseAiplanDemandDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月03日 14:19     */
    @Override
    public List<String> queryDailyWarehouseAiplanDemandHeadList(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto) throws Exception
    {
        dailyWarehouseAiplanDemandDto.setTableSuffix(StringUtils.substring(dailyWarehouseAiplanDemandDto.getDemandPlanVersion(), 2, 8));
        List<String> dateList = dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandHeadList(dailyWarehouseAiplanDemandDto);
        if (CollectionUtils.isEmpty(dateList))
        {
            return Collections.EMPTY_LIST;
        }

        List<String> result = new ArrayList<>(dateList.size());
        for (String date : dateList)
        {
            result.add(DateUtils.formatTime(DateUtils.parseDate(StringUtils.substringBefore(date, StringUtils.SPACE), DateUtils.YMD),
                DateUtils.MD_SLASH));
        }

        return result;
    }

    /**
     *
     * @Description 查询日分仓调拨需求表头下拉列表
     * @param dailyWarehouseAiplanDemandDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月03日 14:20     */
    @Override
    public List<DailyWarehouseAiplanDemandDto> queryDailyWarehouseDemandAiplanHeadSelect(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto)
        throws Exception
    {
        GroupColumnEnum groupColumnEnum = dailyWarehouseAiplanDemandDto.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        dailyWarehouseAiplanDemandDto.setGroupColumn(groupColumn);
        dailyWarehouseAiplanDemandDto.setSortColumn(sortColumn);
        dailyWarehouseAiplanDemandDto.setTableSuffix(StringUtils.substring(dailyWarehouseAiplanDemandDto.getDemandPlanVersion(), 2, 8));

        return dailyWarehouseDemandDao.queryDailyWarehouseDemandAiplanHeadSelect(dailyWarehouseAiplanDemandDto);
    }

    /**
     *
     * @Description 分页查询日分仓调拨需求数据列表
     * @param condition
     * @return
     * <AUTHOR>
     * @date 2024年01月03日 15:03     */
    @Override
    public PageInfo<DailyWarehouseAiplanDemandDto> pageDailyWarehouseAiplanDemandList(PageCondition<DailyWarehouseAiplanDemandDto> condition)
        throws Exception
    {
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto = condition.getCondition();
        dailyWarehouseAiplanDemandDto.setTableSuffix(StringUtils.substring(dailyWarehouseAiplanDemandDto.getDemandPlanVersion(), 2, 8));

        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        PageHelper.startPage(pageNum, pageSize);
        List<DailyWarehouseAiplanDemandDto> keyList =
            dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandDataKeyList(dailyWarehouseAiplanDemandDto);
        PageInfo pageInfo = new PageInfo(keyList);
        if (CollectionUtils.isEmpty(keyList))
        {
            return pageInfo;
        }

        int pageTotal = Integer.parseInt(String.valueOf(pageInfo.getTotal()));

        dailyWarehouseAiplanDemandDto.setKeyList(keyList);

        List<DailyWarehouseAiplanDemandDto> dataList =
            dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandDataJsonList(dailyWarehouseAiplanDemandDto);

        Map<String, String> skuAbcDtoMaps = skuAbcTypeService.querySkuAbcTypeList();

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (DailyWarehouseAiplanDemandDto item : dataList)
        {
            //设置ABC分类
            String skuAbcType = skuAbcDtoMaps.get(item.getSkuCode() +CommonConstants.UNDERLINE_STRING+item.getDistributeType());
            if (StringUtils.isEmpty(skuAbcType)){
                item.setAbcType(CommonConstants.C+CommonConstants.LEI_CHINESE);
            } else {
                item.setAbcType(skuAbcType+CommonConstants.LEI_CHINESE);
            }

            List<PlanValue> planValueList = JSON.parseArray(item.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key =
                    DateUtils.formatTime(DateUtils.parseDate(StringUtils.substringBefore(planValue.getPlanDate(), StringUtils.SPACE), DateUtils.YMD),
                        DateUtils.MD_SLASH);
                item.getDataMap().put(key, planValue);
            }
            item.setData(null);
        }

        return PageUtils.init(dataList, pageNum, pageSize, pageTotal);
    }

    /**
     *
     * @Description 分组聚合查询日分仓需求编辑列表
     * @param dailyWarehouseAiplanDemandDto
     * @return List<DailyWarehouseAiplanDemandRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 17:09
     */
    @Override
    public List<DailyWarehouseAiplanDemandRspVo> queryDailyWarehouseAiplanDemandListGroupBy(
        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto) throws Exception
    {
        // 分组查询
        List<GroupColumnEnum> groupColumnEnumList = dailyWarehouseAiplanDemandDto.getGroupColumnList();
        List<DailyWarehouseAiplanDemandRspVo> dataList = new ArrayList<>();
        StringBuilder groupColumnSB = new StringBuilder();
        StringBuilder sortColumnSB = new StringBuilder();
        for (GroupColumnEnum groupColumnEnum : groupColumnEnumList)
        {
            String groupColumn = groupColumnSB.append(groupColumnEnum.getColumnName()).toString();
            String sortColumn = sortColumnSB.append(groupColumnEnum.getSortColumn().getColumnName()).toString();
            dailyWarehouseAiplanDemandDto.setGroupColumn(groupColumn);
            dailyWarehouseAiplanDemandDto.setSortColumn(sortColumn);
            dailyWarehouseAiplanDemandDto.setTableSuffix(StringUtils.substring(dailyWarehouseAiplanDemandDto.getDemandPlanVersion(), 2, 8));
            List<DailyWarehouseAiplanDemandRspVo> list =
                dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandListGroupBy(dailyWarehouseAiplanDemandDto);
            if (CollectionUtils.isNotEmpty(list))
            {
                dataList.addAll(list);
            }

            groupColumnSB.append(StringUtils.COMMA_SEPARATOR);
            sortColumnSB.append(StringUtils.COMMA_SEPARATOR);
        }

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (DailyWarehouseAiplanDemandRspVo data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key =
                    DateUtils.formatTime(DateUtils.parseDate(StringUtils.substringBefore(planValue.getPlanDate(), StringUtils.SPACE), DateUtils.YMD),
                        DateUtils.MD_SLASH);
                data.getDataMap().put(key, planValue);
            }
            data.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 查询日分仓需求动态表头
     * @param queryDailyWarehouseDemandListReqVo
     * @return
     * <AUTHOR>
     * @date 2024年01月03日 16:20     */
    @Override
    public List<String> queryDailyWarehouseDemandHeadList(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception
    {
        queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));
        
        List<DailyWarehouseDemandDto> queryHead = dailyWarehouseDemandDao.queryDailyWarehouseDemandHeadList(queryDailyWarehouseDemandListReqVo);

        Set<String> headSet = new TreeSet<>();
        Set<String> weekHeadSet = new TreeSet<>();
        for (DailyWarehouseDemandDto i : queryHead)
        {
            headSet.add(i.getDateRecorded());
            weekHeadSet.add(i.getWeekRecorded());
        }

        List<Object> objectList = redisUtils.hmultiGet(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, weekHeadSet);
        Map<String, String> dataqWeekMap = objectList.stream().map(item -> (DataqWeek) item).collect(Collectors.toMap(DataqWeek::getFsclWeekEnd,
            DataqWeek::getFsclWeekStart, (key1, key2) -> key2));

        List<String> headList = new ArrayList<>(headSet.size() + weekHeadSet.size() * 2);
        for (String head : headSet)
        {
            headList.add(head);
            if (Objects.nonNull(dataqWeekMap.get(head)))
            {
                String weekDate = dataqWeekMap.get(head);
                headList.add(weekDate + WEEK_ACT_DATA_HEAD);
                headList.add(weekDate + WEEK_RAW_DATA_HEAD);
            }
        }

        return headList;
    }


    /**
     *
     * @Description 查询日分仓需求计划表头下拉列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return List<DailyWarehouseDemandDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:40
     */
    @Override
    public List<DailyWarehouseDemandDto> queryDailyWarehouseDemandHeadSelect(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo)
        throws Exception
    {
        GroupColumnEnum groupColumnEnum = queryDailyWarehouseDemandListReqVo.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        queryDailyWarehouseDemandListReqVo.setGroupColumn(groupColumn);
        queryDailyWarehouseDemandListReqVo.setSortColumn(sortColumn);
        queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));

        List<DailyWarehouseDemandDto> result = dailyWarehouseDemandDao.queryDailyWarehouseDemandHeadSelect(queryDailyWarehouseDemandListReqVo);

        return result;
    }

    /**
     *
     * @Description 分组聚合查询日分仓需求编辑列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return List<DailyWarehouseDemandRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 17:09
     */
    @Override
    public List<DailyWarehouseDemandRspVo> queryDailyWarehouseDemandListGroupBy(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo)
        throws Exception
    {
        // 分组查询
        List<GroupColumnEnum> groupColumnEnumList = queryDailyWarehouseDemandListReqVo.getGroupColumnList();
        List<DailyWarehouseDemandRspVo> dataList = new ArrayList<>();
        StringBuilder groupColumnSB = new StringBuilder();
        StringBuilder sortColumnSB = new StringBuilder();
        for (GroupColumnEnum groupColumnEnum : groupColumnEnumList)
        {
            String groupColumn = groupColumnSB.append(groupColumnEnum.getColumnName()).toString();
            String sortColumn = sortColumnSB.append(groupColumnEnum.getSortColumn().getColumnName()).toString();
            queryDailyWarehouseDemandListReqVo.setGroupColumn(groupColumn);
            queryDailyWarehouseDemandListReqVo.setSortColumn(sortColumn);
            queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));
            List<DailyWarehouseDemandRspVo> list = dailyWarehouseDemandDao.queryDailyWarehouseDemandListGroupBy(queryDailyWarehouseDemandListReqVo);
            if (CollectionUtils.isNotEmpty(list))
            {
                dataList.addAll(list);
            }

            groupColumnSB.append(StringUtils.COMMA_SEPARATOR);
            sortColumnSB.append(StringUtils.COMMA_SEPARATOR);
        }

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (DailyWarehouseDemandRspVo data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                data.getDataMap().put(planValue.getPlanDate(), planValue);
            }
            data.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 分页查询日分仓需求编辑列表
     * @param condition
     * @return PageInfo<DailyWarehouseDemandRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 17:04
     */
    @Override
    public PageInfo<DailyWarehouseDemandRspVo> pageDailyWarehouseDemandList(PageCondition<QueryDailyWarehouseDemandListReqVo> condition)
        throws Exception
    {
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo = condition.getCondition();
        queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));

        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        PageHelper.startPage(pageNum, pageSize);
        List<DailyWarehouseDemandRspVo> keyList =
            dailyWarehouseDemandDao.queryDailyWarehouseDemandDataKeyList(queryDailyWarehouseDemandListReqVo);
        PageInfo pageInfo = new PageInfo(keyList);
        if (CollectionUtils.isEmpty(keyList))
        {
            return pageInfo;
        }

        int pageTotal = Integer.parseInt(String.valueOf(pageInfo.getTotal()));

        queryDailyWarehouseDemandListReqVo.setKeyList(keyList);

        List<DailyWarehouseDemandRspVo> dataList = dailyWarehouseDemandDao.queryDailyWarehouseDemandDataJsonList(queryDailyWarehouseDemandListReqVo);

        List<String> headList = queryDailyWarehouseDemandHeadList(queryDailyWarehouseDemandListReqVo);

        Map<String, String> skuAbcDtoMaps = skuAbcTypeService.querySkuAbcTypeList();

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (DailyWarehouseDemandRspVo dailyWarehouseDemandRspVo : dataList)
        {
            //设置ABC分类
            String skuAbcType = skuAbcDtoMaps.get(dailyWarehouseDemandRspVo.getSkuCode() +CommonConstants.UNDERLINE_STRING+dailyWarehouseDemandRspVo.getDistributeType());
            if (StringUtils.isEmpty(skuAbcType)){
                dailyWarehouseDemandRspVo.setAbcType(CommonConstants.C+CommonConstants.LEI_CHINESE);
            } else {
                dailyWarehouseDemandRspVo.setAbcType(skuAbcType+CommonConstants.LEI_CHINESE);
            }
            List<PlanValue> planValueList = JSON.parseArray(dailyWarehouseDemandRspVo.getData(), PlanValue.class);
            Map<String, PlanValue> planValueMap =
                planValueList.stream().collect(Collectors.toMap(PlanValue::getPlanDate, Function.identity(), (key1, key2) -> key2));
            Map<String, List<PlanValue>> weekValueGrouping = planValueList.stream().collect(Collectors.groupingBy(PlanValue::getPlanWeek));
            Map<String, PlanValue> weekValueMap = new HashMap<>();
            for (Map.Entry<String, List<PlanValue>> entry : weekValueGrouping.entrySet())
            {
                List<PlanValue> planValues = entry.getValue();
                PlanValue planValue = new PlanValue();
                planValue.setWeekRawValue(planValues.get(0).getWeekRawValue());
                planValue.setWeekActualValue(planValues.stream().collect(Collectors.summingDouble(PlanValue::getPlanValue)).doubleValue());
                weekValueMap.put(entry.getKey(), planValue);
            }

            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (String head : headList)
            {
                PlanValue planValue = null;
                if (StringUtils.endsWith(head, WEEK_RAW_DATA_HEAD))
                {
                    String week = StringUtils.replace(head, WEEK_RAW_DATA_HEAD, StringUtils.EMPTY);
                    planValue = new PlanValue();
                    planValue.setPlanValue(Objects.isNull(weekValueMap.get(week)) ? 0d : weekValueMap.get(week).getWeekRawValue());
                }
                else if (StringUtils.endsWith(head, WEEK_ACT_DATA_HEAD))
                {
                    String week = StringUtils.replace(head, WEEK_ACT_DATA_HEAD, StringUtils.EMPTY);
                    planValue = new PlanValue();
                    planValue.setPlanValue(Objects.isNull(weekValueMap.get(week)) ? 0d : weekValueMap.get(week).getWeekActualValue());
                }
                else
                {
                    planValue = planValueMap.get(head);
                }
                dailyWarehouseDemandRspVo.getDataMap().put(head, planValue);
            }
            dailyWarehouseDemandRspVo.setData(null);
            planValueList.clear();
            planValueMap.clear();
            weekValueGrouping.clear();
            weekValueMap.clear();
        }

        return PageUtils.init(dataList, pageNum, pageSize, pageTotal);
    }

    /**
     *
     * @Description 日分仓调拨需求版本是否在生成中
     * @param generateDailyWarehouseAiplanDemandVo
     * @return Boolean
     * @throws Exception
     * <AUTHOR>
     * @date 2024年04月12日 11:35
     */
    @Override
    public Boolean isDailyWarehouseAiplanProcessing(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo) throws Exception
    {
        // 查询是否存在分布式锁，锁业务:计划编号:版本号
        String lockKey = StringUtils.format(CommonConstants.REDIS_GEN_DAILY_AIPLAN_LOCK_KEY, generateDailyWarehouseAiplanDemandVo.getDemandPlanCode(),
            generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion());

        // 如果存在则认为正在执行
        Boolean result = redisUtils.hasKey(lockKey);

        return result;
    }
}
