package cn.aliyun.ryytn.modules.distribution.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;

import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.mybatis.MybatisUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import cn.aliyun.ryytn.modules.distribution.api.AsyncDailyWarehouseService;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseDemandDateVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.GenerateDailyWarehouseAiplanDemandVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SkuValidRuleVo;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 异步日分仓接口实现
 * <AUTHOR>
 * @date 2024/3/23 19:08
 */
@Slf4j
@Service
public class AsyncDailyWarehouseServiceImpl implements AsyncDailyWarehouseService
{
    @Autowired
    DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    DataqService dataqService;

    @Autowired
    CalendarService calendarService;

    @Autowired
    private MybatisUtils mybatisUtils;

    /**
     *
     * @Description 异步日分仓编辑确认生成日分仓调拨
     * 20240326 需求变更修改日分仓保存+生成的交互，原来一个按钮拆分成分页保存按钮+确认生成按钮，此异步接口废弃
     * @param generateDailyWarehouseAiplanDemandVo
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月23日 19:07
     */
    @Deprecated
    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateDailyWarehouseAiplanDemand(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo, String lockKey) throws Exception
    {
        try
        {
            /*
             * 1.修改原始数据
             * 2.生成日分仓调拨需求数据
             * 3.维护算法中间表
             */
            String subDemandPlanVersion = generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion().substring(2, 8);
            generateDailyWarehouseAiplanDemandVo.setSubDemandPlanVersion(subDemandPlanVersion);

            List<DailyWarehouseDemandDateVo> dailyWarehouseDemandDateVos = generateDailyWarehouseAiplanDemandVo.getDailyWarehouseDemandDateVos();
            if (CollectionUtils.isNotEmpty(dailyWarehouseDemandDateVos))
            {
                // dateValue来记录更新的值
                List<String> idList = dailyWarehouseDemandDateVos.stream().map(DailyWarehouseDemandDateVo::getId).collect(Collectors.toList());
                List<DailyWarehouseDemandDto> dailyWarehouseDemandDtoList = dailyWarehouseDemandDao.queryDailyWarehouseDemandListById(idList);
                for (DailyWarehouseDemandDto dto : dailyWarehouseDemandDtoList)
                {
                    dto.setSubDemandPlanVersion(subDemandPlanVersion);
                    for (DailyWarehouseDemandDateVo vo : dailyWarehouseDemandDateVos)
                    {
                        vo.setSubDemandPlanVersion(subDemandPlanVersion);
                        if (dto.getId().equals(vo.getId()))
                        {
                            dto.setDateValue(vo.getDateValue() - dto.getDateValue());
                        }
                    }
                }

                mybatisUtils.batchUpdateOrInsertFragment(dailyWarehouseDemandDtoList, DailyWarehouseDemandDao.class,
                    (item, dailyWarehouseDemandDao) -> dailyWarehouseDemandDao.batchUpdateDailyWarehouseWeekData(item));
                mybatisUtils.batchUpdateOrInsertFragment(dailyWarehouseDemandDateVos, DailyWarehouseDemandDao.class,
                    (item, dailyWarehouseDemandDao) -> dailyWarehouseDemandDao.batchUpdateDailyWarehouseDateData(item));
            }

            // 查询所有效期规则
            List<SkuValidRuleVo> productRules = dailyWarehouseDemandDao.queryValidRuleByProduct();
            List<SkuValidRuleVo> categoryRules = dailyWarehouseDemandDao.queryValidRuleByCategory();
            List<SkuValidRuleVo> generalRules = dailyWarehouseDemandDao.queryValidGeneralRule();

            Map<String, List<SkuValidRuleVo>> productRuleMap = Collections.EMPTY_MAP;
            if (CollectionUtils.isNotEmpty(productRules))
            {
                productRuleMap =
                    productRules.stream().collect(Collectors.groupingBy(
                        item -> new StringBuilder(item.getSkuCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR).append(item.getDistributeType()).toString()));
            }

            Map<String, List<SkuValidRuleVo>> categoryRuleMap = Collections.EMPTY_MAP;
            if (CollectionUtils.isNotEmpty(categoryRules))
            {
                categoryRuleMap = categoryRules.stream().collect(Collectors.groupingBy(
                    item -> new StringBuilder(item.getCategoryCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR).append(item.getDistributeType()).toString()));
            }

            Map<Integer, List<SkuValidRuleVo>> generalRuleMap = Collections.EMPTY_MAP;
            if (CollectionUtils.isNotEmpty(generalRules))
            {
                generalRuleMap = generalRules.stream().collect(Collectors.groupingBy(SkuValidRuleVo::getDistributeType));
            }

            // 获取版本号
            String aiplanDemandVersion = DateUtils.getDate(DateUtils.YMD);
            generateDailyWarehouseAiplanDemandVo.setAiplanDemandVersion(aiplanDemandVersion);
            List<String> aiplanDemandVersionList = dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandVersionList(generateDailyWarehouseAiplanDemandVo);
            if (CollectionUtils.isNotEmpty(aiplanDemandVersionList))
            {
                int index = aiplanDemandVersionList.stream().map(item -> {
                    return StringUtils.contains(item, StringUtils.DATE_SEPARATOR) ?
                        Integer.valueOf(StringUtils.substringAfterLast(item,
                            StringUtils.DATE_SEPARATOR)) : 0;
                }).max(Comparator.comparingInt(Integer::intValue)).get();
                index = index + 1;

                aiplanDemandVersion = aiplanDemandVersion + "-" + index;
            }
            generateDailyWarehouseAiplanDemandVo.setAiplanDemandVersion(aiplanDemandVersion);

            // 查询日分仓需求数据
            QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo = new QueryDailyWarehouseDemandListReqVo();
            queryDailyWarehouseDemandListReqVo.setDemandPlanCode(generateDailyWarehouseAiplanDemandVo.getDemandPlanCode());
            queryDailyWarehouseDemandListReqVo.setDemandPlanVersion(generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion());
//        DailyWarehouseDemandBaseTable<List<DailyWarehouseDemandRspVo>> listDailyWarehouseDemandBaseTable =
//            this.queryDailyWarehouseDemandListWithoutWeekDate(queryDailyWarehouseDemandListReqVo);
//        List<DailyWarehouseDemandRspVo> dailyWarehouseDemandRspVoList = listDailyWarehouseDemandBaseTable.getList();
            queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));
            // 查询动态字段信息
            List<DailyWarehouseDemandRspVo> dailyWarehouseDemandRspVoList =
                dailyWarehouseDemandDao.queryDailyWarehouseDemandDataJsonList(queryDailyWarehouseDemandListReqVo);

            // 新增的list
            List<DailyWarehouseAiplanDemandDto> addList = new ArrayList<>();
            for (DailyWarehouseDemandRspVo dailyWarehouseDemandRspVo : dailyWarehouseDemandRspVoList)
            {
                List<PlanValue> planValueList = JSON.parseArray(dailyWarehouseDemandRspVo.getData(), PlanValue.class);
                dailyWarehouseDemandRspVo.setData(null);

                String productRuleKey = new StringBuilder(dailyWarehouseDemandRspVo.getSkuCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR)
                    .append(dailyWarehouseDemandRspVo.getDistributeType()).toString();
                String categoryRuleKey = new StringBuilder(dailyWarehouseDemandRspVo.getLv3CategoryCode()).append(StringUtils.BOTTOM_LINE_SEPARATOR)
                    .append(dailyWarehouseDemandRspVo.getDistributeType()).toString();

                List<SkuValidRuleVo> validRuleList = Collections.EMPTY_LIST;
                // 命中产品效期规则
                if (productRuleMap.containsKey(productRuleKey))
                {
                    validRuleList = productRuleMap.get(productRuleKey);
                }
                // 命中品类效期规则
                else if (categoryRuleMap.containsKey(categoryRuleKey))
                {
                    validRuleList = categoryRuleMap.get(categoryRuleKey);
                }
                // 命中通用效期规则
                else if (generalRuleMap.containsKey(dailyWarehouseDemandRspVo.getDistributeType()))
                {
                    validRuleList = generalRuleMap.get(dailyWarehouseDemandRspVo.getDistributeType());
                }
                // 未配置效期规则
                else
                {
                    continue;
                }

                for (PlanValue planValue : planValueList)
                {
                    for (SkuValidRuleVo skuValidRuleVo : validRuleList)
                    {
                        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto = new DailyWarehouseAiplanDemandDto();
//                        BeanUtils.copyProperties(dailyWarehouseDemandRspVo, dailyWarehouseAiplanDemandDto);
                        dailyWarehouseAiplanDemandDto.setId(SeqUtils.getSequenceUid());
                        dailyWarehouseAiplanDemandDto.setDemandPlanCode(dailyWarehouseDemandRspVo.getDemandPlanCode());
                        dailyWarehouseAiplanDemandDto.setDemandPlanName(dailyWarehouseDemandRspVo.getDemandPlanName());
                        dailyWarehouseAiplanDemandDto.setDemandPlanVersion(dailyWarehouseDemandRspVo.getDemandPlanVersion());
                        dailyWarehouseAiplanDemandDto.setAiplanDemandVersion(aiplanDemandVersion);
                        dailyWarehouseAiplanDemandDto.setSkuCode(dailyWarehouseDemandRspVo.getSkuCode());
                        dailyWarehouseAiplanDemandDto.setSkuName(dailyWarehouseDemandRspVo.getSkuName());
                        dailyWarehouseAiplanDemandDto.setDistributeType(dailyWarehouseDemandRspVo.getDistributeType());
                        dailyWarehouseAiplanDemandDto.setWarehouseCode(dailyWarehouseDemandRspVo.getWarehouseCode());
                        dailyWarehouseAiplanDemandDto.setWarehouseName(dailyWarehouseDemandRspVo.getWarehouseName());
                        dailyWarehouseAiplanDemandDto.setLv1CategoryCode(dailyWarehouseDemandRspVo.getLv1CategoryCode());
                        dailyWarehouseAiplanDemandDto.setLv1CategoryName(dailyWarehouseDemandRspVo.getLv1CategoryName());
                        dailyWarehouseAiplanDemandDto.setLv2CategoryCode(dailyWarehouseDemandRspVo.getLv2CategoryCode());
                        dailyWarehouseAiplanDemandDto.setLv2CategoryName(dailyWarehouseDemandRspVo.getLv2CategoryName());
                        dailyWarehouseAiplanDemandDto.setLv3CategoryCode(dailyWarehouseDemandRspVo.getLv3CategoryCode());
                        dailyWarehouseAiplanDemandDto.setLv3CategoryName(dailyWarehouseDemandRspVo.getLv3CategoryName());
                        dailyWarehouseAiplanDemandDto.setValidityPeriod(skuValidRuleVo.getName());
                        dailyWarehouseAiplanDemandDto.setDateRecorded(planValue.getPlanDate());
                        double dateValue = planValue.getPlanValue();
                        dailyWarehouseAiplanDemandDto.setDateValue(dateValue * skuValidRuleVo.getRatio() / 100);
                        dailyWarehouseAiplanDemandDto.setEndDay(skuValidRuleVo.getEndDay());
                        dailyWarehouseAiplanDemandDto.setTableSuffix(subDemandPlanVersion);

                        addList.add(dailyWarehouseAiplanDemandDto);
                    }
                }
            }

            mybatisUtils.batchUpdateOrInsertFragment(addList, DailyWarehouseDemandDao.class,
                (item, dailyWarehouseDemandDao) -> dailyWarehouseDemandDao.batchAddDailyWarehouseAiplanDemandList(item));

            // 将tob toc数据合并到一起
            Map<DailyWarehouseAiplanDemandDto, List<DailyWarehouseAiplanDemandDto>> map = addList.stream().
                collect(Collectors.groupingBy(Function.identity()));

            List<SoWtDemandDto> soWtDemandDtoList = new ArrayList<>();

            map.forEach((key, value) -> {
                DailyWarehouseAiplanDemandDto dto1 = value.get(0);
                SoWtDemandDto soWtDemandDto = new SoWtDemandDto();
                soWtDemandDto.setItemId(dto1.getSkuCode());
                soWtDemandDto.setItemName(dto1.getSkuName());
                soWtDemandDto.setStockPointId(dto1.getWarehouseCode());
                soWtDemandDto.setStockPointName(dto1.getWarehouseName());
                soWtDemandDto.setExpiryLimit(dto1.getEndDay());
                soWtDemandDto.setDemandPlanCode((dto1.getDemandPlanCode()));
                soWtDemandDto.setDemandPlanName(dto1.getDemandPlanName());
                soWtDemandDto.setVersionId(dto1.getDemandPlanVersion() + "," + dto1.getAiplanDemandVersion());
                String date = dto1.getDateRecorded();
                Date expectedDeliveryDate = new Date(date.substring(0, 4) + "/" + date.substring(4, 6) + "/" + date.substring(6, 8));
                soWtDemandDto.setExpectedDeliveryDate(expectedDeliveryDate);
                if (value.size() == 1)
                {
                    soWtDemandDto.setQty(Double.valueOf(dto1.getDateValue()));
                }
                else
                {
                    Double qty = value.stream().collect(Collectors.summarizingDouble(item -> {
                        return Objects.isNull(item.getDateValue()) ? 0d : item.getDateValue();
                    })).getSum();
                    soWtDemandDto.setQty(qty);
                }

                soWtDemandDtoList.add(soWtDemandDto);
            });

            // 删除原有该demandPlanCode下数据 更新提供给算法的中间表
            // 如果数据量过大需要分片
            dailyWarehouseDemandDao.deleteAiplanAlgoList(generateDailyWarehouseAiplanDemandVo.getDemandPlanCode());

            mybatisUtils.batchUpdateOrInsertFragment(soWtDemandDtoList, DailyWarehouseDemandDao.class,
                (item, dailyWarehouseDemandDao) -> dailyWarehouseDemandDao.batchAddAiplanAlgoList(item));
        }
        catch (Exception e)
        {
            log.error("generateDailyWarehouseAiplanDemand has exception:{}", e);
        }
        finally
        {
            redisUtils.unlock(lockKey);
        }
    }
}
