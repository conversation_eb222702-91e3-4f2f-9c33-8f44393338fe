package cn.aliyun.ryytn.modules.distribution.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandBaseTable;
import cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.api.DailyWarehouseDemandService;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseAiplanDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.GenerateDailyWarehouseAiplanDemandVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 日分仓需求接口
 * <AUTHOR>
 * @date 2023/12/4 11:43
 */
@Slf4j
@RestController
@RequestMapping("/api/distribution/DailyWarehouseDemand")
@Api(tags = "日分仓需求接口")
public class DailyWarehouseDemandController
{

    @Autowired
    private DailyWarehouseDemandService dailyWarehouseDemandService;

    /**
     *
     * @Description 查询日分仓需求计划列表
     * @return ResultInfo<List < DailyWarehouseDemandDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:22
     */
    @PostMapping("queryDailyWarehouseDemandPlanList")
    @ResponseBody
    @ApiOperation("查询日分仓需求计划列表")
    public ResultInfo<List<DailyWarehouseDemandDto>> queryDailyWarehouseDemandPlanList() throws Exception
    {
        List<DailyWarehouseDemandDto> result = dailyWarehouseDemandService.queryDailyWarehouseDemandPlanList();
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询日分仓需求版本列表
     * @param dailyWarehouseDemandDto
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:22
     */
    @PostMapping("queryDailyWarehouseDemandVersionList")
    @ResponseBody
    @ApiOperation("查询日分仓需求版本列表")
    public ResultInfo<List<String>> queryDailyWarehouseDemandVersionList(@RequestBody DailyWarehouseDemandDto dailyWarehouseDemandDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(dailyWarehouseDemandDto);
        ValidateUtil.checkIsNotEmpty(dailyWarehouseDemandDto.getDemandPlanCode());

        List<String> result = dailyWarehouseDemandService.queryDailyWarehouseDemandVersionList(dailyWarehouseDemandDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询日分仓需求数据列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月04日 14:17     */
    @PostMapping("queryDailyWarehouseDemandList")
    @ResponseBody
    @ApiOperation("查询日分仓需求数据列表")
    //@RequiresPermissions(value = {"demand:forecastResult:channel:query"})
    public ResultInfo<DailyWarehouseDemandBaseTable<List<DailyWarehouseDemandRspVo>>> queryDailyWarehouseDemandList(
        @RequestBody QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo);
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion());

        DailyWarehouseDemandBaseTable<List<DailyWarehouseDemandRspVo>> result =
            dailyWarehouseDemandService.queryDailyWarehouseDemandList(queryDailyWarehouseDemandListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 保存日分仓编辑（分页保存）
     * @param generateDailyWarehouseAiplanDemandVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月26日 11:07
     */
    @PostMapping("saveDailyWarehouseDemand")
    @ResponseBody
    @ApiOperation("保存日分仓编辑（分页保存）")
    @RequiresPermissions(value = {})
    public ResultInfo<?> saveDailyWarehouseDemand(@RequestBody GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(generateDailyWarehouseAiplanDemandVo);
        ValidateUtil.checkIsNotEmpty(generateDailyWarehouseAiplanDemandVo.getDailyWarehouseDemandDateVos());
        dailyWarehouseDemandService.saveDailyWarehouseDemand(generateDailyWarehouseAiplanDemandVo);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 生成日分仓调拨需求
     * @param generateDailyWarehouseAiplanDemandVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月18日 15:31     */
    @PostMapping("generateDailyWarehouseAiplanDemand")
    @ResponseBody
    @ApiOperation("生成日分仓调拨需求")
    @RequiresPermissions(value = {})
    public ResultInfo<?> generateDailyWarehouseAiplanDemand(@RequestBody GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(generateDailyWarehouseAiplanDemandVo);
        dailyWarehouseDemandService.generateDailyWarehouseAiplanDemand(generateDailyWarehouseAiplanDemandVo);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询日分仓调拨需求数据列表
     * @param queryDailyWarehouseAiplanDemandListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月18日 15:38     */
    @PostMapping("queryDailyWarehouseAiplanDemandList")
    @ResponseBody
    @ApiOperation("查询日分仓调拨需求数据列表")
    //@RequiresPermissions(value = {"demand:forecastResult:channel:query"})
    public ResultInfo<DailyWarehouseDemandBaseTable<List<DailyWarehouseAiplanDemandDto>>> queryDailyWarehouseAiplanDemandList(
        @RequestBody DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto);
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getDemandPlanVersion());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getAiplanDemandVersion());

        DailyWarehouseDemandBaseTable<List<DailyWarehouseAiplanDemandDto>> result =
            dailyWarehouseDemandService.queryDailyWarehouseAiplanDemandList(dailyWarehouseAiplanDemandDto);
        return ResultInfo.success(result);
    }


    /**
     *
     * @Description 查询日分仓调拨需求计划列表
     * @return ResultInfo<List < DailyWarehouseDemandDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:22
     */
    @PostMapping("queryDailyWarehouseAiplanDemandPlanList")
    @ResponseBody
    @ApiOperation("查询日分仓调拨需求计划列表")
    public ResultInfo<List<DailyWarehouseAiplanDemandDto>> queryDailyWarehouseAiplanDemandPlanList() throws Exception
    {
        List<DailyWarehouseAiplanDemandDto> result = dailyWarehouseDemandService.queryDailyWarehouseAiplanDemandPlanList();
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询日分仓调拨需求版本列表
     * @param dailyWarehouseAiplanDemandDto
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:22
     */
    @PostMapping("queryDailyWarehouseAiplanDemandPlanVersionList")
    @ResponseBody
    @ApiOperation("查询日分仓调拨需求版本列表")
    public ResultInfo<List<String>> queryDailyWarehouseAiplanDemandPlanVersionList(@RequestBody DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto);
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getDemandPlanCode());

        List<String> result = dailyWarehouseDemandService.queryDailyWarehouseAiplanDemandPlanVersionList(dailyWarehouseAiplanDemandDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询日分仓调拨需求版本下拉列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月18日 16:41     */
    @PostMapping("queryAiplanDemandVersionList")
    @ResponseBody
    @ApiOperation("查询日分仓调拨需求版本下拉列表")
    public ResultInfo<List<String>> queryAiPlanDemandVersion(
        @RequestBody QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo);
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion());

        List<String> result = dailyWarehouseDemandService.queryAiPlanDemandVersion(queryDailyWarehouseDemandListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询日分仓调拨需求动态表头
     * @param dailyWarehouseAiplanDemandDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月03日 10:04     */
    @PostMapping("queryDailyWarehouseAiplanDemandHeadList")
    @ResponseBody
    @ApiOperation("查询日分仓调拨需求动态表头")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryDailyWarehouseAiplanDemandHeadList(@RequestBody DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto);
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getDemandPlanVersion());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getAiplanDemandVersion());

        List<String> result = dailyWarehouseDemandService.queryDailyWarehouseAiplanDemandHeadList(dailyWarehouseAiplanDemandDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询日分仓调拨需求表头下拉列表
     * @param dailyWarehouseAiplanDemandDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月03日 10:16     */
    @PostMapping("queryDailyWarehouseAiplanDemandHeadSelect")
    @ResponseBody
    @ApiOperation("查询日分仓需求计划表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<DailyWarehouseAiplanDemandDto>> queryDailyWarehouseAiplanDemandHeadSelect(
        @RequestBody DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto);
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getDemandPlanVersion());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getGroupColumnList());

        List<DailyWarehouseAiplanDemandDto> result = dailyWarehouseDemandService.queryDailyWarehouseDemandAiplanHeadSelect(dailyWarehouseAiplanDemandDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询日分仓调拨需求分组聚合数据列表
     * @param dailyWarehouseAiplanDemandDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月03日 10:22     */
    @PostMapping("queryDailyWarehouseAiplanDemandListGroupBy")
    @ResponseBody
    @ApiOperation("查询日分仓调拨需求分组聚合数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<DailyWarehouseAiplanDemandRspVo>> queryDailyWarehouseAiplanDemandListGroupBy(
        @RequestBody DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto);
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getDemandPlanVersion());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getAiplanDemandVersion());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getGroupColumnList());

        List<DailyWarehouseAiplanDemandRspVo> result =
            dailyWarehouseDemandService.queryDailyWarehouseAiplanDemandListGroupBy(dailyWarehouseAiplanDemandDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询日分仓调拨需求数据列表
     * @param condition
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月03日 15:02     */
    @PostMapping("pageDailyWarehouseAiplanDemandList")
    @ResponseBody
    @ApiOperation("分页查询日分仓调拨需求数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<DailyWarehouseAiplanDemandDto>> pageDailyWarehouseAiplanDemandList(
        @RequestBody PageCondition<DailyWarehouseAiplanDemandDto> condition) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getDemandPlanVersion());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getAiplanDemandVersion());

        PageInfo<DailyWarehouseAiplanDemandDto> result = dailyWarehouseDemandService.pageDailyWarehouseAiplanDemandList(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询日分仓需求编辑动态表头
     * @param queryDailyWarehouseDemandListReqVo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月03日 16:17     */
    @PostMapping("queryDailyWarehouseDemandHeadList")
    @ResponseBody
    @ApiOperation("查询日分仓需求编辑动态表头")
    //@RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryDailyWarehouseDemandHeadList(@RequestBody QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo)
        throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo);
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion());
        queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));

        List<String> result = dailyWarehouseDemandService.queryDailyWarehouseDemandHeadList(queryDailyWarehouseDemandListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询日分仓需求编辑表头下拉列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return ResultInfo<List < DailyWarehouseDemandDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:40
     */
    @PostMapping("queryDailyWarehouseDemandHeadSelect")
    @ResponseBody
    @ApiOperation("查询日分仓需求编辑表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<DailyWarehouseDemandDto>> queryDailyWarehouseDemandHeadSelect(
        @RequestBody QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo);
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion());
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getGroupColumnList());

        List<DailyWarehouseDemandDto> result = dailyWarehouseDemandService.queryDailyWarehouseDemandHeadSelect(queryDailyWarehouseDemandListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询日分仓需求编辑分组列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return ResultInfo<List < DailyWarehouseDemandRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:59
     */
    @PostMapping("queryDailyWarehouseDemandListGroupBy")
    @ResponseBody
    @ApiOperation("查询日分仓需求编辑分组列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<DailyWarehouseDemandRspVo>> queryDailyWarehouseDemandListGroupBy(
        @RequestBody QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo);
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion());
        ValidateUtil.checkIsNotEmpty(queryDailyWarehouseDemandListReqVo.getGroupColumnList());

        List<DailyWarehouseDemandRspVo> result = dailyWarehouseDemandService.queryDailyWarehouseDemandListGroupBy(queryDailyWarehouseDemandListReqVo);
        return ResultInfo.success(result);
    }

    @PostMapping("pageDailyWarehouseDemandList")
    @ResponseBody
    @ApiOperation("分页查询日分仓需求编辑数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<DailyWarehouseDemandRspVo>> pageDailyWarehouseDemandList(
        @RequestBody PageCondition<QueryDailyWarehouseDemandListReqVo> condition) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getDemandPlanVersion());

        PageInfo<DailyWarehouseDemandRspVo> result = dailyWarehouseDemandService.pageDailyWarehouseDemandList(condition);
        return ResultInfo.success(result);
    }

    @PostMapping("isDailyWarehouseAiplanProcessing")
    @ResponseBody
    @ApiOperation("日分仓调拨需求版本是否在生成中")
    @RequiresPermissions(value = {})
    public ResultInfo<Boolean> isDailyWarehouseAiplanProcessing(@RequestBody GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(generateDailyWarehouseAiplanDemandVo);
        ValidateUtil.checkIsNotEmpty(generateDailyWarehouseAiplanDemandVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(generateDailyWarehouseAiplanDemandVo.getDemandPlanVersion());

        Boolean result = dailyWarehouseDemandService.isDailyWarehouseAiplanProcessing(generateDailyWarehouseAiplanDemandVo);
        return ResultInfo.success(result);
    }
}
