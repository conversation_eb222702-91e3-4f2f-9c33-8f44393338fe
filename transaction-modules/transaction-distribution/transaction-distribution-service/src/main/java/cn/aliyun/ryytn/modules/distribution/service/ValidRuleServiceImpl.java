package cn.aliyun.ryytn.modules.distribution.service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.excel.util.StringUtils;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.distribution.api.ValidRuleService;
import cn.aliyun.ryytn.modules.distribution.dao.ValidRuleDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.ValidRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct;
import cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleRange;
import cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleVo;
import cn.aliyun.ryytn.modules.system.api.ProductService;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 效期分档规则接口
 * <AUTHOR>
 * @date 2023/11/14 11:08
 */
@Slf4j
@Service
public class ValidRuleServiceImpl implements ValidRuleService
{


    @Autowired
    private ValidRuleDao validRuleDao;


    @Autowired
    private ProductService productService;

    /**
     *
     * @Description 查询效期分档规则列表数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/16 10:12
     */
    @Override
    public List<ValidRuleDto> queryValidRuleList(ValidRuleDto validRuleDto) throws Exception
    {
        List<ValidRuleDto> validRuleDtos = validRuleDao.queryValidRuleList(validRuleDto);
        validRuleDtos = validRuleDtos.stream().sorted((a, b) -> {
            //先根据效期 1 已生效  0 未生效  2 已过期  再根据创建时间倒序
            if (a.getStatus() == 1)
            {
                return b.getStatus() == 1 ? 1 : -1;
            }
            else if (b.getStatus() == 1)
            {
                return a.getStatus() == 1 ? -1 : 1;
            }
            else
            {
                return a.getStatus() - b.getStatus();
            }
        }).collect(Collectors.toList());
        return validRuleDtos;
    }

    /**
     *
     * @Description 查询效期分档规则详情数据
     * @param  id
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/17 14:12
     */
    @Override
    public ValidRuleVo queryValidRuleDetail(String id) throws Exception
    {
        //查主表以及关联子表数据
        ValidRuleVo validRuleVo = validRuleDao.queryValidRuleDetail(id);
        List<RuleCategory> categoryList = validRuleVo.getCategoryList();
        if (categoryList != null && categoryList.size() > 0)
        {
            if (categoryList.get(0).getId() == null)
            {
                validRuleVo.setCategoryList(null);
            }
        }

        List<RuleProduct> productList = validRuleVo.getProductList();
        if (productList != null && productList.size() > 0)
        {
            if (productList.get(0).getId() == null)
            {
                validRuleVo.setProductList(null);
            }
        }
        return validRuleVo;
    }

    /**
     *
     * @Description 删除效期分档规则数据
     * @param  id
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/17 14:12
     */
    @Override
    public void deleteValidRule(String id) throws Exception
    {
        ValidRuleVo validRuleVo = this.queryValidRuleDetail(id);
        // 实在不懂为什么isDefault，0是默认，1是非默认
        if (validRuleVo.getIsDefault().equals(0))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_INITED);
        }

        //删除表数据
        validRuleDao.deleteValidRuleById(id);
        validRuleDao.deleteValidRuleRangeByRuleId(id);
        validRuleDao.deleteValidRuleCategoryByRuleId(id);
        validRuleDao.deleteValidRuleProductByRuleId(id);
    }

    /**
     *
     * @Description 新增、编辑效期分档规则
     * @param  validRuleVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/18 16:58
     */
    @Override
    public void addOrUpdateValidRule(ValidRuleVo validRuleVo) throws Exception
    {
        // 查询可调节天数名称是否重复
        int num = validRuleDao.countValidRuleName(validRuleVo);
        if (num > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }

        //删除关联表，新增
        this.packSkuCodesField(validRuleVo);
        String id = validRuleVo.getId();
        Integer distributeType = validRuleVo.getDistributeType();
        //二、入自己的表
        if (id != null)
        {
            //修改
            validRuleDao.deleteValidRuleRangeByRuleId(id);
            validRuleDao.deleteValidRuleProductByRuleId(id);
            validRuleDao.deleteValidRuleCategoryByRuleId(id);
            validRuleVo.setUpdatedBy("修改");
            validRuleVo.setUpdatedTime(new Date());
            if (validRuleVo.getForeverFlag() == 1)
            {
                validRuleVo.setStartTime(null);
                validRuleVo.setEndTime(null);
            }
            validRuleDao.updateValidRule(validRuleVo);
        }
        else
        {
            //新增
            //添加控制 通用规则只能有一个
//            if (validRuleVo.getName().equals("通用规则") || validRuleVo.getRangeType() == 2)
//            {
//                List<ValidRuleDto> validRuleVos = this.queryValidRuleList(null);
//                if (!CollectionUtils.isEmpty(validRuleVos))
//                {
//                    List<ValidRuleDto> specialList =
//                        validRuleVos.stream()
//                            .filter(item -> item.getDistributeType() == distributeType)
//                            .filter(item -> item.getName().equals("通用规则") || item.getRangeType() == 2).collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(specialList))
//                    {
//                        throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
//                    }
//                }
//            }
            id = SeqUtils.getSequenceUid();
            validRuleVo.setId(id);
            validRuleVo.setCreatedBy("新增");
            validRuleVo.setCreatedTime(new Date());
            validRuleDao.insertValidRule(validRuleVo);
        }
        List<ValidRuleRange> rangeList = validRuleVo.getRangeList();
        for (ValidRuleRange validRuleRange : rangeList)
        {
            validRuleRange.setId(SeqUtils.getSequenceUid());
            validRuleRange.setRuleId(id);
        }
        validRuleDao.insertValidRuleRangeList(rangeList);

        List<RuleCategory> categoryList = validRuleVo.getCategoryList();
        if (categoryList != null && categoryList.size() > 0)
        {
            for (RuleCategory ruleCategory : categoryList)
            {
                ruleCategory.setId(SeqUtils.getSequenceUid());
                ruleCategory.setRuleId(id);
            }
            validRuleDao.insertValidRuleCategoryList(categoryList);
        }

        List<RuleProduct> productList = validRuleVo.getProductList();
        if (productList != null && productList.size() > 0)
        {

            for (RuleProduct ruleProduct : productList)
            {
                ruleProduct.setId(SeqUtils.getSequenceUid());
                ruleProduct.setRuleId(id);
            }
            validRuleDao.insertValidRuleProductList(productList);
        }


    }


    public void packSkuCodesField(ValidRuleVo validRuleVo) throws Exception
    {
        //根据等级赋值 SkuCodes 字段
        List<RuleCategory> categoryList = validRuleVo.getCategoryList();
        if (categoryList == null || categoryList.size() < 1)
        {
            return;
        }
        //前端传多个层级的，我只需要入最后一级
        List<Integer> collect = categoryList.stream().map(RuleCategory::getLevel).sorted().collect(Collectors.toList());
        Integer maxLevel = collect.get(collect.size() - 1);
        categoryList = categoryList.stream().filter(item -> item.getLevel().equals(maxLevel)).collect(Collectors.toList());
        validRuleVo.setCategoryList(categoryList);
        //查询基础产品数据
        List<SkuDto> skuList = productService.querySkuList(null);
        for (RuleCategory ruleCategory : categoryList)
        {
            Integer level = ruleCategory.getLevel();
            String categoryCode = ruleCategory.getCategoryCode();
            String skuCodes = "";
            switch (level)
            {
                case 1:
                    skuCodes = skuList.stream().filter(item -> StringUtils.equals(item.getLv1CategoryCode(), categoryCode)).map(item -> item.getSkuCode())
                        .collect(Collectors.joining(","));
                    break;
                case 2:
                    skuCodes = skuList.stream().filter(item -> StringUtils.equals(item.getLv2CategoryCode(), categoryCode)).map(item -> item.getSkuCode())
                        .collect(Collectors.joining(","));
                    break;
                case 3:
                    skuCodes = skuList.stream().filter(item -> StringUtils.equals(item.getLv3CategoryCode(), categoryCode)).map(item -> item.getSkuCode())
                        .collect(Collectors.joining(","));
                    break;
            }
            ruleCategory.setSkuCodes(skuCodes);
        }

    }


    /**
     * 判断产品在生效的产品是否存在
     * @param validRuleVo
     */
    public Boolean checkIfExistSku(ValidRuleVo validRuleVo)
    {

        Integer distributeType = validRuleVo.getDistributeType();

        List<RuleProduct> productList = validRuleVo.getProductList();

        List<RuleCategory> categoryList = validRuleVo.getCategoryList();

        if (productList != null && productList.size() > 0)
        {
            List<String> skuCodeList = productList.stream().map(item -> item.getSkuCode()).collect(Collectors.toList());
            Integer count = validRuleDao.checkSkuIfExist(skuCodeList, distributeType);
            if (count != null && count > 0)
            {
                return true;
            }
        }

        if (categoryList != null && categoryList.size() > 0)
        {
            //查询现在生效的品类下skuCode集合
            List<String> skuCodeList = validRuleDao.queryCategorySkuCodeList(distributeType);
            //判断新的skuCode 是否包含在里面
            if (skuCodeList.size() > 0)
            {
                for (RuleCategory ruleCategory : categoryList)
                {
                    String skuCodes = ruleCategory.getSkuCodes();
                    String[] split = skuCodes.split(",");
                    for (String skuCode : split)
                    {
                        for (String skuCode2 : skuCodeList)
                        {
                            if (skuCode2.contains(skuCode))
                            {
                                return true;
                            }
                        }
                    }
                }
            }


        }
        return false;
    }
}
