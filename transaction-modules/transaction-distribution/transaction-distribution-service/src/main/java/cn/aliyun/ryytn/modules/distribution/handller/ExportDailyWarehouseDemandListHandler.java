package cn.aliyun.ryytn.modules.distribution.handller;

import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.modules.distribution.api.SkuAbcTypeService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo;

/**
 * @Description 导出日分仓需求列表
 * <AUTHOR>
 * @date 2023/12/22 10:26
 */
@Component("exportDailyWarehouseDemandList")
public class ExportDailyWarehouseDemandListHandler extends AbstractExportExcelHandler
{
    @Autowired
    private DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Autowired
    SkuAbcTypeService skuAbcTypeService;

    @Autowired
    private RedisUtils redisUtils;

    private static final Integer ToB = 0;

    private static final Integer ToC = 1;

    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());
        // 解析参数
        QueryDailyWarehouseDemandListReqVo dailyWarehouseDemandListReqVo =
            condition.getCondition().toJavaObject(QueryDailyWarehouseDemandListReqVo.class);
        ValidateUtil.checkIsNotEmpty(dailyWarehouseDemandListReqVo.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseDemandListReqVo.getDemandPlanVersion());
        dailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(dailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));

        List<DailyWarehouseDemandDto> dailyWarehouseDemandDtoList = dailyWarehouseDemandDao.queryDailyWarehouseDemandList(dailyWarehouseDemandListReqVo);

        Map<String, String> skuAbcDtoMaps = skuAbcTypeService.querySkuAbcTypeList();

        // 封装数据
        if (CollectionUtils.isNotEmpty(dailyWarehouseDemandDtoList))
        {
            int rowId = 1;
            for (DailyWarehouseDemandDto data : dailyWarehouseDemandDtoList)
            {
                data.setRowId(rowId++);
                if (ToB.equals(data.getDistributeType()))
                {
                    data.setDistributeTypeName("ToB");
                }
                else if (ToC.equals(data.getDistributeType()))
                {
                    data.setDistributeTypeName("ToC");
                }
                else
                {
                    data.setDistributeTypeName(null);
                }
                String skuAbcType = skuAbcDtoMaps.get(data.getSkuCode() +CommonConstants.UNDERLINE_STRING+data.getDistributeType());
                if (StringUtils.isEmpty(skuAbcType)){
                    data.setAbcType(CommonConstants.C+CommonConstants.LEI_CHINESE);
                } else {
                    data.setAbcType(skuAbcType+CommonConstants.LEI_CHINESE);
                }
                String dateRecorded = data.getDateRecorded();
                char zero = 48;
                Object month, day;
                if (zero == dateRecorded.charAt(4))
                {
                    month = dateRecorded.charAt(5);
                }
                else
                {
                    month = dateRecorded.substring(4, 6);
                }
                if (zero == dateRecorded.charAt(6))
                {
                    day = dateRecorded.charAt(7);
                }
                else
                {
                    day = dateRecorded.substring(6, 8);
                }
                data.setDateRecorded(month + "月" + day + "日");
            }
        }
        else
        {
            dailyWarehouseDemandDtoList = Collections.emptyList();
        }

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream, DailyWarehouseDemandDto.class).excelType(ExcelTypeEnum.XLSX).sheet("日分仓需求编辑导出")
                .doWrite(dailyWarehouseDemandDtoList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }


}
