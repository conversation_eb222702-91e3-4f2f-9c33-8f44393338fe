package cn.aliyun.ryytn.modules.distribution.task;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.modules.demand.api.ChannelDemandPlanService;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListReqVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.QueryChannelDemandPlanListRspVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.mq.MqFactory;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.modules.distribution.api.AiplanTaskService;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 调拨计划定时任务
 * <AUTHOR>
 * @date 2023/11/29 10:25
 */
@Slf4j
@Service
public class AiplanScheduledTaskServiceImpl implements TaskService
{
    @Autowired
    private AiplanTaskService aiplanTaskService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Autowired
    private ChannelDemandPlanService channelDemandPlanService;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        try
        {
            List<Object> mqRecordList = redisUtils.lGet(CommonConstants.TOPIC_GENERATE_FREIGHTPLAN, 0, -1);
            Set<SoWtDemandDto> soWtDemandSet = mqRecordList.stream().map(item -> (SoWtDemandDto) item).collect(Collectors.toSet());
            List<SoWtDemandDto> soWtDemandList = dailyWarehouseDemandDao.querySoWtDemandVersion();

            List<QueryChannelDemandPlanListRspVo> channelDemandPlanList = channelDemandPlanService.queryChannelDemandPlanList(new QueryChannelDemandPlanListReqVo());
            log.info("get channelDemandPlanList is :{}",channelDemandPlanList);

            for (SoWtDemandDto soWtDemand : soWtDemandList)
            {
                QueryChannelDemandPlanListRspVo channelDemandPlan = channelDemandPlanList.stream().filter(x->x.getDemandPlanCode().equals(soWtDemand.getDemandPlanCode())).findFirst().orElse(null);
                if ((CollectionUtils.isNotEmpty(soWtDemandSet) && soWtDemandSet.contains(soWtDemand)) || (null != channelDemandPlan && channelDemandPlan.getStatus()==-1))
                {
                    continue;
                }
                log.info("add channelDemand FREIGHTPLAN list,id{},name:{}",soWtDemand.getDemandPlanCode(),soWtDemand.getDemandPlanName());
                MqFactory.newProducerService().produce(CommonConstants.TOPIC_GENERATE_FREIGHTPLAN, soWtDemand);
            }
        }
        catch (Exception e)
        {
            log.info("error调拨计划算法执行失败AiplanScheduledTaskServiceImpl.Exception:{}" + e);
            throw e;
        }
    }

    //    @Override
//    @Deprecated
//    public void processOld(SchedulerJob schedulerJob) throws Exception
//    {
//        try
//        {
//            // 获取分布式锁 六小时后自动解锁 没获取到一直抢锁
//            while (!redisUtils.lock(CommonConstants.REDIS_ALGO_DISTRIBUTED_LOCK_KEY, 21600))
//            {
//                // sleep30秒
//                Thread.sleep(30000);
//            }
//
//            List<SoWtDemandDto> soWtDemandList = dailyWarehouseDemandDao.querySoWtDemandVersion();
//            for (SoWtDemandDto soWtDemand : soWtDemandList)
//            {
//                // 执行算法调度任务
//                boolean unexecuted = true;
//                while (unexecuted)
//                {
//                    TaskStatus taskStatus = aiplanTaskService.executeAiplanScheduledTask(soWtDemand);
//                    // 如果没被跳过执行，说明执行完成
//                    if (!taskStatus.equals(TaskStatus.SKIP))
//                    {
//                        unexecuted = false;
//                    }
//                }
//            }
//        }
//        catch (Exception e)
//        {
//            log.info("AiplanScheduledTaskServiceImpl.Exception:{}" + e);
//        }
//        finally
//        {
//            redisUtils.unlock(CommonConstants.REDIS_ALGO_DISTRIBUTED_LOCK_KEY);
//        }
//
//    }

}
