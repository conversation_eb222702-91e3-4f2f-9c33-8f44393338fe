package cn.aliyun.ryytn.modules.distribution.service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSONArray;
import com.aliyun.brain.dataindustry.common.enums.TaskStatus;
import com.aliyun.brain.dataindustry.microapp.MicroAppTaskInstanceOutputVO;
import com.aliyun.brain.dataindustry.microapp.request.ApiRunMicroAppRequest;
import com.aliyun.brain.dataindustry.microapp.request.MicroAppInstantIdRequest;
import com.aliyun.dataq.dataindustry.DataIndustrySpringServiceContext;
import com.aliyun.dataq.dataindustry.config.Header;
import com.aliyun.dataq.dataindustry.service.MicroAppService;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.distribution.api.ChannelTaskService;
import cn.aliyun.ryytn.modules.distribution.dao.AlgoSchedulingRecordDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.AlgoSchedulingRecordDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.AlgoDbReqVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.AlgoDbRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.ChannelArgs;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 渠道预测算法调度任务
 * <AUTHOR>
 * @date 2023/12/13 14:44
 */
@Service
@Slf4j
public class ChannelTaskServiceImpl implements ChannelTaskService
{
    @Resource(name = "dataIndustryContext")
    private DataIndustrySpringServiceContext dataIndustryContext;

    @Value("${dataq.scheduler.userId}")
    private String userId;

    @Value("${dataq.scheduler.tenantCode}")
    private String tenantCode;

    @Value("${dataq.scheduler.workspaceCode}")
    private String workspaceCode;

    @Value("${dataq.scheduler.constantCode}")
    private String constantCode;

    @Autowired
    private AlgoSchedulingRecordDao algoSchedulingRecordDao;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private DataqService dataqService;

    private final Integer CHANNELSCENE = 0;

    @Override
    public TaskStatus executeChannelScheduledTask() throws Exception
    {
        String appCode = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_APPCODE_CHANNEL_FORECAST_TASK");

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DISTRIBUTION_ALGO_DB_LIST"));
        AlgoDbReqVo algoDbVo = new AlgoDbReqVo();
        algoDbVo.setConstantCode(constantCode);
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, algoDbVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        List<AlgoDbRspVo> dataqResultList = jsonArray.toJavaList(AlgoDbRspVo.class);

        // 配置算法调度入参
        ApiRunMicroAppRequest apiRunMicroAppRequest = new ApiRunMicroAppRequest();
        apiRunMicroAppRequest.setAppCode(appCode);
        ChannelArgs channelArgs = new ChannelArgs();
        channelArgs.setAgent("CHANNEL");
        channelArgs.setEvalEnd("20230424");
        channelArgs.setObjectType("CONVENTIONAL");
        channelArgs.setPeriods(12);
        channelArgs.setAlgo_name_and_version("algo_reseller_order_monthly_forecast-V0.0");
        String id = SeqUtils.getSequenceUid();
        channelArgs.setTaskDetailId(id);
        channelArgs.setDefaultDatabaseInfo(dataqResultList.get(0).getExtend());

        Map<String, Object> map = new HashMap<>();
        map.put("args", channelArgs);
        apiRunMicroAppRequest.setApiParamValues(map);
        log.info("Enter executeChannelScheduledTask.apiRunMicroAppRequest:" + apiRunMicroAppRequest);

        // 配置header
        Header header = new Header();
        header.setUserId(userId);
        header.setTenantCode(tenantCode);
        header.setWorkspaceCode(workspaceCode);

        // 执行算法调度任务
        MicroAppService service = dataIndustryContext.getService(MicroAppService.class);
        MicroAppTaskInstanceOutputVO microAppTaskInstanceOutputVO = service.execute(apiRunMicroAppRequest, header);

        // 算法调度记录
        AlgoSchedulingRecordDto algoSchedulingRecordDto = new AlgoSchedulingRecordDto();
        algoSchedulingRecordDto.setId(id);
        algoSchedulingRecordDto.setStartTime(new Date());
        algoSchedulingRecordDto.setAlgoNameAndVersion("algo_reseller_order_monthly_forecast-V0.0");

        // 轮询该算法调度任务，直到它成功或者失败
        boolean isExecutingScheduledTask = true;
        int count = 0;
        TaskStatus status = null;
        while (isExecutingScheduledTask && count < 720)
        {
            // 查询算法调度任务状态并分析
            MicroAppInstantIdRequest microAppInstantIdRequest = new MicroAppInstantIdRequest();
            microAppInstantIdRequest.setAppCode(appCode);
            microAppInstantIdRequest.setTaskInstanceId(microAppTaskInstanceOutputVO.getTaskInstanceId());
            MicroAppTaskInstanceOutputVO result = service.instanceById(microAppInstantIdRequest, header);
            status = result.getStatus();

            if (status.equals(TaskStatus.SUCCESS) || status.equals(TaskStatus.FAILED) || status.equals(TaskStatus.STOPPED) || status.equals(TaskStatus.SKIP))
            {
                // 退出轮询
                isExecutingScheduledTask = false;
            }
            else
            {
                Thread.sleep(30000);
                count++;
            }
        }
        algoSchedulingRecordDto.setEndTime(new Date());
        algoSchedulingRecordDto.setRunningStatus(getStatusCode(status));
        algoSchedulingRecordDto.setScene(CHANNELSCENE);
        // 执行成功数据库中新增调拨计划算法
        algoSchedulingRecordDao.addAlgoSchedulingRecord(algoSchedulingRecordDto);
        return status;
    }

    private Integer getStatusCode(TaskStatus status)
    {
        switch (status)
        {
            case INIT:
                return 0;
            case RUNNING:
                return 1;
            case STOPPING:
                return 2;
            case STOPPED:
                return 3;
            case SKIP:
                return 4;
            case SUCCESS:
                return 5;
            case FAILED:
                return 6;
        }
        return null;
    }
}
