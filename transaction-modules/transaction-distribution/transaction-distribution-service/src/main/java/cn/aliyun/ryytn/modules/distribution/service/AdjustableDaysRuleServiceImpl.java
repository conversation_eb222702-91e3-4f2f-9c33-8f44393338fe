package cn.aliyun.ryytn.modules.distribution.service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.util.StringUtils;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.distribution.api.AdjustableDaysRuleService;
import cn.aliyun.ryytn.modules.distribution.dao.AdjustableDaysRuleDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.AdjustableDaysRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryAdjustableDaysRuleListRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct;
import cn.aliyun.ryytn.modules.system.api.ProductService;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;

/**
 * @Description 可调天数规则实现类
 * <AUTHOR>
 * @date 2023/11/16 10:38
 */
@Service
public class AdjustableDaysRuleServiceImpl implements AdjustableDaysRuleService
{
    @Autowired
    AdjustableDaysRuleDao adjustableDaysRuleDao;

    @Autowired
    private ProductService productService;

    private static final Integer RANGETYPE_PRODUCT = 0;
    private static final Integer RANGETYPE_CATEGORY = 1;
    private static final Integer RANGETYPE_ALL = 2;
    private static final Integer NOT_FOREVER = 0;
    private static final Integer IS_FOREVER = 1;
    private static final Integer STATUS_TO_BE_EFFECTIVE = 0;
    private static final Integer STATUS_EFFECTIVE = 1;
    private static final Integer STATUS_EXPIRED = 2;
    private static final String GENERAL_RULE = "通用规则";


    /**
     *
     * @Description 新增可调天数规则
     * @param adjustableDaysRuleDto
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 14:57     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAdjustableDaysRule(AdjustableDaysRuleDto adjustableDaysRuleDto) throws Exception
    {
        // 查询可调节天数名称是否重复
        int num = adjustableDaysRuleDao.countAdjustableDaysRuleName(adjustableDaysRuleDto);
        if (num > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }

        // 获取skuCodes
        packSkuCodesField(adjustableDaysRuleDto);

        String id = adjustableDaysRuleDto.getId();
        // id存在 则更新主表
        if (ObjectUtils.isNotEmpty(id))
        {
            adjustableDaysRuleDao.deleteAdjustableDaysRuleRange(id);
            adjustableDaysRuleDao.deleteAdjustableDaysRuleRangeCategory(id);
            adjustableDaysRuleDao.deleteAdjustableDaysRuleRangeProduct(id);
            // 封装更新人 更新时间
            adjustableDaysRuleDto.setUpdatedBy((ServiceContextUtils.currentSession().getAccount().getLoginId()));
            adjustableDaysRuleDto.setUpdatedTime(new Date());
            adjustableDaysRuleDao.updateAdjustableDaysRule(adjustableDaysRuleDto);
        }
        else
        {
            // 生成id
            id = SeqUtils.getSequenceUid();
            adjustableDaysRuleDto.setId(id);

            // 获取当前登录用户
            adjustableDaysRuleDto.setCreatedBy(ServiceContextUtils.currentSession().getAccount().getLoginId());

            // 新增可调天数规则主表信息
            adjustableDaysRuleDao.addAdjustableDaysRule(adjustableDaysRuleDto);
        }

        // 避免 Variable used in lambda expression should be final or effectively final 报错
        // lambda表达式中使用的变量应该是final或者有效的final
        String finalId = id;

        // 封装可调天数规则范围子表
        if (CollectionUtils.isNotEmpty(adjustableDaysRuleDto.getAdjustableDaysRuleRangeList()))
        {
            adjustableDaysRuleDto.getAdjustableDaysRuleRangeList().stream()
                .forEach(i -> {
                    i.setId(SeqUtils.getSequenceUid());
                    i.setRuleId(finalId);
                });
            // 插库
            adjustableDaysRuleDao.addAdjustableDaysRuleRange(adjustableDaysRuleDto.getAdjustableDaysRuleRangeList());
        }

        // 根据范围类型 将数据插入不同表中
        if (RANGETYPE_PRODUCT.equals(adjustableDaysRuleDto.getRangeType()))
        {
            if (CollectionUtils.isNotEmpty(adjustableDaysRuleDto.getProductList()))
            {
                // 生成id 封装ruleId
                adjustableDaysRuleDto.getProductList().stream()
                    .forEach(i -> {
                        i.setId(SeqUtils.getSequenceUid());
                        i.setRuleId(finalId);
                    });
                adjustableDaysRuleDao.addAdjustableDaysRuleRangeProduct(adjustableDaysRuleDto.getProductList());
            }
        }

        if (RANGETYPE_CATEGORY.equals(adjustableDaysRuleDto.getRangeType()))
        {
            if (CollectionUtils.isNotEmpty(adjustableDaysRuleDto.getCategoryList()))
            {
                // 生成id 封装ruleId
                adjustableDaysRuleDto.getCategoryList().stream()
                    .forEach(i -> {
                        i.setId(SeqUtils.getSequenceUid());
                        i.setRuleId(finalId);
                    });
                adjustableDaysRuleDao.addAdjustableDaysRuleRangeCategory(adjustableDaysRuleDto.getCategoryList());
            }
        }
    }

    private void packSkuCodesField(AdjustableDaysRuleDto adjustableDaysRuleDto) throws Exception
    {
        //根据等级赋值 SkuCodes 字段
        List<RuleCategory> categoryList = adjustableDaysRuleDto.getCategoryList();
        if (categoryList == null || categoryList.size() < 1)
        {
            return;
        }
        // 前端传多个层级的，我只需要入最后一级
        List<Integer> collect = categoryList.stream().map(RuleCategory::getLevel).sorted().collect(Collectors.toList());
        Integer maxLevel = collect.get(collect.size() - 1);
        categoryList = categoryList.stream().filter(item -> item.getLevel().equals(maxLevel)).collect(Collectors.toList());
        adjustableDaysRuleDto.setCategoryList(categoryList);
        //查询基础产品数据
        List<SkuDto> skuList = productService.querySkuList(null);
        for (RuleCategory ruleCategory : categoryList)
        {
            Integer level = ruleCategory.getLevel();
            String categoryCode = ruleCategory.getCategoryCode();
            String skuCodes = "";
            switch (level)
            {
                case 1:
                    skuCodes = skuList.stream().filter(item -> StringUtils.equals(item.getLv1CategoryCode(), categoryCode)).map(item -> item.getSkuCode())
                        .collect(Collectors.joining(","));
                    break;
                case 2:
                    skuCodes = skuList.stream().filter(item -> StringUtils.equals(item.getLv2CategoryCode(), categoryCode)).map(item -> item.getSkuCode())
                        .collect(Collectors.joining(","));
                    break;
                case 3:
                    skuCodes = skuList.stream().filter(item -> StringUtils.equals(item.getLv3CategoryCode(), categoryCode)).map(item -> item.getSkuCode())
                        .collect(Collectors.joining(","));
                    break;
            }
            ruleCategory.setSkuCodes(skuCodes);
        }
    }

    /**
     *
     * @Description 查询可调天数规则
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月20日 14:58     */
    @Override
    public List<QueryAdjustableDaysRuleListRspVo> queryAdjustableDaysRuleList() throws Exception
    {
        List<QueryAdjustableDaysRuleListRspVo> queryAdjustableDaysRuleListRspVos = adjustableDaysRuleDao.queryAdjustableDaysRuleList();
        // 根据是否永久生效 和 生效时间结束时间 封装规则的当前的状态
        for (QueryAdjustableDaysRuleListRspVo i : queryAdjustableDaysRuleListRspVos)
        {
            if (IS_FOREVER.equals(i.getForeverFlag()))
            {
                i.setStatus(STATUS_EFFECTIVE);
                continue;
            }
            // 获取当前时间
            Date date = new Date();
            // 格式化 避免时分秒对结果的影响
            SimpleDateFormat format = new SimpleDateFormat();
            format.applyPattern("yyyy-MM-dd");
            String currentDate = format.format(date);
            if (currentDate.compareTo(format.format(i.getStartTime())) < 0)
            {
                i.setStatus(STATUS_TO_BE_EFFECTIVE);
            }
            else if (currentDate.compareTo(format.format(i.getStartTime())) >= 0 && currentDate.compareTo(format.format(i.getEndTime())) <= 0)
            {
                i.setStatus(STATUS_EFFECTIVE);
            }
            else if (currentDate.compareTo(format.format(i.getEndTime())) > 0)
            {
                i.setStatus(STATUS_EXPIRED);
            }
        }

        queryAdjustableDaysRuleListRspVos = queryAdjustableDaysRuleListRspVos.stream().sorted((a, b) -> {
            //先根据效期 1 已生效  0 未生效  2 已过期  再根据创建时间倒序
            if (a.getStatus() == 1)
            {
                return b.getStatus() == 1 ? 1 : -1;
            }
            else if (b.getStatus() == 1)
            {
                return a.getStatus() == 1 ? -1 : 1;
            }
            else
            {
                return a.getStatus() - b.getStatus();
            }
        }).collect(Collectors.toList());

        return queryAdjustableDaysRuleListRspVos;
    }

    @Override
    public AdjustableDaysRuleDto queryAdjustableDaysRuleDetail(String id) throws Exception
    {
        AdjustableDaysRuleDto adjustableDaysRuleDto = adjustableDaysRuleDao.queryAdjustableDaysRuleDetail(id);

        // 过滤空数据
        List<RuleCategory> categoryList = adjustableDaysRuleDto.getCategoryList();
        if (categoryList != null && categoryList.size() > 0)
        {
            if (categoryList.get(0).getId() == null)
            {
                adjustableDaysRuleDto.setCategoryList(null);
            }
        }

        List<RuleProduct> productList = adjustableDaysRuleDto.getProductList();
        if (productList != null && productList.size() > 0)
        {
            if (productList.get(0).getId() == null)
            {
                adjustableDaysRuleDto.setProductList(null);
            }
        }

        return adjustableDaysRuleDto;
    }

    /**
     *
     * @Description 删除可调天数规则
     * @param id
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 11:35     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAdjustableDaysRule(String id) throws Exception
    {
        adjustableDaysRuleDao.deleteAdjustableDaysRule(id);
        adjustableDaysRuleDao.deleteAdjustableDaysRuleRange(id);
        adjustableDaysRuleDao.deleteAdjustableDaysRuleRangeCategory(id);
        adjustableDaysRuleDao.deleteAdjustableDaysRuleRangeProduct(id);
    }
}
