package cn.aliyun.ryytn.modules.distribution.service;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Observable;
import java.util.Observer;
import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.aliyun.brain.dataindustry.common.enums.TaskStatus;
import com.aliyun.brain.dataindustry.microapp.MicroAppTaskInstanceOutputVO;
import com.aliyun.brain.dataindustry.microapp.request.ApiRunMicroAppRequest;
import com.aliyun.brain.dataindustry.microapp.request.MicroAppInstantIdRequest;
import com.aliyun.dataq.dataindustry.DataIndustrySpringServiceContext;
import com.aliyun.dataq.dataindustry.config.Header;
import com.aliyun.dataq.dataindustry.service.MicroAppService;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.mq.MqFactory;
import cn.aliyun.ryytn.common.mq.MqRecord;
import cn.aliyun.ryytn.common.mq.api.ConsumerService;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.distribution.api.AiplanTaskService;
import cn.aliyun.ryytn.modules.distribution.dao.AlgoSchedulingRecordDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.AlgoSchedulingRecordDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.AiplanArgs;
import cn.aliyun.ryytn.modules.distribution.entity.vo.AlgoDbReqVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.AlgoDbRspVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 调拨计划算法调度实现类
 * <AUTHOR>
 * @date 2023/11/30 16:07
 */
@Service
@Slf4j
public class AiplanTaskServiceImpl implements AiplanTaskService, Observer, ApplicationListener<ContextRefreshedEvent>
{
    @Resource(name = "dataIndustryContext")
    private DataIndustrySpringServiceContext dataIndustryContext;

    @Value("${dataq.scheduler.userId}")
    private String userId;

    @Value("${dataq.scheduler.tenantCode}")
    private String tenantCode;

    @Value("${dataq.scheduler.workspaceCode}")
    private String workspaceCode;

    @Value("${dtb.boot.dtboost.access-id}")
    private String accessId;

    @Value("${dtb.boot.dtboost.access-key}")
    private String accessKey;

    @Value("${dtb.boot.dtboost.dataindustry-endpoint}")
    private String dataindustryEndpoint;

    @Value("${dataq.scheduler.constantCode}")
    private String constantCode;

    @Autowired
    private AlgoSchedulingRecordDao algoSchedulingRecordDao;

    @Autowired
    private RedisUtils redisUtils;

    private final Integer AIPLANSCENE = 3;

    @Autowired
    private DataqService dataqService;

    private ConsumerService consumerService;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event)
    {
        consumerService = MqFactory.newConsumerService(CommonConstants.TOPIC_GENERATE_FREIGHTPLAN);
        consumerService.addObservers(this);
    }

    @Override
    public void update(Observable o, Object object)
    {
        if (Objects.isNull(object) || StringUtils.isBlank(((MqRecord) object).getValue()))
        {
            return;
        }
        MqRecord mqRecord = (MqRecord) object;
        String value = mqRecord.getValue();
        SoWtDemandDto soWtDemandDto = JSON.parseObject(value, SoWtDemandDto.class);

        try
        {
            executeAiplanScheduledTask(soWtDemandDto);
        }
        catch (Exception e)
        {
            // 让消费线程知道异常，回写mq
            throw (RuntimeException) e;
        }

        // 消息回执
        consumerService.ack(mqRecord);
    }

    @Override
    public void executeAiplanScheduledTask(SoWtDemandDto soWtDemand) throws Exception
    {
        // 阿里算法资源有限，不支持并发执行任务，所有算法任务共用一个锁。
        // 客户没有采购MQ，此处采用自旋抢锁方式削峰执行
        try
        {
            log.info("executeAiplanScheduledTask start.");
            while (!redisUtils.locks(CommonConstants.REDIS_ALGO_DISTRIBUTED_LOCK_KEY, soWtDemand.getDemandPlanCode(), 1800))
            {
                //sleep30秒
                Thread.sleep(30000);
            }
            log.info("executeAiplanScheduledTask get redis lock .");

            String appCode = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_APPCODE_DAILY_WAREHOUSE_AIPLAN_TASK");
            String algoVersion = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_DAILY_WAREHOUSE_AIPLAN_ALGO_VERSION");

            // 配置算法调度入参
            ApiRunMicroAppRequest apiRunMicroAppRequest = new ApiRunMicroAppRequest();
            apiRunMicroAppRequest.setAppCode(appCode);
            AiplanArgs aiplanArgs = new AiplanArgs();
            aiplanArgs.setInvokeType("web");
            aiplanArgs.setPlanStartDate(DateUtils.getDate(DateUtils.YMD_DASH, 1));
            aiplanArgs.setPlanLength(14);
            aiplanArgs.setWeightDemand(100);
            aiplanArgs.setWeightCost(10);
            aiplanArgs.setWeightExpiry(1);
            aiplanArgs.setTimeLimit(10);
            aiplanArgs.setAlgo_name_and_version(algoVersion);
            aiplanArgs.setDemand_plan_code(soWtDemand.getDemandPlanCode());
            aiplanArgs.setDemand_plan_name(soWtDemand.getDemandPlanName());
            aiplanArgs.setVersion_id(soWtDemand.getVersionId());
            String id = SeqUtils.getSequenceUid();
            aiplanArgs.setTask_detail_id(id);

            String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DISTRIBUTION_ALGO_DB_LIST"));
            AlgoDbReqVo algoDbVo = new AlgoDbReqVo();
            algoDbVo.setConstantCode(constantCode);
            DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, algoDbVo);
            JSONArray jsonArray = (JSONArray) dataqResult.getData();
            List<AlgoDbRspVo> dataqResultList = jsonArray.toJavaList(AlgoDbRspVo.class);

            aiplanArgs.setDefaultDatabaseInfo(dataqResultList.get(0).getExtend());

            String currentDate = DateUtils.getDate(DateUtils.YMD);
            String predictionVersionKey = StringUtils.format(CommonConstants.INCREAMENT_FREIGHT_PLAN_PREDICTION_VERSION_KEY, soWtDemand.getDemandPlanCode(),
                currentDate);
            long predictionVersionIndex = redisUtils.incr(predictionVersionKey, 1L, 86400);
            String predictionVersion = "AIPlan-" + currentDate;
            if (predictionVersionIndex > 0)
            {
                predictionVersion = predictionVersion + StringUtils.DATE_SEPARATOR + predictionVersionIndex;
            }

            Map<String, Object> map = new HashMap<>();
            map.put("args", aiplanArgs);
            map.put("task_detail_id_args", id);
            map.put("prediction_version", predictionVersion);
            apiRunMicroAppRequest.setApiParamValues(map);
            log.info("Enter executeAiplanScheduledTask.apiRunMicroAppRequest:{}", apiRunMicroAppRequest);

            // 配置header
            Header header = new Header();
            header.setUserId(userId);
            header.setTenantCode(tenantCode);
            header.setWorkspaceCode(workspaceCode);

            log.info("executeAiplanScheduledTask MicroAppService get  dataIndustryContext.{}",null == dataIndustryContext ?true:false);
            // 执行算法调度任务
            MicroAppService service = dataIndustryContext.getService(MicroAppService.class);
            MicroAppTaskInstanceOutputVO microAppTaskInstanceOutputVO = service.execute(apiRunMicroAppRequest, header);

            // 算法调度记录
            AlgoSchedulingRecordDto algoSchedulingRecordDto = new AlgoSchedulingRecordDto();
            algoSchedulingRecordDto.setId(id);
            algoSchedulingRecordDto.setStartTime(new Date());
            algoSchedulingRecordDto.setAlgoNameAndVersion(algoVersion);

            // 轮询该算法调度任务，直到它成功或者失败
            int count = 0;
            TaskStatus status = TaskStatus.INIT;
            while (count < 60)
            {
                // 查询算法调度任务状态并分析
                MicroAppInstantIdRequest microAppInstantIdRequest = new MicroAppInstantIdRequest();
                microAppInstantIdRequest.setAppCode(appCode);
                microAppInstantIdRequest.setTaskInstanceId(microAppTaskInstanceOutputVO.getTaskInstanceId());
                MicroAppTaskInstanceOutputVO result = service.instanceById(microAppInstantIdRequest, header);
                status = result.getStatus();

                if (status.equals(TaskStatus.SUCCESS))
                {
                    log.info("executeAiplanScheduledTask {} success.", id);
                    break;
                }
                else if (Arrays.asList(CommonConstants.TASK_STATUS_NEED_RELOAD).contains(status.name()))
                {
                    log.error("executeAiplanScheduledTask {} failed.status is {}.", id, status);
                    throw new ServiceException(ErrorCodeConstants.FAIL_DATAQ_ERROR);
                }
                else
                {
                    Thread.sleep(30000);
                    count++;
                }
            }
            algoSchedulingRecordDto.setEndTime(new Date());
            algoSchedulingRecordDto.setRunningStatus(getStatusCode(status));
            algoSchedulingRecordDto.setScene(AIPLANSCENE);
            // 执行成功数据库中新增调拨计划算法
            algoSchedulingRecordDao.addAlgoSchedulingRecord(algoSchedulingRecordDto);
        }
        catch (Exception e)
        {
            log.info("error调拨计划算法执行失败AiplanScheduledTaskServiceImpl.Exception:{}" + e);
            throw e;
        }
        finally
        {
            redisUtils.unlock(CommonConstants.REDIS_ALGO_DISTRIBUTED_LOCK_KEY);
        }
    }

    @Deprecated
    public TaskStatus executeAiplanScheduledTaskOld(SoWtDemandDto soWtDemand) throws Exception
    {
        String appCode = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_APPCODE_DAILY_WAREHOUSE_AIPLAN_TASK");
        String algoVersion = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_DAILY_WAREHOUSE_AIPLAN_ALGO_VERSION");

        // 配置算法调度入参
        ApiRunMicroAppRequest apiRunMicroAppRequest = new ApiRunMicroAppRequest();
        apiRunMicroAppRequest.setAppCode(appCode);
        AiplanArgs aiplanArgs = new AiplanArgs();
        aiplanArgs.setInvokeType("web");
        aiplanArgs.setPlanStartDate(DateUtils.getDate(DateUtils.YMD_DASH));
        aiplanArgs.setPlanLength(14);
        aiplanArgs.setWeightDemand(100);
        aiplanArgs.setWeightCost(10);
        aiplanArgs.setWeightExpiry(1);
        aiplanArgs.setTimeLimit(10);
        aiplanArgs.setAlgo_name_and_version(algoVersion);
        aiplanArgs.setDemand_plan_code(soWtDemand.getDemandPlanCode());
        aiplanArgs.setDemand_plan_name(soWtDemand.getDemandPlanName());
        aiplanArgs.setVersion_id(soWtDemand.getVersionId());
        String id = SeqUtils.getSequenceUid();
        aiplanArgs.setTask_detail_id(id);

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DISTRIBUTION_ALGO_DB_LIST"));
        AlgoDbReqVo algoDbVo = new AlgoDbReqVo();
        algoDbVo.setConstantCode(constantCode);
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, algoDbVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        List<AlgoDbRspVo> dataqResultList = jsonArray.toJavaList(AlgoDbRspVo.class);

        aiplanArgs.setDefaultDatabaseInfo(dataqResultList.get(0).getExtend());

        Map<String, Object> map = new HashMap<>();
        map.put("args", aiplanArgs);
        apiRunMicroAppRequest.setApiParamValues(map);
        log.info("Enter executeAiplanScheduledTask.apiRunMicroAppRequest:" + apiRunMicroAppRequest);

        // 配置header
        Header header = new Header();
        header.setUserId(userId);
        header.setTenantCode(tenantCode);
        header.setWorkspaceCode(workspaceCode);

        // 执行算法调度任务
        MicroAppService service = dataIndustryContext.getService(MicroAppService.class);
        MicroAppTaskInstanceOutputVO microAppTaskInstanceOutputVO = service.execute(apiRunMicroAppRequest, header);

        // 算法调度记录
        AlgoSchedulingRecordDto algoSchedulingRecordDto = new AlgoSchedulingRecordDto();
        algoSchedulingRecordDto.setId(id);
        algoSchedulingRecordDto.setStartTime(new Date());
        algoSchedulingRecordDto.setAlgoNameAndVersion(algoVersion);

        // 轮询该算法调度任务，直到它成功或者失败
        boolean isExecutingScheduledTask = true;
        int count = 0;
        TaskStatus status = null;
        while (isExecutingScheduledTask && count < 720)
        {
            // 查询算法调度任务状态并分析
            MicroAppInstantIdRequest microAppInstantIdRequest = new MicroAppInstantIdRequest();
            microAppInstantIdRequest.setAppCode(appCode);
            microAppInstantIdRequest.setTaskInstanceId(microAppTaskInstanceOutputVO.getTaskInstanceId());
            MicroAppTaskInstanceOutputVO result = service.instanceById(microAppInstantIdRequest, header);
            status = result.getStatus();

            if (status.equals(TaskStatus.SUCCESS) || status.equals(TaskStatus.FAILED) || status.equals(TaskStatus.STOPPED) || status.equals(TaskStatus.SKIP))
            {
                // 退出轮询
                isExecutingScheduledTask = false;
            }
            else
            {
                Thread.sleep(30000);
                count++;
            }
        }
        algoSchedulingRecordDto.setEndTime(new Date());
        algoSchedulingRecordDto.setRunningStatus(getStatusCode(status));
        algoSchedulingRecordDto.setScene(AIPLANSCENE);
        // 执行成功数据库中新增调拨计划算法
        algoSchedulingRecordDao.addAlgoSchedulingRecord(algoSchedulingRecordDto);
        return status;
    }

    private Integer getStatusCode(TaskStatus status)
    {
        switch (status)
        {
            case INIT:
                return 0;
            case RUNNING:
                return 1;
            case STOPPING:
                return 2;
            case STOPPED:
                return 3;
            case SKIP:
                return 4;
            case SUCCESS:
                return 5;
            case FAILED:
                return 6;
        }
        return null;
    }
}
