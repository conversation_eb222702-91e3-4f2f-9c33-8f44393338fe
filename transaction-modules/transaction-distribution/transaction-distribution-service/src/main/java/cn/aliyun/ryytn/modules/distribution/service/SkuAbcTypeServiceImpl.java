package cn.aliyun.ryytn.modules.distribution.service;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.modules.distribution.api.SkuAbcTypeService;
import cn.aliyun.ryytn.modules.distribution.dataqdao.DataqSkuAbcDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SkuAbcDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-09-12 16:38
 * @Description
 */
@Slf4j
@Service
public class SkuAbcTypeServiceImpl implements SkuAbcTypeService {

    @Autowired
    private DataqSkuAbcDao dataqSkuAbcDao;

    private static final Integer TOBCode = 0;

    private static final Integer TOCCode = 1;

    @Override
    public Map<String, String> querySkuAbcTypeList() throws Exception {
        List<SkuAbcDto> skuAbcDtoList = dataqSkuAbcDao.querySkuAbcList(null);
        Map<String, String> skuAbcDtoMap = new HashMap<>();
        for (SkuAbcDto skuAbcDto : skuAbcDtoList) {
            String key = skuAbcDto.getSkuCode();
            String sourceOrderType = skuAbcDto.getSourceOrderType();
            if ("toC".equals(sourceOrderType)) {
                key += CommonConstants.UNDERLINE_STRING + TOCCode;
            } else if ("toB".equals(sourceOrderType)) {
                key += CommonConstants.UNDERLINE_STRING + TOBCode;
            }
            skuAbcDtoMap.put(key, skuAbcDto.getAbcType());
        }
        return skuAbcDtoMap;
    }

}
