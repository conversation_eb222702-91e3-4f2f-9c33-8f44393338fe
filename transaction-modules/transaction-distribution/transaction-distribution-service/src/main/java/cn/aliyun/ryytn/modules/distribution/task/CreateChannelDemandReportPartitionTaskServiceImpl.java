package cn.aliyun.ryytn.modules.distribution.task;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.modules.demand.dao.ChannelDemandReportDao;
import cn.aliyun.ryytn.modules.demand.dataqdao.DataqChannelDemandReportDao;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description 创建渠道需求提报分区表任务
 * <AUTHOR>
 * @date 2025/02/12 15:37
 */
@Slf4j
@Service
public class CreateChannelDemandReportPartitionTaskServiceImpl implements TaskService
{

    @Resource
    private DataqChannelDemandReportDao dataqChannelDemandReportDao;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        // 明年
        String nextYear = DateUtils.formatTime(DateUtils.addYears(new Date(), 1), DateUtils.Y);
        List<String> partitionList = new ArrayList<>(12);
        for (int i = 1; i <= 12; i++)
        {
            partitionList.add(nextYear + String.format("%02d", i));
        }
        dataqChannelDemandReportDao.createChannelDemandReportPartitionTable(partitionList);
    }
}
