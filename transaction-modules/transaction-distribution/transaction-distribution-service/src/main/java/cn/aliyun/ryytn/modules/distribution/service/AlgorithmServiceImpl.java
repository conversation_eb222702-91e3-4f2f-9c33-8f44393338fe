package cn.aliyun.ryytn.modules.distribution.service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.modules.distribution.api.AlgorithmService;
import cn.aliyun.ryytn.modules.distribution.dao.AlgoSchedulingRecordDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.AlgoSchedulingRecordDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PageAlgoVersionReqVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PageAlgoVersionRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryAlgorithmListReqVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryAlgorithmListRspVo;

/**
 * @Description 算法管理实现类
 * <AUTHOR>
 * @date 2023/11/21 16:29
 */
@Service
public class AlgorithmServiceImpl implements AlgorithmService
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private AlgoSchedulingRecordDao algoSchedulingRecordDao;

    /**
     *
     * @Description 查询算法列表
     * @param
     * @return
     * <AUTHOR>
     * @date 2023年11月21日 17:28     */
    @Override
    public List<QueryAlgorithmListRspVo> queryAlgorithmList() throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_DISTRIBUTION_ALGORITHM_LIST"));
        //aiplan
        QueryAlgorithmListReqVo queryAlgorithmListReqVo = new QueryAlgorithmListReqVo();
        queryAlgorithmListReqVo.setScenes("aiplan,logistics");
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryAlgorithmListReqVo);
        // 将json转换成list
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        List<QueryAlgorithmListRspVo> dataqResultList = jsonArray.toJavaList(QueryAlgorithmListRspVo.class);

        // 查询算法调度任务并封装
        List<AlgoSchedulingRecordDto> algoSchedulingRecordList = algoSchedulingRecordDao.queryAlgoSchedulingRecord();
        Map<String, List<AlgoSchedulingRecordDto>> collect = algoSchedulingRecordList.stream().
            collect(Collectors.groupingBy(AlgoSchedulingRecordDto::getAlgoNameAndVersion));
        for (QueryAlgorithmListRspVo vo : dataqResultList)
        {
            List<AlgoSchedulingRecordDto> list = collect.get(vo.getAlgoNameAndVersion());
            if (CollectionUtils.isNotEmpty(list))
            {
                vo.setVersionNum(list.size());
                List<Date> dateList = list.stream().
                    map(AlgoSchedulingRecordDto::getEndTime).
                    sorted().
                    collect(Collectors.toList());
                SimpleDateFormat format = new SimpleDateFormat("yy/MM/dd");
                vo.setLatestVersion(format.format(dateList.get(dateList.size() - 1)));
            }
        }

        return dataqResultList;
    }

    /**
     *
     * @Description 分页查询算法版本
     * @param condition
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月05日 11:14     */
    @Override
    public PageInfo<PageAlgoVersionRspVo> queryAlgoVersion(PageCondition<PageAlgoVersionReqVo> condition) throws Exception
    {
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        List<PageAlgoVersionRspVo> result = algoSchedulingRecordDao.queryAlgoVersion(condition.getCondition());
        for (PageAlgoVersionRspVo i : result)
        {
            i.setVersion(DateUtils.formatDate(i.getStartTime(), DateUtils.YMD_SLASH));
        }
        return new PageInfo<>(result);
    }
}
