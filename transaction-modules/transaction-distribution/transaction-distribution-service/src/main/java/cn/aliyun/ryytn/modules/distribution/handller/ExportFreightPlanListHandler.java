package cn.aliyun.ryytn.modules.distribution.handller;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.excel.HideProperty;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import cn.aliyun.ryytn.modules.distribution.api.FreightPlanService;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.dataqdao.DataqFreightPlanDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.StockFluctuateDto;
import cn.aliyun.ryytn.modules.system.api.ProductService;
import cn.aliyun.ryytn.modules.system.dataqdao.DataqSkuDao;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import cn.aliyun.ryytn.modules.system.entity.vo.SkuConditionVo;

/**
 *
 * @Description 导出调拨计划
 * <AUTHOR>
 * @date 2023/12/29 20:14
 */
@Component("exportFreightPlanListList")
public class ExportFreightPlanListHandler extends AbstractExportExcelHandler
{
    @Autowired
    private DataqFreightPlanDao dataqFreightPlanDao;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private FreightPlanService freightPlanService;

    @Autowired
    private ProductService productService;

    @Autowired
    private DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Autowired
    private DataqSkuDao dataqSkuDao;

    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        FreightPlanDto freightPlanDto = condition.getCondition().toJavaObject(FreightPlanDto.class);

        FreightPlanDto freightPlanVersion = dataqFreightPlanDao.queryFreightPlanVersion(freightPlanDto);
        freightPlanDto.setVersionId(freightPlanVersion.getVersionId());

        freightPlanDto.setGroupColumn(new StringBuilder().append(GroupColumnEnum.item.getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.startPoint.getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.startPhysicalPoint.getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.endPoint.getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.endPhysicalPoint.getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.departureValid.getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.shippingType.getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.departureDate.getColumnName())
            .toString());
        freightPlanDto.setSortColumn(new StringBuilder().append(GroupColumnEnum.item.getSortColumn().getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.startPoint.getSortColumn().getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.startPhysicalPoint.getSortColumn().getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.endPoint.getSortColumn().getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.endPhysicalPoint.getSortColumn().getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.departureValid.getSortColumn().getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.shippingType.getSortColumn().getColumnName()).append(StringUtils.COMMA_SEPARATOR)
            .append(GroupColumnEnum.departureDate.getSortColumn().getColumnName())
            .toString());

        // 查询调拨数据，根据导出结果字段维度分组
        List<FreightPlanDto> freightPlanList = dataqFreightPlanDao.queryFreightPlanDataList(freightPlanDto);

        if (CollectionUtils.isNotEmpty(freightPlanList))
        {
            // 查询日分仓调拨需求合计
            DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto = new DailyWarehouseAiplanDemandDto();
            dailyWarehouseAiplanDemandDto.setDemandPlanCode(freightPlanDto.getDemandPlanCode());
            dailyWarehouseAiplanDemandDto.setDemandPlanVersion(
                StringUtils.substringBefore(freightPlanDto.getVersionId(), StringUtils.COMMA_SEPARATOR));
            dailyWarehouseAiplanDemandDto.setAiplanDemandVersion(
                StringUtils.substringAfter(freightPlanDto.getVersionId(), StringUtils.COMMA_SEPARATOR));
            // 如果多仓比对或者RDC推演指定产品，需要将生产编码转换为多个销售编码
            if (StringUtils.isNotBlank(freightPlanDto.getItemIds()))
            {
                List<String> skuCodeList = dataqSkuDao.querySaleSkuByProductionSku(freightPlanDto.getItemIds());
                if (CollectionUtils.isNotEmpty(skuCodeList))
                {
                    dailyWarehouseAiplanDemandDto.setSkuCodes(skuCodeList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
                }
            }
            dailyWarehouseAiplanDemandDto.setWarehouseCode(freightPlanDto.getEndPhysicalPointId());
            dailyWarehouseAiplanDemandDto.setWarehouseCodes(freightPlanDto.getEndPhysicalPointIds());
            dailyWarehouseAiplanDemandDto.setTableSuffix(StringUtils.substring(dailyWarehouseAiplanDemandDto.getDemandPlanVersion(), 2, 8));

            List<String> dateList = dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandHeadList(dailyWarehouseAiplanDemandDto);
            // 总天数
            BigDecimal totalDays = null;
            if (CollectionUtils.isNotEmpty(dateList))
            {
                totalDays = new BigDecimal(dateList.size());
            }

            List<DailyWarehouseAiplanDemandDto> dailyWarehouseAiplanDemandList =
                dailyWarehouseDemandDao.querySumGroupByWarehouseProductionSku(dailyWarehouseAiplanDemandDto);

            Map<String, Double> dailyWarehouseAiplanDemandMap = dailyWarehouseAiplanDemandList.stream().collect(Collectors.toMap(item -> {
                return item.getWarehouseCode() + StringUtils.DATE_SEPARATOR + item.getSkuCode();
            }, DailyWarehouseAiplanDemandDto::getDateValue, (key1, key2) -> key2));

            String taskDetailId = freightPlanList.get(0).getTaskDetailId();
            // 查询库存数据
            StockFluctuateDto stockFluctuateDto = new StockFluctuateDto();
            stockFluctuateDto.setTaskDetailId(taskDetailId);
            stockFluctuateDto.setItemId(freightPlanDto.getItemId());
            stockFluctuateDto.setItemIds(freightPlanDto.getItemIds());
            stockFluctuateDto.setStockPointId(freightPlanDto.getEndPointId());
            stockFluctuateDto.setStockPointIds(freightPlanDto.getEndPointIds());
            stockFluctuateDto.setPhysicalPointIds(freightPlanDto.getEndPhysicalPointIds());
            stockFluctuateDto.setGroupColumn(
                new StringBuilder().append(GroupColumnEnum.item.getColumnName()).append(StringUtils.COMMA_SEPARATOR)
                    .append(GroupColumnEnum.physicalStockPoint.getColumnName()).append(StringUtils.COMMA_SEPARATOR)
                    .append(GroupColumnEnum.date.getColumnName()).toString());
            List<StockFluctuateDto> stockFluctuateList = dataqFreightPlanDao.queryStockFluctuateListGroupBy(stockFluctuateDto);

            Map<StockFluctuateDto, StockFluctuateDto> stockFluctuateMap = stockFluctuateList.stream().collect(Collectors.toMap(Function.identity(),
                Function.identity(), (key1, key2) -> key2));

            // 查询产品信息
            SkuConditionVo skuConditionVo = new SkuConditionVo();
            skuConditionVo.setStatusId(1);
            List<SkuDto> skuList = productService.querySkuList(skuConditionVo);
            Map<String, SkuDto> skuMap = skuList.stream().collect(Collectors.toMap(SkuDto::getSkuCode, Function.identity(), (key1, key2) -> key2));

            for (FreightPlanDto freightPlan : freightPlanList)
            {
                if (freightPlan.getCdcFlag())
                {
                    freightPlan.setPlanDate(freightPlan.getDepartureDate());
                    freightPlan.setValidRuleLabel(freightPlan.getDepartureValidName());
                }
                else
                {
                    freightPlan.setPlanDate(freightPlan.getArrivalDate());
                    freightPlan.setValidRuleLabel(freightPlan.getArrivalValidName());
                }
                SkuDto sku = skuMap.get(freightPlan.getItemId());
                if (Objects.nonNull(sku))
                {
                    freightPlan.setPlanUnitCnt(sku.getPlanUnitCnt());
                    Double planUnitCnt = Objects.isNull(sku.getPlanUnitCnt()) || sku.getPlanUnitCnt().equals(0d) ? 1d : sku.getPlanUnitCnt();
                    BigDecimal actQtyBg = new BigDecimal(freightPlan.getActQty());
                    BigDecimal planUnitCntBg = new BigDecimal(planUnitCnt);
                    BigDecimal actUnitQtyBg = actQtyBg.divide(planUnitCntBg, 0, RoundingMode.UP);
                    freightPlan.setActUnitQty(actUnitQtyBg.doubleValue());
                    freightPlan.setRoughtWeight(actUnitQtyBg.multiply(new BigDecimal(sku.getRoughtWeight())).doubleValue());
                    freightPlan.setVolumn(actUnitQtyBg.multiply(new BigDecimal(sku.getVolume())).doubleValue());
                }

                StockFluctuateDto stockKey = new StockFluctuateDto();
                stockKey.setItemId(freightPlan.getItemId());
                stockKey.setStockPointId(null);
                stockKey.setPhysicalPointId(freightPlan.getEndPhysicalPointId());
                stockKey.setValidName(null);
                stockKey.setDate(freightPlan.getDepartureDate());
                stockKey.setTaskDetailId(null);
                // 当日期末库存
                BigDecimal tailQty = null;
                if (Objects.nonNull(stockFluctuateMap.get(stockKey)) && Objects.nonNull(stockFluctuateMap.get(stockKey).getTailQty()))
                {
                    tailQty = new BigDecimal(stockFluctuateMap.get(stockKey).getTailQty());
                }
                else
                {
                    tailQty = new BigDecimal(0d);
                }

                // 天数为空，可供应天数为--
                if (Objects.isNull(totalDays))
                {
                    // 被除数为0，去求总数为空，可供应天数为--
                    freightPlan.setDays(StringUtils.DOUBLE_DATE_SEPARATOR);
                    continue;
                }

                String key = freightPlan.getEndPhysicalPointId() + StringUtils.DATE_SEPARATOR + freightPlan.getItemId();
                // 调出需求总数
                BigDecimal totalQty = null;
                if (Objects.nonNull(dailyWarehouseAiplanDemandMap.get(key)) && Objects.nonNull(dailyWarehouseAiplanDemandMap.get(key)) &&
                    !dailyWarehouseAiplanDemandMap.get(key).equals(0d))
                {
                    totalQty = new BigDecimal(dailyWarehouseAiplanDemandMap.get(key));
                }
                else
                // 被除数为0，去求总数为空，可供应天数为--
                {
                    freightPlan.setDays(StringUtils.DOUBLE_DATE_SEPARATOR);
                    continue;
                }
                // 可供应天数=调拨日期期末库存数量/(全部日分仓调拨需求合计/总天数)
                freightPlan.setDays(String.valueOf(tailQty.divide(totalQty.divide(totalDays, 8, RoundingMode.HALF_UP), 0, RoundingMode.DOWN).intValue()));
            }
        }

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream, FreightPlanDto.class).excelType(ExcelTypeEnum.XLSX).sheet("调拨计划导出")
                .doWrite(freightPlanList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }

    // 需求变更，如果客户要求导出按照列表格式行转列导出，在此方法基础上修改
    @Deprecated
    public byte[] exportGroup(ExcelCondition condition) throws Exception
    {
        FreightPlanDto freightPlanDto = condition.getCondition().toJavaObject(FreightPlanDto.class);

        List<FreightPlanDto> freightPlanList = dataqFreightPlanDao.queryFreightPlanDataJsonList(freightPlanDto);

        // 导出模板表格头二维数组
        List<List<String>> headList = new ArrayList<List<String>>();
        // 通过反射获取固定字段表头
        Class clazz = FreightPlanDto.class;
        // 遍历所有字段，获取所有ExcelProperty注解
        List<ExcelProperty> excelPropertyList = new ArrayList<>(clazz.getDeclaredFields().length);
        List<Integer> hideCols = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields())
        {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (Objects.isNull(excelProperty))
            {
                continue;
            }
            HideProperty hideProperty = field.getAnnotation(HideProperty.class);
            if (Objects.nonNull(hideProperty) && hideProperty.isHide())
            {
                hideCols.add(excelProperty.index());
            }
            excelPropertyList.add(excelProperty);
        }
        // ExcelProperty注解集合根据index正序排序，取value值转换为List
        List<List<String>> fieldHeadList =
            excelPropertyList.stream().sorted(Comparator.comparing(ExcelProperty::index)).map(item -> {
                return Arrays.asList(item.value());
            }).collect(Collectors.toList());
        headList.addAll(fieldHeadList);

        List<String> bizDateValueList = dataqFreightPlanDao.queryFreightPlanHeadList(freightPlanDto);
        List<List<String>> trendsHeadList = new ArrayList<>();
        trendsHeadList.add(bizDateValueList);

        headList.addAll(trendsHeadList);

        // 由于存在动态字段，无法通过Java类定义电子表格数据，通过Object二维数组实现，字段顺序写死
        List<List<Object>> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(freightPlanList))
        {
            for (FreightPlanDto item : freightPlanList)
            {
                List<Object> rowDataList = new ArrayList<>();
                rowDataList.add(item.getItemId());
                rowDataList.add(item.getItemName());
                rowDataList.add(item.getStartPhysicalPointName());
                rowDataList.add(item.getEndPhysicalPointName());
                rowDataList.add(item.getValidRuleLabel());
                rowDataList.add(item.getShippingTypeGroupName());
                rowDataList.add(item.getPlanDate());
                rowDataList.add(item.getActQty());
                rowDataList.add(item.getPlanUnitCnt());
                rowDataList.add(item.getActUnitQty());
                rowDataList.add(item.getDays());
                List<PlanValue> planValueList = JSON.parseArray(item.getData(), PlanValue.class);
                if (CollectionUtils.isNotEmpty(planValueList))
                {
                    item.setDataMap(planValueList.stream().collect(Collectors.toMap(PlanValue::getPlanDate, Function.identity(), (ke1, key2) -> key2)));
                }
                for (List<String> trendsKeys : trendsHeadList)
                {
                    String key = trendsKeys.get(0);
                    Double num = 0d;
                    if (Objects.nonNull(item.getDataMap().get(key)))
                    {
                        num = item.getDataMap().get(key).getPlanValue();
                    }
                    rowDataList.add(num);
                }
                dataList.add(rowDataList);
            }
        }

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream, FreightPlanDto.class).excelType(ExcelTypeEnum.XLSX).sheet("调拨计划导出")
                .doWrite(dataList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }
}
