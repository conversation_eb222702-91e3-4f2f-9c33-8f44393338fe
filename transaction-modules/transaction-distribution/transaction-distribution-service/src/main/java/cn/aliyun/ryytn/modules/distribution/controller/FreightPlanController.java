package cn.aliyun.ryytn.modules.distribution.controller;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.modules.demand.entity.dto.AbcTypeDto;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.DataqTask;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.api.FreightPlanService;
import cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryInferenceDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.StockFluctuateDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseStockAnalyDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseAiplanDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 调拨计划
 * <AUTHOR>
 * @date 2023/12/26 14:14
 */
@Slf4j
@RestController
@RequestMapping("/api/distribution/freightPlan")
@Api(tags = "调拨计划")
public class FreightPlanController
{
    @Autowired
    private FreightPlanService freightPlanService;

    /**
     *
     * @Description 查询调拨计划列表
     * @param freightPlanDto
     * @return ResultInfo<List < FreightPlanDto>>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    @PostMapping("queryFreightPlanList")
    @ResponseBody
    @ApiOperation("查询调拨计划列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<FreightPlanDto>> queryFreightPlanList(@RequestBody FreightPlanDto freightPlanDto) throws Exception
    {
        List<FreightPlanDto> result = freightPlanService.queryFreightPlanList(freightPlanDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询调拨计划版本列表
     * @param freightPlanDto
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月26日 19:50
     */
    @PostMapping("queryFreightPlanVersionList")
    @ResponseBody
    @ApiOperation("查询调拨计划版本列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryFreightPlanVersionList(@RequestBody FreightPlanDto freightPlanDto) throws Exception
    {
//        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());

        List<String> result = freightPlanService.queryFreightPlanVersionList(freightPlanDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询调拨计划动态表头
     * @param FreightPlanDto
     * @return ResultInfo<List < String>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:16
     */
    @PostMapping("queryFreightPlanHeadList")
    @ResponseBody
    @ApiOperation("查询调拨计划动态表头")
    @RequiresPermissions(value = {})
    public ResultInfo<List<String>> queryFreightPlanHeadList(@RequestBody FreightPlanDto freightPlanDto) throws Exception
    {
//        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getPredictionVersion());

        List<String> result = freightPlanService.queryFreightPlanHeadList(freightPlanDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询调拨计划表头下拉列表
     * @param FreightPlanDto
     * @return ResultInfo<List < FreightPlanDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @PostMapping("queryFreightPlanHeadSelect")
    @ResponseBody
    @ApiOperation("查询调拨计划表头下拉列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<FreightPlanDto>> queryFreightPlanHeadSelect(@RequestBody FreightPlanDto freightPlanDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getPredictionVersion());

        List<FreightPlanDto> result = freightPlanService.queryFreightPlanHeadSelect(freightPlanDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询调拨计划分组聚合数据列表
     * @param FreightPlanDto
     * @return ResultInfo<List < FreightPlanDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @PostMapping("queryFreightPlanListGroupBy")
    @ResponseBody
    @ApiOperation("查询调拨计划分组聚合数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<FreightPlanDto>> queryFreightPlanListGroupBy(@RequestBody FreightPlanDto freightPlanDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getPredictionVersion());
        ValidateUtil.checkIsNotEmpty(freightPlanDto.getGroupColumnList());

        List<FreightPlanDto> result = freightPlanService.queryFreightPlanListGroupBy(freightPlanDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询调拨计划明细数据列表
     * @param condition
     * @return ResultInfo<PageInfo < FreightPlanDto>>
     * <AUTHOR>
     * @date 2023年12月20日 14:28
     */
    @PostMapping("queryFreightPlanDataPage")
    @ResponseBody
    @ApiOperation("分页查询调拨计划明细数据列表")
    @RequiresPermissions(value = {})
    public ResultInfo<PageInfo<FreightPlanDto>> queryFreightPlanDataPage(@RequestBody PageCondition<FreightPlanDto> condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
//        ValidateUtil.checkIsNotEmpty(condition.getCondition().getDemandPlanCode());
//        ValidateUtil.checkIsNotEmpty(condition.getCondition().getPredictionVersion());

        PageInfo<FreightPlanDto> result = freightPlanService.queryFreightPlanDataPage(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 日分仓需求列表查询
     * @param freightPlanDto
     * @return ResultInfo<List < DailyWarehouseDemandRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 15:43
     */
    @PostMapping("queryFreightPlanDailyWarehouseDemandList")
    @ResponseBody
    @ApiOperation("日分仓需求列表查询")
    @RequiresPermissions(value = {})
    public ResultInfo<List<DailyWarehouseDemandRspVo>> queryFreightPlanDailyWarehouseDemandList(@RequestBody FreightPlanDto freightPlanDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getPredictionVersion());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getItemId());
        ValidateUtil.checkIsNotEmpty(freightPlanDto.getGroupColumnList());

        List<DailyWarehouseDemandRspVo> result = freightPlanService.queryFreightPlanDailyWarehouseDemandList(freightPlanDto);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 日分仓调拨需求列表查询
     * @param freightPlanDto
     * @return ResultInfo<List < DailyWarehouseAiplanDemandRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 15:43
     */
    @PostMapping("queryFreightPlanDailyWarehouseAiPlanList")
    @ResponseBody
    @ApiOperation("日分仓调拨需求列表查询")
    @RequiresPermissions(value = {})
    public ResultInfo<List<DailyWarehouseAiplanDemandRspVo>> queryFreightPlanDailyWarehouseAiPlanList(@RequestBody FreightPlanDto freightPlanDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getPredictionVersion());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getItemId());

        List<DailyWarehouseAiplanDemandRspVo> result = freightPlanService.queryFreightPlanDailyWarehouseAiPlanList(freightPlanDto);

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询建议调拨计划指定分组逻辑的合计数量
     * @param FreightPlanDto
     * @return ResultInfo<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @PostMapping("queryAdvFreightPlanListGroupBySum")
    @ResponseBody
    @ApiOperation("查询建议调拨计划指定分组逻辑的合计数量")
    @RequiresPermissions(value = {})
    public ResultInfo<List<FreightPlanDto>> queryAdvFreightPlanListGroupBySum(@RequestBody FreightPlanDto freightPlanDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getPredictionVersion());
        ValidateUtil.checkIsNotEmpty(freightPlanDto.getGroupColumnList());

        List<FreightPlanDto> result = freightPlanService.queryAdvFreightPlanListGroupBySum(freightPlanDto);

        AbcTypeDto abcTypeDto = freightPlanService.getAbcType(freightPlanDto.getItemId());
        if(null != abcTypeDto){
            result.stream().forEach(x->x.setAbcType(abcTypeDto.getAbcType()));
        }
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询实际保存调拨计划指定分组逻辑的合计数量
     * @param FreightPlanDto
     * @return ResultInfo<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @PostMapping("queryActFreightPlanListGroupBySum")
    @ResponseBody
    @ApiOperation("查询实际保存调拨计划指定分组逻辑的合计数量")
    @RequiresPermissions(value = {})
    public ResultInfo<List<FreightPlanDto>> queryActFreightPlanListGroupBySum(@RequestBody FreightPlanDto freightPlanDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getPredictionVersion());
        ValidateUtil.checkIsNotEmpty(freightPlanDto.getGroupColumnList());

        List<FreightPlanDto> result = freightPlanService.queryActFreightPlanListGroupBySum(freightPlanDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询建议+实际保存调拨计划指定分组逻辑的合计数量
     * @param FreightPlanDto
     * @return ResultInfo<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @PostMapping("queryFreightPlanListGroupBySum")
    @ResponseBody
    @ApiOperation("查询建议+实际保存调拨计划指定分组逻辑的合计数量")
    @RequiresPermissions(value = {})
    public ResultInfo<List<FreightPlanDto>> queryFreightPlanListGroupBySum(@RequestBody FreightPlanDto freightPlanDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getPredictionVersion());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getGroupColumnList());
        List<GroupColumnEnum> groupColumnList = new ArrayList<>();
        groupColumnList.add(GroupColumnEnum.item);
        groupColumnList.add(GroupColumnEnum.startPoint);
        groupColumnList.add(GroupColumnEnum.shippingType);
        freightPlanDto.setGroupColumnList(groupColumnList);
        List<FreightPlanDto> advResult = freightPlanService.queryAdvFreightPlanListGroupBySum(freightPlanDto);

        freightPlanDto.getGroupColumnList().add(GroupColumnEnum.productionDate);
        List<FreightPlanDto> actResult = freightPlanService.queryActFreightPlanListGroupBySum(freightPlanDto);

        List<FreightPlanDto> result = new ArrayList<>(advResult.size() + actResult.size());
        result.addAll(advResult);
        result.addAll(actResult);
        result = result.stream().sorted(Comparator.comparing(FreightPlanDto::getStartPointId).thenComparing(FreightPlanDto::getShippingTypeGroupId)
            .thenComparing(FreightPlanDto::getProductionDate)).collect(Collectors.toList());

        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询调拨计划多仓比对列表
     * @param FreightPlanDto
     * @return ResultInfo<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @PostMapping("queryFreightPlanWarehouseContrastList")
    @ResponseBody
    @ApiOperation("查询调拨计划多仓比对列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<FreightPlanDto>> queryFreightPlanWarehouseContrastList(@RequestBody FreightPlanDto freightPlanDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getPredictionVersion());

        List<FreightPlanDto> result = freightPlanService.queryFreightPlanWarehouseContrastList(freightPlanDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询指定产品/仓库的库存数据
     * @param freightPlanDto
     * @return ResultInfo<List < InventoryStrategyVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @PostMapping("queryFreightPlanInventoryList")
    @ResponseBody
    @ApiOperation("查询指定产品/仓库的库存数据")
    @RequiresPermissions(value = {})
    public ResultInfo<List<InventoryStrategyVo>> queryFreightPlanInventoryList(@RequestBody FreightPlanDto freightPlanDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
//        ValidateUtil.checkIsNotEmpty(freightPlanDto.getSkuCodes());

        List<InventoryStrategyVo> result = freightPlanService.queryFreightPlanInventoryList(freightPlanDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 保存调拨计划
     * 需求变更，展示数据粒度从生产批次修改为效期，编辑保存功能粒度依然为生产频次
     * 上线时间过紧来不及从数据加工开始重新实现，接口当前废弃不可用
     * @param planValueList
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @Deprecated
    @PostMapping("saveFreightPlanData")
    @ResponseBody
    @ApiOperation("保存调拨计划（废弃）")
    @RequiresPermissions(value = {})
    public ResultInfo<?> saveFreightPlanData(@RequestBody List<FreightPlanDto> freightPlanDtoList) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDtoList);

        freightPlanService.saveFreightPlanData(freightPlanDtoList);
        return ResultInfo.success();
    }


    /**
     *
     * @Description 查询生产调入列表
     * @param freightPlanDto
     * @return ResultInfo<List < WarehouseStockAnalyDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @PostMapping("queryProductionFreightList")
    @ResponseBody
    @ApiOperation("查询生产调入列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<WarehouseStockAnalyDto>> queryProductionFreightList(@RequestBody FreightPlanDto freightPlanDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);

        List<WarehouseStockAnalyDto> result = freightPlanService.queryProductionFreightList(freightPlanDto);
        return ResultInfo.success(result);
    }


    /**
     *
     * @Description 查询在途库存列表
     * @param freightPlanDto
     * @return ResultInfo<List < WarehouseStockAnalyDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @PostMapping("queryOnTransInventoryList")
    @ResponseBody
    @ApiOperation("查询在途库存列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<WarehouseStockAnalyDto>> queryOnTransInventoryList(@RequestBody FreightPlanDto freightPlanDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);

        List<WarehouseStockAnalyDto> result = freightPlanService.queryOnTransInventoryList(freightPlanDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询库存列表
     * @param stockFluctuateDto
     * @return ResultInfo<List < StockFluctuateDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月19日 13:53
     */
    @PostMapping("queryStockFluctuateList")
    @ResponseBody
    @ApiOperation("查询库存列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<StockFluctuateDto>> queryStockFluctuateList(@RequestBody StockFluctuateDto stockFluctuateDto) throws Exception
    {
        List<StockFluctuateDto> result = freightPlanService.queryStockFluctuateList(stockFluctuateDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询库存推演数据
     * @param inventoryInferenceDto
     * @return ResultInfo<InventoryInferenceDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月08日 10:56
     */
    @Deprecated
    @PostMapping("queryInventoryInference")
    @ResponseBody
    @ApiOperation("查询库存推演数据")
    @RequiresPermissions(value = {})
    public ResultInfo<InventoryInferenceDto> queryInventoryInference(@RequestBody InventoryInferenceDto inventoryInferenceDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(inventoryInferenceDto);
        ValidateUtil.checkIsNotEmpty(inventoryInferenceDto.getWarehouseCode());

        InventoryInferenceDto result = freightPlanService.queryInventoryInference(inventoryInferenceDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询多仓库比对库存推演数据
     * @param inventoryInferenceDto
     * @return ResultInfo<List < InventoryInferenceDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月08日 10:56
     */
    @Deprecated
    @PostMapping("queryInventoryInferenceWarehouseContrast")
    @ResponseBody
    @ApiOperation("查询多仓库比对库存推演数据")
    @RequiresPermissions(value = {})
    public ResultInfo<List<InventoryInferenceDto>> queryInventoryInferenceWarehouseContrast(@RequestBody InventoryInferenceDto inventoryInferenceDto)
        throws Exception
    {
        ValidateUtil.checkIsNotEmpty(inventoryInferenceDto);
        ValidateUtil.checkIsNotEmpty(inventoryInferenceDto.getSkuCode());

        List<InventoryInferenceDto> result = freightPlanService.queryInventoryInferenceWarehouseContrast(inventoryInferenceDto);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 多仓比对未来可供应天数
     * @param freightPlanDto
     * @return List<StockFluctuateDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月20日 10:11
     */
    @PostMapping("queryFreightDaysWarehouseContrast")
    @ResponseBody
    @ApiOperation("多仓比对未来可供应天数")
    @RequiresPermissions(value = {})
    public ResultInfo<List<StockFluctuateDto>> queryFreightDaysWarehouseContrast(@RequestBody FreightPlanDto freightPlanDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(freightPlanDto.getPredictionVersion());

        List<StockFluctuateDto> result = freightPlanService.queryFreightDaysWarehouseContrast(freightPlanDto);
        return ResultInfo.success(result);
    }

    @PostMapping("generateFreightPlan")
    @ResponseBody
    @ApiOperation("生成调拨计划")
    @RequiresPermissions(value = {})
    public ResultInfo<?> generateFreightPlan(@RequestBody SoWtDemandDto soWtDemandDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(soWtDemandDto);
        ValidateUtil.checkIsNotEmpty(soWtDemandDto.getDemandPlanCode());

        freightPlanService.generateFreightPlan(soWtDemandDto);

        return ResultInfo.success();
    }

    @PostMapping("queryWarehouseCapacity")
    @ResponseBody
    @ApiOperation("查询仓容")
    @RequiresPermissions(value = {})
    public ResultInfo<Long> queryWarehouseCapacity(@RequestBody InventoryInferenceDto inventoryInferenceDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(inventoryInferenceDto);
        ValidateUtil.checkIsNotEmpty(inventoryInferenceDto.getWarehouseCode());

        Long result = freightPlanService.queryWarehouseCapacity(inventoryInferenceDto);
        return ResultInfo.success(result);
    }

    @PostMapping("queryFreightTaskDetail")
    @ResponseBody
    @ApiOperation("查询调拨任务详情")
    @RequiresPermissions(value = {})
    public ResultInfo<DataqTask> queryFreightTaskDetail(@RequestBody FreightPlanDto freightPlanDto) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(freightPlanDto);
        ValidateUtil.checkIsNotEmpty(freightPlanDto.getDemandPlanCode());

        DataqTask dataqTask = freightPlanService.queryFreightTaskDetail(freightPlanDto.getDemandPlanCode());
        return ResultInfo.success(dataqTask);
    }
}
