package cn.aliyun.ryytn.modules.distribution.controller;

import java.util.List;
import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.aliyun.brain.dataindustry.microapp.MicroAppTaskInstanceOutputVO;
import com.aliyun.brain.dataindustry.microapp.request.MicroAppInstantIdRequest;
import com.aliyun.dataq.dataindustry.DataIndustrySpringServiceContext;
import com.aliyun.dataq.dataindustry.config.Header;
import com.aliyun.dataq.dataindustry.service.MicroAppService;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.distribution.api.AiplanTaskService;
import cn.aliyun.ryytn.modules.distribution.api.AlgorithmService;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PageAlgoVersionReqVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PageAlgoVersionRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryAlgorithmListRspVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 算法管理接口
 * <AUTHOR>
 * @date 2023/11/21 16:24
 */
@Slf4j
@RestController
@RequestMapping("/api/distribution/Algorithm")
@Api(tags = "算法管理")
public class AlgorithmController
{
    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private AiplanTaskService aiplanTaskService;

    @Autowired
    private DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Resource(name = "dataIndustryContext")
    private DataIndustrySpringServiceContext dataIndustryContext;

    private static final String APPCODE = "MICROAPP_cce7add5f46845bd8dd65640f13c447e";

    @Value("${dataq.scheduler.userId}")
    private String userId;

    @Value("${dataq.scheduler.tenantCode}")
    private String tenantCode;

    @Value("${dataq.scheduler.workspaceCode}")
    private String workspaceCode;

    @ApiOperation("查询微应用进度")
    @PostMapping("/queryTask")
    public MicroAppTaskInstanceOutputVO queryTask(String appCode, Long taskId) throws Exception
    {
        MicroAppService service = dataIndustryContext.getService(MicroAppService.class);
        MicroAppInstantIdRequest microAppInstantIdRequest = new MicroAppInstantIdRequest();
        microAppInstantIdRequest.setAppCode(appCode);
        microAppInstantIdRequest.setTaskInstanceId(taskId);
        Header header = new Header();
        header.setUserId(userId);
        header.setTenantCode(tenantCode);
        header.setWorkspaceCode(workspaceCode);
        return service.instanceById(microAppInstantIdRequest, header);
    }

//    @ApiOperation("调拨计划测试")
//    @PostMapping("/aiplan/test")
//    public void aiplanTest() throws Exception
//    {
//        List<SoWtDemandDto> soWtDemandList = dailyWarehouseDemandDao.querySoWtDemandVersion();
//        for (SoWtDemandDto soWtDemand : soWtDemandList)
//        {
//            aiplanTaskService.executeAiplanScheduledTask(soWtDemand);
//        }
//    }
//
//    @ApiOperation("最基础测试")
//    @PostMapping("/test/execute")
//    public MicroAppTaskInstanceOutputVO execute(@RequestBody String json)
//    {
//        MicroAppService service = dataIndustryContext.getService(MicroAppService.class);
//        ApiRunMicroAppRequest request = new ApiRunMicroAppRequest();
//        Header header = new Header();
//        header.setUserId("1656878636427662");
//        header.setTenantCode("tenant_1656878636427662");
//        header.setWorkspaceCode("znyy_prod");
//        request.setAppCode("MICROAPP_373a047081c540acaf7ffdb6577c0e63");
//        Map<String, Object> map = new HashMap<>(2);
//        map.putAll(JSONObject.parseObject(json));
//        request.setApiParamValues(map);
//        MicroAppTaskInstanceOutputVO execute = service.execute(request, header);
//        return execute;
//    }

    /**
     *
     * @Description 查询算法管理列表
     * @param
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 16:54     */
    @PostMapping("queryAlgorithmList")
    @ResponseBody
    @ApiOperation("查询算法管理列表")
    //@RequiresPermissions(value = {"demand:forecastResult:channel:query"})
    public ResultInfo<List<QueryAlgorithmListRspVo>> queryAlgorithmList() throws Exception
    {
        List<QueryAlgorithmListRspVo> result = algorithmService.queryAlgorithmList();
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询算法版本
     * @param condition
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月25日 9:51     */
    @PostMapping("pageAlgoVersion")
    @ResponseBody
    @ApiOperation("分页查询算法版本")
    //@RequiresPermissions(value = {"demand:forecastResult:channel:query"})
    public ResultInfo<PageInfo<PageAlgoVersionRspVo>> pageAlgoVersion(@RequestBody PageCondition<PageAlgoVersionReqVo> condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getPageNum());
        ValidateUtil.checkIsNotEmpty(condition.getPageSize());
        ValidateUtil.checkIsNotEmpty(condition.getCondition().getAlgoNameAndVersion());

        PageInfo<PageAlgoVersionRspVo> result = algorithmService.queryAlgoVersion(condition);
        return ResultInfo.success(result);
    }
}
