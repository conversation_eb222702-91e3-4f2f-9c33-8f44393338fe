package cn.aliyun.ryytn.modules.distribution.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aliyun.brain.dataindustry.common.enums.TaskStatus;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.modules.distribution.api.WarehouseTaskService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 渠道需求预测定时任务
 * <AUTHOR>
 * @date 2023/11/30 10:31
 */
@Slf4j
@Service
public class WarehouseScheduledTaskServiceImpl implements TaskService
{
    @Autowired
    private WarehouseTaskService warehouseTaskService;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        try
        {
            // 获取分布式锁 六小时后自动解锁 没获取到一直抢锁
            while (!redisUtils.lock(CommonConstants.REDIS_ALGO_DISTRIBUTED_LOCK_KEY, 21600))
            {
                // sleep30秒
                Thread.sleep(30000);
            }
            // 执行算法调度任务
            boolean unexecuted = true;
            while (unexecuted)
            {
                TaskStatus taskStatus = warehouseTaskService.executeWarehouseScheduledTask();
                // 如果没被跳过执行，说明执行完成
                if (!taskStatus.equals(TaskStatus.SKIP))
                {
                    unexecuted = false;
                }
            }

        }
        catch (Exception e)
        {
            log.error("error分仓预测算法执行失败.WarehouseScheduledTaskServiceImpl.Exception:" + e.getMessage());
        }
        finally
        {
            redisUtils.unlock(CommonConstants.REDIS_ALGO_DISTRIBUTED_LOCK_KEY);
        }
    }
}
