package cn.aliyun.ryytn.modules.distribution.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.distribution.api.ValidRuleService;
import cn.aliyun.ryytn.modules.distribution.entity.dto.ValidRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 效期分档规则
 * <AUTHOR>
 * @date 2023/11/14 11:09
 */
@Slf4j
@RestController
@RequestMapping("/api/distribution/validRule")
@Api(tags = "效期分档规则")
public class ValidRuleController
{

    /**
     * 效期分档规则接口
     */
    @Autowired
    private ValidRuleService validRuleService;

    /**
     *
     * @Description 查询效期分档规则列表
     * @return ResultInfo<List < Object>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 11:09
     */
    @PostMapping("queryValidRuleList")
    @ResponseBody
    @ApiOperation("查询效期分档规则列表")
//    @RequiresPermissions({})
    public ResultInfo<List<ValidRuleDto>> queryValidRuleList(@RequestBody ValidRuleDto validRuleDto) throws Exception
    {
        return ResultInfo.success(validRuleService.queryValidRuleList(validRuleDto));
    }

    /**
     *
     * @Description 查询效期分档规则详情
     * @return ResultInfo <Object>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 15:36
     */
    @PostMapping("queryValidRuleDetail")
    @ResponseBody
    @ApiOperation("查询效期分档规则详情")
//    @RequiresPermissions({})
    public ResultInfo<ValidRuleVo> queryValidRuleDetail(@RequestBody String id) throws Exception
    {

        ValidateUtil.checkIsNotEmpty(id);

        return ResultInfo.success(validRuleService.queryValidRuleDetail(id));
    }


    /**
     *
     * @Description 删除效期分档规则
     * @return ResultInfo <Object>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 15:36
     */
    @PostMapping("deleteValidRule")
    @ResponseBody
    @ApiOperation("删除效期分档规则")
    @Transactional(rollbackFor = Exception.class)
//    @RequiresPermissions({})
    public ResultInfo<?> deleteValidRule(@RequestBody String id) throws Exception
    {

        ValidateUtil.checkIsNotEmpty(id);

        validRuleService.deleteValidRule(id);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 新增、编辑效期分档规则
     * @return ResultInfo <Object>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 15:36
     */
    @PostMapping("addOrUpdateValidRule")
    @ResponseBody
    @ApiOperation("新增、编辑效期分档规则")
    @Transactional(rollbackFor = Exception.class)
//    @RequiresPermissions({})
    public ResultInfo<?> addOrUpdateValidRule(@RequestBody ValidRuleVo validRuleVo) throws Exception
    {

        ValidateUtil.checkIsNotEmpty(validRuleVo);

        ValidateUtil.checkIsNotEmpty(validRuleVo.getRangeList());

        validRuleService.addOrUpdateValidRule(validRuleVo);

        return ResultInfo.success();
    }


}
