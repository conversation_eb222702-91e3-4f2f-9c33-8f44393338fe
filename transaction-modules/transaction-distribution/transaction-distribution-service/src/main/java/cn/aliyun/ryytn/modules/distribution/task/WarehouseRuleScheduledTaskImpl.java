package cn.aliyun.ryytn.modules.distribution.task;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.common.utils.concurrent.ThreadPoolExecutorUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.distribution.api.WarehouseRuleService;
import cn.aliyun.ryytn.modules.distribution.dao.WarehouseRuleDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtStockCapacityDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseCapacityVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 仓能力规则定时任务同步中间表
 * <AUTHOR>
 * @date 2023/12/27 10:05
 */
@Slf4j
@Service
public class WarehouseRuleScheduledTaskImpl implements TaskService
{
    @Autowired
    private WarehouseRuleDao warehouseRuleDao;

    @Autowired
    private WarehouseRuleService warehouseRuleService;

    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        // 删除仓能力表数据
        warehouseRuleDao.deleteSoWtStockCapacity();

        // 查询出三种类型的仓能力规则
        List<WarehouseCapacityVo> warehouseCapacityVoByProduct = warehouseRuleDao.queryWarehouseCapacityByProduct();
        List<WarehouseCapacityVo> warehouseCapacityVoByCategory = warehouseRuleDao.queryWarehouseCapacityByCategory();
        List<WarehouseCapacityVo> warehouseCapacityVoByGeneralRule = warehouseRuleDao.queryWarehouseCapacityByGeneralRule();

        List<SoWtStockCapacityDto> addList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(warehouseCapacityVoByProduct))
        {
            for (WarehouseCapacityVo vo : warehouseCapacityVoByProduct)
            {
                SoWtStockCapacityDto dto1 = new SoWtStockCapacityDto();
                dto1.setRuleId(vo.getId());
                dto1.setStockPointId(vo.getWarehouseCode());
                dto1.setStockPointName(vo.getWarehouseName());
                dto1.setItemId(vo.getSkuCode());
                dto1.setItemName(vo.getSkuName());
                dto1.setGroupId(vo.getId());
                dto1.setType(1);
                dto1.setStatus(1);
                if (Objects.nonNull(vo.getCapacityFlag()) && vo.getCapacityFlag().equals(1))
                {
                    if (Objects.isNull(vo.getCapacity()))
                    {
                        dto1.setCapacity(null);
                    }
                    else
                    {
                        dto1.setCapacity(Double.valueOf(vo.getCapacity()));
                    }
                }
                else
                {
                    dto1.setCapacity(null);
                }

                SoWtStockCapacityDto dto2 = new SoWtStockCapacityDto();
                dto2.setRuleId(vo.getId());
                dto2.setStockPointId(vo.getWarehouseCode());
                dto2.setStockPointName(vo.getWarehouseName());
                dto2.setItemId(vo.getSkuCode());
                dto2.setItemName(vo.getSkuName());
                dto2.setGroupId(vo.getId());
                dto2.setType(2);
                dto2.setStatus(1);
                if (Objects.nonNull(vo.getShipmentUnlimitFlag()) && vo.getShipmentUnlimitFlag().equals(1))
                {
                    dto2.setCapacity(null);
                }
                else if (Objects.nonNull(vo.getShipmentLimitFlag()) && vo.getShipmentLimitFlag().equals(1))
                {
                    if (Objects.isNull(vo.getShipmentLimit()))
                    {
                        dto2.setCapacity(null);
                    }
                    else
                    {
                        dto2.setCapacity(Double.valueOf(vo.getShipmentLimit()));
                    }
                }
                else
                {
                    dto2.setCapacity(null);
                }

                SoWtStockCapacityDto dto3 = new SoWtStockCapacityDto();
                dto3.setRuleId(vo.getId());
                dto3.setStockPointId(vo.getWarehouseCode());
                dto3.setStockPointName(vo.getWarehouseName());
                dto3.setItemId(vo.getSkuCode());
                dto3.setItemName(vo.getSkuName());
                dto3.setGroupId(vo.getId());
                dto3.setType(3);
                dto3.setStatus(1);
                if (Objects.nonNull(vo.getDeliveryUnlimitFlag()) && vo.getDeliveryUnlimitFlag().equals(1))
                {
                    dto3.setCapacity(null);
                }
                else if (Objects.nonNull(vo.getDeliveryLimitFlag()) && vo.getDeliveryLimitFlag().equals(1))
                {
                    if (Objects.isNull(vo.getDeliveryLimit()))
                    {
                        dto3.setCapacity(null);
                    }
                    else
                    {
                        dto3.setCapacity(Double.valueOf(vo.getDeliveryLimit()));
                    }
                }
                else
                {
                    dto3.setCapacity(null);
                }

                addList.add(dto1);
                addList.add(dto2);
                addList.add(dto3);
            }
        }

        // 将品类转换成skuCode
        if (CollectionUtils.isNotEmpty(warehouseCapacityVoByCategory))
        {
            for (WarehouseCapacityVo vo : warehouseCapacityVoByCategory)
            {
                String skuCodes = vo.getSkuCodes();
                String[] split = StringUtils.split(skuCodes, StringUtils.COMMA_SEPARATOR);
                Set<String> skuCodeSet = new HashSet<>(Arrays.asList(split));
                for (String skuCode : skuCodeSet)
                {
                    vo.setSkuCode(skuCode);

                    SoWtStockCapacityDto dto1 = new SoWtStockCapacityDto();
                    dto1.setRuleId(vo.getId());
                    dto1.setStockPointId(vo.getWarehouseCode());
                    dto1.setStockPointName(vo.getWarehouseName());
                    dto1.setItemId(vo.getSkuCode());
                    dto1.setItemName(vo.getSkuName());
                    dto1.setGroupId(vo.getId());
                    dto1.setType(1);
                    dto1.setStatus(1);
                    if (Objects.nonNull(vo.getCapacityFlag()) && vo.getCapacityFlag().equals(1))
                    {
                        if (Objects.isNull(vo.getCapacity()))
                        {
                            dto1.setCapacity(null);
                        }
                        else
                        {
                            dto1.setCapacity(Double.valueOf(vo.getCapacity()));
                        }
                    }
                    else
                    {
                        dto1.setCapacity(null);
                    }

                    SoWtStockCapacityDto dto2 = new SoWtStockCapacityDto();
                    dto2.setRuleId(vo.getId());
                    dto2.setStockPointId(vo.getWarehouseCode());
                    dto2.setStockPointName(vo.getWarehouseName());
                    dto2.setItemId(vo.getSkuCode());
                    dto2.setItemName(vo.getSkuName());
                    dto2.setGroupId(vo.getId());
                    dto2.setType(2);
                    dto2.setStatus(1);
                    if (Objects.nonNull(vo.getShipmentUnlimitFlag()) && vo.getShipmentUnlimitFlag().equals(1))
                    {
                        dto2.setCapacity(null);
                    }
                    else if (Objects.nonNull(vo.getShipmentLimitFlag()) && vo.getShipmentLimitFlag().equals(1))
                    {
                        if (Objects.isNull(vo.getShipmentLimit()))
                        {
                            dto2.setCapacity(null);
                        }
                        else
                        {
                            dto2.setCapacity(Double.valueOf(vo.getShipmentLimit()));
                        }
                    }
                    else
                    {
                        dto2.setCapacity(null);
                    }


                    SoWtStockCapacityDto dto3 = new SoWtStockCapacityDto();
                    dto3.setRuleId(vo.getId());
                    dto3.setStockPointId(vo.getWarehouseCode());
                    dto3.setStockPointName(vo.getWarehouseName());
                    dto3.setItemId(vo.getSkuCode());
                    dto3.setItemName(vo.getSkuName());
                    dto3.setGroupId(vo.getId());
                    dto3.setType(3);
                    dto3.setStatus(1);
                    if (Objects.nonNull(vo.getDeliveryUnlimitFlag()) && vo.getDeliveryUnlimitFlag().equals(1))
                    {
                        dto3.setCapacity(null);
                    }
                    else if (Objects.nonNull(vo.getDeliveryLimitFlag()) && vo.getDeliveryLimitFlag().equals(1))
                    {
                        if (Objects.isNull(vo.getDeliveryLimit()))
                        {
                            dto3.setCapacity(null);
                        }
                        else
                        {
                            dto3.setCapacity(Double.valueOf(vo.getDeliveryLimit()));
                        }
                    }
                    else
                    {
                        dto3.setCapacity(null);
                    }

                    addList.add(dto1);
                    addList.add(dto2);
                    addList.add(dto3);

                }
                skuCodeSet.clear();
            }
        }

        if (CollectionUtils.isNotEmpty(warehouseCapacityVoByGeneralRule))
        {
            for (WarehouseCapacityVo vo : warehouseCapacityVoByGeneralRule)
            {
                SoWtStockCapacityDto dto1 = new SoWtStockCapacityDto();
                dto1.setRuleId(vo.getId());
                dto1.setStockPointId(vo.getWarehouseCode());
                dto1.setStockPointName(vo.getWarehouseName());
                dto1.setItemId(vo.getSkuCode());
                dto1.setItemName(vo.getSkuName());
                dto1.setGroupId(vo.getId());
                dto1.setType(1);
                dto1.setStatus(1);
                if (Objects.nonNull(vo.getCapacityFlag()) && vo.getCapacityFlag().equals(1))
                {
                    if (Objects.isNull(vo.getCapacity()))
                    {
                        dto1.setCapacity(null);
                    }
                    else
                    {
                        dto1.setCapacity(Double.valueOf(vo.getCapacity()));
                    }
                }
                else
                {
                    dto1.setCapacity(null);
                }

                SoWtStockCapacityDto dto2 = new SoWtStockCapacityDto();
                dto2.setRuleId(vo.getId());
                dto2.setStockPointId(vo.getWarehouseCode());
                dto2.setStockPointName(vo.getWarehouseName());
                dto2.setItemId(vo.getSkuCode());
                dto2.setItemName(vo.getSkuName());
                dto2.setGroupId(vo.getId());
                dto2.setType(2);
                dto2.setStatus(1);
                if (Objects.nonNull(vo.getShipmentUnlimitFlag()) && vo.getShipmentUnlimitFlag().equals(1))
                {
                    dto2.setCapacity(null);
                }
                else if (Objects.nonNull(vo.getShipmentLimitFlag()) && vo.getShipmentLimitFlag().equals(1))
                {
                    if (Objects.isNull(vo.getShipmentLimit()))
                    {
                        dto2.setCapacity(null);
                    }
                    else
                    {
                        dto2.setCapacity(Double.valueOf(vo.getShipmentLimit()));
                    }
                }
                else
                {
                    dto2.setCapacity(null);
                }


                SoWtStockCapacityDto dto3 = new SoWtStockCapacityDto();
                dto3.setRuleId(vo.getId());
                dto3.setStockPointId(vo.getWarehouseCode());
                dto3.setStockPointName(vo.getWarehouseName());
                dto3.setItemId(vo.getSkuCode());
                dto3.setItemName(vo.getSkuName());
                dto3.setGroupId(vo.getId());
                dto3.setType(3);
                dto3.setStatus(1);
                if (Objects.nonNull(vo.getDeliveryUnlimitFlag()) && vo.getDeliveryUnlimitFlag().equals(1))
                {
                    dto3.setCapacity(null);
                }
                else if (Objects.nonNull(vo.getDeliveryLimitFlag()) && vo.getDeliveryLimitFlag().equals(1))
                {
                    if (Objects.isNull(vo.getDeliveryLimit()))
                    {
                        dto3.setCapacity(null);
                    }
                    else
                    {
                        dto3.setCapacity(Double.valueOf(vo.getDeliveryLimit()));
                    }
                }
                else
                {
                    dto3.setCapacity(null);
                }

                addList.add(dto1);
                addList.add(dto2);
                addList.add(dto3);
            }
        }

//        warehouseRuleDao.addSoWtStockCapacity(addList);
        ThreadPoolExecutor pool = ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.fragmentThread);
        List<List<SoWtStockCapacityDto>> partition = Lists.partition(addList, 1000);
        List<CompletableFuture<Void>> listFuture = partition.stream().map(soWtStockCapacity -> CompletableFuture.allOf(
            CompletableFuture.runAsync(() -> warehouseRuleDao.addSoWtStockCapacity(soWtStockCapacity), pool))).collect(
            Collectors.toCollection(CopyOnWriteArrayList::new));
        CompletableFuture.allOf(listFuture.toArray(new CompletableFuture[0])).join();
    }
}
