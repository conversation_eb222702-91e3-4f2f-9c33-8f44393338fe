package cn.aliyun.ryytn.modules.distribution.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.distribution.api.AdjustableDaysRuleService;
import cn.aliyun.ryytn.modules.distribution.entity.dto.AdjustableDaysRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryAdjustableDaysRuleListRspVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 可调天数规则接口
 * <AUTHOR>
 * @date 2023/11/16 10:30
 */
@Slf4j
@RestController
@RequestMapping("/api/distribution/adjustableDaysRule")
@Api(tags = "可调天数规则")
public class AdjustableDaysRuleController
{
    @Autowired
    private AdjustableDaysRuleService adjustableDaysRuleService;

    private static final Integer notForever = 0;

    /**
     *
     * @Description 查询可调天数规则列表
     * @param
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月16日 11:04     */
    @PostMapping("queryAdjustableDaysRuleList")
    @ResponseBody
    @ApiOperation("查询可调天数规则列表")
    //@RequiresPermissions(value = {"demand:config:channelDemandReport:query"})
    public ResultInfo<List<QueryAdjustableDaysRuleListRspVo>> queryAdjustableDaysRuleList() throws Exception
    {
        List<QueryAdjustableDaysRuleListRspVo> queryAdjustableDaysRuleListRspVos = adjustableDaysRuleService.queryAdjustableDaysRuleList();
        return ResultInfo.success(queryAdjustableDaysRuleListRspVos);
    }

    /**
     *
     * @Description 新增可调天数规则
     * @param adjustableDaysRuleDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月16日 11:10     */
    @PostMapping("addAdjustableDaysRuleList")
    @ResponseBody
    @ApiOperation("新增/编辑可调天数规则")
    //@RequiresPermissions(value = {"demand:config:channelDemandReport:query"})
    public ResultInfo<?> addAdjustableDaysRule(@RequestBody AdjustableDaysRuleDto adjustableDaysRuleDto)
        throws Exception
    {
        // 校验参数
        ValidateAdjustableDaysRuleDto(adjustableDaysRuleDto);

        adjustableDaysRuleService.addAdjustableDaysRule(adjustableDaysRuleDto);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询可调天数规则详情
     * @param id
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 10:18     */
    @PostMapping("queryAdjustableDaysRuleDetail")
    @ResponseBody
    @ApiOperation("查询可调天数规则详情")
    //@RequiresPermissions(value = {"demand:config:channelDemandReport:query"})
    public ResultInfo<AdjustableDaysRuleDto> queryAdjustableDaysRuleDetail(@RequestBody String id) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(id);
        AdjustableDaysRuleDto adjustableDaysRuleDto = adjustableDaysRuleService.queryAdjustableDaysRuleDetail(id);
        return ResultInfo.success(adjustableDaysRuleDto);
    }

    /**
     *
     * @Description 删除可调天数规则
     * @param id
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月21日 11:33     */
    @PostMapping("deleteAdjustableDaysRuleList")
    @ResponseBody
    @ApiOperation("删除可调天数规则")
    //@RequiresPermissions(value = {"demand:config:channelDemandReport:query"})
    public ResultInfo<?> deleteAdjustableDaysRule(@RequestBody String id) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(id);
        adjustableDaysRuleService.deleteAdjustableDaysRule(id);
        return ResultInfo.success();
    }

    private void ValidateAdjustableDaysRuleDto(AdjustableDaysRuleDto adjustableDaysRuleDto)
    {
        ValidateUtil.checkIsNotEmpty(adjustableDaysRuleDto);
        ValidateUtil.checkIsNotEmpty(adjustableDaysRuleDto.getRangeType());
        ValidateUtil.checkIsNotEmpty(adjustableDaysRuleDto.getForeverFlag());
        // 不是永久生效 需要设置生效时间
        if (notForever.equals(adjustableDaysRuleDto.getForeverFlag()))
        {
            ValidateUtil.checkIsNotEmpty(adjustableDaysRuleDto.getStartTime());
            ValidateUtil.checkIsNotEmpty(adjustableDaysRuleDto.getEndTime());
        }
        ValidateUtil.checkIsNotEmpty(adjustableDaysRuleDto.getName());
    }
}
