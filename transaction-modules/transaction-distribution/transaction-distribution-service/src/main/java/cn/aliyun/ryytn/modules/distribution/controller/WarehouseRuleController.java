package cn.aliyun.ryytn.modules.distribution.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.distribution.api.WarehouseRuleService;
import cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PhysicWarehouseVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 仓能力规则
 * <AUTHOR>
 * @date 2023/11/18 19:19
 */
@Slf4j
@RestController
@RequestMapping("/api/distribution/warehouseRule")
@Api(tags = "仓能力规则")
public class WarehouseRuleController
{

    /**
     * 仓能力规则接口
     */
    @Autowired
    private WarehouseRuleService warehouseService;

    /**
     *
     * @Description 查询仓能力规则列表
     * @return ResultInfo<List < WarehouseRuleDto>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 11:09
     */
    @PostMapping("queryWarehouseRuleList")
    @ResponseBody
    @ApiOperation("查询仓能力规则列表")
//    @RequiresPermissions({})
    public ResultInfo<List<WarehouseRuleDto>> queryValvidRuleList(@RequestBody WarehouseRuleDto warehouseRuleDto) throws Exception
    {
        return ResultInfo.success(warehouseService.queryWarehouseRuleList(warehouseRuleDto));
    }

    /**
     *
     * @Description 查询仓能力规则详情
     * @return ResultInfo <WarehouseRuleVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 15:36
     */
    @PostMapping("queryWarehouseRuleDetail")
    @ResponseBody
    @ApiOperation("查询仓能力规则详情")
//    @RequiresPermissions({})
    public ResultInfo<WarehouseRuleVo> queryWarehouseRuleDetail(@RequestBody String id) throws Exception
    {

        ValidateUtil.checkIsNotEmpty(id);

        return ResultInfo.success(warehouseService.queryWarehouseRuleDetail(id));
    }


    /**
     *
     * @Description 删除仓能力规则
     * @return ResultInfo <Object>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 15:36
     */
    @PostMapping("deleteWarehouseRule")
    @ResponseBody
    @ApiOperation("删除仓能力规则")
    @Transactional(rollbackFor = Exception.class)
//    @RequiresPermissions({})
    public ResultInfo<?> deleteWarehouseRule(@RequestBody String id) throws Exception
    {

        ValidateUtil.checkIsNotEmpty(id);

        warehouseService.deleteWarehouseRule(id);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 新增、编辑仓能力规则
     * @return ResultInfo <Object>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/14 15:36
     */
    @PostMapping("addOrUpdateWarehouseRule")
    @ResponseBody
    @ApiOperation("新增、编辑仓能力规则")
    @Transactional(rollbackFor = Exception.class)
//    @RequiresPermissions({})
    public ResultInfo<?> addOrUpdateWarehouseRule(@RequestBody WarehouseRuleVo warehouseRuleVo) throws Exception
    {

        ValidateUtil.checkIsNotEmpty(warehouseRuleVo);

        ValidateUtil.checkIsNotEmpty(warehouseRuleVo.getCapacityList());

        warehouseService.addOrUpdateWarehouseRule(warehouseRuleVo);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 更新阿里库存能力表的数据状态
     * @return ResultInfo <?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/12/01 11:35
     */
    @PostMapping("updateStockCapacityStatus")
    @ResponseBody
    @ApiOperation("更新库存能力状态")
    @Transactional(rollbackFor = Exception.class)
//    @RequiresPermissions({})
    public ResultInfo<?> updateStockCapacityStatus() throws Exception
    {
        warehouseService.updateStockCapacityStatus();
        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询仓能力规则仓库视图
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月02日 15:05     */
    @PostMapping("queryWarehouseRuleWarehouse")
    @ResponseBody
    @ApiOperation("查询仓能力规则仓库视图")
    //@RequiresPermissions({})
    public ResultInfo<?> queryWarehouseRuleWarehouse() throws Exception
    {
        List<PhysicWarehouseVo> physicWarehouseVos = warehouseService.queryWarehouseRuleWarehouse();
        return ResultInfo.success(physicWarehouseVos);
    }

    /**
     *
     * @Description 查询仓能力规则仓库视图
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月02日 15:05     */
    @PostMapping("queryAdjustableRuleWarehouse")
    @ResponseBody
    @ApiOperation("查询可调天数规则仓库")
    //@RequiresPermissions({})
    public ResultInfo<?> queryAdjustableDaysRuleWarehouse() throws Exception
    {
        List<PhysicWarehouseVo> physicWarehouseVos = warehouseService.queryAdjustableDaysRuleWarehouse();
        return ResultInfo.success(physicWarehouseVos);
    }

}
