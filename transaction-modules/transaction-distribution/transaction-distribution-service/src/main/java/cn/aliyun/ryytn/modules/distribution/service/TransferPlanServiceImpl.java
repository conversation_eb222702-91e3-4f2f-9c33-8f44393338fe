package cn.aliyun.ryytn.modules.distribution.service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.modules.distribution.api.InventoryStrategyService;
import cn.aliyun.ryytn.modules.distribution.api.TransferPlanService;
import cn.aliyun.ryytn.modules.distribution.dao.WarehouseRuleDao;
import cn.aliyun.ryytn.modules.distribution.entity.vo.EditInventoryInferenceVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryInferenceConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryInferenceDataMapVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryInferenceVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SaveTransferPlanVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SkuWarehouseCompareConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SkuWarehouseCompareVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.TransferPlanCondition;
import cn.aliyun.ryytn.modules.distribution.entity.vo.TransferPlanVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 调拨计划
 * <AUTHOR>
 * @date 2023/11/28 11:01
 */
@Slf4j
@Service
public class TransferPlanServiceImpl implements TransferPlanService
{

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private InventoryStrategyService inventoryStrategyService;


    @Autowired
    private WarehouseRuleDao warehouseRuleDao;


    @Value("classpath:dbjh.json")
    private Resource relation;

    @Value("classpath:dcbd.json")
    private Resource dcbd;
    @Value("classpath:kcty.json")
    private Resource kcty;

    @Value("classpath:Rdckcty.json")
    private Resource Rdckcty;
    @Value("classpath:editKcty.json")
    private Resource editKcty;

    /**
     *
     * @Description 查询调拨计划列表数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/24 15:38
     */
    @Override
    public BaseTable<List<TransferPlanVo>> queryTransferPlanList(TransferPlanCondition transferPlanCondition) throws Exception
    {


        BaseTable<List<TransferPlanVo>> DATA = new BaseTable<>();

        List<String> headArray = Arrays.asList("12/13", "12/14", "12/15", "12/16", "12/17", "12/18", "12/19", "12/20", "12/21", "12/22");


        String relationStr = IOUtils.toString(relation.getInputStream(), String.valueOf(StandardCharsets.UTF_8));
        DATA.setHeadArray(headArray);

        List<TransferPlanVo> transferPlanVos = JSONArray.parseArray(relationStr, TransferPlanVo.class);

        DATA.setList(transferPlanVos);

        return DATA;
    }


    /**
     *
     * @Description 查询产品多仓比对列表数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/28 11:05
     */
    @Override
    public BaseTable<List<SkuWarehouseCompareVo>> querySkuWarehouseCompareList(SkuWarehouseCompareConditionVo skuWarehouseCompareConditionVo) throws Exception
    {


        BaseTable<List<SkuWarehouseCompareVo>> dataBaseTable = new BaseTable<>();

        List<String> headArray = Arrays.asList("12/13", "12/14", "12/15", "12/16", "12/17", "12/18", "12/19", "12/20", "12/21", "12/22");

        String relationStr = IOUtils.toString(dcbd.getInputStream(), String.valueOf(StandardCharsets.UTF_8));

        dataBaseTable.setHeadArray(headArray);

        List<SkuWarehouseCompareVo> compareVos = JSONArray.parseArray(relationStr, SkuWarehouseCompareVo.class);

        dataBaseTable.setList(compareVos);

        Integer viewType = skuWarehouseCompareConditionVo.getViewType();


        //1：日分仓调拨需求 2：日分仓需求 3：期初库存 4：调拨建议 5: 计划调拨
        //6：期末库存 7：未来可供应天数
        //8：安全库存天数 9：目标库存天数
        switch (viewType)
        {
            case 1:
            {
                //日分仓调拨需求
                // 当前产品+(仓库名称=当前调入仓库)+当前效期规则的日分仓需求数量合计 数据来源：日分仓需求编辑

                break;
            }
            case 2:
            {
                //日分仓需求
//                List<Map> dailyWarehouseDemandList = transferPlanDao.queryDailyWarehouseDemandList(skuWarehouseCompareConditionVo);
//                DATA = dailyWarehouseDemandList;
                break;
            }
            case 3:
            {
                //期初库存  当前产品在当前RDC的可用库存数量合计

                break;
            }
            case 4:
            {
                //调拨建议  当前产品在当前调入仓库的计划调拨数量合计

                break;
            }
            case 5:
            {
                //计划调拨  逻辑算出来   期初库存+计划调拨-日分仓调拨需求

                break;
            }
            case 6:
            {

                // 期末库存  期初库存+计划调拨-日分仓调拨需求


                break;
            }
            case 7:
            {
                // 未来可供应天数 期末库存/(全部日分仓调拨需求合计/总天数)

                break;
            }
            case 8:
            {
                //安全库存天数  当前产品在库存策略中当前日期的安全库存天数
                List<InventoryStrategyVo> inventoryStrategyList = this.getInventoryStrategyList(skuWarehouseCompareConditionVo.getSkuCode());
                List<Map<String, String>> safetyDaysList = inventoryStrategyList.stream().map(item -> {
                    Map<String, String> singel = new HashMap<>();
                    singel.put("warehouseCode", item.getWarehouseCode());
                    singel.put("warehouseName", item.getWarehouseName());
                    singel.put("safetyDays", item.getSafetyDays().toString());
                    return singel;
                }).collect(Collectors.toList());
//                DATA = safetyDaysList;
                break;
            }
            case 9:
            {
                //目标库存天数  当前产品在库存策略中当前日期的目标库存天数
                List<InventoryStrategyVo> inventoryStrategyList = this.getInventoryStrategyList(skuWarehouseCompareConditionVo.getSkuCode());
                List<Map<String, String>> targetQtyList = inventoryStrategyList.stream().map(item -> {
                    Map<String, String> singel = new HashMap<>();
                    singel.put("warehouseCode", item.getWarehouseCode());
                    singel.put("warehouseName", item.getWarehouseName());
                    singel.put("targetQty", item.getTargetQty().toString());
                    return singel;
                }).collect(Collectors.toList());
//                DATA = targetQtyList;
                break;
            }
        }
//        return DATA;
        return dataBaseTable;

    }

    /**
     * 库存推演
     * @param condition
     * @return
     * @throws Exception
     */
    @Override
    public BaseTable<List<InventoryInferenceVo>> queryInventoryInference(InventoryInferenceConditionVo condition) throws Exception
    {
        BaseTable<List<InventoryInferenceVo>> dataBaseTable = new BaseTable<>();
        String skuCode = condition.getSkuCode();
        //分两种情况（CDC仓库   RDC仓库）

        //【仓容】
        Double capacity = warehouseRuleDao.queryCapacityByCode(condition.getWarehouseCodeIn(), skuCode);

        List<String> headArray = Arrays.asList("12/13", "12/14", "12/15", "12/16", "12/17", "12/18", "12/19", "12/20", "12/21", "12/22");
        dataBaseTable.setHeadArray(headArray);

        List<InventoryStrategyVo> inventoryStrategyList = this.getInventoryStrategyList(skuCode);
        if (condition.getInferenceType() == 0)
        {
            //预置数据
            String relationStr = IOUtils.toString(kcty.getInputStream(), String.valueOf(StandardCharsets.UTF_8));
            List<InventoryInferenceVo> inferenceVos = JSONArray.parseArray(relationStr, InventoryInferenceVo.class);
            dataBaseTable.setList(inferenceVos);
            // CDC仓库
            // 数据来源：
            //【调出量汇总】 调拨需求
            //【直发需求】  dataq
            //【期初库存】dataq
            //【生产调入】dataq
            //【期末库存】 计算
            //【未来可供应天数】计算
            //【安全库存（天数）】 库存策略 有sku就查询，没有就不显示
            if (Objects.nonNull(inventoryStrategyList) && inventoryStrategyList.size() > 0)
            {
                int safetyDays = inventoryStrategyList.stream().filter(item -> item.getSafetyDays() != null).mapToInt(InventoryStrategyVo::getSafetyDays).sum();
            }

        }
        else if (condition.getInferenceType() == 1)
        {
            //预置数据
            String RdckctyStr = IOUtils.toString(Rdckcty.getInputStream(), String.valueOf(StandardCharsets.UTF_8));
            List<InventoryInferenceVo> inferenceVos = JSONArray.parseArray(RdckctyStr, InventoryInferenceVo.class);
            dataBaseTable.setList(inferenceVos);

            //RDC仓库
            if (Objects.nonNull(inventoryStrategyList) && inventoryStrategyList.size() > 0)
            {
                //安全库存（天数）库存策略 有sku就查询，没有就不显示
                int safetyDays = inventoryStrategyList.stream().filter(item -> item.getSafetyDays() != null).mapToInt(InventoryStrategyVo::getSafetyDays).sum();
                //目标库存（天数）库存策略 有sku就查询，没有就不显示
                int targetDays = inventoryStrategyList.stream().filter(item -> item.getSafetyDays() != null).mapToInt(InventoryStrategyVo::getTargetDays).sum();
                // 【日均销量】 ：来源库存策略
                double salesDaily =
                    inventoryStrategyList.stream().filter(item -> item.getDasNum() != null).mapToDouble(InventoryStrategyVo::getDasNum).sum();

            }
            //【日分仓需求】：当前产品/全部产品+当前效期规则+当前RDC所属工厂的日分仓需求数量合计  数据来源：日分仓需求编辑
            //【日分仓调拨需求】：当前产品/全部产品+当前效期规则+当前RDC所属工厂的日分仓调拨需求数量合计 /数据来源：日分仓调拨需求
            //【期初库存】 ：当前产品/全部产品在当前RDC的可用库存数量合计 T+1开始的期初库存=上一天的期末库存
            //【在途库存】 ：当前产品/全部产品在当前RDC的在途库存数量合计
            //【计划调入】 ：当前产品/全部产品在当前RDC的调拨计划中调拨数量合计
            //【期末库存】 ：期初库存+在途库存+计划调入-日分仓调拨需求
            //【未来可供应天数】 期末库存/(全部日分仓调拨需求合计/总天数)

        }
        return dataBaseTable;

    }

    /**
     * 编辑库存推演数据
     * @param condition
     * @return
     * @throws Exception
     */
    @Override
    public BaseTable<List<EditInventoryInferenceVo>> editInventoryInference(Object condition) throws Exception
    {
        BaseTable<List<EditInventoryInferenceVo>> dataBaseTable = new BaseTable<>();
        //预置数据
        List<String> headArray = Arrays.asList("12/13", "12/14", "12/15", "12/16", "12/17", "12/18", "12/19", "12/20", "12/21", "12/22");
        dataBaseTable.setHeadArray(headArray);

        String relationStr = IOUtils.toString(editKcty.getInputStream(), String.valueOf(StandardCharsets.UTF_8));
        List<EditInventoryInferenceVo> inferenceVos = JSONArray.parseArray(relationStr, EditInventoryInferenceVo.class);
        inferenceVos.get(4).setType(1);
        inferenceVos.get(6).setType(1);
        Map<String, InventoryInferenceDataMapVo> map1 = new HashMap<String, InventoryInferenceDataMapVo>();
        for (String i : inferenceVos.get(3).getDataMap().keySet())
        {
            map1.put(i, new InventoryInferenceDataMapVo());
        }
        inferenceVos.get(4).setDataMap(map1);
        Map<String, InventoryInferenceDataMapVo> map2 = new HashMap<String, InventoryInferenceDataMapVo>();
        for (String i : inferenceVos.get(5).getDataMap().keySet())
        {
            map2.put(i, new InventoryInferenceDataMapVo());
        }
        inferenceVos.get(6).setDataMap(map2);
        dataBaseTable.setList(inferenceVos);
        return dataBaseTable;
    }

    /**
     * 查询下拉框数据
     * @return Map<String, Object>
     * @throws Exception
     */
    @Override
    public Map<String, Object> queryComboBoxData()
    {
        //先mock数据给前端  tODO 从dataq获取
        Map<String, Object> combobox = new HashMap<>();
        List<String> aa = new ArrayList<>();
        aa.add("衍生品调拨计划");
        aa.add("乳品调拨计划");
        aa.add("常温奶调拨计划");
        combobox.put("transferPlanList", aa);
        List<String> bb = new ArrayList<>();
        bb.add("aiPlan20231214");
        bb.add("aiPlan20231213");
        bb.add("aiPlan20231212");
        combobox.put("versionList", bb);
        return combobox;
    }

    /**
     * 保存计划调拨数据
     * @param saveTransferPlanVos
     * @throws Exception
     */
    @Override
    public void saveTransferPlanData(List<SaveTransferPlanVo> saveTransferPlanVos) throws Exception
    {
        // TODO: 2023/12/19  调用dataq接口保存
    }

    /**
     * 从库存策略获取数据
     * @param  skuCode 生产编码
     * @return List<InventoryStrategyVo>
     * @throws Exception
     */
    public List<InventoryStrategyVo> getInventoryStrategyList(String skuCode) throws Exception
    {
        //将生产编码转化为销售编码

        //查询生产sku与销售sku映射关系
        String pathPage = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BASEBUS_SKU_PRODUCT_TABLE"));
        Map<String, Object> body = new HashMap<>();
        body.put("productionCode", skuCode);
        Map<String, Object> param = new HashMap<>();
        param.put("return_fields", "production_code,sku_code,production_name");
        Object data = dataqService.invoke(HttpMethod.POST, pathPage, null, param, body).getData();

        if (Objects.nonNull(data))
        {
            List<Map> maps = JSONArray.parseArray(data.toString(), Map.class);
            skuCode = maps.stream().map(item -> item.get("skuCode").toString()).collect(Collectors.joining(","));
        }
//        Map<String, List<Map>> skuCodeMap = maps.stream().collect(Collectors.groupingBy(item -> item.get("skuCode").toString()));

        InventoryStrategyConditionVo vo = new InventoryStrategyConditionVo();
        vo.setSkuCodes(skuCode);
        PageCondition<InventoryStrategyConditionVo> condition = new PageCondition<>();
        condition.setPageNum(1);
        condition.setPageSize(999999);
        condition.setCondition(vo);
        PageInfo<InventoryStrategyVo> inventoryStrategyVoPageInfo = inventoryStrategyService.queryInventoryStrategyList(condition);
        List<InventoryStrategyVo> list = inventoryStrategyVoPageInfo.getList();
        //将销售sku  转化为生产sku
       /* list.stream().forEach(item->{
            String skuCode1 = item.getSkuCode();
            Map map = skuCodeMap.get(skuCode1).get(0);
            String productionCode = map.get("productionCode").toString();
            String productionName = map.get("productionName").toString();
            item.setSkuCode(productionCode);
            item.setSkuName(productionName);
        });*/
        return list;
    }


}
