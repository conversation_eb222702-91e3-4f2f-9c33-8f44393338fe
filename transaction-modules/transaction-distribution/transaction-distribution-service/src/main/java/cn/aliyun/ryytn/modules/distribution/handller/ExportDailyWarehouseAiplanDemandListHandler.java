package cn.aliyun.ryytn.modules.distribution.handller;

import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.modules.distribution.api.SkuAbcTypeService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.util.IOUtils;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto;

/**
 * @Description 导出日分仓调拨需求
 * <AUTHOR>
 * @date 2023/12/22 15:51
 */
@Component("exportDailyWarehouseAiplanDemandList")
public class ExportDailyWarehouseAiplanDemandListHandler extends AbstractExportExcelHandler
{
    @Autowired
    private DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Autowired
    SkuAbcTypeService skuAbcTypeService;

    @Autowired
    private RedisUtils redisUtils;

    private static final Integer ToB = 0;

    private static final Integer ToC = 1;

    @Override
    public byte[] export(ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getCondition());
        // 解析参数
        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto =
            condition.getCondition().toJavaObject(DailyWarehouseAiplanDemandDto.class);
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getDemandPlanCode());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getDemandPlanVersion());
        ValidateUtil.checkIsNotEmpty(dailyWarehouseAiplanDemandDto.getAiplanDemandVersion());
        dailyWarehouseAiplanDemandDto.setTableSuffix(StringUtils.substring(dailyWarehouseAiplanDemandDto.getDemandPlanVersion(), 2, 8));

        List<DailyWarehouseAiplanDemandDto> dailyWarehouseAiplanDemandDtoList =
            dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandList(dailyWarehouseAiplanDemandDto);

        Map<String, String> skuAbcDtoMaps = skuAbcTypeService.querySkuAbcTypeList();

        // 封装数据
        if (CollectionUtils.isNotEmpty(dailyWarehouseAiplanDemandDtoList))
        {
            int rowId = 1;
            for (DailyWarehouseAiplanDemandDto data : dailyWarehouseAiplanDemandDtoList)
            {
                data.setRowId(rowId++);
                if (ToB.equals(data.getDistributeType()))
                {
                    data.setDistributeTypeName("ToB");
                }
                else if (ToC.equals(data.getDistributeType()))
                {
                    data.setDistributeTypeName("ToC");
                }
                else
                {
                    data.setDistributeTypeName(null);
                }

                //20240912增加ABC分类
                String skuAbcType = skuAbcDtoMaps.get(data.getSkuCode() + CommonConstants.UNDERLINE_STRING+data.getDistributeType());
                if (StringUtils.isEmpty(skuAbcType)){
                    data.setAbcType(CommonConstants.C+CommonConstants.LEI_CHINESE);
                } else {
                    data.setAbcType(skuAbcType+CommonConstants.LEI_CHINESE);
                }


                String dateRecorded = data.getDateRecorded();
                char zero = 48;
                Object month, day;
                if (zero == dateRecorded.charAt(4))
                {
                    month = dateRecorded.charAt(5);
                }
                else
                {
                    month = dateRecorded.substring(4, 6);
                }
                if (zero == dateRecorded.charAt(6))
                {
                    day = dateRecorded.charAt(7);
                }
                else
                {
                    day = dateRecorded.substring(6, 8);
                }
                data.setDateRecorded(month + "月" + day + "日");
            }
        }
        else
        {
            dailyWarehouseAiplanDemandDtoList = Collections.emptyList();
        }

        byte[] bytes = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try
        {
            EasyExcel.write(byteArrayOutputStream, DailyWarehouseAiplanDemandDto.class).excelType(ExcelTypeEnum.XLSX).sheet("日分仓调拨需求导出")
                .doWrite(dailyWarehouseAiplanDemandDtoList);

            bytes = byteArrayOutputStream.toByteArray();
        }
        catch (Exception e)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            IOUtils.close(byteArrayOutputStream);
        }

        return bytes;
    }
}
