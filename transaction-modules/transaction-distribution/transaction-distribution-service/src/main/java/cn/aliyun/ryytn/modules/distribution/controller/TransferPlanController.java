package cn.aliyun.ryytn.modules.distribution.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.BaseTable;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.distribution.api.TransferPlanService;
import cn.aliyun.ryytn.modules.distribution.entity.vo.EditInventoryInferenceVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryInferenceConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryInferenceVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SaveTransferPlanVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SkuWarehouseCompareConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SkuWarehouseCompareVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.TransferPlanCondition;
import cn.aliyun.ryytn.modules.distribution.entity.vo.TransferPlanVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 调拨计划
 * <AUTHOR>
 * @date 2023/11/24 14:06
 */
@Slf4j
@RestController
@RequestMapping("/api/distribution/transferPlan")
@Api(tags = "调拨计划（废弃）")
public class TransferPlanController
{


    @Autowired
    private TransferPlanService transferPlanService;

    /**
     *
     * @Description 查询调拨计划列表
     * @return ResultInfo<List < Object>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/24 14:08
     */
    @PostMapping("queryTransferPlanList")
    @ResponseBody
    @ApiOperation("查询调拨计划列表")
//    @RequiresPermissions({})
    public ResultInfo<BaseTable<List<TransferPlanVo>>> queryTransferPlanList(@RequestBody TransferPlanCondition transferPlanCondition) throws Exception
    {
        //参数校验
        ValidateUtil.checkIsNotEmpty(transferPlanCondition);
        ValidateUtil.checkIsNotEmpty(transferPlanCondition.getTransferPlan());
        ValidateUtil.checkIsNotEmpty(transferPlanCondition.getTransferPlanVersion());

        return ResultInfo.success(transferPlanService.queryTransferPlanList(transferPlanCondition));
    }

    /**
     *
     * @Description 查询产品多仓比对列表数据
     * @return ResultInfo<BaseTable < List < SkuWarehouseCompareVo>>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/11/24 14:08
     */
    @PostMapping("querySkuWarehouseCompare")
    @ResponseBody
    @ApiOperation("查询产品多仓比对列表数据")
    public ResultInfo<BaseTable<List<SkuWarehouseCompareVo>>> querySkuWarehouseCompareList(@RequestBody SkuWarehouseCompareConditionVo condition)
        throws Exception
    {
        //参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getSkuCode());

        return ResultInfo.success(transferPlanService.querySkuWarehouseCompareList(condition));
    }

    /**
     *
     * @Description 查询库存推演数据
     * @return ResultInfo<BaseTable < List < InventoryInferenceVo>>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/12/01 09:48
     */
    @PostMapping("queryInventoryInference")
    @ResponseBody
    @ApiOperation("查询库存推演数据")
    public ResultInfo<BaseTable<List<InventoryInferenceVo>>> queryInventoryInference(@RequestBody InventoryInferenceConditionVo condition) throws Exception
    {
        //参数校验
        ValidateUtil.checkIsNotEmpty(condition);
        ValidateUtil.checkIsNotEmpty(condition.getInferenceType());

        return ResultInfo.success(transferPlanService.queryInventoryInference(condition));
    }

    /**
     *
     * @Description 编辑库存推演数据
     * @return ResultInfo<BaseTable < List < EditInventoryInferenceVo>>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/12/19 10:20
     */
    @PostMapping("editInventoryInference")
    @ResponseBody
    @ApiOperation("编辑库存推演数据")
    public ResultInfo<BaseTable<List<EditInventoryInferenceVo>>> editInventoryInference(@RequestBody Object condition) throws Exception
    {
        //参数校验
        ValidateUtil.checkIsNotEmpty(condition);

        return ResultInfo.success(transferPlanService.editInventoryInference(condition));
    }

    /**
     *
     * @Description 查询下拉框数据
     * @return ResultInfo<Object>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/12/01 09:48
     */
    @PostMapping("queryComboBoxData")
    @ResponseBody
    @ApiOperation("查询下拉框数据")
    public ResultInfo<?> queryComboBoxData() throws Exception
    {
        return ResultInfo.success(transferPlanService.queryComboBoxData());
    }

    /**
     *
     * @Description 保存计划调拨数据
     * @return ResultInfo<Object>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/12/19 11:48
     */
    @PostMapping("saveTransferPlanData")
    @ResponseBody
    @ApiOperation("保存计划调拨数据")
    public ResultInfo<?> saveTransferPlanData(@RequestBody List<SaveTransferPlanVo> saveTransferPlanVos) throws Exception
    {
        //校验数据
        ValidateUtil.checkIsNotEmpty(saveTransferPlanVos);

        transferPlanService.saveTransferPlanData(saveTransferPlanVos);
        return ResultInfo.success();
    }


}
