package cn.aliyun.ryytn.modules.distribution.service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.modules.demand.dataqdao.DataqAbcTypeDao;
import cn.aliyun.ryytn.modules.demand.entity.dto.AbcTypeDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.aliyun.brain.dataindustry.common.enums.TaskStatus;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqTask;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.mq.MqFactory;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.demand.api.WarehouseDemandPlanService;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import cn.aliyun.ryytn.modules.distribution.api.AiplanTaskService;
import cn.aliyun.ryytn.modules.distribution.api.FreightPlanService;
import cn.aliyun.ryytn.modules.distribution.dao.DailyWarehouseDemandDao;
import cn.aliyun.ryytn.modules.distribution.dao.WarehouseRuleDao;
import cn.aliyun.ryytn.modules.distribution.dataqdao.DataqFreightPlanDao;
import cn.aliyun.ryytn.modules.distribution.dataqdao.DataqInventoryDao;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryInferenceDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.StockFluctuateDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.StockValue;
import cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseStockAnalyDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseAiplanDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InferenceValue;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo;
import cn.aliyun.ryytn.modules.system.dataqdao.DataqSkuDao;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWarehouseListRspVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 调拨计划接口实现类
 * <AUTHOR>
 * @date 2023/12/26 14:15
 */
@Slf4j
@Service
public class FreightPlanServiceImpl implements FreightPlanService
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private DataqFreightPlanDao dataqFreightPlanDao;

    @Autowired
    private DailyWarehouseDemandDao dailyWarehouseDemandDao;

    @Autowired
    private DataqInventoryDao dataqInventoryDao;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private WarehouseDemandPlanService warehouseDemandPlanService;

    @Autowired
    private AiplanTaskService aiplanTaskService;

    @Autowired
    private WarehouseRuleDao warehouseRuleDao;

    @Autowired
    private DataqSkuDao dataqSkuDao;

    @Autowired
    private DataqAbcTypeDao dataqAbcTypeDao;

    private static final Double ZERO = 0d;

    /**
     *
     * @Description 查询调拨计划列表
     * @param freightPlanDto
     * @return List<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    @Override
    public List<FreightPlanDto> queryFreightPlanList(FreightPlanDto freightPlanDto) throws Exception
    {
        // fix: 1716 产品李伟亮要求直接查询渠道需求计划，可以没有版本数据，但是只要有计划，就要有后续下游数据的计划
//        List<FreightPlanDto> result = dataqFreightPlanDao.queryFreightPlanList(freightPlanDto);
        JSONArray jsonArray = warehouseDemandPlanService.queryWarehouseDemandPlanList();
        List<FreightPlanDto> result = Collections.EMPTY_LIST;
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            result = jsonArray.toJavaList(FreightPlanDto.class);
        }
        return result;
    }

    /**
     *
     * @Description 查询调拨计划版本列表
     * @param freightPlanDto
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月26日 19:50
     */
    @Override
    public List<String> queryFreightPlanVersionList(FreightPlanDto freightPlanDto) throws Exception
    {
        List<String> result = dataqFreightPlanDao.queryFreightPlanVersionList(freightPlanDto);
        return result;
    }

    /**
     *
     * @Description 查询调拨计划动态表头
     * @param FreightPlanDto
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:16
     */
    @Override
    public List<String> queryFreightPlanHeadList(FreightPlanDto freightPlanDto) throws Exception
    {
        List<String> dateList = dataqFreightPlanDao.queryFreightPlanHeadList(freightPlanDto);
        if (CollectionUtils.isEmpty(dateList))
        {
            return null;
        }
        List<String> result = dateList.stream().map(item -> StringUtils.replace(StringUtils.substring(item, 5, 10), StringUtils.DATE_SEPARATOR,
            StringUtils.SLASH_SEPARATOR)).collect(Collectors.toList());
        return result;
    }

    /**
     *
     * @Description 查询调拨计划表头下拉列表
     * @param FreightPlanDto
     * @return List<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月23日 16:15
     */
    @Override
    public List<FreightPlanDto> queryFreightPlanHeadSelect(FreightPlanDto freightPlanDto)
        throws Exception
    {
        GroupColumnEnum groupColumnEnum = freightPlanDto.getGroupColumnList().get(0);
        String groupColumn = groupColumnEnum.getColumnName();
        String sortColumn = groupColumnEnum.getSortColumn().getColumnName();
        freightPlanDto.setGroupColumn(groupColumn);
        freightPlanDto.setSortColumn(sortColumn);

        List<FreightPlanDto> dataList = dataqFreightPlanDao.queryFreightPlanHeadSelect(freightPlanDto);

        return dataList;
    }

    /**
     *
     * @Description 查询调拨计划分组聚合数据列表
     * @param FreightPlanDto
     * @return List<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @Override
    public List<FreightPlanDto> queryFreightPlanListGroupBy(FreightPlanDto freightPlanDto)
        throws Exception
    {
        // 分组查询
        List<GroupColumnEnum> groupColumnEnumList = freightPlanDto.getGroupColumnList();
        List<FreightPlanDto> dataList = new ArrayList<>();
        StringBuilder groupColumnSB = new StringBuilder();
        StringBuilder sortColumnSB = new StringBuilder();
        for (GroupColumnEnum groupColumnEnum : groupColumnEnumList)
        {
            String groupColumn = groupColumnSB.append(groupColumnEnum.getColumnName()).toString();
            String sortColumn = sortColumnSB.append(groupColumnEnum.getSortColumn().getColumnName()).toString();
            freightPlanDto.setGroupColumn(groupColumn);
            freightPlanDto.setSortColumn(sortColumn);
            List<FreightPlanDto> list = dataqFreightPlanDao.queryFreightPlanGroupList(freightPlanDto);
            if (CollectionUtils.isNotEmpty(list))
            {
                dataList.addAll(list);
            }

            groupColumnSB.append(StringUtils.COMMA_SEPARATOR);
            sortColumnSB.append(StringUtils.COMMA_SEPARATOR);
        }
        if (CollectionUtils.isEmpty(dataList))
        {
            return dataList;
        }

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (FreightPlanDto data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key = StringUtils.replace(StringUtils.substring(planValue.getPlanDate(), 5, 10), StringUtils.DATE_SEPARATOR,
                    StringUtils.SLASH_SEPARATOR);
                data.getDataMap().put(key, planValue);
            }
            data.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 分页查询调拨计划明细数据列表
     * @param condition
     * @return PageInfo<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月20日 14:28
     */
    @Override
    public PageInfo<FreightPlanDto> queryFreightPlanDataPage(PageCondition<FreightPlanDto> condition) throws Exception
    {
        int pageNum = condition.getPageNum();
        int pageSize = condition.getPageSize();
        FreightPlanDto freightPlanDto = condition.getCondition();

        // dataq接口效率过低，修改为直接读数据库
        // 由于直接分组聚合查询速度过慢，先分页查询唯一标识的业务字段，再分组查询动态时间数据字段
        PageHelper.startPage(pageNum, pageSize);
        List<FreightPlanDto> dataList = dataqFreightPlanDao.queryFreightPlanDataJsonList(freightPlanDto);

        // 获取缓存逻辑仓物理仓映射关系缓存
//        Map<String, Object> warehouseLogicToPhysicMap = redisUtils.hmget(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY);

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (FreightPlanDto data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key = StringUtils.replace(StringUtils.substring(planValue.getPlanDate(), 5, 10), StringUtils.DATE_SEPARATOR,
                    StringUtils.SLASH_SEPARATOR);
                data.getDataMap().put(key, planValue);
            }
            data.setData(null);
        }

        PageInfo<FreightPlanDto> result = new PageInfo<>(dataList);
        return result;
    }

    /**
     *
     * @Description 查询调拨计划日分仓需求列表
     * @param freightPlanDto
     * @return List<DailyWarehouseDemandRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 19:47
     */
    @Override
    public List<DailyWarehouseDemandRspVo> queryFreightPlanDailyWarehouseDemandList(FreightPlanDto freightPlanDto) throws Exception
    {
        // 逻辑仓切换物理仓
//        transWarehouseLogicToPhysic(freightPlanDto);

        FreightPlanDto freightPlanVersion = dataqFreightPlanDao.queryFreightPlanVersion(freightPlanDto);
        if (Objects.isNull(freightPlanVersion))
        {
            return Collections.EMPTY_LIST;
        }

        QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo = new QueryDailyWarehouseDemandListReqVo();
        queryDailyWarehouseDemandListReqVo.setDemandPlanCode(freightPlanDto.getDemandPlanCode());
        queryDailyWarehouseDemandListReqVo.setDemandPlanVersion(StringUtils.substringBefore(freightPlanVersion.getVersionId(), StringUtils.COMMA_SEPARATOR));
        // 如果多仓比对或者RDC推演指定产品，需要将生产编码转换为多个销售编码
        if (StringUtils.isNotBlank(freightPlanDto.getItemId()))
        {
            List<String> skuCodeList = dataqSkuDao.querySaleSkuByProductionSku(freightPlanDto.getItemId());
            if (CollectionUtils.isNotEmpty(skuCodeList))
            {
                queryDailyWarehouseDemandListReqVo.setSkuCodes(skuCodeList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
            }
        }
        queryDailyWarehouseDemandListReqVo.setWarehouseCode(
            StringUtils.isNotEmpty(freightPlanDto.getStartPhysicalPointId()) ? freightPlanDto.getStartPhysicalPointId() :
                freightPlanDto.getEndPhysicalPointId());
        queryDailyWarehouseDemandListReqVo.setWarehouseCodes(
            StringUtils.isNotEmpty(freightPlanDto.getStartPhysicalPointIds()) ? freightPlanDto.getStartPhysicalPointIds() :
                freightPlanDto.getEndPhysicalPointIds());

        String groupColumn =
            freightPlanDto.getGroupColumnList().stream().map(GroupColumnEnum::getColumnName).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        String sortColumn =
            freightPlanDto.getGroupColumnList().stream().map(item -> item.getSortColumn().getColumnName())
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        queryDailyWarehouseDemandListReqVo.setGroupColumn(groupColumn);
        queryDailyWarehouseDemandListReqVo.setSortColumn(sortColumn);
        queryDailyWarehouseDemandListReqVo.setTableSuffix(StringUtils.substring(queryDailyWarehouseDemandListReqVo.getDemandPlanVersion(), 2, 8));
        List<DailyWarehouseDemandRspVo> dataList = dailyWarehouseDemandDao.queryDailyWarehouseDemandListGroupBy(queryDailyWarehouseDemandListReqVo);
        for (DailyWarehouseDemandRspVo data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key = DateUtils.formatTime(DateUtils.parseDate(planValue.getPlanDate(), DateUtils.YMD), DateUtils.MD_SLASH);
                data.getDataMap().put(key, planValue);
            }
            data.setData(null);
        }
        return dataList;
    }

    /**
     *
     * @Description 查询调拨计划日分仓调拨需求列表
     * @param freightPlanDto
     * @return List<DailyWarehouseAiplanDemandRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 19:47
     */
    @Override
    public List<DailyWarehouseAiplanDemandRspVo> queryFreightPlanDailyWarehouseAiPlanList(FreightPlanDto freightPlanDto) throws Exception
    {
        // 逻辑仓切换物理仓
//        transWarehouseLogicToPhysic(freightPlanDto);

        FreightPlanDto freightPlanVersion = dataqFreightPlanDao.queryFreightPlanVersion(freightPlanDto);
        if (Objects.isNull(freightPlanVersion))
        {
            return Collections.EMPTY_LIST;
        }

        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto = new DailyWarehouseAiplanDemandDto();
        dailyWarehouseAiplanDemandDto.setDemandPlanCode(freightPlanDto.getDemandPlanCode());
        dailyWarehouseAiplanDemandDto.setDemandPlanVersion(
            StringUtils.substringBefore(freightPlanVersion.getVersionId(), StringUtils.COMMA_SEPARATOR));
        dailyWarehouseAiplanDemandDto.setAiplanDemandVersion(
            StringUtils.substringAfter(freightPlanVersion.getVersionId(), StringUtils.COMMA_SEPARATOR));
        // 如果多仓比对或者RDC推演指定产品，需要将生产编码转换为多个销售编码
        if (StringUtils.isNotBlank(freightPlanDto.getItemId()))
        {
            List<String> skuCodeList = dataqSkuDao.querySaleSkuByProductionSku(freightPlanDto.getItemId());
            if (CollectionUtils.isNotEmpty(skuCodeList))
            {
                dailyWarehouseAiplanDemandDto.setSkuCodes(skuCodeList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
            }
        }
        dailyWarehouseAiplanDemandDto.setWarehouseCode(freightPlanDto.getCdcFlag() ? freightPlanDto.getStartPhysicalPointId() :
            freightPlanDto.getEndPhysicalPointId());
        dailyWarehouseAiplanDemandDto.setWarehouseCodes(freightPlanDto.getCdcFlag() ? freightPlanDto.getStartPhysicalPointIds() :
            freightPlanDto.getEndPhysicalPointIds());
        dailyWarehouseAiplanDemandDto.setValidityPeriod(
            freightPlanDto.getCdcFlag() ? freightPlanDto.getDepartureValidName() : freightPlanDto.getArrivalValidName());
        dailyWarehouseAiplanDemandDto.setValidityPeriods(
            freightPlanDto.getCdcFlag() ? freightPlanDto.getDepartureValidNames() : freightPlanDto.getArrivalValidNames());

        String groupColumn =
            freightPlanDto.getGroupColumnList().stream().map(GroupColumnEnum::getColumnName).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        String sortColumn =
            freightPlanDto.getGroupColumnList().stream().map(item -> item.getSortColumn().getColumnName())
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        dailyWarehouseAiplanDemandDto.setGroupColumn(groupColumn);
        dailyWarehouseAiplanDemandDto.setSortColumn(sortColumn);
        dailyWarehouseAiplanDemandDto.setTableSuffix(StringUtils.substring(dailyWarehouseAiplanDemandDto.getDemandPlanVersion(), 2, 8));
        List<DailyWarehouseAiplanDemandRspVo> dataList =
            dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandListGroupBy(dailyWarehouseAiplanDemandDto);
        for (DailyWarehouseAiplanDemandRspVo data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key = DateUtils.formatTime(DateUtils.parseDate(planValue.getPlanDate(), DateUtils.YMD), DateUtils.MD_SLASH);
                data.getDataMap().put(key, planValue);
            }
            data.setData(null);
        }
        return dataList;
    }

    /**
     *
     * @Description 查询调拨计划指定分组逻辑的建议合计数量
     * @param FreightPlanDto
     * @return List<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @Override
    public List<FreightPlanDto> queryAdvFreightPlanListGroupBySum(FreightPlanDto freightPlanDto) throws Exception
    {
        String groupColumn =
            freightPlanDto.getGroupColumnList().stream().map(GroupColumnEnum::getColumnName).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        String sortColumn =
            freightPlanDto.getGroupColumnList().stream().map(item -> item.getSortColumn().getColumnName())
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        freightPlanDto.setGroupColumn(groupColumn);
        freightPlanDto.setSortColumn(sortColumn);

        List<FreightPlanDto> dataList = dataqFreightPlanDao.queryAdvFreightPlanGroupList(freightPlanDto);
        if (CollectionUtils.isEmpty(dataList))
        {
            return dataList;
        }

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        String defaultProductionDate = "1970-01-01";
        for (FreightPlanDto data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key = StringUtils.replace(StringUtils.substring(planValue.getPlanDate(), 5, 10), StringUtils.DATE_SEPARATOR,
                    StringUtils.SLASH_SEPARATOR);
                data.getDataMap().put(key, planValue);
            }
            data.setData(null);
            // 标识该记录是否可以修改
            data.setModifyFlag(false);
            // 防止null在controller排序空指针
            data.setProductionDate(defaultProductionDate);
        }

        return dataList;
    }

    /**
     *
     * @Description 查询实际保存调拨计划指定分组逻辑的合计数量
     * @param FreightPlanDto
     * @return List<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @Override
    public List<FreightPlanDto> queryActFreightPlanListGroupBySum(FreightPlanDto freightPlanDto) throws Exception
    {
        String groupColumn =
            freightPlanDto.getGroupColumnList().stream().map(GroupColumnEnum::getColumnName).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        String sortColumn =
            freightPlanDto.getGroupColumnList().stream().map(item -> item.getSortColumn().getColumnName())
                .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR));
        freightPlanDto.setGroupColumn(groupColumn);
        freightPlanDto.setSortColumn(sortColumn);

        List<FreightPlanDto> dataList = dataqFreightPlanDao.queryFreightPlanGroupList(freightPlanDto);

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (FreightPlanDto data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key = StringUtils.replace(StringUtils.substring(planValue.getPlanDate(), 5, 10), StringUtils.DATE_SEPARATOR,
                    StringUtils.SLASH_SEPARATOR);
                data.getDataMap().put(key, planValue);
            }
            data.setData(null);
            data.setModifyFlag(true);
        }

        return dataList;
    }

    /**
     *
     * @Description 查询调拨计划多仓比对列表
     * @param FreightPlanDto
     * @return List<FreightPlanDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月20日 14:12
     */
    @Override
    public List<FreightPlanDto> queryFreightPlanWarehouseContrastList(FreightPlanDto freightPlanDto) throws Exception
    {
        List<FreightPlanDto> dataList = dataqFreightPlanDao.queryFreightPlanWarehouseContrastList(freightPlanDto);

        // 解析dataq销售目标响应，并转换动态字段格式封装为固定字段
        for (FreightPlanDto data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key = StringUtils.replace(StringUtils.substring(planValue.getPlanDate(), 5, 10), StringUtils.DATE_SEPARATOR,
                    StringUtils.SLASH_SEPARATOR);
                data.getDataMap().put(key, planValue);
            }
            data.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 询指定产品/仓库的库存数据列表
     * @param freightPlanDto
     * @return List<InventoryStrategyVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月02日 19:45
     */
    @Override
    public List<InventoryStrategyVo> queryFreightPlanInventoryList(FreightPlanDto freightPlanDto) throws Exception
    {
        // 库存策略数据仓库都是逻辑仓，需要转换物理仓
//        if (StringUtils.isNotBlank(freightPlanDto.getWarehouseCodes()))
//        {
//            List<String> logicWarehouseCodeList = Arrays.asList(freightPlanDto.getWarehouseCodes().split(StringUtils.COMMA_SEPARATOR));
//            List<Object> objectList = redisUtils.hmultiGet(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY, logicWarehouseCodeList);
//            if (CollectionUtils.isNotEmpty(objectList))
//            {
//                freightPlanDto.setWarehouseCodes(objectList.stream().map(item -> {
//                    QueryWarehouseListRspVo warehouse = (QueryWarehouseListRspVo) item;
//                    return ((QueryWarehouseListRspVo) item).getBizWarehouseCode();
//                }).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
//            }
//        }

        List<String> nowDateList = dataqFreightPlanDao.queryFreightPlanHeadList(freightPlanDto);
        if (CollectionUtils.isEmpty(nowDateList))
        {
            return Collections.EMPTY_LIST;
        }

        InventoryStrategyConditionVo inventoryStrategyConditionVo = new InventoryStrategyConditionVo();
        inventoryStrategyConditionVo.setNowDateList(nowDateList);
        inventoryStrategyConditionVo.setSkuCodes(freightPlanDto.getSkuCodes());
        inventoryStrategyConditionVo.setWarehouseCodes(freightPlanDto.getWarehouseCodes());

        List<InventoryStrategyVo> dataList = dataqInventoryDao.queryInventoryList(inventoryStrategyConditionVo);
        if (CollectionUtils.isEmpty(dataList))
        {
            return Collections.EMPTY_LIST;
        }

        for (InventoryStrategyVo inventory : dataList)
        {
            if (StringUtils.isBlank(inventory.getData()))
            {
                continue;
            }
            JSONArray jsonArray = JSON.parseArray(inventory.getData());
            List<InventoryStrategyVo> dataMapList = jsonArray.toJavaList(InventoryStrategyVo.class);
            for (InventoryStrategyVo data : dataMapList)
            {
                String key = StringUtils.replace(StringUtils.substring(data.getNowDate(), 5, 10), StringUtils.DATE_SEPARATOR,
                    StringUtils.SLASH_SEPARATOR);
                inventory.getDataMap().put(key, data);
            }
            inventory.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 保存调拨计划数据
     * 需求变更，展示数据粒度从生产批次修改为效期，编辑保存功能粒度依然为生产频次
     * 上线时间过紧来不及从数据加工开始重新实现，接口当前废弃不可用
     * @param freightPlanDtoList
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月04日 13:54
     */
    @Deprecated
    @Override
    public void saveFreightPlanData(List<FreightPlanDto> freightPlanDtoList) throws Exception
    {
        dataqFreightPlanDao.saveFreightPlanData(freightPlanDtoList);
    }

    /**
     *
     * @Description 查询生产调入列表
     * @param freightPlanDto
     * @return List<WarehouseStockAnalyDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月05日 17:20
     */
    @Override
    public List<WarehouseStockAnalyDto> queryProductionFreightList(FreightPlanDto freightPlanDto) throws Exception
    {
        freightPlanDto.setCdcFlag(true);
        List<String> dateList = dataqFreightPlanDao.queryFreightPlanHeadList(freightPlanDto);
        if (CollectionUtils.isEmpty(dateList))
        {
            return Collections.EMPTY_LIST;
        }
        dateList = dateList.stream().map(item -> StringUtils.replace(item, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());

        // 逻辑仓转物理仓
//        QueryWarehouseListRspVo queryWarehouseListRspVo =
//            (QueryWarehouseListRspVo) redisUtils.hget(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY, freightPlanDto.getStartPointId());
//        String warehouseCode = queryWarehouseListRspVo.getBizWarehouseCode();
        String warehouseCode = freightPlanDto.getStartPhysicalPointId();

        WarehouseStockAnalyDto warehouseStockAnalyDto = new WarehouseStockAnalyDto();
        warehouseStockAnalyDto.setDateList(dateList);
        warehouseStockAnalyDto.setSkuCode(freightPlanDto.getItemId());
        warehouseStockAnalyDto.setWarehouseCode(warehouseCode);

        List<WarehouseStockAnalyDto> dataList = dataqFreightPlanDao.queryProductionFreightList(warehouseStockAnalyDto);
        for (WarehouseStockAnalyDto data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key = DateUtils.formatTime(DateUtils.parseDate(planValue.getPlanDate(), DateUtils.YMD), DateUtils.MD_SLASH);
                data.getDataMap().put(key, planValue);
            }
            data.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 查询在途库存列表
     * @param freightPlanDto
     * @return List<WarehouseStockAnalyDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月05日 17:20
     */
    @Override
    public List<WarehouseStockAnalyDto> queryOnTransInventoryList(FreightPlanDto freightPlanDto) throws Exception
    {
        freightPlanDto.setCdcFlag(false);
        List<String> dateList = dataqFreightPlanDao.queryFreightPlanHeadList(freightPlanDto);
        if (CollectionUtils.isEmpty(dateList))
        {
            return Collections.EMPTY_LIST;
        }
        dateList = dateList.stream().map(item -> StringUtils.replace(item, StringUtils.DATE_SEPARATOR, StringUtils.EMPTY)).collect(Collectors.toList());


        WarehouseStockAnalyDto warehouseStockAnalyDto = new WarehouseStockAnalyDto();
        warehouseStockAnalyDto.setDateList(dateList);
        warehouseStockAnalyDto.setSkuCode(freightPlanDto.getItemId());
        warehouseStockAnalyDto.setValidName(freightPlanDto.getArrivalValidName());
        warehouseStockAnalyDto.setWarehouseCode(freightPlanDto.getEndPhysicalPointId());
//        if (StringUtils.isNotBlank(freightPlanDto.getEndPointId()))
//        {
//            // 逻辑仓转物理仓
//            QueryWarehouseListRspVo queryWarehouseListRspVo =
//                (QueryWarehouseListRspVo) redisUtils.hget(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY, freightPlanDto.getEndPointId());
//            String warehouseCode = queryWarehouseListRspVo.getBizWarehouseCode();
//            warehouseStockAnalyDto.setWarehouseCode(warehouseCode);
//        }
        if (CollectionUtils.isNotEmpty(freightPlanDto.getGroupColumnList()))
        {
            warehouseStockAnalyDto.setGroupColumn(
                freightPlanDto.getGroupColumnList().stream().map(GroupColumnEnum::getColumnName).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
        }

        List<WarehouseStockAnalyDto> dataList = dataqFreightPlanDao.queryOnTransInventoryList(warehouseStockAnalyDto);
        for (WarehouseStockAnalyDto data : dataList)
        {
            List<PlanValue> planValueList = JSON.parseArray(data.getData(), PlanValue.class);
            // 每条数据都按照动态字段列表的顺序，动态获取字段value
            for (PlanValue planValue : planValueList)
            {
                String key = DateUtils.formatTime(DateUtils.parseDate(planValue.getPlanDate(), DateUtils.YMD), DateUtils.MD_SLASH);
                data.getDataMap().put(key, planValue);
            }
            data.setData(null);
        }
        return dataList;
    }


    /**
     *
     * @Description 查询库存列表
     * @param stockFluctuateDto
     * @return List<StockFluctuateDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月19日 13:57
     */
    @Override
    public List<StockFluctuateDto> queryStockFluctuateList(StockFluctuateDto stockFluctuateDto) throws Exception
    {
        List<StockFluctuateDto> dataList = dataqFreightPlanDao.queryStockFluctuateList(stockFluctuateDto);
        if (CollectionUtils.isEmpty(dataList))
        {
            return dataList;
        }

        for (StockFluctuateDto stockFluctuate : dataList)
        {
            List<StockValue> stockValueList = JSON.parseArray(stockFluctuate.getData(), StockValue.class);
            for (StockValue stockValue : stockValueList)
            {
                String key = StringUtils.replace(StringUtils.substring(stockValue.getStockDate(), 5, 10), StringUtils.DATE_SEPARATOR,
                    StringUtils.SLASH_SEPARATOR);
                stockFluctuate.getDataMap().put(key, stockValue);
            }
            stockFluctuate.setData(null);
        }

        return dataList;
    }

    /**
     *
     * @Description 查询库存推演数据
     * @param inventoryInferenceDto
     * @return InventoryInferenceDto
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月08日 10:56
     */
    @Deprecated
    @Override
    public InventoryInferenceDto queryInventoryInference(InventoryInferenceDto inventoryInferenceDto) throws Exception
    {
        // 逻辑仓转物理仓
        QueryWarehouseListRspVo warehouse =
            (QueryWarehouseListRspVo) redisUtils.hget(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY, inventoryInferenceDto.getWarehouseCode());
        if (Objects.nonNull(warehouse))
        {
            inventoryInferenceDto.setWarehouseCode(warehouse.getBizWarehouseCode());
        }

        // 查询库存推演
        List<InventoryInferenceDto> inventoryInferenceList = dataqFreightPlanDao.queryInventoryInferenceList(inventoryInferenceDto);
        if (CollectionUtils.isEmpty(inventoryInferenceList))
        {
            return null;
        }
        inventoryInferenceDto = inventoryInferenceList.get(0);

        // 查询平均调拨数量
        List<InventoryInferenceDto> avgAllocationList = dataqFreightPlanDao.queryWarehouseAvgAllocation(inventoryInferenceDto);
        Map<InventoryInferenceDto, InventoryInferenceDto> avgAllocationMap = Collections.EMPTY_MAP;
        if (CollectionUtils.isNotEmpty(avgAllocationList))
        {
            avgAllocationMap = avgAllocationList.stream().collect(Collectors.toMap(Function.identity(), Function.identity(), (key1, key2) -> key2));
        }

        Double avgAllocation =
            Objects.isNull(avgAllocationMap.get(inventoryInferenceDto)) ? 0d : avgAllocationMap.get(inventoryInferenceDto).getAvgAllocation();
        inventoryInferenceDto.setAvgAllocation(Objects.isNull(avgAllocation) ? 0d : avgAllocation);

        // 处理库存推演期初值和期末值
        // 数据库视图是写死的列，Java Bean也只能写死字段，此处只能写死封装代码
        Date startDate = DateUtils.parseDate(inventoryInferenceDto.getDs(), DateUtils.YMD_DASH);
        String oiRegex = "^oi\\d+$";
        String eiRegex = "^ei\\d+$";
        String oi = "oi";
        String ei = "ei";
        Field[] fields = InventoryInferenceDto.class.getDeclaredFields();
        BigDecimal avg = new BigDecimal(inventoryInferenceDto.getAvgAllocation());
        for (Field field : fields)
        {
            field.setAccessible(true);
            if (field.getName().matches(oiRegex))
            {
                Integer index = Integer.valueOf(StringUtils.substringAfter(field.getName(), oi));
                Date planDate = DateUtils.addDays(startDate, index);
                String key = DateUtils.formatTime(planDate, DateUtils.MD_SLASH);
                if (inventoryInferenceDto.getDataMap().containsKey(key))
                {
                    inventoryInferenceDto.getDataMap().get(key).setOiValue((Double) field.get(inventoryInferenceDto));
                }
                else
                {
                    InferenceValue inferenceValue = new InferenceValue();
                    inferenceValue.setPlanDate(DateUtils.formatTime(planDate, DateUtils.YMD_DASH));
                    inferenceValue.setOiValue((Double) field.get(inventoryInferenceDto));
                    inventoryInferenceDto.getDataMap().put(key, inferenceValue);
                }
            }
            if (field.getName().matches(eiRegex))
            {
                Integer index = Integer.valueOf(StringUtils.substringAfter(field.getName(), ei));
                Date planDate = DateUtils.addDays(startDate, index);
                String key = DateUtils.formatTime(planDate, DateUtils.MD_SLASH);
                Double eiValue = (Double) field.get(inventoryInferenceDto);
                Double availDay = eiValue;
                if (!ZERO.equals(avg.doubleValue()))
                {
                    availDay = new BigDecimal(eiValue).divide(avg, 0, RoundingMode.DOWN).doubleValue();
                }
                if (inventoryInferenceDto.getDataMap().containsKey(key))
                {
                    inventoryInferenceDto.getDataMap().get(key).setEiValue(eiValue);
                    inventoryInferenceDto.getDataMap().get(key).setAvailDay(availDay);
                }
                else
                {
                    InferenceValue inferenceValue = new InferenceValue();
                    inferenceValue.setPlanDate(DateUtils.formatTime(planDate, DateUtils.YMD_DASH));
                    inferenceValue.setEiValue(eiValue);
                    inferenceValue.setAvailDay(availDay);
                    inventoryInferenceDto.getDataMap().put(key, inferenceValue);
                }
            }
        }

        return inventoryInferenceDto;
    }

    /**
     *
     * @Description 查询多仓库比对库存推演数据
     * @param inventoryInferenceDto
     * @return List<InventoryInferenceDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月08日 14:07
     */
    @Deprecated
    @Override
    public List<InventoryInferenceDto> queryInventoryInferenceWarehouseContrast(InventoryInferenceDto inventoryInferenceDto) throws Exception
    {
        // 查询库存推演
        List<InventoryInferenceDto> inventoryInferenceList = dataqFreightPlanDao.queryInventoryInferenceList(inventoryInferenceDto);
        if (CollectionUtils.isEmpty(inventoryInferenceList))
        {
            return Collections.EMPTY_LIST;
        }

        // 查询平均调拨数量
        List<InventoryInferenceDto> avgAllocationList = dataqFreightPlanDao.queryWarehouseAvgAllocation(inventoryInferenceDto);
        Map<InventoryInferenceDto, InventoryInferenceDto> avgAllocationMap = Collections.EMPTY_MAP;
        if (CollectionUtils.isNotEmpty(avgAllocationList))
        {
            avgAllocationMap = avgAllocationList.stream().collect(Collectors.toMap(Function.identity(), Function.identity(), (key1, key2) -> key2));
        }

        // 处理库存推演期初值和期末值
        // 数据库视图是写死的列，Java Bean也只能写死字段，此处只能写死封装代码
        String oiRegex = "^oi\\d+$";
        String eiRegex = "^ei\\d+$";
        String oi = "oi";
        String ei = "ei";
        Field[] fields = InventoryInferenceDto.class.getDeclaredFields();
        for (InventoryInferenceDto inventoryInference : inventoryInferenceList)
        {
            Double avgAllocation =
                Objects.isNull(avgAllocationMap.get(inventoryInferenceDto)) ? 0d : avgAllocationMap.get(inventoryInferenceDto).getAvgAllocation();
            inventoryInferenceDto.setAvgAllocation(Objects.isNull(avgAllocation) ? 0d : avgAllocation);

            BigDecimal avg = new BigDecimal(inventoryInference.getAvgAllocation());
            Date startDate = DateUtils.parseDate(inventoryInference.getDs(), DateUtils.YMD_DASH);
            for (Field field : fields)
            {
                field.setAccessible(true);
                if (field.getName().matches(oiRegex))
                {
                    Integer index = Integer.valueOf(StringUtils.substringAfter(field.getName(), oi));
                    Date planDate = DateUtils.addDays(startDate, index);
                    String key = DateUtils.formatTime(planDate, DateUtils.MD_SLASH);
                    if (inventoryInference.getDataMap().containsKey(key))
                    {
                        inventoryInference.getDataMap().get(key).setOiValue((Double) field.get(inventoryInference));
                    }
                    else
                    {
                        InferenceValue inferenceValue = new InferenceValue();
                        inferenceValue.setPlanDate(DateUtils.formatTime(planDate, DateUtils.YMD_DASH));
                        inferenceValue.setOiValue((Double) field.get(inventoryInference));
                        inventoryInference.getDataMap().put(key, inferenceValue);
                    }
                }
                if (field.getName().matches(eiRegex))
                {
                    Integer index = Integer.valueOf(StringUtils.substringAfter(field.getName(), ei));
                    Date planDate = DateUtils.addDays(startDate, index);
                    String key = DateUtils.formatTime(planDate, DateUtils.MD_SLASH);
                    Double eiValue = (Double) field.get(inventoryInference);
                    Double availDay = eiValue;
                    if (!ZERO.equals(avg.doubleValue()))
                    {
                        availDay = new BigDecimal(eiValue).divide(avg, 0, RoundingMode.DOWN).doubleValue();
                    }
                    if (inventoryInference.getDataMap().containsKey(key))
                    {
                        inventoryInference.getDataMap().get(key).setEiValue(eiValue);
                        inventoryInference.getDataMap().get(key).setAvailDay(availDay);
                    }
                    else
                    {
                        InferenceValue inferenceValue = new InferenceValue();
                        inferenceValue.setPlanDate(DateUtils.formatTime(planDate, DateUtils.YMD_DASH));
                        inferenceValue.setEiValue(eiValue);
                        inferenceValue.setAvailDay(availDay);
                        inventoryInference.getDataMap().put(key, inferenceValue);
                    }
                }
            }
        }

        return inventoryInferenceList;
    }

    /**
     *
     * @Description 多仓比对未来可供应天数
     * @param freightPlanDto
     * @return List<StockFluctuateDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月20日 10:16
     */
    @Override
    public List<StockFluctuateDto> queryFreightDaysWarehouseContrast(FreightPlanDto freightPlanDto) throws Exception
    {
        // 期末库存/(全部日分仓调拨需求合计/总天数)
        // 产品多仓比对，是RDC视角
        freightPlanDto.setCdcFlag(false);

        FreightPlanDto freightPlanVersion = dataqFreightPlanDao.queryFreightPlanVersion(freightPlanDto);
        if (Objects.isNull(freightPlanVersion))
        {
            return Collections.EMPTY_LIST;
        }

        // 查询日分仓调拨需求合计
        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto = new DailyWarehouseAiplanDemandDto();
        dailyWarehouseAiplanDemandDto.setDemandPlanCode(freightPlanDto.getDemandPlanCode());
        dailyWarehouseAiplanDemandDto.setDemandPlanVersion(
            StringUtils.substringBefore(freightPlanVersion.getVersionId(), StringUtils.COMMA_SEPARATOR));
        dailyWarehouseAiplanDemandDto.setAiplanDemandVersion(
            StringUtils.substringAfter(freightPlanVersion.getVersionId(), StringUtils.COMMA_SEPARATOR));
        // 如果多仓比对或者RDC推演指定产品，需要将生产编码转换为多个销售编码
        if (StringUtils.isNotBlank(freightPlanDto.getItemId()))
        {
            List<String> skuCodeList = dataqSkuDao.querySaleSkuByProductionSku(freightPlanDto.getItemId());
            if (CollectionUtils.isNotEmpty(skuCodeList))
            {
                dailyWarehouseAiplanDemandDto.setSkuCodes(skuCodeList.stream().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
            }
        }
        dailyWarehouseAiplanDemandDto.setWarehouseCode(freightPlanDto.getEndPhysicalPointId());
        dailyWarehouseAiplanDemandDto.setTableSuffix(StringUtils.substring(dailyWarehouseAiplanDemandDto.getDemandPlanVersion(), 2, 8));

        List<String> dateList = dailyWarehouseDemandDao.queryDailyWarehouseAiplanDemandHeadList(dailyWarehouseAiplanDemandDto);
        // 总天数
        BigDecimal totalDays = null;
        if (CollectionUtils.isNotEmpty(dateList))
        {
            totalDays = new BigDecimal(dateList.size());
        }
        else
        {
            totalDays = new BigDecimal(1);
        }

        List<DailyWarehouseAiplanDemandDto> dailyWarehouseAiplanDemandList = dailyWarehouseDemandDao.querySumGroupByWarehouse(dailyWarehouseAiplanDemandDto);
        if (CollectionUtils.isEmpty(dailyWarehouseAiplanDemandList))
        {
            return Collections.EMPTY_LIST;
        }

        Map<String, Double> dailyWarehouseAiplanDemandMap = dailyWarehouseAiplanDemandList.stream()
            .collect(Collectors.toMap(DailyWarehouseAiplanDemandDto::getWarehouseCode, DailyWarehouseAiplanDemandDto::getDateValue, (key1, key2) -> key2));

        // 查询期末库存
        StockFluctuateDto stockFluctuateDto = new StockFluctuateDto();
        stockFluctuateDto.setTaskDetailId(freightPlanDto.getTaskDetailId());
        stockFluctuateDto.setItemId(freightPlanDto.getItemId());
        stockFluctuateDto.setPhysicalPointId(freightPlanDto.getEndPhysicalPointId());
        List<StockFluctuateDto> stockFluctuateList = this.queryStockFluctuateList(stockFluctuateDto);

        if (CollectionUtils.isEmpty(stockFluctuateList))
        {
            return Collections.EMPTY_LIST;
        }

        for (StockFluctuateDto stockFluctuate : stockFluctuateList)
        {
            Double dataValue = dailyWarehouseAiplanDemandMap.get(stockFluctuate.getPhysicalPointId());
            if (Objects.isNull(dataValue) || dataValue.equals(0d))
            {
                continue;
            }
            for (Map.Entry<String, StockValue> entry : stockFluctuate.getDataMap().entrySet())
            {
                String key = entry.getKey();
                StockValue stockValue = entry.getValue();
                BigDecimal dailyValue = new BigDecimal(dataValue);
                BigDecimal tailNum = new BigDecimal(Objects.isNull(stockValue.getTailNum()) ? 0d : stockValue.getTailNum());
                BigDecimal avgValue = dailyValue.divide(totalDays, 4, RoundingMode.UP);
                Integer days = tailNum.divide(avgValue, 0, RoundingMode.DOWN).intValue();
                stockValue.setDays(days);
            }
        }

        return stockFluctuateList;
    }

    /**
     *
     * @Description 转换物理仓为逻辑仓
     * 由于阿里算法输出调拨量结果tdm_kcjh_txn_freight_qty_di表仓库字段为逻辑仓
     * 页面需要展示物理仓，所以在页面筛选条件传入业务代码dao之前，需要转换为逻辑仓拼接到sql上
     * @param freightPlanDto
     * <AUTHOR>
     * @date 2024年01月02日 11:10
     */
    private void transWarehousePhysicToLogic(FreightPlanDto freightPlanDto)
    {
        // 出仓编号
        if (StringUtils.isNotEmpty(freightPlanDto.getStartPointId()))
        {
            List<QueryWarehouseListRspVo> logicWarehouseList =
                (List<QueryWarehouseListRspVo>) redisUtils.hget(CommonConstants.WAREHOUSE_PHYSIC_TO_LOGIC_KEY, freightPlanDto.getStartPointId());
            if (CollectionUtils.isNotEmpty(logicWarehouseList))
            {
                freightPlanDto.setStartPointId(null);
                freightPlanDto.setStartPointIds(logicWarehouseList.stream().map(QueryWarehouseListRspVo::getWarehouseCode).distinct()
                    .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
            }
        }
        // 多个出仓编号
        else if (StringUtils.isNotEmpty(freightPlanDto.getStartPointIds()))
        {
            List<String> physicWarehouseCodeList = Arrays.asList(freightPlanDto.getStartPointIds().split(StringUtils.COMMA_SEPARATOR));
            List<Object> objectList = redisUtils.hmultiGet(CommonConstants.WAREHOUSE_PHYSIC_TO_LOGIC_KEY, physicWarehouseCodeList);
            if (CollectionUtils.isNotEmpty(objectList))
            {
                List<String> logicWarehouseCodeList = new ArrayList<>();
                for (Object object : objectList)
                {
                    if (Objects.isNull(object))
                    {
                        continue;
                    }
                    List<QueryWarehouseListRspVo> physicWarehouseList = (List<QueryWarehouseListRspVo>) object;
                    for (QueryWarehouseListRspVo physicWarehouse : physicWarehouseList)
                    {
                        logicWarehouseCodeList.add(physicWarehouse.getWarehouseCode());
                    }
                }
                freightPlanDto.setStartPointIds(logicWarehouseCodeList.stream().distinct().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
            }
        }

        // 入仓编号
        if (StringUtils.isNotEmpty(freightPlanDto.getEndPointId()))
        {
            List<QueryWarehouseListRspVo> logicWarehouseList =
                (List<QueryWarehouseListRspVo>) redisUtils.hget(CommonConstants.WAREHOUSE_PHYSIC_TO_LOGIC_KEY, freightPlanDto.getEndPointId());
            if (CollectionUtils.isNotEmpty(logicWarehouseList))
            {
                freightPlanDto.setEndPointId(null);
                freightPlanDto.setEndPointIds(logicWarehouseList.stream().map(QueryWarehouseListRspVo::getWarehouseCode).distinct()
                    .collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
            }
        }
        // 多个入仓编号
        else if (StringUtils.isNotEmpty(freightPlanDto.getEndPointIds()))
        {
            List<String> physicWarehouseCodeList = Arrays.asList(freightPlanDto.getEndPointIds().split(StringUtils.COMMA_SEPARATOR));
            List<Object> objectList = redisUtils.hmultiGet(CommonConstants.WAREHOUSE_PHYSIC_TO_LOGIC_KEY, physicWarehouseCodeList);
            if (CollectionUtils.isNotEmpty(objectList))
            {
                List<String> logicWarehouseCodeList = new ArrayList<>();
                for (Object object : objectList)
                {
                    if (Objects.isNull(object))
                    {
                        continue;
                    }
                    List<QueryWarehouseListRspVo> physicWarehouseList = (List<QueryWarehouseListRspVo>) object;
                    for (QueryWarehouseListRspVo physicWarehouse : physicWarehouseList)
                    {
                        logicWarehouseCodeList.add(physicWarehouse.getWarehouseCode());
                    }
                }
                freightPlanDto.setEndPointIds(logicWarehouseCodeList.stream().distinct().collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
            }
        }
    }

    /**
     *
     * @Description 逻辑仓转物理仓
     * @param freightPlanDto
     * <AUTHOR>
     * @date 2024年01月02日 13:46
     */
    private void transWarehouseLogicToPhysic(FreightPlanDto freightPlanDto)
    {
        // 逻辑仓转物理仓
        if (StringUtils.isNotEmpty(freightPlanDto.getStartPointId()))
        {
            QueryWarehouseListRspVo warehouse =
                (QueryWarehouseListRspVo) redisUtils.hget(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY, freightPlanDto.getStartPointId());
            if (Objects.isNull(warehouse))
            {
                freightPlanDto.setStartPointId(null);
            }
            else
            {
                freightPlanDto.setStartPointId(warehouse.getBizWarehouseCode());
            }
        }
        if (StringUtils.isNotEmpty(freightPlanDto.getEndPointId()))
        {
            QueryWarehouseListRspVo warehouse =
                (QueryWarehouseListRspVo) redisUtils.hget(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY, freightPlanDto.getEndPointId());
            if (Objects.isNull(warehouse))
            {
                freightPlanDto.setEndPointId(null);
            }
            else
            {
                freightPlanDto.setEndPointId(warehouse.getBizWarehouseCode());
            }
        }
        if (StringUtils.isNotEmpty(freightPlanDto.getStartPointIds()))
        {
            List<String> startPointIds = Arrays.asList(freightPlanDto.getStartPointIds().split(StringUtils.COMMA_SEPARATOR));
            List<Object> objectList = redisUtils.hmultiGet(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY, startPointIds);
            if (CollectionUtils.isEmpty(objectList))
            {
                freightPlanDto.setStartPointIds(null);
            }
            else
            {
                freightPlanDto.setStartPointIds(objectList.stream().map(item -> {
                    QueryWarehouseListRspVo warehouse = (QueryWarehouseListRspVo) item;
                    return ((QueryWarehouseListRspVo) item).getBizWarehouseCode();
                }).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
            }
        }
        if (StringUtils.isNotEmpty(freightPlanDto.getEndPointIds()))
        {
            List<String> endPointIds = Arrays.asList(freightPlanDto.getEndPointIds().split(StringUtils.COMMA_SEPARATOR));
            List<Object> objectList = redisUtils.hmultiGet(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY, endPointIds);
            if (CollectionUtils.isEmpty(objectList))
            {
                freightPlanDto.setEndPointIds(null);
            }
            else
            {
                freightPlanDto.setEndPointIds(objectList.stream().map(item -> {
                    QueryWarehouseListRspVo warehouse = (QueryWarehouseListRspVo) item;
                    return ((QueryWarehouseListRspVo) item).getBizWarehouseCode();
                }).collect(Collectors.joining(StringUtils.COMMA_SEPARATOR)));
            }
        }
    }

    /**
     *
     * @Description 生成调拨计划，接口大概需要执行10分钟
     * @param soWtDemandDto
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月12日 11:38
     */
    @Override
    public void generateFreightPlan(SoWtDemandDto soWtDemandDto) throws Exception
    {
        String lockKey = StringUtils.format(CommonConstants.REDIS_GENERATE_FREIGHT_PLAN_LOCK_KEY, soWtDemandDto.getDemandPlanCode());
        boolean lock = redisUtils.lock(lockKey, 60L);
        if (!lock)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }

        try
        {
            List<SoWtDemandDto> soWtDemandDtoList = dailyWarehouseDemandDao.querySoWtDemandList(soWtDemandDto);
            if (CollectionUtils.isEmpty(soWtDemandDtoList))
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
            }
            SoWtDemandDto soWtDemand = soWtDemandDtoList.get(0);

            // 阿里算法资源有限，不能支持多任务并发，所有算法任务公用一个锁。
            String lockValue = (String) redisUtils.get(CommonConstants.REDIS_ALGO_DISTRIBUTED_LOCK_KEY);

            // 消息队列里有该计划的调拨任务
            List<Object> mqRecordList = redisUtils.lGet(MqFactory.getTopic(CommonConstants.TOPIC_GENERATE_FREIGHTPLAN), 0, -1);
            Set<SoWtDemandDto> soWtDemandSet = mqRecordList.stream().map(item -> (SoWtDemandDto) item).collect(Collectors.toSet());
            if (StringUtils.equals(soWtDemand.getDemandPlanCode(), lockValue) ||
                (CollectionUtils.isNotEmpty(soWtDemandSet) && soWtDemandSet.contains(soWtDemand)))
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
            }
            MqFactory.newProducerService().produce(CommonConstants.TOPIC_GENERATE_FREIGHTPLAN, soWtDemand);
        }
        catch (ServiceException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            log.error("generate freight plan has exception:{}", e);
            throw new ServiceException();
        }
        finally
        {
            redisUtils.unlock(lockKey);
        }
    }

    /**
     *
     * @Description 查询仓容
     * @param inventoryInferenceDto
     * @return Long
     * @throws Exception
     * <AUTHOR>
     * @date 2024年03月19日 17:18
     */
    @Override
    public Long queryWarehouseCapacity(InventoryInferenceDto inventoryInferenceDto) throws Exception
    {
        // 逻辑仓转物理仓
//        QueryWarehouseListRspVo queryWarehouseListRspVo =
//            (QueryWarehouseListRspVo) redisUtils.hget(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY, inventoryInferenceDto.getWarehouseCode());
//        String warehouseCode = queryWarehouseListRspVo.getBizWarehouseCode();
        String warehouseCode = inventoryInferenceDto.getWarehouseCode();

        // 如果产品不为空，则先查询产品仓能力
        Long capacity = null;
        if (StringUtils.isNotEmpty(inventoryInferenceDto.getSkuCode()))
        {
            capacity = warehouseRuleDao.queryCapacityBySku(warehouseCode, inventoryInferenceDto.getSkuCode());

            if (Objects.isNull(capacity))
            {
                SkuDto sku = (SkuDto) redisUtils.get(StringUtils.format(CommonConstants.SKU_CACHE_KEY, inventoryInferenceDto.getSkuCode()));
                capacity = warehouseRuleDao.queryCapacityByCategory(warehouseCode, sku.getLv3CategoryCode());
            }
        }

        if (Objects.isNull(capacity))
        {
            capacity = warehouseRuleDao.queryDefaultCapacity(warehouseCode);
        }

        return capacity;
    }

    /**
     *
     * @Description 查询调拨任务详情
     * @param demandPlanCode
     * @return DataqTask
     * @throws Exception
     * <AUTHOR>
     * @date 2024年04月24日 14:04
     */
    @Override
    public DataqTask queryFreightTaskDetail(String demandPlanCode) throws Exception
    {
        // 阿里算法资源有限，不能支持多任务并发，所有算法任务公用一个锁。
        String lockValue = (String) redisUtils.get(CommonConstants.REDIS_ALGO_DISTRIBUTED_LOCK_KEY);

        // 消息队列里有该计划的调拨任务
        List<Object> mqRecordList = redisUtils.lGet(MqFactory.getTopic(CommonConstants.TOPIC_GENERATE_FREIGHTPLAN), 0, -1);
        Set<String> demandPlanCodeSet = mqRecordList.stream().map(item -> {
            SoWtDemandDto soWtDemandDto = (SoWtDemandDto) item;
            return soWtDemandDto.getDemandPlanCode();
        }).collect(Collectors.toSet());
        DataqTask dataqTask = null;
        if (StringUtils.equals(demandPlanCode, lockValue) || (CollectionUtils.isNotEmpty(demandPlanCodeSet) && demandPlanCodeSet.contains(demandPlanCode)))
        {
            dataqTask = new DataqTask();
            dataqTask.setStatus(TaskStatus.RUNNING.name());
        }

        return dataqTask;
    }

    @Override
    public AbcTypeDto getAbcType(String itemId) {
        AbcTypeDto param = new AbcTypeDto();
        param.setSkuCode(itemId);
        return dataqAbcTypeDao.queryAbcTypeGoupBySkucode(param);
    }
}
