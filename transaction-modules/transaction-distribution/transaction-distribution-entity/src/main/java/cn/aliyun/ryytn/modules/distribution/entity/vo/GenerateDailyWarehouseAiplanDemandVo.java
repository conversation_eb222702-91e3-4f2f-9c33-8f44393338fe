package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 生成日分仓调拨需求请求Vo
 * <AUTHOR>
 * @date 2023/12/20 14:06
 */
@Setter
@Getter
@ToString
@ApiModel("生成日分仓调拨需求请求Vo")
public class GenerateDailyWarehouseAiplanDemandVo implements Serializable
{
    private static final long serialVersionUID = -214873100185585406L;
    @ApiModelProperty("需求计划编码")
    private String demandPlanCode;

    @ApiModelProperty("需求计划版本名称")
    private String demandPlanVersion;

    private String subDemandPlanVersion;

    private String aiplanDemandVersion;

    private String tableSuffix;

    @ApiModelProperty("修改的数据 id,dateValue")
    List<DailyWarehouseDemandDateVo> dailyWarehouseDemandDateVos;
}
