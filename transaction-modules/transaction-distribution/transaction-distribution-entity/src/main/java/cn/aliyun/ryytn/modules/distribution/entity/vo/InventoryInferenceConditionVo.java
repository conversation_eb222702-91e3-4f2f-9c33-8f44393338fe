package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存推演查询条件Vo
 * <AUTHOR>
 * @date 2023/12/01 09:53
 */
@Setter
@Getter
@ToString
@ApiModel("库存推演查询条件Vo")
public class InventoryInferenceConditionVo extends TransferPlanCondition implements Serializable
{
    private static final long serialVersionUID = -3358423256967188562L;
    /**
     * 推演类型  0：CDC仓库推演  1:RDC仓库推演
     */
    @ApiModelProperty("推演类型")
    private Integer inferenceType;


}
