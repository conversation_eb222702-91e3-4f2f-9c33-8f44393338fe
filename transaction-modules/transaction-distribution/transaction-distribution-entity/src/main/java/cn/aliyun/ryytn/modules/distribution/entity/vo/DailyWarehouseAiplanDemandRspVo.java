package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日分仓调拨需求响应Vo
 * <AUTHOR>
 * @date 2023/12/18 15:38
 */
@Setter
@Getter
@ToString
@ApiModel("日分仓调拨需求响应Vo")
public class DailyWarehouseAiplanDemandRspVo implements Serializable
{
    private static final long serialVersionUID = 8155764834948815117L;
    private String id;

    @ApiModelProperty("产品编码")
    private String skuCode;

    @ApiModelProperty("产品简称")
    private String skuName;

    @ApiModelProperty("0:TOB业务，1:TOC业务")
    private Integer distributeType;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("产品分类编码")
    private String lv1CategoryCode;

    @ApiModelProperty("产品分类名称")
    private String lv1CategoryName;

    @ApiModelProperty("产品大类编码")
    private String lv2CategoryCode;

    @ApiModelProperty("产品大类名称")
    private String lv2CategoryName;

    @ApiModelProperty("产品小类编码")
    private String lv3CategoryCode;

    @ApiModelProperty("产品小类名称")
    private String lv3CategoryName;

    @ApiModelProperty("效期规则")
    private String validityPeriod;

    @ApiModelProperty("日期")
    private String planDate;

    @ApiModelProperty("数量")
    private Double planValue;

    @ApiModelProperty("动态数据json串，格式不固定，看sql聚合函数")
    private String data;

    @ApiModelProperty("动态字段数据映射")
    private Map<String, PlanValue> dataMap = new HashMap<>();

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        DailyWarehouseAiplanDemandRspVo that = (DailyWarehouseAiplanDemandRspVo) o;
        return Objects.equals(skuCode, that.skuCode) && Objects.equals(skuName, that.skuName) && Objects.equals(distributeType, that.distributeType) &&
            Objects.equals(warehouseCode, that.warehouseCode) && Objects.equals(warehouseName, that.warehouseName) &&
            Objects.equals(lv1CategoryCode, that.lv1CategoryCode) && Objects.equals(lv1CategoryName, that.lv1CategoryName) &&
            Objects.equals(lv2CategoryCode, that.lv2CategoryCode) && Objects.equals(lv2CategoryName, that.lv2CategoryName) &&
            Objects.equals(lv3CategoryCode, that.lv3CategoryCode) && Objects.equals(lv3CategoryName, that.lv3CategoryName);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(skuCode, skuName, distributeType, warehouseCode, warehouseName, lv1CategoryCode, lv1CategoryName, lv2CategoryCode, lv2CategoryName,
            lv3CategoryCode, lv3CategoryName);
    }
}
