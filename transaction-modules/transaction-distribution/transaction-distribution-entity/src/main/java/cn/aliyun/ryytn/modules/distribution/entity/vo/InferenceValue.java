package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description TODO
 * <AUTHOR>
 * @date 2024/1/8 15:07
 */
@Setter
@Getter
@ToString
@ApiModel("计划值")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InferenceValue implements Serializable
{
    private static final long serialVersionUID = 3285845343703339412L;
    @ApiModelProperty("日期,yyyy-MM-dd")
    private String planDate;

    @ApiModelProperty("期初库存")
    private Double oiValue;

    @ApiModelProperty("期末库存")
    private Double eiValue;

    @ApiModelProperty("未来可供应天数")
    private Double availDay;
}
