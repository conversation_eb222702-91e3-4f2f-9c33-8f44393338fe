package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;

import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 调拨计划
 * <AUTHOR>
 * @date 2023/12/26 14:20
 */
@Setter
@Getter
@ToString
@ApiModel("调拨计划")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class FreightPlanDto implements Serializable
{
    private static final long serialVersionUID = -8572970604963658919L;
    @ApiModelProperty("唯一编号")
    @ExcelIgnore
    private String id;

    @ApiModelProperty("需求计划编号")
    @ExcelIgnore
    private String demandPlanCode;

    @ApiModelProperty("需求计划名称")
    @ExcelIgnore
    private String demandPlanName;

    @ApiModelProperty("日分仓需求版本，算法中间表只预留了一个版本号字段，需要从tdm_kcjh_txn_freight_qty_di.version_id拆分")
    @ExcelIgnore
    private String demandPlanVersion;

    @ApiModelProperty("日分仓调拨需求版本，算法中间表只预留了一个版本号字段，需要从tdm_kcjh_txn_freight_qty_di.version_id拆分")
    @ExcelIgnore
    private String aiplanDemandVersion;

    @ApiModelProperty("算法版本code")
    @ExcelIgnore
    private String algoNameAndVersion;

    @ApiModelProperty("预测版本")
    @ExcelIgnore
    private String predictionVersion;

    @ApiModelProperty("物品编码")
    @ExcelProperty(value = "生产编码", index = 0)
    private String itemId;

    @ApiModelProperty("物品编码，英文逗号分隔")
    @ExcelIgnore
    private String itemIds;

    @ApiModelProperty("物品名称")
    @ExcelProperty(value = "产品简称", index = 1)
    private String itemName;

    @ApiModelProperty("生产日期")
    @ExcelIgnore
    private String productionDate;

    @ApiModelProperty("起点编码，逻辑仓")
    @ExcelProperty(value = "调出逻辑仓编码", index = 2)
    private String startPointId;

    @ApiModelProperty("起点编码，逻辑仓，英文逗号分隔")
    @ExcelIgnore
    private String startPointIds;

    @ApiModelProperty("起点名称")
    @ExcelProperty(value = "调出逻辑仓", index = 3)
    private String startPointName;

    @ApiModelProperty("起点编码，物理仓")
    @ExcelIgnore
    private String startPhysicalPointId;

    @ApiModelProperty("起点编码，物理仓，英文逗号分隔")
    @ExcelIgnore
    private String startPhysicalPointIds;

    @ApiModelProperty("起点名称，物理仓")
    @ExcelProperty(value = "调出物理仓", index = 4)
    private String startPhysicalPointName;

    @ApiModelProperty("出发日期")
    @ExcelIgnore
    private String departureDate;

    @ApiModelProperty("终点编码，逻辑仓")
    @ExcelProperty(value = "调入逻辑仓编码", index = 5)
    private String endPointId;

    @ApiModelProperty("终点编码，逻辑仓，英文逗号分隔")
    @ExcelIgnore
    private String endPointIds;

    @ApiModelProperty("终点名称，RDC入库仓名称")
    @ExcelProperty(value = "调入逻辑仓", index = 6)
    private String endPointName;

    @ApiModelProperty("终点编码，物理仓")
    @ExcelIgnore
    private String endPhysicalPointId;

    @ApiModelProperty("终点编码，物理仓，英文逗号分隔")
    @ExcelIgnore
    private String endPhysicalPointIds;

    @ApiModelProperty("终点名称，物理仓")
    @ExcelProperty(value = "调入物理仓", index = 7)
    private String endPhysicalPointName;

    @ApiModelProperty("到达日期")
    @ExcelProperty(value = "到达时间", index = 19)
    private String arrivalDate;

    @ApiModelProperty("效期规则")
    @ExcelProperty(value = "效期规则", index = 8)
    private String validRuleLabel;

    @ApiModelProperty("出库时效期规则")
    @ExcelIgnore
    private String departureValidName;

    @ApiModelProperty("出库时效期规则")
    @ExcelIgnore
    private String departureValidNames;

    @ApiModelProperty("入库时效期规则")
    @ExcelIgnore
    private String arrivalValidName;

    @ApiModelProperty("入库时效期规则")
    @ExcelIgnore
    private String arrivalValidNames;

    @ApiModelProperty("运输方式编码")
    @ExcelIgnore
    private String shippingTypeGroupId;

    @ApiModelProperty("运输方式编码，英文逗号分隔")
    @ExcelIgnore
    private String shippingTypeGroupIds;

    @ApiModelProperty("运输方式名称")
    @ExcelProperty(value = "运输方式", index = 9)
    private String shippingTypeGroupName;

    @ApiModelProperty("建议调拨量")
    @ExcelIgnore
    private Double qty;

    @ApiModelProperty("实际调拨量")
    @ExcelProperty(value = "调拨数量（提/罐）", index = 11)
    private Double actQty;

    @ApiModelProperty("单位")
    @ExcelIgnore
    private String unit;

    @ApiModelProperty("备注")
    @ExcelIgnore
    private String remark;

    @ApiModelProperty("扩展字段")
    @ExcelIgnore
    private String extend;

    @ApiModelProperty("状态")
    @ExcelIgnore
    private Integer status;

    @ApiModelProperty("创建结果")
    @ExcelIgnore
    private String gmtCreateResult;

    @ApiModelProperty("二级分区：任务运行唯一ID")
    @ExcelIgnore
    private String taskDetailId;

    @ApiModelProperty("是否删除")
    @ExcelIgnore
    private Integer deleted;

    @ApiModelProperty("一级分区：日期分区")
    @ExcelIgnore
    private String ds;

    @ApiModelProperty("创建人")
    @ExcelIgnore
    private String creator;

    @ApiModelProperty("结果生成时间，YYYY-MM-DD  HH:MI:SS")
    @ExcelIgnore
    private String gmtCreate;

    @ApiModelProperty("最后修改时间")
    @ExcelIgnore
    private String lastModifier;

    @ApiModelProperty("修改时间")
    @ExcelIgnore
    private String gmtModify;

    @ApiModelProperty("算法生成版本号，通过so_wt_demand中间表传递，格式：demandPlanVersion,aiplanDemandVersion")
    @ExcelIgnore
    private String versionId;

    @ApiModelProperty("动态字段json字符串，格式未定，看查询sql分组聚合函数")
    @ExcelIgnore
    private String data;

    @ApiModelProperty("分组字段列表")
    @ExcelIgnore
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("动态字段数据")
    @ExcelIgnore
    private Map<String, PlanValue> dataMap = new HashMap<>();

    @ApiModelProperty("分组字段")
    @ExcelIgnore
    private String groupColumn;

    @ApiModelProperty("排序字段")
    @ExcelIgnore
    private String sortColumn;

    @ApiModelProperty("业务字段标识列表")
    @ExcelIgnore
    private List<FreightPlanDto> keyList;

    @ApiModelProperty("产品编号，英文逗号分隔，用于查询库存策略相关数据")
    @ExcelIgnore
    private String skuCodes;

    @ApiModelProperty("仓库编号，英文逗号分隔，用于查询库存策略相关数据")
    @ExcelIgnore
    private String warehouseCodes;

    @ApiModelProperty("是否cdc，原来默认是false，需求变更默认true，控制查询时间字段是出库时间还是入库时间，false：入库时间，true：出库时间")
    @ExcelIgnore
    private Boolean cdcFlag = true;

    @ApiModelProperty("是否可以修改")
    @ExcelIgnore
    private Boolean modifyFlag;

    @ExcelProperty(value = "调拨日期", index = 10)
    private String planDate;

    @ApiModelProperty("装箱系数")
    @ExcelProperty(value = "装箱系数", index = 12)
    private Integer planUnitCnt;

    @ApiModelProperty("调拨数量(箱)")
    @ExcelProperty(value = "调拨数量(箱)", index = 13)
    private Double actUnitQty;

    @ApiModelProperty("调拨毛重(kg)")
    @ExcelProperty(value = "调拨毛重(kg)", index = 14)
    private Double roughtWeight;

    @ApiModelProperty("调拨体积(m3)")
    @ExcelProperty(value = "调拨体积(m3)", index = 15)
    private Double volumn;

    @ApiModelProperty("可供应天数")
    @ExcelProperty(value = "可供应天数", index = 16)
    private String days;

    @ApiModelProperty("生产开始时间")
    @ExcelProperty(value = "生产开始时间", index = 17)
    private String productionDateStart;



    @ApiModelProperty("生产结束时间")
    @ExcelProperty(value = "生产结束时间", index = 18)
    private String productionDateEnd;

    @ApiModelProperty("abcType")
    @ExcelIgnore
    private String abcType;
    /**
     *
     * @Description 获取效期规则内容
     * @return String
     * <AUTHOR>
     * @date 2024年03月13日 14:16
     */
    public String getValidRuleLabel()
    {
        return this.cdcFlag ? this.departureValidName : this.arrivalValidName;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        FreightPlanDto object = (FreightPlanDto) o;
        return Objects.equals(this.demandPlanCode, object.demandPlanCode)
            && Objects.equals(this.predictionVersion, object.predictionVersion)
            && Objects.equals(this.itemId, object.itemId)
            && Objects.equals(this.departureDate, object.departureDate)
            && Objects.equals(this.arrivalDate, object.arrivalDate)
            && Objects.equals(this.startPointId, object.startPointId)
            && Objects.equals(this.endPointId, object.endPointId)
            && Objects.equals(this.startPhysicalPointId, object.startPhysicalPointId)
            && Objects.equals(this.endPhysicalPointId, object.endPhysicalPointId)
            && Objects.equals(this.departureValidName, object.departureValidName)
            && Objects.equals(this.arrivalValidName, object.arrivalValidName)
            && Objects.equals(this.shippingTypeGroupId, object.shippingTypeGroupId)
            && Objects.equals(this.taskDetailId, object.taskDetailId);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((demandPlanCode == null) ? 0 : demandPlanCode.hashCode());
        result = prime * result + ((predictionVersion == null) ? 0 : predictionVersion.hashCode());
        result = prime * result + ((itemId == null) ? 0 : itemId.hashCode());
        result = prime * result + ((departureDate == null) ? 0 : departureDate.hashCode());
        result = prime * result + ((arrivalDate == null) ? 0 : arrivalDate.hashCode());
        result = prime * result + ((startPointId == null) ? 0 : startPointId.hashCode());
        result = prime * result + ((endPointId == null) ? 0 : endPointId.hashCode());
        result = prime * result + ((startPhysicalPointId == null) ? 0 : startPhysicalPointId.hashCode());
        result = prime * result + ((endPhysicalPointId == null) ? 0 : endPhysicalPointId.hashCode());
        result = prime * result + ((departureValidName == null) ? 0 : departureValidName.hashCode());
        result = prime * result + ((arrivalValidName == null) ? 0 : arrivalValidName.hashCode());
        result = prime * result + ((shippingTypeGroupId == null) ? 0 : shippingTypeGroupId.hashCode());
        result = prime * result + ((taskDetailId == null) ? 0 : taskDetailId.hashCode());
        return result;
    }
}
