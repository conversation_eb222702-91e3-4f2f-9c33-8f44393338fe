package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 调拨计划DataMapVo
 * <AUTHOR>
 * @date 2023/12/13 14:15
 */
@Setter
@Getter
@ToString
@ApiModel("调拨计划DataMapVo")
public class TransferPlanDataMapVo implements Serializable
{
    private static final long serialVersionUID = -1658423256967188562L;

    /**
     * 数据值
     */
    @ApiModelProperty("数据值")
    private Double value;

    public TransferPlanDataMapVo(Double value)
    {
        this.value = value;
    }
}
