package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import cn.aliyun.ryytn.modules.distribution.entity.vo.InferenceValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存推演实体类，对应阿里数据库视图：v_inventory_inference
 * 项目要求使用此视图，视图列是固定的，无法做到推演日期的扩展。
 * <AUTHOR>
 * @date 2024/1/8 10:12
 */
@Setter
@Getter
@ToString
@ApiModel("库存推演")
public class InventoryInferenceDto implements Serializable
{
    private static final long serialVersionUID = -4441613767854215605L;
    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("仓库编号（物理仓）")
    private String warehouseCode;

    @ApiModelProperty("仓库名称（物理仓）")
    private String warehouseName;

    @ApiModelProperty("推演首日日期，yyyy-MM-dd")
    private String ds;

    @ApiModelProperty("推演第0天期初库存")
    private Double oi0;

    @ApiModelProperty("推演第0天期末库存")
    private Double ei0;

    @ApiModelProperty("推演第1天期初库存")
    private Double oi1;

    @ApiModelProperty("推演第1天期末库存")
    private Double ei1;

    @ApiModelProperty("推演第2天期初库存")
    private Double oi2;

    @ApiModelProperty("推演第2天期末库存")
    private Double ei2;

    @ApiModelProperty("推演第3天期初库存")
    private Double oi3;

    @ApiModelProperty("推演第3天期末库存")
    private Double ei3;

    @ApiModelProperty("推演第4天期初库存")
    private Double oi4;

    @ApiModelProperty("推演第4天期末库存")
    private Double ei4;

    @ApiModelProperty("推演第5天期初库存")
    private Double oi5;

    @ApiModelProperty("推演第5天期末库存")
    private Double ei5;

    @ApiModelProperty("推演第6天期初库存")
    private Double oi6;

    @ApiModelProperty("推演第6天期末库存")
    private Double ei6;

    @ApiModelProperty("推演第7天期初库存")
    private Double oi7;

    @ApiModelProperty("推演第7天期末库存")
    private Double ei7;

    @ApiModelProperty("推演第8天期初库存")
    private Double oi8;

    @ApiModelProperty("推演第8天期末库存")
    private Double ei8;

    @ApiModelProperty("推演第9天期初库存")
    private Double oi9;

    @ApiModelProperty("推演第9天期末库存")
    private Double ei9;

    @ApiModelProperty("推演第10天期初库存")
    private Double oi10;

    @ApiModelProperty("推演第10天期末库存")
    private Double ei10;

    @ApiModelProperty("推演第11天期初库存")
    private Double oi11;

    @ApiModelProperty("推演第11天期末库存")
    private Double ei11;

    @ApiModelProperty("推演第12天期初库存")
    private Double oi12;

    @ApiModelProperty("推演第12天期末库存")
    private Double ei12;

    @ApiModelProperty("推演第13天期初库存")
    private Double oi13;

    @ApiModelProperty("推演第13天期末库存")
    private Double ei13;

    @ApiModelProperty("平均调拨数量")
    private Double avgAllocation;

    @ApiModelProperty("推演数据")
    private Map<String, InferenceValue> dataMap = new HashMap<>();

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        InventoryInferenceDto object = (InventoryInferenceDto) o;
        return Objects.equals(this.skuCode, object.skuCode)
            && Objects.equals(this.warehouseCode, object.warehouseCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((warehouseCode == null) ? 0 : warehouseCode.hashCode());
        return result;
    }
}
