package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.List;

import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 新增日分仓需求Vo
 * <AUTHOR>
 * @date 2023/12/4 14:18
 */
@Setter
@Getter
@ToString
public class AddDailyWarehouseDemandListVo extends DailyWarehouseDemandDto implements Serializable
{
    private static final long serialVersionUID = -2320419779774537740L;

    /**
     * 分仓需求计划周数据
     */
    private List<WarehouseDemandPlanWeekData> dailyWarehouseDemandWeekDataList;
}
