package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存策略-分仓预测结果
 * <AUTHOR>
 * @date 2023/11/28 14:12
 */
@Setter
@Getter
@ToString
public class PredictResult implements Serializable
{
    private static final long serialVersionUID = -7758455256967188555L;
    private String skuCode;
    private String warehouseCode;
    private Double predictionResult = 0.0;


}
