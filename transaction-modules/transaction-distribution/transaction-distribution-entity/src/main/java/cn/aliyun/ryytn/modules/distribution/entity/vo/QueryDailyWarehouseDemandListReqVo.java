package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnore;

import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询日分仓需求入参
 * <AUTHOR>
 * @date 2023/12/4 14:15
 */
@Setter
@Getter
@ToString
@ApiModel("查询日分仓需求入参")
public class QueryDailyWarehouseDemandListReqVo implements Serializable
{
    private static final long serialVersionUID = -8158636752064684666L;
    @ApiModelProperty("需求计划编码")
    private String demandPlanCode;

    @ApiModelProperty("需求计划名称")
    private String demandPlanName;

    @ApiModelProperty("需求计划版本")
    private String demandPlanVersion;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品编号，英文逗号分隔")
    private String skuCodes;

    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    @ApiModelProperty("仓库编号，英文逗号分隔")
    private String warehouseCodes;

    @ApiModelProperty("渠道类型")
    private String distributeType;

    @ApiModelProperty("渠道类型，英文逗号分隔")
    private String distributeTypes;

    @ApiModelProperty("分组字段枚举列表")
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("业务标识列表")
    private List<DailyWarehouseDemandRspVo> keyList;

    @ApiModelProperty("分组字段，英文逗号分隔")
    private String groupColumn;

    @ApiModelProperty("排序字段，英文逗号分隔")
    private String sortColumn;

    @ApiModelProperty("开始日期")
    private String beginDate;

    @ApiModelProperty("结束日期")
    private String endDate;

    @ApiModelProperty("一级品类编号")
    @ExcelIgnore
    private String lv1CategoryCode;

    @ApiModelProperty("二级品类编号")
    @ExcelIgnore
    private String lv2CategoryCode;

    @ApiModelProperty("三级品类编号")
    @ExcelIgnore
    private String lv3CategoryCode;

    @ApiModelProperty("一级品类编号，英文逗号分隔")
    @ExcelIgnore
    private String lv1CategoryCodes;

    @ApiModelProperty("二级品类编号，英文逗号分隔")
    @ExcelIgnore
    private String lv2CategoryCodes;

    @ApiModelProperty("三级品类编号，英文逗号分隔")
    @ExcelIgnore
    private String lv3CategoryCodes;

    @ApiModelProperty("分表后缀")
    @ExcelIgnore
    private String tableSuffix;
    @ApiModelProperty("类型")
    @ExcelIgnore
    private Integer planDataType;
}
