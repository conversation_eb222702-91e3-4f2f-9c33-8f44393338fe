package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import cn.aliyun.ryytn.common.constants.BizDateTypeEnum;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 在途库存/生产调入
 * <AUTHOR>
 * @date 2024/1/5 17:09
 */
@Setter
@Getter
@ToString
@ApiModel("在途库存/生产调入")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class WarehouseStockAnalyDto implements Serializable
{
    private static final long serialVersionUID = 6768233033882832773L;
    @ApiModelProperty("维度组合")
    private String dimComb;

    @ApiModelProperty("时间类型:DAY,WEEK，MONTH,YEAR")
    private BizDateTypeEnum bizDateType;

    @ApiModelProperty("时间类型值,日：20230101;周:0230103;月:202301")
    private String bizDateValue;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("仓库类型编码")
    private String lv1TypeCode;

    @ApiModelProperty("仓库类型名称")
    private String lv1TypeName;

    @ApiModelProperty("skuCode")
    private String skuCode;

    @ApiModelProperty("sku名称")
    private String skuName;

    @ApiModelProperty("仓库的类型是CDC表示出库，仓库类型是RDC表示入库时间")
    private String inWarehouseDate;

    @ApiModelProperty("生产日期")
    private String produceDate;

    @ApiModelProperty("可用库存、期初库存")
    private Double availableNum;

    @ApiModelProperty("在途库存")
    private Double onTransitNum;

    @ApiModelProperty("预计生产")
    private Double productionNum;

    @ApiModelProperty("单位")
    private String dataUnit;

    @ApiModelProperty("时间")
    private String ds;

    @ApiModelProperty("效期名称")
    private String validName;

    @ApiModelProperty("时间列表")
    private List<String> dateList;

    @ApiModelProperty("动态字段值json，格式根据sql聚合函数确定")
    private String data;

    @ApiModelProperty("动态字段Map")
    private Map<String, PlanValue> dataMap = new HashMap<>();

    @ApiModelProperty("分组字段")
    private String groupColumn;
}
