package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 编辑库存推演Vo
 * <AUTHOR>
 * @date 2023/12/19 16:42
 */
@Setter
@Getter
@ToString
@ApiModel("编辑库存推演Vo")
public class EditInventoryInferenceVo implements Serializable
{
    private static final long serialVersionUID = 7703385754220942398L;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("区分字段 null为原始 1为自己创建的")
    private Integer type;

    /**
     * 动态字段数据映射
     */
    @ApiModelProperty("动态字段数据映射")
    private Map<String, InventoryInferenceDataMapVo> dataMap = new HashMap<>();


}
