package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 阿里服务水平实体
 * <AUTHOR>
 * @date 2023/11/23 09:58
 */
@Setter
@Getter
@ToString
public class SoSsServiceLevelDto implements Serializable
{
    private static final long serialVersionUID = -1256423256967188222L;

    /**
     * 库存点编码
     */
    private String stockPointId;
    /**
     * 库存点名称
     */
    private String stockPointName;
    /**
     * 物品编码
     */
    private String itemId;
    /**
     * 物品名称
     */
    private String itemName;
    /**
     * 服务水平 默认 0.96
     */
    private Double serviceLevel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private Integer status;
}
