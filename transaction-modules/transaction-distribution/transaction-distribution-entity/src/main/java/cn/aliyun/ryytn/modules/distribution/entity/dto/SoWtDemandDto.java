package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日分仓调拨需求中间表
 * <AUTHOR>
 * @date 2023/12/20 15:04
 */
@Setter
@Getter
@ToString
@ApiModel("日分仓调拨需求中间表")
public class SoWtDemandDto implements Serializable
{
    private static final long serialVersionUID = -2024739892026667151L;
    private String itemId;

    private String itemName;

    private String stockPointId;

    private String stockPointName;

    private Integer expiryLimit;

    private Date expectedDeliveryDate;

    private Double qty;

    private String remark;

    private Integer status;

    private String demandPlanCode;

    private String demandPlanName;

    private String versionId;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        SoWtDemandDto object = (SoWtDemandDto) o;
        return Objects.equals(this.demandPlanCode, object.demandPlanCode)
            && Objects.equals(this.versionId, object.versionId);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((demandPlanCode == null) ? 0 : demandPlanCode.hashCode());
        result = prime * result + ((versionId == null) ? 0 : versionId.hashCode());
        return result;
    }
}
