package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询可调天数规则响应Vo
 * <AUTHOR>
 * @date 2023/11/20 16:08
 */
@Setter
@Getter
@ToString
@ApiModel("查询可调天数规则响应Vo")
public class QueryAdjustableDaysRuleListRspVo implements Serializable
{
    private static final long serialVersionUID = 5214215008458634755L;
    private String id;

    @ApiModelProperty("规则名称")
    private String name;

    @ApiModelProperty("生效时间 yyyy-MM-dd")
    @JsonFormat(pattern = DateUtils.YMD_DASH, timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("结束时间 yyyy-MM-dd")
    @JsonFormat(pattern = DateUtils.YMD_DASH, timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("是否永久生效 0:否，1：是")
    private Integer foreverFlag;

    @ApiModelProperty("范围类型(0:产品，1:品类，2：全部)")
    private Integer rangeType;

    @ApiModelProperty("产品范围规则")
    private String productRange;

    @ApiModelProperty("品类范围规则")
    private String categoryRange;

    @ApiModelProperty("生效状态(0:待生效，1:已生效，2：已失效)")
    private Integer status;

    @ApiModelProperty("0为默认规则，1为手工添加规则")
    private Integer isDefault;
}
