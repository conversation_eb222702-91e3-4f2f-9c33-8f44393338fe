package cn.aliyun.ryytn.modules.distribution.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024-09-12 11:33
 * @Description
 */
@Setter
@Getter
@ToString
@ApiModel("skuAbc分类Dto")
public class SkuAbcDto implements Serializable {

    private static final long serialVersionUID = 2329789352824557018L;
    @ApiModelProperty("产品编码")
    private String skuCode;


    @ApiModelProperty("产品名称")
    private String skuName;


    @ApiModelProperty("类型")
    private String sourceOrderType;

    @ApiModelProperty("ABC类型")
    private String abcType;

    @ApiModelProperty("ds")
    private String ds;
}
