package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 效期分档规则范围
 * <AUTHOR>
 * @date 2023/11/14 16:22
 */
@Setter
@Getter
@ToString
@ApiModel("效期分档规则范围")
public class ValidRuleRange implements Serializable
{
    private static final long serialVersionUID = -3688423256967188562L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;
    /**
     * 标签名称
     */
    @ApiModelProperty("标签名称")
    private String name;

    /**
     * 效期归档主键id
     */
    @ApiModelProperty("效期归档主键id")
    private String ruleId;
    /**
     * 分档起始天数
     */
    @ApiModelProperty("分档起始天数")
    private Integer startDay;
    /**
     * 分档结束天数
     */
    @ApiModelProperty("分档结束天数")
    private Integer endDay;
    /**
     * 效期需求占比
     */
    @ApiModelProperty("效期需求占比")
    private Double ratio;

}
