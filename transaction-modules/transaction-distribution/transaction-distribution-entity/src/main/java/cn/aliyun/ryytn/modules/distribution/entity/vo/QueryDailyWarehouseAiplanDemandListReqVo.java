package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.List;

import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询日分仓调拨需求请求Vo
 * <AUTHOR>
 * @date 2023/12/18 15:34
 */
@Setter
@Getter
@ToString
@ApiModel("查询日分仓调拨需求请求Vo")
public class QueryDailyWarehouseAiplanDemandListReqVo implements Serializable
{
    private static final long serialVersionUID = -4936660945132606615L;
    @ApiModelProperty("需求计划名称")
    private String demandPlanCode;

    @ApiModelProperty("需求计划版本")
    private String demandPlanVersion;

    @ApiModelProperty("调拨需求版本")
    private String aiplanDemandVersion;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("产品编号，英文逗号分隔")
    private String skuCodes;

    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    @ApiModelProperty("仓库编号，英文逗号分隔")
    private String warehouseCodes;

    @ApiModelProperty("渠道类型")
    private String distributeType;

    @ApiModelProperty("渠道类型，英文逗号分隔")
    private String distributeTypes;

    @ApiModelProperty("效期规则")
    private String validityPeriod;

    @ApiModelProperty("效期规则")
    private String validityPeriods;

    @ApiModelProperty("分组字段枚举列表")
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("")
    private List<DailyWarehouseAiplanDemandRspVo> keyList;

    @ApiModelProperty("")
    private String groupColumn;

    @ApiModelProperty("")
    private String sortColumn;
}
