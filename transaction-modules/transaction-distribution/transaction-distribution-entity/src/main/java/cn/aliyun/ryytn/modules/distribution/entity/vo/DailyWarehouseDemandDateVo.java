package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日分仓调拨需求数据Vo
 * <AUTHOR>
 * @date 2023/12/23 16:22
 */
@Setter
@Getter
@ToString
@ApiModel("日分仓调拨需求数据Vo")
public class DailyWarehouseDemandDateVo implements Serializable
{
    private static final long serialVersionUID = 868737110454997345L;
    private String id;

    private double dateValue;

    private String subDemandPlanVersion;
}
