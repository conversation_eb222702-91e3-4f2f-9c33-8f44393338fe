package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存策略条件VO
 * <AUTHOR>
 * @date 2023/11/28 19:08
 */
@Setter
@Getter
@ToString
@ApiModel("库存策略条件VO")
public class InventoryStrategyConditionVo implements Serializable
{
    private static final long serialVersionUID = 1959578268106225294L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;
    /**
     * 仓库编码集合
     */
    @ApiModelProperty("仓库编码集合")
    private String warehouseCodes;
    /**
     * 仓库名称集合
     */
    @ApiModelProperty("仓库名称集合")
    private String warehouseNames;

    /**
     * 产品编码集合
     */
    @ApiModelProperty("产品编码集合")
    private String skuCodes;
    /**
     * 产品名称集合
     */
    @ApiModelProperty("产品名称集合")
    private String skuNames;

    @ApiModelProperty("观测时间点，yyyy-MM-dd")
    private String nowDate;

    @ApiModelProperty("观测时间点集合，yyyy-MM-dd")
    private List<String> nowDateList;
}
