package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存值
 * <AUTHOR>
 * @date 2024/3/19 11:40
 */
@Setter
@Getter
@ToString
@ApiModel("库存值")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class StockValue implements Serializable
{
    private static final long serialVersionUID = -9190213046952893176L;
    @ApiModelProperty("库存日期")
    private String stockDate;

    @ApiModelProperty("期初库存")
    private Double availableNum;

    @ApiModelProperty("期末库存")
    private Double tailNum;

    @ApiModelProperty("在途库存")
    private Double onTransitNum;

    @ApiModelProperty("预计生产")
    private Double productionNum;

    @ApiModelProperty("未来可供应天数")
    private Integer days;
}
