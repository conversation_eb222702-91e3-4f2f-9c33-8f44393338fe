package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 渠道预测算法入参
 * <AUTHOR>
 * @date 2023/12/13 14:51
 */
@Setter
@Getter
@ToString
public class ChannelArgs implements Serializable
{
    private static final long serialVersionUID = -5965051274164337681L;

    /**
     * 写死 CHANNEL
     */
    private String agent;

    /**
     * 20230424
     */
    private String evalEnd;

    /**
     * CONVENTIONAL
     */
    private String objectType;

    /**
     * 12
     */
    private Integer periods;

    /**
     * 写死为：algo_reseller_order_monthly_forecast-V0.0  ，因为要通过这个值，展示算法管理模块
     *      或者， 如果这个值需要改变，记得同步修改 ，表：cdop_biz.tdm_bas_algo_model_info_df 的    对应一行的   ，algo_name_and_version列的值
     */
    private String algo_name_and_version;

    /**
     * 自己生成
     */
    private String taskDetailId;

    /**
     * 数据库配置信息
     */
    private String defaultDatabaseInfo;
}
