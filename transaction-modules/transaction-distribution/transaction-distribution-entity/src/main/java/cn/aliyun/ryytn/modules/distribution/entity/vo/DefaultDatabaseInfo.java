package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 数据库配置信息
 * <AUTHOR>
 * @date 2023/12/6 10:03
 */
@Setter
@Getter
@ToString
public class DefaultDatabaseInfo implements Serializable
{
    private static final long serialVersionUID = 4321898385321094716L;

    private String access_id;

    private String secret_access_key;

    private String project;

    private String endpoint;
}
