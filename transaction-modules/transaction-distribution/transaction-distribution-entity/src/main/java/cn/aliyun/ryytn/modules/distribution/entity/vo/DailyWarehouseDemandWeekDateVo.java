package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日分仓周数据
 * <AUTHOR>
 * @date 2023/12/28 15:13
 */
@Setter
@Getter
@ToString
@ApiModel("日分仓周数据")
public class DailyWarehouseDemandWeekDateVo implements Serializable
{
    private static final long serialVersionUID = 2058935448147274380L;

    @ApiModelProperty("0为周原始数据，1为周更新数据")
    private Integer type;

    @ApiModelProperty("值")
    private Double value;
}
