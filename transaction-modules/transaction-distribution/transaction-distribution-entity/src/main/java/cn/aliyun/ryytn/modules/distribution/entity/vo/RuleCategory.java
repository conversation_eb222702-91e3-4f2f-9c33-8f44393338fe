package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 适用范围品类-子表
 * <AUTHOR>
 * @date 2023/11/14 17:00
 */
@Setter
@Getter
@ToString
@ApiModel("适用范围品类-子表")
public class RuleCategory implements Serializable
{
    private static final long serialVersionUID = -6688423256967188562L;

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;
    /**
     * 效期归档主键id
     */
    @ApiModelProperty("效期归档主键id")
    private String ruleId;
    /**
     * 品类编码
     */
    @ApiModelProperty("品类编码")
    private String categoryCode;
    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private String categoryName;
    /**
     * 品类名称
     */
    @ApiModelProperty("品类等级")
    private Integer level;
    /**
     * 品类名称
     */
    @ApiModelProperty("产品")
    private String skuCodes;
}
