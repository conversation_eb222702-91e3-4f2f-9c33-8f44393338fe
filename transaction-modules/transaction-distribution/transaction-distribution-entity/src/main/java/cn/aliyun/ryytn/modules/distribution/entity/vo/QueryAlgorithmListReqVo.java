package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询算法列表请求Vo
 * <AUTHOR>
 * @date 2023/12/25 10:56
 */
@Setter
@Getter
@ToString
@ApiModel("查询算法列表请求Vo")
public class QueryAlgorithmListReqVo implements Serializable
{
    private static final long serialVersionUID = 3309418481830169167L;

    @ApiModelProperty("预测场景")
    private String scene;

    @ApiModelProperty("预测场景,逗号分隔")
    private String scenes;
}
