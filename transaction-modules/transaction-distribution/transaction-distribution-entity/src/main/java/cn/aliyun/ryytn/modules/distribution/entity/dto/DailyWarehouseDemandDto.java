package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日分仓需求
 * <AUTHOR>
 * @date 2023/12/4 16:48
 */
@Setter
@Getter
@ToString
@ApiModel("日分仓需求")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DailyWarehouseDemandDto implements Serializable
{
    private static final long serialVersionUID = -2148158717134616980L;
    @ExcelProperty(value = {"#"}, index = 0)
    private Integer rowId;

    @ExcelIgnore
    private String id;

    @ApiModelProperty("需求计划编码")
    @ExcelIgnore
    private String demandPlanCode;

    @ApiModelProperty("需求计划名称")
    @ExcelIgnore
    private String demandPlanName;

    @ApiModelProperty("需求计划版本名称")
    @JSONField(name = "versionId")
    @ExcelIgnore
    private String demandPlanVersion;

    /**
     * 版本中的实际字段，匹配分表
     */
    @ExcelIgnore
    private String subDemandPlanVersion;

    @ExcelProperty(value = {"产品编码"}, index = 4)
    @ApiModelProperty("产品编码")
    @ColumnWidth(15)
    private String skuCode;

    @ExcelProperty(value = {"产品简称"}, index = 5)
    @ApiModelProperty("产品简称")
    @ColumnWidth(25)
    private String skuName;

    @ApiModelProperty("0:TOB业务，1:TOC业务")
    @ExcelIgnore
    private Integer distributeType;

    @ExcelProperty(value = {"渠道类型"}, index = 7)
    @ApiModelProperty("渠道名称 0:TOB业务，1:TOC业务")
    private String distributeTypeName;

    @ApiModelProperty("仓库编码")
    @ExcelIgnore
    private String warehouseCode;

    @ExcelProperty(value = {"仓库名称"}, index = 6)
    @ColumnWidth(25)
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("产品分类编码")
    @ExcelIgnore
    private String lv1CategoryCode;

    @ExcelProperty(value = {"产品分类"}, index = 1)
    @ColumnWidth(15)
    @ApiModelProperty("产品分类名称")
    private String lv1CategoryName;

    @ApiModelProperty("产品大类编码")
    @ExcelIgnore
    private String lv2CategoryCode;

    @ExcelProperty(value = {"产品大类"}, index = 2)
    @ColumnWidth(15)
    @ApiModelProperty("产品大类名称")
    private String lv2CategoryName;

    @ApiModelProperty("产品小类编码")
    @ExcelIgnore
    private String lv3CategoryCode;

    @ExcelProperty(value = {"产品小类"}, index = 3)
    @ColumnWidth(25)
    @ApiModelProperty("产品小类名称")
    private String lv3CategoryName;

    @ApiModelProperty("日期")
    @ExcelProperty(value = {"出货日"}, index = 9)
    @ColumnWidth(10)
    private String dateRecorded;

    @ApiModelProperty("日数据")
    @ExcelProperty(value = {"数量"}, index = 10)
    private double dateValue;

    @ApiModelProperty("日数据")
    @ExcelProperty(value = {"ABC分类"}, index = 11)
    private String abcType;

    @ApiModelProperty("周日期,每周第一天")
    @JSONField(name = "planDate")
    @ExcelIgnore
    private String weekRecorded;

    @ApiModelProperty("周原始数据")
    @JSONField(name = "planValue")
    @ExcelIgnore
    private double weekRawValue;

    @ApiModelProperty("周实际数据")
    @ExcelIgnore
    private double weekActualValue;

    @ApiModelProperty("周实际数据变化量")
    @ExcelIgnore
    private double weekValueDeviation;

    @ApiModelProperty("状态：1：已生成,2：生成中")
    @ExcelIgnore
    private Integer status;

    @ApiModelProperty("分表后缀")
    @ExcelIgnore
    private String tableSuffix;

    @ApiModelProperty("数据类型:0日销;1活动")
    @ExcelIgnore
    private Integer planDataType;


    @ApiModelProperty("类型")
    @ExcelProperty(value = {"类型"}, index = 8)
    private String planDataTypeName;

    @ApiModelProperty("日分仓开始时间")
    @ExcelIgnore
    private String startDate;
    @ApiModelProperty("日分仓结束时间")
    @ExcelIgnore
    private String endDate;
}
