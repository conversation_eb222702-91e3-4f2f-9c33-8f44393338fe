package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import cn.aliyun.ryytn.common.utils.json.CustomIntegerDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 仓能力容量
 * <AUTHOR>
 * @date 2023/11/19 17:33
 */
@Setter
@Getter
@ToString
@ApiModel("仓能力容量")
public class WarehouseRuleCapacity implements Serializable
{
    private static final long serialVersionUID = -2150639995164489915L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 效期归档主键id
     */
    @ApiModelProperty("效期归档主键id")
    private String ruleId;
    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;
    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    /**
     * 库容（提/罐）
     */
    @ApiModelProperty("库容（提/罐）")
    private Integer capacity;
    /**
     * 是否开启库容
     */
    @ApiModelProperty("是否开启库容 0:否 1：是")
    private Integer capacityFlag;
    /**
     * 收货能力上限（提/罐）
     */
    @ApiModelProperty("收货能力上限（提/罐）")
    @JsonDeserialize(using = CustomIntegerDeserializer.class)
    private Integer deliveryLimit;
    /**
     * 是否开启收货能力上限
     */
    @ApiModelProperty("是否开启收货能力上限 0:否 1：是")
    private Integer deliveryLimitFlag;
    /**
     * 是否设置收货无限能力
     */
    @ApiModelProperty("是否设置收货无限能力 0:否 1：是")
    private Integer deliveryUnLimitFlag;
    /**
     * 出库能力上限（提/罐）
     */
    @ApiModelProperty("效期出库能力上限（提/罐）需求占比")
    @JsonDeserialize(using = CustomIntegerDeserializer.class)
    private Integer shipmentLimit;
    /**
     * 是否开启出库能力上限
     */
    @ApiModelProperty("是否开启出库能力上限  0:否 1：是")
    private Integer shipmentLimitFlag;
    /**
     * 是否设置出库无限能力
     */
    @ApiModelProperty("是否设置出库无限能力 0:否 1：是")
    private Integer shipmentUnLimitFlag;
}
