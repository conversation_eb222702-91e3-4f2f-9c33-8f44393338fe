package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 调拨计划条件Vo
 * <AUTHOR>
 * @date 2023/11/21 17:54
 */
@Setter
@Getter
@ToString
@ApiModel("调拨计划条件Vo")
public class TransferPlanCondition implements Serializable
{
    private static final long serialVersionUID = -2158423256967188562L;

    /**
     * 调拨计划
     */
    @ApiModelProperty("调拨计划")
    private String transferPlan;

    /**
     * 调拨计划版本
     */
    @ApiModelProperty("调拨计划版本")
    private String transferPlanVersion;

    /**
     * 产品编码
     */
    @ApiModelProperty("生产编码")
    private String skuCode;

    /**
     * 产品简称
     */
    @ApiModelProperty("产品简称")
    private String skuName;

    /**
     * 调入仓库编码
     */
    @ApiModelProperty("调入仓库编码")
    private String warehouseCodeIn;

    /**
     * 调出仓库编码
     */
    @ApiModelProperty("调出仓库编码")
    private String warehouseCodeOut;

    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /**
     * 产品效期类型  0:总量  1 ：新鲜  2 ：常规
     */
    private String validType;

}
