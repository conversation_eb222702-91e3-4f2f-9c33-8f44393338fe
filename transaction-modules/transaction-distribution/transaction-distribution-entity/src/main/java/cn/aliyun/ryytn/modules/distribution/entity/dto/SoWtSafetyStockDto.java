package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 阿里安全库存实体
 * <AUTHOR>
 * @date 2023/11/29 18:48
 */
@Setter
@Getter
@ToString
public class SoWtSafetyStockDto implements Serializable
{
    private static final long serialVersionUID = -5256423256967188222L;
    /**
     * 物品编码
     */
    private String itemId;
    /**
     * 物品名称
     */
    private String itemName;

    /**
     * 库存点编码
     */
    private String stockPointId;
    /**
     * 库存点名称
     */
    private String stockPointName;
    /**
     * 安全库存    不填写默认为0
     */
    private Double safetyStock;
    /**
     * 目标库存 不填写默认无限制
     */
    private Double targetStock;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private Integer status;
}
