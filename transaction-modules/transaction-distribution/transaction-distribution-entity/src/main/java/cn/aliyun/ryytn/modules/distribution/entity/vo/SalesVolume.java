package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日均销量实体
 * <AUTHOR>
 * @date 2023/11/28 13:40
 */
@Setter
@Getter
@ToString
@ApiModel("日均销量实体")
public class SalesVolume implements Serializable
{
    private static final long serialVersionUID = -1727300378758265525L;
    @ApiModelProperty("yyyy/yyyymm")
    private String bizDateValue;
    @ApiModelProperty("商品编码")
    private String skuCode;
    @ApiModelProperty("商品名称")
    private String skuName;
    @ApiModelProperty("仓库code")
    private String warehouseCode;
    @ApiModelProperty("仓库name")
    private String warehouseName;
    @ApiModelProperty("出库数量")
    private Double outboundNum = 0.0;
    @ApiModelProperty("数量单位")
    private String numUnit;

    @ApiModelProperty("时间粒度")
    private String bizDateType;

    @ApiModelProperty("产品编号，英文逗号分隔")
    private String skuCodes;

    @ApiModelProperty("仓库编号，英文逗号分隔")
    private String warehouseCodes;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;
}
