package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 调拨计划算法
 * <AUTHOR>
 * @date 2023/12/8 17:48
 */
@Setter
@Getter
@ToString
public class AlgoSchedulingRecordDto implements Serializable
{
    private static final long serialVersionUID = -3641560383803169067L;

    private String id;

    /**
     * 算法名称和版本
     */
    private String algoNameAndVersion;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 运行状态
     */
    private Integer runningStatus;

    /**
     * 算法类型 0：渠道 1：分仓 2：库存 3：调拨
     */
    private Integer scene;
}
