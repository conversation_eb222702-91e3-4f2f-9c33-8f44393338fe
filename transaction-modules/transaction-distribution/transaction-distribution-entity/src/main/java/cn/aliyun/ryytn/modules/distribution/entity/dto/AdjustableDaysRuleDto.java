package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 可调节天数规则
 * <AUTHOR>
 * @date 2023/11/16 11:25
 */
@Setter
@Getter
@ToString
@ApiModel("可调节天数规则")
public class AdjustableDaysRuleDto implements Serializable
{
    private static final long serialVersionUID = -4482597028821770798L;
    private String id;

    @ApiModelProperty("规则名称")
    private String name;

    @ApiModelProperty("生效时间 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty("结束时间 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty("是否永久生效 0:否，1：是")
    private Integer foreverFlag;

    @ApiModelProperty("可调节天数规则范围")
    private List<AdjustableDaysRuleRangeDto> adjustableDaysRuleRangeList;

    @ApiModelProperty("范围类型(0:产品，1:品类，2：全部)")
    private Integer rangeType;

    @ApiModelProperty("产品集合")
    private List<RuleProduct> productList;

    @ApiModelProperty("品类集合")
    private List<RuleCategory> categoryList;

    private String createdBy;

    private String updatedBy;

    private Date updatedTime;

    /**
     * 0为默认规则 1为通用规则
     */
    private Integer isDefault;
}
