package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 仓能力规则详情
 * <AUTHOR>
 * @date 2023/11/19 17:40
 */
@Setter
@Getter
@ToString
@ApiModel("仓能力规则详情")
public class WarehouseRuleVo implements Serializable
{
    private static final long serialVersionUID = 8625322365802739418L;

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 规则名称
     */
    @ApiModelProperty("规则名称")
    private String name;

    /**
     * 范围类型(0:产品，1:品类，2：全部)
     */
    @ApiModelProperty("范围类型(0:产品，1:品类，2：全部)")
    private Integer rangeType;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    @DateTimeFormat(pattern = DateUtils.YMD_DASH)
    private Date startTime;

    /**
     * 失效时间
     */
    @ApiModelProperty("失效时间")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    @DateTimeFormat(pattern = DateUtils.YMD_DASH)
    private Date endTime;

    /**
     * 状态  0： 待生效  1 ：生效中 2： 已失效
     */
    @ApiModelProperty("状态  0： 待生效  1 ：生效中 2： 已失效")
    private Integer status;

    /**
     * 是否永久生效 0:否，1：是
     */
    @ApiModelProperty("是否永久生效 0:否，1：是")
    private Integer foreverFlag;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    @DateTimeFormat(pattern = DateUtils.YMD_DASH)
    private Date createdTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    @DateTimeFormat(pattern = DateUtils.YMD_DASH)
    private Date updatedTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;

    /**
     * 品类名称拼接
     */
    @ApiModelProperty("品类名称拼接")
    private String categoryNames;
    /**
     * 产品名称拼接
     */
    @ApiModelProperty("产品名称拼接")
    private String skuNames;

    /**
     * 容量集合
     */
    @ApiModelProperty("容量集合")
    private List<WarehouseRuleCapacity> capacityList;

    /**
     * 产品集合
     */
    @ApiModelProperty("产品集合")
    private List<RuleProduct> productList;

    /**
     * 品类集合
     */
    @ApiModelProperty("品类集合")
    private List<RuleCategory> categoryList;

    private Integer isDefault;

}
