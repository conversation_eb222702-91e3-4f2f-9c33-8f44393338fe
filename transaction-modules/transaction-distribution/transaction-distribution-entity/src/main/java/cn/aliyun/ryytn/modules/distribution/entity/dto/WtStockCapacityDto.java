package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 阿里库存能力实体
 * <AUTHOR>
 * @date 2023/11/21 17:36
 */
@Setter
@Getter
@ToString
public class WtStockCapacityDto implements Serializable
{
    private static final long serialVersionUID = -1256423256967188562L;

    /**
     * 库存点编码
     */
    private String ruleId;
    /**
     * 库存点编码
     */
    private String stockPointId;
    /**
     * 库存点名称
     */
    private String stockPointName;
    /**
     * 物品编码
     */
    private String itemId;
    /**
     * 物品名称
     */
    private String itemName;
    /**
     * 分组编码
     */
    private String groupId;
    /**
     * 1-库存容量；2-出库能力；3-入库能力
     */
    private Integer type;

    /**
     * 量
     */
    private Double capacity;


    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private Integer status;
}
