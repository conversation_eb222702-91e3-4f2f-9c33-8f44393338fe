package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 产品效期规则Vo
 * <AUTHOR>
 * @date 2023/12/15 15:40
 */
@Setter
@Getter
@ToString
public class SkuValidRuleVo implements Serializable
{
    private static final long serialVersionUID = -4410810357150173607L;
    /**
     * 0:TOB 1:TOC
     */
    private Integer distributeType;

    /**
     * 效期名称
     */
    private String name;

    /**
     * 效期需求占比
     */
    private double ratio;

    /**
     * 品类编码
     */
    private String categoryCode;

    /**
     * 产品编码
     */
    private String skuCode;

    /**
     * 产品编码,逗号分隔
     */
    private String skuCodes;

    /**
     * 效期限制
     */
    private Integer endDay;
}
