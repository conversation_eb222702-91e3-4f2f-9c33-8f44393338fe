package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询库存策略数据详情条件VO
 * <AUTHOR>
 * @date 2023/11/29 13:51
 */
@Setter
@Getter
@ToString
@ApiModel("查询库存策略数据详情条件VO")
public class QueryInventoryDetailConditionVo implements Serializable
{

    private static final long serialVersionUID = -5622253256967188562L;
    @ApiModelProperty("产品编码")
    private String skuCode;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

}
