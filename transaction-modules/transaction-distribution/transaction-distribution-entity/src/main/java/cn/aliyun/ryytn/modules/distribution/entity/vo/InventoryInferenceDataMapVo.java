package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存推演Vo
 * <AUTHOR>
 * @date 2023/12/13 17:30
 */
@Setter
@Getter
@ToString
@ApiModel("库存推演Vo")
public class InventoryInferenceDataMapVo implements Serializable
{
    private static final long serialVersionUID = -5977423256967188562L;

    /**
     * 数据值
     */
    @ApiModelProperty("数据值")
    private Double value;


}
