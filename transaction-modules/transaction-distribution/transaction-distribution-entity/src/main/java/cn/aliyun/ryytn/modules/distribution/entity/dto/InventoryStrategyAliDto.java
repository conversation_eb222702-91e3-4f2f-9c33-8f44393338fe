package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * @Description 库存策略阿里Dto
 * <AUTHOR>
 * @date 2024/1/3 17:52 */
@Setter
@Getter
@ToString
@ApiModel("库存策略阿里Dto")
public class InventoryStrategyAliDto implements Serializable
{
    private static final long serialVersionUID = 1224528165403175090L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    private String warehouseCodes;
    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /**
     * 仓库类别
     */
    @ApiModelProperty("仓库类别")
    private String warehouseType;

    /**
     * 产品编码
     */
    @ApiModelProperty("产品编码")
    private String skuCode;

    private String skuCodes;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String skuName;

    private String skuNames;

    /**
     * 产品简称
     */
    @ApiModelProperty("产品简称")
    private String skuNameSimple;

    /**
     * 安全库存天数
     */
    @ApiModelProperty("安全库存天数")
    private Integer inventorySafeDay;

    /**
     * 安全库存数量
     */
    @ApiModelProperty("安全库存数量")
    private Integer inventorySafeAmount;

    /**
     * 建议安全库存数量
     */
    @ApiModelProperty("建议安全库存数量")
    private Integer inventorySafeAmountAdv;

    /**
     * 周转库存天数
     */
    @ApiModelProperty("周转库存天数")
    private Integer inventoryTurnoverDay;
    /**
     * 周转库存数量
     */
    @ApiModelProperty("周转库存数量")
    private Integer inventoryTurnoverAmount;
    /**
     * 建议目标库存天数
     */
    @ApiModelProperty("建议目标库存天数")
    private Integer inventoryTargetDayAdv;
    /**
     * 建议目标库存数量
     */
    @ApiModelProperty("建议目标库存数量")
    private Integer inventoryTargetAmountAdv;
    /**
     * 目标库存天数
     */
    @ApiModelProperty("目标库存天数")
    private Integer inventoryTargetDay;
    /**
     * 目标库存数量
     */
    @ApiModelProperty("目标库存数量")
    private Integer inventoryTargetAmount;
    /**
     * 日均销量
     */
    @ApiModelProperty("日均销量")
    private Integer salesDaily;
    /**
     * 特殊策略开启标识 0:否 1：是
     */
    @ApiModelProperty("特殊策略开启标识 0:否 1：是")
    private Integer specialStrategyFlag;
    /**
     * 特殊安全库存天数
     */
    @ApiModelProperty("特殊安全库存天数")
    private Integer inventorySafeDaySpec;

    /**
     * 特殊周转库存天数
     */
    @ApiModelProperty("特殊周转库存天数")
    private Integer inventoryTurnoverDaySpec;
    /**
     * 策略生效时间起
     */
    @ApiModelProperty("策略生效时间起")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    @DateTimeFormat(pattern = DateUtils.YMD_DASH)
    private Date startTimeStrategy;
    /**
     * 策略生效时间止
     */
    @ApiModelProperty("策略生效时间止")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    @DateTimeFormat(pattern = DateUtils.YMD_DASH)
    private Date endTimeStrategy;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updatedTime;

    @ApiModelProperty("分组列枚举列表")
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("分组列")
    private String groupColumn;

    @ApiModelProperty("排序列")
    private String sortColumn;

    @ApiModelProperty("动态字段json，格式不固定，看具体sql聚合函数")
    private String data;

    @ApiModelProperty("业务标识集合")
    private List<InventoryStrategyDto> keyList;
}
