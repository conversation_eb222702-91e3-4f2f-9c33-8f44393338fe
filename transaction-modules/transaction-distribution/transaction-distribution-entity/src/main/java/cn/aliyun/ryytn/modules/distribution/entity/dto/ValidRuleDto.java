package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 效期分档规则Dto
 * <AUTHOR>
 * @date 2023/11/14 10:23
 */
@Setter
@Getter
@ToString
@ApiModel("效期分档规则Dto")
public class ValidRuleDto implements Serializable
{
    private static final long serialVersionUID = 4734259644925441386L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 规则名称
     */
    @ApiModelProperty("规则名称")
    private String name;
    /**
     * 范围类型(0:产品，1:品类，2：全部)
     */
    @ApiModelProperty("范围类型(0:产品，1:品类，2：全部)")
    private Integer rangeType;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    private String startTime;
    /**
     * 失效时间
     */
    @ApiModelProperty("失效时间")
    private String endTime;
    /**
     * 状态  0： 待生效  1 ：生效中 2： 已失效
     */
    @ApiModelProperty("状态  0： 待生效  1 ：生效中 2： 已失效")
    private Integer status;
    /**
     * 是否永久生效 0:否，1：是
     */
    @ApiModelProperty("是否永久生效 0:否，1：是")
    private Integer foreverFlag;
    /**
     * 分档类型 0:TOB业务，1:TOC业务
     */
    @ApiModelProperty("分档类型 0:TOB业务，1:TOC业务")
    private Integer distributeType;
    /**
     * 品类名称集合
     */
    @ApiModelProperty("品类名称集合")
    private String categoryNames;
    /**
     * 产品名称集合
     */
    @ApiModelProperty("产品名称集合")
    private String skuNames;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date createdTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = DateUtils.YMDHMS_STD)
    private Date updatedTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;

    /**
     * 0为默认规则 1为通用规则
     */
    private Integer isDefault;

}
