package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询算法管理列表响应Vo
 * <AUTHOR>
 * @date 2023/11/22 9:28
 */
@Setter
@Getter
@ToString
@ApiModel("查询算法管理列表响应Vo")
public class QueryAlgorithmListRspVo implements Serializable
{
    private static final long serialVersionUID = 7680650851875800961L;

    @ApiModelProperty("算法版本code")
    private String algoNameAndVersion;

    @ApiModelProperty("任务id")
    private String algoId;

    @ApiModelProperty("建议类型")
    private String scene;

    @ApiModelProperty("建议粒度")
    private String periodType;

    @ApiModelProperty("建议跨度")
    private String periodPrediction;

    @ApiModelProperty("滚动频次")
    private String modelTrainingPeriod;

    @ApiModelProperty("版本号")
    private String algoVersion;

    @ApiModelProperty("滚动版本数")
    private Integer versionNum;

    @ApiModelProperty("最新版本")
    private String latestVersion;
}
