package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存推演Vo
 * <AUTHOR>
 * @date 2023/12/13 17:30
 */
@Setter
@Getter
@ToString
@ApiModel("库存推演Vo")
public class InventoryInferenceVo implements Serializable
{
    private static final long serialVersionUID = -5958423256967188562L;
    /**
     * 数据类型 1: 调出量汇总(调拨计划) 2:直发需求 3:期初库存
     * 4:生产调入 5:期末库存 6：仓容 7： 未来可供应天数  8：安全库存(天数) 9：目标库存(天数)
     * 10：日分仓需求 11：日分仓调拨需求  12：在途库存  13：计划调入
     */
    @ApiModelProperty("数据类型 1: 调出量汇总(调拨计划) 2:直发需求 3:期初库存\n" +
        "     * 4:生产调入 5:期末库存 6：仓容 7： 未来可供应天数  8：安全库存(天数) 9：目标库存(天数)\n" +
        "     * 10：日分仓需求 11：日分仓调拨需求  12：在途库存  13：计划调入")
    private Integer dataType;

    /**
     * 动态字段数据映射
     */
    @ApiModelProperty("动态字段数据映射")
    private Map<String, InventoryInferenceDataMapVo> dataMap = new HashMap<>();


}
