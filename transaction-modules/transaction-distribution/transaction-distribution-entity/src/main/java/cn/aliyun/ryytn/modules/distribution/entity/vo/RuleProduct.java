package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 适用范围产品-子表
 * <AUTHOR>
 * @date 2023/11/14 16:42
 */
@Setter
@Getter
@ToString
@ApiModel("适用范围产品-子表")
public class RuleProduct implements Serializable
{
    private static final long serialVersionUID = -7788423256967188562L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;
    /**
     * 效期归档主键id
     */
    @ApiModelProperty("效期归档主键id")
    private String ruleId;
    /**
     * 产品编码
     */
    @ApiModelProperty("产品编码")
    private String skuCode;
    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String skuName;

}
