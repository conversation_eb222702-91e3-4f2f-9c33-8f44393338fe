package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 仓能力Vo
 * <AUTHOR>
 * @date 2023/12/28 14:31
 */
@Setter
@Getter
@ToString
public class WarehouseCapacityVo implements Serializable
{
    private static final long serialVersionUID = 8904735978074279049L;

    private String id;

    private String warehouseCode;

    private String warehouseName;

    /**
     * 库容
     */
    private Integer capacity;

    /**
     * 是否开启库容
     */
    private Integer capacityFlag;

    /**
     * 收货能力上限
     */
    private Integer deliveryLimit;

    /**
     * 是否开启收货能力上限
     */
    private Integer deliveryLimitFlag;

    /**
     * 是否开启无限收货能力
     */
    private Integer deliveryUnlimitFlag;

    /**
     * 出库能力上限
     */
    private Integer shipmentLimit;

    /**
     * 是否开启出库能力上限
     */
    private Integer shipmentLimitFlag;

    /**
     * 是否开启无限出库能力
     */
    private Integer shipmentUnlimitFlag;

    private String skuCode;

    private String skuName;

    private String skuCodes;
}
