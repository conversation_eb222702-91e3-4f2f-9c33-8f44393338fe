package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 仓能力表
 * <AUTHOR>
 * @date 2023/12/28 17:43
 */
@Setter
@Getter
@ToString
public class SoWtStockCapacityDto implements Serializable
{
    private static final long serialVersionUID = 1232527208569273458L;

    private String ruleId;

    private String stockPointId;

    private String stockPointName;

    private String itemId;

    private String itemName;

    private String groupId;

    private Integer type;

    private Double capacity;

    private String remark;

    private Integer status;
}
