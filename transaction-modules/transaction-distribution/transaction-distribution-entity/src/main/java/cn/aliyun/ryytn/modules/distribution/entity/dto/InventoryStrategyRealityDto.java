package cn.aliyun.ryytn.modules.distribution.entity.dto;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024-09-05 14:26
 * @Description
 */
@Setter
@Getter
@ToString
@ApiModel("日均销量（实际）Dto")
public class InventoryStrategyRealityDto implements Serializable{


    private static final long serialVersionUID = 8538869802381273566L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    /**
     * 产品编码
     */
    @ApiModelProperty("产品编码")
    private String skuCode;

    /**
     * 日均销量(实际)
     */
    @ApiModelProperty("日均销量(实际)")
    private Integer salesDailyReality;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 创建人
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private String updatedTime;

}
