package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存策略配置
 * <AUTHOR>
 * @date 2024/4/17 14:08
 */
@Setter
@Getter
@ToString
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel("库存策略配置")
public class GraphKcclConfigDto implements Serializable
{
    private static final long serialVersionUID = -5960779494330032320L;
    @ApiModelProperty("编号")
    private String id;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("修改人")
    private String lastModifier;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("修改时间")
    private Date gmtModify;

    @ApiModelProperty("产品编号")
    private String skuCode;

    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    @ApiModelProperty("安全库存天数")
    private Integer safetyDays;

    @ApiModelProperty("周转库存天数")
    private Integer turnoverDays;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("起始生效时间,yyyy-MM-dd")
    private String effStartTime;

    @ApiModelProperty("结束生效时间,yyyy-MM-dd")
    private String effEndTime;

    @ApiModelProperty("是否自定义，0：不是特殊策略，1：开启特殊策略")
    private Integer isCustom;
}
