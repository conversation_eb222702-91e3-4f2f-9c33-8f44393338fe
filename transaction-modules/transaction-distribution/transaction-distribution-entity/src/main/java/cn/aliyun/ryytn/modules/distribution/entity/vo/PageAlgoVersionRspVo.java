package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分页查询算法版本响应Vo
 * <AUTHOR>
 * @date 2023/12/25 9:50
 */
@Setter
@Getter
@ToString
@ApiModel("分页查询算法版本响应Vo")
public class PageAlgoVersionRspVo implements Serializable
{
    private static final long serialVersionUID = -6472470607297839477L;

    private Date startTime;

    private String version;
}
