package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 产品多仓比对列表Vo
 * <AUTHOR>
 * @date 2023/12/13 16:50
 */
@Setter
@Getter
@ToString
@ApiModel("产品多仓比对列表Vo")
public class SkuWarehouseCompareVo implements Serializable
{
    private static final long serialVersionUID = -4558423256967188562L;
    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /**
     * 动态字段数据映射
     */
    @ApiModelProperty("动态字段数据映射")
    private Map<String, TransferPlanDataMapVo> dataMap = new HashMap<>();

}
