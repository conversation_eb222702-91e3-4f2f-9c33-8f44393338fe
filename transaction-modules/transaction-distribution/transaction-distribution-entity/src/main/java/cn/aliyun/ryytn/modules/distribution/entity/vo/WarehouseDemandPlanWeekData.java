package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分仓需求计划周数据
 * <AUTHOR>
 * @date 2023/12/4 14:54
 */
@Setter
@Getter
@ToString
public class WarehouseDemandPlanWeekData implements Serializable
{
    private static final long serialVersionUID = -2009749981333047518L;
    /**
     * 周日期   某月某周的第一天,例如20231204
     */
    private String week;

    /**
     * 周数据
     */
    private Double weekValue;
}
