package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日分仓调拨需求Vo
 * <AUTHOR>
 * @date 2023/12/19 9:42
 */
@Setter
@Getter
@ToString
@ApiModel("日分仓调拨需求Vo")
public class DailyWarehouseDemandVo extends DailyWarehouseDemandDto implements Serializable
{
    private static final long serialVersionUID = 4152313063320245633L;

    @ApiModelProperty("需求计划编码")
    private String demandPlanCode;

    @ApiModelProperty("0:TOB业务，1:TOC业务")
    private String receiverType;
}
