package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存预测入参
 * <AUTHOR>
 * @date 2023/12/12 11:23
 */
@Setter
@Getter
@ToString
public class LogisticsArgs implements Serializable
{
    private static final long serialVersionUID = 7648528325342816856L;

    /**
     * 写死 web
     */
    private String invokeType;

    /**
     * 写死 10
     */
    private Integer timeLimit;

    /**
     * 写死为：algo_warehouse_kc_forecast-V0.0  ，因为要通过这个值，展示算法管理模块
     *      或者， 如果这个值需要改变，记得同步修改 ，表：cdop_biz.tdm_bas_algo_model_info_df 的    对应一行的   ，algo_name_and_version列的值
     */
    private String algo_name_and_version;

    /**
     * 自己生成
     */
    private String task_detail_id;

    /**
     * 数据库配置信息
     */
    private String defaultDatabaseInfo;
}
