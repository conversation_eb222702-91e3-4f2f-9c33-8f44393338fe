package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonInclude;

import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 算法输出库存结果
 * <AUTHOR>
 * @date 2024/3/19 11:31
 */
@Setter
@Getter
@ToString
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel("算法输出库存结果")
public class StockFluctuateDto implements Serializable
{
    private static final long serialVersionUID = 6336947300681838760L;
    @ApiModelProperty("编号")
    private String id;

    @ApiModelProperty("产品编号")
    private String itemId;

    @ApiModelProperty("产品名称")
    private String itemName;

    @ApiModelProperty("生产日期")
    private String productionDate;

    @ApiModelProperty("仓库编号（逻辑仓）")
    private String stockPointId;

    @ApiModelProperty("仓库名称")
    private String stockPointName;

    @ApiModelProperty("库存日期")
    private String date;

    @ApiModelProperty("期初库存")
    private Double qty;

    @ApiModelProperty("期末库存")
    private Double tailQty;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("时间戳")
    private String ds;

    @ApiModelProperty("任务编号")
    private String taskDetailId;

    @ApiModelProperty("效期名称")
    private String validName;

    @ApiModelProperty("物理仓编号")
    private String physicalPointId;

    @ApiModelProperty("物理仓名称")
    private String physicalPointName;

    @ApiModelProperty("json数据")
    private String data;

    @ApiModelProperty("行转列数据")
    private Map<String, StockValue> dataMap = new HashMap<>();

    @ApiModelProperty("分组字段列表")
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("分组字段")
    private String groupColumn;

    @ApiModelProperty("产品编号，英文逗号分隔")
    private String itemIds;

    @ApiModelProperty("逻辑仓编号，英文逗号分隔")
    private String stockPointIds;

    @ApiModelProperty("物理仓编号，英文逗号分隔")
    private String physicalPointIds;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        StockFluctuateDto object = (StockFluctuateDto) o;
        return Objects.equals(this.itemId, object.itemId)
            && Objects.equals(this.date, object.date)
            && Objects.equals(this.stockPointId, object.stockPointId)
            && Objects.equals(this.physicalPointId, object.physicalPointId)
            && Objects.equals(this.validName, object.validName)
            && Objects.equals(this.taskDetailId, object.taskDetailId);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((itemId == null) ? 0 : itemId.hashCode());
        result = prime * result + ((date == null) ? 0 : date.hashCode());
        result = prime * result + ((stockPointId == null) ? 0 : stockPointId.hashCode());
        result = prime * result + ((physicalPointId == null) ? 0 : physicalPointId.hashCode());
        result = prime * result + ((validName == null) ? 0 : validName.hashCode());
        result = prime * result + ((taskDetailId == null) ? 0 : taskDetailId.hashCode());
        return result;
    }
}
