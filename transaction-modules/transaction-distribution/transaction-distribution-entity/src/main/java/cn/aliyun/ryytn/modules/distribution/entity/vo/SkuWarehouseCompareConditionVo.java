package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 产品多仓比对列表查询条件
 * <AUTHOR>
 * @date 2023/11/28 10:47
 */
@Setter
@Getter
@ToString
@ApiModel("产品多仓比对列表查询条件Vo")
public class SkuWarehouseCompareConditionVo extends TransferPlanCondition implements Serializable
{
    private static final long serialVersionUID = -7758423256967188562L;
    /**
     * 查询类型 1：日分仓调拨需求 2：日均销量 3：期初库存 4：计划调拨
     *          5：期末库存 6：未来可供应天数
     *          7：安全库存天数 8：目标库存天数
     */
    @ApiModelProperty("查询类型 *  1：日分仓调拨需求 2：日均销量 3：期初库存 4：计划调拨\n" +
        "     *          5：期末库存 6：未来可供应天数\n" +
        "     *          7：安全库存天数 8：目标库存天数")
    private Integer viewType = 1;


}
