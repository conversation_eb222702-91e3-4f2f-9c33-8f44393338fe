package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 可调节天数规则范围
 * <AUTHOR>
 * @date 2023/11/16 14:19
 */
@Setter
@Getter
@ToString
@ApiModel("可调节天数规则范围")
public class AdjustableDaysRuleRangeDto implements Serializable
{
    private static final long serialVersionUID = -6612148391687801944L;
    private String id;

    @ApiModelProperty("规则id")
    private String ruleId;

    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("可调节天数")
    private Integer adjustableDays;
}
