package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存策略配置Dto
 * <AUTHOR>
 * @date 2023/11/20 19:25
 */
@Setter
@Getter
@ToString
@ApiModel("库存策略配置Dto")
public class InventoryStrategyConfDto implements Serializable
{
    private static final long serialVersionUID = -6358423256967188562L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 配置项
     */
    @ApiModelProperty("配置项")
    private String configName;
    /**
     * 配置值
     */
    @ApiModelProperty("配置值")
    private String configValue;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    private Date createdTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    private Date updatedTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;

}
