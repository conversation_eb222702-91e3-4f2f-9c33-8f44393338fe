package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 仓库和sku关系实体
 * <AUTHOR>
 * @date 2023/11/22 16:15
 */
@Setter
@Getter
@ToString
public class WarehouseSkuDto implements Serializable
{
    private static final long serialVersionUID = -5626423256967188562L;

    /**
     * 仓库编码
     */
    private String warehouseCode;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * sku编码
     */
    private String skuCode;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * 生效时间yyyymmdd
     */
    private String beginDate;
    /**
     * 结束时间yyyymmdd
     */
    private String endDate;
    /**
     * 创建时间
     */
    private String gmtCreate;
    /**
     * 修改时间
     */
    private String gmtModified;

    /**
     * 状态
     */
    private String status;
}
