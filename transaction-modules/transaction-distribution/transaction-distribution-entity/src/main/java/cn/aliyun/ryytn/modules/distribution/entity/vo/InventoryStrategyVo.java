package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 库存策略VO
 * <AUTHOR>
 * @date 2023/11/20 19:25
 */
@Setter
@Getter
@ToString
@ApiModel("库存策略VO")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InventoryStrategyVo implements Serializable
{
    private static final long serialVersionUID = -860617904177169095L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;
    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    private String warehouseCodes;
    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    private String warehouseNames;

    /**
     * 仓库类别
     */
    @ApiModelProperty("仓库类别")
    private String warehouseType;

    /**
     * 产品编码
     */
    @ApiModelProperty("产品编码")
    private String skuCode;

    private String skuCodes;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String skuName;

    private String skuNames;

    /**
     * 建议安全库存数量
     */
    @ApiModelProperty("建议安全库存数量")
    private Double safetyQtyAdv;

    /**
     * 建议安全库存数量
     */
    @ApiModelProperty("建议安全库存天数")
    private Integer safetyDaysAdv;
    /**
     * 建议目标库存天数
     */
    @ApiModelProperty("建议目标库存天数")
    private Integer targetDaysAdv;
    /**
     * 建议目标库存数量
     */
    @ApiModelProperty("建议目标库存数量")
    private Double targetQtyAdv;

    @ApiModelProperty("提前期库存天数")
    private Integer leadDaysAdv;

    @ApiModelProperty("提前期库存数量")
    private Double leadQtyAdv;

    /**
     * 安全库存天数
     */
    @ApiModelProperty("安全库存天数")
    private Integer safetyDays;

    /**
     * 安全库存数量
     */
    @ApiModelProperty("安全库存数量")
    private Double safetyQty;

    /**
     * 周转库存天数
     */
    @ApiModelProperty("周转库存天数")
    private Integer turnoverDays;

    /**
     * 周转库存数量
     */
    @ApiModelProperty("周转库存数量")
    private Double turnoverQty;
    /**
     * 周转库存数量
     */
    @ApiModelProperty("周转库存数量")
    private Double dasQty;

    /**
     * 质检库存天数
     */
    @ApiModelProperty("质检库存天数")
    private Integer qcDays;

    /**
     * 质检库存数量
     */
    @ApiModelProperty("质检库存数量")
    private Double qcQty;
    /**
     * 目标库存天数
     */
    @ApiModelProperty("目标库存天数")
    private Integer targetDays;
    /**
     * 目标库存数量
     */
    @ApiModelProperty("目标库存数量")
    private Double targetQty;
    /**
     * 日均销量
     */
    @ApiModelProperty("日均销量")
    private Double dasNum;
    /**
     * 特殊策略开启标识 0:否 1：是
     */
    @ApiModelProperty("特殊策略开启标识 0:否 1：是")
    private Integer isCustom;
    /**
     * 策略生效时间起
     */
    @ApiModelProperty("起始生效时间")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    @DateTimeFormat(pattern = DateUtils.YMD_DASH)
    private String effStartTime;
    /**
     * 策略生效时间止
     */
    @ApiModelProperty("截止生效时间")
    @JsonFormat(pattern = DateUtils.YMD_DASH)
    @DateTimeFormat(pattern = DateUtils.YMD_DASH)
    private String effEndTime;

    /**
     * 是否删除 0：否  1：是
     */
    @ApiModelProperty("是否删除 0：否  1：是")
    private Integer isDelete;

    @ApiModelProperty("观测时间点，yyyy-MM-dd")
    private String nowDate;

    @ApiModelProperty("JSON数据，格式不确定，根据查询sql聚合函数控制")
    private String data;

    @ApiModelProperty("动态数据map")
    private Map<String, InventoryStrategyVo> dataMap = new HashMap<>();
    @ApiModelProperty("分组列枚举列表")
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("分组列")
    private String groupColumn;

    @ApiModelProperty("排序列")
    private String sortColumn;

    @ApiModelProperty("业务标识集合")
    private List<InventoryStrategyVo> keyList;

    @ApiModelProperty("月份，yyyyMM")
    private String month;
    @ApiModelProperty("abc分类")
    private String abcType;


    @ApiModelProperty("自定义日销量")
    private Double dayNumDefine;

    @ApiModelProperty("M0日销")
    private Double outboundNumM0;

    @ApiModelProperty("M1日销")
    private Double outboundNumM1;

    @ApiModelProperty("M2日销")
    private Double outboundNumM2;

    @ApiModelProperty("调拨计划量")
    private Double outboundNumAiplan;

    @ApiModelProperty("日均销量(实际)")
    private Integer salesDailyReality;
    /**
     * 日均销量建议值
     */
    @ApiModelProperty("日均销量建议值")
    private Double dasNumAdvice;
    @ApiModelProperty("操作类型")
    private String operateType;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("更新人")
    private String updatedBy;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        InventoryStrategyVo object = (InventoryStrategyVo) o;
        return Objects.equals(this.skuCode, object.skuCode)
            && Objects.equals(this.warehouseCode, object.warehouseCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((warehouseCode == null) ? 0 : warehouseCode.hashCode());
        return result;
    }
}
