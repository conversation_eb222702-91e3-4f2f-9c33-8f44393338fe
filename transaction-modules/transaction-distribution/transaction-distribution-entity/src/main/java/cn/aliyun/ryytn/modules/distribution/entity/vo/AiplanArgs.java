package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 调拨计划调度任务入参
 * <AUTHOR>
 * @date 2023/12/6 9:57
 */
@Setter
@Getter
@ToString
public class AiplanArgs implements Serializable
{

    private static final long serialVersionUID = 176984628075227582L;
    /**
     * 写死 web
     */
    private String invokeType;

    /**
     * 2023-10-30,以后变动
     */
    private String planStartDate;

    /**
     * 写死 5
     */
    private Integer planLength;

    /**
     * 写死 100
     */
    private Integer weightDemand;

    /**
     * 写死 10
     */
    private Integer weightCost;

    /**
     * 写死 1
     */
    private Integer weightExpiry;

    /**
     * 写死 10
     */
    private Integer timeLimit;

    /**
     * 写死为：algo_warehouse_AIPlan_day_forecast-V0.0  ，因为要通过这个值，展示算法管理模块
     *      或者， 如果这个值需要改变，记得同步修改 ，表：cdop_biz.tdm_bas_algo_model_info_df 的    对应一行的   ，algo_name_and_version列的值
     */
    private String algo_name_and_version;

    /**
     * 自己生成
     */
    private String task_detail_id;

    /**
     * 需求计划编号
     */
    private String demand_plan_code;

    /**
     * 需求计划名称
     */
    private String demand_plan_name;

    /**
     * 需求计划版本编号
     */
    private String version_id;

    /**
     * 数据库配置信息
     */
    private String defaultDatabaseInfo;
}
