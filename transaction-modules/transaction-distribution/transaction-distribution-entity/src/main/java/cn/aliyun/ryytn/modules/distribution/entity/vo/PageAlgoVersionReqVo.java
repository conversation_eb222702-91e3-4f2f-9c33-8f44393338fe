package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分页查询算法版本请求Vo
 * <AUTHOR>
 * @date 2023/12/25 9:48
 */
@Setter
@Getter
@ToString
@ApiModel("分页查询算法版本请求Vo")
public class PageAlgoVersionReqVo implements Serializable
{
    private static final long serialVersionUID = -2758544580584226687L;

    @ApiModelProperty("算法名称和版本")
    private String algoNameAndVersion;
}
