package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonInclude;

import cn.aliyun.ryytn.modules.demand.constant.GroupColumnEnum;
import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日分仓调拨需求
 * <AUTHOR>
 * @date 2023/12/15 17:12
 */
@Setter
@Getter
@ToString
@ApiModel("日分仓调拨需求")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DailyWarehouseAiplanDemandDto implements Serializable
{
    private static final long serialVersionUID = 1953327339574524005L;
    @ExcelProperty(value = {"#"}, index = 0)
    private Integer rowId;

    @ExcelIgnore
    private String id;

    @ExcelIgnore
    private String demandPlanCode;

    @ExcelIgnore
    private String demandPlanName;

    @ExcelIgnore
    private String demandPlanVersion;

    @ExcelIgnore
    private String aiplanDemandVersion;

    @ExcelProperty(value = {"产品编码"}, index = 4)
    @ColumnWidth(15)
    private String skuCode;

    @ExcelIgnore
    private String skuCodes;

    @ExcelProperty(value = {"产品简称"}, index = 5)
    @ColumnWidth(25)
    private String skuName;

    @ExcelIgnore
    private Integer distributeType;

    @ExcelIgnore
    private String distributeTypes;

    @ExcelProperty(value = {"渠道名称"}, index = 7)
    private String distributeTypeName;

    @ExcelIgnore
    private String warehouseCode;

    @ExcelIgnore
    private String warehouseCodes;

    @ExcelProperty(value = {"仓库名称"}, index = 6)
    @ColumnWidth(25)
    private String warehouseName;

    @ExcelIgnore
    private String lv1CategoryCode;

    @ExcelProperty(value = {"产品分类"}, index = 1)
    @ColumnWidth(15)
    private String lv1CategoryName;

    @ExcelIgnore
    private String lv2CategoryCode;

    @ExcelProperty(value = {"产品大类"}, index = 2)
    @ColumnWidth(15)
    private String lv2CategoryName;

    @ExcelIgnore
    private String lv3CategoryCode;

    @ExcelProperty(value = {"产品小类"}, index = 3)
    @ColumnWidth(25)
    private String lv3CategoryName;

    @ExcelProperty(value = {"效期规则"}, index = 8)
    @ColumnWidth(15)
    private String validityPeriod;

    @ExcelIgnore
    private String validityPeriods;

    @ExcelProperty(value = {"出货日"}, index = 9)
    @ColumnWidth(10)
    private String dateRecorded;

    @ExcelProperty(value = {"数量"}, index = 10)
    private double dateValue;

    @ExcelProperty(value = {"ABC分类"}, index = 11)
    private String abcType;

    @ExcelIgnore
    private Integer endDay;

    @ApiModelProperty("分组列枚举列表")
    @ExcelIgnore
    private List<GroupColumnEnum> groupColumnList;

    @ApiModelProperty("分组列")
    @ExcelIgnore
    private String groupColumn;

    @ApiModelProperty("排序列")
    @ExcelIgnore
    private String sortColumn;

    @ApiModelProperty("动态字段json，格式不固定，看具体sql聚合函数")
    @ExcelIgnore
    private String data;

    @ApiModelProperty("业务标识集合")
    @ExcelIgnore
    private List<DailyWarehouseAiplanDemandDto> keyList;

    @ApiModelProperty("一级品类编号，英文逗号分隔")
    @ExcelIgnore
    private String lv1CategoryCodes;

    @ApiModelProperty("二级品类编号，英文逗号分隔")
    @ExcelIgnore
    private String lv2CategoryCodes;

    @ApiModelProperty("三级品类编号，英文逗号分隔")
    @ExcelIgnore
    private String lv3CategoryCodes;

    @ApiModelProperty("动态字段数据映射")
    @ExcelIgnore
    private Map<String, PlanValue> dataMap = new HashMap<>();

    @ApiModelProperty("分表后缀")
    @ExcelIgnore
    private String tableSuffix;

    @ApiModelProperty("开始日期")
    @ExcelIgnore
    private String beginDate;

    @ApiModelProperty("结束日期")
    @ExcelIgnore
    private String endDate;

    @ApiModelProperty("状态：1：已生成，2：生成中")
    @ExcelIgnore
    private Integer status;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        DailyWarehouseAiplanDemandDto that = (DailyWarehouseAiplanDemandDto) o;
        return Objects.equals(skuCode, that.skuCode) && Objects.equals(warehouseCode, that.warehouseCode) && Objects.equals(dateRecorded, that.dateRecorded) &&
            Objects.equals(endDay, that.endDay);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(skuCode, warehouseCode, dateRecorded, endDay);
    }
}
