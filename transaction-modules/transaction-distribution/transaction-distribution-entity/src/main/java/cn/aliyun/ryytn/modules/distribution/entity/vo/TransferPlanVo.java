package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 调拨计划Vo
 * <AUTHOR>
 * @date 2023/12/12 17:54
 */
@Setter
@Getter
@ToString
@ApiModel("调拨计划Vo")
public class TransferPlanVo implements Serializable
{
    private static final long serialVersionUID = -8558423256967188562L;

    /**
     * 需求计划
     */
    @ApiModelProperty("需求计划")
    private String demandPlan;

    /**
     * 需求计划版本
     */
    @ApiModelProperty("需求计划版本")
    private String demandPlanVersion;

    /**
     * 产品编码
     */
    @ApiModelProperty("生产编码")
    private String skuCode;

    /**
     * 产品简称
     */
    @ApiModelProperty("产品简称")
    private String skuName;

    /**
     * 调入仓库编码
     */
    @ApiModelProperty("调入仓库编码")
    private String warehouseCodeIn;
    /**
     * 调入仓库名称
     */
    @ApiModelProperty("调入仓库名称")
    private String warehouseNameIn;

    /**
     * 调出仓库编码
     */
    @ApiModelProperty("调出仓库编码")
    private String warehouseCodeOut;

    /**
     * 调出仓库名称
     */
    @ApiModelProperty("调出仓库名称")
    private String warehouseNameOut;

    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /**
     * 产品效期类型   1 ：新鲜效期  2 ：常规效期
     */
    @ApiModelProperty("效期规则")
    private String validType;

    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String transType;


    /**
     * 动态字段数据映射
     */
    @ApiModelProperty("动态字段数据映射")
    private Map<String, TransferPlanDataMapVo> dataMap = new HashMap<>();

}
