package cn.aliyun.ryytn.modules.distribution.entity.dto;

import java.io.Serializable;
import java.util.Objects;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 日均销量视图
 * <AUTHOR>
 * @date 2023/12/28 9:31
 */
@Setter
@Getter
@ToString
public class DailySalesView implements Serializable
{

    private static final long serialVersionUID = -4871447721605105255L;

    private String bizWarehouseCode;

    private String skuCode;

    private Double outboundNum;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        DailySalesView that = (DailySalesView) o;
        return Objects.equals(bizWarehouseCode, that.bizWarehouseCode) && Objects.equals(skuCode, that.skuCode);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(bizWarehouseCode, skuCode);
    }
}
