package cn.aliyun.ryytn.modules.distribution.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 物理仓库Vo
 * <AUTHOR>
 * @date 2023年12月14日 16:25
 */
@Setter
@Getter
@ToString
@ApiModel("物理仓库Vo")
public class PhysicWarehouseVo implements Serializable
{
    private static final long serialVersionUID = 2965239342728255657L;

    @ApiModelProperty("物理仓库编码")
    private String warehouseCode;
    @ApiModelProperty("物理仓库名称")
    private String warehouseName;
    @ApiModelProperty("仓库类型编号")
    private String warehouseTypeCode;

}
