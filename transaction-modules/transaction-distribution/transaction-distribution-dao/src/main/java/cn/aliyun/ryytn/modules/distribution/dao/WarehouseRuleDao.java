package cn.aliyun.ryytn.modules.distribution.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtStockCapacityDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.WtStockCapacityDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PhysicWarehouseVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct;
import cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseCapacityVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleCapacity;
import cn.aliyun.ryytn.modules.distribution.entity.vo.WarehouseRuleVo;

/**
 * @Description 仓能力规则Dao
 * <AUTHOR>
 * @date 2023/11/19 17:18
 */
public interface WarehouseRuleDao
{

    /**
     *
     * @Description 查询仓能力规则列表
     * @param warehouseRuleDto
     * @return List<WarehouseRuleDto>
     * <AUTHOR>
     * @date 2023/11/19 17:18
     */
    List<WarehouseRuleDto> queryWarehoueRuleList(WarehouseRuleDto warehouseRuleDto);

    /**
     *
     * @Description 查询仓能力规则详情
     * @param id
     * @return WarehouseRuleVo
     * <AUTHOR>
     * @date 2023/11/19 17:18
     */
    WarehouseRuleVo queryWarehouseRuleDetail(@Param("id") String id);

    /**
     *
     * @Description 删除仓能力规则
     * @param id
     * <AUTHOR>
     * @date 2023/11/19 17:18
     */
    void deleteWarehouseRuleById(@Param("id") String id);

    /**
     *
     * @Description 删除仓能力规则范围列表数据
     * @param ruleId
     * <AUTHOR>
     * @date 2023/11/19 17:18
     */
    void deleteWarehouseRuleRangeByRuleId(@Param("ruleId") String ruleId);

    /**
     *
     * @Description 删除仓能力规则产品列表数据
     * @param ruleId
     * <AUTHOR>
     * @date 2023/11/19 17:18
     */
    void deleteWarehouseRuleProductByRuleId(@Param("ruleId") String ruleId);

    /**
     *
     * @Description 删除仓能力规则品类列表数据
     * @param ruleId
     * <AUTHOR>
     * @date 2023/11/19 17:18
     */
    void deleteWarehouseRuleCategoryByRuleId(@Param("ruleId") String ruleId);

    /**
     *
     * @Description 查询品类表中skuCodes
     * <AUTHOR>
     * @date 2023/11/19 17:18
     */
    List<Map<String, String>> querySkuCodeList();

    /**
     *
     * @Description 查询品类表中skuCodes
     * <AUTHOR>
     * @date 2023/11/19 17:18
     */
    List<Map<String, String>> queryCategorySkuCodeList();

    /**
     * 修改仓能力规则主表
     * @param warehouseRuleVo
     */
    void updateWarehouseRule(WarehouseRuleVo warehouseRuleVo);

    /**
     * 新增仓能力规则主表
     * @param warehouseRuleVo
     */
    void insertWarehouseRule(WarehouseRuleVo warehouseRuleVo);


    /**
     * 批量新增范围列表
     * @param capacityList
     */
    void insertWarehouseRuleRangeList(List<WarehouseRuleCapacity> capacityList);

    /**
     * 批量新增产品列表
     * @param productList
     */
    void insertWarehouseRuleProductList(List<RuleProduct> productList);

    /**
     * 批量新增品类列表
     * @param categoryList
     */
    void insertWarehouseRuleCategoryList(List<RuleCategory> categoryList);

    /**
     * 批量新增品类列表
     * @param ruleId
     */
    void deleteStockCapacity(@Param("ruleId") String ruleId);

    /**
     * 批量新增品类列表
     * @param wtStockCapacityDtos
     */
    void insertStockCapacity(List<WtStockCapacityDto> wtStockCapacityDtos);

    /**
     * 更新库存能力状态
     */
    void updateStockCapacityStatus();

    /**
     * 查询仓容
     * @param warehouseCode
     * @param skuCode
     * @return
     */
    Double queryCapacityByCode(@Param("warehouseCode") String warehouseCode, @Param("skuCode") String skuCode);

    void deleteSoWtStockCapacity();

    int countWarehouseRuleName(WarehouseRuleVo warehouseRuleVo);

    List<WarehouseCapacityVo> queryWarehouseCapacityByProduct();

    List<WarehouseCapacityVo> queryWarehouseCapacityByCategory();

    List<WarehouseCapacityVo> queryWarehouseCapacityByGeneralRule();

    void addSoWtStockCapacity(List<SoWtStockCapacityDto> addList);

    List<PhysicWarehouseVo> queryWarehouseRuleWarehouse();

    List<PhysicWarehouseVo> queryAdjustableDaysRuleWarehouse();

    /**
     *
     * @Description 根据产品查询仓容
     * @param warehouseCode
     * @param skuCode
     * @return Long
     * <AUTHOR>
     * @date 2024年03月19日 17:26
     */
    Long queryCapacityBySku(@Param("warehouseCode") String warehouseCode, @Param("skuCode") String skuCode);

    /**
     *
     * @Description 根据品类查询仓容
     * @param warehouseCode
     * @param lv3CategoryCode
     * @return Long
     * <AUTHOR>
     * @date 2024年03月19日 17:34
     */
    Long queryCapacityByCategory(@Param("warehouseCode") String warehouseCode, @Param("lv3CategoryCode") String lv3CategoryCode);

    /**
     *
     * @Description 查询通用仓容
     * @param warehouseCode
     * @return Long
     * <AUTHOR>
     * @date 2024年03月19日 17:35
     */
    Long queryDefaultCapacity(String warehouseCode);
}
