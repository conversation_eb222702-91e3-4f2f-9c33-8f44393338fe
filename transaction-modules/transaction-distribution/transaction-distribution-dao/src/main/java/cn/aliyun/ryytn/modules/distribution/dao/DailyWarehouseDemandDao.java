package cn.aliyun.ryytn.modules.distribution.dao;

import java.util.List;
import java.util.Map;

import cn.aliyun.ryytn.modules.demand.entity.vo.DailyWarehouseDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseAiplanDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.DailyWarehouseDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseAiplanDemandRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.DailyWarehouseDemandDateVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.GenerateDailyWarehouseAiplanDemandVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryDailyWarehouseDemandListReqVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SkuValidRuleVo;

/**
 * @Description 日分仓需求Dao层
 * <AUTHOR>
 * @date 2023/12/4 14:24
 */
public interface DailyWarehouseDemandDao
{
    /**
     *
     * @Description 查询日分仓需求计划是否存在
     * @param queryDailyWarehouseDemandListReqVo
     * @return int
     * <AUTHOR>
     * @date 2024年01月23日 10:49
     */
    int queryDailyWarehouseDemandPlanExists(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo);

    /**
     *
     * @Description 查询日分仓需求计划列表
     * @return List<DailyWarehouseDemandDto>
     * <AUTHOR>
     * @date 2024年01月17日 16:20
     */
    List<DailyWarehouseDemandDto> queryDailyWarehouseDemandPlanList();


    /**
     * 查询需要重新刷新的日分仓需求任务
     * @return
     */
    List<DailyWarehouseDemandDto> queryRefreshDailyDemandPlanList();

    /**
     *
     * @Description 查询日分仓需求版本列表
     * @param dailyWarehouseDemandDto
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月17日 16:19
     */
    List<String> queryDailyWarehouseDemandVersionList(DailyWarehouseDemandDto dailyWarehouseDemandDto);

    void createDailyWarehousePartitionTable(List<String> partitionList);

    void createDailyWarehouseAiplanPartitionTable(List<String> partitionList);

    int batchAddDailyWarehouseDemandList(DailyWarehouseDemandDto dailyWarehouseDemandDto);

    int addDailyWarehouseDemandList(List<DailyWarehouseDemandDto> dailyWarehouseDemandDtoList) throws Exception;

    List<DailyWarehouseDemandDto> queryDailyWarehouseDemandList(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo);

    List<SkuValidRuleVo> queryValidRuleByProduct();

    List<SkuValidRuleVo> queryValidRuleByCategory();

    List<SkuValidRuleVo> queryValidGeneralRule();

    Integer batchAddDailyWarehouseAiplanDemandList(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto);

    void addDailyWarehouseAiplanDemandList(List<DailyWarehouseAiplanDemandDto> dailyWarehouseAiplanDemandDtoList);

    /**
     *
     * @Description 查询当日已有版本号
     * @param generateDailyWarehouseAiplanDemandVo
     * @return List<String>
     * <AUTHOR>
     * @date 2024年03月20日 9:45
     */
    List<String> queryDailyWarehouseAiplanDemandVersionList(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo);

    int countDailyWarehouseAiplanDemandVersion(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo);

    List<DailyWarehouseAiplanDemandDto> queryDailyWarehouseAiplanDemandList(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto);

    /**
     *
     * @Description 查询日分仓调拨需求计划下拉列表
     * @return List<DailyWarehouseAiplanDemandDto>
     * <AUTHOR>
     * @date 2024年01月17日 17:11
     */
    List<DailyWarehouseAiplanDemandDto> queryDailyWarehouseAiplanDemandPlanList() throws Exception;

    /**
     *
     * @Description 查询日分仓调拨需求计划版本下拉列表
     * @param dailyWarehouseAiplanDemandDto
     * @return List<String>
     * <AUTHOR>
     * @date 2024年01月17日 17:11
     */
    List<String> queryDailyWarehouseAiplanDemandPlanVersionList(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto);

    List<String> queryAiPlanDemandVersion(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo);

    Integer batchAddAiplanAlgoList(SoWtDemandDto soWtDemandDto);

    void addAiplanAlgoList(List<SoWtDemandDto> soWtDemandDtoList);

    Integer batchUpdateDailyWarehouseWeekData(DailyWarehouseDemandDto dailyWarehouseDemandDto);

    void updateDailyWarehouseWeekData(List<DailyWarehouseDemandDto> dailyWarehouseDemandDtoList);

    Integer batchUpdateDailyWarehouseDateData(DailyWarehouseDemandDateVo dailyWarehouseDemandDateVo);

    void updateDailyWarehouseDateData(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo);

    List<DailyWarehouseDemandDto> queryDailyWarehouseDemandListById(List<String> idList);

    void deleteDailyWarehouseDemand(String demandPlanCode);

    void deleteDailyWarehouseAiPlanDemand(String demandPlanCode);

    void deleteAiplanAlgoList(String demandPlanCode);

    /**
     *
     * @Description List<SoWtDemandDto>
     * @return 查询调拨计划算法中间表版本
     * <AUTHOR>
     * @date 2023年12月29日 15:39
     */
    List<SoWtDemandDto> querySoWtDemandVersion();

    /**
     *
     * @Description 查询调拨计划中间表列表
     * @param soWtDemandDto
     * @return List<SoWtDemandDto>
     * <AUTHOR>
     * @date 2024年03月12日 15:58
     */
    List<SoWtDemandDto> querySoWtDemandList(SoWtDemandDto soWtDemandDto);

    /**
     *
     * @Description 分组聚合查询日分仓需求编辑列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return List<DailyWarehouseDemandRspVo>
     * <AUTHOR>
     * @date 2023年12月27日 17:22
     */
    List<DailyWarehouseDemandRspVo> queryDailyWarehouseDemandListGroupBy(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo);

    /**
     *
     * @Description 查询日分仓需求编辑数据标识列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return List<DailyWarehouseDemandRspVo>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<DailyWarehouseDemandRspVo> queryDailyWarehouseDemandDataKeyList(
        QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo);

    /**
     *
     * @Description 查询日分仓需求编辑数据动态数据json列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return List<DailyWarehouseAiplanDemandRspVo>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<DailyWarehouseDemandRspVo> queryDailyWarehouseDemandDataJsonList(
        QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo);

    /**
     *
     * @Description 分组聚合查询日分仓调拨需求列表
     * @param queryDailyWarehouseAiplanDemandListReqVo
     * @return List<DailyWarehouseAiplanDemandRspVo>
     * <AUTHOR>
     * @date 2023年12月27日 17:30
     */
    List<DailyWarehouseAiplanDemandRspVo> queryDailyWarehouseAiplanDemandListGroupBy(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto);

    /**
     *
     * @Description 查询日分仓调拨需求数据标识列表
     * @param dailyWarehouseAiplanDemandDto
     * @return List<DailyWarehouseAiplanDemandDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<DailyWarehouseAiplanDemandDto> queryDailyWarehouseAiplanDemandDataKeyList(
        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto);

    /**
     *
     * @Description 查询日分仓调拨需求数据动态数据json列表
     * @param dailyWarehouseAiplanDemandDto
     * @return List<DailyWarehouseAiplanDemandDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<DailyWarehouseAiplanDemandDto> queryDailyWarehouseAiplanDemandDataJsonList(
        DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto);

    /**
     *
     * @Description 查询日分仓调拨需求动态表头
     * @param dailyWarehouseAiplanDemandDto
     * @return
     * <AUTHOR>
     * @date 2024年01月03日 14:20     */
    List<String> queryDailyWarehouseAiplanDemandHeadList(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto);

    /**
     *
     * @Description 查询日分仓调拨需求表头下拉列表
     * @param dailyWarehouseAiplanDemandDto
     * @return
     * <AUTHOR>
     * @date 2024年01月03日 14:20     */
    List<DailyWarehouseAiplanDemandDto> queryDailyWarehouseDemandAiplanHeadSelect(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto);

    /**
     *
     * @Description 查询日分仓需求计划动态表头
     * @param queryDailyWarehouseDemandListReqVo
     * @return List<DailyWarehouseDemandDto>
     * <AUTHOR>
     * @date 2024年01月17日 16:56
     */
    List<DailyWarehouseDemandDto> queryDailyWarehouseDemandHeadList(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo);

    /**
     *
     * @Description 查询日分仓需求计划表头下拉列表
     * @param queryDailyWarehouseDemandListReqVo
     * @return List<DailyWarehouseDemandDto>
     * <AUTHOR>
     * @date 2024年01月17日 16:47
     */
    List<DailyWarehouseDemandDto> queryDailyWarehouseDemandHeadSelect(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo);

    /**
     *
     * @Description 根据仓库分组查询需求汇总
     * @param dailyWarehouseAiplanDemandDto
     * @return List<DailyWarehouseAiplanDemandDto>
     * <AUTHOR>
     * @date 2024年03月20日 18:32
     */
    List<DailyWarehouseAiplanDemandDto> querySumGroupByWarehouse(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto);

    /**
     *
     * @Description 根据仓库+生产编号分组查询需求汇总
     * @param dailyWarehouseAiplanDemandDto
     * @return List<DailyWarehouseAiplanDemandDto>
     * <AUTHOR>
     * @date 2024年03月20日 18:32
     */
    List<DailyWarehouseAiplanDemandDto> querySumGroupByWarehouseProductionSku(DailyWarehouseAiplanDemandDto dailyWarehouseAiplanDemandDto);

    /**
     *
     * @Description 删除指定计划版本状态为生成中的日分仓编辑数据
     * @param queryDailyWarehouseDemandListReqVo
     * <AUTHOR>
     * @date 2024年04月12日 14:16
     */
    void deleteDailyWarehouseDemandIniting(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo);


    /**
     * 重新生成日分仓时,需要删除之前的版本再生成
     * @param generateDailyWarehouseAiplanDemandVo
     */
    void deleteDailyWarehouse (QueryDailyWarehouseDemandListReqVo generateDailyWarehouseAiplanDemandVo);

    /**
     * 查询已经生成的日分仓需求时间区间,服务于重新生成
     * @param generateDailyWarehouseAiplanDemandVo
     * @return
     */
    List<Map<String,String>> queryDailyWareHouseDateRange(QueryDailyWarehouseDemandListReqVo generateDailyWarehouseAiplanDemandVo);

    /**
     *
     * @Description 修改指定计划版本的日分仓编辑数据状态为正常
     * @param queryDailyWarehouseDemandListReqVo
     * <AUTHOR>
     * @date 2024年04月12日 14:30
     */
    void udpateDailyWarehouseDemandNormal(QueryDailyWarehouseDemandListReqVo queryDailyWarehouseDemandListReqVo);

    /**
     *
     * @Description 删除指定计划版本状态为生成中的日分仓调拨数据
     * @param generateDailyWarehouseAiplanDemandVo
     * <AUTHOR>
     * @date 2024年04月12日 15:07
     */
    void deleteDailyWarehouseAiplanDemandIniting(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo);

    /**
     *
     * @Description 修改指定计划版本的日分仓调拨数据状态为正常
     * @param generateDailyWarehouseAiplanDemandVo
     * <AUTHOR>
     * @date 2024年04月12日 15:07
     */
    void udpateDailyWarehouseAiplanDemandNormal(GenerateDailyWarehouseAiplanDemandVo generateDailyWarehouseAiplanDemandVo);
}
