package cn.aliyun.ryytn.modules.distribution.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.ValidRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct;
import cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleRange;
import cn.aliyun.ryytn.modules.distribution.entity.vo.ValidRuleVo;

/**
 * @Description 效期分档规则Dao
 * <AUTHOR>
 * @date 2023/11/14 10:17
 */
public interface ValidRuleDao
{

    /**
     *
     * @Description 查询效期分档规则列表
     * @param validRuleDto
     * @return List<ValidRuleVo>
     * <AUTHOR>
     * @date 2023/11/14 10:17
     */
    List<ValidRuleDto> queryValidRuleList(ValidRuleDto validRuleDto);

    /**
     *
     * @Description 查询效期分档规则详情
     * @param id
     * @return ValidRuleVo
     * <AUTHOR>
     * @date 2023/11/16 15:13
     */
    ValidRuleVo queryValidRuleDetail(@Param("id") String id);

    /**
     *
     * @Description 删除效期分档规则
     * @param id
     * <AUTHOR>
     * @date 2023/11/16 15:14
     */
    void deleteValidRuleById(@Param("id") String id);

    /**
     *
     * @Description 删除效期分档规则范围列表数据
     * @param ruleId
     * <AUTHOR>
     * @date 2023/11/17 13:14
     */
    void deleteValidRuleRangeByRuleId(@Param("ruleId") String ruleId);

    /**
     *
     * @Description 删除效期分档规则产品列表数据
     * @param ruleId
     * <AUTHOR>
     * @date 2023/11/17 13:14
     */
    void deleteValidRuleProductByRuleId(@Param("ruleId") String ruleId);

    /**
     *
     * @Description 删除效期分档规则品类列表数据
     * @param ruleId
     * <AUTHOR>
     * @date 2023/11/17 13:14
     */
    void deleteValidRuleCategoryByRuleId(@Param("ruleId") String ruleId);

    /**
     *
     * @Description 查询产品是否存在
     * @param skuList
     * <AUTHOR>
     * @date 2023/11/17 13:14
     */
    Integer checkSkuIfExist(@Param("skuList") List<String> skuList, @Param("distributeType") Integer distributeType);

    /**
     *
     * @Description 查询产品表中skuCodes
     * <AUTHOR>
     * @date 2023/11/17 13:14
     */
    List<String> queryCategorySkuCodeList(@Param("distributeType") Integer distributeType);

    /**
     * 修改效期分档规则主表
     * @param validRuleVo
     */
    void updateValidRule(ValidRuleVo validRuleVo);

    /**
     * 新增效期分档规则主表
     * @param validRuleVo
     */
    void insertValidRule(ValidRuleVo validRuleVo);


    /**
     * 批量新增范围列表
     * @param rangeList
     */
    void insertValidRuleRangeList(List<ValidRuleRange> rangeList);

    /**
     * 批量新增产品列表
     * @param productList
     */
    void insertValidRuleProductList(List<RuleProduct> productList);

    /**
     * 批量新增品类列表
     * @param categoryList
     */
    void insertValidRuleCategoryList(List<RuleCategory> categoryList);


    int countValidRuleName(ValidRuleVo validRuleVo);

    /**
     *
     * @Description 查询效期规则名称
     * @param freightPlanDto
     * @return String
     * <AUTHOR>
     * @date 2023年12月29日 19:48
     */
    String queryValidRuleName(FreightPlanDto freightPlanDto);
}
