package cn.aliyun.ryytn.modules.distribution.dao;

import java.util.List;

import cn.aliyun.ryytn.modules.distribution.entity.dto.DailySalesView;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyConfDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyRealityDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryStrategyDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoSsAverageDemandDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoSsServiceLevelDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.SoWtSafetyStockDto;

import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo;

/**
 * @Description 库存策略配置Dao
 * <AUTHOR>
 * @date 2023/11/20 20:06
 */
public interface InventoryStrategyDao
{

    /**
     *
     * @Description 查询库存策略配置列表
     * @return List<InventoryStrategyConfDto>
     * <AUTHOR>
     * @date 023/11/20 20:06
     */
    List<InventoryStrategyConfDto> queryInventoryStrategyConfList();

    /**
     *
     * @Description 查询库存策略配置列表
     * @param  inventoryStrategyDto
     * @return List<InventoryStrategyDto>
     * <AUTHOR>
     * @date 023/11/20 20:06
     */
    List<InventoryStrategyDto> queryInventoryStrategyList(InventoryStrategyDto inventoryStrategyDto);

    /**
     *
     * @Description 查询库存策略配置详情
     * @param inventoryStrategyConfDto
     * <AUTHOR>
     * @date 023/11/20 20:06
     */
    void updateInventoryStrategyConf(InventoryStrategyConfDto inventoryStrategyConfDto);


    /**
     * 删除库存策略
     */
    void deleteInventoryStrategy();

    /**
     * 查询库存策略详情
     * @param id
     */
    InventoryStrategyDto queryInventoryStrategyDetail(@Param("id") String id);

    /**
     * 编辑库存策略
     * @param inventoryStrategyDto
     */
    void updateInventoryStrategy(InventoryStrategyDto inventoryStrategyDto);

    /**
     * 批量新增库存策略Dto
     * @param inventoryStrategyDtoList
     */
    void insertInventoryStrategyList(List<InventoryStrategyDto> inventoryStrategyDtoList);

    /**
     * 查询日均销量实际
     * @param inventoryStrategyRealityDto
     */
    InventoryStrategyRealityDto queryStrategyReality(InventoryStrategyRealityDto inventoryStrategyRealityDto);

    /**
     * 更新日均销量实际
     * @param inventoryStrategyRealityDto
     */
    void updateStrategyReality(InventoryStrategyRealityDto inventoryStrategyRealityDto);

    /**
     * 新增日均销量实际
     * @param inventoryStrategyRealityDto
     */
    void insertStrategyReality(InventoryStrategyRealityDto inventoryStrategyRealityDto);

    /**
     * 删除日均销量实际
     * @param inventoryStrategyRealityDto
     */
    void deleteStrategyReality(InventoryStrategyRealityDto inventoryStrategyRealityDto);

    /**
     * 清空服务水平表
     */
    void deleteAllServiceLevel();

    /**
     * 清空日均需求表
     */
    void deleteAllAverageDemand();

    /**
     * 清空安全库存
     */
    void deleteAllSafetyStock();

    /**
     * 批量新增服务水平Dto
     * @param serviceLevelDtos
     */
    void insertServiceLevelList(List<SoSsServiceLevelDto> serviceLevelDtos);

    /**
     * 批量新增日均需求Dto
     * @param averageDemandDtos
     */
    void insertAverageDemandList(List<SoSsAverageDemandDto> averageDemandDtos);

    /**
     * 批量新增安全库存Dto
     * @param safetyStockDtos
     */
    void insertSafetyStockList(List<SoWtSafetyStockDto> safetyStockDtos);


    List<DailySalesView> selectDailySalesView();

    List<InventoryStrategyVo> queryInventoryStrategyHeadSelect(InventoryStrategyVo inventoryStrategyVo);

    List<InventoryStrategyVo> queryInventoryStrategyViewDataKeyList(InventoryStrategyVo inventoryStrategyVo);

    List<InventoryStrategyVo> pageInventoryStrategyView(InventoryStrategyVo inventoryStrategyVo);

    List<InventoryStrategyVo> pageInventoryStrategyViewFromTable(InventoryStrategyVo inventoryStrategyVo);

    List<InventoryStrategyVo> queryDasNumList(String month);



    /**
     * 删除临时表数据
     */
    void deleteTempInventoryStrategyThird();

    /**
     * 插入到临时表中
     */
    void insertTempInventoryStrategyThird();
    /**
     * 删除正式表数据
     */
    void deleteTempInventoryStrategy();
    /**
     * 从临时表中获取数据插入到正式表中
     */
    void insertTempInventoryStrategy();

}
