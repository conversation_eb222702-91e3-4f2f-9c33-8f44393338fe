package cn.aliyun.ryytn.modules.distribution.dao;

import java.util.List;

import cn.aliyun.ryytn.modules.distribution.entity.dto.AlgoSchedulingRecordDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PageAlgoVersionReqVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.PageAlgoVersionRspVo;

/**
 * @Description 调拨计划Dao层
 * <AUTHOR>
 * @date 2023/12/8 17:42
 */
public interface AlgoSchedulingRecordDao
{
    void addAlgoSchedulingRecord(AlgoSchedulingRecordDto algoSchedulingRecordDto);

    List<AlgoSchedulingRecordDto> queryAlgoSchedulingRecord();

    List<PageAlgoVersionRspVo> queryAlgoVersion(PageAlgoVersionReqVo condition);
}
