package cn.aliyun.ryytn.modules.distribution.dataqdao;

import java.util.List;

import cn.aliyun.ryytn.modules.demand.entity.vo.PlanValue;
import cn.aliyun.ryytn.modules.distribution.entity.dto.FreightPlanDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.InventoryInferenceDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.StockFluctuateDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.WarehouseStockAnalyDto;

/**
 * @Description 调拨计划Dao
 * <AUTHOR>
 * @date 2023/12/26 14:17
 */
public interface DataqFreightPlanDao
{
    /**
     *
     * @Description 查询调拨计划列表
     * @param freightPlanDto
     * @return List<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<FreightPlanDto> queryFreightPlanList(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 查询调拨计划版本列表
     * @param freightPlanDto
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<String> queryFreightPlanVersionList(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 查询调拨计划动态表头
     * @param freightPlanDto
     * @return List<String>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<String> queryFreightPlanHeadList(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 查询调拨计划表头筛选列表
     * @param freightPlanDto
     * @return List<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<FreightPlanDto> queryFreightPlanHeadSelect(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 查询调拨计划分组聚合列表
     * @param freightPlanDto
     * @return List<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<FreightPlanDto> queryFreightPlanGroupList(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 查询调拨计划指定分组逻辑的建议合计数量
     * @param freightPlanDto
     * @return List<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<FreightPlanDto> queryAdvFreightPlanGroupList(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 查询调拨计划多仓比对列表
     * @param freightPlanDto
     * @return List<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<FreightPlanDto> queryFreightPlanWarehouseContrastList(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 查询调拨计划业务数据标识列表
     * @param freightPlanDto
     * @return List<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<FreightPlanDto> queryFreightPlanDataKeyList(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 查询调拨计划业务数据动态数据json列表
     * @param freightPlanDto
     * @return List<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月26日 19:19
     */
    List<FreightPlanDto> queryFreightPlanDataJsonList(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 查询调拨计划数据列表
     * @param freightPlanDto
     * @return List<FreightPlanDto>
     * <AUTHOR>
     * @date 2023年12月29日 19:06
     */
    List<FreightPlanDto> queryFreightPlanDataList(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 查询调拨计划版本号，格式：demandPlanVersion,aiplanDemandVersion
     * @param freightPlanDto
     * @return FreightPlanDto
     * <AUTHOR>
     * @date 2023年12月27日 20:17
     */
    FreightPlanDto queryFreightPlanVersion(FreightPlanDto freightPlanDto);

    /**
     *
     * @Description 根据编号查询调拨计划算法输出结果列表
     * @param freightPlanDto
     * @return FreightPlanDto
     * <AUTHOR>
     * @date 2023年12月27日 20:17
     */
    List<FreightPlanDto> queryFreightPlanListByIds(List<PlanValue> planValueList);

    /**
     *
     * @Description 保存调拨计划
     * @param freightPlanDtoList
     * <AUTHOR>
     * @date 2024年01月05日 14:47
     */
    void saveFreightPlanData(List<FreightPlanDto> freightPlanDtoList);

    /**
     *
     * @Description 查询生产调入列表
     * @param warehouseStockAnalyDto
     * @return List<WarehouseStockAnalyDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月05日 17:20
     */
    List<WarehouseStockAnalyDto> queryProductionFreightList(WarehouseStockAnalyDto warehouseStockAnalyDto);

    /**
     *
     * @Description 查询在途库存列表
     * @param warehouseStockAnalyDto
     * @return List<WarehouseStockAnalyDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2024年01月05日 17:20
     */
    List<WarehouseStockAnalyDto> queryOnTransInventoryList(WarehouseStockAnalyDto warehouseStockAnalyDto);

    /**
     *
     * @Description 查询库存推演期初、期末库存
     * @param inventoryInference
     * @return List<InventoryInferenceDto>
     * <AUTHOR>
     * @date 2024年01月08日 11:00
     */
    List<InventoryInferenceDto> queryInventoryInferenceList(InventoryInferenceDto inventoryInference);

    /**
     *
     * @Description 查询平均调拨数量
     * @param inventoryInference
     * @return List<InventoryInferenceDto>
     * <AUTHOR>
     * @date 2024年01月08日 11:00
     */
    List<InventoryInferenceDto> queryWarehouseAvgAllocation(InventoryInferenceDto inventoryInference);

    /**
     *
     * @Description 删除调拨计划数据
     * @param demandPlanCode
     * <AUTHOR>
     * @date 2024年03月04日 10:23
     */
    void deleteFreightPlan(String demandPlanCode);

    /**
     *
     * @Description 查询库存列表
     * @param stockFluctuateDto
     * @return List<StockFluctuateDto>
     * <AUTHOR>
     * @date 2024年03月19日 14:00
     */
    List<StockFluctuateDto> queryStockFluctuateList(StockFluctuateDto stockFluctuateDto);

    /**
     *
     * @Description 分组查询库存列表
     * @param stockFluctuateDto
     * @return List<StockFluctuateDto>
     * <AUTHOR>
     * @date 2024年04月22日 11:27
     */
    List<StockFluctuateDto> queryStockFluctuateListGroupBy(StockFluctuateDto stockFluctuateDto);
}
