package cn.aliyun.ryytn.modules.distribution.dao;

import java.util.List;

import cn.aliyun.ryytn.modules.distribution.entity.dto.AdjustableDaysRuleDto;
import cn.aliyun.ryytn.modules.distribution.entity.dto.AdjustableDaysRuleRangeDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.QueryAdjustableDaysRuleListRspVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleCategory;
import cn.aliyun.ryytn.modules.distribution.entity.vo.RuleProduct;

/**
 * @Description 可调节天数规则Dao层
 * <AUTHOR>
 * @date 2023/11/16 15:15
 */
public interface AdjustableDaysRuleDao
{
    List<QueryAdjustableDaysRuleListRspVo> queryAdjustableDaysRuleList();

    int countAdjustableDaysRuleName(AdjustableDaysRuleDto adjustableDaysRuleDto);

    void addAdjustableDaysRule(AdjustableDaysRuleDto adjustableDaysRuleDto);

    void addAdjustableDaysRuleRange(List<AdjustableDaysRuleRangeDto> adjustableDaysRuleRangeList);

    void addAdjustableDaysRuleRangeProduct(List<RuleProduct> productList);

    void addAdjustableDaysRuleRangeCategory(List<RuleCategory> categoryList);

    AdjustableDaysRuleDto queryAdjustableDaysRuleDetail(String id);

    void deleteAdjustableDaysRule(String id);

    void deleteAdjustableDaysRuleRange(String id);

    void deleteAdjustableDaysRuleRangeCategory(String id);

    void deleteAdjustableDaysRuleRangeProduct(String id);

    void updateAdjustableDaysRule(AdjustableDaysRuleDto adjustableDaysRuleDto);

    Integer checkSkuIfExist(List<String> skuCodeList);

    List<String> queryCategorySkuCodeList();

    int countAdjustableDaysGeneralRule();
}
