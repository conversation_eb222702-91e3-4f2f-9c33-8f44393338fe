package cn.aliyun.ryytn.modules.distribution.dataqdao;

import java.util.List;

import cn.aliyun.ryytn.modules.distribution.entity.dto.GraphKcclConfigDto;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyConditionVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.InventoryStrategyVo;
import cn.aliyun.ryytn.modules.distribution.entity.vo.SalesVolume;

/**
 * @Description Dataq库存策略Dao
 * <AUTHOR>
 * @date 2024/1/3 16:00
 */
public interface DataqInventoryDao
{
    /**
     *
     * @Description 查询库存策略数据列表
     * @param inventoryStrategyConditionVo
     * @return List<InventoryStrategyVo>
     * <AUTHOR>
     * @date 2024年01月03日 16:45
     */
    List<InventoryStrategyVo> queryInventoryList(InventoryStrategyConditionVo inventoryStrategyConditionVo);

    /**
     *
     * @Description 查询日均销量
     * @param salesVolume
     * @return List<SalesVolume>
     * <AUTHOR>
     * @date 2024年04月15日 11:24
     */
    List<SalesVolume> querySalesVolumeList(SalesVolume salesVolume);

    /**
     *
     * @Description 删除自定义库存策略配置
     * @param graphKcclConfigDto
     * <AUTHOR>
     * @date 2024年04月17日 14:37
     */
    void deleteGraphKcclConfig(GraphKcclConfigDto graphKcclConfigDto);

    /**
     *
     * @Description 新增自定义库存策略配置
     * @param graphKcclConfigList
     * <AUTHOR>
     * @date 2024年04月17日 14:38
     */
    void addGraphKcclConfigList(List<GraphKcclConfigDto> graphKcclConfigList);

    /**
     *
     * @Description 根据业务唯一标识查询自定义库存策略配置详情
     * @param graphKcclConfig
     * @return List<GraphKcclConfigDto>
     * <AUTHOR>
     * @date 2024年04月17日 14:46
     */
    List<GraphKcclConfigDto> queryGraphKcclConfigDetail(GraphKcclConfigDto graphKcclConfig);
}
