package cn.aliyun.ryytn.modules.master.dao;

import cn.aliyun.ryytn.modules.master.entity.ProductInheritance;
import cn.aliyun.ryytn.modules.master.entity.dto.ProductInheritanceQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 商品继承关系DAO
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
public interface ProductInheritanceDao {

    /**
     * 新增商品继承关系
     *
     * @param inheritance 关系信息
     * @return 影响行数
     */
    int insert(ProductInheritance inheritance);

    /**
     * 修改商品继承关系
     *
     * @param inheritance 关系信息
     * @return 影响行数
     */
    int update(ProductInheritance inheritance);

    /**
     * 根据ID查询
     *
     * @param id 主键ID
     * @return 关系信息
     */
    ProductInheritance selectById(@Param("id") String id);

    /**
     * 检查唯一性
     *
     * @param originalSkuId 原商品ID
     * @param newSkuId 新品商品ID
     * @param relationType 关系类型
     * @param customStore 定制店铺
     * @param customChannel 定制渠道
     * @param channelOwnership 渠道归属
     * @param excludeId 排除的ID（修改时使用）
     * @return 存在的记录数
     */
    int checkUniqueness(@Param("originalSkuId") String originalSkuId,
                       @Param("newSkuId") String newSkuId,
                       @Param("relationType") Integer relationType,
                       @Param("customStore") String customStore,
                       @Param("customChannel") String customChannel,
                       @Param("channelOwnership") Integer channelOwnership,
                       @Param("excludeId") String excludeId);

    /**
     * 批量作废
     *
     * @param ids 关系ID列表
     * @param updatedBy 修改人
     * @return 影响行数
     */
    int batchInvalidate(@Param("ids") List<String> ids, @Param("updatedBy") String updatedBy);

    /**
     * 查询已作废的记录数
     *
     * @param ids 关系ID列表
     * @return 已作废的记录数
     */
    int countInvalidated(@Param("ids") List<String> ids);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 关系列表
     */
    List<ProductInheritance> queryPage(ProductInheritanceQueryDto condition);
}
