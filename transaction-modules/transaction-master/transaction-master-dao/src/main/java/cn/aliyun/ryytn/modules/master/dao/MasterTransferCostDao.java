package cn.aliyun.ryytn.modules.master.dao;

import cn.aliyun.ryytn.common.entity.info.MasterTransferCost;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运输成本数据访问接口
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface MasterTransferCostDao {

    /**
     * 分页查询运输成本列表
     *
     * @param request 分页查询条件
     * @return 运输成本列表
     */
    List<MasterTransferCostPageResponse> queryByPage(MasterTransferCostPageRequest request);

    /**
     * 插入运输成本
     *
     * @param record 运输成本信息
     * @return 影响行数
     */
    int insert(MasterTransferCost record);

    /**
     * 修改运输成本
     *
     * @param record 运输成本信息
     * @return 影响行数
     */
    int update(MasterTransferCost record);

    /**
     * 修改运输成本状态
     *
     * @param record 运输成本信息
     * @return 影响行数
     */
    int updateStatus(MasterTransferCost record);

    /**
     * 根据ID查询运输成本
     *
     * @param id 运输成本ID
     * @return 运输成本信息
     */
    MasterTransferCost selectById(@Param("id") String id);

    /**
     * 检查唯一性
     *
     * @param record 运输成本信息
     * @return 存在的记录数
     */
    int checkUnique(MasterTransferCost record);
}