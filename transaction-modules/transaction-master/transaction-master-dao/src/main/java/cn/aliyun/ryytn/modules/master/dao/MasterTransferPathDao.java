package cn.aliyun.ryytn.modules.master.dao;

import cn.aliyun.ryytn.common.entity.info.MasterTransferPath;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-27 13:48
 */
public interface MasterTransferPathDao {
    /**
     * 插入调拨路径
     *
     * @param entity 调拨路径实体
     * @return 影响行数
     */
    int insert(MasterTransferPath entity);

    /**
     * 统计重叠时间段的调拨路径数量
     *
     * @return 重叠记录数
     */
    int countOverlap(MasterTransferPath entity);

    MasterTransferPath selectById(@Param("id") String id);

    void update(MasterTransferPath entity);

    /**
     * 更新调拨路径状态
     *
     * @param entity 调拨路径实体
     * @return 影响行数
     */
    int updateStatus(MasterTransferPath entity);

    /**
     * 分页查询调拨路径
     *
     * @param condition 查询条件
     * @return 查询结果
     */
    List<MasterWarehousePathPageResponse> selectByPage(MasterWarehousePathPageRequest condition);
}
