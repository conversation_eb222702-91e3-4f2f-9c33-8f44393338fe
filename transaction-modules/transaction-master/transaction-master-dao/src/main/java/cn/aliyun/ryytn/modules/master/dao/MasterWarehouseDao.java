package cn.aliyun.ryytn.modules.master.dao;

import cn.aliyun.ryytn.common.entity.sap.MasterWarehouse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehouseQueryRequest;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-06 15:44
 */
public interface MasterWarehouseDao {
    MasterWarehouse selectById(@Param("id") String id);

    // 根据仓库编码批量查询
    List<MasterWarehouse> selectByWarehouseCodes(@Param("codes") Collection<String> warehouseCodes);

    // 批量插入
    void batchInsert(@Param("list") List<MasterWarehouse> entities);

    // 批量更新
    void batchUpdate(@Param("list") List<MasterWarehouse> entities);

    /**
     * 分页查询仓库列表
     *
     * @param condition 查询条件
     * @return 仓库列表
     */
    List<MasterWarehouse> selectByPage(@Param("condition") MasterWarehouseQueryRequest condition);

    int updateByUser(MasterWarehouse request);
}
