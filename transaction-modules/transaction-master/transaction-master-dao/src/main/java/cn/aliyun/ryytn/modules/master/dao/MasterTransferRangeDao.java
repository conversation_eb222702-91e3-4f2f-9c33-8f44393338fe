package cn.aliyun.ryytn.modules.master.dao;

import cn.aliyun.ryytn.common.entity.info.MasterTransferRange;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调拨仓辐射DAO
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface MasterTransferRangeDao {

    /**
     * 分页查询调拨仓辐射
     *
     * @param condition 查询条件
     * @return 调拨仓辐射列表
     */
    List<MasterTransferRangePageResponse> queryByPage(MasterTransferRangePageRequest condition);

    /**
     * 插入调拨仓辐射
     *
     * @param entity 调拨仓辐射实体
     * @return 影响行数
     */
    int insert(MasterTransferRange entity);

    /**
     * 更新调拨仓辐射
     *
     * @param entity 调拨仓辐射实体
     * @return 影响行数
     */
    int update(MasterTransferRange entity);

    /**
     * 更新调拨仓辐射状态
     *
     * @param entity 调拨仓辐射实体
     * @return 影响行数
     */
    int updateStatus(MasterTransferRange entity);

    /**
     * 根据ID查询调拨仓辐射
     *
     * @param id 调拨仓辐射ID
     * @return 调拨仓辐射实体
     */
    MasterTransferRange selectById(@Param("id") Long id);

    /**
     * 检查组合唯一性
     *
     * @param entity 调拨仓辐射实体
     * @return 重复记录数
     */
    int countOverlap(MasterTransferRange entity);
}