package cn.aliyun.ryytn.modules.master.dao;

import cn.aliyun.ryytn.common.entity.info.AvailableStock;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockQueryRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 可用库存DAO
 * @date 2024/10/10 15:00
 */
public interface AvailableStockDao {

    /**
     * 新增可用库存
     *
     * @param stock 库存信息
     * @return 影响行数
     */
    int insert(AvailableStock stock);

    /**
     * 修改可用库存
     *
     * @param stock 库存信息
     * @return 影响行数
     */
    int update(AvailableStock stock);

    /**
     * 根据ID查询
     *
     * @param id 主键ID
     * @return 库存信息
     */
    AvailableStock selectById(@Param("id") String id);

    /**
     * 检查唯一性
     *
     * @param factoryName    工厂名称
     * @param skuId          商品ID
     * @param warehouseId    仓库ID
     * @param productionDate 生产日期
     * @param excludeId      排除的ID（修改时使用）
     * @return 存在的记录数
     */
    int checkUniqueness(@Param("factoryName") String factoryName,
                        @Param("skuId") String skuId,
                        @Param("warehouseId") String warehouseId,
                        @Param("productionDate") String productionDate,
                        @Param("excludeId") String excludeId);

    /**
     * 批量作废
     *
     * @param ids       库存ID列表
     * @param updatedBy 修改人
     * @return 影响行数
     */
    int batchInvalidate(@Param("ids") List<String> ids, @Param("updatedBy") String updatedBy);

    /**
     * 查询已作废的记录数
     *
     * @param ids 库存ID列表
     * @return 已作废的记录数
     */
    int countInvalidated(@Param("ids") List<String> ids);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 库存列表
     */
    List<AvailableStock> queryPage(AvailableStockQueryRequest condition);
}
