package cn.aliyun.ryytn.modules.master.dao;

import cn.aliyun.ryytn.common.entity.info.MasterRegion;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 区域信息数据访问接口
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface MasterRegionDao {
    /**
     * 分页查询区域信息列表，包含父区域名称
     *
     * @param request 分页查询条件
     * @return 区域信息列表
     */
    List<MasterRegionPageResponse> queryByPage(MasterRegionPageRequest request);

    /**
     * 插入区域信息
     *
     * @param region 区域信息
     */
    int insert(MasterRegion region);

    /**
     * 修改区域信息
     *
     * @param region 区域信息
     */
    int update(MasterRegion region);

    /**
     * 修改区域信息状态
     *
     * @param region 区域信息
     */
    int updateStatus(MasterRegion region);

    /**
     * 根据ID查询区域信息
     *
     * @param id 区域ID
     * @return 区域信息
     */
    MasterRegion selectById(@Param("id") String id);

    /**
     * 统计相同ID和名称的区域信息数量
     *
     * @param id   区域ID
     * @param name 区域名称
     * @return 数量
     */
    int countOverlap(@Param("id") String id, @Param("name") String name);
}