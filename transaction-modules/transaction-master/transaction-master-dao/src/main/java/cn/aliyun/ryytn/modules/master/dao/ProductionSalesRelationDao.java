package cn.aliyun.ryytn.modules.master.dao;

import cn.aliyun.ryytn.common.entity.info.ProductionSalesRelation;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationQueryRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 生产销售关系DAO
 * @date 2024/10/10 15:00
 */
public interface ProductionSalesRelationDao {

    /**
     * 新增生产销售关系
     *
     * @param relation 关系信息
     * @return 影响行数
     */
    int insert(ProductionSalesRelation relation);

    /**
     * 修改生产销售关系
     *
     * @param relation 关系信息
     * @return 影响行数
     */
    int update(ProductionSalesRelation relation);

    /**
     * 根据ID查询
     *
     * @param id 主键ID
     * @return 关系信息
     */
    ProductionSalesRelation selectById(@Param("id") String id);

    /**
     * 检查唯一性
     *
     * @param salesSkuId      销售商品ID
     * @param productionSkuId 生产商品ID
     * @param excludeId       排除的ID（修改时使用）
     * @return 存在的记录数
     */
    int checkUniqueness(@Param("salesSkuId") String salesSkuId,
                        @Param("productionSkuId") String productionSkuId,
                        @Param("excludeId") String excludeId);

    /**
     * 批量作废
     *
     * @param ids       关系ID列表
     * @param updatedBy 修改人
     * @return 影响行数
     */
    int batchInvalidate(@Param("ids") List<String> ids, @Param("updatedBy") String updatedBy);

    /**
     * 查询已作废的记录数
     *
     * @param ids 关系ID列表
     * @return 已作废的记录数
     */
    int countInvalidated(@Param("ids") List<String> ids);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 关系列表
     */
    List<ProductionSalesRelation> queryPage(ProductionSalesRelationQueryRequest condition);
}
