package cn.aliyun.ryytn.modules.master.dao;

import java.util.List;

import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterSkuQueryRequest;
import org.apache.ibatis.annotations.Param;

/**
 * @Description 主SKU信息Dao
 * <AUTHOR>
 * @date 2024/10/10 10:00
 */
public interface MasterSkuDao
{
    /**
     * 根据商品编码列表批量查询主SKU信息
     *
     * @param productCodes 商品编码列表
     * @return 主SKU信息列表
     */
    List<MasterSku> queryByProductCodes(List<String> productCodes);

    /**
     * 批量新增主SKU信息
     *
     * @param masterSkuList 主SKU信息列表
     * @return 影响行数
     */
    int batchInsert(List<MasterSku> masterSkuList);

    /**
     * 批量更新主SKU信息
     *
     * @param masterSkuList 主SKU信息列表
     * @return 影响行数
     */
    int batchUpdate(List<MasterSku> masterSkuList);

    /**
     * 分页查询主SKU信息
     * @param condition
     * @return
     */
    List<MasterSku> queryMasterSkuPage(@Param("condition") MasterSkuQueryRequest condition);

    /**
     * 查询所有商品编码
     *
     * @return 商品编码列表
     */
    List<String> queryAllProductCodes();
}
