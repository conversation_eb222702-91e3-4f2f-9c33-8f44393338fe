package cn.aliyun.ryytn.modules.master.dao;

import cn.aliyun.ryytn.common.entity.info.TransferSubstitution;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionQueryRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 转移替代关系DAO
 * @date 2024/10/10 15:00
 */
public interface TransferSubstitutionDao {

    /**
     * 新增转移替代关系
     *
     * @param relation 关系信息
     * @return 影响行数
     */
    int insert(TransferSubstitution relation);

    /**
     * 修改转移替代关系
     *
     * @param relation 关系信息
     * @return 影响行数
     */
    int update(TransferSubstitution relation);

    /**
     * 根据ID查询
     *
     * @param id 主键ID
     * @return 关系信息
     */
    TransferSubstitution selectById(@Param("id") String id);

    /**
     * 检查唯一性
     *
     * @param originalSkuId   原商品ID
     * @param substituteSkuId 替代商品ID
     * @param excludeId       排除的ID（修改时使用）
     * @return 存在的记录数
     */
    int checkUniqueness(@Param("originalSkuId") String originalSkuId,
                        @Param("substituteSkuId") String substituteSkuId,
                        @Param("excludeId") String excludeId);

    /**
     * 批量作废
     *
     * @param ids       关系ID列表
     * @param updatedBy 修改人
     * @return 影响行数
     */
    int batchInvalidate(@Param("ids") List<String> ids, @Param("updatedBy") String updatedBy);

    /**
     * 查询已作废的记录数
     *
     * @param ids 关系ID列表
     * @return 已作废的记录数
     */
    int countInvalidated(@Param("ids") List<String> ids);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 关系列表
     */
    List<TransferSubstitution> queryPage(TransferSubstitutionQueryRequest condition);
}
