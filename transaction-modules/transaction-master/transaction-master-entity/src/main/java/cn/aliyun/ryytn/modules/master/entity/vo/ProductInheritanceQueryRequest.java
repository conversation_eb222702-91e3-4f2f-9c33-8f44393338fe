package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 商品继承关系查询DTO
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "商品继承关系查询DTO")
public class ProductInheritanceQueryRequest {

    @ApiModelProperty("原商品ID列表")
    private List<String> originalSkuIds;

    @ApiModelProperty("新品商品ID列表")
    private List<String> newSkuIds;

    @ApiModelProperty("关系类型列表")
    private List<Integer> relationTypes;

    @ApiModelProperty("定制店铺列表")
    private List<String> customStores;

    @ApiModelProperty("定制渠道列表")
    private List<String> customChannels;

    @ApiModelProperty("渠道归属列表")
    private List<Integer> channelOwnerships;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;
}
