package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 可用库存请求
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "可用库存请求")
public class AvailableStockRequest {

    @ApiModelProperty("主键ID（修改时必填）")
    private String id;

    @ApiModelProperty(value = "工厂名称", required = true)
    private String factoryName;

    @ApiModelProperty(value = "商品ID", required = true)
    private String skuId;

    @ApiModelProperty(value = "仓库ID", required = true)
    private String warehouseId;

    @ApiModelProperty(value = "生产日期", required = true)
    private String productionDate;

    @ApiModelProperty(value = "可用库存数量", required = true)
    private BigDecimal quantity;

    @ApiModelProperty(value = "库存快照日期", required = true)
    private String stockDate;
}
