package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 生产销售关系响应
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "生产销售关系响应")
public class ProductionSalesRelationQueryPageResponse {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("销售商品ID")
    private String salesSkuId;

    @ApiModelProperty("销售商品编码")
    private String salesSkuCode;

    @ApiModelProperty("销售商品名称")
    private String salesSkuName;

    @ApiModelProperty("生产商品ID")
    private String productionSkuId;

    @ApiModelProperty("生产商品编码")
    private String productionSkuCode;

    @ApiModelProperty("生产商品名称")
    private String productionSkuName;

    @ApiModelProperty("转换系数")
    private Integer conversionFactor;

    @ApiModelProperty("创建人账号")
    private String createdBy;

    @ApiModelProperty("最后修改人账号")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后修改时间")
    private Date updatedTime;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusDesc;
}
