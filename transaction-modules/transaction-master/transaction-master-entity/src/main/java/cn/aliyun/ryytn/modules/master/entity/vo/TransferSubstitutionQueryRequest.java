package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 转移替代关系查询DTO
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "转移替代关系查询DTO")
public class TransferSubstitutionQueryRequest {

    @ApiModelProperty("原商品ID列表")
    private List<String> originalSkuIds;

    @ApiModelProperty("替代商品ID列表")
    private List<String> substituteSkuIds;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;
}
