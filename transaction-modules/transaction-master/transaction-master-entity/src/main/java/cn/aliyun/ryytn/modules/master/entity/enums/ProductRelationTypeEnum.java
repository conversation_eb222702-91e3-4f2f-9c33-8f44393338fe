package cn.aliyun.ryytn.modules.master.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description 商品关系类型枚举
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Getter
@AllArgsConstructor
public enum ProductRelationTypeEnum {

    NEW_OLD_BRAND(0, "新老品牌"),
    STORE_CUSTOM(1, "店铺定制"),
    CHANNEL_CUSTOM(2, "渠道定制");

    private final Integer code;
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ProductRelationTypeEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        
        for (ProductRelationTypeEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        ProductRelationTypeEnum enumValue = getByCode(code);
        return enumValue != null ? enumValue.getDesc() : null;
    }
}
