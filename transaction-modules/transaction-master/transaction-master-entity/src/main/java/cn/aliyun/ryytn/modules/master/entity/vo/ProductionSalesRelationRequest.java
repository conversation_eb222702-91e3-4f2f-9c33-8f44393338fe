package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * @Description 生产销售关系请求
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "生产销售关系请求")
public class ProductionSalesRelationRequest {

    @ApiModelProperty("主键ID（修改时必填）")
    private String id;

    @ApiModelProperty(value = "销售商品ID", required = true)
    @NotNull(message = "销售商品ID不能为空")
    private String salesSkuId;

    @ApiModelProperty(value = "生产商品ID", required = true)
    @NotNull(message = "生产商品ID不能为空")
    private String productionSkuId;

    @ApiModelProperty(value = "转换系数", required = true)
    @NotNull(message = "转换系数不能为空")
    @Positive(message = "转换系数必须为正整数")
    private Integer conversionFactor;
}
