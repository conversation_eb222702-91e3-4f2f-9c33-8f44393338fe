package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2025-05-27 15:14
 */
@Data
@ApiModel("调拨路径启用禁用参数")
public class MasterTransferPathUpdateStatusRequest {

    @NotBlank(message = "调拨路径ID不能为空")
    @ApiModelProperty(value = "调拨路径ID", required = true)
    private String id;
}
