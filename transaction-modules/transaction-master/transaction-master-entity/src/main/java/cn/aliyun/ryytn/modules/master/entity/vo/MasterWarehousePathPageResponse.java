package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-05-27 15:46
 */
@Data
public class MasterWarehousePathPageResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("修改人")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("修改时间")
    private Date updatedTime;

    @ApiModelProperty("状态 1-生效 2-失效")
    private Integer status;

    @ApiModelProperty("发货仓ID")
    private String sendWarehouseId;

    @ApiModelProperty("收货仓ID")
    private String receiveWarehouseId;

    @ApiModelProperty("生效时间")
    private Date activeTime;

    @ApiModelProperty("结束时间")
    private Date disableTime;

    @ApiModelProperty("状态 1-生效 2-失效")
    private String statusName;

    @ApiModelProperty("发货仓编码")
    private String sendWarehouseCode;

    @ApiModelProperty("收货仓编码")
    private String receiveWarehouseCode;

    @ApiModelProperty("发货仓名称")
    private String sendWarehouseName;

    @ApiModelProperty("收货仓名称")
    private String receiveWarehouseName;


    @ApiModelProperty("生效时间-字符串")
    private String activeTimeStr;

    @ApiModelProperty("结束时间-字符串")
    private String disableTimeStr;

    @ApiModelProperty("创建时间-字符串")
    private String createdTimeStr;

    @ApiModelProperty("修改时间-字符串")
    private String updatedTimeStr;


}
