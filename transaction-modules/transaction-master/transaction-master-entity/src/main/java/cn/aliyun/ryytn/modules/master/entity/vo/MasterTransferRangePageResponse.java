package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 调拨仓辐射分页查询响应
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("调拨仓辐射分页查询响应")
public class MasterTransferRangePageResponse {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updatedTime;

    /**
     * 状态 1-生效 0-失效
     */
    @ApiModelProperty("状态 1-生效 0-失效")
    private Integer status;

    /**
     * 状态名称
     */
    @ApiModelProperty("状态名称")
    private String statusName;

    /**
     * 辐射类型 0-TOB 1-TOC
     */
    @ApiModelProperty("辐射类型 0-TOB 1-TOC")
    private Integer radiationType;

    /**
     * 辐射类型名称
     */
    @ApiModelProperty("辐射类型名称")
    private String radiationTypeName;

    /**
     * 商品ID
     */
    @ApiModelProperty("商品ID")
    private String skuId;

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;

    /**
     * 一级分类
     */
    @ApiModelProperty("一级分类")
    private String primaryCategory;

    /**
     * 四级分类
     */
    @ApiModelProperty("四级分类")
    private String quaternaryCategory;

    /**
     * 省ID
     */
    @ApiModelProperty("省ID")
    private String provinceId;

    /**
     * 省名称
     */
    @ApiModelProperty("省名称")
    private String provinceName;

    /**
     * 市ID
     */
    @ApiModelProperty("市ID")
    private String cityId;

    /**
     * 市名称
     */
    @ApiModelProperty("市名称")
    private String cityName;

    /**
     * 仓库ID
     */
    @ApiModelProperty("仓库ID")
    private String warehouseId;

    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /**
     * 销售部门
     */
    @ApiModelProperty("销售部门")
    private String salesDepartment;

    /**
     * 起始公斤重量（不包含）
     */
    @ApiModelProperty("起始公斤重量（不包含）")
    private BigDecimal startKg;

    /**
     * 结束公斤重量（不包含）
     */
    @ApiModelProperty("结束公斤重量（不包含）")
    private BigDecimal endKg;
}