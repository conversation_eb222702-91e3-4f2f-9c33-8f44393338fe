package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 运输成本分页查询请求
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("运输成本分页查询请求")
public class MasterTransferCostPageRequest {

    @ApiModelProperty("修改人集合")
    private List<String> updatedByList;

    @ApiModelProperty("修改时间-开始")
    private Date updatedTimeStart;

    @ApiModelProperty("修改时间-结束")
    private Date updatedTimeEnd;

    @ApiModelProperty("状态集合")
    private List<Integer> statusList;

    @ApiModelProperty("出发省集合")
    private List<String> fromProvinceIdList;

    @ApiModelProperty("出发市集合")
    private List<String> fromCityIdList;

    @ApiModelProperty("到达省集合")
    private List<String> toProvinceIdList;

    @ApiModelProperty("到达市集合")
    private List<String> toCityIdList;

    @ApiModelProperty("十三米（元/吨）集合")
    private List<BigDecimal> cost13mList;

    @ApiModelProperty("十五米（元/吨）集合")
    private List<BigDecimal> cost15mList;

    @ApiModelProperty("铁柜（元/吨）集合")
    private List<BigDecimal> costRailList;

    @ApiModelProperty("汽运时效（天）集合")
    private List<Integer> roadDurationList;

    @ApiModelProperty("铁运时效（天）集合")
    private List<Integer> railDurationList;
}