package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 调拨路径DTO
 */
@Data
@ApiModel(value = "调拨路径创建参数")
public class MasterTransferPathCreateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(message = "发货仓ID不能为空")
    @ApiModelProperty(value = "发货仓ID", required = true)
    private String sendWarehouseId;

    @NotBlank(message = "收货仓ID不能为空")
    @ApiModelProperty(value = "收货仓ID", required = true)
    private String receiveWarehouseId;

    @NotNull(message = "生效时间不能为空")
    @ApiModelProperty(value = "生效时间", required = true)
    private Date activeTime;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private Date disableTime;

}