package cn.aliyun.ryytn.modules.master.entity.vo;

import cn.aliyun.ryytn.modules.master.entity.enums.MasterWarehouseTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SAP主数据-仓库档案查询入参
 *
 * <AUTHOR>
 * @since 2025-05-07 17:46
 */
@Data
public class MasterWarehouseQueryRequest implements Serializable {
    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    private String plantCode;

    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    /**
     * 仓库名称-模糊搜索
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /**
     * 仓库类型 {@link MasterWarehouseTypeEnum}
     */
    @ApiModelProperty("仓库类型")
    private Integer warehouseType;

    @ApiModelProperty("所在省")
    private String province;

    /**
     * 仓库名称-模糊搜索
     */
    @ApiModelProperty("实体仓名称")
    private String phyWarehouseName;
}
