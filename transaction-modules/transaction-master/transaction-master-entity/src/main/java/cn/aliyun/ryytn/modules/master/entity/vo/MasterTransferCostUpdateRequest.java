package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 运输成本修改请求
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("运输成本修改请求")
public class MasterTransferCostUpdateRequest {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", required = true)
    @NotBlank(message = "主键ID不能为空")
    private String id;

    /**
     * 出发省
     */
    @ApiModelProperty(value = "出发省", required = true)
    @NotBlank(message = "出发省不能为空")
    private String fromProvinceId;

    /**
     * 出发市
     */
    @ApiModelProperty(value = "出发市", required = true)
    @NotBlank(message = "出发市不能为空")
    private String fromCityId;

    /**
     * 到达省
     */
    @ApiModelProperty(value = "到达省", required = true)
    @NotBlank(message = "到达省不能为空")
    private String toProvinceId;

    /**
     * 到达市
     */
    @ApiModelProperty(value = "到达市", required = true)
    @NotBlank(message = "到达市不能为空")
    private String toCityId;

    /**
     * 十三米（元/吨）
     */
    @ApiModelProperty(value = "十三米（元/吨）", required = true)
    @NotNull(message = "十三米（元/吨）不能为空")
    private BigDecimal cost13m;

    /**
     * 十五米（元/吨）
     */
    @ApiModelProperty(value = "十五米（元/吨）", required = true)
    @NotNull(message = "十五米（元/吨）不能为空")
    private BigDecimal cost15m;

    /**
     * 铁柜（元/吨）
     */
    @ApiModelProperty(value = "铁柜（元/吨）", required = true)
    @NotNull(message = "铁柜（元/吨）不能为空")
    private BigDecimal costRail;

    /**
     * 汽运时效（天）
     */
    @ApiModelProperty(value = "汽运时效（天）", required = true)
    @NotNull(message = "汽运时效（天）不能为空")
    private Integer roadDuration;

    /**
     * 铁运时效（天）
     */
    @ApiModelProperty(value = "铁运时效（天）", required = true)
    @NotNull(message = "铁运时效（天）不能为空")
    private Integer railDuration;
}