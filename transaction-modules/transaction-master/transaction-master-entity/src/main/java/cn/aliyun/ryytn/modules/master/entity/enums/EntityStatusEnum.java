package cn.aliyun.ryytn.modules.master.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.Optional;

/**
 * 状态枚举
 *
 * <AUTHOR>
 * @since 2025-05-27 15:02
 */
@Getter
@AllArgsConstructor
public enum EntityStatusEnum {
    /*0：无效，1：有效*/
    NO_ACTIVE(0, "无效"),
    IS_ACTIVE(1, "有效"),
    ;
    private final Integer code;
    private final String name;

    public static EntityStatusEnum getEnumByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }

        for (EntityStatusEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        EntityStatusEnum statusEnum = getEnumByCode(code);
        return Optional.ofNullable(statusEnum)
                .map(EntityStatusEnum::getName)
                .orElse(null);
    }
}
