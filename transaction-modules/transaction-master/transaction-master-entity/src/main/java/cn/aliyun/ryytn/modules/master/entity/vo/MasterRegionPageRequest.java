package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 区域信息分页查询请求
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("区域信息分页查询请求")
public class MasterRegionPageRequest {
    /**
     * 编码即ID列表
     */
    @ApiModelProperty("编码即ID列表")
    private List<String> idList;

    /**
     * 名称列表
     */
    @ApiModelProperty("名称列表")
    private List<String> nameList;

    /**
     * 父级ID列表
     */
    @ApiModelProperty("父级ID列表")
    private List<String> parentIdList;

    /**
     * 状态列表 1-生效 2-失效
     */
    @ApiModelProperty("状态列表 1-生效 2-失效")
    private List<Integer> statusList;

    /**
     * 修改人列表
     */
    @ApiModelProperty("修改人列表")
    private List<String> updatedByList;

    @ApiModelProperty("修改时间-开始")
    private Date updatedTimeStart;

    @ApiModelProperty("修改时间-结束")
    private Date updatedTimeEnd;
}
