package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 区域信息分页查询请求
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("区域信息分页查询请求")
public class MasterRegionPageRequest {
    /**
     * 编码即ID
     */
    @ApiModelProperty("编码即ID")
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 父级ID
     */
    @ApiModelProperty("父级ID")
    private String parentId;

    /**
     * 状态 1-生效 2-失效
     */
    @ApiModelProperty("状态 1-生效 2-失效")
    private Integer status;
}
