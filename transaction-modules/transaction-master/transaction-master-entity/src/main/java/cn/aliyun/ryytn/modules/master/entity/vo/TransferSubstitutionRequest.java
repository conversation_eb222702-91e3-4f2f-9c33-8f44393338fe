package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description 转移替代关系请求
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "转移替代关系请求")
public class TransferSubstitutionRequest {

    @ApiModelProperty("主键ID（修改时必填）")
    private String id;

    @ApiModelProperty(value = "原商品ID", required = true)
    @NotNull(message = "原商品ID不能为空")
    private String originalSkuId;

    @ApiModelProperty(value = "替代商品ID", required = true)
    @NotNull(message = "替代商品ID不能为空")
    private String substituteSkuId;
}
