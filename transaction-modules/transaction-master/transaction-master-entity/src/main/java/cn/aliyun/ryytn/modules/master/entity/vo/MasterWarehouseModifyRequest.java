package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SAP主数据-仓库档案修改入参
 *
 * <AUTHOR>
 * @since 2025-05-07 17:46
 */
@Data
public class MasterWarehouseModifyRequest implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("ID")
    private String id;

    /**
     * 仓库类型-枚举如下：RDC仓、低温仓、工厂仓、奶粉仓、其他仓、虚拟仓、原辅料仓
     */
    @ApiModelProperty("仓库类型")
    private Integer warehouseType;

    /**
     * 仓库名称
     */
    @ApiModelProperty("实体仓名称")
    private String phyWarehouseName;
}
