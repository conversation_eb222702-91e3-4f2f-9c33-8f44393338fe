package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 创建区域信息请求
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("创建区域信息请求")
public class MasterRegionCreateRequest {
    /**
     * 编码即ID
     */
    @ApiModelProperty("编码即ID")
    @NotBlank(message = "编码不能为空")
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 父级ID
     */
    @ApiModelProperty("父级ID")
    private String parentId;
}
