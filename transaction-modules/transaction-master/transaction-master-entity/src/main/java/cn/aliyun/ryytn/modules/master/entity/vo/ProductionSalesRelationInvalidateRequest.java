package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Description 生产销售关系作废请求
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "生产销售关系作废请求")
public class ProductionSalesRelationInvalidateRequest {

    @ApiModelProperty(value = "要作废的关系ID列表", required = true)
    @NotEmpty(message = "关系ID列表不能为空")
    private List<String> ids;
}
