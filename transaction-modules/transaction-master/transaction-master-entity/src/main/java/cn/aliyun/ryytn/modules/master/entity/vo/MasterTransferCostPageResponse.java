package cn.aliyun.ryytn.modules.master.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运输成本分页查询响应
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("运输成本分页查询响应")
public class MasterTransferCostPageResponse {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("修改人")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("出发省ID")
    private String fromProvinceId;

    @ApiModelProperty("出发省名称")
    private String fromProvinceName;

    @ApiModelProperty("出发市ID")
    private String fromCityId;

    @ApiModelProperty("出发市名称")
    private String fromCityName;

    @ApiModelProperty("到达省ID")
    private String toProvinceId;

    @ApiModelProperty("到达省名称")
    private String toProvinceName;

    @ApiModelProperty("到达市ID")
    private String toCityId;

    @ApiModelProperty("到达市名称")
    private String toCityName;

    @ApiModelProperty("十三米（元/吨）")
    private BigDecimal cost13m;

    @ApiModelProperty("十五米（元/吨）")
    private BigDecimal cost15m;

    @ApiModelProperty("铁柜（元/吨）")
    private BigDecimal costRail;

    @ApiModelProperty("汽运时效（天）")
    private Integer roadDuration;

    @ApiModelProperty("铁运时效（天）")
    private Integer railDuration;
}