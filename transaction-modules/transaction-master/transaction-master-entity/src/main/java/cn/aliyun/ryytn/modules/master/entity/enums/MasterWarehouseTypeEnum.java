package cn.aliyun.ryytn.modules.master.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 仓类型枚举
 *
 * <AUTHOR>
 * @since 2025-05-08 18:02
 */
@Getter
@AllArgsConstructor
public enum MasterWarehouseTypeEnum {
    /*RDC仓、低温仓、工厂仓、奶粉仓、其他仓、虚拟仓、原辅料仓*/
    RDC(1, "RDC仓"),
    LOW_TEMPERATURE(2, "低温仓"),
    FACTORY(3, "工厂仓"),
    MILK_POWDER(4, "奶粉仓"),
    OTHER(5, "其他仓"),
    VIRTUAL(6, "虚拟仓"),
    ORIGINAL(7, "原辅料仓"),
    ;
    private final Integer code;
    private final String desc;

    public static MasterWarehouseTypeEnum getEnumByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        
        for (MasterWarehouseTypeEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
