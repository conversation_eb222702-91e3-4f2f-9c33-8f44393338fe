package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description 同步主SKU信息请求
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "同步主SKU信息请求")
public class MasterSkuSyncByCodeRequest {

    @ApiModelProperty(value = "商品编码数组，如果为空则同步所有商品")
    private List<String> productCodes;
}
