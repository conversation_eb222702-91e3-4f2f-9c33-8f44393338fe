package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 运输成本状态修改请求
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("运输成本状态修改请求")
public class MasterTransferCostUpdateStatusRequest {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", required = true)
    @NotBlank(message = "主键ID不能为空")
    private String id;
}