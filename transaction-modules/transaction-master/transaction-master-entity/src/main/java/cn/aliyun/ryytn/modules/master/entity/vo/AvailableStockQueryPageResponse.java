package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 可用库存响应
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "可用库存响应")
public class AvailableStockQueryPageResponse {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("工厂名称")
    private String factoryName;

    @ApiModelProperty("商品ID")
    private String skuId;

    @ApiModelProperty("商品编码")
    private String skuCode;

    @ApiModelProperty("商品名称")
    private String skuName;

    @ApiModelProperty("仓库ID")
    private String warehouseId;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("生产日期")
    private String productionDate;

    @ApiModelProperty("可用库存数量")
    private BigDecimal quantity;

    @ApiModelProperty("库存快照日期")
    private String stockDate;

    @ApiModelProperty("创建人账号")
    private String createdBy;

    @ApiModelProperty("最后修改人账号")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后修改时间")
    private Date updatedTime;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusDesc;
}
