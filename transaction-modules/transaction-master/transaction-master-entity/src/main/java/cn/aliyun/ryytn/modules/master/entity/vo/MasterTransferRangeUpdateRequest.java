package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 调拨仓辐射修改请求
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel(value = "调拨仓辐射修改参数")
public class MasterTransferRangeUpdateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "ID不能为空")
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    @ApiModelProperty(value = "辐射类型 0-TOB 1-TOC")
    @NotNull(message = "辐射类型不能为空")
    @Range(min = 0, max = 1, message = "辐射类型必须为0或1，其中0-TOB 1-TOC")
    private Integer radiationType;

    @NotBlank(message = "商品ID不能为空")
    @ApiModelProperty(value = "商品ID", required = true)
    private String skuId;

    @ApiModelProperty(value = "省ID")
    private String provinceId;

    @ApiModelProperty(value = "市ID")
    private String cityId;

    @ApiModelProperty(value = "仓库ID")
    @NotBlank(message = "仓库ID不能为空")
    private String warehouseId;

    @ApiModelProperty(value = "销售部门")
    private String salesDepartment;

    @ApiModelProperty(value = "起始公斤重量（不包含）")
    private BigDecimal startKg;

    @ApiModelProperty(value = "结束公斤重量（不包含）")
    private BigDecimal endKg;
}