package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 调拨仓辐射分页查询请求
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("调拨仓辐射分页查询请求")
public class MasterTransferRangePageRequest {

    /**
     * 修改人列表
     */
    @ApiModelProperty("修改人列表")
    private List<String> updatedByList;

    /**
     * 修改时间-开始
     */
    @ApiModelProperty("修改时间-开始")
    private Date updatedTimeStart;
    /**
     * 修改时间-结束
     */
    @ApiModelProperty("修改时间-结束")
    private Date updatedTimeEnd;

    /**
     * 辐射类型列表 0-TOB 1-TOC
     */
    @ApiModelProperty("辐射类型列表0或1，其中0-TOB 1-TOC")
    private List<Integer> radiationTypeList;

    /**
     * 商品ID列表
     */
    @ApiModelProperty("商品ID列表")
    private List<String> skuIdList;

    /**
     * 省ID列表
     */
    @ApiModelProperty("省ID列表")
    private List<String> provinceIdList;

    /**
     * 市ID列表
     */
    @ApiModelProperty("市ID列表")
    private List<String> cityIdList;

    /**
     * 仓库ID列表
     */
    @ApiModelProperty("仓库ID列表")
    private List<String> warehouseIdList;

    /**
     * 销售部门列表
     */
    @ApiModelProperty("销售部门列表")
    private List<String> salesDepartmentList;

    /**
     * 起始公斤重量列表
     */
    @ApiModelProperty("起始公斤重量列表")
    private List<BigDecimal> startKgList;

    /**
     * 结束公斤重量列表
     */
    @ApiModelProperty("结束公斤重量列表")
    private List<BigDecimal> endKgList;
}