package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 商品继承关系响应
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "商品继承关系响应")
public class ProductInheritanceQueryPageResponse {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("原商品ID")
    private String originalSkuId;

    @ApiModelProperty("原商品编码")
    private String originalSkuCode;

    @ApiModelProperty("原商品名称")
    private String originalSkuName;

    @ApiModelProperty("新品商品ID")
    private String newSkuId;

    @ApiModelProperty("新品商品编码")
    private String newSkuCode;

    @ApiModelProperty("新品商品名称")
    private String newSkuName;

    @ApiModelProperty("关系类型")
    private Integer relationType;

    @ApiModelProperty("关系类型描述")
    private String relationTypeDesc;

    @ApiModelProperty("定制店铺")
    private String customStore;

    @ApiModelProperty("定制渠道")
    private String customChannel;

    @ApiModelProperty("渠道归属")
    private Integer channelOwnership;

    @ApiModelProperty("渠道归属描述")
    private String channelOwnershipDesc;

    @ApiModelProperty("创建人账号")
    private String createdBy;

    @ApiModelProperty("最后修改人账号")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后修改时间")
    private Date updatedTime;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusDesc;

    @ApiModelProperty("创建时间字符串")
    private String createdTimeStr;

    @ApiModelProperty("最后修改时间字符串")
    private String updatedTimeStr;
}
