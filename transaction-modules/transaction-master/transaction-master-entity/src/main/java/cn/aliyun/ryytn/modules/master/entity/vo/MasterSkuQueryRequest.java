package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description 主SKU信息查询DTO
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "主SKU信息查询DTO")
public class MasterSkuQueryRequest {

    @ApiModelProperty(value = "商品编码")
    private String productCode;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "一级分类")
    private String primaryCategory;

    @ApiModelProperty(value = "二级分类")
    private String secondaryCategory;

    @ApiModelProperty(value = "三级分类")
    private String tertiaryCategory;

    @ApiModelProperty(value = "四级分类")
    private String quaternaryCategory;

    @ApiModelProperty(value = "零级编码")
    private String zeroLevelCode;

    @ApiModelProperty(value = "零级名称")
    private String zeroLevelDesc;

    @ApiModelProperty(value = "商品状态")
    private String productStatus;

    @ApiModelProperty(value = "下市状态")
    private String offMarketStatus;

    @ApiModelProperty(value = "下市时间开始", example = "2024-01-01", notes = "格式：yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date offMarketDateStart;

    @ApiModelProperty(value = "下市时间结束", example = "2024-12-31", notes = "格式：yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date offMarketDateEnd;


}
