package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-27 15:47
 */
@Data
public class MasterWarehousePathPageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("修改人列表")
    private List<String> updatedByList;

    @ApiModelProperty("修改时间-开始")
    private Date updatedTimeStart;
    @ApiModelProperty("修改时间-结束")
    private Date updatedTimeEnd;

    @ApiModelProperty("状态列表 1-生效 2-失效")
    private List<Integer> statusList;

    @ApiModelProperty("发货仓ID列表")
    private List<String> sendWarehouseIdList;

    @ApiModelProperty("收货仓ID列表")
    private List<String> receiveWarehouseIdList;

    @ApiModelProperty("生效时间-开始")
    private Date activeTimeStart;
    @ApiModelProperty("生效时间-结束")
    private Date activeTimeEnd;

    @ApiModelProperty("结束时间-开始")
    private Date disableTimeStart;
    @ApiModelProperty("结束时间-结束")
    private Date disableTimeEnd;
}
