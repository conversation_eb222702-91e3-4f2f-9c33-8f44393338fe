package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-05-27 15:47
 */
@Data
public class MasterWarehousePathPageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("修改人")
    private String updatedBy;

    @ApiModelProperty("修改时间-开始")
    private Date updatedTimeStart;
    @ApiModelProperty("修改时间-结束")
    private Date updatedTimeEnd;

    @ApiModelProperty("状态 1-生效 2-失效")
    private Integer status;

    @ApiModelProperty("发货仓ID")
    private String sendWarehouseId;

    @ApiModelProperty("收货仓ID")
    private String receiveWarehouseId;

    @ApiModelProperty("生效时间-开始")
    private Date activeTimeStart;
    @ApiModelProperty("生效时间-结束")
    private Date activeTimeEnd;

    @ApiModelProperty("结束时间-开始")
    private Date disableTimeStart;
    @ApiModelProperty("结束时间-结束")
    private Date disableTimeEnd;
}
