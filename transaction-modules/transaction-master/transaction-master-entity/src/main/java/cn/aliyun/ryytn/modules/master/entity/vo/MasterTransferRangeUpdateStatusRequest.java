package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 调拨仓辐射状态修改请求
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel(value = "调拨仓辐射状态修改参数")
public class MasterTransferRangeUpdateStatusRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "ID不能为空")
    @ApiModelProperty(value = "ID", required = true)
    private Long id;
}