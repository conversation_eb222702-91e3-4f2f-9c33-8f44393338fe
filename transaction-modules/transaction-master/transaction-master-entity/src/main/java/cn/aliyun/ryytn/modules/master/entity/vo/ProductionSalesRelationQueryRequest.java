package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 生产销售关系查询DTO
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "生产销售关系查询DTO")
public class ProductionSalesRelationQueryRequest {

    @ApiModelProperty("销售商品ID列表")
    private List<String> salesSkuIds;

    @ApiModelProperty("生产商品ID列表")
    private List<String> productionSkuIds;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;
}
