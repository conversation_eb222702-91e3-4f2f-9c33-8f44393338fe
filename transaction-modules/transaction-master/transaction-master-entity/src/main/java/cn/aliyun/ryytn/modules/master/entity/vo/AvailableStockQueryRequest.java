package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 可用库存查询DTO
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "可用库存查询DTO")
public class AvailableStockQueryRequest {

    @ApiModelProperty("工厂名称列表")
    private List<String> factoryNames;

    @ApiModelProperty("商品ID列表")
    private List<String> skuIds;

    @ApiModelProperty("仓库ID列表")
    private List<String> warehouseIds;

    @ApiModelProperty("生产日期列表")
    private List<String> productionDates;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;
}
