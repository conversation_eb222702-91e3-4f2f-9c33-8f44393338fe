package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 转移替代关系响应
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "转移替代关系响应")
public class TransferSubstitutionQueryPageResponse {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("原商品ID")
    private String originalSkuId;

    @ApiModelProperty("原商品编码")
    private String originalSkuCode;

    @ApiModelProperty("原商品名称")
    private String originalSkuName;

    @ApiModelProperty("替代商品ID")
    private String substituteSkuId;

    @ApiModelProperty("替代商品编码")
    private String substituteSkuCode;

    @ApiModelProperty("替代商品名称")
    private String substituteSkuName;

    @ApiModelProperty("创建人账号")
    private String createdBy;

    @ApiModelProperty("最后修改人账号")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后修改时间")
    private Date updatedTime;

    @ApiModelProperty("状态（1-有效，0-无效）")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusDesc;
}
