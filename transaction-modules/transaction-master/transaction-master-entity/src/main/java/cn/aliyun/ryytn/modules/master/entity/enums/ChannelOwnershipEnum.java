package cn.aliyun.ryytn.modules.master.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description 渠道归属枚举
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Getter
@AllArgsConstructor
public enum ChannelOwnershipEnum {

    TOC(0, "TOC"),
    TOB(1, "TOB"),
    TOC_AND_TOB(2, "TOC&TOB");

    private final Integer code;
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ChannelOwnershipEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        
        for (ChannelOwnershipEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        ChannelOwnershipEnum enumValue = getByCode(code);
        return enumValue != null ? enumValue.getDesc() : null;
    }
}
