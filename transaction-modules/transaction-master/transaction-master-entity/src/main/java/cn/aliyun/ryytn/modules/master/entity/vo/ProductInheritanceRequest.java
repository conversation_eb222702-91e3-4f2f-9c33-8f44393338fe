package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description 商品继承关系请求
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@ApiModel(description = "商品继承关系请求")
public class ProductInheritanceRequest {

    @ApiModelProperty("主键ID（修改时必填）")
    private String id;

    @ApiModelProperty(value = "原商品ID", required = true)
    @NotNull(message = "原商品ID不能为空")
    private String originalSkuId;

    @ApiModelProperty(value = "新品商品ID", required = true)
    @NotNull(message = "新品商品ID不能为空")
    private String newSkuId;

    @ApiModelProperty(value = "关系类型", required = true)
    @NotNull(message = "关系类型不能为空")
    private Integer relationType;

    @ApiModelProperty("定制店铺（当关系类型为店铺定制时必填）")
    private String customStore;

    @ApiModelProperty("定制渠道（当关系类型为渠道定制时必填）")
    private String customChannel;

    @ApiModelProperty(value = "渠道归属", required = true)
    @NotNull(message = "渠道归属不能为空")
    private Integer channelOwnership;
}
