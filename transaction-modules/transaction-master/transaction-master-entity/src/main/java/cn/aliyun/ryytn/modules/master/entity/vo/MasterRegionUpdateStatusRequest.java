package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 修改区域信息状态请求
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("修改区域信息状态请求")
public class MasterRegionUpdateStatusRequest {
    /**
     * 编码即ID
     */
    @ApiModelProperty("编码即ID")
    @NotBlank(message = "编码不能为空")
    private String id;
}
