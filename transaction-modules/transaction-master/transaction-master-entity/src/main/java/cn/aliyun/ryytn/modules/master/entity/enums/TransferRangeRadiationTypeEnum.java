package cn.aliyun.ryytn.modules.master.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.Optional;

/**
 * 辐射类型枚举
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@AllArgsConstructor
public enum TransferRangeRadiationTypeEnum {

    TOB(0, "TOB"),
    TOC(1, "TOC");

    private final Integer code;
    private final String name;


    public static TransferRangeRadiationTypeEnum getEnumByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }

        for (TransferRangeRadiationTypeEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(Integer code) {
        TransferRangeRadiationTypeEnum statusEnum = getEnumByCode(code);
        return Optional.ofNullable(statusEnum)
                .map(TransferRangeRadiationTypeEnum::getName)
                .orElse(null);
    }
}