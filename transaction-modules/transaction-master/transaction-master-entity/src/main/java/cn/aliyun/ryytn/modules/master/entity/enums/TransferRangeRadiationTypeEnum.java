package cn.aliyun.ryytn.modules.master.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * 辐射类型枚举
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@AllArgsConstructor
public enum TransferRangeRadiationTypeEnum {

    TOB(0, "TOB"),
    TOC(1, "TOC");

    private final Integer code;
    private final String name;


    public static TransferRangeRadiationTypeEnum getEnumByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }

        for (TransferRangeRadiationTypeEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(Integer code) {
        TransferRangeRadiationTypeEnum statusEnum = getEnumByCode(code);
        return Optional.ofNullable(statusEnum)
                .map(TransferRangeRadiationTypeEnum::getName)
                .orElse(null);
    }

    /**
     * 根据名称获取编码
     *
     * @param name 名称
     * @return 编码
     */
    public static Integer getCodeByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (TransferRangeRadiationTypeEnum item : TransferRangeRadiationTypeEnum.values()) {
            if (item.getName().equalsIgnoreCase(name)) {
                return item.getCode();
            }
        }
        
        return null;
    }
}