package cn.aliyun.ryytn.modules.master.entity.vo;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/4/29 18:02
 * @Description
 */
@Data
public class SapSyncMasterWarehouseRequest implements Serializable {
    /**
     * 工厂
     */
    @JSONField(name = "WERKS")
    private String plantCode;

    /**
     * 仓库编码
     */
    @JSONField(name = "LGORT")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @JSONField(name = "LGOBE")
    private String warehouseName;

    /**
     * 联系人
     */
    @JSONField(name = "ZLXR")
    private String contactPerson;

    /**
     * 手机号
     */
    @JSONField(name = "ZSJH")
    private String mobile;
    /**
     * 电话号码
     */
    @JSONField(name = "ZDHHM")
    private String telephone;

    /**
     * 所在省
     */
    @JSONField(name = "BEZEI")
    private String province;

    /**
     * 所在市
     */
    @JSONField(name = "ZSZS1")
    private String city;

    /**
     * 所在区
     */
    @JSONField(name = "ZSZQ")
    private String district;

    /**
     * 详细地址
     */
    @JSONField(name = "ZFHDZ")
    private String fullAddress;
}
