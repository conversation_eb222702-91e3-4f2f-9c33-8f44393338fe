package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 区域信息分页查询响应
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@ApiModel("区域信息分页查询响应")
public class MasterRegionPageResponse {
    /**
     * 编码即ID
     */
    @ApiModelProperty("编码即ID")
    private String id;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updatedTime;

    /**
     * 状态 1-生效 2-失效
     */
    @ApiModelProperty("状态 1-生效 2-失效")
    private Integer status;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 父级ID
     */
    @ApiModelProperty("父级ID")
    private String parentId;


    /**
     * 状态 1-生效 2-失效
     */
    @ApiModelProperty("状态 1-生效 2-失效")
    private String statusName;

    /**
     * 父级ID
     */
    @ApiModelProperty("父级名称")
    private String parentName;


    @ApiModelProperty("创建时间-字符串")
    private String createdTimeStr;

    @ApiModelProperty("修改时间-字符串")
    private String updatedTimeStr;
}
