package cn.aliyun.ryytn.modules.master.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-05-27 15:14
 */
@Data
@ApiModel("调拨路径修改参数")
public class MasterTransferPathUpdateRequest {

    @NotNull(message = "调拨路径ID不能为空")
    @ApiModelProperty(value = "调拨路径ID", required = true)
    private String id;

    @NotBlank(message = "发货仓ID不能为空")
    @ApiModelProperty(value = "发货仓ID", required = true)
    private String sendWarehouseId;

    @NotBlank(message = "收货仓ID不能为空")
    @ApiModelProperty(value = "发货仓ID", required = true)
    private String receiveWarehouseId;

    @NotNull(message = "生效时间不能为空")
    @ApiModelProperty(value = "生效时间", required = true)
    private Date activeTime;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private Date disableTime;
}
