package cn.aliyun.ryytn.modules.master.api;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangeCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangeUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangeUpdateStatusRequest;
import com.github.pagehelper.PageInfo;

/**
 * 调拨仓辐射服务接口
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface MasterTransferRangeService {

    /**
     * 分页查询调拨仓辐射
     *
     * @param condition 分页查询条件
     * @return 分页结果
     */
    PageInfo<MasterTransferRangePageResponse> queryByPage(PageCondition<MasterTransferRangePageRequest> condition);

    /**
     * 创建调拨仓辐射
     *
     * @param request 创建请求
     * @param loginId 登录用户ID
     */
    void create(MasterTransferRangeCreateRequest request, String loginId);

    /**
     * 更新调拨仓辐射
     *
     * @param request 更新请求
     * @param loginId 登录用户ID
     */
    void update(MasterTransferRangeUpdateRequest request, String loginId);

    /**
     * 更新调拨仓辐射状态
     *
     * @param request 状态更新请求
     * @param loginId 登录用户ID
     */
    void updateStatus(MasterTransferRangeUpdateStatusRequest request, String loginId);
}