package cn.aliyun.ryytn.modules.master.api;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationInvalidateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationRequest;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @Description 生产销售关系Service接口
 * @date 2024/10/10 15:00
 */
public interface ProductionSalesRelationService {

    /**
     * 新增或修改生产销售关系
     *
     * @param request 请求参数
     */
    void saveOrUpdate(ProductionSalesRelationRequest request);

    /**
     * 批量作废生产销售关系
     *
     * @param request 作废请求
     */
    void batchInvalidate(ProductionSalesRelationInvalidateRequest request);

    /**
     * 分页查询生产销售关系
     *
     * @param pageCondition 分页查询条件
     * @return 分页结果
     */
    PageInfo<ProductionSalesRelationQueryPageResponse> queryPage(PageCondition<ProductionSalesRelationQueryRequest> pageCondition);
}
