package cn.aliyun.ryytn.modules.master.api;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.sap.MasterWarehouse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehouseModifyRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehouseQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.SapSyncMasterWarehouseRequest;
import com.github.pagehelper.PageInfo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-06 15:41
 */
public interface MasterWarehouseService {
    /**
     * 同步仓库档案数据
     *
     * @param requestList sap入参
     * @param loginId
     */
    void syncMasterWarehouseInfo(List<SapSyncMasterWarehouseRequest> requestList, String loginId);

    /**
     * 分页查询仓库列表
     *
     * @param condition 分页查询条件
     * @return 分页结果
     */
    PageInfo<MasterWarehouse> queryByPage(PageCondition<MasterWarehouseQueryRequest> condition);

    /**
     * 修改仓库信息
     *
     * @param request 修改请求
     * @param loginId 登录人ID
     */
    void modifyByUser(MasterWarehouseModifyRequest request, String loginId);

    List<MasterWarehouse> queryByIds(Collection<String> ids);
}
