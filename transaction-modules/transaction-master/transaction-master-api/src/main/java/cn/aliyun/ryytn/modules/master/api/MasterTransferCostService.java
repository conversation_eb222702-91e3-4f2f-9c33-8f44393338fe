package cn.aliyun.ryytn.modules.master.api;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostUpdateStatusRequest;
import com.github.pagehelper.PageInfo;

public interface MasterTransferCostService {
    /**
     * 分页查询运输成本
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    PageInfo<MasterTransferCostPageResponse> queryByPage(PageCondition<MasterTransferCostPageRequest> condition);

    /**
     * 创建运输成本
     *
     * @param request 创建请求
     * @param loginId 登录用户ID
     */
    void create(MasterTransferCostCreateRequest request, String loginId);

    /**
     * 修改运输成本
     *
     * @param request 修改请求
     * @param loginId 登录用户ID
     */
    void update(MasterTransferCostUpdateRequest request, String loginId);

    /**
     * 修改运输成本状态
     *
     * @param request 状态修改请求
     * @param loginId 登录用户ID
     */
    void updateStatus(MasterTransferCostUpdateStatusRequest request, String loginId);
}