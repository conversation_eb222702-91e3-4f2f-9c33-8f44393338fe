package cn.aliyun.ryytn.modules.master.api;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterSkuQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterSkuSyncByCodeRequest;
import com.github.pagehelper.PageInfo;

import java.util.Collection;
import java.util.List;

/**
 * @Description 主SKU信息服务接口
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
public interface MasterSkuService
{
    /**
     *
     * @Description 从SAP同步主SKU信息
     * @param syncTime 同步日期，格式为"yyyyMMdd"，如果为null则使用前一天
     * @param productCodes 商品编码数组，如果为null或空则同步所有商品
     * @throws Exception
     * <AUTHOR>
     * @date 2024年10月10日 15:00
     */
    void syncMasterSkuFromSap(String syncTime, List<String> productCodes) throws Exception;

    /**
     * 分页查询主SKU信息
     *
     * @param pageCondition 分页查询条件
     * @return 分页结果
     */
    PageInfo<MasterSku> queryMasterSkuPage(PageCondition<MasterSkuQueryRequest> pageCondition);

    /**
     * 根据商品编码同步SKU信息（为空就更新已经存在的编码）
     *
     * @param request
     */
    void syncMasterSkuFromSapByProductCode(MasterSkuSyncByCodeRequest request);

    /**
     * 根据ID列表查询商品
     *
     * @param ids ID列表
     * @return 商品列表
     */
    List<MasterSku> queryByIds(Collection<String> ids);
}
