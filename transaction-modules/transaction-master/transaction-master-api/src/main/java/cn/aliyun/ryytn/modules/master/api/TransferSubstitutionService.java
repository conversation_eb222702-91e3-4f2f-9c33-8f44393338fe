package cn.aliyun.ryytn.modules.master.api;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionInvalidateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionRequest;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @Description 转移替代关系Service接口
 * @date 2024/10/10 15:00
 */
public interface TransferSubstitutionService {

    /**
     * 新增或修改转移替代关系
     *
     * @param request 请求参数
     */
    void saveOrUpdate(TransferSubstitutionRequest request);

    /**
     * 批量作废转移替代关系
     *
     * @param request 作废请求
     */
    void batchInvalidate(TransferSubstitutionInvalidateRequest request);

    /**
     * 分页查询转移替代关系
     *
     * @param pageCondition 分页查询条件
     * @return 分页结果
     */
    PageInfo<TransferSubstitutionQueryPageResponse> queryPage(PageCondition<TransferSubstitutionQueryRequest> pageCondition);
}
