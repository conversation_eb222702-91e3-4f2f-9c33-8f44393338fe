package cn.aliyun.ryytn.modules.master.api;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockInvalidateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockRequest;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @Description 可用库存Service接口
 * @date 2024/10/10 15:00
 */
public interface AvailableStockService {

    /**
     * 新增或修改可用库存
     *
     * @param request 请求参数
     */
    void saveOrUpdate(AvailableStockRequest request);

    /**
     * 批量作废可用库存
     *
     * @param request 作废请求
     */
    void batchInvalidate(AvailableStockInvalidateRequest request);

    /**
     * 分页查询可用库存
     *
     * @param pageCondition 分页查询条件
     * @return 分页结果
     */
    PageInfo<AvailableStockQueryPageResponse> queryPage(PageCondition<AvailableStockQueryRequest> pageCondition);
}
