package cn.aliyun.ryytn.modules.master.api;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferPathCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferPathUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferPathUpdateStatusRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageResponse;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @since 2025-05-27 13:46
 */
public interface MasterTransferPathService {
    PageInfo<MasterWarehousePathPageResponse> queryByPage(PageCondition<MasterWarehousePathPageRequest> condition);

    void create(MasterTransferPathCreateRequest request, String loginId);

    void update(MasterTransferPathUpdateRequest request, String loginId);

    /**
     * 启用/禁用
     *
     * @param request 请求参数
     * @param loginId 登录用户ID
     */
    void updateStatus(MasterTransferPathUpdateStatusRequest request, String loginId);

}
