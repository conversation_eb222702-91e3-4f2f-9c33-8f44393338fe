package cn.aliyun.ryytn.modules.master.api;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.master.entity.dto.ProductInheritanceQueryDto;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceInvalidateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceVO;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @Description 商品继承关系Service接口
 * @date 2024/10/10 15:00
 */
public interface ProductInheritanceService {

    /**
     * 新增或修改商品继承关系
     *
     * @param request 请求参数
     */
    void saveOrUpdate(ProductInheritanceRequest request);

    /**
     * 批量作废商品继承关系
     *
     * @param request 作废请求
     */
    void batchInvalidate(ProductInheritanceInvalidateRequest request);

    /**
     * 分页查询商品继承关系
     *
     * @param pageCondition 分页查询条件
     * @return 分页结果
     */
    PageInfo<ProductInheritanceVO> queryPage(PageCondition<ProductInheritanceQueryDto> pageCondition);
}
