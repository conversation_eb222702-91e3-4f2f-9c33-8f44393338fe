package cn.aliyun.ryytn.modules.master.api;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.info.MasterRegion;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionUpdateStatusRequest;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 区域信息服务接口
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface MasterRegionService {
    /**
     * 分页查询区域信息列表
     *
     * @param condition 分页查询条件
     * @return 分页查询结果
     */
    PageInfo<MasterRegionPageResponse> queryByPage(PageCondition<MasterRegionPageRequest> condition);

    /**
     * 保存区域信息
     *
     * @param request 创建区域信息请求
     * @param loginId 当前登录用户ID
     */
    void create(MasterRegionCreateRequest request, String loginId);

    /**
     * 修改区域信息
     *
     * @param request 修改区域信息请求
     * @param loginId 当前登录用户ID
     */
    void update(MasterRegionUpdateRequest request, String loginId);

    /**
     * 修改区域信息状态
     *
     * @param request 修改区域信息状态请求
     * @param loginId 当前登录用户ID
     */
    void updateStatus(MasterRegionUpdateStatusRequest request, String loginId);

    /**
     * 根据ID列表批量查询区域信息
     *
     * @param ids 区域ID列表
     * @return 区域信息列表
     */
    List<MasterRegion> queryByIds(List<String> ids);
}
