<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.TransferSubstitutionDao">

    <!-- 新增 -->
    <insert id="insert" parameterType="cn.aliyun.ryytn.common.entity.info.TransferSubstitution">
        INSERT INTO t_ryytn_master_transfer_substitution (id,
                                                          original_sku_id,
                                                          substitute_sku_id,
                                                          created_by,
                                                          updated_by,
                                                          created_time,
                                                          updated_time,
                                                          status)
        VALUES (#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{originalSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{substituteSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{createdBy},
                #{updatedBy},
                #{createdTime},
                #{updatedTime},
                #{status})
    </insert>

    <!-- 修改 -->
    <update id="update" parameterType="cn.aliyun.ryytn.common.entity.info.TransferSubstitution">
        UPDATE t_ryytn_master_transfer_substitution
        SET original_sku_id   = #{originalSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            substitute_sku_id = #{substituteSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            updated_by        = #{updatedBy},
            updated_time      = #{updatedTime}
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="string" resultType="cn.aliyun.ryytn.common.entity.info.TransferSubstitution">
        SELECT CAST(id AS VARCHAR)                as id,
               CAST(original_sku_id AS VARCHAR)   as original_sku_id,
               CAST(substitute_sku_id AS VARCHAR) as substitute_sku_id,
               created_by,
               updated_by,
               created_time,
               updated_time,
               status
        FROM t_ryytn_master_transfer_substitution
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <!-- 检查唯一性 -->
    <select id="checkUniqueness" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_transfer_substitution
        WHERE original_sku_id =
        #{originalSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        AND substitute_sku_id =
        #{substituteSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        AND status = 1
        <if test="excludeId != null and excludeId != ''">
            AND id !=
            #{excludeId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </if>
    </select>

    <!-- 批量作废 -->
    <update id="batchInvalidate">
        UPDATE t_ryytn_master_transfer_substitution
        SET status = 0,
        updated_by = #{updatedBy},
        updated_time = now()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
        AND status = 1
    </update>

    <!-- 查询已作废的记录数 -->
    <select id="countInvalidated" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_transfer_substitution
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
        AND status = 0
    </select>

    <!-- 分页查询 -->
    <select id="queryPage" parameterType="cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionQueryRequest"
            resultType="cn.aliyun.ryytn.common.entity.info.TransferSubstitution">
        SELECT CAST(id AS VARCHAR) as id,
        CAST(original_sku_id AS VARCHAR) as original_sku_id,
        CAST(substitute_sku_id AS VARCHAR) as substitute_sku_id,
        created_by,
        updated_by,
        created_time,
        updated_time,
        status
        FROM t_ryytn_master_transfer_substitution
        <where>
            <if test="originalSkuIds != null and originalSkuIds.size() > 0">
                AND original_sku_id IN
                <foreach collection="originalSkuIds" item="originalSkuId" open="(" separator="," close=")">
                    #{originalSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
                </foreach>
            </if>
            <if test="substituteSkuIds != null and substituteSkuIds.size() > 0">
                AND substitute_sku_id IN
                <foreach collection="substituteSkuIds" item="substituteSkuId" open="(" separator="," close=")">
                    #{substituteSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
                </foreach>
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>
