<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.AvailableStockDao">

    <!-- 新增 -->
    <insert id="insert" parameterType="cn.aliyun.ryytn.common.entity.info.AvailableStock">
        INSERT INTO t_ryytn_master_available_stock (id,
                                                    factory_name,
                                                    sku_id,
                                                    warehouse_id,
                                                    production_date,
                                                    quantity,
                                                    stock_date,
                                                    created_by,
                                                    updated_by,
                                                    created_time,
                                                    updated_time,
                                                    status)
        VALUES (#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{factoryName},
                #{skuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{warehouseId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{productionDate},
                #{quantity},
                #{stockDate},
                #{createdBy},
                #{updatedBy},
                #{createdTime},
                #{updatedTime},
                #{status})
    </insert>

    <!-- 修改 -->
    <update id="update" parameterType="cn.aliyun.ryytn.common.entity.info.AvailableStock">
        UPDATE t_ryytn_master_available_stock
        SET factory_name    = #{factoryName},
            sku_id          = #{skuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            warehouse_id    = #{warehouseId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            production_date = #{productionDate},
            quantity        = #{quantity},
            stock_date      = #{stockDate},
            updated_by      = #{updatedBy},
            updated_time    = #{updatedTime}
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="string" resultType="cn.aliyun.ryytn.common.entity.info.AvailableStock">
        SELECT CAST(id AS VARCHAR)           as id,
               factory_name,
               CAST(sku_id AS VARCHAR)       as sku_id,
               CAST(warehouse_id AS VARCHAR) as warehouse_id,
               production_date,
               quantity,
               stock_date,
               created_by,
               updated_by,
               created_time,
               updated_time,
               status
        FROM t_ryytn_master_available_stock
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <!-- 检查唯一性 -->
    <select id="checkUniqueness" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_available_stock
        WHERE factory_name = #{factoryName}
        AND sku_id = #{skuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        AND warehouse_id =
        #{warehouseId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        AND production_date = #{productionDate}
        AND status = 1
        <if test="excludeId != null and excludeId != ''">
            AND id !=
            #{excludeId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </if>
    </select>

    <!-- 批量作废 -->
    <update id="batchInvalidate">
        UPDATE t_ryytn_master_available_stock
        SET status = 0,
        updated_by = #{updatedBy},
        updated_time = now()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
        AND status = 1
    </update>

    <!-- 查询已作废的记录数 -->
    <select id="countInvalidated" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_available_stock
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
        AND status = 0
    </select>

    <!-- 分页查询 -->
    <select id="queryPage" parameterType="cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockQueryRequest"
            resultType="cn.aliyun.ryytn.common.entity.info.AvailableStock">
        SELECT CAST(id AS VARCHAR) as id,
        factory_name,
        CAST(sku_id AS VARCHAR) as sku_id,
        CAST(warehouse_id AS VARCHAR) as warehouse_id,
        production_date,
        quantity,
        stock_date,
        created_by,
        updated_by,
        created_time,
        updated_time,
        status
        FROM t_ryytn_master_available_stock
        <where>
            <if test="factoryNames != null and factoryNames.size() > 0">
                AND factory_name IN
                <foreach collection="factoryNames" item="factoryName" open="(" separator="," close=")">
                    #{factoryName}
                </foreach>
            </if>
            <if test="skuIds != null and skuIds.size() > 0">
                AND sku_id IN
                <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                    #{skuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
                </foreach>
            </if>
            <if test="warehouseIds != null and warehouseIds.size() > 0">
                AND warehouse_id IN
                <foreach collection="warehouseIds" item="warehouseId" open="(" separator="," close=")">
                    #{warehouseId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
                </foreach>
            </if>
            <if test="productionDates != null and productionDates.size() > 0">
                AND production_date IN
                <foreach collection="productionDates" item="productionDate" open="(" separator="," close=")">
                    #{productionDate}
                </foreach>
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>
