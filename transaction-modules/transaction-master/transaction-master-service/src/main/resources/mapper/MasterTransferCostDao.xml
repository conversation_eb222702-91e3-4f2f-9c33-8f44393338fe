<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.MasterTransferCostDao">
    <select id="queryByPage" resultType="cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageResponse">
        SELECT
        tc.id, tc.created_by, tc.updated_by, tc.created_time, tc.updated_time, tc.status,
        tc.from_province_id, fp.name as from_province_name,
        tc.from_city_id, fc.name as from_city_name,
        tc.to_province_id, tp.name as to_province_name,
        tc.to_city_id, tc2.name as to_city_name,
        tc.cost_13m, tc.cost_15m, tc.cost_rail,
        tc.road_duration, tc.rail_duration
        FROM t_ryytn_master_transfer_cost tc
        LEFT JOIN t_ryytn_master_region fp ON tc.from_province_id = fp.id
        LEFT JOIN t_ryytn_master_region fc ON tc.from_city_id = fc.id
        LEFT JOIN t_ryytn_master_region tp ON tc.to_province_id = tp.id
        LEFT JOIN t_ryytn_master_region tc2 ON tc.to_city_id = tc2.id
        <where>
            <if test="updatedByList != null and updatedByList.size() > 0">
                AND tc.updated_by IN
                <foreach collection="updatedByList" item="updatedBy" open="(" separator="," close=")">
                    #{updatedBy}
                </foreach>
            </if>
            <if test="updatedTimeStart != null">
                AND tc.updated_time >= #{updatedTimeStart}
            </if>
            <if test="updatedTimeEnd != null">
                AND tc.updated_time &lt;= #{updatedTimeEnd}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND tc.status IN
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="fromProvinceIdList != null and fromProvinceIdList.size() > 0">
                AND tc.from_province_id IN
                <foreach collection="fromProvinceIdList" item="fromProvinceId" open="(" separator=","
                         close=")">
                    #{fromProvinceId}
                </foreach>
            </if>
            <if test="fromCityIdList != null and fromCityIdList.size() > 0">
                AND tc.from_city_id IN
                <foreach collection="fromCityIdList" item="fromCityId" open="(" separator="," close=")">
                    #{fromCityId}
                </foreach>
            </if>
            <if test="toProvinceIdList != null and toProvinceIdList.size() > 0">
                AND tc.to_province_id IN
                <foreach collection="toProvinceIdList" item="toProvinceId" open="(" separator=","
                         close=")">
                    #{toProvinceId}
                </foreach>
            </if>
            <if test="toCityIdList != null and toCityIdList.size() > 0">
                AND tc.to_city_id IN
                <foreach collection="toCityIdList" item="toCityId" open="(" separator="," close=")">
                    #{toCityId}
                </foreach>
            </if>
            <if test="cost13mList != null and cost13mList.size() > 0">
                AND tc.cost_13m IN
                <foreach collection="cost13mList" item="cost13m" open="(" separator="," close=")">
                    #{cost13m}
                </foreach>
            </if>
            <if test="cost15mList != null and cost15mList.size() > 0">
                AND tc.cost_15m IN
                <foreach collection="cost15mList" item="cost15m" open="(" separator="," close=")">
                    #{cost15m}
                </foreach>
            </if>
            <if test="costRailList != null and costRailList.size() > 0">
                AND tc.cost_rail IN
                <foreach collection="costRailList" item="costRail" open="(" separator="," close=")">
                    #{costRail}
                </foreach>
            </if>
            <if test="roadDurationList != null and roadDurationList.size() > 0">
                AND tc.road_duration IN
                <foreach collection="roadDurationList" item="roadDuration" open="(" separator=","
                         close=")">
                    #{roadDuration}
                </foreach>
            </if>
            <if test="railDurationList != null and railDurationList.size() > 0">
                AND tc.rail_duration IN
                <foreach collection="railDurationList" item="railDuration" open="(" separator=","
                         close=")">
                    #{railDuration}
                </foreach>
            </if>
        </where>
        ORDER BY tc.id DESC
    </select>

    <select id="selectById" resultType="cn.aliyun.ryytn.common.entity.info.MasterTransferCost">
        SELECT *
        FROM t_ryytn_master_transfer_cost
        WHERE id = #{id}
    </select>

    <select id="checkUnique" resultType="java.lang.Integer"
            parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferCost">
        SELECT COUNT(1)
        FROM t_ryytn_master_transfer_cost
        WHERE from_province_id = #{fromProvinceId}
        AND from_city_id = #{fromCityId}
        AND to_province_id = #{toProvinceId}
        AND to_city_id = #{toCityId}
        <if test="id != null and id != ''">
            AND id != #{id}
        </if>
    </select>

    <insert id="insert" parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferCost">
        INSERT INTO t_ryytn_master_transfer_cost (id, created_by, updated_by, created_time, updated_time, status,
                                                  from_province_id, from_city_id, to_province_id, to_city_id,
                                                  cost_13m, cost_15m, cost_rail, road_duration, rail_duration)
        VALUES (#{id}, #{createdBy}, #{updatedBy}, #{createdTime}, #{updatedTime}, #{status},
                #{fromProvinceId}, #{fromCityId}, #{toProvinceId}, #{toCityId},
                #{cost13m}, #{cost15m}, #{costRail}, #{roadDuration}, #{railDuration})
    </insert>

    <update id="update" parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferCost">
        UPDATE t_ryytn_master_transfer_cost
        <set>
            <if test="fromProvinceId != null">from_province_id = #{fromProvinceId},</if>
            <if test="fromCityId != null">from_city_id = #{fromCityId},</if>
            <if test="toProvinceId != null">to_province_id = #{toProvinceId},</if>
            <if test="toCityId != null">to_city_id = #{toCityId},</if>
            <if test="cost13m != null">cost_13m = #{cost13m},</if>
            <if test="cost15m != null">cost_15m = #{cost15m},</if>
            <if test="costRail != null">cost_rail = #{costRail},</if>
            <if test="roadDuration != null">road_duration = #{roadDuration},</if>
            <if test="railDuration != null">rail_duration = #{railDuration},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime}</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateStatus">
        UPDATE t_ryytn_master_transfer_cost
        SET status       = #{status},
            updated_by   = #{updatedBy},
            updated_time = #{updatedTime}
        WHERE id = #{id}
    </update>
</mapper>