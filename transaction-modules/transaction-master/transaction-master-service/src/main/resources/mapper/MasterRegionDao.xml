<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.MasterRegionDao">
    <select id="queryByPage" resultType="cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageResponse">
        SELECT
        r.id,
        r.created_by,
        r.updated_by,
        r.created_time,
        r.updated_time,
        r.status,
        r.name,
        r.parent_id,
        p.name as parentName
        FROM t_ryytn_master_region r
        LEFT JOIN t_ryytn_master_region p ON r.parent_id = p.id
        <where>
            <if test="id != null and id != ''">
                AND r.id = #{id}
            </if>
            <if test="name != null and name != ''">
                AND r.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="parentId != null and parentId != ''">
                AND r.parent_id = #{parentId}
            </if>
            <if test="status != null">
                AND r.status = #{status}
            </if>
        </where>
        ORDER BY r.created_time DESC
    </select>

    <insert id="insert" parameterType="cn.aliyun.ryytn.common.entity.info.MasterRegion">
        INSERT INTO t_ryytn_master_region (id, created_by, updated_by, created_time, updated_time, status, name,
                                           parent_id)
        VALUES (#{id}, #{createdBy}, #{updatedBy}, #{createdTime}, #{updatedTime}, #{status}, #{name}, #{parentId})
    </insert>

    <update id="update" parameterType="cn.aliyun.ryytn.common.entity.info.MasterRegion">
        UPDATE t_ryytn_master_region
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="parentId != null and parentId != ''">
                parent_id = #{parentId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            updated_by = #{updatedBy},
            updated_time = #{updatedTime}
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateStatus" parameterType="cn.aliyun.ryytn.common.entity.info.MasterRegion">
        UPDATE t_ryytn_master_region
        SET status     = #{status},
            updated_by = #{updatedBy},
            updated_time = #{updatedTime}
        WHERE id = #{id}
    </update>

    <select id="selectById" resultType="cn.aliyun.ryytn.common.entity.info.MasterRegion">
        SELECT id,
               created_by,
               updated_by,
               created_time,
               updated_time,
               status,
               name,
               parent_id
        FROM t_ryytn_master_region
        WHERE id = #{id}
    </select>

    <select id="countOverlap" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_region
        WHERE id = #{id}
           OR name = #{name}
    </select>
</mapper>
