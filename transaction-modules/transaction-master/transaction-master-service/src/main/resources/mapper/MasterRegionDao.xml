<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.MasterRegionDao">
    <select id="queryByPage" resultType="cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageResponse">
        SELECT
        r.id,
        r.created_by,
        r.updated_by,
        r.created_time,
        r.updated_time,
        r.status,
        r.name,
        r.parent_id,
        p.name as parentName
        FROM t_ryytn_master_region r
        LEFT JOIN t_ryytn_master_region p ON r.parent_id = p.id
        <where>
            <if test="idList != null and idList.size() > 0">
                AND r.id IN
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="nameList != null and nameList.size() > 0">
                AND r.name IN
                <foreach collection="nameList" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>
            <if test="parentIdList != null and parentIdList.size() > 0">
                AND r.parent_id IN
                <foreach collection="parentIdList" item="parentId" open="(" separator="," close=")">
                    #{parentId}
                </foreach>
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND r.status IN
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="updatedByList != null and updatedByList.size() > 0">
                AND r.updated_by IN
                <foreach collection="updatedByList" item="updatedBy" open="(" separator="," close=")">
                    #{updatedBy}
                </foreach>
            </if>
            <if test="updatedTimeStart != null">
                AND r.updated_time &gt;= #{updatedTimeStart}
            </if>
            <if test="updatedTimeEnd != null">
                AND r.updated_time &lt;= #{updatedTimeEnd}
            </if>
        </where>
        ORDER BY r.created_time DESC
    </select>

    <insert id="insert" parameterType="cn.aliyun.ryytn.common.entity.info.MasterRegion">
        INSERT INTO t_ryytn_master_region (id, created_by, updated_by, created_time, updated_time, status, name,
                                           parent_id)
        VALUES (#{id}, #{createdBy}, #{updatedBy}, #{createdTime}, #{updatedTime}, #{status}, #{name}, #{parentId})
    </insert>

    <update id="update" parameterType="cn.aliyun.ryytn.common.entity.info.MasterRegion">
        UPDATE t_ryytn_master_region
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="parentId != null and parentId != ''">
                parent_id = #{parentId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            updated_by = #{updatedBy},
            updated_time = #{updatedTime}
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateStatus" parameterType="cn.aliyun.ryytn.common.entity.info.MasterRegion">
        UPDATE t_ryytn_master_region
        SET status     = #{status},
            updated_by = #{updatedBy},
            updated_time = #{updatedTime}
        WHERE id = #{id}
    </update>

    <select id="selectById" resultType="cn.aliyun.ryytn.common.entity.info.MasterRegion">
        SELECT id,
               created_by,
               updated_by,
               created_time,
               updated_time,
               status,
               name,
               parent_id
        FROM t_ryytn_master_region
        WHERE id = #{id}
    </select>

    <select id="countOverlap" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_region
        WHERE id = #{id}
    </select>

    <select id="selectByIds" resultType="cn.aliyun.ryytn.common.entity.info.MasterRegion">
        SELECT *
        FROM t_ryytn_master_region
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
