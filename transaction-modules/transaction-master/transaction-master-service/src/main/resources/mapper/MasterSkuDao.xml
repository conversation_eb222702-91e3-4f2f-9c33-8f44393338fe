<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.MasterSkuDao">


    <!-- 根据商品编码列表批量查询主SKU信息 -->
    <select id="queryByProductCodes" parameterType="java.util.List" resultType="cn.aliyun.ryytn.common.entity.sap.MasterSku">
        SELECT
            id,
            product_code,
            product_name,
            create_time,
            update_time,
            material_type,
            material_type_desc,
            material_group,
            material_group_desc,
            base_unit_desc,
            gross_weight,
            net_weight,
            weight_unit,
            barcode,
            brand,
            short_name,
            model_spec,
            shelf_life,
            length,
            width,
            height,
            volume,
            batch_management_flag,
            layer11_unit,
            layer9_quantity,
            layer10_quantity,
            tax_classification,
            sn_enabled,
            traceability_enabled,
            primary_category,
            secondary_category,
            tertiary_category,
            quaternary_category,
            off_market_status,
            off_market_date,
            product_status,
            zero_level_desc,
            zero_level_code,
            sap_create_date,
            sap_create_time,
            sap_update_date,
            sap_updater
        FROM t_ryytn_master_sku
        WHERE product_code IN
        <foreach collection="list" item="productCode" open="(" close=")" separator=",">
            #{productCode}
        </foreach>
    </select>



    <!-- 批量新增主SKU信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_ryytn_master_sku (
            id,
            product_code,
            product_name,
            create_time,
            update_time,
            material_type,
            material_type_desc,
            material_group,
            material_group_desc,
            base_unit_desc,
            gross_weight,
            net_weight,
            weight_unit,
            barcode,
            brand,
            short_name,
            model_spec,
            shelf_life,
            length,
            width,
            height,
            volume,
            batch_management_flag,
            layer11_unit,
            layer9_quantity,
            layer10_quantity,
            tax_classification,
            sn_enabled,
            traceability_enabled,
            primary_category,
            secondary_category,
            tertiary_category,
            quaternary_category,
            off_market_status,
            off_market_date,
            product_status,
            zero_level_desc,
            zero_level_code,
            sap_create_date,
            sap_create_time,
            sap_update_date,
            sap_updater
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{item.productCode},
            #{item.productName},
            #{item.createTime},
            #{item.updateTime},
            #{item.materialType},
            #{item.materialTypeDesc},
            #{item.materialGroup},
            #{item.materialGroupDesc},
            #{item.baseUnitDesc},
            #{item.grossWeight},
            #{item.netWeight},
            #{item.weightUnit},
            #{item.barcode},
            #{item.brand},
            #{item.shortName},
            #{item.modelSpec},
            #{item.shelfLife},
            #{item.length},
            #{item.width},
            #{item.height},
            #{item.volume},
            #{item.batchManagementFlag},
            #{item.layer11Unit},
            #{item.layer9Quantity},
            #{item.layer10Quantity},
            #{item.taxClassification},
            #{item.snEnabled},
            #{item.traceabilityEnabled},
            #{item.primaryCategory},
            #{item.secondaryCategory},
            #{item.tertiaryCategory},
            #{item.quaternaryCategory},
            #{item.offMarketStatus},
            #{item.offMarketDate},
            #{item.productStatus},
            #{item.zeroLevelDesc},
            #{item.zeroLevelCode},
            #{item.sapCreateDate},
            #{item.sapCreateTime},
            #{item.sapUpdateDate},
            #{item.sapUpdater}
        )
        </foreach>
    </insert>







    <!-- 批量更新主SKU信息 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE t_ryytn_master_sku
            SET
                product_code = #{item.productCode},
                product_name = #{item.productName},
                update_time = #{item.updateTime},
                material_type = #{item.materialType},
                material_type_desc = #{item.materialTypeDesc},
                material_group = #{item.materialGroup},
                material_group_desc = #{item.materialGroupDesc},
                base_unit_desc = #{item.baseUnitDesc},
                gross_weight = #{item.grossWeight},
                net_weight = #{item.netWeight},
                weight_unit = #{item.weightUnit},
                barcode = #{item.barcode},
                brand = #{item.brand},
                short_name = #{item.shortName},
                model_spec = #{item.modelSpec},
                shelf_life = #{item.shelfLife},
                length = #{item.length},
                width = #{item.width},
                height = #{item.height},
                volume = #{item.volume},
                batch_management_flag = #{item.batchManagementFlag},
                layer11_unit = #{item.layer11Unit},
                layer9_quantity = #{item.layer9Quantity},
                layer10_quantity = #{item.layer10Quantity},
                tax_classification = #{item.taxClassification},
                sn_enabled = #{item.snEnabled},
                traceability_enabled = #{item.traceabilityEnabled},
                primary_category = #{item.primaryCategory},
                secondary_category = #{item.secondaryCategory},
                tertiary_category = #{item.tertiaryCategory},
                quaternary_category = #{item.quaternaryCategory},
                off_market_status = #{item.offMarketStatus},
                off_market_date = #{item.offMarketDate},
                product_status = #{item.productStatus},
                zero_level_desc = #{item.zeroLevelDesc},
                zero_level_code = #{item.zeroLevelCode},
                sap_create_date = #{item.sapCreateDate},
                sap_create_time = #{item.sapCreateTime},
                sap_update_date = #{item.sapUpdateDate},
                sap_updater = #{item.sapUpdater}
            WHERE id = #{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
    </update>

    <!-- 分页查询主SKU信息 -->
    <select id="queryMasterSkuPage" parameterType="cn.aliyun.ryytn.modules.master.entity.vo.MasterSkuQueryRequest" resultType="cn.aliyun.ryytn.common.entity.sap.MasterSku">
        SELECT
            id,
            product_code,
            product_name,
            create_time,
            update_time,
            material_type,
            material_type_desc,
            material_group,
            material_group_desc,
            base_unit_desc,
            gross_weight,
            net_weight,
            weight_unit,
            barcode,
            brand,
            short_name,
            model_spec,
            shelf_life,
            length,
            width,
            height,
            volume,
            batch_management_flag,
            layer11_unit,
            layer9_quantity,
            layer10_quantity,
            tax_classification,
            sn_enabled,
            traceability_enabled,
            primary_category,
            secondary_category,
            tertiary_category,
            quaternary_category,
            off_market_status,
            off_market_date,
            product_status,
            zero_level_desc,
            zero_level_code,
            sap_create_date,
            sap_create_time,
            sap_update_date,
            sap_updater
        FROM t_ryytn_master_sku
        <where>
            <if test="condition != null">
                <if test="condition.productCode != null and condition.productCode != ''">
                    AND product_code = #{condition.productCode}
                </if>
                <if test="condition.productName != null and condition.productName != ''">
                    AND product_name LIKE CONCAT('%', #{condition.productName}, '%')
                </if>
                <if test="condition.primaryCategory != null and condition.primaryCategory != ''">
                    AND primary_category = #{condition.primaryCategory}
                </if>
                <if test="condition.secondaryCategory != null and condition.secondaryCategory != ''">
                    AND secondary_category = #{condition.secondaryCategory}
                </if>
                <if test="condition.tertiaryCategory != null and condition.tertiaryCategory != ''">
                    AND tertiary_category = #{condition.tertiaryCategory}
                </if>
                <if test="condition.quaternaryCategory != null and condition.quaternaryCategory != ''">
                    AND quaternary_category = #{condition.quaternaryCategory}
                </if>
                <if test="condition.zeroLevelCode != null and condition.zeroLevelCode != ''">
                    AND zero_level_code = #{condition.zeroLevelCode}
                </if>
                <if test="condition.zeroLevelDesc != null and condition.zeroLevelDesc != ''">
                    AND zero_level_desc LIKE CONCAT('%', #{condition.zeroLevelDesc}, '%')
                </if>
                <if test="condition.productStatus != null and condition.productStatus != ''">
                    AND product_status = #{condition.productStatus}
                </if>
                <if test="condition.offMarketStatus != null and condition.offMarketStatus != ''">
                    AND off_market_status = #{condition.offMarketStatus}
                </if>
                <if test="condition.offMarketDateStart != null">
                    AND off_market_date >= #{condition.offMarketDateStart}
                </if>
                <if test="condition.offMarketDateEnd != null">
                    AND off_market_date &lt;= #{condition.offMarketDateEnd}
                </if>
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 查询所有商品编码 -->
    <select id="queryAllProductCodes" resultType="java.lang.String">
        SELECT product_code FROM t_ryytn_master_sku
    </select>

    <!-- 根据ID列表查询主SKU信息 -->
    <select id="queryByIds" resultType="cn.aliyun.ryytn.common.entity.sap.MasterSku">
        SELECT id,
               product_code,
               product_name,
               primary_category,
               secondary_category,
               tertiary_category,
               quaternary_category,
               product_status,
               off_market_status,
               off_market_date,
               zero_level_desc,
               zero_level_code,
               sap_create_date,
               sap_create_time,
               sap_update_date,
               sap_updater,
               create_time,
               update_time
        FROM t_ryytn_master_sku
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
    </select>
</mapper>