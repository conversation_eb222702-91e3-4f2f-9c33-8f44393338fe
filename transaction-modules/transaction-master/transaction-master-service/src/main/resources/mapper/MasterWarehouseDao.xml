<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.MasterWarehouseDao">
    <select id="selectById" resultType="cn.aliyun.ryytn.common.entity.sap.MasterWarehouse">
        SELECT *
        FROM t_ryytn_master_warehouse
        WHERE id = #{id}
    </select>

    <!-- 批量查询 -->
    <select id="selectByWarehouseCodes" resultType="cn.aliyun.ryytn.common.entity.sap.MasterWarehouse">
        SELECT id, warehouse_code
        FROM t_ryytn_master_warehouse
        WHERE warehouse_code IN
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert">
        INSERT INTO t_ryytn_master_warehouse (
        id, warehouse_code, plant_code, warehouse_name,
        contact_person, mobile, telephone, province,
        city, district, full_address,
        created_by,updated_by,created_time, updated_time
        ) VALUES
        <foreach item="item" collection="list" separator=",">
            (
            #{item.id}, #{item.warehouseCode}, #{item.plantCode}, #{item.warehouseName},
            #{item.contactPerson}, #{item.mobile}, #{item.telephone}, #{item.province},
            #{item.city}, #{item.district}, #{item.fullAddress},
            #{item.createdBy}, #{item.updatedBy}, #{item.createdTime}, #{item.updatedTime}
            )
        </foreach>
    </insert>

    <!-- 批量更新 -->
    <update id="batchUpdate">
        <foreach item="item" collection="list" separator=";">
            UPDATE t_ryytn_master_warehouse SET
            plant_code = #{item.plantCode},
            warehouse_name = #{item.warehouseName},
            contact_person = #{item.contactPerson},
            mobile = #{item.mobile},
            telephone = #{item.telephone},
            province = #{item.province},
            city = #{item.city},
            district = #{item.district},
            full_address = #{item.fullAddress},
            updated_by = #{item.updatedBy},
            updated_time = #{item.updatedTime}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByPage" resultType="cn.aliyun.ryytn.common.entity.sap.MasterWarehouse">
        SELECT * FROM t_ryytn_master_warehouse
        <where>
            <if test="condition != null">
                <if test="condition.plantCode != null and condition.plantCode != ''">
                    AND plant_code = #{condition.plantCode}
                </if>
                <if test="condition.warehouseCode != null and condition.warehouseCode != ''">
                    AND warehouse_code = #{condition.warehouseCode}
                </if>
                <if test="condition.warehouseName != null and condition.warehouseName != ''">
                    AND warehouse_name LIKE CONCAT('%', #{condition.warehouseName}, '%')
                </if>
                <if test="condition.warehouseType != null and condition.warehouseType != ''">
                    AND warehouse_type = #{condition.warehouseType}
                </if>
                <if test="condition.province != null and condition.province != ''">
                    AND province LIKE CONCAT('%', #{condition.province}, '%')
                </if>
                <if test="condition.phyWarehouseName != null and condition.phyWarehouseName != ''">
                    AND phy_warehouse_name LIKE CONCAT('%', #{condition.phyWarehouseName}, '%')
                </if>
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <update id="updateByUser">
        UPDATE t_ryytn_master_warehouse
        <set>
            warehouse_type = #{warehouseType},
            phy_warehouse_name = #{phyWarehouseName},
            updated_by = #{updatedBy},
            updated_time = #{updatedTime}
        </set>
        WHERE id = #{id}
    </update>
</mapper>
