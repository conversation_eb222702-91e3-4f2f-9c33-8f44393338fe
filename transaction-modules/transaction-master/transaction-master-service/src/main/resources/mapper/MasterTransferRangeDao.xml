<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.MasterTransferRangeDao">
    <select id="queryByPage" resultType="cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageResponse">
        SELECT
        tr.id,
        tr.created_by,
        tr.updated_by,
        tr.created_time,
        tr.updated_time,
        tr.status,
        tr.radiation_type,
        tr.sku_id,
        tr.province_id,
        tr.city_id,
        tr.warehouse_id,
        tr.sales_department,
        tr.start_kg,
        tr.end_kg,
        s.product_code, s.product_name, s.primary_category, s.quaternary_category,
        p.name as province_name,
        c.name as city_name,
        w.warehouse_code, w.warehouse_name
        FROM t_ryytn_master_transfer_range tr
        LEFT JOIN t_ryytn_master_sku s ON tr.sku_id = s.id
        LEFT JOIN t_ryytn_master_region p ON tr.province_id = p.id
        LEFT JOIN t_ryytn_master_region c ON tr.city_id = c.id
        LEFT JOIN t_ryytn_master_warehouse w ON tr.warehouse_id = w.id
        <where>
            <if test="updatedByList != null and updatedByList.size() > 0">
                AND tr.updated_by IN
                <foreach collection="updatedByList" item="updatedBy" open="(" separator="," close=")">
                    #{updatedBy}
                </foreach>
            </if>
            <if test="updatedTimeStart != null">
                AND tr.updated_time &gt;= #{updatedTimeStart}
            </if>
            <if test="updatedTimeEnd != null">
                AND tr.updated_time &lt;= #{updatedTimeEnd}
            </if>
            <if test="radiationTypeList != null and radiationTypeList.size() > 0">
                AND tr.radiation_type IN
                <foreach collection="radiationTypeList" item="radiationType" open="(" separator="," close=")">
                    #{radiationType}
                </foreach>
            </if>
            <if test="skuIdList != null and skuIdList.size() > 0">
                AND tr.sku_id IN
                <foreach collection="skuIdList" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test="provinceIdList != null and provinceIdList.size() > 0">
                AND tr.province_id IN
                <foreach collection="provinceIdList" item="provinceId" open="(" separator="," close=")">
                    #{provinceId}
                </foreach>
            </if>
            <if test="cityIdList != null and cityIdList.size() > 0">
                AND tr.city_id IN
                <foreach collection="cityIdList" item="cityId" open="(" separator="," close=")">
                    #{cityId}
                </foreach>
            </if>
            <if test="warehouseIdList != null and warehouseIdList.size() > 0">
                AND tr.warehouse_id IN
                <foreach collection="warehouseIdList" item="warehouseId" open="(" separator="," close=")">
                    #{warehouseId}
                </foreach>
            </if>
            <if test="salesDepartmentList != null and salesDepartmentList.size() > 0">
                AND tr.sales_department IN
                <foreach collection="salesDepartmentList" item="salesDepartment" open="(" separator="," close=")">
                    #{salesDepartment}
                </foreach>
            </if>
            <if test="startKgList != null and startKgList.size() > 0">
                AND tr.start_kg IN
                <foreach collection="startKgList" item="startKg" open="(" separator="," close=")">
                    #{startKg}
                </foreach>
            </if>
            <if test="endKgList != null and endKgList.size() > 0">
                AND tr.end_kg IN
                <foreach collection="endKgList" item="endKg" open="(" separator="," close=")">
                    #{endKg}
                </foreach>
            </if>
        </where>
        ORDER BY tr.id DESC
    </select>

    <!-- 根据ID查询 -->
    <select id="selectById" resultType="cn.aliyun.ryytn.common.entity.info.MasterTransferRange">
        SELECT *
        FROM t_ryytn_master_transfer_range
        WHERE id = #{id}
    </select>

    <!-- 检查组合唯一性 -->
    <select id="countOverlap" resultType="java.lang.Integer"
            parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferRange">
        SELECT COUNT(1)
        FROM t_ryytn_master_transfer_range
        WHERE status=1
        AND warehouse_id = #{warehouseId}
        <if test="provinceId != null and provinceId != ''">
            AND province_id = #{provinceId}
        </if>
        <if test="cityId != null and cityId != ''">
            AND city_id = #{cityId}
        </if>
        <if test="skuId != null and skuId != ''">
            AND sku_id =
            #{skuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </if>
        <if test="salesDepartment != null and salesDepartment != ''">
            AND sales_department = #{salesDepartment}
        </if>
        <if test="startKg != null">
            AND start_kg = #{startKg}
        </if>
        <if test="endKg != null">
            AND end_kg = #{endKg}
        </if>
        <if test="id != null and id != ''">
            AND id != #{id}
        </if>
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferRange"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO t_ryytn_master_transfer_range (created_by, updated_by, created_time, updated_time, status,
                                                   radiation_type, sku_id, province_id, city_id, warehouse_id,
                                                   sales_department, start_kg, end_kg)
        VALUES (#{createdBy}, #{updatedBy}, #{createdTime}, #{updatedTime}, #{status},
                #{radiationType}, #{skuId}, #{provinceId}, #{cityId}, #{warehouseId},
                #{salesDepartment}, #{startKg}, #{endKg})
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferRange">
        UPDATE t_ryytn_master_transfer_range
        <set>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="radiationType != null">radiation_type = #{radiationType},</if>
            <if test="skuId != null">sku_id = #{skuId},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="salesDepartment != null">sales_department = #{salesDepartment},</if>
            <if test="startKg != null">start_kg = #{startKg},</if>
            <if test="endKg != null">end_kg = #{endKg},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新状态 -->
    <update id="updateStatus" parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferRange">
        UPDATE t_ryytn_master_transfer_range
        SET status       = #{status},
            updated_by   = #{updatedBy},
            updated_time = #{updatedTime}
        WHERE id = #{id}
    </update>
</mapper>