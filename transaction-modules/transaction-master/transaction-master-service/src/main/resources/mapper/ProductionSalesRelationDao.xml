<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.ProductionSalesRelationDao">

    <!-- 新增 -->
    <insert id="insert" parameterType="cn.aliyun.ryytn.common.entity.info.ProductionSalesRelation">
        INSERT INTO t_ryytn_master_production_sales_relation (id,
                                                              sales_sku_id,
                                                              production_sku_id,
                                                              conversion_factor,
                                                              created_by,
                                                              updated_by,
                                                              created_time,
                                                              updated_time,
                                                              status)
        VALUES (#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{salesSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{productionSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{conversionFactor},
                #{createdBy},
                #{updatedBy},
                #{createdTime},
                #{updatedTime},
                #{status})
    </insert>

    <!-- 修改 -->
    <update id="update" parameterType="cn.aliyun.ryytn.common.entity.info.ProductionSalesRelation">
        UPDATE t_ryytn_master_production_sales_relation
        SET sales_sku_id      = #{salesSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            production_sku_id = #{productionSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            conversion_factor = #{conversionFactor},
            updated_by        = #{updatedBy},
            updated_time      = #{updatedTime}
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="string"
            resultType="cn.aliyun.ryytn.common.entity.info.ProductionSalesRelation">
        SELECT CAST(id AS VARCHAR)                as id,
               CAST(sales_sku_id AS VARCHAR)      as sales_sku_id,
               CAST(production_sku_id AS VARCHAR) as production_sku_id,
               conversion_factor,
               created_by,
               updated_by,
               created_time,
               updated_time,
               status
        FROM t_ryytn_master_production_sales_relation
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <!-- 检查唯一性 -->
    <select id="checkUniqueness" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_production_sales_relation
        WHERE sales_sku_id =
        #{salesSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        AND production_sku_id =
        #{productionSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        AND status = 1
        <if test="excludeId != null and excludeId != ''">
            AND id !=
            #{excludeId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </if>
    </select>

    <!-- 批量作废 -->
    <update id="batchInvalidate">
        UPDATE t_ryytn_master_production_sales_relation
        SET status = 0,
        updated_by = #{updatedBy},
        updated_time = now()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
        AND status = 1
    </update>

    <!-- 查询已作废的记录数 -->
    <select id="countInvalidated" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_production_sales_relation
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
        AND status = 0
    </select>

    <!-- 分页查询 -->
    <select id="queryPage" parameterType="cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationQueryRequest"
            resultType="cn.aliyun.ryytn.common.entity.info.ProductionSalesRelation">
        SELECT CAST(id AS VARCHAR) as id,
        CAST(sales_sku_id AS VARCHAR) as sales_sku_id,
        CAST(production_sku_id AS VARCHAR) as production_sku_id,
        conversion_factor,
        created_by,
        updated_by,
        created_time,
        updated_time,
        status
        FROM t_ryytn_master_production_sales_relation
        <where>
            <if test="salesSkuIds != null and salesSkuIds.size() > 0">
                AND sales_sku_id IN
                <foreach collection="salesSkuIds" item="salesSkuId" open="(" separator="," close=")">
                    #{salesSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
                </foreach>
            </if>
            <if test="productionSkuIds != null and productionSkuIds.size() > 0">
                AND production_sku_id IN
                <foreach collection="productionSkuIds" item="productionSkuId" open="(" separator="," close=")">
                    #{productionSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
                </foreach>
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>
