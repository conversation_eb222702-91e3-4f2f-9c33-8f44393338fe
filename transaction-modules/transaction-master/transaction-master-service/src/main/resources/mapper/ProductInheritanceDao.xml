<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.ProductInheritanceDao">

    <!-- 新增 -->
    <insert id="insert" parameterType="cn.aliyun.ryytn.common.entity.info.ProductInheritance">
        INSERT INTO t_ryytn_master_product_inheritance (id,
                                                        original_sku_id,
                                                        new_sku_id,
                                                        relation_type,
                                                        custom_store,
                                                        custom_channel,
                                                        channel_ownership,
                                                        created_by,
                                                        updated_by,
                                                        created_time,
                                                        updated_time,
                                                        status)
        VALUES (#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{originalSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{newSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
                #{relationType},
                #{customStore},
                #{customChannel},
                #{channelOwnership},
                #{createdBy},
                #{updatedBy},
                #{createdTime},
                #{updatedTime},
                #{status})
    </insert>

    <!-- 修改 -->
    <update id="update" parameterType="cn.aliyun.ryytn.common.entity.info.ProductInheritance">
        UPDATE t_ryytn_master_product_inheritance
        SET original_sku_id   = #{originalSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            new_sku_id        = #{newSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            relation_type     = #{relationType},
            custom_store      = #{customStore},
            custom_channel    = #{customChannel},
            channel_ownership = #{channelOwnership},
            updated_by        = #{updatedBy},
            updated_time      = #{updatedTime}
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="string" resultType="cn.aliyun.ryytn.common.entity.info.ProductInheritance">
        SELECT CAST(id AS VARCHAR)              as id,
               CAST(original_sku_id AS VARCHAR) as original_sku_id,
               CAST(new_sku_id AS VARCHAR)      as new_sku_id,
               relation_type,
               custom_store,
               custom_channel,
               channel_ownership,
               created_by,
               updated_by,
               created_time,
               updated_time,
               status
        FROM t_ryytn_master_product_inheritance
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <!-- 检查唯一性 -->
    <select id="checkUniqueness" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_product_inheritance
        WHERE original_sku_id =
        #{originalSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        AND new_sku_id =
        #{newSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        AND relation_type = #{relationType}
        AND (custom_store = #{customStore} OR (custom_store IS NULL AND #{customStore} IS NULL))
        AND (custom_channel = #{customChannel} OR (custom_channel IS NULL AND #{customChannel} IS NULL))
        AND channel_ownership = #{channelOwnership}
        AND status = 1
        <if test="excludeId != null and excludeId != ''">
            AND id !=
            #{excludeId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </if>
    </select>

    <!-- 批量作废 -->
    <update id="batchInvalidate">
        UPDATE t_ryytn_master_product_inheritance
        SET status = 0,
        updated_by = #{updatedBy},
        updated_time = now()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
        AND status = 1
    </update>

    <!-- 查询已作废的记录数 -->
    <select id="countInvalidated" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_product_inheritance
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
        AND status = 0
    </select>

    <!-- 分页查询 -->
    <select id="queryPage" parameterType="cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceQueryRequest"
            resultType="cn.aliyun.ryytn.common.entity.info.ProductInheritance">
        SELECT CAST(id AS VARCHAR) as id,
        CAST(original_sku_id AS VARCHAR) as original_sku_id,
        CAST(new_sku_id AS VARCHAR) as new_sku_id,
        relation_type,
        custom_store,
        custom_channel,
        channel_ownership,
        created_by,
        updated_by,
        created_time,
        updated_time,
        status
        FROM t_ryytn_master_product_inheritance
        <where>
            <if test="originalSkuIds != null and originalSkuIds.size() > 0">
                AND original_sku_id IN
                <foreach collection="originalSkuIds" item="originalSkuId" open="(" separator="," close=")">
                    #{originalSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
                </foreach>
            </if>
            <if test="newSkuIds != null and newSkuIds.size() > 0">
                AND new_sku_id IN
                <foreach collection="newSkuIds" item="newSkuId" open="(" separator="," close=")">
                    #{newSkuId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
                </foreach>
            </if>
            <if test="relationTypes != null and relationTypes.size() > 0">
                AND relation_type IN
                <foreach collection="relationTypes" item="relationType" open="(" separator="," close=")">
                    #{relationType}
                </foreach>
            </if>
            <if test="customStores != null and customStores.size() > 0">
                AND custom_store IN
                <foreach collection="customStores" item="customStore" open="(" separator="," close=")">
                    #{customStore}
                </foreach>
            </if>
            <if test="customChannels != null and customChannels.size() > 0">
                AND custom_channel IN
                <foreach collection="customChannels" item="customChannel" open="(" separator="," close=")">
                    #{customChannel}
                </foreach>
            </if>
            <if test="channelOwnerships != null and channelOwnerships.size() > 0">
                AND channel_ownership IN
                <foreach collection="channelOwnerships" item="channelOwnership" open="(" separator="," close=")">
                    #{channelOwnership}
                </foreach>
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>
