<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.master.dao.MasterTransferPathDao">
    <!-- 分页查询调拨路径 -->
    <select id="selectByPage" resultType="cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageResponse">
        SELECT
        p.id,
        p.send_warehouse_id AS sendWarehouseId,
        sw.warehouse_name AS sendWarehouseName,
        p.receive_warehouse_id AS receiveWarehouseId,
        rw.warehouse_name AS receiveWarehouseName,
        p.status,
        p.active_time AS activeTime,
        p.disable_time AS disableTime,
        p.created_by AS createdBy,
        p.created_time AS createdTime,
        p.updated_by AS updatedBy,
        p.updated_time AS updatedTime
        FROM t_ryytn_master_transfer_path p
        LEFT JOIN t_ryytn_master_warehouse sw ON p.send_warehouse_id = sw.id
        LEFT JOIN t_ryytn_master_warehouse rw ON p.receive_warehouse_id = rw.id
        <where>
            <!-- 基本条件 -->
            <if test="status != null">
                AND p.status = #{status}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                AND p.updated_by = #{updatedBy}
            </if>
            <!-- 时间范围条件 -->
            <if test="updatedTimeStart != null">
                AND p.updated_time &gt;= #{updatedTimeStart}
            </if>
            <if test="updatedTimeEnd != null">
                AND p.updated_time &lt;= #{updatedTimeEnd}
            </if>
            <if test="activeTimeStart != null">
                AND p.active_time &gt;= #{activeTimeStart}
            </if>
            <if test="activeTimeEnd != null">
                AND p.active_time &lt;= #{activeTimeEnd}
            </if>
            <if test="disableTimeStart != null">
                AND p.disable_time &gt;= #{disableTimeStart}
            </if>
            <if test="disableTimeEnd != null">
                AND p.disable_time &lt;= #{disableTimeEnd}
            </if>
            <!-- 仓库ID条件 -->
            <if test="sendWarehouseId != null and sendWarehouseId != ''">
                AND p.send_warehouse_id = #{sendWarehouseId}
            </if>
            <if test="receiveWarehouseId != null and receiveWarehouseId != ''">
                AND p.receive_warehouse_id = #{receiveWarehouseId}
            </if>
        </where>
        ORDER BY p.id DESC
    </select>

    <!-- 插入调拨路径 -->
    <insert id="insert" parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferPath">
        INSERT INTO t_ryytn_master_transfer_path (id,
                                                  created_by,
                                                  updated_by,
                                                  created_time,
                                                  updated_time,
                                                  status,
                                                  send_warehouse_id,
                                                  receive_warehouse_id,
                                                  active_time,
                                                  disable_time)
        VALUES (#{id},
                #{createdBy},
                #{updatedBy},
                #{createdTime},
                #{updatedTime},
                #{status},
                #{sendWarehouseId},
                #{receiveWarehouseId},
                #{activeTime},
                #{disableTime})
    </insert>

    <!-- 更新调拨路径 -->
    <update id="update" parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferPath">
        UPDATE t_ryytn_master_transfer_path
        <set>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sendWarehouseId != null">send_warehouse_id = #{sendWarehouseId},</if>
            <if test="receiveWarehouseId != null">receive_warehouse_id = #{receiveWarehouseId},</if>
            <if test="activeTime != null">active_time = #{activeTime},</if>
            <if test="disableTime != null">disable_time = #{disableTime}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新调拨路径状态 -->
    <update id="updateStatus" parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferPath">
        UPDATE t_ryytn_master_transfer_path
        SET status       = #{status},
            updated_by   = #{updatedBy},
            updated_time = #{updatedTime}
        WHERE id = #{id}
    </update>

    <select id="selectById" resultType="cn.aliyun.ryytn.common.entity.info.MasterTransferPath">
        SELECT *
        FROM t_ryytn_master_transfer_path
        WHERE id = #{id}
    </select>

    <select id="countOverlap" parameterType="cn.aliyun.ryytn.common.entity.info.MasterTransferPath" resultType="int">
        SELECT COUNT(1)
        FROM t_ryytn_master_transfer_path
        WHERE send_warehouse_id = #{sendWarehouseId}
        AND receive_warehouse_id = #{receiveWarehouseId}
        AND status = 1
        AND active_time = #{activeTime}
        AND disable_time = #{disableTime}
        <if test="id != null and id != ''">
            AND id != #{id}
        </if>
    </select>

</mapper>