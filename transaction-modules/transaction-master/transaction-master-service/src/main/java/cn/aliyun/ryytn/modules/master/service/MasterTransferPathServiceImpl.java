package cn.aliyun.ryytn.modules.master.service;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.info.MasterTransferPath;
import cn.aliyun.ryytn.common.entity.sap.MasterWarehouse;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.master.api.MasterTransferPathService;
import cn.aliyun.ryytn.modules.master.api.MasterWarehouseService;
import cn.aliyun.ryytn.modules.master.dao.MasterTransferPathDao;
import cn.aliyun.ryytn.modules.master.entity.enums.EntityStatusEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferPathCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferPathUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferPathUpdateStatusRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageResponse;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 调拨路径服务实现
 */
@Slf4j
@Service
public class MasterTransferPathServiceImpl implements MasterTransferPathService {
    @Resource
    private MasterTransferPathDao masterTransferPathDao;

    @Resource
    private MasterWarehouseService masterWarehouseService;

    @Override
    public PageInfo<MasterWarehousePathPageResponse> queryByPage(PageCondition<MasterWarehousePathPageRequest> condition) {
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        List<MasterWarehousePathPageResponse> responseList = masterTransferPathDao.selectByPage(condition.getCondition());
        for (MasterWarehousePathPageResponse response : responseList) {
            response.setStatusName(EntityStatusEnum.getNameByCode(response.getStatus()));
        }

        return new PageInfo<>(responseList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(MasterTransferPathCreateRequest request, String loginId) {
        // 验证时间范围
        if (request.getActiveTime().after(request.getDisableTime())) {
            throw new ServiceException("生效时间不能晚于结束时间");
        }

        checkWarehouseExists(request.getSendWarehouseId(), request.getReceiveWarehouseId());

        // 转换并保存
        MasterTransferPath entity = new MasterTransferPath();
        entity.setSendWarehouseId(request.getSendWarehouseId());
        entity.setReceiveWarehouseId(request.getReceiveWarehouseId());
        entity.setActiveTime(request.getActiveTime());
        entity.setDisableTime(request.getDisableTime());

        entity.setId(SeqUtils.getSequenceUid());
        entity.setStatus(EntityStatusEnum.IS_ACTIVE.getCode());
        entity.setCreatedTime(new Date());
        entity.setUpdatedTime(new Date());
        entity.setCreatedBy(loginId);
        entity.setUpdatedBy(loginId);

        // 检查组合唯一性
        checkCreateUnique(entity);

        masterTransferPathDao.insert(entity);
    }

    @Override
    public void update(MasterTransferPathUpdateRequest request, String loginId) {
        // 验证时间范围
        if (request.getActiveTime().after(request.getDisableTime())) {
            throw new ServiceException("生效时间不能晚于结束时间");
        }

        // 查询原记录是否存在
        MasterTransferPath existingPath = masterTransferPathDao.selectById(request.getId());
        if (existingPath == null) {
            throw new ServiceException("调拨路径不存在");
        }
        if (EntityStatusEnum.NO_ACTIVE.getCode().equals(existingPath.getStatus())) {
            throw new ServiceException("调拨路径已失效，不能修改");
        }

        checkWarehouseExists(request.getSendWarehouseId(), request.getReceiveWarehouseId());

        // 更新实体
        MasterTransferPath updateEntity = new MasterTransferPath();
        updateEntity.setId(request.getId());
        updateEntity.setSendWarehouseId(request.getSendWarehouseId());
        updateEntity.setReceiveWarehouseId(request.getReceiveWarehouseId());
        updateEntity.setActiveTime(request.getActiveTime());
        updateEntity.setDisableTime(request.getDisableTime());
        updateEntity.setUpdatedTime(new Date());
        updateEntity.setUpdatedBy(loginId);
        updateEntity.setStatus(existingPath.getStatus());

        // 检查组合唯一性
        checkCreateUnique(updateEntity);

        // 更新记录
        masterTransferPathDao.update(updateEntity);
    }

    @Override
    public void updateStatus(MasterTransferPathUpdateStatusRequest request, String loginId) {
        // 查询原记录是否存在
        MasterTransferPath existingPath = masterTransferPathDao.selectById(request.getId());
        if (existingPath == null) {
            throw new ServiceException("调拨路径不存在");
        }

        // 获取当前状态
        Integer currentStatus = existingPath.getStatus();

        // 计算新状态（切换状态：0->1或1->0）
        Integer newStatus = EntityStatusEnum.IS_ACTIVE.getCode().equals(currentStatus) ?
                EntityStatusEnum.NO_ACTIVE.getCode() : EntityStatusEnum.IS_ACTIVE.getCode();

        // 更新实体
        MasterTransferPath updateEntity = new MasterTransferPath();
        updateEntity.setId(request.getId());
        updateEntity.setStatus(newStatus);
        updateEntity.setUpdatedTime(new Date());
        updateEntity.setUpdatedBy(loginId);

        // 更新记录
        masterTransferPathDao.updateStatus(updateEntity);

        log.info("调拨路径状态已更新，ID: {}, 旧状态: {}, 新状态: {}, 操作人: {}",
                request.getId(), currentStatus, newStatus, loginId);
    }

    private void checkWarehouseExists(String sendWarehouseId, String receiveWarehouseId) {
        // 查询发货仓
        List<MasterWarehouse> warehouseList =
                masterWarehouseService.queryByIds(Arrays.asList(sendWarehouseId, receiveWarehouseId));

        if (CollectionUtils.isEmpty(warehouseList)) {
            throw new ServiceException("仓库不存在");
        }

        // 将查询结果转换为Map，方便后续处理
        Map<String, MasterWarehouse> warehouseMap = warehouseList.stream()
                .collect(Collectors.toMap(MasterWarehouse::getId, warehouse -> warehouse));

        // 检查发货仓是否存在
        MasterWarehouse sendWarehouse = warehouseMap.get(sendWarehouseId);
        if (sendWarehouse == null) {
            throw new ServiceException("发货仓不存在");
        }

        // 检查收货仓是否存在
        MasterWarehouse receiveWarehouse = warehouseMap.get(receiveWarehouseId);
        if (receiveWarehouse == null) {
            throw new ServiceException("收货仓不存在");
        }

        // 检查是否为同一仓库
        if (sendWarehouseId.equals(receiveWarehouseId)) {
            throw new ServiceException("发货仓和收货仓不能相同");
        }
    }

    /**
     * 检查组合唯一性
     */
    private void checkCreateUnique(MasterTransferPath dto) {
        int count = masterTransferPathDao.countOverlap(dto);

        if (count > 0) {
            throw new ServiceException("相同发货仓和收货仓在指定时间范围内已存在调拨路径");
        }
    }
}