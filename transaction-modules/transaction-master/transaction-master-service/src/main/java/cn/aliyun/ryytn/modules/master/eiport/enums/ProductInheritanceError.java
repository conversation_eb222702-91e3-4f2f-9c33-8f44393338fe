package cn.aliyun.ryytn.modules.master.eiport.enums;

/**
 * @Description 商品继承关系导入错误枚举
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
public enum ProductInheritanceError {

    ORIGINAL_SKU_CODE_IS_NOT_NULL("原商品编码不能为空"),
    NEW_SKU_CODE_IS_NOT_NULL("新品商品编码不能为空"),
    RELATION_TYPE_IS_NOT_NULL("关系类型不能为空"),
    CHANNEL_OWNERSHIP_IS_NOT_NULL("渠道归属不能为空"),
    CUSTOM_STORE_IS_NOT_NULL_WHEN_STORE_CUSTOM("当关系类型为店铺定制时，定制店铺不能为空"),
    CUSTOM_CHANNEL_IS_NOT_NULL_WHEN_CHANNEL_CUSTOM("当关系类型为渠道定制时，定制渠道不能为空"),
    ORIGINAL_SKU_CODE_NOT_EXISTS("原商品编码[%s]不存在"),
    NEW_SKU_CODE_NOT_EXISTS("新品商品编码[%s]不存在"),
    RELATION_TYPE_NOT_EXISTS("关系类型[%s]不存在，请选择：新老品牌、店铺定制、渠道定制"),
    CHANNEL_OWNERSHIP_NOT_EXISTS("渠道归属[%s]不存在，请选择：TOC、TOB、TOC&TOB"),
    DUPLICATE_IN_EXCEL("表格内存在重复的原商品编码+新品商品编码+关系类型+定制店铺+定制渠道+渠道归属组合");

    private final String error;

    ProductInheritanceError(String error) {
        this.error = error;
    }

    public String getError() {
        return error;
    }
}
