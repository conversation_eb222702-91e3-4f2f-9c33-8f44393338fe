package cn.aliyun.ryytn.modules.master.eiport;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.sap.MasterWarehouse;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.modules.master.api.MasterRegionService;
import cn.aliyun.ryytn.modules.master.api.MasterTransferRangeService;
import cn.aliyun.ryytn.modules.master.api.MasterWarehouseService;
import cn.aliyun.ryytn.modules.master.eiport.model.MasterTransferRangeImportTemplateVO;
import cn.aliyun.ryytn.modules.master.eiport.model.export.MasterTransferRangeExportTemplateVO;
import cn.aliyun.ryytn.modules.master.entity.enums.TransferRangeRadiationTypeEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangeCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageResponse;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 调拨仓辐射导入导出模板
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@ImportService(value = "masterTransferRangeEIportDataTemplate",
        filename = "调拨仓辐射导入模版",
        templateDesc = "填写规范：\n" +
                "1.仓库编码：填写有效的仓库编码\n" +
                "2.辐射类型：填写ToB或ToC\n" +
                "3.省编码：填写有效的省级区域编码\n" +
                "4.市编码：填写有效的市级区域编码，且属于所填省\n" +
                "5.辐射距离（公里）：填写大于0的数字\n" +
                "6.生效时间：填写有效的日期时间，格式为yyyy-MM-dd HH:mm:ss\n" +
                "7.结束时间：填写有效的日期时间，格式为yyyy-MM-dd HH:mm:ss",
        async = false)
@ExportService(value = "masterTransferRangeEIportDataTemplate",
        filename = "调拨仓辐射数据导出",
        async = false)
public class MasterTransferRangeEIportDataTemplate
        extends EIPortDataTemplate<MasterTransferRangeImportTemplateVO, MasterTransferRangePageRequest>
        implements ImportPostProcessor<MasterTransferRangeImportTemplateVO> {

    @Autowired
    private MasterTransferRangeService masterTransferRangeService;

    @Autowired
    private MasterWarehouseService masterWarehouseService;

    @Autowired
    private MasterRegionService masterRegionService;

    @Override
    public boolean preValid(ImportDataWrapper<MasterTransferRangeImportTemplateVO> importDataWrapper) {
        List<MasterTransferRangeImportTemplateVO> importData = importDataWrapper.getData();
        return validate(importData);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<MasterTransferRangeImportTemplateVO> importDataWrapper) {
        List<MasterTransferRangeImportTemplateVO> importDataList = importDataWrapper.getData();

        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();

        // 批量保存
        for (MasterTransferRangeImportTemplateVO template : importDataList) {
            MasterTransferRangeCreateRequest request = new MasterTransferRangeCreateRequest();
            request.setRadiationType(template.getRadiationType());
            request.setSkuId(template.getSkuId());
            request.setProvinceId(template.getProvinceCode());
            request.setCityId(template.getCityCode());
            request.setWarehouseId(template.getWarehouseId());
            request.setSalesDepartment(template.getSalesDepartment());
            request.setStartKg(template.getStartKg());
            request.setEndKg(template.getEndKg());

            try {
                masterTransferRangeService.create(request, loginId);
            } catch (Exception e) {
                log.warn("导入出错，{}", Throwables.getStackTraceAsString(e));
                template.setErrorMsg("导入出错：" + e.getMessage());
            }
        }
        return null;
    }

    @Override
    public List<?> getExportTableData(MasterTransferRangePageRequest request) {
        // 查询数据
        PageCondition<MasterTransferRangePageRequest> pageCondition = new PageCondition<>();
        pageCondition.setPageNum(1);
        pageCondition.setPageSize(Integer.MAX_VALUE);
        pageCondition.setCondition(request);
        PageInfo<MasterTransferRangePageResponse> pageInfo = masterTransferRangeService.queryByPage(pageCondition);
        List<MasterTransferRangePageResponse> rangeList = pageInfo.getList();

        // 直接使用 GeiCommonConvert 转换，字段名已经匹配
        return GeiCommonConvert.convert(rangeList, MasterTransferRangeExportTemplateVO.class);
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(MasterTransferRangePageRequest request) {
        return ExcelMetaInfoCreator.create(MasterTransferRangeExportTemplateVO.class);
    }

    /**
     * 校验导入数据
     */
    private boolean validate(List<MasterTransferRangeImportTemplateVO> importDataList) {
        boolean flag = true;

        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();

        // 1. 基本字段校验
        for (MasterTransferRangeImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            if (StringUtils.isBlank(template.getWarehouseCode())) {
                errorMsg.append("仓库编码不能为空").append("；");
            }

            if (StringUtils.isBlank(template.getRadiationTypeName())) {
                errorMsg.append("辐射类型不能为空").append("；");
            } else {
                // 转换辐射类型
                Integer radiationType = TransferRangeRadiationTypeEnum.getCodeByName(template.getRadiationTypeName());
                if (Objects.isNull(radiationType)) {
                    errorMsg.append("辐射类型无效，请填写ToB或ToC").append("；");
                }
                template.setRadiationType(radiationType);
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 2. 校验仓库编码并设置ID
        Set<String> warehouseCodes = importDataList.stream()
                .map(MasterTransferRangeImportTemplateVO::getWarehouseCode)
                .collect(Collectors.toSet());

        List<MasterWarehouse> warehouseList = masterWarehouseService.queryByCode(new ArrayList<>(warehouseCodes));
        if (CollectionUtils.isEmpty(warehouseList)) {
            for (MasterTransferRangeImportTemplateVO template : importDataList) {
                template.setErrorMsg("仓库编码不存在");
            }
            return false;
        }

        // 将查询结果转换为Map，方便后续处理
        Map<String, MasterWarehouse> warehouseMap = warehouseList.stream()
                .collect(Collectors.toMap(MasterWarehouse::getWarehouseCode, warehouse -> warehouse));
        //  校验数据并设置ID
        for (MasterTransferRangeImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            // 校验仓库
            MasterWarehouse warehouse = warehouseMap.get(template.getWarehouseCode());
            if (warehouse == null) {
                errorMsg.append(String.format("仓库编码[%s]不存在", template.getWarehouseCode())).append("；");
            } else {
                template.setWarehouseId(warehouse.getId());
            }


            /*保存数据*/
            saveData(template, loginId);

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        return flag;
    }

    private void saveData(MasterTransferRangeImportTemplateVO template, String loginId) {
        MasterTransferRangeCreateRequest request = new MasterTransferRangeCreateRequest();
        request.setRadiationType(template.getRadiationType());
        request.setSkuId(template.getSkuId());
        request.setProvinceId(template.getProvinceCode());
        request.setCityId(template.getCityCode());
        request.setWarehouseId(template.getWarehouseId());
        request.setSalesDepartment(template.getSalesDepartment());
        request.setStartKg(template.getStartKg());
        request.setEndKg(template.getEndKg());

        try {
            masterTransferRangeService.create(request, loginId);
        } catch (Exception e) {
            log.warn("导入出错，{}", Throwables.getStackTraceAsString(e));
            template.setErrorMsg("导入出错：" + e.getMessage());
        }
    }
}