package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.master.api.ProductInheritanceService;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceInvalidateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceRequest;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description 商品继承关系Controller
 * @date 2024/10/10 15:00
 */
@Slf4j
@RestController
@RequestMapping("/api/master/productInheritance")
@Api(tags = "商品继承关系管理")
public class ProductInheritanceController {

    @Autowired
    private ProductInheritanceService productInheritanceService;

    /**
     * 新增或修改商品继承关系
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/save-or-update")
    @ApiOperation(value = "新增或修改商品继承关系", notes = "新增时ID为空，修改时ID必填")
    @RequiresPermissions(value = {""})
    public ResultInfo<?> saveOrUpdate(@Valid @RequestBody ProductInheritanceRequest request) {
        try {
            productInheritanceService.saveOrUpdate(request);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("新增或修改商品继承关系失败", e);
            return ResultInfo.fail(null, "操作失败：" + e.getMessage());
        }
    }

    /**
     * 批量作废商品继承关系
     *
     * @param request 作废请求
     * @return 操作结果
     */
    @PostMapping("/batch-invalidate")
    @ApiOperation(value = "批量作废商品继承关系", notes = "将状态改为无效，已作废的记录不能重复作废")
    @RequiresPermissions(value = {""})
    public ResultInfo<?> batchInvalidate(@Valid @RequestBody ProductInheritanceInvalidateRequest request) {
        try {
            productInheritanceService.batchInvalidate(request);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("批量作废商品继承关系失败", e);
            return ResultInfo.fail(null, "作废失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询商品继承关系
     *
     * @param pageCondition 分页查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询商品继承关系", notes = "支持按原商品ID、新品商品ID、关系类型等条件查询")
    @RequiresPermissions(value = {""})
    public ResultInfo<PageInfo<ProductInheritanceQueryPageResponse>> page(@RequestBody PageCondition<ProductInheritanceQueryRequest> pageCondition) {
        try {
            // 设置默认分页参数
            if (pageCondition.getPageNum() == null) {
                pageCondition.setPageNum(1);
            }
            if (pageCondition.getPageSize() == null) {
                pageCondition.setPageSize(100);
            }
            if (pageCondition.getCondition() == null) {
                pageCondition.setCondition(new ProductInheritanceQueryRequest());
            }

            PageInfo<ProductInheritanceQueryPageResponse> pageInfo = productInheritanceService.queryPage(pageCondition);
            return ResultInfo.success(pageInfo);
        } catch (Exception e) {
            log.error("查询商品继承关系失败", e);
            return (ResultInfo<PageInfo<ProductInheritanceQueryPageResponse>>) ResultInfo.fail(null, "查询失败：" + e.getMessage());
        }
    }
}
