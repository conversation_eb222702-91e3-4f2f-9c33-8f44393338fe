package cn.aliyun.ryytn.modules.master.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

/**
 * 区域信息导出模板
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
public class MasterRegionExportTemplateVO {

    /**
     * 区域编码
     */
    @ExcelField(value = "区域编码")
    private String regionCode;

    /**
     * 区域名称
     */
    @ExcelField(value = "区域名称")
    private String regionName;

    /**
     * 父区域编码
     */
    @ExcelField(value = "父区域编码")
    private String parentRegionCode;

    /**
     * 父区域名称
     */
    @ExcelField(value = "父区域名称")
    private String parentRegionName;

    /**
     * 区域级别
     */
    @ExcelField(value = "区域级别")
    private Integer regionLevel;

    /**
     * 状态名称
     */
    @ExcelField(value = "状态")
    private String statusName;

    /**
     * 创建人
     */
    @ExcelField(value = "创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelField(value = "创建时间")
    private String createdTimeStr;

    /**
     * 更新人
     */
    @ExcelField(value = "更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
    @ExcelField(value = "更新时间")
    private String updatedTimeStr;
}