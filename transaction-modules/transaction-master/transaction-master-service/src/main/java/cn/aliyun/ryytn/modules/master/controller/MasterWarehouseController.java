package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.entity.sap.MasterWarehouse;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.modules.master.api.MasterWarehouseService;
import cn.aliyun.ryytn.modules.master.entity.enums.MasterWarehouseTypeEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehouseModifyRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehouseQueryRequest;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description SAP主数据-仓库档案
 * @date 2023年9月19日 下午2:11:38
 */
@Slf4j
@RestController
@RequestMapping("/api/master/warehouse")
@Api(tags = "SAP主数据-仓库档案")
public class MasterWarehouseController {
    @Autowired
    private MasterWarehouseService masterWarehouseService;


    @PostMapping("page")
    @ResponseBody
    @ApiOperation("分页查询仓库列表")
    @RequiresPermissions(value = {""})
    public ResultInfo<PageInfo<MasterWarehouse>> queryByPage(@RequestBody PageCondition<MasterWarehouseQueryRequest> condition) throws Exception {
        if (Objects.isNull(condition)) {
            condition = new PageCondition<>();
        }

        PageInfo<MasterWarehouse> page = masterWarehouseService.queryByPage(condition);
        return ResultInfo.success(page);
    }

    @PostMapping("modify")
    @ResponseBody
    @ApiOperation("修改仓库信息")
    @RequiresPermissions(value = {""})
    public ResultInfo<?> modifyByUser(@RequestBody MasterWarehouseModifyRequest request) {
        checkModifyRequest(request);

//        String loginId = "c011375";
        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterWarehouseService.modifyByUser(request, loginId);
        return ResultInfo.success();
    }

    private void checkModifyRequest(MasterWarehouseModifyRequest request) {
        if (request == null) {
            throw new ServiceException("修改仓库信息请求不能为空");
        }
        if (StringUtils.isEmpty(request.getId())) {
            throw new ServiceException("仓库ID不能为空");
        }

        Integer warehouseType = request.getWarehouseType();
        if (Objects.nonNull(warehouseType)) {
            MasterWarehouseTypeEnum warehouseTypeEnum = MasterWarehouseTypeEnum.getEnumByCode(warehouseType);
            if (Objects.isNull(warehouseTypeEnum)) {
                throw new ServiceException("仓库类型不存在");
            }
        }
    }

}
