package cn.aliyun.ryytn.modules.master.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

import java.util.Date;



/**
 * <AUTHOR>
 * @Description 生产销售关系导出模板VO
 * @date 2024/10/10 15:00
 */
@Data
public class ProductionSalesRelationExportTemplateVO {

    /**
     * 销售商品编码
     */
    @ExcelField(value = "销售商品编码")
    private String salesSkuCode;

    /**
     * 销售商品名称
     */
    @ExcelField(value = "销售商品名称")
    private String salesSkuName;

    /**
     * 生产商品编码
     */
    @ExcelField(value = "生产商品编码")
    private String productionSkuCode;

    /**
     * 生产商品名称
     */
    @ExcelField(value = "生产商品名称")
    private String productionSkuName;

    /**
     * 转换系数
     */
    @ExcelField(value = "转换系数")
    private Integer conversionFactor;

    /**
     * 创建人账号
     */
    @ExcelField(value = "创建人")
    private String createdBy;

    /**
     * 最后修改人账号
     */
    @ExcelField(value = "最后修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ExcelField(value = "创建时间")
    private String createdTimeStr;

    /**
     * 最后修改时间
     */
    @ExcelField(value = "最后修改时间")
    private String updatedTimeStr;

    /**
     * 状态描述
     */
    @ExcelField(value = "状态")
    private String statusDesc;
}
