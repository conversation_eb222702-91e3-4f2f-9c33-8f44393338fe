package cn.aliyun.ryytn.modules.master.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 可用库存导出模板VO
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
public class AvailableStockExportTemplateVO {

    /**
     * 工厂名称
     */
    @ExcelField(value = "工厂名称")
    private String factoryName;

    /**
     * 商品编码
     */
    @ExcelField(value = "商品编码")
    private String skuCode;

    /**
     * 商品名称
     */
    @ExcelField(value = "商品名称")
    private String skuName;

    /**
     * 仓库编码
     */
    @ExcelField(value = "仓库编码")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ExcelField(value = "仓库名称")
    private String warehouseName;

    /**
     * 生产日期
     */
    @ExcelField(value = "生产日期")
    private String productionDate;

    /**
     * 可用库存数量
     */
    @ExcelField(value = "可用库存数量")
    private BigDecimal quantity;

    /**
     * 库存快照日期
     */
    @ExcelField(value = "库存快照日期")
    private String stockDate;

    /**
     * 创建人账号
     */
    @ExcelField(value = "创建人")
    private String createdBy;

    /**
     * 最后修改人账号
     */
    @ExcelField(value = "最后修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ExcelField(value = "创建时间")
    private String createdTimeStr;

    /**
     * 最后修改时间
     */
    @ExcelField(value = "最后修改时间")
    private String updatedTimeStr;

    /**
     * 状态描述
     */
    @ExcelField(value = "状态")
    private String statusDesc;
}
