package cn.aliyun.ryytn.modules.master.eiport.model;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 区域信息导入模板
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MasterRegionImportTemplateVO extends ImportTemplateVO {
    /**
     * 区域编码
     */
    @ExcelField(value = "区域编码", importKey = true)
    private String regionCode;

    /**
     * 区域名称
     */
    @ExcelField(value = "区域名称", importKey = true)
    private String regionName;

    /**
     * 父区域编码
     */
    @ExcelField(value = "父区域编码")
    private String parentRegionCode;

}