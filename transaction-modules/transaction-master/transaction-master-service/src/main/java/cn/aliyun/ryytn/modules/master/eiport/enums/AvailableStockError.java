package cn.aliyun.ryytn.modules.master.eiport.enums;

/**
 * @Description 可用库存导入错误枚举
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
public enum AvailableStockError {

    FACTORY_NAME_IS_NOT_NULL("工厂名称不能为空"),
    SKU_CODE_IS_NOT_NULL("商品编码不能为空"),
    WAREHOUSE_CODE_IS_NOT_NULL("仓库编码不能为空"),
    PRODUCTION_DATE_IS_NOT_NULL("生产日期不能为空"),
    PRODUCTION_DATE_FORMAT_ERROR("生产日期格式错误，必须为yyyyMMdd格式"),
    QUANTITY_IS_NOT_NULL("可用库存数量不能为空"),
    QUANTITY_MUST_POSITIVE("可用库存数量必须大于0"),
    STOCK_DATE_IS_NOT_NULL("库存快照日期不能为空"),
    STOCK_DATE_FORMAT_ERROR("库存快照日期格式错误，必须为yyyyMMdd格式"),
    SKU_CODE_NOT_EXISTS("商品编码[%s]不存在"),
    WAREHOUSE_CODE_NOT_EXISTS("仓库编码[%s]不存在"),
    DUPLICATE_IN_EXCEL("表格内存在重复的工厂名称+商品编码+仓库编码+生产日期组合");

    private final String error;

    AvailableStockError(String error) {
        this.error = error;
    }

    public String getError() {
        return error;
    }
}
