package cn.aliyun.ryytn.modules.master.service;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.sap.MasterWarehouse;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.master.api.MasterWarehouseService;
import cn.aliyun.ryytn.modules.master.dao.MasterWarehouseDao;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehouseModifyRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehouseQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.SapSyncMasterWarehouseRequest;
import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-05-06 15:42
 */
@Slf4j
@Service
public class MasterWarehouseServiceImpl implements MasterWarehouseService {
    @Autowired
    private MasterWarehouseDao masterWarehouseDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncMasterWarehouseInfo(List<SapSyncMasterWarehouseRequest> requestList, String loginId) {
        checkWarehouseInfo(requestList);

        // 1. 提取请求中的仓库编码集合
        Set<String> warehouseCodes = requestList.stream()
                .map(SapSyncMasterWarehouseRequest::getWarehouseCode)
                .collect(Collectors.toSet());

        // 2. 查询已存在的仓库记录
        Map<String, MasterWarehouse> existingWarehouses =
                ListUtils.emptyIfNull(masterWarehouseDao.selectByWarehouseCodes(warehouseCodes))
                        .stream().collect(Collectors.toMap(MasterWarehouse::getWarehouseCode, Function.identity()));

        // 3. 分离新增和更新的数据
        List<MasterWarehouse> createList = new ArrayList<>();
        List<MasterWarehouse> updateList = new ArrayList<>();
        for (SapSyncMasterWarehouseRequest request : requestList) {
            MasterWarehouse entity = convertToEntity(request, loginId);
            if (existingWarehouses.containsKey(request.getWarehouseCode())) {
                entity.setId(existingWarehouses.get(request.getWarehouseCode()).getId());
                updateList.add(entity);
                continue;
            }

            entity.setId(SeqUtils.getSequenceUid());
            entity.setCreatedTime(new Date());
            entity.setCreatedBy(loginId);
            createList.add(entity);
        }

        // 4. 变更数据
        if (!createList.isEmpty()) {
            List<List<MasterWarehouse>> split = CollUtil.split(createList, 1000);
            for (List<MasterWarehouse> list : split) {
                masterWarehouseDao.batchInsert(list);
            }
        }
        if (!updateList.isEmpty()) {
            List<List<MasterWarehouse>> split = CollUtil.split(updateList, 1000);
            for (List<MasterWarehouse> list : split) {
                masterWarehouseDao.batchUpdate(list);
            }
        }

        log.info("syncMasterWarehouseInfo success createListSize:{},updateListSize:{}", createList.size(), updateList.size());
    }


    @Override
    public PageInfo<MasterWarehouse> queryByPage(PageCondition<MasterWarehouseQueryRequest> condition) {
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        java.util.List<MasterWarehouse> warehouseList = masterWarehouseDao.selectByPage(condition.getCondition());
        return new PageInfo<>(warehouseList);
    }

    @Override
    public void modifyByUser(MasterWarehouseModifyRequest request, String loginId) {
        MasterWarehouse warehouse = masterWarehouseDao.selectById(request.getId());
        if (warehouse == null) {
            log.info("要修改的仓库记录不存在，id: {}", request.getId());
            throw new ServiceException("要修改的仓库记录不存在");
        }
        if (Objects.equals(request.getWarehouseType(), warehouse.getWarehouseType())
                && Objects.equals(request.getPhyWarehouseName(), warehouse.getPhyWarehouseName())) {
            return;
        }

        MasterWarehouse updateWarehouse = new MasterWarehouse();
        updateWarehouse.setId(request.getId());
        updateWarehouse.setWarehouseType(request.getWarehouseType());
        updateWarehouse.setPhyWarehouseName(request.getPhyWarehouseName());
        updateWarehouse.setUpdatedBy(loginId);
        updateWarehouse.setUpdatedTime(new Date());
        int rows = masterWarehouseDao.updateByUser(updateWarehouse);
        if (rows == 0) {
            throw new ServiceException("修改仓库类型失败");
        }
    }

    @Override
    public List<MasterWarehouse> queryByIds(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return masterWarehouseDao.selectByIds(ids);
    }

    private void checkWarehouseInfo(List<SapSyncMasterWarehouseRequest> requestList) {
        if (CollectionUtils.isEmpty(requestList)) {
            throw new ServiceException("明细参数不能为空");
        }

        for (SapSyncMasterWarehouseRequest request : requestList) {
            if (StringUtils.isEmpty(request.getPlantCode())) {
                throw new ServiceException("工厂不能为空");
            }
            if (StringUtils.isEmpty(request.getWarehouseCode())) {
                throw new ServiceException("仓库编码不能为空");
            }
            if (StringUtils.isEmpty(request.getWarehouseName())) {
                throw new ServiceException("仓库名称不能为空");
            }
        }
    }

    private MasterWarehouse convertToEntity(SapSyncMasterWarehouseRequest request, String loginId) {
        MasterWarehouse entity = new MasterWarehouse();
        entity.setWarehouseCode(request.getWarehouseCode());
        entity.setPlantCode(request.getPlantCode());
        entity.setWarehouseName(request.getWarehouseName());
        entity.setContactPerson(request.getContactPerson());
        entity.setMobile(request.getMobile());
        entity.setTelephone(request.getTelephone());
        entity.setProvince(request.getProvince());
        entity.setCity(request.getCity());
        entity.setDistrict(request.getDistrict());
        entity.setFullAddress(request.getFullAddress());

        entity.setUpdatedBy(loginId);
        entity.setUpdatedTime(new Date());
        return entity;
    }
}
