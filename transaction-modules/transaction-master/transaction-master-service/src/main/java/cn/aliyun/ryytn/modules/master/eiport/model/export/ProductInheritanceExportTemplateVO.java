package cn.aliyun.ryytn.modules.master.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

/**
 * @Description 商品继承关系导出模板VO
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
public class ProductInheritanceExportTemplateVO {

    /**
     * 原商品编码
     */
    @ExcelField(value = "原商品编码")
    private String originalSkuCode;

    /**
     * 原商品名称
     */
    @ExcelField(value = "原商品名称")
    private String originalSkuName;

    /**
     * 新品商品编码
     */
    @ExcelField(value = "新品商品编码")
    private String newSkuCode;

    /**
     * 新品商品名称
     */
    @ExcelField(value = "新品商品名称")
    private String newSkuName;

    /**
     * 关系类型描述
     */
    @ExcelField(value = "关系类型")
    private String relationTypeDesc;

    /**
     * 定制店铺
     */
    @ExcelField(value = "定制店铺")
    private String customStore;

    /**
     * 定制渠道
     */
    @ExcelField(value = "定制渠道")
    private String customChannel;

    /**
     * 渠道归属描述
     */
    @ExcelField(value = "渠道归属")
    private String channelOwnershipDesc;

    /**
     * 创建人账号
     */
    @ExcelField(value = "创建人")
    private String createdBy;

    /**
     * 最后修改人账号
     */
    @ExcelField(value = "最后修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ExcelField(value = "创建时间")
    private String createdTimeStr;

    /**
     * 最后修改时间
     */
    @ExcelField(value = "最后修改时间")
    private String updatedTimeStr;

    /**
     * 状态描述
     */
    @ExcelField(value = "状态")
    private String statusDesc;
}
