package cn.aliyun.ryytn.modules.master.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 调拨路径导出模板VO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
public class MasterTransferPathExportTemplateVO {
    /**
     * 发货仓ID
     */
    private String sendWarehouseId;

    /**
     * 发货仓名称
     */
    @ExcelField("发货仓名称")
    private String sendWarehouseName;

    @ApiModelProperty("发货仓编码")
    private String sendWarehouseCode;

    /**
     * 收货仓ID
     */
    private String receiveWarehouseId;

    /**
     * 收货仓名称
     */
    @ExcelField("收货仓名称")
    private String receiveWarehouseName;

    @ApiModelProperty("收货仓编码")
    private String receiveWarehouseCode;

    /**
     * 状态
     */
    @ExcelField("状态")
    private String statusName;

    /**
     * 生效时间
     */
//    @ExcelField("生效时间")
//    private Date activeTime;

    /**
     * 结束时间
     */
//    @ExcelField("结束时间")
//    private Date disableTime;

    /**
     * 创建人
     */
    @ExcelField("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
//    @ExcelField("创建时间")
//    private Date createdTime;

    /**
     * 更新人
     */
    @ExcelField("更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
//    @ExcelField("更新时间")
//    private Date updatedTime;

    /**
     * 生效时间
     */
    @ExcelField("生效时间")
    private String activeTimeStr;

    /**
     * 结束时间
     */
    @ExcelField("结束时间")
    private String disableTimeStr;

    @ExcelField("创建时间")
    private String createdTimeStr;

    @ExcelField("修改时间")
    private String updatedTimeStr;
}