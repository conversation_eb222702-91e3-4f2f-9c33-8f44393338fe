package cn.aliyun.ryytn.modules.master.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

/**
 * @Description 转移替代关系导出模板VO
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
public class TransferSubstitutionExportTemplateVO {

    /**
     * 原商品编码
     */
    @ExcelField(value = "原商品编码")
    private String originalSkuCode;

    /**
     * 原商品名称
     */
    @ExcelField(value = "原商品名称")
    private String originalSkuName;

    /**
     * 替代商品编码
     */
    @ExcelField(value = "替代商品编码")
    private String substituteSkuCode;

    /**
     * 替代商品名称
     */
    @ExcelField(value = "替代商品名称")
    private String substituteSkuName;

    /**
     * 创建人账号
     */
    @ExcelField(value = "创建人")
    private String createdBy;

    /**
     * 最后修改人账号
     */
    @ExcelField(value = "最后修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ExcelField(value = "创建时间")
    private String createdTimeStr;

    /**
     * 最后修改时间
     */
    @ExcelField(value = "最后修改时间")
    private String updatedTimeStr;

    /**
     * 状态描述
     */
    @ExcelField(value = "状态")
    private String statusDesc;
}
