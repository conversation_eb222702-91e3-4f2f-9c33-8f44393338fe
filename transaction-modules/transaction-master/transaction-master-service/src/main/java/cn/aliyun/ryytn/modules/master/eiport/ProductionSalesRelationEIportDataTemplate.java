package cn.aliyun.ryytn.modules.master.eiport;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.modules.master.api.ProductionSalesRelationService;
import cn.aliyun.ryytn.modules.master.dao.MasterSkuDao;
import cn.aliyun.ryytn.modules.master.eiport.enums.ProductionSalesRelationError;
import cn.aliyun.ryytn.modules.master.eiport.model.ProductionSalesRelationImportTemplateVO;
import cn.aliyun.ryytn.modules.master.eiport.model.export.ProductionSalesRelationExportTemplateVO;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationRequest;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 生产销售关系导入导出模板
 * @date 2024/10/10 15:00
 */
@Slf4j
@ImportService(value = "productionSalesRelationEIportDataTemplate",
        filename = "生产销售关系导入模版",
        templateDesc = "填写规范：\n" +
                "1.销售商品编码：填写有效的销售商品编码\n" +
                "2.生产商品编码：填写有效的生产商品编码\n" +
                "3.转换系数：填写正整数，表示生产1个单位商品可转换的销售商品数量",
        async = false)
@ExportService(value = "productionSalesRelationEIportDataTemplate",
        filename = "生产销售关系数据导出",
        async = false)
public class ProductionSalesRelationEIportDataTemplate
        extends EIPortDataTemplate<ProductionSalesRelationImportTemplateVO, ProductionSalesRelationQueryRequest>
        implements ImportPostProcessor<ProductionSalesRelationImportTemplateVO> {

    @Autowired
    private ProductionSalesRelationService productionSalesRelationService;

    @Autowired
    private MasterSkuDao masterSkuDao;

    @Override
    public boolean preValid(ImportDataWrapper<ProductionSalesRelationImportTemplateVO> importDataWrapper) {
        List<ProductionSalesRelationImportTemplateVO> importData = importDataWrapper.getData();
        return validate(importData);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<ProductionSalesRelationImportTemplateVO> importDataWrapper) {
        List<ProductionSalesRelationImportTemplateVO> importDataList = importDataWrapper.getData();

        // 批量保存
        List<ProductionSalesRelationRequest> requestList = new ArrayList<>();
        for (ProductionSalesRelationImportTemplateVO template : importDataList) {
            ProductionSalesRelationRequest request = new ProductionSalesRelationRequest();
            request.setSalesSkuId(template.getSalesSkuId());
            request.setProductionSkuId(template.getProductionSkuId());
            request.setConversionFactor(template.getConversionFactor());
            requestList.add(request);
        }

        // 分批处理，每批最多200条
        if (requestList.size() > 200) {
            int batchSize = 200;
            int totalSize = requestList.size();
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<ProductionSalesRelationRequest> batch = requestList.subList(i, endIndex);
                for (ProductionSalesRelationRequest request : batch) {
                    productionSalesRelationService.saveOrUpdate(request);
                }
            }
        } else {
            for (ProductionSalesRelationRequest request : requestList) {
                productionSalesRelationService.saveOrUpdate(request);
            }
        }

        return null;
    }

    @Override
    public List<?> getExportTableData(ProductionSalesRelationQueryRequest request) {
        // 查询数据
        PageCondition<ProductionSalesRelationQueryRequest> pageCondition = new PageCondition<>();
        pageCondition.setPageNum(1);
        pageCondition.setPageSize(Integer.MAX_VALUE);
        pageCondition.setCondition(request);
        List<ProductionSalesRelationQueryPageResponse> relationList = productionSalesRelationService.queryPage(pageCondition).getList();

        // 直接使用 GeiCommonConvert 转换，字段名已经匹配
        return GeiCommonConvert.convert(relationList, ProductionSalesRelationExportTemplateVO.class);
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(ProductionSalesRelationQueryRequest request) {
        return ExcelMetaInfoCreator.create(ProductionSalesRelationExportTemplateVO.class);
    }

    /**
     * 校验导入数据
     */
    private boolean validate(List<ProductionSalesRelationImportTemplateVO> importDataList) {
        boolean flag = true;

        // 1. 基本字段校验
        for (ProductionSalesRelationImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            if (StringUtils.isBlank(template.getSalesSkuCode())) {
                errorMsg.append(ProductionSalesRelationError.SALES_SKU_CODE_IS_NOT_NULL.getError()).append("；");
            }

            if (StringUtils.isBlank(template.getProductionSkuCode())) {
                errorMsg.append(ProductionSalesRelationError.PRODUCTION_SKU_CODE_IS_NOT_NULL.getError()).append("；");
            }

            if (template.getConversionFactor() == null) {
                errorMsg.append(ProductionSalesRelationError.CONVERSION_FACTOR_IS_NOT_NULL.getError()).append("；");
            } else if (template.getConversionFactor() <= 0) {
                errorMsg.append(ProductionSalesRelationError.CONVERSION_FACTOR_MUST_POSITIVE.getError()).append("；");
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 2. 收集所有商品编码进行批量查询
        Set<String> allProductCodes = new HashSet<>();
        for (ProductionSalesRelationImportTemplateVO template : importDataList) {
            if (StringUtils.isNotBlank(template.getSalesSkuCode())) {
                allProductCodes.add(template.getSalesSkuCode());
            }
            if (StringUtils.isNotBlank(template.getProductionSkuCode())) {
                allProductCodes.add(template.getProductionSkuCode());
            }
        }

        // 批量查询SKU信息
        Map<String, MasterSku> skuCodeMap = new HashMap<>();
        if (!allProductCodes.isEmpty()) {
            List<MasterSku> skuList = masterSkuDao.queryByProductCodes(new ArrayList<>(allProductCodes));
            if (!CollectionUtils.isEmpty(skuList)) {
                skuCodeMap = skuList.stream().collect(Collectors.toMap(MasterSku::getProductCode, sku -> sku));
            }
        }

        // 3. 校验商品编码是否存在并设置ID
        for (ProductionSalesRelationImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            // 校验销售商品编码
            MasterSku salesSku = skuCodeMap.get(template.getSalesSkuCode());
            if (salesSku == null) {
                errorMsg.append(String.format(ProductionSalesRelationError.SALES_SKU_CODE_NOT_EXISTS.getError(), template.getSalesSkuCode())).append("；");
            } else {
                template.setSalesSkuId(String.valueOf(salesSku.getId()));
                template.setSalesSkuName(salesSku.getProductName());
            }

            // 校验生产商品编码
            MasterSku productionSku = skuCodeMap.get(template.getProductionSkuCode());
            if (productionSku == null) {
                errorMsg.append(String.format(ProductionSalesRelationError.PRODUCTION_SKU_CODE_NOT_EXISTS.getError(), template.getProductionSkuCode())).append("；");
            } else {
                template.setProductionSkuId(String.valueOf(productionSku.getId()));
                template.setProductionSkuName(productionSku.getProductName());
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 4. 校验表格内唯一性
        Map<String, Integer> duplicateMap = new HashMap<>();
        for (int i = 0; i < importDataList.size(); i++) {
            ProductionSalesRelationImportTemplateVO template = importDataList.get(i);
            String key = template.getSalesSkuId() + "_" + template.getProductionSkuId();

            if (duplicateMap.containsKey(key)) {
                template.setErrorMsg(ProductionSalesRelationError.DUPLICATE_IN_EXCEL.getError() + "；");
                importDataList.get(duplicateMap.get(key)).setErrorMsg(ProductionSalesRelationError.DUPLICATE_IN_EXCEL.getError() + "；");
                flag = false;
            } else {
                duplicateMap.put(key, i);
            }
        }

        if (!flag) {
            return false;
        }

//        // 5. 校验数据库唯一性
//        List<String> salesSkuIds = importDataList.stream()
//                .map(ProductionSalesRelationImportTemplateVO::getSalesSkuId)
//                .filter(StringUtils::isNotBlank)
//                .distinct()
//                .collect(Collectors.toList());
//
//        List<String> productionSkuIds = importDataList.stream()
//                .map(ProductionSalesRelationImportTemplateVO::getProductionSkuId)
//                .filter(StringUtils::isNotBlank)
//                .distinct()
//                .collect(Collectors.toList());
//
//        ProductionSalesRelationQueryRequest queryRequest = new ProductionSalesRelationQueryRequest();
//        queryRequest.setSalesSkuIds(salesSkuIds);
//        queryRequest.setProductionSkuIds(productionSkuIds);
//        queryRequest.setStatus(EntityStatusEnum.IS_ACTIVE.getCode());
//
//        PageCondition<ProductionSalesRelationQueryRequest> pageCondition = new PageCondition<>();
//        pageCondition.setPageNum(1);
//        pageCondition.setPageSize(Integer.MAX_VALUE);
//        pageCondition.setCondition(queryRequest);
//        List<ProductionSalesRelationQueryPageResponse> existingRelations = productionSalesRelationService.queryPage(pageCondition).getList();
//
//        if (!CollectionUtils.isEmpty(existingRelations)) {
//            Set<String> existingKeys = existingRelations.stream()
//                    .map(relation -> relation.getSalesSkuId() + "_" + relation.getProductionSkuId())
//                    .collect(Collectors.toSet());
//
//            for (ProductionSalesRelationImportTemplateVO template : importDataList) {
//                String key = template.getSalesSkuId() + "_" + template.getProductionSkuId();
//                if (existingKeys.contains(key)) {
//                    template.setErrorMsg(ProductionSalesRelationError.DUPLICATE_IN_DATABASE.getError() + "；");
//                    flag = false;
//                }
//            }
//        }

        // 6. 设置操作人信息
        Account currentAccount = getCurrentAccount();
        if (currentAccount != null) {
            for (ProductionSalesRelationImportTemplateVO template : importDataList) {
                if (StringUtils.isBlank(template.getErrorMsg())) {
                    template.setOperatorCode(currentAccount.getId());
                    template.setOperatorName(currentAccount.getName());
                }
            }
        }

        return flag;
    }

    /**
     * 获取当前账户
     */
    private Account getCurrentAccount() {
        try {
            return ServiceContextUtils.currentSession().getAccount();
        } catch (Exception e) {
            log.warn("获取当前账户失败", e);
            return null;
        }
    }
}
