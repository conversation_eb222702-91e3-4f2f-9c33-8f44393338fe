package cn.aliyun.ryytn.modules.master.service;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.sap.SapPushUtil;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.master.api.MasterSkuService;
import cn.aliyun.ryytn.modules.master.dao.MasterSkuDao;
import cn.aliyun.ryytn.modules.master.entity.dto.SapQuerySkuRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterSkuQueryRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Description SAP数据同步服务实现
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Slf4j
@Service
public class MasterSkuServiceImpl implements MasterSkuService
{

    @Resource
    private SapPushUtil sapPushUtil;

    @Autowired
    private MasterSkuDao masterSkuDao;
    /**
     *
     * @Description 从SAP同步主SKU信息
     * @param syncTime 同步日期，格式为"yyyyMMdd"，如果为null则使用前一天
     * @param productCodes 商品编码数组，如果为null或空则同步所有商品
     * @throws Exception
     * <AUTHOR>
     * @date 2024年10月10日 15:00
     */
    @Override
    public void syncMasterSkuFromSap(String syncTime, List<String> productCodes) throws Exception {
        if (StringUtils.isBlank(syncTime)) {
            throw new ServiceException("Sync time cannot be empty");
        }

        // 每批最多处理的记录数
        final int BATCH_SIZE = 50;

        // 构建请求列表
        List<SapQuerySkuRequest> requestList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(productCodes)) {
            log.info("Start syncing master SKU for {} products from SAP", productCodes.size());

            // 根据商品编码构建请求
            for (String productCode : productCodes) {
                SapQuerySkuRequest request = new SapQuerySkuRequest();
                request.setProductCode(productCode);
                requestList.add(request);
            }
        } else {
            log.info("Start syncing master SKU for all products from SAP, sync date: {}", syncTime);

            // 根据同步日期构建请求
            SapQuerySkuRequest request = new SapQuerySkuRequest();
            request.setSyncDate(syncTime);
            requestList.add(request);
        }

        // 如果请求列表为空，直接返回
        if (requestList.isEmpty()) {
            log.warn("No request to process");
            return;
        }

        // 使用Guava的Lists.partition方法将请求列表分组，每组最多50条
        List<List<SapQuerySkuRequest>> batchList = Lists.partition(requestList, BATCH_SIZE);

        if (requestList.size() > BATCH_SIZE) {
            log.info("Request size {} exceeds batch size {}, will process in {} batches",
                    requestList.size(), BATCH_SIZE, batchList.size());
        }

        // 处理每一批请求
        int totalProcessedRecords = 0;

        for (int i = 0; i < batchList.size(); i++) {
            List<SapQuerySkuRequest> batch = batchList.get(i);
            log.info("Processing batch {}/{}, with {} requests", i + 1, batchList.size(), batch.size());

            // 发送请求并处理响应
            String responseStr = sapPushUtil.execute(SapPushUtil.GET_SKU_FROM_SAP_FUN_ID, UUID.randomUUID().toString(), batch);
            JSONObject responseJson = JSON.parseObject(responseStr);
            log.info("Batch {}/{} response received", i + 1, batchList.size());

            // 检查响应中的CTRL字段
            JSONObject ctrl = responseJson.getJSONObject("CTRL");
            if (ctrl == null) {
                log.error("Batch {}/{} response ctrl is empty", i + 1, batchList.size());
                continue; // 跳过这一批次，继续处理下一批
            }

            // 检查响应状态
            if (!"S".equals(ctrl.getString("MSGTY"))) {
                log.error("Batch {}/{} response status is not success: {}", i + 1, batchList.size(), ctrl.getString("MSAGE"));
                continue; // 跳过这一批次，继续处理下一批
            }

            // 检查响应中的DATA字段
            JSONArray batchData = responseJson.getJSONArray("DATA");
            if (batchData != null && !batchData.isEmpty()) {
                // 直接处理当前批次的数据
                log.info("Batch {}/{} processing {} records", i + 1, batchList.size(), batchData.size());
                processMasterSkuData(batchData);
                totalProcessedRecords += batchData.size();
                log.info("Batch {}/{} processed {} records, total processed so far: {}",
                        i + 1, batchList.size(), batchData.size(), totalProcessedRecords);
            } else {
                log.warn("Batch {}/{} response data is empty", i + 1, batchList.size());
            }
        }

        // 没有处理任何数据
        if (totalProcessedRecords == 0) {
            log.warn("No data processed");
            return;
        }

        log.info("Sync master SKU from SAP completed, processed {} records in total", totalProcessedRecords);
    }

    /**
     * 处理主SKU数据，保存到数据库
     *
     * @param dataArray SAP返回的数据数组
     * @throws Exception
     */
    private void processMasterSkuData(JSONArray dataArray) throws Exception {
        if (dataArray == null || dataArray.isEmpty()) {
            return;
        }

        // 每批最多处理的记录数
        final int BATCH_SIZE = 100;

        // 将JSONArray转换为List<JSONObject>，以便使用Lists.partition
        List<JSONObject> dataList = new ArrayList<>();
        for (int i = 0; i < dataArray.size(); i++) {
            dataList.add(dataArray.getJSONObject(i));
        }

        // 使用Guava的Lists.partition方法将数据列表分组，每组最多100条
        List<List<JSONObject>> batchDataLists = Lists.partition(dataList, BATCH_SIZE);

        if (dataList.size() > BATCH_SIZE) {
            log.info("Data array size {} exceeds batch size {}, will process in {} batches",
                    dataList.size(), BATCH_SIZE, batchDataLists.size());
        }

        int totalSuccessCount = 0;
        int totalFailCount = 0;
        int totalInsertCount = 0;
        int totalUpdateCount = 0;

        // 处理每一批数据
        for (int batchIndex = 0; batchIndex < batchDataLists.size(); batchIndex++) {
            List<JSONObject> batchDataList = batchDataLists.get(batchIndex);
            log.info("Processing batch {}/{}, with {} records", batchIndex + 1, batchDataLists.size(), batchDataList.size());

            // 收集当前批次的商品编码
            List<String> allProductCodes = new ArrayList<>();
            Map<String, JSONObject> dataMap = new HashMap<>();

            for (JSONObject dataObj : batchDataList) {
                String productCode = dataObj.getString("MATNR"); // 商品编码

                if (StringUtils.isNotBlank(productCode)) {
                    allProductCodes.add(productCode);
                    dataMap.put(productCode, dataObj);
                } else {
                    log.warn("Product code is empty, skip this record");
                }
            }

            if (allProductCodes.isEmpty()) {
                log.warn("Batch {}/{} has no valid product codes, skip this batch", batchIndex + 1, batchDataLists.size());
                continue;
            }

            log.info("Batch {}/{} found {} valid product codes", batchIndex + 1, batchDataLists.size(), allProductCodes.size());

            // 批量查询现有的MasterSku记录
            List<MasterSku> existingMasterSkus = masterSkuDao.queryByProductCodes(allProductCodes);
            Map<String, MasterSku> existingMasterSkuMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(existingMasterSkus)) {
                for (MasterSku masterSku : existingMasterSkus) {
                    existingMasterSkuMap.put(masterSku.getProductCode(), masterSku);
                }
                log.info("Batch {}/{} found {} existing master SKUs in database",
                        batchIndex + 1, batchDataLists.size(), existingMasterSkuMap.size());
            }

            // 准备新增和更新的列表
            List<MasterSku> toInsertList = new ArrayList<>();
            List<MasterSku> toUpdateList = new ArrayList<>();
            Date now = new Date();

            int successCount = 0;
            int failCount = 0;

            // 处理每一条数据
            for (String productCode : allProductCodes) {
            try {
                JSONObject dataObj = dataMap.get(productCode);
                MasterSku existMasterSku = existingMasterSkuMap.get(productCode);

                // 创建新的MasterSku对象
                MasterSku masterSku = new MasterSku();
                masterSku.setProductCode(productCode);
                masterSku.setProductName(dataObj.getString("MAKTX")); // 商品名称

                // 设置SAP的日期和时间字段
                masterSku.setSapCreateDate(dataObj.getString("ERSDA")); // SAP创建日期
                masterSku.setSapCreateTime(dataObj.getString("CREATED")); // SAP创建时间
                masterSku.setSapUpdateDate(dataObj.getString("LAEDA")); // SAP更改日期
                masterSku.setSapUpdater(dataObj.getString("AENAM")); // SAP更改者
                masterSku.setMaterialType(dataObj.getString("MTART")); // 物料类型
                masterSku.setMaterialTypeDesc(dataObj.getString("MTARTT")); // 物料类型描述
                masterSku.setMaterialGroup(dataObj.getString("MATKL")); // 物料组
                masterSku.setMaterialGroupDesc(dataObj.getString("MATKLT")); // 物料组描述
                masterSku.setBaseUnitDesc(dataObj.getString("MEINST")); // 基本计量单位描述

                // 转换数值字段
                String grossWeightStr = dataObj.getString("BRGEW");
                if (StringUtils.isNotBlank(grossWeightStr)) {
                    masterSku.setGrossWeight(new BigDecimal(grossWeightStr)); // 毛重
                }

                String netWeightStr = dataObj.getString("NTGEW");
                if (StringUtils.isNotBlank(netWeightStr)) {
                    masterSku.setNetWeight(new BigDecimal(netWeightStr)); // 净重
                }

                masterSku.setWeightUnit(dataObj.getString("GEWEI")); // 重量单位
                masterSku.setBarcode(dataObj.getString("BCODE")); // 条码
                masterSku.setBrand(dataObj.getString("BRAND")); // 品牌
                masterSku.setShortName(dataObj.getString("BRNAM")); // 简称
                masterSku.setModelSpec(dataObj.getString("MODEL")); // 规格型号

                String shelfLifeStr = dataObj.getString("MAXLZ");
                if (StringUtils.isNotBlank(shelfLifeStr)) {
                    masterSku.setShelfLife(new BigDecimal(shelfLifeStr)); // 保质期
                }

                String lengthStr = dataObj.getString("ZLENGTH");
                if (StringUtils.isNotBlank(lengthStr)) {
                    masterSku.setLength(new BigDecimal(lengthStr)); // 长
                }

                String widthStr = dataObj.getString("ZWIDTH");
                if (StringUtils.isNotBlank(widthStr)) {
                    masterSku.setWidth(new BigDecimal(widthStr)); // 宽
                }

                String heightStr = dataObj.getString("ZHIGH");
                if (StringUtils.isNotBlank(heightStr)) {
                    masterSku.setHeight(new BigDecimal(heightStr)); // 高
                }

                String volumeStr = dataObj.getString("ZVOLUME");
                if (StringUtils.isNotBlank(volumeStr)) {
                    masterSku.setVolume(new BigDecimal(volumeStr)); // 体积
                }

                masterSku.setBatchManagementFlag(dataObj.getString("XCHPF")); // 批次管理需求的标识
                masterSku.setLayer11Unit(dataObj.getString("ZCW_UNIT")); // 11层：单位

                String layer9QuantityStr = dataObj.getString("ZGUIGE");
                if (StringUtils.isNotBlank(layer9QuantityStr)) {
                    masterSku.setLayer9Quantity(new BigDecimal(layer9QuantityStr)); // 9层：入数
                }

                String layer10QuantityStr = dataObj.getString("ZGUIGE2");
                if (StringUtils.isNotBlank(layer10QuantityStr)) {
                    masterSku.setLayer10Quantity(new BigDecimal(layer10QuantityStr)); // 10层：提数
                }

                masterSku.setTaxClassification(dataObj.getString("ZSSFL")); // 税收分类
                masterSku.setSnEnabled(dataObj.getString("ZSFSN")); // 是否启用SN
                masterSku.setTraceabilityEnabled(dataObj.getString("ZSFSY")); // 是否溯源
                masterSku.setPrimaryCategory(dataObj.getString("ZYJFL")); // 一级分类
                masterSku.setSecondaryCategory(dataObj.getString("ZEJFL")); // 二级分类
                masterSku.setTertiaryCategory(dataObj.getString("ZSJFL")); // 三级分类
                masterSku.setQuaternaryCategory(dataObj.getString("ZZSJFL")); // 四级分类
                masterSku.setOffMarketStatus(dataObj.getString("ZXSZT")); // 下市状态
                masterSku.setProductStatus(dataObj.getString("MSTAE")); // 商品状态
                masterSku.setZeroLevelDesc(dataObj.getString("ZPRODUCT_DL")); // 零级描述
                masterSku.setZeroLevelCode(dataObj.getString("ZLJID")); // 零级编码

                if (existMasterSku == null) {
                    // 新增
                    masterSku.setId(SeqUtils.getSequenceUid());
                    masterSku.setCreateTime(now);
                    masterSku.setUpdateTime(now);
                    toInsertList.add(masterSku);
                } else {
                    // 更新
                    masterSku.setId(existMasterSku.getId());
                    masterSku.setCreateTime(existMasterSku.getCreateTime());
                    masterSku.setUpdateTime(now);
                    toUpdateList.add(masterSku);
                }

                successCount++;
            } catch (Exception e) {
                log.error("Process master SKU data failed for product code: {}", productCode, e);
                failCount++;
            }
            }

            // 批量插入新记录
            if (!toInsertList.isEmpty()) {
                int insertCount = masterSkuDao.batchInsert(toInsertList);
                log.info("Batch {}/{} inserted {} new master SKUs", batchIndex + 1, batchDataLists.size(), insertCount);
                totalInsertCount += insertCount;
            }

            // 批量更新现有记录
            if (!toUpdateList.isEmpty()) {
                int updateCount = masterSkuDao.batchUpdate(toUpdateList);
                log.info("Batch {}/{} updated {} existing master SKUs", batchIndex + 1, batchDataLists.size(), updateCount);
                totalUpdateCount += updateCount;
            }

            totalSuccessCount += successCount;
            totalFailCount += failCount;

            log.info("Batch {}/{} completed, success: {}, fail: {}", batchIndex + 1, batchDataLists.size(), successCount, failCount);
        }

        log.info("Process master SKU data completed, total success: {}, total fail: {}, total inserted: {}, total updated: {}",
                totalSuccessCount, totalFailCount, totalInsertCount, totalUpdateCount);
    }

    /**
     * 分页查询主SKU信息
     *
     * @param pageCondition 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageInfo<MasterSku> queryMasterSkuPage(PageCondition<MasterSkuQueryRequest> pageCondition) {
        // 设置分页参数
        PageHelper.startPage(pageCondition.getPageNum(), pageCondition.getPageSize());

        // 执行查询
        List<MasterSku> list = masterSkuDao.queryMasterSkuPage(pageCondition.getCondition());

        // 返回分页结果
        return new PageInfo<>(list);
    }
}
