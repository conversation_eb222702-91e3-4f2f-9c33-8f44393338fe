package cn.aliyun.ryytn.modules.master.service;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.info.ProductInheritance;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.master.api.ProductInheritanceService;
import cn.aliyun.ryytn.modules.master.dao.MasterSkuDao;
import cn.aliyun.ryytn.modules.master.dao.ProductInheritanceDao;
import cn.aliyun.ryytn.modules.master.entity.enums.ChannelOwnershipEnum;
import cn.aliyun.ryytn.modules.master.entity.enums.EntityStatusEnum;
import cn.aliyun.ryytn.modules.master.entity.enums.ProductRelationTypeEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceInvalidateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceRequest;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 商品继承关系Service实现类
 * @date 2024/10/10 15:00
 */
@Slf4j
@Service
public class ProductInheritanceServiceImpl implements ProductInheritanceService {

    @Autowired
    private ProductInheritanceDao productInheritanceDao;

    @Autowired
    private MasterSkuDao masterSkuDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(ProductInheritanceRequest request) {
        // 获取当前用户
        String currentUser = getCurrentUser();
        Date now = new Date();

        // 业务校验
        validateRequest(request);

        // 检查唯一性
        int existCount = productInheritanceDao.checkUniqueness(
                request.getOriginalSkuId(),
                request.getNewSkuId(),
                request.getRelationType(),
                request.getCustomStore(),
                request.getCustomChannel(),
                request.getChannelOwnership(),
                request.getId()
        );
        if (existCount > 0) {
            throw new ServiceException("该商品继承关系已存在");
        }

        ProductInheritance inheritance = new ProductInheritance();
        inheritance.setOriginalSkuId(request.getOriginalSkuId());
        inheritance.setNewSkuId(request.getNewSkuId());
        inheritance.setRelationType(request.getRelationType());
        inheritance.setCustomStore(request.getCustomStore());
        inheritance.setCustomChannel(request.getCustomChannel());
        inheritance.setChannelOwnership(request.getChannelOwnership());
        inheritance.setUpdatedBy(currentUser);
        inheritance.setUpdatedTime(now);

        if (request.getId() == null || request.getId().trim().isEmpty()) {
            // 新增
            inheritance.setId(String.valueOf(SeqUtils.getSequenceUid()));
            inheritance.setCreatedBy(currentUser);
            inheritance.setCreatedTime(now);
            inheritance.setStatus(EntityStatusEnum.IS_ACTIVE.getCode());

            int result = productInheritanceDao.insert(inheritance);
            if (result <= 0) {
                throw new ServiceException("新增商品继承关系失败");
            }
            log.info("新增商品继承关系成功，ID: {}", inheritance.getId());
        } else {
            // 修改
            inheritance.setId(request.getId());

            // 检查记录是否存在
            ProductInheritance existInheritance = productInheritanceDao.selectById(request.getId());
            if (existInheritance == null) {
                throw new ServiceException("要修改的记录不存在");
            }
            if (!EntityStatusEnum.IS_ACTIVE.getCode().equals(existInheritance.getStatus())) {
                throw new ServiceException("只能修改有效状态的记录");
            }

            int result = productInheritanceDao.update(inheritance);
            if (result <= 0) {
                throw new ServiceException("修改商品继承关系失败");
            }
            log.info("修改商品继承关系成功，ID: {}", inheritance.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInvalidate(ProductInheritanceInvalidateRequest request) {
        // 参数校验
        if (request == null) {
            throw new ServiceException("请求参数不能为空");
        }

        if (CollectionUtils.isEmpty(request.getIds())) {
            throw new ServiceException("关系ID列表不能为空");
        }

        // 检查是否有已经作废的记录
        int invalidatedCount = productInheritanceDao.countInvalidated(request.getIds());
        if (invalidatedCount > 0) {
            throw new ServiceException("存在已经作废的记录，不能重复作废");
        }

        // 获取当前用户
        String currentUser = getCurrentUser();

        // 批量作废
        int result = productInheritanceDao.batchInvalidate(request.getIds(), currentUser);
        log.info("批量作废商品继承关系成功，作废记录数: {}", result);
    }

    @Override
    public PageInfo<ProductInheritanceQueryPageResponse> queryPage(PageCondition<ProductInheritanceQueryRequest> pageCondition) {
        // 设置分页参数
        PageHelper.startPage(pageCondition.getPageNum(), pageCondition.getPageSize());

        // 查询关系数据
        List<ProductInheritance> inheritanceList = productInheritanceDao.queryPage(pageCondition.getCondition());

        // 转换为VO
        List<ProductInheritanceQueryPageResponse> voList = convertToVOList(inheritanceList);

        return new PageInfo<>(voList);
    }

    /**
     * 业务校验
     */
    private void validateRequest(ProductInheritanceRequest request) {
        // 基本参数校验
        if (request == null) {
            throw new ServiceException("请求参数不能为空");
        }

        if (!StringUtils.hasText(request.getOriginalSkuId())) {
            throw new ServiceException("原商品ID不能为空");
        }

        if (!StringUtils.hasText(request.getNewSkuId())) {
            throw new ServiceException("新品商品ID不能为空");
        }

        if (request.getRelationType() == null) {
            throw new ServiceException("关系类型不能为空");
        }

        if (request.getChannelOwnership() == null) {
            throw new ServiceException("渠道归属不能为空");
        }

        // 校验关系类型
        ProductRelationTypeEnum relationTypeEnum = ProductRelationTypeEnum.getByCode(request.getRelationType());
        if (relationTypeEnum == null) {
            throw new ServiceException("无效的关系类型");
        }

        // 校验渠道归属
        ChannelOwnershipEnum channelOwnershipEnum = ChannelOwnershipEnum.getByCode(request.getChannelOwnership());
        if (channelOwnershipEnum == null) {
            throw new ServiceException("无效的渠道归属");
        }

        // 根据关系类型校验必填字段
        if (ProductRelationTypeEnum.STORE_CUSTOM.getCode().equals(request.getRelationType())) {
            if (!StringUtils.hasText(request.getCustomStore())) {
                throw new ServiceException("当关系类型为店铺定制时，定制店铺不能为空");
            }
        }

        if (ProductRelationTypeEnum.CHANNEL_CUSTOM.getCode().equals(request.getRelationType())) {
            if (!StringUtils.hasText(request.getCustomChannel())) {
                throw new ServiceException("当关系类型为渠道定制时，定制渠道不能为空");
            }
        }
    }

    /**
     * 转换为VO列表
     */
    private List<ProductInheritanceQueryPageResponse> convertToVOList(List<ProductInheritance> inheritanceList) {
        if (CollectionUtils.isEmpty(inheritanceList)) {
            return new ArrayList<>();
        }

        // 收集所有需要查询的SKU ID
        Set<String> skuIds = new HashSet<>();
        for (ProductInheritance inheritance : inheritanceList) {
            if (inheritance.getOriginalSkuId() != null && !inheritance.getOriginalSkuId().trim().isEmpty()) {
                skuIds.add(inheritance.getOriginalSkuId());
            }
            if (inheritance.getNewSkuId() != null && !inheritance.getNewSkuId().trim().isEmpty()) {
                skuIds.add(inheritance.getNewSkuId());
            }
        }

        // 批量查询SKU信息
        Map<String, MasterSku> skuMap = new HashMap<>();
        if (!skuIds.isEmpty()) {
            List<String> idList = new ArrayList<>(skuIds);
            List<MasterSku> skuList = masterSkuDao.queryByIds(idList);
            if (!CollectionUtils.isEmpty(skuList)) {
                skuMap = skuList.stream().collect(Collectors.toMap(sku -> String.valueOf(sku.getId()), sku -> sku));
            }
        }

        // 转换为VO
        List<ProductInheritanceQueryPageResponse> voList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (ProductInheritance inheritance : inheritanceList) {
            ProductInheritanceQueryPageResponse vo = new ProductInheritanceQueryPageResponse();
            BeanUtils.copyProperties(inheritance, vo);

            // 设置原商品SKU信息
            if (inheritance.getOriginalSkuId() != null && !inheritance.getOriginalSkuId().trim().isEmpty()) {
                MasterSku originalSku = skuMap.get(inheritance.getOriginalSkuId());
                if (originalSku != null) {
                    vo.setOriginalSkuCode(originalSku.getProductCode());
                    vo.setOriginalSkuName(originalSku.getProductName());
                }
            }

            // 设置新品商品SKU信息
            if (inheritance.getNewSkuId() != null && !inheritance.getNewSkuId().trim().isEmpty()) {
                MasterSku newSku = skuMap.get(inheritance.getNewSkuId());
                if (newSku != null) {
                    vo.setNewSkuCode(newSku.getProductCode());
                    vo.setNewSkuName(newSku.getProductName());
                }
            }

            // 设置时间字符串格式
            if (inheritance.getCreatedTime() != null) {
                vo.setCreatedTimeStr(dateFormat.format(inheritance.getCreatedTime()));
            }
            if (inheritance.getUpdatedTime() != null) {
                vo.setUpdatedTimeStr(dateFormat.format(inheritance.getUpdatedTime()));
            }

            // 设置关系类型描述
            vo.setRelationTypeDesc(ProductRelationTypeEnum.getDescByCode(vo.getRelationType()));

            // 设置渠道归属描述
            vo.setChannelOwnershipDesc(ChannelOwnershipEnum.getDescByCode(vo.getChannelOwnership()));

            // 设置状态描述
            vo.setStatusDesc(EntityStatusEnum.getNameByCode(vo.getStatus()));

            voList.add(vo);
        }

        return voList;
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        try {
            return ServiceContextUtils.currentSession().getAccount().getLoginId();
        } catch (Exception e) {
            log.warn("获取当前用户失败，使用默认用户", e);
            return "SYSTEM";
        }
    }
}
