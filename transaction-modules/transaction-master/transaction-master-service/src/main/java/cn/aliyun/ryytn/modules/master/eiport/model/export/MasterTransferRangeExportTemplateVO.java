package cn.aliyun.ryytn.modules.master.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 调拨仓辐射导出模板VO
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
public class MasterTransferRangeExportTemplateVO {
    /**
     * 仓库ID
     */
    private String warehouseId;

    /**
     * 仓库编码
     */
    @ExcelField("仓库编码")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ExcelField("仓库名称")
    private String warehouseName;

    /**
     * 辐射类型
     */
    private Integer radiationType;

    /**
     * 辐射类型名称
     */
    @ExcelField("辐射类型")
    private String radiationTypeName;

    /**
     * 省ID
     */
    @ExcelField("省编码")
    private String provinceId;

    /**
     * 省名称
     */
    @ExcelField("省名称")
    private String provinceName;

    /**
     * 市ID
     */
    @ExcelField("市编码")
    private String cityId;

    /**
     * 市名称
     */
    @ExcelField("市名称")
    private String cityName;

    /**
     * 辐射距离（公里）
     */
    @ExcelField("辐射距离（公里）")
    private BigDecimal distance;

    /**
     * 生效时间
     */
    @ExcelField("生效时间")
    private String activeTimeStr;

    /**
     * 结束时间
     */
    @ExcelField("结束时间")
    private String disableTimeStr;

    /**
     * 状态
     */
    @ExcelField("状态")
    private String statusName;

    /**
     * 创建人
     */
    @ExcelField("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelField("创建时间")
    private String createdTimeStr;

    /**
     * 更新人
     */
    @ExcelField("更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
    @ExcelField("更新时间")
    private String updatedTimeStr;
}