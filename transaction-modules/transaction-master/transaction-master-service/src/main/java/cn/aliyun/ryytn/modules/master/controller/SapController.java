package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.master.api.MasterWarehouseService;
import cn.aliyun.ryytn.modules.master.entity.vo.SapSyncMasterWarehouseRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description SAP数据同步接口
 * @date 2023年9月19日 下午2:11:38
 */
@Slf4j
@RestController
@RequestMapping("/api/master/sap")
@Api(tags = "SAP对接")
public class SapController {
    @Autowired
    private MasterWarehouseService masterWarehouseService;


    @PostMapping("syncMasterInfo")
    @ResponseBody
    @ApiOperation("同步SAP系统分部数据")
//    @RequiresPermissions({})
    public ResultInfo<?> syncMasterInfo(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("syncMasterInfo requestBody:{}", jsonObject.toJSONString());
        JSONObject ctrl = jsonObject.getJSONObject("CTRL");
        if (Objects.isNull(ctrl)) {
            return ResultInfo.fail(ErrorCodeConstants.FAIL_PARAM_EMPTY, "基本信息不能为空");
        }
        String loginId = Optional.ofNullable(ctrl.getString("UNAME")).orElse("").toLowerCase();

        String funId = ctrl.getString("FUNID");
        if ("ZSCMINF002".equals(funId)) {
            JSONArray dataList = jsonObject.getJSONArray("DATA");
            if (CollectionUtils.isEmpty(dataList)) {
                return ResultInfo.fail(ErrorCodeConstants.FAIL_PARAM_EMPTY, "参数不能为空");
            }

            List<SapSyncMasterWarehouseRequest> requestList = dataList.toJavaList(SapSyncMasterWarehouseRequest.class);
            masterWarehouseService.syncMasterWarehouseInfo(requestList, loginId);
            return ResultInfo.success();
        }

        return ResultInfo.fail(ErrorCodeConstants.FAIL_OTHER, "FUNID未识别");
    }

}
