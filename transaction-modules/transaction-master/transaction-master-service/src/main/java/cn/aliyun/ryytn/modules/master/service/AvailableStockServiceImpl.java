package cn.aliyun.ryytn.modules.master.service;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.info.AvailableStock;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.common.entity.sap.MasterWarehouse;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.master.api.AvailableStockService;
import cn.aliyun.ryytn.modules.master.dao.AvailableStockDao;
import cn.aliyun.ryytn.modules.master.dao.MasterSkuDao;
import cn.aliyun.ryytn.modules.master.dao.MasterWarehouseDao;
import cn.aliyun.ryytn.modules.master.entity.enums.EntityStatusEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockInvalidateRequest;

import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockRequest;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 可用库存Service实现类
 * @date 2024/10/10 15:00
 */
@Slf4j
@Service
public class AvailableStockServiceImpl implements AvailableStockService {

    @Autowired
    private AvailableStockDao availableStockDao;

    @Autowired
    private MasterSkuDao masterSkuDao;

    @Autowired
    private MasterWarehouseDao masterWarehouseDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(AvailableStockRequest request) {
        // 参数校验
        validateRequest(request);

        // 获取当前用户
        String currentUser = getCurrentUser();
        Date now = new Date();

        // 检查唯一性
        int existCount = availableStockDao.checkUniqueness(
                request.getFactoryName(),
                request.getSkuId(),
                request.getWarehouseId(),
                request.getProductionDate(),
                request.getId()
        );
        if (existCount > 0) {
            throw new ServiceException("该工厂、商品、仓库和生产日期的库存记录已存在");
        }

        AvailableStock stock = new AvailableStock();
        stock.setFactoryName(request.getFactoryName());
        stock.setSkuId(request.getSkuId());
        stock.setWarehouseId(request.getWarehouseId());
        stock.setProductionDate(request.getProductionDate());
        stock.setQuantity(request.getQuantity());
        stock.setStockDate(request.getStockDate());
        stock.setUpdatedBy(currentUser);
        stock.setUpdatedTime(now);

        if (request.getId() == null || request.getId().trim().isEmpty()) {
            // 新增
            stock.setId(String.valueOf(SeqUtils.getSequenceUid()));
            stock.setCreatedBy(currentUser);
            stock.setCreatedTime(now);
            stock.setStatus(EntityStatusEnum.IS_ACTIVE.getCode());

            int result = availableStockDao.insert(stock);
            if (result <= 0) {
                throw new ServiceException("新增可用库存失败");
            }
            log.info("新增可用库存成功，ID: {}", stock.getId());
        } else {
            // 修改
            stock.setId(request.getId());

            // 检查记录是否存在
            AvailableStock existStock = availableStockDao.selectById(request.getId());
            if (existStock == null) {
                throw new ServiceException("要修改的记录不存在");
            }
            if (!EntityStatusEnum.IS_ACTIVE.getCode().equals(existStock.getStatus())) {
                throw new ServiceException("只能修改有效状态的记录");
            }

            int result = availableStockDao.update(stock);
            if (result <= 0) {
                throw new ServiceException("修改可用库存失败");
            }
            log.info("修改可用库存成功，ID: {}", stock.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInvalidate(AvailableStockInvalidateRequest request) {
        // 参数校验
        if (request == null) {
            throw new ServiceException("请求参数不能为空");
        }

        if (CollectionUtils.isEmpty(request.getIds())) {
            throw new ServiceException("库存ID列表不能为空");
        }

        // 检查是否有已经作废的记录
        int invalidatedCount = availableStockDao.countInvalidated(request.getIds());
        if (invalidatedCount > 0) {
            throw new ServiceException("存在已经作废的记录，不能重复作废");
        }

        // 获取当前用户
        String currentUser = getCurrentUser();

        // 批量作废
        int result = availableStockDao.batchInvalidate(request.getIds(), currentUser);
        log.info("批量作废可用库存成功，作废记录数: {}", result);
    }

    @Override
    public PageInfo<AvailableStockQueryPageResponse> queryPage(PageCondition<AvailableStockQueryRequest> pageCondition) {
        // 设置分页参数
        PageHelper.startPage(pageCondition.getPageNum(), pageCondition.getPageSize());

        // 查询库存数据
        List<AvailableStock> stockList = availableStockDao.queryPage(pageCondition.getCondition());

        // 转换为VO
        List<AvailableStockQueryPageResponse> voList = convertToVOList(stockList);

        return new PageInfo<>(voList);
    }

    /**
     * 转换为VO列表
     */
    private List<AvailableStockQueryPageResponse> convertToVOList(List<AvailableStock> stockList) {
        if (CollectionUtils.isEmpty(stockList)) {
            return new ArrayList<>();
        }

        // 收集所有需要查询的SKU ID和仓库ID
        Set<String> skuIds = new HashSet<>();
        Set<String> warehouseIds = new HashSet<>();
        for (AvailableStock stock : stockList) {
            if (stock.getSkuId() != null && !stock.getSkuId().trim().isEmpty()) {
                skuIds.add(stock.getSkuId());
            }
            if (stock.getWarehouseId() != null && !stock.getWarehouseId().trim().isEmpty()) {
                warehouseIds.add(stock.getWarehouseId());
            }
        }

        // 批量查询SKU信息
        Map<String, MasterSku> skuMap = new HashMap<>();
        if (!skuIds.isEmpty()) {
            List<String> skuIdList = new ArrayList<>(skuIds);
            List<MasterSku> skuList = masterSkuDao.queryByIds(skuIdList);
            if (!CollectionUtils.isEmpty(skuList)) {
                skuMap = skuList.stream().collect(Collectors.toMap(sku -> String.valueOf(sku.getId()), sku -> sku));
            }
        }

        // 批量查询仓库信息
        Map<String, MasterWarehouse> warehouseMap = new HashMap<>();
        if (!warehouseIds.isEmpty()) {
            List<MasterWarehouse> warehouseList = masterWarehouseDao.selectByIds(warehouseIds);
            if (!CollectionUtils.isEmpty(warehouseList)) {
                warehouseMap = warehouseList.stream().collect(Collectors.toMap(MasterWarehouse::getId, warehouse -> warehouse));
            }
        }

        // 转换为VO
        List<AvailableStockQueryPageResponse> voList = new ArrayList<>();
        for (AvailableStock stock : stockList) {
            AvailableStockQueryPageResponse vo = new AvailableStockQueryPageResponse();
            BeanUtils.copyProperties(stock, vo);

            // 设置SKU信息
            if (stock.getSkuId() != null && !stock.getSkuId().trim().isEmpty()) {
                MasterSku sku = skuMap.get(stock.getSkuId());
                if (sku != null) {
                    vo.setSkuCode(sku.getProductCode());
                    vo.setSkuName(sku.getProductName());
                }
            }

            // 设置仓库信息
            if (stock.getWarehouseId() != null && !stock.getWarehouseId().trim().isEmpty()) {
                MasterWarehouse warehouse = warehouseMap.get(stock.getWarehouseId());
                if (warehouse != null) {
                    vo.setWarehouseCode(warehouse.getWarehouseCode());
                    vo.setWarehouseName(warehouse.getWarehouseName());
                }
            }

            // 设置状态描述
            vo.setStatusDesc(EntityStatusEnum.getNameByCode(vo.getStatus()));

            voList.add(vo);
        }

        return voList;
    }

    /**
     * 参数校验
     */
    private void validateRequest(AvailableStockRequest request) {
        if (request == null) {
            throw new ServiceException("请求参数不能为空");
        }

        if (!StringUtils.hasText(request.getFactoryName())) {
            throw new ServiceException("工厂名称不能为空");
        }

        if (!StringUtils.hasText(request.getSkuId())) {
            throw new ServiceException("商品ID不能为空");
        }

        if (!StringUtils.hasText(request.getWarehouseId())) {
            throw new ServiceException("仓库ID不能为空");
        }

        if (!StringUtils.hasText(request.getProductionDate())) {
            throw new ServiceException("生产日期不能为空");
        }

        if (request.getQuantity() == null) {
            throw new ServiceException("可用库存数量不能为空");
        }

        if (!StringUtils.hasText(request.getStockDate())) {
            throw new ServiceException("库存快照日期不能为空");
        }
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        try {
            return ServiceContextUtils.currentSession().getAccount().getLoginId();
        } catch (Exception e) {
            log.warn("获取当前用户失败，使用默认用户", e);
            return "SYSTEM";
        }
    }
}
