package cn.aliyun.ryytn.modules.master.eiport.model;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 运输成本导入模板VO
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MasterTransferCostImportTemplateVO extends ImportTemplateVO {
    /**
     * 出发省编码
     */
    @ExcelField(value = "出发省编码", importKey = true)
    private String fromProvinceCode;

    /**
     * 出发市编码
     */
    @ExcelField(value = "出发市编码", importKey = true)
    private String fromCityCode;

    /**
     * 到达省编码
     */
    @ExcelField(value = "到达省编码", importKey = true)
    private String toProvinceCode;

    /**
     * 到达市编码
     */
    @ExcelField(value = "到达市编码", importKey = true)
    private String toCityCode;

    /**
     * 十三米（元/吨）
     */
    @ExcelField("十三米（元/吨）")
    private BigDecimal cost13m;

    /**
     * 十五米（元/吨）
     */
    @ExcelField("十五米（元/吨）")
    private BigDecimal cost15m;

    /**
     * 铁柜（元/吨）
     */
    @ExcelField("铁柜（元/吨）")
    private BigDecimal costRail;

    /**
     * 汽运时效（天）
     */
    @ExcelField("汽运时效（天）")
    private Integer roadDuration;

    /**
     * 铁运时效（天）
     */
    @ExcelField("铁运时效（天）")
    private Integer railDuration;

    // 以下字段用于内部处理，不在Excel中显示
    /**
     * 出发省ID（内部使用）
     */
    private String fromProvinceId;

    /**
     * 出发市ID（内部使用）
     */
    private String fromCityId;

    /**
     * 到达省ID（内部使用）
     */
    private String toProvinceId;

    /**
     * 到达市ID（内部使用）
     */
    private String toCityId;
}