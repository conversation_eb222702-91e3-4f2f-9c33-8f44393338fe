package cn.aliyun.ryytn.modules.master.eiport.model;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description 转移替代关系导入模板VO
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TransferSubstitutionImportTemplateVO extends ImportTemplateVO {

    /**
     * 原商品编码
     */
    @ExcelField(value = "原商品编码", importKey = true)
    private String originalSkuCode;

    /**
     * 原商品名称
     */
    @ExcelField("原商品名称")
    private String originalSkuName;

    /**
     * 替代商品编码
     */
    @ExcelField(value = "替代商品编码", importKey = true)
    private String substituteSkuCode;

    /**
     * 替代商品名称
     */
    @ExcelField("替代商品名称")
    private String substituteSkuName;

    // 以下字段用于内部处理，不在Excel中显示
    /**
     * 原商品ID（内部使用）
     */
    private String originalSkuId;

    /**
     * 替代商品ID（内部使用）
     */
    private String substituteSkuId;
}
