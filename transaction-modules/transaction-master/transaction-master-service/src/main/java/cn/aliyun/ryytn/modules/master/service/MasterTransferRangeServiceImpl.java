package cn.aliyun.ryytn.modules.master.service;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.info.MasterRegion;
import cn.aliyun.ryytn.common.entity.info.MasterTransferRange;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.common.entity.sap.MasterWarehouse;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.modules.master.api.MasterRegionService;
import cn.aliyun.ryytn.modules.master.api.MasterSkuService;
import cn.aliyun.ryytn.modules.master.api.MasterTransferRangeService;
import cn.aliyun.ryytn.modules.master.api.MasterWarehouseService;
import cn.aliyun.ryytn.modules.master.dao.MasterTransferRangeDao;
import cn.aliyun.ryytn.modules.master.entity.enums.EntityStatusEnum;
import cn.aliyun.ryytn.modules.master.entity.enums.TransferRangeRadiationTypeEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangeCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangeUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangeUpdateStatusRequest;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 调拨仓辐射服务实现
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@Service
public class MasterTransferRangeServiceImpl implements MasterTransferRangeService {

    @Resource
    private MasterTransferRangeDao masterTransferRangeDao;

    @Resource
    private MasterRegionService masterRegionService;

    @Resource
    private MasterSkuService masterSkuService;

    @Resource
    private MasterWarehouseService masterWarehouseService;

    @Override
    public PageInfo<MasterTransferRangePageResponse> queryByPage(PageCondition<MasterTransferRangePageRequest> condition) {
        // 设置分页参数
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());

        // 执行查询
        List<MasterTransferRangePageResponse> responseList = masterTransferRangeDao.queryByPage(condition.getCondition());
        // 处理状态名称和辐射类型名称
        for (MasterTransferRangePageResponse response : responseList) {
            // 设置状态名称
            response.setStatusName(EntityStatusEnum.getNameByCode(response.getStatus()));
            // 设置辐射类型名称
            response.setRadiationTypeName(TransferRangeRadiationTypeEnum.getNameByCode(response.getRadiationType()));

            response.setCreatedTimeStr(DateUtil.format(response.getCreatedTime(), DatePattern.NORM_DATETIME_PATTERN));
            response.setUpdatedTimeStr(DateUtil.format(response.getUpdatedTime(), DatePattern.NORM_DATETIME_PATTERN));
        }

        return new PageInfo<>(responseList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(MasterTransferRangeCreateRequest request, String loginId) {
        // 转换为实体
        MasterTransferRange entity = new MasterTransferRange();
        entity.setRadiationType(request.getRadiationType());
        entity.setSkuId(request.getSkuId());
        entity.setProvinceId(request.getProvinceId());
        entity.setCityId(request.getCityId());
        entity.setWarehouseId(request.getWarehouseId());
        entity.setSalesDepartment(request.getSalesDepartment());
        entity.setStartKg(request.getStartKg());
        entity.setEndKg(request.getEndKg());

        entity.setStatus(EntityStatusEnum.IS_ACTIVE.getCode());
        entity.setCreatedTime(new Date());
        entity.setUpdatedTime(new Date());
        entity.setCreatedBy(loginId);
        entity.setUpdatedBy(loginId);

        // 验证数据存在性和唯一性
        checkExistenceAndUniqueness(entity);

        // 生成ID并插入
//        entity.setId(SeqUtils.getSequenceUid());
        int result = masterTransferRangeDao.insert(entity);
        if (result <= 0) {
            throw new ServiceException("创建调拨仓辐射失败");
        }

        log.info("调拨仓辐射创建成功，ID: {}, 操作人: {}", entity.getId(), loginId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MasterTransferRangeUpdateRequest request, String loginId) {
        // 查询原记录是否存在
        MasterTransferRange existingRange = masterTransferRangeDao.selectById(request.getId());
        if (existingRange == null) {
            throw new ServiceException("调拨仓辐射信息不存在");
        }
        if (EntityStatusEnum.NO_ACTIVE.getCode().equals(existingRange.getStatus())) {
            throw new ServiceException("调拨仓辐射信息已失效，不能修改");
        }

        // 转换为实体
        MasterTransferRange entity = new MasterTransferRange();
        entity.setId(request.getId());
        entity.setRadiationType(request.getRadiationType());
        entity.setSkuId(request.getSkuId());
        entity.setProvinceId(request.getProvinceId());
        entity.setCityId(request.getCityId());
        entity.setWarehouseId(request.getWarehouseId());
        entity.setSalesDepartment(request.getSalesDepartment());
        entity.setStartKg(request.getStartKg());
        entity.setEndKg(request.getEndKg());
        entity.setUpdatedTime(new Date());
        entity.setUpdatedBy(loginId);

        // 验证数据存在性和唯一性
        checkExistenceAndUniqueness(entity);

        // 更新记录
        int result = masterTransferRangeDao.update(entity);
        if (result <= 0) {
            throw new ServiceException("修改调拨仓辐射失败");
        }

        log.info("调拨仓辐射修改成功，ID: {}, 操作人: {}", entity.getId(), loginId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(MasterTransferRangeUpdateStatusRequest request, String loginId) {
        // 查询原记录是否存在
        MasterTransferRange existingRange = masterTransferRangeDao.selectById(request.getId());
        if (existingRange == null) {
            throw new ServiceException("调拨仓辐射信息不存在");
        }

        // 获取当前状态
        Integer currentStatus = existingRange.getStatus();
        if (EntityStatusEnum.NO_ACTIVE.getCode().equals(currentStatus)) {
            throw new ServiceException("当前状态已为无效状态");
        }

        // 计算新状态（切换状态：1->2 或 2->1）
        Integer newStatus = EntityStatusEnum.IS_ACTIVE.getCode().equals(currentStatus) ?
                EntityStatusEnum.NO_ACTIVE.getCode() : EntityStatusEnum.IS_ACTIVE.getCode();

        MasterTransferRange updateEntity = new MasterTransferRange();
        updateEntity.setId(request.getId());
        updateEntity.setStatus(newStatus);
        updateEntity.setUpdatedTime(new Date());
        updateEntity.setUpdatedBy(loginId);

        masterTransferRangeDao.updateStatus(updateEntity);

        log.info("调拨仓辐射状态已更新，ID: {}, 旧状态: {}, 新状态: {}, 操作人: {}",
                request.getId(), currentStatus, newStatus, loginId);
    }

    /**
     * 检查数据存在性和唯一性
     *
     * @param entity 调拨仓辐射实体
     */
    private void checkExistenceAndUniqueness(MasterTransferRange entity) {
        // 1. 检查商品是否存在
        if (!StringUtils.hasText(entity.getSkuId())) {
            throw new ServiceException("商品ID不能为空");
        }
        List<MasterSku> skuList = masterSkuService.queryByIds(Collections.singletonList(entity.getSkuId()));
        if (CollectionUtils.isEmpty(skuList)) {
            throw new ServiceException("商品不存在");
        }

        // 2. 检查省市是否存在
        List<String> regionIds = new ArrayList<>();
        if (StringUtils.hasText(entity.getProvinceId())) {
            regionIds.add(entity.getProvinceId());
        }
        if (StringUtils.hasText(entity.getCityId())) {
            regionIds.add(entity.getCityId());
        }
        if (!regionIds.isEmpty()) {
            List<MasterRegion> regions = masterRegionService.queryByIds(regionIds);
            Map<String, MasterRegion> regionMap = regions.stream()
                    .collect(Collectors.toMap(MasterRegion::getId, Function.identity(), (k1, k2) -> k1));
            // 检查省份
            if (StringUtils.hasText(entity.getProvinceId())
                    && !regionMap.containsKey(entity.getProvinceId())) {
                throw new ServiceException("省份不存在");
            }
            // 检查城市
            if (StringUtils.hasText(entity.getCityId())
                    && !regionMap.containsKey(entity.getCityId())) {
                throw new ServiceException("城市不存在");
            }
        }

        // 3. 检查仓库是否存在
        if (StringUtils.hasText(entity.getWarehouseId())) {
            List<MasterWarehouse> warehouses = masterWarehouseService.queryByIds(
                    Collections.singletonList(entity.getWarehouseId()));
            if (CollectionUtils.isEmpty(warehouses)) {
                throw new ServiceException("仓库不存在");
            }
        }

        // 4. 检查组合唯一性
        int count = masterTransferRangeDao.countOverlap(entity);
        if (count > 0) {
            throw new ServiceException("相同组合的调拨仓辐射已存在");
        }
    }
}