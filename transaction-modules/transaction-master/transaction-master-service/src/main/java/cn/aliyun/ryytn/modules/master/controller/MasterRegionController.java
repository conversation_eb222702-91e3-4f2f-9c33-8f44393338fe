package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.master.api.MasterRegionService;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionUpdateStatusRequest;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 区域信息
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@RestController
@RequestMapping("/api/master/masterRegion")
@Api(tags = "区域信息")
public class MasterRegionController {
    @Resource
    private MasterRegionService masterRegionService;

    @PostMapping("/page")
    @ResponseBody
    @ApiOperation("分页查询区域信息列表")
//    @RequiresPermissions(value = {""})
    public ResultInfo<PageInfo<MasterRegionPageResponse>> queryByPage(
            @RequestBody PageCondition<MasterRegionPageRequest> condition) {
        if (Objects.isNull(condition)) {
            condition = new PageCondition<>();
        }

        PageInfo<MasterRegionPageResponse> page = masterRegionService.queryByPage(condition);
        return ResultInfo.success(page);
    }

    @PostMapping("/create")
    @ResponseBody
    @ApiOperation("保存区域信息")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> create(@RequestBody @Validated MasterRegionCreateRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterRegionService.create(request, loginId);
        return ResultInfo.success();
    }

    @PostMapping("/disable")
    @ResponseBody
    @ApiOperation("禁用")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> disable(@RequestBody @Validated MasterRegionUpdateStatusRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterRegionService.updateStatus(request, loginId);
        return ResultInfo.success();
    }

    @PostMapping("/update")
    @ResponseBody
    @ApiOperation("修改区域信息")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> update(@RequestBody @Validated MasterRegionUpdateRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterRegionService.update(request, loginId);
        return ResultInfo.success();
    }
}
