package cn.aliyun.ryytn.modules.master.eiport.model;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description 可用库存导入模板VO
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AvailableStockImportTemplateVO extends ImportTemplateVO {

    /**
     * 工厂名称
     */
    @ExcelField(value = "工厂名称", importKey = true)
    private String factoryName;

    /**
     * 商品编码
     */
    @ExcelField(value = "商品编码", importKey = true)
    private String skuCode;

    /**
     * 商品名称
     */
    @ExcelField("商品名称")
    private String skuName;

    /**
     * 仓库编码
     */
    @ExcelField(value = "仓库编码", importKey = true)
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ExcelField("仓库名称")
    private String warehouseName;

    /**
     * 生产日期
     */
    @ExcelField(value = "生产日期", importKey = true)
    private String productionDate;

    /**
     * 可用库存数量
     */
    @ExcelField("可用库存数量")
    private BigDecimal quantity;

    /**
     * 库存快照日期
     */
    @ExcelField("库存快照日期")
    private String stockDate;

    // 以下字段用于内部处理，不在Excel中显示
    /**
     * 商品ID（内部使用）
     */
    private String skuId;

    /**
     * 仓库ID（内部使用）
     */
    private String warehouseId;
}
