package cn.aliyun.ryytn.modules.master.eiport;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.sap.MasterWarehouse;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.modules.master.api.MasterTransferPathService;
import cn.aliyun.ryytn.modules.master.api.MasterWarehouseService;
import cn.aliyun.ryytn.modules.master.eiport.model.MasterTransferPathImportTemplateVO;
import cn.aliyun.ryytn.modules.master.eiport.model.export.MasterTransferPathExportTemplateVO;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferPathCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageResponse;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 调拨路径导入导出模板
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Slf4j
@ImportService(value = "masterTransferPathEIportDataTemplate",
        filename = "调拨路径导入模版",
        templateDesc = "填写规范：\n" +
                "1.发货仓编码：填写有效的仓库编码\n" +
                "2.收货仓编码：填写有效的仓库编码，且不能与发货仓相同\n" +
                "3.生效时间：填写有效的日期时间，格式为yyyy-MM-dd HH:mm:ss\n" +
                "4.结束时间：填写有效的日期时间，格式为yyyy-MM-dd HH:mm:ss",
        async = false)
@ExportService(value = "masterTransferPathEIportDataTemplate",
        filename = "调拨路径数据导出",
        async = false)
public class MasterTransferPathEIportDataTemplate
        extends EIPortDataTemplate<MasterTransferPathImportTemplateVO, MasterWarehousePathPageRequest>
        implements ImportPostProcessor<MasterTransferPathImportTemplateVO> {

    @Autowired
    private MasterTransferPathService masterTransferPathService;

    @Autowired
    private MasterWarehouseService masterWarehouseService;


    @Override
    public boolean preValid(ImportDataWrapper<MasterTransferPathImportTemplateVO> importDataWrapper) {
        List<MasterTransferPathImportTemplateVO> importData = importDataWrapper.getData();
        return validate(importData);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<MasterTransferPathImportTemplateVO> importDataWrapper) {
//        List<MasterTransferPathImportTemplateVO> importDataList = importDataWrapper.getData();
//
//        String loginId = "c012087";
////        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
//
//        // 批量保存
//        for (MasterTransferPathImportTemplateVO template : importDataList) {
//            MasterTransferPathCreateRequest request = new MasterTransferPathCreateRequest();
//            request.setSendWarehouseId(template.getSendWarehouseId());
//            request.setReceiveWarehouseId(template.getReceiveWarehouseId());
//            request.setActiveTime(template.getActiveTime());
//            request.setDisableTime(template.getDisableTime());
//
//            try {
//                masterTransferPathService.create(request, loginId);
//            } catch (Exception e) {
//                log.warn("导入出错，{}", Throwables.getStackTraceAsString(e));
//                template.setErrorMsg("导入出错：" + e.getMessage());
//            }
//        }
        return null;
    }

    @Override
    public List<?> getExportTableData(MasterWarehousePathPageRequest request) {
        // 查询数据
        PageCondition<MasterWarehousePathPageRequest> pageCondition = new PageCondition<>();
        pageCondition.setPageNum(1);
        pageCondition.setPageSize(Integer.MAX_VALUE);
        pageCondition.setCondition(request);
        PageInfo<MasterWarehousePathPageResponse> pageInfo = masterTransferPathService.queryByPage(pageCondition);
        List<MasterWarehousePathPageResponse> relationList = pageInfo.getList();

        // 直接使用 GeiCommonConvert 转换，字段名已经匹配
        return GeiCommonConvert.convert(relationList, MasterTransferPathExportTemplateVO.class);
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(MasterWarehousePathPageRequest request) {
        return ExcelMetaInfoCreator.create(MasterTransferPathExportTemplateVO.class);
    }

    /**
     * 校验导入数据
     */
    private boolean validate(List<MasterTransferPathImportTemplateVO> importDataList) {
        boolean flag = true;

        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();

        // 1. 基本字段校验
        for (MasterTransferPathImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            if (StringUtils.isBlank(template.getSendWarehouseCode())) {
                errorMsg.append("发货仓编码不能为空").append("；");
            }

            if (StringUtils.isBlank(template.getReceiveWarehouseCode())) {
                errorMsg.append("收货仓编码不能为空").append("；");
            }

            if (template.getActiveTime() == null) {
                errorMsg.append("生效时间不能为空").append("；");
            }

            if (template.getDisableTime() == null) {
                errorMsg.append("结束时间不能为空").append("；");
            }

            if (template.getActiveTime().after(template.getDisableTime())) {
                throw new ServiceException("生效时间不能晚于结束时间");
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 2. 校验仓库名称并设置ID
        for (MasterTransferPathImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            List<MasterWarehouse> warehouseList =
                    masterWarehouseService.queryByCode(Arrays.asList(template.getReceiveWarehouseCode(), template.getSendWarehouseCode()));
            if (CollectionUtils.isEmpty(warehouseList)) {
                errorMsg.append("发货仓和收货仓不存在").append("；");
            }

            // 将查询结果转换为Map，方便后续处理
            Map<String, MasterWarehouse> warehouseMap = warehouseList.stream()
                    .collect(Collectors.toMap(MasterWarehouse::getWarehouseCode, warehouse -> warehouse));

            // 检查发货仓是否存在
            MasterWarehouse sendWarehouse = warehouseMap.get(template.getSendWarehouseCode());
            // 校验发货仓
            if (sendWarehouse == null) {
                errorMsg.append(String.format("发货仓[%s]不存在", template.getSendWarehouseCode())).append("；");
            } else {
                template.setSendWarehouseId(sendWarehouse.getId());
            }

            // 校验收货仓
            MasterWarehouse receiveWarehouse = warehouseMap.get(template.getReceiveWarehouseCode());
            if (receiveWarehouse == null) {
                errorMsg.append(String.format("收货仓[%s]不存在", template.getReceiveWarehouseCode())).append("；");
            } else {
                template.setReceiveWarehouseId(receiveWarehouse.getId());
            }

            // 检查是否为同一仓库
            if (sendWarehouse != null && receiveWarehouse != null
                    && sendWarehouse.getId().equals(receiveWarehouse.getId())) {
                errorMsg.append("发货仓和收货仓不能相同").append("；");
            }

            /*保存数据*/
            saveData(template, loginId);

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        return flag;
    }

    private void saveData(MasterTransferPathImportTemplateVO template, String loginId) {
        MasterTransferPathCreateRequest request = new MasterTransferPathCreateRequest();
        request.setSendWarehouseId(template.getSendWarehouseId());
        request.setReceiveWarehouseId(template.getReceiveWarehouseId());
        request.setActiveTime(template.getActiveTime());
        request.setDisableTime(template.getDisableTime());
        try {
            masterTransferPathService.create(request, loginId);
        } catch (Exception e) {
            log.warn("导入出错，{}", Throwables.getStackTraceAsString(e));
            template.setErrorMsg("导入出错：" + e.getMessage());
        }
    }
}