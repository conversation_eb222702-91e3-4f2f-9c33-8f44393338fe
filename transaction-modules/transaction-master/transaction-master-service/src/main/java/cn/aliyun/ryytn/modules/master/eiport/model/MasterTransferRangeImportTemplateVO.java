package cn.aliyun.ryytn.modules.master.eiport.model;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 调拨仓辐射导入模板VO
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MasterTransferRangeImportTemplateVO extends ImportTemplateVO {

    /**
     * 商品编码
     */
    @ExcelField(value = "商品编码", importKey = true)
    private String productCode;

    /**
     * 仓库编码
     */
    @ExcelField(value = "仓库编码", importKey = true)
    private String warehouseCode;

    /**
     * 辐射类型名称
     */
    @ExcelField(value = "辐射类型", importKey = true)
    private String radiationTypeName;

    /**
     * 省编码
     */
    @ExcelField(value = "省编码", importKey = true)
    private String provinceCode;

    /**
     * 市编码
     */
    @ExcelField(value = "市编码", importKey = true)
    private String cityCode;

    @ApiModelProperty(value = "销售部门")
    private String salesDepartment;

    @ApiModelProperty(value = "起始公斤重量（不包含）")
    private BigDecimal startKg;

    @ApiModelProperty(value = "结束公斤重量（不包含）")
    private BigDecimal endKg;


    // 以下字段用于内部处理，不在Excel中显示
    /**
     * 仓库ID（内部使用）
     */
    private String warehouseId;

    /**
     * 辐射类型（内部使用）
     */
    private Integer radiationType;

    private String skuId;
}