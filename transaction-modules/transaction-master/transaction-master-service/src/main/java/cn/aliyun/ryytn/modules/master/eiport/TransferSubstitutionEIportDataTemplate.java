package cn.aliyun.ryytn.modules.master.eiport;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.modules.master.api.TransferSubstitutionService;
import cn.aliyun.ryytn.modules.master.dao.MasterSkuDao;
import cn.aliyun.ryytn.modules.master.eiport.enums.TransferSubstitutionError;
import cn.aliyun.ryytn.modules.master.eiport.model.TransferSubstitutionImportTemplateVO;
import cn.aliyun.ryytn.modules.master.eiport.model.export.TransferSubstitutionExportTemplateVO;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionRequest;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 转移替代关系导入导出模板
 * @date 2024/10/10 15:00
 */
@Slf4j
@ImportService(value = "transferSubstitutionEIportDataTemplate",
        filename = "转移替代关系导入模版",
        templateDesc = "填写规范：\n" +
                "1.原商品编码：填写有效的原商品编码\n" +
                "2.替代商品编码：填写有效的替代商品编码",
        async = false)
@ExportService(value = "transferSubstitutionEIportDataTemplate",
        filename = "转移替代关系数据导出",
        async = false)
public class TransferSubstitutionEIportDataTemplate
        extends EIPortDataTemplate<TransferSubstitutionImportTemplateVO, TransferSubstitutionQueryRequest>
        implements ImportPostProcessor<TransferSubstitutionImportTemplateVO> {

    @Autowired
    private TransferSubstitutionService transferSubstitutionService;

    @Autowired
    private MasterSkuDao masterSkuDao;

    @Override
    public List<TransferSubstitutionImportTemplateVO> getImportTemplateData(ImportQueryRequest request) {
        // 返回空模板，不预填充数据
        return super.getImportTemplateData(request);
    }

    @Override
    public boolean preValid(ImportDataWrapper<TransferSubstitutionImportTemplateVO> importDataWrapper) {
        List<TransferSubstitutionImportTemplateVO> importData = importDataWrapper.getData();
        return validate(importData);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<TransferSubstitutionImportTemplateVO> importDataWrapper) {
        return null;
    }

    @Override
    public List<?> getExportTableData(TransferSubstitutionQueryRequest request) {
        // 查询数据
        PageCondition<TransferSubstitutionQueryRequest> pageCondition = new PageCondition<>();
        pageCondition.setPageNum(1);
        pageCondition.setPageSize(Integer.MAX_VALUE);
        pageCondition.setCondition(request);
        List<TransferSubstitutionQueryPageResponse> relationList = transferSubstitutionService.queryPage(pageCondition).getList();

        // 直接使用 GeiCommonConvert 转换，字段名已经匹配
        return GeiCommonConvert.convert(relationList, TransferSubstitutionExportTemplateVO.class);
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(TransferSubstitutionQueryRequest request) {
        return ExcelMetaInfoCreator.create(TransferSubstitutionExportTemplateVO.class);
    }

    /**
     * 校验导入数据
     */
    private boolean validate(List<TransferSubstitutionImportTemplateVO> importDataList) {
        boolean flag = true;

        // 1. 基本字段校验
        for (TransferSubstitutionImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            if (StringUtils.isBlank(template.getOriginalSkuCode())) {
                errorMsg.append(TransferSubstitutionError.ORIGINAL_SKU_CODE_IS_NOT_NULL.getError()).append("；");
            }

            if (StringUtils.isBlank(template.getSubstituteSkuCode())) {
                errorMsg.append(TransferSubstitutionError.SUBSTITUTE_SKU_CODE_IS_NOT_NULL.getError()).append("；");
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 2. 收集所有商品编码进行批量查询
        Set<String> allProductCodes = new HashSet<>();
        for (TransferSubstitutionImportTemplateVO template : importDataList) {
            if (StringUtils.isNotBlank(template.getOriginalSkuCode())) {
                allProductCodes.add(template.getOriginalSkuCode());
            }
            if (StringUtils.isNotBlank(template.getSubstituteSkuCode())) {
                allProductCodes.add(template.getSubstituteSkuCode());
            }
        }

        // 批量查询SKU信息
        Map<String, MasterSku> skuCodeMap = new HashMap<>();
        if (!allProductCodes.isEmpty()) {
            List<MasterSku> skuList = masterSkuDao.queryByProductCodes(new ArrayList<>(allProductCodes));
            if (!CollectionUtils.isEmpty(skuList)) {
                skuCodeMap = skuList.stream().collect(Collectors.toMap(MasterSku::getProductCode, sku -> sku));
            }
        }

        // 3. 校验商品编码是否存在并设置ID
        for (TransferSubstitutionImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            // 校验原商品编码
            MasterSku originalSku = skuCodeMap.get(template.getOriginalSkuCode());
            if (originalSku == null) {
                errorMsg.append(String.format(TransferSubstitutionError.ORIGINAL_SKU_CODE_NOT_EXISTS.getError(), template.getOriginalSkuCode())).append("；");
            } else {
                template.setOriginalSkuId(String.valueOf(originalSku.getId()));
                template.setOriginalSkuName(originalSku.getProductName());
            }

            // 校验替代商品编码
            MasterSku substituteSku = skuCodeMap.get(template.getSubstituteSkuCode());
            if (substituteSku == null) {
                errorMsg.append(String.format(TransferSubstitutionError.SUBSTITUTE_SKU_CODE_NOT_EXISTS.getError(), template.getSubstituteSkuCode())).append("；");
            } else {
                template.setSubstituteSkuId(String.valueOf(substituteSku.getId()));
                template.setSubstituteSkuName(substituteSku.getProductName());
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 4. 校验表格内唯一性
        Map<String, Integer> duplicateMap = new HashMap<>();
        for (int i = 0; i < importDataList.size(); i++) {
            TransferSubstitutionImportTemplateVO template = importDataList.get(i);
            String key = template.getOriginalSkuId() + "_" + template.getSubstituteSkuId();

            if (duplicateMap.containsKey(key)) {
                template.setErrorMsg(TransferSubstitutionError.DUPLICATE_IN_EXCEL.getError() + "；");
                importDataList.get(duplicateMap.get(key)).setErrorMsg(TransferSubstitutionError.DUPLICATE_IN_EXCEL.getError() + "；");
                flag = false;
            } else {
                duplicateMap.put(key, i);
            }
        }
        if (!flag) {
            return false;
        }

        // 保存
        for (TransferSubstitutionImportTemplateVO template : importDataList) {
            TransferSubstitutionRequest request = new TransferSubstitutionRequest();
            request.setOriginalSkuId(template.getOriginalSkuId());
            request.setSubstituteSkuId(template.getSubstituteSkuId());
            try {
                transferSubstitutionService.saveOrUpdate(request);
            } catch (Exception e) {
                template.setErrorMsg("保存失败：" + e.getMessage() + "；");
            }
        }


        return flag;
    }
}
