package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.entity.ResultInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @Description 生产销售关系导入导出Controller
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Slf4j
@RestController
@RequestMapping("/api/master/production-sales-relation/eiport")
@Api(tags = "生产销售关系导入导出")
public class ProductionSalesRelationEIportController {

    @Autowired
    private com.cainiao.cntech.dsct.scp.gei.core.GeiExcelManager geiExcelManager;

    /**
     * 导出生产销售关系模板
     *
     * @param response HTTP响应
     * @param queryMap 查询参数
     */
    @PostMapping("/export-template")
    @ApiOperation(value = "导出生产销售关系导入模板", notes = "下载包含现有数据的导入模板")
    public void exportTemplate(HttpServletResponse response, 
                              @RequestParam Map<String, Object> queryMap) {
        try {
            geiExcelManager.exportTemplate("productionSalesRelationEIportDataTemplate", response, queryMap);
            log.info("导出生产销售关系导入模板成功");
        } catch (Exception e) {
            log.error("导出生产销售关系导入模板失败", e);
            throw new RuntimeException("导出模板失败：" + e.getMessage());
        }
    }

    /**
     * 导入生产销售关系数据
     *
     * @param file Excel文件
     * @param params 附加参数
     * @return 导入结果
     */
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation(value = "导入生产销售关系数据", notes = "上传Excel文件导入生产销售关系数据")
    public ResultInfo<Object> importData(@RequestPart @ApiParam("Excel文件") MultipartFile file,
                                        @RequestParam @ApiParam("附加参数") Map<String, Object> params) {
        try {
            Object result = geiExcelManager.importData("productionSalesRelationEIportDataTemplate", file, params);
            log.info("导入生产销售关系数据成功");
            return ResultInfo.success(result);
        } catch (Exception e) {
            log.error("导入生产销售关系数据失败", e);
            return ResultInfo.fail(null, "导入失败：" + e.getMessage());
        }
    }

    /**
     * 导出生产销售关系数据
     *
     * @param response HTTP响应
     * @param queryMap 查询参数
     */
    @PostMapping("/export-data")
    @ApiOperation(value = "导出生产销售关系数据", notes = "导出符合条件的生产销售关系数据")
    public void exportData(HttpServletResponse response,
                          @RequestParam Map<String, Object> queryMap) {
        try {
            geiExcelManager.exportData("productionSalesRelationEIportDataTemplate", response, queryMap);
            log.info("导出生产销售关系数据成功");
        } catch (Exception e) {
            log.error("导出生产销售关系数据失败", e);
            throw new RuntimeException("导出数据失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否支持异步处理
     *
     * @return 是否支持异步
     */
    @GetMapping("/check-async")
    @ApiOperation(value = "检查是否支持异步处理")
    public ResultInfo<Boolean> checkAsync() {
        try {
            boolean isAsync = geiExcelManager.isAsync("productionSalesRelationEIportDataTemplate");
            return ResultInfo.success(isAsync);
        } catch (Exception e) {
            log.error("检查异步支持失败", e);
            return ResultInfo.fail(false, "检查失败：" + e.getMessage());
        }
    }
}
