package cn.aliyun.ryytn.modules.master.eiport;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.common.entity.sap.MasterWarehouse;
import cn.aliyun.ryytn.modules.master.api.AvailableStockService;
import cn.aliyun.ryytn.modules.master.dao.MasterSkuDao;
import cn.aliyun.ryytn.modules.master.dao.MasterWarehouseDao;
import cn.aliyun.ryytn.modules.master.eiport.enums.AvailableStockError;
import cn.aliyun.ryytn.modules.master.eiport.model.AvailableStockImportTemplateVO;
import cn.aliyun.ryytn.modules.master.eiport.model.export.AvailableStockExportTemplateVO;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockRequest;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 可用库存导入导出模板
 * @date 2024/10/10 15:00
 */
@Slf4j
@ImportService(value = "availableStockEIportDataTemplate",
        filename = "可用库存导入模版",
        templateDesc = "填写规范：\n" +
                "1.工厂名称：填写有效的工厂名称\n" +
                "2.商品编码：填写有效的商品编码\n" +
                "3.仓库编码：填写有效的仓库编码\n" +
                "4.生产日期：格式必须为yyyyMMdd，如：20241010\n" +
                "5.可用库存数量：必须大于0的数字\n" +
                "6.库存快照日期：格式必须为yyyyMMdd，如：20241010",
        async = false)
@ExportService(value = "availableStockEIportDataTemplate",
        filename = "可用库存数据导出",
        async = false)
public class AvailableStockEIportDataTemplate
        extends EIPortDataTemplate<AvailableStockImportTemplateVO, AvailableStockQueryRequest>
        implements ImportPostProcessor<AvailableStockImportTemplateVO> {

    @Autowired
    private AvailableStockService availableStockService;

    @Autowired
    private MasterSkuDao masterSkuDao;

    @Autowired
    private MasterWarehouseDao masterWarehouseDao;


    @Override
    public List<AvailableStockImportTemplateVO> getImportTemplateData(ImportQueryRequest request) {
        // 返回空模板，不预填充数据
        return super.getImportTemplateData(request);
    }

    @Override
    public boolean preValid(ImportDataWrapper<AvailableStockImportTemplateVO> importDataWrapper) {
        List<AvailableStockImportTemplateVO> importData = importDataWrapper.getData();
        return validate(importData);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<AvailableStockImportTemplateVO> importDataWrapper) {
        List<AvailableStockImportTemplateVO> importDataList = importDataWrapper.getData();

        // 批量保存
        List<AvailableStockRequest> requestList = new ArrayList<>();
        for (AvailableStockImportTemplateVO template : importDataList) {
            AvailableStockRequest request = new AvailableStockRequest();
            request.setFactoryName(template.getFactoryName());
            request.setSkuId(template.getSkuId());
            request.setWarehouseId(template.getWarehouseId());
            request.setProductionDate(template.getProductionDate());
            request.setQuantity(template.getQuantity());
            request.setStockDate(template.getStockDate());
            requestList.add(request);
        }

        // 分批处理，每批最多200条
        if (requestList.size() > 200) {
            int batchSize = 200;
            int totalSize = requestList.size();
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<AvailableStockRequest> batch = requestList.subList(i, endIndex);
                for (AvailableStockRequest request : batch) {
                    availableStockService.saveOrUpdate(request);
                }
            }
        } else {
            for (AvailableStockRequest request : requestList) {
                availableStockService.saveOrUpdate(request);
            }
        }

        return null;
    }

    @Override
    public List<?> getExportTableData(AvailableStockQueryRequest request) {
        // 查询数据
        PageCondition<AvailableStockQueryRequest> pageCondition = new PageCondition<>();
        pageCondition.setPageNum(1);
        pageCondition.setPageSize(Integer.MAX_VALUE);
        pageCondition.setCondition(request);
        List<AvailableStockQueryPageResponse> stockList = availableStockService.queryPage(pageCondition).getList();

        // 直接使用 GeiCommonConvert 转换，字段名已经匹配
        return GeiCommonConvert.convert(stockList, AvailableStockExportTemplateVO.class);
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(AvailableStockQueryRequest request) {
        return ExcelMetaInfoCreator.create(AvailableStockExportTemplateVO.class);
    }

    /**
     * 校验导入数据
     */
    private boolean validate(List<AvailableStockImportTemplateVO> importDataList) {
        boolean flag = true;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        dateFormat.setLenient(false); // 严格模式

        // 1. 基本字段校验
        for (AvailableStockImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            if (StringUtils.isBlank(template.getFactoryName())) {
                errorMsg.append(AvailableStockError.FACTORY_NAME_IS_NOT_NULL.getError()).append("；");
            }

            if (StringUtils.isBlank(template.getSkuCode())) {
                errorMsg.append(AvailableStockError.SKU_CODE_IS_NOT_NULL.getError()).append("；");
            }

            if (StringUtils.isBlank(template.getWarehouseCode())) {
                errorMsg.append(AvailableStockError.WAREHOUSE_CODE_IS_NOT_NULL.getError()).append("；");
            }

            if (StringUtils.isBlank(template.getProductionDate())) {
                errorMsg.append(AvailableStockError.PRODUCTION_DATE_IS_NOT_NULL.getError()).append("；");
            } else {
                // 校验生产日期格式
                try {
                    dateFormat.parse(template.getProductionDate());
                } catch (ParseException e) {
                    errorMsg.append(AvailableStockError.PRODUCTION_DATE_FORMAT_ERROR.getError()).append("；");
                }
            }

            if (template.getQuantity() == null) {
                errorMsg.append(AvailableStockError.QUANTITY_IS_NOT_NULL.getError()).append("；");
            } else if (template.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                errorMsg.append(AvailableStockError.QUANTITY_MUST_POSITIVE.getError()).append("；");
            }

            if (StringUtils.isBlank(template.getStockDate())) {
                errorMsg.append(AvailableStockError.STOCK_DATE_IS_NOT_NULL.getError()).append("；");
            } else {
                // 校验库存快照日期格式
                try {
                    dateFormat.parse(template.getStockDate());
                } catch (ParseException e) {
                    errorMsg.append(AvailableStockError.STOCK_DATE_FORMAT_ERROR.getError()).append("；");
                }
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 2. 收集所有商品编码和仓库编码进行批量查询
        Set<String> allProductCodes = new HashSet<>();
        Set<String> allWarehouseCodes = new HashSet<>();
        for (AvailableStockImportTemplateVO template : importDataList) {
            if (StringUtils.isNotBlank(template.getSkuCode())) {
                allProductCodes.add(template.getSkuCode());
            }
            if (StringUtils.isNotBlank(template.getWarehouseCode())) {
                allWarehouseCodes.add(template.getWarehouseCode());
            }
        }

        // 批量查询SKU信息
        Map<String, MasterSku> skuCodeMap = new HashMap<>();
        if (!allProductCodes.isEmpty()) {
            List<MasterSku> skuList = masterSkuDao.queryByProductCodes(new ArrayList<>(allProductCodes));
            if (!CollectionUtils.isEmpty(skuList)) {
                skuCodeMap = skuList.stream().collect(Collectors.toMap(MasterSku::getProductCode, sku -> sku));
            }
        }

        // 批量查询仓库信息
        Map<String, MasterWarehouse> warehouseCodeMap = new HashMap<>();
        if (!allWarehouseCodes.isEmpty()) {
            List<MasterWarehouse> warehouseList = masterWarehouseDao.selectByWarehouseCodes(new ArrayList<>(allWarehouseCodes));
            if (!CollectionUtils.isEmpty(warehouseList)) {
                warehouseCodeMap = warehouseList.stream().collect(Collectors.toMap(MasterWarehouse::getWarehouseCode, warehouse -> warehouse));
            }
        }

        // 3. 校验商品编码和仓库编码是否存在并设置ID
        for (AvailableStockImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            // 校验商品编码
            MasterSku sku = skuCodeMap.get(template.getSkuCode());
            if (sku == null) {
                errorMsg.append(String.format(AvailableStockError.SKU_CODE_NOT_EXISTS.getError(), template.getSkuCode())).append("；");
            } else {
                template.setSkuId(String.valueOf(sku.getId()));
                template.setSkuName(sku.getProductName());
            }

            // 校验仓库编码
            MasterWarehouse warehouse = warehouseCodeMap.get(template.getWarehouseCode());
            if (warehouse == null) {
                errorMsg.append(String.format(AvailableStockError.WAREHOUSE_CODE_NOT_EXISTS.getError(), template.getWarehouseCode())).append("；");
            } else {
                template.setWarehouseId(warehouse.getId());
                template.setWarehouseName(warehouse.getWarehouseName());
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 4. 校验表格内唯一性
        Map<String, Integer> duplicateMap = new HashMap<>();
        for (int i = 0; i < importDataList.size(); i++) {
            AvailableStockImportTemplateVO template = importDataList.get(i);
            String key = template.getFactoryName() + "_" + template.getSkuCode() + "_" +
                    template.getWarehouseCode() + "_" + template.getProductionDate();

            if (duplicateMap.containsKey(key)) {
                template.setErrorMsg(AvailableStockError.DUPLICATE_IN_EXCEL.getError() + "；");
                importDataList.get(duplicateMap.get(key)).setErrorMsg(AvailableStockError.DUPLICATE_IN_EXCEL.getError() + "；");
                flag = false;
            } else {
                duplicateMap.put(key, i);
            }
        }

        return flag;
    }
}
