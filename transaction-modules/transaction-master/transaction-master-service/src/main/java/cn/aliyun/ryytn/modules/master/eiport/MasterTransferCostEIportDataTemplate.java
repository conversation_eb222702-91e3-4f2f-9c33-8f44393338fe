package cn.aliyun.ryytn.modules.master.eiport;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.master.api.MasterRegionService;
import cn.aliyun.ryytn.modules.master.api.MasterTransferCostService;
import cn.aliyun.ryytn.modules.master.dao.MasterRegionDao;
import cn.aliyun.ryytn.modules.master.eiport.model.MasterTransferCostImportTemplateVO;
import cn.aliyun.ryytn.modules.master.eiport.model.export.MasterTransferCostExportTemplateVO;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageResponse;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 运输成本导入导出模板
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@ImportService(value = "masterTransferCostEIportDataTemplate",
        filename = "运输成本导入模版",
        templateDesc = "填写规范：\n" +
                "1.出发省编码：必填，必须是有效的省级区域编码\n" +
                "2.出发市编码：必填，必须是有效的市级区域编码，且属于出发省\n" +
                "3.到达省编码：必填，必须是有效的省级区域编码\n" +
                "4.到达市编码：必填，必须是有效的市级区域编码，且属于到达省\n" +
                "5.十三米（元/吨）：必填\n" +
                "6.十五米（元/吨）：必填\n" +
                "7.铁柜（元/吨）：必填\n" +
                "8.汽运时效（天）：必填\n" +
                "9.铁运时效（天）：必填",
        async = false)
@ExportService(value = "masterTransferCostEIportDataTemplate",
        filename = "运输成本数据导出",
        async = false)
public class MasterTransferCostEIportDataTemplate
        extends EIPortDataTemplate<MasterTransferCostImportTemplateVO, MasterTransferCostPageRequest>
        implements ImportPostProcessor<MasterTransferCostImportTemplateVO> {

    @Autowired
    private MasterTransferCostService masterTransferCostService;

    @Autowired
    private MasterRegionService masterRegionService;

    @Autowired
    private MasterRegionDao masterRegionDao;

    @Override
    public boolean preValid(ImportDataWrapper<MasterTransferCostImportTemplateVO> importDataWrapper) {
        List<MasterTransferCostImportTemplateVO> importData = importDataWrapper.getData();
        return validate(importData);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<MasterTransferCostImportTemplateVO> importDataWrapper) {
//        List<MasterTransferCostImportTemplateVO> importDataList = importDataWrapper.getData();
//
//        String loginId = "c012087";
//        // String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
//
//        // 批量保存
//        for (MasterTransferCostImportTemplateVO template : importDataList) {
//            MasterTransferCostCreateRequest request = new MasterTransferCostCreateRequest();
//            request.setFromProvinceId(template.getFromProvinceId());
//            request.setFromCityId(template.getFromCityId());
//            request.setToProvinceId(template.getToProvinceId());
//            request.setToCityId(template.getToCityId());
//            request.setCost13m(template.getCost13m());
//            request.setCost15m(template.getCost15m());
//            request.setCostRail(template.getCostRail());
//            request.setRoadDuration(template.getRoadDuration());
//            request.setRailDuration(template.getRailDuration());
//
//            try {
//                masterTransferCostService.create(request, loginId);
//            } catch (Exception e) {
//                log.warn("导入出错，{}", Throwables.getStackTraceAsString(e));
//                template.setErrorMsg("导入出错：" + e.getMessage());
//            }
//        }
        return null;
    }

    @Override
    public List<?> getExportTableData(MasterTransferCostPageRequest request) {
        // 查询数据
        PageCondition<MasterTransferCostPageRequest> pageCondition = new PageCondition<>();
        pageCondition.setPageNum(1);
        pageCondition.setPageSize(Integer.MAX_VALUE);
        pageCondition.setCondition(request);
        PageInfo<MasterTransferCostPageResponse> pageInfo = masterTransferCostService.queryByPage(pageCondition);
        List<MasterTransferCostPageResponse> costList = pageInfo.getList();

        // 直接使用 GeiCommonConvert 转换，字段名已经匹配
        return GeiCommonConvert.convert(costList, MasterTransferCostExportTemplateVO.class);
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(MasterTransferCostPageRequest request) {
        return ExcelMetaInfoCreator.create(MasterTransferCostExportTemplateVO.class);
    }

    /**
     * 校验导入数据
     */
    private boolean validate(List<MasterTransferCostImportTemplateVO> importDataList) {
        boolean flag = true;

        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();

        // 1. 基本字段校验
        for (MasterTransferCostImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            if (StringUtils.isBlank(template.getFromProvinceCode())) {
                errorMsg.append("出发省编码不能为空").append("；");
            }

            if (StringUtils.isBlank(template.getFromCityCode())) {
                errorMsg.append("出发市编码不能为空").append("；");
            }

            if (StringUtils.isBlank(template.getToProvinceCode())) {
                errorMsg.append("到达省编码不能为空").append("；");
            }

            if (StringUtils.isBlank(template.getToCityCode())) {
                errorMsg.append("到达市编码不能为空").append("；");
            }


            /*保存数据*/
            saveData(template, loginId);

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }
        return flag;
    }

    private void saveData(MasterTransferCostImportTemplateVO template, String loginId) {
        MasterTransferCostCreateRequest request = new MasterTransferCostCreateRequest();
        request.setFromProvinceId(template.getFromProvinceId());
        request.setFromCityId(template.getFromCityId());
        request.setToProvinceId(template.getToProvinceId());
        request.setToCityId(template.getToCityId());
        request.setCost13m(template.getCost13m());
        request.setCost15m(template.getCost15m());
        request.setCostRail(template.getCostRail());
        request.setRoadDuration(template.getRoadDuration());
        request.setRailDuration(template.getRailDuration());

        try {
            masterTransferCostService.create(request, loginId);
        } catch (Exception e) {
            log.warn("导入出错，{}", Throwables.getStackTraceAsString(e));
            template.setErrorMsg("导入出错：" + e.getMessage());
        }
    }
}