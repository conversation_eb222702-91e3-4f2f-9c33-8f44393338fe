package cn.aliyun.ryytn.modules.master.eiport.model;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 调拨路径导入模板VO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MasterTransferPathImportTemplateVO extends ImportTemplateVO {

    /**
     * 发货仓编码
     */
    @ExcelField(value = "发货仓编码", importKey = true)
    private String sendWarehouseCode;

    /**
     * 收货仓编码
     */
    @ExcelField(value = "收货仓编码", importKey = true)
    private String receiveWarehouseCode;

    /**
     * 生效时间
     */
    @ExcelField("生效时间")
    private Date activeTime;

    /**
     * 结束时间
     */
    @ExcelField("结束时间")
    private Date disableTime;

    // 以下字段用于内部处理，不在Excel中显示
    /**
     * 发货仓ID（内部使用）
     */
    private String sendWarehouseId;

    /**
     * 收货仓ID（内部使用）
     */
    private String receiveWarehouseId;
}