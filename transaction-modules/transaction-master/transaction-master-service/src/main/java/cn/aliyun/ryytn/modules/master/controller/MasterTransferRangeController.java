package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.master.api.MasterTransferRangeService;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangeCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangePageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangeUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferRangeUpdateStatusRequest;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 调拨仓辐射
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@RestController
@RequestMapping("/api/master/masterTransferRange")
@Api(tags = "调拨仓辐射")
public class MasterTransferRangeController {
    @Resource
    private MasterTransferRangeService masterTransferRangeService;

    @PostMapping("/page")
    @ResponseBody
    @ApiOperation("分页查询调拨仓辐射列表")
//    @RequiresPermissions(value = {""})
    public ResultInfo<PageInfo<MasterTransferRangePageResponse>> queryByPage(
            @RequestBody PageCondition<MasterTransferRangePageRequest> condition) {
        if (Objects.isNull(condition)) {
            condition = new PageCondition<>();
        }

        PageInfo<MasterTransferRangePageResponse> page = masterTransferRangeService.queryByPage(condition);
        return ResultInfo.success(page);
    }

    @PostMapping("/create")
    @ResponseBody
    @ApiOperation("保存调拨仓辐射")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> create(@RequestBody @Validated MasterTransferRangeCreateRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterTransferRangeService.create(request, loginId);
        return ResultInfo.success();
    }

    @PostMapping("/update")
    @ResponseBody
    @ApiOperation("修改调拨仓辐射")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> update(@RequestBody @Validated MasterTransferRangeUpdateRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterTransferRangeService.update(request, loginId);
        return ResultInfo.success();
    }

    @PostMapping("/status")
    @ResponseBody
    @ApiOperation("禁用/启用")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> updateStatus(@RequestBody @Validated MasterTransferRangeUpdateStatusRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterTransferRangeService.updateStatus(request, loginId);
        return ResultInfo.success();
    }
}
