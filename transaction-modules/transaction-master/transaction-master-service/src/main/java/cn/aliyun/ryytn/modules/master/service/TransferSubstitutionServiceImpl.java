package cn.aliyun.ryytn.modules.master.service;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.info.TransferSubstitution;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.master.api.TransferSubstitutionService;
import cn.aliyun.ryytn.modules.master.dao.MasterSkuDao;
import cn.aliyun.ryytn.modules.master.dao.TransferSubstitutionDao;
import cn.aliyun.ryytn.modules.master.entity.enums.EntityStatusEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionInvalidateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionQueryRequest;

import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionRequest;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 转移替代关系Service实现类
 * @date 2024/10/10 15:00
 */
@Slf4j
@Service
public class TransferSubstitutionServiceImpl implements TransferSubstitutionService {

    @Autowired
    private TransferSubstitutionDao transferSubstitutionDao;

    @Autowired
    private MasterSkuDao masterSkuDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(TransferSubstitutionRequest request) {
        // 参数校验
        validateRequest(request);

        // 获取当前用户
        String currentUser = getCurrentUser();
        Date now = new Date();

        // 检查唯一性
        int existCount = transferSubstitutionDao.checkUniqueness(
                request.getOriginalSkuId(),
                request.getSubstituteSkuId(),
                request.getId()
        );
        if (existCount > 0) {
            throw new ServiceException("该原商品和替代商品的关系已存在");
        }

        TransferSubstitution relation = new TransferSubstitution();
        relation.setOriginalSkuId(request.getOriginalSkuId());
        relation.setSubstituteSkuId(request.getSubstituteSkuId());
        relation.setUpdatedBy(currentUser);
        relation.setUpdatedTime(now);

        if (request.getId() == null || request.getId().trim().isEmpty()) {
            // 新增
            relation.setId(String.valueOf(SeqUtils.getSequenceUid()));
            relation.setCreatedBy(currentUser);
            relation.setCreatedTime(now);
            relation.setStatus(EntityStatusEnum.IS_ACTIVE.getCode());

            int result = transferSubstitutionDao.insert(relation);
            if (result <= 0) {
                throw new ServiceException("新增转移替代关系失败");
            }
            log.info("新增转移替代关系成功，ID: {}", relation.getId());
        } else {
            // 修改
            relation.setId(request.getId());

            // 检查记录是否存在
            TransferSubstitution existRelation = transferSubstitutionDao.selectById(request.getId());
            if (existRelation == null) {
                throw new ServiceException("要修改的记录不存在");
            }
            if (!EntityStatusEnum.IS_ACTIVE.getCode().equals(existRelation.getStatus())) {
                throw new ServiceException("只能修改有效状态的记录");
            }

            int result = transferSubstitutionDao.update(relation);
            if (result <= 0) {
                throw new ServiceException("修改转移替代关系失败");
            }
            log.info("修改转移替代关系成功，ID: {}", relation.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInvalidate(TransferSubstitutionInvalidateRequest request) {
        // 参数校验
        if (request == null) {
            throw new ServiceException("请求参数不能为空");
        }

        if (CollectionUtils.isEmpty(request.getIds())) {
            throw new ServiceException("关系ID列表不能为空");
        }

        // 检查是否有已经作废的记录
        int invalidatedCount = transferSubstitutionDao.countInvalidated(request.getIds());
        if (invalidatedCount > 0) {
            throw new ServiceException("存在已经作废的记录，不能重复作废");
        }

        // 获取当前用户
        String currentUser = getCurrentUser();

        // 批量作废
        int result = transferSubstitutionDao.batchInvalidate(request.getIds(), currentUser);
        log.info("批量作废转移替代关系成功，作废记录数: {}", result);
    }

    @Override
    public PageInfo<TransferSubstitutionQueryPageResponse> queryPage(PageCondition<TransferSubstitutionQueryRequest> pageCondition) {
        // 设置分页参数
        PageHelper.startPage(pageCondition.getPageNum(), pageCondition.getPageSize());

        // 查询关系数据
        List<TransferSubstitution> relationList = transferSubstitutionDao.queryPage(pageCondition.getCondition());

        // 转换为VO
        List<TransferSubstitutionQueryPageResponse> voList = convertToVOList(relationList);

        return new PageInfo<>(voList);
    }

    /**
     * 转换为VO列表
     */
    private List<TransferSubstitutionQueryPageResponse> convertToVOList(List<TransferSubstitution> relationList) {
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }

        // 收集所有需要查询的SKU ID
        Set<String> skuIds = new HashSet<>();
        for (TransferSubstitution relation : relationList) {
            if (relation.getOriginalSkuId() != null && !relation.getOriginalSkuId().trim().isEmpty()) {
                skuIds.add(relation.getOriginalSkuId());
            }
            if (relation.getSubstituteSkuId() != null && !relation.getSubstituteSkuId().trim().isEmpty()) {
                skuIds.add(relation.getSubstituteSkuId());
            }
        }

        // 批量查询SKU信息
        Map<String, MasterSku> skuMap = new HashMap<>();
        if (!skuIds.isEmpty()) {
            List<String> idList = new ArrayList<>(skuIds);
            List<MasterSku> skuList = masterSkuDao.queryByIds(idList);
            if (!CollectionUtils.isEmpty(skuList)) {
                skuMap = skuList.stream().collect(Collectors.toMap(sku -> String.valueOf(sku.getId()), sku -> sku));
            }
        }

        // 转换为VO
        List<TransferSubstitutionQueryPageResponse> voList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (TransferSubstitution relation : relationList) {
            TransferSubstitutionQueryPageResponse vo = new TransferSubstitutionQueryPageResponse();
            BeanUtils.copyProperties(relation, vo);

            // 设置原商品SKU信息
            if (relation.getOriginalSkuId() != null && !relation.getOriginalSkuId().trim().isEmpty()) {
                MasterSku originalSku = skuMap.get(relation.getOriginalSkuId());
                if (originalSku != null) {
                    vo.setOriginalSkuCode(originalSku.getProductCode());
                    vo.setOriginalSkuName(originalSku.getProductName());
                }
            }

            // 设置替代商品SKU信息
            if (relation.getSubstituteSkuId() != null && !relation.getSubstituteSkuId().trim().isEmpty()) {
                MasterSku substituteSku = skuMap.get(relation.getSubstituteSkuId());
                if (substituteSku != null) {
                    vo.setSubstituteSkuCode(substituteSku.getProductCode());
                    vo.setSubstituteSkuName(substituteSku.getProductName());
                }
            }

            // 设置时间字符串格式
            if (relation.getCreatedTime() != null) {
                vo.setCreatedTimeStr(dateFormat.format(relation.getCreatedTime()));
            }
            if (relation.getUpdatedTime() != null) {
                vo.setUpdatedTimeStr(dateFormat.format(relation.getUpdatedTime()));
            }

            // 设置状态描述
            vo.setStatusDesc(EntityStatusEnum.getNameByCode(vo.getStatus()));

            voList.add(vo);
        }

        return voList;
    }

    /**
     * 参数校验
     */
    private void validateRequest(TransferSubstitutionRequest request) {
        if (request == null) {
            throw new ServiceException("请求参数不能为空");
        }

        if (!StringUtils.hasText(request.getOriginalSkuId())) {
            throw new ServiceException("原商品ID不能为空");
        }

        if (!StringUtils.hasText(request.getSubstituteSkuId())) {
            throw new ServiceException("替代商品ID不能为空");
        }
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        try {
            return ServiceContextUtils.currentSession().getAccount().getLoginId();
        } catch (Exception e) {
            log.warn("获取当前用户失败，使用默认用户", e);
            return "SYSTEM";
        }
    }
}
