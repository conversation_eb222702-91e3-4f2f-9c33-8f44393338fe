package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.master.api.AvailableStockService;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockInvalidateRequest;
import cn.aliyun.ryytn.modules.master.entity.dto.AvailableStockQueryDto;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockVO;
import cn.aliyun.ryytn.modules.master.entity.vo.AvailableStockRequest;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * <AUTHOR>
 * @Description 可用库存Controller
 * @date 2024/10/10 15:00
 */
@Slf4j
@RestController
@RequestMapping("/api/master/availableStock")
@Api(tags = "可用库存管理")
public class AvailableStockController {

    @Autowired
    private AvailableStockService availableStockService;

    /**
     * 新增或修改可用库存
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "新增或修改可用库存", notes = "新增时ID为空，修改时ID必填")
    @RequiresPermissions(value = {""})
    public ResultInfo<?> saveOrUpdate(@RequestBody AvailableStockRequest request) {
        try {
            availableStockService.saveOrUpdate(request);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("新增或修改可用库存失败", e);
            return ResultInfo.fail(null, "操作失败：" + e.getMessage());
        }
    }

    /**
     * 批量作废可用库存
     *
     * @param request 作废请求
     * @return 操作结果
     */
    @PostMapping("/batchInvalidate")
    @ApiOperation(value = "批量作废可用库存", notes = "将状态改为无效，已作废的记录不能重复作废")
    @RequiresPermissions(value = {""})
    public ResultInfo<?> batchInvalidate(@RequestBody AvailableStockInvalidateRequest request) {
        try {
            availableStockService.batchInvalidate(request);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("批量作废可用库存失败", e);
            return ResultInfo.fail(null, "作废失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询可用库存
     *
     * @param pageCondition 分页查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询可用库存", notes = "支持按工厂名称、商品ID、仓库ID、生产日期等条件查询")
    @RequiresPermissions(value = {""})
    public ResultInfo<PageInfo<AvailableStockQueryPageResponse>> page(@RequestBody PageCondition<AvailableStockQueryRequest> pageCondition) {
        try {
            // 设置默认分页参数
            if (pageCondition.getPageNum() == null) {
                pageCondition.setPageNum(1);
            }
            if (pageCondition.getPageSize() == null) {
                pageCondition.setPageSize(100);
            }
            if (pageCondition.getCondition() == null) {
                pageCondition.setCondition(new AvailableStockQueryRequest());
            }

            PageInfo<AvailableStockQueryPageResponse> pageInfo = availableStockService.queryPage(pageCondition);
            return ResultInfo.success(pageInfo);
        } catch (Exception e) {
            log.error("查询可用库存失败", e);
            return (ResultInfo<PageInfo<AvailableStockQueryPageResponse>>) ResultInfo.fail(null, "查询失败：" + e.getMessage());
        }
    }
}
