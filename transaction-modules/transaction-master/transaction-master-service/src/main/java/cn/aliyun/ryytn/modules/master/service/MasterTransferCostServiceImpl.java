package cn.aliyun.ryytn.modules.master.service;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.info.MasterRegion;
import cn.aliyun.ryytn.common.entity.info.MasterTransferCost;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.master.api.MasterRegionService;
import cn.aliyun.ryytn.modules.master.api.MasterTransferCostService;
import cn.aliyun.ryytn.modules.master.dao.MasterTransferCostDao;
import cn.aliyun.ryytn.modules.master.entity.enums.EntityStatusEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostUpdateStatusRequest;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 运输成本服务实现
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@Service
public class MasterTransferCostServiceImpl implements MasterTransferCostService {
    @Resource
    private MasterTransferCostDao masterTransferCostDao;

    @Resource
    private MasterRegionService masterRegionService;

    @Override
    public PageInfo<MasterTransferCostPageResponse> queryByPage(PageCondition<MasterTransferCostPageRequest> condition) {
        // 设置默认分页参数
        if (condition.getPageNum() == null) {
            condition.setPageNum(1);
        }
        if (condition.getPageSize() == null) {
            condition.setPageSize(10);
        }

        // 开始分页查询
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        List<MasterTransferCostPageResponse> list = masterTransferCostDao.queryByPage(condition.getCondition());

        // 处理状态名称
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                item.setStatusName(EntityStatusEnum.getNameByCode(item.getStatus()));
            });
        }

        return new PageInfo<>(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(MasterTransferCostCreateRequest request, String loginId) {
        // 转换为实体
        MasterTransferCost entity = new MasterTransferCost();
        entity.setFromProvinceId(request.getFromProvinceId());
        entity.setFromCityId(request.getFromCityId());
        entity.setToProvinceId(request.getToProvinceId());
        entity.setToCityId(request.getToCityId());
        entity.setCost13m(request.getCost13m());
        entity.setCost15m(request.getCost15m());
        entity.setCostRail(request.getCostRail());
        entity.setRoadDuration(request.getRoadDuration());
        entity.setRailDuration(request.getRailDuration());

        entity.setCreatedTime(new Date());
        entity.setUpdatedTime(new Date());
        entity.setStatus(EntityStatusEnum.IS_ACTIVE.getCode());
        entity.setCreatedBy(loginId);
        entity.setUpdatedBy(loginId);

        // 检查唯一性和区域存在性
        checkUniqueAndRegion(entity);

        entity.setId(SeqUtils.getSequenceUid());
        // 插入记录
        int result = masterTransferCostDao.insert(entity);
        if (result <= 0) {
            throw new ServiceException("创建运输成本失败");
        }

        log.info("运输成本创建成功，ID: {}, 操作人: {}", entity.getId(), loginId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MasterTransferCostUpdateRequest request, String loginId) {
        // 查询原记录是否存在
        MasterTransferCost existingCost = masterTransferCostDao.selectById(request.getId());
        if (existingCost == null) {
            throw new ServiceException("运输成本不存在");
        }
        if (EntityStatusEnum.NO_ACTIVE.getCode().equals(existingCost.getStatus())) {
            throw new ServiceException("运输成本已失效，不能修改");
        }

        // 转换为实体
        MasterTransferCost entity = new MasterTransferCost();
        entity.setId(request.getId());
        entity.setFromProvinceId(request.getFromProvinceId());
        entity.setFromCityId(request.getFromCityId());
        entity.setToProvinceId(request.getToProvinceId());
        entity.setToCityId(request.getToCityId());
        entity.setCost13m(request.getCost13m());
        entity.setCost15m(request.getCost15m());
        entity.setCostRail(request.getCostRail());

        entity.setRoadDuration(request.getRoadDuration());
        entity.setRailDuration(request.getRailDuration());
        entity.setUpdatedTime(new Date());
        entity.setUpdatedBy(loginId);

        // 检查唯一性和区域存在性
        checkUniqueAndRegion(entity);

        // 更新记录
        int result = masterTransferCostDao.update(entity);
        if (result <= 0) {
            throw new ServiceException("修改运输成本失败");
        }

        log.info("运输成本修改成功，ID: {}, 操作人: {}", entity.getId(), loginId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(MasterTransferCostUpdateStatusRequest request, String loginId) {
        // 查询原记录是否存在
        MasterTransferCost existingCost = masterTransferCostDao.selectById(request.getId());
        if (existingCost == null) {
            throw new ServiceException("运输成本不存在");
        }

        // 获取当前状态
        Integer currentStatus = existingCost.getStatus();
        // 计算新状态（切换状态：1->0 或 0->1）
        Integer newStatus = EntityStatusEnum.IS_ACTIVE.getCode().equals(currentStatus) ?
                EntityStatusEnum.NO_ACTIVE.getCode() : EntityStatusEnum.IS_ACTIVE.getCode();

        MasterTransferCost update = new MasterTransferCost();
        update.setId(existingCost.getId());
        update.setStatus(newStatus);
        update.setUpdatedTime(new Date());
        update.setUpdatedBy(loginId);
        // 更新状态
        int result = masterTransferCostDao.updateStatus(update);
        if (result <= 0) {
            throw new ServiceException("修改运输成本状态失败");
        }

        log.info("运输成本状态已更新，ID: {}, 旧状态: {}, 新状态: {}, 操作人: {}",
                request.getId(), currentStatus, newStatus, loginId);
    }

    /**
     * 检查唯一性和区域存在性
     *
     * @param record 运输成本记录
     */
    private void checkUniqueAndRegion(MasterTransferCost record) {
        // 1. 检查区域是否存在
        List<String> regionIds = new ArrayList<>();
        regionIds.add(record.getFromProvinceId());
        regionIds.add(record.getFromCityId());
        regionIds.add(record.getToProvinceId());
        regionIds.add(record.getToCityId());
        List<MasterRegion> regions = masterRegionService.queryByIds(regionIds);
        Map<String, MasterRegion> regionMap = regions.stream()
                .collect(Collectors.toMap(MasterRegion::getId, Function.identity(), (k1, k2) -> k1));
        // 检查出发省
        if (!regionMap.containsKey(record.getFromProvinceId())) {
            throw new ServiceException("出发省不存在");
        }
        // 检查出发市
        if (!regionMap.containsKey(record.getFromCityId())) {
            throw new ServiceException("出发市不存在");
        }
        // 检查到达省
        if (!regionMap.containsKey(record.getToProvinceId())) {
            throw new ServiceException("到达省不存在");
        }
        // 检查到达市
        if (!regionMap.containsKey(record.getToCityId())) {
            throw new ServiceException("到达市不存在");
        }

        // 2. 检查唯一性
        int count = masterTransferCostDao.checkUnique(record);
        if (count > 0) {
            throw new ServiceException("相同的运输路线已存在");
        }
    }
}