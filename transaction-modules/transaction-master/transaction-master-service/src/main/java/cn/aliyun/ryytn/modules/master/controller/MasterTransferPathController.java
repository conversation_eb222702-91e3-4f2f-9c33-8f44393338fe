package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.master.api.MasterTransferPathService;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferPathCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferPathUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferPathUpdateStatusRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterWarehousePathPageResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 调拨路径
 *
 * <AUTHOR>
 * @since 2025-05-27 13:43
 */
@Slf4j
@RestController
@RequestMapping("/api/master/masterTransferPath")
@Api(tags = "调拨路径")
public class MasterTransferPathController {
    @Resource
    private MasterTransferPathService masterTransferPathService;

    @PostMapping("/page")
    @ResponseBody
    @ApiOperation("分页查询仓库列表")
//    @RequiresPermissions(value = {""})
    public ResultInfo<PageInfo<MasterWarehousePathPageResponse>> queryByPage(
            @RequestBody PageCondition<MasterWarehousePathPageRequest> condition) {
        if (Objects.isNull(condition)) {
            condition = new PageCondition<>();
        }

        PageInfo<MasterWarehousePathPageResponse> page = masterTransferPathService.queryByPage(condition);
        return ResultInfo.success(page);
    }

    @PostMapping("/create")
    @ResponseBody
    @ApiOperation("保存调拨路径")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> create(@RequestBody @Validated MasterTransferPathCreateRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterTransferPathService.create(request, loginId);
        return ResultInfo.success();
    }

    @PostMapping("/update")
    @ResponseBody
    @ApiOperation("修改调拨路径")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> update(@RequestBody @Validated MasterTransferPathUpdateRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterTransferPathService.update(request, loginId);
        return ResultInfo.success();
    }


    @PostMapping("/status")
    @ResponseBody
    @ApiOperation("禁用/启用")
//    @RequiresPermissions(value = {""})
    public ResultInfo<?> updateStatus(@RequestBody @Validated MasterTransferPathUpdateStatusRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterTransferPathService.updateStatus(request, loginId);
        return ResultInfo.success();
    }
}
