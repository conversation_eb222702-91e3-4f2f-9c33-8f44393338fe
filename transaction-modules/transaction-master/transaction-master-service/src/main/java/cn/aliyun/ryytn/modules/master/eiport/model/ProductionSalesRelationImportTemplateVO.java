package cn.aliyun.ryytn.modules.master.eiport.model;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description 生产销售关系导入模板VO
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductionSalesRelationImportTemplateVO extends ImportTemplateVO {

    /**
     * 销售商品编码
     */
    @ExcelField(value = "销售商品编码", importKey = true)
    private String salesSkuCode;

    /**
     * 销售商品名称
     */
    @ExcelField("销售商品名称")
    private String salesSkuName;

    /**
     * 生产商品编码
     */
    @ExcelField(value = "生产商品编码", importKey = true)
    private String productionSkuCode;

    /**
     * 生产商品名称
     */
    @ExcelField("生产商品名称")
    private String productionSkuName;

    /**
     * 转换系数
     */
    @ExcelField("转换系数")
    private Integer conversionFactor;

    // 以下字段用于内部处理，不在Excel中显示
    /**
     * 销售商品ID（内部使用）
     */
    private String salesSkuId;

    /**
     * 生产商品ID（内部使用）
     */
    private String productionSkuId;
}
