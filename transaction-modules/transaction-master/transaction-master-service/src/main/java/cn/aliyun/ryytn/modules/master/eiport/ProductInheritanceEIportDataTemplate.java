package cn.aliyun.ryytn.modules.master.eiport;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.modules.master.api.ProductInheritanceService;
import cn.aliyun.ryytn.modules.master.dao.MasterSkuDao;
import cn.aliyun.ryytn.modules.master.eiport.enums.ProductInheritanceError;
import cn.aliyun.ryytn.modules.master.eiport.model.ProductInheritanceImportTemplateVO;
import cn.aliyun.ryytn.modules.master.eiport.model.export.ProductInheritanceExportTemplateVO;
import cn.aliyun.ryytn.modules.master.entity.enums.ChannelOwnershipEnum;
import cn.aliyun.ryytn.modules.master.entity.enums.ProductRelationTypeEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductInheritanceRequest;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 商品继承关系导入导出模板
 * @date 2024/10/10 15:00
 */
@Slf4j
@ImportService(value = "productInheritanceEIportDataTemplate",
        filename = "商品继承关系导入模版",
        templateDesc = "填写规范：\n" +
                "1.原商品编码：填写有效的原商品编码\n" +
                "2.新品商品编码：填写有效的新品商品编码\n" +
                "3.关系类型：选择新老品牌、店铺定制、渠道定制\n" +
                "4.定制店铺：当关系类型为店铺定制时必填\n" +
                "5.定制渠道：当关系类型为渠道定制时必填\n" +
                "6.渠道归属：选择TOC、TOB、TOC&TOB",
        async = false)
@ExportService(value = "productInheritanceEIportDataTemplate",
        filename = "商品继承关系数据导出",
        async = false)
public class ProductInheritanceEIportDataTemplate
        extends EIPortDataTemplate<ProductInheritanceImportTemplateVO, ProductInheritanceQueryRequest>
        implements ImportPostProcessor<ProductInheritanceImportTemplateVO> {

    @Autowired
    private ProductInheritanceService productInheritanceService;

    @Autowired
    private MasterSkuDao masterSkuDao;

    @Override
    public List<ProductInheritanceImportTemplateVO> getImportTemplateData(ImportQueryRequest request) {
        // 返回空模板，不预填充数据
        return super.getImportTemplateData(request);
    }

    @Override
    public boolean preValid(ImportDataWrapper<ProductInheritanceImportTemplateVO> importDataWrapper) {
        List<ProductInheritanceImportTemplateVO> importData = importDataWrapper.getData();
        return validate(importData);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<ProductInheritanceImportTemplateVO> importDataWrapper) {
        return null;
    }

    @Override
    public List<?> getExportTableData(ProductInheritanceQueryRequest request) {
        // 查询数据
        PageCondition<ProductInheritanceQueryRequest> pageCondition = new PageCondition<>();
        pageCondition.setPageNum(1);
        pageCondition.setPageSize(Integer.MAX_VALUE);
        pageCondition.setCondition(request);
        List<ProductInheritanceQueryPageResponse> inheritanceList = productInheritanceService.queryPage(pageCondition).getList();

        // 直接使用 GeiCommonConvert 转换，字段名已经匹配
        return GeiCommonConvert.convert(inheritanceList, ProductInheritanceExportTemplateVO.class);
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(ProductInheritanceQueryRequest request) {
        return ExcelMetaInfoCreator.create(ProductInheritanceExportTemplateVO.class);
    }

    /**
     * 校验导入数据
     */
    private boolean validate(List<ProductInheritanceImportTemplateVO> importDataList) {
        boolean flag = true;

        // 1. 基本字段校验
        for (ProductInheritanceImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            if (StringUtils.isBlank(template.getOriginalSkuCode())) {
                errorMsg.append(ProductInheritanceError.ORIGINAL_SKU_CODE_IS_NOT_NULL.getError()).append("；");
            }

            if (StringUtils.isBlank(template.getNewSkuCode())) {
                errorMsg.append(ProductInheritanceError.NEW_SKU_CODE_IS_NOT_NULL.getError()).append("；");
            }

            if (StringUtils.isBlank(template.getRelationTypeDesc())) {
                errorMsg.append(ProductInheritanceError.RELATION_TYPE_IS_NOT_NULL.getError()).append("；");
            }

            if (StringUtils.isBlank(template.getChannelOwnershipDesc())) {
                errorMsg.append(ProductInheritanceError.CHANNEL_OWNERSHIP_IS_NOT_NULL.getError()).append("；");
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 2. 枚举值校验和转换
        for (ProductInheritanceImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            // 校验关系类型
            ProductRelationTypeEnum relationTypeEnum = getRelationTypeByDesc(template.getRelationTypeDesc());
            if (relationTypeEnum == null) {
                errorMsg.append(String.format(ProductInheritanceError.RELATION_TYPE_NOT_EXISTS.getError(), template.getRelationTypeDesc())).append("；");
            } else {
                template.setRelationType(relationTypeEnum.getCode());

                // 根据关系类型校验定制店铺和定制渠道
                if (relationTypeEnum == ProductRelationTypeEnum.STORE_CUSTOM) {
                    if (StringUtils.isBlank(template.getCustomStore())) {
                        errorMsg.append(ProductInheritanceError.CUSTOM_STORE_IS_NOT_NULL_WHEN_STORE_CUSTOM.getError()).append("；");
                    }
                } else if (relationTypeEnum == ProductRelationTypeEnum.CHANNEL_CUSTOM) {
                    if (StringUtils.isBlank(template.getCustomChannel())) {
                        errorMsg.append(ProductInheritanceError.CUSTOM_CHANNEL_IS_NOT_NULL_WHEN_CHANNEL_CUSTOM.getError()).append("；");
                    }
                }
            }

            // 校验渠道归属
            ChannelOwnershipEnum channelOwnershipEnum = getChannelOwnershipByDesc(template.getChannelOwnershipDesc());
            if (channelOwnershipEnum == null) {
                errorMsg.append(String.format(ProductInheritanceError.CHANNEL_OWNERSHIP_NOT_EXISTS.getError(), template.getChannelOwnershipDesc())).append("；");
            } else {
                template.setChannelOwnership(channelOwnershipEnum.getCode());
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 3. 收集所有商品编码进行批量查询
        Set<String> allProductCodes = new HashSet<>();
        for (ProductInheritanceImportTemplateVO template : importDataList) {
            if (StringUtils.isNotBlank(template.getOriginalSkuCode())) {
                allProductCodes.add(template.getOriginalSkuCode());
            }
            if (StringUtils.isNotBlank(template.getNewSkuCode())) {
                allProductCodes.add(template.getNewSkuCode());
            }
        }

        // 批量查询SKU信息
        Map<String, MasterSku> skuCodeMap = new HashMap<>();
        if (!allProductCodes.isEmpty()) {
            List<MasterSku> skuList = masterSkuDao.queryByProductCodes(new ArrayList<>(allProductCodes));
            if (!CollectionUtils.isEmpty(skuList)) {
                skuCodeMap = skuList.stream().collect(Collectors.toMap(MasterSku::getProductCode, sku -> sku));
            }
        }

        // 4. 校验商品编码是否存在并设置ID
        for (ProductInheritanceImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            // 校验原商品编码
            MasterSku originalSku = skuCodeMap.get(template.getOriginalSkuCode());
            if (originalSku == null) {
                errorMsg.append(String.format(ProductInheritanceError.ORIGINAL_SKU_CODE_NOT_EXISTS.getError(), template.getOriginalSkuCode())).append("；");
            } else {
                template.setOriginalSkuId(String.valueOf(originalSku.getId()));
                template.setOriginalSkuName(originalSku.getProductName());
            }

            // 校验新品商品编码
            MasterSku newSku = skuCodeMap.get(template.getNewSkuCode());
            if (newSku == null) {
                errorMsg.append(String.format(ProductInheritanceError.NEW_SKU_CODE_NOT_EXISTS.getError(), template.getNewSkuCode())).append("；");
            } else {
                template.setNewSkuId(String.valueOf(newSku.getId()));
                template.setNewSkuName(newSku.getProductName());
            }

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        // 5. 校验表格内唯一性
        Map<String, Integer> duplicateMap = new HashMap<>();
        for (int i = 0; i < importDataList.size(); i++) {
            ProductInheritanceImportTemplateVO template = importDataList.get(i);
            String key = template.getOriginalSkuCode() + "_" + template.getNewSkuCode() + "_" +
                    template.getRelationTypeDesc() + "_" +
                    (template.getCustomStore() != null ? template.getCustomStore() : "") + "_" +
                    (template.getCustomChannel() != null ? template.getCustomChannel() : "") + "_" +
                    template.getChannelOwnershipDesc();

            if (duplicateMap.containsKey(key)) {
                template.setErrorMsg(ProductInheritanceError.DUPLICATE_IN_EXCEL.getError() + "；");
                importDataList.get(duplicateMap.get(key)).setErrorMsg(ProductInheritanceError.DUPLICATE_IN_EXCEL.getError() + "；");
                flag = false;
            } else {
                duplicateMap.put(key, i);
            }
        }

        if (!flag) {
            return false;
        }

        for (ProductInheritanceImportTemplateVO template : importDataList) {
            ProductInheritanceRequest request = new ProductInheritanceRequest();
            request.setOriginalSkuId(template.getOriginalSkuId());
            request.setNewSkuId(template.getNewSkuId());
            request.setRelationType(template.getRelationType());
            request.setCustomStore(template.getCustomStore());
            request.setCustomChannel(template.getCustomChannel());
            request.setChannelOwnership(template.getChannelOwnership());
            try {
                productInheritanceService.saveOrUpdate(request);
            } catch (Exception e) {
                template.setErrorMsg("保存失败：" + e.getMessage() + "；");
            }
        }

        return flag;
    }

    /**
     * 根据描述获取关系类型枚举
     */
    private ProductRelationTypeEnum getRelationTypeByDesc(String desc) {
        if (StringUtils.isBlank(desc)) {
            return null;
        }

        for (ProductRelationTypeEnum value : ProductRelationTypeEnum.values()) {
            if (value.getDesc().equals(desc.trim())) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据描述获取渠道归属枚举
     */
    private ChannelOwnershipEnum getChannelOwnershipByDesc(String desc) {
        if (StringUtils.isBlank(desc)) {
            return null;
        }

        for (ChannelOwnershipEnum value : ChannelOwnershipEnum.values()) {
            if (value.getDesc().equals(desc.trim())) {
                return value;
            }
        }
        return null;
    }
}
