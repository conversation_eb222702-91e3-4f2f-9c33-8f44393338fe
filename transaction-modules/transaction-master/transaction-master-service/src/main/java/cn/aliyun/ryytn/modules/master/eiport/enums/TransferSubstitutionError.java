package cn.aliyun.ryytn.modules.master.eiport.enums;

/**
 * @Description 转移替代关系导入错误枚举
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
public enum TransferSubstitutionError {

    ORIGINAL_SKU_CODE_IS_NOT_NULL("原商品编码不能为空"),
    SUBSTITUTE_SKU_CODE_IS_NOT_NULL("替代商品编码不能为空"),
    ORIGINAL_SKU_CODE_NOT_EXISTS("原商品编码[%s]不存在"),
    SUBSTITUTE_SKU_CODE_NOT_EXISTS("替代商品编码[%s]不存在"),
    DUPLICATE_IN_EXCEL("表格内存在重复的原商品+替代商品组合");

    private final String error;

    TransferSubstitutionError(String error) {
        this.error = error;
    }

    public String getError() {
        return error;
    }
}
