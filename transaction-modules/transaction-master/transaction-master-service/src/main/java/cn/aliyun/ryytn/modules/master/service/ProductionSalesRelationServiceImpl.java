package cn.aliyun.ryytn.modules.master.service;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.info.ProductionSalesRelation;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.master.api.ProductionSalesRelationService;
import cn.aliyun.ryytn.modules.master.dao.MasterSkuDao;
import cn.aliyun.ryytn.modules.master.dao.ProductionSalesRelationDao;
import cn.aliyun.ryytn.modules.master.entity.enums.EntityStatusEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationInvalidateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.ProductionSalesRelationRequest;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 生产销售关系Service实现类
 * @date 2024/10/10 15:00
 */
@Slf4j
@Service
public class ProductionSalesRelationServiceImpl implements ProductionSalesRelationService {

    @Autowired
    private ProductionSalesRelationDao productionSalesRelationDao;

    @Autowired
    private MasterSkuDao masterSkuDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(ProductionSalesRelationRequest request) {
        // 获取当前用户
        String currentUser = getCurrentUser();
        Date now = new Date();

        // 检查唯一性
        int existCount = productionSalesRelationDao.checkUniqueness(
                request.getSalesSkuId(),
                request.getProductionSkuId(),
                request.getId()
        );
        if (existCount > 0) {
            throw new ServiceException("该销售商品和生产商品的关系已存在");
        }

        ProductionSalesRelation relation = new ProductionSalesRelation();
        relation.setSalesSkuId(request.getSalesSkuId());
        relation.setProductionSkuId(request.getProductionSkuId());
        relation.setConversionFactor(request.getConversionFactor());
        relation.setUpdatedBy(currentUser);
        relation.setUpdatedTime(now);

        if (request.getId() == null || request.getId().trim().isEmpty()) {
            // 新增
            relation.setId(SeqUtils.getSequenceUid());
            relation.setCreatedBy(currentUser);
            relation.setCreatedTime(now);
            relation.setStatus(EntityStatusEnum.IS_ACTIVE.getCode());

            int result = productionSalesRelationDao.insert(relation);
            if (result <= 0) {
                throw new ServiceException("新增生产销售关系失败");
            }
            log.info("新增生产销售关系成功，ID: {}", relation.getId());
        } else {
            // 修改
            relation.setId(request.getId());

            // 检查记录是否存在
            ProductionSalesRelation existRelation = productionSalesRelationDao.selectById(request.getId());
            if (existRelation == null) {
                throw new ServiceException("要修改的记录不存在");
            }
            if (!EntityStatusEnum.IS_ACTIVE.getCode().equals(existRelation.getStatus())) {
                throw new ServiceException("只能修改有效状态的记录");
            }

            int result = productionSalesRelationDao.update(relation);
            if (result <= 0) {
                throw new ServiceException("修改生产销售关系失败");
            }
            log.info("修改生产销售关系成功，ID: {}", relation.getId());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInvalidate(ProductionSalesRelationInvalidateRequest request) {
        // 检查是否有已经作废的记录
        int invalidatedCount = productionSalesRelationDao.countInvalidated(request.getIds());
        if (invalidatedCount > 0) {
            throw new ServiceException("存在已经作废的记录，不能重复作废");
        }

        // 获取当前用户
        String currentUser = getCurrentUser();

        // 批量作废
        int result = productionSalesRelationDao.batchInvalidate(request.getIds(), currentUser);
        log.info("批量作废生产销售关系成功，作废记录数: {}", result);
    }

    @Override
    public PageInfo<ProductionSalesRelationQueryPageResponse> queryPage(PageCondition<ProductionSalesRelationQueryRequest> pageCondition) {
        // 设置分页参数
        PageHelper.startPage(pageCondition.getPageNum(), pageCondition.getPageSize());

        // 查询关系数据
        List<ProductionSalesRelation> relationList = productionSalesRelationDao.queryPage(pageCondition.getCondition());

        // 转换为VO
        List<ProductionSalesRelationQueryPageResponse> voList = convertToVOList(relationList);

        return new PageInfo<>(voList);
    }

    /**
     * 转换为VO列表
     */
    private List<ProductionSalesRelationQueryPageResponse> convertToVOList(List<ProductionSalesRelation> relationList) {
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }

        // 收集所有需要查询的SKU ID
        Set<String> skuIds = new HashSet<>();
        for (ProductionSalesRelation relation : relationList) {
            if (relation.getSalesSkuId() != null && !relation.getSalesSkuId().trim().isEmpty()) {
                skuIds.add(relation.getSalesSkuId());
            }
            if (relation.getProductionSkuId() != null && !relation.getProductionSkuId().trim().isEmpty()) {
                skuIds.add(relation.getProductionSkuId());
            }
        }

        // 批量查询SKU信息
        Map<String, MasterSku> skuMap = new HashMap<>();
        if (!skuIds.isEmpty()) {
            List<String> idList = new ArrayList<>(skuIds);
            List<MasterSku> skuList = masterSkuDao.queryByIds(idList);
            if (!CollectionUtils.isEmpty(skuList)) {
                skuMap = skuList.stream().collect(Collectors.toMap(sku -> String.valueOf(sku.getId()), sku -> sku));
            }
        }

        // 转换为VO
        List<ProductionSalesRelationQueryPageResponse> voList = new ArrayList<>();
        for (ProductionSalesRelation relation : relationList) {
            ProductionSalesRelationQueryPageResponse vo = new ProductionSalesRelationQueryPageResponse();
            BeanUtils.copyProperties(relation, vo);

            // 设置销售SKU信息
            if (relation.getSalesSkuId() != null && !relation.getSalesSkuId().trim().isEmpty()) {
                MasterSku salesSku = skuMap.get(relation.getSalesSkuId());
                if (salesSku != null) {
                    vo.setSalesSkuCode(salesSku.getProductCode());
                    vo.setSalesSkuName(salesSku.getProductName());
                }
            }

            // 设置生产SKU信息
            if (relation.getProductionSkuId() != null && !relation.getProductionSkuId().trim().isEmpty()) {
                MasterSku productionSku = skuMap.get(relation.getProductionSkuId());
                if (productionSku != null) {
                    vo.setProductionSkuCode(productionSku.getProductCode());
                    vo.setProductionSkuName(productionSku.getProductName());
                }
            }

            // 设置状态描述
            vo.setStatusDesc(EntityStatusEnum.getNameByCode(vo.getStatus()));

            voList.add(vo);
        }

        return voList;
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        try {
            return ServiceContextUtils.currentSession().getAccount().getLoginId();
        } catch (Exception e) {
            log.warn("获取当前用户失败，使用默认用户", e);
            return "SYSTEM";
        }
    }
}
