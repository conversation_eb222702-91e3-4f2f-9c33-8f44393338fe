package cn.aliyun.ryytn.modules.master.eiport.enums;

/**
 * @Description 生产销售关系导入错误枚举
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
public enum ProductionSalesRelationError {

    SALES_SKU_CODE_IS_NOT_NULL("销售商品编码不能为空"),
    PRODUCTION_SKU_CODE_IS_NOT_NULL("生产商品编码不能为空"),
    CONVERSION_FACTOR_IS_NOT_NULL("转换系数不能为空"),
    CONVERSION_FACTOR_MUST_POSITIVE("转换系数必须为正整数"),
    SALES_SKU_CODE_NOT_EXISTS("销售商品编码[%s]不存在"),
    PRODUCTION_SKU_CODE_NOT_EXISTS("生产商品编码[%s]不存在"),
    DUPLICATE_IN_EXCEL("表格内存在重复的销售商品+生产商品组合"),
    DUPLICATE_IN_DATABASE("该销售商品+生产商品组合已存在");

    private final String error;

    ProductionSalesRelationError(String error) {
        this.error = error;
    }

    public String getError() {
        return error;
    }
}
