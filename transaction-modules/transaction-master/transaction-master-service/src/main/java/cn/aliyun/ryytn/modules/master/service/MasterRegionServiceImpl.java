package cn.aliyun.ryytn.modules.master.service;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.info.MasterRegion;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.modules.master.api.MasterRegionService;
import cn.aliyun.ryytn.modules.master.dao.MasterRegionDao;
import cn.aliyun.ryytn.modules.master.entity.enums.EntityStatusEnum;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionUpdateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionUpdateStatusRequest;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 区域信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@Service
public class MasterRegionServiceImpl implements MasterRegionService {
    @Resource
    private MasterRegionDao masterRegionDao;

    @Override
    public PageInfo<MasterRegionPageResponse> queryByPage(PageCondition<MasterRegionPageRequest> condition) {
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        List<MasterRegionPageResponse> responseList = masterRegionDao.queryByPage(condition.getCondition());
        for (MasterRegionPageResponse response : responseList) {
            response.setStatusName(EntityStatusEnum.getNameByCode(response.getStatus()));
        }
        return new PageInfo<>(responseList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(MasterRegionCreateRequest request, String loginId) {
        // 验证 ID 和名称唯一性
        checkCreateUnique(request.getId(), request.getName());

        MasterRegion entity = new MasterRegion();
        entity.setId(request.getId());
        entity.setName(request.getName());
        entity.setParentId(request.getParentId());
        entity.setStatus(EntityStatusEnum.IS_ACTIVE.getCode());
        entity.setCreatedTime(new Date());
        entity.setUpdatedTime(new Date());
        entity.setCreatedBy(loginId);
        entity.setUpdatedBy(loginId);

        masterRegionDao.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MasterRegionUpdateRequest request, String loginId) {
        // 查询原记录是否存在
        MasterRegion existingRegion = masterRegionDao.selectById(request.getId());
        if (existingRegion == null) {
            throw new ServiceException("区域信息不存在");
        }
        if (EntityStatusEnum.NO_ACTIVE.getCode().equals(existingRegion.getStatus())) {
            throw new ServiceException("区域信息已失效，不能修改");
        }

        // 验证 ID 和名称 唯一性
        checkCreateUnique(request.getId(), request.getName());

        MasterRegion updateEntity = new MasterRegion();
        updateEntity.setId(request.getId());
        updateEntity.setName(request.getName());
        updateEntity.setParentId(request.getParentId());
        updateEntity.setUpdatedTime(new Date());
        updateEntity.setUpdatedBy(loginId);

        masterRegionDao.update(updateEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(MasterRegionUpdateStatusRequest request, String loginId) {
        // 查询原记录是否存在
        MasterRegion existingRegion = masterRegionDao.selectById(request.getId());
        if (existingRegion == null) {
            throw new ServiceException("区域信息不存在");
        }

        // 获取当前状态
        Integer currentStatus = existingRegion.getStatus();

        // 计算新状态（切换状态：1->2 或 2->1）
        Integer newStatus = EntityStatusEnum.IS_ACTIVE.getCode().equals(currentStatus) ?
                EntityStatusEnum.NO_ACTIVE.getCode() : EntityStatusEnum.IS_ACTIVE.getCode();

        MasterRegion updateEntity = new MasterRegion();
        updateEntity.setId(request.getId());
        updateEntity.setStatus(newStatus);
        updateEntity.setUpdatedTime(new Date());
        updateEntity.setUpdatedBy(loginId);

        masterRegionDao.updateStatus(updateEntity);

        log.info("区域信息状态已更新，ID: {}, 旧状态: {}, 新状态: {}, 操作人: {}",
                request.getId(), currentStatus, newStatus, loginId);
    }

    /**
     * 名称
     */
    private void checkCreateUnique(String id, String name) {
        int count = masterRegionDao.countOverlap(id, name);
        if (count > 0) {
            throw new ServiceException("相同编码或名称的区域信息已存在");
        }
    }
}
