package cn.aliyun.ryytn.modules.master.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 运输成本导出模板VO
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
public class MasterTransferCostExportTemplateVO {
    /**
     * 出发省ID
     */
    @ExcelField("出发省编码")
    private String fromProvinceId;

    /**
     * 出发省名称
     */
    @ExcelField("出发省名称")
    private String fromProvinceName;

    /**
     * 出发市ID
     */
    @ExcelField("出发市编码")
    private String fromCityId;

    /**
     * 出发市名称
     */
    @ExcelField("出发市名称")
    private String fromCityName;

    /**
     * 到达省ID
     */
    @ExcelField("到达省编码")
    private String toProvinceId;

    /**
     * 到达省名称
     */
    @ExcelField("到达省名称")
    private String toProvinceName;

    /**
     * 到达市ID
     */
    @ExcelField("到达市编码")
    private String toCityId;

    /**
     * 到达市名称
     */
    @ExcelField("到达市名称")
    private String toCityName;

    /**
     * 十三米（元/吨）
     */
    @ExcelField("十三米（元/吨）")
    private BigDecimal cost13m;

    /**
     * 十五米（元/吨）
     */
    @ExcelField("十五米（元/吨）")
    private BigDecimal cost15m;

    /**
     * 铁柜（元/吨）
     */
    @ExcelField("铁柜（元/吨）")
    private BigDecimal costRail;

    /**
     * 汽运时效（天）
     */
    @ExcelField("汽运时效（天）")
    private Integer roadDuration;

    /**
     * 铁运时效（天）
     */
    @ExcelField("铁运时效（天）")
    private Integer railDuration;

    /**
     * 状态
     */
    @ExcelField("状态")
    private String statusName;

    /**
     * 创建人
     */
    @ExcelField("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelField("创建时间")
    private String createdTimeStr;

    /**
     * 更新人
     */
    @ExcelField("更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
    @ExcelField("更新时间")
    private String updatedTimeStr;
}