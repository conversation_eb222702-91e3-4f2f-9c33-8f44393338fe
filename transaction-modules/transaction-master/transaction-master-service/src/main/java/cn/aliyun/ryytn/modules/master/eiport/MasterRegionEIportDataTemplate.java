package cn.aliyun.ryytn.modules.master.eiport;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.modules.master.api.MasterRegionService;
import cn.aliyun.ryytn.modules.master.dao.MasterRegionDao;
import cn.aliyun.ryytn.modules.master.eiport.model.MasterRegionImportTemplateVO;
import cn.aliyun.ryytn.modules.master.eiport.model.export.MasterRegionExportTemplateVO;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterRegionPageResponse;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 区域信息导入导出模板
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@ImportService(value = "masterRegionEIportDataTemplate",
        filename = "区域信息导入模版",
        templateDesc = "填写规范：\n" +
                "1.区域编码：必填，唯一标识\n" +
                "2.区域名称：必填\n" +
                "3.父区域编码：选填，若填写则必须是已存在的区域编码\n" +
                "4.区域级别：必填，1-省级，2-市级，3-区县级",
        async = false)
@ExportService(value = "masterRegionEIportDataTemplate",
        filename = "区域信息数据导出",
        async = false)
public class MasterRegionEIportDataTemplate
        extends EIPortDataTemplate<MasterRegionImportTemplateVO, MasterRegionPageRequest>
        implements ImportPostProcessor<MasterRegionImportTemplateVO> {

    @Autowired
    private MasterRegionService masterRegionService;

    @Autowired
    private MasterRegionDao masterRegionDao;

    @Override
    public boolean preValid(ImportDataWrapper<MasterRegionImportTemplateVO> importDataWrapper) {
        List<MasterRegionImportTemplateVO> importData = importDataWrapper.getData();
        return validate(importData);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<MasterRegionImportTemplateVO> importDataWrapper) {
//        List<MasterRegionImportTemplateVO> importDataList = importDataWrapper.getData();
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
//
//        // 批量保存
//        for (MasterRegionImportTemplateVO template : importDataList) {
//            MasterRegionCreateRequest request = new MasterRegionCreateRequest();
//            request.setId(template.getRegionCode());
//            request.setName(template.getRegionName());
//            request.setParentId(template.getParentRegionCode());
//
//            try {
//                masterRegionService.create(request, loginId);
//            } catch (Exception e) {
//                log.warn("导入出错，{}", Throwables.getStackTraceAsString(e));
//                template.setErrorMsg("导入出错：" + e.getMessage());
//            }
//        }
        return null;
    }

    @Override
    public List<?> getExportTableData(MasterRegionPageRequest request) {
        // 查询数据
        PageCondition<MasterRegionPageRequest> pageCondition = new PageCondition<>();
        pageCondition.setPageNum(1);
        pageCondition.setPageSize(Integer.MAX_VALUE);
        pageCondition.setCondition(request);
        PageInfo<MasterRegionPageResponse> pageInfo = masterRegionService.queryByPage(pageCondition);
        List<MasterRegionPageResponse> regionList = pageInfo.getList();

        // 直接使用 GeiCommonConvert 转换，字段名已经匹配
        return GeiCommonConvert.convert(regionList, MasterRegionExportTemplateVO.class);
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(MasterRegionPageRequest request) {
        return ExcelMetaInfoCreator.create(MasterRegionExportTemplateVO.class);
    }

    /**
     * 校验导入数据
     */
    private boolean validate(List<MasterRegionImportTemplateVO> importDataList) {
        boolean flag = true;

        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();

        // 1. 基本字段校验
        for (MasterRegionImportTemplateVO template : importDataList) {
            StringBuilder errorMsg = new StringBuilder();

            if (StringUtils.isBlank(template.getRegionCode())) {
                errorMsg.append("区域编码不能为空").append("；");
            }

            if (StringUtils.isBlank(template.getRegionName())) {
                errorMsg.append("区域名称不能为空").append("；");
            }

            /*保存数据*/
            saveData(template, loginId);

            if (errorMsg.length() > 0) {
                template.setErrorMsg(errorMsg.toString());
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }

        return flag;
    }

    private void saveData(MasterRegionImportTemplateVO template, String loginId) {
        MasterRegionCreateRequest request = new MasterRegionCreateRequest();
        request.setId(template.getRegionCode());
        request.setName(template.getRegionName());
        request.setParentId(template.getParentRegionCode());

        try {
            masterRegionService.create(request, loginId);
        } catch (Exception e) {
            log.warn("导入出错，{}", Throwables.getStackTraceAsString(e));
            template.setErrorMsg("导入出错：" + e.getMessage());
        }
    }
}