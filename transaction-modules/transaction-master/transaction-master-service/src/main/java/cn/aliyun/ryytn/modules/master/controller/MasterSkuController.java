package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.entity.sap.MasterSku;
import cn.aliyun.ryytn.modules.master.api.MasterSkuService;
import cn.aliyun.ryytn.modules.master.dao.MasterSkuDao;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterSkuQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterSkuSyncByCodeRequest;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.CompletableFuture;

/**
 * @Description 主SKU信息Controller
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Slf4j
@RestController
@RequestMapping("/api/master/masterSku")
@Api(tags = "主SKU信息管理")
public class MasterSkuController {

    @Autowired
    private MasterSkuService masterSkuService;

    @Autowired
    private MasterSkuDao masterSkuDao;

    /**
     * 分页查询主SKU信息
     *
     * @param pageCondition
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询主SKU信息", notes = "日期格式：yyyy-MM-dd")
    @RequiresPermissions(value = {""})
    public ResultInfo<PageInfo<MasterSku>> page(@RequestBody PageCondition<MasterSkuQueryRequest> pageCondition) {
        if (pageCondition == null) {
            pageCondition = new PageCondition<>();
        }

        // 调用Service层方法进行分页查询
        PageInfo<MasterSku> pageInfo = masterSkuService.queryMasterSkuPage(pageCondition);

        return ResultInfo.success(pageInfo);
    }

    /**
     * 从SAP同步主SKU信息
     *
     * @param request 同步请求
     * @return 同步结果
     */
    @PostMapping("/sync")
    @ApiOperation(value = "从SAP同步主SKU信息", notes = "接收商品编码数组，调用SAP接口同步主SKU信息")
    @RequiresPermissions(value = {""})
    public ResultInfo<String> syncMasterSkuFromSap(@RequestBody(required = false) MasterSkuSyncByCodeRequest request) {
        // 创建一个新的请求对象，确保它是 effectively final
        final MasterSkuSyncByCodeRequest finalRequest = request != null ? request : new MasterSkuSyncByCodeRequest();

        // 异步执行同步操作
        CompletableFuture.runAsync(() -> {
            try {
                log.info("Start syncing master SKU from SAP asynchronously, request: {}", finalRequest);
                masterSkuService.syncMasterSkuFromSapByProductCode(finalRequest);
                log.info("Async sync master SKU from SAP completed successfully");
            } catch (Exception e) {
                log.error("Failed to sync master SKU from SAP asynchronously", e);
            }
        });

        return ResultInfo.success("正在同步中，请稍后查看结果");
    }
}
