package cn.aliyun.ryytn.modules.master.task;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import cn.aliyun.ryytn.modules.master.api.MasterSkuService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 从SAP同步主SKU信息定时任务
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Slf4j
@Service
public class SyncMasterSkuFromSapTaskServiceImpl implements TaskService
{
    @Autowired
    private MasterSkuService masterSkuService;

    /**
     *
     * @Description 执行任务，异常直接抛出给quartz，按照misfire策略执行
     * @param schedulerJob 调度任务信息，可以包含以下JSON参数：
     *                     syncTime: 同步日期，格式为"yyyyMMdd"
     *                     productCodes: 商品编码数组，可以是单个商品编码或者商品编码数组
     * @throws Exception
     * <AUTHOR>
     * @date 2024年10月10日 15:00
     */
    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        log.info("Start executing sync master SKU from SAP scheduled task");
        try
        {
            // 解析参数
            String syncTimeStr = null;
            List<String> productCodes = null;

            if (StringUtils.isNotBlank(schedulerJob.getParam()))
            {
                try
                {
                    JSONObject paramJson = JSON.parseObject(schedulerJob.getParam());

                    // 解析同步时间，格式为"yyyyMMdd"
                    syncTimeStr = paramJson.getString("syncTime");

                    // 解析商品编码数组
                    if (paramJson.containsKey("productCodes")) {
                        JSONArray productCodesArray = paramJson.getJSONArray("productCodes");
                        if (productCodesArray != null && !productCodesArray.isEmpty()) {
                            productCodes = new ArrayList<>(productCodesArray.size());
                            for (int i = 0; i < productCodesArray.size(); i++) {
                                productCodes.add(productCodesArray.getString(i));
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    log.warn("Failed to parse task parameters, will use default values", e);
                }
            }
            // 执行同步
            masterSkuService.syncMasterSkuFromSap(syncTimeStr, productCodes);
            log.info("Sync master SKU from SAP scheduled task completed successfully");
        }
        catch (Exception e)
        {
            log.error("Failed to execute sync master SKU from SAP scheduled task", e);
            throw e;
        }
    }
}
