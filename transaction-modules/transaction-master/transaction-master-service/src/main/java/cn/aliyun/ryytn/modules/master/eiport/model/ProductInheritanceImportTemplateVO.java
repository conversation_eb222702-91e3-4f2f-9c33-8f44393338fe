package cn.aliyun.ryytn.modules.master.eiport.model;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description 商品继承关系导入模板VO
 * <AUTHOR>
 * @date 2024/10/10 15:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductInheritanceImportTemplateVO extends ImportTemplateVO {

    /**
     * 原商品编码
     */
    @ExcelField(value = "原商品编码", importKey = true)
    private String originalSkuCode;

    /**
     * 原商品名称
     */
    @ExcelField("原商品名称")
    private String originalSkuName;

    /**
     * 新品商品编码
     */
    @ExcelField(value = "新品商品编码", importKey = true)
    private String newSkuCode;

    /**
     * 新品商品名称
     */
    @ExcelField("新品商品名称")
    private String newSkuName;

    /**
     * 关系类型
     */
    @ExcelField(value = "关系类型", importKey = true)
    private String relationTypeDesc;

    /**
     * 定制店铺
     */
    @ExcelField(value = "定制店铺", importKey = true)
    private String customStore;

    /**
     * 定制渠道
     */
    @ExcelField(value = "定制渠道", importKey = true)
    private String customChannel;

    /**
     * 渠道归属
     */
    @ExcelField(value = "渠道归属", importKey = true)
    private String channelOwnershipDesc;

    // 以下字段用于内部处理，不在Excel中显示
    /**
     * 原商品ID（内部使用）
     */
    private String originalSkuId;

    /**
     * 新品商品ID（内部使用）
     */
    private String newSkuId;

    /**
     * 关系类型编码（内部使用）
     */
    private Integer relationType;

    /**
     * 渠道归属编码（内部使用）
     */
    private Integer channelOwnership;
}
