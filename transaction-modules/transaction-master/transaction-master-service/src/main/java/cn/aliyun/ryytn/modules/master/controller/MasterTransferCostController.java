package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.master.api.MasterTransferCostService;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostCreateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.MasterTransferCostUpdateStatusRequest;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 运输成本控制器
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@RestController
@RequestMapping("/master/transferCost")
@Api(tags = "运输成本管理")
public class MasterTransferCostController {

    @Resource
    private MasterTransferCostService masterTransferCostService;

    /**
     * 分页查询运输成本
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation("分页查询运输成本")
    public ResultInfo<PageInfo<MasterTransferCostPageResponse>> queryByPage(@RequestBody PageCondition<MasterTransferCostPageRequest> condition) {
        if (Objects.isNull(condition)) {
            condition = new PageCondition<>();
        }

        PageInfo<MasterTransferCostPageResponse> pageInfo = masterTransferCostService.queryByPage(condition);
        return ResultInfo.success(pageInfo);
    }

    /**
     * 创建运输成本
     *
     * @param request 创建请求
     * @return 结果
     */
    @PostMapping("/create")
    @ApiOperation("创建运输成本")
    public ResultInfo<?> create(@RequestBody @Validated MasterTransferCostCreateRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterTransferCostService.create(request, loginId);
        return ResultInfo.success();
    }

    /**
     * 修改运输成本
     *
     * @param request 修改请求
     * @return 结果
     */
    @PostMapping("/update")
    @ApiOperation("修改运输成本")
    public ResultInfo<?> update(@RequestBody @Validated MasterTransferCostCreateRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterTransferCostService.create(request, loginId);
        return ResultInfo.success();
    }

    /**
     * 禁用
     *
     * @param request 状态修改请求
     * @return 结果
     */
    @PostMapping("/disable")
    @ApiOperation("禁用")
    public ResultInfo<?> disable(@RequestBody @Validated MasterTransferCostUpdateStatusRequest request) {
        String loginId = "c012087";
//        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        masterTransferCostService.updateStatus(request, loginId);
        return ResultInfo.success();
    }
}