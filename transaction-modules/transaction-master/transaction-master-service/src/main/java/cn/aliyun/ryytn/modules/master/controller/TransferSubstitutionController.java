package cn.aliyun.ryytn.modules.master.controller;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.master.api.TransferSubstitutionService;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionInvalidateRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionQueryPageResponse;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionQueryRequest;
import cn.aliyun.ryytn.modules.master.entity.vo.TransferSubstitutionRequest;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * <AUTHOR>
 * @Description 转移替代关系Controller
 * @date 2024/10/10 15:00
 */
@Slf4j
@RestController
@RequestMapping("/api/master/transferSubstitution")
@Api(tags = "转移替代关系管理")
public class TransferSubstitutionController {

    @Autowired
    private TransferSubstitutionService transferSubstitutionService;

    /**
     * 新增或修改转移替代关系
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "新增或修改转移替代关系", notes = "新增时ID为空，修改时ID必填")
    @RequiresPermissions(value = {""})
    public ResultInfo<?> saveOrUpdate(@RequestBody TransferSubstitutionRequest request) {
        try {
            transferSubstitutionService.saveOrUpdate(request);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("新增或修改转移替代关系失败", e);
            return ResultInfo.fail(null, "操作失败：" + e.getMessage());
        }
    }

    /**
     * 批量作废转移替代关系
     *
     * @param request 作废请求
     * @return 操作结果
     */
    @PostMapping("/batchInvalidate")
    @ApiOperation(value = "批量作废转移替代关系", notes = "将状态改为无效，已作废的记录不能重复作废")
    @RequiresPermissions(value = {""})
    public ResultInfo<?> batchInvalidate(@RequestBody TransferSubstitutionInvalidateRequest request) {
        try {
            transferSubstitutionService.batchInvalidate(request);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("批量作废转移替代关系失败", e);
            return ResultInfo.fail(null, "作废失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询转移替代关系
     *
     * @param pageCondition 分页查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询转移替代关系", notes = "支持按原商品ID、替代商品ID、状态等条件查询")
    @RequiresPermissions(value = {""})
    public ResultInfo<PageInfo<TransferSubstitutionQueryPageResponse>> page(@RequestBody PageCondition<TransferSubstitutionQueryRequest> pageCondition) {
        try {
            // 设置默认分页参数
            if (pageCondition.getPageNum() == null) {
                pageCondition.setPageNum(1);
            }
            if (pageCondition.getPageSize() == null) {
                pageCondition.setPageSize(100);
            }
            if (pageCondition.getCondition() == null) {
                pageCondition.setCondition(new TransferSubstitutionQueryRequest());
            }

            PageInfo<TransferSubstitutionQueryPageResponse> pageInfo = transferSubstitutionService.queryPage(pageCondition);
            return ResultInfo.success(pageInfo);
        } catch (Exception e) {
            log.error("查询转移替代关系失败", e);
            return (ResultInfo<PageInfo<TransferSubstitutionQueryPageResponse>>) ResultInfo.fail(null, "查询失败：" + e.getMessage());
        }
    }
}
