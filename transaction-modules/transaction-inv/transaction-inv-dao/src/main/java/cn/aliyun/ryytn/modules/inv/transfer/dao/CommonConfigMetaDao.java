package cn.aliyun.ryytn.modules.inv.transfer.dao;

import cn.aliyun.ryytn.modules.inv.entity.business.request.InvSkuCdcFullSupplyUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.transfer.dos.ScpCommonConfigMeta;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:21
 * @description：
 */
public interface CommonConfigMetaDao {

    List<ScpCommonConfigMeta> selectByCondition(Map<String, Object> params);

    Long batchUpdateScpCommonConfigMeta(List<ScpCommonConfigMeta> updateList);
    Long batchSaveScpCommonConfigMeta(List<ScpCommonConfigMeta> updateList);

}
