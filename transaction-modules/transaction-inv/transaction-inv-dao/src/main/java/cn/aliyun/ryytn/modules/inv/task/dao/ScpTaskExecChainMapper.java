package cn.aliyun.ryytn.modules.inv.task.dao;


import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskExecChainDO;

import java.util.List;
import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-20 15:27
 * @description
 */
public interface ScpTaskExecChainMapper {
    List<ScpTaskExecChainDO> selectByCondition(Map<String, Object> params);

    Long insert(ScpTaskExecChainDO scpTaskExecChainDO);

    Long update(Map<String, Object> params);
}
