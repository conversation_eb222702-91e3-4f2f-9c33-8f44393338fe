package cn.aliyun.ryytn.modules.inv.md.dao;

import cn.aliyun.ryytn.modules.inv.entity.md.dos.InvCdcWarehouseDO;
import cn.aliyun.ryytn.modules.inv.entity.md.dos.InvRdcWarehouseDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/24 10:20
 * @description：仓库Mapper
 */
public interface InvWarehouseDao {
    /**
     * 工厂仓筛选项公共查询
     * @param cdc
     * @return
     */
    List<InvCdcWarehouseDO> cdcSearch(String cdc);

    /**
     * rdc仓筛选项公共查询
     * @param rdc
     * @return
     */
    List<InvRdcWarehouseDO> rdcSearch(String rdc);

}
