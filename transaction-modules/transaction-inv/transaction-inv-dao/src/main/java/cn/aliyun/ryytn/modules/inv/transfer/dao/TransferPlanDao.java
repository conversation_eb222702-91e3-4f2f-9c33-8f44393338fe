package cn.aliyun.ryytn.modules.inv.transfer.dao;

import cn.aliyun.ryytn.modules.inv.entity.transfer.dos.TransferPlan;
import cn.aliyun.ryytn.modules.inv.entity.transfer.request.TransferPlanPageQueryRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2025/5/26 13:38
 */
public interface TransferPlanDao {

    List<TransferPlan> selectInventoryDataList();

    List<TransferPlan> selectInventoryDataPageList(TransferPlanPageQueryRequest request);

    Long selectCount(Map<String, Object> params);


    // 下拉生产编码数组
    List<String> getProductionCodingList(String key);

    // 下拉调出物理仓数组
    List<String> getOutPhysicalWarehouseNameList(String key);

    // 下拉调出逻辑仓数组
    List<String> getOutLogicalWarehouseNameList(String key);

    // 下拉调入物理仓数组
    List<String> getInLogicalWarehouseNameList(String key);

    // 下拉调入逻辑仓数组
    List<String> getInPhysicalWarehouseNameList(String key);

    // 下拉有效期规则数组
    List<String> getValidityRuleList(String key);

    // 下拉运输方式数组
    List<String> getModeOfTransportList(String key);

    // 下拉预警状态数组
    List<String> getAlarmStatusList(String key);
}
