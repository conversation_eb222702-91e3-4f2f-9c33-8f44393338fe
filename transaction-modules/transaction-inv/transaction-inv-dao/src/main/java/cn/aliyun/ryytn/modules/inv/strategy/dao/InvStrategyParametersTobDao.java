package cn.aliyun.ryytn.modules.inv.strategy.dao;

import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyParametersTobDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvParametersRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/29 14:19
 * @description：
 */
public interface InvStrategyParametersTobDao {

    int batchInsert(List<InvStrategyParametersTobDO> params);

    List<InvStrategyParametersTobDO> selectByInvStrategyId(Map<String, Object> params);

    Long selectCount(Map<String, Object> params);

    void batchUpdate(List<InvParametersRequest> requests);

    void batchUpdateByIds(InvParametersRequest request);

    void deleteByStrategyIdAndStatus(Long strategyId, Integer status);

    void enableByStrategyId(Long strategyId);

    Integer deleteByStatus(Integer status);

    void deletebyStrategyId(Long strategyId);
}
