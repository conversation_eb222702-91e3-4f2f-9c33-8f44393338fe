package cn.aliyun.ryytn.modules.inv.task.dao;


import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskCfgDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-19 11:25
 * @description
 */
public interface ScpTaskCfgMapper {
    List<ScpTaskCfgDO> selectByCondition(Map<String, Object> params);
    Long selectCount(Map<String, Object> params);

    Long upsert(List<ScpTaskCfgDO> params);
    Long delete(@Param("taskTypeCode") String taskTypeCode, @Param("odpsCode") Long odpsCode);

    Long update(List<ScpTaskCfgDO> finalUpdate);
}
