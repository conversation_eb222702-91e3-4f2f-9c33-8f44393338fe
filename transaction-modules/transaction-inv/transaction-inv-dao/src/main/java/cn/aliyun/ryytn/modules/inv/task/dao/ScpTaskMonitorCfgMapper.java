package cn.aliyun.ryytn.modules.inv.task.dao;

import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskMonitorCfgDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-27 11:27
 * @description 监控器配置表没有复杂查询业务，所以使用mp快速开发
 */
public interface ScpTaskMonitorCfgMapper {

    List<ScpTaskMonitorCfgDO> selectByCondition(String taskType, String dateExpr);



}
