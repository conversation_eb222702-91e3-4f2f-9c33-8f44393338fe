package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.converter;

import com.alibaba.fastjson2.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterRegistry;
import org.springframework.core.convert.support.DefaultConversionService;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDate;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-06 20:15
 * @description 响应转换服务
 */
public class ResponseConversionService extends DefaultConversionService {
    private final Logger LOGGER = LoggerFactory.getLogger(ResponseConversionService.class);
    private volatile static ResponseConversionService service;
    private ResponseConversionService() {
        super();
        addDefaultConverters(this);
    }
    public static ResponseConversionService getInstance(){
        if (service == null) {
            synchronized (ResponseConversionService.class) {
                if (service == null) { service = new ResponseConversionService(); }
            }
        }
        return service;
    }
    public static void addDefaultConverters(ConverterRegistry converterRegistry) {
        converterRegistry.addConverter(new LocalDateStringToLocalDateConverter());
    }

    public <T> T doConvert(String result, Type targetType) {
        try {
            Class<T> clazz;
            if (targetType instanceof ParameterizedType) {
                ParameterizedType pt = (ParameterizedType) targetType;
                clazz = ((Class<T>) pt.getRawType());
            } else {
                clazz = (Class<T>) targetType;
            }
            return convert(result, clazz);
        } catch (Exception e) {
            try {
                return JSON.parseObject(result, targetType);
            }catch (Exception exception){
                LOGGER.error("response convert error, result:{}", result, e);
                return null;
            }
        }
    }
    final static class LocalDateStringToLocalDateConverter implements Converter<String, LocalDate> {
        LocalDateStringToLocalDateConverter() {
        }
        public LocalDate convert(String source) {
            return LocalDate.parse(source);
        }
    }
}
