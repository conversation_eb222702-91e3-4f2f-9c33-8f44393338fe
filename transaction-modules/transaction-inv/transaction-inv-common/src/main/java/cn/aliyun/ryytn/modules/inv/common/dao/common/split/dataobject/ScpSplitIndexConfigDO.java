package cn.aliyun.ryytn.modules.inv.common.dao.common.split.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-03-11 14:32
 * @description
 */
@Data
@TableName("scp_split_index_config")
public class ScpSplitIndexConfigDO {

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 配置指标编码，应该全局唯一
     */
    private String indexCode;

    /**
     * meta编码
     */
    private String metaCode;

    /**
     * meta名称
     */
    private String metaName;

    /**
     * 分组字段名，逗号分隔
     */
    private String groupFields;

    /**
     * 是否隐藏，false不隐藏，ture隐藏
     */
    private Boolean hidden;

    /**
     * 用于meta的顺序
     */
    private Integer sort;

    /**
     * 设置冻结该列，如果不设置当前字段，则表示不冻结 left - 左侧冻结 / right - 右侧冻结
     */
    private String fixed;

    /**
     * 多维开关
     */
    private Boolean multi;

    /**
     * 分割数据的目标class全路径
     */
    private String targetClass;
}
