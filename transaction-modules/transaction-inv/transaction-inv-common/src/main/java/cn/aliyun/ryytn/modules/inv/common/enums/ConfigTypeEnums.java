package cn.aliyun.ryytn.modules.inv.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-06-18 11:59
 * @description 配置类型枚举
 */
@Getter
@AllArgsConstructor
public enum ConfigTypeEnums {
    SELECT("select", "选择器"),
    RATIO("ratio", "单选"),
    INPUT("input", "输入框"),
    TEXT("text", "文本"),
    INPUT_NUMBER("inputNumber", "数字输入框"),
    TIME("time", "时间选择"),
    ;

    private final String code;
    private final String name;
}
