package cn.aliyun.ryytn.modules.inv.common.enums;

/**
 * 系统枚举的枚举名称
 * 初始化会找到util enums 配置在这个类中的枚举类 进行初始化加载在本地缓存
 * <p>
 *
 * <AUTHOR>
 */
public enum EnumCollect {

    /**
     * 文件业务
     */
    FILE_BIZ("fileBiz", "com.cainiao.cntech.dsct.application.enums.FileBizEnum");

    private final String name;

    private final String path;

    EnumCollect(String name, String path) {
        this.name = name;
        this.path = path;
    }

    public String getName() {
        return name;
    }

    public String getPath() {
        return path;
    }
}
