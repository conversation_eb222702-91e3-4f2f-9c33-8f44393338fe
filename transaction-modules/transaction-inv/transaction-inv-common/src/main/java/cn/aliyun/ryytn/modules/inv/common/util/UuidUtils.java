//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.aliyun.ryytn.modules.inv.common.util;

import java.util.UUID;

public class UuidUtils {
    private static final String REPLACED_SYMBOL_SOURCE = "-";
    private static final String REPLACED_SYMBOL_TARGET = "";

    public UuidUtils() {
    }

    public static String getUniqueId() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }
}
