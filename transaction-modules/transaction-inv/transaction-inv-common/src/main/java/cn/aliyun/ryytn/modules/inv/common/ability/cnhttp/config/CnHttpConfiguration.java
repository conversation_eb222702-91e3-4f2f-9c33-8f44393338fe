package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.config;

import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.CnHttpApiTemplate;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.DefaultCnHttpApiTemplate;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.fallback.FallbackFactoryManager;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.proxy.CnHttpClientProxyCreator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-04 14:08
 * @description 配置类
 */
@Configuration
@ConditionalOnProperty(prefix = "cnhttp", name = "enable", havingValue = "true")
public class CnHttpConfiguration {
    @Bean
    public CnHttpClientProxyCreator outsideProxyCreator(){
        return new CnHttpClientProxyCreator();
    }
    @Bean
    public CnHttpApiProperties outsideProperties(){
        return new CnHttpApiProperties();
    }
    @Bean
    public FallbackFactoryManager fallbackFactoryManager(){ return new FallbackFactoryManager(); }
    @Bean
    @ConditionalOnMissingBean(CnHttpApiTemplate.class)
    public CnHttpApiTemplate outsideApiTemplate(){
        return new DefaultCnHttpApiTemplate(outsideProperties());
    }
}
