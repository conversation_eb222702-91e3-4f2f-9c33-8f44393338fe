package cn.aliyun.ryytn.modules.inv.common.dao.common.request;

import cn.aliyun.ryytn.modules.inv.common.dao.common.enums.QueryOrderEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 15:45
 * @description
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class BasePageQueryRequest extends BaseTenantRequest {

    /**
     * 原始页
     */
    private Long currentPage = 1L;
    /**
     * 数据查询起始行
     */
    private Long pageNum;

    private Long pageSize = 10L;

    private Boolean paging = Boolean.TRUE;

    private String orderColumn;
    private String orderType = QueryOrderEnum.ASC.getCode();
    /**
     * 校正分页标记
     */
//    private Boolean correctPage = Boolean.TRUE;

    public void calcPage(){
        if(Boolean.TRUE.equals(getPaging()) && Objects.nonNull(getCurrentPage())){
            setPageNum((getCurrentPage() - 1) * getPageSize());
        }
    }
    public boolean enablePaging(){
        return Boolean.TRUE.equals(getPaging());
    }

//    public boolean isCorrectPage(){
//        return Boolean.TRUE.equals(getCorrectPage());
//    }
}