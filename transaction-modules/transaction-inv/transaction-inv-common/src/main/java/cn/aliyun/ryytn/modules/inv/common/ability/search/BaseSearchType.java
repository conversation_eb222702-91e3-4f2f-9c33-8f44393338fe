package cn.aliyun.ryytn.modules.inv.common.ability.search;

import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;

import java.util.List;
import java.util.function.Function;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-06-12 17:24
 * @description 条件搜索通用接口，枚举需要实现
 */
public interface BaseSearchType<T> {

    /**
     * 搜索字段，需要与数据库表的字段对应
     * @return
     */
    List<String> getSearchField();

    /**
     * 转换器，搜索结果 转换为OptionDTO
     * @return
     */
    Function<T, OptionDTO> getConvert();

    default OptionDTO convert(T t){
        Function<T, OptionDTO> convert = getConvert();
        if(convert == null){
            return null;
        }
        return convert.apply(t);
    }
}
