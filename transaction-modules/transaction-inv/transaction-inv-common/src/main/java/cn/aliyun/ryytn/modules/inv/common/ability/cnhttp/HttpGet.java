package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp;

import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;

import java.net.URI;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-07 14:46
 * @description
 */
public class HttpGet extends HttpEntityEnclosingRequestBase {
    public HttpGet(String url) {
        this.setURI(URI.create(url));
    }
    public HttpGet(URI uri) {
        this.setURI(uri);
    }
    public String getMethod() {
        return HttpMethod.GET.name();
    }
}