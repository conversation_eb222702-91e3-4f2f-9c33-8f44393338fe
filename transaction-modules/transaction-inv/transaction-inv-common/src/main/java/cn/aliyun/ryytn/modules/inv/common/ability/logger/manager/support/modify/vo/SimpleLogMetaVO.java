package cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.vo;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import lombok.Data;

import java.util.List;


/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-14 10:13
 * @description 变更记录
 */
@Data
public class SimpleLogMetaVO {
    /**
     * 修改前的值
     */
    @MetaField("变更前")
    private List<String> beforeModify;
    /**
     * 修改后的值
     */
    @MetaField("变更后")
    private List<String> afterModify;
    /**
     * 变更时间
     */
    @MetaField("变更时间")
    private String modifyTime;
    /**
     * 操作人
     */
    @MetaField("变更人")
    private String operatorName;
}
