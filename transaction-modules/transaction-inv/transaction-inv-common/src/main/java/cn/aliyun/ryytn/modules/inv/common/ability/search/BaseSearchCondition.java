package cn.aliyun.ryytn.modules.inv.common.ability.search;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-06-12 17:24
 * @description 条件搜索通用接口，枚举需要实现
 */
public interface BaseSearchCondition<T, R> {

    /**
     * 搜索字段，需要与数据库表的字段对应
     * @return
     */
    List<String> getSearchField();

    default List<String> getGroupField(){
        return null;
    }

    default Consumer<QueryWrapper<T>> getBeforeListExt(){
        return null;
    }

    /**
     * 转换器，搜索结果 转换为OptionDTO
     * @return
     */
    Function<T, R> getConvert();

    default R convert(T t){
        if(Objects.isNull(t)){
            return null;
        }
        Function<T, R> convert = getConvert();
        if(convert == null){
            return null;
        }
        return convert.apply(t);
    }
}
