package cn.aliyun.ryytn.modules.inv.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2025/5/27 15:46
 */
@Getter
public enum TransferPlanEnum {

    sku("SKU", "sku"),
    skuCdc("SKU*工厂仓","skuCdc"),
    skuRdc("SKU*RDC","skuRdc"),
    cdc("工厂仓","cdc"),
    rdc("RDC","rdc"),
    cdcRdc("工厂仓*RDC","cdcRdc"),
    ;


    private String label;

    private String value;

    TransferPlanEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

    // 缓存：value -> label 的映射
    private static final Map<String, String> VALUE_TO_LABEL_MAP = new HashMap<>();
    // 缓存：label -> value 的映射
    private static final Map<String, String> LABEL_TO_VALUE_MAP = new HashMap<>();

    static {
        for (TransferPlanEnum item : values()) {
            VALUE_TO_LABEL_MAP.put(item.getValue(), item.getLabel());
            LABEL_TO_VALUE_MAP.put(item.getLabel(), item.getValue());
        }
    }

    /**
     * 根据 value 获取 label
     */
    public static String getLabelByValue(String value) {
        return VALUE_TO_LABEL_MAP.get(value);
    }

    /**
     * 根据 label 获取 value
     */
    public static String getValueByLabel(String label) {
        return LABEL_TO_VALUE_MAP.get(label);
    }

    /**
     * 获取所有 value 列表
     */
    public static List<String> getAllValues() {
        return Arrays.stream(values())
                .map(TransferPlanEnum::getValue)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有 label 列表
     */
    public static List<String> getAllLabels() {
        return Arrays.stream(values())
                .map(TransferPlanEnum::getLabel)
                .collect(Collectors.toList());
    }
}
