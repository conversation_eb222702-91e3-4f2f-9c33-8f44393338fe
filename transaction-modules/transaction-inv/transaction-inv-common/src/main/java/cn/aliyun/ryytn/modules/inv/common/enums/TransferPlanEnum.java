package cn.aliyun.ryytn.modules.inv.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2025/5/27 15:46
 */
@Getter
@AllArgsConstructor
public enum TransferPlanEnum {

    sku("SKU", "sku"),
    skuCdc("SKU*工厂仓","skuCdc"),
    skuRdc("SKU*RDC","skuRdc"),
    cdc("工厂仓","cdc"),
    rdc("RDC","rdc"),
    cdcRdc("工厂仓*RDC","cdcRdc"),
    ;


    private String label;

    private String value;
}
