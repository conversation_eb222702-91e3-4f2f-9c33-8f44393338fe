package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-04 15:46
 * @description 请求异常
 */
public class CnHttpRequestException extends RuntimeException {
    public CnHttpRequestException() {
    }

    public CnHttpRequestException(ErrorEnum errorEnum) {
        this(errorEnum.info());
    }

    public CnHttpRequestException(String message) {
        super(message);
    }

    public CnHttpRequestException(String message, Throwable cause) {
        super(message, cause);
    }

    public CnHttpRequestException(Throwable cause) {
        super(cause);
    }

    public CnHttpRequestException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
