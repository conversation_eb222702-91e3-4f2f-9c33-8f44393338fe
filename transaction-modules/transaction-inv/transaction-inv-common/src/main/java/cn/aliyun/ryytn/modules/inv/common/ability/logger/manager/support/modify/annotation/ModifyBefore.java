package cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.annotation;

import java.lang.annotation.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-11 14:44
 * @description 埋点日志 修改前标记
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ModifyBefore {
    /**
     * 格式化内容：
     *
     * @see ConstantReplaceFieldFormatter : 常量替换格式化器
     * @see RefConstantReplaceFieldFormatter : 引用常量替换格式化器
     * @see StringFormatFieldFormatter : StringFormat格式化器
     * @see NumberFieldFormatter : 基础数值运算格式化器
     * @see LocalDateFormatFieldFormatter : 日期格式化器
     */
    String[] value() default {};
}
