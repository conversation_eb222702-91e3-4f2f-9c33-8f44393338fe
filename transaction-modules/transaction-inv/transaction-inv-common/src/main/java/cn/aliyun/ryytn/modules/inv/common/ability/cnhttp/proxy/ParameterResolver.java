package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.proxy;

import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.annotation.PathVariable;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.annotation.RequestBody;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.annotation.RequestHeader;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.annotation.RequestPart;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception.CnHttpParameterResolveException;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception.ErrorEnum;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.core.io.InputStreamSource;

import java.io.File;
import java.io.InputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-04 16:25
 * @description
 */
public class ParameterResolver {
    /** 兼容Spring的RequestHeader  */
    private final static String SPRING_REQUEST_HEADER = "org.springframework.web.bind.annotation.RequestHeader";
    /** 兼容Spring的RequestBody  */
    private final static String SPRING_REQUEST_BODY = "org.springframework.web.bind.annotation.RequestBody";
    /** 兼容Spring的PathVariable  */
    private final static String SPRING_PATH_VARIABLE = "org.springframework.web.bind.annotation.PathVariable";
    /** 兼容Spring的RequestPart  */
    private final static String SPRING_REQUEST_PART = "org.springframework.web.bind.annotation.RequestPart";
    private final static String REQUIRED_METHOD = "required";
    private final static String VALUE_METHOD = "value";
    private final static String NAME_METHOD = "name";
    private final static String MATCHES_SECOND_UPPERCASE = "([a-z])([A-Z]).*";
    interface Resolver {
        boolean resolve(CnHttpApiWrapper wrapper, Parameter parameter, Object argument);
    }
    private static final List<Resolver> RESOLVERS;
    static {
        RESOLVERS = new ArrayList<>();
        // request part
        RESOLVERS.add((wrapper, parameter, argument) -> {
            Class<? extends Annotation> requestPart = getAnnotationClass(parameter, SPRING_REQUEST_PART, RequestPart.class);
            // 仅处理requestPart注解
            if(requestPart == null){ return false; }
            Boolean value = invokeAnnotationMethod(parameter, requestPart, REQUIRED_METHOD, Boolean.class);
            if(argument == null && Boolean.TRUE.equals(value)){ throw new CnHttpParameterResolveException(ErrorEnum.REQUEST_PART_EMPTY);}
            if(argument == null){ return true; }
            Map<?, ?> map = parseMap(argument);
            if(map == null){
                if(argument instanceof Collection){
                    for (Object obj : ((Collection) argument)) {
                        if(!isPart(obj)){ throw new CnHttpParameterResolveException(ErrorEnum.REQUEST_PART_NOT_SUPPORT); }
                    }
                    wrapper.getPart().put(parameter.getName(), argument);
                }else if(argument.getClass().isArray()){
                    for (Object obj : ((Object[]) argument)) {
                        if(!isPart(obj)){ throw new CnHttpParameterResolveException(ErrorEnum.REQUEST_PART_NOT_SUPPORT); }
                    }
                    wrapper.getPart().put(parameter.getName(), argument);
                }else if(isPart(argument)) {
                    wrapper.getPart().put(parameter.getName(), argument);
                }
                wrapper.setContentType(ContentType.MULTIPART_FORM_DATA.getMimeType());
                wrapper.getHeaders().remove("Content-Type");
                return true;
            }
            throw new CnHttpParameterResolveException(ErrorEnum.REQUEST_PART_NOT_SUPPORT);
        });
        // path variable
        RESOLVERS.add((wrapper, parameter, argument) -> {
            Class<? extends Annotation> pathVariable = getAnnotationClass(parameter, SPRING_PATH_VARIABLE, PathVariable.class);
            // 仅处理pathVariable注解
            if(pathVariable == null){ return false; }
            Boolean value = invokeAnnotationMethod(parameter, pathVariable, REQUIRED_METHOD, Boolean.class);
            if(argument == null && Boolean.TRUE.equals(value)){ throw new CnHttpParameterResolveException(ErrorEnum.PATH_VARIABLE_EMPTY);}
            if(argument == null){ return true; }
            Map<?, ?> map = parseMap(argument);
            if(map == null){
                String name = resolveValueAndName(parameter, pathVariable);
                wrapper.getPathVariable().put(name , argument.toString());
            }else {
                throw new CnHttpParameterResolveException(ErrorEnum.PATH_VARIABLE_CAN_CONVERT_TO_JSON);
            }
            return true;
        });
        // body
        RESOLVERS.add((wrapper, parameter, argument) -> {
            Class<? extends Annotation> bodyClass = getAnnotationClass(parameter, SPRING_REQUEST_BODY, RequestBody.class);
            // 仅处理RequestBody注解
            if(bodyClass == null){ return false; }
            Boolean value = invokeAnnotationMethod(parameter, bodyClass, REQUIRED_METHOD, Boolean.class);
            if(argument == null && Boolean.TRUE.equals(value)){ throw new CnHttpParameterResolveException(ErrorEnum.REQUEST_BODY_EMPTY);}
            if(argument == null){ return true; }
            Map<?, ?> map = parseMap(argument);
            if(map == null){
                wrapper.getBody().put(parameter.getName(), argument);
                if(argument instanceof Collection || argument.getClass().isArray()){
                    if(Boolean.FALSE.equals(wrapper.getIsBodyArr())){ wrapper.setIsBodyArr(Boolean.TRUE); }
                }
            }else {
                for (Map.Entry<?, ?> entry : map.entrySet()) {
                    String key = String.valueOf(entry.getKey());
                    if (key.matches(MATCHES_SECOND_UPPERCASE)) {
                        key = key.substring(0,2).toLowerCase() + key.substring(2);
                    }
                    wrapper.getBody().put(key, entry.getValue());
                }
            }
            return true;
        });
        // params 能执行该解析器，那说明不被RequestBody标记，正常解析params
        RESOLVERS.add((wrapper, parameter, argument) -> {
            Map<?, ?> map = parseMap(argument);
            if(map == null){
                wrapper.getParams().put(parameter.getName(), argument);
            }else {
                for (Map.Entry<?, ?> entry : map.entrySet()) {
                    wrapper.getParams().put(String.valueOf(entry.getKey()), entry.getValue());
                }
            }
            return true;
        });
    }

    public static void resolveRequestArgsAndPadding(CnHttpApiWrapper wrapper, Object[] args){
        if(wrapper == null || wrapper.getSourceMethod() == null){return;}
        Parameter[] parameters = wrapper.getSourceMethod().getParameters();
        int index = 0;
        a: for (Parameter parameter : parameters) {
            if(getAnnotationClass(parameter, SPRING_REQUEST_HEADER, RequestHeader.class) != null){
                index++;
                continue;
            }
            for (Resolver resolver : RESOLVERS) {
                if (resolver.resolve(wrapper, parameter, args[index])) {
                    index++;
                    continue a;
                }
            }
            index++;
        }
        Map<String, Object> body = wrapper.getBody();
        if(Objects.nonNull(body) && body.size() > 1){
            wrapper.setIsBodyArr(Boolean.FALSE);
        }
    }

    public static void resolveHeaderAndPadding(CnHttpApiWrapper wrapper, Object[] args){
        if(wrapper == null){ return; }
        Map<String, String> headers = Maps.newLinkedHashMap();
        if (StringUtils.isNotBlank(wrapper.getContentType())) { headers.put("Content-Type", wrapper.getContentType()); }
        if(wrapper.getSourceMethod() == null || Objects.isNull(args) || args.length < 1){
            wrapper.addHeaders(headers);
            return;
        }
        Parameter[] parameters = wrapper.getSourceMethod().getParameters();
        int index = 0;
        for (Parameter parameter : parameters) {
            Class<? extends Annotation> requestHeaderClass = getAnnotationClass(parameter, SPRING_REQUEST_HEADER, RequestHeader.class);
            if(requestHeaderClass != null){
                Object argument;
                Boolean value = invokeAnnotationMethod(parameter, requestHeaderClass, REQUIRED_METHOD, Boolean.class);
                if((argument = args[index]) == null && Boolean.TRUE.equals(value)){ throw new CnHttpParameterResolveException(ErrorEnum.REQUEST_HEADER_EMPTY);}
                String name = resolveValueAndName(parameter, requestHeaderClass);
                headers.put(name, argument == null ? null : argument.toString());
            }
            index++;
        }
        wrapper.addHeaders(headers);
    }
    public static Map<?, ?> parseMap(Object argument){
        try {
            String str;
            if(argument instanceof String){ str = (String) argument;  }
            else { str = JSON.toJSONString(argument); }
            return JSON.parseObject(str, new TypeReference<LinkedHashMap<?, ?>>(){});
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 获取注解Class
     * @param parameter 参数对象
     * @param supportClass 支持的类型 全限定名
     * @param nativeClass 原生自带的类型
     * @return 注解Class
     */
    private static Class<? extends Annotation> getAnnotationClass(Parameter parameter, String supportClass, Class<? extends Annotation> nativeClass){
        try {
            Class<? extends Annotation> requestHeader = (Class<? extends Annotation>) Class.forName(supportClass);
            boolean annotationPresent = parameter.isAnnotationPresent(requestHeader);
            if(annotationPresent){
                return requestHeader;
            }
        }catch (Exception ignored){ }
        return parameter.isAnnotationPresent(nativeClass) ? nativeClass : null;
    }
    private static Object invokeAnnotationMethod(Parameter parameter, Class<? extends Annotation> clazz, String methodName){
        try {
            Annotation annotation = parameter.getAnnotation(clazz);
            Method method = clazz.getMethod(methodName);
            return method.invoke(annotation);
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            return null;
        }
    }
    private static <T> T invokeAnnotationMethod(Parameter parameter, Class<? extends Annotation> clazz, String methodName, Class<T> returnType){
        try {
            Annotation annotation = parameter.getAnnotation(clazz);
            Method method = clazz.getMethod(methodName);
            return returnType.cast(method.invoke(annotation));
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            return null;
        }
    }
    private static String resolveValueAndName(Parameter parameter, Class<? extends Annotation> clazz){
        Object value = invokeAnnotationMethod(parameter, clazz, VALUE_METHOD);
        if(value != null && StringUtils.isNotBlank(value.toString())){
            return value.toString();
        }else {
            value = invokeAnnotationMethod(parameter, clazz, NAME_METHOD);
            return value != null && StringUtils.isNotBlank(value.toString()) ? value.toString() : parameter.getName();
        }
    }
    private static boolean searchClass(Class<?> source, Class<?> target){
        if(source == null || target == null){return false;}
        if(Objects.equals(source, target)){return true;}
        if(Object.class.equals(source)){return false;}
        for (Class<?> anInterface : source.getInterfaces()) {
            if(searchClass(anInterface, target)){return true;}
        }
        return searchClass(source.getSuperclass(), target);
    }

    private static boolean isPart(Object argument){
        return argument instanceof File || argument instanceof InputStream || argument instanceof InputStreamSource;
    }
}
