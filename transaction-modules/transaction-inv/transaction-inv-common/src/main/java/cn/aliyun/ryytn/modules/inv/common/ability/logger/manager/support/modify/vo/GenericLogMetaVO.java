package cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.vo;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import lombok.Data;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-20 14:40
 * @description
 */
@Data
public class GenericLogMetaVO {
    /**
     * 修改前的值
     */
    @MetaField("变更前")
    private List<String> beforeModify;
    /**
     * 修改后的值
     */
    @MetaField("变更后")
    private List<String> afterModify;
    /**
     * 修改原因
     */
    @MetaField("调整原因")
    private List<String> modifyReason;
    /**
     * 变更时间
     */
    @MetaField("变更时间")
    private String modifyTime;
    @MetaField("变更人")
    private String operatorName;
}
