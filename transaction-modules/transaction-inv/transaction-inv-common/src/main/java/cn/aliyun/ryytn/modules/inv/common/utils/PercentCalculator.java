package cn.aliyun.ryytn.modules.inv.common.utils;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-10-15 19:35
 * @description 百分比计算函数
 */
public final class PercentCalculator {
    private static final Integer MULTIPLY = 10000;

    public static List<Integer> calcPercent(List<BigDecimal> values) {
        if (null == values) {
            return null;
        }
        // 类型转换
        BigDecimal[] decimals = new BigDecimal[values.size()];
        for (int i = 0; i < values.size(); i++) {
            if (values.get(i) == null) {
                decimals[i] = BigDecimal.ZERO;
            } else {
                decimals[i] = values.get(i);
            }
        }
        // 计算总数
        BigDecimal total = sum(decimals);
        // 百分比总数 1
        BigDecimal ratioTotal = BigDecimal.ONE;
        BigDecimal[] result = new BigDecimal[decimals.length];
        int index = 0;
        LinkedHashMap<Integer, BigDecimal> reversedOrderlyMap = reverseOrderlyToMap(decimals);
        for (Map.Entry<Integer, BigDecimal> entry : reversedOrderlyMap.entrySet()) {
            if (index == decimals.length - 1) {
                result[entry.getKey()] = ratioTotal;
                break;
            }
            if (null != entry.getValue() && BigDecimal.ZERO.compareTo(total) != 0) {
                BigDecimal ratio = entry.getValue().divide(total, 4, RoundingMode.HALF_UP);
                ratioTotal = ratioTotal.subtract(ratio);
                // 处理减法计算后，总数出现负数的情况
                if (BigDecimal.ZERO.compareTo(ratioTotal) > 0) {
                    ratio = ratio.add(ratioTotal);
                    ratioTotal = BigDecimal.ZERO;
                }
                result[entry.getKey()] = ratio;
            } else {
                result[entry.getKey()] = BigDecimal.ZERO;
            }
            index++;
        }
        return Arrays.stream(result).map(PercentCalculator::mul).collect(Collectors.toList());
    }
    public static List<BigDecimal> splitValue(List<Integer> percent, BigDecimal value, Integer equallyCount) {
        if (null == value || BigDecimal.ZERO.equals(value)) {
            int size = 0;
            if (null != percent) {
                size = percent.size();
            } else if (null != equallyCount && equallyCount > 0) {
                size = equallyCount;
            }
            List<BigDecimal> result = new ArrayList<>(size);
            for (int i = 0; i < size; i++) {
                result.add(value);
            }
            return result;
        }
        // 没有百分比
        if (null == percent) {
            // 且没有默认平分数量，返回null
            if (null == equallyCount || equallyCount < 1) {
                return new ArrayList<>(Collections.singletonList(value));
            }
            // 均分
            return equallySplit(value, equallyCount);
        }
        // 非空判断
        BigDecimal[] ratios = new BigDecimal[percent.size()];
        for (int i = 0; i < percent.size(); i++) {
            if (percent.get(i) == null) {
                ratios[i] = BigDecimal.ZERO;
            } else {
                ratios[i] = div(percent.get(i));
            }
        }
        BigDecimal valueTotal = BigDecimal.ZERO;
        BigDecimal[] result = new BigDecimal[ratios.length];
        int index = 0;
        LinkedHashMap<Integer, BigDecimal> reversedOrderlyMap = reverseOrderlyToMap(ratios);
        for (Map.Entry<Integer, BigDecimal> entry : reversedOrderlyMap.entrySet()) {
            if (index == ratios.length - 1) {
                result[entry.getKey()] = value.subtract(valueTotal);
                break;
            }
            if (null != entry.getValue() || valueTotal.compareTo(value) == 0) {
                BigDecimal splitValue = value.multiply(entry.getValue()).setScale(0, RoundingMode.CEILING);
                valueTotal = valueTotal.add(splitValue);
                // 处理加法计算后，总数大于原value的情况
                if (valueTotal.compareTo(value) > 0) {
                    BigDecimal diff = valueTotal.subtract(value);
                    splitValue = splitValue.subtract(diff);
                    valueTotal = value;
                }
                result[entry.getKey()] = splitValue;
            } else {
                result[entry.getKey()] = BigDecimal.ZERO;
            }
            index++;
        }
        return Arrays.asList(result);
    }

    private static BigDecimal sum(BigDecimal[] decimals) {
        BigDecimal total = BigDecimal.ZERO;
        for (BigDecimal decimal : decimals) {
            if (null != decimal) {
                total = total.add(decimal);
            }
        }
        return total;
    }

    private static LinkedHashMap<Integer, BigDecimal> reverseOrderlyToMap(BigDecimal[] decimals) {
        LinkedHashMap<Integer, BigDecimal> orderlyMap = new LinkedHashMap<>();
        for (int i = 0; i < decimals.length; i++) {
            int maxIndex = 0;
            for (int j = 1; j < decimals.length; j++) {
                if (decimals[maxIndex].compareTo(decimals[j]) < 0) {
                    maxIndex = j;
                }
            }
            orderlyMap.put(maxIndex, decimals[maxIndex]);
            decimals[maxIndex] = BigDecimal.valueOf(-999999999999999999L);
        }
        return orderlyMap;
    }

    /**
     * 均分
     */
    private static List<BigDecimal> equallySplit(BigDecimal value, Integer defaultCount) {
        List<BigDecimal> result = new ArrayList<>();
        BigDecimal div = value.divide(BigDecimal.valueOf(defaultCount), 2, RoundingMode.HALF_UP);
        BigDecimal tempTotal = BigDecimal.ZERO;
        for (int i = 0; i < defaultCount; i++) {
            result.add(div);
            tempTotal = tempTotal.add(div);
        }
        BigDecimal diff = value.subtract(tempTotal);
        result.set(0, result.get(0).add(diff));
        return result;
    }

    private static Integer mul(BigDecimal value) {
        return value.multiply(BigDecimal.valueOf(MULTIPLY)).intValue();
    }

    private static BigDecimal div(Integer value) {
        return BigDecimal.valueOf(value).divide(BigDecimal.valueOf(MULTIPLY), 4, RoundingMode.HALF_UP);
    }
}