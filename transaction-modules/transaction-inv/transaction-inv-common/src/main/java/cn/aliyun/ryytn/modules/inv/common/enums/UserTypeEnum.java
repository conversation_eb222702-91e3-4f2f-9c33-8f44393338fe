package cn.aliyun.ryytn.modules.inv.common.enums;

/**
 * 用户类型枚举
 *
 * <AUTHOR>
 * @version $Id: UserTypeEnum, v 0.1 2023/8/4 11:27 daoqi Exp $$
 */
public enum UserTypeEnum {

    /** 管理员 */
    ADMINISTRATOR("ADMINISTRATOR", "管理员"),
    /** 销售人员 */
    SALES_MAN("SALES_MAN", "销售人员"),
    /** 计划人员 */
    PLANNER("PLANNER", "计划人员"),
    /** 未知 */
    UNKNOWN("UNKNOWN", "未知");

    /** 编码 */
    private String code;

    /** 中文描述 */
    private String desc;


    /**
     * @param code
     * @param desc
     */
    UserTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过编码获取对应的枚举
     *
     * @param code
     * @return
     */
    public static UserTypeEnum getByCode(String code) {
        for (UserTypeEnum type : UserTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return UNKNOWN;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }


    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of desc
     */
    public String getDesc() {
        return desc;
    }
}
