package cn.aliyun.ryytn.modules.inv.common.ability.constant;

import cn.aliyun.ryytn.modules.inv.common.ability.constant.service.ConstantDTO;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.service.ConstantQueryRequest;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.service.ConstantService;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-10-23 14:16
 * @description
 */
public class ConstantFactory {
    @Setter
    @Accessors(chain = true)
    private String type;
    @Setter
    @Accessors(chain = true)
    private String blur;
    @Setter
    @Accessors(chain = true)
    private List<String> code;
    private final ConstantService constantService;

    private List<ConstantDTO> cache;
    private Long cacheTime;
    private final long maxCacheTime;

    ConstantFactory(InjectConstant injectConstant, ConstantService constantService){
        this.type = injectConstant.type();
        this.blur = injectConstant.blur();
        this.maxCacheTime = injectConstant.maxCacheTime();
        this.code = Arrays.asList(injectConstant.code());
        this.constantService = constantService;
    }
    public List<ConstantDTO> get(){
        long now = System.currentTimeMillis();
        if(isExpired(now)) {
            synchronized (ConstantFactory.class) {
                if(isExpired(now)) {
                    ConstantQueryRequest request = new ConstantQueryRequest();
                    request.setCode(code);
                    request.setType(type);
                    request.setBlur(blur);
                    cache = constantService.queryData(request);
                    cacheTime = now;
                }
            }
        }
        return cache;
    }
    public Map<String, ConstantDTO> getMap(){
        Map<String, ConstantDTO> result = new HashMap<>();
        List<ConstantDTO> list = get();
        getMap(result, list, ConstantDTO::getCode);
        return result;
    }
    public ConstantDTO getConstant(String key){
        return getMap().get(key);
    }

    public Map<String, ConstantDTO> getMapByCodeName(){
        Map<String, ConstantDTO> result = new HashMap<>();
        List<ConstantDTO> list = get();
        getMap(result, list, dto -> dto.getCode() + dto.getName());
        return result;
    }
    public Map<String, ConstantDTO> getMapByName(){
        Map<String, ConstantDTO> result = new HashMap<>();
        List<ConstantDTO> list = get();
        getMap(result, list, ConstantDTO::getName);
        return result;
    }
    private void getMap(Map<String, ConstantDTO> result, List<ConstantDTO> list, Function<ConstantDTO, String> groupKey){
        for (ConstantDTO constantDTO : list) {
            if(Objects.nonNull(constantDTO)){
                result.put(groupKey.apply(constantDTO), constantDTO);
                if(CollectionUtils.isNotEmpty(constantDTO.getChildren())){
                    getMap(result, constantDTO.getChildren(), groupKey);
                }
            }
        }
    }


    private boolean isExpired(long now){
        return maxCacheTime <= 0 || Objects.isNull(cache) || Objects.isNull(cacheTime) || (now - cacheTime) > maxCacheTime;
    }

}
