package cn.aliyun.ryytn.modules.inv.common.helper;

import cn.aliyun.ryytn.modules.inv.common.enums.UserTypeEnum;
import cn.aliyun.ryytn.modules.inv.common.helper.context.CnUserInfoUtil;
import cn.aliyun.ryytn.modules.inv.common.model.LoginUserDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.List;

/**
 * 用户信息上下文ThreadLocal
 *
 * <AUTHOR>
 * @version $Id: UserContextHolder, v 0.1 2021-08-17 15:09 liupingping.lp Exp $$
 */
public class UserContextHolder {

    /** 日志 */
    private static final Logger LOGGER = LoggerFactory.getLogger(UserContextHolder.class);

    /** 开发环境对应的spring.profiles value值 */
    private static final String SPRING_PROFILES_DEV_MODE_KEY = "dev";

    /** 线上环境对应的spring.profiles value值 */
    private static final String SPRING_PROFILES_PRODUCTION_MODE_KEY = "production";

    /**
     * 获取当前用户信息
     * @return
     */
    public static LoginUserDTO getLoginUser() {
        // 获取登陆信息，若已登录，则直接返回登陆用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication != null && authentication.getPrincipal() != null && (!"anonymousUser".equals(authentication.getPrincipal()))) {
            return (LoginUserDTO) authentication.getPrincipal();
        }

        return null;
    }

    public static void changeTenant(String code){
        LoginUserDTO loginUser = getLoginUser();
        loginUser.setTenantCode(code);

        SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
        Authentication authentication = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
        securityContext.setAuthentication(authentication);
        SecurityContextHolder.setContext(securityContext);
    }

    /**
     * 获取当前登录用户工号
     *
     * @return
     */
    public static String getLoginUserId() {
        LoginUserDTO loginUser = getLoginUser();
        if (loginUser == null) {
            return null;
        }
        return loginUser.getUserId();
    }

    public static UserTypeEnum getLoginUserType() {
        LoginUserDTO loginUser = getLoginUser();
        if (loginUser != null) {
            return loginUser.getUserTypeEnum();
        }
        return null;
    }


    public static String getTenantCode() {
        /*LoginUserDTO loginUser = getLoginUser();
        Assert.notNull(loginUser, ScpErrorCode.NOT_LOGIN,"需要先登录才能获取租户信息");
        return loginUser.getTenantCode();*/
        return getCnTenant();
    }

    public static String getUsername() {
        /*LoginUserDTO loginUser = getLoginUser();
        Assert.notNull(loginUser, ScpErrorCode.NOT_LOGIN, "需要先登录才能获取租户信息");
        return loginUser.getUsername();*/
        return getCnUserName();
    }



    /**
     * 判断是否为开发环境
     *
     * @return
     */
    private static boolean inDevMode() {
        String activeProfile = SpringContextHolder.getActiveProfile();
        return SPRING_PROFILES_DEV_MODE_KEY.equals(activeProfile);
    }

    /**
     * 返回测试环境专用的登陆用户
     * @return
     */
    public static LoginUserDTO getMockLoginUser() {
        LoginUserDTO mockLoginUser = new LoginUserDTO();
        mockLoginUser.setUserId("emp001");
        mockLoginUser.setPassword("xxx");
        mockLoginUser.setUserTypeEnum(UserTypeEnum.PLANNER);
        mockLoginUser.setAccountNonExpired(true);
        mockLoginUser.setAccountNonLocked(true);
        mockLoginUser.setCredentialsNonExpired(true);
        mockLoginUser.setEnabled(true);

        List<GrantedAuthority> authorityList = AuthorityUtils.createAuthorityList();

        mockLoginUser.setAuthorities(authorityList);
        return mockLoginUser;
    }

    /**
     * 获取Cn当前用户信息
     * @return
     */
    public static LoginUserDTO getCnLoginUser() {
        return CnUserInfoUtil.getLoginContext();
    }
    /**
     * 获取当前用户信息
     * 兼系统嵌入登录态
     * @return
     */
    public static String getCnUserId() {
        LoginUserDTO loginUser = getLoginUser();
        if(loginUser != null && StringUtils.isNotBlank(loginUser.getUserId())){
            return loginUser.getUserId();
        }
        LoginUserDTO cnLoginUser = getCnLoginUser();
        return cnLoginUser.getUserId();
    }
    /**
     * 获取当前用户信息
     * 兼系统嵌入登录态
     * @return
     */
    public static String getCnUserName() {
        LoginUserDTO loginUser = getLoginUser();
        if(loginUser != null && StringUtils.isNotBlank(loginUser.getUsername())){
            return loginUser.getUsername();
        }
        LoginUserDTO cnLoginUser = getCnLoginUser();
        return cnLoginUser.getUserNickname();
    }
    /**
     * 获取当前用户信息
     * 兼系统嵌入登录态
     * @return
     */
    public static String getCnTenant() {
        LoginUserDTO loginUser = getLoginUser();
        if(loginUser != null && StringUtils.isNotBlank(loginUser.getTenantCode())){
            return loginUser.getTenantCode();
        }
        LoginUserDTO cnLoginUser = getCnLoginUser();
        return cnLoginUser.getTenantCode();
    }

    /**
     * 获取当前用户信息
     * 兼系统嵌入登录态
     * @return
     */
    public static <T> T getCnAddition(String key, Class<T> resultType) {
        LoginUserDTO cnLoginUser = getCnLoginUser();
        return cnLoginUser.getAddition(key, resultType);
    }
}
