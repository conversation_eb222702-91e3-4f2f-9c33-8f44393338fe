package cn.aliyun.ryytn.modules.inv.common.dao.common.enums;

/**
 * <AUTHOR> zheng han
 * @Description
 * @Date 2024/5/31 14:01
 */
public enum DeleteTypeEnum {

    /**
     * 删除标识：1-已删除，0-未删除
     */
    DELETE_FLAG(1, "已删除"),
    NOT_DELETE_FLAG(0, "未删除");
    private final Integer code;
    private final String name;

    DeleteTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
