package cn.aliyun.ryytn.modules.inv.common.service;

import cn.aliyun.ryytn.modules.inv.common.dao.common.dataobject.CommonTenantDO;
import cn.aliyun.ryytn.modules.inv.common.model.CommonTenantDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【scp_common_tenant(租户表)】的数据库操作Service
* @createDate 2023-08-10 19:53:59
*/
public interface CommonTenantService extends IService<CommonTenantDO> {
    /**
     * 租户列表
     * @return
     */
    List<CommonTenantDTO> listAll();

}
