package cn.aliyun.ryytn.modules.inv.common.ability.split.monitor;

import cn.aliyun.ryytn.modules.inv.common.ability.split.SplitConfig;
import cn.aliyun.ryytn.modules.inv.common.ability.split.manager.SplitIndexManager;
import cn.aliyun.ryytn.modules.inv.common.ability.split.manager.dto.SplitFieldConfig;
import cn.aliyun.ryytn.modules.inv.common.ability.split.manager.dto.SplitIndexCombine;
import cn.aliyun.ryytn.modules.inv.common.utils.StringConstants;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-08-21 19:41
 * @description 分割字段监控器
 */
@Component
public class SplitDynamicMonitor {
    private static SplitIndexManager splitIndexManager;
    @Resource
    public void inject(SplitIndexManager splitIndexManager) {
        SplitDynamicMonitor.splitIndexManager = splitIndexManager;
    }
    /**
     * 监控上下文
     * 用于注册 监听field -> 监听处理器实体的映射关系
     */
    private static final ThreadLocal<Map<SplitFieldConfig, SplitMonitorContext>> MONITOR_CONTAINER = ThreadLocal.withInitial(() -> null);
    /**
     * 可继承的上下文
     */
    private static final InheritableThreadLocal<Map<SplitFieldConfig, SplitMonitorContext>> INHERITABLE_CONTAINER = new InheritableThreadLocal<>();


    private static final InheritableThreadLocal<Consumer<Map<String, Object>>> PROCESSOR_CONTAINER = new InheritableThreadLocal<>();


    /**
     * 开启监听
     * @param indexCode 指标code
     * @param handler 监听处理器
     */
    public static void start(String indexCode, String fieldName, SplitFieldMonitorHandler handler) {
        if(Objects.isNull(indexCode) || Objects.isNull(fieldName) || Objects.isNull(handler)){
            throw new IllegalArgumentException("DataClass or field or handler is null");
        }
        SplitIndexCombine splitIndex = splitIndexManager.getByCode(indexCode);
        SplitFieldConfig splitField = null;
        for (SplitFieldConfig field : splitIndex.getFields()) {
            if (StringUtils.equals(field.getFieldCode(), fieldName)) {
                splitField = field;
                break;
            }
        }
        if(Objects.isNull(splitField)) {
            throw new IllegalArgumentException("Monitor field is not found");
        }
        SplitMonitorContext splitMonitorContext = initialContext(splitField);
        if(Objects.isNull(splitMonitorContext)){
            throw new UnknownError("Unknown Error");
        }
        splitMonitorContext.setHandler(handler);
    }

    public static void afterProcessor(Consumer<Map<String, Object>> processor) {
        if (Objects.nonNull(processor)) {
            PROCESSOR_CONTAINER.set(processor);
        }
    }
    public static void advice(Map<String, Object> splitRow) {
        Consumer<Map<String, Object>> processor = PROCESSOR_CONTAINER.get();
        if (Objects.nonNull(processor)) {
            processor.accept(splitRow);
        }
    }

    /**
     * 进行监听并通知回调处理器
     * @param row 当前行数据
     * @param field 当前行所属的字段
     */
    public static <T> void advice(Map<String, Object> row, SplitFieldConfig field, List<SplitConfig<T>> dynamicConfigs){
        Map<SplitFieldConfig, SplitMonitorContext> monitorContainer = getContainer();
        if(Objects.isNull(monitorContainer)){ return; }
        // step1: 推送至依赖自身的目标field中
        monitorContainer.forEach((k, v) -> {
            if (v.getDepend() != null && v.getDepend().contains(field.getFieldCode())) {
                v.addDependData(field.getFieldCode(), row);
                execHandler(k, v.getCurrentData(), dynamicConfigs);
            }
        });
        // step2: 再判断自身是否预备完成
        execHandler(field, row, dynamicConfigs);
    }

    /**
     * 退出监听，实际只是清除当前线程上下文
     */
    public static void close(){
        Map<SplitFieldConfig, SplitMonitorContext> monitor = getContainer();
        if(Objects.nonNull(monitor)){
            MONITOR_CONTAINER.remove();
            INHERITABLE_CONTAINER.remove();
            monitor.forEach((k, v) -> v.clear() );
            monitor.clear();
        }
        PROCESSOR_CONTAINER.remove();
    }
    private static <T> void execHandler(SplitFieldConfig field, Map<String, Object> row, List<SplitConfig<T>> configs){
        SplitMonitorContext context = getContextByField(field);
        if(Objects.isNull(context)){ return; }
        if(context.isReady()){
            if(Objects.isNull(context.getCurrentData())){ context.setCurrentData(row); }
            //List<SplitConfig<?>> configs = JsonUtils.toObject(dynamicConfigs, new TypeReference<List<SplitConfig<?>>>(){});
            context.setDynamicConfigs(configs);
            SplitFieldMonitorHandler handler = context.getHandler();
            try {
                handler.accept(context);
                context.acceptAfter();
            }catch (Exception e){
                close();
                throw e;
            }
        }else if(Objects.nonNull(row)){
            context.setCurrentData(row);
        }
    }

    private static <T> SplitMonitorContext initialContext(SplitFieldConfig field){
        Map<SplitFieldConfig, SplitMonitorContext> monitorContainer = getContainer();
        if(Objects.isNull(monitorContainer)){
            monitorContainer = Maps.newHashMap();
            MONITOR_CONTAINER.set(monitorContainer);
            INHERITABLE_CONTAINER.set(monitorContainer);
        }
        return monitorContainer.getOrDefault(field, createSplitFieldMonitor(field, monitorContainer));
    }
    private static <T> SplitMonitorContext createSplitFieldMonitor(SplitFieldConfig field, Map<SplitFieldConfig, SplitMonitorContext> monitorContainer){
        SplitMonitorContext context = new SplitMonitorContext();
        String[] depends = StringUtils.split(field.getDepends(), StringConstants.COMMA);
        if(Objects.nonNull(depends)) {
            for (String fieldName : depends) {
                context.addDepend(fieldName);
            }
        }
        return monitorContainer.put(field, context);
    }

    private static Map<SplitFieldConfig, SplitMonitorContext> getContainer(){
        Map<SplitFieldConfig, SplitMonitorContext> primaryContainer = MONITOR_CONTAINER.get();
        if(Objects.nonNull(primaryContainer)){
            return primaryContainer;
        }
        Map<SplitFieldConfig, SplitMonitorContext> parentContainer = INHERITABLE_CONTAINER.get();
        if(Objects.nonNull(parentContainer)){
            Map<SplitFieldConfig, SplitMonitorContext> cloneContainer = Maps.newHashMap();
            parentContainer.forEach((k, source) -> {
                cloneContainer.put(k, source.copy());
            });
            MONITOR_CONTAINER.set(cloneContainer);
            return cloneContainer;
        }
        return null;
    }

    private static SplitMonitorContext getContextByField(SplitFieldConfig field){
        Map<SplitFieldConfig, SplitMonitorContext> contextMap = getContainer();
        if(Objects.isNull(contextMap)){
            throw new IllegalStateException("Monitor mode is not enabled");
        }
        return contextMap.get(field);
    }
}
