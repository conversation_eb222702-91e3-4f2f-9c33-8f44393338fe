package cn.aliyun.ryytn.modules.inv.common.ability.logger.aspect;

import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.DeleteLogger;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.QueryLogger;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.DataLoggerManager;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.model.DataLogWrapper;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-14 22:00
 * @description
 */
@Aspect
@Component
public class QueryLoggerAspect {
    @Resource
    private DataLoggerManager dataLoggerManager;

    protected static QueryLogger getQueryLogger(ProceedingJoinPoint invocation) {
        MethodSignature signature = (MethodSignature) invocation.getSignature();
        return signature.getMethod().getAnnotation(QueryLogger.class);
    }
    protected static DeleteLogger getDeleteLogger(ProceedingJoinPoint invocation) {
        MethodSignature signature = (MethodSignature) invocation.getSignature();
        return signature.getMethod().getAnnotation(DeleteLogger.class);
    }

    protected static DataLogWrapper resolveQueryLogWrapper(QueryLogger queryLogger, ProceedingJoinPoint invocation) {
        MethodSignature signature = (MethodSignature) invocation.getSignature();
        if (StringUtils.isBlank(queryLogger.bizCode())) {
            return null;
        }
        Object[] rawArgs = invocation.getArgs();
        if (rawArgs == null || rawArgs.length < 1) {
            return null;
        }
        return DataLogWrapper.of(signature.getMethod(), rawArgs, queryLogger)
                .setTriggerObject(invocation.getTarget()).setTriggerClass(invocation.getTarget().getClass());
    }
    protected static DataLogWrapper resolveDeleteLogWrapper(DeleteLogger deleteLogger, ProceedingJoinPoint invocation) {
        MethodSignature signature = (MethodSignature) invocation.getSignature();
        if (StringUtils.isBlank(deleteLogger.bizCode())) {
            return null;
        }
        Object[] rawArgs = invocation.getArgs();
        if (rawArgs == null || rawArgs.length < 1) {
            return null;
        }
        return DataLogWrapper.of(signature.getMethod(), rawArgs, deleteLogger)
                .setTriggerObject(invocation.getTarget()).setTriggerClass(invocation.getTarget().getClass());
    }

    @Pointcut("@annotation(cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.QueryLogger)")
    public void pointcut() {
    }
    @Pointcut("@annotation(cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.DeleteLogger)")
    public void pointcutDelete() {
    }

    @Around("pointcut()")
    public Object pointcut(ProceedingJoinPoint invocation) throws Throwable {
        QueryLogger queryLogger = getQueryLogger(invocation);
        DataLogWrapper wrapper = resolveQueryLogWrapper(queryLogger, invocation);
        if (Objects.nonNull(wrapper)) {
            if (Long.class.equals(wrapper.getMethod().getReturnType())) {
                return dataLoggerManager.queryCount(wrapper);
            }
            return dataLoggerManager.query(wrapper);
        }
        return invocation.proceed();
    }

    @Around("pointcutDelete()")
    public Object aroundDelete(ProceedingJoinPoint invocation) throws Throwable {
        DeleteLogger deleteLogger = getDeleteLogger(invocation);
        Object proceed = invocation.proceed();
        DataLogWrapper wrapper = resolveDeleteLogWrapper(deleteLogger, invocation);
        if (proceed instanceof Boolean && Boolean.FALSE.equals(proceed)) {
            return Boolean.FALSE;
        } else if (proceed instanceof Number && ((Number) proceed).doubleValue() < 1 ) {
            return proceed;
        }
        if (Objects.nonNull(wrapper)) {
            dataLoggerManager.delete(wrapper);
        }
        return proceed;
    }
}
