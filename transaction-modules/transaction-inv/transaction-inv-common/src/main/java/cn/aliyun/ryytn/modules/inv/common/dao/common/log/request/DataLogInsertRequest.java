package cn.aliyun.ryytn.modules.inv.common.dao.common.log.request;

import lombok.Data;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-11 16:12
 * @description 数据修改日志分页insert请求体
 * 这里dao层限制查询类型，避免业务层对参数疑义
 */
@Data
public class DataLogInsertRequest {
    /**
     * 表名后缀
     */
    private String suffix;
    /**
     * 业务数据各自的主键内容，
     * 例如分天比应该: 物料编码 + 仓编码作为主键内容
     */
    private String bizPk;
    /**
     * '业务编码'
     */
    private String bizCode;
    /**
     * 日志记录内容，json
     */
    private String bizInfo;
    /**
     * '操作人编码'
     */
    private String operatorCode;
    /**
     * '操作人名称'
     */
    private String operatorName;
    /**
     * '租户'
     */
    private String tenantCode;
}
