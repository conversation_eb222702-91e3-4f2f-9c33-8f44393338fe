package cn.aliyun.ryytn.modules.inv.common.utils;

import cn.aliyun.ryytn.modules.inv.common.exception.Assert;
import cn.aliyun.ryytn.modules.inv.common.exception.ErrorCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;

/**
 * date相关工具方法
 *
 * <AUTHOR>
 * @date 2023-06-13 15:30
 */
public class DateUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    /**
     * 默认的日期格式
     */
    public static final String DEFAULT_DATE_FORMAT = "yyyyMMdd";

    /**
     * yyyyMM格式
     */
    public static final String DATE_FORMAT_MONTH = "yyyyMM";

    /**
     * yyyy-MM格式
     */
    public static final String STANDARD_MONTH_FORMAT = "yyyy-MM";
    public static final String STANDARD_MONTH_DAY_FORMAT = "MM-dd";
    public static final String STANDARD_MONTH_DAY_HH_MM_FORMAT = "MM-dd HH:mm";
    public static final String DOT_MONTH_DAY_FORMAT = "MM.dd";
    /**
     * 标准日期格式
     */
    public static final String STANDARD_DATE_FORMAT = "yyyy-MM-dd";
    public static final String EXCEL_STANDARD_DATE_FORMAT = "yyyy/MM/dd";

    public static final String HTML_STANDARD_DATE_FORMAT = "YYYY-MM-DD";
    public static final String HTML_MINUTE_DATE_FORMAT = "YYYY-MM-DD HH:mm";
    public static final String HTML_SECOND_DATE_FORMAT = "YYYY-MM-DD HH:mm:ss";

    /**
     * yyyyMMddHH
     */
    public static final String DATE_HOUR_FORMAT = "yyyyMMddHH";

    /**
     * excel日期格式
     */
    public static final String EXCEL_DATE_FORMAT = "yyyy/M/d";
    /**
     * 时间格式，到小时-分钟
     */
    public static final String MINUTE_DATE_FORMAT = "yyyy-MM-dd HH:mm";
    /**
     * 默认的时间格式
     */
    public static final String DEFAULT_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DEFAULT_DATETIME_INT_FORMAT = "yyyyMMddHHmmss";

    public static final String IPART_DATETIME_FORMAT = "yyyy/MM/dd HH:mm:ss";

    public static final String DATE_END_FORMAT = "yyyy-MM-dd 23:59:59";

    /**
     * 月-日 日期格式
     */
    public static final String DATE_FORMAT_MM_DD = "MM-dd";

    /**
     * 月.日 日期格式
     */
    public static final String DATE_FORMAT_MM_DOT_DD = "MM.dd";
    /**
     * 默认的时间格式
     */
    public static final String DEFAULT_TIME_FORMAT = "HH:mm:ss";

    /**
     * 时间格式，到小时-分钟，不包括天
     */
    public static final String MINUTE_TIME_FORMAT = "HH:mm";
    public static final String HOURS_FORMAT = "HH";
    public static final String DATE_HOURS_FORMAT = "yyyy-MM-dd HH";

    public static final String[] CHINESE_WEEK_DAY = {"一", "二", "三", "四", "五", "六", "日"};

    public static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 获取指定日期之前的日期
     *
     * @param daysAgo
     * @param format
     * @return
     */
    public static String getDateByDaysAgo(int daysAgo, String format) {
        LocalDateTime date = LocalDateTime.now();
        return localDateTimeToString(date.plusDays(daysAgo), format);
    }

    /**
     * 获取指定日期之前的日期
     *
     * @param daysAgo
     * @param format
     * @return
     */
    public static String getDateByDaysAgo(int daysAgo, String dateStr, String format) {
        LocalDate localDate = stringToLocalDate(dateStr, format);
        return localDateToString(localDate.plusDays(daysAgo), format);
    }

    /**
     * 获取指定日期之前的周
     *
     * @param weeksAgo
     * @param format
     * @return
     */
    public static String getWeekByWeeksAgo(int weeksAgo, String format) {
        LocalDate date = LocalDate.now().plusDays(weeksAgo * 7);
        String weekStartDay = localDateTimeToString(weekStartTime(date), DateUtil.DEFAULT_DATE_FORMAT);
        String weekEndDay = localDateTimeToString(weekEndTime(date), DateUtil.DEFAULT_DATE_FORMAT);
        return weekStartDay + "-" + weekEndDay;
    }

    /**
     * 获取当天日期
     *
     * @param format
     * @return
     */
    public static String today(String format) {
        return localDateTimeToString(LocalDateTime.now(), format);
    }

    /**
     * 获取当天日期
     *
     * @param format
     * @return
     */
    public static String yesterday(String format) {
        return localDateTimeToString(LocalDateTime.now().minusDays(1L), format);
    }

    public static long betweenDay(LocalDate startDate, LocalDate endDate) {
        // 获取具体日期间隔日
        return ChronoUnit.DAYS.between(startDate, endDate);
    }

    /**
     * 计算当前周是当年的第几周
     *
     * @param weeksAgo
     * @return
     */
    public static int getWeekOfYear(int weeksAgo) {
        LocalDate date = LocalDate.now().plusDays(weeksAgo * 7);
        return date.get(WeekFields.ISO.weekOfYear());
    }

    /**
     * 根据周日期范围获取周属于当前年份多少周
     *
     * @param weekRange 周日期范围 yyyyMMdd-yyyyMMdd
     * @return
     */
    public static int getWeekOfYear(String weekRange) {
        String[] dateArr = weekRange.split("-");
        Assert.isTrue(dateArr.length == 2, ErrorCode.ILLEGAL_ARGUMENT, "weekRange={}", weekRange);
        LocalDate localDate = DateUtil.stringToLocalDate(dateArr[0], DateUtil.DEFAULT_DATE_FORMAT);
        return localDate.get(WeekFields.ISO.weekOfYear());
    }

    /**
     * 根据周日期范围获取周属于当前年份多少周
     *
     * @param weekRange 周日期范围 yyyyMMdd-yyyyMMdd
     * @return
     */
    public static String getWeekOfYearMonth(String weekRange) {
        String[] dateArr = weekRange.split("-");
        Assert.isTrue(dateArr.length == 2, ErrorCode.ILLEGAL_ARGUMENT, "weekRange={}", weekRange);
        LocalDate localDate = DateUtil.stringToLocalDate(dateArr[1], DateUtil.DEFAULT_DATE_FORMAT);

        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);
        Date date = Date.from(zdt.toInstant());

        return getWeeksInMonthOfDate(date);
    }

    /**
     * 获取指定日期-XXXX年X月第X周
     * TODO 待确认逻辑，每月第几周的计算跟之前想的有偏差
     *
     * @param date
     * @return
     */
    public static String getWeeksInMonthOfDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //设置每周第一天为周一 默认每周第一天为周日
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        //获取当前日期所在周周日
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        return String.valueOf(calendar.get(Calendar.YEAR)).concat("年").
            concat(String.valueOf(calendar.get(Calendar.MONTH) + 1)).concat("月第").
            concat(String.valueOf(calendar.get(Calendar.WEEK_OF_MONTH))).concat("周");
    }

    /**
     * 获取后一天
     *
     * @param date 当前日期，yyyyMMdd
     * @return
     */
    public static String getNextDay(String date) {
        LocalDate localDate = DateUtil.stringToLocalDate(date, DEFAULT_DATE_FORMAT);
        LocalDate nextDate = localDate.plusDays(1);
        return DateUtil.localDateToString(nextDate, DateUtil.DEFAULT_DATE_FORMAT);
    }

    /**
     * 获取后一周
     *
     * @param week 当前周，yyyyMMdd-yyyyMMdd
     * @return
     */
    public static String getNextWeek(String week) {
        String[] dateArr = week.split("-");
        Assert.isTrue(dateArr.length == 2, ErrorCode.ILLEGAL_ARGUMENT, "weekRange={}", week);

        LocalDate weekStartDate = DateUtil.stringToLocalDate(dateArr[0], DEFAULT_DATE_FORMAT);
        LocalDate weekEndDate = DateUtil.stringToLocalDate(dateArr[1], DEFAULT_DATE_FORMAT);
        LocalDate nextStartDate = weekStartDate.plusDays(7);
        LocalDate nextEndDate = weekEndDate.plusDays(7);
        return localDateToString(nextStartDate, DEFAULT_DATE_FORMAT) + "-" + localDateToString(nextEndDate,
            DEFAULT_DATE_FORMAT);
    }

    /**
     * 获取后一月
     *
     * @param month 当前月 yyyyMM
     * @return
     */
    public static String getNextMonth(String month) {
        LocalDate localDate = DateUtil.stringToLocalDate(month + "01", DateUtil.DEFAULT_DATE_FORMAT);
        LocalDate nextDate = localDate.plusMonths(1);
        return localDateToString(nextDate, "yyyyMM");
    }

    /**
     * 获取月份的天数
     *
     * @param month
     * @return
     */
    public static Integer getLengthOfMonth(String month) {
        LocalDate localDate;
        if (month.length() == 6) {
            localDate = DateUtil.stringToLocalDate(month + "01", DateUtil.DEFAULT_DATE_FORMAT);
        } else {
            localDate = DateUtil.stringToLocalDate(month, DateUtil.DEFAULT_DATE_FORMAT);
        }
        return localDate != null ? localDate.lengthOfMonth() : null;
    }

    /**
     * 获取指定日期之前的周
     *
     * @param monthsAgo
     * @param format
     * @return
     */
    public static String getMonthByMonthsAgo(int monthsAgo, String format) {
        LocalDateTime date = LocalDateTime.now();
        return localDateTimeToString(date.plusMonths(monthsAgo), format);
    }

    public static String getMonthByMonthsAgo(LocalDate date, int monthsAgo, String format) {
        return localDateToString(date.plusMonths(monthsAgo), format);
    }

    public static String getMonthByMonthsAgo(int monthsAgo, String dateStr, String format) {
        LocalDate localDate;
        if (StringUtils.length(dateStr) == 6 && StringUtils.equals(format, DateUtil.DATE_FORMAT_MONTH)) {
            // yyyyMM格式，不能直接转换为localDate，需加上dd
            localDate = DateUtil.stringToLocalDate(dateStr + "01", DateUtil.DEFAULT_DATE_FORMAT);
        } else {
            localDate = DateUtil.stringToLocalDate(dateStr, DateUtil.DEFAULT_DATE_FORMAT);
        }
        return localDateToString(localDate.plusMonths(monthsAgo), format);
    }

    /**
     * @param dateTime
     * @param dayOfWeek
     * @return
     */
    public static LocalDate getWeekDay(LocalDateTime dateTime, Integer dayOfWeek) {
        // 先获取周一
        return dateTime.minusDays(dateTime.getDayOfWeek().getValue() - 1)
            // 再获取dayOfWeek所在天
            .plusDays(dayOfWeek - 1).toLocalDate();
    }

    /**
     * Date -> 字符串
     *
     * @param date   日期
     * @param format 格式
     * @return 时间字符串
     */
    public static String dateToString(Date date, String format) {
        if (date == null || format == null) {
            return null;
        }
        SimpleDateFormat f = new SimpleDateFormat(format);
        return f.format(date);
    }

    /**
     * 时间戳转换为日期时间
     *
     * @param timeMillis
     * @return
     */
    public static LocalDateTime timeMillisToLocalDateTime(Long timeMillis) {
        return Instant.ofEpochMilli(timeMillis)
            .atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
    }

    /**
     * 时间 -> 字符串
     *
     * @param time   时间
     * @param format 格式
     * @return 字符串
     */
    public static String localDateTimeToString(LocalDateTime time, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return null == time ? null : time.format(formatter);
    }

    /**
     * 字符串 -> 时间
     *
     * @param timeStr 字符串
     * @param format  格式
     * @return LocalDateTime
     */
    public static LocalDateTime stringToLocalDateTime(String timeStr, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return StringUtils.isEmpty(timeStr) ? null : LocalDateTime.parse(timeStr, formatter);
    }

    /**
     * LocalDate -> 字符串
     *
     * @param localDate 日期
     * @param format    格式
     * @return 字符串
     */
    public static String localDateToString(LocalDate localDate, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return null == localDate ? null : localDate.format(formatter);
    }

    /**
     * LocalDate -> 字符串
     *
     * @param localDate 日期
     * @return 字符串
     */
    public static String localDateToString(LocalDate localDate) {
        return localDateToString(localDate, STANDARD_DATE_FORMAT);
    }

    /**
     * 字符串 -> LocalDate
     *
     * @param dateStr 字符串
     * @param format  格式
     * @return LocalDate
     */
    public static LocalDate stringToLocalDate(String dateStr, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        LocalDate date = null;
        try {
            date = StringUtils.isEmpty(dateStr) ? null : LocalDate.parse(dateStr, formatter);
        } catch (DateTimeParseException e) {
        }
        return date;
    }

    /**
     * date -> LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate dateToLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * LocalDate -> date
     *
     * @param localDate
     * @return
     */
    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zoneId = ZoneId.systemDefault();
        return Date.from(localDate.atStartOfDay().atZone(zoneId).toInstant());
    }

    public static LocalDateTime localDateToLocalDateTime(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.MIN);
    }
    public static LocalDate localDateTimeToLocalDate(LocalDateTime date) {
        return date.toLocalDate();
    }

    /**
     * 字符串 -> LocalDate
     *
     * @param dateStr 字符串
     * @return LocalDate
     */
    public static LocalDate stringToLocalDate(String dateStr) {
        return stringToLocalDate(dateStr, STANDARD_DATE_FORMAT);
    }

    /**
     * 字符串 -> LocalTime
     *
     * @param dateStr 字符串
     * @return LocalDate
     */
    public static LocalTime stringToLocalTime(String dateStr) {
        return stringToLocalTime(dateStr, DEFAULT_TIME_FORMAT);
    }
    /**
     * 字符串 -> LocalTime
     *
     * @param dateStr 字符串
     * @return LocalDate
     */
    public static LocalTime stringToLocalTime(String dateStr, String format) {
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
            return LocalTime.parse(dateStr, dateTimeFormatter);
        } catch (Exception ignored) {
        }
        return null;
    }

    /**
     * 字符串 -> Date
     *
     * @param dateStr 字符串
     * @return Date
     */
    public static Date stringToDate(String dateStr) {
        LocalDate localDate = stringToLocalDate(dateStr, DEFAULT_DATE_FORMAT);
        return localDateToDate(localDate);
    }

    /**
     * Date -> LocalDateTime
     *
     * @param dateToConvert date类型的时间
     * @return LocalDateTime
     */
    public static LocalDateTime toLocalDateTime(Date dateToConvert) {
        return LocalDateTime.ofInstant(dateToConvert.toInstant(),
            ZoneId.systemDefault());
    }

    /**
     * 获取偏移日期列表(不包含周末)
     *
     * @param dateFrom 起始日期
     * @param offset   偏移量，大于0的值
     * @return 偏移日期列表(包含起始日期)
     */
    public static List<LocalDate> offsetDates(LocalDate dateFrom, int offset) {
        List<LocalDate> offsetDates = new ArrayList<>();
        for (int i = 0; offsetDates.size() < offset; i++) {
            LocalDate localDate = dateFrom.plusDays(i);
            DayOfWeek dayOfWeek = localDate.getDayOfWeek();
            if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
                continue;
            }
            offsetDates.add(localDate);
        }
        return offsetDates;
    }

    /**
     * 获取一个日期的开始时间，即0点
     *
     * @param date 指定的日期
     * @return 指定日期的开始时间，0点
     */
    public static LocalDateTime getDateBeginTime(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.MIN);
    }

    /**
     * 获取一个日期的开始时间，即0点
     *
     * @param dateStr 指定的日期字符串
     * @param format  格式
     * @return 指定日期的开始时间，0点
     */
    public static LocalDateTime getDateBeginTime(String dateStr, String format) {
        LocalDate localDate = DateUtil.stringToLocalDate(dateStr, format);
        return getDateBeginTime(localDate);
    }

    /**
     * 获取一个日期的结束时间，即23:59:59.999999999
     *
     * @param date 指定的日期
     * @return 指定日期的结束时间，23:59:59.999999999
     */
    public static LocalDateTime getDateEndTime(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.MAX);
    }

    /**
     * 获取一个日期的结束时间，即23:59:59.999999999
     *
     * @param dateStr 指定的日期字符串
     * @param format  格式
     * @return 指定日期的结束时间，23:59:59.999999999
     */
    public static LocalDateTime getDateEndTime(String dateStr, String format) {
        LocalDate localDate = DateUtil.stringToLocalDate(dateStr, format);
        return getDateEndTime(localDate);
    }

    /**
     * default格式的日期转stand格式的日期
     *
     * @param dateStr default格式的日期
     * @return stand格式的日期
     */
    public static String defaultDateToStandard(String dateStr) {
        return DateUtil.localDateToString(DateUtil.stringToLocalDate(dateStr, DEFAULT_DATE_FORMAT),
            STANDARD_DATE_FORMAT);
    }

    public static Date add(Date date, int calendarField, int amount) {
        if (date == null) {
            throw new IllegalArgumentException("The date must not be null");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendarField, amount);
        return c.getTime();
    }

    /**
     * 本周开始时间
     *
     * @return
     */
    public static LocalDateTime weekStartTime(LocalDate date) {
        return LocalDateTime.of(date.minusDays(date.getDayOfWeek().getValue() - 1), LocalTime.MIN);
    }

    /**
     * 本周结束时间
     *
     * @return
     */
    public static LocalDateTime weekEndTime(LocalDate date) {
        return LocalDateTime.of(date.plusDays(7 - date.getDayOfWeek().getValue()), LocalTime.MAX);
    }

    /**
     * 日期是周几
     *
     * @param date
     * @return 周一是1，周二是2... 周日是7
     */
    public static Integer getDayOfWeek(LocalDate date) {
        if (date == null) {
            return null;
        } else {
            return date.getDayOfWeek().ordinal() + 1;
        }
    }

    /**
     * 日期是周几
     *
     * @param date
     * @return 周一是1，周二是2... 周日是7
     */
    public static Integer getDayOfHalfMonth(LocalDate date) {
        if (date == null) {
            return null;
        }
        Integer dayOfMonth = getDayOfMonth(date);
        if (dayOfMonth <= 14) {
            return dayOfMonth;
        }
        return dayOfMonth - 14;
    }

    /**
     * 日期是每月第几天
     *
     * @param date
     * @return 1~31
     */
    public static Integer getDayOfMonth(LocalDate date) {
        if (date == null) {
            return null;
        } else {
            return date.getDayOfMonth();
        }
    }
    /**
     * 日期是每月第几天
     *
     * @param date
     * @return 1~31
     */
    public static Integer getDayOfMonth(LocalDateTime date) {
        if (date == null) {
            return null;
        } else {
            return date.getDayOfMonth();
        }
    }

    /**
     * 获取中文的周几
     *
     * @param dayOfWeek
     * @return
     */
    public static String getChineseDayOfWeek(Integer dayOfWeek) {
        int maxWeekDay = 7;
        int minWeekDay = 1;
        if (dayOfWeek == null || dayOfWeek < minWeekDay || dayOfWeek > maxWeekDay) {
            return null;
        } else {
            return "周" + CHINESE_WEEK_DAY[dayOfWeek - 1];
        }
    }

    /**
     * 获取中文的每月几号
     *
     * @param dayOfMonth
     * @return
     */
    public static String getChineseDayOfMonth(Integer dayOfMonth) {
        if (dayOfMonth != null) {
            return dayOfMonth + "号";
        } else {
            return null;
        }
    }

    /**
     * 字符串类型的日期加减
     *
     * @param dateStr
     * @param format
     * @param days
     * @return
     */
    public static String stringDateAdd(String dateStr, String format, Integer days) {
        LocalDate date = DateUtil.stringToLocalDate(dateStr, format);
        if (date != null && days != null) {
            return DateUtil.localDateToString(date.plusDays(days), format);
        } else {
            return null;
        }
    }

    /**
     * 字符串类型的月加减
     *
     * @param months
     * @param format
     * @return
     */
    public static String stringMonthAdd(String dateStr, String format, Integer months) {
        LocalDate date;
        if (StringUtils.length(dateStr) == 6 && StringUtils.equals(format, DateUtil.DATE_FORMAT_MONTH)) {
            // yyyyMM格式，不能直接转换为localDate，需加上dd
            date = DateUtil.stringToLocalDate(dateStr + "01", DateUtil.DEFAULT_DATE_FORMAT);
        } else {
            date = DateUtil.stringToLocalDate(dateStr, DateUtil.DEFAULT_DATE_FORMAT);
        }
        if (date != null && months != null) {
            return DateUtil.localDateToString(date.plusMonths(months), format);
        } else {
            return null;
        }
    }

    /**
     * 根据月份，获取当月所有周的开始时间
     * 如果周跨月，强制取周的第一天
     *
     * @param month
     * @return
     */
    public static List<LocalDate> getWeekByMonth(String month) {
        List<LocalDate> weeksInMonth = new ArrayList<>();
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_MONTH);
            YearMonth yearMonth = YearMonth.parse(month, formatter);
            LocalDate date = yearMonth.atDay(1);
            int startDayOfWeek = date.getDayOfWeek().getValue();
            int numDaysInMonth = yearMonth.lengthOfMonth();
            LocalDate currentDay = date;
            while (currentDay.getDayOfMonth() <= numDaysInMonth
                && currentDay.getMonth().getValue() == yearMonth.getMonthValue()) {
                weeksInMonth.add(getFirstDayOfWeek(currentDay));
                // +1 for inclusive end day
                int remainingDays = 7 - startDayOfWeek + 1;
                LocalDate endDay = currentDay.plusDays(remainingDays - 1);
                currentDay = endDay.plusDays(1);
                // reset for subsequent weeks
                startDayOfWeek = 1;
            }
        } catch (DateTimeParseException e) {
        }
        return weeksInMonth;
    }

    /**
     * 获取本周第一天, 周一~周日
     *
     * @param date
     * @return
     */
    public static LocalDate getFirstDayOfWeek(LocalDate date) {
        if (date == null) {
            return null;
        } else {
            return date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        }
    }
    public static LocalDate getFirstDayOfWeek(LocalDate date, int plusWeek) {
        if (date == null) {
            return null;
        } else {
            return getFirstDayOfWeek(plusWeeks(date, plusWeek));
        }
    }
    /**
     * 获取本周最后天
     * 注：一周指的是 周一~周日
     *
     * @param date
     * @return
     */
    public static LocalDate getLastDayOfWeek(LocalDate date) {
        if (date == null) {
            return null;
        } else {
            return date.plusDays(6).with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
        }
    }
    public static LocalDate getLastDayOfWeek(LocalDate date, int plusWeek) {
        if (date == null) {
            return null;
        } else {
            return getLastDayOfWeek(plusWeeks(date, plusWeek));
        }
    }

    /**
     * 获取本周第一天, 周一~周日
     *
     * @param date
     * @return
     */
    public static LocalDateTime getFirstDayOfWeek(LocalDateTime date) {
        if (date == null) {
            return null;
        } else {
            return date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        }
    }
    public static LocalDateTime getFirstDayOfWeek(LocalDateTime date, int plusWeek) {
        if (date == null) {
            return null;
        } else {
            return getFirstDayOfWeek(plusWeeks(date, plusWeek));
        }
    }
    /**
     * 获取本周最后天
     * 注：一周指的是 周一~周日
     *
     * @param date
     * @return
     */
    public static LocalDateTime getLastDayOfWeek(LocalDateTime date) {
        if (date == null) {
            return null;
        } else {
            return date.plusDays(6).with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
        }
    }
    public static LocalDateTime getLastDayOfWeek(LocalDateTime date, int plusWeek) {
        if (date == null) {
            return null;
        } else {
            return getLastDayOfWeek(plusWeeks(date, plusWeek));
        }
    }

    public static LocalDate getFirstDayOfMonth(LocalDate date) {
        if (date == null) {
            return null;
        } else {
            return date.with(TemporalAdjusters.firstDayOfMonth());
        }
    }
    public static LocalDate getFirstDayOfMonth(LocalDate date, int plusMonths) {
        if (date == null) {
            return null;
        } else {
            return getFirstDayOfMonth(plusMonths(date, plusMonths));
        }
    }

    public static LocalDate getLastDayOfMonth(LocalDate date) {
        if (date == null) {
            return null;
        } else {
            return date.with(TemporalAdjusters.lastDayOfMonth());
        }
    }
    public static LocalDate getLastDayOfMonth(LocalDate date, int plusMonths) {
        if (date == null) {
            return null;
        } else {
            return getLastDayOfMonth(plusMonths(date, plusMonths));
        }
    }
    public static LocalDateTime getFirstDayOfMonth(LocalDateTime date, int plusMonths) {
        if (date == null) {
            return null;
        } else {
            return getFirstDayOfMonth(plusMonths(date, plusMonths));
        }
    }

    public static LocalDateTime getFirstDayOfMonth(LocalDateTime date) {
        if (date == null) {
            return null;
        } else {
            return date.with(TemporalAdjusters.firstDayOfMonth());
        }
    }
    public static LocalDateTime getLastDayOfMonth(LocalDateTime date) {
        if (date == null) {
            return null;
        } else {
            return date.with(TemporalAdjusters.lastDayOfMonth());
        }
    }
    public static LocalDateTime getLastDayOfMonth(LocalDateTime date, int plusMonths) {
        if (date == null) {
            return null;
        } else {
            return getLastDayOfMonth(plusMonths(date, plusMonths));
        }
    }

    public static int getMonthDays(String month) {
        String str = month;
        if (StringUtils.length(month) == 6) {
            str = month + "01";
        }
        LocalDate date = stringToLocalDate(str, DEFAULT_DATE_FORMAT);
        return date.lengthOfMonth();
    }

    /**
     * 是否在日期范围内
     *
     * @param start
     * @param end
     * @param current
     * @return
     */
    public static boolean isInRange(LocalDate start, LocalDate end, LocalDate current) {
        if (start == null || end == null || current == null) {
            return false;
        } else {
            return !start.isAfter(current) && !end.isBefore(current);
        }
    }

    /**
     * <<<<<<< Updated upstream
     * 获取最近一个完整月
     * 20231101  -》 202310
     * 20231031  -》 202310
     * 20231030  -》 2023109
     *
     * @param statDate
     * @param dateFormat
     * @param monthFormat
     * @return
     */
    public static String getLatestMonth(String statDate, String dateFormat, String monthFormat) {
        LocalDate date = DateUtil.stringToLocalDate(statDate, dateFormat);
        if (date == null) {
            return null;
        }
        LocalDate monthLastDay = DateUtil.getLastDayOfMonth(date);
        if (monthLastDay.equals(date)) {
            return DateUtil.localDateToString(date, monthFormat);
        } else {
            return DateUtil.getMonthByMonthsAgo(date, -1, monthFormat);
        }
    }

    /**
     * 获取最近一个完整周的周日
     * 20231113 -》 20231112
     * 20231112 -》 20231112
     * 20231111 -》 20231105
     *
     * @param statDate
     * @param format
     * @return
     */
    public static String getLatestWeek(String statDate, String format) {
        LocalDate date = DateUtil.stringToLocalDate(statDate, format);
        if (date == null) {
            return null;
        }
        int weekDay = DateUtil.getDayOfWeek(date);
        LocalDate ret;
        if (weekDay == 7) {
            //周日，返回本周的周一
            ret = date;
        } else {
            //非周日，返回上周的周日
            ret = date.plusDays(-weekDay);
        }
        return DateUtil.localDateToString(ret, format);
    }

    /**
     * 字符串日期格式转换
     *
     * @param statDate
     * @param srcFormat
     * @param targetFormt
     * @return
     */
    public static String stringFormatChange(String statDate, String srcFormat, String targetFormt) {
        String date = statDate;
        String format = srcFormat;
        if (StringUtils.length(statDate) == 6 && StringUtils.equals(srcFormat, DateUtil.DATE_FORMAT_MONTH)) {
            // yyyyMM格式，不能直接转换为localDate，需加上dd
            date = statDate + "01";
            format = DateUtil.DEFAULT_DATE_FORMAT;
        }
        return DateUtil.localDateToString(DateUtil.stringToLocalDate(date, format), targetFormt);
    }

    /**
     * 校验是否为过去一年内
     *
     * @param date
     * @return
     */
    public static boolean isWithinPastYear(LocalDate date) {
        date = date.minusDays(1);
        long daysBetween = ChronoUnit.YEARS.between(date, LocalDate.now());
        return daysBetween >= 0;
    }

    public static boolean isWithinNextYear(LocalDate date) {
        date = date.plusDays(1);
        long daysBetween = ChronoUnit.YEARS.between(date, LocalDate.now());
        return daysBetween >= 0;
    }

    public static boolean isInOneYearRange(LocalDate dateToCheck) {
        LocalDate currentDate = LocalDate.now();
        LocalDate oneYearAgo = currentDate.minusYears(1);
        LocalDate oneYearLater = currentDate.plusYears(1);
        return (dateToCheck.isAfter(oneYearAgo) || dateToCheck.isEqual(oneYearAgo)) &&
            (dateToCheck.isBefore(oneYearLater) || dateToCheck.isEqual(oneYearLater));
    }

    /**
     * yyyyMMdd日期，获取当周第一天
     *
     * @param date
     * @return
     */
    public static String getWeekFirstDay(String date) {
        LocalDate day = DateUtil.stringToLocalDate(date, DateUtil.DEFAULT_DATE_FORMAT);
        LocalDate weekFirstDay = DateUtil.getFirstDayOfWeek(day);
        return DateUtil.localDateToString(weekFirstDay, DateUtil.DEFAULT_DATE_FORMAT);
    }

    /**
     * yyyyMMdd日期，获取当周最后天
     *
     * @param date
     * @return
     */
    public static String getWeekLastDay(String date) {
        LocalDate day = DateUtil.stringToLocalDate(date, DateUtil.DEFAULT_DATE_FORMAT);
        LocalDate weekLastDay = DateUtil.getLastDayOfWeek(day);
        return DateUtil.localDateToString(weekLastDay, DateUtil.DEFAULT_DATE_FORMAT);
    }

    /**
     * yyyyMMdd日期，获取当月的yyyyMM
     *
     * @param date
     * @return
     */
    public static String getMonth(String date) {
        return StringUtils.substring(date, 0, 6);
    }

    /**
     * 日期，获取当月的第一天
     *
     * @param date
     * @return
     */
    public static String getMonthFirstDay(String date) {
        return StringUtils.substring(date, 0, 6) + "01";
    }

    /**
     * yyyyMMdd日期，获取当月最后一天
     *
     * @param date
     * @return
     */
    public static String getMonthLastDay(String date) {
        LocalDate day;
        if (StringUtils.length(date) == DateUtil.DEFAULT_DATE_FORMAT.length()) {
            day = DateUtil.stringToLocalDate(date, DateUtil.DEFAULT_DATE_FORMAT);
        } else {
            day = DateUtil.stringToLocalDate(date + "01", DateUtil.DEFAULT_DATE_FORMAT);
        }
        LocalDate monthLastDay = DateUtil.getLastDayOfMonth(day);
        return DateUtil.localDateToString(monthLastDay, DateUtil.DEFAULT_DATE_FORMAT);
    }

    /**
     * 计算两个日期之间相差多少天 结果为：date1 - date2
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Integer calcDateDiffDays(LocalDate date1, LocalDate date2) {
        long until = date2.until(date1, ChronoUnit.DAYS);
        return ((Long)until).intValue();

    }
    /**
     * 计算两个日期之间相差多少天 结果为：date1 - date2
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Integer calcDateDiffDays(LocalDateTime date1, LocalDateTime date2) {
        long until = date2.until(date1, ChronoUnit.DAYS);
        return ((Long)until).intValue();

    }

    public static boolean isToday(Date date) {
        boolean ret = true;
        LocalDate givenDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate today = LocalDate.now();

        if (!givenDate.isEqual(today)) {
            ret = false;
        }
        return ret;
    }
    public static boolean isToday(LocalDate date) {
        if(Objects.isNull(date)){ return false; }
        return LocalDate.now().isEqual(date);
    }

    public static Date plusDay(Date currentDate, Integer daysToAdd) {
        if (daysToAdd == null || daysToAdd == 0) {
            return currentDate;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
        return calendar.getTime();
    }

    public static String getOffsetDateStr(Integer offset) {
        return DateUtil.dateToString(DateUtil.plusDay(new Date(), offset), DateUtil.DEFAULT_DATE_FORMAT);
    }

    public static Long diffDays(String start, String end, String format) {
        LocalDate startDate = stringToLocalDate(start, format);
        LocalDate endDate = stringToLocalDate(end, format);
        return endDate.toEpochDay() - startDate.toEpochDay();
    }

    public static Date stringToDate2(String dateStr, String format) {
        LocalDate localDate = stringToLocalDate(dateStr, format);
        return localDateToDate(localDate);
    }

    public static void main(String[] args) {
        String str = "2024-02-15";
        LocalDate localDate = stringToLocalDate(str, DateUtil.STANDARD_DATE_FORMAT);
        Integer dayOfHalfMonth = getDayOfHalfMonth(localDate);
        System.out.println(dayOfHalfMonth);
    }

    public static LocalDateTime getFirstDayOfYear(LocalDateTime localDate) {
        if (localDate == null) {
            return null;
        } else {
            return localDate.with(TemporalAdjusters.firstDayOfYear());
        }
    }
    public static LocalDateTime getLastDayOfYear(LocalDateTime localDate) {
        if (localDate == null) {
            return null;
        } else {
            return localDate.with(TemporalAdjusters.lastDayOfYear());
        }
    }

    public static LocalDate getFirstDayOfYear(LocalDate localDate) {
        if (localDate == null) {
            return null;
        } else {
            return localDate.with(TemporalAdjusters.firstDayOfYear());
        }
    }
    public static LocalDate getLastDayOfYear(LocalDate localDate) {
        if (localDate == null) {
            return null;
        } else {
            return localDate.with(TemporalAdjusters.lastDayOfYear());
        }
    }

    public static Date removeHHMMSS(Date date) {
        if (date == null) {
            return null;
        }
        try {
            return sdf.parse(sdf.format(date));
        } catch (ParseException e) {
            LOGGER.error("去除时间[{}]中的时分秒异常：", date, e);
        }
        return date;
    }

    /**
     * 取半月的环比日期
     *
     * @param today
     * @return
     */
    public static LocalDate getHalfMonthComparison(LocalDate today) {
        LocalDate halfMonthComparison;
        if (today.getDayOfMonth() == 1) {
            // 今天是1号，取上个月的15号作为环比日期
            halfMonthComparison = today.minusMonths(1).withDayOfMonth(15);
        } else if (today.getDayOfMonth() == 15 || today.getDayOfMonth() < 15) {
            // 今天是15号，取本月的1号作为环比日期
            //            2-15号也取1号为环比日期
            halfMonthComparison = today.withDayOfMonth(1);
        } else {
            // 今天日期在15号之后，取本月的15号作为环比日期
            halfMonthComparison = today.withDayOfMonth(15);
        }
        return halfMonthComparison;
    }

    public static int firstDayOfMonth() {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfMonth = today.with(TemporalAdjusters.firstDayOfMonth());
        return firstDayOfMonth.getDayOfMonth();
    }

    public static int lastDayOfMonth() {
        LocalDate today = LocalDate.now();
        LocalDate lastDayOfMonth = today.with(TemporalAdjusters.lastDayOfMonth());
        return lastDayOfMonth.getDayOfMonth();
    }

    public static Long localDateToMillis(LocalDate date){
        if(date == null){ return null; }
        return localDateTimeToMillis(date.atStartOfDay());
    }
    public static Long localDateTimeToMillis(LocalDateTime dateTime){
        if(dateTime == null){ return null; }
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static Integer dateToInteger(LocalDate date){
        String toStr = localDateToString(date, DEFAULT_DATE_FORMAT);
        return Integer.valueOf(toStr == null ? "0" : toStr);
    }
    public static Long datetimeToLong(LocalDateTime date){
        String toStr = localDateTimeToString(date, DEFAULT_DATETIME_INT_FORMAT);
        return Long.valueOf(toStr == null ? "0" : toStr);
    }
    public static LocalDateTime longToLocalDateTime(Long date){
        return stringToLocalDateTime(String.valueOf(date), DEFAULT_DATETIME_INT_FORMAT);
    }
    public static LocalDate integerToLocalDate(Integer date){
        return stringToLocalDate(String.valueOf(date), DEFAULT_DATE_FORMAT);
    }
    public static LocalDate integerToLocalDate(Integer date, String format){
        LocalDate localDate = integerToLocalDate(date);
        return stringToLocalDate(localDateToString(localDate, format), format);
    }
    public static LocalDateTime plusDays(LocalDateTime date, long plusDays) {
        return date.plusDays(plusDays);
    }
    public static LocalDateTime plusHours(LocalDateTime date, long plusHours) {
        return date.plusHours(plusHours);
    }
    public static LocalDateTime plusNanos(LocalDateTime date, long nanos){
        if(Objects.isNull(date)){
            return null;
        }
        return date.plusNanos(nanos);
    }
    public static LocalDateTime plusWeeks(LocalDateTime date, long plusWeeks){
        if(Objects.isNull(date)){
            return null;
        }
        return date.plusWeeks(plusWeeks);
    }
    public static LocalDateTime plusMonths(LocalDateTime date, long plusMonths){
        if(Objects.isNull(date)){
            return null;
        }
        return date.plusMonths(plusMonths);
    }

    public static LocalDate plusDays(LocalDate date, long plusDays){
        if(Objects.isNull(date)){
            return null;
        }
        return date.plusDays(plusDays);
    }
    public static LocalDate plusWeeks(LocalDate date, long plusWeeks){
        if(Objects.isNull(date)){
            return null;
        }
        return date.plusWeeks(plusWeeks);
    }
    public static LocalDate plusMonths(LocalDate date, long plusMonths){
        if(Objects.isNull(date)){
            return null;
        }
        return date.plusMonths(plusMonths);
    }
    public static LocalDate plusYears(LocalDate date, long plusYears){
        if(Objects.isNull(date)){
            return null;
        }
        return date.plusYears(plusYears);
    }
    public static Integer getMonth(LocalDate date){
        if(Objects.isNull(date)){
            return null;
        }
        return date.getMonth().getValue();
    }
    public static Integer getMonth(LocalDateTime date){
        if(Objects.isNull(date)){
            return null;
        }
        return date.getMonth().getValue();
    }
    public static Integer getWeek(LocalDate date){
        if(Objects.isNull(date)){
            return null;
        }
        return date.getDayOfWeek().getValue();
    }
    public static Integer getWeek(LocalDateTime date){
        if(Objects.isNull(date)){
            return null;
        }
        return date.getDayOfWeek().getValue();
    }

    public static boolean lessThan(LocalDate date1, LocalDate date2){
        if(Objects.isNull(date1) || Objects.isNull(date2)){
            return false;
        }
        return date1.isBefore(date2);
    }
    public static boolean lessThanEqual(LocalDate date1, LocalDate date2){
        if(Objects.isNull(date1) || Objects.isNull(date2)){
            return false;
        }
        return date1.isBefore(date2) || date1.isEqual(date2);
    }


    public static LocalDateTime startOfDay(LocalDate date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return date.atStartOfDay();
    }
    public static LocalDateTime endOfDay(LocalDate date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return date.atTime(23, 59, 59);
    }
    public static LocalDateTime startOfDay() {
        return startOfDay(LocalDate.now());
    }
    public static LocalDateTime endOfDay() {
        return endOfDay(LocalDate.now());
    }

    public static LocalDateTime clearHms(LocalDateTime date) {
        if (date == null) {
            return null;
        } else {
            return date.withHour(0).withMinute(0).withSecond(0).withNano(0);
        }
    }

    public static long weekBetween(LocalDate start, LocalDate end) {
        return ChronoUnit.WEEKS.between(start, end);
    }
    public static long weekBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.WEEKS.between(start, end);
    }

    public static long monthBetween(LocalDate start, LocalDate end) {
        return ChronoUnit.MONTHS.between(start, end);
    }
    public static long monthBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.MONTHS.between(start, end);
    }
}
