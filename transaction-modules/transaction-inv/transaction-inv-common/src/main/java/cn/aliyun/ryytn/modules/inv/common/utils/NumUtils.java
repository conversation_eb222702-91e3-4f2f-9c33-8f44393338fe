package cn.aliyun.ryytn.modules.inv.common.utils;

import java.math.BigDecimal;

public class NumUtils {
    public static Integer safeInt(Object obj) {
        if (obj == null) {
            return null;
        } else if (obj instanceof Integer) {
            return (Integer)obj;
        } else if (obj instanceof Number) {
            return ((Number)obj).intValue();
        } else {
            try {
                return Integer.valueOf(obj.toString());
            } catch (NumberFormatException e) {
                return null;
            }
        }
    }

    public static Integer setInt(Object o) {
        return safeInt(o) == null ? 0 : safeInt(o);
    }

    public static Double safeDouble(Object obj) {
        if (obj == null) {
            return null;
        } else if (obj instanceof Double) {
            return (Double)obj;
        } else if (obj instanceof Number) {
            return ((Number)obj).doubleValue();
        } else {
            try {
                return Double.valueOf(obj.toString());
            } catch (NumberFormatException e) {
                return null;
            }
        }
    }
    public static BigDecimal safeDecimal(Object obj) {
        if (obj == null) {
            return null;
        } else if (obj instanceof Double) {
            return BigDecimal.valueOf((Double)obj);
        } else if (obj instanceof Number) {
            return BigDecimal.valueOf(((Number)obj).doubleValue());
        } else {
            try {
                return BigDecimal.valueOf(Double.parseDouble(obj.toString()));
            } catch (NumberFormatException e) {
                return null;
            }
        }
    }
    public static Long safeLong(Object obj) {
        if (obj == null) {
            return null;
        } else if (obj instanceof Long) {
            return (Long)obj;
        } else if (obj instanceof Number) {
            return ((Number)obj).longValue();
        } else {
            try {
                return Long.valueOf(obj.toString());
            } catch (NumberFormatException e) {
                return null;
            }
        }
    }

    public static Boolean isInteger(Object obj){
        if (obj == null) {
            return false;
        }
        try {
            Integer.parseInt(obj.toString());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 去除浮点型尾巴
     * 例如 5.00000001 -> 5.0,  4.9999999 -> 5.0
     *
     * @param value
     * @return
     */
    public static Double roundIfCloseToInt(Double value) {
        if (value == null) {
            return null;
        }
        double diff = Math.abs(value - Math.round(value));
        double threshold = 1e-4;
        if (diff < threshold) {
            return (double)Math.round(value);
        } else {
            return value;
        }

    }
}
