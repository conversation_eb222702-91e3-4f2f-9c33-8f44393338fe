package cn.aliyun.ryytn.modules.inv.common.ability.meta;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeFieldWrapper;
import cn.aliyun.ryytn.modules.inv.common.model.KVPair;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-08-19 16:36
 * @description metaInfo助手
 */
public class MetaInfoHolder {

    /**
     * 创建metaInfo附加属性对象
     * @return Extra
     */
    @SafeVarargs
    public static Extra createExtra(KVPair<String, String>... require){
        return createExtra(null, require);
    }
    @SafeVarargs
    public static Extra createExtra(Object value, KVPair<String, String>... require){
        List<KVPair<String, String>> required = null;
        if(require != null && require.length > 0){
            required = Stream.of(require).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        return Extra.of(required, value);
    }
    /**
     * 附加属性描述对象，用于描述附加属性
     */
    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Extra {

        /**
         * 用于条件渲染, key=当前行中的属性名， value=当前行中属性名对应的属性值
         */
        private List<KVPair<String, String>> required;
        /**
         * 当前字段渲染的附加值
         */
        private Object value;
        private String title;
        /**
         * 后缀
         */
        private String suffix;
        public Extra setValue(Object value){
            this.value = value;
            return this;
        }

        public static Extra of(List<KVPair<String, String>> required){
            return of(required, null);
        }
        public static Extra of(List<KVPair<String, String>> required, Object value){
            return new Extra(required, value, null, null);
        }
        public <T extends MetaInfo> T set(T t){
            t.setExtra(this);
            return t;
        }

        public <T extends MergeFieldWrapper> T set(T t){
            t.setExtra(this);
            return t;
        }
    }
}
