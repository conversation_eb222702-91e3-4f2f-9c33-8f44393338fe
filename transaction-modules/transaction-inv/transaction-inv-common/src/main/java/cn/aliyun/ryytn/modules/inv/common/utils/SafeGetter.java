package cn.aliyun.ryytn.modules.inv.common.utils;

import java.util.function.BiFunction;
import java.util.function.Supplier;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-08-05 17:45
 * @description 可替换操作的安全获取工具类
 */
public class SafeGetter {

    /**
     * 获取值， 如果值为null则返回替换值
     * @param source 原值
     * @param replace 替换值
     * @return T
     */
    public static <T> T get(T source, T replace){
        return source == null ? replace : source;
    }
    /**
     * 获取值， 如果值为null则返回替换值
     * @param source 原值
     * @param replace 替换值
     * @return T
     */
    public static <T> T get(T source, Supplier<T> replace){
        if(replace == null){
            return source;
        }
        return source == null ? replace.get() : source;
    }

    /**
     * 按指定比较器的结果获取值
     * @param source 原值
     * @param replace 替换值
     * @param compare 自定义比较器。 会传入source 与 replace， 如果该比较器返回true则使用replace做返回值， 否则将返回source
     *        BiFunction<T source, T replace, Boolean>
     * @return T
     */
    public static <T> T get(T source, T replace, BiFunction<T, T, Boolean> compare){
        if(compare == null){ return get(source, replace); }
        return Boolean.TRUE.equals(compare.apply(source, replace)) ? replace : source;
    }

    /**
     * 获取int类型值， 如果值为null或者小于等于0则返回替换值
     * @param source number 原值
     * @param replace number 替换值
     * @return Integer
     */
    public static Integer getGtZeroInt(Number source, Number replace){
        Number number = getGtZero(source, replace);
        return number != null ? number.intValue() : null;
    }

    /**
     * 获取Number类型值， 如果值为null或者小于等于0则返回替换值
     * @param source 原值
     * @param replace 替换值
     * @return Number
     */
    private static Number getGtZero(Number source, Number replace){
        return get(source, replace, (s, r) -> s == null || s.doubleValue() <= 0);
    }
}
