package cn.aliyun.ryytn.modules.inv.common.enums;

/**
 * 任务执行状态枚举
 *
 * <AUTHOR>
 * @version $Id: TaskScheduleStatusEnum, v 0.1 2023/7/14 下午2:51 daoqi Exp $$
 */
public enum TaskScheduleStatusEnum {

    /**
     * 未开始
     */
    NOT_START("NOT_START", "未开始"),
    /**
     * 执行中
     */
    DOING("DOING", "执行中"),
    /**
     * 已完成
     */
    COMPLETE("COMPLETE", "已完成");

    /**
     * 编码
     */
    private final String code;
    /**
     * 名字
     */
    private final String name;


    TaskScheduleStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TaskScheduleStatusEnum getByCode(String code) {
        for (TaskScheduleStatusEnum tableEnum : values()) {
            if (tableEnum.getCode().equals(code)) {
                return tableEnum;
            }
        }
        return null;
    }


    /**
     * get the value of code.
     *
     * @return the value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * get the value of name.
     *
     * @return the value of name
     */
    public String getName() {
        return name;
    }

}
