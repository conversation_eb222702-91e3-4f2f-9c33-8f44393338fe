package cn.aliyun.ryytn.modules.inv.common.utils;

/**
 * 字符串常量池
 */
public interface StringConstants {
    char C_SPACE = ' ';
    char SEMICOLON = ';';
    char C_TAB = '\t';
    char C_DOT = '.';
    char C_SLASH = '/';
    char C_BACKSLASH = '\\';
    char C_CR = '\r';
    char C_LF = '\n';
    char C_UNDERLINE = '_';
    char C_WAVY = '~';
    char C_COMMA = ',';
    char ASTERISK = '*';
    char C_DELIM_START = '{';
    char C_DELIM_END = '}';
    char C_BRACKET_START = '[';
    char C_BRACKET_END = ']';
    char C_COLON = ':';
    char C_AT = '@';
    String PERCENT_SIGN = "%";
    String TAB = "\t";
    String DOT = ".";
    String DOUBLE_DOT = "..";
    String SLASH = "/";
    String BACKSLASH = "\\";
    String CR = "\r";
    String LF = "\n";
    String CRLF = "\r\n";
    String UNDERLINE = "_";
    String DASHED = "-";
    String EMPTY = "";
    String ADD = "+";
    String COMMA = ",";
    String DELIM_START = "{";
    String DELIM_END = "}";
    String BRACKET_START = "[";
    String BRACKET_END = "]";
    String COLON = ":";
    String AT = "@";
    String HTML_NBSP = "&nbsp;";
    String HTML_AMP = "&amp;";
    String HTML_QUOTE = "&quot;";
    String HTML_APOS = "&apos;";
    String HTML_LT = "&lt;";
    String HTML_GT = "&gt;";
    String EMPTY_JSON = "{}";
    String GT = ">";
    String GE = ">=";
    String LT = "<";
    String LE = "<=";
}