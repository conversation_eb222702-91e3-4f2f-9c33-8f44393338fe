package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.fallback;

import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.annotation.CnHttpClient;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception.CnHttpFallbackParserException;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception.ErrorEnum;
import org.springframework.context.ApplicationContext;
import org.springframework.core.ResolvableType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-06 17:34
 * @description
 */
class FallbackFactoryParser {

    protected static void parse(ApplicationContext context, Map<String, List<FallbackFactory<?>>> fallbackFactories) {
        Map<String, FallbackFactory> factoryMap = context.getBeansOfType(FallbackFactory.class);
        factoryMap.forEach((k, v) -> {
            ResolvableType resolvableType = ResolvableType.forClass(v.getClass());
            Class<?> clientClass = deepSearchClientClass(resolvableType, resolvableType);
            List<FallbackFactory<?>> fallbackFactoryList = fallbackFactories.computeIfAbsent(clientClass.getName(), k1 -> new ArrayList<>());
            fallbackFactoryList.add(v);
        });
    }
    /**
     * 递归查找ClientClass
     * @param raw 初始查找类型，不会随着递归而改变
     * @param source 源类型
     * @return
     */
    private static Class<?> deepSearchClientClass(ResolvableType raw, ResolvableType source){
        if(source == null || Objects.equals(source.getRawClass(), Object.class)){ return null; }
        if(Objects.equals(source.getRawClass(), FallbackFactory.class)){ return source.getGeneric(0).resolve(); }
        for (ResolvableType interfaceClass : source.getInterfaces()) {
            Class<?> clientClass = deepSearchClientClass(raw, interfaceClass);
            if (Objects.nonNull(clientClass)) {
                if(clientClass.isAnnotationPresent(CnHttpClient.class)){
                    return clientClass;
                }else {
                    throw new CnHttpFallbackParserException(String.format(ErrorEnum.FALLBACK_PARSE_ERROR.info(), raw.getRawClass()));
                }
            }
        }
        throw new CnHttpFallbackParserException(String.format(ErrorEnum.FALLBACK_IS_NO_MARK.info(), raw.getRawClass()));
    }
}
