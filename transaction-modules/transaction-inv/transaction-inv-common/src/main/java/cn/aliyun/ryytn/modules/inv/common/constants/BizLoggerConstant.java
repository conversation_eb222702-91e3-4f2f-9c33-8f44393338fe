package cn.aliyun.ryytn.modules.inv.common.constants;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-14 16:29
 * @description
 */
public interface BizLoggerConstant {
    interface BizCode {
        String HTTP = "md.http";
        String INV_BUSSINESS = "inv.bussiness";
        String INV_PRIORITY = "inv.priority";
        String STRATEGY_RESULTS = "strategy.results";

        String STRATEGY_CENTER = "strategy.center";

        String SAFETY_STOCK = "safety.stock";

        String TAG_PRODUCT = "workbench.tagProduct";

        String fcstModify = "demand.fcst_modify";

        String INV_SKUREPLNTYPE = "inv.skuReplnType";

        String INV_SKUCDCFULLSUPPLY = "inv.skuCdcFullSupply";

    }
    interface Suffix {
        String STRATEGY = "strategy";
        String HTTP = "http";
        String INV = "inv";
    }
}
