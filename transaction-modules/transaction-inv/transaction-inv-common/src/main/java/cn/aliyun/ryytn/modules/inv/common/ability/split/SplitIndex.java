package cn.aliyun.ryytn.modules.inv.common.ability.split;

import java.lang.annotation.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-10-11 20:36
 * @description 描述分割字段info
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SplitIndex {
    /**
     * meta code
     */
    String value();

    /**
     * meta name
     */
    String name();

    /**
     * 分组字段名
     */
    String[] group();

    /**
     * 是否隐藏
     */
    boolean hidden() default false;

    /**
     * 用于meta的顺序
     */
    int sort() default 0;

    /**
     * 设置冻结该列，如果不设置当前字段，则表示不冻结 left - 左侧冻结 / right - 右侧冻结
     */
    String fixed() default "";

    /**
     * 多维开关
     */
    boolean multi() default false;

}
