package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-04 15:46
 * @description 失败降级异常
 */
public class CnHttpFallbackException extends RuntimeException {
    public CnHttpFallbackException() {
    }
    public CnHttpFallbackException(ErrorEnum errorEnum) {
        this(errorEnum.info());
    }

    public CnHttpFallbackException(String message) {
        super(message);
    }

    public CnHttpFallbackException(String message, Throwable cause) {
        super(message, cause);
    }

    public CnHttpFallbackException(Throwable cause) {
        super(cause);
    }

    public CnHttpFallbackException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
