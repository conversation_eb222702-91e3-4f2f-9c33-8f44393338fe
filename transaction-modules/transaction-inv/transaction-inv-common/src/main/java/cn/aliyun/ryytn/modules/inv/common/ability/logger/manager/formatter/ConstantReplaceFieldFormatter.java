package cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.formatter;

import org.springframework.stereotype.Component;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-20 14:59
 * @description 常量替换格式化器
 * c{1:是;2:否}
 */
@Component
public class ConstantReplaceFieldFormatter implements FieldFormatter {
    public static final String FORMAT_GAP = ";";
    public static final String FORMAT_MAPPER = ":";

    protected static String doFormat(String format, Object value) {
        String[] mapperArray = format.split(FORMAT_GAP);
        for (String mapper : mapperArray) {
            String[] map = mapper.split(FORMAT_MAPPER);
            if (map.length != 2) {
                continue;
            }
            if (map[0].equals(String.valueOf(value))) {
                return map[1];
            }
        }
        return null;
    }

    @Override
    public boolean support(String format) {
        return format.startsWith("c{") && format.endsWith("}");
    }

    @Override
    public String format(Object source, Object value, String format) {
        return doFormat(FieldFormatter.subFormat(format, 2), value);
    }
}
