package cn.aliyun.ryytn.modules.inv.common.utils;

import java.math.BigDecimal;

/**
 * this is description
 *
 * <AUTHOR>
 * @version $Id: MathUtil, v 0.1 2023/8/17 20:14 daoqi Exp $$
 */
public class MathUtil {


    /**
     * 判断两个double类型的数字相等
     * 暂不考虑double精度
     * @param d1
     * @param d2
     * @return
     */
    public static boolean isDoubleEquals(Double d1, Double d2) {
        if (d1 == null && d2 == null) {
            return true;
        }
        if (d1 == null || d2 == null) {
            return false;
        }
        return d1.equals(d2);
    }

    /**
     * 两个double类型的数相除，保留两位小数
     *
     * @param a
     * @param b
     * @param scale
     * @return
     */
    public static Double divide(Double a, Double b, int scale) {
        if (a == null || b == null) {
            return null;
        }
        // 分母为0 ，特殊处理
        if (b == 0d) {
            return 0d;
        }
        BigDecimal bd1 = new BigDecimal(Double.toString(a));
        BigDecimal bd2 = new BigDecimal(Double.toString(b));
        return bd1.divide(bd2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 两个double类型的数相除，保留两位小数
     *
     * @param a
     * @param b
     * @return
     */
    public static Double multiply(Double a, Double b) {
        if (a == null || b == null) {
            return null;
        }
        return a * b;
    }
}
