package cn.aliyun.ryytn.modules.inv.common.ability.constant;

import java.lang.annotation.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-03 09:32
 * @description
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface InjectConstant {
    /**
     * 类型
     */
    String type();

    /**
     * 配置编码
     * @return
     */
    String[] code() default {};

    String blur() default "";

    /**
     * 缓存时间，毫秒单位
     * 小于等于0不缓存
     */
    long maxCacheTime() default 0;
}
