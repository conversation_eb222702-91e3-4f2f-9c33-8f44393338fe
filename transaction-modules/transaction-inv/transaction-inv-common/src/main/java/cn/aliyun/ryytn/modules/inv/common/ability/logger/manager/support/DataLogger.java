package cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support;


import cn.aliyun.ryytn.modules.inv.common.ability.logger.model.DataLogWrapper;
import cn.aliyun.ryytn.modules.inv.common.dao.common.log.dataobject.ScpBizDataLogDO;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-14 19:37
 * @description
 */
public interface DataLogger {
    /**
     * 是否支持写入
     *
     * @param methodResult
     * @return
     */
    boolean supportWrite(Object methodResult);

    /**
     * 写入转换
     *
     * @param wrapper
     * @param currentArgument
     * @return
     */
    String writeConvert(DataLogWrapper wrapper, Object currentArgument);

    /**
     * 读取转换
     *
     * @param list
     * @return
     */
    Object readConvert(List<ScpBizDataLogDO> list);
}
