package cn.aliyun.ryytn.modules.inv.common.assembler;

import cn.aliyun.ryytn.modules.inv.common.dao.common.dataobject.CommonTenantDO;
import cn.aliyun.ryytn.modules.inv.common.model.CommonTenantDTO;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (风学)
 * @since 2023/8/30
 */
public class TenantAssembler {
    public static List<CommonTenantDTO> do2DtoList(List<CommonTenantDO> list) {
        return list.stream().map(e->{
            CommonTenantDTO dto=new CommonTenantDTO();
            BeanUtils.copyProperties(e,dto);
            return dto;
        }).collect(Collectors.toList());
    }
}
