package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.proxy;

import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.CnHttpApiTemplate;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.HttpMethod;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.annotation.CnHttpClient;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.annotation.CnHttpRequest;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.config.CnHttpApiProperties;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.config.CnHttpClientConfig;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.config.CnHttpRequestConfig;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception.CnHttpRequestNotFoundException;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception.ErrorEnum;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.fallback.FallbackFactoryManager;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.processor.CnHttpExecuteProcessor;
import cn.aliyun.ryytn.modules.inv.common.utils.JsonUtils;
import com.alibaba.fastjson2.JSONReader;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;

import java.lang.reflect.Method;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-04 16:12
 * @description 外部接口方法拦截器
 */
class CnHttpRequestMethodInterceptor implements MethodInterceptor {
    private final ConfigurableListableBeanFactory beanFactory;
    private final Class<?> sourceClientClass;
    CnHttpRequestMethodInterceptor(ConfigurableListableBeanFactory beanFactory, Class<?> sourceClientClass){
        this.beanFactory = beanFactory;
        this.sourceClientClass = sourceClientClass;
    }
    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        Method method = invocation.getMethod();
        if(method.isAnnotationPresent(CnHttpRequest.class)){
            CnHttpApiProperties outsideProperties = getBean(CnHttpApiProperties.class);
            CnHttpRequest outsideRequest = method.getAnnotation(CnHttpRequest.class);
            CnHttpClient client = sourceClientClass.getAnnotation(CnHttpClient.class);
            CnHttpApiWrapper outsideApiWrapper = resolveOutsideApiWrapper(method, outsideProperties, client, outsideRequest);
            return sendApi(outsideApiWrapper, invocation.getArguments());
        }
        return invocation.proceed();
    }
    private Object sendApi(CnHttpApiWrapper wrapper, Object[] args){
        CnHttpApiTemplate apiTemplate = getBean(CnHttpApiTemplate.class);
        ParameterResolver.resolveHeaderAndPadding(wrapper, args);
        doAfterParsingHeaders(wrapper);
        ParameterResolver.resolveRequestArgsAndPadding(wrapper, args);
        try {
            if(HttpMethod.GET.matches(wrapper.getMethod())){
                return apiTemplate.get(wrapper);
            }else if(HttpMethod.POST.matches(wrapper.getMethod())){
                return apiTemplate.post(wrapper);
            }else if(HttpMethod.PUT.matches(wrapper.getMethod())){
                return apiTemplate.put(wrapper);
            }else if(HttpMethod.DELETE.matches(wrapper.getMethod())){
                return apiTemplate.delete(wrapper);
            }else {
                throw new CnHttpRequestNotFoundException(ErrorEnum.HTTP_METHOD_NOT_SUPPORT);
            }
        }catch (CnHttpRequestNotFoundException e) {
            throw e;
        }catch (Throwable e) {
            FallbackFactoryManager factoryManager = getBean(FallbackFactoryManager.class);
            Object result = factoryManager.executeFallbackStrategy(sourceClientClass, wrapper.getSourceMethod(), args, e);
            if(e.equals(result)){ throw e; }
            return result;
        }
    }
    private <T> T getBean(Class<T> clazz){
        return beanFactory.getBean(clazz);
    }
    private CnHttpApiWrapper resolveOutsideApiWrapper(Method method, CnHttpApiProperties outsideProperties, CnHttpClient outsideClient, CnHttpRequest outsideRequest){
        CnHttpClientConfig outsideApi = outsideProperties.getApiByName(outsideClient.value());
        String rootApi = outsideClient.rootApi();
        if(outsideApi != null && StringUtils.isNotBlank(outsideApi.getRootApi())){
            rootApi = outsideApi.getRootApi();
        }
        String uri = null, requestMethod = null, contentType = null;
        Map<String, String> headers = null;
        CnHttpRequestConfig outsideApiRequest;
        if(outsideApi != null && (outsideApiRequest = outsideApi.getMethodByName(outsideRequest.value())) != null){
            uri = outsideApiRequest.getUri();
            requestMethod = outsideApiRequest.getMethod();
            contentType = outsideApiRequest.getContentType();
            headers = outsideApiRequest.getHeaders();
        }
        uri = getAvailableString(uri, outsideRequest.uri());
        requestMethod = getAvailableString(requestMethod, outsideRequest.method());
        contentType = getAvailableString(contentType, outsideRequest.contentType());
        if(Objects.nonNull(outsideRequest.headers())){
            try {
                Map<String, String> annotationHeaders = parseAnnotationHeaders(outsideRequest);
                if(Objects.isNull(headers)){
                    headers = annotationHeaders;
                }else if(Objects.nonNull(annotationHeaders)) {
                    annotationHeaders.putAll(headers);
                    headers = annotationHeaders;
                }
            }catch (Exception ignored){}
        }
        if(StringUtils.isBlank(uri)){
            throw new CnHttpRequestNotFoundException(String.format(ErrorEnum.REQUEST_URL_NOT_FOUND.info(), sourceClientClass.getSimpleName()));
        }else if(StringUtils.isBlank(requestMethod)){
            throw new CnHttpRequestNotFoundException(String.format(ErrorEnum.REQUEST_METHOD_NOT_FOUND.info(), sourceClientClass.getSimpleName()));
        }
        return CnHttpApiWrapper.of(rootApi, uri, requestMethod, contentType, method)
                .addHeaders(headers)
                .setClientMark(outsideClient.value()).setRequestMark(outsideRequest.value());
    }
    private String getAvailableString(String source, String replace){
        return StringUtils.isNotBlank(source) ? source : replace;
    }
    private <K, V> Map<K, V> getAvailableMap(Map<K, V> source, Map<K, V> replace){
        return Objects.nonNull(source) ? source : replace;
    }
    private void doAfterParsingHeaders(CnHttpApiWrapper wrapper){
        Map<String, CnHttpExecuteProcessor> processors = beanFactory.getBeansOfType(CnHttpExecuteProcessor.class);
        processors.forEach((k,v) -> {
            v.afterParsingHeaders(wrapper.getHeaders());
        });
    }
    private Map<String, String> parseAnnotationHeaders(CnHttpRequest outsideRequest){
        String[] headers = outsideRequest.headers();
        if(Objects.nonNull(headers) && headers.length > 0){
            StringBuilder headerBuilder = new StringBuilder("{");
            for (String header : headers) {
                headerBuilder.append(header).append(",");
            }
            headerBuilder.replace(headerBuilder.length() - 1, headerBuilder.length(), "}");
            Map<String, Object> headersMap = JsonUtils.toMap(headerBuilder.toString(), JSONReader.Feature.values());
            Map<String, String> result = new LinkedHashMap<>(headersMap.size());
            headersMap.forEach((k, v) -> {
                result.put(k, String.valueOf(v));
            });
            return result;
        }
        return null;
    }
}
