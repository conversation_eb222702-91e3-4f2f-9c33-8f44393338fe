package cn.aliyun.ryytn.modules.inv.common.service.impl;

import cn.aliyun.ryytn.modules.inv.common.dao.common.dataobject.CommonRolePermissionDO;
import cn.aliyun.ryytn.modules.inv.common.dao.common.dao.CommonRolePermissionMapper;
import cn.aliyun.ryytn.modules.inv.common.service.CommonRolePermissionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【scp_common_role_permission(角色表)】的数据库操作Service实现
* @createDate 2023-08-10 19:53:59
*/
@Service
public class CommonRolePermissionServiceImpl extends ServiceImpl<CommonRolePermissionMapper, CommonRolePermissionDO>
    implements CommonRolePermissionService {

}




