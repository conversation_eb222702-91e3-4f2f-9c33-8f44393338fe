package cn.aliyun.ryytn.modules.inv.common.dao.common.log.request;

import cn.aliyun.ryytn.modules.inv.common.dao.common.request.BasePageQueryRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-11 16:12
 * @description 数据修改日志分页查询请求体
 * 这里dao层限制查询类型，避免业务层对参数疑义
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataLogPageQueryRequest extends BasePageQueryRequest {
    /**
     * 表名后缀
     */
    private String suffix;
    /**
     * 业务编码
     */
    private String bizCode;
    /**
     * 业务主键
     */
    private String bizPk;
    /**
     * logger type
     */
    private String type;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 操作人
     */
    private Integer operatorCode;
}
