package cn.aliyun.ryytn.modules.inv.common.ability.desc;

import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.common.utils.StreamUtils;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-03 09:32
 * @description
 */
public class ValueDescResolver {
    /**
     * Excel 换行符
     */
    public static final String SPACE = String.valueOf((char) 10);
    /**
     * 默认后缀
     */
    public static final String DEFAULT_SUFFIX = "Explain";


    public static <T> T nullToDefault(T value, T defaultValue){
        return Objects.isNull(value) ? defaultValue : value;
    }
    public static <T> Object getAndReplace(T value, Object defaultValue){
        return Objects.isNull(value) ? defaultValue : value;
    }


    public static <T> Map<String, Object> resolve(List<BaseValueDesc<T>> values, T t){
        if(Objects.isNull(values) || values.isEmpty()){ return Maps.newHashMap(); }
        Map<String, List<BaseValueDesc<T>>> typeGroup = StreamUtils.group(values, BaseValueDesc::getType);
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(typeGroup.size());
        if(Objects.nonNull(t)){
            typeGroup.forEach((k, v) -> {
                result.put(
                    k,
                    v.stream().map(item -> {
                        String label = item.getLabel(t);
                        Object value = item.getValue(t);
                        ValueDescDTO descDTO = ValueDescDTO.of(label, value == null ? "-" : String.valueOf(value), item.isHighlight());
                        if(CollectionUtils.isNotEmpty(item.getChildren())){ descDTO.setChildren(resolve(item.getChildren(), t)); }
                        return descDTO;
                    }).collect(Collectors.toList())
                );
            });
        }
        return result;
    }

    public static <T> Map<String, Object> resolve(BaseValueDesc<T>[] values, T t){
        if(Objects.isNull(values) || values.length == 0){ return new HashMap<>(); }
        Map<String, List<BaseValueDesc<T>>> groupBy = Arrays.stream(values)
                .collect(Collectors.groupingBy(BaseValueDesc::getType));
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(groupBy.size());
        if(Objects.nonNull(t)){
            groupBy.forEach((k, v) -> {
                result.put(
                        k,
                        v.stream().map(item -> {
                            String label = item.getLabel(t);
                            Object value = item.getValue(t);
                            return OptionDTO.of(label, value == null ? "-" : String.valueOf(value), item.isHighlight());
                        }).collect(Collectors.toList())
                );
            });
        }
        return result;
    }

    public static <T> String resolve2Str(BaseValueDesc<T>[] values, T t){
        if(Objects.nonNull(values) && values.length > 0 && Objects.nonNull(t)){
            StringBuilder strBuilder = new StringBuilder();
            for (BaseValueDesc<T> value : values) {
                strBuilder.append(value.getLabel(t)).append(value.getValue(t)).append(SPACE);
            }
            return strBuilder.substring(0, strBuilder.length() - 1);
        }
        return "";
    }
}
