package cn.aliyun.ryytn.modules.inv.common.ability.meta.merge;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfoHolder;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaType;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-27 15:55
 * @description 合并字段包装类
 */
@Data
@Accessors(chain = true)
public class MergeFieldWrapper {
    @JsonIgnore
    private Object entity;

    /**
     * 是否换行
     */
    private Integer newline = 1;
    /**
     * 空格
     */
    private Integer spaces = 1;
    /**
     * 值
     */
    private Object value;
    /**
     * 值类型
     * @see MetaType
     */
    private String type = MetaType.TEXT.getCode();

    /**
     * 字体颜色
     */
    private String color;

    /**
     * 扩展字段
     */
    private MetaInfoHolder.Extra extra;
    /**
     * ？提示内容
     */
    private String tooltip;
    /**
     * ？提示内容
     */
    private String prefix;

    /**
     * ？提示内容
     */
    private String suffix;

    private Boolean editable;


    /**
     * 可选项
     */
    private List<OptionDTO> optionList;

    /**
     * 子级
     */
    private List<MergeFieldWrapper> children;

    public static MergeFieldWrapper of(Object value){
        return of(value, null);
    }
    public static MergeFieldWrapper of(Object value, MetaType type){
        return of(null, value, type);
    }
    public static MergeFieldWrapper of(Integer newline, Object value, MetaType type){
        MergeFieldWrapper wrapper = new MergeFieldWrapper();
        wrapper.setValue(value);
        if(Objects.nonNull(newline)){
            wrapper.setNewline(newline);
        }
        if(Objects.nonNull(type)){
            wrapper.setType(type.getCode());
        }
        return wrapper;
    }
}
