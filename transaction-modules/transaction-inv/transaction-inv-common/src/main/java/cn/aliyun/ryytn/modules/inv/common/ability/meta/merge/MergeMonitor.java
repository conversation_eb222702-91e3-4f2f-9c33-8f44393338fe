package cn.aliyun.ryytn.modules.inv.common.ability.meta.merge;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-14 09:29
 * @description
 */
public class MergeMonitor {
    /**
     * 监控上下文
     * 用于注册 监听metaCode -> 监听处理器实体的映射关系
     */
    private static final ThreadLocal<Map<String, BiConsumer<List<MergeFieldWrapper>,MergeFieldWrapper>>> MONITOR_CONTAINER = ThreadLocal.withInitial(() -> null);

    static void start(String fieldName, BiConsumer<List<MergeFieldWrapper>,MergeFieldWrapper> acceptor){
        if(StringUtils.isBlank(fieldName) || Objects.isNull(acceptor)){
            return;
        }
        Map<String, BiConsumer<List<MergeFieldWrapper>,MergeFieldWrapper>> container = MONITOR_CONTAINER.get();
        if(container == null){
            container = new HashMap<>();
            MONITOR_CONTAINER.set(container);
        }
        container.put(fieldName, acceptor);
    }
    static void exit(){
        MONITOR_CONTAINER.remove();
    }
    public static void advice(String fieldName, List<MergeFieldWrapper> merged, MergeFieldWrapper wrapper){
        Map<String, BiConsumer<List<MergeFieldWrapper>,MergeFieldWrapper>> container = MONITOR_CONTAINER.get();
        if(container != null){
            BiConsumer<List<MergeFieldWrapper>,MergeFieldWrapper> acceptor = container.get(fieldName);
            if(Objects.nonNull(acceptor)){
                acceptor.accept(merged, wrapper);
            }
        }
    }
}
