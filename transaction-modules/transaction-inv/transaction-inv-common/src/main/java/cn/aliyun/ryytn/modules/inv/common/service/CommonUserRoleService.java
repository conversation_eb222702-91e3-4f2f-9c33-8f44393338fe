package cn.aliyun.ryytn.modules.inv.common.service;

import cn.aliyun.ryytn.modules.inv.common.dao.common.dataobject.CommonUserRoleDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【scp_common_user_role(用户登陆信息表)】的数据库操作Service
* @createDate 2023-08-10 19:53:59
*/
public interface CommonUserRoleService extends IService<CommonUserRoleDO> {
    /**
     * 获取用户角色
     * @param userId
     * @return
     */
    List<CommonUserRoleDO> getByUserId(String userId);
}
