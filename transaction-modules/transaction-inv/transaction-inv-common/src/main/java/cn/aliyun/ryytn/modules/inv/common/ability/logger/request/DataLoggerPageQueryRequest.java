package cn.aliyun.ryytn.modules.inv.common.ability.logger.request;

import cn.aliyun.ryytn.modules.inv.common.ability.logger.model.LoggerType;
import cn.aliyun.ryytn.modules.inv.common.dao.common.request.BasePageQueryRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-11 16:12
 * @description 数据修改日志分页查询请求体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataLoggerPageQueryRequest extends BasePageQueryRequest {
    /**
     * 表名后缀
     */
    private String suffix;
    /**
     * 业务编码
     */
    private String bizCode;
    /**
     * 业务主键
     */
    private List<String> bizData;
    /**
     * logger type
     */
    private String type = LoggerType.MODIFY;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 操作人
     */
    private Integer operatorCode;
}
