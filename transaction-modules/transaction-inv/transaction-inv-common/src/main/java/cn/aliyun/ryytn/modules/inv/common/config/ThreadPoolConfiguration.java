package cn.aliyun.ryytn.modules.inv.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-06-27 19:17
 * @description
 */
@Configuration
public class ThreadPoolConfiguration {
    /**
     * IO密集，
     * 使用无边队列确保任务不会丢失
     * @return 线程池
     */
    @Bean
    public ThreadPoolExecutor commonIOThreadPool() {
        int core = Runtime.getRuntime().availableProcessors();
        return new ThreadPoolExecutor(
                core + 1 , core * 2, 30L, TimeUnit.MICROSECONDS
                ,new LinkedBlockingQueue<>()
               , new ThreadPoolExecutor.AbortPolicy()
        );
    }
}
