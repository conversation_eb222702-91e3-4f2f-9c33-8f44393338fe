package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-05 20:05
 * @description
 */
@Getter
@AllArgsConstructor
public enum ErrorEnum {

    REQUEST_BODY_EMPTY("Request body is required cannot be empty"),
    PATH_VARIABLE_EMPTY("Path variable is required cannot be empty"),
    REQUEST_PART_EMPTY("Request part is required cannot be empty"),
    REQUEST_PART_NOT_SUPPORT("Request part is illegal file type"),
    PATH_VARIABLE_CAN_CONVERT_TO_JSON("The parameter is marked as PathVariable, but it can be converted to jsonObject"),
    REQUEST_HEADER_EMPTY("Request header is required cannot be empty"),
    REQUEST_BODY_CANNOT_CONVERT_TO_JSON("The parameter is marked as RequestBody, but it cannot be converted to json"),
    FALLBACK_PARSE_ERROR("Fallback strategy client is not a OutsideClient, fallback factory is %s"),
    FALLBACK_IS_NO_MARK("Fallback strategy client is no mark OutsideClient, fallback factory is %s"),
    HTTP_METHOD_NOT_SUPPORT("Outside http request method not support, only support GET/POST/PUT/DELETE"),
    REQUEST_URL_NOT_FOUND("Find client api request url on %s is not found"),
    REQUEST_METHOD_NOT_FOUND("Find client api request method on %s not found"),
    SEND_HTTP_ILLEGAL_PARAMS("Illegal request parameters, wrapper is %s, returnType is %s, httpMethod is %s"),
    SEND_HTTP_ILLEGAL_URL("Illegal request url: %s"),
    URI_FORMAT_ERROR("Uri format error"),
    ;

    private final String info;

    public String info(){
        return info;
    }
}
