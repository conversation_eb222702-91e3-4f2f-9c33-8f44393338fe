package cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify;

import lombok.Data;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-11 21:33
 * @description
 */
@Data
public class BizDataModifyLogInfo {
    /**
     * '业务编码'
     */
    private String bizCode;
    /**
     * 业务主键
     */
    private String bizPk;
    /**
     * 修改前的值
     */
    private List<String> beforeModify;
    /**
     * 修改后的值
     */
    private List<String> afterModify;
    /**
     * 修改原因
     */
    private List<String> modifyReason;
    /**
     * 变更时间
     */
    private String modifyTime;

    private Long version;
    /**
     * 操作人
     */
    private String operatorCode;
    private String operatorName;


    private BizDataModifyLogInfo(List<String> beforeModify, List<String> afterModify, List<String> modifyReason) {
        this.beforeModify = beforeModify;
        this.afterModify = afterModify;
        this.modifyReason = modifyReason;
    }

    public static BizDataModifyLogInfo of(List<String> beforeModify, List<String> afterModify, List<String> modifyReason) {
        return new BizDataModifyLogInfo(beforeModify, afterModify, modifyReason);
    }
}
