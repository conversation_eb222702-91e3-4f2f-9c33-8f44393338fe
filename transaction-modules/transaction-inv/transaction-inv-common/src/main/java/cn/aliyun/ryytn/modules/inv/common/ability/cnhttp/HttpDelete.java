package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp;

import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;

import java.net.URI;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-07 14:47
 * @description
 */
public class HttpDelete extends HttpEntityEnclosingRequestBase {
    public HttpDelete(String url) {
        this.setURI(URI.create(url));
    }
    public HttpDelete(URI uri) {
        this.setURI(uri);
    }

    public String getMethod() {
        return HttpMethod.DELETE.name();
    }
}