package cn.aliyun.ryytn.modules.inv.common.ability.meta;


import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Collection;


/**
 * <AUTHOR> (风学)
 * @since 2023/12/21
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MetaInfo {

    /**
     * 列唯一id，与list数组元素的key对应
     */
    private String code;
    /**
     * 表头文字，列名
     */
    private String name;
    /**
     * 父meta， 用户多维表格
     */
    private String parent;

    /**
     * 是否为主键， 如果一个类有多个主键，则只取第一个为主键
     * @return true是，false否
     */
    private Boolean primaryKey;
    private Integer primarySort;

    /**
     * boolean,是否隐藏该列
     */
    private Boolean hidden;
    /**
     * 表格头tooltip
     */
    private String tooltip;

    /**
     * format 根据d3format 进行了封装，更加简单易读且没有学习成本\n"
     * + "● float 保留2位小数，千分位自动用,分割；\n"
     * + "● float1 保留1位小数，千分位自动用,分割；\n"
     * + "● float4 保留4位小数，千分位自动用,分割；\n"
     * + "● float5  保留5位小数，千分位自动用,分割；\n"
     * + "● int 保留整数，千分位自动用,分割；\n"
     * + "● percent0 保留百分比，0位小数；\n"
     * + "● percent 保留百分比，2位小数；\n"
     * + "● percent1 保留百分比，1位小数；\n"
     * + "● percent4 保留百分比，4位小数；
     */
    private String format;

    /**
     * 设置冻结该列，如果不设置当前字段，则表示不冻结 left - 左侧冻结 / right - 右侧冻结
     */
    private String fixed;

    /**
     * 列排序顺序
     */
    private int sort;

    /**
     * 字段校验标签
     */
    private String validateTag;

    /**
     * 扩展字段
     */
    private MetaInfoHolder.Extra extra;

    private String type;

    /**
     * 表示维度还是字段
     * 维度 false
     * 字段 true
     */
    private Boolean field;
    private String fieldButtonType;
    /**
     * Edit 为true 则表示该字段在弹框内可以编辑
     * （不传则默认不可编辑）
     *
     * @return
     */
    private boolean modalEdit;

    /**
     * 为 true 则表示该字段不会出现在弹框中
     * （不传则默认在弹框中有）
     *
     * @return
     */
    private boolean modalHide;
    /**
     * 是否合并
     * true 代表合并
     * false 代表不合并
     *
     * @return
     */
    private Boolean isMerged;
    /**
     * 前端表格字段是否开启排序
     */
    private Boolean sortable;

    /**
     * 表头是否有下拉框
     */
    private Boolean filter;

    /**
     * 对象字段类型
     */
    private Class<?> fieldClass;

    private String orderColumn;

    private Collection<MetaInfo> children;

    /**
     * '组别'
     */
    private String groupName;
    /**
     * 配置编码
     */
    private String configCode;

    @ApiModelProperty("合并")
    private String merge;

    /**
     * 合并名称
     */
    @ApiModelProperty("合并名称")
    private String mergerName;

    /**
     * 当前列别名
     */
    @ApiModelProperty("当前列别名")
    private String anotherName;

    public MetaInfo(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MetaInfo of(String code, String name, Class<?> fieldClass){
        MetaInfo metaInfo = new MetaInfo(code, name);
        metaInfo.setFieldClass(fieldClass);
        return metaInfo;
    }

    public static MetaInfo of(String code, String name){
        return new MetaInfo(code, name);
    }

}
