package cn.aliyun.ryytn.modules.inv.common.ability.split.manager.dto;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-03-11 15:08
 * @description
 */
@Data
@ToString
public class SplitFieldConfig {

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 指标编码，scp_split_index_config表对应
     */
    private String indexCode;

    /**
     * 标识此列是否是公用的， split分割时有用
     */
    private Boolean common;

    /**
     * 标识此列是否是动态指标列
     */
    private Boolean dynamic;

    /**
     * 子meta, 多维分隔有用
     */
    private String childMeta;

    /**
     * 实体类的属性名
     */
    private String fieldCode;

    /**
     * 指标文本描述
     */
    private String fieldName;

    /**
     * 用于meta的顺序
     */
    private Integer sort;

    /**
     * 匹配某个场景下生效，多个使用逗号分隔
     */
    private String mapping;

    /**
     * info提示
     */
    private String info;

    /**
     * 是否隐藏，false不隐藏，ture隐藏
     */
    private Boolean hidden;

    /**
     * 给定一组数据，自定义返回需要填充的值
     */
    private String customClass;

    /**
     * 设置依赖的指标数据行, 需要填写其他分隔字段的字段名
     */
    private String depends;
}
