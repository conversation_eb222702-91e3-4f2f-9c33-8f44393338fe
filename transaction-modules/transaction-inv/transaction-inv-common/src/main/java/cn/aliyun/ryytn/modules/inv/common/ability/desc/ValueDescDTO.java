package cn.aliyun.ryytn.modules.inv.common.ability.desc;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-06-13 15:19
 * @description 基础的值描述接口
 */
@Data
@Accessors(chain = true)
public class ValueDescDTO {
    private String label;
    private Object value;
    private String type;
    private Map<String, Object> children;
    private Boolean highlight = false;
    private Boolean bold = false;
    private Integer newline = 1;
    private String color = "black";

    public ValueDescDTO() {}

    public ValueDescDTO(Object value, String label) {
        this.value = value;
        this.label = label;
    }
    public ValueDescDTO(Object value, String label, String type) {
        this.value = value;
        this.label = label;
        this.type = type;
    }

    public static ValueDescDTO of(Object value, String label){
        return new ValueDescDTO(value, label);
    }
    public static ValueDescDTO of(Object value, String label, String type){
        return new ValueDescDTO(value, label, type);
    }
    public static ValueDescDTO of(String label, Object value, Boolean highlight){
        ValueDescDTO optionDTO = new ValueDescDTO(value, label);
        optionDTO.setHighlight(highlight);
        return optionDTO;
    }
}
