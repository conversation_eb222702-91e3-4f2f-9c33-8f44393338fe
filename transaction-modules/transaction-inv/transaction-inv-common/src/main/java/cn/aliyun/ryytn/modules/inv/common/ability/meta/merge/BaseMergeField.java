package cn.aliyun.ryytn.modules.inv.common.ability.meta.merge;

import java.util.function.Function;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 18:17
 * @description 合并字段配置
 * 这里的T表示传入的对象类型，用于getReplaceInfo返回字段值使用
 */
public interface BaseMergeField<T> {
    /**
     * 返回一个值，用于format占位符的内容
     */
    Function<T, MergeFieldWrapper> getWrapperCreator();

    /**
     * 该合并字段是否生效
     * 只有为true时才进行合并
     * @return true生效 false失效
     */
    default Function<T, Boolean> getRequired(){
        return item -> true;
    }
    /**
     * 提供对外暴露函数
     * 调用getReplaceInfo拿到替换内容
     * @param t
     * @return
     */
    default MergeFieldWrapper doGetWrapper(T t){
        Function<T, MergeFieldWrapper> replaceInfo;
        if((replaceInfo = getWrapperCreator()) == null){ return null; }
        return replaceInfo.apply(t);
    }

    default Boolean doGetRequired(T t){
        Function<T, Boolean> replaceInfo;
        if((replaceInfo = getRequired()) == null){ return true; }
        return replaceInfo.apply(t);
    }
}
