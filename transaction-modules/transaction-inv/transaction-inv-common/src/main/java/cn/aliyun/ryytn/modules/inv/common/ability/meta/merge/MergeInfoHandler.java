package cn.aliyun.ryytn.modules.inv.common.ability.meta.merge;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.AnnotationExtraUtils;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfoHolder;
import cn.aliyun.ryytn.modules.inv.common.enums.ColorEnum;
import cn.aliyun.ryytn.modules.inv.common.utils.ReflectionUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StrUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StreamUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.BiConsumer;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 18:29
 * @description 合并字段处理器
 */
public class MergeInfoHandler {

    public static void advice(String code, BiConsumer<List<MergeFieldWrapper>,MergeFieldWrapper> acceptor){
        MergeMonitor.start(code, acceptor);
    }

    public static <T> void merge(List<T> list, String... paramMappings) {
        try {
            if(Objects.isNull(list)){ return; }
            for (T t : list) { merge(t, false, null); }
        }finally {
            MergeMonitor.exit();
        }
    }

    public static <T> void merge(List<T> list){
        try {
            if(Objects.isNull(list)){ return; }
            for (T t : list) { merge(t, false); }
        }finally {
            MergeMonitor.exit();
        }
    }
    public static <T> void merge(T t){
        merge(t, true);
    }
    public static <T> void merge(T t, String... paramMappings){
        merge(t, true, paramMappings);
    }
    public static <T> void merge(T t, boolean isExitMonitor, String... paramMappings){
        try {
            if(Objects.isNull(t)){ return; }
            List<Field> fields = ReflectionUtils.getFieldsByAnnotation(t.getClass(), MergeField.class);
            Map<String, List<Field>> group = StreamUtils.group(fields, field -> field.getAnnotation(MergeField.class).value());
            group.forEach((k, v) -> {
                v.sort(Comparator.comparingInt(field -> field.getAnnotation(MergeField.class).sort()));
                List<MergeFieldWrapper> merged = new ArrayList<>(v.size());
                for (Field field : v) {
                    MergeField annotation = field.getAnnotation(MergeField.class);
                    if(paramMappings != null && annotation.mapping() != null && annotation.mapping().length > 0){
                        // 注解配置的mapping
                        List<String> configMapping = Arrays.asList(annotation.mapping());
                        boolean mappingFlag = true;
                        for (String paramMapping : paramMappings) {
                            // 只要配置的mapping不包含传入的mapping，则表示不通过匹配。
                            if (configMapping.contains(paramMapping)) {
                                mappingFlag = true;
                                break;
                            }else {
                                mappingFlag = false;
                            }
                        }
                        if(!mappingFlag){ continue; }
                    }
                    String finalValue = StrUtils.format(annotation.format(), annotation.nullable(), ReflectionUtils.getFieldValue(field, t));
                    if(finalValue != null){
                        MergeFieldWrapper wrapper = MergeFieldWrapper.of(annotation.newline(), finalValue, annotation.metaType());
                        if(StringUtils.isNotBlank(annotation.extra())){
                            Object finalExtra = AnnotationExtraUtils.getExtra(annotation.metaType(), annotation.extra());
                            MetaInfoHolder.createExtra(finalExtra).set(wrapper);
                        }
                        wrapper.setEntity(t);
                        wrapper.setPrefix(annotation.prefix());
                        wrapper.setSuffix(annotation.suffix());
                        wrapper.setColor(ColorEnum.EMPTY.equals(annotation.color()) ? null : annotation.color().getCode());
                        wrapper.setSpaces(annotation.spaces());
                        wrapper.setEditable(annotation.editable());
                        merged.add( wrapper );
                        MergeMonitor.advice(field.getName(), merged, wrapper);
                        wrapper.setEntity(null);
                    }
                }
                ReflectionUtils.setFieldValue(t, k, merged);
            });
        }catch (Exception e){
            MergeMonitor.exit();
            throw e;
        }finally {
            if(isExitMonitor){
                MergeMonitor.exit();
            }
        }
    }
}
