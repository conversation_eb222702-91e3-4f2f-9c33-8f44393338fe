package cn.aliyun.ryytn.modules.inv.common.model;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OptionDTO {
    private String label;
    private String value;
    private String type;
    private Integer sort;
    private List<OptionDTO> children;
    private Boolean highlight = false;

    public OptionDTO() {}

    public OptionDTO(String value, String label) {
        this.value = value;
        this.label = label;
    }
    public OptionDTO(String value, String label, String type) {
        this.value = value;
        this.label = label;
        this.type = type;
    }

    public static OptionDTO of(String value, String label) {
        return new OptionDTO(value, label);
    }
    public static OptionDTO of(String value, String label, Integer sort) {
        return new OptionDTO(value, label).setSort(sort);
    }
    public static OptionDTO of(Object value, String label, String type){
        return new OptionDTO(String.valueOf(value), label, type);
    }
    public static OptionDTO of(String label, String value, Boolean highlight){
        OptionDTO optionDTO = new OptionDTO(value, label);
        optionDTO.setHighlight(highlight);
        return optionDTO;
    }
    /**
     * 添加子元素
     *
     * @param child
     */
    public void addChild(OptionDTO child) {
        if (child == null) {
            return;
        }
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
    }

    /**
     * 添加子元素，去重
     *
     * @param child
     */
    public OptionDTO addChildUnique(OptionDTO child) {
        OptionDTO ret = child;
        if (child == null) {
            return ret;
        }
        if (children == null) {
            children = new ArrayList<>();
        }
        boolean hasSame = false;
        for (OptionDTO vo : children) {
            if (StringUtils.equals(vo.getValue(), child.getValue())) {
                hasSame = true;
                ret = vo;
                break;
            }
        }
        if (!hasSame) {
            children.add(child);
        }
        return ret;
    }
}
