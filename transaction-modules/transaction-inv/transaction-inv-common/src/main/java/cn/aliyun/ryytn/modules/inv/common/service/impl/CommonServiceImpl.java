package cn.aliyun.ryytn.modules.inv.common.service.impl;

import cn.aliyun.ryytn.modules.inv.common.enums.EnumCollect;
import cn.aliyun.ryytn.modules.inv.common.service.CommonService;
import cn.aliyun.ryytn.modules.inv.common.util.LogUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMap.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 系统通用服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CommonServiceImpl implements CommonService {

    private final static String ENUM_CODE = "code";
    private final static String ENUM_NAME = "name";
    private final static String ENUM_LABEL = "label";
    private final static String ENUM_VALUE = "value";

    /**
     * 系统枚举
     */
    private static Map<String, List<Object>> SYSTEM_ENUM_MAP = null;

    private static final Builder<String, List<Object>> BUILDER = ImmutableMap.builder();

    @PostConstruct
    public void init() {
        LogUtils.info(log, "Start Init System Enum ");
        List<String> enumNameList = new LinkedList<>();
        for (EnumCollect enumCollect : EnumCollect.values()) {
            getSystemEnumList(enumCollect);
            enumNameList.add(enumCollect.getName());
        }
        SYSTEM_ENUM_MAP = BUILDER.build();
        LogUtils.info(log, "End Init System Enum {}", enumNameList);
    }

    private void getSystemEnumList(EnumCollect enumCollect) {
        Class<?> aClass = null;
        try {
            aClass = Class.forName(enumCollect.getPath());
        } catch (Exception e) {
            LogUtils.error(log, "system enum init enumName {} error {}  ", enumCollect.getPath(), e.getMessage());
        }
        // 如果类不为空 并且是枚举类
        if (aClass != null && aClass.isEnum()) {
            Object[] enumConstants = aClass.getEnumConstants();
            List<Object> mapList = new ArrayList<>();
            for (Object enumConstant : enumConstants) {
                Map<String, Object> enumDescMap = new HashMap<>(16);
                Field[] fields = aClass.getDeclaredFields();
                for (Field field : fields) {
                    // 非静态属性 ｜ 非枚举常量
                    if (!field.isSynthetic() && !field.isEnumConstant()) {
                        field.setAccessible(true);
                        // 将枚举中的 code和name转换成前端标签
                        String name = convertEnumFieldName(field.getName());
                        try {
                            Object o = field.get(enumConstant);
                            enumDescMap.put(name, o);
                        } catch (IllegalAccessException e) {
                            // doNothing
                        }
                    }
                }
                // 如果没有属性则不进行枚举转换处理
                if (CollectionUtils.isNotEmpty(enumDescMap.values())) {
                    mapList.add(enumDescMap);
                }
            }
            BUILDER.put(enumCollect.getName(), mapList);
        }
    }

    private String convertEnumFieldName(String name) {
        if (ENUM_NAME.equals(name)) {
            name = ENUM_LABEL;
        }
        if (ENUM_CODE.equals(name)) {
            name = ENUM_VALUE;
        }
        return name;
    }

    @Override
    public Map<String, List<Object>> getSystemEnum(String enumCode) {
        if (StringUtils.isBlank(enumCode)) {
            return SYSTEM_ENUM_MAP;
        } else {
            List<Object> enumValueList = SYSTEM_ENUM_MAP.get(enumCode);
            HashMap<String, List<Object>> map = new HashMap<>(16);
            map.put(enumCode, enumValueList);
            return map;
        }
    }

}
