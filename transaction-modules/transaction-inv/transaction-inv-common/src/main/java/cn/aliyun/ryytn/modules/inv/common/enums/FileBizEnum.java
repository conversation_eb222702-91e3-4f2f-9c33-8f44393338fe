package cn.aliyun.ryytn.modules.inv.common.enums;

/**
 * 文件业务枚举
 *
 * <AUTHOR>
 */
public enum FileBizEnum {

    /**
     * BIZ_A
     */
    BIZ_A("BIZ_A", "业务A");

    private final String code;
    private final String name;

    FileBizEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static FileBizEnum getEnumByCode(String code) {
        for (FileBizEnum fileBizEnum:values()) {
            if(fileBizEnum.getCode().equals(code)){
                return fileBizEnum;
            }
        }
        return null;
    }
}
