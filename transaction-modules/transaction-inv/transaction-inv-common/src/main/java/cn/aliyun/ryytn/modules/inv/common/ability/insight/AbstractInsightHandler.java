package cn.aliyun.ryytn.modules.inv.common.ability.insight;

import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.ReflectionUtils;
import org.springframework.core.ResolvableType;

import java.util.*;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 11:55
 * @description 抽象数据洞察， 仅提供通用的获取数据方法
 * 这里的T表示请求参数类型
 * R表示查询结果类型
 * U表示最终返回的结果类型
 */
public abstract class AbstractInsightHandler<T, R, F> implements InsightHandler<T, R> {
    /**
     * 请求参数类型
     */
    private final Class<T> requestClass;
    /**
     * 查询结果列表的数据类型
     */
    private final Class<R> queryDataClass;
    /**
     * 最终返回的数据类型
     */
    private final Class<F> finalClass;
    /**
     * 数据处理器
     */
    private final List<DataProcessor<T, R, F>> DATA_PROCESSORS = new ArrayList<>(1);

    public AbstractInsightHandler() {
        register();
        ResolvableType resolvableType = ResolvableType.forClass(this.getClass());
        while (!Objects.equals(resolvableType.getSuperType().getRawClass(), Object.class)) {
            resolvableType = resolvableType.getSuperType();
        }
        this.requestClass = (Class<T>) resolveClassForIndex(resolvableType, 0);
        this.queryDataClass = (Class<R>) resolveClassForIndex(resolvableType, 1);
        this.finalClass = (Class<F>) resolveClassForIndex(resolvableType, 2);
        init();
    }

    private Class<?> resolveClassForIndex(ResolvableType resolvableType, int index) {
        return resolvableType.getGeneric(index).resolve();
    }

    protected void init() {
    }

    protected void register() {
    }

    protected void registerProcessor(DataProcessor<T, R, F> process) {
        DATA_PROCESSORS.add(process);
    }

    protected void registerHandler(InsightMapping insightType) {
        InsightManager.register(insightType, this);
    }

    public List<F> getData(Map<String, Object> queryMap) {
        T request = resolveRequest(queryMap);
        if (Objects.isNull(request) || !checkType(requestClass, request.getClass())) {
            return Collections.emptyList();
        }
        List<R> queryData = queryData(request);
        return doProcessor(request, queryData);
    }

    /**
     * 允许指定outClazz 返回值指定的类型结果，
     * 但前提是与U类型相同
     *
     * @param request  请求
     * @param outClazz 指定返回class
     * @param <U>
     * @return
     */
    public <U> List<U> getData(Map<String, Object> request, Class<U> outClazz) {
        if (Objects.isNull(request) || !checkType(requestClass, request.getClass()) || !checkType(finalClass, outClazz)) {
            return Collections.emptyList();
        }
        return getData(request).stream()
                .map(outClazz::cast)
                .collect(Collectors.toList());
    }

    private boolean checkType(Class<?> sourceClass, Class<?> targetClass) {
        if (sourceClass.equals(targetClass)) {
            return true;
        }
        Class<?> temp = targetClass;
        while (!Object.class.equals(temp)) {
            temp = temp.getSuperclass();
            if (sourceClass.equals(temp)) {
                return true;
            }
        }
        Class<?>[] interfaces = targetClass.getInterfaces();
        for (Class<?> anInterface : interfaces) {
            if (sourceClass.equals(anInterface)) {
                return true;
            }
        }
        return false;
    }

    public Long getCount(Map<String, Object> queryMap) {
        T request = resolveRequest(queryMap);
        if (Objects.isNull(request) || !requestClass.equals(request.getClass())) {
            return 0L;
        }
        return queryCount(request);
    }

    private List<F> doProcessor(T request, List<R> queryData) {
        // 存在数据处理器时，执行并返回处理器的结果
        if (!DATA_PROCESSORS.isEmpty()) {
            List<F> result = null;
            for (DataProcessor<T, R, F> dataProcessor : DATA_PROCESSORS) {
                result = dataProcessor.processed(request, queryData, result);
            }
            return result;
        }
        // 如果没有处理器，则判断返回值类型F 和 查询结果类型R 是否相同，相同则强制类型转换
        if (queryDataClass.equals(finalClass)) {
            return queryData.stream().map(finalClass::cast).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 默认空实现， 子类需要分页则重写该方法
     *
     * @param t
     * @return
     */
    @Override
    public Long queryCount(T t) {
        return 0L;
    }

    private T resolveRequest(Map<String, Object> queryMap) {
        if (Objects.isNull(queryMap)) {
            return ReflectionUtils.createInstance(requestClass);
        }
        for (String key : queryMap.keySet()) {
            Object value = queryMap.get(key);
            if (value instanceof String) {
                String valueStr = String.valueOf(value);
                if ((valueStr.contains(","))) {
                    String[] split = valueStr.split(",");
                    queryMap.put(key, split);
                }
            }
        }
        return JsonUtils.toObject(queryMap, requestClass);
    }

    public interface DataProcessor<T, R, F> {
        List<F> processed(T request, List<R> rawQueryData, List<F> pre);
    }
}
