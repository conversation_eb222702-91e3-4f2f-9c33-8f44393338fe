package cn.aliyun.ryytn.modules.inv.common.utils;

import cn.aliyun.ryytn.modules.inv.common.dao.common.dataobject.CommonDO;
import cn.aliyun.ryytn.modules.inv.common.dao.common.dataobject.TenantDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

/**
 * <AUTHOR> (凤学)
 * @since 2023/8/11
 */
public class MapperUtils {

    public static <T extends CommonDO> LambdaQueryWrapper<T> queryWrapper(Class<T> clz) {
        LambdaQueryWrapper<T> wrapper = Wrappers.lambdaQuery();
        wrapper.setEntityClass(clz);
        return wrapper;
    }

    public static <T extends CommonDO> LambdaUpdateWrapper<T> updateWrapper(Class<T> clz) {
        LambdaUpdateWrapper<T> wrapper = Wrappers.lambdaUpdate();
        wrapper.setEntityClass(clz);
        return wrapper;
    }

    public static <T extends TenantDO> LambdaQueryWrapper<T> queryWrapper(Class<T> clz, String tenantCode) {
        LambdaQueryWrapper<T> wrapper = Wrappers.lambdaQuery();
        wrapper.setEntityClass(clz);
        wrapper.eq(TenantDO::getTenantCode,tenantCode);
        return wrapper;
    }

    public static <T extends TenantDO> LambdaUpdateWrapper<T> updateWrapper(Class<T> clz,String tenantCode) {
        LambdaUpdateWrapper<T> wrapper = Wrappers.lambdaUpdate();
        wrapper.setEntityClass(clz);
        wrapper.eq(TenantDO::getTenantCode,tenantCode);
        return wrapper;
    }
}
