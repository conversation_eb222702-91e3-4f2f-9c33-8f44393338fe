package cn.aliyun.ryytn.modules.inv.common.convent;

import cn.aliyun.ryytn.modules.inv.common.utils.IndexCalculator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext.ConvertProcessExtensible;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.model.ConvertExtensibleWrapper;

import java.math.BigDecimal;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-23 10:51
 * @description 小数清除0转String
 */
public class DecimalToPercentConvertExt implements ConvertProcessExtensible<String> {
    @Override
    public String doConvert(ConvertExtensibleWrapper ew) throws IllegalAccessException {
        Object o = ew.getSourceField().get(ew.getSourceObject());
        if (o instanceof Number) {
            return String.format("%s%%", IndexCalculator.clear0Decimal(
                 IndexCalculator.ratioToPercent(BigDecimal.valueOf(((Number) o).doubleValue()), 2)
            ));
        } else if (o instanceof String) {
            String numberStr = (String) o;
            if (numberStr.endsWith("%")) {
                return numberStr;
            }
            return String.format("%s%%", IndexCalculator.clear0Decimal(
                    IndexCalculator.ratioToPercent(BigDecimal.valueOf(Double.parseDouble(numberStr)), 2)
            ));
        }
        return null;
    }
}
