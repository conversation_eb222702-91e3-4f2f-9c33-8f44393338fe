package cn.aliyun.ryytn.modules.inv.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-07 11:03
 * @description 供应方式
 */
@Getter
@AllArgsConstructor
public enum SupplyTypeEnum {
    TRANSFER("transfer", "调拨入库"),
    PURCHASE("purchase", "采购入库"),
    ;
    private final String code;
    private final String name;

    public static String getNameByCode(String code){
        SupplyTypeEnum supply = getByCode(code);
        return supply == null ? "" : supply.name;
    }
    public static SupplyTypeEnum getByCode(String code){
        if(StringUtils.isBlank(code)){ return null; }
        for (SupplyTypeEnum supply : values()) {
            if(StringUtils.equals(supply.code, code)){ return supply; }
        }
        return null;
    }

    public boolean isThis(String code){
        return StringUtils.equalsIgnoreCase(code, getCode());
    }
}
