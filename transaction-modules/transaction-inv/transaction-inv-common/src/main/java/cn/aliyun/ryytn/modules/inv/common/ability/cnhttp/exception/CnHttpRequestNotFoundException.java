package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-04 15:46
 * @description 外部方法未找到异常
 */
public class CnHttpRequestNotFoundException extends RuntimeException {
    public CnHttpRequestNotFoundException() {
    }

    public CnHttpRequestNotFoundException(ErrorEnum errorEnum) {
        this(errorEnum.info());
    }

    public CnHttpRequestNotFoundException(String message) {
        super(message);
    }

    public CnHttpRequestNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public CnHttpRequestNotFoundException(Throwable cause) {
        super(cause);
    }

    public CnHttpRequestNotFoundException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
