package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.proxy;

import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.annotation.CnHttpClient;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception.CnHttpCreateProxyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.ProxyFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.ClassMetadata;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;

import java.io.IOException;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-04 14:51
 * @description 外部客户端代理创建器
 */
@Slf4j
public class CnHttpClientProxyCreator implements BeanFactoryPostProcessor {
    /**
     * 由于希望扫描包通过配置文件设置，故不采用注解 + import方式
     */
    private final String CLIENT_PACKAGE_KEY = "cnhttp.client-packages";
    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        ConfigurableEnvironment environment = beanFactory.getBean(ConfigurableEnvironment.class);
        String packages = environment.getProperty(CLIENT_PACKAGE_KEY);
        if(StringUtils.isBlank(packages)){ return; }
        // 按照包解析
        String[] clientPackages = resolveClientPackages(packages);
        if(clientPackages == null){ return; }
        PathMatchingResourcePatternResolver patternResolver = new PathMatchingResourcePatternResolver();
        CachingMetadataReaderFactory metaFactory = new CachingMetadataReaderFactory();
        for (String clientPackage : clientPackages) {
            clientPackage = String.format("classpath*:%s/**/*.class", clientPackage.replace('.', '/'));
            try {
                Resource[] resources = patternResolver.getResources(clientPackage);
                for (Resource resource : resources) {
                    MetadataReader metadataReader = metaFactory.getMetadataReader(resource);
                    Object proxy = createProxy(metadataReader, beanFactory);
                    if(proxy != null){
                        beanFactory.registerSingleton(metadataReader.getClassMetadata().getClassName(), proxy);
                        log.info("CnHttp client proxy created: {}", metadataReader.getClassMetadata().getClassName());
                    }
                }
            } catch (IOException | ClassNotFoundException e) {
                throw new CnHttpCreateProxyException(e);
            }

        }
    }
    private String[] resolveClientPackages(String clientPackages){
        if(StringUtils.isBlank(clientPackages)){ return null; }
        return clientPackages.split(",");
    }
    private Object createProxy(MetadataReader metadataReader, ConfigurableListableBeanFactory beanFactory) throws IOException, ClassNotFoundException {
        ClassMetadata classMetadata = metadataReader.getClassMetadata();
        AnnotationMetadata annotationMetadata = metadataReader.getAnnotationMetadata();
        if(hasAnnotationClass(CnHttpClient.class, annotationMetadata) && hasInterface(classMetadata)){
            return doCreateProxy(beanFactory, classMetadata);
        }
        return null;
    }
    private Object doCreateProxy(ConfigurableListableBeanFactory beanFactory, ClassMetadata classMetadata) throws ClassNotFoundException {
        Class<?> sourceClass = Class.forName(classMetadata.getClassName());
        ProxyFactory proxyFactory = new ProxyFactory();
        proxyFactory.setInterfaces(Class.forName(classMetadata.getClassName()));
        proxyFactory.addAdvice(new CnHttpRequestMethodInterceptor(beanFactory, sourceClass));
        return proxyFactory.getProxy();
    }
    private boolean hasAnnotationClass(Class<?> clazz, AnnotationMetadata annotationMetadata){
        return annotationMetadata.hasAnnotation(clazz.getName()) || annotationMetadata.hasMetaAnnotation(clazz.getName());
    }
    private boolean hasInterface(ClassMetadata classMetadata){
        return classMetadata.isInterface();
    }


}
