package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp;

import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception.CnHttpUriFormatException;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception.ErrorEnum;
import cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.proxy.ParameterResolver;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-07 16:00
 * @description
 */
public class URLUtil {
    /**
     * 检查url是否非法
     * @param url url
     * @return true非法，false合法
     */
    public static boolean checkUrlIllegal(String url){
        return StringUtils.isBlank(url);
    }

    /**
     * 构建pathVariable
     * @param baseUrl 基础url
     * @param pathVariable pathVariable
     * @return 构建后的url
     */
    public static String buildPathVariable(String baseUrl, Map<String, String> pathVariable) {
        if(StringUtils.isBlank(baseUrl)){ return baseUrl; }
        if(baseUrl.contains("{") && (pathVariable == null || pathVariable.isEmpty()) ){
            throw new CnHttpUriFormatException(ErrorEnum.URI_FORMAT_ERROR);
        }
        if(!baseUrl.contains("{")){  return baseUrl; }
        for (Map.Entry<String, String> entry : pathVariable.entrySet()) {
            String variable = "{"+ entry.getKey() +"}";
            baseUrl = baseUrl.replace(variable,  String.valueOf(formatValue(entry.getValue())));
        }
        return baseUrl;
    }

    /**
     * 拼接参数到url上
     * @param baseUrl 基础url
     * @param params 参数
     * @return 拼接后的url
     */
    public static String buildParamsToUrl(String baseUrl, Map<String, Object> params) {
        if(params == null || params.isEmpty()){ return baseUrl; }
        if(baseUrl.endsWith("/")){ baseUrl = baseUrl.substring(0, baseUrl.length() - 1); }
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append("?");
        params.forEach( (k, v) -> {
            Object value = formatValue(v);
            if(value != null){
                urlBuilder.append(k).append("=");
                urlBuilder.append(value);
                urlBuilder.append("&");
            }
        });
        return urlBuilder.substring(0, urlBuilder.length() - 1);
    }

    /**
     * 格式化值
     * @param value 参数值
     * @return 格式后的value
     */
    private static Object formatValue(Object value){
        if(value == null){  return null; }
        try {
            String str = value.toString();
            return filter(str).brackets().doubleQuotes().colonToEqualSign().get();
        }catch (CnHttpUriFormatException e){
            return null;
        }
    }

    public static StringFilter filter(Object value){
        if(value == null){ return StringFilter.of(null); }
        try {
            if(JSONValidator.from(value.toString()).validate()){
                Map<?, ?> map = ParameterResolver.parseMap(value);
                if(map == null){
                    String json = JSON.toJSONString(value);
                    return StringFilter.of(json);
                }
                // url的参数，不允许出现复合类型
                throw new CnHttpUriFormatException();
            }
            return StringFilter.of(value.toString());
        }catch (CnHttpUriFormatException e){
            throw e;
        }catch (Exception e){
            return StringFilter.of(String.valueOf(value));
        }
    }
    public static class StringFilter {
        private String str;
        public static StringFilter of(String str){
            return new StringFilter(str);
        }
        public StringFilter(String str){
            if(str == null){ str = ""; }
            this.str = str;
        }
        public StringFilter brackets(){
            str = str.replace("[", "")
                    .replace("]", "");
            return this;
        }
        public StringFilter doubleQuotes(){
            str = str.replace("\"", "");
            return this;
        }
        public StringFilter colonToEqualSign(){
            str = str.replace(":", "=");
            return this;
        }
        public StringFilter spaceToEncode(){
            str = str.replace(" ", "%20");
            return this;
        }
        public String get(){
            return str;
        }
    }
}
