package cn.aliyun.ryytn.modules.inv.common.service;

/**
 * <AUTHOR>
 */
public interface CommonDictService {

    /**
     * 根据code查询字典
     *
     * @param dictCode
     * @return
     */
    String queryDictValueByCode(String dictCode);

    /**
     * 根据code查询字典
     *
     * @param dictCode
     * @param clazz 类型
     * @return
     */
    <T> T queryDictValueByCode(String dictCode, Class<T> clazz);

}
