package cn.aliyun.ryytn.modules.inv.common.dao.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-12 17:40
 * @description
 */
@Getter
@AllArgsConstructor
public enum QueryOrderEnum {
    ASC("asc"),
    DESC("desc");
    private final String code;

    public static QueryOrderEnum getOrderByCode(Object code) {
        if(Objects.nonNull(code)){
            String str = code.toString();
            for (QueryOrderEnum orderType : values()) {
                if(orderType.code.equalsIgnoreCase(str)){
                    return orderType;
                }
            }
        }
        return ASC;
    }
}
