//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.aliyun.ryytn.modules.inv.common.exception;


import cn.aliyun.ryytn.modules.inv.common.enums.LogLevelEnum;

public class ErrorCode {
    public static final ErrorCode SYSTEM_ERROR;
    public static final ErrorCode ILLEGAL_ARGUMENT;
    public static final ErrorCode UPLOAD_FILE_OVER_SIZE;
    public static final ErrorCode EXCEL_IMPORT_ERROR;
    public static final ErrorCode ILLEGAL_LOGIN;
    public static final ErrorCode OSS_OPERATION_ERROR;
    public static final ErrorCode USER_NOT_EXIST;
    private final String code;
    private final String desc;
    private final LogLevelEnum logLevel;

    public ErrorCode(String code, String desc, LogLevelEnum logLevel) {
        this.code = code;
        this.desc = desc;
        this.logLevel = logLevel;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public LogLevelEnum getLogLevel() {
        return this.logLevel;
    }

    static {
        SYSTEM_ERROR = new ErrorCode("SYSTEM_ERROR", "系统异常，请稍后再试", LogLevelEnum.ERROR);
        ILLEGAL_ARGUMENT = new ErrorCode("ILLEGAL_ARGUMENT", "参数非法，请核实", LogLevelEnum.ERROR);
        UPLOAD_FILE_OVER_SIZE = new ErrorCode("UPLOAD_FILE_OVER_SIZE", "上传文件过大", LogLevelEnum.ERROR);
        EXCEL_IMPORT_ERROR = new ErrorCode("EXCEL_IMPORT_ERROR", "Excel上传出错", LogLevelEnum.ERROR);
        ILLEGAL_LOGIN = new ErrorCode("ILLEGAL_LOGIN", "未授权的访问！请刷新页面后重试", LogLevelEnum.ERROR);
        OSS_OPERATION_ERROR = new ErrorCode("OSS_OPERATION_ERROR", "操作OSS文件错误", LogLevelEnum.ERROR);
        USER_NOT_EXIST = new ErrorCode("USER_NOT_EXIST", "用户不存在上下文！", LogLevelEnum.ERROR);
    }
}
