package cn.aliyun.ryytn.modules.inv.common.ability.cnhttp.exception;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-04 15:46
 * @description 创建代理异常
 */
public class CnHttpCreateProxyException extends RuntimeException {
    public CnHttpCreateProxyException() {
    }

    public CnHttpCreateProxyException(String message) {
        super(message);
    }
    public CnHttpCreateProxyException(ErrorEnum errorEnum) {
        this(errorEnum.info());
    }

    public CnHttpCreateProxyException(String message, Throwable cause) {
        super(message, cause);
    }

    public CnHttpCreateProxyException(Throwable cause) {
        super(cause);
    }

    public CnHttpCreateProxyException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
