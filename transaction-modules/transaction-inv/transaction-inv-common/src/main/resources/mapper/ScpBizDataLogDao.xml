<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.common.dao.common.log.dao.ScpBizDataLogMapper">
    <resultMap id="Basic_Result_Map" type="cn.aliyun.ryytn.modules.inv.common.dao.common.log.dataobject.ScpBizDataLogDO">
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
        <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
        <result column="biz_pk" jdbcType="VARCHAR" property="bizPk" />
        <result column="biz_info" jdbcType="LONGVARCHAR" property="bizInfo" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="operator_code" jdbcType="VARCHAR" property="operatorCode" />
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    </resultMap>
    <sql id="Table_Name">
        <trim>
            cdop_biz.scp_biz_data_log<if test="suffix != null and suffix != ''">_${suffix}</if>
        </trim>
    </sql>
    <sql id="Basic_Where_Clause">
        <where>
            biz_code = #{bizCode}
            <if test="bizPk != null and bizPk != ''">
                and biz_pk = #{bizPk}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="operatorCode != null and operatorCode != ''">
                and operator_code = #{operatorCode}
            </if>
        </where>
    </sql>
    <sql id="Insert_Column_List">
        ( biz_code, biz_pk, biz_info, version, operator_code, operator_name, tenant_code, gmt_create, gmt_modified )
    </sql>
    <select id="selectByCondition" parameterType="cn.aliyun.ryytn.modules.inv.common.dao.common.log.request.DataLogPageQueryRequest" resultMap="Basic_Result_Map">
        select *
        from <include refid="Table_Name" />
        <include refid="Basic_Where_Clause" />
        order by version desc
    </select>

    <select id="selectCount" resultType="long">
        select
        count(1)
        from <include refid="Table_Name" />
        <include refid="Basic_Where_Clause" />
    </select>

    <insert id="insert" parameterType="cn.aliyun.ryytn.modules.inv.common.dao.common.log.request.DataLogInsertRequest">
        insert into <include refid="Table_Name" />
        <include refid="Insert_Column_List" />
        values (
        #{bizCode}, #{bizPk}, #{bizInfo}, (<include refid="getNextVersion" />)
        ,#{operatorCode}, #{operatorName}, #{tenantCode}, now(), now()
        )
    </insert>
    <insert id="batchInsert" parameterType="cn.aliyun.ryytn.modules.inv.common.dao.common.log.request.DataLogInsertRequest">
        insert into <include refid="Table_Name" />
        <include refid="Insert_Column_List" />
        <foreach collection="params" item="item" separator="union all">
            select
            #{item.bizCode}, #{item.bizPk}, #{item.bizInfo}, COALESCE(max(version) + 1, 1) as nextVersion
            ,#{item.operatorCode}, #{item.operatorName}, #{item.tenantCode}, now(), now()
            from <include refid="Table_Name" />
            <where>
                biz_code = #{item.bizCode} and biz_pk = #{item.bizPk}
            </where>
        </foreach>
    </insert>

    <delete id="delete">
        delete from <include refid="Table_Name" />
        <where>
            biz_code = #{bizCode}
            <if test="bizPk != null and bizPk.size() > 0">
                <foreach collection="bizPk" item="item" open="and biz_pk in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
        </where>
    </delete>

    <sql id="getNextVersion">
        select nextVersion from (
        select COALESCE(max(version) + 1, 1) as nextVersion from <include refid="Table_Name" />
        <where>
            biz_code = #{bizCode} and biz_pk = #{bizPk}
        </where>
        ) t
    </sql>
    <sql id="getNextVersion2">
        select nextVersion from (
        select COALESCE(max(version) + 1, 1) as nextVersion from <include refid="Table_Name" />
        <where>
            biz_code = #{item.bizCode} and biz_pk = #{item.bizPk}
        </where>
        ) t
    </sql>
</mapper>