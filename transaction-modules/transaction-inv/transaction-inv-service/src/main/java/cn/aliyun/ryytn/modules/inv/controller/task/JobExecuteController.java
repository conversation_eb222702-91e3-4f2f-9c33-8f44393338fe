package cn.aliyun.ryytn.modules.inv.controller.task;


import cn.aliyun.ryytn.modules.inv.api.task.JobExecuteService;
import cn.aliyun.ryytn.modules.inv.api.task.TaskManagementService;
import cn.aliyun.ryytn.modules.inv.api.task.core.DateExprResolver;
import cn.aliyun.ryytn.modules.inv.api.task.enums.JobExecMode;
import cn.aliyun.ryytn.modules.inv.api.task.enums.TaskStatusEnum;
import cn.aliyun.ryytn.modules.inv.api.task.request.JobExecuteQueryRequest;
import cn.aliyun.ryytn.modules.inv.api.task.request.TaskConfigQueryRequest;
import cn.aliyun.ryytn.modules.inv.api.task.request.TaskConfigUpsertRequest;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.common.model.ResponseResult;
import cn.aliyun.ryytn.modules.inv.controller.task.taskApi.TaskManagementApi;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.TaskManagementDTO;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskCfgDO;
import cn.aliyun.ryytn.modules.inv.entity.task.vo.TaskCfgVO;
import cn.aliyun.ryytn.modules.inv.service.task.manager.TaskConfigManager;
import com.cainiao.cntech.dsct.scp.gei.biz.web.model.OptionVO;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-25 16:52
 * @description
 */
@RestController
@RequestMapping(TaskManagementApi.JobExecute.ROOT)
@Api(value = "任务执行控制层")
public class JobExecuteController {

    @Resource
    private JobExecuteService jobExecuteService;
    @Resource
    private TaskManagementService taskManagementService;
    @Resource
    private TaskConfigManager taskConfigManager;

    @ApiOperation(value = "检查是否已有提交的任务")
    @GetMapping(TaskManagementApi.JobExecute.CHECK_COMMITTED)
    public ResponseResult<Integer> checkExistsCommitted() {
        List<TaskManagementDTO> list = jobExecuteService.queryBaseTaskByStatus(Arrays.asList(TaskStatusEnum.AWAIT, TaskStatusEnum.RUNNING));
        return ResponseResult.success(Objects.isNull(list) ? 0 : list.size());
    }

    @ApiOperation(value = "执行当前")
    @PostMapping(TaskManagementApi.JobExecute.EXEC_CURR)
    public ResponseResult<Long> execCurr(@RequestBody List<JobExecuteQueryRequest> requests) {
        for (JobExecuteQueryRequest request : requests) {
            request.setJobMode(JobExecMode.EXEC_CURR.getCode());
        }
        return ResponseResult.success(jobExecuteService.execute(requests));
    }
    @ApiOperation(value = "执行下游")
    @PostMapping(TaskManagementApi.JobExecute.EXEC_DOWN)
    public ResponseResult<Long> execDown(@RequestBody List<JobExecuteQueryRequest> requests) {
        for (JobExecuteQueryRequest request : requests) {
            request.setJobMode(JobExecMode.EXEC_DOWN.getCode());
        }
        return ResponseResult.success(jobExecuteService.execute(requests));
    }

    @ApiOperation(value = "取消重跑")
    @GetMapping(TaskManagementApi.JobExecute.CANCEL_EXEC)
    public ResponseResult<Boolean> cancelExec(Long taskId) {
        return ResponseResult.success(jobExecuteService.cancelExecute(taskId));
    }

    @ApiOperation(value = "生成系统调度任务信息")
    @GetMapping(TaskManagementApi.JobExecute.GEN_SYS_TASK_BASE_INFO)
    public ResponseResult<Boolean> genSysTaskBaseInfo(String taskType) {
        return ResponseResult.success(jobExecuteService.genSysTaskBaseInfo(taskType));
    }

    @ApiOperation(value = "更新系统任务的完成时间")
    @GetMapping(TaskManagementApi.JobExecute.NOTIFY_SYS_TASK)
    public ResponseResult<Boolean> notifySysTask(String taskType) {
        return ResponseResult.success(jobExecuteService.updateSysTask(taskType, TaskStatusEnum.RUNNING, TaskStatusEnum.COMPLETED, null, LocalDateTime.now()));
    }

    @ApiOperation(value = "获取任务配置列表")
    @PostMapping(TaskManagementApi.JobExecute.GET_TASK_CONFIG)
    public ResponseResult<PageMetaInfoWrapper> getTaskConfig(@RequestBody TaskConfigQueryRequest request) {
        List<ScpTaskCfgDO> list = taskManagementService.queryTaskConfig(request);
        return ResponseResult.success(PageMetaInfoWrapper.of(request, taskManagementService.queryTaskConfigCount(request)
                , list, TaskCfgVO.class));
    }
    @ApiOperation(value = "获取任务配置是否可操作状态")
    @GetMapping(TaskManagementApi.JobExecute.GET_TASK_CONFIG_STATUS)
    public ResponseResult<Integer> getTaskConfigStatus() {
        return ResponseResult.success(1);
    }
    @ApiOperation(value = "获取常用日期表达式")
    @GetMapping(TaskManagementApi.JobExecute.GET_DATE_EXPR)
    public ResponseResult<List<OptionVO>> getDateExpr() {
        List<OptionDTO> commonOptions = DateExprResolver.getCommonOptions();
        return ResponseResult.success(GeiCommonConvert.convert(commonOptions, OptionVO.class));
    }
    @ApiOperation(value = "获取常用日期表达式")
    @GetMapping(TaskManagementApi.JobExecute.GET_ODPS_LIST)
    public ResponseResult<List<OptionVO>> getOdpsList(@RequestParam(required = false) String key) {
        List<OptionDTO> optionList = taskConfigManager.queryOdpsOptionList(key);
        return ResponseResult.success(GeiCommonConvert.convert(optionList, OptionVO.class));
    }
    @ApiOperation(value = "任务维护")
    @PostMapping(TaskManagementApi.TaskManagement.UPSERT_TASK_CFG)
    public ResponseResult<Long> upsertTaskCfg(@RequestBody TaskConfigUpsertRequest request) {
        return ResponseResult.success(taskManagementService.upsertTaskCfg(Collections.singletonList(request)));
    }
    @ApiOperation(value = "删除任务配置")
    @GetMapping(TaskManagementApi.TaskManagement.REMOVE_TASK_CFG)
    public ResponseResult<Long> removeTaskCfg(String taskTypeCode, Long odpsCode) {
        return ResponseResult.success(taskManagementService.deleteTaskCfg(taskTypeCode, odpsCode));
    }


    @ApiOperation(value = "重跑分销")
    @GetMapping(TaskManagementApi.JobExecute.RERUN_DISTRIBUTION)
    public ResponseResult<Long> rerunDistribution() {
        return ResponseResult.success(jobExecuteService.rerunDistribution());
    }
}
