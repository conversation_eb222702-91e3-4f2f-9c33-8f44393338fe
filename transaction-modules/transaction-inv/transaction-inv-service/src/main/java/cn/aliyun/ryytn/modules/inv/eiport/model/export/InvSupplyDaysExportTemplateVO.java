package cn.aliyun.ryytn.modules.inv.eiport.model.export;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/24 20:55
 * @description：
 */
@Data
public class InvSupplyDaysExportTemplateVO {

    /**
     * 工厂仓编码
     */
    @ExcelField(value = "工厂仓编码")
    private String cdcCode;
    /**
     * 工厂仓名称
     */
    @ExcelField(value = "工厂仓名称")
    private String cdcName;
    /**
     * 读取生产天数
     */
    @ExcelField(value = "读取生产天数")
    private Integer supplyDays;

    @ExcelField(value = "操作人")
    private String operatorName;

    @ExcelField(value = "最近更新时间")
    private String gmtModified;

}
