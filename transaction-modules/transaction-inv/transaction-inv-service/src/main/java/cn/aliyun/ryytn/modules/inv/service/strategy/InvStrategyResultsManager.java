package cn.aliyun.ryytn.modules.inv.service.strategy;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.BuriedLogger;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.QueryLogger;
import cn.aliyun.ryytn.modules.inv.common.constants.BizLoggerConstant;
import cn.aliyun.ryytn.modules.inv.common.exception.Assert;
import cn.aliyun.ryytn.modules.inv.common.exception.ErrorCode;
import cn.aliyun.ryytn.modules.inv.common.utils.JsonUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StreamUtils;
import cn.aliyun.ryytn.modules.inv.constant.strategy.error.InvStrategyResultsError;
import cn.aliyun.ryytn.modules.inv.entity.business.logger.InvStrategyReaultsLogMetaInfo;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyResultsTobDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyResultsTocDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvStrategyResulesUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvStrategyResultsPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyResultsTobDao;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyResultsTocDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/6 10:33
 * @description：
 */
@Service
@QueryLogger(bizCode = BizLoggerConstant.BizCode.STRATEGY_RESULTS, suffix = BizLoggerConstant.Suffix.STRATEGY, metaClass = InvStrategyReaultsLogMetaInfo.class)
public class InvStrategyResultsManager {

    @Resource
    private InvStrategyResultsTocDao invStrategyResultsTocDao;

    @Resource
    private InvStrategyResultsTobDao invStrategyResultsTobDao;

    @BuriedLogger(bizCode = BizLoggerConstant.BizCode.STRATEGY_RESULTS, suffix = BizLoggerConstant.Suffix.STRATEGY)
    public Long tocUpdate(List<InvStrategyResulesUpdateRequest> request, Boolean importFlag) {
        if (!importFlag) {
            InvStrategyResultsPageQueryRequest invStrategyResultsPageQueryRequest = new InvStrategyResultsPageQueryRequest();
            invStrategyResultsPageQueryRequest.setSku(Collections.singletonList(request.get(0).getSkuCode()));
            invStrategyResultsPageQueryRequest.setRdc(Collections.singletonList(request.get(0).getRdcCode()));
            List<InvStrategyResultsTocDO> invStrategyResultsTocDOS = invStrategyResultsTocDao.selectByCondition(JsonUtils.toMap(invStrategyResultsPageQueryRequest));
            Assert.isTrue(CollectionUtils.isNotEmpty(invStrategyResultsTocDOS), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.DATA_IS_NULL.getError());
            InvStrategyResultsTocDO invStrategyResultsTocDO = invStrategyResultsTocDOS.get(0);
            InvStrategyResulesUpdateRequest updateRequest = request.get(0);
            updateRequest.setManualTargetDaysBefore(invStrategyResultsTocDO.getManualTargetDays());
            updateRequest.setManualSafetyDaysBefore(invStrategyResultsTocDO.getManualSafetyDays());
            updateRequest.setRdcName(invStrategyResultsTocDO.getRdcName());
            updateRequest.setSkuName(invStrategyResultsTocDO.getSkuName());
            if (updateRequest.getManualSafetyDays() == null || invStrategyResultsTocDO.getDasNum() == null) {
                updateRequest.setManualSafetyQty(null);
            } else {
                updateRequest.setManualSafetyQty(updateRequest.getManualSafetyDays()*invStrategyResultsTocDO.getDasNum());
            }
            if (updateRequest.getManualTargetDays() == null || invStrategyResultsTocDO.getDasNum() == null) {
                updateRequest.setManualTargetQty(null);
            } else {
                updateRequest.setManualTargetQty(updateRequest.getManualTargetDays()*invStrategyResultsTocDO.getDasNum());
            }
        }
        return invStrategyResultsTocDao.update(request);
    }

    @BuriedLogger(bizCode = BizLoggerConstant.BizCode.STRATEGY_RESULTS, suffix = BizLoggerConstant.Suffix.STRATEGY)
    public Long tobUpdate(List<InvStrategyResulesUpdateRequest> request, Boolean importFlag) {
        if (!importFlag) {
            InvStrategyResultsPageQueryRequest invStrategyResultsPageQueryRequest = new InvStrategyResultsPageQueryRequest();
            invStrategyResultsPageQueryRequest.setSku(Collections.singletonList(request.get(0).getSkuCode()));
            invStrategyResultsPageQueryRequest.setRdc(Collections.singletonList(request.get(0).getRdcCode()));
            List<InvStrategyResultsTobDO> invStrategyResultsTobDOS = invStrategyResultsTobDao.selectByCondition(JsonUtils.toMap(invStrategyResultsPageQueryRequest));
            Assert.isTrue(CollectionUtils.isNotEmpty(invStrategyResultsTobDOS), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.DATA_IS_NULL.getError());
            InvStrategyResultsTobDO invStrategyResultsTobDO = invStrategyResultsTobDOS.get(0);
            InvStrategyResulesUpdateRequest updateRequest = request.get(0);
            updateRequest.setManualTargetDaysBefore(invStrategyResultsTobDO.getManualTargetDays());
            updateRequest.setManualSafetyDaysBefore(invStrategyResultsTobDO.getManualSafetyDays());
            updateRequest.setRdcName(invStrategyResultsTobDO.getRdcName());
            updateRequest.setSkuName(invStrategyResultsTobDO.getSkuName());
            if (updateRequest.getManualSafetyDays() == null || invStrategyResultsTobDO.getDasNum() == null) {
                updateRequest.setManualSafetyQty(null);
            } else {
                updateRequest.setManualSafetyQty(updateRequest.getManualSafetyDays()*invStrategyResultsTobDO.getDasNum());
            }
            if (updateRequest.getManualTargetDays() == null || invStrategyResultsTobDO.getDasNum() == null) {
                updateRequest.setManualTargetQty(null);
            } else {
                updateRequest.setManualTargetQty(updateRequest.getManualTargetDays()*invStrategyResultsTobDO.getDasNum());
            }
        }
        return invStrategyResultsTobDao.update(request);
    }

    public Long tocResetSuggestValue(InvStrategyResultsPageQueryRequest request) {
        validateData(request);
        List<InvStrategyResultsTocDO> resultsTocDOS = invStrategyResultsTocDao.selectByCondition(JsonUtils.toMap(request));
        if (CollectionUtils.isEmpty(resultsTocDOS)) {
            return null;
        }
        request.setIdList(StreamUtils.map(resultsTocDOS, InvStrategyResultsTocDO::getId));
        return invStrategyResultsTocDao.resetSuggestValue(request);
    }

    public Long tobResetSuggestValue(InvStrategyResultsPageQueryRequest request) {
        validateData(request);
        List<InvStrategyResultsTobDO> resultsTobDOS = invStrategyResultsTobDao.selectByCondition(JsonUtils.toMap(request));
        if (CollectionUtils.isEmpty(resultsTobDOS)) {
            return null;
        }
        request.setIdList(StreamUtils.map(resultsTobDOS, InvStrategyResultsTobDO::getId));
        return invStrategyResultsTobDao.resetSuggestValue(request);
    }

    private void validateData(InvStrategyResultsPageQueryRequest request) {
//        if (CollectionUtils.isEmpty(request.getSku()) && CollectionUtils.isEmpty(request.getCategory())
//                && CollectionUtils.isEmpty(request.getAbcType()) && CollectionUtils.isEmpty(request.getRdc())
//                && CollectionUtils.isEmpty(request.getStrategyName()) && CollectionUtils.isEmpty(request.getErrStatus())) {
//            Assert.isTrue(CollectionUtils.isNotEmpty(request.getItemList()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.QUERY_PARAM_IS_NOT_NULL.getError());
//        }
        request.setPaging(false);
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        if (Objects.nonNull(currentAccount)) {
            request.setOperatorCode(currentAccount.getId());
            request.setOperatorName(currentAccount.getName());
        }
    }
}
