package cn.aliyun.ryytn.modules.inv.service.task;

import cn.aliyun.ryytn.modules.inv.api.task.JobExecuteService;
import cn.aliyun.ryytn.modules.inv.api.task.TaskManagementService;
import cn.aliyun.ryytn.modules.inv.api.task.request.*;
import cn.aliyun.ryytn.modules.inv.common.utils.DateUtil;
import cn.aliyun.ryytn.modules.inv.api.task.core.JobEventPublisher;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.TaskManagementDTO;
import cn.aliyun.ryytn.modules.inv.api.task.enums.JobExecMode;
import cn.aliyun.ryytn.modules.inv.api.task.enums.JobSchedulingEnum;
import cn.aliyun.ryytn.modules.inv.api.task.enums.TaskGroupType;
import cn.aliyun.ryytn.modules.inv.api.task.enums.TaskStatusEnum;
import cn.aliyun.ryytn.modules.inv.api.task.error.TaskError;
import cn.aliyun.ryytn.modules.inv.service.task.manager.ExecChainManager;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskCfgDO;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskExecChainDO;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskTypeCfgDO;
import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.common.utils.SnowflakeIdWorker;
import com.cainiao.cntech.dsct.scp.gei.common.utils.StreamUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-20 14:39
 * @description
 */
@Service
public class JobExecuteServiceImpl implements JobExecuteService {
    private static final SnowflakeIdWorker ID_WORKER = new SnowflakeIdWorker(0, 0);
    @Resource
    private TaskManagementService taskManagementService;
    @Resource
    private ExecChainManager execChainManager;
    @Resource
    private JobEventPublisher jobEventPublisher;

    /**
     * 执行当前
     *
     * @param requests
     * @return
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long execute(List<JobExecuteQueryRequest> requests) {
        // 根据任务id查询任务信息
        TaskManagementPageQueryRequest taskQuery = new TaskManagementPageQueryRequest();
        taskQuery.setTask(StreamUtils.map(requests, JobExecuteQueryRequest::getTaskId));
        taskQuery.setPaging(false);
        List<TaskManagementDTO> taskList = taskManagementService.queryPageData(taskQuery);
        Map<Long, TaskManagementDTO> taskMap = StreamUtils.singleGroup(taskList, TaskManagementDTO::getTaskId);
        for (JobExecuteQueryRequest request : requests) {
            Assert.notNull(request.getJobMode(), ErrorCode.ILLEGAL_ARGUMENT, TaskError.JOB_EXEC_MODE_EMPTY.getError());
            Assert.notNull(request.getTaskId(), ErrorCode.ILLEGAL_ARGUMENT, TaskError.TASK_ID_EMPTY.getError());
            JobExecMode jobExecMode = JobExecMode.getByCode(request.getJobMode());
            Assert.isTrue(Objects.nonNull(jobExecMode), ErrorCode.ILLEGAL_ARGUMENT, TaskError.JOB_EXEC_MODE_ILLEGAL.getError());
            TaskManagementDTO baseTask = taskMap.get(request.getTaskId());
            Assert.isTrue(Objects.nonNull(baseTask), ErrorCode.ILLEGAL_ARGUMENT, TaskError.TASK_ID_ILLEGAL.getError());
            // 如果任务处于待运行或运行中，则不能再次提交执行
            String status = baseTask.getStatus();
            Assert.isFalse(TaskStatusEnum.AWAIT.isThis(status) || TaskStatusEnum.RUNNING.isThis(status)
                    , ErrorCode.SYSTEM_ERROR, TaskError.TASK_REPEAT_SUBMIT.getError());
            // step1: 更改状态      step2: 将任务添加到执行链
            updateTaskAndPushExecChain(request, baseTask, jobExecMode);
            // step3: 发送任务执行事件
            jobEventPublisher.sendExecEvent(request.getTaskId());
        }
        return 1L;
    }

    public void updateTaskAndPushExecChain(JobExecuteQueryRequest request, TaskManagementDTO taskDTO, JobExecMode jobExecMode) {
        // step1: 更改状态
        Long updateResult = updateBaseTask(request.getTaskId(), TaskStatusEnum.AWAIT, null, LocalDateTime.now(), null);
        Assert.isTrue(updateResult > 0, ErrorCode.SYSTEM_ERROR, TaskError.TASK_STATUS_OPERATION_FAILED.getError());
        // step2: 将任务添加到执行链
        pushExecChain(request, taskDTO, jobExecMode);
    }

    @Override
    public Long updateBaseTask(Long taskId, TaskStatusEnum taskStatus, String errorMsg, LocalDateTime triggerStartTime, LocalDateTime triggerEndTime) {
        TaskManageUpdateRequest updateRequest = new TaskManageUpdateRequest();
        updateRequest.setStatus(taskStatus.getCode());
        updateRequest.setErrorMsg(errorMsg);
        updateRequest.setTriggerStartTime(Objects.nonNull(triggerStartTime) ? triggerStartTime.withNano(0) : triggerStartTime);
        updateRequest.setTriggerEndTime(Objects.nonNull(triggerEndTime) ? triggerEndTime.withNano(0) : triggerEndTime);
        updateRequest.setTask(Collections.singletonList(taskId));
        if (Objects.nonNull(triggerStartTime)) {
            updateRequest.setCheckNqStatus(taskStatus.getCode());
        }
        return taskManagementService.update(updateRequest);
    }

    @Override
    public List<TaskManagementDTO> queryBaseTaskByStatus(List<TaskStatusEnum> taskStatus) {
        if (CollectionUtils.isEmpty(taskStatus)) {
            return Collections.emptyList();
        }
        List<String> status = StreamUtils.map(taskStatus, TaskStatusEnum::getCode);
        TaskManagementPageQueryRequest query = new TaskManagementPageQueryRequest();
        query.setPaging(false);
        query.setMultiStatus(status);
        return taskManagementService.queryPageData(query);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelExecute(Long taskId) {
        Assert.isTrue(Objects.nonNull(taskId), ErrorCode.ILLEGAL_ARGUMENT, TaskError.TASK_ID_IS_NOT_NULL.getError());
        ExecChainUpdateRequest request = new ExecChainUpdateRequest();
        request.setRootTaskId(taskId);
        request.setErrorMsg("执行链节点已被手动取消");
        request.setBeforeStatus(TaskStatusEnum.AWAIT.getCode());
        request.setStatus(TaskStatusEnum.FAILED.getCode());
        boolean cancelResult = execChainManager.updateChainNode(request);
        Assert.isTrue(cancelResult, ErrorCode.ILLEGAL_ARGUMENT, TaskError.TASK_CANCEL_FAILED.getError());
        Long taskUpdateResult = updateBaseTask(taskId, TaskStatusEnum.NOT_RUN, "任务已被手动取消", null, LocalDateTime.now());
        Assert.isTrue(taskUpdateResult > 0, ErrorCode.ILLEGAL_ARGUMENT, TaskError.TASK_CANCEL_FAILED.getError());
        return true;
    }

    @Override
    public Boolean genSysTaskBaseInfo(String taskType) {
        TaskTypeQueryRequest query = new TaskTypeQueryRequest();
        query.setTaskType(taskType);
        List<ScpTaskTypeCfgDO> list = taskManagementService.queryTaskTypeConfig(query);
        if(CollectionUtils.isEmpty(list)){
            return true;
        }
        LocalDateTime now = LocalDateTime.now();
        TaskInsertRequest request = new TaskInsertRequest();
        request.setTaskType(Collections.singletonList(taskType));
        request.setSchedulingType(JobSchedulingEnum.PERIODIC.getCode());
        request.setSchedulingTime(now);
        taskManagementService.createTask(request);
        updateSysTask(taskType, TaskStatusEnum.NOT_RUN, TaskStatusEnum.RUNNING, now, null);
        return true;
    }

    @Override
    public Boolean updateSysTask(String taskType, TaskStatusEnum updateBefore, TaskStatusEnum updateAfter, LocalDateTime triggerStartTime, LocalDateTime triggerEndTime) {
        TaskManagementPageQueryRequest taskQuery = new TaskManagementPageQueryRequest();
        taskQuery.setTaskType(Collections.singletonList(taskType));
        taskQuery.setSchedulingType(JobSchedulingEnum.PERIODIC.getCode());
        taskQuery.setCreateTime(DateUtil.getDateBeginTime(LocalDate.now()));
        taskQuery.setStatus(updateBefore.getCode());
        taskQuery.setPaging(false);
        List<TaskManagementDTO> taskList = taskManagementService.queryPageData(taskQuery);
        if (CollectionUtils.isEmpty(taskList)) {
            return true;
        }
        for (TaskManagementDTO taskBase : taskList) {
            if (updateBefore.isThis(taskBase.getStatus())) {
                updateBaseTask(taskBase.getTaskId(), updateAfter, null, triggerStartTime, triggerEndTime);
            }
        }
        return true;
    }

    public void pushExecChain(JobExecuteQueryRequest request, TaskManagementDTO taskDTO, JobExecMode jobExecMode) {
        Assert.notNull(jobExecMode, ErrorCode.ILLEGAL_ARGUMENT, TaskError.JOB_EXEC_MODE_EMPTY.getError());
        long batchNo = ID_WORKER.nextId();
        if (JobExecMode.EXEC_DOWN.equals(jobExecMode)) {
            String preTaskType = taskDTO.getTaskType();
            TaskConfigQueryRequest query = new TaskConfigQueryRequest();
            query.setGroupType(TaskGroupType.TASK_TYPE.getCode());
            while (StringUtils.isNotBlank(preTaskType)) {
                query.setPreTaskType(preTaskType);
                List<ScpTaskCfgDO> scpTaskCfgList = taskManagementService.queryTaskConfig(query);
                if (CollectionUtils.isNotEmpty(scpTaskCfgList)) {
                    ScpTaskCfgDO taskCfg = scpTaskCfgList.get(0);
                    ScpTaskExecChainDO scpTaskExecChainDO = new ScpTaskExecChainDO();
                    scpTaskExecChainDO.setBatchNo(batchNo);
                    scpTaskExecChainDO.setRootTaskId(request.getTaskId());
                    scpTaskExecChainDO.setRootTaskType(taskCfg.getTaskTypeCode());
                    scpTaskExecChainDO.setExecMode(JobExecMode.EXEC_CURR.getCode());
                    scpTaskExecChainDO.setStatus(TaskStatusEnum.AWAIT.getCode());
                    execChainManager.insertChainNode(scpTaskExecChainDO);
                    preTaskType = taskCfg.getTaskTypeCode();
                } else {
                    preTaskType = null;
                }
            }
        } else {
            ScpTaskExecChainDO scpTaskExecChainDO = new ScpTaskExecChainDO();
            scpTaskExecChainDO.setBatchNo(batchNo);
            scpTaskExecChainDO.setRootTaskId(request.getTaskId());
            scpTaskExecChainDO.setRootTaskType(taskDTO.getTaskType());
            scpTaskExecChainDO.setExecMode(jobExecMode.getCode());
            scpTaskExecChainDO.setStatus(TaskStatusEnum.AWAIT.getCode());
            execChainManager.insertChainNode(scpTaskExecChainDO);
        }
    }
}
