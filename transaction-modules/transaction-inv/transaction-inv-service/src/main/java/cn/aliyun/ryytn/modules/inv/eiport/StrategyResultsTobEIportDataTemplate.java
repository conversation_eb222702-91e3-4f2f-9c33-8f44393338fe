package cn.aliyun.ryytn.modules.inv.eiport;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.utils.JsonUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.NumUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StreamUtils;
import cn.aliyun.ryytn.modules.inv.constant.strategy.enums.ReplnTypeEnum;
import cn.aliyun.ryytn.modules.inv.constant.strategy.enums.SkuLevelEnum;
import cn.aliyun.ryytn.modules.inv.constant.strategy.error.InvStrategyResultsError;
import cn.aliyun.ryytn.modules.inv.eiport.model.StrateyResultsTobImportTemplateVO;
import cn.aliyun.ryytn.modules.inv.eiport.model.StrateyResultsTocImportTemplateVO;
import cn.aliyun.ryytn.modules.inv.eiport.model.export.StrategyResultsTobExportTemplateVO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyResultsTobDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvStrategyResulesUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvStrategyResultsPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.service.strategy.InvStrategyResultsManager;
import cn.aliyun.ryytn.modules.inv.service.strategy.InvStrategyResultsServiceImpl;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyResultsTobDao;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants.StrategyResults.*;

/**
 * <AUTHOR>
 * @date 2025/5/7 16:58
 * @description：
 */
@ImportService(value = "strategyResultsTobEIportDataTemplate", filename = "库存策略结果导入模版", templateDesc = "填写规范：\n" +
        "1.安全库存（人工调整）：填写>=0的整数；如：2\n" +
        "2.目标库存（人工调整）：填写>=0的整数，数值必须>安全库存天数；如：2", async = false, dict = "STRATEGY_RESULTS", sliceSize = 10000)
@ExportService(value = "strategyResultsTobEIportDataTemplate", filename = "库存策略结果数据导出", async = false)
public class StrategyResultsTobEIportDataTemplate extends EIPortDataTemplate<StrateyResultsTobImportTemplateVO, InvStrategyResultsPageQueryRequest> implements ImportPostProcessor<StrateyResultsTobImportTemplateVO> {

    @Autowired
    private InvStrategyResultsServiceImpl invStrategyResultsService;

    @Autowired
    private InvStrategyResultsTobDao invStrategyResultsTobDao;

    @Autowired
    private InvStrategyResultsManager invStrategyResultsManager;

    @Override
    public List<StrateyResultsTobImportTemplateVO> getImportTemplateData(ImportQueryRequest request) {
        if (CollectionUtils.isEmpty(request.getDimensionList())) {
            return super.getImportTemplateData(request);
        }
        InvStrategyResultsPageQueryRequest pageQueryRequest = JsonUtils.toRequestObject(request.getParams(), InvStrategyResultsPageQueryRequest.class);
        pageQueryRequest.setPaging(false);
        pageQueryRequest.setReplnType(ReplnTypeEnum.TOB.getCode());
        PageMetaInfoWrapper pageMetaInfoWrapper = invStrategyResultsService.queryPageData(pageQueryRequest);
        if (Objects.isNull(pageMetaInfoWrapper) || CollectionUtils.isEmpty(pageMetaInfoWrapper.getList())) {
            return super.getImportTemplateData(request);
        }
        List<StrateyResultsTobImportTemplateVO> convert = GeiCommonConvert.convert(pageMetaInfoWrapper.getList(), StrateyResultsTobImportTemplateVO.class);
        Function<StrateyResultsTobImportTemplateVO, String> queryListGroup = null;
        if (request.getDimensionList().contains(LV1)) {
            queryListGroup = item -> item.getLv1CategoryName();
            convert = convert.stream().filter(t -> StringUtils.isNotEmpty(t.getLv1CategoryName())).collect(Collectors.toList());
        } else if (request.getDimensionList().contains(LV2)) {
            queryListGroup = item -> item.getLv2CategoryName();
            convert = convert.stream().filter(t -> StringUtils.isNotEmpty(t.getLv2CategoryName())).collect(Collectors.toList());
        } else if (request.getDimensionList().contains(LV4)) {
            queryListGroup = item -> item.getLv4CategoryName();
            convert = convert.stream().filter(t -> StringUtils.isNotEmpty(t.getLv4CategoryName())).collect(Collectors.toList());
        } else if (request.getDimensionList().contains(ABC_TYPE_NAME)) {
            queryListGroup = item -> item.getAbcType();
            convert = convert.stream().filter(t -> StringUtils.isNotEmpty(t.getAbcType())).collect(Collectors.toList());
        } else if (request.getDimensionList().contains(SKU)) {
            queryListGroup = item -> item.getSkuCode();
        } else if (request.getDimensionList().contains(RDC_NAME)) {
            queryListGroup = item -> item.getRdcName();
        }
        if (CollectionUtils.isEmpty(convert)) {
            return super.getImportTemplateData(request);
        }
        Map<String, List<StrateyResultsTobImportTemplateVO>> group = StreamUtils.group(convert, queryListGroup);
        List<StrateyResultsTobImportTemplateVO> returnList = new ArrayList<>();
        group.forEach((k, v) -> {
            returnList.add(v.get(0));
        });
        return returnList;
    }

    @Override
    public List<?> getExportTableData(InvStrategyResultsPageQueryRequest request) {
        request.setPaging(false);
        PageMetaInfoWrapper pageMetaInfoWrapper = invStrategyResultsService.queryPageData(request);
        return pageMetaInfoWrapper.getList();
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(InvStrategyResultsPageQueryRequest request) {
        return ExcelMetaInfoCreator.create(StrategyResultsTobExportTemplateVO.class);
    }

    @Override
    public boolean preValid(ImportDataWrapper<StrateyResultsTobImportTemplateVO> importDataWrapper) {
        return validate(importDataWrapper);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<StrateyResultsTobImportTemplateVO> importDataWrapper) {
        List<StrateyResultsTobImportTemplateVO> data = importDataWrapper.getData();
        invStrategyResultsManager.tobUpdate(GeiCommonConvert.convert(data, InvStrategyResulesUpdateRequest.class), true);
        return null;
    }

    private boolean validate(ImportDataWrapper<StrateyResultsTobImportTemplateVO> importDataWrapper) {
        boolean flag = true;
        List<String> dimensionList = importDataWrapper.getDimensionList();
        dimensionList.addAll(importDataWrapper.getFieldsList());
        List<StrateyResultsTobImportTemplateVO> importData = importDataWrapper.getData();
        if (CollectionUtils.isEmpty(importData)) {
            return false;
        }
        flag = validateParam(dimensionList, importData);
        if (!flag) {
            return false;
        }
        InvStrategyResultsPageQueryRequest request = new InvStrategyResultsPageQueryRequest();
        request.setPaging(false);
        request.setReplnType(ReplnTypeEnum.TOB.getCode());
        setParam(request, dimensionList, importData);
        List<InvStrategyResultsTobDO> invStrategyResultsTobDOS = invStrategyResultsTobDao.selectByCondition(JsonUtils.toMap(request));
        if (CollectionUtils.isEmpty(invStrategyResultsTobDOS)) {
            setErrMsg(importData, dimensionList);
            return false;
        }
        flag = validateData(invStrategyResultsTobDOS, importData, dimensionList);
        return flag;
    }

    private boolean validateData(List<InvStrategyResultsTobDO> invStrategyResultsTobDOS, List<StrateyResultsTobImportTemplateVO> importData, List<String> dimensionList) {
        boolean flag = true;
        boolean resultFlag = true;
        Function<InvStrategyResultsTobDO, String> queryListGroup = null;
        if (dimensionList.contains(LV1)) {
            queryListGroup = item -> item.getLv1CategoryName();
        } else if (dimensionList.contains(LV2)) {
            queryListGroup = item -> item.getLv2CategoryName();
        } else if (dimensionList.contains(LV4)) {
            queryListGroup = item -> item.getLv4CategoryName();
        } else if (dimensionList.contains(ABC_TYPE_NAME)) {
            queryListGroup = item -> item.getAbcType();
        } else if (dimensionList.contains(SKU)) {
            queryListGroup = item -> item.getSkuCode();
        } else if (dimensionList.contains(RDC_NAME)) {
            queryListGroup = item -> item.getRdcName();
        }
        Map<String, List<InvStrategyResultsTobDO>> resultsGroup = StreamUtils.group(invStrategyResultsTobDOS, queryListGroup);
        List<StrateyResultsTobImportTemplateVO> updateList = new ArrayList<>();
        for (StrateyResultsTobImportTemplateVO importDatum : importData) {
            List<InvStrategyResultsTobDO> resultsTobDOS = null;
            if (dimensionList.contains(LV1)) {
                resultsTobDOS = resultsGroup.get(importDatum.getLv1CategoryName());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getLv1CategoryName());
            } else if (dimensionList.contains(LV2)) {
                resultsTobDOS = resultsGroup.get(importDatum.getLv2CategoryName());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getLv2CategoryName());
            } else if (dimensionList.contains(LV4)) {
                resultsTobDOS = resultsGroup.get(importDatum.getLv4CategoryName());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getLv4CategoryName());
            } else if (dimensionList.contains(ABC_TYPE_NAME)) {
                resultsTobDOS = resultsGroup.get(SkuLevelEnum.getCodeByName(importDatum.getAbcTypeName()));
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getAbcTypeName());
            } else if (dimensionList.contains(SKU)) {
                resultsTobDOS = resultsGroup.get(importDatum.getSkuCode());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getSkuCode());
            } else if (dimensionList.contains(RDC_NAME)) {
                resultsTobDOS = resultsGroup.get(importDatum.getRdcName());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getRdcName());
            }
            if (dimensionList.contains(MANUAL_SAFETY_DAYS) && dimensionList.contains(MANUAL_TARGET_DAYS)) {
                if (importDatum.getManualSafetyDays() > importDatum.getManualTargetDays()) {
                    importDatum.setErrorMsg(InvStrategyResultsError.TARGET_LESS_SAFETY.getError());
                    flag = false;
                }
            } else if (dimensionList.contains(MANUAL_SAFETY_DAYS)) {
                flag = setSafetyErrMsg(resultsTobDOS, importDatum);
            } else if (dimensionList.contains(MANUAL_TARGET_DAYS)) {
                flag = setTargetErrMsg(resultsTobDOS, importDatum);
            }
            if (flag) {
                updateList.addAll(setRequest(dimensionList, resultsTobDOS, importDatum));
            }else {
                resultFlag = false;
            }
        }
        if (resultFlag) {
            importData.clear();
            importData.addAll(updateList);
        }
        return resultFlag;
    }

    private boolean setSafetyErrMsg(List<InvStrategyResultsTobDO> resultsTobDOS, StrateyResultsTobImportTemplateVO importDatum) {
        boolean flag = true;
        StringBuilder stringBuilder = new StringBuilder();
        for (InvStrategyResultsTobDO tobDO : resultsTobDOS) {
            if (Objects.isNull(tobDO.getManualTargetDays())) {
                continue;
            }
            if (importDatum.getManualSafetyDays() > tobDO.getManualTargetDays()) {
                stringBuilder.append(String.format("%s-%s;", tobDO.getSkuCode(), tobDO.getRdcName()));
                flag = false;
            }
        }
        if (stringBuilder.length() > 0) {
            stringBuilder.append(InvStrategyResultsError.TARGET_LESS_SAFETY.getError());
            importDatum.setErrorMsg(stringBuilder.toString());
        }
        return flag;
    }

    private boolean setTargetErrMsg(List<InvStrategyResultsTobDO> resultsTobDOS, StrateyResultsTobImportTemplateVO importDatum) {
        boolean flag = true;
        StringBuilder stringBuilder = new StringBuilder();
        for (InvStrategyResultsTobDO tobDO : resultsTobDOS) {
            if (Objects.isNull(tobDO.getManualSafetyDays())) {
                continue;
            }
            if (tobDO.getManualSafetyDays() > importDatum.getManualTargetDays()) {
                stringBuilder.append(String.format("%s-%s;", tobDO.getSkuCode(), tobDO.getRdcName()));
                flag = false;
            }
        }
        if (stringBuilder.length() > 0) {
            stringBuilder.append(InvStrategyResultsError.TARGET_LESS_SAFETY.getError());
            importDatum.setErrorMsg(stringBuilder.toString());
        }
        return flag;
    }


    private List<StrateyResultsTobImportTemplateVO> setRequest(List<String> dimensionList, List<InvStrategyResultsTobDO> resultsTobDOS, StrateyResultsTobImportTemplateVO importDatum) {
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        List<StrateyResultsTobImportTemplateVO> list = new ArrayList<>();
        for (InvStrategyResultsTobDO resultsTobDO : resultsTobDOS) {
            StrateyResultsTobImportTemplateVO tobImportTemplateVO = new StrateyResultsTobImportTemplateVO();
            tobImportTemplateVO.setSkuCode(resultsTobDO.getSkuCode());
            tobImportTemplateVO.setSkuName(resultsTobDO.getSkuName());
            tobImportTemplateVO.setRdcCode(resultsTobDO.getRdcCode());
            tobImportTemplateVO.setRdcName(resultsTobDO.getRdcName());
            tobImportTemplateVO.setManualTargetDaysBefore(resultsTobDO.getManualTargetDays());
            tobImportTemplateVO.setManualSafetyDaysBefore(resultsTobDO.getManualSafetyDays());
            tobImportTemplateVO.setManualSafetyDays(importDatum.getManualSafetyDays());
            tobImportTemplateVO.setManualTargetDays(importDatum.getManualTargetDays());
            if (dimensionList.contains(MANUAL_SAFETY_DAYS) && dimensionList.contains(MANUAL_TARGET_DAYS)) {
                if (Objects.equals(tobImportTemplateVO.getManualSafetyDays(), resultsTobDO.getManualSafetyDays()) && Objects.equals(tobImportTemplateVO.getManualTargetDays(), resultsTobDO.getManualTargetDays())) {
                    continue;
                }
            } else if (dimensionList.contains(MANUAL_SAFETY_DAYS)) {
                if (Objects.equals(tobImportTemplateVO.getManualSafetyDays(), resultsTobDO.getManualSafetyDays())) {
                    continue;
                }
            } else if (dimensionList.contains(MANUAL_TARGET_DAYS)) {
                if (Objects.equals(tobImportTemplateVO.getManualTargetDays(), resultsTobDO.getManualTargetDays())) {
                    continue;
                }
            }
            if (tobImportTemplateVO.getManualSafetyDays() == null || resultsTobDO.getDasNum() == null) {
                tobImportTemplateVO.setManualSafetyQty(null);
            } else {
                tobImportTemplateVO.setManualSafetyQty(tobImportTemplateVO.getManualSafetyDays()*resultsTobDO.getDasNum());
            }
            if (tobImportTemplateVO.getManualTargetDays() == null || resultsTobDO.getDasNum() == null) {
                tobImportTemplateVO.setManualTargetQty(null);
            } else {
                tobImportTemplateVO.setManualTargetQty(tobImportTemplateVO.getManualTargetDays()*resultsTobDO.getDasNum());
            }
            if (Objects.isNull(currentAccount)) {
                list.add(tobImportTemplateVO);
                continue;
            }
            tobImportTemplateVO.setOperatorCode(currentAccount.getId());
            tobImportTemplateVO.setOperatorName(currentAccount.getName());
            list.add(tobImportTemplateVO);
        }
        return list;
    }

    private boolean setErrMsg(List<InvStrategyResultsTobDO> resultsTobDOS, StrateyResultsTobImportTemplateVO importDatum, String name) {
        boolean flag = true;
        if (CollectionUtils.isEmpty(resultsTobDOS)) {
            importDatum.setErrorMsg(String.format(InvStrategyResultsError.PARAMETERS_NOT_FOUND.getError(),name));
            flag = false;
        }
        return flag;
    }

    private boolean setErrMsg(List<StrateyResultsTobImportTemplateVO> importData, List<String> dimensionList) {
        for (StrateyResultsTobImportTemplateVO datum : importData) {
            if (dimensionList.contains(LV1)) {
                datum.setErrorMsg(String.format(InvStrategyResultsError.PARAMETERS_NOT_FOUND.getError(),datum.getLv1CategoryName()));
            } else if (dimensionList.contains(LV2)) {
                datum.setErrorMsg(String.format(InvStrategyResultsError.PARAMETERS_NOT_FOUND.getError(),datum.getLv2CategoryName()));
            } else if (dimensionList.contains(LV4)) {
                datum.setErrorMsg(String.format(InvStrategyResultsError.PARAMETERS_NOT_FOUND.getError(),datum.getLv4CategoryName()));
            } else if (dimensionList.contains(ABC_TYPE_NAME)) {
                datum.setErrorMsg(String.format(InvStrategyResultsError.PARAMETERS_NOT_FOUND.getError(),datum.getAbcTypeName()));
            } else if (dimensionList.contains(SKU)) {
                datum.setErrorMsg(String.format(InvStrategyResultsError.PARAMETERS_NOT_FOUND.getError(),datum.getSkuCode()));
            } else if (dimensionList.contains(RDC_NAME)) {
                datum.setErrorMsg(String.format(InvStrategyResultsError.PARAMETERS_NOT_FOUND.getError(),datum.getRdcName()));
            }
        }
        return false;
    }

    private void setParam(InvStrategyResultsPageQueryRequest request, List<String> dimensionList, List<StrateyResultsTobImportTemplateVO> importData) {
        if (dimensionList.contains(LV1)) {
            List<String> map = StreamUtils.map(importData, StrateyResultsTobImportTemplateVO::getLv1CategoryName);
            request.setCategoryName(map);
        } else if (dimensionList.contains(LV2)) {
            List<String> map = StreamUtils.map(importData, StrateyResultsTobImportTemplateVO::getLv2CategoryName);
            request.setCategoryName(map);
        } else if (dimensionList.contains(LV4)) {
            List<String> map = StreamUtils.map(importData, StrateyResultsTobImportTemplateVO::getLv4CategoryName);
            request.setCategoryName(map);
        } else if (dimensionList.contains(ABC_TYPE_NAME)) {
            List<String> map = StreamUtils.map(importData, t -> SkuLevelEnum.getCodeByName(t.getAbcTypeName()));
            request.setAbcType(map);
        } else if (dimensionList.contains(SKU)) {
            List<String> map = StreamUtils.map(importData, StrateyResultsTobImportTemplateVO::getSkuCode);
            request.setSku(map);
        } else if (dimensionList.contains(RDC_NAME)) {
            List<String> map = StreamUtils.map(importData, StrateyResultsTobImportTemplateVO::getRdcName);
            request.setRdcName(map);
        }
    }

    private boolean validateParam(List<String> dimensionList, List<StrateyResultsTobImportTemplateVO> importData) {
        boolean flag = true;
        StringBuilder stringBuilder = new StringBuilder();
        for (StrateyResultsTobImportTemplateVO importDatum : importData) {
            stringBuilder.delete(0, stringBuilder.length());
            //校验字段非空
            validateNotNull(dimensionList, importDatum, stringBuilder);
            if (stringBuilder.length() > 0) {
                flag = false;
                importDatum.setErrorMsg(stringBuilder.toString());
                continue;
            }
            //校验数值格式与范围
            validateParamData(dimensionList, importDatum, stringBuilder);
            if (stringBuilder.length() > 0) {
                flag = false;
                importDatum.setErrorMsg(stringBuilder.toString());
            }
        }
        return flag;
    }

    private void validateNotNull(List<String> dimension, StrateyResultsTobImportTemplateVO importDatum, StringBuilder stringBuilder) {
        if (dimension.contains(LV1) && StringUtils.isBlank(importDatum.getLv1CategoryName())) {
            stringBuilder.append(InvStrategyResultsError.LV1_NAME_NOT_NULL.getError());
        } else if (dimension.contains(LV2) && StringUtils.isBlank(importDatum.getLv2CategoryName())) {
            stringBuilder.append(InvStrategyResultsError.LV2_NAME_NOT_NULL.getError());
        } else if (dimension.contains(LV4) && StringUtils.isBlank(importDatum.getLv4CategoryName())) {
            stringBuilder.append(InvStrategyResultsError.LV4_NAME_NOT_NULL.getError());
        } else if (dimension.contains(ABC_TYPE_NAME) && StringUtils.isBlank(importDatum.getAbcTypeName())) {
            stringBuilder.append(InvStrategyResultsError.ABC_TYPE_NAME_NOT_NULL.getError());
        } else if (dimension.contains(SKU) && StringUtils.isBlank(importDatum.getSkuCode())) {
            stringBuilder.append(InvStrategyResultsError.SKU_CODE_NOT_NULL.getError());
        } else if (dimension.contains(SKU) && StringUtils.isBlank(importDatum.getSkuName())) {
            stringBuilder.append(InvStrategyResultsError.SKU_NAME_NOT_NULL.getError());
        } else if (dimension.contains(RDC_NAME) && StringUtils.isBlank(importDatum.getRdcName())) {
            stringBuilder.append(InvStrategyResultsError.RDC_NAME_NOT_NULL.getError());
        }
        if (dimension.contains(MANUAL_SAFETY_DAYS) && StringUtils.isBlank(importDatum.getManualSafetyDaysStr())) {
            stringBuilder.append(InvStrategyResultsError.SAFETY_NOT_NULL.getError());
        }
        if (dimension.contains(MANUAL_TARGET_DAYS) && StringUtils.isBlank(importDatum.getManualTargetDaysStr())) {
            stringBuilder.append(InvStrategyResultsError.TARGET_NOT_NULL.getError());
        }
    }

    private void validateParamData(List<String> dimension, StrateyResultsTobImportTemplateVO importDatum, StringBuilder stringBuilder) {
        Integer manualSafetyDays = null;
        if (dimension.contains(MANUAL_SAFETY_DAYS)) {
            manualSafetyDays = NumUtils.safeInt(importDatum.getManualSafetyDaysStr());
            if (Objects.isNull(manualSafetyDays)) {
                stringBuilder.append(InvStrategyResultsError.SAFETY_TYPE_ERROR.getError());
            }
        }
        Integer manualTargetDays = null;
        if (dimension.contains(MANUAL_TARGET_DAYS)) {
            manualTargetDays = NumUtils.safeInt(importDatum.getManualTargetDaysStr());
            if (Objects.isNull(manualTargetDays)) {
                stringBuilder.append(InvStrategyResultsError.TARGET_TYPE_ERROR.getError());
            }
        }
        if (stringBuilder.length() > 0) {
            return;
        }
        if (Objects.nonNull(manualSafetyDays)) {
            if (manualSafetyDays < 1 || manualSafetyDays > 100) {
                stringBuilder.append(InvStrategyResultsError.SAFETY_RANGE_ERROR.getError());
            }
        }
        if (Objects.nonNull(manualTargetDays)) {
            if (manualTargetDays < 1 || manualTargetDays > 100) {
                stringBuilder.append(InvStrategyResultsError.TARGET_RANGE_ERROR.getError());
            }
        }
        if (stringBuilder.length() > 0) {
            return;
        }
        importDatum.setManualSafetyDays(manualSafetyDays);
        importDatum.setManualTargetDays(manualTargetDays);
    }

}
