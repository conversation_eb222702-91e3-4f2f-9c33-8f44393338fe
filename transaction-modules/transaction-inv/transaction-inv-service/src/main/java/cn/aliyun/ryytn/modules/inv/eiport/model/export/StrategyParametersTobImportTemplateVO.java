package cn.aliyun.ryytn.modules.inv.eiport.model.export;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.BaseAction;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

import java.time.LocalDateTime;

import static cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants.StrategyCenter.*;

/**
 * <AUTHOR>
 * @date 2025/4/28 18:34
 * @description：
 */
@Data
public class StrategyParametersTobImportTemplateVO extends BaseAction {

    /**
     * 主键
     */
    public Long id;

    /**
     * 创建时间
     */
    public LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    public LocalDateTime gmtModified;

    /**
     * 库存策略Id
     */
    public Long invStrategyId;

    /**
     * ABC分类
     */
    public String abcType;

    /**
     * ABC分类名称
     */
    @ExcelField(value = "ABC分类", mapping = {BY_ABC})
    private String abcTypeName;

    /**
     * 产品分类编码
     */
    public String lv1CategoryCode;

    /**
     * 产品分类名称
     */
    @MetaField(value = "一级分类", mapping = {BY_ONE})
    public String lv1CategoryName;

    /**
     * 产品大类编码
     */
    public String lv2CategoryCode;

    /**
     * 产品大类名称
     */
    @MetaField(value = "二级分类", mapping = {BY_TWO})
    public String lv2CategoryName;

    /**
     * 产品小类编码
     */
    public String lv4CategoryCode;

    /**
     * 产品小类名称
     */
    @MetaField(value = "四级分类", mapping = {BY_FOUR})
    public String lv4CategoryName;

    /**
     * SKU编码
     */
    @MetaField(value = "SKU编码", mapping = {BY_SKU})
    public String skuCode;

    /**
     * SKU名称
     */
    @MetaField(value = "SKU简称", mapping = {BY_SKU})
    public String skuName;

    /**
     * RDC仓编码
     */
    public String rdcCode;

    /**
     * RDC仓名称
     */
    @MetaField(value = "仓库")
    public String rdcName;

    /**
     * 日均销
     */
    @MetaField(value = "日均销", mapping = {BY_SKU})
    public Integer dailySalesAvg;

    /**
     * 配置方式
     */
    @MetaField(value = "配置方式")
    public String collocationMethod;

    /**
     * 配置方式
     */
    public String collocationMethodName;

    /**
     * 极限水位
     */
    @MetaField(value = "A（极限水位）")
    public Integer limitWaterLevel;

    /**
     * 目标库存
     */
    @MetaField(value = "B（目标库存）")
    public Integer target;

    /**
     * 再订货点
     */
    @MetaField(value = "C（再订货点）")
    public Integer ropCnt;

    /**
     * 缺货报警点
     */
    @MetaField(value = "D（缺货报警点）")
    public Integer oosPoint;

    /**
     * 操作人编码
     */
    public String operatorCode;

    /**
     * 操作人名称
     */
    public String operatorName;

    /**
     * 操作时间
     */
    public LocalDateTime operationTime;

    /**
     * 状态 (0:中间态，1:最终态)
     */
    public Integer status;

}
