package cn.aliyun.ryytn.modules.inv.service.task.core.executor;


import cn.aliyun.ryytn.modules.inv.api.task.TaskManagementService;
import cn.aliyun.ryytn.modules.inv.api.task.request.TaskManagementPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.task.entity.*;
import cn.aliyun.ryytn.modules.inv.api.task.core.executor.template.JobExecTemplate;
import cn.aliyun.ryytn.modules.inv.api.task.core.JobEventPublisher;
import cn.aliyun.ryytn.modules.inv.api.task.enums.JobExecMode;
import cn.aliyun.ryytn.modules.inv.api.task.enums.TaskStatusEnum;
import cn.aliyun.ryytn.modules.inv.api.task.error.TaskError;
import cn.aliyun.ryytn.modules.inv.api.task.request.TaskConfigQueryRequest;
import cn.aliyun.ryytn.modules.inv.common.utils.SafeGetter;
import cn.aliyun.ryytn.modules.inv.entity.task.enums.JobCore;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.TaskManagementDTO;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskCfgDO;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskCoreStatDO;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskExecChainDO;
import cn.aliyun.ryytn.modules.inv.service.task.manager.ExecChainManager;
import cn.aliyun.ryytn.modules.inv.service.task.manager.TaskConfigManager;
import cn.aliyun.ryytn.modules.inv.service.task.manager.TaskCoreStatManager;
import com.cainiao.cntech.dsct.scp.gei.common.enums.BooleanEnum;
import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.StreamUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-19 13:45
 * @description
 */
@Slf4j
@Component
public class JobExecutor implements ApplicationContextAware, ApplicationRunner {
    public static final JobCore CORE = JobCore.EXECUTOR;
    private final Map<String, JobExecTemplate> JOBS = Maps.newHashMap();
    private ApplicationContext context;
    @Resource
    private TaskCoreStatManager taskCoreStatManager;
    @Resource
    private JobEventPublisher jobEventPublisher;
    @Resource
    private ExecChainManager execChainManager;
    @Resource
    private TaskConfigManager taskConfigManager;
    @Resource
    private TaskManagementService taskManagementService;
    @Resource
    private StateModifier stateModifier;

    /**
     * 对外暴露确认任务状态接口功能
     */
    public ExecuteResult confirmJobState(String execRoute, Long dagId) {
        if (Objects.isNull(execRoute)) {
            return null;
        }
        JobExecTemplate jobTemplate = JOBS.get(execRoute);
        if (Objects.isNull(jobTemplate)) {
            return null;
        }
        return jobTemplate.confirmJobState(dagId);
    }

    /**
     * 监控器通知事件监听
     *
     * @param jobMonitorNotify
     */
    @Async
    @EventListener
    public void monitorNotifyTrigger(JobMonitorNotify jobMonitorNotify) {
        JobExecContext jobExecContext = GeiCommonConvert.convert(jobMonitorNotify.getJobMonitorContext(), JobExecContext.class);
        try {
            if (!jobMonitorNotify.isSuccess()) {
                JobMonitorContext jobMonitorContext = jobMonitorNotify.getJobMonitorContext();
                ScpTaskExecChainDO chainNode = jobMonitorContext.getChainNode();
                if (TaskStatusEnum.FAILED.isThis(chainNode.getStatus())) {
                    return;
                }
                StringBuilder errorMsgBuilder = new StringBuilder();
                if (CollectionUtils.isNotEmpty(jobMonitorNotify.getFailedResult())) {
                    List<MonitorResult> failedResult = jobMonitorNotify.getFailedResult();
                    int i = 1;
                    for (MonitorResult monitorResult : failedResult) {
                        errorMsgBuilder.append(i++).append(": ").append(monitorResult.getErrorMsg()).append("；");
                    }
                }
                stateModifier.modify(chainNode, jobExecContext, TaskStatusEnum.FAILED, errorMsgBuilder.toString());
            }
        } catch (Exception e) {
            log.error("monitorNotifyTrigger error: ", e);
            JobMonitorContext jobMonitorContext = jobMonitorNotify.getJobMonitorContext();
            if (Objects.isNull(jobMonitorContext) || Objects.isNull(jobMonitorContext.getTaskId())) {
                return;
            }
            stateModifier.modify(jobMonitorContext.getChainNode(), jobExecContext, TaskStatusEnum.FAILED, StateModifier.buildErrorMsg(e));
        } finally {
            doExecuteTaskTrigger(jobExecContext);
        }
    }

    /**
     * 任务执行事件监听
     */
    @Async
    @EventListener
    public void executeJobTrigger(JobExecContext jobExecContext) {
        // 前置校验
        if (!triggerBefore(jobExecContext)) {
            return;
        }
        doExecuteTaskTrigger(jobExecContext);
    }

    private void doExecuteTaskTrigger(JobExecContext jobExecContext) {
        // 执行器工作
        if (doExecuteJob(jobExecContext)) {
            // 发送监听器事件
            jobEventPublisher.sendMonitorEvent(GeiCommonConvert.convert(jobExecContext, JobMonitorContext.class));
        } else {
            // 返回false时关闭任务执行器
            int retry = 5;
            while (retry-- > 0) {
                try {
                    if (taskCoreStatManager.deleteByCore(CORE) > 0) {
                        break;
                    }
                } catch (Exception e) {
                    log.error("关闭任务执行器失败，重试次数: {}, error is {}", retry, e);
                }
            }
        }
    }

    private boolean doExecuteJob(JobExecContext jobExecContext) {
        if (Objects.isNull(jobExecContext)) {
            return false;
        }
        JobExecTemplate jobTemplate = JOBS.get(jobExecContext.getExecRoute());
        if (Objects.isNull(jobTemplate)) {
            stateModifier.modify(null, jobExecContext, TaskStatusEnum.FAILED, "没有路由到有效的任务执行器");
            return false;
        }
        // step1: 查找执行链下一个节点
        ScpTaskExecChainDO chainNextNode;
        try {
            chainNextNode = execChainManager.getChainNextNode();
            if (Objects.isNull(chainNextNode)) {
                return false;
            }
            jobExecContext.setChainNode(chainNextNode);
        } catch (Exception e) {
            stateModifier.modify(null, jobExecContext, TaskStatusEnum.FAILED, StateModifier.buildErrorMsg(e));
            return false;
        }
        try {
            // step2: 前置参数准备
            JobExecMode jobExecMode = JobExecMode.getByCode(chainNextNode.getExecMode());
            Assert.isTrue(Objects.nonNull(jobExecMode), ErrorCode.ILLEGAL_ARGUMENT, TaskError.JOB_EXEC_MODE_ILLEGAL.getError());
            TaskConfigQueryRequest request = jobExecMode.getRequest(chainNextNode, jobExecContext.getTaskCfgList());
            if (Objects.isNull(request)) {
                // 当任务配置查询请求体为空时，认为当前执行链节点已结束，设置状态为已完成
                stateModifier.modify(chainNextNode, jobExecContext, TaskStatusEnum.COMPLETED, "");
                return doExecuteJob(jobExecContext);
            }

            // step3: 根据节点保存的执行模式（执行当前 ｜ 执行下游），查询任务配置
            List<ScpTaskCfgDO> scpTaskCfgList = taskConfigManager.queryConfig(request);
            if (CollectionUtils.isEmpty(scpTaskCfgList)) {
                throw new RuntimeException("没有找到相关任务配置，无法执行任务！request is: " + JsonUtils.toStr(request));
            }
            scpTaskCfgList = appendChainNodeAndResetTaskCfgListIfNeed(scpTaskCfgList, chainNextNode, jobExecContext);
            log.info("批次号：{}，任务id：{}，任务类型：{}，本次执行的任务列表：{}"
                    , chainNextNode.getBatchNo(), chainNextNode.getRootTaskId()
                    , chainNextNode.getRootTaskType(), JsonUtils.toStr(scpTaskCfgList));
            // step4: 记录表状态
            stateModifier.modify(chainNextNode, jobExecContext, TaskStatusEnum.RUNNING, null);
            // step5: 回填任务执行上下文
            fillJobExecContext(jobExecContext, chainNextNode, scpTaskCfgList);
            // step6: 正式提交任务
            ExecuteResult submit = jobTemplate.submit(jobExecContext);
            if (submit.isSuccess()) {
                jobExecContext.setDagIds(submit.getDagIds());
                return true;
            }
            throw new RuntimeException(submit.getErrorMsg());
        } catch (Exception e) {
            log.error("doExecuteJob error: ", e);
            // step7: 当前执行链节点执行异常时，且属于执行当前任务模式时 不影响后续节点运行
            stateModifier.modify(chainNextNode, jobExecContext, TaskStatusEnum.FAILED, StateModifier.buildErrorMsg(e));
            return doExecuteJob(jobExecContext);
        }
    }

    private void fillJobExecContext(JobExecContext jobExecContext, ScpTaskExecChainDO chainNextNode, List<ScpTaskCfgDO> sckTaskCfgDO) {
        jobExecContext.setTaskCfgList(sckTaskCfgDO);
        jobExecContext.setBatchNo(chainNextNode.getBatchNo());
        jobExecContext.setTaskId(chainNextNode.getRootTaskId());
        jobExecContext.setBaseTaskInfo(getRunningBaseTaskInfo(chainNextNode.getRootTaskId(), null));
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean triggerBefore(JobExecContext jobExecContext) {
        if (Objects.isNull(jobExecContext)) {
            return false;
        }
        // 如果执行器已被触发，则直接返回 不再重复触发
        try {
            ScpTaskCoreStatDO scpTaskCoreStatDO = new ScpTaskCoreStatDO();
            scpTaskCoreStatDO.setCoreCode(CORE.getCode());
            scpTaskCoreStatDO.setCoreName(CORE.getName());
            scpTaskCoreStatDO.setStatus(BooleanEnum.TRUE.getCode());
            Long total = taskCoreStatManager.insert(scpTaskCoreStatDO);
            if (total < 1) {
                return false;
            }
        } catch (Exception e) {
            log.error("前置检查执行器状态异常: ", e);
            return false;
        }
        return true;
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext context) throws BeansException {
        this.context = context;
    }

    @Override
    public void run(ApplicationArguments args) {
        JOBS.putAll(context.getBeansOfType(JobExecTemplate.class));
    }

    public TaskManagementDTO getRunningBaseTaskInfo(Long taskId, TaskStatusEnum status) {
        TaskManagementPageQueryRequest query = new TaskManagementPageQueryRequest();
        if (Objects.nonNull(taskId)) {
            query.setTask(Collections.singletonList(taskId));
        }
        if (Objects.nonNull(status)) {
            query.setMultiStatus(Collections.singletonList(status.getCode()));
        }
        List<TaskManagementDTO> baseTaskData = taskManagementService.queryPageData(query);
        if (CollectionUtils.isNotEmpty(baseTaskData)) {
            return baseTaskData.get(0);
        }
        return null;
    }

    /**
     * 如果需要，将追加执行链节点并重新设置任务配置列表
     *
     * @param scpTaskCfgList 任务配置列表
     * @param chainNextNode  当前执行链节点
     * @return 任务配置列表
     */
    private List<ScpTaskCfgDO> appendChainNodeAndResetTaskCfgListIfNeed(List<ScpTaskCfgDO> scpTaskCfgList, ScpTaskExecChainDO chainNextNode, JobExecContext context) {
        Function<ScpTaskCfgDO, Long> toLongMapper = item -> SafeGetter.get(item.getDateExprPriority(), 0L);
        // 按照日期表达式分组
        Map<String, List<ScpTaskCfgDO>> dateExprGroup = StreamUtils.group(scpTaskCfgList, item -> SafeGetter.get(item.getDateExpr(), Constants.DEFAULT_DATE_EXPR));
        // 执行链节点的日期表达式是否已被设置过
        boolean dateExprIsValid = Objects.nonNull(chainNextNode.getDateExpr()) && !StringUtils.equals(chainNextNode.getDateExpr(), Constants.DEFAULT_CHAIN_DATE_EXPR);
        if (dateExprIsValid) {
            chainNextNode.setBeforeDateExpr(chainNextNode.getDateExpr());
            return scpTaskCfgList;
        }
        // 只存在一种表达式时，不用追加执行链
        if (dateExprGroup.size() == 1) {
            chainNextNode.setBeforeDateExpr(chainNextNode.getDateExpr());
            chainNextNode.setDateExpr(scpTaskCfgList.get(0).getDateExpr());
            //chainNextNode.setDateExprPriority(StreamUtils.sumLong(scpTaskCfgList, toLongMapper));
            long sumDateExprPriority = scpTaskCfgList.stream().mapToLong(ScpTaskCfgDO::getDateExprPriority).sum();
            chainNextNode.setDateExprPriority(sumDateExprPriority);
            chainNextNode.setAllDateExpr(scpTaskCfgList.get(0).getDateExpr());
            return scpTaskCfgList;
        }
        Map<String, Long> dateExprOrder = new LinkedHashMap<>(dateExprGroup.size());
        // dateExprGroup.forEach((k, v) -> dateExprOrder.put(k, StreamUtils.sumLong(v, toLongMapper)));
        dateExprGroup.forEach((k, v) -> dateExprOrder.put(k, v.stream().mapToLong(ScpTaskCfgDO::getDateExprPriority).sum()));
        List<String> sortedDateExprList = StreamUtils.getSortedKeyList(dateExprOrder, Long.MAX_VALUE);
        String allDateExpr = StringUtils.join(sortedDateExprList, Constants.DEFAULT_CHAIN_ALL_DATE_EXPR_GAP);
        String firstExpr = sortedDateExprList.remove(0);
        chainNextNode.setBeforeDateExpr(chainNextNode.getDateExpr());
        chainNextNode.setDateExpr(firstExpr);
        chainNextNode.setDateExprPriority(dateExprOrder.get(firstExpr));
        chainNextNode.setAllDateExpr(allDateExpr);
        log.info("检测到任务配置列表中存在多个日期表达式，当前执行链节点：{}", JsonUtils.toStr(chainNextNode));
        for (String expr : sortedDateExprList) {
            ScpTaskExecChainDO scpTaskExecChain = GeiCommonConvert.copyAll(chainNextNode);
            scpTaskExecChain.setDateExpr(expr);
            scpTaskExecChain.setStatus(TaskStatusEnum.AWAIT.getCode());
            scpTaskExecChain.setDateExprPriority(dateExprOrder.get(expr));
            execChainManager.insertChainNode(scpTaskExecChain);
            log.info("检测到任务配置列表中存在多个日期表达式，更新或追加执行链节点：{}", JsonUtils.toStr(scpTaskExecChain));
        }
        // 过滤出最终的任务配置列表
        List<ScpTaskCfgDO> filteredTaskList = StreamUtils.filter(scpTaskCfgList, item -> {
            String dateExpr = item.getDateExpr();
            if (StringUtils.equals(Constants.DEFAULT_DATE_EXPR, firstExpr) && StringUtils.isBlank(dateExpr)) {
                return true;
            }
            return StringUtils.equals(dateExpr, firstExpr);
        });
        // 如果不包含根任务节点，则追加根任务节点，并添加到忽略任务列表中
        boolean existsRoot = false;
        for (ScpTaskCfgDO taskCfg : filteredTaskList) {
            if (BooleanEnum.TRUE.isThis(taskCfg.getRootOdps())) {
                existsRoot = true;
                break;
            }
        }
        if (!existsRoot) {
            for (ScpTaskCfgDO taskCfg : scpTaskCfgList) {
                if (BooleanEnum.TRUE.isThis(taskCfg.getRootOdps())) {
                    filteredTaskList.add(taskCfg);
                    context.setExcludeTaskCfgList(Collections.singletonList(taskCfg));
                    break;
                }
            }
        }
        return filteredTaskList;
    }
}
