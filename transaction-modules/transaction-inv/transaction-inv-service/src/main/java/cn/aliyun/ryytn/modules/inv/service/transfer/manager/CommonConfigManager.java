package cn.aliyun.ryytn.modules.inv.service.transfer.manager;


import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.inv.common.enums.BooleanEnum;
import cn.aliyun.ryytn.modules.inv.common.exception.Assert;
import cn.aliyun.ryytn.modules.inv.common.exception.ErrorCode;
import cn.aliyun.ryytn.modules.inv.constant.transfer.TransferConstants;
import cn.aliyun.ryytn.modules.inv.constant.transfer.error.ConfigError;
import cn.aliyun.ryytn.modules.inv.constant.transfer.error.TransferError;
import cn.aliyun.ryytn.modules.inv.entity.transfer.dto.ScpCommonConfigDTO;
import cn.aliyun.ryytn.modules.inv.entity.transfer.request.CommonConfigDeleteRequest;
import cn.aliyun.ryytn.modules.inv.entity.transfer.request.CommonConfigQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.transfer.request.CommonConfigValueInsertRequest;
import cn.aliyun.ryytn.modules.inv.service.transfer.dto.CommonConfigDTO;
import cn.aliyun.ryytn.modules.inv.service.transfer.enums.OptionType;
import cn.aliyun.ryytn.modules.inv.service.transfer.enums.ValueType;
import cn.aliyun.ryytn.modules.inv.transfer.dao.CommonConfigDao;
import cn.aliyun.ryytn.modules.inv.transfer.dao.CommonConfigValueDao;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.StreamUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/20 14:28
 * @description：
 */
@Component
public class CommonConfigManager {
    @Resource
    private CommonConfigDao commonConfigDao;
    @Resource
    private DynamicOptionManager dynamicOptionManager;
    @Resource
    private CommonConfigValueDao commonConfigValueDao;

//    public List<CommonConfigDTO> getConfigByModule(String module, Map<String, Object> params) {
//        CommonConfigQueryRequest request = CommonConfigQueryRequest.of(params);
//        request.setModuleName(module);
//        request.setParams(params);
//        return queryConfigByCondition(request);
//    }

    public List<CommonConfigDTO> queryConfigByCondition(CommonConfigQueryRequest request) {
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        if (Objects.nonNull(currentAccount)) {
            request.setCreator(currentAccount.getId());
        }else {
            request.setCreator(CommonConstants.SYSADMIN_ROLE_ID);
        }
        List<ScpCommonConfigDTO> commonConfigList = commonConfigDao.selectByCondition(JsonUtils.toMap(request));
        List<CommonConfigDTO> result = new ArrayList<>(commonConfigList.size());
        for (ScpCommonConfigDTO commonConfig : commonConfigList) {
            CommonConfigDTO config = GeiCommonConvert.convert(commonConfig, CommonConfigDTO.class);
            OptionType optionType = OptionType.getByCode(commonConfig.getOptionsType());
            if (BooleanEnum.isTrue(request.getIsHandleOption()) && Objects.nonNull(optionType) && Objects.nonNull(config)) {
                config.setOptions(optionType.getOptionList(request, commonConfig, dynamicOptionManager));;
            }
            result.add(config);
        }
        return result;
    }

    public Long save(List<CommonConfigValueInsertRequest> requests) {
        fillSaveRequests(requests);
        requests = requests.stream().filter(s -> !(s.getConfigCode().equals("field") || s.getConfigCode().equals("fixedField") || s.getConfigCode().equals("optionalField"))).collect(Collectors.toList());
        deleteBeforeSave(requests);
        return commonConfigValueDao.upsert(JsonUtils.toList(requests));
    }

    private void fillSaveRequests(List<CommonConfigValueInsertRequest> requests) {
        Assert.isTrue(CollectionUtils.isNotEmpty(requests), ErrorCode.ILLEGAL_ARGUMENT, ConfigError.SAVE_REQUEST_NOT_NULL.getError());
        String tenantCode = "ryytn";
        String cnUserId = null;
        String cnUserName = null;
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        Assert.isTrue(Objects.nonNull(currentAccount), ErrorCode.ILLEGAL_ARGUMENT, TransferError.OPERATOR_NOT_NULL.getError());
        cnUserId = currentAccount.getId();
        cnUserName = currentAccount.getName();
        CommonConfigQueryRequest query = CommonConfigQueryRequest.of();
        List<String> configCodes = StreamUtils.map(requests, CommonConfigValueInsertRequest::getConfigCode);
        query.setConfig(configCodes);
        query.setTenantCode(tenantCode);
        query.setModuleName(requests.get(0).getModuleName());
        query.setIsHandleOption(Boolean.FALSE);
        List<CommonConfigDTO> configList = queryConfigByCondition(query);
        Map<String, CommonConfigDTO> configMap = StreamUtils.singleGroup(configList, CommonConfigDTO::getConfigCode);
        for (CommonConfigValueInsertRequest request : requests) {

            request.setTenantCode(tenantCode);
            request.setCreatorCode(cnUserId);
            request.setCreatorName(cnUserName);
            if ("field".equals(request.getConfigCode()) || "fixedField".equals(request.getConfigCode()) || "optionalField".equals(request.getConfigCode())){
                request.setConfigValue("[]");
                continue;
            }
            CommonConfigDTO config = configMap.get(request.getConfigCode());
            Assert.isTrue(Objects.nonNull(config), ErrorCode.ILLEGAL_ARGUMENT, String.format(ConfigError.CONFIG_NOT_FOUND.getError(), request.getConfigCode()));
            ValueType valueType = ValueType.getByCode(config.getValueType());
            if (Objects.nonNull(valueType)) {
                if (Objects.equals(valueType, ValueType.ARRAY)) {
                  //  Assert.isTrue(CollectionUtils.isNotEmpty((List) request.getConfigValue()), ErrorCode.ILLEGAL_ARGUMENT, String.format(ConfigError.CONFIG_VALUE_NOT_NULL.getError(), request.getConfigCode()));
                }
                request.setConfigValue(valueType.toValue(request.getConfigValue()));
            }
          //  Assert.isTrue(Objects.nonNull(request.getConfigValue()), ErrorCode.ILLEGAL_ARGUMENT, String.format(ConfigError.CONFIG_VALUE_NOT_NULL.getError(), request.getConfigCode()));
            if (Objects.nonNull(request.getValueExt())) {
                request.setValueExt(JsonUtils.toStr(request.getValueExt()));
            }
            if (Objects.isNull(request.getCreatorCode())) {
                request.setCreatorCode(cnUserId);
            }
            if (Objects.isNull(request.getCreatorName())) {
                request.setCreatorName(cnUserName);
            }
            if (Objects.isNull(request.getTenantCode())) {
                request.setTenantCode(tenantCode);
            }
            if (Objects.equals(request.getConfigCode(), TransferConstants.CommonConfig.FIELD)) {
                request.setConfigValue(null);
            }
        }
    }
    private void deleteBeforeSave(List<CommonConfigValueInsertRequest> requests) {
        List<CommonConfigDeleteRequest> deleteRequests = GeiCommonConvert.convert(requests, CommonConfigDeleteRequest.class);
        commonConfigValueDao.delete(JsonUtils.toList(deleteRequests));
    }
}
