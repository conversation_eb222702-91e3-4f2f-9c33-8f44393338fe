package cn.aliyun.ryytn.modules.inv.service.stat;

import cn.aliyun.ryytn.modules.inv.api.stat.StatService;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.StatDateNotifyDO;
import cn.aliyun.ryytn.modules.inv.stat.dao.StatDateNotifyMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class StatServiceImpl implements StatService {

    @Autowired
    private StatDateNotifyMapper statDateNotifyMapper;

    @Override
    public String getModuleStatDate(String moduleCode, String dateFormat) {
        StatDateNotifyDO entity = getEntityByModuleCode(moduleCode);
        if (entity != null) {
            String statDate = entity.getStatDate();
            return DateUtil.localDateToString(
                DateUtil.stringToLocalDate(statDate, DateUtil.STANDARD_DATE_FORMAT), dateFormat);
        } else {
            return null;
        }
    }

    @Override
    public StatDateNotifyDO getModule(String moduleCode) {
        return getEntityByModuleCode(moduleCode);
    }

    //
    private StatDateNotifyDO getEntityByModuleCode(String moduleCode) {
//        LambdaQueryWrapper<StatDateNotifyDO> wrapper = Wrappers.lambdaQuery();
//        wrapper.eq(StatDateNotifyDO::getModuleCode, moduleCode);
        return statDateNotifyMapper.selectOne(moduleCode);
    }
}
