package cn.aliyun.ryytn.modules.inv.controller.task.taskApi;

/**
 * <AUTHOR>
 * @date 2024/9/27 11:00
 * @description：
 */
public interface TaskManagementApi {

    interface TaskManagement {

        String ROOT = "/api/task-management";

        String GATHER = "/gather";

        String LIST = "/list";

        String QUERY_BY_TASK_ID_OR_NAME = "/queryByTaskIdOrName";

        String QUERY_SCREENING_ITEM = "/queryScreeningItem";

        String UPDATE_STATUS = "/updateStatus";

        String QUERY_IMPACT_RANGE = "/queryImpactRange";

        String SELECT_IMPACT_RANGE = "/queryCoverMtWh";

        String CREATE = "/create";
        String GET_COVER_SEARCH_TYPE = "/getCoverSearchType";
        String SEARCH_TASK_STATUS = "/searchTaskStatus.json";
        String SEARCH_SCHEDULING = "/searchScheduling.json";

        String REMOVE_TASK = "/removeTask";
        String UPSERT_TASK_CFG = "/upsertTaskCfg";
        String REMOVE_TASK_CFG = "/removeTaskCfg";

    }

    interface JobExecute {
        String ROOT = "/api/job-exec";
        String EXEC_CURR = "/curr";
        String EXEC_DOWN = "/down";
        String CHECK_COMMITTED = "/check-committed";
        String CANCEL_EXEC = "/cancel";
        String GEN_SYS_TASK_BASE_INFO = "/genSysTask";
        String NOTIFY_SYS_TASK = "/notifySysTask";
        String GET_TASK_CONFIG = "/getTaskConfig";
        String GET_DATE_EXPR = "/getDateExpr";
        String GET_ODPS_LIST = "/getOdpsList.json";
        String GET_TASK_CONFIG_STATUS = "/getTaskConfigStatus";


        String RERUN_DISTRIBUTION = "/rerunDistribution";
    }
}
