package cn.aliyun.ryytn.modules.inv.service.task;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.modules.inv.api.task.request.*;
import cn.aliyun.ryytn.modules.inv.common.exception.Assert;
import cn.aliyun.ryytn.modules.inv.common.exception.ErrorCode;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.common.utils.IndexCalculator;
import cn.aliyun.ryytn.modules.inv.common.utils.PageUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StreamUtils;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.*;
import cn.aliyun.ryytn.modules.inv.api.md.PlanUnitService;
import cn.aliyun.ryytn.modules.inv.task.dao.ScpTaskManagementMapper;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.TaskImpactRangeDTO;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.TaskManagementDTO;
import cn.aliyun.ryytn.modules.inv.api.task.enums.JobSchedulingEnum;
import cn.aliyun.ryytn.modules.inv.api.task.enums.TaskCoverEnum;
import cn.aliyun.ryytn.modules.inv.api.task.enums.TaskStatusEnum;
import cn.aliyun.ryytn.modules.inv.api.task.error.TaskError;
import cn.aliyun.ryytn.modules.inv.service.task.manager.TaskConfigManager;
import cn.aliyun.ryytn.modules.inv.service.task.manager.TaskTypeConfigManager;
import cn.aliyun.ryytn.modules.inv.api.task.TaskManagementService;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.SnowflakeIdWorker;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/11/13 11:08
 * @description：任务管理service实现类
 */
@Service
public class TaskManagementServiceImpl implements TaskManagementService {

    @Resource
    private ScpTaskManagementMapper scpTaskManagementMapper;

    // todo: 改成品仓主数据
   /* @Resource
    private ScpMdPlanUnitBaseMapper scpMdPlanUnitBaseMapper;*/

    @Resource
    private PlanUnitService planUnitService;

    @Resource
    private TaskConfigManager taskConfigManager;
    @Resource
    private TaskTypeConfigManager taskTypeConfigManager;
    private static final SnowflakeIdWorker ID_WORKER = new SnowflakeIdWorker(0, 0);

    @Override
    public List<OptionDTO> getSumNumber(TaskManagementPageQueryRequest request) {
        ScpTaskManagementSumNumberDO scpTaskManagementSumNumberDO = scpTaskManagementMapper.getSumNumber(JsonUtils.toMap(request));
        List<OptionDTO> optionDTOList = new ArrayList<>();
        for (TaskStatusEnum value : TaskStatusEnum.values()) {
            optionDTOList.add(OptionDTO.of(value.getCode(), value.getName(), value.getSumNumber(scpTaskManagementSumNumberDO)));
        }
        return optionDTOList;
    }

    @Override
    public List<TaskManagementDTO> queryPageData(TaskManagementPageQueryRequest request) {
        PageUtils.prePageHandle(request, () -> queryCount(request));
        List<ScpTaskManagementDO> scpTaskManagementDOList = scpTaskManagementMapper.selectByCondition(JsonUtils.toMap(request));
        List<TaskManagementDTO> convert = GeiCommonConvert.convert(scpTaskManagementDOList, TaskManagementDTO.class);
        Map<String, ScpTaskTypeCfgDO> taskCfgMap = queryTaskTypeGroup();
        convert.forEach(t -> {
            t.setStatusDescribe(TaskStatusEnum.getNameByCode(t.getStatus()));
            ScpTaskTypeCfgDO cfg = taskCfgMap.get(t.getTaskType());
            if(Objects.nonNull(cfg)){
                t.setTaskTypeDescribe(cfg.getTaskTypeName());
            }
            // t.setSchedulingTypeDescribe(JobSchedulingEnum.getNameByCode(t.getSchedulingType()));
 /*           if (BigDecimal.ONE.compareTo(t.getImpactRange()) == 0) {
                t.setImpactRangeDescribe("全部品仓（100/100%）");
            } else {
                t.setImpactRangeDescribe(String.format("部分品仓（100/%s%%）"
                        , IndexCalculator.scale(IndexCalculator.multiply(t.getImpactRange(), 100), 2))
                );
            }*/
        });
        return convert;
    }

    @Override
    public Long queryCount(TaskManagementPageQueryRequest request) {
        return scpTaskManagementMapper.selectCount(JsonUtils.toMap(request));
    }

    @Override
    public List<TaskManagementDTO> queryByTaskIdOrName(String task) {
        List<ScpTaskManagementDO> queryList = scpTaskManagementMapper.queryByTaskIdOrName(task);
        return GeiCommonConvert.convert(queryList, TaskManagementDTO.class);
    }

    @Override
    public Long update(TaskManageUpdateRequest request){
        Assert.isTrue(StringUtils.isNotEmpty(request.getStatus()),
                ErrorCode.ILLEGAL_ARGUMENT, TaskError.STATUS_IS_NOT_NULL.getError());
        Assert.isTrue(CollectionUtils.isNotEmpty(request.getTask()),
                ErrorCode.ILLEGAL_ARGUMENT, TaskError.TASK_ID_IS_NOT_NULL.getError());

        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        if (Objects.nonNull(currentAccount)) {
            request.setOperatorCode(currentAccount.getId());
            request.setOperatorName(currentAccount.getName());
        }
        return scpTaskManagementMapper.update(JsonUtils.toMap(request));
    }

    @Override
    public List<TaskImpactRangeDTO> queryImpactRange(TaskCoverPageQueryRequest request) {
        request.setPaging(false);
        List<ScpTaskImpactRangeDO> list = scpTaskManagementMapper.queryImpactRange(JsonUtils.toMap(request));
        return GeiCommonConvert.convert(list, TaskImpactRangeDTO.class);
    }

    @Override
    public Long queryImpactRangeCount(TaskCoverPageQueryRequest request) {
        return scpTaskManagementMapper.selectImpactRangeCount(JsonUtils.toMap(request));
    }

    @Override
    public List<TaskImpactRangeDTO> selectImpactRange(TaskCoverPageQueryRequest request) {
        Assert.isTrue(StringUtils.isNotEmpty(request.getRange()), ErrorCode.ILLEGAL_ARGUMENT, TaskError.SELECT_RANGE_EMPTY.getError());
        TaskCoverEnum coverEnum = TaskCoverEnum.getByCode(request.getRange());
        Assert.isTrue(Objects.nonNull(coverEnum), ErrorCode.ILLEGAL_ARGUMENT, TaskError.SELECT_RANGE_ILLEGAL.getError());

        // todo： 查询品仓主数据
       /* PlanUnitPageQueryRequest query = coverEnum.getUnitQuery(request);
        List<ScpMdPlanUnitBaseDO> list = scpMdPlanUnitBaseMapper.selectByCondition(JsonUtils.toMap(query));
        return CommonConvert.convert(list, TaskImpactRangeDTO.class);*/
        return null;
    }

    @Override
    public Long selectImpactRangeCount(TaskCoverPageQueryRequest request) {
        TaskCoverEnum coverEnum = TaskCoverEnum.getByCode(request.getRange());
        Assert.isTrue(Objects.nonNull(coverEnum), ErrorCode.ILLEGAL_ARGUMENT, TaskError.SELECT_RANGE_ILLEGAL.getError());
        // todo： 查询品仓主数据
        //return scpMdPlanUnitBaseMapper.selectCount(JsonUtils.toMap(coverEnum.getUnitQuery(request)));
    return null;
    }

    @Transactional
    @Override
    public Long createTask(TaskInsertRequest request) {
        Assert.isTrue(CollectionUtils.isNotEmpty(request.getTaskType()), ErrorCode.ILLEGAL_ARGUMENT, TaskError.TASK_TYPE_IS_NOT_NULL.getError());
        List<TaskCoverInsertRequest> impactRangeList = request.getImpactRangeList();
/*        checkCoverMtWh(impactRangeList);
        // todo: 查询品仓主数据 ==== start
        //Long count = scpMdPlanUnitBaseMapper.selectCount(new HashMap<>());
        Long count = 0L;
        // 计算比例
        BigDecimal impactRange = CollectionUtils.isEmpty(impactRangeList) ? BigDecimal.ONE : IndexCalculator.scale(IndexCalculator.div(impactRangeList.size(), count), 4);
        // todo: 查询品仓主数据 ==== end*/

        // 填充任务类型名称
        Map<String, ScpTaskTypeCfgDO> taskCfgMap = queryTaskTypeGroup();
        List<TaskFinalInsertRequest> finalRequestList = new ArrayList<>();
        List<String> taskTypeList = request.getTaskType();
        for (String taskType : taskTypeList) {
            TaskFinalInsertRequest finalRequest = GeiCommonConvert.convert(request, TaskFinalInsertRequest.class);
            Assert.isTrue(Objects.nonNull(finalRequest), ErrorCode.ILLEGAL_ARGUMENT, TaskError.INSERT_COVER_NOT_EXISTS.getError());
            if(Objects.isNull(finalRequest.getSchedulingType())){
                finalRequest.setSchedulingType(JobSchedulingEnum.MANUAL.getCode());
                finalRequest.setSchedulingTime(null);
            }else if(JobSchedulingEnum.MANUAL.isThis(finalRequest.getSchedulingType())){
                finalRequest.setSchedulingTime(null);
            }
            long taskId = ID_WORKER.nextId();
            ScpTaskTypeCfgDO cfg = taskCfgMap.get(taskType);
            if(Objects.nonNull(cfg)){
                finalRequest.setTaskName(cfg.getTaskTypeName()
                        /*+ (BigDecimal.ONE.compareTo(impactRange) == 0 ? "-全部品仓" : "-部分品仓")*/
                );
            }
            // finalRequest.setImpactRange(impactRange);
            finalRequest.setOperationTime(LocalDateTime.now());
            finalRequest.setStatus(TaskStatusEnum.NOT_RUN.getCode());
            finalRequest.setTaskId(taskId);
            finalRequest.setTaskType(taskType);
            finalRequestList.add(finalRequest);
            if(CollectionUtils.isNotEmpty(impactRangeList)){
                for (TaskCoverInsertRequest coverInsert : impactRangeList) { coverInsert.setTaskId(taskId); }
                scpTaskManagementMapper.batchInsertImpactRange(JsonUtils.toList(impactRangeList));
            }
        }
        return scpTaskManagementMapper.batchUpsert(JsonUtils.toList(finalRequestList));
    }

    /**
     * 检查覆盖品仓是否合法
     */
    private void checkCoverMtWh(List<TaskCoverInsertRequest> impactRangeList){
        if(CollectionUtils.isEmpty(impactRangeList)){
            return;
        }
        // todo: 查询品仓主数据
       /* PlanUnitPageQueryRequest request = new PlanUnitPageQueryRequest();
        request.setMaterial( StreamUtils.map(impactRangeList, TaskCoverInsertRequest::getMaterialCode) );
        request.setWarehouse( StreamUtils.map(impactRangeList, TaskCoverInsertRequest::getWarehouseCode) );
        request.setPaging(false);
        List<PlanUnitBaseDTO> list = planUnitService.queryPageData(request);
        Map<String, PlanUnitBaseDTO> unitMap = StreamUtils.singleGroup(list, item -> item.getMaterialCode() + item.getWarehouseCode());
        for (TaskCoverInsertRequest cover : impactRangeList) {
            String key = cover.getMaterialCode() + cover.getWarehouseCode();
            PlanUnitBaseDTO planUnitBaseDTO = unitMap.get(key);
            Assert.isTrue(Objects.nonNull(planUnitBaseDTO), ErrorCode.ILLEGAL_ARGUMENT, TaskError.INSERT_COVER_NOT_EXISTS.getError());
            cover.setMaterialName(planUnitBaseDTO.getMaterialName());
            cover.setWarehouseName(planUnitBaseDTO.getWarehouseName());
        }*/
    }

    @Override
    public List<ScpTaskCfgDO> queryTaskConfig(TaskConfigQueryRequest request) {
//        PageUtils.prePageHandle(request, () -> taskConfigManager.queryConfigCount(request));
        return taskConfigManager.queryConfig(request);
    }
    @Override
    public Long queryTaskConfigCount(TaskConfigQueryRequest request){
        return taskConfigManager.queryConfigCount(request);
    }

    @Override
    public List<ScpTaskTypeCfgDO> queryTaskTypeConfig(TaskTypeQueryRequest request){
        return taskTypeConfigManager.queryConfig(request);
    }

    @Override
    public Long upsertTaskCfg(List<TaskConfigUpsertRequest> requests) {
        return taskConfigManager.upsert(requests);
    }
    @Override
    public Long deleteTaskCfg(String taskTypeCode, Long odpsCode) {
        return taskConfigManager.delete(taskTypeCode, odpsCode);
    }


    @Override
    public Long removeTask(Long taskId) {
        Assert.isTrue(Objects.nonNull(taskId), ErrorCode.ILLEGAL_ARGUMENT, TaskError.TASK_ID_IS_NOT_NULL.getError());
        Long deleteResult = scpTaskManagementMapper.deleteTaskByTaskId(taskId);
        Assert.isTrue(deleteResult > 0, ErrorCode.ILLEGAL_ARGUMENT, TaskError.TASK_REMOVE_FAILED.getError());
        return deleteResult;
    }

    private Map<String, ScpTaskTypeCfgDO> queryTaskTypeGroup(){
        List<ScpTaskTypeCfgDO> list = queryTaskTypeConfig(new TaskTypeQueryRequest());
        return StreamUtils.singleGroup(list, ScpTaskTypeCfgDO::getTaskTypeCode);
    }
}

