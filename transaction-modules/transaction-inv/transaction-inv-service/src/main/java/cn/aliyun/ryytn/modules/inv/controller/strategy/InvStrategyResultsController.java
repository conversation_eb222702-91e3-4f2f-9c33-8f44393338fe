package cn.aliyun.ryytn.modules.inv.controller.strategy;

import cn.aliyun.ryytn.modules.inv.api.strategy.InvStrategyResultsService;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.constant.strategy.enums.ReplnTypeEnum;
import cn.aliyun.ryytn.modules.inv.controller.strategy.api.InvStrategyApi;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dto.InvStrategyResultsGatherDTO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvStrategyResulesUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvStrategyResultsPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.vo.InvStrategyResultsGatherVO;
import cn.aliyun.ryytn.modules.inv.common.model.ResponseResult;
import cn.aliyun.ryytn.udf.enums.InvResultsExceptionErrorEnum;
import com.cainiao.cntech.dsct.scp.gei.biz.web.model.OptionVO;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/6 10:34
 * @description：
 */
@Slf4j
@RestController
@RequestMapping(InvStrategyApi.StockStrategyResultsToc.ROOT)
@Api(tags = "库存策略结果")
public class InvStrategyResultsController {

    @Autowired
    private InvStrategyResultsService invStrategyResultsService;

    @ApiOperation("快速筛选项查询")
    @GetMapping(InvStrategyApi.SafetyStock.QUICK_QUERY_CLAUSE)
    public ResponseResult<List<OptionVO>> quickQueryClause() {
        List<OptionDTO> optionList = invStrategyResultsService.quickQueryClauseOption();
        return ResponseResult.success(GeiCommonConvert.convert(optionList, OptionVO.class));
    }

    @ApiOperation(value = "库存策略中心汇总查询")
    @PostMapping(InvStrategyApi.StockStrategyResultsToc.GATHER)
    public ResponseResult<List<InvStrategyResultsGatherVO>> gather(@RequestBody InvStrategyResultsPageQueryRequest request) {
        List<InvStrategyResultsGatherDTO> queryList = invStrategyResultsService.queryGather(request);
        return ResponseResult.success(GeiCommonConvert.convert(queryList, InvStrategyResultsGatherVO.class));
    }

    @ApiOperation(value = "库存策略中心列表查询")
    @PostMapping(InvStrategyApi.StockStrategyResultsToc.LIST)
    public ResponseResult<PageMetaInfoWrapper> pageQueryData(@RequestBody InvStrategyResultsPageQueryRequest request) {
        PageMetaInfoWrapper pageMetaInfoWrapper = invStrategyResultsService.queryPageData(request);
        return ResponseResult.success(pageMetaInfoWrapper);
    }

    @ApiOperation(value = "库存策略管理列表更新")
    @PostMapping(InvStrategyApi.StockStrategyResultsToc.UPDATE)
    public ResponseResult<Long> update(@RequestBody InvStrategyResulesUpdateRequest request) {
        return ResponseResult.success(invStrategyResultsService.update(request));
    }

    @ApiOperation(value = "库存策略管理重置算法建议值")
    @PostMapping(InvStrategyApi.StockStrategyResultsToc.RESET_SUGGEST_VALUE)
    public ResponseResult<Long> resetSuggestValue(@RequestBody InvStrategyResultsPageQueryRequest request) {
        return ResponseResult.success(invStrategyResultsService.resetSuggestValue(request));
    }

    @ApiOperation(value = "优先级生效查询")
    @PostMapping(InvStrategyApi.StockStrategyResultsToc.SELECT_PRIORITY)
    public ResponseResult<PageMetaInfoWrapper> selectPriority(@RequestBody InvStrategyResultsPageQueryRequest request) {
        return ResponseResult.success(invStrategyResultsService.selectPriority(request));
    }

    @ApiOperation(value = "异常筛选项")
    @GetMapping(InvStrategyApi.StockStrategyResultsToc.SELECT_EXCEPTION)
    public ResponseResult<List<OptionVO>> selectException(@RequestParam(required = false) String replnType) {
        if (Objects.equals(ReplnTypeEnum.TOB.getCode(), replnType)) {
            return ResponseResult.success(getTobList());
        }
        return ResponseResult.success(getTocList());
    }

    @ApiOperation(value = "时间查询")
    @GetMapping(InvStrategyApi.StockStrategyResultsToc.SELECT_DATE)
    public ResponseResult<String> selectDate() {
        return ResponseResult.success(invStrategyResultsService.selectDate());
    }

    public static List<OptionVO> getTocList() {
        List<OptionVO> list = new ArrayList<>();
        list.add(OptionVO.of(InvResultsExceptionErrorEnum.ALL.getCode(), InvResultsExceptionErrorEnum.ALL.getName()));
        list.add(OptionVO.of(InvResultsExceptionErrorEnum.NORMAL.getCode(), InvResultsExceptionErrorEnum.NORMAL.getName()));
        OptionVO exception = OptionVO.of(InvResultsExceptionErrorEnum.ABNORMAL.getCode(), InvResultsExceptionErrorEnum.ABNORMAL.getName());
        List<OptionVO> children = new ArrayList<>();
        children.add(OptionVO.of(InvResultsExceptionErrorEnum.TOC_DAS_NUM_ZERO.getCode(), InvResultsExceptionErrorEnum.TOC_DAS_NUM_ZERO.getName()));
        children.add(OptionVO.of(InvResultsExceptionErrorEnum.TOC_SAFETY_ZERO.getCode(), InvResultsExceptionErrorEnum.TOC_SAFETY_ZERO.getName()));
        children.add(OptionVO.of(InvResultsExceptionErrorEnum.TOC_PARAM_ZERO.getCode(), InvResultsExceptionErrorEnum.TOC_PARAM_ZERO.getName()));
        children.add(OptionVO.of(InvResultsExceptionErrorEnum.TOC_DIST_ERROR.getCode(), InvResultsExceptionErrorEnum.TOC_DIST_ERROR.getName()));
        children.add(OptionVO.of(InvResultsExceptionErrorEnum.TOC_SHARED_SKU.getCode(), InvResultsExceptionErrorEnum.TOC_SHARED_SKU.getName()));
        exception.setChildren(children);
        list.add(exception);
        return list;
    }

    public static List<OptionVO> getTobList() {
        List<OptionVO> list = new ArrayList<>();
        list.add(OptionVO.of(InvResultsExceptionErrorEnum.ALL.getCode(), InvResultsExceptionErrorEnum.ALL.getName()));
        list.add(OptionVO.of(InvResultsExceptionErrorEnum.NORMAL.getCode(), InvResultsExceptionErrorEnum.NORMAL.getName()));
        OptionVO exception = OptionVO.of(InvResultsExceptionErrorEnum.ABNORMAL.getCode(), InvResultsExceptionErrorEnum.ABNORMAL.getName());
        List<OptionVO> children = new ArrayList<>();
//        children.add(OptionVO.of(InvResultsExceptionErrorEnum.TOC_DAS_NUM_ZERO.getCode(), InvResultsExceptionErrorEnum.TOC_DAS_NUM_ZERO.getName()));
//        children.add(OptionVO.of(InvResultsExceptionErrorEnum.TOC_SAFETY_ZERO.getCode(), InvResultsExceptionErrorEnum.TOC_SAFETY_ZERO.getName()));
//        children.add(OptionVO.of(InvResultsExceptionErrorEnum.TOC_PARAM_ZERO.getCode(), InvResultsExceptionErrorEnum.TOC_PARAM_ZERO.getName()));
//        children.add(OptionVO.of(InvResultsExceptionErrorEnum.TOC_DIST_ERROR.getCode(), InvResultsExceptionErrorEnum.TOC_DIST_ERROR.getName()));
//        children.add(OptionVO.of(InvResultsExceptionErrorEnum.TOC_SHARED_SKU.getCode(), InvResultsExceptionErrorEnum.TOC_SHARED_SKU.getName()));
        exception.setChildren(children);
        list.add(exception);
        return list;
    }


}
