package cn.aliyun.ryytn.modules.inv.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/24 20:55
 * @description：
 */
@Data
public class InvSkuReplnTypeExportTemplateVO {

    /**
     * sku编码
     */
    @ExcelField(value = "SKU编码", importKey = true)
    private String skuCode;
    /**
     * sku简称
     */
    @ExcelField("SKU简称")
    private String skuName;

    /**
     * 补货方式
     */
    @ExcelField("补货方式")
    private String replnType;

    @ExcelField(value = "操作人")
    private String operatorName;

    @ExcelField(value = "最近更新时间")
    private String gmtModified;

}
