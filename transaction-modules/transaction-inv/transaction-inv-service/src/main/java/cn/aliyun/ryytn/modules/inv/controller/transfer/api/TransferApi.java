package cn.aliyun.ryytn.modules.inv.controller.transfer.api;

/**
 * <AUTHOR>
 * @date 2025/5/20 14:28
 * @description：
 */
public interface TransferApi {
    interface CommonConfig {

        String ROOT = "/api/config-manage";

        String GET_CONFIG = "/getConfig/{module}";

        String SAVE = "/save";
    }

    interface TransferPlan {

        String ROOT = "/api/allocation-plan";

        String SAVE = "/saveMetaConfig";

        String LAYOUT = "/pageLayoutQuery";

        String LIST = "/list";

        String GET_PRODUCTION_CODING_LIST = "/getProductionCodingList";
        String GET_OUT_PHYSICAL_WAREHOUSE_NAME_LIST = "/getOutPhysicalWarehouseNameList";
        String GET_OUT_LOGICAL_WAREHOUSE_NAME_LIST = "/getOutLogicalWarehouseNameList";
        String GET_IN_LOGICAL_WAREHOUSE_NAME_LIST = "/getInLogicalWarehouseNameList";
        String GET_IN_PHYSICAL_WAREHOUSE_NAME_LIST = "/getInPhysicalWarehouseNameList";
        String GET_VALIDITY_RULE_LIST = "/getValidityRuleList";
        String GET_MODE_OF_TRANSPORT_LIST = "/getModeOfTransportList";
        String GET_ALARM_STATUS_LIST = "/getAlarmStatusList";
    }

}
