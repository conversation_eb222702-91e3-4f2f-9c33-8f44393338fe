// This file is auto-generated, don't edit it. Thanks.
package cn.aliyun.ryytn.modules.inv.service.task.core.demo;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dataworks_public20200518.AsyncClient;
import com.aliyun.sdk.service.dataworks_public20200518.models.RunCycleDagNodesRequest;
import com.aliyun.sdk.service.dataworks_public20200518.models.RunCycleDagNodesResponse;
import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import com.google.gson.Gson;
import darabonba.core.client.ClientOverrideConfiguration;

import java.time.LocalDateTime;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;

public class RunCycleDagNodes {
    public static String AK = "LTAI5tMM5FpZAxJnzGYoWYVw";
    public static String SK = "******************************";
    public static String REGION = "cn-beijing";
    public static String ENV = "PROD";
    public static String POINT = String.format("dataworks.%s.aliyuncs.com", RunCycleDagNodes.REGION);

    public static void main(String[] args) throws Exception {

        // HttpClient Configuration
        /*HttpClient httpClient = new ApacheAsyncHttpClientBuilder()
                .connectionTimeout(Duration.ofSeconds(10)) // Set the connection timeout time, the default is 10 seconds
                .responseTimeout(Duration.ofSeconds(10)) // Set the response timeout time, the default is 20 seconds
                .maxConnections(128) // Set the connection pool size
                .maxIdleTimeOut(Duration.ofSeconds(50)) // Set the connection pool timeout, the default is 30 seconds
                // Configure the proxy
                .proxy(new ProxyOptions(ProxyOptions.Type.HTTP, new InetSocketAddress("<your-proxy-hostname>", 9001))
                        .setCredentials("<your-proxy-username>", "<your-proxy-password>"))
                // If it is an https connection, you need to configure the certificate, or ignore the certificate(.ignoreSSL(true))
                .x509TrustManagers(new X509TrustManager[]{})
                .keyManagers(new KeyManager[]{})
                .ignoreSSL(false)
                .build();*/

        // Configure Credentials authentication information, including ak, secret, token
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                // Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
                .accessKeyId(AK)
                .accessKeySecret(SK)
                //.securityToken(System.getenv("ALIBABA_CLOUD_SECURITY_TOKEN")) // use STS token
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region(REGION) // Region ID
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/dataworks-public
                                .setEndpointOverride(POINT)
                        //.setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();
        TimeZone timeZone = TimeZone.getTimeZone("Asia/Shanghai");
        LocalDateTime now = LocalDateTime.now(timeZone.toZoneId());
        now = DateUtil.plusDays(now, -1);
        // Parameter settings for API request
        RunCycleDagNodesRequest runCycleDagNodesRequest = RunCycleDagNodesRequest.builder()
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .projectEnv(ENV)
                .startBizDate(DateUtil.localDateTimeToString(now, "yyyy-MM-dd 00:00:00"))
                //.bizBeginTime(DateUtil.localDateTimeToString(now, "HH:00:00"))
                //.bizEndTime(DateUtil.localDateTimeToString(now, "HH:59:59"))
                .endBizDate(DateUtil.localDateTimeToString(now, "yyyy-MM-dd 00:00:00"))
                .name("scp_demand_mt_wh_fcst_list测试")
                .rootNodeId(210001928904L)
                .parallelism(false)
                .includeNodeIds("210001928904,210001928926")
                .build();
        CompletableFuture<RunCycleDagNodesResponse> response = client.runCycleDagNodes(runCycleDagNodesRequest);
        RunCycleDagNodesResponse resp = response.get();
        System.out.println(new Gson().toJson(resp));

        client.close();
    }

}