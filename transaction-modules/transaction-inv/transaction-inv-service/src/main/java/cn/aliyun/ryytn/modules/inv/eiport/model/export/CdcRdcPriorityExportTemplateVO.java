package cn.aliyun.ryytn.modules.inv.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/24 20:55
 * @description：
 */
@Data
public class CdcRdcPriorityExportTemplateVO {

    /**
     * 工厂仓编码
     */
    @ExcelField(value = "工厂仓编码")
    private String cdcCode;
    /**
     * 工厂仓名称
     */
    @ExcelField(value = "工厂仓名称")
    private String cdcName;

    /**
     * cdc供应优先级
     */
    @ExcelField(value = "工厂仓供应优先级")
    private Integer cdcSupplyPriority;

    /**
     * rdc编码
     */
    @ExcelField(value = "RDC编码")
    private String rdcCode;

    /**
     * rdc名称
     */
    @ExcelField(value = "RDC名称")
    private String rdcName;

    /**
     * rdc缺货优先级
     */
    @ExcelField(value = "RDC缺货优先级")
    private Integer rdcOosPriority;

    @ExcelField(value = "操作人")
    private String operatorName;

    @ExcelField(value = "最近更新时间")
    private String gmtModified;
}
