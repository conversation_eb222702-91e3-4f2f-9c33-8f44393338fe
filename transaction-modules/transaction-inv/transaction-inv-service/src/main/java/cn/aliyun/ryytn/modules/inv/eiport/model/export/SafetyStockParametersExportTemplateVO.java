package cn.aliyun.ryytn.modules.inv.eiport.model.export;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeField;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/12 16:55
 * @description：
 */
@Data
public class SafetyStockParametersExportTemplateVO {
    
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * sku编码
     */
    @ExcelField(value = "SKU编码")
    private String skuCode;

    /**
     * sku名称
     */
    @ExcelField(value = "SKU简称")
    private String skuName;

    /**
     * 产品分类编码
     */
    private String lv1CategoryCode;

    /**
     * 产品分类名称
     */
    @ExcelField(value = "品类")
    private String categoryName;
//
//    /**
//     * 产品大类编码
//     */
//    private String lv2CategoryCode;
//
//    /**
//     * 产品大类名称
//     */
//    @ExcelField(value = "二级品类")
//    private String lv2CategoryName;
//
//    /**
//     * 产品小类编码
//     */
//    private String lv4CategoryCode;
//
//    /**
//     * 产品小类名称
//     */
//    @ExcelField(value = "四级品类")
//    private String lv4CategoryName;

    /**
     * ABC分类
     */
    private String abcType;

    /**
     * ABC分类名称
     */
    @ExcelField(value = "ABC分类")
    private String abcTypeName;

    /**
     * rdc仓编码
     */
    private String rdcCode;

    /**
     * rdc仓名称
     */
    @ExcelField(value = "仓库")
    private String rdcName;


    /**
     * 服务水平
     */
    @ExcelField(value = "服务水平")
    private String serviceRatio;

    /**
     * 最小安全库存天数
     */
    @ExcelField(value = "最小安全库存天数")
    private Integer minSafetyDays;

    /**
     * 最大安全库存天数
     */
    @ExcelField(value = "最大安全库存天数")
    private Integer maxSafetyDays;

    /**
     * 操作人编码
     */
    private String operatorCode;

    /**
     * 操作人名称
     */
    @ExcelField(value = "操作人")
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 修改时间
     */
    @ExcelField(value = "最近更新时间")
    private String gmtModified;


}
