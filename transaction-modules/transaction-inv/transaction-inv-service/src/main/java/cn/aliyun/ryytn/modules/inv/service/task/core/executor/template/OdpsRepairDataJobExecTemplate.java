package cn.aliyun.ryytn.modules.inv.service.task.core.executor.template;


import cn.aliyun.ryytn.modules.inv.api.task.core.TaskProperties;
import cn.aliyun.ryytn.modules.inv.api.task.core.executor.template.JobExecTemplate;
import cn.aliyun.ryytn.modules.inv.entity.task.entity.JobExecContext;
import cn.aliyun.ryytn.modules.inv.api.task.core.DateExprResolver;
import cn.aliyun.ryytn.modules.inv.entity.task.entity.ExecuteResult;
import cn.aliyun.ryytn.modules.inv.common.utils.SafeGetter;
import cn.aliyun.ryytn.modules.inv.common.utils.StringConstants;
import cn.aliyun.ryytn.modules.inv.entity.task.entity.Constants;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.TaskManagementDTO;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskCfgDO;
import cn.aliyun.ryytn.modules.inv.service.task.core.demo.RunCycleDagNodes;
import cn.aliyun.ryytn.modules.inv.service.task.core.executor.StateModifier;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dataworks_public20200518.AsyncClient;
import com.aliyun.sdk.service.dataworks_public20200518.models.*;
import com.cainiao.cntech.dsct.scp.gei.common.enums.BooleanEnum;
import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.StreamUtils;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-19 11:02
 * @description 作业执行模版
 */
@Slf4j
@Component("odpsRepairDataJobExecutor")
public class OdpsRepairDataJobExecTemplate implements JobExecTemplate {
    private final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    @Resource
    private TaskProperties taskProperties;

    /**
     * {
     * "headers": {
     * "Keep-Alive":"timeout\u003d25"
     * ,"Access-Control-Expose-Headers":"*"
     * ,"Access-Control-Allow-Origin":"*"
     * ,"ETag":"1u+zcDXojJU9LREbgy0eleQ3"
     * ,"x-acs-request-id":"6DEEBE4F-5533-5407-A417-63F60CB49E55"
     * ,"Connection":"keep-alive"
     * ,"Content-Length":"143"
     * ,"Date":"Fri, 22 Nov 2024 07:55:43 GMT"
     * ,"Content-Type":"application/json;charset\u003dutf-8"
     * ,"x-acs-trace-id":"bd5d584cda4dbdef55ff9b9f8924505c"
     * }
     * ,"statusCode":200
     * ,"body":{
     * "data":[210233646041]
     * ,"errorCode": ""
     * ,"errorMessage": ""
     * ,"httpStatusCode":200
     * ,"requestId":"6DEEBE4F-5533-5407-A417-63F60CB49E55"
     * ,"success":true
     * }
     * }
     */
    @Override
    public ExecuteResult submit(JobExecContext jobExecContext) {
        if (Objects.isNull(jobExecContext) || Objects.isNull(jobExecContext.getTaskId())
                || Objects.isNull(jobExecContext.getBatchNo()) || CollectionUtils.isEmpty(jobExecContext.getTaskCfgList())) {
            return ExecuteResult.failed("非法的作业执行上下文: " + JsonUtils.toStr(jobExecContext));
        }
        List<ScpTaskCfgDO> taskCfgList = jobExecContext.getTaskCfgList();
        List<ScpTaskCfgDO> rootNodeFiltered = StreamUtils.filter(taskCfgList, item -> BooleanEnum.TRUE.isThis(item.getRootOdps()));
        ScpTaskCfgDO rootCfg;
        if (CollectionUtils.isEmpty(rootNodeFiltered) || Objects.isNull(rootCfg = rootNodeFiltered.get(0))) {
            return ExecuteResult.failed("ODPS根任务编码不存在: " + JsonUtils.toStr(taskCfgList));
        }
        AsyncClient client = createClient();
        RunCycleDagNodesRequest request = createRunCycleDagNodesRequest(rootCfg, taskCfgList, jobExecContext);
        CompletableFuture<RunCycleDagNodesResponse> response = client.runCycleDagNodes(request);
        try {
            RunCycleDagNodesResponse resp = response.join();
            RunCycleDagNodesResponseBody respBody = resp.getBody();
            if (Boolean.TRUE.equals(respBody.getSuccess())) {
                return ExecuteResult.success(respBody.getData());
            } else {
                return ExecuteResult.failed(respBody.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("OdpsRepairDataJobExecTemplate is error: ", e);
            return ExecuteResult.failed(StateModifier.buildErrorMsg(e));
        } finally {
            client.close();
        }
    }

    @Override
    public ExecuteResult confirmJobState(Long dagId) {
        AsyncClient client = createClient();
        GetDagRequest request = createGetDagRequest(dagId);
        CompletableFuture<GetDagResponse> response = client.getDag(request);
        GetDagResponse resp;
        try {
            resp = response.join();
            GetDagResponseBody respBody = resp.getBody();
            return ExecuteResult.of(respBody);
        } finally {
            client.close();
        }
    }

    private StaticCredentialProvider createCredentialProvider() {
        return StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(taskProperties.getAccessKey())
                .accessKeySecret(taskProperties.getSecretKey())
                .build());
    }

    private AsyncClient createClient() {
        return AsyncClient.builder()
                .region(taskProperties.getRegion()) // Region ID
                .credentialsProvider(createCredentialProvider())
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride(String.format("dataworks.%s.aliyuncs.com", taskProperties.getRegion()))
                ).build();
    }

    private RunCycleDagNodesRequest createRunCycleDagNodesRequest(ScpTaskCfgDO rootTaskCfg, List<ScpTaskCfgDO> taskCfgList, JobExecContext jobExecContext) {
        // 如果配置列表存在与根节点不一样的时间表达式，则过滤掉根任务
        boolean isFilteredRoot = false;
        for (ScpTaskCfgDO scpTaskCfgDO : taskCfgList) {
            if (!StringUtils.equals(scpTaskCfgDO.getDateExpr(), rootTaskCfg.getDateExpr())) {
                isFilteredRoot = true;
                break;
            }
        }
        if (isFilteredRoot) {
            taskCfgList = StreamUtils.filter(taskCfgList, item -> !BooleanEnum.TRUE.isThis(item.getRootOdps()));
        }
        List<Long> odpsCodes = StreamUtils.mapNotDistinct(taskCfgList, ScpTaskCfgDO::getOdpsCode);
        String includeNodeIds = StringUtils.join(odpsCodes, StringConstants.C_COMMA);
        TaskManagementDTO baseTaskInfo = jobExecContext.getBaseTaskInfo();
        String currentDateExpr = taskCfgList.get(0).getDateExpr();
        LocalDateTime now = DateExprResolver.resolve(currentDateExpr);
        final String jobName;
        if (Objects.isNull(baseTaskInfo)) {
            jobName = String.format("OdpsRepairDataJobTemplate补数据任务_%s_%s_%s(%s)_%s", jobExecContext.getBatchNo()
                    , jobExecContext.getTaskId(), rootTaskCfg.getTaskTypeName(), SafeGetter.get(currentDateExpr, Constants.DEFAULT_DATE_EXPR), getTime());
        } else {
            jobName = String.format("「任务管理-%s」_%s(%s)_%s", baseTaskInfo.getTaskName(), rootTaskCfg.getTaskTypeName(), SafeGetter.get(currentDateExpr, Constants.DEFAULT_DATE_EXPR), getTime());
        }
        return RunCycleDagNodesRequest.builder()
                .projectEnv(taskProperties.getEnv())
                .startBizDate(DateUtil.localDateTimeToString(now, "yyyy-MM-dd 00:00:00"))
                //.bizBeginTime(DateUtil.localDateTimeToString(now, "HH:00:00"))
                //.bizEndTime(DateUtil.localDateTimeToString(now, "HH:59:59"))
                .endBizDate(DateUtil.localDateTimeToString(now, "yyyy-MM-dd 00:00:00"))
                .name(jobName)
                .rootNodeId(rootTaskCfg.getOdpsCode())
                .parallelism(false)
                .includeNodeIds(includeNodeIds)
                .build();
    }

    private GetDagRequest createGetDagRequest(Long dagId) {
        return GetDagRequest.builder()
                .projectEnv(RunCycleDagNodes.ENV)
                .dagId(dagId)
                .build();
    }

    private String getTime() {
        // 格式化当前日期和时间
        return LocalDateTime.now().format(DATE_FORMATTER);
    }
}
