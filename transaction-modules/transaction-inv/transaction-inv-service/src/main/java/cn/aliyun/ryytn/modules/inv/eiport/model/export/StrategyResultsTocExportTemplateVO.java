package cn.aliyun.ryytn.modules.inv.eiport.model.export;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/24 20:55
 * @description：
 */
@Data
public class StrategyResultsTocExportTemplateVO {
    
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * sku编码
     */
    @ExcelField(value = "SKU编码")
    private String skuCode;

    /**
     * sku名称
     */
    @ExcelField(value = "SKU简称")
    private String skuName;

    /**
     * 产品分类编码
     */
    private String lv1CategoryCode;

    /**
     * 产品分类名称
     */
    @ExcelField(value = "品类")
    private String categoryName;
//
//    /**
//     * 产品大类编码
//     */
//    private String lv2CategoryCode;
//
//    /**
//     * 产品大类名称
//     */
//    @ExcelField(value = "二级品类")
//    private String lv2CategoryName;
//
//    /**
//     * 产品小类编码
//     */
//    private String lv4CategoryCode;
//
//    /**
//     * 产品小类名称
//     */
//    @ExcelField(value = "四级品类")
//    private String lv4CategoryName;

    /**
     * ABC分类
     */
    private String abcType;

    /**
     * ABC分类名称
     */
    @ExcelField(value = "ABC分类")
    private String abcTypeName;

    /**
     * rdc仓编码
     */
    private String rdcCode;

    /**
     * rdc仓名称
     */
    @ExcelField(value = "仓库")
    private String rdcName;


    /**
     * 服务水平
     */
    @ExcelField(value = "服务水平")
    private String serviceRatioStr;

    /**
     * 最小安全库存天数
     */
    @ExcelField(value = "最小安全库存天数")
    private Integer minSafetyDays;

    /**
     * 最大安全库存天数
     */
    @ExcelField(value = "最大安全库存天数")
    private Integer maxSafetyDays;

    /**
     * 日均销
     */
    @ExcelField(value = "日均销")
    private Integer dasNum;

    /**
     * 提前期天数
     */
    @ExcelField(value = "提前期天数")
    private Integer leadTimeDay;

    /**
     * 提前期库存
     */
    @ExcelField(value = "提前期库存")
    private Integer leadTimeCnt;

    /**
     * 算法建议安全库存天
     */
    @ExcelField(value = "安全库存天数（算法建议）")
    private Integer sugSafetyDays;

    /**
     * 算法建议安全库存件
     */
    @ExcelField(value = "安全库存件数（算法建议）")
    private Integer sugSafetyQty;

    /**
     * 人工调整安全库存天
     */
    @ExcelField(value = "安全库存天数（人工调整）")
    private Integer manualSafetyDays;

    /**
     * 人工调整安全库存件
     */
    @ExcelField(value = "安全库存件数（人工调整）")
    private Integer manualSafetyQty;

    /**
     * 分销优化安全库存天
     */
    @ExcelField(value = "安全库存天数（分销优化）")
    private Integer distSafetyDays;

    /**
     * 分销优化安全库存件
     */
    @ExcelField(value = "安全库存件数（分销优化）")
    private Integer distSafetyQty;

    /**
     * 算法建议目标库存天
     */
    @ExcelField(value = "目标库存天数（算法建议）")
    private Integer sugTargetDays;

    /**
     * 算法建议目标库存件
     */
    @ExcelField(value = "目标库存件数（算法建议）")
    private Integer sugTargetQty;

    /**
     * 人工调整目标库存天
     */
    @ExcelField(value = "目标库存天数（人工调整）")
    private Integer manualTargetDays;

    /**
     * 人工调整目标库存件
     */
    @ExcelField(value = "目标库存件数（人工调整）")
    private Integer manualTargetQty;

    /**
     * 分销优化目标库存天
     */
    @ExcelField(value = "目标库存天数（分销优化）")
    private Integer distTargetDays;

    /**
     * 分销优化目标库存件
     */
    @ExcelField(value = "目标库存件数（分销优化）")
    private Integer distTargetQty;

    /**
     * 库存策略Id
     */
    private Long invStrategyId;

    /**
     * 库存策略场景名称
     */
    @ExcelField(value = "关联策略场景")
    private String invStrategyName;

    /**
     * x1天
     */
    @ExcelField(value = "X1天数")
    private Integer paramX1Days;

    /**
     * x1数量
     */
    @ExcelField(value = "X1件数")
    private Integer paramX1Qty;

    /**
     * x2天
     */
    @ExcelField(value = "X2天数")
    private Integer paramX2Days;

    /**
     * x2数量
     */
    @ExcelField(value = "X2件数")
    private Integer paramX2Qty;

    /**
     * 安全库存天
     */
    private Integer paramSafetyDays;

    /**
     * 安全库存数量
     */
    private Integer paramSafetyQty;

    /**
     * 目标库存天
     */
    private Integer paramTargetDays;

    /**
     * 目标库存数量
     */
    private Integer paramTargetQty;

    /**
     * 异常状态
     */
    @ExcelField(value = "异常状态")
    private String errStatus;

    /**
     * 操作人编码
     */
    private String operatorCode;

    /**
     * 操作人名称
     */
    @ExcelField(value = "操作人")
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 修改时间
     */
    @ExcelField(value = "最近更新时间")
    private String gmtModified;


}
