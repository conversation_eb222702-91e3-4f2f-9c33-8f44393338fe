package cn.aliyun.ryytn.modules.inv.eiport;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.inv.api.strategy.InvStrategyCenterService;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.utils.*;
import cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants;
import cn.aliyun.ryytn.modules.inv.constant.strategy.enums.*;
import cn.aliyun.ryytn.modules.inv.constant.strategy.error.InvStrategyCenterError;
import cn.aliyun.ryytn.modules.inv.eiport.model.StrategyParametersTobImportTemplateVO;
import cn.aliyun.ryytn.modules.inv.eiport.model.StrategyParametersTocImportTemplateVO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyParametersTocDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvParametersRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvUpsertStrategyRequest;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyParametersTocDao;
import cn.hutool.core.lang.TypeReference;
import cn.aliyun.ryytn.modules.inv.common.exception.Assert;
import cn.aliyun.ryytn.modules.inv.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@ImportService(value = "strategyParametersTocEIportDataTemplate", filename = "库存策略参数导入模版", templateDesc = "填写规范：\n" +
        "1.配置方式：填写按天数或按数量；如：按天数\n" +
        "2.X1：填写0～99的整数；如：2\n" +
        "3.X2：填写0～99的整数，数值必须>=X1；如：2\n" +
        "4.安全库存：填写>=0的整数；如：2\n" +
        "5.目标库存：填写>=0的整数；如：2", async = false, sliceSize = 10000)
@ExportService(value = "strategyParametersTocEIportDataTemplate", filename = "库存策略参数数据导出", async = false)
public class StrategyParametersTocEIportDataTemplate extends EIPortDataTemplate<StrategyParametersTocImportTemplateVO, InvUpsertStrategyRequest> implements ImportPostProcessor<StrategyParametersTocImportTemplateVO> {
    @Autowired
    private InvStrategyCenterService invStrategyCenterService;

    @Autowired
    private InvStrategyParametersTocDao invStrategyParametersTocDao;

    @Override
    public List<StrategyParametersTocImportTemplateVO> getImportTemplateData(ImportQueryRequest request) {
        Map<String, Object> params = request.getParams();
        if (Objects.isNull(params)) {
            return super.getImportTemplateData(request);
        }
        if (Objects.isNull(params)) {
            return super.getImportTemplateData(request);
        }
        Object dimension = params.get(StrategyConstants.StrategyCenter.DIMENSION);
        if (Objects.isNull(dimension)) {
            return super.getImportTemplateData(request);
        }
        if (!InvDimensionEnum.codeValues().contains(String.valueOf(dimension))) {
            return super.getImportTemplateData(request);
        }
        if (Objects.isNull(params.get(StrategyConstants.StrategyCenter.TOKEN_ID)) && Objects.isNull(params.get(StrategyConstants.StrategyCenter.TOKEN))) {
            return super.getImportTemplateData(request);
        }
        Long strategyId = null;
        if (Objects.nonNull(params.get(StrategyConstants.StrategyCenter.TOKEN_ID))) {
            strategyId = Long.valueOf(String.valueOf(params.get(StrategyConstants.StrategyCenter.TOKEN_ID)));
        } else if (Objects.nonNull(params.get(StrategyConstants.StrategyCenter.TOKEN))) {
            Map<String, Object> map = JwtUtils.parse(String.valueOf(params.get(StrategyConstants.StrategyCenter.TOKEN)), new TypeReference<Map<String, Object>>() {
            });
            if (map != null) {
                Object id = map.get(StrategyConstants.StrategyCenter.TOKEN_ID);
                Assert.isTrue(Objects.nonNull(id), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
                strategyId = Long.valueOf(String.valueOf(id));
            }
        }
        if (Objects.isNull(strategyId)) {
            return super.getImportTemplateData(request);
        }
        InvUpsertStrategyRequest invUpsertStrategyRequest = JsonUtils.toRequestObject(request.getParams(), InvUpsertStrategyRequest.class);
        invUpsertStrategyRequest.setPaging(false);
        invUpsertStrategyRequest.setDimension(String.valueOf(dimension));
        invUpsertStrategyRequest.setReplnType(ReplnTypeEnum.TOC.getCode());
        invUpsertStrategyRequest.setId(strategyId);
        PageMetaInfoWrapper pageMetaInfoWrapper = invStrategyCenterService.selectParameters(invUpsertStrategyRequest);
        if (Objects.isNull(pageMetaInfoWrapper) || CollectionUtils.isEmpty(pageMetaInfoWrapper.getList())) {
            return super.getImportTemplateData(request);
        }
        List<StrategyParametersTocImportTemplateVO> convert = GeiCommonConvert.convert(pageMetaInfoWrapper.getList(), StrategyParametersTocImportTemplateVO.class);
        for (StrategyParametersTocImportTemplateVO importTemplateVO : convert) {
            importTemplateVO.setX1Str(Objects.nonNull(importTemplateVO.getX1()) ? String.valueOf(importTemplateVO.getX1()) : null);
            importTemplateVO.setX2Str(Objects.nonNull(importTemplateVO.getX2()) ? String.valueOf(importTemplateVO.getX2()) : null);
            importTemplateVO.setTargetStr(Objects.nonNull(importTemplateVO.getTarget()) ? String.valueOf(importTemplateVO.getTarget()) : null);
            importTemplateVO.setSafetyStr(Objects.nonNull(importTemplateVO.getSafety()) ? String.valueOf(importTemplateVO.getSafety()) : null);
        }
        return convert;
    }

    @Override
    public ExcelMetaInfoWrapper getExportWrapper(InvUpsertStrategyRequest request) {
        request.setPaging(false);
        request.setImportFlag("1");
        PageMetaInfoWrapper pageMetaInfoWrapper = invStrategyCenterService.selectParameters(request);
        return GeiCommonConvert.convert(pageMetaInfoWrapper, ExcelMetaInfoWrapper.class);
    }

//    @Override
//    public List<ExcelMetaInfo> getImportTemplateMetaInfo(ImportQueryRequest request) {
//        List<ExcelMetaInfo> result = ExcelMetaInfoCreator.create(StrategyParametersTocImportTemplateVO.class, request.getDimension());
//        return result;
//    }

    @Override
    public boolean preValid(ImportDataWrapper<StrategyParametersTocImportTemplateVO> importDataWrapper) {
//        List<StrategyParametersTocImportTemplateVO> importData = importDataWrapper.getData();
//        Map<String, Object> params = importDataWrapper.getParams();
        if (!validateParams(importDataWrapper)) {
            return false;
        }
        return validateData(importDataWrapper);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<StrategyParametersTocImportTemplateVO> importDataWrapper) {
        List<InvParametersRequest> convert = GeiCommonConvert.convert(importDataWrapper.getData(), InvParametersRequest.class);
        invStrategyParametersTocDao.batchUpdate(convert);
        return null;
    }

    private boolean validateParams(ImportDataWrapper<StrategyParametersTocImportTemplateVO> importDataWrapper) {
        List<StrategyParametersTocImportTemplateVO> importData = importDataWrapper.getData();
        Map<String, Object> params = importDataWrapper.getParams();
        if (CollectionUtils.isEmpty(importData)) {
            return false;
        }
        if (Objects.isNull(params)) {
            return setErrMsg(importData, InvStrategyCenterError.PARAM_IS_NOT_NULL.getError());
        }
        Object dimension = params.get(StrategyConstants.StrategyCenter.DIMENSION);
        if (Objects.isNull(dimension)) {
            return setErrMsg(importData, InvStrategyCenterError.DIMENSION_IS_NOT_NULL.getError());
        }
        if (!InvDimensionEnum.codeValues().contains(String.valueOf(dimension))) {
            return setErrMsg(importData, InvStrategyCenterError.DIMENSION_IS_NOT_EXISTENT.getError());
        }
        Object replnType = params.get(StrategyConstants.StrategyCenter.REPLN_TYPE);
        if (Objects.isNull(replnType)) {
            return setErrMsg(importData, InvStrategyCenterError.REPLN_TYPE_IS_NOT_NULL.getError());
        }
        if (!Objects.equals(ReplnTypeEnum.TOC.getCode(), String.valueOf(replnType))) {
            return setErrMsg(importData, InvStrategyCenterError.REPLN_TYPE_IS_NOT_EXISTENT.getError());
        }
        if (Objects.isNull(params.get(StrategyConstants.StrategyCenter.TOKEN_ID)) && Objects.isNull(params.get(StrategyConstants.StrategyCenter.TOKEN))) {
            return setErrMsg(importData, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
        }
        Long strategyId = null;
        if (Objects.nonNull(params.get(StrategyConstants.StrategyCenter.TOKEN_ID))) {
            strategyId = Long.valueOf(String.valueOf(params.get(StrategyConstants.StrategyCenter.TOKEN_ID)));
        } else if (Objects.nonNull(params.get(StrategyConstants.StrategyCenter.TOKEN))) {
            Map<String, Object> map = JwtUtils.parse(String.valueOf(params.get(StrategyConstants.StrategyCenter.TOKEN)), new TypeReference<Map<String, Object>>() {
            });
            if (map != null) {
                Object id = map.get(StrategyConstants.StrategyCenter.TOKEN_ID);
                Assert.isTrue(Objects.nonNull(id), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
                strategyId = Long.valueOf(String.valueOf(id));
            }
        }
        if (Objects.isNull(strategyId)) {
            return setErrMsg(importData, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
        }
        for (StrategyParametersTocImportTemplateVO datum : importData) {
            datum.setInvStrategyId(strategyId);
        }
        return true;
    }

    private boolean setErrMsg(List<StrategyParametersTocImportTemplateVO> importData, String errorMsg) {
        for (StrategyParametersTocImportTemplateVO datum : importData) {
            datum.setErrorMsg(errorMsg);
        }
        return false;
    }

    private boolean validateData(ImportDataWrapper<StrategyParametersTocImportTemplateVO> importDataWrapper) {
        List<StrategyParametersTocImportTemplateVO> importData = importDataWrapper.getData();
        Map<String, Object> params = importDataWrapper.getParams();
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        boolean flag = true;
        String dimension = String.valueOf(params.get(StrategyConstants.StrategyCenter.DIMENSION));
        Long invStrategyId = importData.get(0).getInvStrategyId();
        InvUpsertStrategyRequest invUpsertStrategyRequest = new InvUpsertStrategyRequest();
        invUpsertStrategyRequest.setPaging(false);
        invUpsertStrategyRequest.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
        invUpsertStrategyRequest.setStrategyId(invStrategyId);
        List<InvStrategyParametersTocDO> invStrategyParametersTocDOS = invStrategyParametersTocDao.selectByInvStrategyId(JsonUtils.toMap(invUpsertStrategyRequest));
        if (CollectionUtils.isEmpty(invStrategyParametersTocDOS)) {
            setErrMsg(importData, InvStrategyCenterError.STRATEGY_PARAMETERS_IS_NULL.getError());
            return false;
        }
        Map<String, InvStrategyParametersTocDO> parametersTocDOMap = StreamUtils.singleGroup(invStrategyParametersTocDOS, t -> getKey(t));
        StringBuilder stringBuilder = new StringBuilder();
        for (StrategyParametersTocImportTemplateVO importDatum : importData) {
            stringBuilder.delete(0, stringBuilder.length());
            //校验字段非空
            validateNotNull(dimension, importDatum, stringBuilder);
            if (stringBuilder.length() > 0) {
                flag = false;
                importDatum.setErrorMsg(stringBuilder.toString());
                continue;
            }
            String key = getKey(importDatum);
            InvStrategyParametersTocDO invStrategyParametersTocDO = parametersTocDOMap.get(key);
            if (Objects.isNull(invStrategyParametersTocDO)) {
                flag = false;
                importDatum.setErrorMsg(String.format(InvStrategyCenterError.PARAMETERS_NOT_FOUND.getError(),
                        key.replaceAll(StringConstants.DASHED, "").replaceAll(StringConstants.UNDERLINE, "")));
                continue;
            }
            //校验数值格式与范围
            validateParamData(importDatum, stringBuilder);
            if (stringBuilder.length() > 0) {
                flag = false;
                importDatum.setErrorMsg(stringBuilder.toString());
                continue;
            }
            if (StringUtils.isNotBlank(importDatum.getCollocationMethodName())) {
                importDatum.setCollocationMethod(InvCollocationMethodEnum.getCodeByName(importDatum.getCollocationMethodName()));
            }
            importDatum.setId(invStrategyParametersTocDO.getId());
            importDatum.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
            if (Objects.isNull(currentAccount)) {
                continue;
            }
            importDatum.setOperatorCode(currentAccount.getId());
            importDatum.setOperatorName(currentAccount.getName());
        }
        return flag;
    }

    private void validateNotNull(String dimension, StrategyParametersTocImportTemplateVO importDatum, StringBuilder stringBuilder) {
        if (Objects.equals(InvDimensionEnum.BY_SKU.getCode(), dimension) && StringUtils.isBlank(importDatum.getSkuCode())) {
            stringBuilder.append(InvStrategyCenterError.SKU_CODE_IS_NOT_NULL.getError());
        }
        if (Objects.equals(InvDimensionEnum.BY_FOUR.getCode(), dimension) && StringUtils.isBlank(importDatum.getLv4CategoryName())) {
            stringBuilder.append(InvStrategyCenterError.LV4_IS_NOT_NULL.getError());
        }
        if (Objects.equals(InvDimensionEnum.BY_TWO.getCode(), dimension) && StringUtils.isBlank(importDatum.getLv2CategoryName())) {
            stringBuilder.append(InvStrategyCenterError.LV2_IS_NOT_NULL.getError());
        }
        if (Objects.equals(InvDimensionEnum.BY_ONE.getCode(), dimension) && StringUtils.isBlank(importDatum.getLv1CategoryName())) {
            stringBuilder.append(InvStrategyCenterError.LV1_IS_NOT_NULL.getError());
        }
        if (Objects.equals(InvDimensionEnum.BY_ABC.getCode(), dimension) && StringUtils.isBlank(importDatum.getAbcTypeName())) {
            stringBuilder.append(InvStrategyCenterError.ABC_IS_NOT_NULL.getError());
        }
        if (StringUtils.isBlank(importDatum.getRdcName())) {
            stringBuilder.append(InvStrategyCenterError.RDC_NAME_IS_NOT_NULL.getError());
        }
        if (StringUtils.isBlank(importDatum.getX1Str())) {
            stringBuilder.append(InvStrategyCenterError.X1_IS_NULL.getError());
        }
        if (StringUtils.isBlank(importDatum.getX2Str())) {
            stringBuilder.append(InvStrategyCenterError.X2_IS_NULL.getError());
        }
    }

    private void validateParamData(StrategyParametersTocImportTemplateVO importDatum, StringBuilder stringBuilder) {
        Integer x1 = NumUtils.safeInt(importDatum.getX1Str());
        Integer x2 = NumUtils.safeInt(importDatum.getX2Str());
        if (StringUtils.isNotBlank(importDatum.getSafetyStr())) {
            Integer safety = NumUtils.safeInt(importDatum.getSafetyStr());
            if (Objects.isNull(safety)) {
                stringBuilder.append(InvStrategyCenterError.SAFETY_TYPE_ERROR.getError());
            } else {
                if (safety < 0) {
                    stringBuilder.append(InvStrategyCenterError.SAFETY_RANGE_ERROR.getError());
                }
                importDatum.setSafety(safety);
            }
        }
        if (StringUtils.isNotBlank(importDatum.getTargetStr())) {
            Integer target = NumUtils.safeInt(importDatum.getTargetStr());
            if (Objects.isNull(target)) {
                stringBuilder.append(InvStrategyCenterError.TARGET_TYPE_ERROR.getError());
            } else {
                if (target < 0) {
                    stringBuilder.append(InvStrategyCenterError.TARGET_RANGE_ERROR.getError());
                }
                importDatum.setTarget(target);
                importDatum.setTargetToc(target);
            }
        }
        if (Objects.isNull(x1)) {
            stringBuilder.append(InvStrategyCenterError.X1_TYPE_ERROR.getError());
        }
        if (Objects.isNull(x2)) {
            stringBuilder.append(InvStrategyCenterError.X2_TYPE_ERROR.getError());
        }
        if (stringBuilder.length() > 0) {
            return;
        }
        if (x1 < 0 || x1 > 99) {
            stringBuilder.append(InvStrategyCenterError.X1_RANGE_ERROR.getError());
        }
        if (x2 < 0 || x2 > 99) {
            stringBuilder.append(InvStrategyCenterError.X2_RANGE_ERROR.getError());
        }
        if (x2 < x1) {
            stringBuilder.append(InvStrategyCenterError.X2_LESS_THEN_X1.getError());
        }
        if (stringBuilder.length() > 0) {
            return;
        }
        importDatum.setX1(x1);
        importDatum.setX2(x2);
    }

    private String getKey(InvStrategyParametersTocDO invStrategyParametersTocDO) {
        return String.format("%s_%s_%s_%s_%s_%s", getItem(invStrategyParametersTocDO.getAbcType()),
                getItem(invStrategyParametersTocDO.getLv1CategoryName()),
                getItem(invStrategyParametersTocDO.getLv2CategoryName()),
                getItem(invStrategyParametersTocDO.getLv4CategoryName()),
                getItem(invStrategyParametersTocDO.getSkuCode()),
                getItem(invStrategyParametersTocDO.getRdcName())
        );
    }

    private String getKey(StrategyParametersTocImportTemplateVO importTemplateVO) {
        return String.format("%s_%s_%s_%s_%s_%s", getItem(SkuLevelEnum.getCodeByName(importTemplateVO.getAbcTypeName())),
                getItem(importTemplateVO.getLv1CategoryName()),
                getItem(importTemplateVO.getLv2CategoryName()),
                getItem(importTemplateVO.getLv4CategoryName()),
                getItem(importTemplateVO.getSkuCode()),
                getItem(importTemplateVO.getRdcName())
        );
    }

    private String getItem(String item) {
        if (StringUtils.isBlank(item)) {
            return StringConstants.DASHED;
        }
        return item;
    }

}
