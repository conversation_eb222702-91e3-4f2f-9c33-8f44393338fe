package cn.aliyun.ryytn.modules.inv.eiport;

import cn.aliyun.ryytn.modules.inv.api.strategy.InvStrategyCenterService;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants;
import cn.aliyun.ryytn.modules.inv.eiport.converter.MergeFieldToStringConverter;
import cn.aliyun.ryytn.modules.inv.eiport.model.export.InvStrategyPriorityExportTemplateVO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvBatchUpdateStatusRequest;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.ExportDataTemplate;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@ExportService(value = "strategyStatusUpdateEIportDataTemplate", filename = "库存策略状态变更校验数据导出", async = false)
public class StrategyStatusUpdateEIportDataTemplate implements ExportDataTemplate<InvBatchUpdateStatusRequest> {
    @Autowired
    private InvStrategyCenterService invStrategyCenterService;

//    @Override
//    public ExcelMetaInfoWrapper getExportWrapper(InvBatchUpdateStatusRequest request) {
//        Map<String, Object>  object = invStrategyCenterService.batchUpdateStatus(request);
//        if (Objects.nonNull(object)) {
//            Object meta = object.get(StrategyConstants.StrategyCenter.META);
//            return GeiCommonConvert.convert(meta, ExcelMetaInfoWrapper.class).addWriteConverter(new MergeFieldToStringConverter());
//        }
//        return null;
//    }

    @Override
    public List<?> getExportTableData(InvBatchUpdateStatusRequest request) {
        Map<String, Object>  object = invStrategyCenterService.batchUpdateStatus(request);
        if (Objects.nonNull(object)) {
            Object meta = object.get(StrategyConstants.StrategyCenter.META);
            MetaInfoWrapper convert = GeiCommonConvert.convert(meta, MetaInfoWrapper.class);
            return convert.getList();
        }
        return null;
    }


    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(InvBatchUpdateStatusRequest request) {
        if (Objects.nonNull(request.getRepeatConfirm())) {
            return ExcelMetaInfoCreator.create(InvStrategyPriorityExportTemplateVO.class, StrategyConstants.StrategyCenter.PRIORITY);
        }
        if (Objects.isNull(request.getRepeatConfirm())) {
            return ExcelMetaInfoCreator.create(InvStrategyPriorityExportTemplateVO.class, StrategyConstants.StrategyCenter.DUPLICATION);
        }
        return null;
    }
}
