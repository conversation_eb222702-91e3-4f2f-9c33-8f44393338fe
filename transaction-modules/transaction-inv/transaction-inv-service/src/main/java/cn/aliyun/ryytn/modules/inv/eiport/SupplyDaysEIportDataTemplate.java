package cn.aliyun.ryytn.modules.inv.eiport;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.modules.inv.api.business.InvSupplyDaysService;
import cn.aliyun.ryytn.modules.inv.business.dao.InvSupplyDaysDao;
import cn.aliyun.ryytn.modules.inv.common.utils.JsonUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.NumUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StreamUtils;
import cn.aliyun.ryytn.modules.inv.constant.business.error.InvSupplyDaysError;
import cn.aliyun.ryytn.modules.inv.eiport.model.SupplyDaysImportTemplateVO;
import cn.aliyun.ryytn.modules.inv.eiport.model.export.InvSupplyDaysExportTemplateVO;
import cn.aliyun.ryytn.modules.inv.entity.business.dos.InvSupplyDaysDO;
import cn.aliyun.ryytn.modules.inv.entity.business.dto.InvSupplyDaysDTO;
import cn.aliyun.ryytn.modules.inv.entity.business.request.InvSupplyDaysPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.business.request.InvSupplyDaysUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.business.vo.InvSupplyDaysVO;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@ImportService(value = "supplyDaysEIportDataTemplate", filename = "供应天数配置导入模版", templateDesc = "填写规范：\n" +
        "1.读取生产天数：填写0～100的整数；如：20", async = false)
@ExportService(value = "supplyDaysEIportDataTemplate", filename = "供应天数配置数据导出", async = false)
public class SupplyDaysEIportDataTemplate extends EIPortDataTemplate<SupplyDaysImportTemplateVO, InvSupplyDaysPageQueryRequest> implements ImportPostProcessor<SupplyDaysImportTemplateVO> {
    @Autowired
    private InvSupplyDaysService invSupplyDaysService;
    @Autowired
    private InvSupplyDaysDao invSupplyDaysDao;

    @Override
    public List<SupplyDaysImportTemplateVO> getImportTemplateData(ImportQueryRequest request) {
        InvSupplyDaysPageQueryRequest pageQueryRequest = JsonUtils.toRequestObject(request.getParams(), InvSupplyDaysPageQueryRequest.class);
        pageQueryRequest.setPaging(false);
        List<InvSupplyDaysDTO> invSupplyDaysDTOS = invSupplyDaysService.queryPageData(pageQueryRequest);
        if (CollectionUtils.isEmpty(invSupplyDaysDTOS)) {
            return super.getImportTemplateData(request);
        }
        List<SupplyDaysImportTemplateVO> convert = GeiCommonConvert.convert(invSupplyDaysDTOS, SupplyDaysImportTemplateVO.class);
        for (SupplyDaysImportTemplateVO importTemplateVO : convert) {
            importTemplateVO.setSupplyDaysStr(Objects.nonNull(importTemplateVO.getSupplyDays()) ? importTemplateVO.getSupplyDays().toString() : null);
        }
        return convert;
    }

    @Override
    public boolean preValid(ImportDataWrapper<SupplyDaysImportTemplateVO> importDataWrapper) {
        List<SupplyDaysImportTemplateVO> importData = importDataWrapper.getData();
        return validate(importData);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<SupplyDaysImportTemplateVO> importDataWrapper) {
        List<SupplyDaysImportTemplateVO> importData = importDataWrapper.getData();
        List<InvSupplyDaysUpdateRequest> convert = GeiCommonConvert.convert(importData, InvSupplyDaysUpdateRequest.class);
        invSupplyDaysService.batchUpdate(convert, true);
        return null;
    }

    @Override
    public List<?> getExportTableData(InvSupplyDaysPageQueryRequest request) {
        request.setPaging(false);
        return GeiCommonConvert.convert(invSupplyDaysService.queryPageData(request), InvSupplyDaysVO.class);
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(InvSupplyDaysPageQueryRequest request) {
        return ExcelMetaInfoCreator.create(InvSupplyDaysExportTemplateVO.class);
    }

    private boolean validate(List<SupplyDaysImportTemplateVO> requests) {
        boolean flag = true;
        List<String> cdcCodeList = StreamUtils.map(requests, SupplyDaysImportTemplateVO::getCdcCode);
        if (CollectionUtils.isEmpty(cdcCodeList)) {
            for (SupplyDaysImportTemplateVO request : requests) {
                request.setErrorMsg(InvSupplyDaysError.CDC_CODE_IS_NOT_NULL.getError());
            }
            flag = false;
        }
        if (!flag) {
            return flag;
        }
        InvSupplyDaysPageQueryRequest queryRequest = new InvSupplyDaysPageQueryRequest();
        queryRequest.setPaging(false);
        queryRequest.setCdc(cdcCodeList);
        List<InvSupplyDaysDO> invSupplyDaysDOS = invSupplyDaysDao.selectByCondition(JsonUtils.toMap(queryRequest));
        if (CollectionUtils.isEmpty(invSupplyDaysDOS)) {
            for (SupplyDaysImportTemplateVO request : requests) {
                request.setErrorMsg(InvSupplyDaysError.CDC_CODE_QUERY_IS_NOT_NULL.getError());
            }
            flag = false;
        }
        if (!flag) {
            return flag;
        }
        Map<String, InvSupplyDaysDO> stringInvSupplyDaysDOMap = StreamUtils.singleGroup(invSupplyDaysDOS, InvSupplyDaysDO::getCdcCode);
        List<SupplyDaysImportTemplateVO> updateList = new ArrayList<>();
        boolean resultFlag = validateDate(requests, stringInvSupplyDaysDOMap, flag, updateList);
        if (resultFlag) {
            requests.clear();
            requests.addAll(updateList);
        }
        return resultFlag;
    }

    private boolean validateDate (List<SupplyDaysImportTemplateVO> requests, Map<String, InvSupplyDaysDO> stringInvSupplyDaysDOMap, boolean flag, List<SupplyDaysImportTemplateVO> updateList) {
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (SupplyDaysImportTemplateVO request : requests) {
            stringBuilder.delete(0, stringBuilder.length());
            if (StringUtils.isBlank(request.getCdcCode())) {
                stringBuilder.append(InvSupplyDaysError.CDC_CODE_IS_NOT_NULL.getError());
            }
            if (StringUtils.isBlank(request.getCdcName())) {
                stringBuilder.append(InvSupplyDaysError.CDC_NAME_IS_NOT_NULL.getError());
            }
            if (StringUtils.isBlank(request.getSupplyDaysStr())) {
                stringBuilder.append(InvSupplyDaysError.SUPPLY_DAYS_ERROR.getError());
            }
            Integer supplyDays = null;
            if (StringUtils.isNotBlank(request.getSupplyDaysStr())) {
                supplyDays = NumUtils.safeInt(request.getSupplyDaysStr());
                if (Objects.isNull(supplyDays)) {
                    stringBuilder.append(InvSupplyDaysError.SUPPLY_DAYS_IS_NUMBER.getError());
                }
            }
            if (stringBuilder.length() > 0) {
                flag = false;
                request.setErrorMsg(stringBuilder.toString());
                continue;
            }
            if (supplyDays < 0 || supplyDays > 100) {
                stringBuilder.append(InvSupplyDaysError.SUPPLY_DAYS_RANGE_ERROR.getError());
            }
            InvSupplyDaysDO invSupplyDaysDO = stringInvSupplyDaysDOMap.get(request.getCdcCode());
            if (Objects.isNull(invSupplyDaysDO)) {
                stringBuilder.append(InvSupplyDaysError.CDC_CODE_QUERY_IS_NOT_NULL.getError());
            }
            if (stringBuilder.length() > 0) {
                flag = false;
                request.setErrorMsg(stringBuilder.toString());
                continue;
            }
            if (Objects.equals(invSupplyDaysDO.getSupplyDays(), supplyDays)) {
                continue;
            }
            request.setCdcName(invSupplyDaysDO.getCdcName());
            request.setSupplyDays(supplyDays);
            request.setSupplyDaysBefore(invSupplyDaysDO.getSupplyDays());
            if (Objects.isNull(currentAccount)) {
                continue;
            }
            request.setOperatorCode(currentAccount.getId());
            request.setOperatorName(currentAccount.getName());
            updateList.add(request);
        }
        return flag;
    }
}
