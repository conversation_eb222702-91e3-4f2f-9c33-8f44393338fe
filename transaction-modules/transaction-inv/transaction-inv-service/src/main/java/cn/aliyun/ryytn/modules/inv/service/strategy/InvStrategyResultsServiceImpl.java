package cn.aliyun.ryytn.modules.inv.service.strategy;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.inv.api.strategy.InvStrategyResultsService;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.ConstantFactory;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.InjectConstant;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.service.ConstantDTO;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfoCreator;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfoHolder;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaType;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.entity.TagExtra;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeInfoHandler;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.common.utils.JsonUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.NumUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StreamUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StringConstants;
import cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants;
import cn.aliyun.ryytn.modules.inv.constant.strategy.enums.InvDimensionEnum;
import cn.aliyun.ryytn.modules.inv.constant.strategy.enums.ReplnTypeEnum;
import cn.aliyun.ryytn.modules.inv.constant.strategy.enums.SkuLevelEnum;
import cn.aliyun.ryytn.modules.inv.constant.strategy.error.InvStrategyResultsError;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyPriorityDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyResultsTobDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyResultsTocDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dto.InvStrategyResultsGatherDTO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvStrategyResulesUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvStrategyResultsPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.vo.InvStrategyPriorityVO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.vo.InvStrategyResultsTobVO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.vo.InvStrategyResultsTocVO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.vo.StrategyResultsExceptionVO;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyPriorityDao;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyResultsTobDao;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyResultsTocDao;
import cn.aliyun.ryytn.modules.inv.common.exception.Assert;
import cn.aliyun.ryytn.modules.inv.common.exception.ErrorCode;
import cn.aliyun.ryytn.udf.enums.InvResultsExceptionErrorEnum;
import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants.StrategyResults.*;

/**
 * <AUTHOR>
 * @date 2025/5/6 10:33
 * @description：
 */
@Service
public class InvStrategyResultsServiceImpl implements InvStrategyResultsService {

    @Autowired
    private InvStrategyResultsTocDao invStrategyResultsTocDao;

    @Autowired
    private InvStrategyResultsTobDao invStrategyResultsTobDao;

    @Autowired
    private InvStrategyResultsManager invStrategyResultsManager;

    @Autowired
    private InvStrategyPriorityDao invStrategyPriorityDao;


    @InjectConstant(type = StrategyConstants.StrategyResults.STRATEGY_RESULTS_QUERY)
    private ConstantFactory quickQueryFactory;

    @Override
    public List<OptionDTO> quickQueryClauseOption() {
        List<ConstantDTO> constantList = quickQueryFactory.get();
        return StreamUtils.map(constantList, item -> OptionDTO.of(item.getCode(), item.getName()));
    }

    private void paddingPageQueryRequest(InvStrategyResultsPageQueryRequest request) {
        if (Objects.isNull(request.getQuickQueryClauseValue())) {
            String quickQueryClause = request.getQuickQueryClause();
            ConstantDTO constant = quickQueryFactory.getConstant(quickQueryClause);
            if (Objects.nonNull(constant)) {
                request.setQuickQueryClauseValue(constant.getExtra());
            }
        }
    }

    @Override
    public List<InvStrategyResultsGatherDTO> queryGather(InvStrategyResultsPageQueryRequest request) {
        paddingPageQueryRequest(request);
        Long tocCount = invStrategyResultsTocDao.selectCount(JsonUtils.toMap(request));
        Long tobCount = invStrategyResultsTobDao.selectCount(JsonUtils.toMap(request));
        List<InvStrategyResultsGatherDTO> list = new ArrayList<>();
        list.add(new InvStrategyResultsGatherDTO(ReplnTypeEnum.TOC.getCode(),ReplnTypeEnum.TOC.getDesc(), NumUtils.setInt(tocCount)));
        list.add(new InvStrategyResultsGatherDTO(ReplnTypeEnum.TOB.getCode(),ReplnTypeEnum.TOB.getDesc(), NumUtils.setInt(tobCount)));
        return list;
    }

    @Override
    public PageMetaInfoWrapper queryPageData(InvStrategyResultsPageQueryRequest request) {
        Assert.isTrue(StringUtils.isNotBlank(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.REPLN_TYPE_IS_NOT_NULL.getError());
        Assert.isTrue(ReplnTypeEnum.codeValues().contains(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.REPLN_TYPE_IS_NOT_EXISTENT.getError());
        paddingPageQueryRequest(request);
        if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOC.getCode())) {
            return tocQueryPageData(request);
        } else {
            return tobQueryPageData(request);
        }
    }

    private PageMetaInfoWrapper tocQueryPageData(InvStrategyResultsPageQueryRequest request) {
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        List<InvStrategyResultsTocDO> invStrategyResultsTocDOS = invStrategyResultsTocDao.selectByCondition(JsonUtils.toMap(request));
        List<InvStrategyResultsTocVO> convert = GeiCommonConvert.convert(invStrategyResultsTocDOS, InvStrategyResultsTocVO.class);
        for (InvStrategyResultsTocVO vo : convert) {
            vo.setAbcTypeName(SkuLevelEnum.getNameByCode(vo.getAbcType()));
            vo.setCategoryName(String.format("%s/%s/%s",vo.getLv1CategoryName(), vo.getLv2CategoryName(), vo.getLv4CategoryName()));
            vo.setServiceRatioStr(vo.getServiceRatio());
            List<StrategyResultsExceptionVO> exceptionVOS = new ArrayList<>();
            Boolean flag = true;
            StringBuilder stringBuilder = new StringBuilder();
            List<String> errList = new ArrayList<>();
            if (StringUtils.isNotEmpty(vo.getErrStatus())) {
                errList = Arrays.asList(vo.getErrStatus().split(StringConstants.COMMA));
            }
            if (errList.contains(InvResultsExceptionErrorEnum.TOC_DAS_NUM_ZERO.getCode())) {
                flag = false;
                exceptionVOS.add(StrategyResultsExceptionVO.of(InvResultsExceptionErrorEnum.TOC_DAS_NUM_ZERO.getName()
                        , InvResultsExceptionErrorEnum.TOC_DAS_NUM_ZERO.getColor()
                        , MetaType.TAG.getCode(), InvResultsExceptionErrorEnum.TOC_DAS_NUM_ZERO.getTooltip()));
                stringBuilder.append(InvResultsExceptionErrorEnum.TOC_DAS_NUM_ZERO.getName()).append(StringConstants.SEMICOLON);
            }
            if (errList.contains(InvResultsExceptionErrorEnum.TOC_SAFETY_ZERO.getCode())) {
                flag = false;
                exceptionVOS.add(StrategyResultsExceptionVO.of(InvResultsExceptionErrorEnum.TOC_SAFETY_ZERO.getName()
                        , InvResultsExceptionErrorEnum.TOC_SAFETY_ZERO.getColor()
                        , MetaType.TAG.getCode(), InvResultsExceptionErrorEnum.TOC_SAFETY_ZERO.getTooltip()));
                stringBuilder.append(InvResultsExceptionErrorEnum.TOC_SAFETY_ZERO.getName()).append(StringConstants.SEMICOLON);
            }
            if (errList.contains(InvResultsExceptionErrorEnum.TOC_PARAM_ZERO.getCode())) {
                flag = false;
                exceptionVOS.add(StrategyResultsExceptionVO.of(InvResultsExceptionErrorEnum.TOC_PARAM_ZERO.getName()
                        , InvResultsExceptionErrorEnum.TOC_PARAM_ZERO.getColor()
                        , MetaType.TAG.getCode(), InvResultsExceptionErrorEnum.TOC_PARAM_ZERO.getTooltip()));
                stringBuilder.append(InvResultsExceptionErrorEnum.TOC_PARAM_ZERO.getName()).append(StringConstants.SEMICOLON);
            }
            if (errList.contains(InvResultsExceptionErrorEnum.TOC_DIST_ERROR.getCode())) {
                flag = false;
                exceptionVOS.add(StrategyResultsExceptionVO.of(InvResultsExceptionErrorEnum.TOC_DIST_ERROR.getName()
                        , InvResultsExceptionErrorEnum.TOC_DIST_ERROR.getColor()
                        , MetaType.TAG.getCode(), InvResultsExceptionErrorEnum.TOC_DIST_ERROR.getTooltip()));
                stringBuilder.append(InvResultsExceptionErrorEnum.TOC_DIST_ERROR.getName()).append(StringConstants.SEMICOLON);
            }
            if (errList.contains(InvResultsExceptionErrorEnum.TOC_SHARED_SKU.getCode())) {
                flag = false;
                exceptionVOS.add(StrategyResultsExceptionVO.of(InvResultsExceptionErrorEnum.TOC_SHARED_SKU.getName()
                        , InvResultsExceptionErrorEnum.TOC_SHARED_SKU.getColor()
                        , MetaType.TAG.getCode(), InvResultsExceptionErrorEnum.TOC_SHARED_SKU.getTooltip()));
                stringBuilder.append(InvResultsExceptionErrorEnum.TOC_SHARED_SKU.getName()).append(StringConstants.SEMICOLON);
            }
            if (flag) {
                exceptionVOS.add(StrategyResultsExceptionVO.of(InvResultsExceptionErrorEnum.NORMAL.getName()
                        , InvResultsExceptionErrorEnum.NORMAL.getColor()
                        , MetaType.TAG.getCode(), InvResultsExceptionErrorEnum.NORMAL.getTooltip()));
                vo.setErrStatus(InvResultsExceptionErrorEnum.NORMAL.getName());
            } else {
                vo.setErrStatus(stringBuilder.toString());
            }
            vo.setException(exceptionVOS);
        }
        MergeInfoHandler.advice(StrategyConstants.StrategyResults.ABC_TYPE, (merged, wrapper) -> {
            SkuLevelEnum colorByValue = SkuLevelEnum.getByCode(String.valueOf(wrapper.getValue()));
            if (Objects.nonNull(colorByValue)) {
                MetaInfoHolder.createExtra(TagExtra.of(colorByValue.getFontColor(), colorByValue.getBackgroudColor())).set(wrapper);
            }
        });
        MergeInfoHandler.merge(convert);
        PageMetaInfoWrapper pageMetaInfoWrapper = PageMetaInfoWrapper.of(request, invStrategyResultsTocDao.selectCount(JsonUtils.toMap(request)), convert, InvStrategyResultsTocVO.class);
        return pageMetaInfoWrapper;
    }

    private PageMetaInfoWrapper tobQueryPageData(InvStrategyResultsPageQueryRequest request) {
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        List<InvStrategyResultsTobDO> invStrategyResultsTobDOS = invStrategyResultsTobDao.selectByCondition(JsonUtils.toMap(request));
        List<InvStrategyResultsTobVO> convert = GeiCommonConvert.convert(invStrategyResultsTobDOS, InvStrategyResultsTobVO.class);
        for (InvStrategyResultsTobVO vo : convert) {
            vo.setAbcTypeName(SkuLevelEnum.getNameByCode(vo.getAbcType()));
            vo.setCategoryName(String.format("%s/%s/%s",vo.getLv1CategoryName(), vo.getLv2CategoryName(), vo.getLv4CategoryName()));
            vo.setServiceRatioStr(vo.getServiceRatio());
            List<StrategyResultsExceptionVO> exceptionVOS = new ArrayList<>();
            Boolean flag = true;
            StringBuilder stringBuilder = new StringBuilder();
            List<String> errList = new ArrayList<>();
            if (StringUtils.isNotEmpty(vo.getErrStatus())) {
                errList = Arrays.asList(vo.getErrStatus().split(StringConstants.COMMA));
            }
            if (flag) {
                exceptionVOS.add(StrategyResultsExceptionVO.of(InvResultsExceptionErrorEnum.NORMAL.getName()
                        , InvResultsExceptionErrorEnum.NORMAL.getColor()
                        , MetaType.TAG.getCode(), InvResultsExceptionErrorEnum.NORMAL.getTooltip()));
                vo.setErrStatus(InvResultsExceptionErrorEnum.NORMAL.getName());
            } else {
                vo.setErrStatus(stringBuilder.toString());
            }
            vo.setException(exceptionVOS);
        }
        MergeInfoHandler.advice(StrategyConstants.StrategyResults.ABC_TYPE, (merged, wrapper) -> {
            SkuLevelEnum colorByValue = SkuLevelEnum.getByCode(String.valueOf(wrapper.getValue()));
            if (Objects.nonNull(colorByValue)) {
                MetaInfoHolder.createExtra(TagExtra.of(colorByValue.getFontColor(), colorByValue.getBackgroudColor())).set(wrapper);
            }
        });
        MergeInfoHandler.merge(convert);
        PageMetaInfoWrapper pageMetaInfoWrapper = PageMetaInfoWrapper.of(request, invStrategyResultsTobDao.selectCount(JsonUtils.toMap(request)), convert, InvStrategyResultsTobVO.class);
        return pageMetaInfoWrapper;
    }

    @Override
    public Long update(InvStrategyResulesUpdateRequest request) {
        validateData(request);
        if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOC.getCode())) {
            return invStrategyResultsManager.tocUpdate(Collections.singletonList(request), false);
        } else {
            return invStrategyResultsManager.tobUpdate(Collections.singletonList(request), false);
        }
    }

    private void validateData(InvStrategyResulesUpdateRequest request) {
        Assert.isTrue(StringUtils.isNotBlank(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.REPLN_TYPE_IS_NOT_NULL.getError());
        Assert.isTrue(ReplnTypeEnum.codeValues().contains(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.REPLN_TYPE_IS_NOT_EXISTENT.getError());
        Assert.isTrue(StringUtils.isNotBlank(request.getSkuCode()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.SKU_CODE_NOT_NULL.getError());
        Assert.isTrue(StringUtils.isNotBlank(request.getRdcCode()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.RDC_CODE_NOT_NULL.getError());
        Assert.isTrue(Objects.nonNull(request.getManualSafetyDays()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.SAFETY_NOT_NULL.getError());
        Assert.isTrue(Objects.nonNull(request.getManualTargetDays()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.TARGET_NOT_NULL.getError());
        Assert.isTrue(request.getManualSafetyDays() >= 1 && request.getManualSafetyDays() <= 100, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.SAFETY_RANGE_ERROR.getError());
        Assert.isTrue(request.getManualTargetDays() >= 1 && request.getManualTargetDays() <= 100, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.TARGET_RANGE_ERROR.getError());
        Assert.isTrue(request.getManualTargetDays() >= request.getManualSafetyDays(), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.TARGET_LESS_SAFETY.getError());
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        if (Objects.nonNull(currentAccount)) {
            request.setOperatorCode(currentAccount.getId());
            request.setOperatorName(currentAccount.getName());
        }
    }

    @Override
    public Long resetSuggestValue(InvStrategyResultsPageQueryRequest request) {
        Assert.isTrue(StringUtils.isNotBlank(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.REPLN_TYPE_IS_NOT_NULL.getError());
        Assert.isTrue(ReplnTypeEnum.codeValues().contains(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.REPLN_TYPE_IS_NOT_EXISTENT.getError());
        if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOC.getCode())) {
            return invStrategyResultsManager.tocResetSuggestValue(request);
        } else {
            return invStrategyResultsManager.tobResetSuggestValue(request);
        }
    }

    @Override
    public PageMetaInfoWrapper selectPriority(InvStrategyResultsPageQueryRequest request) {
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        Assert.isTrue(StringUtils.isNotBlank(request.getSkuCode()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.SKU_CODE_NOT_NULL.getError());
        Assert.isTrue(StringUtils.isNotBlank(request.getRdcCode()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyResultsError.RDC_CODE_NOT_NULL.getError());
        List<InvStrategyPriorityDO> invStrategyPriorityDOS = invStrategyPriorityDao.selectBySkuAndRdc(request);
        List<InvStrategyPriorityVO> convert = GeiCommonConvert.convert(invStrategyPriorityDOS, InvStrategyPriorityVO.class);
        for (InvStrategyPriorityVO vo : convert) {
            vo.setCurrentDimensionName(InvDimensionEnum.getNameByCode(vo.getCurrentDimension()));
            vo.setConflictDimensionName(InvDimensionEnum.getNameByCode(vo.getConflictDimension()));
            vo.setPriorityTakeEffectName(InvDimensionEnum.getNameByCode(vo.getPriorityTakeEffect()));
        }
        MetaInfoCreator.advice(CONFLICT, metaInfo -> {
            MetaInfoHolder.createExtra(CONFLICT_COLOR).set(metaInfo);
        });
        MergeInfoHandler.merge(convert);
        PageMetaInfoWrapper pageMetaInfoWrapper = PageMetaInfoWrapper.of(request, invStrategyPriorityDao.selectCount(request), convert, MetaInfoCreator.create(InvStrategyPriorityVO.class, StrategyConstants.StrategyResults.PRIORITY_LIST));
        return pageMetaInfoWrapper;
    }

    @Override
    public String selectDate() {
        return DateUtil.localDateTimeToString(invStrategyResultsTocDao.selectDate(STRATEGY_RESULTS), DateUtil.DEFAULT_DATETIME_FORMAT);
    }
}
