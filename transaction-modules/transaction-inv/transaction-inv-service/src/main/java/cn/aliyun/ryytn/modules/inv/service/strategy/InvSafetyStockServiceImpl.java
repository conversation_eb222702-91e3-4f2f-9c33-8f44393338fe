package cn.aliyun.ryytn.modules.inv.service.strategy;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.inv.api.strategy.InvSafetyStockService;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.ConstantFactory;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.InjectConstant;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.service.ConstantDTO;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.BuriedLogger;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.QueryLogger;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfoHolder;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.entity.TagExtra;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeInfoHandler;
import cn.aliyun.ryytn.modules.inv.common.constants.BizLoggerConstant;
import cn.aliyun.ryytn.modules.inv.common.exception.Assert;
import cn.aliyun.ryytn.modules.inv.common.exception.ErrorCode;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.common.utils.JsonUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StreamUtils;
import cn.aliyun.ryytn.modules.inv.constant.business.BusinessConstants;
import cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants;
import cn.aliyun.ryytn.modules.inv.constant.strategy.enums.SkuLevelEnum;
import cn.aliyun.ryytn.modules.inv.constant.strategy.error.InvSafetyStockError;
import cn.aliyun.ryytn.modules.inv.entity.business.logger.InvStrategyReaultsLogMetaInfo;
import cn.aliyun.ryytn.modules.inv.entity.business.request.InvSupplyDaysPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvSafetyStockParametersDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvSafetyStockPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvSafetyStockUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.vo.InvSafetyStockParametersVO;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvSafetyStockDao;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/9 17:55
 * @description：
 */
@Service
@QueryLogger(bizCode = BizLoggerConstant.BizCode.SAFETY_STOCK, suffix = BizLoggerConstant.Suffix.STRATEGY, metaClass = InvStrategyReaultsLogMetaInfo.class)
public class InvSafetyStockServiceImpl implements InvSafetyStockService {

    @Autowired
    private InvSafetyStockDao invSafetyStockDao;


    @InjectConstant(type = StrategyConstants.SafetyStock.SAFETY_STOCK_QUERY)
    private ConstantFactory quickQueryFactory;

    @Override
    public List<OptionDTO> quickQueryClauseOption() {
        List<ConstantDTO> constantList = quickQueryFactory.get();
        return StreamUtils.map(constantList, item -> OptionDTO.of(item.getCode(), item.getName()));
    }

    private void paddingPageQueryRequest(InvSafetyStockPageQueryRequest request) {
        if (Objects.isNull(request.getQuickQueryClauseValue())) {
            String quickQueryClause = request.getQuickQueryClause();
            ConstantDTO constant = quickQueryFactory.getConstant(quickQueryClause);
            if (Objects.nonNull(constant)) {
                request.setQuickQueryClauseValue(constant.getExtra());
            }
        }
    }

    @Override
    public PageMetaInfoWrapper queryPageData(InvSafetyStockPageQueryRequest request) {
        paddingPageQueryRequest(request);
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        List<InvSafetyStockParametersDO> invStrategyResultsTocDOS = invSafetyStockDao.selectByCondition(JsonUtils.toMap(request));
        List<InvSafetyStockParametersVO> convert = GeiCommonConvert.convert(invStrategyResultsTocDOS, InvSafetyStockParametersVO.class);
        for (InvSafetyStockParametersVO vo : convert) {
            vo.setAbcTypeName(SkuLevelEnum.getNameByCode(vo.getAbcType()));
            vo.setCategoryName(String.format("%s/%s/%s",vo.getLv1CategoryName(), vo.getLv2CategoryName(), vo.getLv4CategoryName()));
        }
        MergeInfoHandler.advice(StrategyConstants.StrategyResults.ABC_TYPE, (merged, wrapper) -> {
            SkuLevelEnum colorByValue = SkuLevelEnum.getByCode(String.valueOf(wrapper.getValue()));
            if (Objects.nonNull(colorByValue)) {
                MetaInfoHolder.createExtra(TagExtra.of(colorByValue.getFontColor(), colorByValue.getBackgroudColor())).set(wrapper);
            }
        });
        MergeInfoHandler.merge(convert);
        PageMetaInfoWrapper pageMetaInfoWrapper = PageMetaInfoWrapper.of(request, invSafetyStockDao.selectCount(JsonUtils.toMap(request)), convert, InvSafetyStockParametersVO.class);
        return pageMetaInfoWrapper;
    }

    @Override
    @BuriedLogger(bizCode = BizLoggerConstant.BizCode.SAFETY_STOCK, suffix = BizLoggerConstant.Suffix.STRATEGY)
    public Long update(List<InvSafetyStockUpdateRequest> requests, boolean importFlag) {
        if (!importFlag) {
            Assert.isTrue(CollectionUtils.isNotEmpty(requests), ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.PARAM_IS_NOT_NULL.getError());
            InvSafetyStockUpdateRequest request = requests.get(0);
            validateData(request);
            InvSafetyStockPageQueryRequest pageQueryRequest = new InvSafetyStockPageQueryRequest();
            pageQueryRequest.setSku(Collections.singletonList(request.getSkuCode()));
            pageQueryRequest.setRdc(Collections.singletonList(request.getRdcCode()));
            List<InvSafetyStockParametersDO> safetyStockParametersDOS = invSafetyStockDao.selectByCondition(JsonUtils.toMap(pageQueryRequest));
            Assert.isTrue(CollectionUtils.isNotEmpty(safetyStockParametersDOS), ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.DATA_IS_NULL.getError());
            InvSafetyStockParametersDO parametersDO = safetyStockParametersDOS.get(0);
            if (Objects.nonNull(parametersDO.getServiceRatio())) {
                request.setServiceRatioBeforeStr(String.format("%s%%", parametersDO.getServiceRatio().multiply(BigDecimal.valueOf(100)).setScale(2)));
            }
            request.setMinSafetyDaysBefore(parametersDO.getMinSafetyDays());
            request.setMaxSafetyDaysBefore(parametersDO.getMaxSafetyDays());
            request.setRdcName(parametersDO.getRdcName());
            request.setSkuName(parametersDO.getSkuName());
        }
        return invSafetyStockDao.update(requests);
    }

    private void validateData(InvSafetyStockUpdateRequest request) {
        Assert.isTrue(StringUtils.isNotBlank(request.getSkuCode()), ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.SKU_CODE_NOT_NULL.getError());
        Assert.isTrue(StringUtils.isNotBlank(request.getRdcCode()), ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.RDC_CODE_NOT_NULL.getError());
        Assert.isTrue(Objects.nonNull(request.getMinSafetyDays()), ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.MIN_SAFETY_NOT_NULL.getError());
        Assert.isTrue(Objects.nonNull(request.getMaxSafetyDays()), ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.MAX_SAFETY_NOT_NULL.getError());
        Assert.isTrue(Objects.nonNull(request.getServiceRatio()), ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.SERVICE_RATIO_NOT_NULL.getError());
        Assert.isTrue(request.getServiceRatio().compareTo(BigDecimal.ONE) >= 0
                && request.getServiceRatio().compareTo(BigDecimal.valueOf(100)) <= 0, ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.SERVICE_RATIO_RANGE_ERROR.getError());
        Assert.isTrue(request.getMinSafetyDays() >= 1 && request.getMinSafetyDays() <= 100, ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.MIN_SAFETY_RANGE_ERROR.getError());
        Assert.isTrue(request.getMaxSafetyDays() >= 1 && request.getMaxSafetyDays() <= 100, ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.MAX_SAFETY_RANGE_ERROR.getError());
        Assert.isTrue(request.getMaxSafetyDays() > request.getMinSafetyDays(), ErrorCode.ILLEGAL_ARGUMENT, InvSafetyStockError.SAFETY_LESS_SAFETY.getError());
        request.setServiceRatio(request.getServiceRatio().divide(BigDecimal.valueOf(100)).setScale(4, RoundingMode.HALF_UP));
        request.setServiceRatioStr(String.format("%s%%", request.getServiceRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        if (Objects.nonNull(currentAccount)) {
            request.setOperatorCode(currentAccount.getId());
            request.setOperatorName(currentAccount.getName());
        }
    }

}
