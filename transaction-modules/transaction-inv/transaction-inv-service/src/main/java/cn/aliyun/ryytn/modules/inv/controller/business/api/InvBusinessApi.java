package cn.aliyun.ryytn.modules.inv.controller.business.api;

/**
 * <AUTHOR>
 * @date 2025/4/24 10:05
 * @description：
 */
public interface InvBusinessApi {

    interface SupplyDays {

        String ROOT = "/api/supply-days";

        String LIST = "/list";

        String QUICK_QUERY_CLAUSE = "/quickQueryClause";

        String UPDATE = "/update";


    }

    interface CdcRdcPriority {
        String ROOT = "/api/cdc-rdc-priority";

        String LIST = "/list";

        String QUICK_QUERY_CLAUSE = "/quickQueryClause";

        String UPDATE = "/update";
    }

    interface SkuReplnType{

        String ROOT = "/api/sku_repln_type";

        String LIST = "/list";

        String UPDATE = "/update";

        String QUICK_QUERY_CLAUSE = "/quickQueryClause";
    }

    interface SkuCdcFullSupply{

        String ROOT = "/api/sku_cdc_full_supply";

        String LIST = "/list";

        String UPDATE = "/update";

        String QUICK_QUERY_CLAUSE = "/quickQueryClause";

        String YES_OR_NO_ENUM_LIST = "/yes_or_no_enum_list";
    }

}
