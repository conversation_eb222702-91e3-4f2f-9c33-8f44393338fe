package cn.aliyun.ryytn.modules.inv.service.transfer.enums;

import cn.aliyun.ryytn.modules.inv.common.function.MultiFunction;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.common.utils.JsonUtils;
import cn.aliyun.ryytn.modules.inv.entity.transfer.dto.ScpCommonConfigDTO;
import cn.aliyun.ryytn.modules.inv.entity.transfer.request.CommonConfigQueryRequest;
import cn.aliyun.ryytn.modules.inv.service.transfer.manager.DynamicOptionManager;
import com.alibaba.fastjson2.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20 14:28
 * @description：
 */
@Getter
@AllArgsConstructor
public enum OptionType {
    STATIC("static", "静态可选项", (request, config, manager) -> {
        String options = config.getOptions();
        return JsonUtils.toObject(options, new TypeReference<List<OptionDTO>>(){});
    }),
    DYNAMIC("dynamic", "动态可选项", (request, config, manager) ->
        manager.getOption(config.getOptions(), request, config)
    ),;

    private final String code;
    private final String name;
    private final MultiFunction<CommonConfigQueryRequest, ScpCommonConfigDTO, DynamicOptionManager, List<OptionDTO>> optionFunction;

    public static OptionType getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (OptionType optionType : OptionType.values()) {
            if (StringUtils.equalsIgnoreCase(optionType.getCode(), code)) {
                return optionType;
            }
        }
        return null;
    }

    public List<OptionDTO> getOptionList(CommonConfigQueryRequest request, ScpCommonConfigDTO commonConfig, DynamicOptionManager dynamicOptionManager) {
        return optionFunction.apply(request, commonConfig, dynamicOptionManager);
    }
}
