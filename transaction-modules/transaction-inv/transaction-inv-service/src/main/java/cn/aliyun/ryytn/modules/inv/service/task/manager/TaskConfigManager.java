package cn.aliyun.ryytn.modules.inv.service.task.manager;


import cn.aliyun.ryytn.modules.inv.api.task.enums.TaskGroupType;
import cn.aliyun.ryytn.modules.inv.common.helper.UserContextHolder;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.api.task.error.TaskError;
import cn.aliyun.ryytn.modules.inv.common.utils.StringConstants;
import cn.aliyun.ryytn.modules.inv.task.dao.ScpTaskCfgMapper;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskCfgDO;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskTypeCfgDO;
import cn.aliyun.ryytn.modules.inv.api.task.request.TaskConfigQueryRequest;
import cn.aliyun.ryytn.modules.inv.api.task.request.TaskConfigUpsertRequest;
import cn.aliyun.ryytn.modules.inv.api.task.request.TaskTypeQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.common.enums.BooleanEnum;
import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.StreamUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-22 10:04
 * @description
 */
@Component
public class TaskConfigManager {
    @Resource
    private ScpTaskCfgMapper scpTaskCfgMapper;
    @Resource
    private TaskTypeConfigManager taskTypeConfigManager;

    public List<ScpTaskCfgDO> queryConfig(TaskConfigQueryRequest request) {
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        return scpTaskCfgMapper.selectByCondition(JsonUtils.toMap(request));
    }

    public Long queryConfigCount(TaskConfigQueryRequest request) {
        return scpTaskCfgMapper.selectCount(JsonUtils.toMap(request));
    }

    public List<OptionDTO> queryOdpsOptionList(String input) {
        TaskConfigQueryRequest request = new TaskConfigQueryRequest();
        request.setBlurOdpsKey(input);
        request.setGroupType(TaskGroupType.ODPS.getCode());
        request.setPageSize(20L);
        request.calcPage();
        List<ScpTaskCfgDO> taskCfgList = scpTaskCfgMapper.selectByCondition(JsonUtils.toMap(request));
        return StreamUtils.map(taskCfgList, item -> OptionDTO.of(String.valueOf(item.getOdpsCode()), item.getOdpsName()));
    }

    public Long upsert(List<TaskConfigUpsertRequest> requests) {
        List<ScpTaskTypeCfgDO> taskTypeCfg = taskTypeConfigManager.queryConfig(new TaskTypeQueryRequest());
        Map<String, ScpTaskTypeCfgDO> taskTypeCfgMap = StreamUtils.singleGroup(taskTypeCfg, ScpTaskTypeCfgDO::getTaskTypeCode);
        List<ScpTaskCfgDO> finalUpsert = new ArrayList<>(requests.size());
        List<ScpTaskCfgDO> finalUpdate = new ArrayList<>(requests.size());
        String cnTenant = UserContextHolder.getCnTenant();
        TaskConfigQueryRequest queryRequest = new TaskConfigQueryRequest();
        queryRequest.setPaging(false);
        List<ScpTaskCfgDO> taskCfgList = scpTaskCfgMapper.selectByCondition(JsonUtils.toMap(queryRequest));
        Assert.isTrue(CollectionUtils.isNotEmpty(taskCfgList), ErrorCode.ILLEGAL_ARGUMENT, TaskError.UPDATE_LIST_NOT_EXISTS.getError());
        Map<String, ScpTaskCfgDO> stringScpTaskCfgDOMap = StreamUtils.singleGroup(taskCfgList, item -> item.getOdpsCode() + StringConstants.SEMICOLON + item.getTaskTypeCode());
        for (TaskConfigUpsertRequest request : requests) {
            ScpTaskTypeCfgDO scpTaskType = taskTypeCfgMap.get(request.getTaskTypeCode());
            Assert.isTrue(Objects.nonNull(scpTaskType), ErrorCode.ILLEGAL_ARGUMENT, TaskError.TASK_TYPE_NOT_EXISTS.getError());
            Assert.isTrue(Objects.nonNull(request.getOdpsCode()), ErrorCode.ILLEGAL_ARGUMENT, TaskError.ODPS_CODE_EMPTY.getError());
            Assert.isTrue(StringUtils.isNotBlank(request.getOdpsName()), ErrorCode.ILLEGAL_ARGUMENT, TaskError.ODPS_NAME_EMPTY.getError());
            Assert.isTrue(StringUtils.isNotBlank(request.getDateExpr()), ErrorCode.ILLEGAL_ARGUMENT, TaskError.DATE_EXPR_EMPTY.getError());
            Assert.isTrue(Objects.nonNull(request.getDateExprPriority()), ErrorCode.ILLEGAL_ARGUMENT, TaskError.DATE_EXPR_PRIORITY_EMPTY.getError());
            ScpTaskCfgDO cfg = GeiCommonConvert.convert(request, ScpTaskCfgDO.class);
            cfg.setTaskTypeName(scpTaskType.getTaskTypeName());
            cfg.setPreTaskTypeCode(scpTaskType.getPreTaskTypeCode());
            cfg.setPreTaskTypeName(scpTaskType.getPreTaskTypeName());
            cfg.setNextTaskTypeCode(scpTaskType.getNextTaskTypeCode());
            cfg.setNextTaskTypeName(scpTaskType.getNextTaskTypeName());
            String rootOdps = BooleanEnum.getByBool(request.getRootOdps()).getCode();
            cfg.setRootOdps(rootOdps);
            Integer enable = BooleanEnum.getByBool(request.getEnable()).getICode();
            cfg.setEnable(enable);
            if (Objects.nonNull(stringScpTaskCfgDOMap.get(cfg.getOdpsCode() + StringConstants.SEMICOLON + cfg.getTaskTypeCode()))) {
                finalUpdate.add(cfg);
                continue;
            }
            finalUpsert.add(cfg);
        }
        Long count = 0l;
        if (CollectionUtils.isNotEmpty(finalUpsert)) {
            count += scpTaskCfgMapper.upsert(finalUpsert);
        }
        if (CollectionUtils.isNotEmpty(finalUpdate)) {
            count += scpTaskCfgMapper.update(finalUpdate);
        }
        return count;
    }

    public Long delete(String taskTypeCode, Long odpsCode) {
        return scpTaskCfgMapper.delete(taskTypeCode, odpsCode);
    }
}
