package cn.aliyun.ryytn.modules.inv.service.strategy;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.modules.inv.api.strategy.InvStrategyCenterService;
import cn.aliyun.ryytn.modules.inv.business.dao.InvSkuReplnTypeDao;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfo;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfoCreator;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeInfoHandler;
import cn.aliyun.ryytn.modules.inv.common.utils.*;
import cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants;
import cn.aliyun.ryytn.modules.inv.constant.strategy.enums.*;
import cn.aliyun.ryytn.modules.inv.constant.strategy.error.InvStrategyCenterError;
import cn.aliyun.ryytn.modules.inv.entity.md.dos.InvUnitRdcSkuDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.*;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dto.*;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.*;
import cn.aliyun.ryytn.modules.inv.entity.strategy.vo.InvSkuRdcVO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.vo.InvStrategyPriorityVO;
import cn.aliyun.ryytn.modules.inv.md.dao.InvUnitRdcSkuDao;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyCenterDao;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyParametersTobDao;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyParametersTocDao;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyPriorityDao;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.lang.TypeReference;
import cn.aliyun.ryytn.modules.inv.common.exception.Assert;
import cn.aliyun.ryytn.modules.inv.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/23 14:53
 * @description：
 */
@Service
@Slf4j
public class InvStrategyCenterServiceImpl implements InvStrategyCenterService {

    @Autowired
    private InvStrategyCenterDao invStrategyCenterDao;

    @Autowired
    private InvUnitRdcSkuDao invUnitRdcSkuDao;

    @Autowired
    private InvStrategyPriorityDao invStrategyPriorityDao;

    @Autowired
    private InvSkuReplnTypeDao invSkuReplnTypeDao;

    @Autowired
    private InvStrategyParametersTobDao invStrategyParametersTobDao;

    @Autowired
    private InvStrategyParametersTocDao invStrategyParametersTocDao;

    @Autowired
    private InvStrategyCenterManager invStrategyCenterManager;

    @Autowired
    private ThreadPoolExecutor commonIOThreadPool;

    @Override
    public List<IndicatorSummaryDTO> queryIndicator() {
        List<IndicatorSummaryDTO> indicatorSummaryDTOS = new ArrayList<>();
        InvStrategyIndicatorDO stockStrategyIndicator = invStrategyCenterDao.queryIndicator();
        IndicatorSummaryDTO strategyCount = new IndicatorSummaryDTO(InvStrategyIndicatorEnum.STRATEGY_COUNT.getName(), NumUtils.setInt(stockStrategyIndicator.getStrategyCount()));
        IndicatorSummaryDTO strategyCover = new IndicatorSummaryDTO(InvStrategyIndicatorEnum.STRATEGY_COVER.getName(), NumUtils.setInt(stockStrategyIndicator.getStrategyCover()));
        strategyCount.setChildren(Collections.singletonList(strategyCover));
        IndicatorSummaryDTO enableCount = new IndicatorSummaryDTO(InvStrategyIndicatorEnum.ENABLE_COUNT.getName(), NumUtils.setInt(stockStrategyIndicator.getEnableCount()));
        IndicatorSummaryDTO enableCover = new IndicatorSummaryDTO(InvStrategyIndicatorEnum.ENABLE_COVER.getName(), NumUtils.setInt(stockStrategyIndicator.getEnableCover()));
        enableCount.setChildren(Collections.singletonList(enableCover));
        IndicatorSummaryDTO disableCount = new IndicatorSummaryDTO(InvStrategyIndicatorEnum.DISABLE_COUNT.getName(), NumUtils.setInt(stockStrategyIndicator.getDisableCount()));
        IndicatorSummaryDTO disableCover = new IndicatorSummaryDTO(InvStrategyIndicatorEnum.DISABLE_COVER.getName(), NumUtils.setInt(stockStrategyIndicator.getDisableCover()));
        disableCount.setChildren(Collections.singletonList(disableCover));
        indicatorSummaryDTOS.add(strategyCount);
        indicatorSummaryDTOS.add(enableCount);
        indicatorSummaryDTOS.add(disableCount);
        return indicatorSummaryDTOS;
    }

    @Override
    public List<String> queryAllName(String name) {
        return invStrategyCenterDao.queryAllName(name);
    }

    @Override
    public List<InvStrategyCenterGatherDTO> queryGather(InvStrategyCenterPageQueryRequest request) {
        List<InvStrategyCenterGatherDO> invStrategyCenterGatherDOS = invStrategyCenterDao.queryGather(JsonUtils.toMap(request));
        Map<String, InvStrategyCenterGatherDO> identityMap = CollStreamUtil.toIdentityMap(invStrategyCenterGatherDOS, InvStrategyCenterGatherDO::getDimension);
        List<InvStrategyCenterGatherDTO> list = new ArrayList<>();
        for (InvDimensionEnum invDimensionEnum : InvDimensionEnum.sortedValues()) {
            InvStrategyCenterGatherDO invStrategyCenterGatherDO = identityMap.get(invDimensionEnum.getCode());
            if (invStrategyCenterGatherDO == null) {
                list.add(new InvStrategyCenterGatherDTO(invDimensionEnum.getCode(), invDimensionEnum.getName(), 0));
                continue;
            }
            list.add(new InvStrategyCenterGatherDTO(invDimensionEnum.getCode(), invDimensionEnum.getName(), invStrategyCenterGatherDO.getCount()));
        }
        return list;
    }

    @Override
    public List<InvStrategyCenterDTO> queryPageData(InvStrategyCenterPageQueryRequest request) {
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        List<InvStrategyCenterDO> queryList = invStrategyCenterDao.selectByCondition(JsonUtils.toMap(request));
        List<InvStrategyCenterDTO> stockStrategyCenterDTOS = GeiCommonConvert.convert(queryList, InvStrategyCenterDTO.class);
        stockStrategyCenterDTOS.forEach(t -> {
            t.setDimensionName(InvDimensionEnum.getNameByCode(t.getDimension()));
            t.setReplnTypeName(ReplnTypeEnum.getDescByCode(t.getReplnType()));
            t.setStatusName(InvStrategyStatusEnum.getNameByCode(t.getStatus()));
        });
        return stockStrategyCenterDTOS;
    }

    @Override
    public Long queryCount(InvStrategyCenterPageQueryRequest request) {
        return invStrategyCenterDao.selectCount(JsonUtils.toMap(request));
    }

    @Override
    public Long delete(Long id) {
        invStrategyParametersTobDao.deletebyStrategyId(id);
        invStrategyParametersTocDao.deletebyStrategyId(id);
        return invStrategyCenterDao.delete(id);
    }

    @Override
    public Map<String, Object> batchUpdateStatus(InvBatchUpdateStatusRequest request) {
        Assert.isTrue(CollectionUtils.isNotEmpty(request.getIds()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.PARAM_IS_NOT_NULL.getError());
        Assert.isTrue(Objects.nonNull(request.getStatus()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STATUS_IS_NOT_NULL.getError());
        Assert.isTrue(StringUtils.isNotBlank(request.getDimension()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.DIMENSION_IS_NOT_NULL.getError());
        //查询所有策略覆盖产品
        List<InvStrategyCenterDTO> invStrategyCenterList = new ArrayList<>();
        List<InvStrategyCenterDTO> invStrategyCenterListOne = invStrategyCenterDao.selectSkuListByIdsOne(Collections.emptyList());
        List<InvStrategyCenterDTO> invStrategyCenterListTwo = invStrategyCenterDao.selectSkuListByIdsTwo(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(invStrategyCenterListOne)) {
            invStrategyCenterList.addAll(invStrategyCenterListOne);
        }
        if (CollectionUtils.isNotEmpty(invStrategyCenterListTwo)) {
            invStrategyCenterList.addAll(invStrategyCenterListTwo);
        }
//        List<InvStrategyCenterDTO> invStrategyCenterList = invStrategyCenterDao.selectSkuListByIds(Collections.emptyList());
        Assert.isTrue(CollectionUtils.isNotEmpty(invStrategyCenterList), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.QUERY_DATA_IS_NOT_NULL.getError());
        //先处理策略覆盖的品
        for (InvStrategyCenterDTO invStrategyCenterDTO : invStrategyCenterList) {
            if (StringUtils.isNotEmpty(invStrategyCenterDTO.getSkuList())) {
                invStrategyCenterDTO.setSkuCodeList(Arrays.asList(invStrategyCenterDTO.getSkuList().replaceAll("[\\[\\]]", "").replaceAll(" ", "").split(StringConstants.COMMA)));
            }
        }
        //操作人
        if (Objects.nonNull(ServiceContextUtils.currentSession()) && Objects.nonNull(ServiceContextUtils.currentSession().getAccount())) {
            request.setOperatorCode(ServiceContextUtils.currentSession().getAccount().getId());
            request.setOperatorName(ServiceContextUtils.currentSession().getAccount().getName());
        }
        if (Objects.equals(InvStrategyStatusEnum.ENABLED.getCode(), request.getStatus())) {
            return batchEnabled(request, invStrategyCenterList);
        } else if (Objects.equals(InvStrategyStatusEnum.DISABLE.getCode(), request.getStatus())) {
            return batchDisable(request, invStrategyCenterList);
        }
        return null;
    }

    private Map<String, Object> batchEnabled(InvBatchUpdateStatusRequest request, List<InvStrategyCenterDTO> invStrategyCenterList) {
        //获取当前选中策略覆盖产品，如果全部为启用中，则直接返回成功
        List<InvStrategyCenterDTO> requestStrategyList = invStrategyCenterList.stream().filter(t ->
                Objects.equals(InvStrategyStatusEnum.DISABLE.getCode(), t.getStatus()) && request.getIds().contains(t.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(requestStrategyList)) {
            return null;
        }
        //校验启用库存策略重复
        if (Objects.isNull(request.getRepeatConfirm())) {
            Map<String, Object> object = checkDuplication(request, invStrategyCenterList);
            if (Objects.nonNull(object)) {
                return object;
            }
        }
        Boolean flag = true;
        //校验是否有重复，如果enableList不为空，证明存在重复，涉及到策略启用禁用
        if (CollectionUtils.isEmpty(request.getEnableList())) {
            request.setEnableList(request.getIds());
            flag = false;
        }
        if (Objects.isNull(request.getPriorityConfirm())) {
            Map<String, Object> object = checkPriority(request, invStrategyCenterList);
            if (Objects.nonNull(object)) {
                return object;
            }
        }
        if (Objects.nonNull(request.getPriorityList())) {
            List<InvStrategyPriorityDO> convert = GeiCommonConvert.convert(request.getPriorityList(), InvStrategyPriorityDO.class);
            if (convert.size() > 500) {
                List<CompletableFuture<Object>> tasks = new ArrayList<>();
                int batchSize = 500;
                int totalSize = convert.size();
                for (int i = 0; i < totalSize; i += batchSize) {
                    int endIndex = Math.min(i+batchSize, totalSize);
                    List<InvStrategyPriorityDO> batch = convert.subList(i, endIndex);
                    tasks.add(
                            CompletableFuture.supplyAsync(() -> {
                                invStrategyPriorityDao.batchInsert(batch);
                                return null;
                            }, commonIOThreadPool).exceptionally(e -> {
                                log.error("insert data error: ", e);
                                return null;
                            })
                    );
                }
                for (CompletableFuture<Object> task : tasks) {
                    task.join();
                }
            } else {
                invStrategyPriorityDao.batchInsert(convert);
            }
        }
        //修改策略状态
        request.setIds(null);
        if (flag) {
            invStrategyCenterDao.updateDisableStatus(JsonUtils.toMap(request));
        }
        invStrategyCenterDao.updateEnableStatus(JsonUtils.toMap(request));
        updateEnable(request, invStrategyCenterList);
        return null;
    }

    public void updateEnable(InvBatchUpdateStatusRequest request, List<InvStrategyCenterDTO> invStrategyCenterList) {
        List<InvStrategyCenterDTO> dimensionCenterList = invStrategyCenterList.stream().filter(t ->
                Objects.equals(t.getDimension(), request.getDimension())).collect(Collectors.toList());
        List<InvStrategyUpdateRequest> updateRequests = new ArrayList<>();
        for (InvStrategyCenterDTO centerDTO : dimensionCenterList) {
            if (request.getEnableList().contains(centerDTO.getId()) && Objects.equals(InvStrategyStatusEnum.DISABLE.getCode(), centerDTO.getStatus())) {
                updateRequests.add(InvStrategyUpdateRequest.of(centerDTO.getName(), InvStrategyStatusEnum.ENABLED.getName(), InvStrategyStatusEnum.DISABLE.getName()));
            } else if (!request.getEnableList().contains(centerDTO.getId()) && Objects.equals(InvStrategyStatusEnum.ENABLED.getCode(), centerDTO.getStatus())) {
                updateRequests.add(InvStrategyUpdateRequest.of(centerDTO.getName(), InvStrategyStatusEnum.DISABLE.getName(), InvStrategyStatusEnum.ENABLED.getName()));
            }
        }
        invStrategyCenterManager.update(updateRequests);
    }

    /**
     * 校验 启用库存策略重复
     * @param request
     * @param invStrategyCenterList
     * @return
     */
    private Map<String, Object> checkDuplication(InvBatchUpdateStatusRequest request, List<InvStrategyCenterDTO> invStrategyCenterList) {
        //获取当前选中策略覆盖产品，不需要考虑状态
        List<InvStrategyCenterDTO> requestStrategyList = invStrategyCenterList.stream().filter(t -> request.getIds().contains(t.getId())).collect(Collectors.toList());
        List<InvStrategyCenterDTO> enabledStrategyList = invStrategyCenterList.stream().filter(t -> !request.getIds().contains(t.getId())
                && Objects.equals(t.getStatus(), InvStrategyStatusEnum.ENABLED.getCode())
                && Objects.equals(request.getDimension(), t.getDimension())).collect(Collectors.toList());
        if (Objects.equals(request.getIds().size(), 1)) {
            for (InvStrategyCenterDTO strategyCenterDTO : requestStrategyList) {
                strategyCenterDTO.setGmtModified(LocalDateTime.now());
            }
        }
        requestStrategyList.addAll(enabledStrategyList);
        //品对应的策略Map
        Map<String, List<InvStrategyCenterDTO>> skuCodeCenterMap = new HashMap<>();
        for (InvStrategyCenterDTO strategyCenterDTO : requestStrategyList) {
            if (CollectionUtils.isEmpty(strategyCenterDTO.getSkuCodeList())) {
                continue;
            }
            for (String skuCode : strategyCenterDTO.getSkuCodeList()) {
                List<InvStrategyCenterDTO> strategyCenterDTOList = skuCodeCenterMap.get(skuCode);
                if (CollectionUtils.isEmpty(strategyCenterDTOList)) {
                    List<InvStrategyCenterDTO> list = new ArrayList<>();
                    list.add(strategyCenterDTO);
                    skuCodeCenterMap.put(skuCode, list);
                } else {
                    strategyCenterDTOList.add(strategyCenterDTO);
                    skuCodeCenterMap.put(skuCode, strategyCenterDTOList);
                }
            }
        }
        //冲突策略Id集合
        Set<Long> conflictIdSet = new HashSet<>();
        //获取策略重复的sku，查询sku对应的rdc仓库
        List<String> repeatSkuCodeList = skuCodeCenterMap.entrySet().stream().filter(t -> t.getValue().size() > 1).map(t -> t.getKey()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(repeatSkuCodeList)) {
            return null;
        }
        //最终返回的列表数据
        List<InvStrategyPriorityDTO> repeatList = new ArrayList<>();
        List<InvUnitRdcSkuDO> invUnitRdcSkuDOS = invUnitRdcSkuDao.selectBySkuCodes(repeatSkuCodeList);
        Map<String, List<InvUnitRdcSkuDO>> skuRdcMap = StreamUtils.group(invUnitRdcSkuDOS, InvUnitRdcSkuDO::getSkuCode);
        skuCodeCenterMap.forEach((k, v) -> {
            if (v.size() <= 1) {
                return;
            }
            //根据修改时间排序，以修改时间最晚的策略为主
            v.sort(Comparator.comparing(InvStrategyCenterDTO::getGmtModified).reversed());
            InvStrategyCenterDTO strategyCenterDTO = v.get(0);
            List<InvUnitRdcSkuDO> skuRdcList = skuRdcMap.get(k);
            Boolean flag = false;
            for (InvStrategyCenterDTO invStrategyCenterDTO : v) {
                //去除修改时间最晚的策略
                if (!flag) {
                    flag = true;
                    continue;
                }
                //品仓关系不一定存在，只添加品的信息
                if (CollectionUtils.isEmpty(skuRdcList)) {
                    repeatList.add(setRepeatList(k, strategyCenterDTO, invStrategyCenterDTO));
                } else {
                    repeatList.addAll(setRepeatList(skuRdcList, strategyCenterDTO, invStrategyCenterDTO));
                }
                conflictIdSet.add(invStrategyCenterDTO.getId());
            }
        });
        //当前批量启用的策略以及正在启用中的策略id，去除冲突的策略id，就是当前维度真正启用的策略
        List<Long> strategyIdList = requestStrategyList.stream().map(t -> t.getId()).collect(Collectors.toList());
        strategyIdList.removeAll(conflictIdSet);
        if (CollectionUtils.isNotEmpty(repeatList)) {
            Map<String, Object> map = new HashMap<>();
            List<InvStrategyPriorityVO> convert = GeiCommonConvert.convert(repeatList, InvStrategyPriorityVO.class);
            MergeInfoHandler.merge(convert);
            MetaInfoWrapper metaInfoWrapper = MetaInfoWrapper.of(convert, MetaInfoCreator.create(InvStrategyPriorityVO.class, StrategyConstants.StrategyCenter.DUPLICATION));
            map.put(StrategyConstants.StrategyCenter.META, metaInfoWrapper);
            map.put(StrategyConstants.StrategyCenter.ENABLE_LIST, strategyIdList);
            map.put(StrategyConstants.StrategyCenter.REPEAT_CONFIRM, 1);
            map.put(StrategyConstants.StrategyCenter.TYPE, StrategyConstants.StrategyCenter.REPEAT_CONFIRM);
            map.put(StrategyConstants.StrategyCenter.TOTAL, String.format("「%s」行品仓与其他策略包重复，当同一维度对应的同一对象不同策略参数时；将按照最新更新进行生效，冲突库存策略将被禁用。请确认是否启用？", convert.size()));
            return map;
        }
        return null;
    }

    private InvStrategyPriorityDTO setRepeatList(String skuCode, InvStrategyCenterDTO strategyCenterDTO, InvStrategyCenterDTO invStrategyCenterDTO) {
        InvStrategyPriorityDTO strategyPriorityDTO = new InvStrategyPriorityDTO();
        strategyPriorityDTO.setSkuCode(skuCode);
        strategyPriorityDTO.setCurrentInvStrategyId(strategyCenterDTO.getId());
        strategyPriorityDTO.setCurrentInvStrategyName(strategyCenterDTO.getName());
        strategyPriorityDTO.setCurrentDimension(strategyCenterDTO.getDimension());
        strategyPriorityDTO.setCurrentDimensionName(InvDimensionEnum.getNameByCode(strategyCenterDTO.getDimension()));
        strategyPriorityDTO.setConflictDimension(invStrategyCenterDTO.getDimension());
        strategyPriorityDTO.setConflictDimensionName(InvDimensionEnum.getNameByCode(invStrategyCenterDTO.getDimension()));
        strategyPriorityDTO.setConflictInvStrategyId(invStrategyCenterDTO.getId());
        strategyPriorityDTO.setConflictInvStrategyName(invStrategyCenterDTO.getName());
        strategyPriorityDTO.setConflictOperatorCode(invStrategyCenterDTO.getOperatorCode());
        strategyPriorityDTO.setConflictOperatorName(invStrategyCenterDTO.getOperatorName());
        strategyPriorityDTO.setConflictUpdateTime(invStrategyCenterDTO.getGmtModified());
        return strategyPriorityDTO;
    }


    private List<InvStrategyPriorityDTO> setRepeatList(List<InvUnitRdcSkuDO> skuRdcList, InvStrategyCenterDTO strategyCenterDTO, InvStrategyCenterDTO invStrategyCenterDTO) {
        List<InvStrategyPriorityDTO> repeatList = new ArrayList<>();
        for (InvUnitRdcSkuDO invUnitRdcSkuDO : skuRdcList) {
            InvStrategyPriorityDTO strategyPriorityDTO = GeiCommonConvert.convert(invUnitRdcSkuDO, InvStrategyPriorityDTO.class);
            strategyPriorityDTO.setCurrentInvStrategyId(strategyCenterDTO.getId());
            strategyPriorityDTO.setCurrentInvStrategyName(strategyCenterDTO.getName());
            strategyPriorityDTO.setCurrentDimension(strategyCenterDTO.getDimension());
            strategyPriorityDTO.setCurrentDimensionName(InvDimensionEnum.getNameByCode(strategyCenterDTO.getDimension()));
            strategyPriorityDTO.setConflictDimension(invStrategyCenterDTO.getDimension());
            strategyPriorityDTO.setConflictDimensionName(InvDimensionEnum.getNameByCode(invStrategyCenterDTO.getDimension()));
            strategyPriorityDTO.setConflictInvStrategyId(invStrategyCenterDTO.getId());
            strategyPriorityDTO.setConflictInvStrategyName(invStrategyCenterDTO.getName());
            strategyPriorityDTO.setConflictOperatorCode(invStrategyCenterDTO.getOperatorCode());
            strategyPriorityDTO.setConflictOperatorName(invStrategyCenterDTO.getOperatorName());
            strategyPriorityDTO.setConflictUpdateTime(invStrategyCenterDTO.getGmtModified());
            repeatList.add(strategyPriorityDTO);
        }
        return repeatList;
    }

    private Map<String, Object> checkPriority(InvBatchUpdateStatusRequest request, List<InvStrategyCenterDTO> invStrategyCenterList) {
        /**
         * 判断优先级时，需要判断下，有哪些品是维度有变化的，本次操作后所有启用策略覆盖的品 - 本次操作前所有启用策略覆盖的品，就是启用状态 维度变化的品，
         * 维度有变化的品 - 比当前维度高的策略覆盖的品，再去和维度比当前维度低的启用中的策略做比较，就是优先级变动数据，优先级变高
         * 本次操作前所有启用策略覆盖的品 - 本次操作后所有启用策略覆盖的品，就是当前维度此次操作禁用的品，需要 - 高维度启用中策略覆盖的品，然后再去和低纬度策略启用中的品做比较，优先级从高变低
         */
        //当前维度
        String dimension = request.getDimension();
        //本次操作前所有启用策略
        List<InvStrategyCenterDTO> beforeStrategyList = invStrategyCenterList.stream().filter(t -> Objects.equals(t.getStatus(), InvStrategyStatusEnum.ENABLED.getCode())
                && Objects.equals(t.getDimension(), dimension)).collect(Collectors.toList());
        List<String> beforeSkuCodeList = new ArrayList<>();
        for (InvStrategyCenterDTO strategyCenterDTO : beforeStrategyList) {
            if (CollectionUtils.isNotEmpty(strategyCenterDTO.getSkuCodeList())) {
                beforeSkuCodeList.addAll(strategyCenterDTO.getSkuCodeList());
            }
        }
        //本次操作后所有启用策略
        List<InvStrategyCenterDTO> afterStrategyList = invStrategyCenterList.stream().filter(t -> request.getEnableList().contains(t.getId())).collect(Collectors.toList());
        List<String> afterSkuCodeList = new ArrayList<>();
        for (InvStrategyCenterDTO strategyCenterDTO : afterStrategyList) {
            if (CollectionUtils.isNotEmpty(strategyCenterDTO.getSkuCodeList())) {
                afterSkuCodeList.addAll(strategyCenterDTO.getSkuCodeList());
            }
        }
        //获取所有优先级比当前维度高的策略
        List<String> greaterThanList = InvDimensionEnum.getGreaterThanList(dimension);
        List<InvStrategyCenterDTO> greaterThanCenterList = invStrategyCenterList.stream().filter(t -> greaterThanList.contains(t.getDimension())
                && Objects.equals(t.getStatus(), InvStrategyStatusEnum.ENABLED.getCode())).collect(Collectors.toList());
        List<String> greaterThanSkuCodeList = new ArrayList<>();
        for (InvStrategyCenterDTO strategyCenterDTO : greaterThanCenterList) {
            if (CollectionUtils.isNotEmpty(strategyCenterDTO.getSkuCodeList())) {
                greaterThanSkuCodeList.addAll(strategyCenterDTO.getSkuCodeList());
            }
        }
        //本次操作后所有启用策略覆盖的品 - 本次操作前所有启用策略覆盖的品，就是启用状态 维度变化的品
        List<String> copyAfterSkuCodeList = new ArrayList<>();
        copyAfterSkuCodeList.addAll(afterSkuCodeList);
        //本次操作前所有启用策略覆盖的品
        List<String> copyBeforeSkuCodeList = new ArrayList<>();
        copyBeforeSkuCodeList.addAll(beforeSkuCodeList);
        //本次操作后所有启用策略覆盖的品 - 本次操作前所有启用策略覆盖的品，就是启用状态 维度变化的品，维度有变化的品 - 比当前维度高的策略覆盖的品，再去和维度比当前维度低的启用中的策略做比较，就是优先级变动数据，优先级变高
        copyAfterSkuCodeList.removeAll(beforeSkuCodeList);
//        copyAfterSkuCodeList.removeAll(greaterThanSkuCodeList);
        //本次操作前所有启用策略覆盖的品 - 本次操作后所有启用策略覆盖的品，就是当前维度此次操作禁用的品，需要 - 高维度启用中策略覆盖的品，然后再去和低纬度策略启用中的品做比较，优先级从高变低
        copyBeforeSkuCodeList.removeAll(afterSkuCodeList);
        copyBeforeSkuCodeList.removeAll(greaterThanSkuCodeList);
        if (CollectionUtils.isEmpty(copyAfterSkuCodeList) && CollectionUtils.isEmpty(copyBeforeSkuCodeList)) {
            return null;
        }
        //获取维度有变化的品对应的所有rdc仓库
        List<String> querySkuCodeList = new ArrayList<>();
        querySkuCodeList.addAll(copyAfterSkuCodeList);
        querySkuCodeList.addAll(copyBeforeSkuCodeList);
        List<InvUnitRdcSkuDO> invUnitRdcSkuDOS = invUnitRdcSkuDao.selectBySkuCodes(querySkuCodeList);
        Map<String, List<InvUnitRdcSkuDO>> skuRdcMap = StreamUtils.group(invUnitRdcSkuDOS, InvUnitRdcSkuDO::getSkuCode);
        //通过sku获取sku所属策略
        Map<List<String>, InvStrategyCenterDTO> skuCodeStrategyMap = invStrategyCenterList.stream().filter(t -> Objects.equals(t.getDimension(), dimension)).collect(Collectors.toMap(t -> t.getSkuCodeList(), t -> t, (k1, k2) -> k1));
        //获取比当前维度低的策略覆盖的品仓
        String lessThanCode = InvDimensionEnum.BY_SKU.getCode();
        //最终返回的列表数据
        List<InvStrategyPriorityDTO> priorityList = new ArrayList<>();
        while (Objects.nonNull(lessThanCode)) {
            String finalLessThanCode = lessThanCode;
            List<InvStrategyCenterDTO> lessThanCenterList = invStrategyCenterList.stream().filter(t -> Objects.equals(finalLessThanCode, t.getDimension())
                    && Objects.equals(t.getStatus(), InvStrategyStatusEnum.ENABLED.getCode())).collect(Collectors.toList());
            lessThanCenterList.sort(Comparator.comparing(InvStrategyCenterDTO::getGmtModified).reversed());
            for (InvStrategyCenterDTO strategyCenterDTO : lessThanCenterList) {
                if (CollectionUtils.isEmpty(strategyCenterDTO.getSkuCodeList()) || Objects.equals(strategyCenterDTO.getDimension(), dimension)) {
                    continue;
                }
                for (String skuCode : strategyCenterDTO.getSkuCodeList()) {
                    if (!copyAfterSkuCodeList.contains(skuCode) && !copyBeforeSkuCodeList.contains(skuCode)) {
                        continue;
                    }
                    List<InvUnitRdcSkuDO> skuRdcList = skuRdcMap.get(skuCode);
                    InvStrategyCenterDTO currentDimensionStrategy = null;
                    for (List<String> skuCodes : skuCodeStrategyMap.keySet()) {
                        if (skuCodes.contains(skuCode)) {
                            currentDimensionStrategy = skuCodeStrategyMap.get(skuCodes);
                        }
                    }
                    if (copyAfterSkuCodeList.contains(skuCode)) {
                        priorityList.addAll(setHighStrategyPriorityDTO(skuRdcList, currentDimensionStrategy, strategyCenterDTO, dimension, skuCode));
                    }
                    if (copyBeforeSkuCodeList.contains(skuCode)) {
                        priorityList.addAll(setLowStrategyPriorityDTO(skuRdcList, currentDimensionStrategy, strategyCenterDTO, dimension, skuCode));
                    }
                    copyAfterSkuCodeList.remove(skuCode);
                }
            }
            lessThanCode = InvDimensionEnum.getLessThanList(finalLessThanCode);
        }
        if (CollectionUtils.isNotEmpty(priorityList)) {
            Map<String, Object> map = new HashMap<>();
            List<InvStrategyPriorityVO> convert = GeiCommonConvert.convert(priorityList, InvStrategyPriorityVO.class);
            MergeInfoHandler.merge(convert);
            MetaInfoWrapper metaInfoWrapper = MetaInfoWrapper.of(convert, MetaInfoCreator.create(InvStrategyPriorityVO.class, StrategyConstants.StrategyCenter.PRIORITY));
            map.put(StrategyConstants.StrategyCenter.META, metaInfoWrapper);
            map.put(StrategyConstants.StrategyCenter.REPEAT_CONFIRM,1);
            map.put(StrategyConstants.StrategyCenter.PRIORITY_CONFIRM, 1);
            map.put(StrategyConstants.StrategyCenter.ENABLE_LIST, request.getEnableList());
            map.put(StrategyConstants.StrategyCenter.TYPE, StrategyConstants.StrategyCenter.PRIORITY_CONFIRM);
            map.put(StrategyConstants.StrategyCenter.TOTAL, String.format("共有「%s」行品仓同时命中其他策略包，当同一品仓命中多个维度的不同策略时，将按照最细维度进行生效：ABC分类<一级<二级<四级<SKU。请确认启用吗？", convert.size()));
            return map;
        }
        return null;
    }

    private List<InvStrategyPriorityDTO> setHighStrategyPriorityDTO(List<InvUnitRdcSkuDO> skuRdcList, InvStrategyCenterDTO currentDimensionStrategy, InvStrategyCenterDTO strategyCenterDTO, String dimension, String skuCode) {
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        List<InvStrategyPriorityDTO> priorityList = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuRdcList)) {
            InvStrategyPriorityDTO strategyPriorityDTO = new InvStrategyPriorityDTO();
            strategyPriorityDTO.setSkuCode(skuCode);
            setPriorityDTO(strategyPriorityDTO, currentDimensionStrategy, strategyCenterDTO);
            if (Objects.nonNull(currentAccount)) {
                strategyPriorityDTO.setOperatorCode(currentAccount.getId());
                strategyPriorityDTO.setOperatorName(currentAccount.getName());
            }
            priorityList.add(strategyPriorityDTO);
            return  priorityList;
        }
        for (InvUnitRdcSkuDO invUnitRdcSkuDO : skuRdcList) {
            InvStrategyPriorityDTO strategyPriorityDTO = GeiCommonConvert.convert(invUnitRdcSkuDO, InvStrategyPriorityDTO.class);
            setPriorityDTO(strategyPriorityDTO, currentDimensionStrategy, strategyCenterDTO);
            if (Objects.nonNull(currentAccount)) {
                strategyPriorityDTO.setOperatorCode(currentAccount.getId());
                strategyPriorityDTO.setOperatorName(currentAccount.getName());
            }
            priorityList.add(strategyPriorityDTO);
        }
        return priorityList;
    }

    private void setPriorityDTO (InvStrategyPriorityDTO strategyPriorityDTO, InvStrategyCenterDTO currentDimensionStrategy, InvStrategyCenterDTO strategyCenterDTO) {
        if (InvDimensionEnum.getByCode(strategyCenterDTO.getDimension()).getSort() > InvDimensionEnum.getByCode(currentDimensionStrategy.getDimension()).getSort()) {
            strategyPriorityDTO.setCurrentInvStrategyId(currentDimensionStrategy.getId());
            strategyPriorityDTO.setCurrentInvStrategyName(currentDimensionStrategy.getName());
            strategyPriorityDTO.setCurrentDimension(currentDimensionStrategy.getDimension());
            strategyPriorityDTO.setCurrentDimensionName(InvDimensionEnum.getNameByCode(currentDimensionStrategy.getDimension()));
            strategyPriorityDTO.setConflictDimension(strategyCenterDTO.getDimension());
            strategyPriorityDTO.setConflictDimensionName(InvDimensionEnum.getNameByCode(strategyCenterDTO.getDimension()));
            strategyPriorityDTO.setConflictInvStrategyId(strategyCenterDTO.getId());
            strategyPriorityDTO.setConflictInvStrategyName(strategyCenterDTO.getName());
            strategyPriorityDTO.setConflictOperatorCode(strategyCenterDTO.getOperatorCode());
            strategyPriorityDTO.setConflictOperatorName(strategyCenterDTO.getOperatorName());
            strategyPriorityDTO.setConflictUpdateTime(strategyCenterDTO.getGmtModified());
            strategyPriorityDTO.setPriorityTakeEffect(strategyCenterDTO.getDimension());
            strategyPriorityDTO.setPriorityTakeEffectName(InvDimensionEnum.getNameByCode(strategyCenterDTO.getDimension()));
        } else {
            strategyPriorityDTO.setCurrentInvStrategyId(currentDimensionStrategy.getId());
            strategyPriorityDTO.setCurrentInvStrategyName(currentDimensionStrategy.getName());
            strategyPriorityDTO.setCurrentDimension(currentDimensionStrategy.getDimension());
            strategyPriorityDTO.setCurrentDimensionName(InvDimensionEnum.getNameByCode(currentDimensionStrategy.getDimension()));
            strategyPriorityDTO.setConflictDimension(strategyCenterDTO.getDimension());
            strategyPriorityDTO.setConflictDimensionName(InvDimensionEnum.getNameByCode(strategyCenterDTO.getDimension()));
            strategyPriorityDTO.setConflictInvStrategyId(strategyCenterDTO.getId());
            strategyPriorityDTO.setConflictInvStrategyName(strategyCenterDTO.getName());
            strategyPriorityDTO.setConflictOperatorCode(strategyCenterDTO.getOperatorCode());
            strategyPriorityDTO.setConflictOperatorName(strategyCenterDTO.getOperatorName());
            strategyPriorityDTO.setConflictUpdateTime(strategyCenterDTO.getGmtModified());
            strategyPriorityDTO.setPriorityTakeEffect(currentDimensionStrategy.getDimension());
            strategyPriorityDTO.setPriorityTakeEffectName(InvDimensionEnum.getNameByCode(currentDimensionStrategy.getDimension()));
        }
    }


    private List<InvStrategyPriorityDTO> setLowStrategyPriorityDTO(List<InvUnitRdcSkuDO> skuRdcList, InvStrategyCenterDTO strategyIdName, InvStrategyCenterDTO strategyCenterDTO, String dimension, String skuCode) {
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        List<InvStrategyPriorityDTO> priorityList = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuRdcList)) {
            InvStrategyPriorityDTO strategyPriorityDTO = new InvStrategyPriorityDTO();
            strategyPriorityDTO.setSkuCode(skuCode);
            strategyPriorityDTO.setCurrentInvStrategyId(strategyIdName.getId());
            strategyPriorityDTO.setCurrentInvStrategyName(strategyIdName.getName());
            strategyPriorityDTO.setCurrentDimension(strategyIdName.getDimension());
            strategyPriorityDTO.setCurrentDimensionName(InvDimensionEnum.getNameByCode(strategyIdName.getDimension()));
            strategyPriorityDTO.setConflictDimension(strategyCenterDTO.getDimension());
            strategyPriorityDTO.setConflictDimensionName(InvDimensionEnum.getNameByCode(strategyCenterDTO.getDimension()));
            strategyPriorityDTO.setConflictInvStrategyId(strategyCenterDTO.getId());
            strategyPriorityDTO.setConflictInvStrategyName(strategyCenterDTO.getName());
            strategyPriorityDTO.setConflictOperatorCode(strategyCenterDTO.getOperatorCode());
            strategyPriorityDTO.setConflictOperatorName(strategyCenterDTO.getOperatorName());
            strategyPriorityDTO.setConflictUpdateTime(strategyCenterDTO.getGmtModified());
            strategyPriorityDTO.setPriorityTakeEffect(strategyCenterDTO.getDimension());
            strategyPriorityDTO.setPriorityTakeEffectName(InvDimensionEnum.getNameByCode(strategyCenterDTO.getDimension()));
            if (Objects.nonNull(currentAccount)) {
                strategyPriorityDTO.setOperatorCode(currentAccount.getId());
                strategyPriorityDTO.setOperatorName(currentAccount.getName());
            }
            priorityList.add(strategyPriorityDTO);
            return  priorityList;
        }
        for (InvUnitRdcSkuDO invUnitRdcSkuDO : skuRdcList) {
            InvStrategyPriorityDTO strategyPriorityDTO = GeiCommonConvert.convert(invUnitRdcSkuDO, InvStrategyPriorityDTO.class);
            strategyPriorityDTO.setCurrentInvStrategyId(strategyIdName.getId());
            strategyPriorityDTO.setCurrentInvStrategyName(strategyIdName.getName());
            strategyPriorityDTO.setCurrentDimension(strategyIdName.getDimension());
            strategyPriorityDTO.setCurrentDimensionName(InvDimensionEnum.getNameByCode(strategyIdName.getDimension()));
            strategyPriorityDTO.setConflictDimension(strategyCenterDTO.getDimension());
            strategyPriorityDTO.setConflictDimensionName(InvDimensionEnum.getNameByCode(strategyCenterDTO.getDimension()));
            strategyPriorityDTO.setConflictInvStrategyId(strategyCenterDTO.getId());
            strategyPriorityDTO.setConflictInvStrategyName(strategyCenterDTO.getName());
            strategyPriorityDTO.setConflictOperatorCode(strategyCenterDTO.getOperatorCode());
            strategyPriorityDTO.setConflictOperatorName(strategyCenterDTO.getOperatorName());
            strategyPriorityDTO.setConflictUpdateTime(strategyCenterDTO.getGmtModified());
            strategyPriorityDTO.setPriorityTakeEffect(strategyCenterDTO.getDimension());
            strategyPriorityDTO.setPriorityTakeEffectName(InvDimensionEnum.getNameByCode(strategyCenterDTO.getDimension()));
            if (Objects.nonNull(currentAccount)) {
                strategyPriorityDTO.setOperatorCode(currentAccount.getId());
                strategyPriorityDTO.setOperatorName(currentAccount.getName());
            }
            priorityList.add(strategyPriorityDTO);
        }
        return priorityList;
    }


    private Map<String, Object> batchDisable(InvBatchUpdateStatusRequest request, List<InvStrategyCenterDTO> invStrategyCenterList) {
        //获取当前选中策略覆盖产品，只查询启用状态的策略
        List<InvStrategyCenterDTO> requestCenterList = invStrategyCenterList.stream().filter(t -> request.getIds().contains(t.getId())
                && Objects.equals(t.getStatus(), InvStrategyStatusEnum.ENABLED.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(requestCenterList)) {
            return null;
        }
        String dimension = requestCenterList.get(0).getDimension();
        //requestCenterList覆盖的所有品 - greaterThanCenterList覆盖的所有品，就是维度变化的品，需要和lessThanCenterList覆盖的品做优先级比较，优先级由requestCenterList变为lessThanCenterList
        //requestCenterList和lessThanCenterList比较时，需要根据lessThanCenterList优先级，一层一层比较
        //获取比当前维度高的策略覆盖的品仓
        List<String> greaterThanList = InvDimensionEnum.getGreaterThanList(dimension);
        List<InvStrategyCenterDTO> greaterThanCenterList = invStrategyCenterList.stream().filter(t -> greaterThanList.contains(t.getDimension())
                && Objects.equals(t.getStatus(), InvStrategyStatusEnum.ENABLED.getCode())).collect(Collectors.toList());
        List<String> skuCodeList = new ArrayList<>();
        for (InvStrategyCenterDTO strategyCenterDTO : requestCenterList) {
            if (CollectionUtils.isNotEmpty(strategyCenterDTO.getSkuCodeList())) {
                skuCodeList.addAll(strategyCenterDTO.getSkuCodeList());
            }
        }
        List<String> greaterThanSkuCodeList = new ArrayList<>();
        for (InvStrategyCenterDTO strategyCenterDTO : greaterThanCenterList) {
            if (CollectionUtils.isNotEmpty(strategyCenterDTO.getSkuCodeList())) {
                greaterThanSkuCodeList.addAll(strategyCenterDTO.getSkuCodeList());
            }
        }
        skuCodeList.removeAll(greaterThanSkuCodeList);
        //获取维度有变化的品对应的所有rdc仓库
        List<InvUnitRdcSkuDO> invUnitRdcSkuDOS = invUnitRdcSkuDao.selectBySkuCodes(skuCodeList);
        Map<String, List<InvUnitRdcSkuDO>> skuRdcMap = StreamUtils.group(invUnitRdcSkuDOS, InvUnitRdcSkuDO::getSkuCode);
        //通过sku获取sku所属策略
        Map<List<String>, InvStrategyCenterDTO> skuCodeStrategyMap = requestCenterList.stream().filter(t -> Objects.equals(t.getDimension(),dimension)).collect(Collectors.toMap(t -> t.getSkuCodeList(), t -> t, (k1, k2) -> k1));
        //最终保存的优先级生效数据
        List<InvStrategyPriorityDTO> priorityList = new ArrayList<>();
        //获取比当前维度低的策略覆盖的品仓
        String lessThanCode = InvDimensionEnum.getLessThanList(dimension);
        while (Objects.nonNull(lessThanCode)) {
            String finalLessThanCode = lessThanCode;
            List<InvStrategyCenterDTO> lessThanCenterList = invStrategyCenterList.stream().filter(t -> Objects.equals(finalLessThanCode, t.getDimension())
                    && Objects.equals(t.getStatus(), InvStrategyStatusEnum.ENABLED.getCode())).collect(Collectors.toList());
            lessThanCenterList.sort(Comparator.comparing(InvStrategyCenterDTO::getGmtModified).reversed());
            for (InvStrategyCenterDTO strategyCenterDTO : lessThanCenterList) {
                if (CollectionUtils.isEmpty(strategyCenterDTO.getSkuCodeList())) {
                    continue;
                }
                for (String skuCode : strategyCenterDTO.getSkuCodeList()) {
                    if (!skuCodeList.contains(skuCode)) {
                        continue;
                    }
                    List<InvUnitRdcSkuDO> skuRdcList = skuRdcMap.get(skuCode);
                    InvStrategyCenterDTO strategyIdName = null;
                    for (List<String> skuCodes : skuCodeStrategyMap.keySet()) {
                        if (skuCodes.contains(skuCode)) {
                            strategyIdName = skuCodeStrategyMap.get(skuCodes);
                        }
                    }
                    priorityList.addAll(setLowStrategyPriorityDTO(skuRdcList, strategyIdName, strategyCenterDTO, dimension, skuCode));
                    skuCodeList.remove(skuCode);
                }
            }
            lessThanCode = InvDimensionEnum.getLessThanList(finalLessThanCode);
        }
        if (CollectionUtils.isNotEmpty(priorityList)) {
            List<InvStrategyPriorityDO> convert = GeiCommonConvert.convert(GeiCommonConvert.convert(priorityList, InvStrategyPriorityVO.class), InvStrategyPriorityDO.class);
            if (convert.size() > 500) {
                List<CompletableFuture<Object>> tasks = new ArrayList<>();
                int batchSize = 500;
                int totalSize = convert.size();
                for (int i = 0; i < totalSize; i += batchSize) {
                    int endIndex = Math.min(i+batchSize, totalSize);
                    List<InvStrategyPriorityDO> batch = convert.subList(i, endIndex);
                    tasks.add(
                            CompletableFuture.supplyAsync(() -> {
                                invStrategyPriorityDao.batchInsert(batch);
                                return null;
                            }, commonIOThreadPool).exceptionally(e -> {
                                log.error("insert data error: ", e);
                                return null;
                            })
                    );
                }
                for (CompletableFuture<Object> task : tasks) {
                    task.join();
                }
            } else {
                invStrategyPriorityDao.batchInsert(convert);
            }
        }
        //修改策略状态
        request.setEnableList(null);
        invStrategyCenterDao.updateDisableStatus(JsonUtils.toMap(request));
        updateDisable(request, invStrategyCenterList);
        return null;
    }

    public void updateDisable(InvBatchUpdateStatusRequest request, List<InvStrategyCenterDTO> invStrategyCenterList) {
        List<InvStrategyCenterDTO> dimensionCenterList = invStrategyCenterList.stream().filter(t ->
                Objects.equals(t.getDimension(), request.getDimension())).collect(Collectors.toList());
        List<InvStrategyUpdateRequest> updateRequests = new ArrayList<>();
        for (InvStrategyCenterDTO centerDTO : dimensionCenterList) {
            if (request.getIds().contains(centerDTO.getId()) && Objects.equals(InvStrategyStatusEnum.ENABLED.getCode(), centerDTO.getStatus())) {
                updateRequests.add(InvStrategyUpdateRequest.of(centerDTO.getName(), InvStrategyStatusEnum.DISABLE.getName(), InvStrategyStatusEnum.ENABLED.getName()));
            }
        }
        invStrategyCenterManager.update(updateRequests);
    }


    @Override
    public InvStrategyCenterDTO queryById(Long id) {
        List<InvStrategyCenterDO> invStrategyCenterList = invStrategyCenterDao.selectByIds(Collections.singletonList(id));
        InvStrategyCenterDO invStrategyCenterDO = invStrategyCenterList.get(0);
        InvStrategyCenterDTO convert = GeiCommonConvert.convert(invStrategyCenterDO, InvStrategyCenterDTO.class);
        if (StringUtils.isNotEmpty(invStrategyCenterDO.getTag())) {
            convert.setTag(Arrays.asList(invStrategyCenterDO.getTag().replaceAll("[\\[\\]]", "").replaceAll(" ", "").split(StringConstants.COMMA)));
        }
        if (StringUtils.isNotEmpty(invStrategyCenterDO.getSku())) {
            convert.setSku(Arrays.asList(invStrategyCenterDO.getSku().replaceAll("[\\[\\]]", "").replaceAll(" ", "").split(StringConstants.COMMA)));
        }
        if (Objects.nonNull(invStrategyCenterDO.getSelectAll())) {
            convert.setSku(Arrays.asList(StrategyConstants.StrategyCenter.SELECT_ALL));
        }
        return convert;
    }

    @Override
    public PageMetaInfoWrapper selectSkuRdcCode(InvUpsertStrategyRequest request) {
        Assert.isTrue(StringUtils.isNotBlank(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_NULL.getError());
        Assert.isTrue(StringUtils.isNotBlank(request.getDimension()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.DIMENSION_IS_NOT_NULL.getError());
        Assert.isTrue(CollectionUtils.isNotEmpty(request.getCategory()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.CATEGORY_IS_NOT_NULL.getError());
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_SKU.getCode()) && request.getCategory().contains(StrategyConstants.StrategyCenter.SELECT_ALL)) {
            request.setCategory(null);
        }
        List<InvSkuRdcDTO> invSkuRdcDTOS = invSkuReplnTypeDao.selectSkuRdcCode(JsonUtils.toMap(request));
        for (InvSkuRdcDTO invSkuRdcDTO : invSkuRdcDTOS) {
            invSkuRdcDTO.setAbcTypeName(SkuLevelEnum.getNameByCode(invSkuRdcDTO.getAbcType()));
        }
        return setSkuRdcList(invSkuRdcDTOS, request);
    }

    private PageMetaInfoWrapper setSkuRdcList(List<InvSkuRdcDTO> invSkuRdcDTOS, InvUpsertStrategyRequest request) {
        List<MetaInfo> metaInfos = new ArrayList<>();
        if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_ABC.getCode())) {
            metaInfos = MetaInfoCreator.create(InvSkuRdcVO.class, InvDimensionEnum.BY_ABC.getCode());
        } else if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_ONE.getCode())) {
            metaInfos = MetaInfoCreator.create(InvSkuRdcVO.class, InvDimensionEnum.BY_ONE.getCode());
        } else if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_TWO.getCode())) {
            metaInfos = MetaInfoCreator.create(InvSkuRdcVO.class, InvDimensionEnum.BY_TWO.getCode());
        } else if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_FOUR.getCode())) {
            metaInfos = MetaInfoCreator.create(InvSkuRdcVO.class, InvDimensionEnum.BY_FOUR.getCode());
        } else if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_SKU.getCode())) {
            metaInfos = MetaInfoCreator.create(InvSkuRdcVO.class, InvDimensionEnum.BY_SKU.getCode());
        }
        return PageMetaInfoWrapper.of(request, invSkuReplnTypeDao.selectSkuRdcCodeCount(request), invSkuRdcDTOS, metaInfos);
    }

    @Override
    public boolean createParameters(InvUpsertStrategyRequest request) {
        Assert.isTrue(StringUtils.isNotBlank(request.getType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.PARAM_IS_NOT_NULL.getError());
        if (Objects.equals(request.getType(), StrategyConstants.StrategyCenter.BY_STRATEGY_ID)) {
            Assert.isTrue(Objects.nonNull(request.getId()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
            return createParametersByStrategyId(request);
        } else if (Objects.equals(request.getType(), StrategyConstants.StrategyCenter.BY_DIMENSION)) {
            Assert.isTrue(StringUtils.isNotBlank(request.getToken()) || Objects.nonNull(request.getId()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
            return createParametersByDimension(request);
        }
        Assert.isTrue(false, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.TYPE_IS_NOT_EXISTENT.getError());
        return false;
    }

    private boolean createParametersByStrategyId(InvUpsertStrategyRequest request) {
        Long strategyId = request.getId();
        SnowflakeIdWorker snowflakeIdWorker = new SnowflakeIdWorker(0, 0);
        InvUpsertStrategyRequest invUpsertStrategyRequest = new InvUpsertStrategyRequest();
        invUpsertStrategyRequest.setPaging(false);
        invUpsertStrategyRequest.setStatus(InvStrategyStatusEnum.ENABLED.getCode());
        invUpsertStrategyRequest.setStrategyId(strategyId);
        if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOB.getCode())) {
            List<InvStrategyParametersTobDO> list = invStrategyParametersTobDao.selectByInvStrategyId(JsonUtils.toMap(invUpsertStrategyRequest));
            Assert.isTrue(CollectionUtils.isNotEmpty(list), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_PARAMETERS_IS_NULL.getError());
            for (InvStrategyParametersTobDO invStrategyParametersTobDO : list) {
                invStrategyParametersTobDO.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
                invStrategyParametersTobDO.setId(snowflakeIdWorker.nextId());
            }
            invStrategyParametersTobDao.deleteByStrategyIdAndStatus(strategyId, InvStrategyStatusEnum.DISABLE.getCode());
            if (list.size() > 500) {
                List<CompletableFuture<Object>> tasks = new ArrayList<>();
                int batchSize = 500;
                int totalSize = list.size();
                for (int i = 0; i < totalSize; i += batchSize) {
                    int endIndex = Math.min(i+batchSize, totalSize);
                    List<InvStrategyParametersTobDO> batch = list.subList(i, endIndex);
                    tasks.add(
                        CompletableFuture.supplyAsync(() -> {
                            invStrategyParametersTobDao.batchInsert(batch);
                            return null;
                        }, commonIOThreadPool).exceptionally(e -> {
                            log.error("insert data error: ", e);
                            return null;
                        })
                    );
                }
                for (CompletableFuture<Object> task : tasks) {
                    task.join();
                }
            } else {
                invStrategyParametersTobDao.batchInsert(list);
            }
        } else if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOC.getCode())) {
            List<InvStrategyParametersTocDO> list = invStrategyParametersTocDao.selectByInvStrategyId(JsonUtils.toMap(invUpsertStrategyRequest));
            Assert.isTrue(CollectionUtils.isNotEmpty(list), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_PARAMETERS_IS_NULL.getError());
            for (InvStrategyParametersTocDO invStrategyParametersTobDO : list) {
                invStrategyParametersTobDO.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
                invStrategyParametersTobDO.setId(snowflakeIdWorker.nextId());
            }
            invStrategyParametersTocDao.deleteByStrategyIdAndStatus(strategyId, InvStrategyStatusEnum.DISABLE.getCode());
            if (list.size() > 500) {
                List<CompletableFuture<Object>> tasks = new ArrayList<>();
                int batchSize = 500;
                int totalSize = list.size();
                for (int i = 0; i < totalSize; i += batchSize) {
                    int endIndex = Math.min(i+batchSize, totalSize);
                    List<InvStrategyParametersTocDO> batch = list.subList(i, endIndex);
                    tasks.add(
                            CompletableFuture.supplyAsync(() -> {
                                invStrategyParametersTocDao.batchInsert(batch);
                                return null;
                            }, commonIOThreadPool).exceptionally(e -> {
                                log.error("insert data error: ", e);
                                return null;
                            })
                    );
                }
                for (CompletableFuture<Object> task : tasks) {
                    task.join();
                }
            } else {
                invStrategyParametersTocDao.batchInsert(list);
            }
        } else {
            Assert.isTrue(false, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_EXISTENT.getError());
        }
        return true;
    }

    private boolean createParametersByDimension(InvUpsertStrategyRequest request) {
        Assert.isTrue(StringUtils.isNotBlank(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_NULL.getError());
        Assert.isTrue(StringUtils.isNotBlank(request.getDimension()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.DIMENSION_IS_NOT_NULL.getError());
        Assert.isTrue(ReplnTypeEnum.codeValues().contains(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_EXISTENT.getError());
        Assert.isTrue(InvDimensionEnum.codeValues().contains(request.getDimension()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.DIMENSION_IS_NOT_EXISTENT.getError());
        //如果是按产品维度，查询sku-rdc仓库关联关系表，如果非按产品维度，获取类别*rdc仓库乘积
        if (Objects.equals(InvDimensionEnum.BY_SKU.getCode(), request.getDimension())) {
            // TODO 产品维度关联查询安全库存、目标库存
            if (request.getCategory().contains(StrategyConstants.StrategyCenter.SELECT_ALL)) {
                request.setCategory(null);
            }
            List<InvSkuRdcDTO> invSkuRdcDTOS = invSkuReplnTypeDao.selectSkuRdcCode(JsonUtils.toMap(request));
            Assert.isTrue(CollectionUtils.isNotEmpty(invSkuRdcDTOS), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.DATA_IS_NULL.getError());
            saveStrategyParameters(invSkuRdcDTOS, request);
        } else {
            List<InvSkuRdcDTO> invSkuRdcDTOList = invSkuReplnTypeDao.selectCategory(JsonUtils.toMap(request));
            Assert.isTrue(CollectionUtils.isNotEmpty(invSkuRdcDTOList), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.DATA_IS_NULL.getError());
            saveStrategyParameters(invSkuRdcDTOList, request);
        }
        return true;
    }

    private void saveStrategyParameters(List<InvSkuRdcDTO> invSkuRdcDTOList, InvUpsertStrategyRequest request) {
        Long strategyId = request.getId();
        //新增不会传入Id,需要通过token解密获取
        if (Objects.isNull(request.getId())) {
            Assert.isTrue(Objects.nonNull(request.getToken()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
            strategyId = getId(request);
        }
        Assert.isTrue(Objects.nonNull(strategyId), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
        SnowflakeIdWorker snowflakeIdWorker = new SnowflakeIdWorker(0, 0);
        InvUpsertStrategyRequest invUpsertStrategyRequest = new InvUpsertStrategyRequest();
        invUpsertStrategyRequest.setPaging(false);
        invUpsertStrategyRequest.setStrategyId(strategyId);
        invUpsertStrategyRequest.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
        if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOB.getCode())) {
            List<InvStrategyParametersTobDO> invStrategyParametersTobDOS = invStrategyParametersTobDao.selectByInvStrategyId(JsonUtils.toMap(invUpsertStrategyRequest));
            Map<String, InvStrategyParametersTobDO> parametersTobDOMap = null;
            if (CollectionUtils.isNotEmpty(invStrategyParametersTobDOS)) {
                parametersTobDOMap = StreamUtils.singleGroup(invStrategyParametersTobDOS, t -> getKey(t));
            }
            List<InvStrategyParametersTobDO> list = new ArrayList<>();
            for (InvSkuRdcDTO invSkuRdcDTO : invSkuRdcDTOList) {
                InvStrategyParametersTobDO convert = setParametersTob(invSkuRdcDTO, request.getDimension());
                convert.setRdcCode(invSkuRdcDTO.getRdcCode());
                convert.setRdcName(invSkuRdcDTO.getRdcName());
                convert.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
                convert.setId(snowflakeIdWorker.nextId());
                convert.setInvStrategyId(strategyId);
                convert.setCollocationMethod(InvCollocationMethodEnum.BY_DAY.getCode());
                convert.setLimitWaterLevel(Objects.isNull(invSkuRdcDTO.getSysTargetDays()) ? 0 : invSkuRdcDTO.getSysTargetDays());
                convert.setRopCnt(Objects.isNull(invSkuRdcDTO.getSysTargetDays()) ? 0 : invSkuRdcDTO.getSysTargetDays());
                convert.setOosPoint(Objects.isNull(invSkuRdcDTO.getSysTargetDays()) ? 0 : invSkuRdcDTO.getSysTargetDays());
                convert.setTarget(Objects.isNull(invSkuRdcDTO.getSysTargetDays()) ? 0 : invSkuRdcDTO.getSysTargetDays());
                list.add(convert);
                if (Objects.isNull(parametersTobDOMap)) {
                    continue;
                }
                String key = getKey(convert);
                InvStrategyParametersTobDO tobDO = parametersTobDOMap.get(key);
                if (Objects.nonNull(tobDO)) {
                    convert.setCollocationMethod(tobDO.getCollocationMethod());
                    convert.setLimitWaterLevel(tobDO.getLimitWaterLevel());
                    convert.setRopCnt(tobDO.getRopCnt());
                    convert.setOosPoint(tobDO.getOosPoint());
                    convert.setTarget(tobDO.getTarget());
                }
            }
            invStrategyParametersTobDao.deleteByStrategyIdAndStatus(strategyId, InvStrategyStatusEnum.DISABLE.getCode());
            if (list.size() > 500) {
                List<CompletableFuture<Object>> tasks = new ArrayList<>();
                int batchSize = 500;
                int totalSize = list.size();
                for (int i = 0; i < totalSize; i += batchSize) {
                    int endIndex = Math.min(i+batchSize, totalSize);
                    List<InvStrategyParametersTobDO> batch = list.subList(i, endIndex);
                    tasks.add(
                            CompletableFuture.supplyAsync(() -> {
                                invStrategyParametersTobDao.batchInsert(batch);
                                return null;
                            }, commonIOThreadPool).exceptionally(e -> {
                                log.error("insert data error: ", e);
                                return null;
                            })
                    );
                }
                for (CompletableFuture<Object> task : tasks) {
                    task.join();
                }
            } else {
                invStrategyParametersTobDao.batchInsert(list);
            }
        } else {
            List<InvStrategyParametersTocDO> invStrategyParametersTocDOS = invStrategyParametersTocDao.selectByInvStrategyId(JsonUtils.toMap(invUpsertStrategyRequest));
            Map<String, InvStrategyParametersTocDO> parametersTocDOMap = null;
            if (CollectionUtils.isNotEmpty(invStrategyParametersTocDOS)) {
                parametersTocDOMap = StreamUtils.singleGroup(invStrategyParametersTocDOS, t -> getKey(t));
            }
            List<InvStrategyParametersTocDO> list = new ArrayList<>();
            for (InvSkuRdcDTO invSkuRdcDTO : invSkuRdcDTOList) {
                InvStrategyParametersTocDO convert = setParametersToc(invSkuRdcDTO, request.getDimension());
                convert.setRdcCode(invSkuRdcDTO.getRdcCode());
                convert.setRdcName(invSkuRdcDTO.getRdcName());
                convert.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
                convert.setId(snowflakeIdWorker.nextId());
                convert.setInvStrategyId(strategyId);
                convert.setCollocationMethod(InvCollocationMethodEnum.BY_DAY.getCode());
                convert.setX1(0);
                convert.setX2(0);
                convert.setTarget(invSkuRdcDTO.getSysTargetDays());
                convert.setSafety(invSkuRdcDTO.getSysSafetyDays());
                list.add(convert);
                if (Objects.isNull(parametersTocDOMap)) {
                    continue;
                }
                String key = getKey(convert);
                InvStrategyParametersTocDO tocDO = parametersTocDOMap.get(key);
                if (Objects.nonNull(tocDO)) {
                    convert.setCollocationMethod(tocDO.getCollocationMethod());
                    convert.setX1(tocDO.getX1());
                    convert.setX2(tocDO.getX2());
                    convert.setTarget(tocDO.getTarget());
                    convert.setSafety(tocDO.getSafety());
                }
            }
            invStrategyParametersTocDao.deleteByStrategyIdAndStatus(strategyId, InvStrategyStatusEnum.DISABLE.getCode());
            if (list.size() > 500) {
                List<CompletableFuture<Object>> tasks = new ArrayList<>();
                int batchSize = 500;
                int totalSize = list.size();
                for (int i = 0; i < totalSize; i += batchSize) {
                    int endIndex = Math.min(i+batchSize, totalSize);
                    List<InvStrategyParametersTocDO> batch = list.subList(i, endIndex);
                    tasks.add(
                            CompletableFuture.supplyAsync(() -> {
                                invStrategyParametersTocDao.batchInsert(batch);
                                return null;
                            }, commonIOThreadPool).exceptionally(e -> {
                                log.error("insert data error: ", e);
                                return null;
                            })
                    );
                }
                for (CompletableFuture<Object> task : tasks) {
                    task.join();
                }
            } else {
                invStrategyParametersTocDao.batchInsert(list);
            }
        }
    }

    private String getKey(InvStrategyParametersTobDO tobDO) {
        return String.format("%s_%s_%s_%s_%s_%s", getItem(tobDO.getAbcType()),
                getItem(tobDO.getLv1CategoryCode()),
                getItem(tobDO.getLv2CategoryCode()),
                getItem(tobDO.getLv4CategoryCode()),
                getItem(tobDO.getSkuCode()),
                getItem(tobDO.getRdcCode())
        );
    }

    private String getKey(InvStrategyParametersTocDO tocDO) {
        return String.format("%s_%s_%s_%s_%s_%s", getItem(tocDO.getAbcType()),
                getItem(tocDO.getLv1CategoryCode()),
                getItem(tocDO.getLv2CategoryCode()),
                getItem(tocDO.getLv4CategoryCode()),
                getItem(tocDO.getSkuCode()),
                getItem(tocDO.getRdcCode())
        );
    }

    private String getItem(String item) {
        if (cn.aliyun.ryytn.common.utils.string.StringUtils.isBlank(item)) {
            return StringConstants.DASHED;
        }
        return item;
    }

    private InvStrategyParametersTobDO setParametersTob(InvSkuRdcDTO invSkuRdcDTO, String dimension) {
        InvStrategyParametersTobDO invStrategyParametersTobDO = new InvStrategyParametersTobDO();
        if (Objects.equals(dimension, InvDimensionEnum.BY_ABC.getCode())) {
            invStrategyParametersTobDO.setAbcType(invSkuRdcDTO.getAbcType());
        } else if (Objects.equals(dimension, InvDimensionEnum.BY_ONE.getCode())) {
            invStrategyParametersTobDO.setLv1CategoryCode(invSkuRdcDTO.getLv1CategoryCode());
            invStrategyParametersTobDO.setLv1CategoryName(invSkuRdcDTO.getLv1CategoryName());
        } else if (Objects.equals(dimension, InvDimensionEnum.BY_TWO.getCode())) {
            invStrategyParametersTobDO.setLv2CategoryCode(invSkuRdcDTO.getLv2CategoryCode());
            invStrategyParametersTobDO.setLv2CategoryName(invSkuRdcDTO.getLv2CategoryName());
        } else if (Objects.equals(dimension, InvDimensionEnum.BY_FOUR.getCode())) {
            invStrategyParametersTobDO.setLv4CategoryCode(invSkuRdcDTO.getLv4CategoryCode());
            invStrategyParametersTobDO.setLv4CategoryName(invSkuRdcDTO.getLv4CategoryName());
        } else if (Objects.equals(dimension, InvDimensionEnum.BY_SKU.getCode())) {
            invStrategyParametersTobDO.setSkuCode(invSkuRdcDTO.getSkuCode());
            invStrategyParametersTobDO.setSkuName(invSkuRdcDTO.getSkuName());
        }
        return invStrategyParametersTobDO;
    }

    private InvStrategyParametersTocDO setParametersToc(InvSkuRdcDTO invSkuRdcDTO, String dimension) {
        InvStrategyParametersTocDO invStrategyParametersTocDO = new InvStrategyParametersTocDO();
        if (Objects.equals(dimension, InvDimensionEnum.BY_ABC.getCode())) {
            invStrategyParametersTocDO.setAbcType(invSkuRdcDTO.getAbcType());
        } else if (Objects.equals(dimension, InvDimensionEnum.BY_ONE.getCode())) {
            invStrategyParametersTocDO.setLv1CategoryCode(invSkuRdcDTO.getLv1CategoryCode());
            invStrategyParametersTocDO.setLv1CategoryName(invSkuRdcDTO.getLv1CategoryName());
        } else if (Objects.equals(dimension, InvDimensionEnum.BY_TWO.getCode())) {
            invStrategyParametersTocDO.setLv2CategoryCode(invSkuRdcDTO.getLv2CategoryCode());
            invStrategyParametersTocDO.setLv2CategoryName(invSkuRdcDTO.getLv2CategoryName());
        } else if (Objects.equals(dimension, InvDimensionEnum.BY_FOUR.getCode())) {
            invStrategyParametersTocDO.setLv4CategoryCode(invSkuRdcDTO.getLv4CategoryCode());
            invStrategyParametersTocDO.setLv4CategoryName(invSkuRdcDTO.getLv4CategoryName());
        } else if (Objects.equals(dimension, InvDimensionEnum.BY_SKU.getCode())) {
            invStrategyParametersTocDO.setSkuCode(invSkuRdcDTO.getSkuCode());
            invStrategyParametersTocDO.setSkuName(invSkuRdcDTO.getSkuName());
        }
        return invStrategyParametersTocDO;
    }
    
    @Override
    public PageMetaInfoWrapper selectParameters(InvUpsertStrategyRequest request) {
        Assert.isTrue(Objects.nonNull(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_NULL.getError());
        Assert.isTrue(ReplnTypeEnum.codeValues().contains(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_EXISTENT.getError());
        Assert.isTrue(StringUtils.isNotBlank(request.getDimension()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.DIMENSION_IS_NOT_NULL.getError());
        Assert.isTrue(InvDimensionEnum.codeValues().contains(request.getDimension()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.DIMENSION_IS_NOT_EXISTENT.getError());
        if (Objects.nonNull(request.getSelectMeta())) {
            List<MetaInfo> metaInfos = setMetaInfo(request);
            return PageMetaInfoWrapper.of(MetaInfoWrapper.of(Collections.emptyList(), metaInfos));
        }
        Long strategyId = null;
        if (Objects.nonNull(request.getId())) {
            strategyId = request.getId();
        } else {
            Assert.isTrue(Objects.nonNull(request.getToken()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
            strategyId = getId(request);
        }
        Assert.isTrue(Objects.nonNull(strategyId), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        request.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
        request.setStrategyId(strategyId);
        if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOB.getCode())) {
            List<InvStrategyParametersTobDO> invStrategyParametersTobDOS = invStrategyParametersTobDao.selectByInvStrategyId(JsonUtils.toMap(request));
            List<InvStrategyParametersTobVO> convert = GeiCommonConvert.convert(invStrategyParametersTobDOS, InvStrategyParametersTobVO.class);
            for (InvStrategyParametersTobVO vo : convert) {
                vo.setAbcTypeName(SkuLevelEnum.getNameByCode(vo.getAbcType()));
                vo.setCollocationMethodName(InvCollocationMethodEnum.getNameByCode(vo.getCollocationMethod()));
            }
            List<MetaInfo> metaInfos = setMetaInfo(request);
            return PageMetaInfoWrapper.of(request, invStrategyParametersTobDao.selectCount(JsonUtils.toMap(request)), convert, metaInfos);
        } else if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOC.getCode())) {
            List<InvStrategyParametersTocDO> invStrategyParametersTocDOS = invStrategyParametersTocDao.selectByInvStrategyId(JsonUtils.toMap(request));
            List<InvStrategyParametersTocVO> convert = GeiCommonConvert.convert(invStrategyParametersTocDOS, InvStrategyParametersTocVO.class);
            for (InvStrategyParametersTocVO vo : convert) {
                vo.setAbcTypeName(SkuLevelEnum.getNameByCode(vo.getAbcType()));
                vo.setCollocationMethodName(InvCollocationMethodEnum.getNameByCode(vo.getCollocationMethod()));
            }
            List<MetaInfo> metaInfos = setMetaInfo(request);
            return PageMetaInfoWrapper.of(request, invStrategyParametersTocDao.selectCount(JsonUtils.toMap(request)), convert, metaInfos);
        }
        return null;
    }

    private List<MetaInfo> setMetaInfo(InvUpsertStrategyRequest request) {
        Class<?> clazz;
        if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOB.getCode())) {
            clazz = InvStrategyParametersTobVO.class;
        } else {
            clazz = InvStrategyParametersTocVO.class;
        }
        if (!Objects.equals(request.getImportFlag(), "1")) {
            request.setImportFlag("0");
        }
        List<MetaInfo> metaInfos = new ArrayList<>();
        if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_ABC.getCode())) {
            metaInfos = MetaInfoCreator.create(clazz, InvDimensionEnum.BY_ABC.getCode(), request.getImportFlag());
        } else if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_ONE.getCode())) {
            metaInfos = MetaInfoCreator.create(clazz, InvDimensionEnum.BY_ONE.getCode(), request.getImportFlag());
        } else if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_TWO.getCode())) {
            metaInfos = MetaInfoCreator.create(clazz, InvDimensionEnum.BY_TWO.getCode(), request.getImportFlag());
        } else if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_FOUR.getCode())) {
            metaInfos = MetaInfoCreator.create(clazz, InvDimensionEnum.BY_FOUR.getCode(), request.getImportFlag());
        } else if (Objects.equals(request.getDimension(), InvDimensionEnum.BY_SKU.getCode())) {
            metaInfos = MetaInfoCreator.create(clazz, InvDimensionEnum.BY_SKU.getCode(), request.getImportFlag());
        }
        return metaInfos;
    }

    @Override
    public boolean updateParameters(List<InvParametersRequest> requests) {
        validateData(requests);
        String replnType = requests.get(0).getReplnType();
        if (Objects.equals(replnType, ReplnTypeEnum.TOB.getCode())) {
            invStrategyParametersTobDao.batchUpdate(requests);
        } else {
            invStrategyParametersTocDao.batchUpdate(requests);
        }
        return true;
    }

    private void validateData(List<InvParametersRequest> requests) {
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        for (InvParametersRequest request : requests) {
            Assert.isTrue(Objects.nonNull(request.getId()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_PARAMETERS_ID_IS_NULL.getError());
            if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOB.getCode())) {
                Assert.isTrue(Objects.nonNull(request.getLimitWaterLevel()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.LIMIT_IS_NULL.getError());
                Assert.isTrue(Objects.nonNull(request.getTarget()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.TARGET_IS_NULL.getError());
                Assert.isTrue(Objects.nonNull(request.getRopCnt()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.ROP_CNT_IS_NULL.getError());
                Assert.isTrue(Objects.nonNull(request.getOosPoint()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.OOS_POINT_IS_NULL.getError());
                Assert.isTrue(request.getLimitWaterLevel() >= 0, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.LIMIT_RANGE_ERROR.getError());
                Assert.isTrue(request.getTarget() >= 0, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.TARGET_RANGE_ERROR.getError());
                Assert.isTrue(request.getRopCnt() >= 0, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.ROP_CNT_RANGE_ERROR.getError());
                Assert.isTrue(request.getOosPoint() >= 0, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.OOS_POINT_RANGE_ERROR.getError());
                Assert.isTrue(request.getLimitWaterLevel() >= request.getTarget() && request.getTarget() >= request.getRopCnt()
                        && request.getRopCnt() >= request.getOosPoint(), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.OOS_POINT_RANGE_ERROR.getError());
            } else if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOC.getCode())) {
                Assert.isTrue(Objects.nonNull(request.getX1()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.X1_IS_NULL.getError());
                Assert.isTrue(Objects.nonNull(request.getX2()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.X2_IS_NULL.getError());
                Assert.isTrue(request.getX1() >= 0 && request.getX1() <= 99, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.X2_IS_NULL.getError());
                Assert.isTrue(request.getX1() >= 0 && request.getX2() <= 99, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.X2_IS_NULL.getError());
                Assert.isTrue(request.getX1() <= request.getX2(), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.X2_LESS_THEN_X1.getError());
//                Assert.isTrue(Objects.nonNull(request.getSafety()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.SAFETY_IS_NULL.getError());
//                Assert.isTrue(Objects.nonNull(request.getTarget()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.TARGET_IS_NULL.getError());
                if (Objects.nonNull(request.getSafety())) {
                    Assert.isTrue(request.getSafety() >= 0, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.SAFETY_RANGE_ERROR.getError());
                }
                if (Objects.nonNull(request.getTargetToc())) {
                    Assert.isTrue(request.getTargetToc() >= 0, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.TARGET_RANGE_ERROR.getError());
                }
            } else {
                Assert.isTrue(false, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_EXISTENT.getError());
            }
            if (Objects.nonNull(currentAccount)) {
                request.setOperatorCode(currentAccount.getId());
                request.setOperatorName(currentAccount.getName());
            }
            request.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
        }
    }

    @Override
    public boolean batchUpdateParameters(InvParametersRequest request) {
        Assert.isTrue(Objects.nonNull(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_NULL.getError());
        Assert.isTrue(ReplnTypeEnum.codeValues().contains(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_EXISTENT.getError());
        Assert.isTrue(CollectionUtils.isNotEmpty(request.getIds()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_PARAMETERS_ID_IS_NULL.getError());
        Assert.isTrue(Objects.nonNull(request.getCollocationMethod()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.COLLOCATION_METHOD_NOT_NULL.getError());
        request.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        if (Objects.nonNull(currentAccount)) {
            request.setOperatorCode(currentAccount.getId());
            request.setOperatorName(currentAccount.getName());
        }
        if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOB.getCode())) {
            invStrategyParametersTobDao.batchUpdateByIds(request);
        } else if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOC.getCode())) {
            invStrategyParametersTocDao.batchUpdateByIds(request);
        }
        return false;
    }

    @Override
    public Long addOrUpdateStrategy(InvUpsertStrategyRequest request) {
        Long strategyId = validateRequest(request);
        request.setStatus(InvStrategyStatusEnum.DISABLE.getCode());
//        // TODO 保存并启用逻辑，先保存，再调用批量启用接口
//        if (Objects.nonNull(request.getEnable())) {
//            request.setStatus(InvStrategyStatusEnum.ENABLED.getCode());
//        }
        if (Objects.equals(InvDimensionEnum.BY_SKU.getCode(), request.getDimension())) {
            request.setSkuStr(request.getCategory().toString());
            if (request.getCategory().contains(StrategyConstants.StrategyCenter.SELECT_ALL)) {
                List<String> strings = invSkuReplnTypeDao.querySkuByReplnType(request.getReplnType());
                request.setSkuStr(strings.toString());
                request.setSelectAll(1);
            }
        } else {
            request.setTag(request.getCategory().toString());
        }
        if (!Objects.isNull(request.getId())) {
            invStrategyCenterDao.updateStrategy(JsonUtils.toMap(request));
        } else {
            request.setId(strategyId);
            invStrategyCenterDao.insertStrategy(JsonUtils.toMap(request));
        }
        //修改库存策略状态
        if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOB.getCode())) {
            invStrategyParametersTobDao.deleteByStrategyIdAndStatus(strategyId, InvStrategyStatusEnum.ENABLED.getCode());
            invStrategyParametersTobDao.enableByStrategyId(strategyId);
        } else if (Objects.equals(request.getReplnType(), ReplnTypeEnum.TOC.getCode())) {
            invStrategyParametersTocDao.deleteByStrategyIdAndStatus(strategyId, InvStrategyStatusEnum.ENABLED.getCode());
            invStrategyParametersTocDao.enableByStrategyId(strategyId);
        }
        return strategyId;
    }

    private Long validateRequest(InvUpsertStrategyRequest request) {
        Assert.isTrue(StringUtils.isNotBlank(request.getName()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_NAME_IS_NOT_NULL.getError());
        Assert.isTrue(StringUtils.isNotBlank(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_NULL.getError());
        Assert.isTrue(StringUtils.isNotBlank(request.getDimension()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.DIMENSION_IS_NOT_NULL.getError());
        Assert.isTrue(ReplnTypeEnum.codeValues().contains(request.getReplnType()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REPLN_TYPE_IS_NOT_EXISTENT.getError());
        Assert.isTrue(InvDimensionEnum.codeValues().contains(request.getDimension()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.DIMENSION_IS_NOT_EXISTENT.getError());
        Assert.isTrue(CollectionUtils.isNotEmpty(request.getCategory()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_NAME_IS_NOT_NULL.getError());
        Assert.isTrue(Objects.nonNull(request.getCoverCnt()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_NAME_IS_NOT_NULL.getError());
        Long strategyId = null;
        Long count = invStrategyCenterDao.selectByName(request.getName());
        if (Objects.nonNull(request.getId())) {
            strategyId = request.getId();
            List<InvStrategyCenterDO> invStrategyCenterDOList = invStrategyCenterDao.selectByIds(Collections.singletonList(strategyId));
            Assert.isTrue(CollectionUtils.isNotEmpty(invStrategyCenterDOList), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_IS_NOT_EXISTENT.getError());
            InvStrategyCenterDO invStrategyCenterDO = invStrategyCenterDOList.get(0);
            if (!Objects.equals(invStrategyCenterDO.getName(), request.getName())) {
                Assert.isTrue(count <= 0, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REQUEST_PARAM_ERROR.getError(), request.getName());
            }
        } else {
            Assert.isTrue(Objects.nonNull(request.getToken()), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
            strategyId = getId(request);
            Assert.isTrue(count <= 0, ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.REQUEST_PARAM_ERROR.getError(), request.getName());
        }
        Assert.isTrue(Objects.nonNull(strategyId), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
        return strategyId;
    }

    private Long getId (InvUpsertStrategyRequest request) {
        Map<String, Object> map = JwtUtils.parse(request.getToken(), new TypeReference<Map<String, Object>>() {
        });
        if (map != null) {
            Object id = map.get(StrategyConstants.StrategyCenter.TOKEN_ID);
            Assert.isTrue(Objects.nonNull(id), ErrorCode.ILLEGAL_ARGUMENT, InvStrategyCenterError.STRATEGY_ID_IS_NOT_NULL.getError());
            return Long.valueOf(String.valueOf(id));
        }
        return null;
    }

    @Override
    public Long deleteByStrategyId(Long strategyId) {
        invStrategyParametersTocDao.deleteByStrategyIdAndStatus(strategyId, InvStrategyStatusEnum.DISABLE.getCode());
        invStrategyParametersTobDao.deleteByStrategyIdAndStatus(strategyId, InvStrategyStatusEnum.DISABLE.getCode());
        return 1l;
    }

    @Scheduled(cron = "0 0 2 * * ?")
    @Transactional
    public void deleteByStatus() {
        try {
            Integer deletedFromTob = invStrategyParametersTobDao.deleteByStatus(InvStrategyStatusEnum.DISABLE.getCode());
            Integer deletedFromToc = invStrategyParametersTocDao.deleteByStatus(InvStrategyStatusEnum.DISABLE.getCode());
            log.info("Deleted {} records from TOB and {} records from TOC with status = 0", deletedFromTob, deletedFromToc);
        } catch (Exception e) {
            log.error("Error occurred while deleting parameters with status = 0", e);
        }
    }

}
