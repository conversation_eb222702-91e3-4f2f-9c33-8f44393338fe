package cn.aliyun.ryytn.modules.inv.service.business;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.Session;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.inv.api.business.CdcRdcPriorityService;
import cn.aliyun.ryytn.modules.inv.business.dao.CdcRdcPriorityDao;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.ConstantFactory;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.InjectConstant;
import cn.aliyun.ryytn.modules.inv.common.ability.constant.service.ConstantDTO;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.BuriedLogger;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.QueryLogger;
import cn.aliyun.ryytn.modules.inv.common.constants.BizLoggerConstant;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.common.utils.NumUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StreamUtils;
import cn.aliyun.ryytn.modules.inv.constant.business.BusinessConstants;
import cn.aliyun.ryytn.modules.inv.constant.business.error.CdcRdcPriorityError;
import cn.aliyun.ryytn.modules.inv.entity.business.dos.CdcRdcPriorityDO;
import cn.aliyun.ryytn.modules.inv.entity.business.dto.CdcRdcPriorityDTO;
import cn.aliyun.ryytn.modules.inv.entity.business.logger.CdcRdcPriorityLogMetaInfo;
import cn.aliyun.ryytn.modules.inv.entity.business.request.CdcRdcPriorityPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.business.request.CdcRdcPriorityUpdateRequest;
import cn.aliyun.ryytn.modules.inv.common.exception.Assert;
import cn.aliyun.ryytn.modules.inv.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@QueryLogger(bizCode = BizLoggerConstant.BizCode.INV_PRIORITY, suffix = BizLoggerConstant.Suffix.INV, metaClass = CdcRdcPriorityLogMetaInfo.class)
public class CdcRdcPriorityServiceImpl implements CdcRdcPriorityService {

    @Autowired
    CdcRdcPriorityDao cdcRdcPriorityDao;

    @InjectConstant(type = BusinessConstants.InjectConstantsType.RDC_RDC_PRIORITY_QUERY)
    private ConstantFactory quickQueryFactory;

    private static final int MAX_PRIORITY = 100;

    private static final int MIN_PRIORITY = 0;

    @Override
    public List<CdcRdcPriorityDTO> queryPageData(CdcRdcPriorityPageQueryRequest request) {
        paddingPageQueryRequest(request);
        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        List<CdcRdcPriorityDO> queryList = cdcRdcPriorityDao.selectByCondition(JsonUtils.toMap(request));
        List<CdcRdcPriorityDTO> result = GeiCommonConvert.convert(queryList, CdcRdcPriorityDTO.class);
        return result;
    }

    @Override
    public Long queryCount(CdcRdcPriorityPageQueryRequest request) {
        paddingPageQueryRequest(request);
        return cdcRdcPriorityDao.selectCount(JsonUtils.toMap(request));
    }

    private void paddingPageQueryRequest(CdcRdcPriorityPageQueryRequest request) {
        if (Objects.isNull(request.getQuickQueryClauseValue())) {
            String quickQueryClause = request.getQuickQueryClause();
            ConstantDTO constant = quickQueryFactory.getConstant(quickQueryClause);
            if (Objects.nonNull(constant)) {
                request.setQuickQueryClauseValue(constant.getExtra());
            }
        }
    }

    @Override
    public List<OptionDTO> quickQueryClauseOption() {
        List<ConstantDTO> constantList = quickQueryFactory.get();
        return StreamUtils.map(constantList, item -> OptionDTO.of(item.getCode(), item.getName()));
    }

    @Override
    @BuriedLogger(bizCode = BizLoggerConstant.BizCode.INV_PRIORITY, suffix = BizLoggerConstant.Suffix.INV)
    public Long batchUpdate(List<CdcRdcPriorityUpdateRequest> updateRequest, Boolean isImport) {
        if (!isImport) {
            // 非导入，不能为空
            Assert.isTrue(CollectionUtils.isNotEmpty(updateRequest), ErrorCode.ILLEGAL_ARGUMENT, CdcRdcPriorityError.PARAM_IS_NOT_NULL.getError());

            if (!validate(updateRequest, isImport)) {
                // 校验异常，从对象的errorMsg字段获取具体的错误信息
                StringBuilder errorMsg = new StringBuilder();
                for (CdcRdcPriorityUpdateRequest request : updateRequest) {
                    if (!StringUtils.isBlank(request.getErrorMsg())) {
                        if (!errorMsg.toString().isEmpty()) {
                            errorMsg.append("; ");
                        }
                        errorMsg.append(request.getErrorMsg());
                    }
                }
                Assert.isTrue(false, ErrorCode.ILLEGAL_ARGUMENT, errorMsg.toString());
            }
        }

        return cdcRdcPriorityDao.batchUpdate(updateRequest);
    }


    @Override
    public boolean validate(List<CdcRdcPriorityUpdateRequest> updateRequest, Boolean isImport) {
        // step1 参数静态检查，如非空、值域范围
        boolean staticValidateRet = staticValidate(updateRequest, isImport);

        if (!staticValidateRet) {
            return false;
        }

        // 获取所有调拨路线
        CdcRdcPriorityPageQueryRequest queryRequest = new CdcRdcPriorityPageQueryRequest();
        queryRequest.setPaging(false);
        List<CdcRdcPriorityDO> existEntities = cdcRdcPriorityDao.selectByCondition(JsonUtils.toMap(queryRequest));

        // step2 检查调拨线路是否存在
        if (!validateExist(existEntities, updateRequest)) {
            return false;
        }

        // step3 检查优先级冲突
        setEntityPriority(existEntities, updateRequest);
        boolean conflictRet = validateRdcConflictsByCdc(existEntities, updateRequest) && validateCdcConflictsByRdc(existEntities, updateRequest);

        // 设置操作人
        setOperator(updateRequest);

        return conflictRet;
    }

    public void setEntityPriority(List<CdcRdcPriorityDO> entities, List<CdcRdcPriorityUpdateRequest> requests) {
        for (CdcRdcPriorityDO entity : entities) {
            for (CdcRdcPriorityUpdateRequest request : requests) {
                if (StringUtils.equals(request.getKey(), entity.getKey())) {
                    entity.setRdcOosPriority(request.getRdcOosPriority());
                    entity.setCdcSupplyPriority(request.getCdcSupplyPriority());
                }
            }
        }
    }

    public boolean validateExist(List<CdcRdcPriorityDO> entities, List<CdcRdcPriorityUpdateRequest> requests) {
        Map<String, CdcRdcPriorityDO> entityMap = StreamUtils.singleGroup(entities, entity -> entity.getKey());
        boolean lineExistRet = true;
        for (CdcRdcPriorityUpdateRequest request : requests) {
            // 校验调拨路线是否存在
            CdcRdcPriorityDO beforeEntity = entityMap.get(request.getKey());
            if (beforeEntity == null) {
                // 未找到已有线路
                lineExistRet = false;
                request.setErrorMsg(CdcRdcPriorityError.CDC_RDC_REL_NOT_EXIST.getError());
            } else {
                // 设置旧值
                request.setCdcSupplyPriorityBefore(beforeEntity.getCdcSupplyPriority());
                request.setRdcOosPriorityBefore(beforeEntity.getRdcOosPriority());
                request.setCdcName(beforeEntity.getCdcName());
                request.setRdcName(beforeEntity.getRdcName());
            }
        }
        return lineExistRet;
    }

    public boolean staticValidate(CdcRdcPriorityUpdateRequest request) {
        StringBuilder stringBuilder = new StringBuilder();
        if (request == null) {
            request.setErrorMsg(CdcRdcPriorityError.PARAM_IS_NOT_NULL.getError());
            return false;
        }
        if (StringUtils.isEmpty(request.getCdcCode())) {
            stringBuilder.append(CdcRdcPriorityError.CDC_CODE_IS_NOT_NULL.getError());
            //request.setErrorMsg(CdcRdcPriorityError.CDC_CODE_IS_NOT_NULL.getError());
        }
        if (StringUtils.isEmpty(request.getRdcCode())) {
            stringBuilder.append(CdcRdcPriorityError.RDC_CODE_IS_NOT_NULL.getError());
            //request.setErrorMsg(CdcRdcPriorityError.RDC_CODE_IS_NOT_NULL.getError());
        }
        if (StringUtils.isEmpty(request.getCdcName())) {
            stringBuilder.append(CdcRdcPriorityError.CDC_NAME_IS_NOT_NULL.getError());
            //request.setErrorMsg(CdcRdcPriorityError.CDC_NAME_IS_NOT_NULL.getError());
        }
        if (StringUtils.isEmpty(request.getRdcName())) {
            stringBuilder.append(CdcRdcPriorityError.RDC_NAME_IS_NOT_NULL.getError());
            //request.setErrorMsg(CdcRdcPriorityError.RDC_NAME_IS_NOT_NULL.getError());
        }
        if (stringBuilder.length() > 0) {
            request.setErrorMsg(stringBuilder.toString());
            return false;
        }
        if (StringUtils.isNotBlank(request.getCdcSupplyPriorityStr())) {
            Integer cdcPriority = NumUtils.safeInt(request.getCdcSupplyPriorityStr());
            if (Objects.isNull(cdcPriority)) {
                stringBuilder.append(CdcRdcPriorityError.CDC_SUPPLY_PRIORITY_IS_NUMBER.getError());
            } else {
                request.setCdcSupplyPriority(cdcPriority);
            }
        }
        if (StringUtils.isNotBlank(request.getRdcOosPriorityStr())) {
            Integer rdcPriority = NumUtils.safeInt(request.getRdcOosPriorityStr());
            if (Objects.isNull(rdcPriority)) {
                stringBuilder.append(CdcRdcPriorityError.RDC_OOS_PRIORITY_IS_NUMBER.getError());
            } else {
                request.setRdcOosPriority(rdcPriority);
            }
        }
        if (stringBuilder.length() > 0) {
            request.setErrorMsg(stringBuilder.toString());
            return false;
        }
        if (Objects.isNull(request.getCdcSupplyPriority())) {
            stringBuilder.append(CdcRdcPriorityError.CDC_SUPPLY_PRIORITY_ERROR.getError());
            //request.setErrorMsg(CdcRdcPriorityError.CDC_SUPPLY_PRIORITY_ERROR.getError());
        }
        if (Objects.isNull(request.getRdcOosPriority())) {
            stringBuilder.append(CdcRdcPriorityError.RDC_OOS_PRIORITY_ERROR.getError());
            //request.setErrorMsg(CdcRdcPriorityError.RDC_OOS_PRIORITY_ERROR.getError());
        }
        if (stringBuilder.length() > 0) {
            request.setErrorMsg(stringBuilder.toString());
            return false;
        }
//        if (!StringUtils.isNumeric(request.getCdcSupplyPriority().toString())) {
//            stringBuilder.append(CdcRdcPriorityError.CDC_SUPPLY_PRIORITY_IS_NUMBER.getError());
//            request.setErrorMsg(CdcRdcPriorityError.CDC_SUPPLY_PRIORITY_IS_NUMBER.getError());
//        }
//        if (!StringUtils.isNumeric(request.getRdcOosPriority().toString())) {
//            stringBuilder.append(CdcRdcPriorityError.RDC_OOS_PRIORITY_IS_NUMBER.getError());
//            request.setErrorMsg(CdcRdcPriorityError.RDC_OOS_PRIORITY_IS_NUMBER.getError());
//        }
        if (request.getCdcSupplyPriority() < MIN_PRIORITY || request.getCdcSupplyPriority() > MAX_PRIORITY) {
            stringBuilder.append(CdcRdcPriorityError.CDC_SUPPLY_PRIORITY_RANGE_ERROR.getError());
//            request.setErrorMsg(CdcRdcPriorityError.CDC_SUPPLY_PRIORITY_RANGE_ERROR.getError());
        }
        if (request.getRdcOosPriority() < MIN_PRIORITY || request.getRdcOosPriority() > MAX_PRIORITY) {
            stringBuilder.append(CdcRdcPriorityError.RDC_OOS_PRIORITY_RANGE_ERROR.getError());
//            request.setErrorMsg(CdcRdcPriorityError.RDC_OOS_PRIORITY_RANGE_ERROR.getError());
        }
        if (stringBuilder.length() > 0) {
            request.setErrorMsg(stringBuilder.toString());
            return false;
        }
        return StringUtils.isBlank(request.getErrorMsg());
    }

    // 设置操作人
    private void setOperator(List<CdcRdcPriorityUpdateRequest> updateRequest) {
        Session session = ServiceContextUtils.currentSession();
        Account currentAccount = null;
        if (session != null) {
            currentAccount = session.getAccount();
        }
        if (Objects.nonNull(currentAccount) && updateRequest != null) {
            for (CdcRdcPriorityUpdateRequest request : updateRequest) {
                request.setOperatorCode(currentAccount.getId());
                request.setOperatorName(currentAccount.getName());
            }
        }

    }

    public boolean staticValidate(List<CdcRdcPriorityUpdateRequest> updateRequest, Boolean isImport) {
        boolean ret = true;
        for (CdcRdcPriorityUpdateRequest request : updateRequest) {
            boolean oneLineValidateRet = staticValidate(request);
            if (!oneLineValidateRet) {
                ret = false;
            }
        }
        return ret;
    }

    private List<CdcRdcPriorityUpdateRequest> getUniqList(List<CdcRdcPriorityUpdateRequest> entities) {
        return entities.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    private List<CdcRdcPriorityDO> findConflictSubArr(Map<Integer, List<CdcRdcPriorityDO>> priorityCountMap) {
        List<CdcRdcPriorityDO> conflictEntities = new ArrayList<>();
        priorityCountMap.forEach((priority, entitiesWithSamePriority) -> {
            if (entitiesWithSamePriority.size() > 1) {
                conflictEntities.addAll(entitiesWithSamePriority);
            }
        });
        return conflictEntities;
    }

    public boolean validateRdcConflictsByCdc(List<CdcRdcPriorityDO> entities, List<CdcRdcPriorityUpdateRequest> requests) {
        // 存储冲突的记录
        List<CdcRdcPriorityDO> conflictEntities = new ArrayList<>();

        if (CollectionUtils.isEmpty(entities)) {
            return true;
        }

        // 获取所有唯一的 cdcCode
        Set<String> uniqueCdcCodes = entities.stream().map(CdcRdcPriorityDO::getCdcCode).filter(Objects::nonNull).collect(Collectors.toSet());

        // 遍历每个 cdcCode
        for (String cdcCode : uniqueCdcCodes) {
            // 找出当前 cdcCode 下的所有元素
            List<CdcRdcPriorityDO> cdcEntities = entities.stream().filter(entity -> cdcCode.equals(entity.getCdcCode())).collect(Collectors.toList());

            // 记录 rdcOosPriority 出现的次数
            Map<Integer, List<CdcRdcPriorityDO>> priorityCountMap = new HashMap<>();

            for (CdcRdcPriorityDO entity : cdcEntities) {
                // 跳过 rdcOosPriority 为 null 的情况
                if (entity.getRdcOosPriority() == null) {
                    continue;
                }

                // 记录每个 优先级 对应的实体
                priorityCountMap.computeIfAbsent(entity.getRdcOosPriority(), k -> new ArrayList<>()).add(entity);
            }

            // 找到优先级重复的实体
            conflictEntities.addAll(findConflictSubArr(priorityCountMap));
        }

        int cnt = 0;
        for (CdcRdcPriorityUpdateRequest request : requests) {
            for (CdcRdcPriorityDO conflictEntity : conflictEntities) {
                if (StringUtils.equals(request.getKey(), conflictEntity.getKey())) {
                    request.setErrorMsg(CdcRdcPriorityError.RDC_OOS_PRIORITY_CONFLICT.getError());
                    cnt++;
                }
            }
        }

        // 一个实体可能在多处重复，做去重处理
        return cnt == 0;
    }

    public boolean validateCdcConflictsByRdc(List<CdcRdcPriorityDO> entities, List<CdcRdcPriorityUpdateRequest> requests) {
        // 存储冲突的记录
        List<CdcRdcPriorityDO> conflictEntities = new ArrayList<>();

        if (CollectionUtils.isEmpty(entities)) {
            return true;
        }

        // 获取所有唯一的 rdcCode
        Set<String> uniqueRdcCodes = entities.stream().map(CdcRdcPriorityDO::getRdcCode).filter(Objects::nonNull).collect(Collectors.toSet());

        // 遍历每个 rdcCode
        for (String rdcCode : uniqueRdcCodes) {
            // 找出当前 rdcCode 下的所有元素
            List<CdcRdcPriorityDO> rdcEntities = entities.stream().filter(entity -> rdcCode.equals(entity.getRdcCode())).collect(Collectors.toList());

            // 记录 rdcOosPriority 出现的次数
            Map<Integer, List<CdcRdcPriorityDO>> priorityCountMap = new HashMap<>();

            for (CdcRdcPriorityDO entity : rdcEntities) {
                // 跳过 rdcOosPriority 为 null 的情况
                if (entity.getCdcSupplyPriority() == null) {
                    continue;
                }

                // 记录每个 rdcOosPriority 对应的实体
                priorityCountMap.computeIfAbsent(entity.getCdcSupplyPriority(), k -> new ArrayList<>()).add(entity);
            }

            // 找出重复的 rdcOosPriority 对应的实体
            conflictEntities.addAll(findConflictSubArr(priorityCountMap));
        }

        int cnt = 0;
        for (CdcRdcPriorityUpdateRequest request : requests) {
            for (CdcRdcPriorityDO conflictEntity : conflictEntities) {
                if (StringUtils.equals(request.getKey(), conflictEntity.getKey())) {
                    request.setErrorMsg(CdcRdcPriorityError.CDC_SUPPLY_PRIORITY_CONFLICT.getError());
                    cnt++;
                }
            }
        }
        return cnt == 0;
    }
}
