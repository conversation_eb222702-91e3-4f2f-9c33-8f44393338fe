package cn.aliyun.ryytn.modules.inv.service.task.manager;


import cn.aliyun.ryytn.modules.inv.api.task.enums.TaskStatusEnum;
import cn.aliyun.ryytn.modules.inv.api.task.request.ExecChainUpdateRequest;
import cn.aliyun.ryytn.modules.inv.task.dao.ScpTaskExecChainMapper;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskExecChainDO;
import cn.aliyun.ryytn.modules.inv.api.task.request.ExecChainPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.SnowflakeIdWorker;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-21 16:17
 * @description
 */
@Component
public class ExecChainManager {

    @Resource
    private ScpTaskExecChainMapper scpTaskExecChainMapper;
    private static final SnowflakeIdWorker ID_WORKER = new SnowflakeIdWorker(0, 0);

    @Transactional(rollbackFor = Exception.class)
    public ScpTaskExecChainDO getChainNextNode() {
        ExecChainPageQueryRequest request = new ExecChainPageQueryRequest();
        request.setStatus(Collections.singletonList(TaskStatusEnum.RUNNING.getCode()));
        request.setPaging(false);
        List<ScpTaskExecChainDO> list = scpTaskExecChainMapper.selectByCondition(JsonUtils.toMap(request));
        // 如果有正在执行中的，则返回正在执行中的
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        // 按创建时间排序，返回第一个
        request.setStatus(Collections.singletonList(TaskStatusEnum.AWAIT.getCode()));
        request.setOrderColumn("gmt_create,batch_no,date_expr_priority");
        request.setPageSize(1L);
        request.setPaging(true);
        request.calcPage();
        list = scpTaskExecChainMapper.selectByCondition(JsonUtils.toMap(request));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<ScpTaskExecChainDO> getChainNodeByBatch(Long batchNo) {
        if (Objects.isNull(batchNo)) {
            return null;
        }
        ExecChainPageQueryRequest request = new ExecChainPageQueryRequest();
        request.setBatch(Collections.singletonList(batchNo));
        request.setPaging(false);
        return scpTaskExecChainMapper.selectByCondition(JsonUtils.toMap(request));
    }

    public boolean updateChainNode(ScpTaskExecChainDO execChainDO) {
        return updateChainNode(GeiCommonConvert.convert(execChainDO, ExecChainUpdateRequest.class));
    }

    public boolean updateChainNode(ExecChainUpdateRequest request) {
        if (Objects.isNull(request)) {
            return false;
        }
        if (Objects.isNull(request.getRootTaskId())) {
            return false;
        }
        if (Objects.nonNull(request.getSubmitTime())) {
            request.setSubmitTimeStr(DateUtil.localDateTimeToString(request.getSubmitTime(), DateUtil.DEFAULT_DATETIME_FORMAT));
        }
        return scpTaskExecChainMapper.update(JsonUtils.toMap(request)) > 0;
    }

    public boolean insertChainNode(ScpTaskExecChainDO scpTaskExecChainDO) {
        scpTaskExecChainDO.setChainNodeId(ID_WORKER.nextId());
        return scpTaskExecChainMapper.insert(scpTaskExecChainDO) > 0;
    }
}
