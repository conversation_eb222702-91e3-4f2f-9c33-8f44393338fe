package cn.aliyun.ryytn.modules.inv.eiport;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.captcha.CaptchaUtils;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.inv.api.strategy.InvSafetyStockService;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.utils.*;
import cn.aliyun.ryytn.modules.inv.constant.strategy.enums.SkuLevelEnum;
import cn.aliyun.ryytn.modules.inv.constant.strategy.error.InvSafetyStockError;
import cn.aliyun.ryytn.modules.inv.eiport.model.SafetyStockParametersImportTemplateVO;
import cn.aliyun.ryytn.modules.inv.eiport.model.export.SafetyStockParametersExportTemplateVO;
import cn.aliyun.ryytn.modules.inv.entity.business.dto.InvSkuCdcFullSupplyDTO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvSafetyStockParametersDO;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvSafetyStockUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvStrategyResulesUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.request.InvSafetyStockPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.strategy.vo.InvSafetyStockParametersVO;
import cn.aliyun.ryytn.modules.inv.service.strategy.InvStrategyResultsManager;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvSafetyStockDao;
import cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyResultsTobDao;
import cn.hutool.core.collection.CollectionUtil;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfo;
import com.cainiao.cntech.dsct.scp.gei.common.meta.ExcelMetaInfoCreator;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportResult;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.ImportPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.EIPortDataTemplate;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants.SafetyStockParameters.*;

/**
 * <AUTHOR>
 * @date 2025/5/12 15:17
 * @description: 安全库存参数
 */

@ImportService(value = "safetyStockParametersEIportDataTemplate", filename = "安全库存参数导入模版", templateDesc = "填写规范：\n" +
        "1.服务水平：填写1～100的整数，如：87\n" +
        "2.最小安全库存天数：填写1～100的整数，如：87\n" +
        "3.最大安全库存天数：填写1～100的整数，数值必须>最小安全库存天数\n" +
        "如：89", async = false, dict = "SAFETY_STOCK_PARAMETERS")
@ExportService(value = "safetyStockParametersEIportDataTemplate", filename = "安全库存参数数据导出", async = false)
public class SafetyStockParametersEIportDataTemplate extends EIPortDataTemplate<SafetyStockParametersImportTemplateVO, InvSafetyStockPageQueryRequest> implements ImportPostProcessor<SafetyStockParametersImportTemplateVO> {

    @Autowired
    private InvSafetyStockService invSafetyStockService;

    @Autowired
    private InvSafetyStockDao invSafetyStockDao;

    @Override
    public List<?> getExportTableData(InvSafetyStockPageQueryRequest request) {
        request.setPaging(false);
        PageMetaInfoWrapper pageMetaInfoWrapper = invSafetyStockService.queryPageData(request);
        return pageMetaInfoWrapper.getList();
    }

    @Override
    public List<ExcelMetaInfo> getExportMetaInfo(InvSafetyStockPageQueryRequest request) {
        return ExcelMetaInfoCreator.create(SafetyStockParametersExportTemplateVO.class);
    }

    @Override
    public List<SafetyStockParametersImportTemplateVO> getImportTemplateData(ImportQueryRequest request) {
        InvSafetyStockPageQueryRequest pageQueryRequest = JsonUtils.toRequestObject(request.getParams(), InvSafetyStockPageQueryRequest.class);
        pageQueryRequest.setPaging(false);
        List<InvSafetyStockParametersDO> list = invSafetyStockDao.selectByCondition(JsonUtils.toMap(pageQueryRequest));
        if (CollectionUtil.isEmpty(list)) {
            return super.getImportTemplateData(request);
        }
        if (CollectionUtils.isEmpty(request.getDimensionList())) {
            return super.getImportTemplateData(request);
        }
        List<SafetyStockParametersImportTemplateVO> convert = GeiCommonConvert.convert(list, SafetyStockParametersImportTemplateVO.class);
        for (SafetyStockParametersImportTemplateVO importTemplateVO : convert) {
            importTemplateVO.setAbcTypeName(SkuLevelEnum.getNameByCode(importTemplateVO.getAbcType()));
        }
        Function<SafetyStockParametersImportTemplateVO, String> queryListGroup = null;
        if (request.getDimensionList().contains(LV1)) {
            queryListGroup = SafetyStockParametersImportTemplateVO::getLv1CategoryName;
            convert = convert.stream().filter(t -> StringUtils.isNotEmpty(t.getLv1CategoryName())).collect(Collectors.toList());
        } else if (request.getDimensionList().contains(LV2)) {
            queryListGroup = SafetyStockParametersImportTemplateVO::getLv2CategoryName;
            convert = convert.stream().filter(t -> StringUtils.isNotEmpty(t.getLv2CategoryName())).collect(Collectors.toList());
        } else if (request.getDimensionList().contains(LV4)) {
            queryListGroup = SafetyStockParametersImportTemplateVO::getLv4CategoryName;
            convert = convert.stream().filter(t -> StringUtils.isNotEmpty(t.getLv4CategoryName())).collect(Collectors.toList());
        } else if (request.getDimensionList().contains(ABC_TYPE_NAME)) {
            queryListGroup = SafetyStockParametersImportTemplateVO::getAbcType;
            convert = convert.stream().filter(t -> StringUtils.isNotEmpty(t.getAbcTypeName())).collect(Collectors.toList());
        } else if (request.getDimensionList().contains(SKU)) {
            queryListGroup = SafetyStockParametersImportTemplateVO::getSkuCode;
        } else if (request.getDimensionList().contains(RDC_NAME)) {
            queryListGroup = SafetyStockParametersImportTemplateVO::getRdcName;
        } else if (request.getDimensionList().contains(SKU_AND_RDC)){
            queryListGroup = item -> item.getSkuCode()+ StringConstants.UNDERLINE + item.getRdcName();
        }
        if (CollectionUtils.isEmpty(convert)) {
            return super.getImportTemplateData(request);
        }
        Map<String, List<SafetyStockParametersImportTemplateVO>> group = StreamUtils.group(convert, queryListGroup);
        List<SafetyStockParametersImportTemplateVO> returnList = new ArrayList<>();
        group.forEach((k, v) -> {
            returnList.add(v.get(0));
        });
        return returnList;
    }

    @Override
    public boolean preValid(ImportDataWrapper<SafetyStockParametersImportTemplateVO> importDataWrapper) {
        return validate(importDataWrapper);
    }

    @Override
    public ImportResult importData(ImportDataWrapper<SafetyStockParametersImportTemplateVO> importDataWrapper) {
        List<SafetyStockParametersImportTemplateVO> data = importDataWrapper.getData();

        if (CollectionUtils.isNotEmpty(data)){
            List<InvSafetyStockUpdateRequest> updateRequestList = GeiCommonConvert.convert(data, InvSafetyStockUpdateRequest.class);
            if (updateRequestList.size() > 200) {
                int batchSize = 200;
                int totalSize = updateRequestList.size();
                for (int i = 0; i < totalSize; i += batchSize) {
                    int endIndex = Math.min(i+batchSize, totalSize);
                    List<InvSafetyStockUpdateRequest> batch = updateRequestList.subList(i, endIndex);
                    invSafetyStockService.update(batch, true);
                }
            } else {
                invSafetyStockService.update(updateRequestList, true);
            }

        }
         return null;
    }

    private boolean validate(ImportDataWrapper<SafetyStockParametersImportTemplateVO> importDataWrapper) {
        boolean flag = true;
        List<String> dimensionList = importDataWrapper.getDimensionList();
        dimensionList.addAll(importDataWrapper.getFieldsList());
        List<SafetyStockParametersImportTemplateVO> importData = importDataWrapper.getData();
        if (CollectionUtils.isEmpty(importData)) {
            return false;
        }
       
        InvSafetyStockPageQueryRequest request = new InvSafetyStockPageQueryRequest();
        request.setPaging(false);
        setParam(request, dimensionList, importData);
        List<InvSafetyStockParametersDO> invStrategyResultsTobDOList = invSafetyStockDao.selectByCondition(JsonUtils.toMap(request));
        if (CollectionUtils.isEmpty(invStrategyResultsTobDOList)) {
            setErrMsg(importData, dimensionList);
            return false;
        }
        Map<String, List<InvSafetyStockParametersDO>> resultsGroupMap = resultsGroupMap(invStrategyResultsTobDOList, dimensionList);
        flag = validateParam(dimensionList, importData,resultsGroupMap);
        if (!flag) {
            return false;
        }
        flag = validateData(invStrategyResultsTobDOList, importData, dimensionList,resultsGroupMap);
        return flag;
    }

    private boolean validateData(List<InvSafetyStockParametersDO> invStrategyResultsTobDOList, List<SafetyStockParametersImportTemplateVO> importData, List<String> dimensionList,Map<String, List<InvSafetyStockParametersDO>> resultsGroupMap) {
        boolean flag = true;
        boolean resultFlag = true;
        
        List<SafetyStockParametersImportTemplateVO> updateList = new ArrayList<>();
        for (SafetyStockParametersImportTemplateVO importDatum : importData) {
            List<InvSafetyStockParametersDO> resultsTobDOS = null;
            if (dimensionList.contains(LV1)) {
                resultsTobDOS = resultsGroupMap.get(importDatum.getLv1CategoryName());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getLv1CategoryName());
            } else if (dimensionList.contains(LV2)) {
                resultsTobDOS = resultsGroupMap.get(importDatum.getLv2CategoryName());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getLv2CategoryName());
            } else if (dimensionList.contains(LV4)) {
                resultsTobDOS = resultsGroupMap.get(importDatum.getLv4CategoryName());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getLv4CategoryName());
            } else if (dimensionList.contains(ABC_TYPE_NAME)) {
                resultsTobDOS = resultsGroupMap.get(SkuLevelEnum.getCodeByName(importDatum.getAbcTypeName()));
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getAbcTypeName());
            } else if (dimensionList.contains(SKU)) {
                resultsTobDOS = resultsGroupMap.get(importDatum.getSkuCode());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getSkuCode());
            } else if (dimensionList.contains(RDC_NAME)) {
                resultsTobDOS = resultsGroupMap.get(importDatum.getRdcName());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getRdcName());
            } else if (dimensionList.contains(SKU_AND_RDC)){
                resultsTobDOS = resultsGroupMap.get(importDatum.getSkuCode() + StringConstants.UNDERLINE + importDatum.getRdcName());
                flag = setErrMsg(resultsTobDOS, importDatum, importDatum.getSkuCode() + StringConstants.UNDERLINE + importDatum.getRdcName());
            }

            if (flag) {
                updateList.addAll(setRequest(resultsTobDOS, importDatum));
            }else {
                resultFlag = false;
            }
        }
        if (resultFlag) {
            importData.clear();
            importData.addAll(updateList);
        }

        return resultFlag;
    }
    
    private Map<String, List<InvSafetyStockParametersDO>> resultsGroupMap(List<InvSafetyStockParametersDO> invStrategyResultsTobDOList,List<String> dimensionList){
        Function<InvSafetyStockParametersDO, String> queryListGroup = null;
        if (dimensionList.contains(LV1)) {
            queryListGroup = InvSafetyStockParametersDO::getLv1CategoryName;
        } else if (dimensionList.contains(LV2)) {
            queryListGroup = InvSafetyStockParametersDO::getLv2CategoryName;
        } else if (dimensionList.contains(LV4)) {
            queryListGroup = InvSafetyStockParametersDO::getLv4CategoryName;
        } else if (dimensionList.contains(ABC_TYPE_NAME)) {
            queryListGroup = InvSafetyStockParametersDO::getAbcType;
        } else if (dimensionList.contains(SKU)) {
            queryListGroup = InvSafetyStockParametersDO::getSkuCode;
        } else if (dimensionList.contains(RDC_NAME)) {
            queryListGroup = InvSafetyStockParametersDO::getRdcName;
        } else if (dimensionList.contains(SKU_AND_RDC)){
            queryListGroup = item -> item.getSkuCode()+ StringConstants.UNDERLINE + item.getRdcName();
        }
        Map<String, List<InvSafetyStockParametersDO>> resultsGroupMap = StreamUtils.group(invStrategyResultsTobDOList, queryListGroup);
        return resultsGroupMap;
    } 

    private List<SafetyStockParametersImportTemplateVO> setRequest(List<InvSafetyStockParametersDO> resultsTobDOS, SafetyStockParametersImportTemplateVO importDatum) {
        //操作人
        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        List<SafetyStockParametersImportTemplateVO> list = new ArrayList<>();
        for (InvSafetyStockParametersDO resultsTobDO : resultsTobDOS) {
            SafetyStockParametersImportTemplateVO tobImportTemplateVO = new SafetyStockParametersImportTemplateVO();
            tobImportTemplateVO.setSkuCode(resultsTobDO.getSkuCode());
            tobImportTemplateVO.setSkuName(resultsTobDO.getSkuName());
            tobImportTemplateVO.setRdcCode(resultsTobDO.getRdcCode());
            tobImportTemplateVO.setRdcName(resultsTobDO.getRdcName());


            // 添加是否数据更改
            boolean updateFlag = false;
            if (importDatum.getServiceRatio() != null){
                BigDecimal serviceRatio = importDatum.getServiceRatio().divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                tobImportTemplateVO.setServiceRatio(serviceRatio);
                if (resultsTobDO.getServiceRatio() == null || !(tobImportTemplateVO.getServiceRatio().compareTo(resultsTobDO.getServiceRatio()) == 0)){
                    updateFlag = true;
                }
                tobImportTemplateVO.setServiceRatioStr(String.valueOf(String.format("%s%%", serviceRatio.multiply(BigDecimal.valueOf(100)).setScale(2))));
            }

            // 空值处理
            if (Objects.nonNull(resultsTobDO.getServiceRatio())){
                tobImportTemplateVO.setServiceRatioBeforeStr(String.valueOf(String.format("%s%%", resultsTobDO.getServiceRatio().multiply(BigDecimal.valueOf(100)).setScale(2))));

            }

            if (importDatum.getMinSafetyDays() != null){
                if (!(resultsTobDO.getMinSafetyDays() != null && Objects.equals(importDatum.getMinSafetyDays(), resultsTobDO.getMinSafetyDays()))){
                    updateFlag = true;
                }
                tobImportTemplateVO.setMinSafetyDays(importDatum.getMinSafetyDays());
            }else {
                tobImportTemplateVO.setMinSafetyDays(resultsTobDO.getMinSafetyDays());
            }
            tobImportTemplateVO.setMinSafetyDaysBefore(resultsTobDO.getMinSafetyDays());

            if (importDatum.getMaxSafetyDays() != null){
                if (!(resultsTobDO.getMaxSafetyDays() != null && Objects.equals(importDatum.getMaxSafetyDays(), resultsTobDO.getMaxSafetyDays()))){
                     updateFlag = true;
                }
                tobImportTemplateVO.setMaxSafetyDays(importDatum.getMaxSafetyDays());

            }else {
                tobImportTemplateVO.setMaxSafetyDays(resultsTobDO.getMaxSafetyDays());
            }
            tobImportTemplateVO.setMaxSafetyDaysBefore(resultsTobDO.getMaxSafetyDays());


            if (Objects.nonNull(currentAccount)) {
                tobImportTemplateVO.setOperatorCode(currentAccount.getId());
                tobImportTemplateVO.setOperatorName(currentAccount.getName());
            }

            if (updateFlag){
                list.add(tobImportTemplateVO);
            }
        }
        return list;
    }

    private boolean setErrMsg(List<InvSafetyStockParametersDO> resultsTobDOS, SafetyStockParametersImportTemplateVO importDatum, String name) {
        boolean flag = true;
        if (CollectionUtils.isEmpty(resultsTobDOS)) {
            importDatum.setErrorMsg(String.format(InvSafetyStockError.PARAMETERS_NOT_FOUND.getError(),name));
            flag = false;
        }
        return flag;
    }

    private boolean setErrMsg(List<SafetyStockParametersImportTemplateVO> importData, List<String> dimensionList) {
        for (SafetyStockParametersImportTemplateVO datum : importData) {
            if (dimensionList.contains(LV1)) {
                datum.setErrorMsg(String.format(InvSafetyStockError.PARAMETERS_NOT_FOUND.getError(),datum.getLv1CategoryName()));
            } else if (dimensionList.contains(LV2)) {
                datum.setErrorMsg(String.format(InvSafetyStockError.PARAMETERS_NOT_FOUND.getError(),datum.getLv2CategoryName()));
            } else if (dimensionList.contains(LV4)) {
                datum.setErrorMsg(String.format(InvSafetyStockError.PARAMETERS_NOT_FOUND.getError(),datum.getLv4CategoryName()));
            } else if (dimensionList.contains(ABC_TYPE_NAME)) {
                datum.setErrorMsg(String.format(InvSafetyStockError.PARAMETERS_NOT_FOUND.getError(),datum.getAbcTypeName()));
            } else if (dimensionList.contains(SKU)) {
                datum.setErrorMsg(String.format(InvSafetyStockError.PARAMETERS_NOT_FOUND.getError(),datum.getSkuCode()));
            } else if (dimensionList.contains(RDC_NAME)) {
                datum.setErrorMsg(String.format(InvSafetyStockError.PARAMETERS_NOT_FOUND.getError(),datum.getRdcName()));
            } else if (dimensionList.contains(SKU_AND_RDC)){
                datum.setErrorMsg(String.format(InvSafetyStockError.PARAMETERS_NOT_FOUND.getError(),datum.getSkuCode()+"及"+datum.getRdcName()));
            }
        }
        return false;
    }

    private void setParam(InvSafetyStockPageQueryRequest request, List<String> dimensionList, List<SafetyStockParametersImportTemplateVO> importData) {
        if (dimensionList.contains(LV1)) {
            List<String> map = StreamUtils.map(importData, SafetyStockParametersImportTemplateVO::getLv1CategoryName);
            request.setCategoryNameList(map);
        } else if (dimensionList.contains(LV2)) {
            List<String> map = StreamUtils.map(importData, SafetyStockParametersImportTemplateVO::getLv2CategoryName);
            request.setCategoryNameList(map);
        } else if (dimensionList.contains(LV4)) {
            List<String> map = StreamUtils.map(importData, SafetyStockParametersImportTemplateVO::getLv4CategoryName);
            request.setCategoryNameList(map);
        } else if (dimensionList.contains(ABC_TYPE_NAME)) {
            List<String> map = StreamUtils.map(importData, t -> SkuLevelEnum.getCodeByName(t.getAbcTypeName()));
            request.setAbcType(map);
        } else if (dimensionList.contains(SKU)) {
            List<String> map = StreamUtils.map(importData, SafetyStockParametersImportTemplateVO::getSkuCode);
            request.setSku(map);
        } else if (dimensionList.contains(RDC_NAME)) {
            List<String> map = StreamUtils.map(importData, SafetyStockParametersImportTemplateVO::getRdcName);
            request.setRdc(map);
        } else if (dimensionList.contains(SKU_AND_RDC)){
            List<String> skuMap = StreamUtils.map(importData, SafetyStockParametersImportTemplateVO::getSkuCode);
            List<String> rdcNameMap = StreamUtils.map(importData, SafetyStockParametersImportTemplateVO::getRdcName);
            request.setSku(skuMap);
            request.setRdcName(rdcNameMap);
        }
    }

    private boolean validateParam(List<String> dimensionList, List<SafetyStockParametersImportTemplateVO> importData, Map<String, List<InvSafetyStockParametersDO>> resultsGroupMap) {
        boolean flag = true;
        StringBuilder stringBuilder = new StringBuilder();
        for (SafetyStockParametersImportTemplateVO importDatum : importData) {
            stringBuilder.setLength(0);
            //校验字段非空
            validateNotNull(dimensionList, importDatum, stringBuilder);
            if (stringBuilder.length() > 0) {
                flag = false;
                importDatum.setErrorMsg(stringBuilder.toString());
                continue;
            }
            //校验数值格式与范围
            validateParamData(importDatum, stringBuilder,resultsGroupMap,dimensionList);
            if (stringBuilder.length() > 0) {
                flag = false;
                importDatum.setErrorMsg(stringBuilder.toString());
            }
        }
        return flag;
    }

    private void validateNotNull(List<String> dimension, SafetyStockParametersImportTemplateVO importDatum, StringBuilder stringBuilder) {
        if (dimension.contains(LV1) && StringUtils.isBlank(importDatum.getLv1CategoryName())) {
            stringBuilder.append(InvSafetyStockError.LV1_NAME_NOT_NULL.getError());
        } else if (dimension.contains(LV2) && StringUtils.isBlank(importDatum.getLv2CategoryName())) {
            stringBuilder.append(InvSafetyStockError.LV2_NAME_NOT_NULL.getError());
        } else if (dimension.contains(LV4) && StringUtils.isBlank(importDatum.getLv4CategoryName())) {
            stringBuilder.append(InvSafetyStockError.LV4_NAME_NOT_NULL.getError());
        } else if (dimension.contains(ABC_TYPE_NAME) && StringUtils.isBlank(importDatum.getAbcTypeName())) {
            stringBuilder.append(InvSafetyStockError.ABC_TYPE_NAME_NOT_NULL.getError());
        } else if ((dimension.contains(SKU) || dimension.contains(SKU_AND_RDC)) && StringUtils.isBlank(importDatum.getSkuCode())) {
            stringBuilder.append(InvSafetyStockError.SKU_CODE_NOT_NULL.getError());
        } else if ((dimension.contains(SKU) || dimension.contains(SKU_AND_RDC))  && StringUtils.isBlank(importDatum.getSkuName())) {
            stringBuilder.append(InvSafetyStockError.SKU_NAME_NOT_NULL.getError());
        } else if ((dimension.contains(RDC_NAME) || dimension.contains(SKU_AND_RDC)) && StringUtils.isBlank(importDatum.getRdcName())) {
            stringBuilder.append(InvSafetyStockError.RDC_NAME_NOT_NULL.getError());
        } else if (dimension.contains(SERVICE_RATIO) && StringUtils.isBlank(importDatum.getServiceRatioStr())){
            stringBuilder.append(InvSafetyStockError.SERVICE_RATIO_NOT_NULL.getError());
        } else if (dimension.contains(MIN_SAFETY_DAYS) && StringUtils.isBlank(importDatum.getMinSafetyDaysStr())) {
            stringBuilder.append(InvSafetyStockError.MIN_SAFETY_DAYS_NOT_NULL.getError());
        } else if (dimension.contains(MAX_SAFETY_DAYS) && StringUtils.isBlank(importDatum.getMaxSafetyDaysStr())) {
            stringBuilder.append(InvSafetyStockError.MAX_SAFETY_DAYS_NOT_NULL.getError());
        }
    }

    private void validateParamData(SafetyStockParametersImportTemplateVO importDatum, StringBuilder stringBuilder,Map<String, List<InvSafetyStockParametersDO>> resultsGroupMap,List<String> dimensionList) {
        Double serviceRatio = NumUtils.safeDouble(importDatum.getServiceRatioStr());
        Integer  minSafetyDays = NumUtils.safeInt(importDatum.getMinSafetyDaysStr());
        Integer maxSafetyDays = NumUtils.safeInt(importDatum.getMaxSafetyDaysStr());
        List<InvSafetyStockParametersDO> resultsTobDOList = new ArrayList<>();
        if (dimensionList.contains(LV1)) {
            resultsTobDOList = resultsGroupMap.get(importDatum.getLv1CategoryName());
        } else if (dimensionList.contains(LV2)) {
            resultsTobDOList = resultsGroupMap.get(importDatum.getLv2CategoryName());
        } else if (dimensionList.contains(LV4)) {
            resultsTobDOList = resultsGroupMap.get(importDatum.getLv4CategoryName());
        } else if (dimensionList.contains(ABC_TYPE_NAME)) {
            resultsTobDOList = resultsGroupMap.get(SkuLevelEnum.getCodeByName(importDatum.getAbcTypeName()));
        } else if (dimensionList.contains(SKU)) {
            resultsTobDOList = resultsGroupMap.get(importDatum.getSkuCode());
        } else if (dimensionList.contains(RDC_NAME)) {
            resultsTobDOList = resultsGroupMap.get(importDatum.getRdcName());
        } else if (dimensionList.contains(SKU_AND_RDC)){
            resultsTobDOList = resultsGroupMap.get(importDatum.getSkuCode() + StringConstants.UNDERLINE + importDatum.getRdcName());
        }

        if (dimensionList.contains(SERVICE_RATIO)  ) {
            if (serviceRatio < 0 || serviceRatio > 100){
                stringBuilder.append(InvSafetyStockError.SERVICE_RATIO_ERROR.getError());
            }else {
                importDatum.setServiceRatio(new BigDecimal(serviceRatio));
            }
        }

        if (minSafetyDays != null && (minSafetyDays < 0 || minSafetyDays > 100)){
            stringBuilder.append(InvSafetyStockError.MIN_SAFETY_DAYS_ERROR.getError());
        }

        if (maxSafetyDays != null && (maxSafetyDays < 0 || maxSafetyDays > 100)){
            stringBuilder.append(InvSafetyStockError.MAX_SAFETY_DAYS_ERROR.getError());
        }

        /**
         * 三种情况考虑安全库存参数
         */
        StringBuilder safetyDaysBuilder = new StringBuilder();
        for (InvSafetyStockParametersDO stockParametersDO : resultsTobDOList) {
            safetyDaysBuilder.setLength(0);
            // 一。导入纬度有最小安全库存参数没有最大安全库存参数
            // 二。导入纬度有最大安全库存参数没有最大安全库存参数
            // 三。导入纬度有最小安全库存参数有最大安全库存参数
            if (dimensionList.contains(MIN_SAFETY_DAYS) && !dimensionList.contains(MAX_SAFETY_DAYS)){
                if (stockParametersDO.getMaxSafetyDays() != null && minSafetyDays >= stockParametersDO.getMaxSafetyDays()){
                    safetyDaysBuilder.append(String.format(InvSafetyStockError.BUILDER_TARGET_LESS_SAFETY.getError(),
                            stockParametersDO.getSkuCode(), stockParametersDO.getRdcName(), minSafetyDays, stockParametersDO.getMaxSafetyDays())).append(System.lineSeparator());
                }else {
                    importDatum.setMinSafetyDays(minSafetyDays);
                }
            } else if (dimensionList.contains(MAX_SAFETY_DAYS) && !dimensionList.contains(MIN_SAFETY_DAYS)) {

                if (stockParametersDO.getMinSafetyDays() != null && stockParametersDO.getMinSafetyDays() >= maxSafetyDays){
                    safetyDaysBuilder.append(String.format(InvSafetyStockError.BUILDER_TARGET_LESS_SAFETY.getError(),
                            stockParametersDO.getSkuCode(), stockParametersDO.getRdcName(), stockParametersDO.getMinSafetyDays(), maxSafetyDays)).append(System.lineSeparator());
                }else {
                    importDatum.setMaxSafetyDays(maxSafetyDays);
                }
            } else if (dimensionList.contains(MIN_SAFETY_DAYS) && dimensionList.contains(MAX_SAFETY_DAYS)) {
                if (minSafetyDays >= maxSafetyDays){
                    safetyDaysBuilder.append(String.format(InvSafetyStockError.BUILDER_TARGET_LESS_SAFETY.getError(),
                            stockParametersDO.getSkuCode(), stockParametersDO.getRdcName(), minSafetyDays, maxSafetyDays)).append(System.lineSeparator());
                }else {
                    importDatum.setMinSafetyDays(minSafetyDays);
                    importDatum.setMaxSafetyDays(maxSafetyDays);
                }
            }
            stringBuilder.append(safetyDaysBuilder);
        }

    }

}
