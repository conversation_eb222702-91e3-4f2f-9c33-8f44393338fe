package cn.aliyun.ryytn.modules.inv.eiport;


import cn.aliyun.ryytn.modules.inv.api.transfer.InvTransferPlanService;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.eiport.converter.MergeFieldToStringConverter;
import cn.aliyun.ryytn.modules.inv.entity.transfer.request.TransferPlanPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.template.ExportDataTemplate;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 */
@ExportService(value = "transferPlanEIportDataTemplate", filename = "调拨计划数据导出", async = false)
public class TransferPlanEIportDataTemplate implements ExportDataTemplate<TransferPlanPageQueryRequest> {
    @Autowired
    private InvTransferPlanService invTransferPlanService;

    @Override
    public ExcelMetaInfoWrapper getExportWrapper(TransferPlanPageQueryRequest request) {
        request.setPaging(false);
        PageMetaInfoWrapper pageMetaInfoWrapper = invTransferPlanService.pageQueryData(request);
        return GeiCommonConvert.convert(pageMetaInfoWrapper, ExcelMetaInfoWrapper.class).addWriteConverter(new MergeFieldToStringConverter());
    }

}
