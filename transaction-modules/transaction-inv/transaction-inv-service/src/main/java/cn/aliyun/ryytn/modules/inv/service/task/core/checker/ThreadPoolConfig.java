package cn.aliyun.ryytn.modules.inv.service.task.core.checker;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Supplier;

public class ThreadPoolConfig {
    public static final int CORE_THREAD = Runtime.getRuntime().availableProcessors();

    // 拒绝策略
    public static class RejectedExecution {
        // 交由调用线程执行
        public static final Supplier<RejectedExecutionHandler> CallerRunsPolicy = ThreadPoolExecutor.CallerRunsPolicy::new;
        // 抛弃任务并抛异常
        public static final Supplier<RejectedExecutionHandler> AbortPolicy = ThreadPoolExecutor.AbortPolicy::new;
        // 抛弃任务不抛异常
        public static final Supplier<RejectedExecutionHandler> DiscardPolicy = ThreadPoolExecutor.DiscardPolicy::new;
        // 丢弃阻塞队列最前面的任务，添加这个新任务
        public static final Supplier<RejectedExecutionHandler> DiscardOldestPolicy = ThreadPoolExecutor.DiscardOldestPolicy::new;
    }
}