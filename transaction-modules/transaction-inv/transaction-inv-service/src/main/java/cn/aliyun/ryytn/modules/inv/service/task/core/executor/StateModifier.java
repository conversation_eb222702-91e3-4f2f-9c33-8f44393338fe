package cn.aliyun.ryytn.modules.inv.service.task.core.executor;

import cn.aliyun.ryytn.modules.inv.api.task.JobExecuteService;
import cn.aliyun.ryytn.modules.inv.entity.task.entity.JobExecContext;
import cn.aliyun.ryytn.modules.inv.entity.task.enums.JobCore;
import cn.aliyun.ryytn.modules.inv.api.task.enums.TaskStatusEnum;
import cn.aliyun.ryytn.modules.inv.common.function.MultiConsumer;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskCoreStatDO;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskExecChainDO;
import cn.aliyun.ryytn.modules.inv.service.task.manager.ExecChainManager;
import cn.aliyun.ryytn.modules.inv.service.task.manager.TaskCoreStatManager;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-12-12 09:47
 * @description
 */
@Component
public class StateModifier {
    @Resource
    private JobExecuteService jobExecuteService;
    @Resource
    private ExecChainManager execChainManager;
    @Resource
    private TaskCoreStatManager taskCoreStatManager;

    private final Map<TaskStatusEnum, MultiConsumer<ScpTaskExecChainDO, JobExecContext, String>> STRATEGY = new HashMap<>();

    public static String buildErrorMsg(Exception exception) {
        return String.format("「%s」error is: %s", exception.toString(), exception.getMessage());
    }

    @PostConstruct
    public void init() {
        STRATEGY.put(TaskStatusEnum.FAILED, (chain, context, errorMsg) -> {
            Long taskId = -1L;
            if (Objects.nonNull(context)) {
                taskId = context.getTaskId();
            }
            if (Objects.nonNull(chain)) {
                List<ScpTaskExecChainDO> chainNodeByBatch = execChainManager.getChainNodeByBatch(chain.getBatchNo());
                ScpTaskExecChainDO execChainNode = chainNodeByBatch.get(0);
                // 按照批次，批量修改
                execChainNode.setDateExpr(null);
                execChainNode.setChainNodeId(null);
                execChainNode.setStatus(TaskStatusEnum.FAILED.getCode());
                execChainNode.setErrorMsg(errorMsg);
                execChainManager.updateChainNode(execChainNode);
                taskId = chain.getRootTaskId();
            }
            jobExecuteService.updateBaseTask(taskId, TaskStatusEnum.FAILED, errorMsg, null, LocalDateTime.now());
            updateCoreStat(null, null);
            if (Objects.nonNull(context)) {
                clearContext(context);
            }
        });
        STRATEGY.put(TaskStatusEnum.RUNNING, (chain, context, errorMsg) -> {
            chain.setStatus(TaskStatusEnum.RUNNING.getCode());
            chain.setSubmitTime(LocalDateTime.now());
            //      设置核心模块状态记录的 批次号
            updateCoreStat(chain.getBatchNo(), null);
            //      设置执行链节点状态为执行中
            execChainManager.updateChainNode(chain);
            //      设置base任务状态为执行中
            jobExecuteService.updateBaseTask(chain.getRootTaskId(), TaskStatusEnum.RUNNING, null, null, null);
            chain.setBeforeDateExpr(null);
        });
        STRATEGY.put(TaskStatusEnum.COMPLETED, (chain, context, errorMsg) -> {
            List<ScpTaskExecChainDO> chainNodeList = execChainManager.getChainNodeByBatch(chain.getBatchNo());
            int completed = 0;
            for (ScpTaskExecChainDO node : chainNodeList) {
                if (equalsTaskExecChain(chain, node)) {
                    node.setBeforeDateExpr(node.getDateExpr());
                    node.setDateExpr(null);
                    node.setStatus(TaskStatusEnum.COMPLETED.getCode());
                    node.setErrorMsg(errorMsg);
                    execChainManager.updateChainNode(node);
                }
                if (TaskStatusEnum.COMPLETED.isThis(node.getStatus())) {
                    completed++;
                }
            }
            // 根据批次获取的节点都为成功后，再修改任务列表状态为已完成
            if (completed == chainNodeList.size()) {
                jobExecuteService.updateBaseTask(chain.getRootTaskId(), TaskStatusEnum.COMPLETED, errorMsg, null, LocalDateTime.now());
                updateCoreStat(null, null);
            }
        });
    }

    private Boolean equalsTaskExecChain(ScpTaskExecChainDO chain, ScpTaskExecChainDO node) {
        return Objects.equals(chain.getBatchNo(), node.getBatchNo())
                && Objects.equals(chain.getRootTaskId(), node.getRootTaskId()) && Objects.equals(chain.getRootTaskType(), node.getRootTaskType()) && Objects.equals(chain.getDateExpr(), node.getDateExpr());
    }

    public void modify(ScpTaskExecChainDO chainNode, JobExecContext context, TaskStatusEnum status, String error) {
        MultiConsumer<ScpTaskExecChainDO, JobExecContext, String> modifyStrategy = STRATEGY.get(status);
        if (Objects.nonNull(modifyStrategy)) {
            modifyStrategy.accept(chainNode, context, error);
        }
    }

    private void clearContext(JobExecContext context) {
        context.setTaskCfgList(null);
    }

    private void updateCoreStat(Long batchNo, TaskStatusEnum taskStatus) {
        ScpTaskCoreStatDO scpTaskCoreStatDO = new ScpTaskCoreStatDO();
        scpTaskCoreStatDO.setBatchNo(batchNo);
        scpTaskCoreStatDO.setStatus(Objects.isNull(taskStatus) ? null : taskStatus.getCode());
        scpTaskCoreStatDO.setCoreCode(JobCore.EXECUTOR.getCode());
        taskCoreStatManager.updateByCore(scpTaskCoreStatDO);
    }
}
