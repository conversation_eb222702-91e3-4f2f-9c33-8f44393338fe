package cn.aliyun.ryytn.modules.inv.service.transfer.impl;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.inv.api.transfer.InvTransferPlanService;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.*;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeFieldWrapper;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeMonitor;
import cn.aliyun.ryytn.modules.inv.common.enums.BooleanEnum;
import cn.aliyun.ryytn.modules.inv.common.enums.ColorEnum;
import cn.aliyun.ryytn.modules.inv.common.exception.Assert;
import cn.aliyun.ryytn.modules.inv.common.exception.ErrorCode;
import cn.aliyun.ryytn.modules.inv.common.utils.JsonUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.ReflectionUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StrUtils;
import cn.aliyun.ryytn.modules.inv.common.utils.StringConstants;
import cn.aliyun.ryytn.modules.inv.constant.transfer.enums.MetaMergerEnum;
import cn.aliyun.ryytn.modules.inv.constant.transfer.error.TransferError;
import cn.aliyun.ryytn.modules.inv.entity.transfer.dos.InventoryData;
import cn.aliyun.ryytn.modules.inv.entity.transfer.dos.ScpCommonConfigMeta;
import cn.aliyun.ryytn.modules.inv.entity.transfer.request.*;
import cn.aliyun.ryytn.modules.inv.service.transfer.CommonConfigService;
import cn.aliyun.ryytn.modules.inv.service.transfer.vo.CommonConfigVO;
import cn.aliyun.ryytn.modules.inv.transfer.dao.CommonConfigMetaDao;
import cn.aliyun.ryytn.modules.inv.transfer.dao.InventoryDataDao;
import cn.hutool.core.map.MapUtil;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.collections4.CollectionUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/21 13:38
 * @description：
 */
@Service
public class InvTransferPlanServiceImpl implements InvTransferPlanService {


    @Resource
    private CommonConfigMetaDao commonConfigMetaDao;

    @Resource
    private CommonConfigService commonConfigService;

    @Resource
    private InventoryDataDao inventoryDataDao;

    @Override
    @Transactional
    public Long saveMetaConfig(TransferConfigQueryRequest request) {
       // Assert.isTrue(CollectionUtils.isNotEmpty(requests), ErrorCode.ILLEGAL_ARGUMENT, TransferError.REQUEST_NOT_NULL.getError());

        Long metaFlag = null;
        if (CollectionUtils.isNotEmpty(request.getMetaList())){
             metaFlag = saveMateList(request.getMetaList());

        }

        if (CollectionUtils.isNotEmpty(request.getConfigList())){
           commonConfigService.save(request.getConfigList());
        }

        return metaFlag;

    }

    private Long saveMateList(List<TransferConfigMetaInsertRequest> requests){

        Account currentAccount = null;
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();
        }
        Assert.isTrue(Objects.nonNull(currentAccount), ErrorCode.ILLEGAL_ARGUMENT, TransferError.OPERATOR_NOT_NULL.getError());
        //查询当前操作人下是否有meta信息，如果没有，则查询超级管理员下的信息
        ScpCommonConfigMetaRequest configMetaVO = new ScpCommonConfigMetaRequest();
        configMetaVO.setCreatorCode(currentAccount.getId());
        List<ScpCommonConfigMeta> configMetaList = commonConfigMetaDao.selectByCondition(JsonUtils.toMap(configMetaVO));
        if (CollectionUtils.isEmpty(configMetaList)){
            configMetaVO.setCreatorCode(CommonConstants.SYSADMIN_ROLE_ID);
            configMetaList = commonConfigMetaDao.selectByCondition(JsonUtils.toMap(configMetaVO));
        }
        Assert.isTrue(CollectionUtils.isNotEmpty(configMetaList), ErrorCode.ILLEGAL_ARGUMENT, TransferError.MEAT_IS_NULL.getError());
        //查出来meta信息之后，和requests做对比，requests里面的hidden为false，sort为传入数据，否则hidden为ture，sort为999
        Map<String, TransferConfigMetaInsertRequest> requestCodeMap = requests.stream().collect(Collectors.toMap(TransferConfigMetaInsertRequest::getCode, v -> v, (v1, v2) -> v1));
        // 合并名称数组
        Map<String, List<TransferConfigMetaInsertRequest>> mergerMap = requests.stream().filter(t -> Objects.nonNull(t.getMergerName())).collect(Collectors.groupingBy(t -> t.getMergerName()));

        // 根据configList保存信息是否隐藏显示
        List<ScpCommonConfigMeta> addScpCommonConfigMetaList = new ArrayList<>();
        for (ScpCommonConfigMeta configMeta : configMetaList) {
            TransferConfigMetaInsertRequest metaInsertRequest = requestCodeMap.get(configMeta.getCode());
            if (metaInsertRequest != null){
                List<TransferConfigMetaInsertRequest> groupByMergerNameList = mergerMap.get(metaInsertRequest.getMergerName());
                if (StringUtils.isNotBlank(metaInsertRequest.getMerge()) && CollectionUtils.isNotEmpty(groupByMergerNameList) && groupByMergerNameList.size()>1){
                    if (metaInsertRequest.getMerge().equals(MetaMergerEnum.ROW.getCode())){
                        configMeta.setParent(metaInsertRequest.getMergerName());
                        configMeta.setValue(metaInsertRequest.getAnotherName());
                        configMeta.setMergerMetaValue(null);
                        configMeta.setFormat("%s");
                    } else if (metaInsertRequest.getMerge().equals(MetaMergerEnum.LINE.getCode())) {
                        configMeta.setMergerMetaValue(metaInsertRequest.getMergerName());
                        configMeta.setFormat(String.format("%s:", metaInsertRequest.getAnotherName())+"%s");
                        configMeta.setParent(null);
                    }
                    configMeta.setYesOrNotMerge(BooleanEnum.TRUE.getICode());
                    configMeta.setMergerMetaCode(metaInsertRequest.getMergerName());
                }else {
                    configMeta.setYesOrNotMerge(BooleanEnum.FALSE.getICode());
                    configMeta.setParent(null);
                    configMeta.setMergerMetaCode(null);
                    configMeta.setMergerMetaValue(null);
                    configMeta.setFormat("%s");
                    if (metaInsertRequest.getHidden() != null){
                        configMeta.setHidden(BooleanEnum.getByBool(metaInsertRequest.getHidden()).getICode());
                    }
                }
                configMeta.setSort(metaInsertRequest.getSort());
            }else {
                configMeta.setSort(999);
                configMeta.setHidden(BooleanEnum.TRUE.getICode());
            }

            if (!Objects.equals(configMeta.getCreatorCode(), currentAccount.getId())){
                configMeta.setCreatorCode(currentAccount.getId());
                configMeta.setCreatorName(currentAccount.getName());
                addScpCommonConfigMetaList.add(configMeta);

            }
        }

        // 批量更新保存
        Long flag;
        if (CollectionUtils.isNotEmpty(addScpCommonConfigMetaList)){
            flag = commonConfigMetaDao.batchSaveScpCommonConfigMeta(addScpCommonConfigMetaList);
        }else {
            flag = commonConfigMetaDao.batchUpdateScpCommonConfigMeta(configMetaList);
        }
        return flag;

    }

    /**
     * 若meta为空，默认查询超管
     * 根据config配置项配置meta是否显示，config code关联，通过value匹配code
     * @param request
     * @return
     */
    @Override
    public MetaInfoWrapper pageLayoutQuery(TransferConfigQueryRequest request) throws JsonProcessingException {

        if (CollectionUtils.isEmpty(request.getMetaList())){
            return noRquest();
        }else {
            return haveRquest(request);
        }
    }

    /**
     * 查询当前登录人的meta，没有查询超管的
     * @return
     */
    private List<ScpCommonConfigMeta> getScpCommonConfigMetaList (){

        ScpCommonConfigMetaRequest configMetaVO = new ScpCommonConfigMetaRequest();
        Account currentAccount = null;
        List<ScpCommonConfigMeta> configMetaList = new ArrayList<>();
        if (Objects.nonNull(ServiceContextUtils.currentSession())) {
            currentAccount = ServiceContextUtils.currentSession().getAccount();

            if (currentAccount != null){
                configMetaVO.setCreatorCode(currentAccount.getId());
                configMetaList = commonConfigMetaDao.selectByCondition(JsonUtils.toMap(configMetaVO));
            }
        }

        if (CollectionUtils.isEmpty(configMetaList)){
            configMetaVO.setCreatorCode(CommonConstants.SYSADMIN_ROLE_ID);
            configMetaList = commonConfigMetaDao.selectByCondition(JsonUtils.toMap(configMetaVO));
        }

        Assert.isTrue(CollectionUtils.isNotEmpty(configMetaList), ErrorCode.ILLEGAL_ARGUMENT, TransferError.TRANS_FER_PLAN_IS_NULL.getError());
        return configMetaList;
    }

    // 参数都为空情况下
    private MetaInfoWrapper noRquest(){

        List<ScpCommonConfigMeta> scpCommonConfigMetaList = getScpCommonConfigMetaList();

        List<TransferConfigMetaInsertRequest> metaInsertRequestList = new ArrayList<>();
        for (ScpCommonConfigMeta scpCommonConfigMeta : scpCommonConfigMetaList) {

            if (StringUtils.isNotBlank(scpCommonConfigMeta.getMergerMetaValue())){

                scpCommonConfigMeta.setMerge(MetaMergerEnum.LINE.getCode());
                scpCommonConfigMeta.setMergerName(scpCommonConfigMeta.getMergerMetaValue());
                scpCommonConfigMeta.setAnotherName(scpCommonConfigMeta.getValue());

            }

            if (StringUtils.isNotBlank(scpCommonConfigMeta.getParent())){
                scpCommonConfigMeta.setMerge(MetaMergerEnum.ROW.getCode());
                scpCommonConfigMeta.setMergerName(scpCommonConfigMeta.getParent());
                scpCommonConfigMeta.setAnotherName(scpCommonConfigMeta.getValue());
            }
/*            if ("skuCode".equals(scpCommonConfigMeta.getCode()) || "skuName".equals(scpCommonConfigMeta.getCode())){
                scpCommonConfigMeta.setMergerMetaValue("sku");
                scpCommonConfigMeta.setFormat(String.format("%s:", scpCommonConfigMeta.getValue())+"%s");
                scpCommonConfigMeta.setYesOrNotMerge(BooleanEnum.TRUE.getICode());
                scpCommonConfigMeta.setSort(scpCommonConfigMeta.getSort());
                scpCommonConfigMeta.setHidden(BooleanEnum.FALSE.getICode());

            }*/

            TransferConfigMetaInsertRequest metaInsertRequest = new TransferConfigMetaInsertRequest();
            metaInsertRequest.setName(scpCommonConfigMeta.getValue());
            metaInsertRequest.setCode(scpCommonConfigMeta.getCode());
            metaInsertRequest.setMergerName(scpCommonConfigMeta.getMergerName());
            metaInsertRequest.setAnotherName(scpCommonConfigMeta.getAnotherName());
            metaInsertRequest.setMerge(scpCommonConfigMeta.getMerge());
            metaInsertRequest.setMergerName(scpCommonConfigMeta.getMergerName());
            metaInsertRequest.setSort(scpCommonConfigMeta.getSort());
            metaInsertRequest.setConfigCode(scpCommonConfigMeta.getConfigCode());
            metaInsertRequest.setGroupName(scpCommonConfigMeta.getGroupName());
            metaInsertRequestList.add(metaInsertRequest);
        }

        Map<String, TransferConfigMetaInsertRequest> requestCodeMap = metaInsertRequestList.stream().collect(Collectors.toMap(TransferConfigMetaInsertRequest::getCode, v -> v, (v1, v2) -> v1));

        return compleMetaInfoWrapper(scpCommonConfigMetaList, requestCodeMap);

    }

    // 都有参数情况下
    private MetaInfoWrapper haveRquest(TransferConfigQueryRequest request) throws JsonProcessingException {

        Assert.isTrue(CollectionUtils.isNotEmpty(request.getConfigList()), ErrorCode.ILLEGAL_ARGUMENT, TransferError.CONFIGLIST_NOT_NULL.getError());
        // 有参数情况传参
        List<CommonConfigValueInsertRequest> configList = request.getConfigList().stream().filter(s-> Objects.nonNull(s.getConfigValue())).collect(Collectors.toList());
        List<TransferConfigMetaInsertRequest> metaRequestList = request.getMetaList();

        metaRequestList = metaRequestList.stream().filter(s -> StringUtils.isNotEmpty(s.getConfigCode())).collect(Collectors.toList());

       // Map<String, CommonConfigValueInsertRequest> configCodeMap = configList.stream().collect(Collectors.toMap(CommonConfigValueInsertRequest::getConfigCode, v -> v, (v1, v2) -> v2));

        Map<String, Map<String, CommonConfigValueInsertRequest>> groupNameConfigCodeMap =
                configList.stream()
                        .collect(Collectors.groupingBy(
                                CommonConfigValueInsertRequest::getGroupName,
                                Collectors.toMap(
                                        CommonConfigValueInsertRequest::getConfigCode,
                                        v -> v,
                                        (v1, v2) -> v2  // 如果有重复的 configCode，保留后者
                                )
                        ));

        Map<String, List<TransferConfigMetaInsertRequest>> mergerMap = metaRequestList.stream().filter(t -> Objects.nonNull(t.getMergerName())).collect(Collectors.groupingBy(TransferConfigMetaInsertRequest::getMergerName));

        Map<String, TransferConfigMetaInsertRequest> requestCodeMap = metaRequestList.stream().collect(Collectors.toMap(TransferConfigMetaInsertRequest::getCode, v -> v, (v1, v2) -> v1));


/*        Collection<MetaInfo>  childrenMetaInfoList = new ArrayList<>();
        for (TransferConfigMetaInsertRequest metaInsertRequest : metaRequestList) {
            Collection<MetaInfo> children = metaInsertRequest.getChildren();
            if (CollectionUtils.isNotEmpty(children)){
                childrenMetaInfoList.addAll(children);
            }
        }

        Map<String, MetaInfo> metaInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(childrenMetaInfoList)){
            childrenMetaInfoList.stream().collect(Collectors.toMap(MetaInfo::getCode, v -> v, (v1, v2) -> v1));
        }*/

        List<ScpCommonConfigMeta> metaList = new ArrayList<>();
        // 根据配置类判断是否隐藏meta
        for (TransferConfigMetaInsertRequest metaRequest : metaRequestList) {
            // 校验必填选项
            checkMetaRequest(metaRequest);

            Map<String, CommonConfigValueInsertRequest> configCodeMap = groupNameConfigCodeMap.get(metaRequest.getGroupName());
            ScpCommonConfigMeta scpCommonConfigMeta = new ScpCommonConfigMeta();
            scpCommonConfigMeta.setValue(metaRequest.getName());
            scpCommonConfigMeta.setCode(metaRequest.getCode());
            scpCommonConfigMeta.setSort(metaRequest.getSort());



            // 根据commonConfigRequest的config value值对比meta的code值
            // 优先比对合并字段名称其次比对code
            if (MapUtil.isNotEmpty(configCodeMap)){
                CommonConfigValueInsertRequest commonConfigRequest = configCodeMap.get(metaRequest.getConfigCode());
                if (Objects.nonNull(commonConfigRequest)){

                    List<String> configValueList = (List<String>) commonConfigRequest.getConfigValue();
                    if (configValueList.contains(metaRequest.getMergerName()) || configValueList.contains(metaRequest.getCode())){
                        if (Objects.nonNull(metaRequest.getMerge()) && Objects.equals(MetaMergerEnum.ROW.getCode(), metaRequest.getMerge())) {
                            if (Objects.nonNull(mergerMap.get(metaRequest.getMergerName())) && mergerMap.get(metaRequest.getMergerName()).size() > 1) {
                                scpCommonConfigMeta.setParent(metaRequest.getMergerName());
                                scpCommonConfigMeta.setValue(metaRequest.getAnotherName());
                                scpCommonConfigMeta.setYesOrNotMerge(BooleanEnum.TRUE.getICode());

                                scpCommonConfigMeta.setAnotherName(metaRequest.getAnotherName());
                                scpCommonConfigMeta.setMerge(metaRequest.getMerge());
                                scpCommonConfigMeta.setMergerName(metaRequest.getMergerName());
                            }
                        } else if (Objects.nonNull(metaRequest.getMerge()) && Objects.equals(MetaMergerEnum.LINE.getCode(), metaRequest.getMerge())) {
                            if (Objects.nonNull(mergerMap.get(metaRequest.getMergerName())) && mergerMap.get(metaRequest.getMergerName()).size() > 1) {
                                scpCommonConfigMeta.setMergerMetaValue(metaRequest.getMergerName());
                                scpCommonConfigMeta.setFormat(String.format("%s:", metaRequest.getAnotherName())+"%s");
                                scpCommonConfigMeta.setYesOrNotMerge(BooleanEnum.TRUE.getICode());

                                scpCommonConfigMeta.setAnotherName(metaRequest.getAnotherName());
                                scpCommonConfigMeta.setMerge(metaRequest.getMerge());
                                scpCommonConfigMeta.setMergerName(metaRequest.getMergerName());
                            }
                        }
                        scpCommonConfigMeta.setSort(metaRequest.getSort());
                        scpCommonConfigMeta.setHidden(BooleanEnum.FALSE.getICode());
                    }else {
                        scpCommonConfigMeta.setHidden(BooleanEnum.TRUE.getICode());
                    }

                    scpCommonConfigMeta.setConfigCode(commonConfigRequest.getConfigCode());
                    scpCommonConfigMeta.setGroupName(commonConfigRequest.getGroupName());

                }else {
                    scpCommonConfigMeta.setHidden(BooleanEnum.TRUE.getICode());
                }
            }else {
                scpCommonConfigMeta.setHidden(BooleanEnum.TRUE.getICode());
            }


/*            MetaInfo metaInfo = metaInfoMap.get(scpCommonConfigMeta.getCode());
            if (metaInfo != null){

                scpCommonConfigMeta.setParent(metaInfo.getMergerName());
                scpCommonConfigMeta.setValue(metaInfo.getAnotherName());
                scpCommonConfigMeta.setYesOrNotMerge(BooleanEnum.TRUE.getICode());

                scpCommonConfigMeta.setAnotherName(metaInfo.getAnotherName());
                scpCommonConfigMeta.setSort(metaInfo.getSort());
                scpCommonConfigMeta.setMerge(metaInfo.getMerge());
                scpCommonConfigMeta.setMergerName(metaInfo.getMergerName());
                scpCommonConfigMeta.setHidden(BooleanEnum.FALSE.getICode());
            }*/


            //默认skuCoed 和 skuName合并
/*            if ("skuCode".equals(scpCommonConfigMeta.getCode()) || "skuName".equals(scpCommonConfigMeta.getCode())){
                scpCommonConfigMeta.setMergerMetaValue("sku");
                scpCommonConfigMeta.setFormat(String.format("%s:", metaRequest.getName())+"%s");
                scpCommonConfigMeta.setYesOrNotMerge(BooleanEnum.TRUE.getICode());
                scpCommonConfigMeta.setSort(metaRequest.getSort());
                scpCommonConfigMeta.setHidden(BooleanEnum.FALSE.getICode());

            } else if ("sku".equals(scpCommonConfigMeta.getCode())) {
                continue;
            }*/

            metaList.add(scpCommonConfigMeta);
        }

        return compleMetaInfoWrapper(metaList, requestCodeMap);
    }


    private MetaInfoWrapper compleMetaInfoWrapper(List<ScpCommonConfigMeta> configMetaList, Map<String, TransferConfigMetaInsertRequest> requestCodeMap){



        //configMetaList根据getMergerMetaValue分组，循环V：List<ScpCommonConfigMeta>
        Map<String, List<ScpCommonConfigMeta>> groupMap = configMetaList.stream().filter(s -> StringUtils.isNotBlank(s.getMergerMetaValue())).collect(Collectors.groupingBy(ScpCommonConfigMeta::getMergerMetaValue));

        List<InventoryData> inventoryDataList = inventoryDataDao.selectInventoryDataList();
        List<Map<String, Object>> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventoryDataList)){
            list = GeiCommonConvert.convertList(inventoryDataList);
        }
        for (Map<String, Object> map : list) {
            // 先组装meta信息中merger_meta_value为空的
            groupMap.forEach((k,v)->{
                List<MergeFieldWrapper> merged = new ArrayList<>();
                for (ScpCommonConfigMeta scpCommonConfigMeta : v) {
                    String finalValue = StrUtils.format(scpCommonConfigMeta.getFormat(), true, map.get(scpCommonConfigMeta.getCode()));
                    MergeFieldWrapper wrapper = MergeFieldWrapper.of(scpCommonConfigMeta.getNewLine(), finalValue, MetaType.TEXT);
                    wrapper.setEntity(map);
                    wrapper.setPrefix("");
                    wrapper.setSuffix("");
                    wrapper.setColor(null);
                    wrapper.setSpaces(1);
                    wrapper.setEditable(false);
                    merged.add( wrapper );
                    MergeMonitor.advice(scpCommonConfigMeta.getValue(), merged, wrapper);
                    wrapper.setEntity(null);
                }
                map.put(k, merged);
            });

        }


        List<MetaInfo> metaInfoList = new LinkedList<>();
        for (ScpCommonConfigMeta scpCommonConfigMeta : configMetaList) {

            boolean MergerMetaValueFlag = StringUtils.isNotBlank(scpCommonConfigMeta.getMergerMetaValue());
            TransferConfigMetaInsertRequest metaInsertRequest = requestCodeMap.get(scpCommonConfigMeta.getCode());
            MetaInfo metaInfo = MetaInfo
                    .builder()
                    .code(scpCommonConfigMeta.getCode())
                    .name(scpCommonConfigMeta.getValue())
                    .parent(scpCommonConfigMeta.getParent())
                    .anotherName(metaInsertRequest != null ? metaInsertRequest.getName() : scpCommonConfigMeta.getAnotherName())
                    .primaryKey(false)
                    .primarySort(0)
                    .format(scpCommonConfigMeta.getFormat())
                    .fixed("")
                    .hidden(MergerMetaValueFlag || BooleanEnum.TRUE.isThis(scpCommonConfigMeta.getHidden()))
                    .sort(scpCommonConfigMeta.getSort() != null ? scpCommonConfigMeta.getSort() : 999)
                    .validateTag("")
                    .isMerged(BooleanEnum.isTrue(scpCommonConfigMeta.getYesOrNotMerge()))
                    .configCode(scpCommonConfigMeta.getConfigCode())
                    .groupName(scpCommonConfigMeta.getGroupName())
                    .merge(scpCommonConfigMeta.getMerge())
                    .mergerName(scpCommonConfigMeta.getMergerName())
                    .anotherName(scpCommonConfigMeta.getAnotherName())
                    .build();
            metaInfoList.add(metaInfo);
            MetaMonitor.advice(metaInfo);
        }

        // 添加合并的meta
        groupMap.forEach((k ,v)->{
            ScpCommonConfigMeta scpCommonConfigMeta = v.get(0);

            MetaInfo metaInfo = MetaInfo
                    .builder()
                    .code(scpCommonConfigMeta.getMergerMetaValue())
                    .name(scpCommonConfigMeta.getMergerMetaValue())
                    .parent(scpCommonConfigMeta.getParent())

                    .primaryKey(false)
                    .primarySort(0)
                    .format(scpCommonConfigMeta.getFormat())
                    .fixed("")
                    .hidden(BooleanEnum.TRUE.isThis(scpCommonConfigMeta.getHidden()))
                    .sort(scpCommonConfigMeta.getSort() != null ? scpCommonConfigMeta.getSort() : 999)
                    .validateTag("")
                    .isMerged(BooleanEnum.isTrue(scpCommonConfigMeta.getYesOrNotMerge()))
                    .configCode(scpCommonConfigMeta.getConfigCode())
                    .groupName(scpCommonConfigMeta.getGroupName())
                    .build();
            metaInfoList.add(metaInfo);
            MetaMonitor.advice(metaInfo);
        });

        List<MetaInfo> complexMetaInfoList = MetaInfoCreator.complexMetaHandle(metaInfoList);

        // children 合并列信息拼装回去
        List<MetaInfo> finalComplexMetaInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(complexMetaInfoList)){
            for (MetaInfo metaInfo : complexMetaInfoList) {
                finalComplexMetaInfoList.add(metaInfo);
                Collection<MetaInfo> childrenMetaInfoList = metaInfo.getChildren();
                if (CollectionUtils.isNotEmpty(childrenMetaInfoList)){
                    for (MetaInfo info : childrenMetaInfoList) {

                        TransferConfigMetaInsertRequest metaInsertRequest = requestCodeMap.get(info.getCode());
                        MetaInfo childrenMetaInfo = MetaInfo
                                .builder()
                                .code(metaInsertRequest.getCode())
                                .name(metaInsertRequest.getName())
                                .mergerName(metaInsertRequest.getMergerName())
                                .anotherName(metaInsertRequest.getAnotherName())
                                .parent(null)
                                .primaryKey(false)
                                .primarySort(0)
                                .format(null)
                                .fixed("")
                                .hidden(true)
                                .sort(metaInsertRequest.getSort() != null ? metaInsertRequest.getSort() : 999)
                                .validateTag("")
                                .merge(metaInsertRequest.getMerge())
                                .isMerged(true)
                                .configCode(metaInsertRequest.getConfigCode())
                                .groupName(metaInsertRequest.getGroupName())
                                .build();

                        finalComplexMetaInfoList.add(childrenMetaInfo);
                    }
                }

            }
        }


        return MetaInfoWrapper.of(list, finalComplexMetaInfoList);
    }

    private void checkMetaRequest(TransferConfigMetaInsertRequest metaRequest){

        if (StringUtils.isNotBlank(metaRequest.getMerge())){
            Assert.isTrue(StringUtils.isNotBlank(metaRequest.getMergerName()), ErrorCode.ILLEGAL_ARGUMENT, TransferError.MERGER_NAME_IS_NOT_NULL.getError());
            Assert.isTrue(StringUtils.isNotBlank(metaRequest.getAnotherName()), ErrorCode.ILLEGAL_ARGUMENT, TransferError.ANOTHER_NAME_IS_NOT_NULL.getError());
        }

    }

    @Override
    public PageMetaInfoWrapper pageQueryData(TransferPlanPageQueryRequest request) {

        // 查询meat配置信息
        List<ScpCommonConfigMeta> scpCommonConfigMetaList = getScpCommonConfigMetaList();

/*        for (ScpCommonConfigMeta scpCommonConfigMeta : scpCommonConfigMetaList) {
            if ("skuCode".equals(scpCommonConfigMeta.getCode()) || "skuName".equals(scpCommonConfigMeta.getCode())){
                scpCommonConfigMeta.setMergerMetaValue("sku");
                scpCommonConfigMeta.setFormat(String.format("%s:", scpCommonConfigMeta.getValue())+"%s");
                scpCommonConfigMeta.setYesOrNotMerge(BooleanEnum.TRUE.getICode());
                scpCommonConfigMeta.setSort(scpCommonConfigMeta.getSort());
                scpCommonConfigMeta.setHidden(BooleanEnum.FALSE.getICode());

            }
        }*/

        Map<String, ScpCommonConfigMeta> codeMateMap = scpCommonConfigMetaList.stream().collect(Collectors.toMap(ScpCommonConfigMeta::getCode, v -> v, (v1, v2) -> v1));

        if (request.enablePaging()) {
            PageHelper.startPage(Objects.isNull(request.getCurrentPage()) ? 0 : request.getCurrentPage().intValue(), Objects.isNull(request.getPageSize()) ? 10 : request.getPageSize().intValue());
        }
        List<InventoryData> inventoryDataList = inventoryDataDao.selectInventoryDataPageList(request);

        Map<String, List<ScpCommonConfigMeta>> groupMap = scpCommonConfigMetaList.stream().filter(s -> StringUtils.isNotBlank(s.getMergerMetaValue())).collect(Collectors.groupingBy(ScpCommonConfigMeta::getMergerMetaValue));

        List<Map<String, Object>> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventoryDataList)){
            list = GeiCommonConvert.convertList(inventoryDataList);
        }
        for (Map<String, Object> map : list) {
            // 先组装meta信息中merger_meta_value为空的
            groupMap.forEach((k,v)->{
                List<MergeFieldWrapper> merged = new ArrayList<>();
                for (ScpCommonConfigMeta scpCommonConfigMeta : v) {
                    String finalValue = StrUtils.format(scpCommonConfigMeta.getFormat(), true, map.get(scpCommonConfigMeta.getCode()));
                    MergeFieldWrapper wrapper = MergeFieldWrapper.of(scpCommonConfigMeta.getNewLine(), finalValue, MetaType.TEXT);
                    wrapper.setEntity(map);
                    wrapper.setPrefix("");
                    wrapper.setSuffix("");
                    wrapper.setColor(null);
                    wrapper.setSpaces(1);
                    wrapper.setEditable(false);
                    merged.add( wrapper );
                    MergeMonitor.advice(scpCommonConfigMeta.getValue(), merged, wrapper);
                    wrapper.setEntity(null);
                }
                map.put(k, merged);
            });

        }

        List<MetaInfo> metaInfoList = new LinkedList<>();
        for (ScpCommonConfigMeta scpCommonConfigMeta : scpCommonConfigMetaList) {

            boolean MergerMetaValueFlag = StringUtils.isNotBlank(scpCommonConfigMeta.getMergerMetaValue());
            ScpCommonConfigMeta configMeta = codeMateMap.get(scpCommonConfigMeta.getCode());
            MetaInfo metaInfo = MetaInfo
                    .builder()
                    .code(scpCommonConfigMeta.getCode())
                    .name(scpCommonConfigMeta.getValue())
                    .parent(scpCommonConfigMeta.getParent())
                    .anotherName(configMeta != null ? configMeta.getValue() : scpCommonConfigMeta.getAnotherName())
                    .primaryKey(false)
                    .primarySort(0)
                    .format(scpCommonConfigMeta.getFormat())
                    .fixed("")
                    .hidden(MergerMetaValueFlag || BooleanEnum.TRUE.isThis(scpCommonConfigMeta.getHidden()))
                    .sort(scpCommonConfigMeta.getSort() != null ? scpCommonConfigMeta.getSort() : 999)
                    .validateTag("")
                    .isMerged(BooleanEnum.isTrue(scpCommonConfigMeta.getYesOrNotMerge()))
                    .configCode(scpCommonConfigMeta.getConfigCode())
                    .groupName(scpCommonConfigMeta.getGroupName())
                    .merge(scpCommonConfigMeta.getMerge())
                    .mergerName(scpCommonConfigMeta.getMergerName())
                    .anotherName(scpCommonConfigMeta.getAnotherName())
                    .build();
            metaInfoList.add(metaInfo);
            MetaMonitor.advice(metaInfo);
        }

        // 添加合并的meta
        groupMap.forEach((k ,v)->{
            ScpCommonConfigMeta scpCommonConfigMeta = v.get(0);

            MetaInfo metaInfo = MetaInfo
                    .builder()
                    .code(scpCommonConfigMeta.getMergerMetaValue())
                    .name(scpCommonConfigMeta.getMergerMetaValue())
                    .parent(scpCommonConfigMeta.getParent())
                    .primaryKey(false)
                    .primarySort(0)
                    .format(scpCommonConfigMeta.getFormat())
                    .fixed("")
                    .hidden(BooleanEnum.TRUE.isThis(scpCommonConfigMeta.getHidden()))
                    .sort(scpCommonConfigMeta.getSort() != null ? scpCommonConfigMeta.getSort() : 999)
                    .validateTag("")
                    .isMerged(BooleanEnum.isTrue(scpCommonConfigMeta.getYesOrNotMerge()))
                    .configCode(scpCommonConfigMeta.getConfigCode())
                    .groupName(scpCommonConfigMeta.getGroupName())
                    .build();
            metaInfoList.add(metaInfo);
            MetaMonitor.advice(metaInfo);
        });

        List<MetaInfo> complexMetaInfoList = MetaInfoCreator.complexMetaHandle(metaInfoList);

        Long count = inventoryDataDao.selectCount(JsonUtils.toMap(request));
        return PageMetaInfoWrapper.of(request, count, list, complexMetaInfoList);

    }

    @Override
    public List<String> getProductionCodingList(String key) {
        return inventoryDataDao.getProductionCodingList(key);
    }

    @Override
    public List<String> getOutPhysicalWarehouseNameList(String key) {
        return inventoryDataDao.getOutPhysicalWarehouseNameList(key);
    }

    @Override
    public List<String> getOutLogicalWarehouseNameList(String key) {
        return inventoryDataDao.getOutLogicalWarehouseNameList(key);
    }

    @Override
    public List<String> getInLogicalWarehouseNameList(String key) {
        return inventoryDataDao.getInLogicalWarehouseNameList(key);
    }

    @Override
    public List<String> getInPhysicalWarehouseNameList(String key) {
        return inventoryDataDao.getInPhysicalWarehouseNameList(key);
    }

    @Override
    public List<String> getValidityRuleList(String key) {
        return inventoryDataDao.getValidityRuleList(key);
    }

    @Override
    public List<String> getModeOfTransportList(String key) {
        return inventoryDataDao.getModeOfTransportList(key);
    }

    @Override
    public List<String> getAlarmStatusList(String key) {
        return inventoryDataDao.getAlarmStatusList(key);
    }

    private List<ScpCommonConfigMeta> assemblyData(List<TransferConfigMetaInsertRequest> requests) {
        List<ScpCommonConfigMeta> metaList = new ArrayList<>();
        Map<String, List<TransferConfigMetaInsertRequest>> mergerMap = requests.stream().filter(t -> Objects.nonNull(t.getMergerName())).collect(Collectors.groupingBy(t -> t.getMergerName()));
        for (TransferConfigMetaInsertRequest request : requests) {
            ScpCommonConfigMeta scpCommonConfigMeta = new ScpCommonConfigMeta();
            scpCommonConfigMeta.setValue(request.getName());
            scpCommonConfigMeta.setCode(request.getCode());
            if (Objects.nonNull(request.getMerge()) && Objects.equals(MetaMergerEnum.ROW.getCode(), request.getMerge())) {
                if (Objects.nonNull(mergerMap) && mergerMap.get(request.getMergerName()).size() > 1) {
                    scpCommonConfigMeta.setParent(request.getMergerName());
                    scpCommonConfigMeta.setValue(request.getAnotherName());
                }
            } else if (Objects.nonNull(request.getMerge()) && Objects.equals(MetaMergerEnum.LINE.getCode(), request.getMerge())) {
                if (Objects.nonNull(mergerMap) && mergerMap.get(request.getMergerName()).size() > 1) {
                    scpCommonConfigMeta.setMergerMetaValue(request.getMergerName());
                    scpCommonConfigMeta.setFormat(String.format("%s:", request.getAnotherName())+"%s");
                }
            }
            scpCommonConfigMeta.setSort(request.getSort());
            scpCommonConfigMeta.setMapping(request.getCode());
        }
        return metaList;
    }

    public static void main(String[] args) throws JsonProcessingException {
        Object configValue = "[\"productionCoding\",\"sku\",\"outPhysicalWarehouse\"]";

        ObjectMapper mapper = new ObjectMapper();
        List<String> list = mapper.readValue(configValue.toString(), new TypeReference<List<String>>() {});
        System.out.println(list);

    }

}
