package cn.aliyun.ryytn.modules.inv.controller.business;

import cn.aliyun.ryytn.modules.inv.api.business.CdcRdcPriorityService;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.controller.business.api.InvBusinessApi;
import cn.aliyun.ryytn.modules.inv.entity.business.dto.CdcRdcPriorityDTO;
import cn.aliyun.ryytn.modules.inv.entity.business.request.CdcPriorityUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.business.request.CdcRdcPriorityPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.business.request.CdcRdcPriorityUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.business.request.RdcPriorityUpdateRequest;
import cn.aliyun.ryytn.modules.inv.entity.business.vo.CdcRdcPriorityVO;
import cn.aliyun.ryytn.modules.inv.common.model.ResponseResult;
import com.cainiao.cntech.dsct.scp.gei.biz.web.model.OptionVO;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.GeiCommonConvert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping(InvBusinessApi.CdcRdcPriority.ROOT)
@Api(tags = "CDC-RDC优先级")
public class CdcRdcPriorityController {
    @Autowired
    private CdcRdcPriorityService cdcRdcPriorityService;

    @PostMapping(InvBusinessApi.CdcRdcPriority.LIST)
    @ApiOperation(value = "列表查询")
    public ResponseResult<PageMetaInfoWrapper> pageQueryData(@RequestBody CdcRdcPriorityPageQueryRequest request) {
        List<CdcRdcPriorityDTO> queryList = cdcRdcPriorityService.queryPageData(request);
        List<CdcRdcPriorityVO> resultList = GeiCommonConvert.convert(queryList, CdcRdcPriorityVO.class);
        return ResponseResult.success(PageMetaInfoWrapper.of(
                request, cdcRdcPriorityService.queryCount(request),
                resultList, CdcRdcPriorityVO.class
        ));
    }

    @ApiOperation("快速筛选项查询")
    @GetMapping(InvBusinessApi.CdcRdcPriority.QUICK_QUERY_CLAUSE)
    public ResponseResult<List<OptionVO>> quickQueryClause() {
        List<OptionDTO> optionList = cdcRdcPriorityService.quickQueryClauseOption();
        return ResponseResult.success(GeiCommonConvert.convert(optionList, OptionVO.class));
    }

//    @Deprecated
//    public ResponseResult<Long> updateCdc(@RequestBody CdcPriorityUpdateRequest updateRequest) {
//        return ResponseResult.success(cdcRdcPriorityService.batchUpdateCdc(Collections.singletonList(updateRequest), false));
//    }
//
//    @Deprecated
//    public ResponseResult<Long> updateRdc(@RequestBody RdcPriorityUpdateRequest updateRequest) {
//        return ResponseResult.success(cdcRdcPriorityService.batchUpdateRdc(Collections.singletonList(updateRequest), false));
//    }

    @ApiOperation("更新")
    @PostMapping(InvBusinessApi.CdcRdcPriority.UPDATE)
    public ResponseResult<Long> update(@RequestBody CdcRdcPriorityUpdateRequest updateRequest) {
        return ResponseResult.success(cdcRdcPriorityService.batchUpdate(Collections.singletonList(updateRequest), false));
    }
}
