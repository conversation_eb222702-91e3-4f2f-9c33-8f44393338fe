package cn.aliyun.ryytn.modules.inv.service.task.manager;

import cn.aliyun.ryytn.modules.inv.entity.task.entity.Constants;
import cn.aliyun.ryytn.modules.inv.task.dao.ScpTaskMonitorCfgMapper;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskMonitorCfgDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-27 11:37
 * @description 监控器配置管理
 */
@Component
public class MonitorConfigManager {
    @Resource
    private ScpTaskMonitorCfgMapper scpTaskMonitorCfgMapper;

    /**
     * 根据任务类型查询监控模块列表
     *
     * @param taskType
     * @return
     */
    public List<ScpTaskMonitorCfgDO> getModulesByType(String taskType, String dateExpr) {
//        LambdaQueryWrapper<ScpTaskMonitorCfgDO> qw = new LambdaQueryWrapper<>();
//        qw.eq(ScpTaskMonitorCfgDO::getTaskType, taskType);
        if (StringUtils.isBlank(dateExpr)) {
            dateExpr = Constants.DEFAULT_DATE_EXPR;
        }
//        qw.eq(ScpTaskMonitorCfgDO::getDateExpr, dateExpr);
        return scpTaskMonitorCfgMapper.selectByCondition(taskType, dateExpr);
    }
}
