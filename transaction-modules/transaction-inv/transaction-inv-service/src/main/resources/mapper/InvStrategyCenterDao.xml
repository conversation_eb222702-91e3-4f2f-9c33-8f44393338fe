<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyCenterDao">

    <sql id="Table_Name">
        cdop_biz.tdm_kcjh_strategy_center
    </sql>

    <sql id="Repln_Table_Name">
        cdop_biz.tdm_kcjh_sku_repln_type
    </sql>


    <sql id="Basic_Where_Clause">
        <where>
            <!-- 维度 -->
            <if test="dimension != null and dimension != ''">
                and dimension = #{dimension}
            </if>
            <!-- 场景名称 -->
            <if test="strategyName != null and strategyName.size() > 0">
                <foreach collection="strategyName" item="item" separator="," open=" and name in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 补货类型 -->
            <if test="replnType != null and replnType.size() > 0">
                <foreach collection="replnType" item="item" separator="," open=" and repln_type in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 状态 -->
            <if test="status != null and status.size() > 0">
                <foreach collection="status" item="item" separator="," open=" and status in(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="queryIndicator" resultType="cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyIndicatorDO">
        select
            COALESCE(SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END), 0) AS enableCount,
            COALESCE(SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END), 0) AS disableCount,
            COUNT(*) AS strategyCount,
            COALESCE(SUM(CASE WHEN status = 1 THEN cover_cnt ELSE 0 END), 0) AS enableCover,
            COALESCE(SUM(CASE WHEN status = 0 THEN cover_cnt ELSE 0 END), 0) AS disableCover,
            SUM(cover_cnt) AS strategyCover
        from
        <include refid="Table_Name"/>
    </select>

    <select id="queryAllName" resultType="string">
        select
        name
        from
        <include refid="Table_Name"/>
        <where>
            <if test="name != null and name != ''">
                name like concat('%',#{name},'%')
            </if>
        </where>
    </select>

    <select id="queryGather"
            resultType="cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyCenterGatherDO">
        select
            dimension, count(dimension) as count
        from
        <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>
        group by dimension
    </select>

    <select id="selectByCondition" resultType="cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyCenterDO">
        select
            *
        from
        <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>
        order by gmt_modified desc
    </select>

    <select id="selectCount" resultType="long">
        select
            count(1)
        from
        <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>
    </select>

    <select id="selectByName" resultType="long">
        select
            count(0)
        from
        <include refid="Table_Name"/>
        <where>
            <if test="name != null and name != ''">
                name = #{name}
            </if>
        </where>
    </select>

    <delete id="delete">
        delete from
        <include refid="Table_Name"/>
        where id = #{id}
    </delete>

    <select id="selectByIds" resultType="cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyCenterDO">
        select
            *
        from
        <include refid="Table_Name"/>
        <where>
            <if test="ids != null and ids.size() > 0">
                <foreach collection="ids" item="item" separator="," open=" and id in(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectSkuListByIdsOne"
            resultType="cn.aliyun.ryytn.modules.inv.entity.strategy.dto.InvStrategyCenterDTO">
        SELECT
            *
        FROM (
            SELECT
                *
            FROM (
                SELECT
                    strategy.*,
                    '[' || STRING_AGG (sku.sku_code :: TEXT, ', ') || ']' AS skuList
                    FROM <include refid="Table_Name"/> strategy
                    LEFT JOIN <include refid="Repln_Table_Name"/> sku
                    ON sku.abc_type = ANY (ARRAY (SELECT UNNEST(STRING_TO_ARRAY(REPLACE(REPLACE(strategy.tag, '[', ''), ']', ''), ', '))))
                    AND strategy.repln_type = COALESCE(sku.repln_type, 'toC')
                    WHERE strategy.dimension = 'byAbc'
                <if test="ids != null and ids.size() > 0">
                    <foreach collection="ids" item="item" separator="," open=" and strategy.id in(" close=")">
                        #{item}
                    </foreach>
                </if>
                GROUP BY strategy.ID
            ) byAbc
            UNION ALL
            (
                SELECT
                    strategy.*,
                    '[' || STRING_AGG (sku.sku_code :: TEXT, ', ') || ']' AS skuList
                FROM <include refid="Table_Name"/> strategy
                LEFT JOIN <include refid="Repln_Table_Name"/> sku
                ON sku.lv1_category_code = ANY (ARRAY (SELECT UNNEST(STRING_TO_ARRAY(REPLACE(REPLACE(strategy.tag, '[', ''), ']', ''), ', '))))
                AND strategy.repln_type = COALESCE(sku.repln_type, 'toC')
                WHERE strategy.dimension = 'byOne'
                <if test="ids != null and ids.size() > 0">
                    <foreach collection="ids" item="item" separator="," open=" and strategy.id in(" close=")">
                        #{item}
                    </foreach>
                </if>
                GROUP BY strategy.ID
            )
            UNION ALL
            (
                SELECT
                    strategy.*,
                    '[' || STRING_AGG (sku.sku_code :: TEXT, ', ') || ']' AS skuList
                FROM <include refid="Table_Name"/> strategy
                LEFT JOIN <include refid="Repln_Table_Name"/> sku
                ON sku.lv2_category_code = ANY (ARRAY (SELECT UNNEST(STRING_TO_ARRAY(REPLACE(REPLACE(strategy.tag, '[', ''), ']', ''), ', '))))
                AND strategy.repln_type = COALESCE(sku.repln_type, 'toC')
                WHERE strategy.dimension = 'byTwo'
                <if test="ids != null and ids.size() > 0">
                    <foreach collection="ids" item="item" separator="," open=" and strategy.id in(" close=")">
                        #{item}
                    </foreach>
                </if>
                GROUP BY strategy.ID
            )
        ) a
    </select>

    <select id="selectSkuListByIdsTwo"
            resultType="cn.aliyun.ryytn.modules.inv.entity.strategy.dto.InvStrategyCenterDTO">
        SELECT
            *
        FROM (
            SELECT
                *
            FROM (
                SELECT
                    strategy.*,
                    '[' || STRING_AGG (sku.sku_code :: TEXT, ', ') || ']' AS skuList
                FROM <include refid="Table_Name"/> strategy
                LEFT JOIN <include refid="Repln_Table_Name"/> sku
                ON sku.lv4_category_code = ANY (ARRAY (SELECT UNNEST(STRING_TO_ARRAY(REPLACE(REPLACE(strategy.tag, '[', ''), ']', ''), ', '))))
                AND strategy.repln_type = COALESCE(sku.repln_type, 'toC')
                WHERE strategy.dimension = 'byFour'
                <if test="ids != null and ids.size() > 0">
                    <foreach collection="ids" item="item" separator="," open=" and strategy.id in(" close=")">
                        #{item}
                    </foreach>
                </if>
                GROUP BY strategy.ID
            ) byFour
            UNION ALL
            (
                SELECT
                    *,
                    sku AS skuCode
                FROM <include refid="Table_Name"/> strategy
                WHERE dimension = 'bySku'
                <if test="ids != null and ids.size() > 0">
                    <foreach collection="ids" item="item" separator="," open=" and id in(" close=")">
                        #{item}
                    </foreach>
                </if>
            )
        ) a
    </select>

    <select id="selectSkuListByIds"
            resultType="cn.aliyun.ryytn.modules.inv.entity.strategy.dto.InvStrategyCenterDTO">
        SELECT
            *
        FROM (
            SELECT
                *
            FROM (
                SELECT
                    strategy.*,
                    '[' || STRING_AGG (sku.sku_code :: TEXT, ', ') || ']' AS skuList
                FROM <include refid="Table_Name"/> strategy
                LEFT JOIN <include refid="Repln_Table_Name"/> sku
                ON sku.abc_type = ANY (ARRAY (SELECT UNNEST(STRING_TO_ARRAY(REPLACE(REPLACE(strategy.tag, '[', ''), ']', ''), ', '))))
                WHERE strategy.dimension = 'byAbc'
                <if test="ids != null and ids.size() > 0">
                    <foreach collection="ids" item="item" separator="," open=" and strategy.id in(" close=")">
                        #{item}
                    </foreach>
                </if>
                GROUP BY strategy.ID
            ) byAbc
            UNION ALL
            (
                SELECT
                    strategy.*,
                    '[' || STRING_AGG (sku.sku_code :: TEXT, ', ') || ']' AS skuList
                FROM <include refid="Table_Name"/> strategy
                LEFT JOIN <include refid="Repln_Table_Name"/> sku
                ON sku.lv1_category_code = ANY (ARRAY (SELECT UNNEST(STRING_TO_ARRAY(REPLACE(REPLACE(strategy.tag, '[', ''), ']', ''), ', '))))
                WHERE strategy.dimension = 'byOne'
                <if test="ids != null and ids.size() > 0">
                    <foreach collection="ids" item="item" separator="," open=" and strategy.id in(" close=")">
                        #{item}
                    </foreach>
                </if>
                GROUP BY strategy.ID
            )
            UNION ALL
            (
                SELECT
                    strategy.*,
                    '[' || STRING_AGG (sku.sku_code :: TEXT, ', ') || ']' AS skuList
                FROM <include refid="Table_Name"/> strategy
                LEFT JOIN <include refid="Repln_Table_Name"/> sku
                ON sku.lv2_category_code = ANY (ARRAY (SELECT UNNEST(STRING_TO_ARRAY(REPLACE(REPLACE(strategy.tag, '[', ''), ']', ''), ', '))))
                WHERE strategy.dimension = 'byTwo'
                <if test="ids != null and ids.size() > 0">
                    <foreach collection="ids" item="item" separator="," open=" and strategy.id in(" close=")">
                        #{item}
                    </foreach>
                </if>
                GROUP BY strategy.ID
            )
            UNION ALL
            (
                SELECT
                    strategy.*,
                    '[' || STRING_AGG (sku.sku_code :: TEXT, ', ') || ']' AS skuList
                FROM <include refid="Table_Name"/> strategy
                LEFT JOIN <include refid="Repln_Table_Name"/> sku
                ON sku.lv4_category_code = ANY (ARRAY (SELECT UNNEST(STRING_TO_ARRAY(REPLACE(REPLACE(strategy.tag, '[', ''), ']', ''), ', '))))
                WHERE strategy.dimension = 'byFour'
                <if test="ids != null and ids.size() > 0">
                    <foreach collection="ids" item="item" separator="," open=" and strategy.id in(" close=")">
                        #{item}
                    </foreach>
                </if>
                GROUP BY strategy.ID
            )
            UNION ALL
            (
                SELECT
                    *,
                    sku AS skuCode
                FROM <include refid="Table_Name"/> strategy
                WHERE dimension = 'bySku'
                <if test="ids != null and ids.size() > 0">
                    <foreach collection="ids" item="item" separator="," open=" and id in(" close=")">
                        #{item}
                    </foreach>
                </if>
            )
        ) a
    </select>

    <update id="updateDisableStatus">
        update <include refid="Table_Name"/>
        <set>
            <if test="operatorCode != null and operatorCode != ''">operator_code = #{operatorCode},</if>
            <if test="operatorName != null and operatorName != ''">operator_name = #{operatorName},</if>
            operation_time  = now(),
            gmt_modified = now(),
            status = 0
        </set>
        <where>
            status != 0 and dimension = #{dimension}
            <if test="enableList != null and enableList.size() > 0">
                <foreach collection="enableList" item="item" separator="," open=" and id not in(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size() > 0">
                <foreach collection="ids" item="item" separator="," open=" and id in(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>
    <update id="updateEnableStatus">
        update <include refid="Table_Name"/>
        <set>
            <if test="operatorCode != null and operatorCode != ''">operator_code = #{operatorCode},</if>
            <if test="operatorName != null and operatorName != ''">operator_name = #{operatorName},</if>
            operation_time  = now(),
            gmt_modified = now(),
            status = 1
        </set>
        <where>
            status != 1 and dimension = #{dimension}
            <if test="enableList != null and enableList.size() > 0">
                <foreach collection="enableList" item="item" separator="," open=" and id in(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

    <update id="updateStrategy">
        update <include refid="Table_Name"/>
        <set>
            gmt_modified = now(),
            name = #{name},
            repln_type = #{replnType},
            dimension = #{dimension},
            tag = #{tag},
            sku = #{skuStr},
            cover_cnt = #{coverCnt},
            status = #{status},
            operator_code = #{operatorCode},
            operator_name = #{operatorName},
            operation_time = now(),
            select_all = #{selectAll}
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <insert id="insertStrategy">
        insert into
        <include refid="Table_Name"/>
        (id, name, repln_type, dimension, tag, sku, cover_cnt, status, operator_code, operator_name, operation_time, select_all)
        values (
        #{id}, #{name}, #{replnType}, #{dimension}, #{tag}, #{skuStr} ,#{coverCnt},#{status}, #{operatorCode}, #{operatorName}, now(), #{selectAll}
        )
    </insert>

</mapper>