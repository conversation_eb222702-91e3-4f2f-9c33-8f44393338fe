<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.task.dao.ScpTaskCoreStatMapper">

    <sql id="Table_Name">
        cdop_biz.scp_task_core_stat
    </sql>
    <sql id="Basic_Where_Clause">
        <where>
            <!-- 核心模块类型 -->
            <if test='core != null and core != ""'>
                core_code = #{core}
            </if>
        </where>
    </sql>
    <select id="selectByCondition" resultType="cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskCoreStatDO">
        select * from
        <include refid="Table_Name" />
        <include refid="Basic_Where_Clause" />
    </select>

    <insert id="insert">
        insert into <include refid="Table_Name" /> (
            core_code, core_name, status
            <if test='batchNo != null and batchNo != ""'>
                , batch_no
            </if>
        ) values (
             #{coreCode}, #{coreName}, #{status}
            <if test='batchNo != null and batchNo != ""'>
                , #{batchNo}
            </if>
        );
    </insert>

    <update id="updateByCore">
        update <include refid="Table_Name" />
        <set>
            batch_no = #{batchNo}, gmt_modified = now()
            <if test='status != null and status != ""'>
                ,status = #{status}
            </if>
        </set>
        where core_code = #{coreCode}
    </update>

    <delete id="deleteByCore">
        delete from <include refid="Table_Name" />
        where core_code = #{coreCode}
    </delete>
</mapper>