<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.business.dao.InvSkuCdcFullSupplyDao">
    <sql id="Table_Name">
        cdop_biz.tdm_kcjh_conf_sku_cdc_full_supply
    </sql>
    <sql id="Unit_Table_Name">
        cdop_biz.tdm_kcjh_unit_rdc_sku_df
    </sql>
    <sql id="RDC_Table_Name">
        cdop_biz.dim_bas_warehouse_rdc_df
    </sql>
    <sql id="Basic_Where_Clause">
        <where>
            status = 1
            <!-- 工厂仓编码 -->
            <if test="cdcCodeList != null and cdcCodeList.size() > 0">
                <foreach collection="cdcCodeList" item="item" separator="," open=" and cdc_code in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- sku编码 -->
            <if test="skuList != null and skuList.size() > 0">
                <foreach collection="skuList" item="item" separator="," open=" and sku_code in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 是否全量推出 -->
            <if test="isFullSupplyList != null and isFullSupplyList.size() > 0">
                <foreach collection="isFullSupplyList" item="item" separator="," open=" and is_full_supply in(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test='quickQueryClauseValue != null and quickQueryClauseValue != ""'>
                and ${quickQueryClauseValue}
            </if>
        </where>
    </sql>

    <select id="querySkuCdcFullSupplyList" resultType="cn.aliyun.ryytn.modules.inv.entity.business.dto.InvSkuCdcFullSupplyDTO">
        select
        *
        from
        <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>
        order by case when is_full_supply is null then 0 else 1 end, gmt_modified desc
    </select>

    <select id="queryCount" resultType="Long">
        select
        count(1)
        from
        <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>
    </select>

    <update id="updateSkuCdcFullSupply" parameterType="cn.aliyun.ryytn.modules.inv.entity.business.request.InvSkuCdcFullSupplyUpdateRequest">

        update <include refid="Table_Name"/>
        <set>
            <if test="operatorCode != null and operatorCode != ''">operator_code = #{operatorCode},</if>
            <if test="operatorName != null and operatorName != ''">operator_name = #{operatorName},</if>
            is_full_supply = #{isFullSupply},
            operation_time  = now(),
            gmt_modified = now()
        </set>
        where cdc_code = #{cdcCode} and sku_code = #{skuCode}
    </update>

    <update id="batchUpdateSkuCdcFullSupply" parameterType="cn.aliyun.ryytn.modules.inv.entity.business.request.InvSkuCdcFullSupplyUpdateRequest">

        <foreach collection="list" item="item" separator=";">
            update <include refid="Table_Name"/>
            <set>
                <if test="item.operatorCode != null and item.operatorCode != ''">operator_code = #{item.operatorCode},</if>
                <if test="item.operatorName != null and item.operatorName != ''">operator_name = #{item.operatorName},</if>
                is_full_supply = #{item.isFullSupply},
                operation_time  = now(),
                gmt_modified = now()
            </set>
            where code = #{item.cdcCode}
        </foreach>

    </update>
</mapper>