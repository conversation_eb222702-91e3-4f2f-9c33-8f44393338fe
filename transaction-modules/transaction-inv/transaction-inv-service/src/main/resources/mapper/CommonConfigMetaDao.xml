<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.transfer.dao.CommonConfigMetaDao">
    <sql id="Table_Name">
        cdop_biz.scp_common_config_meta
    </sql>
    <sql id="Basic_Where_Clause">
        <where>
            <if test="creatorCode != null">
                and creator_code = #{creatorCode}
            </if>
            <if test="moduleName != null and moduleName != ''">
                and module_name = #{moduleName}
            </if>
            <if test="groupName != null and groupName != ''">
                and group_name = #{groupName}
            </if>
            <if test="configCode != null and configCode != ''">
                and config_code = #{configCode}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
        </where>
    </sql>

    <select id="selectByCondition" resultType="cn.aliyun.ryytn.modules.inv.entity.transfer.dos.ScpCommonConfigMeta">
        select
            *
        from
        <include refid="Table_Name" />
        <include refid="Basic_Where_Clause" />
    </select>

    <update id="batchUpdateScpCommonConfigMeta" parameterType="cn.aliyun.ryytn.modules.inv.entity.transfer.dos.ScpCommonConfigMeta">

        <foreach collection="list" item="item" separator=";">
            update <include refid="Table_Name"/>
            <set>
                yes_or_not_merge = #{item.yesOrNotMerge},
                parent = #{item.parent},
                merger_meta_code = #{item.mergerMetaCode},
                merger_meta_value = #{item.mergerMetaValue},
                format = #{item.format},
                value = #{item.value},
                sort = #{item.sort},
                hidden = #{item.hidden},
                gmt_create  = now(),
                gmt_modified = now()
            </set>
            where code = #{item.code} and creator_code = #{item.creatorCode}
        </foreach>

    </update>

    <insert id="batchSaveScpCommonConfigMeta" parameterType="cn.aliyun.ryytn.modules.inv.entity.transfer.dos.ScpCommonConfigMeta">
        INSERT INTO <include refid="Table_Name"/>
        (
        tenant_code, module_name, group_name, config_code, creator_code,
        value, code, format, meta_type, mapping, parent, info,
        yes_or_not_merge, merger_meta_value, merger_meta_code,
        new_line, sort, hidden, gmt_create, gmt_modified, creator_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.tenantCode}, #{item.moduleName}, #{item.groupName},
            #{item.configCode}, #{item.creatorCode}, #{item.value},
            #{item.code}, #{item.format}, #{item.metaType}, #{item.mapping},
            #{item.parent}, #{item.info}, #{item.yesOrNotMerge},
            #{item.mergerMetaValue}, #{item.mergerMetaCode},
            #{item.newLine}, #{item.sort}, #{item.hidden},
            #{item.gmtCreate}, #{item.gmtModified}, #{item.creatorName}
            )
        </foreach>
    </insert>
</mapper>