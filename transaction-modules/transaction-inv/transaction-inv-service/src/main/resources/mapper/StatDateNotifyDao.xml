<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.stat.dao.StatDateNotifyMapper">

    <sql id="Table_Name">
        cdop_biz.scp_stat_date_notify
    </sql>


    <select id="selectOne" resultType="cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.StatDateNotifyDO">
        select
            *
        from
        <include refid="Table_Name" />
        where module_code = #{moduleCode}
    </select>
</mapper>