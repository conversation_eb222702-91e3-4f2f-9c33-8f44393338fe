<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.task.dao.ScpTaskExecChainMapper">
    <sql id="Table_Name">
        cdop_biz.scp_task_exec_chain
    </sql>
    <sql id="Basic_Where_Clause">
        <where>
            <!-- 状态 -->
            <if test='status != null and status.size() > 0'>
                <foreach collection="status" item="code" separator="," open=" and status in(" close=")">
                    #{code}
                </foreach>
            </if>
            <!-- 批次号 -->
            <if test='batch != null and batch.size() > 0'>
                <foreach collection="batch" item="code" separator="," open=" and batch_no in(" close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </sql>
    <select id="selectByCondition" resultType="cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskExecChainDO">
        select * from
        <include refid="Table_Name" />
        <include refid="Basic_Where_Clause" />

<!--        for update-->
    </select>

    <insert id="insert">
        insert into <include refid="Table_Name" /> (
            chain_node_id, batch_no, root_task_id, root_task_type, exec_mode, status, date_expr
            <if test="dateExprPriority != null"> ,date_expr_priority </if>
            <if test="gmtCreate != null"> ,gmt_create </if>
            <if test="gmtModified != null"> ,gmt_modified </if>
            <if test="allDateExpr != null"> ,all_date_expr </if>
        ) values (
            #{chainNodeId}, #{batchNo}, #{rootTaskId}, #{rootTaskType}, #{execMode}, #{status}
            <choose>
                <when test='dateExpr != null and dateExpr != ""'> ,#{dateExpr} </when>
                <otherwise> ,'-' </otherwise>
            </choose>
            <if test="dateExprPriority != null"> ,#{dateExprPriority} </if>
            <if test="gmtCreate != null"> ,#{gmtCreate} </if>
            <if test="gmtModified != null"> ,#{gmtModified} </if>
            <if test="allDateExpr != null"> ,#{allDateExpr} </if>
        )
<!--        on duplicate key update date_expr_priority = values(date_expr_priority);-->
    </insert>

    <update id="update">
        update <include refid="Table_Name" />
        <set>
            <if test='status != null and status != ""'>
                status = #{status}
            </if>
            <if test='errorMsg != null and errorMsg != ""'>
                ,error_msg = #{errorMsg}
            </if>
            <if test='submitTimeStr != null and submitTimeStr != ""'>
                ,submit_time = TO_TIMESTAMP(#{submitTimeStr}, 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test='dateExpr != null and dateExpr != ""'>
                ,date_expr = #{dateExpr}
            </if>
            <if test='dateExprPriority != null'>
                ,date_expr_priority = #{dateExprPriority}
            </if>
            <if test='allDateExpr != null and allDateExpr != ""'>
                ,all_date_expr = #{allDateExpr}
            </if>
        </set>
        <where>
            root_task_id = #{rootTaskId}
            <if test='batchNo != null'>
                and batch_no = #{batchNo}
            </if>
            <if test='chainNodeId != null'>
                and chain_node_id = #{chainNodeId}
            </if>
            <if test='beforeStatus != null and beforeStatus != ""'>
                and status = #{beforeStatus}
            </if>
            <if test='beforeDateExpr != null and beforeDateExpr != ""'>
                and date_expr = #{beforeDateExpr}
            </if>
        </where>
    </update>
</mapper>