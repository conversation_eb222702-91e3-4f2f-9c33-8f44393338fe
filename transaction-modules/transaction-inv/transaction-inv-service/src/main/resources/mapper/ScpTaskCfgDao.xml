<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.task.dao.ScpTaskCfgMapper">

    <sql id="Table_Name">
        cdop_biz.scp_task_cfg
    </sql>
    <sql id="Basic_Where_Clause">
        <where>
            enable = 1
            <!-- 任务类型 -->
            <if test='taskType != null and taskType != ""'>
                and task_type_code = #{taskType}
            </if>
            <!-- 前一个任务类型 -->
            <if test='preTaskType != null and preTaskType != ""'>
                and pre_task_type_code = #{preTaskType}
            </if>
            <!-- 下一个任务类型 -->
            <if test='nextTaskType != null and nextTaskType != ""'>
                and next_task_type_code = #{nextTaskType}
            </if>
            <if test='dateExpr != null and dateExpr != ""'>
                and ( date_expr = #{dateExpr} or root_odps = '1' )
            </if>
            <if test='odpsCode != null and odpsCode.size() > 0'>
                <foreach collection="odpsCode" item="code" separator="," open=" and odps_code in(" close=")">
                    #{code}
                </foreach>
            </if>
            <if test='blurOdpsKey != null and blurOdpsKey != ""'>
                and (
                    odps_code like concat('%', #{blurOdpsKey}, '%')
                    or odps_name like concat('%', #{blurOdpsKey}, '%')
                )
            </if>
        </where>
    </sql>

    <select id="selectByCondition" resultType="cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskCfgDO">
        select
            <choose>
                <when test='groupType != null and groupType == "taskType"'>
                    task_type_code as taskTypeCode, max(task_type_name) as taskTypeName,
                    max(pre_task_type_code) as preTaskTypeCode,
                    max(pre_task_type_name) as preTaskTypeName,
                    max(next_task_type_code) as nextTaskTypeCode,
                    max(next_task_type_name) as nextTaskTypeName
                </when>
                <when test='groupType != null and groupType == "odps"'>
                    odps_code as odpsCode, max(odps_name) as odpsName
                </when>
                <otherwise> * </otherwise>
            </choose>
        from
        <include refid="Table_Name" />
        <include refid="Basic_Where_Clause" />
        <if test='groupType != null and groupType == "taskType"'>
            group by task_type_code
        </if>
        <if test='groupType != null and groupType == "odps"'>
            group by odps_code
        </if>
        <if test='groupType == null'>
            order by task_type_code, date_expr
        </if>

    </select>
    <select id="selectCount" resultType="long">
        select count(1) from (
            select 1 from
            <include refid="Table_Name" />
            <include refid="Basic_Where_Clause" />
            <if test='groupType != null and groupType == "taskType"'>
                group by task_type_code
            </if>
            <if test='groupType != null and groupType == "odps"'>
                group by odps_code
            </if>
            <if test='groupType == null'>
                order by task_type_code, date_expr
            </if>
        ) t1
    </select>
    <insert id="upsert">
        insert into <include refid="Table_Name" /> (
            task_type_code, task_type_name,
            pre_task_type_code, pre_task_type_name,
            next_task_type_code, next_task_type_name,
            odps_code, odps_name, root_odps,
            date_expr, date_expr_priority, enable
        ) values
        <foreach collection="params" item="item" separator=",">
            (
                #{item.taskTypeCode}, #{item.taskTypeName},
                #{item.preTaskTypeCode}, #{item.preTaskTypeName},
                #{item.nextTaskTypeCode}, #{item.nextTaskTypeName},
                #{item.odpsCode}, #{item.odpsName}, #{item.rootOdps},
                #{item.dateExpr}, #{item.dateExprPriority}, #{item.enable}
            )
        </foreach>
<!--        on duplicate key update-->
<!--        task_type_name = values(task_type_name),-->
<!--        pre_task_type_code = values(pre_task_type_code),-->
<!--        pre_task_type_name = values(pre_task_type_name),-->
<!--        next_task_type_code = values(next_task_type_code),-->
<!--        next_task_type_name = values(next_task_type_name),-->
<!--        odps_code = values(odps_code),-->
<!--        odps_name = values(odps_name),-->
<!--        root_odps = values(root_odps),-->
<!--        date_expr = values(date_expr),-->
<!--        date_expr_priority = values(date_expr_priority),-->
<!--        enable = values(enable),-->
<!--        gmt_modified = now()-->
    </insert>

    <update id="update">
        <foreach collection="finalUpdate" item="item" separator=";">
            update <include refid="Table_Name"/>
            <set>
                task_type_name = #{item.taskTypeName},
                pre_task_type_code = #{item.preTaskTypeCode},
                pre_task_type_name = #{item.preTaskTypeName},
                next_task_type_code = #{item.nextTaskTypeCode},
                next_task_type_name = #{item.nextTaskTypeName},
                odps_name = #{item.odpsName},
                root_odps = #{item.rootOdps},
                date_expr = #{item.dateExpr},
                date_expr_priority = #{item.dateExprPriority},
                enable = #{item.enable},
                gmt_modified = now()
            </set>
            where task_type_code = #{item.taskTypeCode} and odps_code = #{item.odpsCode}
        </foreach>
    </update>

    <delete id="delete">
        delete from <include refid="Table_Name" />
        where task_type_code = #{taskTypeCode} and odps_code = #{odpsCode}
    </delete>
</mapper>