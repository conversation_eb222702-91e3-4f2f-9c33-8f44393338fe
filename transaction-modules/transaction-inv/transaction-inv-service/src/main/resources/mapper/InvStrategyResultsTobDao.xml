<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyResultsTobDao">

    <sql id="Table_Name">
        cdop_biz.tdm_kcjh_strategy_results_tob
    </sql>

    <sql id="Left_Table_Name">
        cdop_biz.tdm_kcjh_safety_stock_parameters
    </sql>

    <sql id="Basic_Where_Clause">
        <where>
            a.status = 1
            <!-- sku -->
            <if test="sku != null and sku.size() > 0">
                <foreach collection="sku" item="item" separator="," open=" and a.sku_code in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 品类 -->
            <if test="category != null and category.size() > 0">
                and (
                <foreach collection="category" item="item" separator="," open="a.lv1_category_code in("
                         close=")">#{item}
                </foreach>
                or
                <foreach collection="category" item="item" separator="," open="a.lv2_category_code in("
                         close=")">#{item}
                </foreach>
                or
                <foreach collection="category" item="item" separator="," open="a.lv4_category_code in("
                         close=")">#{item}
                </foreach>
                )
            </if>
            <!-- abc类型 -->
            <if test="abcType != null and abcType.size() > 0">
                <foreach collection="abcType" item="item" separator="," open=" and a.abc_type in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- rdcCode -->
            <if test="rdc != null and rdc.size() > 0">
                <foreach collection="rdc" item="item" separator="," open=" and a.rdc_code in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 场景名称 -->
            <if test="strategyName != null and strategyName.size() > 0">
                <foreach collection="strategyName" item="item" separator="," open=" and a.inv_strategy_name in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 异常状态 -->
            <if test="errStatus != null and errStatus.size() > 0">
                <foreach collection="errStatus" item="item" separator=" or " open=" and (" close=")">
                    <if test="item == 'normal'">
                        (
                            das_num is not null and das_num != 0
                            and b.min_safety_days is not null and b.min_safety_days != 0
                            and b.max_safety_days is not null and b.max_safety_days != 0
                            and sug_safety_days is not null and sug_safety_days != 0
                            and sug_safety_qty is not null and sug_safety_qty != 0
                            and param_limit_water_level_days is not null and param_limit_water_level_days != 0
                            and param_limit_water_level_qty is not null and param_limit_water_level_qty != 0
                            and param_target_inv_days is not null and param_target_inv_days != 0
                            and param_target_inv_qty is not null and param_target_inv_qty != 0
                            and param_rop_cnt_days is not null and param_rop_cnt_days != 0
                            and param_rop_cnt_qty is not null and param_rop_cnt_qty != 0
                            and param_oos_alarm_point_days is not null and param_oos_alarm_point_days != 0
                            and param_oos_alarm_point_qty is not null and param_oos_alarm_point_qty != 0
                        )
                    </if>
                    <if test="item == 'abnormal'">
                        (
                            das_num is null or das_num = 0
                            or b.min_safety_days is null or b.min_safety_days = 0
                            or b.max_safety_days is null or b.max_safety_days = 0
                            or sug_safety_days is null or sug_safety_days = 0
                            or sug_safety_qty is null or sug_safety_qty = 0
                            or param_limit_water_level_days is null or param_limit_water_level_days = 0
                            or param_limit_water_level_qty is null or param_limit_water_level_qty = 0
                            or param_target_inv_days is null or param_target_inv_days = 0
                            or param_target_inv_qty is null or param_target_inv_qty = 0
                            or param_rop_cnt_days is null or param_rop_cnt_days = 0
                            or param_rop_cnt_qty is null or param_rop_cnt_qty = 0
                            or param_oos_alarm_point_days is null or param_oos_alarm_point_days = 0
                            or param_oos_alarm_point_qty is null or param_oos_alarm_point_qty = 0
                        )
                    </if>
                    <if test="item == 'safety'">
                        (
                            b.min_safety_days is null or b.min_safety_days = 0
                            or b.max_safety_days is null or b.max_safety_days = 0
                            or sug_safety_days is null or sug_safety_days = 0
                            or sug_safety_qty is null or sug_safety_qty = 0
                        )
                    </if>
                    <if test="item == 'dasNum'">
                        (das_num is null or das_num = 0)
                    </if>
                    <if test="item == 'param'">
                        (
                            param_limit_water_level_days is null or param_limit_water_level_days = 0
                            or param_limit_water_level_qty is null or param_limit_water_level_qty = 0
                            or param_target_inv_days is null or param_target_inv_days = 0
                            or param_target_inv_qty is null or param_target_inv_qty = 0
                            or param_rop_cnt_days is null or param_rop_cnt_days = 0
                            or param_rop_cnt_qty is null or param_rop_cnt_qty = 0
                            or param_oos_alarm_point_days is null or param_oos_alarm_point_days = 0
                            or param_oos_alarm_point_qty is null or param_oos_alarm_point_qty = 0
                        )
                    </if>
                    <if test="item == 'all'">
                        1 = 1
                    </if>
                </foreach>
            </if>
            <!-- 选中品仓 -->
            <if test="itemList != null and itemList.size() > 0">
                <foreach collection="itemList" item="item" separator=" or " open=" and (" close=")">
                   (a.sku_code = #{item.skuCode} and a.rdc_code = #{item.rdcCode})
                </foreach>
            </if>
            <!-- rdcName -->
            <if test="rdcName != null and rdcName.size() > 0">
                <foreach collection="rdcName" item="item" separator="," open=" and a.rdc_name in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 品类名称 -->
            <if test="categoryName != null and categoryName.size() > 0">
                and (
                <foreach collection="categoryName" item="item" separator="," open="a.lv1_category_name in("
                         close=")">#{item}
                </foreach>
                or
                <foreach collection="categoryName" item="item" separator="," open="a.lv2_category_name in("
                         close=")">#{item}
                </foreach>
                or
                <foreach collection="categoryName" item="item" separator="," open="a.lv4_category_name in("
                         close=")">#{item}
                </foreach>
                )
            </if>
            <if test='quickQueryClauseValue != null and quickQueryClauseValue != ""'>
                and ${quickQueryClauseValue}
            </if>
        </where>
    </sql>

    <select id="selectCount" resultType="java.lang.Long">
        select
            count(1)
        from
        <include refid="Table_Name"/> a
        left join <include refid="Left_Table_Name"/> b on a.sku_code = b.sku_code and a.rdc_code = b.rdc_code
        <include refid="Basic_Where_Clause"/>
    </select>

    <select id="selectByCondition" resultType="cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyResultsTobDO">
        select
            *
        from <include refid="Table_Name"/> a
        left join <include refid="Left_Table_Name"/> b on a.sku_code = b.sku_code and a.rdc_code = b.rdc_code
        <include refid="Basic_Where_Clause"/>
        order by a.gmt_modified desc
    </select>

    <update id="update">
        <foreach collection="list" item="item" separator=";">
            update <include refid="Table_Name"/>
            <set>
                <if test="item.operatorCode != null and item.operatorCode != ''">operator_code = #{item.operatorCode},</if>
                <if test="item.operatorName != null and item.operatorName != ''">operator_name = #{item.operatorName},</if>
                operation_time  = now(),
                <if test="item.manualSafetyDays != null">manual_safety_days = #{item.manualSafetyDays},</if>
                <if test="item.manualTargetDays != null">manual_target_days = #{item.manualTargetDays},</if>
                <if test="item.manualSafetyQty != null">manual_safety_qty = #{item.manualSafetyQty},</if>
                <if test="item.manualTargetQty != null">manual_target_qty = #{item.manualTargetQty},</if>
                gmt_modified = now()
            </set>
            where sku_code = #{item.skuCode} and rdc_code = #{item.rdcCode}
        </foreach>
    </update>

    <update id="resetSuggestValue">
        update <include refid="Table_Name"/>
        <set>
            <if test="operatorCode != null and operatorCode != ''">operator_code = #{operatorCode},</if>
            <if test="operatorName != null and operatorName != ''">operator_name = #{operatorName},</if>
            operation_time  = now(),
            manual_safety_days = null,
            manual_safety_qty = null,
            manual_target_days = null,
            manual_target_qty = null,
            gmt_modified = now()
        </set>
        <where>
            <if test="idList != null and idList.size() > 0">
                <foreach collection="idList" item="item" separator="," open=" id in(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

</mapper>