<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.md.dao.InvUnitRdcSkuDao">
    <sql id="Table_Name">
        cdop_biz.tdm_kcjh_unit_rdc_sku_df
    </sql>

    <select id="selectBySkuCodes" resultType="cn.aliyun.ryytn.modules.inv.entity.md.dos.InvUnitRdcSkuDO">
        select * from <include refid="Table_Name"/>
        <where>
            <if test="skuCodes != null and skuCodes.size() > 0">
                <foreach collection="skuCodes" item="item" separator="," open=" and sku_code in(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


</mapper>