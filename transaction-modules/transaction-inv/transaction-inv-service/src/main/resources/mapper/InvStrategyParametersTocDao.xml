<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.strategy.dao.InvStrategyParametersTocDao">
    <sql id="Table_Name">
        cdop_biz.tdm_kcjh_strategy_parameters_toc
    </sql>

    <sql id="Insert_Column_List">
        (
        id, inv_strategy_id, abc_type, lv1_category_code, lv1_category_name, lv2_category_code, lv2_category_name, lv4_category_code, lv4_category_name,
        sku_code, sku_name, rdc_code, rdc_name, collocation_method, x1, x2, safety, target, operator_code, operator_name, operation_time, status
        )
    </sql>

    <insert id="batchInsert">
        INSERT INTO
        <include refid="Table_Name"/>
        <include refid="Insert_Column_List"/>
        VALUES
        <foreach collection="params" item="item" separator=",">
            (#{item.id},
            #{item.invStrategyId},
            #{item.abcType},
            #{item.lv1CategoryCode},
            #{item.lv1CategoryName},
            #{item.lv2CategoryCode},
            #{item.lv2CategoryName},
            #{item.lv4CategoryCode},
            #{item.lv4CategoryName},
            #{item.skuCode},
            #{item.skuName},
            #{item.rdcCode},
            #{item.rdcName},
            #{item.collocationMethod},
            #{item.x1},
            #{item.x2},
            #{item.safety},
            #{item.target},
            #{item.operatorCode},
            #{item.operatorName},
            now(),
            #{item.status})
        </foreach>
    </insert>

    <select id="selectByInvStrategyId"
            resultType="cn.aliyun.ryytn.modules.inv.entity.strategy.dos.InvStrategyParametersTocDO">
        select * from <include refid="Table_Name"/>
        <where>
            inv_strategy_id = #{strategyId} and status = #{status}
            <if test="dimension != null and dimension != '' and dimension == 'byAbc'">
                <if test="category != null and category.size() > 0">
                    <foreach collection="category" item="item" separator="," open=" and abc_type in(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dimension != null and dimension != '' and dimension == 'byOne'">
                <if test="category != null and category.size() > 0">
                    <foreach collection="category" item="item" separator="," open=" and lv1_category_code in(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dimension != null and dimension != '' and dimension == 'byTwo'">
                <if test="category != null and category.size() > 0">
                    <foreach collection="category" item="item" separator="," open=" and lv2_category_code in(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dimension != null and dimension != '' and dimension == 'byFour'">
                <if test="category != null and category.size() > 0">
                    <foreach collection="category" item="item" separator="," open=" and lv4_category_code in(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dimension != null and dimension != '' and dimension == 'bySku'">
                <if test="category != null and category.size() > 0">
                    <foreach collection="category" item="item" separator="," open=" and sku_code in(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="rdc != null and rdc.size() > 0">
                <foreach collection="rdc" item="item" separator="," open=" and rdc_code in(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by id
    </select>

    <select id="selectCount" resultType="java.lang.Long">
        select count(0) from <include refid="Table_Name"/>
        <where>
            inv_strategy_id = #{strategyId} and status = #{status}
            <if test="dimension != null and dimension != '' and dimension == 'byAbc'">
                <if test="category != null and category.size() > 0">
                    <foreach collection="category" item="item" separator="," open=" and abc_type in(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dimension != null and dimension != '' and dimension == 'byOne'">
                <if test="category != null and category.size() > 0">
                    <foreach collection="category" item="item" separator="," open=" and lv1_category_code in(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dimension != null and dimension != '' and dimension == 'byTwo'">
                <if test="category != null and category.size() > 0">
                    <foreach collection="category" item="item" separator="," open=" and lv2_category_code in(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dimension != null and dimension != '' and dimension == 'byFour'">
                <if test="category != null and category.size() > 0">
                    <foreach collection="category" item="item" separator="," open=" and lv4_category_code in(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dimension != null and dimension != '' and dimension == 'bySku'">
                <if test="category != null and category.size() > 0">
                    <foreach collection="category" item="item" separator="," open=" and sku_code in(" close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="rdc != null and rdc.size() > 0">
                <foreach collection="rdc" item="item" separator="," open=" and rdc_code in(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update <include refid="Table_Name"/>
            <set>
                <if test="item.operatorCode != null and item.operatorCode != ''">operator_code = #{item.operatorCode},</if>
                <if test="item.operatorName != null and item.operatorName != ''">operator_name = #{item.operatorName},</if>
                <if test="item.collocationMethod != null and item.collocationMethod != ''">collocation_method = #{item.collocationMethod},</if>
                operation_time  = now(),
                <if test="item.x1 != null">x1 = #{item.x1},</if>
                <if test="item.x1 != null">x2 = #{item.x2},</if>
                <if test="item.targetToc != null">target = #{item.targetToc},</if>
                <if test="item.targetToc == null">target = null,</if>
                <if test="item.safety != null">safety = #{item.safety},</if>
                <if test="item.safety == null">safety = null,</if>
                gmt_modified = now()
            </set>
            where id = #{item.id} and status = #{item.status}
        </foreach>
    </update>

    <update id="batchUpdateByIds">
        update <include refid="Table_Name"/>
        <set>
            <if test="operatorCode != null and operatorCode != ''">operator_code = #{operatorCode},</if>
            <if test="operatorName != null and operatorName != ''">operator_name = #{operatorName},</if>
            operation_time  = now(),
            collocation_method = #{collocationMethod},
            gmt_modified = now()
        </set>
        <where>
            status = #{status}
            <if test="ids != null and ids.size() > 0">
                <foreach collection="ids" item="item" separator="," open=" and id in(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

    <delete id="deleteByStrategyIdAndStatus">
        delete from <include refid="Table_Name"/>
        where inv_strategy_id = #{strategyId} and status = #{status}
    </delete>

    <update id="enableByStrategyId">
        update <include refid="Table_Name"/>
        set status = 1
        where inv_strategy_id = #{strategyId} and status = 0
    </update>

    <delete id="deleteByStatus">
        delete from <include refid="Table_Name"/>
        where status = #{status}
    </delete>

    <delete id="deletebyStrategyId">
        delete from <include refid="Table_Name"/>
        where inv_strategy_id = #{strategyId}
    </delete>

</mapper>