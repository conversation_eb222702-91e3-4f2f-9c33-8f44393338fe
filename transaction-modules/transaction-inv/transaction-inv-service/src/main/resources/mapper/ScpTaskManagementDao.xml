<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.task.dao.ScpTaskManagementMapper">

    <resultMap id="Basic_Result_Map" type="cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskManagementDO">
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="task_type" jdbcType="VARCHAR" property="taskType"/>
        <result column="impact_range" jdbcType="DECIMAL" property="impactRange"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="trigger_start_time" jdbcType="TIMESTAMP" property="triggerStartTime"/>
        <result column="trigger_end_time" jdbcType="TIMESTAMP" property="triggerEndTime"/>
        <result column="scheduling_type" jdbcType="VARCHAR" property="schedulingType"/>
        <result column="scheduling_time" jdbcType="TIMESTAMP" property="schedulingTime"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg"/>
        <result column="operator_code" jdbcType="VARCHAR" property="operatorCode"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime"/>
    </resultMap>

    <sql id="Table_Name">
        cdop_biz.scp_task_management
    </sql>

    <sql id="Basic_Where_Clause">
        <where>
            <!-- 任务状态 -->
            <if test="status != null and status != '' and status != 'all'">
                and status = #{status}
            </if>
            <if test="multiStatus != null and multiStatus.size() > 0">
                <foreach collection="multiStatus" item="code" separator="," open=" and status in(" close=")">
                    #{code}
                </foreach>
            </if>
            <!-- 任务id或名称 -->
            <if test="task != null and task.size() > 0">
                <foreach collection="task" item="item" separator="," open=" and task_id in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 任务类型 -->
            <if test="taskType != null and taskType.size() > 0">
                <foreach collection="taskType" item="item" separator="," open=" and task_type in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 任务创建时间 -->
            <if test="createTime != null">
                and gmt_create >= #{createTime}
            </if>
            <!-- 任务调度类型 -->
            <if test='schedulingType != null and schedulingType != ""'>
                and scheduling_type = #{schedulingType}
            </if>

        </where>
    </sql>

    <select id="getSumNumber" resultType="cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskManagementSumNumberDO">
        SELECT COUNT(*) AS allSum,
        SUM(CASE WHEN status = 'not_run' THEN 1 ELSE 0 END) AS notRunSum,
        SUM(CASE WHEN status = 'await' THEN 1 ELSE 0 END) AS awaitSum,
        SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) AS runningSum,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) AS completedSum,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) AS failedSum
        from
        <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>
    </select>

    <select id="selectByCondition" resultMap="Basic_Result_Map">
        select
        *
        from
        <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>

    </select>

    <select id="selectCount" resultType="long">
        select
        count(1)
        from
        <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>
    </select>

    <select id="queryByTaskIdOrName" resultMap="Basic_Result_Map">
        select
        task_id,task_name
        from
        <include refid="Table_Name"/>
        <where>
            <if test="task != null and task != ''">
                task_id like concat('%',#{task},'%') or task_name like concat('%',#{task},'%')
            </if>
        </where>
    </select>

    <update id="update">
        update <include refid="Table_Name"/>
        <set>
            status = #{status}, operation_time = now()
            <if test="errorMsg != null">
                ,error_msg = #{errorMsg}
            </if>
            <if test="triggerStartTime != null">
                ,trigger_start_time = now()
            </if>
            <if test="triggerEndTime != null or triggerEndTime != null">
                ,trigger_end_time = now()
            </if>
            <if test='operatorCode != null and operatorCode != ""'>
                ,operator_code = #{operatorCode}
            </if>
            <if test='operatorName != null and operatorName != ""'>
                ,operator_name = #{operatorName}
            </if>
        </set>
        <where>
            <if test="task != null and task.size() > 0">
                <foreach collection="task" item="item" separator="," open=" and task_id in(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test='checkNqStatus != null and checkNqStatus != ""'>
                and status != #{checkNqStatus}
            </if>

        </where>
    </update>

    <sql id="Insert_Column_List">
        task_id, task_name, task_type, impact_range, status
        , scheduling_type, scheduling_time
        , remarks, error_msg, operator_code, operator_name, operation_time
        ,model_code, model_name, scene, scheduling_time_param
    </sql>

    <update id="batchUpsert">
        insert into <include refid="Table_Name" />
        (<include refid="Insert_Column_List"/>)
        values
        <foreach collection="params" item="item" separator=",">
            (   #{item.taskId},
                #{item.taskName},
                #{item.taskType},
                #{item.impactRange},
                #{item.status},
                #{item.schedulingType},
                #{item.schedulingTime},
                #{item.remarks},
                #{item.errorMsg},
                #{item.operatorCode},
                #{item.operatorName},
                now(),
                #{item.modelCode},
                #{item.modelName},
                #{item.scene},
                #{item.schedulingTimeParam}
            )
        </foreach>
<!--        on duplicate key update-->
<!--        task_name = values(task_name),-->
<!--        task_type = values(task_type),-->
<!--        impact_range = values(impact_range),-->
<!--        status = values(status),-->
<!--        remarks = values(remarks),-->
<!--        error_msg = values(error_msg),-->
<!--        operator_code = values(operator_code),-->
<!--        operator_name = values(operator_name),-->
<!--        operation_time = values(operation_time)-->
    </update>











    <resultMap id="Basic_CoveringMtWh_Map" type="cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskImpactRangeDO">
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode"/>
        <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName"/>
    </resultMap>

    <sql id="CoveringMtWh_Table_Name">
        cdop_biz.scp_task_cover_range
    </sql>

    <sql id="Basic_CoveringMtWh_Where_Clause">
        <where>
            <!-- 任务id -->
            <if test="taskIds != null and taskIds.size() > 0">
                <foreach collection="taskIds" item="item" separator="," open=" and task_id in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!--物料编码-->
            <if test="material != null and material.size() > 0">
                <foreach collection="material" item="item" separator="," open="and material_code in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 仓库编码 -->
            <if test="warehouse != null and warehouse.size() > 0">
                <foreach collection="warehouse" item="item" separator="," open="and warehouse_code in(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="queryImpactRange" resultMap="Basic_CoveringMtWh_Map">
        select
        *
        from
        <include refid="CoveringMtWh_Table_Name"/>
        <include refid="Basic_CoveringMtWh_Where_Clause"/>

    </select>

    <select id="selectImpactRangeCount" resultType="long">
        select
        count(1)
        from
        <include refid="CoveringMtWh_Table_Name"/>
        <include refid="Basic_CoveringMtWh_Where_Clause"/>
    </select>

    <sql id="Insert_ImpactRange_Column_List">
        task_id, material_code, material_name, warehouse_code, warehouse_name
    </sql>

    <update id="batchInsertImpactRange">
        insert into <include refid="CoveringMtWh_Table_Name" />
        (<include refid="Insert_ImpactRange_Column_List"/>)
        values
        <foreach collection="params" item="item" separator=",">
            (#{item.taskId},
            #{item.materialCode},
            #{item.materialName},
            #{item.warehouseCode},
            #{item.warehouseName}
            )
        </foreach>
    </update>

    <delete id="deleteTaskByTaskId">
        delete from <include refid="Table_Name"/>
        <where>
            task_id = #{taskId} and status = 'not_run'
        </where>
    </delete>


</mapper>