<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.transfer.dao.InventoryDataDao">
    <sql id="Table_Name">
        cdop_biz.tdm_kcjh_transfer_plan
    </sql>
    <sql id="Left_Table_Name">
        cdop_biz.tdm_kcjh_sku_repln_type
    </sql>

    <sql id="Basic_Where_Clause">
        <where>
            <!-- 生产编码 -->
            <if test="productionCodingList != null and productionCodingList.size() > 0">
                <foreach collection="productionCodingList" item="item" separator="," open=" and plan.production_coding in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- sku编码 -->
            <if test="skuList != null and skuList.size() > 0">
                <foreach collection="skuList" item="item" separator="," open=" and plan.sku_code in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- abc类型 -->
            <if test="abcType != null and abcType.size() > 0">
                <foreach collection="abcType" item="item" separator="," open=" and plan.abc_type in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 调出物理仓 -->
            <if test="outPhysicalWarehouseNameList != null and outPhysicalWarehouseNameList.size() > 0">
                <foreach collection="outPhysicalWarehouseNameList" item="item" separator="," open=" and plan.out_physical_warehouse_name in(" close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 调出逻辑仓 -->
            <if test="outLogicalWarehouseNameList != null and outLogicalWarehouseNameList.size() > 0">
                <foreach collection="outLogicalWarehouseNameList" item="item" separator="," open=" and plan.out_logical_warehouse_name in(" close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 调入物理仓 -->
            <if test="inLogicalWarehouseNameList != null and inLogicalWarehouseNameList.size() > 0">
                <foreach collection="inLogicalWarehouseNameList" item="item" separator="," open=" and plan.in_logical_warehouse_name in(" close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 调入逻辑仓 -->
            <if test="inPhysicalWarehouseNameList != null and inPhysicalWarehouseNameList.size() > 0">
                <foreach collection="inPhysicalWarehouseNameList" item="item" separator="," open=" and plan.in_physical_warehouse_name in(" close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 效期规则 -->
            <if test="validityRuleList != null and validityRuleList.size() > 0">
                <foreach collection="validityRuleList" item="item" separator="," open=" and plan.validity_rule in(" close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 运输方式 -->
            <if test="modeOfTransportList != null and modeOfTransportList.size() > 0">
                <foreach collection="modeOfTransportList" item="item" separator="," open=" and plan.mode_of_transport in(" close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 告警状态 -->
            <if test="alarmStatusList != null and alarmStatusList.size() > 0">
                <foreach collection="alarmStatusList" item="item" separator="," open=" and plan.alarm_status in(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 补货类型 -->
            <if test="replnTypeList != null and replnTypeList != '' and replnTypeList == 'toC'">
                and (sku.repln_type = #{replnTypeList} or sku.repln_type is null or sku.repln_type = '')
            </if>
            <if test="replnTypeList != null and replnTypeList != '' and replnTypeList == 'toB'">
                and sku.repln_type = #{replnTypeList}
            </if>

        </where>
    </sql>

    <select id="selectInventoryDataList" resultType="cn.aliyun.ryytn.modules.inv.entity.transfer.dos.InventoryData">
        select
        *
        from
        <include refid="Table_Name"/>
        limit 10
    </select>

    <select id="selectInventoryDataPageList" resultType="cn.aliyun.ryytn.modules.inv.entity.transfer.dos.InventoryData">
        select
            plan.*
        from
        <include refid="Table_Name"/> plan
        left join <include refid="Left_Table_Name"/> sku
        on plan.sku_code = sku.sku_code
        <include refid="Basic_Where_Clause" />

    </select>

    <select id="selectCount" resultType="java.lang.Long">
        select
        count(1)
        from
        <include refid="Table_Name"/> plan
        left join <include refid="Left_Table_Name"/> sku
        on plan.sku_code = sku.sku_code
        <include refid="Basic_Where_Clause" />
    </select>

    <select id="getProductionCodingList" resultType="string">
        SELECT DISTINCT production_coding
        FROM <include refid="Table_Name"/>
        WHERE production_coding IS NOT NULL
        <if test="key != null and key != ''">
            and production_coding like concat('%',#{key},'%')
        </if>
        ORDER BY production_coding
    </select>


    <select id="getOutPhysicalWarehouseNameList" resultType="string">
        SELECT DISTINCT out_physical_warehouse_name
        FROM <include refid="Table_Name"/>
        WHERE out_physical_warehouse_name IS NOT NULL
        <if test="key != null and key != ''">
            and out_physical_warehouse_name like concat('%',#{key},'%')
        </if>
        ORDER BY out_physical_warehouse_name
    </select>

    <select id="getOutLogicalWarehouseNameList" resultType="string">
        SELECT DISTINCT out_logical_warehouse_name
        FROM <include refid="Table_Name"/>
        WHERE out_logical_warehouse_name IS NOT NULL
        <if test="key != null and key != ''">
            and out_logical_warehouse_name like concat('%',#{key},'%')
        </if>
        ORDER BY out_logical_warehouse_name
    </select>

    <select id="getInLogicalWarehouseNameList" resultType="string">
        SELECT DISTINCT in_logical_warehouse_name
        FROM <include refid="Table_Name"/>
        WHERE in_logical_warehouse_name IS NOT NULL
        <if test="key != null and key != ''">
            and in_logical_warehouse_name like concat('%',#{key},'%')
        </if>
        ORDER BY in_logical_warehouse_name
    </select>

    <select id="getInPhysicalWarehouseNameList" resultType="string">
        SELECT DISTINCT in_physical_warehouse_name
        FROM <include refid="Table_Name"/>
        WHERE in_physical_warehouse_name IS NOT NULL
        <if test="key != null and key != ''">
            and in_physical_warehouse_name like concat('%',#{key},'%')
        </if>
        ORDER BY in_physical_warehouse_name
    </select>

    <select id="getValidityRuleList" resultType="string">
        SELECT DISTINCT validity_rule
        FROM <include refid="Table_Name"/>
        WHERE validity_rule IS NOT NULL
        <if test="key != null and key != ''">
            and validity_rule like concat('%',#{key},'%')
        </if>
        ORDER BY validity_rule
    </select>

    <select id="getModeOfTransportList" resultType="string">
        SELECT DISTINCT mode_of_transport
        FROM <include refid="Table_Name"/>
        WHERE mode_of_transport IS NOT NULL
        <if test="key != null and key != ''">
            and mode_of_transport like concat('%',#{key},'%')
        </if>
        ORDER BY mode_of_transport
    </select>

    <select id="getAlarmStatusList" resultType="string">
        SELECT DISTINCT alarm_status
        FROM <include refid="Table_Name"/>
        WHERE alarm_status IS NOT NULL
        <if test="key != null and key != ''">
            and alarm_status like concat('%',#{key},'%')
        </if>
        ORDER BY alarm_status
    </select>

</mapper>