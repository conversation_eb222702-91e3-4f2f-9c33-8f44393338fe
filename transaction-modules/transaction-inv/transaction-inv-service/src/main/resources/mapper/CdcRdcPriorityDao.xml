<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.business.dao.CdcRdcPriorityDao">
    <sql id="Table_Name">
        cdop_biz.tdm_kcjh_conf_cdc_rdc_priority
    </sql>
    <sql id="Basic_Where_Clause">
        <where>
            status = 1
            <!-- 工厂仓编码 -->
            <if test="cdc != null and cdc.size() > 0">
                <foreach collection="cdc" item="item" separator="," open=" and cdc_code in(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="rdc != null and rdc.size() > 0">
                <foreach collection="rdc" item="item" separator="," open=" and rdc_code in(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test='quickQueryClauseValue != null and quickQueryClauseValue != ""'>
                and ${quickQueryClauseValue}
            </if>
        </where>
    </sql>

    <select id="selectByCondition" resultType="cn.aliyun.ryytn.modules.inv.entity.business.dos.CdcRdcPriorityDO">
        select
        *
        from
        <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>
        order by case when cdc_supply_priority is null and rdc_oos_priority is null then 0
        when cdc_supply_priority is null or rdc_oos_priority is null then 1
        else 2 end, gmt_modified desc
    </select>
    <select id="selectCount" resultType="long">
        select
        count(1)
        from
        <include refid="Table_Name"/>
        <include refid="Basic_Where_Clause"/>
    </select>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update <include refid="Table_Name"/>
            <set>
                <if test="item.operatorCode != null and item.operatorCode != ''">operator_code = #{item.operatorCode},</if>
                <if test="item.operatorName != null and item.operatorName != ''">operator_name = #{item.operatorName},</if>
                cdc_supply_priority = #{item.cdcSupplyPriority},
                rdc_oos_priority = #{item.rdcOosPriority},
                operation_time  = now(),
                gmt_modified = now()
            </set>
            where cdc_code = #{item.cdcCode} and rdc_code=#{item.rdcCode}
        </foreach>
    </update>

</mapper>