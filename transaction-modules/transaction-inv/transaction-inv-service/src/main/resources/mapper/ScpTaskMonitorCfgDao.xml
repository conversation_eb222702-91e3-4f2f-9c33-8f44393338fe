<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.inv.task.dao.ScpTaskMonitorCfgMapper">

    <sql id="Table_Name">
        cdop_biz.scp_task_monitor_cfg
    </sql>

    <select id="selectByCondition" resultType="cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject.ScpTaskMonitorCfgDO">
        select
            *
        from
        <include refid="Table_Name" />
        where task_type = #{taskType} and date_expr = #{dateExpr}
    </select>

</mapper>