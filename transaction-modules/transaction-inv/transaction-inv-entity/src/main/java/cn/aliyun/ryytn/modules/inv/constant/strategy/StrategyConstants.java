package cn.aliyun.ryytn.modules.inv.constant.strategy;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-03-06 20:04
 * @description
 */
public interface StrategyConstants {
    interface StrategyCenter {
        String INV_STRATEGY_STATUS_LIST = "invStrategyStatusList";

        String REPLN_TYPE_LIST = "replnTypeList";

        String DIMENSION = "dimension";

        String REPLN_TYPE = "replnType";

        String TOKEN_ID = "Id";

        String TOKEN = "token";

        String META = "metaInfoWrapper";

        String ENABLE_LIST = "enableList";

        String TOTAL = "total";

        String PRIORITY = "priority";

        String DUPLICATION = "duplication";

        String REPEAT_CONFIRM = "repeatConfirm";

        String PRIORITY_CONFIRM = "priorityConfirm";

        String TYPE ="type";

        String BY_STRATEGY_ID = "byStrategyId";

        String BY_DIMENSION = "byDimension";

        String BY_ABC = "byAbc";

        String BY_ONE = "byOne";

        String BY_TWO = "byTwo";

        String BY_FOUR = "byFour";

        String BY_SKU = "bySku";

        String SELECT_ALL = "selectAll";

    }

    interface StrategyResults {
        String ABC_TYPE = "abcType";

        String PRIORITY_LIST = "priorityList";

        String LV1 = "lv1";

        String LV2 = "lv2";

        String LV4 = "lv4";

        String ABC_TYPE_NAME = "abc";

        String SKU = "sku";

        String RDC_NAME = "rdc";

        String CONFLICT = "conflictInvStrategyName";

        String CONFLICT_COLOR = "#146acc";

        String MANUAL_SAFETY_DAYS = "manualSafetyDaysStr";

        String MANUAL_TARGET_DAYS = "manualTargetDaysStr";

        String STRATEGY_RESULTS = "STRATEGY_RESULTS";

        String STRATEGY_RESULTS_QUERY = "quick_query_strategy_results";

    }

    interface SafetyStockParameters {
        String LV1 = "lv1";

        String LV2 = "lv2";

        String LV4 = "lv4";

        String ABC_TYPE_NAME = "abc";

        String SKU = "sku";

        String RDC_NAME = "rdc";

        String SKU_AND_RDC = "skuAndRdc";

        String SERVICE_RATIO = "serviceRatioStr";

        String MIN_SAFETY_DAYS = "minSafetyDaysStr";

        String MAX_SAFETY_DAYS = "maxSafetyDaysStr";
    }

    interface SafetyStock {

        String SAFETY_STOCK_QUERY = "quick_query_safety_stock";

    }
}
