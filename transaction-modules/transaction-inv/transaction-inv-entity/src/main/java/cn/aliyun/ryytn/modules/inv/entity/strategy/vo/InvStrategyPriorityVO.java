package cn.aliyun.ryytn.modules.inv.entity.strategy.vo;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeField;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeFieldWrapper;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext.LocalDateTimeToStringConvertExt;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

import static cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants.StrategyCenter.DUPLICATION;
import static cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants.StrategyCenter.PRIORITY;
import static cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants.StrategyResults.PRIORITY_LIST;

/**
 * <AUTHOR>
 * @date 2025/4/25 17:32
 * @description：
 */
@Data
public class InvStrategyPriorityVO {

    /**
     * 主键Id
     */
    private Long id;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
    /**
     * sku
     */
    @MetaField(value = "SKU", mapping = {PRIORITY,DUPLICATION,PRIORITY_LIST})
    List<MergeFieldWrapper> sku;
    /**
     * sku编码
     */
    @MergeField(value = "sku")
    private String skuCode;
    /**
     * sku名称
     */
    @MergeField(value = "sku")
    private String skuName;
    /**
     * rdc仓库编码
     */
    private String rdcCode;
    /**
     * rdc仓库名称
     */
    @MetaField(value = "仓库", mapping = {PRIORITY,DUPLICATION,PRIORITY_LIST})
    private String rdcName;
    /**
     * 当前库存策略Id
     */
    private Long currentInvStrategyId;
    /**
     * 当前库存策略名称
     */
    @MetaField(value = "当前库存策略", mapping = {PRIORITY_LIST}, extra = "#146acc")
    private String currentInvStrategyName;
    /**
     * 当前库存策略维度
     */
    private String currentDimension;
    /**
     * 当前库存策略维度名称
     */
    @MetaField(value = "当前库存策略维度", mapping = {PRIORITY,DUPLICATION,PRIORITY_LIST})
    private String currentDimensionName;
    /**
     * 冲突库存策略维度
     */
    private String conflictDimension;
    /**
     * 冲突库存策略维度名称
     */
    @MetaField(value = "冲突库存策略维度", mapping = {PRIORITY,DUPLICATION,PRIORITY_LIST})
    private String conflictDimensionName;
    /**
     * 冲突库存策略Id
     */
    private Long conflictInvStrategyId;
    /**
     * 冲突库存策略名称
     */
    @MetaField(value = "冲突库存策略", mapping = {PRIORITY,DUPLICATION,PRIORITY_LIST})
    private String conflictInvStrategyName;
    /**
     * 操作人编码
     */
    private String conflictOperatorCode;
    /**
     * 操作人名称
     */
    @MetaField(value = "冲突库存策略操作人", mapping = {PRIORITY,DUPLICATION,PRIORITY_LIST})
    private String conflictOperatorName;
    /**
     * 操作时间
     */
    @MetaField(value = "冲突库存策略最新更新时间", mapping = {PRIORITY,DUPLICATION,PRIORITY_LIST})
    @ConvertField(extensible = LocalDateTimeToStringConvertExt.class)
    private String conflictUpdateTime;
    /**
     * 优先级生效范围
     */
    private String priorityTakeEffect;
    /**
     * 优先级生效范围名称
     */
    @MetaField(value = "优先级生效范围", mapping = {PRIORITY,PRIORITY_LIST})
    private String priorityTakeEffectName;
    /**
     * 操作人编码
     */
    private String operatorCode;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;



}
