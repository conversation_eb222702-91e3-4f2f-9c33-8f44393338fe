package cn.aliyun.ryytn.modules.inv.entity.strategy.request;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.BaseAction;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import lombok.Data;

import java.time.LocalDateTime;

import static cn.aliyun.ryytn.modules.inv.constant.strategy.StrategyConstants.StrategyCenter.*;

/**
 * <AUTHOR>
 * @date 2025/4/28 18:34
 * @description：
 */
@Data
public class InvStrategyParametersTocVO extends BaseAction {

    /**
     * id
     */
    private Long id;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
    /**
     * 库存策略Id
     */
    private Long invStrategyId;
    /**
     * ABC分类
     */
    private String abcType;
    /**
     * ABC分类名称
     */
    @MetaField(value = "ABC分类", mapping = {BY_ABC})
    private String abcTypeName;
    /**
     * 产品分类编码
     */
    private String lv1CategoryCode;
    /**
     * 产品分类名称
     */
    @MetaField(value = "一级品类", mapping = {BY_ONE})
    private String lv1CategoryName;
    /**
     * 产品大类编码
     */
    private String lv2CategoryCode;
    /**
     * 产品大类名称
     */
    @MetaField(value = "二级品类", mapping = {BY_TWO})
    private String lv2CategoryName;
    /**
     * 产品小类编码
     */
    private String lv4CategoryCode;
    /**
     * 产品小类名称
     */
    @MetaField(value = "四级品类", mapping = {BY_FOUR})
    private String lv4CategoryName;
    /**
     * SKU编码
     */
    @MetaField(value = "SKU编码", mapping = {BY_SKU})
    private String skuCode;
    /**
     * SKU名称
     */
    @MetaField(value = "SKU简称", mapping = {BY_SKU})
    private String skuName;
    /**
     * RDC仓编码
     */
    private String rdcCode;
    /**
     * RDC仓名称
     */
    @MetaField(value = "仓库")
    private String rdcName;
    /**
     * 日均销
     */
    @MetaField(value = "日均销", mapping = {BY_SKU})
    private Integer dailySalesAvg;
    /**
     * 配置方式
     */
    @MetaField(value = "配置方式", mapping = {"0"})
    public String collocationMethod;

    /**
     * 配置方式
     */
    @MetaField(value = "配置方式", mapping = {"1"})
    public String collocationMethodName;
    /**
     * X1
     */
    @MetaField(value = "X1")
    private Integer x1;
    /**
     * X2
     */
    @MetaField(value = "X2")
    private Integer x2;
    /**
     * 安全库存
     */
    @MetaField(value = "安全库存", tooltip = "值为空代表读取算法建议值")
    private Integer safety;
    /**
     * 目标库存
     */
    private Integer target;
    @MetaField(value = "目标库存", tooltip = "值为空代表读取算法建议值")
    @ConvertField(value = "target")
    private Integer targetToc;
    /**
     * 操作人编码
     */
    private String operatorCode;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    /**
     * 状态 (0:中间态，1:最终态)
     */
    private Integer status;

}
