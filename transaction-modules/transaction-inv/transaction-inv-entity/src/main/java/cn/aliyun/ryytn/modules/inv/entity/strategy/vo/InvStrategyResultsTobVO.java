package cn.aliyun.ryytn.modules.inv.entity.strategy.vo;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.BaseAction;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaType;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeField;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeFieldWrapper;
import cn.aliyun.ryytn.modules.inv.common.convent.DecimalToPercentConvertExt;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext.LocalDateTimeToStringConvertExt;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/6 11:47
 * @description：
 */
@Data
public class InvStrategyResultsTobVO extends BaseAction {
    @MetaField(value = "SKU信息")
    List<MergeFieldWrapper> sku;
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * ABC分类
     */
    @MergeField(value = "sku", newline = 0, sort = 0,  metaType = MetaType.SQUARE_TAG)
    private String abcType;

    /**
     * ABC分类名称
     */
    private String abcTypeName;

    /**
     * sku编码
     */
    @MergeField(value = "sku", newline = 1, sort = 1, metaType = MetaType.CODE)
    @MetaField(value = "SKU编码", primaryKey = true, hidden = true)
    private String skuCode;

    /**
     * sku名称
     */
    @MergeField(value = "sku", newline = 1, sort = 2, metaType = MetaType.NAME)
    @MetaField(value = "SKU名称", primaryKey = true, hidden = true)
    private String skuName;

    /**
     * 产品分类编码
     */
    private String lv1CategoryCode;

    /**
     * 产品分类名称
     */
    @MergeField(value = "sku", newline = 0, sort = 3, extra = "#ec7020")
    private String lv1CategoryName;

    /**
     * 产品大类编码
     */
    private String lv2CategoryCode;

    /**
     * 产品大类名称
     */
    @MergeField(value = "sku", newline = 0, sort = 4, extra = "#ec7020", format = "-> %s")
    private String lv2CategoryName;

    /**
     * 产品小类编码
     */
    private String lv4CategoryCode;

    /**
     * 产品小类名称
     */
    @MergeField(value = "sku", newline = 0, sort = 5, extra = "#ec7020", format = "-> %s")
    private String lv4CategoryName;

    private String categoryName;

    /**
     * rdc仓编码
     */
    private String rdcCode;

    /**
     * rdc仓名称
     */
    @MetaField(value = "仓库", primaryKey = true)
    private String rdcName;


    @MetaField(value = "安全库存计算参数")
    List<MergeFieldWrapper> safetyStock;

    /**
     * 服务水平
     */
    @MergeField(value = "safetyStock", format = "服务水平：%s")
    @ConvertField(extensible = DecimalToPercentConvertExt.class)
    private String serviceRatio;

    private String serviceRatioStr;

    /**
     * 最小安全库存天数
     */
    @MergeField(value = "safetyStock", format = "最小安全库存天数：%s")
    private Integer minSafetyDays;

    /**
     * 最大安全库存天数
     */
    @MergeField(value = "safetyStock", format = "最大安全库存天数：%s")
    private Integer maxSafetyDays;

    /**
     * 日均销
     */
    @MergeField(value = "safetyStock", format = "日均销：%s")
    private Integer dasNum;

    /**
     * 提前期天数
     */
    @MergeField(value = "safetyStock", format = "提前期天数：%s")
    private Integer leadTimeDay;

    /**
     * 提前期库存
     */
    @MergeField(value = "safetyStock", format = "提前期库存：%s")
    private Integer leadTimeCnt;

    /**
     * 算法建议安全库存天
     */
    @MetaField(value = "天数", parent = "安全库存（算法建议）", sortable = true, orderColumn = "sug_safety_days")
    private Integer sugSafetyDays;

    /**
     * 算法建议安全库存件
     */
    @MetaField(value = "件数", parent = "安全库存（算法建议）", sortable = true, orderColumn = "sug_safety_qty")
    private Integer sugSafetyQty;

    /**
     * 人工调整安全库存天
     */
    @MetaField(value = "天数", parent = "安全库存（人工调整）", sortable = true, orderColumn = "manual_safety_days")
    private Integer manualSafetyDays;

    /**
     * 人工调整安全库存件
     */
    @MetaField(value = "件数", parent = "安全库存（人工调整）", sortable = true, orderColumn = "manual_safety_qty")
    private Integer manualSafetyQty;

    /**
     * 分销优化安全库存天
     */
    @MetaField(value = "天数", parent = "安全库存（分销优化）", sortable = true, orderColumn = "dist_safety_days")
    private Integer distSafetyDays;

    /**
     * 分销优化安全库存件
     */
    @MetaField(value = "件数", parent = "安全库存（分销优化）", sortable = true, orderColumn = "dist_safety_qty")
    private Integer distSafetyQty;

    /**
     * 算法建议目标库存天
     */
    @MetaField(value = "天数", parent = "目标库存（算法建议）", sortable = true, orderColumn = "sug_target_days")
    private Integer sugTargetDays;

    /**
     * 算法建议目标库存件
     */
    @MetaField(value = "件数", parent = "目标库存（算法建议）", sortable = true, orderColumn = "sug_target_qty")
    private Integer sugTargetQty;

    /**
     * 人工调整目标库存天
     */
    @MetaField(value = "天数", parent = "目标库存（人工调整）", sortable = true, orderColumn = "manual_target_days")
    private Integer manualTargetDays;

    /**
     * 人工调整目标库存件
     */
    @MetaField(value = "件数", parent = "目标库存（人工调整）", sortable = true, orderColumn = "manual_target_qty")
    private Integer manualTargetQty;

    /**
     * 分销优化目标库存天
     */
    @MetaField(value = "天数", parent = "目标库存（分销优化）", sortable = true, orderColumn = "dist_target_days")
    private Integer distTargetDays;

    /**
     * 分销优化目标库存件
     */
    @MetaField(value = "件数", parent = "目标库存（分销优化）", sortable = true, orderColumn = "dist_target_qty")
    private Integer distTargetQty;

    /**
     * 库存策略Id
     */
    private Long invStrategyId;

    /**
     * 库存策略场景名称
     */
    @MetaField(value = "关联策略场景")
    private String invStrategyName;

    @MetaField(value = "库存策略参数")
    private List<MergeFieldWrapper> invStrategy;

    /**
     * A(极限水位)天
     */
    @MergeField(value = "invStrategy", format = "A天数：%s", newline = 0)
    private Integer paramLimitWaterLevelDays;

    /**
     * A(极限水位)件
     */
    @MergeField(value = "invStrategy", format = "| A数量：%s")
    private Integer paramLimitWaterLevelQty;

    /**
     * B(目标库存)天
     */
    @MergeField(value = "invStrategy", format = "B天数：%s", newline = 0)
    private Integer paramTargetInvDays;

    /**
     * B(目标库存)件
     */
    @MergeField(value = "invStrategy", format = "| B数量：%s")
    private Integer paramTargetInvQty;

    /**
     * C(再订货点)天
     */
    @MergeField(value = "invStrategy", format = "C天数：%s", newline = 0)
    private Integer paramRopCntDays;

    /**
     * C(再订货点)件
     */
    @MergeField(value = "invStrategy", format = "| C数量：%s")
    private Integer paramRopCntQty;

    /**
     * D(缺货报警点)天
     */
    @MergeField(value = "invStrategy", format = "D天数：%s", newline = 0)
    private Integer paramOosAlarmPointDays;

    /**
     * D(缺货报警点)件
     */
    @MergeField(value = "invStrategy", format = "| D数量：%s")
    private Integer paramOosAlarmPointQty;

    /**
     * 异常状态
     */
    @MetaField(value = "异常状态")
    private List<StrategyResultsExceptionVO> exception;

    private String errStatus;

    /**
     * 操作人编码
     */
    private String operatorCode;

    /**
     * 操作人名称
     */
    @MetaField(value = "操作人")
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 修改时间
     */
    @ConvertField(extensible = LocalDateTimeToStringConvertExt.class)
    @MetaField(value = "最近更新时间", sortable = true, orderColumn = "gmt_modified")
    private String gmtModified;

    /**
     * 0:中间态，1:最终态
     */
    private Integer status;

}
