package cn.aliyun.ryytn.modules.inv.entity.strategy.dos;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/6 11:47
 * @description：
 */
@Data
public class InvStrategyResultsTobDO {
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * ABC分类
     */
    private String abcType;

    /**
     * ABC分类名称
     */
    private String abcTypeName;

    /**
     * 产品分类编码
     */
    private String lv1CategoryCode;

    /**
     * 产品分类名称
     */
    private String lv1CategoryName;

    /**
     * 产品大类编码
     */
    private String lv2CategoryCode;

    /**
     * 产品大类名称
     */
    private String lv2CategoryName;

    /**
     * 产品小类编码
     */
    private String lv4CategoryCode;

    /**
     * 产品小类名称
     */
    private String lv4CategoryName;

    /**
     * rdc仓编码
     */
    private String rdcCode;

    /**
     * rdc仓名称
     */
    private String rdcName;

    /**
     * 服务水平
     */
    private BigDecimal serviceRatio;

    /**
     * 最小安全库存天数
     */
    private Integer minSafetyDays;

    /**
     * 最大安全库存天数
     */
    private Integer maxSafetyDays;

    /**
     * 日均销
     */
    private Integer dasNum;

    /**
     * 提前期天数
     */
    private Integer leadTimeDay;

    /**
     * 提前期库存
     */
    private Integer leadTimeCnt;

    /**
     * 算法建议安全库存天
     */
    private Integer sugSafetyDays;

    /**
     * 算法建议安全库存件
     */
    private Integer sugSafetyQty;

    /**
     * 人工调整安全库存天
     */
    private Integer manualSafetyDays;

    /**
     * 人工调整安全库存件
     */
    private Integer manualSafetyQty;

    /**
     * 分销优化安全库存天
     */
    private Integer distSafetyDays;

    /**
     * 分销优化安全库存件
     */
    private Integer distSafetyQty;

    /**
     * 算法建议目标库存天
     */
    private Integer sugTargetDays;

    /**
     * 算法建议目标库存件
     */
    private Integer sugTargetQty;

    /**
     * 人工调整目标库存天
     */
    private Integer manualTargetDays;

    /**
     * 人工调整目标库存件
     */
    private Integer manualTargetQty;

    /**
     * 分销优化目标库存天
     */
    private Integer distTargetDays;

    /**
     * 分销优化目标库存件
     */
    private Integer distTargetQty;

    /**
     * 库存策略Id
     */
    private Long invStrategyId;

    /**
     * 库存策略场景名称
     */
    private String invStrategyName;

    /**
     * A(极限水位)天
     */
    private Integer paramLimitWaterLevelDays;

    /**
     * A(极限水位)件
     */
    private Integer paramLimitWaterLevelQty;

    /**
     * B(目标库存)天
     */
    private Integer paramTargetInvDays;

    /**
     * B(目标库存)件
     */
    private Integer paramTargetInvQty;

    /**
     * C(再订货点)天
     */
    private Integer paramRopCntDays;

    /**
     * C(再订货点)件
     */
    private Integer paramRopCntQty;

    /**
     * D(缺货报警点)天
     */
    private Integer paramOosAlarmPointDays;

    /**
     * D(缺货报警点)件
     */
    private Integer paramOosAlarmPointQty;

    /**
     * 异常状态
     */
    private String errStatus;

    /**
     * 操作人编码
     */
    private String operatorCode;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 0:中间态，1:最终态
     */
    private Integer status;

}
