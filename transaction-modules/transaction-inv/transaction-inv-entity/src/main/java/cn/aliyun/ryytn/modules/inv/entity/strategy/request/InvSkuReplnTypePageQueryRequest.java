package cn.aliyun.ryytn.modules.inv.entity.strategy.request;

import cn.aliyun.ryytn.modules.inv.common.dao.common.request.BasePageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/6 13:23
 * @description：库存策略结果列表查询
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("SKU补货类型列表查询请求体")
public class InvSkuReplnTypePageQueryRequest extends BasePageQueryRequest {

    /**
     * sku编码
     */
    @ApiModelProperty("sku编码")
    private List<String> skuList;

    /**
     * 品类
     */
    @ApiModelProperty("品类")
    private List<String> categoryList;

    /**
     * 补货方式
     */
    @ApiModelProperty("补货方式")
    private List<String> replnTypeList;

    /**
     * 快速查询子句
     */
    private String quickQueryClause;
    private String quickQueryClauseValue;
}
