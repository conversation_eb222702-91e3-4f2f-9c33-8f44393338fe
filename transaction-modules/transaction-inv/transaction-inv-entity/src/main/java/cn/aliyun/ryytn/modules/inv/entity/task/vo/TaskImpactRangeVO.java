package cn.aliyun.ryytn.modules.inv.entity.task.vo;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import com.cainiao.cntech.dsct.scp.gei.common.meta.BaseAction;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/11/13 14:10
 * @description：任务覆盖范围
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskImpactRangeVO extends BaseAction {

    /**
     * 任务Id
     */
    private Long taskId;
    /**
     * 仓库编码
     */
    @MetaField(value = "仓库编码")
    private String warehouseCode;
    /**
     * 仓库名称
     */
    @MetaField(value = "仓库名称")
    private String warehouseName;
    /**
     * 物料编码
     */
    @MetaField(value = "产品编码")
    private String materialCode;
    /**
     * 物料名称
     */
    @MetaField(value = "产品名称")
    private String materialName;

}
