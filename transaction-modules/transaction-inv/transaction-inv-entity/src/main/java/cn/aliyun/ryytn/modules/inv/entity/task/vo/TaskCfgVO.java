package cn.aliyun.ryytn.modules.inv.entity.task.vo;


import cn.aliyun.ryytn.modules.inv.common.ability.meta.BaseAction;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext.BoolIntegerToStringConvertExt;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-19 11:21
 * @description 任务配置
 */
@Data
public class TaskCfgVO extends BaseAction {
     /**
      * 主键
      */
     private Long id;

     /**
      * 创建时间
      */
     private LocalDateTime gmtCreate;

     /**
      * 最新更新时间
      */
     private LocalDateTime gmtModified;

     /**
      * '任务类型编码' | '任务类型名称'
      */
     private String taskTypeCode;
     @MetaField("任务类型")
     private String taskTypeName;

     /**
      * '上游任务类型编码' | '上游任务类型名称'
      */
     @MetaField("上游任务类型")
     private String preTaskTypeName;

     /**
      * '下游任务类型编码' | '下游任务类型名称'
      */
     @MetaField("下游任务类型")
     private String nextTaskTypeName;

     /**
      * 'dataworks任务编码' | 'dataworks任务名称'
      */
     @MetaField("ODPS任务编码")
     private Long odpsCode;
     @MetaField("ODPS任务名称")
     private String odpsName;
     /**
      * 调度日期表达式
      */
     @MetaField("调度日期表达式")
     private String dateExpr;
     /**
      * 是否为根任务
      */
     @MetaField("是否为根任务")
     @ConvertField(value = "rootOdps", extensible = BoolIntegerToStringConvertExt.class)
     private String rootOdpsName;
     private String rootOdps;

     /**
      * 在同一组任务类型内，不同日期表达式之间的执行顺序
      */
     private Long dateExprPriority;

     /**
      * 是否启用，1启用，0不启用
      */
     @MetaField("是否启用")
     @ConvertField(value = "enable", extensible = BoolIntegerToStringConvertExt.class)
     private String enableName;
     private Integer enable;

}
