package cn.aliyun.ryytn.modules.inv.entity.business.dos;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:30
 * @description：
 */
@Data
public class CdcRdcPriorityDO {
    /**
     * cdc编码
     */
    private String cdcCode;

    /**
     * cdc名称
     */
    private String cdcName;

    /**
     * cdc供应优先级
     */
    private Integer cdcSupplyPriority;

    /**
     * rdc编码
     */
    private String rdcCode;

    /**
     * rdc名称
     */
    private String rdcName;

    /**
     * rdc缺货优先级
     */
    private Integer rdcOosPriority;

    /**
     * 操作人编码
     */
    private String operatorCode;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    /**
     * 状态 0未启用，1启用
     */
    private Integer status;
    /**
     * 更新日期
     */
    private Integer statDate;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CdcRdcPriorityDO that = (CdcRdcPriorityDO) o;
        return StringUtils.equals(cdcCode, that.cdcCode) &&
                StringUtils.equals(rdcCode, that.rdcCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cdcCode, rdcCode);
    }

    public String getKey() {
        return rdcCode + "_" + cdcCode;
    }
}
