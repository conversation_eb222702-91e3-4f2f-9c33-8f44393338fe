package cn.aliyun.ryytn.modules.inv.entity.transfer.request;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfo;
import lombok.Data;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025/5/20 14:28
 * @description：
 */
@Data
public class CommonConfigValueInsertRequest {

    /**
     * '模块'
     */
    private String moduleName;
    /**
     * '组别'
     */
    private String groupName;
    /**
     * '配置编码'
     */
    private String configCode;
    /**
     * '配置值'
     */
    private Object configValue;
    /**
     * '配置值扩展'
     */
    private Object valueExt;
    /**
     * '创建人编码'
     */
    private String creatorCode;
    /**
     * '创建人名称'
     */
    private String creatorName;
    /**
     * '租户编码'
     */
    private String tenantCode;

    private Collection<MetaInfo> children;

}