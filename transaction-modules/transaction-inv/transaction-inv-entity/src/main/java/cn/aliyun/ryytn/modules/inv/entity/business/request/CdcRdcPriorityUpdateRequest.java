package cn.aliyun.ryytn.modules.inv.entity.business.request;

import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.BizPk;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.annotation.ModifyAfter;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.annotation.ModifyBefore;
import cn.aliyun.ryytn.modules.inv.entity.business.dos.CdcRdcPriorityDO;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelValid;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:30
 * @description：
 */
@Data
public class CdcRdcPriorityUpdateRequest extends ImportTemplateVO {

    @BizPk
    @ExcelField(value = "工厂仓编码", importKey = true)
    private String cdcCode;

    @BizPk
    @ExcelField("工厂仓名称")
    private String cdcName;

    @ExcelField("工厂仓供应优先级")
    @ExcelValid
    private String cdcSupplyPriorityStr;

    @ModifyAfter({"c{null:-}","f{工厂仓供应优先级：%s}"})
    private Integer cdcSupplyPriority;

    @BizPk
    @ExcelField(value = "RDC编码",importKey = true)
    private String rdcCode;

    @BizPk
    @ExcelField("RDC名称")
    private String rdcName;

    @ExcelField("RDC缺货优先级")
    @ExcelValid
    private String rdcOosPriorityStr;

    @ModifyAfter({"c{null:-}","f{RDC缺货优先级：%s}"})
    private Integer rdcOosPriority;

    @ModifyBefore({"c{null:-}","f{工厂仓供应优先级：%s}"})
    private Integer cdcSupplyPriorityBefore;

    @ModifyBefore({"c{null:-}","f{RDC缺货优先级：%s}"})
    private Integer rdcOosPriorityBefore;

    private String operatorCode;

    private String operatorName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CdcRdcPriorityUpdateRequest that = (CdcRdcPriorityUpdateRequest) o;
        return StringUtils.equals(cdcCode, that.cdcCode) &&
                StringUtils.equals(rdcCode, that.rdcCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cdcCode, rdcCode);
    }

    public String getKey() {
        return rdcCode + "_" + cdcCode;
    }
}
