package cn.aliyun.ryytn.modules.inv.entity.transfer.dos;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collection;

@Data
public class ScpCommonConfigMeta {

    /**
     * 租户编码
     */
    private String tenantCode = "ryytn";

    /**
     * 模块
     */
    private String moduleName;

    /**
     * 组别
     */
    private String groupName;

    /**
     * 配置编码
     */
    private String configCode;

    /**
     * 创建人编码
     */
    private String creatorCode;


    /**
     * meta名称
     */
    private String value;

    /**
     * metaCode
     */
    private String code;

    /**
     * format
     */
    private String format;

    /**
     * @see cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaType
     */
    private String metaType = "text";

    /**
     * 映射
     */
    private String mapping = "{}";

    /**
     * 父meta描述
     */
    private String parent;

    /**
     * info
     */
    private String info;

    /**
     * 是否合并
     */
    private Integer yesOrNotMerge = 0;

    /**
     * 合并字段Meta描述
     */
    private String mergerMetaValue;

    /**
     * 合并字段MetaCode
     */
    private String mergerMetaCode;

    /**
     * 是否换行
     */
    private Integer newLine = 1;

    /**
     * 排序
     */
    private Integer sort = 0;

    /**
     * 是否隐藏
     */
    private Integer hidden = 0;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 创建人名称
     */
    private String creatorName;

    @ApiModelProperty("合并")
    private String merge;

    /**
     * 合并名称
     */
    @ApiModelProperty("合并名称")
    private String mergerName;

    /**
     * 当前列别名
     */
    @ApiModelProperty("当前列别名")
    private String anotherName;



    private Collection<MetaInfo> children;
}