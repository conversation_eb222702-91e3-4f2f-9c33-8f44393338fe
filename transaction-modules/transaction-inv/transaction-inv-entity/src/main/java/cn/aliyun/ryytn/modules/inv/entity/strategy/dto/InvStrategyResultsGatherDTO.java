package cn.aliyun.ryytn.modules.inv.entity.strategy.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/24 18:56
 * @description：
 */
@Data
public class InvStrategyResultsGatherDTO {

    /**
     * 补货类型
     */
    private String replnType;


    /**
     * 补货类型名称
     */
    private String replnTypeName;


    /**
     * 数量
     */
    private Integer count;

    public InvStrategyResultsGatherDTO() {

    }

    public InvStrategyResultsGatherDTO(String replnType, String replnTypeName, Integer count) {
        this.replnType = replnType;
        this.replnTypeName = replnTypeName;
        this.count = count;
    }


}
