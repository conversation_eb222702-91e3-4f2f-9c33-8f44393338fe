package cn.aliyun.ryytn.modules.inv.entity.strategy.request;

import cn.aliyun.ryytn.modules.inv.common.dao.common.request.BasePageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26 19:23
 * @description：库存策略结果列表查询
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("库存策略结果列表查询请求体")
public class InvStrategyResultsPageQueryRequest extends BasePageQueryRequest {

    /**
     * 补货类型
     */
    @ApiModelProperty("补货类型")
    private String replnType;
    /**
     * sku
     */
    @ApiModelProperty("sku")
    private List<String> sku;
    /**
     * 品类
     */
    @ApiModelProperty("品类")
    private List<String> category;
    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private List<String> categoryName;
    /**
     * ABC分类
     */
    @ApiModelProperty("ABC分类")
    private List<String> abcType;
    /**
     * 发货仓
     */
    @ApiModelProperty("发货仓")
    private List<String> rdc;
    /**
     * 发货仓
     */
    @ApiModelProperty("发货仓名称")
    private List<String> rdcName;
    /**
     * 策略场景
     */
    @ApiModelProperty("策略场景")
    private List<String> strategyName;
    /**
     * 异常状态
     */
    @ApiModelProperty("异常状态")
    private List<String> errStatus;

    private List<InvStrategyResulesUpdateRequest> itemList;

    /**
     * 操作人编码
     */
    public String operatorCode;
    /**
     * 操作人名称
     */
    public String operatorName;

    /**
     * 查询优先级生效
     */
    private String skuCode;
    /**
     * 查询优先级生效
     */
    private String rdcCode;

    private List<Long> idList;

    /**
     * 快速查询子句
     */
    private String quickQueryClause;
    private String quickQueryClauseValue;


}
