package cn.aliyun.ryytn.modules.inv.entity.strategy.vo;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.BaseAction;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaType;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeField;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeFieldWrapper;
import cn.aliyun.ryytn.modules.inv.common.convent.DecimalToPercentConvertExt;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext.LocalDateTimeToStringConvertExt;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/6 11:47
 * @description：
 */
@Data
public class InvSafetyStockParametersVO extends BaseAction {

    @MetaField(value = "SKU信息")
    private List<MergeFieldWrapper> sku;
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * ABC分类
     */
    @MergeField(value = "sku", newline = 0, sort = 0,  metaType = MetaType.SQUARE_TAG)
    private String abcType;

    /**
     * ABC分类名称
     */
    private String abcTypeName;

    /**
     * sku编码
     */
    @MergeField(value = "sku", newline = 1, sort = 1, metaType = MetaType.CODE)
    @MetaField(value = "SKU编码", primaryKey = true, hidden = true)
    private String skuCode;

    /**
     * sku名称
     */
    @MergeField(value = "sku", newline = 1, sort = 2, metaType = MetaType.NAME)
    @MetaField(value = "SKU名称", primaryKey = true, hidden = true)
    private String skuName;

    /**
     * 产品分类编码
     */
    private String lv1CategoryCode;

    /**
     * 产品分类名称
     */
    @MergeField(value = "sku", newline = 0, sort = 3, extra = "#ec7020")
    private String lv1CategoryName;

    /**
     * 产品大类编码
     */
    private String lv2CategoryCode;

    /**
     * 产品大类名称
     */
    @MergeField(value = "sku", newline = 0, sort = 4, extra = "#ec7020", format = "-> %s")
    private String lv2CategoryName;

    /**
     * 产品小类编码
     */
    private String lv4CategoryCode;

    /**
     * 产品小类名称
     */
    @MergeField(value = "sku", newline = 0, sort = 5, extra = "#ec7020", format = "-> %s")
    private String lv4CategoryName;

    private String categoryName;

    /**
     * rdc仓编码
     */
    private String rdcCode;

    /**
     * rdc仓名称
     */
    @MetaField(value = "仓库", primaryKey = true)
    private String rdcName;


    /**
     * 服务水平
     */
    @MetaField(value = "服务水平", sortable = true, orderColumn = "service_ratio")
    @ConvertField(extensible = DecimalToPercentConvertExt.class)
    private String serviceRatio;

    /**
     * 最小安全库存天数
     */
    @MetaField(value = "最小安全库存天数", sortable = true, orderColumn = "min_safety_days")
    private Integer minSafetyDays;

    /**
     * 最大安全库存天数
     */
    @MetaField(value = "最大安全库存天数", sortable = true, orderColumn = "max_safety_days")
    private Integer maxSafetyDays;

    /**
     * 操作人编码
     */
    private String operatorCode;

    /**
     * 操作人名称
     */
    @MetaField(value = "操作人")
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 修改时间
     */
    @ConvertField(extensible = LocalDateTimeToStringConvertExt.class)
    @MetaField(value = "最近更新时间", sortable = true, orderColumn = "gmt_modified")
    private String gmtModified;

}
