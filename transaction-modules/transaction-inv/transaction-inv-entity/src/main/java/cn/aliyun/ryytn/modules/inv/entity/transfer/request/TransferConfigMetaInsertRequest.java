package cn.aliyun.ryytn.modules.inv.entity.transfer.request;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025/5/21 11:35
 * @description：
 */
@Data
@ApiModel("调拨计划页面配置Meta保存请求体")
public class TransferConfigMetaInsertRequest {

    /**
     * '组别'
     */
    private String groupName;
    /**
     * '配置编码'
     */
    private String configCode;

    /**
     * 是否隐藏，false不隐藏，ture隐藏
     */
    private Boolean hidden;

    /**
     * 当前列名称
     */
    @ApiModelProperty("当前列名称")
    private String name;

    /**
     * 当前列code
     */
    @ApiModelProperty("当前列code")
    private String code;

    /**
     * 合并为行/合并为列
     * @see cn.aliyun.ryytn.modules.inv.constant.transfer.enums.MetaMergerEnum
     */
    @ApiModelProperty("合并")
    private String merge;

    /**
     * 合并名称
     */
    @ApiModelProperty("合并名称")
    private String mergerName;

    /**
     * 当前列别名
     */
    @ApiModelProperty("当前列别名")
    private String anotherName;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;


    private Collection<MetaInfo> children;

}
