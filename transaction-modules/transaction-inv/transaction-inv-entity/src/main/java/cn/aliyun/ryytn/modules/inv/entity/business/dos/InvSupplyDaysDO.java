package cn.aliyun.ryytn.modules.inv.entity.business.dos;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/24 10:24
 * @description：
 */
@Data
public class InvSupplyDaysDO {

    /**
     * 主键自增
     */
    private long id;
    /**
     * 工厂仓编码
     */
    private String cdcCode;
    /**
     * 工厂仓名称
     */
    private String cdcName;
    /**
     * 读取生产天数
     */
    private Integer supplyDays;
    /**
     * 操作人编码
     */
    private String operatorCode;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    /**
     * 状态 0未启用，1启用
     */
    private Integer status;
    /**
     * 更新日期
     */
    private Integer statDate;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
