package cn.aliyun.ryytn.modules.inv.entity.strategy.request;

import cn.aliyun.ryytn.modules.inv.common.dao.common.request.BasePageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 15:42
 * @description：
 */
@Data
@ApiModel("库存策略新增或编辑请求体")
public class InvUpsertStrategyRequest  extends BasePageQueryRequest {

    /**
     * 通过token解析id
     */
    @ApiModelProperty("通过token解析id")
    private String token;

    /**
     * id
     */
    @ApiModelProperty("库存策略Id")
    private Long id;

    /**
     * 补货类型
     */
    @ApiModelProperty("补货类型")
    private String replnType;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 维度
     */
    @ApiModelProperty("维度")
    private String dimension;

    /**
     * 品类
     */
    @ApiModelProperty("选中品类")
    private List<String> category;

    /**
     * 根据id备份，或者根据维度和类别备份
     */
    @ApiModelProperty("类型")
    private String type;


    /**
     * sku
     */
    @ApiModelProperty("品类筛选项")
    private List<String> categorySelect;
    /**
     * sku
     */
    @ApiModelProperty("sku筛选项")
    private List<String> sku;
    /**
     * RDC
     */
    @ApiModelProperty("rdc仓库筛选项")
    private List<String> rdc;

    private String skuStr;

    private String tag;

    private Integer status;
    /**
     * 已选中品仓
     */
    @ApiModelProperty("已选中品仓")
    private Integer coverCnt;
    /**
     * 保存并启用
     */
    @ApiModelProperty("保存并启用")
    private String enable;

    private Long strategyId;


    private String importFlag;

    private Integer selectMeta;

    /**
     * 是否全选
     */
    private Integer selectAll;

}
