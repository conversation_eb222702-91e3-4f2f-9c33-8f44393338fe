package cn.aliyun.ryytn.modules.inv.entity.strategy.vo;


import cn.aliyun.ryytn.modules.inv.common.ability.meta.BaseAction;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext.LocalDateTimeToYmdHmStringConvertExt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26 19:23
 * @description：库存策略中心VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("库存策略")
public class InvStrategyCenterVO extends BaseAction {
    /**
     * 主键id
     */
    @MetaField(value = "策略场景Id")
    @ApiModelProperty("库存策略场景Id")
    private Long id;
    /**
     * gmtCreate
     */
    @MetaField(value = "创建时间")
    @ApiModelProperty("创建时间")
    @ConvertField(extensible = LocalDateTimeToYmdHmStringConvertExt.class)
    private String gmtCreate;
    /**
     * gmtModified
     */
    @MetaField(value = "更新时间")
    @ApiModelProperty("更新时间")
    @ConvertField(extensible = LocalDateTimeToYmdHmStringConvertExt.class)
    private String gmtModified;
    /**
     * 场景名称
     */
    @MetaField(value = "策略场景名称", primaryKey = true)
    @ApiModelProperty("策略场景名称")
    private String name;
    /**
     * 补货类型
     */
    @MetaField(value = "补货类型编码")
    @ApiModelProperty("补货类型编码")
    private String replnType;
    /**
     * 补货类型名称
     */
    @MetaField(value = "补货类型名称")
    @ApiModelProperty("补货类型名称")
    private String replnTypeName;
    /**
     * 维度
     */
    @MetaField(value = "维度编码")
    @ApiModelProperty("维度编码")
    private String dimension;
    /**
     * 维度名称
     */
    @MetaField(value = "维度名称")
    @ApiModelProperty("维度名称")
    private String dimensionName;
    /**
     * 标签
     */
    @MetaField(value = "标签")
    @ApiModelProperty("标签")
    private List<String> tag;

    /**
     * sku
     */
    @MetaField(value = "sku")
    @ApiModelProperty("sku")
    private List<String> sku;
    /**
     * 覆盖品仓数量
     */
    @MetaField(value = "覆盖品仓数量")
    @ApiModelProperty("覆盖品仓数量")
    private Integer coverCnt;
    /**
     * 状态
     */
    @MetaField(value = "状态编码")
    @ApiModelProperty("状态编码")
    private Integer status;
    /**
     * 状态
     */
    @MetaField(value = "状态名称")
    @ApiModelProperty("状态名称")
    private String statusName;

    /**
     * 操作人编码
     */
    @MetaField(value = "操作人编码")
    @ApiModelProperty("操作人编码")
    private String operatorCode;

    /**
     * 操作人名称
     */
    @MetaField(value = "操作人名称")
    @ApiModelProperty("操作人名称")
    private String operatorName;

    /**
     * 操作时间
     */
    @MetaField(value = "操作时间")
    @ApiModelProperty("操作时间")
    @ConvertField(extensible = LocalDateTimeToYmdHmStringConvertExt.class)
    private String operationTime;

}
