package cn.aliyun.ryytn.modules.inv.entity.strategy.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/8 20:34
 * @description：
 */
@Data
public class StrategyResultsExceptionVO {

    private String value;

    private String color;

    private String type;

    private String tooltip;

    public StrategyResultsExceptionVO() {

    }

    public StrategyResultsExceptionVO(String value, String color, String type, String tooltip) {
        this.value = value;
        this.color = color;
        this.type = type;
        this.tooltip = tooltip;
    }

    public static StrategyResultsExceptionVO of(String value, String color, String type, String tooltip){
        return new StrategyResultsExceptionVO(value, color, type, tooltip);
    }

}
