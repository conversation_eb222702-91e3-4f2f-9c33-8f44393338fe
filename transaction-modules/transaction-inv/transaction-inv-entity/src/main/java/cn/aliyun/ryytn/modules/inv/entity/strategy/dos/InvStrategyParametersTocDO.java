package cn.aliyun.ryytn.modules.inv.entity.strategy.dos;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/28 18:34
 * @description：
 */
@Data
public class InvStrategyParametersTocDO {

    /**
     * id
     */
    private Long id;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
    /**
     * 库存策略Id
     */
    private Long invStrategyId;
    /**
     * ABC分类
     */
    private String abcType;
    /**
     * 产品分类编码
     */
    private String lv1CategoryCode;
    /**
     * 产品分类名称
     */
    private String lv1CategoryName;
    /**
     * 产品大类编码
     */
    private String lv2CategoryCode;
    /**
     * 产品大类名称
     */
    private String lv2CategoryName;
    /**
     * 产品小类编码
     */
    private String lv4CategoryCode;
    /**
     * 产品小类名称
     */
    private String lv4CategoryName;
    /**
     * SKU编码
     */
    private String skuCode;
    /**
     * SKU名称
     */
    private String skuName;
    /**
     * RDC仓编码
     */
    private String rdcCode;
    /**
     * RDC仓名称
     */
    private String rdcName;
    /**
     * 配置方式
     */
    private String collocationMethod;
    /**
     * 日均销
     */
    private Integer dailySalesAvg;
    /**
     * X1
     */
    private Integer x1;
    /**
     * X2
     */
    private Integer x2;
    /**
     * 安全库存
     */
    private Integer safety;
    /**
     * 目标库存
     */
    private Integer target;
    /**
     * 操作人编码
     */
    private String operatorCode;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    /**
     * 状态 (0:中间态，1:最终态)
     */
    private Integer status;

}
