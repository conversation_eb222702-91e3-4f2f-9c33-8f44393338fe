package cn.aliyun.ryytn.modules.inv.constant.strategy.error;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InvStrategyCenterError {
    PARAM_IS_NOT_NULL("请求参数不能为空！"),
    QUERY_DATA_IS_NOT_NULL("根据请求参数查询不到数据！"),
    STATUS_IS_NOT_NULL("状态不能为空！"),
    STRATEGY_ID_IS_NOT_NULL("库存策略Id不能为空！"),
    STRATEGY_NAME_IS_NOT_NULL("场景名称不能为空！"),
    STRATEGY_IS_NOT_EXISTENT("库存策略不存在！"),
    REQUEST_PARAM_ERROR("{}场景名称已存在，请修改场景名称"),
    COVER_CNT_IS_NOT_NULL("已选中品仓不能为空！"),
    TYPE_IS_NOT_EXISTENT("类型不存在！"),
    REPLN_TYPE_IS_NOT_NULL("补货方式不能为空！"),
    REPLN_TYPE_IS_NOT_EXISTENT("补货方式不存在！"),
    DIMENSION_IS_NOT_NULL("维度不能为空！"),
    DIMENSION_IS_NOT_EXISTENT("维度不存在！"),
    CATEGORY_IS_NOT_NULL("选择类别不能为空！"),
    STRATEGY_PARAMETERS_IS_NULL("库存策略参数不存在！"),
    DATA_IS_NULL("根据维度和类别未找到任何数据！"),
    STRATEGY_PARAMETERS_ID_IS_NULL("库存策略参数Id不能为空！"),
    X1_IS_NULL("X1不能为空！"),
    X2_IS_NULL("X2不能为空！"),
    X1_RANGE_ERROR("X1只允许输入0～99的整数！"),
    X2_RANGE_ERROR("X2只允许输入0～99的整数！"),
    X2_LESS_THEN_X1("X2必须>=X1！"),
    X1_TYPE_ERROR("X1数据格式错误！"),
    X2_TYPE_ERROR("X2数据格式错误！"),
    SAFETY_TYPE_ERROR("安全库存数据格式错误！"),
    TARGET_TYPE_ERROR("目标库存数据格式错误！"),
    SAFETY_IS_NULL("安全库存不能为空！"),
    TARGET_IS_NULL("目标库存不能为空！"),
    LIMIT_IS_NULL("极限水位不能为空！"),
    ROP_CNT_IS_NULL("再订货点不能为空！"),
    OOS_POINT_IS_NULL("缺货报警点不能为空！"),
    LIMIT_TYPE_ERROR("极限水位数据格式错误！"),
    ROP_CNT_TYPE_ERROR("再订货点数据格式错误！"),
    OOS_POINT_TYPE_ERROR("缺货报警点数据格式错误！"),
    SAFETY_RANGE_ERROR("安全库存只允许输入>=0的整数！"),
    TARGET_RANGE_ERROR("目标库存只允许输入>=0的整数！"),
    LIMIT_RANGE_ERROR("极限水位只允许输入>=0的整数！"),
    ROP_CNT_RANGE_ERROR("再订货点只允许输入>=0的整数！"),
    OOS_POINT_RANGE_ERROR("缺货报警点只允许输入>=0的整数！"),
    RANGE_ERROR("极限水位>=目标库存>=再订货点>=缺货报警点！"),
    COLLOCATION_METHOD_NOT_NULL("配置方式不能为空！"),
    RDC_NAME_IS_NOT_NULL("仓库名称不能为空！"),
    SKU_CODE_IS_NOT_NULL("SKU编码不能为空！"),
    LV4_IS_NOT_NULL("四级品类不能为空！"),
    LV2_IS_NOT_NULL("二级品类不能为空！"),
    LV1_IS_NOT_NULL("一级品类不能为空！"),
    ABC_IS_NOT_NULL("ABC分类不能为空！"),
    PARAMETERS_NOT_FOUND("不存在%s，请校验是否填写错误！"),


    ;

    private final String error;
}
