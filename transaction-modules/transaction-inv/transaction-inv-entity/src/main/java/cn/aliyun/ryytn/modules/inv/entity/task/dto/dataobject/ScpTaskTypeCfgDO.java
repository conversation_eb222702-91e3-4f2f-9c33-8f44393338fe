package cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-19 11:21
 * @description 任务类型配置
 */
@Data
@TableName("scp_task_type_cfg")
public class ScpTaskTypeCfgDO {
     /**
      * 主键
      */
     private Long id;

     /**
      * 创建时间
      */
     private LocalDateTime gmtCreate;

     /**
      * 最新更新时间
      */
     private LocalDateTime gmtModified;

     /**
      * '任务类型编码' | '任务类型名称'
      */
     private String taskTypeCode;
     private String taskTypeName;

     /**
      * '上游任务类型编码' | '上游任务类型名称'
      */
     private String preTaskTypeCode;
     private String preTaskTypeName;

     /**
      * '下游任务类型编码' | '下游任务类型名称'
      */
     private String nextTaskTypeCode;
     private String nextTaskTypeName;
}
