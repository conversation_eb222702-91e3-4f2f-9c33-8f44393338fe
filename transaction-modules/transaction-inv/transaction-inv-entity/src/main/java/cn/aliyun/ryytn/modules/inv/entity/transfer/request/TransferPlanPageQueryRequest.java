package cn.aliyun.ryytn.modules.inv.entity.transfer.request;

import cn.aliyun.ryytn.modules.inv.common.dao.common.request.BasePageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21 10:47
 * @description：
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("调拨计划列表查询请求体")
public class TransferPlanPageQueryRequest extends BasePageQueryRequest {

    /**
     * 生产编码
     */
    @ApiModelProperty("生产编码")
    private List<String> productionCodingList;

    /**
     * SKU
     */
    @ApiModelProperty("SKU")
    private List<String> skuList;

    /**
     * ABC分类
     */
    @ApiModelProperty("ABC分类")
    private List<String> abcType;

    /**
     * 调出物理仓
     */
    @ApiModelProperty("调出物理仓")
    private List<String> outPhysicalWarehouseNameList;

    /**
     * 调出逻辑仓
     */
    @ApiModelProperty("调出逻辑仓")
    private List<String> outLogicalWarehouseNameList;

    /**
     * 调入物理仓
     */
    @ApiModelProperty("调入物理仓")
    private List<String> inLogicalWarehouseNameList;

    /**
     * 调入逻辑仓
     */
    @ApiModelProperty("调入逻辑仓")
    private List<String> inPhysicalWarehouseNameList;

    /**
     * 效期规则
     */
    @ApiModelProperty("效期规则")
    private List<String> validityRuleList;

    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private List<String> modeOfTransportList;

    /**
     * 告警状态
     */
    @ApiModelProperty("告警状态")
    private List<String> alarmStatusList;

    private String replnTypeList;

}
