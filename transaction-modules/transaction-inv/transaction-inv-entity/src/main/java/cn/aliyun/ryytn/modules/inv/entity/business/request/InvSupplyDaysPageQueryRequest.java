package cn.aliyun.ryytn.modules.inv.entity.business.request;

import cn.aliyun.ryytn.modules.inv.common.dao.common.request.BasePageQueryRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/24 10:13
 * @description：
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InvSupplyDaysPageQueryRequest extends BasePageQueryRequest {

    /**
     * 工厂仓
     */
    private List<String> cdc;

    /**
     * 快速查询子句
     */
    private String quickQueryClause;
    private String quickQueryClauseValue;

}
