package cn.aliyun.ryytn.modules.inv.constant.strategy.enums;

import com.cainiao.cntech.dsct.scp.gei.biz.web.model.OptionVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 15:44
 * @description：
 */
@Getter
@AllArgsConstructor
public enum InvResultsExceptionErrorEnum {

    ALL("all", "全部", "", ""),
    NORMAL("normal", "正常", "#0066ff", "正常"),
    ABNORMAL("abnormal", "异常", "", "异常"),
    SAFETY_IS_NUL("safety", "安全库存为0", "#ff0000", "安全库存为0：请检查对应品仓最小安全库存或最大安全库存或算法产出的安全库存建议值。"),
    DAS_NUM_IS_NUL("dasNum", "日均销为0", "#ff0000", "日均销为0：请检查对应品仓算法产出的安全库存天数建议值。"),
    PARAM_IS_NULL("param", "库存策略参数为空", "#ff0000", "库存策略参数为空"),
    ;
    private final String code;
    private final String name;
    private final String color;
    private final String tooltip;

    public static String getNameByCode(String code){
        InvResultsExceptionErrorEnum dimensionEnum = getByCode(code);
        return dimensionEnum == null ? "" : dimensionEnum.name;
    }
    public static InvResultsExceptionErrorEnum getByCode(String code){
        if(StringUtils.isBlank(code)){ return null; }
        for (InvResultsExceptionErrorEnum dimensionEnum : values()) {
            if(StringUtils.equals(dimensionEnum.code, code)){ return dimensionEnum; }
        }
        return null;
    }

    public static List<OptionVO> getList() {
        List<OptionVO> list = new ArrayList<>();
        list.add(OptionVO.of(ALL.getCode(), ALL.getName()));
        list.add(OptionVO.of(NORMAL.getCode(), NORMAL.getName()));
        OptionVO exception = OptionVO.of(ABNORMAL.getCode(), ABNORMAL.getName());
        List<OptionVO> children = new ArrayList<>();
        children.add(OptionVO.of(SAFETY_IS_NUL.getCode(), SAFETY_IS_NUL.getName()));
        children.add(OptionVO.of(DAS_NUM_IS_NUL.getCode(), DAS_NUM_IS_NUL.getName()));
        children.add(OptionVO.of(PARAM_IS_NULL.getCode(), PARAM_IS_NULL.getName()));
        exception.setChildren(children);
        list.add(exception);
        return list;
    }



}
