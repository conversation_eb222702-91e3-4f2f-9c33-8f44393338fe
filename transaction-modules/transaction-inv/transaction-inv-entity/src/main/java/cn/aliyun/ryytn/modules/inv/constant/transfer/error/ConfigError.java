package cn.aliyun.ryytn.modules.inv.constant.transfer.error;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/20 14:28
 * @description：
 */
@Getter
@AllArgsConstructor
public enum ConfigError {
    SAVE_REQUEST_NOT_NULL("保存配置列表不允许为空！"),
    CONFIG_NOT_FOUND("配置编码「%s」未找到，请检查！"),
    CONFIG_VALUE_NOT_NULL("配置值不允许为空，配置编码「%s」请检查！"),
    ;
    private final String error;
}
