package cn.aliyun.ryytn.modules.inv.constant.business.error;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/9 13:50
 * @description: 全量推出供应源配置错误信息
 */
@Getter
@AllArgsConstructor
public enum InvSkuCdcFullSupplyError {

    IS_FULL_SUPPLY_IS_NOT_NULL("是否全量推出不允许为空"),
    CDC_CODE_SKU_CODE_QUERY_IS_NOT_NULL("根据工厂仓编码%s和SKU编码%s查不到数据"),
    CDC_CODE_QUERY_IS_NOT_NULL("根据工厂仓编码查询不到数据！"),
    SKU_CODE_QUERY_IS_NOT_NULL("根据SKU编码查询不到数据！"),
    QUERY_IS_NOT_NULL("查询不到数据！"),
    CDC_CODE_IS_NOT_NULL("工厂仓编码不允许为空！"),
    CDC_NAME_IS_NOT_NULL("工厂仓名称不允许为空！"),
    SKU_CODE_IS_NOT_NULL("SKU编码不允许为空！"),
    SKU_NAME_IS_NOT_NULL("SKU名称不允许为空！"),
    CDC_CODE_OR_SKU_CODE_IS_NOT_NULL("工厂仓编码不允许为空！"),
    IMPORT_CDC_CODE_QUERY_IS_NOT_NULL("不存在工厂仓编码: %s 工厂仓名称:%s，请校验是否填写错误！"),
    IMPORT_SKU_CODE_QUERY_IS_NOT_NULL("不存在SKU编码: %s SKU名称:%s，请校验是否填写错误！"),
    IMPORT_CDC_CODE_AND_SKU_CODE_QUERY_IS_NOT_NULL("不存在工厂仓编码: %s 工厂仓名称:%s，SKU编码: %s SKU名称:%s，请校验是否填写错误！"),
    IS_FULL_SUPPLY_IS_ERROR("是否全量推出的数据格式错误，请修正"),
    CDC_CODE_AND_SKU_CODE_REPEAT_IS_ERROR("存在重复的工厂仓*sku且是否全量推出为不同值，请校验是否填写错误"),
    NOT_UPDATE_IS_ERROR("暂无修改数据变化，请修改数据后导入"),

    ;

    private String error;
}
