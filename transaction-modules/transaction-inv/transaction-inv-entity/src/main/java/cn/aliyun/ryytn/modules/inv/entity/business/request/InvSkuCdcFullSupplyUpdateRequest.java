package cn.aliyun.ryytn.modules.inv.entity.business.request;

import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.BizPk;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.annotation.ModifyAfter;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.annotation.ModifyBefore;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/9 13:56
 * @description: 全量推出供应源配置更新
 */
@Data
public class InvSkuCdcFullSupplyUpdateRequest {

    @BizPk
    private String cdcCode;
    @BizPk
    private String cdcName;
    @BizPk
    private String skuCode;
    @BizPk
    private String skuName;

    private Integer isFullSupply;

    @ModifyAfter({"c{null:-}","f{是否全量推出：%s}"})
    private String isFullSupplyName;

    @ModifyBefore({"c{null:-}","f{是否全量推出：%s}"})
    private String isFullSupplyBeforeName;

    private String operatorCode;

    private String operatorName;
}
