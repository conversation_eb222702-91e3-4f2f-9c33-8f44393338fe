package cn.aliyun.ryytn.modules.inv.entity.business.logger;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaType;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeField;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.merge.MergeFieldWrapper;
import cn.aliyun.ryytn.modules.inv.common.utils.StringConstants;
import cn.aliyun.ryytn.modules.inv.entity.business.convert.InvSkuReplnTypeLogSkuCodeConvertExt;
import cn.aliyun.ryytn.modules.inv.entity.business.convert.InvSkuReplnTypeLogSkuNameConvertExt;
import cn.aliyun.ryytn.modules.inv.entity.business.convert.InvStrategyResultsLogSkuCodeConvertExt;
import cn.aliyun.ryytn.modules.inv.entity.business.convert.InvStrategyResultsLogSkuNameConvertExt;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-03-07 15:48
 * @description
 */
@Data
public class InvStrategyReaultsLogMetaInfo {
    /**
     * '业务编码'
     */
    private String bizCode;
    /**
     * 业务主键
     */
    private String bizPk;

    @MetaField(value = "SKU信息")
    List<MergeFieldWrapper> sku;
//    @MetaField("SKU编码")
    @MergeField(value = "sku", newline = 1, sort = 0, metaType = MetaType.CODE)
    @ConvertField(extensible = InvStrategyResultsLogSkuCodeConvertExt.class)
    private String skuCode;
//    @MetaField("SKU名称")
    @MergeField(value = "sku", newline = 0, sort = 1, metaType = MetaType.NAME)
    @ConvertField(extensible = InvStrategyResultsLogSkuNameConvertExt.class)
    private String skuName;
    @MetaField("仓库")
    private String cdcName;

    /**
     * 修改前的值
     */
    @MetaField("变更前")
    private List<String> beforeModify;
    /**
     * 修改后的值
     */
    @MetaField("变更后")
    private List<String> afterModify;
    /**
     * 修改原因
     */
    private List<String> modifyReason;

    private Long version;
    /**
     * 操作人
     */
    private String operatorCode;

    @MetaField("变更人")
    private String operatorName;
    /**
     * 变更时间
     */
    @MetaField(value = "变更时间", sortable = true, orderColumn = "gmt_modified")
    private String modifyTime;

    public String getBizPk(){
        if (Objects.nonNull(this.bizPk)) {
            String[] split = this.bizPk.split(String.valueOf(StringConstants.SEMICOLON));
            if (split.length == 3) {
                setSkuCode(split[0]);
                setSkuName(split[1]);
                setCdcName(split[2]);
            }
        }
        return this.bizPk;
    }

}
