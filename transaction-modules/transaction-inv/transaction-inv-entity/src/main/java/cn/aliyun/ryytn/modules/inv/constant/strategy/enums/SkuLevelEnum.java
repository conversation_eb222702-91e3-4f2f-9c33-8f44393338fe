package cn.aliyun.ryytn.modules.inv.constant.strategy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25 17:46
 * @description：SKU层级Enum
 */
@Getter
@AllArgsConstructor
public enum SkuLevelEnum {

    LEVEL_A("A", "A类","#EA6A12","#FDF0E8"),
    LEVEL_B("B", "B类","#EA6A12","#FDF0E8"),
    LEVEL_C("C", "C类","#EA6A12","#FDF0E8"),
    ;

    private final String code;
    private final String name;
    private String fontColor;
    private String backgroudColor;
    public static String getNameByCode(String code){
        SkuLevelEnum skuLevelEnum = getByCode(code);
        return skuLevelEnum == null ? "" : skuLevelEnum.name;
    }

    public static String getCodeByName(String name){
        SkuLevelEnum stockStrategyDimensionEnum = getByName(name);
        return stockStrategyDimensionEnum == null ? "" : stockStrategyDimensionEnum.code;
    }

    public static SkuLevelEnum getByName(String name){
        if(StringUtils.isBlank(name)){ return null; }
        for (SkuLevelEnum stockStrategyDimensionEnum : values()) {
            if(StringUtils.equals(stockStrategyDimensionEnum.name, name)){ return stockStrategyDimensionEnum; }
        }
        return null;
    }
    public static SkuLevelEnum getByCode(String code){
        if(StringUtils.isBlank(code)){ return null; }
        for (SkuLevelEnum skuLevelEnum : values()) {
            if(StringUtils.equals(skuLevelEnum.code, code)){ return skuLevelEnum; }
        }
        return null;
    }

    public boolean isThis(String code){
        return StringUtils.equalsIgnoreCase(code, getCode());
    }

    public static List<SkuLevelEnum> sortedValues(){
        List<SkuLevelEnum> list = Arrays.asList(values());
        list.sort(Comparator.comparing(SkuLevelEnum::getName));
        return list;
    }

}
