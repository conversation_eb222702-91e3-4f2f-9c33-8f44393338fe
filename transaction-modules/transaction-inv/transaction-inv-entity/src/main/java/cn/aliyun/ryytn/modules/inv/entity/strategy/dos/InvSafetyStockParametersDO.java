package cn.aliyun.ryytn.modules.inv.entity.strategy.dos;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/6 11:47
 * @description：
 */
@Data
public class InvSafetyStockParametersDO {
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * ABC分类
     */
    private String abcType;

    /**
     * 产品分类编码
     */
    private String lv1CategoryCode;

    /**
     * 产品分类名称
     */
    private String lv1CategoryName;

    /**
     * 产品大类编码
     */
    private String lv2CategoryCode;

    /**
     * 产品大类名称
     */
    private String lv2CategoryName;

    /**
     * 产品小类编码
     */
    private String lv4CategoryCode;

    /**
     * 产品小类名称
     */
    private String lv4CategoryName;

    /**
     * rdc仓编码
     */
    private String rdcCode;

    /**
     * rdc仓名称
     */
    private String rdcName;

    /**
     * 服务水平
     */
    private BigDecimal serviceRatio;

    /**
     * 最小安全库存天数
     */
    private Integer minSafetyDays;

    /**
     * 最大安全库存天数
     */
    private Integer maxSafetyDays;

    /**
     * 操作人编码
     */
    private String operatorCode;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

}
