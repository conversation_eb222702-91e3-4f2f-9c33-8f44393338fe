package cn.aliyun.ryytn.modules.inv.constant.strategy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/24 18:49
 * @description：
 */
@Getter
@AllArgsConstructor
public enum InvDimensionEnum {

    BY_ABC("byAbc", "按ABC分类", 1),
    BY_ONE("byOne", "按一级分类", 2),
    BY_TWO("byTwo", "按二级分类", 3),
    BY_FOUR("byFour", "按四级分类", 4),
    BY_SKU("bySku", "按产品", 5),
    ;
    private final String code;
    private final String name;
    private final Integer sort;

    public static String getNameByCode(String code){
        InvDimensionEnum dimensionEnum = getByCode(code);
        return dimensionEnum == null ? "" : dimensionEnum.name;
    }
    public static InvDimensionEnum getByCode(String code){
        if(StringUtils.isBlank(code)){ return null; }
        for (InvDimensionEnum dimensionEnum : values()) {
            if(StringUtils.equals(dimensionEnum.code, code)){ return dimensionEnum; }
        }
        return null;
    }

    public static List<InvDimensionEnum> sortedValues(){
        List<InvDimensionEnum> list = Arrays.asList(values());
        list.sort(Comparator.comparing(InvDimensionEnum::getSort));
        return list;
    }

    /**
     * 获取比当前维度小的维度code
     * @param code
     * @return
     */
    public static String getLessThanList (String code) {
        if(StringUtils.isBlank(code)){ return null; }
        InvDimensionEnum byCode = getByCode(code);
        for (InvDimensionEnum dimensionEnum : values()) {
            if (dimensionEnum.getSort() == byCode.getSort() - 1) {
                return dimensionEnum.code;
            }
        }
        return null;
    }

    /**
     * 获取比当前维度大的维度code
     * @param code
     * @return
     */
    public static List<String> getGreaterThanList (String code) {
        if(StringUtils.isBlank(code)){ return null; }
        InvDimensionEnum byCode = getByCode(code);
        List<String> codeList = new ArrayList<>();
        for (InvDimensionEnum dimensionEnum : values()) {
            if (dimensionEnum.getSort() > byCode.getSort()) {
                codeList.add(dimensionEnum.getCode());
            }
        }
        return codeList;
    }

    public static List<String> codeValues(){
        List<String> codeValues = new ArrayList<>();
        for (InvDimensionEnum invDimensionEnum : values()) {
            codeValues.add(invDimensionEnum.getCode());
        }
        return codeValues;
    }

}
