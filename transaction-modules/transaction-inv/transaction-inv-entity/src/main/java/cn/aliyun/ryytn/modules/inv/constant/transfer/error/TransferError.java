package cn.aliyun.ryytn.modules.inv.constant.transfer.error;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/20 14:28
 * @description：
 */
@Getter
@AllArgsConstructor
public enum TransferError {
    REQUEST_NOT_NULL("请求参数不允许为空！"),
    CONFIGLIST_NOT_NULL("请求配置类不允许为空！"),
    OPERATOR_NOT_NULL("查询不到当前操作人！"),
    MEAT_IS_NULL("meta信息查询为空！"),
    MERGER_NAME_IS_NOT_NULL("合并名称不能为空"),
    ANOTHER_NAME_IS_NOT_NULL("当前列别名不能为空"),
    TRANS_FER_PLAN_IS_NULL("页面配置为空，请联系管理员"),
    TRANSFER_CONFIG_NOT_NULL("视图配置不能为空"),
    DIMENSION_NOT_NULL("dimension不能为空"),
    DIMENSION_config_Value_NOT_NULL("dimension里configValue不能为空"),

    ;
    private final String error;
}
