package cn.aliyun.ryytn.modules.inv.entity.md.dos;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/25 13:56
 * @description：RDC仓DO
 */
@Data
public class InvRdcWarehouseDO {

    private String rdcCode;
    private String rdcName;
    private String warehouseTypeCode;
    private String warehouseTypeName;
    private String lv1TypeCode;
    private String lv1TypeName;
    private String lv2TypeCode;
    private String lv2TypeName;
    private String factoryCode;
    private String factoryName;
    private String isOwn;
    private String addrCode;
    private String addrName;
    private String longitude;
    private String latitude;
    private String bizWarehouseCode;
    private String bizWarehouseName;
    private String status;
    private String provinceCode;
    private String provinceName;
    private String cityCode;
    private String cityName;
    private String countyCode;
    private String countyName;
    private String gmtCreate;
    private String gmtModified;
    private String ds;

}
