package cn.aliyun.ryytn.modules.inv.entity.task.dto.dataobject;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/11/13 10:25
 * @description：任务管理DO
 */
@Data
public class ScpTaskManagementDO {

    /**
     * 预测模型Id
     */
    private Long modelCode;

    /**
     * 预测模型名称
     */
    private String modelName;

    /**
     * 所属场景
     */
    private String scene;

    /**
     * 定时触发时间参数
     */
    private String schedulingTimeParam;


    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 最新更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 任务Id
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 影响范围
     */
    private String impactRange;

    /**
     * 状态
     */
    private String status;

    /**
     * 开始时间
     */
    private LocalDateTime triggerStartTime;

    /**
     * 结束时间
     */
    private LocalDateTime triggerEndTime;

    /**
     * 调度方式
     */
    private String schedulingType;

    /**
     * 定时调度时间
     */
    private LocalDateTime schedulingTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 报错信息
     */
    private String errorMsg;

    /**
     * 操作人编码
     */
    private String operatorCode;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

}
