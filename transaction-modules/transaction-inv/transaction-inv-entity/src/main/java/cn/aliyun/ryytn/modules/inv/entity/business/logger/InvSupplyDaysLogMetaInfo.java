package cn.aliyun.ryytn.modules.inv.entity.business.logger;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaField;
import cn.aliyun.ryytn.modules.inv.common.utils.StringConstants;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-03-07 15:48
 * @description
 */
@Data
public class InvSupplyDaysLogMetaInfo {
    /**
     * '业务编码'
     */
    private String bizCode;
    /**
     * 业务主键
     */
    private String bizPk;
    @MetaField("工厂仓编码")
    private String cdcCode;
    @MetaField("工厂仓名称")
    private String cdcName;

    /**
     * 修改前的值
     */
    @MetaField("变更前")
    private List<String> beforeModify;
    /**
     * 修改后的值
     */
    @MetaField("变更后")
    private List<String> afterModify;
    /**
     * 修改原因
     */
    private List<String> modifyReason;

    private Long version;
    /**
     * 操作人
     */
    private String operatorCode;

    @MetaField("变更人")
    private String operatorName;
    /**
     * 变更时间
     */
    @MetaField(value = "变更时间", sortable = true, orderColumn = "gmt_modified")
    private String modifyTime;

    public String getBizPk(){
        if (Objects.nonNull(this.bizPk)) {
            String[] split = this.bizPk.split(String.valueOf(StringConstants.SEMICOLON));
            if (split.length == 2) {
                setCdcCode(split[0]);
                setCdcName(split[1]);
            }
        }
        return this.bizPk;
    }

}
