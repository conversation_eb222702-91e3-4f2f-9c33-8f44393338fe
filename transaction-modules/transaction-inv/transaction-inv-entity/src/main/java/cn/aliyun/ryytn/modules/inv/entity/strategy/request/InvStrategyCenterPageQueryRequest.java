package cn.aliyun.ryytn.modules.inv.entity.strategy.request;

import cn.aliyun.ryytn.modules.inv.common.dao.common.request.BasePageQueryRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26 19:23
 * @description：库存策略中心列表查询request
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InvStrategyCenterPageQueryRequest extends BasePageQueryRequest {

    /**
     * 维度
     */
    private String dimension;

    /**
     * 策略场景
     */
    private List<String> strategyName;

    /**
     * 补货类型
     */
    private List<String> replnType;

    /**
     * 状态
     */
    private List<Integer> status;

}
