package cn.aliyun.ryytn.modules.inv.constant.business.error;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum InvSkuReplnTypeError {
    LOGIN_IS_NOT_NULL("操作人信息不能为空"),
    PARAM_IS_NOT_NULL("请求参数不能为空！"),
    SKU_CODE_IS_NOT_NULL("sku编码不能为空！"),
    SKU_NAME_IS_NOT_NULL("sku名称不能为空！"),
    REPLN_TYPE_IS_NOT_NULL("补货方式不允许为空"),
    SKU_CODE_QUERY_IS_NOT_NULL("不存在SKU编码: %s SKU名称:%s,请校验是否填写错误"),
    REPLN_TYPE_IS_ERROR("补货方式的数据格式错误，请修正"),
    NOT_UPDATE_IS_ERROR("暂无修改数据变化，请修改数据后导入"),


    ;

    private String error;
}
