package cn.aliyun.ryytn.modules.inv.entity.business.dos;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/25 13:56
 * @description：
 */
@Data
public class InvSkuReplnTypeDO {

    /**
     * sku编码
     */
    private String skuCode;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * abc类型
     */
    private String abcType;
    /**
     * abc名称
     */
    private String abcName;
    /**
     * 补货方式
     */
    private String replnType;
    /**
     * 一级品类编码
     */
    private String lv1CategoryCode;
    /**
     * 一级品类名称
     */
    private String lv1CategoryName;
    /**
     * 二级品类编码
     */
    private String lv2CategoryCode;
    /**
     * 二级品类名称
     */
    private String lv2CategoryName;
    /**
     * 四级品类编码
     */
    private String lv4CategoryCode;
    /**
     * 四级品类名称
     */
    private String lv4CategoryName;
    /**
     * 操作人编码
     */
    private String operatorCode;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    /**
     * 状态 0未启用 1启用
     */
    private Integer status;
    /**
     * 更新日期
     */
    private Integer statDate;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
