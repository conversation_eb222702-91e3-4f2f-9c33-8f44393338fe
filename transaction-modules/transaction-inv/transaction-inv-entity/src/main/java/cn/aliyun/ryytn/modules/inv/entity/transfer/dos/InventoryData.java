package cn.aliyun.ryytn.modules.inv.entity.transfer.dos;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2025/5/23 19:17
 */
import lombok.*;


/**
 * 库存数据实体类。
 */

@Data
public class InventoryData {

    private String skuCode; // SKU 编码

    private String productionCoding; // 生产编码

    private String skuName; // 商品名称

    private String outPhysicalWarehouseName; // 调出物理仓

    private String outLogicalWarehouseName; // 调出逻辑仓

    private String inLogicalWarehouseName; // 调入物理仓

    private String inPhysicalWarehouseName; // 调入逻辑仓

    private String validityRule; // 有效期规则

    private String modeOfTransport; // 运输方式

    private String alarmStatus; // 预警状态

    private Double skuNationalInvQty; // 全国库存量

    private Double skuNationalAvgDailySalesQty; // 全国日均销量

    private Double skuNationalSupplyQty; // 全国供应量

    private Double skuNationalAvgDays; // 全国平均天数

    private String skuReplnType; // 补货类型

    private Double skuCdcInWhInvQty; // CDC 入库库存数量

    private Double skuCdcWhInvQty; // CDC 仓库库存数量

    private Double skuCdcTransitInvQty; // CDC 在途库存数量

    private Double skuCdcProductionQty; // CDC 生产数量

    private Double skuCdcSupplyQty; // CDC 供应数量

    private Double skuCdcFullSupply; // CDC 完整供应量

    private Double skuRdcInWhInvQty; // RDC 入库库存数量

    private Double skuRdcWhInvQty; // RDC 仓库库存数量

    private Double skuRdcTransitInvQty; // RDC 在途库存数量

    private Double skuRdcInvQty; // RDC 总库存数量

    private Double skuRdcInvDays; // RDC 库存天数

    private Integer skuRdcDasQty; // RDC 日均销售数量

    private Integer skuRdcMinSafetyDays; // RDC 最低安全天数

    private Integer skuRdcMaxSafetyDays; // RDC 最高安全天数

    private Double skuRdcServiceRatio; // RDC 服务率

    private Integer skuRdcParamX1Days; // RDC 参数 X1 天数

    private Double skuRdcParamX1Qty; // RDC 参数 X1 数量

    private Integer skuRdcParamX2Days; // RDC 参数 X2 天数

    private Double skuRdcParamX2Qty; // RDC 参数 X2 数量

    private Double skuRdcParamX1GapQty; // RDC 参数 X1 差值数量

    private Double skuRdcParamX2GapQty; // RDC 参数 X2 差值数量

    private Double skuRdcSafetyGapQty; // RDC 安全差值数量

    private Double skuRdcTargetGapQty; // RDC 目标差值数量

    private Integer skuRdcParamLimitWaterLevelDays; // RDC 水位限制天数

    private Double skuRdcParamLimitWaterLevelQty; // RDC 水位限制数量

    private Integer skuRdcParamTargetInvDays; // RDC 目标库存天数

    private Double skuRdcParamTargetInvQty; // RDC 目标库存数量

    private Integer skuRdcParamRopCntDays; // RDC 补货点天数

    private Double skuRdcParamRopCntQty; // RDC 补货点数量

    private Integer skuRdcParamOosAlarmPointDays; // RDC 缺货预警天数

    private Double skuRdcParamOosAlarmPointQty; // RDC 缺货预警数量

    private Double skuRdcParamLimitWaterLevelGapQty; // RDC 水位限制差值数量

    private Double skuRdcParamTargetInvGapQty; // RDC 目标库存差值数量

    private Double skuRdcParamRopCntGapQty; // RDC 补货点差值数量

    private Double skuRdcParamOosAlarmPointGapQty; // RDC 缺货预警差值数量

    private Integer skuRdcSugSafetyDays; // RDC 建议安全天数

    private Double skuRdcSugSafetyQty; // RDC 建议安全数量

    private Integer skuRdcSugTargetDays; // RDC 建议目标天数

    private Double skuRdcSugTargetQty; // RDC 建议目标数量

    private Integer skuRdcManualSafetyDays; // RDC 手动安全天数

    private Double skuRdcManualSafetyQty; // RDC 手动安全数量

    private Integer skuRdcManualTargetDays; // RDC 手动目标天数

    private Double skuRdcManualTargetQty; // RDC 手动目标数量

    private Integer skuRdcDistSafetyDays; // RDC 分销安全天数

    private Double skuRdcDistSafetyQty; // RDC 分销安全数量

    private Integer skuRdcDistTargetDays; // RDC 分销目标天数

    private Double skuRdcDistTargetQty; // RDC 分销目标数量

    private Integer cdcSupplyDays; // CDC 供应天数

    private Integer rdcPeriodInAdvance; // RDC 提前周期

    private String cdcRdcSupplyPriorityConfig; // CDC 与 RDC 供应优先级配置

    private Integer transferQty;
}
