package cn.aliyun.ryytn.modules.inv.entity.business.request;

import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.BizPk;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.annotation.ModifyAfter;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.annotation.ModifyBefore;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/27 19:07
 * @description：
 */
@Data
public class InvSkuReplnTypeUpdateRequest {

    @BizPk
    private String skuCode;
    @BizPk
    private String skuName;

    private String replnType;

    private String replnTypeBefore;

    @ModifyAfter({"c{null:-}","f{补货方式：%s}"})
    private String replnTypeName;

    @ModifyBefore({"c{null:-}","f{补货方式：%s}"})
    private String replnTypeBeforeName;

    private String operatorCode;

    private String operatorName;

}
