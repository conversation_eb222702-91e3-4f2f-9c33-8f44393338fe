package cn.aliyun.ryytn.modules.inv.entity.strategy.request;

import cn.aliyun.ryytn.modules.inv.common.ability.logger.annotation.BizPk;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.annotation.ModifyAfter;
import cn.aliyun.ryytn.modules.inv.common.ability.logger.manager.support.modify.annotation.ModifyBefore;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/6 14:53
 * @description：
 */
@Data
@ApiOperation("安全库存参数编辑请求体")
public class InvSafetyStockUpdateRequest {

    /**
     * sku编码
     */
    @BizPk
    @ApiModelProperty("sku编码")
    private String skuCode;

    /**
     * sku名称
     */
    @BizPk
    private String skuName;

    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String rdcCode;

    /**
     * 仓库名称
     */
    @BizPk
    private String rdcName;

    /**
     * 服务水平
     */
    @ApiModelProperty("服务水平")
    private BigDecimal serviceRatio;


    /**
     * 服务水平
     */
    @ModifyAfter({"c{null:-}", "f{服务水平：%s}"})
    private String serviceRatioStr;

    /**
     * 最小安全库存天数
     */
    @ModifyAfter({"c{null:-}", "f{最小安全库存天数：%s}"})
    @ApiModelProperty("最小安全库存天数")
    private Integer minSafetyDays;

    /**
     * 最大安全库存天数
     */
    @ModifyAfter({"c{null:-}", "f{最大安全库存天数：%s}"})
    @ApiModelProperty("最大安全库存天数")
    private Integer maxSafetyDays;

    /**
     * 服务水平
     */
    private BigDecimal serviceRatioBefore;

    /**
     * 服务水平
     */
    @ModifyBefore({"c{null:-}", "f{服务水平：%s}"})
    private String serviceRatioBeforeStr;

    /**
     * 最小安全库存天数
     */
    @ModifyBefore({"c{null:-}", "f{最小安全库存天数：%s}"})
    private Integer minSafetyDaysBefore;

    /**
     * 最大安全库存天数
     */
    @ModifyBefore({"c{null:-}", "f{最大安全库存天数：%s}"})
    private Integer maxSafetyDaysBefore;

    /**
     * 操作人编码
     */
    public String operatorCode;
    /**
     * 操作人名称
     */
    public String operatorName;


}
