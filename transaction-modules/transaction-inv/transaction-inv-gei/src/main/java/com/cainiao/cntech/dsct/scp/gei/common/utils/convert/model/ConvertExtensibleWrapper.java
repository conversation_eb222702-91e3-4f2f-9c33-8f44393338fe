package com.cainiao.cntech.dsct.scp.gei.common.utils.convert.model;

import lombok.Getter;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date 2023-08-23 09:16:51
 * @description: 转换扩展的包装
 */
@Getter
public final class ConvertExtensibleWrapper {

    private ConvertExtensibleWrapper() {
    }

    private ConvertExtensibleWrapper(Object sourceObject, Field sourceField, Class<?> sourceFieldType, Object targetObject, Class<?> targetFieldType, String targetFieldName) {
        this.sourceObject = sourceObject;
        this.sourceField = sourceField;
        this.sourceFieldType = sourceFieldType;
        this.targetObject = targetObject;
        this.targetFieldType = targetFieldType;
        this.targetFieldName = targetFieldName;
    }

    // 转换的源实体对象
    private Object sourceObject;

    // 当前正在转换的源实体对象属性
    private Field sourceField;

    // 当前正在转换的源实体对象属性类型
    private Class<?> sourceFieldType;


    // 转换的目标实体对象
    private Object targetObject;

    // 当前正在转换的目标实体对象属性类型
    private Class<?> targetFieldType;

    // 当前正在转换的目标实体对象属性名称
    private String targetFieldName;

    public static ConvertExtensibleWrapper build(Object sourceObject, Field sourceField, Object targetObject, Class<?> targetFieldType, String targetFieldName) {
        return new ConvertExtensibleWrapper(sourceObject, sourceField, sourceField == null ? null : sourceField.getType(), targetObject, targetFieldType, targetFieldName);
    }
}
