package com.cainiao.cntech.dsct.scp.gei.common.utils.convert;

import com.alibaba.fastjson.JSONObject;
import com.cainiao.cntech.dsct.scp.gei.common.utils.ReflectionUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.ConvertField;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation.CopyField;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext.ConvertProcessExtensible;
import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.model.ConvertExtensibleWrapper;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.*;
import java.util.*;
import java.util.stream.Stream;

public class GeiCommonConvert {

    private static final Map<Class<?>, Class<?>> TYPE_MAPPING;

    static {
        TYPE_MAPPING = new HashMap<>(14);
        TYPE_MAPPING.put(char.class, Character.class);
        TYPE_MAPPING.put(Character.class, char.class);

        TYPE_MAPPING.put(byte.class, Byte.class);
        TYPE_MAPPING.put(Byte.class, byte.class);

        TYPE_MAPPING.put(short.class, Short.class);
        TYPE_MAPPING.put(Short.class, short.class);

        TYPE_MAPPING.put(int.class, Integer.class);
        TYPE_MAPPING.put(Integer.class, int.class);

        TYPE_MAPPING.put(long.class, Long.class);
        TYPE_MAPPING.put(Long.class, long.class);

        TYPE_MAPPING.put(float.class, Float.class);
        TYPE_MAPPING.put(Float.class, float.class);

        TYPE_MAPPING.put(double.class, Double.class);
        TYPE_MAPPING.put(Double.class, double.class);
    }

    private static final InheritableThreadLocal<Map<String, ConvertProcessExtensible<?>>> EXT_CACHE = new InheritableThreadLocal<>();

    private static <T, R> void deepConvert(T t, Class<?> tClass, R r, Class<R> rClazz) {
        if (rClazz == Object.class) {
            return;
        }
        deepConvert(t, tClass, r, rClazz.getSuperclass());
        for (Field rfield : rClazz.getDeclaredFields()) {
            if (isIgnore(rfield)) {
                continue;
            }
            String fieldName = rfield.getName();
            boolean isDeep = false;
            Class<? extends ConvertProcessExtensible<?>>[] ext = null;
            if (rfield.isAnnotationPresent(ConvertField.class)) {
                ConvertField convertField = rfield.getAnnotation(ConvertField.class);
                if (StringUtils.isNotBlank(convertField.value())) {
                    Field field = ReflectionUtils.findField(tClass, convertField.value());
                    if (field != null) {
                        fieldName = convertField.value();
                    }
                }
                isDeep = convertField.deep();
                ext = convertField.extensible();
            }
            try {
                Field tMappingField = ReflectionUtils.findField(tClass, fieldName);
                // 找到目标属性
                if (tMappingField != null) {
                    rfield.setAccessible(true);
                    tMappingField.setAccessible(true);
                    Object o;
                    // 类型相同直接获取值
                    Class<?> newType = rfield.getType();
                    Class<?> rawTargetType = tMappingField.getType();

                    // 执行自定义扩展， 非null时作为源属性值对目标属性赋值操作
                    Object extensible = extensible(t, tMappingField, r, rfield, ext);
                    if (extensible != null) {
                        o = extensible;
                    } else if (checkTypeIsSame(rawTargetType, newType)) {
                        o = tMappingField.get(t);
                        if (isDeep) {
                            Type genericType = rfield.getGenericType();
                            if (o instanceof Collection && genericType instanceof ParameterizedType) {
                                Class<?> accountPrincipalApproveClazz = (Class<?>) ((ParameterizedType) genericType).getActualTypeArguments()[0];
                                o = convert((Collection) o, accountPrincipalApproveClazz);
                            }
                        }
                    } else {
                        // 如果允许深层转换 则尝试递归深层转换
                        o = deepConvertIfNeed(tMappingField, rfield, t, isDeep);
                    }
                    if (o != null) {
                        rfield.set(r, o);
                    }
                } else {
                    Class<?> newType = rfield.getType();
                    // 执行自定义扩展， 非null时作为源属性值对目标属性赋值操作
                    Object extensible = extensible(t, null, r, rfield, ext);
                    if (newType.isInstance(extensible)) {
                        rfield.setAccessible(true);
                        rfield.set(r, extensible);
                    }
                }
            } catch (Exception ignored) {
            }
        }
    }

    public static <R> R convert(Map<String, Object> source, Class<R> clazz, Object... args) {
        if (source == null) {
            return null;
        }
        R r = newInstance(clazz, args);
        for (Map.Entry<String, Object> dateValue : source.entrySet()) {
            String key = dateValue.getKey();
            try {
                Field field = clazz.getDeclaredField(key);
                field.setAccessible(true);
                field.set(r, dateValue.getValue());
            } catch (NoSuchFieldException | IllegalAccessException ignored) {
            }
        }
        return r;
    }


    public static <T, R> R convert(T t, Class<R> rClazz, Object... args) {
        try {
            if (t == null) {
                return null;
            }
            Class<?> tClass = t.getClass();
            R resultObject = newInstance(rClazz, args);
            deepConvert(t, tClass, resultObject, rClazz);
            return resultObject;
        } finally {
            EXT_CACHE.remove();
        }
    }

    public static <T, R> List<R> convert(Collection<T> t, Class<R> rClazz, Object... args) {
        if (t == null) {
            return Collections.emptyList();
        }
        return new ArrayList<>(Arrays.asList(convert(t.toArray(), rClazz, args)));
    }

    public static <T, R> R[] convert(T[] t, Class<R> rClazz, Object... args) {
        int len = t != null ? t.length : 0;
        Object result = Array.newInstance(rClazz, len);
        if (t != null) {
            for (int i = 0; i < t.length; i++) {
                Array.set(result, i, GeiCommonConvert.convert(t[i], rClazz, args));
            }
        }
        return (R[]) result;
    }

    public static <T, R> R convertType(T t, Class<R> rClazz) {
        return rClazz.cast(t);
    }

    public static <R> R json2Object(String json, Class<R> clazz) {
        return JSONObject.parseObject(json, clazz);
    }

    /**
     * 检查类型是否相同
     *
     * @param sourceType 源属性类型
     * @param targetType 目标属性类型
     */
    private static boolean checkTypeIsSame(Class<?> sourceType, Class<?> targetType) {
        if (sourceType.equals(targetType)) {
            return true;
        }
        Class<?> mapping = TYPE_MAPPING.get(sourceType);
        return mapping != null && mapping.equals(targetType);
    }

    // 扩展
    private static <T, R> Object extensible(T t, Field tField, R r, Field rfield, Class<? extends ConvertProcessExtensible<?>>[] extensible) {
        if (Objects.nonNull(extensible)) {
            for (Class<? extends ConvertProcessExtensible<?>> aClass : extensible) {
                try {
                    Map<String, ConvertProcessExtensible<?>> extMap = EXT_CACHE.get();
                    ConvertProcessExtensible<?> ext;
                    if (extMap == null) {
                        extMap = new HashMap<>();
                        EXT_CACHE.set(extMap);
                    }
                    ext = extMap.get(aClass.getName());
                    if (ext == null) {
                        ext = aClass.getDeclaredConstructor().newInstance();
                        extMap.put(aClass.getName(), ext);
                    }
                    Object replaceValue = ext.doConvert(ConvertExtensibleWrapper.build(t, tField, r, rfield.getType(), rfield.getName()));
                    if (replaceValue != null) {
                        return replaceValue;
                    }
                } catch (InstantiationException | IllegalAccessException | NoSuchMethodException |
                         InvocationTargetException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    private static boolean isIgnore(Field field) {
        return Modifier.isFinal(field.getModifiers()) || Modifier.isStatic(field.getModifiers());
    }

    private static <R> R newInstance(Class<R> rClazz, Object... args) {
        try {
            Constructor<R> constructor;
            if (args != null && args.length > 0) {
                constructor = rClazz.getDeclaredConstructor(Stream.of(args).map(Object::getClass).toArray(Class[]::new));
            } else {
                constructor = rClazz.getDeclaredConstructor();
            }
            constructor.setAccessible(true);
            return constructor.newInstance(args);
        } catch (NoSuchMethodException | InvocationTargetException | InstantiationException |
                 IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T copyAll(T t, Object... args) {
        return doCopy(t, true, args);
    }

    private static <T> T doCopy(T t, boolean isAll, Object... args) {
        try {
            Class<?> tClass = t.getClass();
            T resultObject = (T) newInstance(tClass, args);

            while (tClass != null && tClass != Object.class) {
                for (Field field : tClass.getDeclaredFields()) {
                    if (isIgnore(field)) {
                        continue;
                    }
                    if (isAll || field.isAnnotationPresent(CopyField.class)) {
                        String fieldName = field.getName();
                        try {
                            Field tMappingField = tClass.getDeclaredField(fieldName);
                            if (tMappingField.getType().equals(field.getType())) {
                                field.setAccessible(true);
                                tMappingField.setAccessible(true);
                                Object o = tMappingField.get(t);
                                if (o != null) {
                                    field.set(resultObject, o);
                                }
                            }
                        } catch (NoSuchFieldException ignored) {
                        }
                    }
                }
                tClass = tClass.getSuperclass();
            }
            return resultObject;
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    private static Object deepConvertIfNeed(Field source, Field target, Object object, boolean isDeep) throws IllegalAccessException {
        // 如果允许深层转换 则尝试递归深层转换
        Object o = source.get(object);
        Class<?> newType = target.getType();
        if (isDeep && Objects.nonNull(o)) {
            newType = newType.getComponentType() == null ? newType : newType.getComponentType();
            Type genericType = target.getGenericType();
            if (o.getClass().isArray()) {
                o = convert((Object[]) o, newType);
            } else if (o instanceof Collection && genericType instanceof ParameterizedType) {
                Class<?> accountPrincipalApproveClazz = (Class<?>) ((ParameterizedType) genericType).getActualTypeArguments()[0];
                o = convert((Collection) o, accountPrincipalApproveClazz);
            } else {
                o = convert(o, newType);
            }
        } else {
            // 否则尝试类型转换
            try {
                o = convertType(source.get(object), newType);
            } catch (Exception ignored) { }
        }
        return o;
    }

    public static <T> List<Map<String, Object>> convertList(List<T> objectList) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (T obj : objectList) {
            list.add(convertObjectToMap(obj));
        }
        return list;
    }

    private static <T> Map<String, Object> convertObjectToMap(T obj) {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();

        for (Method method : clazz.getDeclaredMethods()) {
            if (method.getName().startsWith("get") && !method.getName().equals("getClass")) {
                try {
                    String key = method.getName().substring(3, 4).toLowerCase() + method.getName().substring(4);
                    Object value = method.invoke(obj);
                    map.put(key, value);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return map;
    }
}
