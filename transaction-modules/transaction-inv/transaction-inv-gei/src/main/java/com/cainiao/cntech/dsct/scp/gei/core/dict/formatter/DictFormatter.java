package com.cainiao.cntech.dsct.scp.gei.core.dict.formatter;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 16:44
 * @description
 */
public interface DictFormatter {
    static String subFormat(String format, int start) {
        return subFormat(format, start, format.length() - 1);
    }

    static String subFormat(String format, int start, int end) {
        return format.substring(start, end);
    }

    /**
     * 判断传入的字符串是否支持格式化
     *
     * @param input 配置字符串
     * @return true支持，false不支持
     */
    boolean support(String input);

    /**
     * 传入配置字符串，格式化后将结果返回
     *
     * @param input 配置字符串
     */
    String format(String input);
}
