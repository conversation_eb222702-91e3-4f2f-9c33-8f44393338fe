package com.cainiao.cntech.dsct.scp.gei.ext.processor;

import com.cainiao.cntech.dsct.scp.gei.common.request.GeiPageQueryRequest;
import com.cainiao.cntech.dsct.scp.gei.core.model.SliceDataWrapper;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-14 19:52
 * @description
 */
public class DefaultSliceDataPostProcessor implements SliceDataPostProcessor {

    @Override
    public boolean supportSliceData(Object request) {
        return request instanceof GeiPageQueryRequest;
    }

    @Override
    public void beforeSyncExportData(Object request) {
        if (supportSliceData(request)) {
            GeiPageQueryRequest query = (GeiPageQueryRequest) request;
            query.setPaging(false);
        }
    }

    @Override
    public void beforeAsyncSliceExportData(Object request, SliceDataWrapper sliceDataWrapper) {
        if (supportSliceData(request)) {
            GeiPageQueryRequest basePageQueryRequest = (GeiPageQueryRequest) request;
            basePageQueryRequest.setPaging(sliceDataWrapper.getPaging());
            basePageQueryRequest.setPageSize(sliceDataWrapper.getPageSize());
            basePageQueryRequest.setCurrentPage(sliceDataWrapper.getCurrentPage());
        }
    }
}
