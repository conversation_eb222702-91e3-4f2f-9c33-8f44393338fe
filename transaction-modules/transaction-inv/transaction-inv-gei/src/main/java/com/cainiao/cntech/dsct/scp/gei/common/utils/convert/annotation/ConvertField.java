package com.cainiao.cntech.dsct.scp.gei.common.utils.convert.annotation;

import com.cainiao.cntech.dsct.scp.gei.common.utils.convert.ext.ConvertProcessExtensible;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2023-03-17 09:24:09
 * @description
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ConvertField {
    /**
     * 转换属性名
     * @return
     */
    String value() default "";

    /**
     * 是否允许深层转换
     * @return
     */
    boolean deep() default false;

    /**
     * 转换扩展
     */
    Class<? extends ConvertProcessExtensible<?>>[] extensible() default {};
}
