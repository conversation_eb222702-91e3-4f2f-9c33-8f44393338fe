package com.cainiao.cntech.dsct.scp.gei.support.model;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-11 18:03
 * @description ExcelTemplateVO， 所有excel导出模版映射实体都需要继承该类
 * *** 注意是导出模版的映射实体，并非是导出数据的实体 ***
 */
@Getter
@Setter
public class ImportTemplateVO implements Serializable {

    private static final long serialVersionUID = 1213380474210492385L;

    @ExcelField(value = "异常原因", fixed = "LEFT")
    private String errorMsg;

    /**
     * 必须保证拥有无参构造
     */
    public ImportTemplateVO() {
    }
}
