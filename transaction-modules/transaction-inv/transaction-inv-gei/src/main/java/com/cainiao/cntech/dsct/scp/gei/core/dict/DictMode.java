package com.cainiao.cntech.dsct.scp.gei.core.dict;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 19:53
 * @description
 */
@Getter
@AllArgsConstructor
public enum DictMode {
    COVER(0, "全量覆盖"),
    REPLACE(1, "按meta code替换");

    private final int code;
    private final String name;

    public boolean isThis(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        return getCode() == code;
    }
}
