package com.cainiao.cntech.dsct.scp.gei.core.executor;

import cn.hutool.core.collection.ConcurrentHashSet;
import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.biz.dao.dto.ScpGeiTaskDTO;
import com.cainiao.cntech.dsct.scp.gei.biz.service.dto.ScpGeiChildTaskDTO;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.update.GeiChildTaskUpdateRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.request.update.GeiTaskUpdateRequest;
import com.cainiao.cntech.dsct.scp.gei.biz.service.service.ScpGeiChildTaskService;
import com.cainiao.cntech.dsct.scp.gei.biz.service.service.ScpGeiTaskService;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTaskStatusEnum;
import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTypeEnum;
import com.cainiao.cntech.dsct.scp.gei.common.error.GeiError;
import com.cainiao.cntech.dsct.scp.gei.common.utils.GenericsSearchUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.JsonUtils;
import com.cainiao.cntech.dsct.scp.gei.common.utils.StreamUtils;
import com.cainiao.cntech.dsct.scp.gei.core.GeiExcelManager;
import com.cainiao.cntech.dsct.scp.gei.core.SliceDataManager;
import com.cainiao.cntech.dsct.scp.gei.core.event.GeiAsyncEvent;
import com.cainiao.cntech.dsct.scp.gei.core.executor.model.GeiTaskResult;
import com.cainiao.cntech.dsct.scp.gei.core.executor.model.ResultHandleArgs;
import com.cainiao.cntech.dsct.scp.gei.core.executor.pool.GeiChildTaskThreadPool;
import com.cainiao.cntech.dsct.scp.gei.core.executor.pool.GeiTaskThreadPool;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelWriteWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ImportDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.SliceDataWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.storage.StorageTemplate;
import com.cainiao.cntech.dsct.scp.gei.ext.processor.SliceDataPostProcessor;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportErrorVO;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import com.cainiao.cntech.dsct.scp.gei.support.template.ExportDataTemplate;
import com.cainiao.cntech.dsct.scp.gei.support.template.ImportDataTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 16:48
 * @description gei任务执行器
 */
@Slf4j
public class EIportAsyncTaskExecutor {
    /**
     * 运行中的父任务记录
     */
    private static final Set<Long> PARENT_TASK_RECORD = new ConcurrentHashSet<>();

    @Resource
    private SliceDataManager sliceDataManager;
    @Resource
    private GeiExcelManager geiManager;
    @Resource
    private GeiChildTaskThreadPool geiChildTaskThreadPool;
    @Resource
    private GeiTaskThreadPool geiTaskThreadPool;
    @Resource
    private ScpGeiTaskService scpGeiTaskService;
    @Resource
    private GeiTaskResultHandler geiTaskResultHandler;
    @Resource
    private ExportTemplateExecutor exportTemplateExecutor;
    @Resource
    private ImportTemplateExecutor importTemplateExecutor;
    @Resource
    private ScpGeiChildTaskService scpGeiChildTaskService;
    @Resource
    private StorageTemplate storageTemplate;

    /**
     * 格式化异常对象转换为描述
     *
     * @param e 异常对象
     * @return 异常描述信息
     */
    private static String formatError(Throwable e) {
        return String.format("%s: %s", e.getClass().getName(), e.getMessage());
    }

    @Scheduled(cron = "0 0 1 1/1 * ?")
    public void clearExpireTask() {
        List<ScpGeiTaskDTO> list = scpGeiTaskService.queryExpireTaskList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ScpGeiTaskDTO dto : list) {
            if (GeiTypeEnum.EXPORT.isThis(dto.getTaskType())) {
                storageTemplate.delete(dto.getFileName());
            }
        }
        scpGeiTaskService.clearTask(StreamUtils.map(list, ScpGeiTaskDTO::getTaskId));
        log.info("Clear file expire task total: {}", list.size());
    }

    @Scheduled(cron = "0 0/5 * * * ?")
    public void closeProcessExpireTaskList() {
        List<ScpGeiTaskDTO> list = scpGeiTaskService.queryProcessExpireTaskList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list = StreamUtils.filter(list, item -> !PARENT_TASK_RECORD.contains(item.getTaskId()));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        scpGeiTaskService.closeProcessExpireTask(StreamUtils.map(list, ScpGeiTaskDTO::getTaskId));
        log.info("Close process expire task total: {}", list.size());
    }

    /**
     * 提交异步任务
     *
     * @param event 事件
     */
    @EventListener
    public <T extends ImportTemplateVO> void submit(GeiAsyncEvent<T> event) {
        Long parentTaskId = event.getTaskId();
        try {
            // 设置主任务执行状态为等待中
            updateParentTaskStatus(parentTaskId, GeiTaskStatusEnum.AWAIT, null, null, null);
            // 查询主任务，并校验参数
            ScpGeiTaskDTO geiTask = scpGeiTaskService.getByTaskId(parentTaskId);
            // 设置主任务执行状态为运行中
            updateParentTaskStatus(parentTaskId, GeiTaskStatusEnum.RUNNING, null, null, null);
            Assert.notNull(geiTask, ErrorCode.SYSTEM_ERROR, GeiError.PARENT_TASK_ID_NOT_FOUND.getError());
            GeiTypeEnum geiType = GeiTypeEnum.getByCode(geiTask.getTaskType());
            Assert.isTrue(Objects.nonNull(geiType), ErrorCode.SYSTEM_ERROR, GeiError.TASK_TYPE_ILLEGAL.getError());
            // 查找子任务，开始调度执行
            List<ScpGeiChildTaskDTO> childTasks = scpGeiChildTaskService.queryByParendId(parentTaskId);
            Assert.isTrue(CollectionUtils.isNotEmpty(childTasks), ErrorCode.SYSTEM_ERROR, GeiError.CHILD_TASKS_NOT_FOUND.getError(), parentTaskId);
            CompletableFuture.runAsync(
                            GeiTypeEnum.EXPORT.isThis(geiTask.getTaskType())
                                    ? createExportParentTask(parentTaskId, geiTask, childTasks)
                                    : createImportParentTask(parentTaskId, event, geiTask, childTasks)
                            , geiTaskThreadPool)
                    .exceptionally(e -> {
                        // 异常时修改主任务状态 以及 状态出现原因
                        updateParentTaskStatus(parentTaskId, GeiTaskStatusEnum.FAILED, formatError(e), null, null);
                        PARENT_TASK_RECORD.remove(parentTaskId);
                        return null;
                    });
        } catch (Exception e) {
            // 异常时修改主任务状态 以及 状态出现原因
            updateParentTaskStatus(parentTaskId, GeiTaskStatusEnum.FAILED, formatError(e), null, null);
        }
    }

    private Runnable createExportParentTask(Long parentTaskId, ScpGeiTaskDTO geiTask, List<ScpGeiChildTaskDTO> childTasks) {
        return () -> {
            PARENT_TASK_RECORD.add(parentTaskId);
            // 设置主任务执行状态为运行中
            updateParentTaskStatus(parentTaskId, GeiTaskStatusEnum.RUNNING, null, null, null);
            List<CompletableFuture<?>> tasks = new LinkedList<>();
            for (ScpGeiChildTaskDTO childTask : childTasks) {
                // 创建子任务前，先修改子任务状态为等待中
                updateChildTaskStatus(childTask.getTaskId(), childTask.getParentTaskId(), GeiTaskStatusEnum.AWAIT, null);
            }
            for (ScpGeiChildTaskDTO childTask : childTasks) {
                tasks.add(createExportChildTask(childTask));
            }
            // 调用子任务处理器，处理结果
            GeiTaskResult result = geiTaskResultHandler.handle(ResultHandleArgs.exportArgs(geiTask.getTemplateDictCode(), geiTask), tasks);
            updateParentTaskStatus(parentTaskId, result.getStatus(), result.getErrorMsg(), result.getFilename(), result.getFileAddress());
            PARENT_TASK_RECORD.remove(parentTaskId);
        };
    }

    private <T extends ImportTemplateVO> Runnable createImportParentTask(Long parentTaskId, GeiAsyncEvent<T> event, ScpGeiTaskDTO geiTask, List<ScpGeiChildTaskDTO> childTasks) {
        return () -> {
            PARENT_TASK_RECORD.add(parentTaskId);
            List<CompletableFuture<?>> tasks = new LinkedList<>();
            for (ScpGeiChildTaskDTO childTask : childTasks) {
                // 创建子任务前，先修改子任务状态为等待中
                updateChildTaskStatus(childTask.getTaskId(), childTask.getParentTaskId(), GeiTaskStatusEnum.AWAIT, null);
            }
            for (ScpGeiChildTaskDTO childTask : childTasks) {
                // step1： 首次创建子任务执行，这里两种场景
                //      case1： 启用任务隔离，invokeTemplateAndCreateTask方法将各自校验 + 上传
                //      case2： 关闭任务隔离，invokeTemplateAndCreateTask方法，只返回校验的结果
                tasks.add(createImportChildTask(childTask, event, true));
            }
            // step2: 收集任务的结果
            boolean isSuccess = true;
            for (CompletableFuture<?> task : tasks) {
                ExcelMetaInfoWrapper wrapper = (ExcelMetaInfoWrapper) task.join();
                // 关闭任务隔离的场景下，如果校验成功则返回null
                if (Objects.nonNull(wrapper)) {
                    isSuccess = false;
                }
            }
            ImportService importService = geiManager.getImportTemplateAnnotation(geiTask.getExecuteTemplateCode());
            // step3: 判断如果关闭任务隔离 并且 校验成功，重新创建任务并发上传数据
            if (!importService.sliceIso() && isSuccess) {
                tasks.clear();
                for (ScpGeiChildTaskDTO childTask : childTasks) {
                    tasks.add(createImportChildTask(childTask, event, false));
                }
            } else if (!importService.sliceIso()) {
                scpGeiChildTaskService.updateNotIsoTaskStatusFailedByTaskId(parentTaskId);
            }
            // step4: 这里正常处理任务结果即可
            //      case1:  启用任务隔离，那么tasks就是各自任务并发上传处理的任务集
            //      case2:  关闭任务隔离 且 校验失败，那么tasks就是校验错误信息的任务集
            //      case3:  关闭任务隔离 且 校验成功，那么tasks就是各自任务并发上传处理的任务集
            GeiTaskResult result = geiTaskResultHandler.handle(ResultHandleArgs.importArgs(geiTask.getTemplateDictCode(), geiTask), tasks);
            ImportDataTemplate<T> importTemplate = getTemplateByCode(geiTask.getExecuteTemplateCode());
            importTemplateExecutor.invokeAfterImportProcessed(importTemplate, event.getWrapper());
            updateParentTaskStatus(parentTaskId, result.getStatus(), result.getErrorMsg(), result.getFilename(), result.getFileAddress());
            PARENT_TASK_RECORD.remove(parentTaskId);
        };
    }

    /**
     * 创建导出子任务
     *
     * @param childTask
     * @return
     */
    private CompletableFuture<ExcelWriteWrapper> createExportChildTask(ScpGeiChildTaskDTO childTask) {
        return CompletableFuture.supplyAsync(() -> {
            updateChildTaskStatus(childTask.getTaskId(), childTask.getParentTaskId(), GeiTaskStatusEnum.RUNNING, null);
            Boolean paging = !Boolean.FALSE.equals(childTask.getPaging());
            Long pageSize = childTask.getPageSize();
            Long page = childTask.getPage();
            String executeTemplateCode = childTask.getExecuteTemplateCode();
            ExportDataTemplate<Object> exportDataTemplate = geiManager.getExportTemplate(executeTemplateCode);
            ExportService exportServiceAnnotation = geiManager.getExportTemplateAnnotation(executeTemplateCode);
            Assert.notNull(exportDataTemplate, ErrorCode.SYSTEM_ERROR, "根据子任务执行模版编码未找到对应的模版实现");
            Class<?> paramsType = GenericsSearchUtils.searchClass(exportDataTemplate, ExportDataTemplate.class, 0);
            String requestParams = childTask.getRequestParams();
            Object request = JsonUtils.toObject(requestParams, paramsType);
            invokeBeforeAsyncSliceExportDataProcessors(request, SliceDataWrapper.of(paging, page, pageSize));
            ExcelWriteWrapper excelWriteWrapper = exportTemplateExecutor.invokeTemplate(exportDataTemplate, exportServiceAnnotation, request);
            updateChildTaskStatus(childTask.getTaskId(), childTask.getParentTaskId(), GeiTaskStatusEnum.SUCCESS, "");
            return excelWriteWrapper;
        }, geiChildTaskThreadPool).exceptionally(e -> {
            updateChildTaskStatus(childTask.getTaskId(), childTask.getParentTaskId(), GeiTaskStatusEnum.FAILED, formatError(e));
            return null;
        });
    }

    /**
     * 创建导入子任务
     *
     * @param childTask
     * @param event
     * @param <T>
     * @return
     */
    private <T extends ImportTemplateVO> CompletableFuture<ExcelMetaInfoWrapper> createImportChildTask(ScpGeiChildTaskDTO childTask, GeiAsyncEvent<T> event, boolean isNeedValidate) {
        ImportDataWrapper<T> importDataWrapper = event.getWrapper();
        List<T> importList = importDataWrapper.getData();
        return CompletableFuture.supplyAsync(() -> {
            ExcelMetaInfoWrapper result;
            if (isNeedValidate) {
                updateChildTaskStatus(childTask.getTaskId(), childTask.getParentTaskId(), GeiTaskStatusEnum.RUNNING, null);
            }
            Boolean paging = childTask.getPaging();
            Long pageSize = childTask.getPageSize(), page = childTask.getPage();
            String executeTemplateCode = childTask.getExecuteTemplateCode();
            ImportDataTemplate<T> importDataTemplate = getTemplateByCode(executeTemplateCode);
            ImportService importServiceAnnotation = geiManager.getImportTemplateAnnotation(executeTemplateCode);
            Assert.notNull(importDataTemplate, ErrorCode.SYSTEM_ERROR, "根据子任务执行模版编码未找到对应的模版实现");
            Class<T> modelClass = GenericsSearchUtils.searchClass(importDataTemplate, ImportDataTemplate.class, 0);

            if (Boolean.FALSE.equals(paging)) {
                result = importTemplateExecutor.invokeTemplate(importDataTemplate, importDataWrapper
                        , modelClass, importServiceAnnotation, isNeedValidate);
            } else {
                List<T> partition = ImportTemplateExecutor.sliceImportList(importList, Integer.parseInt(String.valueOf(page)), Integer.parseInt(String.valueOf(pageSize)));
                ImportDataWrapper<T> copyWrapper = JsonUtils.copy(importDataWrapper);
                copyWrapper.setData(partition);
                result = importTemplateExecutor.invokeTemplate(importDataTemplate, copyWrapper, modelClass, importServiceAnnotation, isNeedValidate);
            }
            // 任务隔离 ｜｜ 不需要校验，则更新子任务状态
            if (importServiceAnnotation.sliceIso() || !isNeedValidate) {
                updateChildTaskStatus(childTask.getTaskId(), childTask.getParentTaskId()
                        , result.isSuccess() ? GeiTaskStatusEnum.SUCCESS : GeiTaskStatusEnum.FAILED
                        , result.isSuccess() ? "" : JsonUtils.toStr(result));
            } else if (Objects.nonNull(result)) {
                // 任务不隔离 && 需要校验 && 校验失败，则更新子任务状态
                updateChildTaskStatus(childTask.getTaskId(), childTask.getParentTaskId()
                        , result.isSuccess() ? GeiTaskStatusEnum.SUCCESS : GeiTaskStatusEnum.FAILED
                        , result.isSuccess() ? "" : JsonUtils.toStr(result));
            }
            return result;
        }, geiChildTaskThreadPool).exceptionally(e -> {
            updateChildTaskStatus(childTask.getTaskId(), childTask.getParentTaskId(), GeiTaskStatusEnum.FAILED, formatError(e));
            return ExcelMetaInfoWrapper.of(Collections.singletonList(ImportErrorVO.of(formatError(e)))).failed();
        });
    }

    /**
     * 修改主任务状态
     *
     * @param parentTaskId 主任务id
     * @param status       状态
     * @param statusDesc   状态原因
     */
    private void updateParentTaskStatus(Long parentTaskId, GeiTaskStatusEnum status, String statusDesc, String filename, String fileAddress) {
        GeiTaskUpdateRequest request = new GeiTaskUpdateRequest();
        request.setTaskId(parentTaskId);
        request.setStatus(status.getCode());
        request.setStatusDesc(statusDesc);
        request.setFileAddress(fileAddress);
        request.setFileName(filename);
        if (GeiTaskStatusEnum.RUNNING.equals(status)) {
            request.setGmtProcessStarted(LocalDateTime.now());
        } else if (GeiTaskStatusEnum.SUCCESS.equals(status) || GeiTaskStatusEnum.FAILED.equals(status)) {
            request.setGmtProcessFinished(LocalDateTime.now());
        }
        scpGeiTaskService.updateByTaskId(request);
    }

    /**
     * 修改子任务状态
     *
     * @param childTaskId  子任务id
     * @param parentTaskId 主任务id
     * @param status       状态
     * @param statusDesc   状态原因
     */
    private void updateChildTaskStatus(String childTaskId, Long parentTaskId, GeiTaskStatusEnum status, String statusDesc) {
        GeiChildTaskUpdateRequest request = new GeiChildTaskUpdateRequest();
        request.setTaskId(childTaskId);
        request.setParentTaskId(parentTaskId);
        request.setStatus(status.getCode());
        request.setStatusDesc(statusDesc);
        if (GeiTaskStatusEnum.RUNNING.equals(status)) {
            request.setGmtProcessStarted(LocalDateTime.now());
        } else if (GeiTaskStatusEnum.SUCCESS.equals(status) || GeiTaskStatusEnum.FAILED.equals(status)) {
            request.setGmtProcessFinished(LocalDateTime.now());
        }
        scpGeiChildTaskService.updateByTaskId(request);
    }

    private boolean invokeSupportSliceData(Object request) {
        for (SliceDataPostProcessor processor : sliceDataManager.getProcessors()) {
            if (processor.supportSliceData(request)) {
                return true;
            }
        }
        return false;
    }

    private void invokeBeforeAsyncSliceExportDataProcessors(Object request, SliceDataWrapper sliceDataWrapper) {
        if (invokeSupportSliceData(request)) {
            for (SliceDataPostProcessor processor : sliceDataManager.getProcessors()) {
                processor.beforeAsyncSliceExportData(request, sliceDataWrapper);
            }
        }
    }

    private <T extends ImportTemplateVO> ImportDataTemplate<T> getTemplateByCode(String templateCode) {
       return (ImportDataTemplate<T>) geiManager.getImportTemplate(templateCode);
    }
}
