package com.cainiao.cntech.dsct.scp.gei.support.quick.annotation;


import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-21 10:18
 * @description
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface QuickBasicService {
    /**
     * 服务编码
     *
     * @return
     */
    String value();
}
