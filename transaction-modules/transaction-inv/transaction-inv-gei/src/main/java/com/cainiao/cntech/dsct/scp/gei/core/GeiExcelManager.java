package com.cainiao.cntech.dsct.scp.gei.core;

import com.cainiao.cntech.dsct.scp.gei.common.exception.Assert;
import com.cainiao.cntech.dsct.scp.gei.common.exception.ErrorCode;
import com.cainiao.cntech.dsct.scp.gei.core.executor.ExportTemplateExecutor;
import com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelMetaInfoWrapper;
import com.cainiao.cntech.dsct.scp.gei.core.model.ExcelWriteWrapper;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExportService;
import com.cainiao.cntech.dsct.scp.gei.support.annotation.ImportService;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;
import com.cainiao.cntech.dsct.scp.gei.support.template.ExportDataTemplate;
import com.cainiao.cntech.dsct.scp.gei.support.template.ImportDataTemplate;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 13:42
 * @description gei管理器
 */
public class GeiExcelManager implements ApplicationRunner {
    private final Class<ExportService> EXPORT_TEMPLATE_CLASS = ExportService.class;
    private final Class<ImportService> IMPORT_TEMPLATE_CLASS = ImportService.class;
    /**
     * 导出数据模版： key=ExportTemplate#value(), value：导出模版实现对象
     */
    private final Map<String, ExportDataTemplate<Object>> EXPORT_DATA_TEMPLATES = new LinkedHashMap<>();
    /**
     * 导出数据模版标记的注解映射map： key=ExportTemplate#value(), value：ExportTemplate
     */
    private final Map<String, ExportService> EXPORT_TEMPLATE_ANNOTATIONS = new LinkedHashMap<>();
    /**
     * 导入数据模版： key=ImportDataTemplate#value(), value：导入模版实现对象
     */
    private final Map<String, ImportDataTemplate<? extends ImportTemplateVO>> IMPORT_DATA_TEMPLATES = new LinkedHashMap<>();
    /**
     * 导出数据模版标记的注解映射map： key=ImportDataTemplate#value(), value：ImportTemplate
     */
    private final Map<String, ImportService> IMPORT_TEMPLATE_ANNOTATIONS = new LinkedHashMap<>();
    @Resource
    private ExportTemplateExecutor exportTemplateExecutor;
    @Resource
    private ImportTemplateExecutor importTemplateExecutor;
    @Resource
    private ConfigurableApplicationContext context;
    @Resource
    private SimpleExcelWriter simpleExcelWriter;

    public ExportDataTemplate<Object> getExportTemplate(String code) {
        return EXPORT_DATA_TEMPLATES.get(code);
    }

    public ExportService getExportTemplateAnnotation(String code) {
        return EXPORT_TEMPLATE_ANNOTATIONS.get(code);
    }

    public ImportDataTemplate<? extends ImportTemplateVO> getImportTemplate(String code) {
        return IMPORT_DATA_TEMPLATES.get(code);
    }

    public ImportService getImportTemplateAnnotation(String code) {
        return IMPORT_TEMPLATE_ANNOTATIONS.get(code);
    }

    public void exportData(String code, HttpServletResponse response, Map<String, Object> queryMap) {
        ExportDataTemplate<Object> template = EXPORT_DATA_TEMPLATES.get(code);
        Assert.notNull(template, ErrorCode.SYSTEM_ERROR, "数据导出模版获取失败，dataCode={}", code);
        ExportService exportService = EXPORT_TEMPLATE_ANNOTATIONS.get(code);
        Assert.notNull(exportService, ErrorCode.SYSTEM_ERROR, "数据导出模版获取失败，dataCode={}", code);
        // 异步导出
        if (exportService.async()) {
            exportTemplateExecutor.async(template, exportService, queryMap);
        } else {
            // 同步导出
            ExcelWriteWrapper wrapper = exportTemplateExecutor.sync(template, exportService, queryMap);
            simpleExcelWriter.write(wrapper, response);
        }
    }

    public void exportTemplate(String code, HttpServletResponse response, Map<String, Object> queryMap) {
        ImportDataTemplate<? extends ImportTemplateVO> importDataTemplate = IMPORT_DATA_TEMPLATES.get(code);
        Assert.notNull(importDataTemplate, ErrorCode.SYSTEM_ERROR, "数据导入模版获取失败，dataCode={}", code);
        ImportService importService = IMPORT_TEMPLATE_ANNOTATIONS.get(code);
        Assert.notNull(importService, ErrorCode.SYSTEM_ERROR, "数据导出模版获取失败，dataCode={}", code);
        ExcelWriteWrapper wrapper = importTemplateExecutor.exportTemplate(importDataTemplate, importService, queryMap);
        simpleExcelWriter.write(wrapper, response);
    }

    public ExcelMetaInfoWrapper importData(String code, MultipartFile multipartFile, Map<String, Object> params) throws IOException {
        ImportDataTemplate<? extends ImportTemplateVO> importDataTemplate = IMPORT_DATA_TEMPLATES.get(code);
        Assert.notNull(importDataTemplate, ErrorCode.SYSTEM_ERROR, "数据导入模版获取失败，dataCode={}", code);
        ImportService importService = IMPORT_TEMPLATE_ANNOTATIONS.get(code);
        Assert.notNull(importService, ErrorCode.SYSTEM_ERROR, "数据导出模版获取失败，dataCode={}", code);
        // 异步导入
        if (importService.async()) {
            return importTemplateExecutor.async(importDataTemplate, importService, multipartFile, params);
        } else {
            // 同步导入
            return importTemplateExecutor.sync(importDataTemplate, importService, multipartFile, params);
        }
    }

    public boolean isAsync(String code) {
        ExportService exportService = EXPORT_TEMPLATE_ANNOTATIONS.get(code);
        if (Objects.nonNull(exportService)) {
            return exportService.async();
        }
        ImportService importService = IMPORT_TEMPLATE_ANNOTATIONS.get(code);
        if (Objects.nonNull(importService)) {
            return importService.async();
        }
        return false;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // export
        Map<String, ExportDataTemplate> exportBeans = context.getBeansOfType(ExportDataTemplate.class);
        for (Map.Entry<String, ExportDataTemplate> entry : exportBeans.entrySet()) {
            ExportDataTemplate<Object> template = entry.getValue();
            Class<?> clazz = AopProxyUtils.ultimateTargetClass(template);
            if (clazz.isAnnotationPresent(EXPORT_TEMPLATE_CLASS)) {
                ExportService annotation = clazz.getAnnotation(EXPORT_TEMPLATE_CLASS);
                EXPORT_DATA_TEMPLATES.put(annotation.value(), template);
                EXPORT_TEMPLATE_ANNOTATIONS.put(annotation.value(), annotation);
            }
        }

        // import
        Map<String, ImportDataTemplate> importBeans = context.getBeansOfType(ImportDataTemplate.class);
        for (Map.Entry<String, ImportDataTemplate> entry : importBeans.entrySet()) {
            ImportDataTemplate<? extends ImportTemplateVO> template = entry.getValue();
            Class<?> clazz = AopProxyUtils.ultimateTargetClass(template);
            if (clazz.isAnnotationPresent(IMPORT_TEMPLATE_CLASS)) {
                ImportService annotation = clazz.getAnnotation(IMPORT_TEMPLATE_CLASS);
                IMPORT_DATA_TEMPLATES.put(annotation.value(), template);
                IMPORT_TEMPLATE_ANNOTATIONS.put(annotation.value(), annotation);
            }
        }
    }
}
