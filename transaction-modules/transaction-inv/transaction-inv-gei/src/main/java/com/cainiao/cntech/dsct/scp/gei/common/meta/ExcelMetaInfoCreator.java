package com.cainiao.cntech.dsct.scp.gei.common.meta;

import com.cainiao.cntech.dsct.scp.gei.support.annotation.ExcelField;
import org.apache.commons.collections4.CollectionUtils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 15:02
 * @description excel元信息创建器
 */
public class ExcelMetaInfoCreator {

    public static List<ExcelMetaInfo> create(Class<?> clazz){
        return create(clazz, false);
    }
    public static List<ExcelMetaInfo> create(Class<?> clazz, String... paramMappings) {
        if (null == clazz) {
            return Collections.emptyList();
        }
        return create(clazz, false, paramMappings);
    }
    public static List<ExcelMetaInfo> create(Class<?> clazz, boolean deep, String... paramMappings) {
        if(null == clazz){
            return Collections.emptyList();
        }
        List<ExcelMetaInfo> metaInfoList = new LinkedList<>();
        Class<?> temp = clazz;
        while (!temp.equals(Object.class)) {
            for (Field field : temp.getDeclaredFields()) {
                if (!field.isAnnotationPresent(ExcelField.class)) {
                    continue;
                }
                ExcelField excelField = field.getAnnotation(ExcelField.class);
                if (paramMappings != null && excelField.mapping() != null && excelField.mapping().length > 0) {
                    // 注解配置的mapping
                    List<String> configMapping = Arrays.asList(excelField.mapping());
                    boolean mappingFlag = true;
                    for (String paramMapping : paramMappings) {
                        // 只要配置的mapping不包含传入的mapping，则表示不通过匹配。
                        if (configMapping.contains(paramMapping)) {
                            mappingFlag = true;
                            break;
                        }else {
                            mappingFlag = false;
                        }
                    }
                    if(!mappingFlag){ continue; }
                }
                if (null != excelField) {
                    ExcelMetaInfo metaInfo = ExcelMetaInfo
                            .builder()
                            .code(field.getName())
                            .name(excelField.value())
                            .fixed(excelField.fixed())
                            .hidden(excelField.hidden())
                            .tooltip(excelField.tooltip())
                            .sort(excelField.sort())
                            .importKey(excelField.importKey())
                            .build();
                    metaInfoList.add(metaInfo);
                }
            }
            if(deep){
                temp = temp.getSuperclass();
            }else {
                break;
            }
        }
        sort(metaInfoList);
        return metaInfoList;
    }
    public static void sort(List<ExcelMetaInfo> metaInfoList) {
        if (CollectionUtils.isEmpty(metaInfoList)) {
            return;
        }
        metaInfoList.sort(Comparator.comparing(ExcelMetaInfo::getSort));
    }
}
