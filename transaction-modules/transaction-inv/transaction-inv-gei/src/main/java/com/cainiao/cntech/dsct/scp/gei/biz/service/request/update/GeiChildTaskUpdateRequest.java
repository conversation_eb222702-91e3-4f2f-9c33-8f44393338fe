package com.cainiao.cntech.dsct.scp.gei.biz.service.request.update;

import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiTaskStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 14:44
 * @description 子任务修改请求
 */
@Data
public class GeiChildTaskUpdateRequest {
    /**
     * '任务ID'
     */
    private String taskId;
    /**
     * 父任务id
     */
    private Long parentTaskId;
    /**
     * 任务状态，CREATED新建，AWAIT待运行，RUNNING运行中，FAILED异常，SUCCESS成功
     * @see GeiTaskStatusEnum
     */
    private String status;
    /**
     * '任务状态产生的原因'
     */
    private String statusDesc;
    /**
     * '任务开始处理的时间戳'
     */
    private LocalDateTime gmtProcessStarted;
    /**
     * '任务结束的时间戳'
     */
    private LocalDateTime gmtProcessFinished;
    /**
     * '处理阶段的尝试次数'
     */
    private Integer processTryTimes;
    /**
     * 数据文件地址
     */
    private String dataFileAddress;
    /**
     * '操作人编码'
     */
    private String operatorCode;
    /**
     * '操作人名称'
     */
    private String operatorName;
}
