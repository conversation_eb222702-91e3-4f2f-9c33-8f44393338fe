package com.cainiao.cntech.dsct.scp.gei.ext.converter;

import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import org.springframework.core.annotation.Order;

import java.time.LocalDate;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-22 13:42
 * @description LocalDate -> string转换器。 用于将对象实体中字段值类型为LocalDate的数据转换为字符串返回
 */
@Order
public class LocalDateToStringExcelConverterExcel implements ExcelWriteConverter<String, LocalDate> {
    @Override
    public boolean support(Object fieldValue) {
        return fieldValue instanceof LocalDate;
    }

    @Override
    public String convert(LocalDate fieldValue) {
        return DateUtil.localDateToString(fieldValue);
    }
}
