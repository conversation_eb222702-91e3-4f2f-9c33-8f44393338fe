package com.cainiao.cntech.dsct.scp.gei.core.executor.pool;

import com.cainiao.cntech.dsct.scp.gei.configuration.GeiPoolProperties;

import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-09 15:20
 * @description gei线程池， 主任务使用
 */
public class GeiTaskThreadPool extends ThreadPoolExecutor {
    public GeiTaskThreadPool(GeiPoolProperties geiPoolProperties) {
        super(geiPoolProperties.getCore(), geiPoolProperties.getMax()
                , geiPoolProperties.getKeepAlive(), geiPoolProperties.getKeepAliveUnit()
                , geiPoolProperties.getQueue().getQueue(geiPoolProperties)
                , Executors.defaultThreadFactory()
                , geiPoolProperties.getRejected().getStrategy()
        );
    }
}
