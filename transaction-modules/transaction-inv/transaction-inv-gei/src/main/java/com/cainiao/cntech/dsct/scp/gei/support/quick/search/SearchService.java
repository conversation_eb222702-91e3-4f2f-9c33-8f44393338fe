package com.cainiao.cntech.dsct.scp.gei.support.quick.search;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cainiao.cntech.dsct.scp.gei.biz.web.model.OptionVO;
import com.cainiao.cntech.dsct.scp.gei.common.constants.OrderType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-07 15:21
 * @description 条件搜索通用service
 */
public class SearchService {
    /**
     * 获取条件筛选的OptionDTO
     *
     * @param service    查询service
     * @param searchType 搜索枚举
     * @param queryValue 前端传入的查询值
     * @param <T>
     * @return 返回筛选列表
     */
    public static <T> List<OptionVO> get(IService<T> service, BaseSearchCondition<T> searchType, String queryValue) {
        if (Objects.isNull(searchType)) {
            return Collections.emptyList();
        }
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(queryValue)) {
            List<String> searchField = searchType.getSearchField();
            if (searchField == null || searchField.isEmpty()) {
                return Collections.emptyList();
            }
            for (int i = 0; i < searchField.size(); i++) {
                if (i == 0) {
                    wrapper.like(searchField.get(i), queryValue);
                } else {
                    wrapper.or().like(searchField.get(i), queryValue);
                }
            }
        }
        List<String> groupField = searchType.getGroupField();
        if (CollectionUtils.isNotEmpty(groupField)) {
            wrapper.groupBy(groupField);
            wrapper.select(groupField);
        }
        List<String> orderField = searchType.getOrderField();
        if (CollectionUtils.isNotEmpty(orderField)) {
            if (StringUtils.equalsIgnoreCase(OrderType.DESC, searchType.getOrderMethod())) {
                wrapper.orderByDesc(orderField);
            } else {
                wrapper.orderByAsc(orderField);
            }
        }
        wrapper.last("LIMIT 20");
        Consumer<QueryWrapper<T>> beforeListExt = searchType.getBeforeListExt();
        if (Objects.nonNull(beforeListExt)) {
            beforeListExt.accept(wrapper);
        }
        List<T> ts = service.list(wrapper);
        if (CollectionUtils.isEmpty(ts)) {
            return Collections.emptyList();
        }
        return ts.stream().map(searchType::convert).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
