//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cainiao.cntech.dsct.scp.gei.common.log;


import com.cainiao.cntech.dsct.scp.gei.common.util.IpUtils;
import com.cainiao.cntech.dsct.scp.gei.common.util.UuidUtils;

public class TraceIdHolder {
    private static final ThreadLocal<String> TRACE_LOG_THREAD_LOCAL = new ThreadLocal();

    public TraceIdHolder() {
    }

    public static String getTraceId() {
        return (String)TRACE_LOG_THREAD_LOCAL.get();
    }

    public static void clearTraceId() {
        TRACE_LOG_THREAD_LOCAL.remove();
    }

    public static String setAndGetTraceId() {
        String serverIp = IpUtils.getServerIp();
        String traceId = "[" + UuidUtils.getUniqueId() + "#" + serverIp + "]";
        TRACE_LOG_THREAD_LOCAL.set(traceId);
        return getTraceId();
    }
}
