//package com.cainiao.cntech.dsct.scp.gei.configuration;
//
//import com.baomidou.mybatisplus.core.MybatisConfiguration;
//import com.baomidou.mybatisplus.core.config.GlobalConfig;
//import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
//import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
//import com.cainiao.cntech.dsct.scp.gei.biz.service.manager.ScpGeiChildTaskManager;
//import com.cainiao.cntech.dsct.scp.gei.biz.service.manager.ScpGeiDictManager;
//import com.cainiao.cntech.dsct.scp.gei.biz.service.manager.ScpGeiTaskManager;
//import com.cainiao.cntech.dsct.scp.gei.biz.service.service.ScpGeiChildTaskService;
//import com.cainiao.cntech.dsct.scp.gei.biz.service.service.ScpGeiTaskService;
//import com.cainiao.cntech.dsct.scp.gei.biz.service.service.impl.ScpGeiChildTaskServiceImpl;
//import com.cainiao.cntech.dsct.scp.gei.biz.service.service.impl.ScpGeiTaskServiceImpl;
//import com.cainiao.cntech.dsct.scp.gei.common.constants.GeiCustomSqlSessionConstant;
//import com.cainiao.cntech.dsct.scp.gei.processor.GeiSqlSessionFactoryBeanFactoryPostProcessor;
//import com.cainiao.cntech.dsct.scp.gei.core.*;
//import com.cainiao.cntech.dsct.scp.gei.core.dict.GeiDictResolver;
//import com.cainiao.cntech.dsct.scp.gei.core.dict.formatter.DateDictFormatter;
//import com.cainiao.cntech.dsct.scp.gei.core.event.GeiEventPublisher;
//import com.cainiao.cntech.dsct.scp.gei.core.executor.EIportAsyncTaskExecutor;
//import com.cainiao.cntech.dsct.scp.gei.core.executor.ExportTemplateExecutor;
//import com.cainiao.cntech.dsct.scp.gei.core.executor.GeiTaskResultHandler;
//import com.cainiao.cntech.dsct.scp.gei.core.executor.ImportTemplateExecutor;
//import com.cainiao.cntech.dsct.scp.gei.core.executor.pool.GeiChildTaskThreadPool;
//import com.cainiao.cntech.dsct.scp.gei.core.executor.pool.GeiTaskThreadPool;
//import com.cainiao.cntech.dsct.scp.gei.core.storage.MinioStorageTemplate;
//import com.cainiao.cntech.dsct.scp.gei.core.storage.NoneStorageTemplate;
//import com.cainiao.cntech.dsct.scp.gei.core.storage.OSSStorageTemplate;
//import com.cainiao.cntech.dsct.scp.gei.core.storage.StorageTemplate;
//import com.cainiao.cntech.dsct.scp.gei.ext.converter.ListToStringExcelConverter;
//import com.cainiao.cntech.dsct.scp.gei.ext.converter.LocalDateTimeToStringExcelConverter;
//import com.cainiao.cntech.dsct.scp.gei.ext.converter.LocalDateToStringExcelConverterExcel;
//import com.cainiao.cntech.dsct.scp.gei.ext.converter.WriteConverterManager;
//import com.cainiao.cntech.dsct.scp.gei.ext.formula.FormulaExportPostProcessor;
//import com.cainiao.cntech.dsct.scp.gei.ext.processor.DefaultSliceDataPostProcessor;
//import org.apache.ibatis.logging.stdout.StdOutImpl;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.ComponentScan;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.annotation.Order;
//import org.springframework.core.io.Resource;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.scheduling.annotation.EnableAsync;
//import org.springframework.scheduling.annotation.EnableScheduling;
//
//import javax.annotation.PostConstruct;
//import javax.sql.DataSource;
//import java.util.*;
//
///**
// * work for life
// *
// * <AUTHOR>
// * @date 2025-01-08 18:44
// * @description
// */
//@EnableAsync
//@EnableScheduling
//@Configuration
//@ComponentScan(basePackages = "com.cainiao.cntech.dsct.scp.gei.biz.web")
//public class GeiAutoConfiguration {
//    @Configuration
//    public static class GeiSqlSessionConfiguration {
//        @Configuration
//        @ConditionalOnProperty(prefix = "gei.datasource", name = "enable", havingValue = "true")
//        @MapperScan(basePackages = "com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper", sqlSessionFactoryRef = "geiBizSqlSessionFactory")
//        public static class GeiCustomSqlSessionConfiguration {
//            private final List<String> mapperNames = Arrays.asList("ScpGeiChildTaskMapper.xml", "ScpGeiDictMapper.xml", "ScpGeiTaskMapper.xml");
//            /**
//             * 构造SqlSessionFactory
//             * @return      SqlSessionFactory
//             */
//            @Bean(name = GeiCustomSqlSessionConstant.SESSION_FACTORY_BEAN_NAME)
//            public SqlSessionFactory geiBizSqlSessionFactory(@Autowired(required = false) MybatisPlusInterceptor mybatisPlusInterceptor, DataSource dataSource) throws Exception {
//                MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
//                sqlSessionFactoryBean.setDataSource(dataSource);
//                // 指定mapper文件的路径
//                PathMatchingResourcePatternResolver pathMatchingResourcePatternResolver = new PathMatchingResourcePatternResolver();
//                Resource[] resources = pathMatchingResourcePatternResolver.getResources("classpath*:/mapper/**/*.xml");
//                List<Resource> list = new LinkedList<>();
//                if(resources.length > 0) {
//                    for (Resource resource : resources) {
//                        if (mapperNames.contains(resource.getFilename())) {
//                            list.add(resource);
//                        }
//                    }
//                }
//                sqlSessionFactoryBean.setMapperLocations(list.toArray(new Resource[0]));
//                // 全局配置
//                GlobalConfig globalConfig = new GlobalConfig();
//                globalConfig.setBanner(false);
//                sqlSessionFactoryBean.setGlobalConfig(globalConfig);
//                MybatisConfiguration configuration = sqlSessionFactoryBean.getConfiguration();
//                if (Objects.isNull(configuration)) {
//                    configuration = new MybatisConfiguration();
//                    sqlSessionFactoryBean.setConfiguration(configuration);
//                }
//                configuration.setLogImpl(StdOutImpl.class);
//                // 当有mybatisPlus环境下，设置插件
//                if (Objects.nonNull(mybatisPlusInterceptor)) {
//                    sqlSessionFactoryBean.setPlugins(mybatisPlusInterceptor);
//                }
//                return sqlSessionFactoryBean.getObject();
//            }
//            @Bean
//            public GeiSqlSessionFactoryBeanFactoryPostProcessor datasourcePostProcessor() {
//                return new GeiSqlSessionFactoryBeanFactoryPostProcessor();
//            }
//        }
//
//        @Configuration
//        @ConditionalOnProperty(prefix = "gei.datasource", name = "enable", havingValue = "false", matchIfMissing = true)
//        @MapperScan("com.cainiao.cntech.dsct.scp.gei.biz.dao.mapper")
//        public static class GeiNoCustomSqlSessionConfiguration { }
//    }
//    /**
//     * 业务配置类
//     */
//    @Configuration
//    public static class GeiBizConfiguration {
//        @Bean
//        public ScpGeiChildTaskManager scpGeiChildTaskManager() {
//            return new ScpGeiChildTaskManager();
//        }
//
//        @Bean
//        public ScpGeiDictManager scpGeiDictManager() {
//            return new ScpGeiDictManager();
//        }
//
//        @Bean
//        public ScpGeiTaskManager scpGeiTaskManager() {
//            return new ScpGeiTaskManager();
//        }
//
//        @Bean
//        public ScpGeiChildTaskService scpGeiChildTaskService() {
//            return new ScpGeiChildTaskServiceImpl();
//        }
//
//        @Bean
//        public ScpGeiTaskService scpGeiTaskService() {
//            return new ScpGeiTaskServiceImpl();
//        }
//    }
//
//    /**
//     * 核心配置类
//     */
//    @Configuration
//    public static class GeiCoreConfiguration {
//        @Bean
//        public DateDictFormatter dateDictFormatter() {
//            return new DateDictFormatter();
//        }
//
//        @Bean
//        public GeiDictResolver geiDictResolver() {
//            return new GeiDictResolver();
//        }
//
//        @Bean
//        public GeiEventPublisher geiEventPublisher() {
//            return new GeiEventPublisher();
//        }
//
//        @Bean
//        public GeiExcelManager excelManager() {
//            return new GeiExcelManager();
//        }
//
//        @Bean
//        public SliceDataManager sliceDataManager() {
//            return new SliceDataManager();
//        }
//
//        @Bean
//        public ExportPostProcessorManager exportPostProcessorManager() {
//            return new ExportPostProcessorManager();
//        }
//
//        @Bean
//        public SimpleExcelWriter simpleExcelWriter() {
//            return new SimpleExcelWriter();
//        }
//    }
//
//    /**
//     * 任务执行器配置类
//     */
//    @Configuration
//    public static class GeiTaskExecutorConfiguration {
//        @Bean
//        public ExportTemplateExecutor exportTemplateExecutor() {
//            return new ExportTemplateExecutor();
//        }
//
//        @Bean
//        public ImportTemplateExecutor importTemplateExecutor() {
//            return new ImportTemplateExecutor();
//        }
//
//        @Bean
//        public GeiChildTaskThreadPool geiChildTaskThreadPool(GeiPoolProperties geiPoolProperties) {
//            return new GeiChildTaskThreadPool(geiPoolProperties);
//        }
//
//        @Bean
//        public GeiTaskThreadPool geiTaskThreadPool(GeiPoolProperties geiPoolProperties) {
//            return new GeiTaskThreadPool(geiPoolProperties);
//        }
//
//        @Bean
//        public EIportAsyncTaskExecutor eIportAsyncTaskExecutor() {
//            return new EIportAsyncTaskExecutor();
//        }
//
//        @Bean
//        public GeiTaskResultHandler geiTaskResultHandler() {
//            return new GeiTaskResultHandler();
//        }
//    }
//
//    /**
//     * 配置文件配置类
//     */
//    @Configuration
//    public static class GeiPropertiesConfiguration {
//        @Bean
//        public GeiStorageProperties geiStorageProperties() {
//            return new GeiStorageProperties();
//        }
//
//        @Bean
//        public GeiPoolProperties geiPoolProperties() {
//            return new GeiPoolProperties();
//        }
//    }
//
//    /**
//     * 文件存储配置类
//     */
//    @Configuration
//    @ConditionalOnMissingBean(StorageTemplate.class)
//    public static class StorageConfiguration {
//        @Bean(destroyMethod = "destroy")
//        @ConditionalOnProperty(prefix = "gei.storage", name = "type", havingValue = "oss")
//        public StorageTemplate ossStorageTemplate(GeiStorageProperties geiStorageProperties) {
//            OSSStorageTemplate template = new OSSStorageTemplate();
//            template.initial(geiStorageProperties);
//            return template;
//        }
//
//        @Bean(destroyMethod = "destroy")
//        @ConditionalOnProperty(prefix = "gei.storage", name = "type", havingValue = "minio")
//        public StorageTemplate minioStorageTemplate(GeiStorageProperties geiStorageProperties) {
//            MinioStorageTemplate template = new MinioStorageTemplate();
//            template.initial(geiStorageProperties);
//            return template;
//        }
//
//        @Bean(destroyMethod = "destroy")
//        @ConditionalOnProperty(prefix = "gei.storage", name = "type", havingValue = "none", matchIfMissing = true)
//        public StorageTemplate noneStorageTemplate(GeiStorageProperties geiStorageProperties) {
//            NoneStorageTemplate template = new NoneStorageTemplate();
//            template.initial(geiStorageProperties);
//            return template;
//        }
//    }
//
//    @Configuration
//    public static class GeiExtConfiguration {
//        @Bean
//        public WriteConverterManager writeConverterManager() {
//            return new WriteConverterManager();
//        }
//
//        @Bean
//        @Order
//        public ListToStringExcelConverter listToStringExcelConverter() {
//            return new ListToStringExcelConverter();
//        }
//
//        @Bean
//        @Order
//        public LocalDateTimeToStringExcelConverter localDateTimeToStringExcelConverter() {
//            return new LocalDateTimeToStringExcelConverter();
//        }
//
//        @Bean
//        @Order
//        public LocalDateToStringExcelConverterExcel localDateToStringExcelConverterExcel() {
//            return new LocalDateToStringExcelConverterExcel();
//        }
//
//        @Bean
//        public DefaultSliceDataPostProcessor defaultSliceDataPostProcessor() {
//            return new DefaultSliceDataPostProcessor();
//        }
//
//        @Bean
//        public FormulaExportPostProcessor equationExportPostProcessor() {
//            return new FormulaExportPostProcessor();
//        }
//    }
//
//    @Configuration
//    public static class BasicConfiguration {
//        @Bean
//        public GeiQuickBasicManager geiQuickBasicManager() {
//            return new GeiQuickBasicManager();
//        }
//    }
//}
