package com.cainiao.cntech.dsct.scp.gei.support.template;

import com.cainiao.cntech.dsct.scp.gei.ext.validator.ImportValidator;
import com.cainiao.cntech.dsct.scp.gei.support.model.ImportTemplateVO;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-08 15:53
 * @description 导入数据校验器注册器
 */
public interface ImportDataValidatorRegister<I extends ImportTemplateVO> {
    /**
     * 注册行校验器
     */
    void registerRowValidator(ImportValidator<I> validator);

    /**
     * 注册字段校验器
     */
    void registerColValidator(ImportValidator<I> validator);

    /**
     * 获取行校验器
     */
    List<ImportValidator<I>> getRowValidators();

    /**
     * 获取字段校验器
     */
    List<ImportValidator<I>> getColValidators();
}
