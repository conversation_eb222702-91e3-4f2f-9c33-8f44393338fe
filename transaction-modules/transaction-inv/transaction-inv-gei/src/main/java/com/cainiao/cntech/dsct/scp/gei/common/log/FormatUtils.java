//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cainiao.cntech.dsct.scp.gei.common.log;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.MessageFormatter;

public class FormatUtils {
    public FormatUtils() {
    }

    public static String format(String message, Object... params) {
        StringBuilder sb = new StringBuilder(message == null ? "" : message);
        if (params != null && params.length != 0) {
            Object[] jsonArgv = new Object[params.length];

            for(int i = 0; i < params.length; ++i) {
                jsonArgv[i] = params[i] == null ? "null" : params[i].toString();
            }

            return MessageFormatter.arrayFormat(sb.toString(), jsonArgv).getMessage();
        } else {
            return sb.toString();
        }
    }

    public static String appendTraceId(String message, Object... params) {
        String traceId = StringUtils.isEmpty(TraceIdHolder.getTraceId()) ? "" : TraceIdHolder.getTraceId();
        return traceId + " " + format(message, params);
    }
}
