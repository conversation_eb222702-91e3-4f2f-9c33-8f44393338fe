package com.cainiao.cntech.dsct.scp.gei.support.quick.annotation;

import java.lang.annotation.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2025-01-17 14:50
 * @description 快速集成查询条件
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface QuickQuery {
    /**
     * 映射数据库表的DataObject的字段名，不要写数据库表字段名！
     */
    String value() default "";
    /**
     * 是否范围查询
     *  false 不当条件
     *  true 识别条件
     * >> 对应的请求体类型必须为数组，且下标0位是start， 1位是end
     */
    boolean isRange() default false;
    /**
     * 是否查询不为空的
     *  false 不当条件
     *  true 不为空的条件
     *  >> 对应的请求体类型必须boolean 或 number
     *      boolean = true， 并且 isNotNull = true， 则
     */
    boolean isNotNull() default false;
    /**
     * 是否查询为空的
     *  false 不当条件
     *  true 为空的条件
     */
    boolean isNull() default false;
}
