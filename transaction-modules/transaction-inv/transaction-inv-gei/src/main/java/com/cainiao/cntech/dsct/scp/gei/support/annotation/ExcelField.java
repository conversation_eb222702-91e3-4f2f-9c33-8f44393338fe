package com.cainiao.cntech.dsct.scp.gei.support.annotation;

import java.lang.annotation.*;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-05-08 15:05
 * @description excel模版映射实体字段注解
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelField {
    /**
     * 表头文字，列名
     */
    String value();

    /**
     * 设置匹配约束
     * 指定了匹配约束后，则标注此注解的属性只会在满足该约束下的字段创建MetaInfo
     */
    String[] mapping() default {};

    /**
     * 设置冻结该列，如果不设置当前字段，则表示不冻结 left - 左侧冻结 / right - 右侧冻结
     */
    String fixed() default "";

    /**
     * 是否隐藏
     */
    boolean hidden() default false;

    String tooltip() default "";

    int sort() default 0;

    /**
     * 导入时是否为一行的主键；
     * 如果设置为true，则会校验导入数据是否出现重复行
     */
    boolean importKey() default false;
}
