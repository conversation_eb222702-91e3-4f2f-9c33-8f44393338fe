package com.cainiao.cntech.dsct.scp.gei.ext.style;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Workbook;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $Id: CustomHeadCellStyleStrategy.java, v 0.1  2023-08-6,  0:18  Exp $
 */
public class CustomHeadCellStyleStrategy extends HorizontalCellStyleStrategy {
    private static final int MAX_COLUMN_WIDTH = 255;
    private final Map<Integer, Map<Integer, Integer>> cache = MapUtils.newHashMapWithExpectedSize(8);
    /**
     * 是否包含标题行
     */
    private final Boolean hasTitle;
    private CellStyle cellStyle;

    public CustomHeadCellStyleStrategy() {
        this.hasTitle = Boolean.TRUE;
    }

    public CustomHeadCellStyleStrategy(Boolean hasTitle) {
        this.hasTitle = hasTitle;
    }

    public CustomHeadCellStyleStrategy(WriteCellStyle headWriteCellStyle, List<WriteCellStyle> contentWriteCellStyleList, Boolean hasTitle) {
        super(headWriteCellStyle, contentWriteCellStyleList);
        this.hasTitle = hasTitle;
    }

    public CustomHeadCellStyleStrategy(WriteCellStyle headWriteCellStyle, WriteCellStyle contentWriteCellStyle, Boolean hasTitle) {
        super(headWriteCellStyle, contentWriteCellStyle);
        this.hasTitle = hasTitle;
    }

    @Override
    public int order() {
        // 策略在chain中的排序，默认都使用DefaultStyle，设置左右对齐不生效，指定顺序保障最后执行当前策略
        return Integer.MAX_VALUE - 10000;
    }

    @Override
    protected void setHeadCellStyle(CellWriteHandlerContext context) {
        // 设置第一行，说明行的高度
        if (hasTitle && context.getCell().getRow().getRowNum() == 0) {
            // 设置表头高度
            context.getRow().setHeight((short) 2800);
            // 设置表头对齐方式为居中对齐
        }
        CellStyle cellStyle = context.getCell().getCellStyle();
        cellStyle.setDataFormat((short) 49);
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
    }

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        super.afterCellDispose(context);
        setColumnWidth(context);
    }

    @Override
    protected void setContentCellStyle(CellWriteHandlerContext context) {
        Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();
        if (Objects.isNull(cellStyle)) {
            cellStyle = workbook.createCellStyle();
            cellStyle.setDataFormat((short) 49);
        }
        context.getCell().setCellStyle(cellStyle);
    }

    /**
     * 设置标题列单行显示
     *
     * @param writeSheetHolder
     * @param cellDataList
     * @param cell
     * @param isHead
     */
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
        writeSheetHolder.getParentWriteWorkbookHolder().setUseDefaultStyle(false);
        boolean needSetWidth = isHead || !CollectionUtils.isEmpty(cellDataList);
        if (!needSetWidth) {
            return;
        }
        Map<Integer, Integer> maxColumnWidthMap = cache.computeIfAbsent(writeSheetHolder.getSheetNo(), key -> new HashMap<>(16));
        Integer columnWidth = dataLength(cellDataList, cell, isHead);
        if (columnWidth < 0) {
            writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), 5 * 256);
            return;
        }
        columnWidth += 5;
        if (columnWidth > MAX_COLUMN_WIDTH) {
            columnWidth = MAX_COLUMN_WIDTH;
        }
        Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
        if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
            maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
            writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
        }
    }

    private Integer dataLength(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
        if (isHead) {
            // return cell.getStringCellValue().getBytes().length;
            return cell.getStringCellValue().length();
        }
        int dataLength = -1;
        for (WriteCellData<?> cellData : cellDataList) {
            CellDataTypeEnum type = cellData.getType();
            if (type == null) {
                continue;
            }
            int tempLength = -1;
            switch (type) {
                case STRING:
                    tempLength = BigDecimal.valueOf(cellData.getStringValue().length()).multiply(BigDecimal.valueOf(1.5)).intValue();
                    break;
                case BOOLEAN:
                    tempLength = cellData.getBooleanValue().toString().length() * 2;
                    break;
                case NUMBER:
                    tempLength = cellData.getNumberValue().toString().length() * 3;
                    break;
            }
            if (tempLength > dataLength) {
                dataLength = tempLength;
            }
        }
        return dataLength;
    }

    /**
     * Sets the column width when head create
     *
     * @param context
     */
    protected void setColumnWidth(CellWriteHandlerContext context) {
        // 第一行为说明，不设置column策略
        if (hasTitle && context.getRow().getRowNum() == 0) {
            return;
        }
        CellStyle cellStyle = context.getCell().getCellStyle();
        cellStyle.setDataFormat((short) 49);
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        setColumnWidth(context.getWriteSheetHolder(), context.getCellDataList(), context.getCell(), context.getHead());
    }
}
