//package com.cainiao.cntech.dsct.scp.gei.configuration;
//
//import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiPoolBlockQueue;
//import com.cainiao.cntech.dsct.scp.gei.common.enums.GeiPoolRejectedStrategy;
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
//import javax.annotation.PostConstruct;
//import java.util.Objects;
//import java.util.concurrent.TimeUnit;
//
///**
// * work for life
// *
// * <AUTHOR>
// * @date 2025-01-09 15:22
// * @description gei线程池配置
// */
//@Data
//@ConfigurationProperties(prefix = "gei.pool")
//public class GeiPoolProperties {
//    /**
//     * 核心线程数 占 cpu线程的比例
//     */
//    private Double coreRatio = 0.25D;
//    /**
//     * 最大线程数 占 cpu线程的比例
//     */
//    private Double maxRatio = 0.25D;
//    /**
//     * 核心线程数
//     */
//    private Integer core;
//    /**
//     * 最大线程数
//     */
//    private Integer max;
//    /**
//     * 空闲时间
//     */
//    private Long keepAlive = 10L;
//    /**
//     * 空闲时间单位
//     */
//    private TimeUnit keepAliveUnit = TimeUnit.SECONDS;
//
//    /**
//     * 阻塞队列长度，只有是GeiPoolBlockQueue.ARRAY时生效
//     */
//    private Integer queueSize = 32;
//    /**
//     * 阻塞队列
//     */
//    private GeiPoolBlockQueue queue = GeiPoolBlockQueue.LINKED;
//
//    /**
//     * 拒绝策略
//     */
//    private GeiPoolRejectedStrategy rejected = GeiPoolRejectedStrategy.CALLER;
//
//    @PostConstruct
//    public void init() {
//        if (Objects.isNull(getCore())) {
//            if (Objects.isNull(coreRatio)) {
//                coreRatio = 0.25D;
//                setCoreRatio(coreRatio);
//            }
//            setCore((int) Math.ceil(((double) Runtime.getRuntime().availableProcessors() * coreRatio)));
//        }
//        if (Objects.isNull(getMax())) {
//            if (Objects.isNull(maxRatio)) {
//                maxRatio = 0.25D;
//                setMaxRatio(maxRatio);
//            }
//            setMax((int) Math.ceil(((double) Runtime.getRuntime().availableProcessors() * maxRatio)));
//        }
//    }
//}
