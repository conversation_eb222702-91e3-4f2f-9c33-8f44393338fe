package cn.aliyun.ryytn.modules.inv.api.fcst.enums;

import cn.aliyun.ryytn.modules.inv.api.fcst.request.BaseFcstPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.common.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-10-20 15:20
 * @description
 */
@Getter
@AllArgsConstructor
public enum FcstDatePeriod {
    HOUR("h", "小时", null
            , () -> DateUtil.localDateTimeToString(LocalDateTime.now(), DateUtil.DATE_HOURS_FORMAT) + ":00:00"
            , () -> DateUtil.datetimeToLong(LocalDateTime.now().withSecond(0).withMinute(1))
            , request -> request.setStatDate(DateUtil.datetimeToLong(LocalDateTime.now()))),
    DAY("d", "日", null
            , () -> DateUtil.localDateToString(LocalDate.now())
            , () -> DateUtil.datetimeToLong(DateUtil.clearHms(LocalDateTime.now()))
            , request -> request.setStatDate(DateUtil.datetimeToLong(DateUtil.startOfDay()))),
    CUSTOM_DAY("customDay", "自定义天", "d"
            , () -> DateUtil.localDateToString(LocalDate.now())
            , () -> DateUtil.datetimeToLong(DateUtil.clearHms(LocalDateTime.now()))
            , request -> request.setStatDate(DateUtil.datetimeToLong(DateUtil.startOfDay()))),
    WEEK("w", "周", null
            , () -> DateUtil.localDateToString(DateUtil.getFirstDayOfWeek(LocalDate.now()))
            , () -> DateUtil.datetimeToLong(DateUtil.clearHms(DateUtil.getFirstDayOfWeek(LocalDateTime.now())))
            , request -> request.setStatDate(DateUtil.datetimeToLong(DateUtil.getFirstDayOfWeek(DateUtil.startOfDay())))),
    MONTH("m", "月", null
            , () -> DateUtil.localDateToString(DateUtil.getFirstDayOfMonth(LocalDate.now()))
            , () -> DateUtil.datetimeToLong(DateUtil.clearHms(DateUtil.getFirstDayOfMonth(LocalDateTime.now())))
            , request -> request.setStatDate(DateUtil.datetimeToLong(DateUtil.getFirstDayOfMonth(DateUtil.startOfDay())))),
    ;
    private final String code;
    private final String name;
    /**
     * 关联的日期维度code： customDay -> d
     */
    private final String relCode;
    private final Supplier<String> defaultQueryValue;
    private final Supplier<Long> defaultQueryValueLong;
    private final Consumer<BaseFcstPageQueryRequest> statDateHandler;
    public boolean isThis(String code){
        return getCode().equalsIgnoreCase(code);
    }
    public static FcstDatePeriod getByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (FcstDatePeriod fcstDatePeriod : values()) {
            if(fcstDatePeriod.code.equals(code)){
                return fcstDatePeriod;
            }
        }
        return null;
    }
    public static String getFinalPeriodCode(String code){
        FcstDatePeriod period = getByCode(code);
        if (Objects.isNull(period)) {
            return null;
        }
        return Objects.isNull(period.getRelCode()) ? period.getCode() : period.getRelCode();
    }

    public static LocalDateTime getNow(String code) {
        if (FcstDatePeriod.MONTH.isThis(code)) {
            return DateUtil.getFirstDayOfMonth(LocalDateTime.now()).withHour(0).withMinute(0).withSecond(0);
        } else if (FcstDatePeriod.WEEK.isThis(code)) {
            return DateUtil.getFirstDayOfWeek(LocalDateTime.now()).withHour(0).withMinute(0).withSecond(0);
        } else if (FcstDatePeriod.DAY.isThis(code)) {
            return LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        }
        return LocalDateTime.now();
    }

    public static List<LocalDateTime> getRangeByCode(String code, Long statDate) {
        LocalDateTime date = DateUtil.longToLocalDateTime(statDate);
        if (FcstDatePeriod.MONTH.isThis(code)) {
            return Arrays.asList(DateUtil.getFirstDayOfMonth(date), DateUtil.getLastDayOfMonth(date));
        } else if (FcstDatePeriod.WEEK.isThis(code)) {
            return Arrays.asList(DateUtil.getFirstDayOfWeek(date), DateUtil.getLastDayOfWeek(date));
        } else {
            return Arrays.asList(date, date);
        }
    }
}
