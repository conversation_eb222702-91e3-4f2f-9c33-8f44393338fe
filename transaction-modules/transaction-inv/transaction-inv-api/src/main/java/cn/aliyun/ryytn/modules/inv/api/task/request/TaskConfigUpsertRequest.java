package cn.aliyun.ryytn.modules.inv.api.task.request;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-22 10:05
 * @description
 */
@Data
@Accessors(chain = true)
public class TaskConfigUpsertRequest {
    /**
     * '任务类型编码' | '任务类型名称'
     */
    private String taskTypeCode;
    /**
     * 'dataworks任务编码' | 'dataworks任务名称'
     */
    private Long odpsCode;
    private String odpsName;
    /**
     * 是否为根任务
     */
    private Boolean rootOdps = Boolean.FALSE;
    /**
     * 调度日期表达式
     */
    private String dateExpr;
    /**
     * 在同一组任务类型内，不同日期表达式之间的执行顺序
     */
    private Long dateExprPriority;
    /**
     * 是否启用，1启用，0不启用
     */
    private Boolean enable = Boolean.TRUE;
}
