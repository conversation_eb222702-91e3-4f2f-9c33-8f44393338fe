package cn.aliyun.ryytn.modules.inv.api.task.core;


import cn.aliyun.ryytn.modules.inv.common.enums.WeekFreqTypeEnum;
import cn.aliyun.ryytn.modules.inv.common.model.OptionDTO;
import cn.aliyun.ryytn.modules.inv.common.utils.StringConstants;
import com.cainiao.cntech.dsct.scp.gei.common.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-29 10:00
 * @description 日期表达式解析器
 * 减n天： -1：减1天，-2：减2天 ...
 * 加n天： +1：加1天，+2：加2天 ...
 * 减n周的周几： -1_Monday：上周一，-1_Tuesday：上周二，-1_Wednesday：上周三，-1_Thursday：上周四，-1_Friday：上周五，-1_Saturday：上周六，-1_Sunday：上周日
 * 加n周的周几： +1_Monday：下周一，+1_Tuesday：下周二，+1_Wednesday：下周三，+1_Thursday：下周四，+1_Friday：下周五，+1_Saturday：下周六，+1_Sunday：下周日
 * 减n月的几号： -1_Month_1：上个月1号，-1_Month_2：上个月2号，-1_Month_3：上个月3号，-1_Month_4：上个月4号，-1_Month_5：上个月5号，-1_Month_6：上个月6号，-1_Month_7：上个月7号...
 * 加n月的几号： +1_Month_1：下个月1号，+1_Month_2：下个月2号，+1_Month_3：下个月3号，+1_Month_4：下个月4号，+1_Month_5：下个月5号，+1_Month_6：下个月6号，+1_Month_7：下个月7号...
 *  特别说明-1_Month_31 31就是月底
 */
public class DateExprResolver {
    private final static TimeZone TIME_ZONE = TimeZone.getTimeZone("Asia/Shanghai");

    private final static List<Function<String, LocalDateTime>> RESOLVERS = new ArrayList<>();

    static {
        // 减法解析器
        RESOLVERS.add(expr -> {
            if (!expr.startsWith(StringConstants.DASHED)) {
                return null;
            }
            try {
                Integer days = Integer.valueOf(expr);
                LocalDateTime now = LocalDateTime.now(TIME_ZONE.toZoneId());
                return DateUtil.plusDays(now, days);
            } catch (Exception e) {
                return null;
            }
        });
        // 加法解析器
        RESOLVERS.add(expr -> {
            if (!expr.startsWith(StringConstants.ADD)) {
                return null;
            }
            try {
                Integer days = Integer.valueOf(expr);
                LocalDateTime now = LocalDateTime.now(TIME_ZONE.toZoneId());
                return DateUtil.plusDays(now, days);
            } catch (Exception e) {
                return null;
            }
        });
        // 上n周的周几解析器
        RESOLVERS.add(expr -> {
            if (!expr.startsWith(StringConstants.DASHED)) {
                return null;
            }
            try {
                String[] exprs = expr.split(StringConstants.UNDERLINE);
                Integer subDays = Integer.valueOf(exprs[0]);
                WeekFreqTypeEnum week = WeekFreqTypeEnum.getByCode(exprs[1]);
                if (Objects.isNull(week)) {
                    return null;
                }
                LocalDateTime now = LocalDateTime.now(TIME_ZONE.toZoneId());
                now = DateUtil.plusWeeks(now, subDays);
                return calcWeek(now, week);
            } catch (Exception e) {
                return null;
            }
        });
        // 下n周的周几解析器
        RESOLVERS.add(expr -> {
            if (!expr.startsWith(StringConstants.ADD)) {
                return null;
            }
            try {
                String[] exprs = expr.split(StringConstants.UNDERLINE);
                Integer subDays = Integer.valueOf(exprs[0]);
                WeekFreqTypeEnum week = WeekFreqTypeEnum.getByCode(exprs[1]);
                if (Objects.isNull(week)) {
                    return null;
                }
                LocalDateTime now = LocalDateTime.now(TIME_ZONE.toZoneId());
                now = DateUtil.plusWeeks(now, subDays);
                return calcWeek(now, week);
            } catch (Exception e) {
                return null;
            }
        });
        // 上n月的几号解析器
        RESOLVERS.add(expr -> {
            if (!expr.startsWith(StringConstants.DASHED)) {
                return null;
            }
            try {
                String[] exprs = expr.split(StringConstants.UNDERLINE);
                Integer subDays = Integer.valueOf(exprs[0]);
                String monthMark = exprs[1];
                if (!StringUtils.equals("Month", monthMark)) {
                    return null;
                }
                Integer days = Integer.valueOf(exprs[2]);
                LocalDateTime now = LocalDateTime.now(TIME_ZONE.toZoneId());
                now = DateUtil.plusMonths(now, subDays);
                assert now != null;
                days = Math.min(now.toLocalDate().lengthOfMonth(), days);
                if (days <= 1) {
                    now = DateUtil.getFirstDayOfMonth(now);
                } else if (days >= 31) {
                    now = DateUtil.getLastDayOfMonth(now);
                } else {
                    now = now.withDayOfMonth(days);
                }
                return now;
            } catch (Exception e) {
                return null;
            }
        });
        // 下n月的几号解析器
        RESOLVERS.add(expr -> {
            if (!expr.startsWith(StringConstants.ADD)) {
                return null;
            }
            try {
                String[] exprs = expr.split(StringConstants.UNDERLINE);
                Integer subDays = Integer.valueOf(exprs[0]);
                String monthMark = exprs[1];
                if (!StringUtils.equals("Month", monthMark)) {
                    return null;
                }
                Integer days = Integer.valueOf(exprs[2]);
                LocalDateTime now = LocalDateTime.now(TIME_ZONE.toZoneId());
                now = DateUtil.plusMonths(now, subDays);
                assert now != null;
                days = Math.min(now.toLocalDate().lengthOfMonth(), days);
                if (days <= 1) {
                    now = DateUtil.getFirstDayOfMonth(now);
                } else if (days >= 31) {
                    now = DateUtil.getLastDayOfMonth(now);
                } else {
                    now = now.withDayOfMonth(days);
                }
                return now;
            } catch (Exception e) {
                return null;
            }
        });

    }

    public static LocalDateTime resolve(String expr) {
        for (Function<String, LocalDateTime> resolver : RESOLVERS) {
            LocalDateTime date = resolver.apply(expr);
            if (Objects.nonNull(date)) {
                return date;
            }
        }
        // 最终兜底
        LocalDateTime now = LocalDateTime.now(TIME_ZONE.toZoneId());
        return DateUtil.plusDays(now, -1);
    }

    private static LocalDateTime calcWeek(LocalDateTime dateTime, WeekFreqTypeEnum week) {
        if (Objects.isNull(dateTime) || Objects.isNull(week)) {
            return null;
        }
        int weekDays = week.getDays();
        int dateDays = dateTime.getDayOfWeek().getValue();
        return DateUtil.plusDays(dateTime, weekDays - dateDays);
    }

    public static void main(String[] args) {
        // test
        LocalDateTime resolve = resolve("-2_Month_30");
        System.out.println(resolve);
        resolve = resolve("-1");
        System.out.println(resolve);
    }

    public static List<OptionDTO> getCommonOptions() {
        List<OptionDTO> result = new LinkedList<>();
        result.add(OptionDTO.of("-0", "当天"));
        for (int i = 1; i <= 7; i++) {
            result.add(OptionDTO.of(String.format("-%s", i), String.format("减%s天", i)));
        }
        for (int i = 1; i <= 7; i++) {
            result.add(OptionDTO.of(String.format("+%s", i), String.format("加%s天", i)));
        }
        for (WeekFreqTypeEnum week : WeekFreqTypeEnum.values()) {
            result.add(OptionDTO.of(String.format("-0_%s", week.getCode()), String.format("本%s", week.getName())));
        }
        for (WeekFreqTypeEnum week : WeekFreqTypeEnum.values()) {
            result.add(OptionDTO.of(String.format("-1_%s", week.getCode()), String.format("上%s", week.getName())));
        }
        for (WeekFreqTypeEnum week : WeekFreqTypeEnum.values()) {
            result.add(OptionDTO.of(String.format("+1_%s", week.getCode()), String.format("下%s", week.getName())));
        }
        for (int i = 1; i <= 31; i++) {
            result.add(OptionDTO.of(String.format("-0_Month_%s", i), String.format("本月%s日", i)));
        }
        for (int i = 1; i <= 31; i++) {
            result.add(OptionDTO.of(String.format("-1_Month_%s", i), String.format("上月%s日", i)));
        }
        for (int i = 1; i <= 31; i++) {
            result.add(OptionDTO.of(String.format("+1_Month_%s", i), String.format("下月%s日", i)));
        }

        return result;
    }
}
