package cn.aliyun.ryytn.modules.inv.api.md;


import cn.aliyun.ryytn.modules.inv.api.md.request.PlanUnitPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.md.dto.PlanUnitDTO;

import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-09-25 14:48
 * @description
 */
public interface PlanUnitService {

    List<PlanUnitDTO> queryPageData(PlanUnitPageQueryRequest request);
    Long queryCount(PlanUnitPageQueryRequest request);

}
