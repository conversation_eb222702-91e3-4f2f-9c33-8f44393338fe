package cn.aliyun.ryytn.modules.inv.api.task.request;


import cn.aliyun.ryytn.modules.inv.common.dao.common.request.BasePageQueryRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/13 11:10
 * @description：任务管理request
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskCoverPageQueryRequest extends BasePageQueryRequest {

    /**
     * 任务Id
     */
    private List<Long> taskIds;

    /**
     * 物料
     */
    private List<String> material;

    /**
     * 仓库
     */
    private List<String> warehouse;

    /**
     * 范围
     */
    private String range;

    /**
     * 编码
     */
    private List<String> codes;

}
