package cn.aliyun.ryytn.modules.inv.api.transfer;

import cn.aliyun.ryytn.modules.inv.common.ability.meta.MetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.common.ability.meta.PageMetaInfoWrapper;
import cn.aliyun.ryytn.modules.inv.entity.transfer.request.TransferConfigMetaInsertRequest;
import cn.aliyun.ryytn.modules.inv.entity.transfer.request.TransferConfigQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.transfer.request.TransferPlanPageQueryRequest;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21 13:34
 * @description：
 */
public interface InvTransferPlanService {

    Long saveMetaConfig(TransferConfigQueryRequest request);

    MetaInfoWrapper pageLayoutQuery(TransferConfigQueryRequest request) throws JsonProcessingException;

    PageMetaInfoWrapper pageQueryData(TransferPlanPageQueryRequest request);

    // 下拉生产编码数组
    List<String> getProductionCodingList(String key);

    // 下拉调出物理仓数组
    List<String> getOutPhysicalWarehouseNameList(String key);

    // 下拉调出逻辑仓数组
    List<String> getOutLogicalWarehouseNameList(String key);

    // 下拉调入物理仓数组
    List<String> getInLogicalWarehouseNameList(String key);

    // 下拉调入逻辑仓数组
    List<String> getInPhysicalWarehouseNameList(String key);

    // 下拉有效期规则数组
    List<String> getValidityRuleList(String key);

    // 下拉运输方式数组
    List<String> getModeOfTransportList(String key);

    // 下拉预警状态数组
    List<String> getAlarmStatusList(String key);

}
