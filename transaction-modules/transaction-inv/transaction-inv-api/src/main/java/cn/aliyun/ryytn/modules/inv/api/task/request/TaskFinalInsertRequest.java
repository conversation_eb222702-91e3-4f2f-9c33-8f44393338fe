package cn.aliyun.ryytn.modules.inv.api.task.request;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/11/13 11:10
 * @description：任务管理新建request
 */
@Data
public class TaskFinalInsertRequest {
    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 影响范围
     */
    private BigDecimal impactRange;

    /**
     * 状态
     */
    private String status;

    /**
     * 调度类型
     */
    private String schedulingType;
    /**
     * 调度时间
     */
    private LocalDateTime schedulingTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 操作人编码
     */
    private String operatorCode;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
}