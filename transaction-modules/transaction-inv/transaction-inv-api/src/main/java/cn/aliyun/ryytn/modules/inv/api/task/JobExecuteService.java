package cn.aliyun.ryytn.modules.inv.api.task;



import cn.aliyun.ryytn.modules.inv.api.task.enums.TaskStatusEnum;
import cn.aliyun.ryytn.modules.inv.api.task.request.JobExecuteQueryRequest;
import cn.aliyun.ryytn.modules.inv.entity.task.dto.TaskManagementDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-20 14:31
 * @description 任务运行服务
 */
public interface JobExecuteService {
    /**
     * 执行当前任务
     *
     * @param requests
     * @return
     */
    Long execute(List<JobExecuteQueryRequest> requests);

    /**
     * 修改基础任务
     *
     * @param taskId
     * @param taskStatus
     * @param errorMsg
     * @return
     */
    Long updateBaseTask(Long taskId, TaskStatusEnum taskStatus, String errorMsg, LocalDateTime triggerStartTime, LocalDateTime triggerEndTime);

    /**
     * 根据任务状态查询任务信息
     */
    List<TaskManagementDTO> queryBaseTaskByStatus(List<TaskStatusEnum> taskStatus);

    /**
     * 对待运行状态的任务 取消执行
     *
     * @param taskId
     * @return
     */
    Boolean cancelExecute(Long taskId);

    /**
     * 生成系统调度任务信息
     *
     * @return
     */
    Boolean genSysTaskBaseInfo(String taskType);

    /**
     * 更新系统任务的完成时间
     *
     * @return
     */
    Boolean updateSysTask(String taskType, TaskStatusEnum updateBefore, TaskStatusEnum updateAfter, LocalDateTime triggerStartTime, LocalDateTime triggerEndTime);

    Long rerunDistribution();
}
