package cn.aliyun.ryytn.modules.inv.api.task.enums;

import cn.aliyun.ryytn.modules.inv.api.md.request.PlanUnitPageQueryRequest;
import cn.aliyun.ryytn.modules.inv.api.task.request.TaskCoverPageQueryRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

/**
 * work for life
 *
 * <AUTHOR>
 * @date 2024-11-27 17:37
 * @description 任务覆盖类型
 */
@Getter
@AllArgsConstructor
public enum TaskCoverEnum {

    PRODUCT("product", "按产品", request -> {
        PlanUnitPageQueryRequest query = new PlanUnitPageQueryRequest();
        query.setPaging(false);
        query.setProduct(request.getCodes());
        return query;
    }),
    SHOP("shop", "按门店", request -> {
        PlanUnitPageQueryRequest query = new PlanUnitPageQueryRequest();
        query.setPaging(false);
        query.setShop(request.getCodes());
        return query;
    }),
    ;
    private final String code;
    private final String name;
    private final Function<TaskCoverPageQueryRequest, PlanUnitPageQueryRequest> unitQueryGetter;

    public static TaskCoverEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (TaskCoverEnum coverEnum : values()) {
            if (coverEnum.isThis(code)) {
                return coverEnum;
            }
        }
        return null;
    }

    public boolean isThis(String code) {
        return getCode().equalsIgnoreCase(code);
    }

    public PlanUnitPageQueryRequest getUnitQuery(TaskCoverPageQueryRequest request) {
        return getUnitQueryGetter().apply(request);
    }
}
