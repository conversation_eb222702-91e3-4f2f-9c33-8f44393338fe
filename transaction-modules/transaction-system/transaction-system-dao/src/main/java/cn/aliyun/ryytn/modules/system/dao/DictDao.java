package cn.aliyun.ryytn.modules.system.dao;

import java.util.List;

import cn.aliyun.ryytn.common.entity.DictData;
import cn.aliyun.ryytn.common.entity.DictType;

/**
 * @Description 字典Dao
 * <AUTHOR>
 * @date 2023/9/25 11:11
 */
public interface DictDao
{
    /**
     *
     * @Description 查询字典类型列表
     * @param dictType
     * @return List<DictType>
     * <AUTHOR>
     * @date 2023年9月25日 上午10:43:50
     */
    List<DictType> queryDictTypeList(DictType dictType);

    /**
     *
     * @Description 查询字典类型详情
     * @param dictTypeId
     * @return String
     * <AUTHOR>
     * @date 2023年9月25日 上午10:44:14
     */
    DictType queryDictTypeDetail(String dictTypeId);

    /**
     *
     * @Description 根据字典类型查询字典类型数据
     * @param dictType
     * @return DictType
     * <AUTHOR>
     * @date 2022年6月13日 上午9:58:06
     */
    DictType queryDictTypeByDictType(String dictType);

    /**
     *
     * @Description 校验字典类型是否唯一
     * @param dictType
     * @return int
     * <AUTHOR>
     * @date 2023年9月25日 下午2:55:00
     */
    int checkDictTypeUnique(String dictType);

    /**
     *
     * @Description 新增字典类型
     * @param dictType
     * <AUTHOR>
     * @date 2023年9月25日 上午11:20:21
     */
    void addDictType(DictType dictType);

    /**
     *
     * @Description 恢复字典类型
     * @param dictType
     * <AUTHOR>
     * @date 2023年9月25日 上午11:20:21
     */
    void recoveryDictType(DictType dictType);

    /**
     *
     * @Description 修改字典类型
     * @param dictType
     * <AUTHOR>
     * @date 2023年9月25日 上午11:20:21
     */
    void updateDictType(DictType dictType);

    /**
     *
     * @Description 查询预置的字典类型数量
     * @param dictTypeIdList
     * @return int
     * <AUTHOR>
     * @date 2023年9月25日 下午2:23:20
     */
    int queryInitDictTypeCount(List<String> dictTypeIdList);

    /**
     *
     * @Description 逻辑删除字典类型
     * @param accountName
     * @param dictTypeIdList
     * <AUTHOR>
     * @date 2023年9月25日 下午2:32:21
     */
    void deleteDictType(String accountName, List<String> dictTypeIdList);

    /**
     *
     * @Description 查询所有字典数据列表
     * @return List<DictData>
     * <AUTHOR>
     * @date 2023年9月25日 下午3:31:34
     */
    List<DictData> queryAllDictDataList();

    /**
     *
     * @Description 查询字典数据列表
     * @param dictData
     * @return List<DictData>
     * <AUTHOR>
     * @date 2023年9月25日 下午4:05:10
     */
    List<DictData> queryDictDataList(DictData dictData);

    /**
     *
     * @Description 查询字典数据详情
     * @param dictId
     * @return DictData
     * <AUTHOR>
     * @date 2023年9月25日 下午4:25:55
     */
    DictData queryDictDataDetail(String dictId);

    /**
     *
     * @Description 查询字典类型是否存在
     * @param dictType
     * @return int
     * <AUTHOR>
     * @date 2022年6月12日 上午10:58:00
     */
    int queryDictTypeExists(String dictType);

    /**
     *
     * @Description 根据字典数据查询字典数据
     * @param dictData
     * @return DictData
     * <AUTHOR>
     * @date 2022年6月13日 上午10:01:32
     */
    DictData queryDictDataByDictData(DictData dictData);

    /**
     *
     * @Description 查询父字典数据
     * @param parentId
     * @return DictData
     * <AUTHOR>
     * @date 2023年6月19日 下午4:49:11
     */
    DictData queryParentDictData(String parentId);

    /**
     *
     * @Description 恢复字典数据
     * @param dictData
     * <AUTHOR>
     * @date 2023年9月25日 下午4:49:51
     */
    void recoveryDictData(DictData dictData);

    /**
     *
     * @Description 新增字典数据
     * @param dictData
     * <AUTHOR>
     * @date 2023年9月25日 下午4:55:42
     */
    void addDictData(DictData dictData);

    /**
     *
     * @Description 修改字典数据
     * @param dictData
     * <AUTHOR>
     * @date 2023年9月25日 下午5:04:01
     */
    void updateDictData(DictData dictData);

    /**
     *
     * @Description 查询初始化字典数据数量
     * @param dictIdList
     * @return int
     * <AUTHOR>
     * @date 2023年9月25日 下午5:19:28
     */
    int queryInitDictDataCount(List<String> dictIdList);

    /**
     *
     * @Description 删除字典数据
     * @param accountName
     * @param dictIdList
     * <AUTHOR>
     * @date 2022年6月12日 上午11:05:54
     */
    void deleteDictData(String accountName, List<String> dictIdList);

    /**
     *
     * @Description 校验字典数据是否唯一
     * @param dictData
     * @return int
     * <AUTHOR>
     * @date 2022年6月12日 上午11:25:53
     */
    int checkDictDataUnique(DictData dictData);

    /**
     *
     * @Description 根据字典类型和编码删除字典数据
     * @param dictData
     * <AUTHOR>
     * @date 2022年7月8日 下午4:25:39
     */
    void deleteDictDataByDictTypeCode(DictData dictData);

    /**
     *
     * @Description 修改字典是否叶子节点
     * @param dictData
     * <AUTHOR>
     * @date 2023年6月29日 上午10:34:40
     */
    void updateDictDataLeafFlag(DictData dictData);

}
