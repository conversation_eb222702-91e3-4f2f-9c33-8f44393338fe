package cn.aliyun.ryytn.modules.system.dao;

import java.util.List;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.OADepartment;
import cn.aliyun.ryytn.common.entity.OAJobTitle;
import cn.aliyun.ryytn.common.entity.OAPerson;
import cn.aliyun.ryytn.common.entity.OASubCompany;

/**
 * @Description 分部（分公司）Dao
 * <AUTHOR>
 * @date 2023/9/27 10:16
 */
public interface OADao
{
    /**
     *
     * @Description 新增分部（分公司）数据，replace into，增量更新
     * @param oaSubCompanyList
     * <AUTHOR>
     * @date 2023年09月27日 10:18
     */
    void addOASubCompany(List<OASubCompany> oaSubCompanyList);

    /**
     *
     * @Description 清理失效的分部（分公司）数据，同步时间始终是N天之前的数据，N可配置
     * 由于OA只提供了分页查询接口做同步，所以通过此方式删除OA已删除的数据
     * 配置的N天作为数据的保护期
     * @param expireSyncTime
     * <AUTHOR>
     * @date 2023年09月27日 15:13
     */
    void deleteOASubCompanyExpire(Long expireSyncTime);

    /**
     *
     * @Description 新增部门数据，replace into，增量更新
     * @param oaDepartmentList
     * <AUTHOR>
     * @date 2023年09月27日 14:10
     */
    void addOADepartment(List<OADepartment> oaDepartmentList);

    /**
     *
     * @Description 清理失效的部门数据，同步时间始终是N天之前的数据，N可配置
     * 由于OA只提供了分页查询接口做同步，所以通过此方式删除OA已删除的数据
     * 配置的N天作为数据的保护期
     * @param expireSyncTime
     * <AUTHOR>
     * @date 2023年09月27日 15:13
     */
    void deleteOADepartmentExpire(Long expireSyncTime);

    /**
     *
     * @Description 新增岗位数据，replace into，增量更新
     * @param oaJobTitleList
     * <AUTHOR>
     * @date 2023年09月27日 14:13
     */
    void addOAJobTitle(List<OAJobTitle> oaJobTitleList);

    /**
     *
     * @Description 清理失效的岗位数据，同步时间始终是N天之前的数据，N可配置
     * 由于OA只提供了分页查询接口做同步，所以通过此方式删除OA已删除的数据
     * 配置的N天作为数据的保护期
     * @param expireSyncTime
     * <AUTHOR>
     * @date 2023年09月27日 15:13
     */
    void deleteOAJobTitleExpire(Long expireSyncTime);

    /**
     *
     * @Description 新增人员数据，replace into，增量更新
     * @param oaPersonList
     * <AUTHOR>
     * @date 2023年09月27日 14:13
     */
    void addOAPerson(List<OAPerson> oaPersonList);

    /**
     *
     * @Description 清理失效的人员账号数据，同步时间始终是N天之前的数据，N可配置
     * 由于OA只提供了分页查询接口做同步，所以通过此方式删除OA已删除的数据
     * 配置的N天作为数据的保护期
     * @param expireSyncTime
     * <AUTHOR>
     * @date 2023年09月27日 15:13
     */
    void deleteAccountExpire(Long expireSyncTime);

    /**
     *
     * @Description 清理失效的人员数据，同步时间始终是N天之前的数据，N可配置
     * 由于OA只提供了分页查询接口做同步，所以通过此方式删除OA已删除的数据
     * 配置的N天作为数据的保护期
     * @param expireSyncTime
     * <AUTHOR>
     * @date 2023年09月27日 15:13
     */
    void deleteOAPersonExpire(Long expireSyncTime);

    /**
     *
     * @Description 查询当前批次同步的OA人员中，新增OA人员的账号数据，返回本地账号结构的结果集
     * @param oaPersonList
     * @return List<Account>
     * <AUTHOR>
     * @date 2023年09月28日 15:05
     */
    List<Account> queryAddAccountList(List<OAPerson> oaPersonList);

    /**
     *
     * @Description 查询当前批次同步的OA人员中，修改OA人员的账号数据，返回本地账号结构的结果集
     * @param oaPersonList
     * @return List<Account>
     * <AUTHOR>
     * @date 2023年09月28日 15:05
     */
    List<Account> queryUpdateAccountList(List<OAPerson> oaPersonList);

    /**
     *
     * @Description 查询所有的OA部门列表
     * @return List<OASubCompany>
     * <AUTHOR>
     * @date 2023年10月07日 14:07
     */
    List<OADepartment> queryAllOADepartmentList();
}
