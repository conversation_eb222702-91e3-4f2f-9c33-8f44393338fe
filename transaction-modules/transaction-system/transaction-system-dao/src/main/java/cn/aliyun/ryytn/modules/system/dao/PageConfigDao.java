package cn.aliyun.ryytn.modules.system.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.common.entity.Page;
import cn.aliyun.ryytn.common.entity.PageConfig;

/**
 * @Description 页面配置Dao
 * <AUTHOR>
 * @date 2023年10月10日 17:10
 */
public interface PageConfigDao
{
    /**
     *
     * @Description 查询页面配置列表
     * @return List<PageConfig>
     * <AUTHOR>
     * @date 2023年10月10日 17:10
     */
    List<PageConfig> queryPageConfigList(PageConfig pageConfig);

    /**
     *
     * @Description 批量插入页面配置
     * @param pageConfigList
     * <AUTHOR>
     * @date 2023年10月10日 17:10
     */
    void batchAddPageConfig(List<PageConfig> pageConfigList);

    /**
     *
     * @Description 条件删除页面配置
     * @param pageId
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月11日 11:30
     */
    void deleteByCondition(String pageId);

    /**
     *
     * @Description 更新页面表
     * @param page
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月12日 10:30
     */
    void updatePage(Page page);

    /**
     *
     * @Description 查询页面表是否合计字段
     * @param pageId
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月12日 10:30
     */
    Integer getPageSumFlag(@Param("pageId") String pageId);

}
