package cn.aliyun.ryytn.modules.system.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.common.entity.Account;

/**
 * @Description 账号Dao
 * <AUTHOR>
 * @date 2023/9/28 15:18
 */
public interface AccountDao
{
    /**
     *
     * @Description 批量新增账号
     * @param accountList
     * <AUTHOR>
     * @date 2023年09月28日 15:19
     */
    void batchAddAccount(List<Account> accountList);

    /**
     *
     * @Description 批量修改账号
     * @param accountList
     * <AUTHOR>
     * @date 2023年09月28日 15:30
     */
    void batchUpdateAccount(List<Account> accountList);

    /**
     *
     * @Description 查询账号列表
     * @param account
     * @return List<Account>
     * <AUTHOR>
     * @date 2023年09月28日 18:28
     */
    List<Account> queryAccountList(Account account);

    /**
     *
     * @Description 根据账号编号查询账号
     * @param id
     * @return Account
     * <AUTHOR>
     * @date 2023年09月28日 17:48
     */
    Account queryAccountById(String id);

    /**
     *
     * @Description 查询登录账号是否重复
     * @param account
     * @return int
     * <AUTHOR>
     * @date 2023年09月28日 17:51
     */
    int queryLoginIdRepeat(Account account);

    /**
     *
     * @Description 新增账号
     * @param account
     * <AUTHOR>
     * @date 2023年09月28日 15:19
     */
    void addAccount(Account account);

    /**
     *
     * @Description 修改账号，仅修改姓名、工号、描述
     * @param account
     * <AUTHOR>
     * @date 2023年09月28日 15:30
     */
    void updateAccount(Account account);

    /**
     *
     * @Description 修改账号状态
     * @param account
     * <AUTHOR>
     * @date 2023年09月28日 18:04
     */
    void updateAccountStatus(Account account);

    /**
     *
     * @Description 修改账号密码
     * @param account
     * <AUTHOR>
     * @date 2023年09月28日 18:04
     */
    void updateAccountPassword(Account account);

    /**
     *
     * @Description 重置账号密码
     * @param id
     * @param password
     * <AUTHOR>
     * @date 2023年09月28日 18:13
     */
    void resetAccountPassword(@Param("id") String id, @Param("password") String password);

    /**
     *
     * @Description 删除账号
     * @param id
     * <AUTHOR>
     * @date 2023年09月28日 18:25
     */
    void deleteAccount(String id);

    /**
     *
     * @Description 删除账号角色关联关系
     * @param id
     * <AUTHOR>
     * @date 2023年09月28日 18:27
     */
    void deleteAccountRole(String id);
}
