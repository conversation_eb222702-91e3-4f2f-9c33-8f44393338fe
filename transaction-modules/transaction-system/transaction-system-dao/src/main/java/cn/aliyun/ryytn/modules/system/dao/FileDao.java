package cn.aliyun.ryytn.modules.system.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.common.entity.ServiceFile;
import cn.aliyun.ryytn.common.entity.ServiceFileRef;

/**
 *
 * @Description 文件Dao
 * <AUTHOR>
 * @date 2023年9月19日 下午3:11:58
 */
public interface FileDao
{
    /**
     *
     * @Description 新增文件入库
     * @param serviceFile
     * <AUTHOR>
     * @date 2023年9月22日 下午5:15:30
     */
    void addServiceFile(ServiceFile serviceFile);

    /**
     *
     * @Description 批量新增文件入库
     * @param serviceFile
     * <AUTHOR>
     * @date 2023年9月22日 下午5:15:30
     */
    void addServiceFileList(List<ServiceFile> serviceFile);

    /**
     *
     * @Description 查询文件
     * @param fileId
     * @return ServiceFile
     * <AUTHOR>
     * @date 2023年9月22日 下午5:15:43
     */
    ServiceFile queryServiceFile(String fileId);

    /**
     *
     * @Description 查询失效文件编号列表
     * @param expireTime
     * @return List<String>
     * <AUTHOR>
     * @date 2023年5月19日 下午3:20:48
     */
    List<String> queryExpireServiceFile(String expireTime);

    /**
     *
     * @Description 查询存在业务关联的文件数量
     * @param fileIdList
     * @return int
     * <AUTHOR>
     * @date 2023年09月22日 15:05
     */
    int queryServiceRefExists(List<String> fileIdList);

    /**
     *
     * @Description 删除文件
     * @param fileIdList
     * <AUTHOR>
     * @date 2023年9月22日 下午5:40:27
     */
    void deleteServiceFile(List<String> fileIdList);

    /**
     *
     * @Description 新增业务文件关联关系表，用于业务实体仅绑定1个文件的场景
     * @param serviceFileRef
     * <AUTHOR>
     * @date 2023年6月7日 下午12:56:32
     */
    void addServiceFileRef(ServiceFileRef serviceFileRef);

    /**
     *
     * @Description 批量新增业务文件关联关系表，用于业务实体绑定多个文件的场景
     * @param serviceFileRef
     * <AUTHOR>
     * @date 2023年6月7日 下午12:56:32
     */
    void addServiceFileRefList(List<ServiceFileRef> serviceFileRefList);

    /**
     *
     * @Description 根据业务编号删除业务文件关联关系，用于业务实体绑定多个文件时，修改文件场景
     * @param serviceId
     * @param serviceType
     * <AUTHOR>
     * @date 2023年6月7日 下午12:59:14
     */
    void deleteFileRefByService(@Param("serviceId") String serviceId, @Param("serviceType") String serviceType);

    /**
     *
     * @Description 根据业务编号批量删除业务文件关联关系，用于业务实体绑定多个文件时，删除业务数据的场景
     * @param serviceIdList
     * @param serviceType
     * <AUTHOR>
     * @date 2023年6月7日 下午12:59:14
     */
    void deleteFileRefByServiceList(@Param("serviceIdList") List<String> serviceIdList, @Param("serviceType") String serviceType);

    /**
     *
     * @Description 修改业务文件关联关系，用于业务实体仅绑定1个文件时，修改文件的场景
     * @param serviceFileRef
     * <AUTHOR>
     * @date 2023年6月7日 下午12:59:14
     */
    void updateServiceFileRef(ServiceFileRef serviceFileRef);
}
