package cn.aliyun.ryytn.modules.system.dataqdao;

import java.util.List;

import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import cn.aliyun.ryytn.modules.system.entity.vo.SkuConditionVo;

/**
 * @Description dataq 产品Dao
 * <AUTHOR>
 * @date 2024/4/22 10:39
 */
public interface DataqSkuDao
{
    /**
     *
     * @Description 查询产品列表
     * dataq过滤器原因，导致浮点数字段（如净重、体积等字段无法获取），修改为直接查询数据库
     * @param skuConditionVo
     * @return List<SkuDto>
     * <AUTHOR>
     * @date 2024年04月22日 10:39
     */
    List<SkuDto> querySkuList(SkuConditionVo skuConditionVo);

    /**
     *
     * @Description 根据生产SKU查询销售SKU编号集合
     * @param productionCode
     * @return List<String>
     * <AUTHOR>
     * @date 2024年04月25日 20:03
     */
    List<String> querySaleSkuByProductionSku(String productionCode);
}
