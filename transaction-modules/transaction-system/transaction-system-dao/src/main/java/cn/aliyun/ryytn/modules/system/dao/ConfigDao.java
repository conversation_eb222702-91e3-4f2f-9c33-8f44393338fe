package cn.aliyun.ryytn.modules.system.dao;

import java.util.List;

import cn.aliyun.ryytn.common.entity.SystemConfig;
import cn.aliyun.ryytn.common.entity.SystemConfigCategory;

/**
 *
 * @Description 系统参数配置Dao
 * <AUTHOR>
 * @date 2023/9/22 19:01
 */
public interface ConfigDao
{
    /**
     *
     * @Description 查询系统配置类型列表
     * @return List<SystemConfigCategory>
     * <AUTHOR>
     * @date 2023年9月22日 下午4:34:36
     */
    List<SystemConfigCategory> querySystemConfigCategoryList();

    /**
     *
     * @Description 查询系统配置列表
     * @param systemConfigVo
     * @return List<SystemConfig>
     * <AUTHOR>
     * @date 2023年9月22日 下午4:34:55
     */
    List<SystemConfig> querySystemConfigList(SystemConfig systemConfig);

    /**
     *
     * @Description 查询系统配置详情
     * @param systemConfig
     * @return SystemConfig
     * <AUTHOR>
     * @date 2023年9月22日 下午4:54:14
     */
    SystemConfig querySystemConfigDetail(SystemConfig systemConfig);

    /**
     *
     * @Description 修改系统配置
     * @param systemConfig
     * <AUTHOR>
     * @date 2023年9月22日 下午4:35:10
     */
    void updateSystemConfig(SystemConfig systemConfig);

    /**
     *
     * @Description 批量修改系统配置
     * @param systemConfigList
     * <AUTHOR>
     * @date 2023年9月22日 下午5:28:38
     */
    void batchUpdateSystemConfig(List<SystemConfig> systemConfigList);

    /**
     *
     * @Description 查询全部系统配置列表
     * @return List<SystemConfig>
     * <AUTHOR>
     * @date 2023年9月22日 下午4:34:55
     */
    List<SystemConfig> queryAllSystemConfigList();

    /**
     *
     * @Description 查询系统配置详情列表
     * @param SystemConfigList
     * @return List<SystemConfig>
     * <AUTHOR>
     * @date 2023年8月8日 上午11:08:58
     */
    List<SystemConfig> querySystemConfigDetailList(List<SystemConfig> SystemConfigList);
}
