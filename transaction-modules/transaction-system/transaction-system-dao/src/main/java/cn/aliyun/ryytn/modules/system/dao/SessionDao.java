package cn.aliyun.ryytn.modules.system.dao;

import java.util.List;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.Button;
import cn.aliyun.ryytn.common.entity.Page;

/**
 * @Description 登录Dao
 * <AUTHOR>
 * @date 2023/10/10 10:31
 */
public interface SessionDao
{
    /**
     *
     * @Description 根据登录账号查询账号
     * @param loginId
     * @return Account
     * <AUTHOR>
     * @date 2023年10月10日 16:00
     */
    Account queryAccountByLoginId(String loginId);

    /**
     *
     * @Description 根据账号查询关联的角色编号列表
     * @param account
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月10日 16:03
     */
    List<String> queryRoleIdListByAccount(Account account);

    /**
     *
     * @Description 根据账号查询已授权的菜单
     * @param account
     * @return List<Page>
     * <AUTHOR>
     * @date 2023年10月10日 16:03
     */
    List<Page> queryPageListByAccount(Account account);

    /**
     *
     * @Description 根据账号查询已授权的按钮
     * @param account
     * @return List<Button>
     * <AUTHOR>
     * @date 2023年10月10日 16:03
     */
    List<Button> queryButtonListByAccount(Account account);

    /**
     *
     * @Description 根据账号查询已授权的渠道编号
     * @param account
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月10日 16:03
     */
    List<String> queryChannelIdListByAccount(Account account);

    /**
     *
     * @Description 根据账号查询已授权的产品品类编号
     * @param account
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月10日 16:03
     */
    List<String> queryCategoryIdListByAccount(Account account);

    /**
     *
     * @Description 根据账号查询已授权的工厂编号
     * @param account
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月10日 16:03
     */
    List<String> queryFactoryIdListByAccount(Account account);

    /**
     *
     * @Description 根据账号查询已授权的仓库编号
     * @param account
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月10日 16:03
     */
    List<String> queryDepositoryIdListByAccount(Account account);
}
