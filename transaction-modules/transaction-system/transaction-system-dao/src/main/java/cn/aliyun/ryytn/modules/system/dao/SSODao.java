package cn.aliyun.ryytn.modules.system.dao;

import org.apache.ibatis.annotations.Param;

import cn.aliyun.ryytn.modules.system.entity.dto.ThirdpartSystem;

/**
 * @Description 单点登录数据操作
 * <AUTHOR>
 * @date 2023/10/20 14:21
 */
public interface SSODao
{
    /**
     *
     * @Description 查询第三方系统
     * @param name
     * @param authCode
     * @return ThirdpartSystem
     * <AUTHOR>
     * @date 2023年10月20日 14:43
     */
    ThirdpartSystem queryThirdpartSystem(@Param("name") String name, @Param("authCode") String authCode);
}
