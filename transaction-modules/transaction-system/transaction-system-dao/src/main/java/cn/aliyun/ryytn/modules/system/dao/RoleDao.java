package cn.aliyun.ryytn.modules.system.dao;

import java.util.List;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.Role;
import cn.aliyun.ryytn.modules.system.entity.dto.AccountRole;
import cn.aliyun.ryytn.modules.system.entity.dto.RoleButton;
import cn.aliyun.ryytn.modules.system.entity.dto.RoleChannel;
import cn.aliyun.ryytn.modules.system.entity.dto.RoleDepository;
import cn.aliyun.ryytn.modules.system.entity.dto.RoleFactory;
import cn.aliyun.ryytn.modules.system.entity.dto.RolePage;
import cn.aliyun.ryytn.modules.system.entity.dto.RoleProductCategory;
import cn.aliyun.ryytn.modules.system.entity.vo.AccountRoleVo;
import cn.aliyun.ryytn.modules.system.entity.vo.RoleConditionVo;

/**
 * @Description 角色Dao
 * <AUTHOR>
 * @date 2023/9/28 15:18
 */
public interface RoleDao
{
    /**
     *
     * @Description 查询角色列表
     * @param roleConditionVo
     * @return List<Role>
     * <AUTHOR>
     * @date 2023年10月08日 16:18
     */
    List<Role> queryRoleList(RoleConditionVo roleConditionVo);

    /**
     *
     * @Description 查询角色详情
     * @param roleId
     * @return Role
     * <AUTHOR>
     * @date 2023年10月08日 17:12
     */
    Role queryRoleDetail(String roleId);

    /**
     *
     * @Description 校验角色是否已存在
     * @param role
     * @return int
     * <AUTHOR>
     * @date 2023年10月09日 9:50
     */
    int queryRoleExists(Role role);

    /**
     *
     * @Description 新增角色
     * @param role
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    void addRole(Role role);

    /**
     *
     * @Description 修改角色
     * @param role
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    void updateRole(Role role);

    /**
     *
     * @Description 删除角色
     * @param id
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    void deleteRole(String id);

    /**
     *
     * @Description 删除指定角色关联账号
     * @param id
     * <AUTHOR>
     * @date 2023年10月08日 17:47
     */
    void deleteAccountRoleByRoleId(String id);

    /**
     *
     * @Description 修改角色状态
     * @param role
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    void updateRoleStatus(Role role);

    /**
     *
     * @Description 查询未分配的账号列表
     * @param AccountRoleVo
     * @return List<Account>
     * <AUTHOR>
     * @date 2023年10月09日 14:36
     */
    List<Account> queryUnbindAccountList(AccountRoleVo accountRoleVo);

    /**
     *
     * @Description 查询已分配的账号列表
     * @param AccountRoleVo
     * @return List<Account>
     * <AUTHOR>
     * @date 2023年10月09日 14:36
     */
    List<Account> queryBindedAccountList(AccountRoleVo accountRoleVo);

    /**
     *
     * @Description 新增角色关联账号
     * @param accountRoleList
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    void addAccountRole(List<AccountRole> accountRoleList);

    /**
     *
     * @Description 删除角色关联账号
     * @param accountRoleVo
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    void deleteAccountRole(AccountRoleVo accountRoleVo);

    /**
     *
     * @Description 新增角色菜单授权关系
     * @param rolePageList
     * <AUTHOR>
     * @date 2023年10月08日 16:55
     */
    void addRolePage(List<RolePage> rolePageList);

    /**
     *
     * @Description 删除指定角色菜单授权关系
     * @param id
     * <AUTHOR>
     * @date 2023年10月08日 16:55
     */
    void deleteRolePageByRoleId(String id);

    /**
     *
     * @Description 新增角色按钮授权关系
     * @param roleButtonList
     * <AUTHOR>
     * @date 2023年10月08日 16:58
     */
    void addRoleButton(List<RoleButton> roleButtonList);

    /**
     *
     * @Description 删除指定角色按钮授权关系
     * @param id
     * <AUTHOR>
     * @date 2023年10月08日 16:59
     */
    void deleteRoleButtonByRoleId(String id);

    /**
     *
     * @Description 新增角色渠道授权关系
     * @param roleChannelList
     * <AUTHOR>
     * @date 2023年10月08日 16:59
     */
    void addRoleChannel(List<RoleChannel> roleChannelList);

    /**
     *
     * @Description 删除指定角色渠道授权关系
     * @param id
     * <AUTHOR>
     * @date 2023年10月08日 16:59
     */
    void deleteRoleChannelByRoleId(String id);

    /**
     *
     * @Description 新增角色产品品类授权关系
     * @param roleProductCategoryList
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    void addRoleProductCategory(List<RoleProductCategory> roleProductCategoryList);

    /**
     *
     * @Description 删除指定角色产品品类授权关系
     * @param id
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    void deleteRoleProductCategoryByRoleId(String id);

    /**
     *
     * @Description 新增角色工厂授权关系
     * @param roleFactoryList
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    void addRoleFactory(List<RoleFactory> roleFactoryList);

    /**
     *
     * @Description 删除指定角色工厂授权关系
     * @param id
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    void deleteRoleFactoryByRoleId(String id);

    /**
     *
     * @Description 新增角色仓库授权关系
     * @param roleDepositoryList
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    void addRoleDepository(List<RoleDepository> roleDepositoryList);

    /**
     *
     * @Description 删除指定角色仓库授权关系
     * @param id
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    void deleteRoleDepositoryByRoleId(String id);

    /**
     *
     * @Description 查询指定角色授权菜单编号列表
     * @param id
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    List<String> queryPageIdListByRoleId(String id);

    /**
     *
     * @Description 查询指定角色授权按钮编号列表
     * @param id
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    List<String> queryButtonIdListByRoleId(String id);

    /**
     *
     * @Description 查询指定角色授权渠道编号列表
     * @param id
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    List<String> queryChannelIdListByRoleId(String id);

    /**
     *
     * @Description 查询指定角色授权产品品类编号列表
     * @param id
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    List<String> queryProductCategoryIdListByRoleId(String id);

    /**
     *
     * @Description 查询指定角色授权工厂编号列表
     * @param id
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    List<String> queryFactoryIdListByRoleId(String id);

    /**
     *
     * @Description 查询指定角色授权仓库编号列表
     * @param id
     * @return List<String>
     * <AUTHOR>
     * @date 2023年10月08日 17:00
     */
    List<String> queryDepositoryIdListByRoleId(String id);
}
