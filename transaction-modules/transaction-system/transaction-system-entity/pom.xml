<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.aliyun.ryytn</groupId>
		<artifactId>transaction-system</artifactId>
		<version>1.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>transaction-system-entity</artifactId>
    <packaging>jar</packaging>
	<build>
        <plugins>
	        <!-- 解决maven打包时，会编译特定文件导致文件不可用 -->
	        <plugin>
		        <groupId>org.apache.maven.plugins</groupId>
		        <artifactId>maven-resources-plugin</artifactId>
		        <configuration>
		          <nonFilteredFileExtensions>
		            <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
		            <nonFilteredFileExtension>xls</nonFilteredFileExtension>
		            <nonFilteredFileExtension>doc</nonFilteredFileExtension>
		            <nonFilteredFileExtension>docx</nonFilteredFileExtension>
		            <nonFilteredFileExtension>ttc</nonFilteredFileExtension>
		            <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
		            <nonFilteredFileExtension>ftl</nonFilteredFileExtension>
		            <nonFilteredFileExtension>jks</nonFilteredFileExtension>
		          </nonFilteredFileExtensions>
		        </configuration>
	        </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
