package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 单点登录签名
 * <AUTHOR>
 * @date 2023/10/20 11:00
 */
@Setter
@Getter
@ToString
public class SSOSignature implements Serializable
{
    private static final long serialVersionUID = -850587886478483047L;
    /**
     * 固定OA
     */
    private String requestSource;

    /**
     * 登录账号
     */
    private String loginName;

    /**
     * 当前时间戳，精确到毫秒，时间相差15秒以内
     */
    private Long ts;

    /**
     * code值
     */
    private String code;
}
