package cn.aliyun.ryytn.modules.system.entity.dto;

import java.io.Serializable;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description Sku产品，阿里接口查询获取
 * <AUTHOR>
 * @date 2023/11/6 16:22
 */
@Setter
@Getter
@ToString
@ApiModel("Sku产品，阿里接口查询获取")
public class SkuDto implements Serializable
{
    private static final long serialVersionUID = -7123253881554069044L;
    @ApiModelProperty("商品编码，10码")
    private String skuCode;

    @ApiModelProperty("条形码（69码）")
    private String barCode;

    @ApiModelProperty("商品名称")
    private String skuName;

    @ApiModelProperty("商品状态，是否冻结")
    private Integer status;

    @ApiModelProperty("生命周期")
    private String lifeCycle;

    @ApiModelProperty("品类编码，计划分类")
    private String categoryCode;

    @ApiModelProperty("品类名称，计划分类")
    private String categoryName;

    @ApiModelProperty("保质期天数")
    private Integer shelfLife;

    @ApiModelProperty("毛重")
    private Double roughtWeight;

    @ApiModelProperty("净重")
    private Double netWeight;

    @ApiModelProperty("重量单位")
    private String weightUnit;

    @ApiModelProperty("体积")
    private Double volume;

    @ApiModelProperty("体积单位")
    private String volumeUnit;

    @ApiModelProperty("长")
    private Double length;

    @ApiModelProperty("宽")
    private Double width;

    @ApiModelProperty("高")
    private Double height;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("提数/罐数/瓶数的转换系数")
    private Integer planUnitCnt;

    @ApiModelProperty("产品分类编码")
    private String lv1CategoryCode;

    @ApiModelProperty("产品分类名称")
    private String lv1CategoryName;

    @ApiModelProperty("产品大类编码")
    private String lv2CategoryCode;

    @ApiModelProperty("产品大类名称")
    private String lv2CategoryName;

    @ApiModelProperty("产品小类编码")
    private String lv3CategoryCode;

    @ApiModelProperty("产品小类名称")
    private String lv3CategoryName;

    @ApiModelProperty("财务分类编码")
    private String finCategoryCode;

    @ApiModelProperty("财务分类名称")
    private String finCategory_Name;

    @ApiModelProperty("入数，常温奶专用")
    private Integer atomicUnitCnt;

    @ApiModelProperty("最新单价，成本价")
    private Double price;

    @ApiModelProperty("价格单位")
    private String priceUnit;

    @ApiModelProperty("是否赠品")
    private Integer gift;

    @ApiModelProperty("品牌编码")
    private String brandCode;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("所属集团")
    private String brandGroup;

    @ApiModelProperty("所属组织")
    private String brandOrg;

    @ApiModelProperty("创建时间")
    private String gmtCreate;

    @ApiModelProperty("修改时间")
    private String gmtModified;

    @ApiModelProperty("日期分区yyyymmdd")
    private String ds;

    @ApiModelProperty("对应的生产Sku对象，Sku与ProductionSku关系是多对一，sku多，productionSku一")
    private ProductionSkuDto productionSku;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        SkuDto skuDto = (SkuDto) o;
        return Objects.equals(this.skuCode, skuDto.skuCode)
            && Objects.equals(this.lv1CategoryCode, skuDto.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, skuDto.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, skuDto.lv3CategoryCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((skuCode == null) ? 0 : skuCode.hashCode());
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        return result;
    }
}
