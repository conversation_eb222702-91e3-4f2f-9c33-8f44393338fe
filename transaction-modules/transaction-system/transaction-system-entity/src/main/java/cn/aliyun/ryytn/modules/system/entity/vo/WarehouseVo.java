package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 仓库Vo
 * <AUTHOR>
 * @date 2023年10月10日 10:10
 */
@Setter
@Getter
@ToString
@ApiModel("仓库")
public class WarehouseVo implements Serializable
{
    private static final long serialVersionUID = -3217521063162453313L;
    /**
     * 仓库类型编码
     */
    @ApiModelProperty("一级仓库类型编码，bdc/cdc/rdc/fdc/edc")
    private String lv1TypeCode;

    /**
     * 仓库类型名称
     */
    @ApiModelProperty("一级仓库类型名称，工厂仓/中心仓/区域仓")
    private String lv1TypeName;

    /**
     * 仓库集合
     */
    @ApiModelProperty("仓库集合")
    private List<PhysicWarehouseVo> warehouseList;


}
