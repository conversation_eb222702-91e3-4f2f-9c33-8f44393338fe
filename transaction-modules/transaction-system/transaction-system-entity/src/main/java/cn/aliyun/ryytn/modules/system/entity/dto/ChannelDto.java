package cn.aliyun.ryytn.modules.system.entity.dto;

import java.io.Serializable;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询渠道列表响应参数
 * <AUTHOR>
 * @date 2023/10/25 10:53
 */
@Setter
@Getter
@ToString
@ApiModel("查询渠道列表响应参数")
public class ChannelDto implements Serializable
{
    private static final long serialVersionUID = 496118798869403606L;
    @ApiModelProperty("一级渠道类型编码")
    private String lv1ChannelCode;
    @ApiModelProperty("一级渠道类型名称")
    private String lv1ChannelName;
    @ApiModelProperty("二级渠道类型编码")
    private String lv2ChannelCode;
    @ApiModelProperty("二级渠道类型名称")
    private String lv2ChannelName;
    @ApiModelProperty("三级渠道类型编码")
    private String lv3ChannelCode;
    @ApiModelProperty("三级渠道类型名称")
    private String lv3ChannelName;
//    @ApiModelProperty("渠道类型")
//    private String channelType;

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        ChannelDto channelDto = (ChannelDto) o;
        return Objects.equals(this.lv1ChannelCode, channelDto.lv1ChannelCode)
            && Objects.equals(this.lv2ChannelCode, channelDto.lv2ChannelCode)
            && Objects.equals(this.lv3ChannelCode, channelDto.lv3ChannelCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((lv1ChannelCode == null) ? 0 : lv1ChannelCode.hashCode());
        result = prime * result + ((lv2ChannelCode == null) ? 0 : lv2ChannelCode.hashCode());
        result = prime * result + ((lv3ChannelCode == null) ? 0 : lv3ChannelCode.hashCode());
        return result;
    }
}
