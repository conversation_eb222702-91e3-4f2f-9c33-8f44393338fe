package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 工厂查询条件Vo
 * <AUTHOR>
 * @date 2023/10/26 16:40
 */
@Getter
@Setter
@ToString
@ApiModel("工厂查询条件Vo")
public class FactoryConditionVo implements Serializable
{
    private static final long serialVersionUID = -5674202931025223377L;
    @ApiModelProperty("工厂编码集合")
    private String factoryCodes;
    @ApiModelProperty("工厂名称")
    private String factoryName;
    @ApiModelProperty("工厂编码")
    private String factoryCode;
    @ApiModelProperty("工厂编码、名称查询关键词")
    private String factoryKey;

}
