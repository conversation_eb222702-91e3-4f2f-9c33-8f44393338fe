package cn.aliyun.ryytn.modules.system.entity.dto;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 第三方系统
 * <AUTHOR>
 * @date 2023/10/20 14:39
 */
@Setter
@Getter
@ToString
public class ThirdpartSystem implements Serializable
{
    private static final long serialVersionUID = -2663221719825513315L;
    /**
     * 编号
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 权限码
     */
    private String authCode;

    /**
     * 跳转URL，别人跳转我们的为空
     */
    private String url;

    /**
     * 状态，1：正常，2：停用
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;
}
