package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 产品查询Vo
 * <AUTHOR>
 * @date 2023/10/26 16:04
 */
@Getter
@Setter
@ToString
@ApiModel("产品查询Vo")
public class SkuConditionVo implements Serializable
{
    private static final long serialVersionUID = -5674202931025223737L;
    @ApiModelProperty("商品编码集合")
    private String skuCodes;
    @ApiModelProperty("商品名称")
    private String skuName;
    @ApiModelProperty("商品编码")
    private String skuCode;
    @ApiModelProperty("产品名称、编码模糊查询关键字")
    private String skuKey;
    @ApiModelProperty("状态，0：无效，1：有效")
    private Integer statusId;
}
