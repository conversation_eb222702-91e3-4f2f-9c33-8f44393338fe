package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询仓库返回数据Vo
 * <AUTHOR>
 * @date 2023/11/8 10:01
 */
@Setter
@Getter
@ToString
@ApiModel("查询仓库返回数据Vo")
public class QueryWarehouseListRspVo implements Serializable
{
    private static final long serialVersionUID = -4008280377921132705L;
    @JSONField(defaultValue = "warehouse_code")
    @ApiModelProperty("仓库编码")
    private String warehouseCode;
    @JSONField(defaultValue = "warehouse_name")
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @JSONField(defaultValue = "lv1_type_code")
    @ApiModelProperty("一级仓库类型编码，bdc/cdc/rdc/fdc/edc")
    private String lv1TypeCode;
    @JSONField(defaultValue = "lv1_type_name")
    @ApiModelProperty("一级仓库类型名称，工厂仓/中心仓/区域仓")
    private String lv1TypeName;
    @JSONField(defaultValue = "lv2_type_code")
    @ApiModelProperty("二级仓库类型编码")
    private String lv2TypeCode;
    @JSONField(defaultValue = "lv2_type_name")
    @ApiModelProperty("二级仓库类型名称，运输仓/成品仓/管控仓")
    private String lv2TypeName;
    @JSONField(defaultValue = "factory_code")
    @ApiModelProperty("所属工厂编码")
    private String factoryCode;
    @JSONField(defaultValue = "factory_name")
    @ApiModelProperty("所属工厂名称")
    private String factoryName;
    @JSONField(defaultValue = "is_own")
    @ApiModelProperty("是否自有仓库，1/0")
    private String isOwn;
    @JSONField(defaultValue = "addr_code")
    @ApiModelProperty("仓库地址编码")
    private String addrCode;
    @JSONField(defaultValue = "addr_name")
    @ApiModelProperty("仓库地址")
    private String addrName;
    @ApiModelProperty("经度")
    private String longitude;
    @ApiModelProperty("纬度")
    private String latitude;
    @JSONField(defaultValue = "biz_warehouse_code")
    @ApiModelProperty("业务仓库编码，如临沂山东质检仓、临沂rdc仓，都属于业务上的临沂仓")
    private String bizWarehouseCode;
    @JSONField(defaultValue = "biz_warehouse_name")
    @ApiModelProperty("业务仓库名称")
    private String bizWarehouseName;
    @ApiModelProperty("仓库状态，1/0，是否启用")
    private String status;
    @JSONField(defaultValue = "biz_warehouse_name")
    @ApiModelProperty("省编码")
    private String provinceCode;
    @JSONField(defaultValue = "province_name")
    @ApiModelProperty("省名称")
    private String provinceName;
    @JSONField(defaultValue = "city_code")
    @ApiModelProperty("市编码")
    private String cityCode;
    @JSONField(defaultValue = "city_name")
    @ApiModelProperty("市名称")
    private String cityName;
    @JSONField(defaultValue = "county_code")
    @ApiModelProperty("区编码")
    private String countyCode;
    @JSONField(defaultValue = "county_name")
    @ApiModelProperty("区名称")
    private String countyName;
    @JSONField(defaultValue = "gmt_create")
    @ApiModelProperty("创建时间")
    private String gmtCreate;
    @JSONField(defaultValue = "gmt_modified")
    @ApiModelProperty("修改时间")
    private String gmt_modified;
    @ApiModelProperty("日期分区yyyymmdd")
    private String ds;

}
