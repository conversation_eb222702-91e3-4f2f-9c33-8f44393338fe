package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询dataq日历周数据请求
 * <AUTHOR>
 * @date 2023/11/2 15:21
 */
@Setter
@Getter
@ToString
@ApiModel("查询dataq日历周数据请求")
public class QueryWeekListReqVo implements Serializable
{
    private static final long serialVersionUID = 6728156261275890420L;
    /**
     * 开始时间，yyyyMMdd
     */
    @ApiModelProperty("开始时间，yyyyMMdd")
    private String beginDate;

    /**
     * 结束时间，yyyyMMdd
     */
    @ApiModelProperty("结束时间，yyyyMMdd")
    private String endDate;

    /**
     * 指定时间，yyyyMMdd
     */
    @ApiModelProperty("指定时间，yyyyMMdd")
    private String clearDate;

    /**
     * 财年，yyyy
     */
    @ApiModelProperty("财年，yyyy")
    private String fsclYear;
}
