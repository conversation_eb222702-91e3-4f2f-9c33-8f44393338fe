package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 生产SKU产品查询条件
 * <AUTHOR>
 * @date 2023/11/30 18:11
 */
@Setter
@Getter
@ToString
@ApiModel("生产SKU产品查询条件")
public class ProductionSkuConditionVo implements Serializable
{
    private static final long serialVersionUID = 4514259517241791143L;
    private String productionCode;

    private String skuCode;

    private String productionCodes;

    private String skuCodes;
}
