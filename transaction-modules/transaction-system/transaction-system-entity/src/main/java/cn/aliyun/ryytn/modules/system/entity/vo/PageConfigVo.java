package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;
import java.util.List;

import cn.aliyun.ryytn.common.entity.PageConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 页面配置保存Vo
 * <AUTHOR>
 * @date 2023/10/12 10:43
 */
@Setter
@Getter
@ToString
@ApiModel("页面配置保存实体")
public class PageConfigVo implements Serializable
{
    private static final long serialVersionUID = -3276223602337639255L;
    /**
     * 是否开启合计
     */
    @ApiModelProperty("是否开启合计")
    private Boolean sumFlag;

    /**
     * 页面配置列表
     */
    @ApiModelProperty("页面配置列表")
    private List<PageConfig> pageConfigList;

}
