package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 物理仓库Vo
 * <AUTHOR>
 * @date 2023年12月14日 16:25
 */
@Setter
@Getter
@ToString
@ApiModel("物理仓库Vo")
public class PhysicWarehouseVo implements Serializable
{
    private static final long serialVersionUID = 7192673825822276415L;
    private String lv1TypeCode;
    @JSONField(defaultValue = "lv1_type_name")
    @ApiModelProperty("一级仓库类型名称，工厂仓/中心仓/区域仓")
    private String lv1TypeName;
    @ApiModelProperty("物理仓库编码")
    private String warehouseCode;
    @ApiModelProperty("物理仓库名称")
    private String warehouseName;
    @ApiModelProperty("仓库类型编号")
    private String warehouseTypeCode;

}
