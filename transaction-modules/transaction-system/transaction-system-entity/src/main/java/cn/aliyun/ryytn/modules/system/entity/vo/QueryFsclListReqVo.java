package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 财年查询参数
 * <AUTHOR>
 * @date 2023/10/24 11:15
 */
@Setter
@Getter
@ToString
@ApiModel("财年查询参数")
public class QueryFsclListReqVo implements Serializable
{
    private static final long serialVersionUID = 9144165545325680581L;
    @ApiModelProperty("起始 时间")
    private String beginDate;
    @ApiModelProperty("截止时间")
    private String endDate;
    @ApiModelProperty("选定时间")
    private String clearDate;

}
