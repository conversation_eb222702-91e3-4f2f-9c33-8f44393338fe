package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询产品Vo
 * <AUTHOR>
 * @date 2023/11/3 13:56
 */
@Setter
@Getter
@ToString
@ApiModel("查询产品Vo")
public class QueryProductVo implements Serializable
{
    private static final long serialVersionUID = -8144165545325680581L;
    @JSONField(defaultValue = "sku_code")
    @ApiModelProperty("商品编码，10码")
    private String skuCode;
    @JSONField(defaultValue = "sku_name")
    @ApiModelProperty("商品名称")
    private String skuName;
    @JSONField(defaultValue = "category_code")
    @ApiModelProperty("品类编码，计划分类")
    private String categoryCode;
    @JSONField(defaultValue = "category_name")
    @ApiModelProperty("品类编码，计划分类")
    private String categoryName;
    @JSONField(defaultValue = "lv1_category_code")
    @ApiModelProperty("产品分类编码")
    private String lv1CategoryCode;
    @JSONField(defaultValue = "lv1_category_name")
    @ApiModelProperty("产品分类名称")
    private String lv1CategoryName;
    @JSONField(defaultValue = "lv2_category_code")
    @ApiModelProperty("产品大类编码")
    private String lv2CategoryCode;
    @JSONField(defaultValue = "lv2_category_name")
    @ApiModelProperty("产品大类名称")
    private String lv2CategoryName;
    @JSONField(defaultValue = "lv3_category_code")
    @ApiModelProperty("产品小类编码")
    private String lv3CategoryCode;
    @JSONField(defaultValue = "lv3_category_name")
    @ApiModelProperty("产品小类名称")
    private String lv3CategoryName;
}
