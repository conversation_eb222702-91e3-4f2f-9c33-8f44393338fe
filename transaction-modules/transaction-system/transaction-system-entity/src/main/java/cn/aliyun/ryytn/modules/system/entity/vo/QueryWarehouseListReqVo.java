package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 查询仓库参数Vo
 * <AUTHOR>
 * @date 2023/11/8 9:17
 */
@Setter
@Getter
@ToString
@ApiModel("查询仓库参数Vo")
public class QueryWarehouseListReqVo implements Serializable
{
    private static final long serialVersionUID = -815565545325680581L;

    @ApiModelProperty("仓库编码集合，以逗号隔开")
    private String warehouseCodes;
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @ApiModelProperty("仓库编码")
    private String warehouseCode;
    @ApiModelProperty("搜索关键字")
    private String warehouseKey;

}
