package cn.aliyun.ryytn.modules.system.entity.dto;

import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 产品品类，阿里接口查询获取
 * <AUTHOR>
 * @date 2023/11/6 16:22
 */
@Setter
@Getter
@ApiModel("产品品类，阿里接口查询获取")
public class ProductCategoryDto implements Serializable, Comparable<ProductCategoryDto>
{
    private static final long serialVersionUID = 3534806052200406446L;
    @ApiModelProperty("唯一编号，用于前端渲染树，值取对应层级分类编号")
    private String id;

    @ApiModelProperty("产品分类编码")
    private String lv1CategoryCode;

    @ApiModelProperty("产品分类名称")
    private String lv1CategoryName;

    @ApiModelProperty("产品大类编码")
    private String lv2CategoryCode;

    @ApiModelProperty("产品大类名称")
    private String lv2CategoryName;

    @ApiModelProperty("产品小类编码")
    private String lv3CategoryCode;

    @ApiModelProperty("产品小类名称")
    private String lv3CategoryName;

    @ApiModelProperty("是否选中")
    private Boolean checked = false;

    @ApiModelProperty("子品类集合")
    private Set<ProductCategoryDto> subProductCategory;

    /**
     *
     * @Description 排序
     * @param this
     * @param o
     * @return int
     * <AUTHOR>
     * @date 2023年12月05日 15:13
     */
    @Override
    public int compareTo(ProductCategoryDto o)
    {
        if (StringUtils.compare(this.lv1CategoryCode, o.lv1CategoryCode) > 0)
        {
            return 1;
        }
        else if (StringUtils.compare(this.lv1CategoryCode, o.lv1CategoryCode) < 0)
        {
            return -1;
        }
        else
        {
            if (StringUtils.compare(this.lv2CategoryCode, o.lv2CategoryCode) > 0)
            {
                return 1;
            }
            else if (StringUtils.compare(this.lv2CategoryCode, o.lv2CategoryCode) < 0)
            {
                return -1;
            }
            else
            {
                return StringUtils.compare(this.lv3CategoryCode, o.lv3CategoryCode);
            }
        }
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (null == o || getClass() != o.getClass())
        {
            return false;
        }
        ProductCategoryDto productCategoryDto = (ProductCategoryDto) o;
        return Objects.equals(this.lv1CategoryCode, productCategoryDto.lv1CategoryCode)
            && Objects.equals(this.lv2CategoryCode, productCategoryDto.lv2CategoryCode)
            && Objects.equals(this.lv3CategoryCode, productCategoryDto.lv3CategoryCode);
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((lv1CategoryCode == null) ? 0 : lv1CategoryCode.hashCode());
        result = prime * result + ((lv2CategoryCode == null) ? 0 : lv2CategoryCode.hashCode());
        result = prime * result + ((lv3CategoryCode == null) ? 0 : lv3CategoryCode.hashCode());
        return result;
    }
}
