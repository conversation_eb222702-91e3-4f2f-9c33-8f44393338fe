package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 登录Vo
 * <AUTHOR>
 * @date 2023/10/10 10:43
 */
@Setter
@Getter
@ToString
@ApiModel("登录参数")
public class LoginVo implements Serializable
{
    private static final long serialVersionUID = -3276223602337639292L;
    /**
     * 账号
     */
    @ApiModelProperty("账号")
    private String loginId;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;

    /**
     * 验证码
     */
    @ApiModelProperty("验证码")
    private String captcha;
}
