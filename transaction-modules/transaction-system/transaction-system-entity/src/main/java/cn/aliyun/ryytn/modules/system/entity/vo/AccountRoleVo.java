package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 分配角色添加用户Vo
 * <AUTHOR>
 * @date 2023/10/9 14:19
 */
@Setter
@Getter
@ToString
@ApiModel("角色分配账号")
public class AccountRoleVo implements Serializable
{
    private static final long serialVersionUID = 5601700260271662625L;
    /**
     * 角色编号
     */
    @ApiModelProperty("角色编号")
    private String roleId;

    /**
     * 多选的账号编号
     */
    @ApiModelProperty("多选的账号编号")
    private List<String> accountIdList;

    /**
     * 搜索关键字
     */
    @ApiModelProperty("搜索关键字")
    private String keyword;
}
