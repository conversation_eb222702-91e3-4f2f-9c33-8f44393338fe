package cn.aliyun.ryytn.modules.system.entity.vo;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 角色查询条件Vo
 * <AUTHOR>
 * @date 2023/10/8 17:20
 */
@Setter
@Getter
@ToString
@ApiModel("角色查询条件")
public class RoleConditionVo implements Serializable
{
    private static final long serialVersionUID = -3746043962355880101L;
    /**
     * 角色名称
     */
    @ApiModelProperty("角色名称")
    private String name;

    /**
     * 创建时间开始时间
     */
    @ApiModelProperty("创建时间开始时间")
    private Date beginTime;

    /**
     * 创建时间结束时间
     */
    @ApiModelProperty("创建时间结束时间")
    private Date endTime;

    /**
     * 状态
     */
    @ApiModelProperty("状态，1：正常，2：停用")
    private Integer status;
}
