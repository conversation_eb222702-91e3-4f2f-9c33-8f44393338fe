package cn.aliyun.ryytn.modules.system.entity.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 生产Sku产品对象
 * <AUTHOR>
 * @date 2023/11/30 18:02
 */
@Setter
@Getter
@ToString
@ApiModel("生产Sku产品对象，Sku与ProductionSku关系是多对一，sku多，productionSku一")
public class ProductionSkuDto implements Serializable
{
    private static final long serialVersionUID = 1239856893667514956L;
    private String id;
    private String productionCode;
    private String productionName;
    private String skuCode;
    private String productName;
    private String isactive;
    private String ownerid;
    private String ownername;
    private String ownerename;
    private String adOrgId;
    private Double transferFactor;
    private String ds;
    private String adClientId;
    private String creationdate;
    private String modifierid;
    private String modifieddate;
    private String modifiername;
    private String modifierename;
}
