package cn.aliyun.ryytn.modules.system.api;

import java.util.List;

import cn.aliyun.ryytn.common.entity.Factory;

/**
 * @Description 工厂管理接口
 * <AUTHOR>
 * @date 2023年10月09日 17:25
 */
public interface FactoryService
{
    /**
     *
     * @Description 查询所有工厂列表
     * @return List<Factory>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 17:25
     */
    List<Factory> queryAllFactory() throws Exception;
}
