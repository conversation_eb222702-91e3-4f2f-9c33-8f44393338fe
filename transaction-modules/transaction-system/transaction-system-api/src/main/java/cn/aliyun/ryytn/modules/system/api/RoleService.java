package cn.aliyun.ryytn.modules.system.api;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.Role;
import cn.aliyun.ryytn.modules.system.entity.vo.AccountRoleVo;
import cn.aliyun.ryytn.modules.system.entity.vo.RoleConditionVo;

/**
 *
 * @Description 角色管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午3:11:58
 */
public interface RoleService
{
    /**
     *
     * @Description 分页查询角色列表
     * @param condition
     * @return PageInfo<Role>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:18
     */
    PageInfo<Role> queryRolePage(PageCondition<RoleConditionVo> condition) throws Exception;

    /**
     *
     * @Description 查询角色详情
     * @param id
     * @return Role
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 15:28
     */
    Role queryRoleDetail(String id) throws Exception;

    /**
     *
     * @Description 保存角色
     * @param role
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    void saveRole(Role role) throws Exception;

    /**
     *
     * @Description 删除角色
     * @param id
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 17:17
     */
    void deleteRole(String id) throws Exception;

    /**
     *
     * @Description 修改角色状态
     * @param role
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    void updateRoleStatus(Role role) throws Exception;

    /**
     *
     * @Description 查询未分配的账号列表
     * @param condition
     * @return PageInfo<Account>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 14:35
     */
    PageInfo<Account> queryUnbindAccountPage(PageCondition<AccountRoleVo> condition) throws Exception;

    /**
     *
     * @Description 查询已分配的账号列表
     * @param condition
     * @return PageInfo<Account>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 14:35
     */
    PageInfo<Account> queryBindedAccountPage(PageCondition<AccountRoleVo> condition) throws Exception;

    /**
     *
     * @Description 新增角色关联账号
     * @param accountRoleVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    void addAccountRole(AccountRoleVo accountRoleVo) throws Exception;

    /**
     *
     * @Description 删除角色关联账号
     * @param accountRoleVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    void deleteAccountRole(AccountRoleVo accountRoleVo) throws Exception;
}
