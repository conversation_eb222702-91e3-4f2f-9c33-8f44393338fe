package cn.aliyun.ryytn.modules.system.api;

import java.util.List;

import cn.aliyun.ryytn.common.entity.OADepartment;

/**
 *
 * @Description OA数据同步接口
 * <AUTHOR>
 * @date 2023年9月19日 下午3:11:58
 */
public interface OAService
{
    /**
     *
     * @Description 同步OA系统分部（分公司）数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 9:55
     */
    public void syncOASubCompany() throws Exception;

    /**
     *
     * @Description 同步OA系统部门数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 9:55
     */
    public void syncOADepartment() throws Exception;

    /**
     *
     * @Description 同步OA系统岗位数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 9:55
     */
    public void syncOAJobTitle() throws Exception;

    /**
     *
     * @Description 同步OA系统人员数据
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 9:55
     */
    public void syncOAPerson() throws Exception;

    /**
     *
     * @Description 查询部门树
     * @return List<OADepartment>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月07日 17:15
     */
    public List<OADepartment> queryDepartmentTree() throws Exception;
}
