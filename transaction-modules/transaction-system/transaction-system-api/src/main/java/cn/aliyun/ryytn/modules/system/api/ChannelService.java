package cn.aliyun.ryytn.modules.system.api;

import java.util.List;

import cn.aliyun.ryytn.common.entity.Channel;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;

/**
 * @Description 渠道管理接口
 * <AUTHOR>
 * @date 2023年10月09日 14:43
 */
public interface ChannelService
{

    /**
     *
     * @Description 查询渠道树
     * @return List<ChannelNode>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 14:43
     */
    List<Channel> queryChannelTree() throws Exception;

    /**
     *
     * @Description 查询渠道列表
     * @return List<ChannelDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 17:55
     */
    List<ChannelDto> queryChannelList() throws Exception;
}
