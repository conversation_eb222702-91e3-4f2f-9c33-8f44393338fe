package cn.aliyun.ryytn.modules.system.api;

import java.util.List;
import java.util.Set;

import cn.aliyun.ryytn.common.entity.ProductCategory;
import cn.aliyun.ryytn.modules.system.entity.dto.ProductCategoryDto;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryProductVo;

/**
 * @Description 产品品类管理接口
 * <AUTHOR>
 * @date 2023年10月09日 16:43
 */
public interface ProductCategoryService
{

    /**
     *
     * @Description 查询产品品类树
     * @return List<ProductCategory>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 16:43
     */
    List<ProductCategory> queryProductCategoryTree() throws Exception;

    /**
     *
     * @Description 查询产品品类列表
     * @return List<ProductCategoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月06日 16:43
     */
    List<ProductCategoryDto> queryProductCategoryList() throws Exception;

    /**
     *
     * @Description 构造Dataq的产品品类树
     * @param productVoList
     * @return List<ProductCategoryDto>
     * <AUTHOR>
     * @date 2023年10月25日 16:45
     */
    List<ProductCategory> createProductCategoryTree(List<QueryProductVo> productVoList);

    /**
     *
     * @Description 创建品类树
     * @param categoryList
     * @return Set<ProductCategoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月05日 10:03
     */
    Set<ProductCategoryDto> createCategoryTree(List<ProductCategoryDto> categoryList) throws Exception;
}
