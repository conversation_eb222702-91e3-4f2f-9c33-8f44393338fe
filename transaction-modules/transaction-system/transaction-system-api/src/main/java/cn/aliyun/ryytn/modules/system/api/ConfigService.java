package cn.aliyun.ryytn.modules.system.api;

import java.util.List;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.SystemConfig;
import cn.aliyun.ryytn.common.entity.SystemConfigCategory;

/**
 *
 * @Description 系统参数配置管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午2:11:38
 */
public interface ConfigService
{
    /**
     *
     * @Description 查询系统参数类型列表
     * @return List<SystemConfigCategory>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:12
     */
    public List<SystemConfigCategory> querySystemConfigCategoryList() throws Exception;

    /**
     *
     * @Description 分页查询系统参数列表
     * @param condition
     * @return PageInfo<SystemConfig>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:34
     */
    public PageInfo<SystemConfig> querySystemConfigList(PageCondition<SystemConfig> condition) throws Exception;

    /**
     *
     * @Description 查询系统参数列表
     * @param SystemConfig
     * @return List<SystemConfig>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:34
     */
    public List<SystemConfig> querySystemConfig(SystemConfig SystemConfig) throws Exception;

    /**
     *
     * @Description 修改系统参数
     * @param systemConfig
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:57
     */
    public void updateSystemConfig(SystemConfig systemConfig) throws Exception;

    /**
     *
     * @Description 批量修改系统参数
     * @param systemConfigList
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:19:12
     */
    public void batchUpdateSystemConfig(List<SystemConfig> systemConfigList) throws Exception;

    /**
     *
     * @Description 刷新系统缓存
     * @throws Exception
     * <AUTHOR>
     * @date 2022年8月22日 上午10:56:21
     */
    public void refreshSystemConfig() throws Exception;
}
