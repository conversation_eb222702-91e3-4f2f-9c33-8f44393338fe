package cn.aliyun.ryytn.modules.system.api;

import java.util.Map;

import cn.aliyun.ryytn.common.entity.PageConfig;
import cn.aliyun.ryytn.modules.system.entity.vo.PageConfigVo;

/**
 * @Description 页面配置管理接口
 * <AUTHOR>
 * @date 2023年10月11日 10:50
 */
public interface PageConfigService
{

    /**
     *
     * @Description 查询页面配置工厂列表
     * @param  pageConfig
     * @return List<PageConfig>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月11日 10:50
     */
    Map<String, Object> queryPageConfigList(PageConfig pageConfig) throws Exception;

    /**
     *
     * @Description 批量新增页面配置
     * @param  pageConfigVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月11日 10:50
     */
    void batchAddPageConfig(PageConfigVo pageConfigVo);


}
