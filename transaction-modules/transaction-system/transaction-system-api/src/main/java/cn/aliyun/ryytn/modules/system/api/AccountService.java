package cn.aliyun.ryytn.modules.system.api;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.PageCondition;

/**
 *
 * @Description 本地账号管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午3:11:58
 */
public interface AccountService
{

    /**
     *
     * @Description 分页查询账号列表
     * @param condition
     * @return PageInfo<Account>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:11
     */
    public PageInfo<Account> queryAccountPage(PageCondition<Account> condition) throws Exception;

    /**
     *
     * @Description 新增账号
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    public void addAccount(Account account) throws Exception;

    /**
     *
     * @Description 修改账号
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    public void updateAccount(Account account) throws Exception;

    /**
     *
     * @Description 修改账号状态
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    public void updateAccountStatus(Account account) throws Exception;

    /**
     *
     * @Description 修改账号密码
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2024年02月20日 17:15
     */
    public void updateAccountPassword(Account account) throws Exception;

    /**
     *
     * @Description 重置密码为默认密码
     * @param id
     * @return String 新密码
     * 此处设计不合理
     * 正常应该要么返回随机密码，要么直接修改为固定密码不返回。
     * 但是根据原型图设计要求，需要提示新密码，但是新密码又是固定的。
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    public String resetPassword(String id) throws Exception;

    /**
     *
     * @Description 删除账号
     * @param id
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    public void deleteAccount(String id) throws Exception;
}
