package cn.aliyun.ryytn.modules.system.api;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

/**
 *
 * @Description 文件管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午3:11:58
 */
public interface FileService
{
    /**
     *
     * @Description 上传文件
     * @param multipartFile
     * @return String
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年9月22日 下午4:49:37
     */
    public String uploadFile(MultipartFile multipartFile) throws Exception;

    /**
     *
     * @Description 批量上传文件
     * @param multipartFileList
     * @return List<String>
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年9月22日 下午4:49:37
     */
    public List<String> batchUploadFile(List<MultipartFile> multipartFileList) throws Exception;

    /**
     *
     * @Description 根据文件编号查询文件详情
     * @param fileId
     * @return byte[]
     * <AUTHOR>
     * @date 2023年9月22日 下午4:21:55
     */
    public byte[] download(String fileId) throws Exception;

    /**
     *
     * @Description 删除文件
     * @param fileIdList
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年9月22日 下午4:48:28
     */
    public void deleteFile(List<String> fileIdList) throws Exception;

    /**
     *
     * @Description 删除失效文件
     * edu_service_file表serviceType如果为空，则认为没有被业务使用
     * 默认删除30天以前的文件
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 下午4:48:28
     */
    public void deleteExpireFile() throws Exception;

}
