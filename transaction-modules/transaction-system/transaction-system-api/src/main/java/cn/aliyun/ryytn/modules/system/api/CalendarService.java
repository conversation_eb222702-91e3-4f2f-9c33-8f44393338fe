package cn.aliyun.ryytn.modules.system.api;


import java.util.List;

import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.DataqYear;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryFsclListReqVo;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWeekListReqVo;

/**
 * @Description 日历接口
 * <AUTHOR>
 * @date 2023/10/23 14:28
 */
public interface CalendarService
{
    /**
     * @Description 查询财年列表
     * @param queryFsclListReqVo
     * @return List<DataqYear>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/23 14:28
     */
    List<DataqYear> queryFsclList(QueryFsclListReqVo queryFsclListReqVo) throws Exception;

    /**
     *
     * @Description 查询周列表
     * @param queryWeekListReqVo
     * @return List<DataqWeek>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:09
     */
    List<DataqWeek> queryWeekList(QueryWeekListReqVo queryWeekListReqVo) throws Exception;

    /**
     *
     * @Description 刷新日历周缓存
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月15日 15:29
     */
    void refreshCalendarWeekCache() throws Exception;
}
