package cn.aliyun.ryytn.modules.system.api;

import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.Session;
import cn.aliyun.ryytn.modules.system.entity.vo.LoginVo;

/**
 * @Description 登录接口
 * <AUTHOR>
 * @date 2023/10/10 10:31
 */
public interface SessionService
{
    /**
     *
     * @Description 登录
     * @param loginVo
     * @return Session
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:56
     */
    Session login(LoginVo loginVo) throws Exception;

    /**
     *
     * @Description 登出
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:56
     */
    void logout() throws Exception;

    /**
     *
     * @Description 获取当前会话
     * @return Session
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:56
     */
    Session getCurrentSession() throws Exception;

    /**
     *
     * @Description 绑定会话账号
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月20日 10:40
     */
    void bindSessionAccount(Account account) throws Exception;
}
