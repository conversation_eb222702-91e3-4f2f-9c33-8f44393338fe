package cn.aliyun.ryytn.modules.system.api;

import java.util.List;

import cn.aliyun.ryytn.modules.system.entity.dto.ProductionSkuDto;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import cn.aliyun.ryytn.modules.system.entity.vo.ProductionSkuConditionVo;
import cn.aliyun.ryytn.modules.system.entity.vo.SkuConditionVo;

/**
 * @Description 产品接口
 * <AUTHOR>
 * @date 2023/10/23 17:59
 */
public interface ProductService
{
    /**
     * @Description 查询SKU列表
     * @param skuConditionVo
     * @return List<SkuDto>
     * @throws Exception
     */
    List<SkuDto> querySkuList(SkuConditionVo skuConditionVo) throws Exception;

    /**
     *
     * @Description 查询生产SKU产品列表
     * @param productionSkuConditionVo
     * @return List<ProductionSkuDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月30日 18:10
     */
    List<ProductionSkuDto> queryProductionSkuList(ProductionSkuConditionVo productionSkuConditionVo) throws Exception;

    /**
     *
     * @Description 刷新sku产品到缓存
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月29日 15:38
     */
    void refreshSkuCache() throws Exception;
}
