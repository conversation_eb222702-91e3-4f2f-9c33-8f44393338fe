package cn.aliyun.ryytn.modules.system.api;

import java.util.List;

import cn.aliyun.ryytn.common.entity.Page;

/**
 *
 * @Description 页面管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午3:11:58
 */
public interface PageService
{
    /**
     *
     * @Description 查询页面树
     * @param page
     * @return List<Page>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 11:20
     */
    List<Page> queryPageList(Page page) throws Exception;

    /**
     *
     * @Description 查询页面树
     * @param page
     * @return List<Page>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 11:20
     */
    List<Page> queryPageTree(Page page) throws Exception;
}
