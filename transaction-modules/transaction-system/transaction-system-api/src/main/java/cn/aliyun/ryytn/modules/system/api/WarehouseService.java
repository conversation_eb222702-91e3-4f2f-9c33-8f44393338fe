package cn.aliyun.ryytn.modules.system.api;

import java.util.List;

import cn.aliyun.ryytn.modules.system.entity.vo.PhysicWarehouseVo;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWarehouseListReqVo;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWarehouseListRspVo;
import cn.aliyun.ryytn.modules.system.entity.vo.WarehouseVo;

/**
 * @Description 仓库管理接口
 * <AUTHOR>
 * @date 2023年10月10日 10:40
 */
public interface WarehouseService
{

    /**
     *
     * @Description 查询仓库列表
     * @return List<DepositoryVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:40
     */
    List<WarehouseVo> queryWarehouseGroup() throws Exception;

    /**
     *
     * @Description 查询仓库列表
     * @param warehouseListReqVo
     * @return List<QueryWarehouseListRspVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月16日 21:08
     */
    List<QueryWarehouseListRspVo> queryWarehouseList(QueryWarehouseListReqVo warehouseListReqVo) throws Exception;

    /**
     *
     * @Description 查询物理仓库列表
     * @param warehouseListReqVo
     * @return List<PhysicWarehouseVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月16日 21:08
     */
    List<PhysicWarehouseVo> queryPhysicWarehouseList(QueryWarehouseListReqVo warehouseListReqVo) throws Exception;

    /**
     *
     * @Description 刷新物理仓/逻辑仓映射关系缓存
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 16:07
     */
    void refreshWarehouseMappingCache() throws Exception;
}
