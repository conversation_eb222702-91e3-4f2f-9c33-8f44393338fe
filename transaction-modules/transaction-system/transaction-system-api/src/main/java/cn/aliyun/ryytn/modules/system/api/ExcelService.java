package cn.aliyun.ryytn.modules.system.api;

import org.springframework.web.multipart.MultipartFile;

import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.excel.ExcelTask;

/**
 * @Description 电子表格服务接口
 * <AUTHOR>
 * @date 2023/10/19 12:49
 */
public interface ExcelService
{
    /**
     *
     * @Description 导入电子表格
     * @param file
     * @param condition
     * @return ExcelTask<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月19日 16:18
     */
    ExcelTask<?> importExcel(MultipartFile file, ExcelCondition condition) throws Exception;

    /**
     *
     * @Description 导出电子表格
     * @param condition
     * @return ExcelTask<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月19日 16:19
     */
    ExcelTask<?> exportExcel(ExcelCondition condition) throws Exception;

    /**
     *
     * @Description 查询电子表格任务
     * @param id
     * @return ExcelTask<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月19日 16:19
     */
    ExcelTask<?> queryExcelTask(String id) throws Exception;
}
