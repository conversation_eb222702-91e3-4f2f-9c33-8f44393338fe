package cn.aliyun.ryytn.modules.system.api;

import java.util.List;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.DictData;
import cn.aliyun.ryytn.common.entity.DictType;
import cn.aliyun.ryytn.common.entity.PageCondition;

/**
 * @Description 字典管理接口
 * <AUTHOR>
 * @date 2023/9/25 9:49
 */
public interface DictService
{
    /**
     *
     * @Description 查询字典类型列表
     * @param dictType
     * @return List<DictType>
     * <AUTHOR>
     * @date 2022年6月9日 下午9:28:40
     */
    public List<DictType> queryDictTypeList(DictType dictType) throws Exception;

    /**
     *
     * @Description 分页查询字典类型列表
     * @param condition
     * @return PageInfo<DictType>
     * <AUTHOR>
     * @date 2022年6月9日 下午9:28:40
     */
    public PageInfo<DictType> queryDictTypePage(PageCondition<DictType> condition) throws Exception;

    /**
     *
     * @Description 查询字典类型详情
     * @param dictTypeId
     * @return DictType
     * <AUTHOR>
     * @date 2022年6月9日 下午9:30:13
     */
    public DictType queryDictTypeDetail(String dictTypeId) throws Exception;

    /**
     *
     * @Description 新增字典类型
     * @param dictType
     * <AUTHOR>
     * @date 2022年6月9日 下午9:31:04
     */
    public void addDictType(DictType dictType) throws Exception;

    /**
     *
     * @Description 修改字典类型
     * @param dictType
     * <AUTHOR>
     * @date 2022年6月9日 下午9:31:20
     */
    public void updateDictType(DictType dictType) throws Exception;

    /**
     *
     * @Description 删除字典类型
     * @param dictTypeIdList
     * <AUTHOR>
     * @date 2022年6月9日 下午9:31:33
     */
    public void deleteDictType(List<String> dictTypeIdList) throws Exception;

    /**
     *
     * @Description 校验字典类型唯一性
     * @param dictType
     * <AUTHOR>
     * @date 2022年6月9日 下午9:31:45
     */
    public void checkDictTypeUnique(String dictType) throws Exception;

    /**
     *
     * @Description 加载字典缓存
     * <AUTHOR>
     * @date 2022年6月9日 下午9:36:05
     */
    public void loadDictCache() throws Exception;

    /**
     *
     * @Description 分页查询字典数据列表
     * @param condition
     * @return PageInfo<DictData>
     * <AUTHOR>
     * @date 2022年6月9日 下午9:47:43
     */
    public PageInfo<DictData> queryDictDataPage(PageCondition<DictData> condition) throws Exception;

    /**
     *
     * @Description 查询字典数据列表
     * @param dictData
     * @return List<DictData>
     * <AUTHOR>
     * @date 2022年6月9日 下午9:47:43
     */
    public List<DictData> queryDictDataList(DictData dictData) throws Exception;

    /**
     *
     * @Description 查询字典数据树
     * @param dictData
     * @return List<DictData>
     * <AUTHOR>
     * @date 2022年6月9日 下午9:47:43
     */
    public List<DictData> queryDictDataTree(DictData dictData) throws Exception;

    /**
     *
     * @Description 查询字典数据详情
     * @param dictId
     * @return DictData
     * <AUTHOR>
     * @date 2022年6月9日 下午9:49:54
     */
    public DictData queryDictDataDetail(String dictId) throws Exception;

    /**
     *
     * @Description 新增字典数据
     * @param dictData
     * <AUTHOR>
     * @date 2022年6月9日 下午10:07:09
     */
    public void addDictData(DictData dictData) throws Exception;

    /**
     *
     * @Description 修改字典数据
     * @param dictData
     * <AUTHOR>
     * @date 2022年6月9日 下午10:07:28
     */
    public void updateDictData(DictData dictData) throws Exception;

    /**
     *
     * @Description 删除字典数据
     * @param dictIdList
     * <AUTHOR>
     * @date 2022年6月9日 下午10:06:49
     */
    public void deleteDictData(List<String> dictIdList) throws Exception;

    /**
     *
     * @Description 校验字典数据唯一性
     * @param dictData
     * <AUTHOR>
     * @date 2022年6月9日 下午9:31:45
     */
    public void checkDictDataUnique(DictData dictData) throws Exception;

    /**
     *
     * @Description 查询全部数据字典(所有前端应用都需要获取)
     * @return List<DictType>
     * <AUTHOR>
     * @date 2022年6月9日 下午9:47:43
     */
    public List<DictData> queryAllDictList() throws Exception;
}
