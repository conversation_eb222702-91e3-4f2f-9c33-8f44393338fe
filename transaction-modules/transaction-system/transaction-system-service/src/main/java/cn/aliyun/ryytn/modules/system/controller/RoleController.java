package cn.aliyun.ryytn.modules.system.controller;

import java.util.Objects;

import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import com.alibaba.fastjson.JSONObject;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.entity.Role;
import cn.aliyun.ryytn.common.exception.ParameterException;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.RoleService;
import cn.aliyun.ryytn.modules.system.entity.vo.AccountRoleVo;
import cn.aliyun.ryytn.modules.system.entity.vo.RoleConditionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 角色管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午2:11:38
 */
@Slf4j
@RestController
@RequestMapping("/api/system/role")
@Api(tags = "角色管理")
public class RoleController
{
    @Autowired
    private RoleService roleService;

    /**
     *
     * @Description 分页查询角色列表
     * @param condition
     * @return ResultInfo<PageInfo < Role>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 11:18
     */
    @PostMapping("queryRolePage")
    @ResponseBody
    @ApiOperation("分页查询角色列表")
    @RequiresPermissions(value = {"system:role:query"})
    public ResultInfo<PageInfo<Role>> queryRolePage(@RequestBody PageCondition<RoleConditionVo> condition) throws Exception
    {
        if (Objects.isNull(condition))
        {
            condition = new PageCondition<RoleConditionVo>();
        }

        PageInfo<Role> result = roleService.queryRolePage(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询角色详情
     * @param id
     * @return ResultInfo<Role>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 15:28
     */
    @PostMapping("queryRoleDetail")
    @ResponseBody
    @ApiOperation("查询角色详情")
    @RequiresPermissions(value = {"system:role:query"})
    public ResultInfo<Role> queryRoleDetail(@RequestBody String id) throws Exception
    {
        // 参数不能为空
        ValidateUtil.checkIsNotEmpty(id);

        // 查询角色详情
        Role role = roleService.queryRoleDetail(id);
        return ResultInfo.success(role);
    }

    /**
     *
     * @Description 保存角色
     * @param role
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    @PostMapping("saveRole")
    @ResponseBody
    @ApiOperation("新增/修改角色保存数据")
    @RequiresPermissions(value = {"system:role:add", "system:role:update"}, logical = Logical.OR)
    public ResultInfo<?> saveRole(@RequestBody Role role) throws Exception
    {
        // 参数校验
        checkParam(role);

        // 保存角色
        roleService.saveRole(role);

        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        log.info("登录用户{}对角色{}进行了编辑操作saveRole",currentAccount.getLoginId(), JSONObject.toJSONString(role));

        return ResultInfo.success();
    }

    /**
     *
     * @Description 删除角色
     * @param id
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 17:17
     */
    @PostMapping("deleteRole")
    @ResponseBody
    @ApiOperation("删除角色")
    @RequiresPermissions(value = {"system:role:delete"})
    public ResultInfo<?> deleteRole(@RequestBody String id) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(id);
        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        log.info("登录用户{}对角色{}进行了删除操作deleteRole",currentAccount.getLoginId(), id);
        // 删除角色
        roleService.deleteRole(id);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 修改角色状态
     * @param role
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    @PostMapping("updateRoleStatus")
    @ResponseBody
    @ApiOperation("修改角色状态")
    @RequiresPermissions(value = {"system:role:update"})
    public ResultInfo<?> updateRoleStatus(@RequestBody Role role) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(role);
        // 编号不能为空
        ValidateUtil.checkIsNotEmpty(role.getId());
        // 状态不能为空
        ValidateUtil.checkIsNotEmpty(role.getStatus());
        // 状态合法性
        ValidateUtil.checkNumRange(role.getStatus(), CommonConstants.DATA_STATUS_ENABLE, CommonConstants.DATA_STATUS_DISABLE);

        // 删除角色
        roleService.updateRoleStatus(role);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 分页查询未分配给角色的账号列表
     * @param condition
     * @return ResultInfo<PageInfo < Account>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 14:32
     */
    @PostMapping("queryUnbindAccountPage")
    @ResponseBody
    @ApiOperation("分页查询未分配给角色的账号列表")
    @RequiresPermissions(value = {"system:role:update"})
    public ResultInfo<PageInfo<Account>> queryUnbindAccountPageByRoleId(@RequestBody PageCondition<AccountRoleVo> condition) throws Exception
    {
        if (Objects.isNull(condition))
        {
            condition = new PageCondition<AccountRoleVo>();
        }

        PageInfo<Account> result = roleService.queryUnbindAccountPage(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 分页查询已分配给角色的账号列表
     * @param condition
     * @return ResultInfo<PageInfo < Account>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 14:32
     */
    @PostMapping("queryBindedAccountPage")
    @ResponseBody
    @ApiOperation("分页查询已分配给角色的账号列表")
    @RequiresPermissions(value = {"system:role:update"})
    public ResultInfo<PageInfo<Account>> queryBindedAccountPageByRoleId(@RequestBody PageCondition<AccountRoleVo> condition) throws Exception
    {
        if (Objects.isNull(condition))
        {
            condition = new PageCondition<AccountRoleVo>();
        }

        PageInfo<Account> result = roleService.queryBindedAccountPage(condition);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 新增角色关联账号
     * @param accountRoleVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    @PostMapping("addAccountRole")
    @ResponseBody
    @ApiOperation("角色分配账号添加账号")
    @RequiresPermissions(value = {"system:role:update"})
    public ResultInfo<?> addAccountRole(@RequestBody AccountRoleVo accountRoleVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(accountRoleVo);
        ValidateUtil.checkIsNotEmpty(accountRoleVo.getRoleId());
        ValidateUtil.checkIsNotEmpty(accountRoleVo.getAccountIdList());

        // 新增角色关联账号
        roleService.addAccountRole(accountRoleVo);

        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        log.info("登录用户{}对账户{}进行了角色分配addAccountRole操作",currentAccount.getLoginId(), JSONObject.toJSONString(accountRoleVo));
        return ResultInfo.success();
    }

    /**
     *
     * @Description 删除角色关联账号
     * @param accountRole
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    @PostMapping("deleteAccountRole")
    @ResponseBody
    @ApiOperation("角色分配账号移除账号")
    @RequiresPermissions(value = {"system:role:update"})
    public ResultInfo<?> deleteAccountRole(@RequestBody AccountRoleVo accountRoleVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(accountRoleVo);
        ValidateUtil.checkIsNotEmpty(accountRoleVo.getRoleId());
        ValidateUtil.checkIsNotEmpty(accountRoleVo.getAccountIdList());


        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        log.info("登录用户{}对账户{}进行了取消角色分配deleteAccountRole操作",currentAccount.getLoginId(), JSONObject.toJSONString(accountRoleVo));
        // 删除角色关联账号
        roleService.deleteAccountRole(accountRoleVo);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 参数校验
     * @param role
     * @throws ParameterException
     * <AUTHOR>
     * @date 2023年10月09日 11:25
     */
    private void checkParam(Role role) throws ParameterException
    {
        // 参数不能为空
        ValidateUtil.checkIsNotEmpty(role);
        // 角色名称费控
        ValidateUtil.checkStrLength(role.getName(), 1, 64, false);
        // 状态不能为空
        ValidateUtil.checkIsNotEmpty(role.getStatus());
        // 状态合法性
        ValidateUtil.checkNumRange(role.getStatus(), CommonConstants.DATA_STATUS_ENABLE, CommonConstants.DATA_STATUS_DISABLE);
        // 排序数字校验
        ValidateUtil.checkIsNotEmpty(role.getSortNo());
        // 描述字段长度校验
        ValidateUtil.checkStrLength(role.getDescription(), 1, 256, true);
    }
}
