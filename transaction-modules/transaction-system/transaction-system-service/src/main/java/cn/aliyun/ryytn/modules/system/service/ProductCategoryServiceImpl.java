package cn.aliyun.ryytn.modules.system.service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.ProductCategory;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.ProductCategoryService;
import cn.aliyun.ryytn.modules.system.entity.dto.ProductCategoryDto;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryProductVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 产品品类管理接口
 * <AUTHOR>
 * @date 2023年10月09日 16:43
 */
@Slf4j
@Service
public class ProductCategoryServiceImpl implements ProductCategoryService
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 查询产品品类树
     * @return List<ProductCategory>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 16:43
     */
    @Override
    public List<ProductCategory> queryProductCategoryTree() throws Exception
    {

        //从缓存中获取
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BASEBUS_SKULIST"));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("statusId", 1);

        Map<String, Object> group = new HashMap<>();
        group.put("return_fields", "lv1_category_code,lv1_category_name,lv2_category_code,lv2_category_name," +
            "lv3_category_code,lv3_category_name");
        group.put("group_by", "category_code,category_name,lv1_category_code,lv1_category_name,lv2_category_code,lv2_category_name," +
            "lv3_category_code,lv3_category_name");
        group.put("order_by", "lv1_category_code,lv2_category_code,lv3_category_code");

        JSONArray data = (JSONArray) dataqService.invoke(HttpMethod.POST, path, null, group, jsonObject).getData();

        List<QueryProductVo> productVoList = JSONArray.parseArray(data.toJSONString(), QueryProductVo.class);

        List<ProductCategory> productCategoryTree = this.createProductCategoryTree(productVoList);

        return productCategoryTree;
    }

    /**
     *
     * @Description 查询产品品类列表
     * @return List<ProductCategoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月06日 16:43
     */
    @Override
    public List<ProductCategoryDto> queryProductCategoryList() throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BASEBUS_SKULIST"));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("statusId", 1);
        DataqResult dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, jsonObject);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        return jsonArray.toJavaList(ProductCategoryDto.class);
    }

    /**
     *
     * @Description 构造Dataq的产品品类树
     * @param productVoList
     * @return List<ProductCategory>
     * <AUTHOR>
     * @date 2023年10月25日 16:45
     */
    @Override
    public List<ProductCategory> createProductCategoryTree(List<QueryProductVo> productVoList)
    {

        List<ProductCategory> Data = new ArrayList<>();

        //排序数据  Lv1ChannelCode+Lv2ChannelCode+Lv3ChannelCode
        productVoList = productVoList.stream()
            .filter(item -> Objects.nonNull(item.getLv1CategoryCode()))
            .filter(item -> Objects.nonNull(item.getLv2CategoryCode()))
            .filter(item -> Objects.nonNull(item.getLv3CategoryCode()))
            .sorted(Comparator.comparing(QueryProductVo::getLv1CategoryCode)
                .thenComparing((QueryProductVo::getLv2CategoryCode))
                .thenComparing(QueryProductVo::getLv3CategoryCode))
            .collect(Collectors.toList());

        List<String> first = productVoList.stream().map(item -> item.getLv1CategoryCode()).distinct().collect(Collectors.toList());
        for (String firstKey : first)
        {
            ProductCategory category1 = new ProductCategory();
            List<QueryProductVo> firstList =
                productVoList.stream().filter(item -> StringUtils.equals(item.getLv1CategoryCode(), firstKey)).collect(Collectors.toList());
            QueryProductVo productVo = firstList.get(0);
            String lv1CategoryName = productVo.getLv1CategoryName();
            category1.setName(lv1CategoryName);
            category1.setId(firstKey);
            category1.setLevel(1);
            List<String> second = firstList.stream().map(item -> item.getLv2CategoryCode()).distinct().collect(Collectors.toList());

            for (String secondKey : second)
            {
                ProductCategory category2 = new ProductCategory();
                List<QueryProductVo> secondList = productVoList.stream()
                    .filter(item -> StringUtils.equals(item.getLv1CategoryCode(), firstKey))
                    .filter(item -> StringUtils.equals(item.getLv2CategoryCode(), secondKey)).collect(Collectors.toList());
                if (secondList.size() < 1)
                {
                    continue;
                }
                QueryProductVo productVo2 = secondList.get(0);
                String lv2CategoryName = productVo2.getLv2CategoryName();
                category2.setName(lv2CategoryName);
                category2.setId(secondKey);
                category2.setLevel(2);
                List<String> third = productVoList.stream().map(item -> item.getLv3CategoryCode()).distinct().collect(Collectors.toList());

                for (String threeKey : third)
                {
                    ProductCategory category3 = new ProductCategory();
                    List<QueryProductVo> thirdList = productVoList.stream()
                        .filter(item -> StringUtils.equals(item.getLv1CategoryCode(), firstKey))
                        .filter(item -> StringUtils.equals(item.getLv2CategoryCode(), secondKey))
                        .filter(item -> StringUtils.equals(item.getLv3CategoryCode(), threeKey)).collect(Collectors.toList());
                    if (thirdList.size() < 1)
                    {
                        continue;
                    }
                    QueryProductVo productVo3 = thirdList.get(0);
                    category3.setName(productVo3.getLv3CategoryName());
                    category3.setId(threeKey);
                    category3.setLevel(3);
                    category2.getSubList().add(category3);
                }
                category1.getSubList().add(category2);
            }
            Data.add(category1);
        }
        return Data;
    }

    /**
     *
     * @Description 创建产品品类树
     * @param categoryList
     * @return Set<ProductCategoryDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月05日 15:28
     */
    public Set<ProductCategoryDto> createCategoryTree(List<ProductCategoryDto> categoryList) throws Exception
    {
        Set<ProductCategoryDto> lv1CategorySet = new TreeSet<>();
        for (ProductCategoryDto item : categoryList)
        {
            ProductCategoryDto lv1Category = new ProductCategoryDto();
            lv1Category.setId(item.getLv1CategoryCode());
            lv1Category.setLv1CategoryCode(item.getLv1CategoryCode());
            lv1Category.setLv1CategoryName(item.getLv1CategoryName());
            lv1Category.setLv2CategoryCode(null);
            lv1Category.setLv2CategoryName(null);
            lv1Category.setLv3CategoryCode(null);
            lv1Category.setLv3CategoryName(null);
            lv1CategorySet.add(lv1Category);
        }
        for (ProductCategoryDto lv1Category : lv1CategorySet)
        {
            Set<ProductCategoryDto> lv2CategorySet = new TreeSet<>();
            for (ProductCategoryDto item1 : categoryList)
            {
                if (StringUtils.equals(lv1Category.getLv1CategoryCode(), item1.getLv1CategoryCode()) && StringUtils.isNotEmpty(item1.getLv2CategoryCode()))
                {
                    ProductCategoryDto lv2Category = new ProductCategoryDto();
                    lv2Category.setId(item1.getLv2CategoryCode());
                    lv2Category.setLv1CategoryCode(item1.getLv1CategoryCode());
                    lv2Category.setLv1CategoryName(item1.getLv1CategoryName());
                    lv2Category.setLv2CategoryCode(item1.getLv2CategoryCode());
                    lv2Category.setLv2CategoryName(item1.getLv2CategoryName());
                    lv2Category.setLv3CategoryCode(null);
                    lv2Category.setLv3CategoryName(null);
                    lv2CategorySet.add(lv2Category);
                    Set<ProductCategoryDto> lv3CategorySet = new TreeSet<>();
                    for (ProductCategoryDto item2 : categoryList)
                    {
                        if (StringUtils.equals(lv2Category.getLv1CategoryCode(), item2.getLv1CategoryCode()) &&
                            StringUtils.equals(lv2Category.getLv2CategoryCode(), item2.getLv2CategoryCode()) &&
                            StringUtils.isNotEmpty(item2.getLv3CategoryCode()))
                        {
                            item2.setId(item2.getLv3CategoryCode());
                            lv3CategorySet.add(item2);
                        }
                    }
                    lv2Category.setSubProductCategory(lv3CategorySet);
                }
            }
            lv1Category.setSubProductCategory(lv2CategorySet);
        }
        return lv1CategorySet;
    }
}
