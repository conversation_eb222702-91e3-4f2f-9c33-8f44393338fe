package cn.aliyun.ryytn.modules.system.controller;

import java.util.Objects;

import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import com.alibaba.fastjson.JSONObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.AccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 本地账号管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午2:11:38
 */
@Slf4j
@RestController
@RequestMapping("/api/system/account")
@Api(tags = "账号管理")
public class AccountController
{
    /**
     * 本地账号管理接口
     */
    @Autowired
    private AccountService accountService;

    /**
     *
     * @Description 分页查询账号列表
     * @param condition
     * @return ResultInfo<PageInfo < Account>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:11
     */
    @PostMapping("queryAccountPage")
    @ResponseBody
    @ApiOperation("分页查询账号列表")
    @RequiresPermissions(value = {"system:account:query"})
    public ResultInfo<PageInfo<Account>> queryAccountPage(@RequestBody PageCondition<Account> condition) throws Exception
    {
        if (Objects.isNull(condition))
        {
            condition = new PageCondition<>();
        }

        PageInfo<Account> page = accountService.queryAccountPage(condition);
        return ResultInfo.success(page);
    }

    /**
     *
     * @Description 新增账号
     * @param account
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @PostMapping("addAccount")
    @ResponseBody
    @ApiOperation("新增账号")
    @RequiresPermissions(value = {"system:account:add"})
    public ResultInfo<?> addAccount(@RequestBody Account account) throws Exception
    {
        // 参数校验
        checkParam(account);

        // 新增账号
        accountService.addAccount(account);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 修改账号，仅修改姓名、工号、描述
     * @param account
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @PostMapping("updateAccount")
    @ResponseBody
    @ApiOperation("修改账号")
    @RequiresPermissions(value = {"system:account:update"})
    public ResultInfo<?> updateAccount(@RequestBody Account account) throws Exception
    {
        // 参数校验
        checkParam(account);
        // 编号不能为空
        ValidateUtil.checkIsNotEmpty(account.getId());

        // 修改账号
        accountService.updateAccount(account);

        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        log.info("登录用户{}对账户{}进行了角色关联修改updateAccount操作",currentAccount.getLoginId(), JSONObject.toJSONString(account));
        return ResultInfo.success();
    }

    /**
     *
     * @Description 修改账号状态
     * @param accountId
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @PostMapping("updateAccountStatus")
    @ResponseBody
    @ApiOperation("修改账号状态")
    @RequiresPermissions(value = {"system:account:update"})
    public ResultInfo<?> updateAccountStatus(@RequestBody Account account) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(account);
        ValidateUtil.checkIsNotEmpty(account.getId());
        ValidateUtil.checkNumRange(account.getStatus(), CommonConstants.DATA_STATUS_ENABLE, CommonConstants.DATA_STATUS_DISABLE);

        // 修改账号状态
        accountService.updateAccountStatus(account);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 修改账号密码
     * @param accountId
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @PostMapping("updateAccountPassword")
    @ResponseBody
    @ApiOperation("修改账号密码")
    public ResultInfo<?> updateAccountPassword(@RequestBody Account account) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(account);
        ValidateUtil.checkIsNotEmpty(account.getLoginId());
        ValidateUtil.checkIsNotEmpty(account.getPassword());
        ValidateUtil.checkIsNotEmpty(account.getNewPassword());
        if (StringUtils.equals(account.getPassword(), account.getNewPassword()))
        {
            throw new ServiceException("新密码与原密码不能相同");
        }

        // 修改账号状态
        accountService.updateAccountPassword(account);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 重置密码为默认密码
     * @param id
     * @return ResultInfo<String>
     * 此处设计不合理
     * 正常应该要么返回随机密码，要么直接修改为固定密码不返回。
     * 但是根据原型图设计要求，需要提示新密码，但是新密码又是固定的。
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @PostMapping("resetPassword")
    @ResponseBody
    @ApiOperation("重置密码")
    @RequiresPermissions(value = {"system:account:update"})
    public ResultInfo<String> resetPassword(@RequestBody String id) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(id);

        // 重置密码为默认密码
        return ResultInfo.success(accountService.resetPassword(id));
    }

    /**
     *
     * @Description 删除账号，高保真图上账号列表没有多选框，且领导要求提示信息较为苛刻，咱不考虑支持多选删除
     * @param idList
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @PostMapping("deleteAccount")
    @ResponseBody
    @ApiOperation("删除账号")
    @RequiresPermissions(value = {"system:account:delete"})
    public ResultInfo<?> deleteAccount(@RequestBody String id) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(id);
        Account currentAccount = ServiceContextUtils.currentSession().getAccount();
        log.info("登录用户{}对账户{}进行了删除操作deleteAccount",currentAccount.getLoginId(), id);
        // 删除账号
        accountService.deleteAccount(id);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 参数校验
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:21
     */
    private void checkParam(Account account) throws Exception
    {
        // 对象不能为空
        ValidateUtil.checkIsNotEmpty(account);
        // 登录账号不能为空且长度小于32
        ValidateUtil.checkStrLength(account.getLoginId(), 1, 64, false);
        // 姓名不能为空且长度小于64
        ValidateUtil.checkStrLength(account.getName(), 1, 64, false);
        // id字段不能为空放在updateAccount接口中校验
        // password字段不能为空放在addAccount接口空校验
        // 描述长度小于256
        ValidateUtil.checkStrLength(account.getDescription(), 1, 256, true);
    }
}
