package cn.aliyun.ryytn.modules.system.service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSONArray;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.DataqYear;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryFsclListReqVo;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWeekListReqVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 日历管理
 * <AUTHOR>
 * @date 2023/10/23 14:31
 */
@Slf4j
@Service
public class CalendarServiceImpl implements CalendarService, ApplicationListener<ContextRefreshedEvent>
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event)
    {
        try
        {
            refreshCalendarWeekCache();
        }
        catch (Exception e)
        {
            log.error("onApplicationEvent refreshCalendarCache has exceptoin:", e);
        }
    }

    /**
     * @Description 查询财年列表
     * @param queryFsclListReqVo
     * @return List<DataqYear>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/23 14:28
     */
    @Override
    public List<DataqYear> queryFsclList(QueryFsclListReqVo queryFsclListReqVo) throws Exception
    {
        //从缓存中获取请求路径
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CALENDAR_FSCL_LIST"));

        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryFsclListReqVo);

        List<DataqYear> result = ((JSONArray) dataqResult.getData()).toJavaList(DataqYear.class);

        return result;
    }

    /**
     *
     * @Description 查询周列表
     * @param queryWeekListReqVo
     * @return List<DataqWeek>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 10:09
     */
    @Override
    public List<DataqWeek> queryWeekList(QueryWeekListReqVo queryWeekListReqVo) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_CALENDAR_WEEK_LIST"));

        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, queryWeekListReqVo);

        List<DataqWeek> result = ((JSONArray) dataqResult.getData()).toJavaList(DataqWeek.class);

        return result;
    }

    /**
     *
     * @Description 刷新日历周缓存
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月15日 15:30
     */
    @Override
    public void refreshCalendarWeekCache() throws Exception
    {
        QueryWeekListReqVo queryWeekListReqVo = null;
        List<DataqWeek> list = queryWeekList(queryWeekListReqVo);
        if (CollectionUtils.isEmpty(list))
        {
            return;
        }

        // 删除阿里已经清理的日历维表缓存
        Set<String> itemSet = redisUtils.hKeys(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY);
        if (CollectionUtils.isNotEmpty(itemSet))
        {
            Set<String> daySet = list.stream().map(DataqWeek::getFsclWeekStart).collect(Collectors.toSet());
            Set<String> deleteItemSet = itemSet.stream().filter(item -> {
                return !daySet.contains(item);
            }).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(deleteItemSet))
            {
                redisUtils.hdel(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, deleteItemSet.toArray(new String[0]));
            }
            daySet.clear();
            itemSet.clear();
        }

        // 日期-周缓存，KEY：yyyyMMdd，VALUE：dataqWeek对象
        Map<String, Object> dayMap = list.stream().collect(Collectors.toMap(DataqWeek::getFsclWeekStart, Function.identity(), (k1, k2) -> k1));
        redisUtils.hmset(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, dayMap);
        dayMap.clear();

        // 删除阿里已经清理的日历维表缓存
        itemSet = redisUtils.hKeys(CommonConstants.CALENDAR_MONTHWEEK_CACHE_KEY);
        if (CollectionUtils.isNotEmpty(itemSet))
        {
            Set<String> weekSet = list.stream().map(DataqWeek::getYearMonthWeek).collect(Collectors.toSet());
            Set<String> deleteItemSet = itemSet.stream().filter(item -> {
                return !weekSet.contains(item);
            }).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(deleteItemSet))
            {
                redisUtils.hdel(CommonConstants.CALENDAR_MONTHWEEK_CACHE_KEY, deleteItemSet.toArray(new String[0]));
            }
            weekSet.clear();
            itemSet.clear();
        }

        // 月周-周缓存，KEY：yyyyMMWw，VALUE：dataqWeek对象
        Map<String, Object> monthMap = list.stream().collect(Collectors.toMap(DataqWeek::getYearMonthWeek, Function.identity(), (k1, k2) -> k1));
        redisUtils.hmset(CommonConstants.CALENDAR_MONTHWEEK_CACHE_KEY, monthMap);
        monthMap.clear();

//        QueryWeekListReqVo queryWeekListReqVo = null;
//        List<DataqWeek> list = queryWeekList(queryWeekListReqVo);
//        if (CollectionUtils.isEmpty(list))
//        {
//            return;
//        }
//
//        // 缓存有效期，刷新缓存调度周期为每日，缓存有效期设为30天
//        // 在阿里日历维表数据清理后30天，本地缓存被清理
//        long expireTime = 2592000L;
//
//        // 日期-周缓存，KEY：yyyyMMdd，VALUE：dataqWeek对象
//        Map<String, DataqWeek> dayMap = list.stream().collect(Collectors.toMap(DataqWeek::getFsclWeekStart, Function.identity(), (k1, k2) -> k1));
//        for (Map.Entry<String, DataqWeek> entry : dayMap.entrySet())
//        {
//            String key = StringUtils.format(CommonConstants.CALENDAR_DAYWEEK_CACHE_KEY, entry.getKey());
//            redisUtils.set(key, entry.getKey(), expireTime);
//        }
//        dayMap.clear();
    }
}
