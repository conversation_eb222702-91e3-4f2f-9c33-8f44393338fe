package cn.aliyun.ryytn.modules.system.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.Channel;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.system.api.ChannelService;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 渠道管理接口
 * <AUTHOR>
 * @date 2023年10月9日 14:24
 */
@Slf4j
@RestController
@RequestMapping("/api/system/channel")
@Api(tags = "渠道管理")
public class ChannelController
{

    /**
     * 渠道管理接口
     */
    @Autowired
    private ChannelService channelService;

    /**
     *
     * @Description 查询树
     * @return ResultInfo<List < Channel>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月9日 14:24
     */
    @PostMapping("queryChannelTree")
    @ResponseBody
    @ApiOperation("查询渠道树")
//    @RequiresPermissions({})
    public ResultInfo<List<Channel>> queryPageTree() throws Exception
    {
        return ResultInfo.success(channelService.queryChannelTree());
    }

    /**
     *
     * @Description 查询渠道列表
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 17:54
     */
    @PostMapping("queryChannelList")
    @ResponseBody
    @ApiOperation("查询渠道列表")
    public ResultInfo<?> queryChannelList() throws Exception
    {
        List<ChannelDto> result = channelService.queryChannelList();
        return ResultInfo.success(result);
    }

}
