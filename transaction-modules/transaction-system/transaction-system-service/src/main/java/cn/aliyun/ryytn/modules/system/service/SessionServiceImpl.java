package cn.aliyun.ryytn.modules.system.service;

import java.util.*;
import java.util.stream.Collectors;

import cn.aliyun.ryytn.common.utils.date.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.Button;
import cn.aliyun.ryytn.common.entity.Factory;
import cn.aliyun.ryytn.common.entity.Page;
import cn.aliyun.ryytn.common.entity.Session;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.ChannelService;
import cn.aliyun.ryytn.modules.system.api.FactoryService;
import cn.aliyun.ryytn.modules.system.api.ProductCategoryService;
import cn.aliyun.ryytn.modules.system.api.SessionService;
import cn.aliyun.ryytn.modules.system.api.WarehouseService;
import cn.aliyun.ryytn.modules.system.dao.PageDao;
import cn.aliyun.ryytn.modules.system.dao.SessionDao;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;
import cn.aliyun.ryytn.modules.system.entity.dto.ProductCategoryDto;
import cn.aliyun.ryytn.modules.system.entity.vo.LoginVo;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWarehouseListRspVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 登录接口实现类
 * <AUTHOR>
 * @date 2023/10/10 10:31
 */
@Slf4j
@Service
public class SessionServiceImpl implements SessionService
{
    @Autowired
    private SessionDao sessionDao;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private ProductCategoryService productCategoryService;

    @Autowired
    private FactoryService factoryService;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private PageDao pageDao;

    /**
     *
     * @Description 登录
     * @param loginVo
     * @return Session
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:56
     */
    @Override
    public Session login(LoginVo loginVo) throws Exception
    {
        // 获取当前会话
        Session session = ServiceContextUtils.currentSession();
        Account currentAccount = session.getAccount();
        // 校验当前会话是否已绑定其他账号，如果已使用其他账号登录的会话，不能登录当前账号，并提示页面刷新
        if (Objects.nonNull(currentAccount) && !loginVo.getLoginId().equals(currentAccount.getLoginId()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_OTHER_ERROR);
        }

        // 验证码校验
        String sessionId = session.getSessionId();
        String captchaKey = StringUtils.format(CommonConstants.LOGIN_CAPTCHA_CACHE_KEY, sessionId);
        String captcha = StringUtils.lowerCase(StringUtils.getValue(redisUtils.get(captchaKey)));
        if (!StringUtils.lowerCase(loginVo.getCaptcha()).equals(captcha))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_CAPTCHA_ERROR);
        }

        // 根据登录账号查询账号数据
        Account account = sessionDao.queryAccountByLoginId(loginVo.getLoginId());
        if (Objects.isNull(account))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_ERROR);
        }

        // 密码校验
        if (!loginVo.getPassword().equals(account.getPassword()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_ERROR);
        }

        // OA账号只能单点登录，不能通过登录接口登录
        if (StringUtils.isNotEmpty(account.getOaId()))
        {
            throw new ServiceException("请前往OA进行登录");
        }

        // 状态非法
        if (!CommonConstants.DATA_STATUS_ENABLE.equals(account.getStatus()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_ERROR);
        }

        // 绑定会话账号，提API给单点登录使用
        bindSessionAccount(account);

        return session;
    }

    /**
     *
     * @Description 登出
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:56
     */
    @Override
    public void logout() throws Exception
    {
        // 获取当前会话
        Session session = ServiceContextUtils.currentSession();

        // 解除当前会话账号绑定
        session.setAccount(null);

        // 会话写入缓存
        String sessionId = session.getSessionId();
        String sessionKey = StringUtils.format(CommonConstants.SESSION_CACHE_KEY, sessionId);
        redisUtils.set(sessionKey, session);
    }

    /**
     *
     * @Description 获取当前会话
     * @return Session
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:56
     */
    @Override
    public Session getCurrentSession() throws Exception
    {
        return ServiceContextUtils.currentSession();
    }

    /**
     *
     * @Description 绑定会话账号
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月20日 10:40
     */
    @Override
    public void bindSessionAccount(Account account) throws Exception
    {
        // 查询关联的角色编号列表
        List<String> roleIdList = sessionDao.queryRoleIdListByAccount(account);
        account.setRoleIdList(roleIdList);

        // 如果关联角色列表中包含超级管理员，则后续查询授权数据均查询所有数据，不使用当前账号做条件
        if (roleIdList.contains(CommonConstants.SYSADMIN_ROLE_ID))
        {
            account.setIsAdmin(true);
        }
        else
        {
            account.setIsAdmin(false);
        }

        // 查询被授权的菜单集合
        List<Page> pageList = sessionDao.queryPageListByAccount(account);
        account.setPageList(pageList);
        if (CollectionUtils.isNotEmpty(pageList))
        {
            List<Page> pageTree = pageList.stream().filter(item -> StringUtils.equals(item.getType(), "1")).collect(Collectors.toList());
            // 遍历菜单，构造树形结构
            Iterator<Page> itor = pageTree.iterator();
            while (itor.hasNext())
            {
                Page page = itor.next();
                if (findParent(page, pageTree))
                {
                    itor.remove();
                }
            }
            account.setPageTree(pageTree);
        }

        List<Button> buttonList = sessionDao.queryButtonListByAccount(account);
        account.setButtonList(buttonList);

        // 查询被授权的按钮集合
        if (account.getIsAdmin())
        {
            // 查询所有渠道编号集合
            List<ChannelDto> channelList = channelService.queryChannelList();
            if (CollectionUtils.isNotEmpty(channelList))
            {
                List<String> channelIdList = channelList.stream().map(ChannelDto::getLv3ChannelCode).distinct().collect(Collectors.toList());
                account.setChannelIdList(channelIdList);
            }

            // 查询所有产品品类编号集合
            List<ProductCategoryDto> categoryList = productCategoryService.queryProductCategoryList();
            if (CollectionUtils.isNotEmpty(categoryList))
            {
                List<String> categoryIdList = categoryList.stream().map(ProductCategoryDto::getLv3CategoryCode).distinct().collect(Collectors.toList());
                account.setCategoryIdList(categoryIdList);
            }

            // 查询所有工厂编号集合
            List<Factory> factoryList = factoryService.queryAllFactory();
            if (CollectionUtils.isNotEmpty(factoryList))
            {
                List<String> factoryIdList = factoryList.stream().map(Factory::getActoryCode).distinct().collect(Collectors.toList());
                account.setFactoryIdList(factoryIdList);
            }

            // 查询所有仓库编号集合
            List<QueryWarehouseListRspVo> warehouseList = warehouseService.queryWarehouseList(null);
            if (CollectionUtils.isNotEmpty(warehouseList))
            {
                List<String> depositoryIdList =
                    warehouseList.stream().map(QueryWarehouseListRspVo::getBizWarehouseCode).distinct().collect(Collectors.toList());
                account.setDepositoryIdList(depositoryIdList);
            }
        }
        else
        {
            // 查询被授权的渠道编号集合
            List<String> channelIdList = sessionDao.queryChannelIdListByAccount(account);
            account.setChannelIdList(channelIdList);

            // 查询被授权的产品品类编号集合
            List<String> categoryIdList = sessionDao.queryCategoryIdListByAccount(account);
            account.setCategoryIdList(categoryIdList);

            // 查询被授权的工厂编号集合
            List<String> factoryIdList = sessionDao.queryFactoryIdListByAccount(account);
            account.setFactoryIdList(factoryIdList);

            // 查询被授权的仓库编号集合
            List<String> depositoryIdList = sessionDao.queryDepositoryIdListByAccount(account);
            account.setDepositoryIdList(depositoryIdList);
        }

        // 会话绑定账号，并写入缓存
        Session session = ServiceContextUtils.currentSession();
        String sessionKey = StringUtils.format(CommonConstants.SESSION_CACHE_KEY, session.getSessionId());
        Date now = new Date();
        session.setLoginTime(now);
        session.setExpireTime(DateUtils.addSeconds(now,Integer.parseInt(String.valueOf(CommonConstants.SESSION_EXPIRE_TIME))));
        session.setAccount(account);
        redisUtils.set(sessionKey, session,CommonConstants.SESSION_EXPIRE_TIME);
    }

    /**
     *
     * @Description 递归找父节点
     * @param page
     * @param menus
     * @return boolean
     * <AUTHOR>
     * @date 2023年10月08日 13:53
     */
    private boolean findParent(Page page, List<Page> menus)
    {
        for (Page m : menus)
        {
            if (CollectionUtils.isNotEmpty(m.getSubPageList()))
            {
                if (findParent(page, m.getSubPageList()))
                {
                    return true;
                }
            }
            if (StringUtils.equals(m.getId(), page.getParentId()))
            {
                if (null == m.getSubPageList())
                {
                    m.setSubPageList(new ArrayList<>());
                }
                m.getSubPageList().add(page);
                return true;
            }
        }
        return false;
    }
}
