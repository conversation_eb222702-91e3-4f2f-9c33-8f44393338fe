package cn.aliyun.ryytn.modules.system.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.DictData;
import cn.aliyun.ryytn.common.entity.DictType;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.exception.ParameterException;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.DictService;
import cn.aliyun.ryytn.modules.system.dao.DictDao;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 字典接口实现
 * <AUTHOR>
 * @date 2023/9/25 11:11
 */
@Slf4j
@Service
public class DictServiceImpl implements DictService, ApplicationListener<ContextRefreshedEvent>
{
    @Autowired
    private DictDao dictDao;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event)
    {
        try
        {
            loadDictCache();
        }
        catch (Exception e)
        {
            log.error("onApplicationEvent refreshSystemConfig has exceptoin:", e);
        }
    }

    /**
     *
     * @Description 分页查询字典类型列表
     * @param condition
     * @return List<DictType>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:28:40
     */
    @Override
    public List<DictType> queryDictTypeList(DictType dictType) throws Exception
    {
        // 查询数据库字典类型列表
        List<DictType> dictTypeList = dictDao.queryDictTypeList(dictType);

        return dictTypeList;
    }

    /**
     *
     * @Description 分页查询字典类型列表
     * @param condition
     * @return Page<DictType>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:28:40
     */
    @Override
    public PageInfo<DictType> queryDictTypePage(PageCondition<DictType> condition) throws Exception
    {
        DictType dictType = condition.getCondition();

        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());

        // 查询数据库字典类型列表
        List<DictType> dictTypeList = dictDao.queryDictTypeList(dictType);

        PageInfo<DictType> dictTypePageInfo = new PageInfo<DictType>(dictTypeList);

        return dictTypePageInfo;
    }

    /**
     *
     * @Description 查询字典类型详情
     * @param dictTypeId
     * @return DictType
     * <AUTHOR>
     * @date 2023年9月25日 下午9:30:13
     */
    @Override
    public DictType queryDictTypeDetail(String dictTypeId) throws Exception
    {
        // 查询数据库字典类型详情
        DictType dictTypeDetail = dictDao.queryDictTypeDetail(dictTypeId);

        return dictTypeDetail;
    }

    /**
     *
     * @Description 新增字典类型
     * @param dictType
     * <AUTHOR>
     * @date 2023年9月25日 下午9:31:04
     */
    @Override
    public void addDictType(DictType dictType) throws Exception
    {
        // 参数校验
        validateDictType(dictType);

        DictType dictTypeDB = dictDao.queryDictTypeByDictType(dictType.getDictType());
        // 如果字典类型存在未删除，则返回失败
        if (null != dictTypeDB && !dictTypeDB.getDeleteFlag())
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }

        // 封装字典类型其他字段
        Date currentDate = new Date();
        dictType.setCreatedBy(StringUtils.EMPTY);
        dictType.setCreatedTime(currentDate);
        // 状态默认正常
        dictType.setStatus(CommonConstants.DATA_STATUS_ENABLE);
        dictType.setDeleteFlag(false);
        // 新增字典类型
        dictType.setDataType(CommonConstants.DATA_TYPE_MANAGEMENT);

        // 如果当前字典类型状态是已删除，则恢复字典状态
        if (null != dictTypeDB && dictTypeDB.getDeleteFlag())
        {
            dictType.setDictTypeId(dictTypeDB.getDictTypeId());
            String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
            dictType.setUpdatedBy(loginId);
            dictType.setUpdatedTime(currentDate);
            // 恢复字典类型
            dictDao.recoveryDictType(dictType);

            // 不做级联恢复字典类型下的字典数据
        }
        else
        {
            dictDao.addDictType(dictType);
        }
    }

    /**
     *
     * @Description 修改字典类型
     * @param dictType
     * <AUTHOR>
     * @date 2023年9月25日 下午9:31:20
     */
    @Override
    public void updateDictType(DictType dictType) throws Exception
    {
        // 参数校验
        validateDictType(dictType);
        // 校验字典类型编号不能为空
        ValidateUtil.checkIsNotEmpty(dictType.getDictTypeId());
        // 校验字典状态是否在范围内，只能为1或2
        ValidateUtil.checkStrRegexp(StringUtils.getValue(dictType.getStatus()), "^[1-2]{1}$", true);

        DictType dictTypeDB = dictDao.queryDictTypeDetail(dictType.getDictTypeId());
        // 字典类型不存在
        if (null == dictTypeDB)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }
        // 初始化数据不允许修改
        if (CommonConstants.DATA_TYPE_INITIAL.equals(dictTypeDB.getDataType()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_INITED);
        }

        // 封装字典类型其他字段
        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        dictType.setUpdatedBy(loginId);
        dictType.setUpdatedTime(new Date());

        // 修改字典类型
        dictDao.updateDictType(dictType);

        log.info("Exit updateDictType");
    }

    /**
     *
     * @Description 删除字典类型
     * @param dictTypeIdList
     * <AUTHOR>
     * @date 2023年9月25日 下午9:31:33
     */
    @Override
    public void deleteDictType(List<String> dictTypeIdList) throws Exception
    {
        // 校验参数不能为空
        ValidateUtil.checkIsNotEmpty(dictTypeIdList);

        // 初始化字典不允许删除
        int initCount = dictDao.queryInitDictTypeCount(dictTypeIdList);
        if (initCount > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_INITED);
        }

        // 逻辑删除字典类型
        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        dictDao.deleteDictType(loginId, dictTypeIdList);

        // 不需要级联处理字典数据
        // 原则上所有字典数据一旦被使用，就不允许删除，当字典类型被逻辑删除时，页面已经对该字典类型和下面的字典数据不可见，是否级联删除对业务交互没有影响。
    }

    /**
     *
     * @Description 校验字典类型唯一性
     * @param dictType
     * <AUTHOR>
     * @date 2023年9月25日 下午9:31:45
     */
    @Override
    public void checkDictTypeUnique(String dictType) throws Exception
    {
        int count = dictDao.checkDictTypeUnique(dictType);
        if (count > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }
    }

    /**
     *
     * @Description 加载字典缓存
     * <AUTHOR>
     * @date 2023年9月25日 下午9:36:05
     */
    @Override
    public void loadDictCache() throws Exception
    {
        // 查询数据库所有字典数据
        List<DictData> dictDataList = dictDao.queryAllDictDataList();
        if (CollectionUtils.isEmpty(dictDataList))
        {
            return;
        }

        // 字典数据按字典类型分组
        Map<String, List<DictData>> map = dictDataList.stream().collect(Collectors.groupingBy(item -> item.getDictType()));

        // 刷新字典数据缓存
        for (String dictType : map.keySet())
        {
            // 写所有字典数据缓存
            String dictDataAllKey = StringUtils.format(CommonConstants.DICTDATA_ALL_CACHE_KEY, dictType);
            redisUtils.set(dictDataAllKey, map.get(dictType));

            // 初始化字典翻译缓存
            Map<String, Object> code2NameMap = new HashMap<String, Object>();
            Map<String, Object> name2CodeMap = new HashMap<String, Object>();
            for (DictData dictData : map.get(dictType))
            {
                code2NameMap.put(dictData.getCode(), dictData);
                name2CodeMap.put(dictData.getName(), dictData);
            }

            // 写字典翻译数据缓存
            String dictDataCode2NameKey = StringUtils.format(CommonConstants.DICTDATA_CODE2NAME_CACHE_KEY, dictType);
            String dictDataName2CodeKey = StringUtils.format(CommonConstants.DICTDATA_NAME2CODE_CACHE_KEY, dictType);
            redisUtils.hmset(dictDataCode2NameKey, code2NameMap);
            redisUtils.hmset(dictDataName2CodeKey, name2CodeMap);
        }
    }

    /**
     *
     * @Description 分页查询字典数据列表
     * @param condition
     * @return PageInfo<DictData>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:47:43
     */
    @Override
    public PageInfo<DictData> queryDictDataPage(PageCondition<DictData> condition) throws Exception
    {
        DictData dictData = condition.getCondition();

        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());

        // 查询字典数据列表
        List<DictData> dictDataList = dictDao.queryDictDataList(dictData);

        // 封装分页对象
        PageInfo<DictData> dictDataPageInfo = new PageInfo<DictData>(dictDataList);

        return dictDataPageInfo;
    }

    /**
     *
     * @Description 查询字典数据列表
     * @param dictData
     * @return List<DictData>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:47:43
     */
    @Override
    public List<DictData> queryDictDataList(DictData dictData) throws Exception
    {
        // 查询字典数据列表
        List<DictData> dictDataList = dictDao.queryDictDataList(dictData);

        return dictDataList;
    }

    /**
     *
     * @Description 查询字典数据树
     * @param dictData
     * @return List<DictData>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:47:43
     */
    @Override
    public List<DictData> queryDictDataTree(DictData dictData) throws Exception
    {
        // 查询字典数据列表
        List<DictData> dictDataList = dictDao.queryDictDataList(dictData);

        // 构造树形结构
        Iterator<DictData> itor = dictDataList.iterator();
        while (itor.hasNext())
        {
            DictData tempDictData = itor.next();
            if (findParent(tempDictData, dictDataList))
            {
                itor.remove();
            }
        }
        return dictDataList;
    }

    /**
     *
     * @Description 查询字典数据详情
     * @param dictId
     * @return DictData
     * <AUTHOR>
     * @date 2023年9月25日 下午9:49:54
     */
    @Override
    public DictData queryDictDataDetail(String dictId) throws Exception
    {
        // 查询字典数据列表
        DictData dictData = dictDao.queryDictDataDetail(dictId);

        return dictData;
    }

    /**
     *
     * @Description 新增字典数据
     * @param dictData
     * <AUTHOR>
     * @date 2023年9月25日 下午10:07:09
     */
    @SuppressWarnings("null")
    @Override
    public void addDictData(DictData dictData) throws Exception
    {
        // 根据字典类型和编号查询数据库中的字典数据
        DictData dictDataDB = dictDao.queryDictDataByDictData(dictData);

        // 如果字典数据已存在且状态为未删除，返回失败
        if (null != dictDataDB && !dictDataDB.getDeleteFlag())
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }

        // 封装字典数据对象
        if (StringUtils.isEmpty(dictData.getParentId()) || CommonConstants.TREE_ROOT_PARENTID.equals(dictData.getParentId()))
        {
            dictData.setParentId(CommonConstants.TREE_ROOT_PARENTID);
            dictData.setParentIds(CommonConstants.TREE_ROOT_PARENTID);
            dictData.setLevel(1);
            dictData.setLeafFlag(true);
        }
        else
        {
            // 查询父字典数据
            DictData parentDictData = dictDao.queryParentDictData(dictData.getParentId());
            // 如果父字典数据不存在，抛异常
            if (null == parentDictData)
            {
                throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
            }

            // 如果父字典是叶子节点，修改父字典是否叶子节点为否
            if (parentDictData.getLeafFlag())
            {
                parentDictData.setLeafFlag(false);
                dictDao.updateDictDataLeafFlag(parentDictData);
            }

            // 封装字典数据
            StringBuilder sb = new StringBuilder(parentDictData.getParentIds());
            dictData.setParentIds(sb.append(CommonConstants.STRING_SEPARATOR).append(dictData.getParentId()).toString());
            dictData.setLevel(Math.addExact(parentDictData.getLevel(), 1));
            dictData.setLeafFlag(true);
        }
        Date currentDate = new Date();
        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        dictData.setCreatedBy(loginId);
        dictData.setCreatedTime(currentDate);
        dictData.setStatus(CommonConstants.DATA_STATUS_ENABLE);
        dictData.setDeleteFlag(false);
        dictData.setDataType(CommonConstants.DATA_TYPE_MANAGEMENT);

        // 如果字典数据已删除，则恢复字典数据
        if (null != dictDataDB && dictDataDB.getDeleteFlag())
        {
            dictData.setDictId(dictDataDB.getDictId());
            dictData.setUpdatedBy(StringUtils.EMPTY);
            dictData.setUpdatedTime(currentDate);
            dictDao.recoveryDictData(dictData);
        }
        else
        {
            dictDao.addDictData(dictData);
        }

        log.info("Exit addDictData");
    }

    /**
     *
     * @Description 修改字典数据
     * @param dictData
     * <AUTHOR>
     * @date 2023年9月25日 下午10:07:28
     */
    @Override
    public void updateDictData(DictData dictData) throws Exception
    {
        // 参数校验
        validateDictData(dictData);
        // 校验字典编号不能为空
        ValidateUtil.checkIsNotEmpty(dictData.getDictId());

        // 根据字典类型和编号查询数据库中的字典数据
        DictData dictDataDB = dictDao.queryDictDataDetail(dictData.getDictId());

        // 校验字典数据不存在
        if (null == dictDataDB)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }

        if (CommonConstants.DATA_TYPE_INITIAL.equals(dictDataDB.getDataType()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_INITED);
        }

        // 封装字典数据对象
        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        dictData.setUpdatedBy(loginId);
        dictData.setUpdatedTime(new Date());

        dictDao.updateDictData(dictData);

        log.info("Exit updateDictData");
    }

    /**
     *
     * @Description 删除字典数据
     * @param dictIdList
     * <AUTHOR>
     * @date 2023年9月25日 下午10:06:49
     */
    @Override
    public void deleteDictData(List<String> dictIdList) throws Exception
    {
        // 校验参数不能为空
        ValidateUtil.checkIsNotEmpty(dictIdList);

        // 初始化字典不允许删除
        int initCount = dictDao.queryInitDictDataCount(dictIdList);
        if (initCount > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_INITED);
        }

        // 逻辑删除字典数据（暂不考虑字典类型是管理数据，但却有初始化字典数据的场景），字典暂不支持国际化
        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        dictDao.deleteDictData(loginId, dictIdList);
    }

    @Override
    public void checkDictDataUnique(DictData dictData) throws Exception
    {
        // 查询字典类型和字典编码是否已存在
        int count = dictDao.checkDictDataUnique(dictData);

        if (count > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }
    }

    /**
     *
     * @Description 查询全部数据字典
     * @return List<DictType>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:47:43
     */
    @Override
    public List<DictData> queryAllDictList() throws Exception
    {
        List<DictData> dictDataList = dictDao.queryAllDictDataList();

        return dictDataList;
    }

    /**
     *
     * @Description 递归函数，生成树
     * @param dictDataTree
     * @param dictDataList
     * @return boolean
     * <AUTHOR>
     * @date 2023年9月19日 下午5:34:59
     */
    private boolean findParent(DictData dictDataTree, List<DictData> dictDataList)
    {
        for (DictData item : dictDataList)
        {
            if (CollectionUtils.isNotEmpty(item.getSubDictDataList()))
            {
                if (findParent(dictDataTree, item.getSubDictDataList()))
                {
                    return true;
                }
            }
            if (StringUtils.equals(item.getDictId(), dictDataTree.getParentId()))
            {
                if (null == item.getSubDictDataList())
                {
                    item.setSubDictDataList(new ArrayList<>());
                }
                item.getSubDictDataList().add(dictDataTree);
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @Description 校验DictType参数
     * @param dictType
     * @throws ParameterException
     * <AUTHOR>
     * @date 2023年09月25日 11:38
     */
    private void validateDictType(DictType dictType) throws ParameterException
    {
        // 校验参数不能为空
        ValidateUtil.checkIsNotEmpty(dictType);

        // 校验字典类型不能为空
        ValidateUtil.checkIsNotEmpty(dictType.getDictType());

        // 校验字典类型长度超长
        ValidateUtil.checkStrLength(dictType.getDictType(), 1, 64, false);

        // 校验字典类型非法，只能由大小写字母、数字、英文句号组成
        ValidateUtil.checkStrRegexp(dictType.getDictType(), "^[A-Za-z0-9._]+$", false);

        // 校验字典名称不能为空
        ValidateUtil.checkIsNotEmpty(dictType.getDictName());

        // 校验字典名称长度超长
        ValidateUtil.checkStrLength(dictType.getDictName(), 1, 64, false);

        // 校验字典名称非法，只能由大小写字母、数字、汉字组成
        ValidateUtil.checkStrRegexp(dictType.getDictName(), "^[A-Za-z0-9\u4e00-\u9fa5]+$", false);

        // 校验字典描述长度超长
        ValidateUtil.checkStrLength(dictType.getDescription(), 1, 256, true);
    }

    /**
     *
     * @Description 校验DictData参数
     * @param dictType
     * @throws ParameterException
     * <AUTHOR>
     * @date 2023年09月25日 11:38
     */
    private void validateDictData(DictData dictData) throws ParameterException
    {
        // 校验参数不能为空
        ValidateUtil.checkIsNotEmpty(dictData);

        // 校验字典类型不能为空
        ValidateUtil.checkIsNotEmpty(dictData.getDictType());

        // 校验字典名称长度超长
        ValidateUtil.checkStrLength(dictData.getName(), 1, 64, false);

        // 校验字典名称非法，只能由大小写字母、数字、汉字组成
        ValidateUtil.checkStrRegexp(dictData.getName(), "^[A-Za-z0-9\u4e00-\u9fa5]+$", false);

        // 校验字典名称长度超长
        ValidateUtil.checkStrLength(dictData.getCode(), 1, 64, false);

        // 校验字典编码非法，只能由数字组成
        ValidateUtil.checkStrRegexp(dictData.getCode(), "^[A-Za-z0-9]+$", false);

        // 校验字典描述长度超长
        ValidateUtil.checkStrLength(dictData.getDescription(), 1, 256, true);
    }
}
