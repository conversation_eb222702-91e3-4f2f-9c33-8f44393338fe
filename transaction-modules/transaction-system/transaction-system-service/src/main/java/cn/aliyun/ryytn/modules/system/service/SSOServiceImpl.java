package cn.aliyun.ryytn.modules.system.service;

import java.net.URLDecoder;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.CharsetKit;
import cn.aliyun.ryytn.common.utils.string.RSAUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.SSOService;
import cn.aliyun.ryytn.modules.system.api.SessionService;
import cn.aliyun.ryytn.modules.system.dao.SSODao;
import cn.aliyun.ryytn.modules.system.dao.SessionDao;
import cn.aliyun.ryytn.modules.system.entity.dto.ThirdpartSystem;
import cn.aliyun.ryytn.modules.system.entity.vo.SSOSignature;

/**
 * @Description 单点登录实现类
 * <AUTHOR>
 * @date 2023/10/20 10:27
 */
@Service
public class SSOServiceImpl implements SSOService
{
    @Autowired
    private SessionService sessionService;

    @Autowired
    private SSODao ssoDao;

    @Autowired
    private SessionDao sessionDao;

    @Autowired
    private RedisUtils redisUtils;

    private static final String OA_CHECK_TIME_DEFUALT = "15";

    /**
     *
     * @Description OA系统登录服务
     * @param info
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月20日 10:55
     */
    @Override
    public void oaLogin(String info) throws Exception
    {
        // 获取当前时间
        long currentTime = System.currentTimeMillis();

        // 私钥解密签名
        String signature = RSAUtils.decryptByPrivateKey(URLDecoder.decode(info.replace("+", "%2B"), CharsetKit.UTF_8));
        SSOSignature ssoSignature = JSON.parseObject(signature, SSOSignature.class);

        // 校验签名内容
        if (Objects.isNull(ssoSignature) || StringUtils.isBlank(ssoSignature.getRequestSource()) || StringUtils.isBlank(ssoSignature.getLoginName()) ||
            StringUtils.isBlank(ssoSignature.getCode()) || Objects.isNull(ssoSignature.getTs()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_ERROR);
        }

        // 校验时间戳
        Long ssoCheckTime = Long.valueOf(
            StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "SSO_CHECK_TIME"), OA_CHECK_TIME_DEFUALT));
        if (Math.abs(currentTime - ssoSignature.getTs()) > ssoCheckTime * 1000)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_ERROR);
        }

        // 校验来源和权限码
        ThirdpartSystem thirdpartSystem = ssoDao.queryThirdpartSystem(ssoSignature.getRequestSource(), ssoSignature.getCode());
        if (Objects.isNull(thirdpartSystem))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_ERROR);
        }

        // 校验请求是否被重复使用
        String key = StringUtils.format(CommonConstants.SSO_SIGNATURE_RID_KEY, ssoSignature.getRequestSource(), info);
        if (redisUtils.hasKey(key))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_ERROR);
        }

        // 签名写入缓存，防止签名被重复使用
        redisUtils.set(key, ssoSignature, ssoCheckTime);

        // 校验用户名
        Account account = sessionDao.queryAccountByLoginId(ssoSignature.getLoginName());
        if (Objects.isNull(account))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_ERROR);
        }

        // 状态非法
        if (!CommonConstants.DATA_STATUS_ENABLE.equals(account.getStatus()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_ERROR);
        }

        // 获取当前会话
//        Session session = ServiceContextUtils.currentSession();
//        Account currentAccount = session.getAccount();
//        // 校验当前会话是否已绑定其他账号，如果已使用其他账号登录的会话，不能登录当前账号，并提示页面刷新
//        if (Objects.nonNull(currentAccount) && !account.getLoginId().equals(currentAccount.getLoginId()))
//        {
//            throw new ServiceException(ErrorCodeConstants.FAIL_LOGIN_OTHER_ERROR);
//        }

        // 绑定会话账号
        sessionService.bindSessionAccount(account);

        return;
    }
}
