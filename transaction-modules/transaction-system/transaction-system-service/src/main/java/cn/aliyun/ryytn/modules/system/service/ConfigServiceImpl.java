package cn.aliyun.ryytn.modules.system.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ServiceFileRef;
import cn.aliyun.ryytn.common.entity.SystemConfig;
import cn.aliyun.ryytn.common.entity.SystemConfigCategory;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.ConfigService;
import cn.aliyun.ryytn.modules.system.dao.ConfigDao;
import cn.aliyun.ryytn.modules.system.dao.FileDao;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 系统参数配置管理接口实现类
 * <AUTHOR>
 * @date 2023年9月19日 下午2:11:38
 */
@Slf4j
@Service
public class ConfigServiceImpl implements ConfigService, ApplicationListener<ContextRefreshedEvent>
{
    /**
     * Redis工具
     */
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 系统参数配置Dao
     */
    @Autowired
    private ConfigDao configDao;

    /**
     * 文件Dao
     */
    @Autowired
    private FileDao fileDao;

    /**
     * 配置类型：文件
     */
    private static final Integer CONFIG_TYPE_FILE = 3;

    /**
     * 文件关联表类型：系统参数配置
     */
    private static final String FILE_REF_SERVICE_TYPE = "t_ryytn_config";


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event)
    {
        try
        {
            refreshSystemConfig();
        }
        catch (Exception e)
        {
            log.error("onApplicationEvent refreshSystemConfig has exceptoin:", e);
        }
    }

    /**
     *
     * @Description 查询系统参数类型列表
     * @return List<SystemConfigCategory>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:12
     */
    @Override
    public List<SystemConfigCategory> querySystemConfigCategoryList() throws Exception
    {
        List<SystemConfigCategory> systemConfigCategoryList = configDao.querySystemConfigCategoryList();

        return systemConfigCategoryList;
    }

    /**
     *
     * @Description 分页查询系统参数列表
     * @param condition
     * @return PageInfo<SystemConfig>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:34
     */
    @Override
    public PageInfo<SystemConfig> querySystemConfigList(PageCondition<SystemConfig> condition) throws Exception
    {
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());

        SystemConfig SystemConfig = condition.getCondition();
        if (Objects.isNull(SystemConfig))
        {
            SystemConfig = new SystemConfig();
        }
        // 开放给前端使用，只查询isDisplay是1的数据
        SystemConfig.setIsDisplay(1);

        List<SystemConfig> systemConfigList = configDao.querySystemConfigList(SystemConfig);
        PageInfo<SystemConfig> systemConfigPage = new PageInfo<SystemConfig>(systemConfigList);

        return systemConfigPage;
    }

    /**
     *
     * @Description 查询系统参数列表
     * @param SystemConfig
     * @return List<SystemConfig>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:34
     */
    @Override
    public List<SystemConfig> querySystemConfig(SystemConfig SystemConfig) throws Exception
    {
        List<SystemConfig> systemConfigList = configDao.querySystemConfigList(SystemConfig);

        return systemConfigList;
    }

    /**
     *
     * @Description 修改系统参数
     * @param systemConfig
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:57
     */
    @Override
    public void updateSystemConfig(SystemConfig systemConfig) throws Exception
    {
        // 校验参数不能为空
        ValidateUtil.checkIsNotEmpty(systemConfig);
        ValidateUtil.checkIsNotEmpty(systemConfig.getConfigId());

        SystemConfig newSystemConfig = configDao.querySystemConfigDetail(systemConfig);

        // 校验配置不存在
        if (null == newSystemConfig)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }

        // 校验配置值是否合法，如果正则为空，则配置值可以为空
        ValidateUtil.checkStrRegexp(systemConfig.getConfigValue(), newSystemConfig.getValidator(), StringUtils.isEmpty(newSystemConfig.getValidator()));

        // 封装系统配置新的值
        newSystemConfig.setConfigValue(systemConfig.getConfigValue());
        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
        newSystemConfig.setUpdatedBy(loginId);
        newSystemConfig.setUpdatedTime(new Date());

        // 修改数据库系统配置
        configDao.updateSystemConfig(newSystemConfig);

        // 如果是文件类型，需要同步修改文件表业务类型为参数配置
        List<ServiceFileRef> serviceFileRefList = new ArrayList<ServiceFileRef>();
        if (CONFIG_TYPE_FILE.equals(newSystemConfig.getConfigType()))
        {
            List<String> fileIdList = Arrays.asList(newSystemConfig.getConfigValue().split(","));
            for (String fileId : fileIdList)
            {
                ServiceFileRef fileRef = new ServiceFileRef();
                fileRef.setFileId(fileId);
                fileRef.setServiceId(systemConfig.getConfigId());
                fileRef.setServiceType(FILE_REF_SERVICE_TYPE);
                serviceFileRefList.add(fileRef);
            }
        }
        // 修改配置与文件关联关系
        if (CollectionUtils.isNotEmpty(serviceFileRefList))
        {
            fileDao.deleteFileRefByService(newSystemConfig.getConfigId(), FILE_REF_SERVICE_TYPE);
            fileDao.addServiceFileRefList(serviceFileRefList);
        }

        // 刷新缓存中系统配置
        redisUtils.hset(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, systemConfig.getConfigId(), systemConfig.getConfigValue());
    }

    /**
     *
     * @Description 批量修改系统参数
     * @param systemConfigList
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:19:12
     */
    @Override
    public void batchUpdateSystemConfig(List<SystemConfig> systemConfigList) throws Exception
    {
        if (CollectionUtils.isEmpty(systemConfigList))
        {
            return;
        }

        // 查询数据库当前配置
        List<SystemConfig> SystemConfigs = configDao.querySystemConfigDetailList(systemConfigList);
        ValidateUtil.checkIsNotEmpty(SystemConfigs);
        Map<String, SystemConfig> regexMap = SystemConfigs.stream().collect(Collectors.toMap(SystemConfig::getConfigId, Function.identity()));

        // 正则校验，如果正则为空，则配置值可以为空
        for (SystemConfig systemConfig : systemConfigList)
        {
            String validator = regexMap.get(systemConfig.getConfigId()).getValidator();
            ValidateUtil.checkStrRegexp(systemConfig.getConfigValue(), validator, StringUtils.isEmpty(validator));

            String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
            systemConfig.setUpdatedBy(loginId);
            systemConfig.setUpdatedTime(new Date());
        }

        // 修改数据库系统配置
        configDao.batchUpdateSystemConfig(systemConfigList);

        List<ServiceFileRef> serviceFileRefList = new ArrayList<ServiceFileRef>();
        List<String> serviceIdList = new ArrayList<String>();
        // 刷新缓存中系统配置
        for (SystemConfig systemConfig : systemConfigList)
        {
            // 刷新缓存
            redisUtils.hset(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, systemConfig.getConfigId(), systemConfig.getConfigValue());

            // 如果是文件类型，需要同步修改文件表业务类型为参数配置
            if (CONFIG_TYPE_FILE.equals(systemConfig.getConfigType()))
            {
                List<String> tempList = Arrays.asList(systemConfig.getConfigValue().split(","));
                for (String fileId : tempList)
                {
                    ServiceFileRef fileRef = new ServiceFileRef();
                    fileRef.setFileId(fileId);
                    fileRef.setServiceId(systemConfig.getConfigId());
                    fileRef.setServiceType("edu_service_config");
                    serviceFileRefList.add(fileRef);
                }
                serviceIdList.add(systemConfig.getConfigId());
            }
        }

        // 修改配置与文件关联关系
        if (CollectionUtils.isNotEmpty(serviceFileRefList))
        {
            fileDao.deleteFileRefByServiceList(serviceIdList, FILE_REF_SERVICE_TYPE);
            fileDao.addServiceFileRefList(serviceFileRefList);
        }
    }

    /**
     *
     * @Description 刷新系统缓存
     * @throws Exception
     * <AUTHOR>
     * @date 2022年8月22日 上午10:56:21
     */
    @Override
    public void refreshSystemConfig() throws Exception
    {
        List<SystemConfig> systemConfigList = configDao.queryAllSystemConfigList();
        if (CollectionUtils.isEmpty(systemConfigList))
        {
            return;
        }

        // 刷新缓存中系统配置
        Map<String, Object> systemConfigMap = systemConfigList.stream().collect(Collectors.toMap(SystemConfig::getConfigId, SystemConfig::getConfigValue,
            (a, b) -> a));
        redisUtils.hmset(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, systemConfigMap);
    }
}
