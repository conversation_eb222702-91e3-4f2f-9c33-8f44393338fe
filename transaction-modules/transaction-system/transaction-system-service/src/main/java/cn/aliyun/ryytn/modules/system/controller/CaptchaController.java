package cn.aliyun.ryytn.modules.system.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.Captcha;
import cn.aliyun.ryytn.common.utils.captcha.CaptchaUtils;
import cn.aliyun.ryytn.common.utils.file.FileUtils;
import cn.aliyun.ryytn.common.utils.string.CharsetKit;
import cn.aliyun.ryytn.modules.system.api.CaptchaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 验证码接口
 * <AUTHOR>
 * @date 2023/10/9 17:55
 */
@Slf4j
@RestController
@RequestMapping("/api/system/captcha")
@Api(tags = "验证码")
public class CaptchaController
{
    @Autowired
    private CaptchaService captchaService;

    /**
     *
     * @Description 获取登录验证码
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 18:01
     */
    @GetMapping("queryLoginCaptcha")
    @ResponseBody
    @ApiOperation("获取登录验证码")
    public void queryLoginCaptcha(HttpServletRequest request, HttpServletResponse response) throws Exception
    {
        // 获取登录验证码
        Captcha captcha = captchaService.queryLoginCaptcha();

        response.addHeader(HttpHeaders.CONTENT_TYPE, FileUtils.FILE_EXTNAME_CONTENTTYPE_MAP.get(CaptchaUtils.suffix));
        response.addHeader(HttpHeaders.CONTENT_DISPOSITION, FileUtils.CONTENTDISPOSITION_INLINE + "captcha");
        response.setCharacterEncoding(CharsetKit.UTF_8);
        response.setHeader(HttpHeaders.ACCEPT_RANGES, "bytes");
        response.setContentLength(captcha.getBytes().length);
        response.getOutputStream().write(captcha.getBytes());
    }
}
