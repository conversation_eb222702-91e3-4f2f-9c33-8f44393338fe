package cn.aliyun.ryytn.modules.system.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.MD5Utils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.AccountService;
import cn.aliyun.ryytn.modules.system.dao.AccountDao;
import cn.aliyun.ryytn.modules.system.dao.RoleDao;
import cn.aliyun.ryytn.modules.system.dao.SessionDao;
import cn.aliyun.ryytn.modules.system.entity.dto.AccountRole;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 本地账号管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午3:11:58
 */
@Slf4j
@Service
public class AccountServiceImpl implements AccountService
{
    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RoleDao roleDao;

    @Autowired
    private SessionDao sessionDao;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 分页查询账号列表
     * @param condition
     * @return PageInfo<Account>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:11
     */
    @Override
    public PageInfo<Account> queryAccountPage(PageCondition<Account> condition) throws Exception
    {
        // 初始化分页对象
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        Account account = condition.getCondition();

        // 查询数据库
        List<Account> accountList = accountDao.queryAccountList(account);
        if (CollectionUtils.isNotEmpty(accountList))
        {
            for (Account item : accountList)
            {
                if (StringUtils.isNotEmpty(item.getRoleIds()))
                {
                    item.setRoleIdList(Arrays.asList(StringUtils.split(item.getRoleIds(), StringUtils.COMMA_SEPARATOR)));
                }
            }
        }

        PageInfo<Account> page = new PageInfo<>(accountList);
        return page;
    }

    /**
     *
     * @Description 新增账号
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @Override
    public void addAccount(Account account) throws Exception
    {
        // 封装编号、状态和类型
        account.setId(SeqUtils.getSequenceUid());
        account.setStatus(CommonConstants.DATA_STATUS_ENABLE);
        account.setDataType(CommonConstants.DATA_TYPE_MANAGEMENT);
        // 密码如果为空，则按默认密码新增
        if (StringUtils.isBlank(account.getPassword()))
        {
            String defaultPassword = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "ACCOUNT_DEFAULT_PASSWORD");
            String password = MD5Utils.encrypt(defaultPassword);
            account.setPassword(password);
        }

        // 校验登录账号是否重复
        int num = accountDao.queryLoginIdRepeat(account);
        if (num > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }

        // 新增账号
        accountDao.addAccount(account);

        // 如果角色列表不为空，新增绑定角色
        if (CollectionUtils.isNotEmpty(account.getRoleIdList()))
        {
            List<AccountRole> accountRoleList = new ArrayList<>(account.getRoleIdList().size());
            for (String roleId : account.getRoleIdList())
            {
                if (StringUtils.isBlank(roleId))
                {
                    continue;
                }
                AccountRole accountRole = new AccountRole();
                accountRole.setAccountId(account.getId());
                accountRole.setRoleId(roleId);
                accountRoleList.add(accountRole);
            }
            if (CollectionUtils.isNotEmpty(accountRoleList))
            {
                roleDao.addAccountRole(accountRoleList);
            }
        }
    }

    /**
     *
     * @Description 修改账号，仅修改姓名、工号、描述
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @Override
    public void updateAccount(Account account) throws Exception
    {
        // 校验登录账号是否重复
        int num = accountDao.queryLoginIdRepeat(account);
        if (num > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }

        // 校验账号是否存在
        Account accountExists = accountDao.queryAccountById(account.getId());
        if (Objects.isNull(accountExists))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }

        // 初始化账号或外部系统同步账号不能修改
        if (CommonConstants.DATA_TYPE_INITIAL.equals(accountExists.getDataType()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_INITED);
        }

        // 修改账号，仅修改姓名、工号、描述
        accountDao.updateAccount(account);

        // 删除账号角色绑定关系
        accountDao.deleteAccountRole(account.getId());

        // 如果角色列表不为空，新增绑定角色
        if (CollectionUtils.isNotEmpty(account.getRoleIdList()))
        {
            List<AccountRole> accountRoleList = new ArrayList<>(account.getRoleIdList().size());
            for (String roleId : account.getRoleIdList())
            {
                if (StringUtils.isBlank(roleId))
                {
                    continue;
                }
                AccountRole accountRole = new AccountRole();
                accountRole.setAccountId(account.getId());
                accountRole.setRoleId(roleId);
                accountRoleList.add(accountRole);
            }
            if (CollectionUtils.isNotEmpty(accountRoleList))
            {
                roleDao.addAccountRole(accountRoleList);
            }
        }
    }

    /**
     *
     * @Description 修改账号状态
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @Override
    public void updateAccountStatus(Account account) throws Exception
    {
        // 校验账号是否存在
        Account accountExists = accountDao.queryAccountById(account.getId());
        if (Objects.isNull(accountExists))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }

        // 初始化账号或外部系统同步账号不能修改
        if (CommonConstants.DATA_TYPE_INITIAL.equals(accountExists.getDataType()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_INITED);
        }

        // 修改账号状态
        accountDao.updateAccountStatus(account);
    }

    /**
     *
     * @Description 修改账号密码
     * @param account
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @Override
    public void updateAccountPassword(Account account) throws Exception
    {
        // 校验账号是否存在
        Account accountExists = sessionDao.queryAccountByLoginId(account.getLoginId());
        if (Objects.isNull(accountExists))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }

        // 内部账号不支持修改密码
        if (StringUtils.isNotEmpty(accountExists.getOaId()))
        {
            throw new ServiceException("您没有这个权限");
        }

        if (!StringUtils.equals(accountExists.getPassword(), account.getPassword()))
        {
            throw new ServiceException("您输入的密码有误，请重新输入");
        }

        // 修改账号密码
        accountDao.updateAccountPassword(account);
    }

    /**
     *
     * @Description 重置密码为默认密码
     * @param id
     * @return String 新密码
     * 此处设计不合理
     * 正常应该要么返回随机密码，要么直接修改为固定密码不返回。
     * 但是根据原型图设计要求，需要提示新密码，但是新密码又是固定的。
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @Override
    public String resetPassword(String id) throws Exception
    {
        // 获取系统配置默认密码
        String defaultPassword = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "ACCOUNT_DEFAULT_PASSWORD");
        String password = MD5Utils.encrypt(defaultPassword);

        // 重置密码为默认密码
        accountDao.resetAccountPassword(id, password);

        return defaultPassword;
    }

    /**
     *
     * @Description 删除账号
     * @param id
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 17:12
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAccount(String id) throws Exception
    {
        // 校验账号是否存在
        Account accountExists = accountDao.queryAccountById(id);
        if (Objects.isNull(accountExists))
        {
            return;
        }

        // 初始化账号或外部系统同步账号不能修改
        if (CommonConstants.DATA_TYPE_INITIAL.equals(accountExists.getDataType()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_INITED);
        }

        // 删除账号
        accountDao.deleteAccount(id);

        // 删除账号关联角色
        accountDao.deleteAccountRole(id);

        // TODO 强制退出登录，待实现登录后实现
    }
}
