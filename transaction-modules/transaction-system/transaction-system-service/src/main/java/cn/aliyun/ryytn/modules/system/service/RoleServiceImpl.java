package cn.aliyun.ryytn.modules.system.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.Role;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.modules.system.api.RoleService;
import cn.aliyun.ryytn.modules.system.dao.AccountDao;
import cn.aliyun.ryytn.modules.system.dao.RoleDao;
import cn.aliyun.ryytn.modules.system.dataqdao.DataqCategoryDao;
import cn.aliyun.ryytn.modules.system.dataqdao.DataqChannelDao;
import cn.aliyun.ryytn.modules.system.entity.dto.AccountRole;
import cn.aliyun.ryytn.modules.system.entity.dto.RoleButton;
import cn.aliyun.ryytn.modules.system.entity.dto.RoleChannel;
import cn.aliyun.ryytn.modules.system.entity.dto.RoleDepository;
import cn.aliyun.ryytn.modules.system.entity.dto.RoleFactory;
import cn.aliyun.ryytn.modules.system.entity.dto.RolePage;
import cn.aliyun.ryytn.modules.system.entity.dto.RoleProductCategory;
import cn.aliyun.ryytn.modules.system.entity.vo.AccountRoleVo;
import cn.aliyun.ryytn.modules.system.entity.vo.RoleConditionVo;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 角色管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午3:11:58
 */
@Slf4j
@Service
public class RoleServiceImpl implements RoleService
{
    @Autowired
    private RoleDao roleDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private DataqChannelDao dataqChannelDao;

    @Autowired
    private DataqCategoryDao dataqCategoryDao;

    /**
     *
     * @Description 分页查询角色列表
     * @param condition
     * @return PageInfo<Role>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 17:15
     */
    @Override
    public PageInfo<Role> queryRolePage(PageCondition<RoleConditionVo> condition) throws Exception
    {
        // 初始化分页拦截器
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        RoleConditionVo roleConditionVo = condition.getCondition();

        // 查询角色列表
        List<Role> roleList = roleDao.queryRoleList(roleConditionVo);
        PageInfo<Role> result = new PageInfo<>(roleList);

        return result;
    }

    /**
     *
     * @Description 查询角色详情
     * @param id
     * @return Role
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 15:28
     */
    @Override
    public Role queryRoleDetail(String id) throws Exception
    {
        // 查询角色详情
        Role role = roleDao.queryRoleDetail(id);
        if (Objects.isNull(role))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_NOTEXISTS);
        }

        // 查询已授权的菜单编号列表
        List<String> pageIdList = roleDao.queryPageIdListByRoleId(id);
        role.setPageIdList(pageIdList);

        // 查询已授权的按钮编号列表
        List<String> buttonIdList = roleDao.queryButtonIdListByRoleId(id);
        role.setButtonIdList(buttonIdList);

        // 查询已授权的渠道编号列表
        List<String> channelIdList = roleDao.queryChannelIdListByRoleId(id);
        // 只返回三级编号
        // 原因如下：
        // 客户系统渠道/品类等数据没有编号，只有名称，本系统中该数据的编号均为阿里数据加工根据名称hash得到。
        // 客户修改渠道名称时，导致渠道编号发生变化。
        // 原来权限树种如果存在所有子节点被选中，导致父节点级联勾选的情况，保存时会将父子节点都入库。
        // 查询已有权限时，会导致树的父节点被选中，新增/修改名称的下级节点也被级联选中。
        // 造成权限树显示为有权限，但是实际没权限的假象。
        if (CollectionUtils.isNotEmpty(channelIdList))
        {
            Set<String> lv3ChannelCodeSet = dataqChannelDao.queryLv3ChannelCodeSet();
            channelIdList = channelIdList.stream().filter(item -> lv3ChannelCodeSet.contains(item)).collect(Collectors.toList());
        }
        role.setChannelIdList(channelIdList);

        // 查询已授权的产品品类编号列表
        List<String> categoryIdList = roleDao.queryProductCategoryIdListByRoleId(id);
        if (CollectionUtils.isNotEmpty(categoryIdList))
        {
            Set<String> lv3CategoryCodeSet = dataqCategoryDao.queryLv3CategoryCodeSet();
            categoryIdList.stream().filter(item -> lv3CategoryCodeSet.contains(item)).collect(Collectors.toList());
        }
        role.setCategoryIdList(categoryIdList);

        // 查询已授权的工厂编号列表
        List<String> factoryIdList = roleDao.queryFactoryIdListByRoleId(id);
        role.setFactoryIdList(factoryIdList);

        // 查询已授权的仓库编号列表
        List<String> depositoryIdList = roleDao.queryDepositoryIdListByRoleId(id);
        role.setDepositoryIdList(depositoryIdList);

        return role;
    }

    /**
     *
     * @Description 保存角色，级联更新授权数据，事务管理
     * @param role
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRole(Role role) throws Exception
    {
        // 校验角色名称是否重复
        int exists = roleDao.queryRoleExists(role);
        if (exists > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REPEAT);
        }

        // 角色编号，如果是新增，编号为空
        String roleId = role.getId();
        String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();

        // 编号为空，新增数据
        if (StringUtils.isBlank(roleId))
        {
            // 生成唯一编号
            roleId = SeqUtils.getSequenceUid();
            role.setId(roleId);

            // 不是默认角色
            role.setDefaultFlag(false);
            role.setDataType(CommonConstants.DATA_TYPE_MANAGEMENT);
            role.setCreatedBy(loginId);

            // 新增角色数据
            roleDao.addRole(role);
        }
        // 编号不为空，修改数据
        else
        {
            role.setUpdatedBy(loginId);

            // 修改角色数据
            roleDao.updateRole(role);

            // 删除角色授权菜单数据
            roleDao.deleteRolePageByRoleId(roleId);

            // 删除角色授权按钮数据
            roleDao.deleteRoleButtonByRoleId(roleId);

            // 删除角色授权渠道数据
            roleDao.deleteRoleChannelByRoleId(roleId);

            // 删除角色授权产品品类数据
            roleDao.deleteRoleProductCategoryByRoleId(roleId);

            // 删除角色授权工厂数据
            roleDao.deleteRoleFactoryByRoleId(roleId);

            // 删除角色授权仓库数据
            roleDao.deleteRoleDepositoryByRoleId(roleId);
        }

        // 新增角色授权菜单数据
        if (CollectionUtils.isNotEmpty(role.getPageIdList()))
        {
            // 封装角色授权菜单数据
            List<RolePage> rolePageList = new ArrayList<>(role.getPageIdList().size());
            for (String pageId : role.getPageIdList())
            {
                RolePage rolePage = new RolePage();
                rolePage.setRoleId(roleId);
                rolePage.setPageId(pageId);
                rolePageList.add(rolePage);
            }
            roleDao.addRolePage(rolePageList);
        }

        // 新增角色授权按钮数据
        if (CollectionUtils.isNotEmpty(role.getButtonIdList()))
        {
            // 封装角色授权按钮数据
            List<RoleButton> roleButtonList = new ArrayList<>(role.getButtonIdList().size());
            for (String buttonId : role.getButtonIdList())
            {
                RoleButton roleButton = new RoleButton();
                roleButton.setRoleId(roleId);
                roleButton.setButtonId(buttonId);
                roleButtonList.add(roleButton);
            }
            roleDao.addRoleButton(roleButtonList);
        }

        // 新增角色授权渠道数据
        if (CollectionUtils.isNotEmpty(role.getChannelIdList()))
        {
            // 封装角色授权渠道数据
            List<RoleChannel> roleChannelList = new ArrayList<>(role.getChannelIdList().size());
            for (String channelId : role.getChannelIdList())
            {
                RoleChannel roleChannel = new RoleChannel();
                roleChannel.setRoleId(roleId);
                roleChannel.setChannelId(channelId);
                roleChannelList.add(roleChannel);
            }
            roleDao.addRoleChannel(roleChannelList);
        }

        // 新增角色授权产品品类数据
        if (CollectionUtils.isNotEmpty(role.getCategoryIdList()))
        {
            // 封装角色授权产品品类数据
            List<RoleProductCategory> roleProductCategoryList = new ArrayList<>(role.getCategoryIdList().size());
            for (String categoryId : role.getCategoryIdList())
            {
                RoleProductCategory roleProductCategory = new RoleProductCategory();
                roleProductCategory.setRoleId(roleId);
                roleProductCategory.setCategoryId(categoryId);
                roleProductCategoryList.add(roleProductCategory);
            }
            roleDao.addRoleProductCategory(roleProductCategoryList);
        }

        // 新增角色授权工厂数据
        if (CollectionUtils.isNotEmpty(role.getFactoryIdList()))
        {
            // 封装角色授权工厂数据
            List<RoleFactory> roleFactoryList = new ArrayList<>(role.getFactoryIdList().size());
            for (String factoryId : role.getFactoryIdList())
            {
                RoleFactory roleFactory = new RoleFactory();
                roleFactory.setRoleId(roleId);
                roleFactory.setFactoryId(factoryId);
                roleFactoryList.add(roleFactory);
            }
            roleDao.addRoleFactory(roleFactoryList);
        }

        // 新增角色授权仓库数据
        if (CollectionUtils.isNotEmpty(role.getDepositoryIdList()))
        {
            // 封装角色授权仓库数据
            List<RoleDepository> roleDepositoryList = new ArrayList<>(role.getDepositoryIdList().size());
            for (String depositoryId : role.getDepositoryIdList())
            {
                RoleDepository roleDepository = new RoleDepository();
                roleDepository.setRoleId(roleId);
                roleDepository.setDepositoryId(depositoryId);
                roleDepositoryList.add(roleDepository);
            }
            roleDao.addRoleDepository(roleDepositoryList);
        }
    }

    /**
     *
     * @Description 删除角色，级联删除关联账号数据和授权数据，事务管理
     * @param id
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 17:17
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteRole(String id) throws Exception
    {
        // 如果数据库中不存在，直接返回
        Role role = roleDao.queryRoleDetail(id);
        if (Objects.isNull(role))
        {
            return;
        }

        // 如果数据是预置或外部同步，返回失败不能删除
        if (CommonConstants.DATA_TYPE_INITIAL.equals(role.getDataType()) || CommonConstants.DATA_TYPE_EXTERNAL.equals(role.getDataType()))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_INITED);
        }

        // 删除角色
        roleDao.deleteRole(id);

        // 删除角色关联账号
        roleDao.deleteAccountRoleByRoleId(id);

        // 删除角色授权菜单数据
        roleDao.deleteRolePageByRoleId(id);

        // 删除角色授权按钮数据
        roleDao.deleteRoleButtonByRoleId(id);

        // 删除角色授权渠道数据
        roleDao.deleteRoleChannelByRoleId(id);

        // 删除角色授权产品品类数据
        roleDao.deleteRoleProductCategoryByRoleId(id);

        // 删除角色授权工厂数据
        roleDao.deleteRoleFactoryByRoleId(id);

        // 删除角色授权仓库数据
        roleDao.deleteRoleDepositoryByRoleId(id);
    }

    /**
     *
     * @Description 修改角色状态
     * @param role
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    @Override
    public void updateRoleStatus(Role role) throws Exception
    {
        roleDao.updateRoleStatus(role);
    }

    /**
     *
     * @Description 分页查询已分配的账号列表
     * @param condition
     * @return PageInfo<Account>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 14:35
     */
    public PageInfo<Account> queryUnbindAccountPage(PageCondition<AccountRoleVo> condition) throws Exception
    {
        // 初始化分页拦截器
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        AccountRoleVo accountRoleVo = condition.getCondition();

        // 查询账号列表
        List<Account> accountList = roleDao.queryUnbindAccountList(accountRoleVo);
        PageInfo<Account> result = new PageInfo<>(accountList);

        return result;
    }

    /**
     *
     * @Description 分页查询已分配的账号列表
     * @param condition
     * @return PageInfo<Account>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 14:35
     */
    public PageInfo<Account> queryBindedAccountPage(PageCondition<AccountRoleVo> condition) throws Exception
    {
        // 初始化分页拦截器
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        AccountRoleVo accountRoleVo = condition.getCondition();

        // 查询账号列表
        List<Account> accountList = roleDao.queryBindedAccountList(accountRoleVo);
        PageInfo<Account> result = new PageInfo<>(accountList);

        return result;
    }

    /**
     *
     * @Description 新增角色关联账号
     * @param accountRoleVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    @Override
    public void addAccountRole(AccountRoleVo accountRoleVo) throws Exception
    {
        // 封装角色关联账号数据
        List<AccountRole> accountRoleList = new ArrayList<>(accountRoleVo.getAccountIdList().size());
        for (String accountId : accountRoleVo.getAccountIdList())
        {
            AccountRole accountRole = new AccountRole();
            accountRole.setAccountId(accountId);
            accountRole.setRoleId(accountRoleVo.getRoleId());
            accountRoleList.add(accountRole);
        }

        roleDao.addAccountRole(accountRoleList);
    }

    /**
     *
     * @Description 删除角色关联账号
     * @param accountRoleVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 16:54
     */
    @Override
    public void deleteAccountRole(AccountRoleVo accountRoleVo) throws Exception
    {
        roleDao.deleteAccountRole(accountRoleVo);
    }
}
