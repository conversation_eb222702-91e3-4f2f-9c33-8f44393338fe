package cn.aliyun.ryytn.modules.system.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.entity.Captcha;
import cn.aliyun.ryytn.common.utils.captcha.CaptchaUtils;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.CaptchaService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 验证码接口实现类
 * <AUTHOR>
 * @date 2023/10/9 17:55
 */
@Slf4j
@Service
public class CaptchaServiceImpl implements CaptchaService
{
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 登录验证码失效时间，单位：秒
     */
    private static final long LOGIN_CAPTCHA_EXPIRETIME = 60L;

    /**
     *
     * @Description 获取登录验证码
     * @return Captcha
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 18:03
     */
    public Captcha queryLoginCaptcha() throws Exception
    {
        // 获取验证码
        Captcha captcha = CaptchaUtils.newBuilder().build().getCaptcha();

        // 验证码写入缓存
        String key = StringUtils.format(CommonConstants.LOGIN_CAPTCHA_CACHE_KEY, ServiceContextUtils.currentSession().getSessionId());
        redisUtils.set(key, captcha.getContent(), LOGIN_CAPTCHA_EXPIRETIME);

        return captcha;
    }
}
