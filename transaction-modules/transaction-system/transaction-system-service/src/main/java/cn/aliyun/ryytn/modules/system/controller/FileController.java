package cn.aliyun.ryytn.modules.system.controller;

import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.file.FileUtils;
import cn.aliyun.ryytn.common.utils.string.CharsetKit;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 文件管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午2:11:38
 */
@Slf4j
@RestController
@RequestMapping("/api/system/file")
@Api(tags = "文件管理")
public class FileController
{
    /**
     * 文件管理接口
     */
    @Autowired
    private FileService fileService;

    /**
     *
     * @Description 上传文件
     * @param multipartFile
     * @return ResultInfo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 下午4:49:37
     */
    @PostMapping("/uploadFile")
    @ResponseBody
    @ApiOperation("上传文件")
    @RequiresPermissions({})
    public ResultInfo<String> uploadFile(@RequestPart("file") MultipartFile multipartFile) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(multipartFile);

        // 上传文件
        String fileId = fileService.uploadFile(multipartFile);
        return ResultInfo.success(fileId);
    }

    /**
     *
     * @Description 批量上传文件
     * @param multipartFileList
     * @return List<String>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 下午4:49:37
     */
    @PostMapping("/batchUploadFile")
    @ResponseBody
    @ApiOperation("批量上传文件")
    @RequiresPermissions({})
    public ResultInfo<List<String>> batchUploadFile(@RequestPart("files") MultipartFile[] multipartFiles) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(multipartFiles);

        // 上传文件
        List<String> fileIdList = fileService.batchUploadFile(Arrays.asList(multipartFiles));
        return ResultInfo.success(fileIdList);
    }

    /**
     *
     * @Description 文件预览
     * @param fileId
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 下午4:49:19
     */
    @GetMapping("/previewFile")
    @ResponseBody
    @ApiOperation("预览文件")
    @RequiresPermissions({})
    public void previewFile(HttpServletRequest request, HttpServletResponse response, @RequestParam("fileId") String fileId) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(fileId);

        // 下载文件字节数组
        byte[] bytes = fileService.download(fileId);

        // 封装预览响应
        String fileName = URLEncoder.encode(fileId, CharsetKit.UTF_8);
        response.addHeader(HttpHeaders.CONTENT_TYPE, FileUtils.getContentType(fileName));
        response.addHeader(HttpHeaders.CONTENT_DISPOSITION, FileUtils.CONTENTDISPOSITION_INLINE + fileName);
        response.setCharacterEncoding(CharsetKit.UTF_8);
        response.setHeader(HttpHeaders.ACCEPT_RANGES, "bytes");
        response.setContentLength(bytes.length);
        response.getOutputStream().write(bytes);
    }

    /**
     *
     * @Description 下载文件
     * @param fileId
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 下午4:48:59
     */
    @GetMapping("/downloadFile")
    @ResponseBody
    @ApiOperation("下载文件")
    @RequiresPermissions({})
    public void downloadFile(HttpServletRequest request, HttpServletResponse response, @RequestParam("fileId") String fileId) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(fileId);

        // 下载文件字节数组
        byte[] bytes = fileService.download(fileId);

        // 封装下载响应
        String fileName = URLEncoder.encode(fileId, CharsetKit.UTF_8);
        response.addHeader(HttpHeaders.CONTENT_TYPE, FileUtils.getContentType(fileName));
        response.addHeader(HttpHeaders.CONTENT_DISPOSITION, FileUtils.CONTENTDISPOSITION_ATTACHMENT + fileName);
        response.setCharacterEncoding(CharsetKit.UTF_8);
        response.setHeader(HttpHeaders.ACCEPT_RANGES, "bytes");
        response.setContentLength(bytes.length);
        response.getOutputStream().write(bytes);
    }

    /**
     *
     * @Description 删除文件
     * @param fileIdList
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 下午4:48:28
     */
    @PostMapping("/deleteFile")
    @ResponseBody
    @ApiOperation("删除文件")
    @RequiresPermissions({})
    public ResultInfo<?> deleteFile(@RequestBody List<String> fileIdList) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(fileIdList);

        // 删除文件
        fileService.deleteFile(fileIdList);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 删除失效文件
     * t_ryytn_file_ref表serviceType如果为空，则认为没有被业务使用
     * 默认删除30天以前的文件
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 下午4:48:28
     */
    @PostMapping("/deleteExpireFile")
    @ResponseBody
    @ApiOperation("删除失效文件")
    @RequiresPermissions({})
    public ResultInfo<?> deleteExpireFile() throws Exception
    {
        // 删除失效文件
        fileService.deleteExpireFile();
        return ResultInfo.success();
    }
}
