package cn.aliyun.ryytn.modules.system.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.ProductService;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import cn.aliyun.ryytn.modules.system.entity.vo.SkuConditionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 产品管理
 * <AUTHOR>
 * @date 2023/10/23 18:05
 */
@Slf4j
@RestController
@RequestMapping("/api/system/product")
@Api(tags = "产品管理")
public class ProductController
{

    @Autowired
    private ProductService productService;

    /**
     *
     * @Description 查询SKU列表
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 18:05
     */
    @PostMapping("querySkuList")
    @ResponseBody
    @ApiOperation("查询SKU列表")
    public ResultInfo<List<SkuDto>> querySkuList(@RequestBody SkuConditionVo skuConditionVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(skuConditionVo);
        List<SkuDto> result = productService.querySkuList(skuConditionVo);
        return ResultInfo.success(result);
    }

}
