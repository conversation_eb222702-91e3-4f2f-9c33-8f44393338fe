package cn.aliyun.ryytn.modules.system.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.SSOService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 单点登录
 * <AUTHOR>
 * @date 2023/10/20 10:14
 */
@Slf4j
@RestController
@RequestMapping("/api/system/sso")
@Api(tags = "单点登录")
public class SSOController
{
    @Autowired
    private SSOService ssoService;

    /**
     *
     * @Description OA系统登录服务
     * @param info
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月20日 10:57
     */
    @GetMapping("/oaLogin")
    @ResponseBody
    @ApiOperation("登录")
    public ResultInfo<?> oaLogin(HttpServletRequest request, HttpServletResponse response, @RequestParam("info") String info) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(info);

        // 登录
        ssoService.oaLogin(info);

        // 跳转URL
        String url = StringUtils.substringBefore(request.getRequestURL().toString(), "/api");
        response.sendRedirect(url);

        return ResultInfo.success();
    }
}
