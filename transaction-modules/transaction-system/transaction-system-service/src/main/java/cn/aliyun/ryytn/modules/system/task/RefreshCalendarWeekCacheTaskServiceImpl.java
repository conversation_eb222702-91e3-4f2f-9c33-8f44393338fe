package cn.aliyun.ryytn.modules.system.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 刷新日历数据缓存定时任务
 * <AUTHOR>
 * @date 2023/11/15 15:21
 */
@Slf4j
@Service
public class RefreshCalendarWeekCacheTaskServiceImpl implements TaskService
{
    @Autowired
    private CalendarService calendarService;

    /**
     *
     * @Description 刷新日历数据缓存
     * @param schedulerJob
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月15日 15:23
     */
    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        try{

            calendarService.refreshCalendarWeekCache();
        }catch (Exception e){
            log.error("error刷新日历周数据缓存失败.",e);
        }
    }
}
