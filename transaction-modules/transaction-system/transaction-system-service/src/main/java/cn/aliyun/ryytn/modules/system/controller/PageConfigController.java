package cn.aliyun.ryytn.modules.system.controller;

import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.PageConfig;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.exception.ParameterException;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.PageConfigService;
import cn.aliyun.ryytn.modules.system.entity.vo.PageConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 页面配置管理接口
 * <AUTHOR>
 * @date 2023年10月11日 16:40
 */
@Slf4j
@RestController
@RequestMapping("/api/system/pageConfig")
@Api(tags = "页面配置管理")
public class PageConfigController
{

    /**
     * 页面配置管理接口
     */
    @Autowired
    private PageConfigService pageConfigService;

    /**
     *
     * @Description 页面配置列表
     * @Param pageConfig
     * @return ResultInfo<List < PageConfig>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月11日 16:40
     */
    @PostMapping("queryPageConfigList")
    @ResponseBody
    @ApiOperation("查询页面配置列表")
    @RequiresPermissions({})
    public ResultInfo<Map<String, Object>> queryPageConfigList(@RequestBody PageConfig pageConfig) throws Exception
    {
        return ResultInfo.success(pageConfigService.queryPageConfigList(pageConfig));
    }

    /**
     *
     * @Description 批量插入页面配置
     * @Param pageConfigVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月11日 16:40
     */
    @PostMapping("batchAddPageConfig")
    @ResponseBody
    @ApiOperation("批量插入页面配置")
    @RequiresPermissions({})
    public ResultInfo<?> batchAddPageConfig(@RequestBody PageConfigVo pageConfigVo) throws Exception
    {
        //参数校验
        checkParam(pageConfigVo);

        //插入数据
        pageConfigService.batchAddPageConfig(pageConfigVo);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 参数校验
     * @param pageConfigVo
     * @throws ParameterException
     * <AUTHOR>
     * @date 2023年10月16日 9:30
     */
    private void checkParam(PageConfigVo pageConfigVo) throws ParameterException
    {
        // 参数不能为空
        ValidateUtil.checkIsNotEmpty(pageConfigVo);
        // 合计标识不能为空
        ValidateUtil.checkIsNotEmpty(pageConfigVo.getSumFlag());
        //页面配置列表不能为空
        ValidateUtil.checkIsNotEmpty(pageConfigVo.getPageConfigList());
    }

}
