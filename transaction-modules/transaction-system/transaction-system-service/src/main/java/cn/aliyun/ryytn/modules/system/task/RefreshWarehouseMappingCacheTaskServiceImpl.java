package cn.aliyun.ryytn.modules.system.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.modules.system.api.WarehouseService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 刷新仓库逻辑仓物理仓映射关系缓存定时任务
 * <AUTHOR>
 * @date 2023/11/15 15:21
 */
@Slf4j
@Service
public class RefreshWarehouseMappingCacheTaskServiceImpl implements TaskService
{
    @Autowired
    private WarehouseService warehouseService;

    /**
     *
     * @Description 刷新仓库逻辑仓物理仓映射关系数据缓存
     * @param schedulerJob
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月15日 15:23
     */
    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        try{
            warehouseService.refreshWarehouseMappingCache();
        }catch (Exception e){
            log.error("error刷新物理仓逻辑仓映射关系失败",e);
        }
    }
}
