package cn.aliyun.ryytn.modules.system.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSONArray;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.Factory;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.modules.system.api.FactoryService;
import cn.aliyun.ryytn.modules.system.entity.vo.FactoryConditionVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 工厂管理接口
 * <AUTHOR>
 * @date 2023年10月09日 17:30
 */
@Slf4j
@Service
public class FactoryServiceImpl implements FactoryService
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 查询所有工厂列表
     * @return List<Factory>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 17:35
     */
    @Override
    public List<Factory> queryAllFactory() throws Exception
    {
        FactoryConditionVo factoryConditionVo = new FactoryConditionVo();
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BASEBUS_FACTORY_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, factoryConditionVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        List<Factory> factoryList = jsonArray.toJavaList(Factory.class);

        return factoryList;
    }
}
