package cn.aliyun.ryytn.modules.system.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.ProductCategory;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.system.api.ProductCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 产品品类管理接口
 * <AUTHOR>
 * @date 2023年10月9日 16:24
 */
@Slf4j
@RestController
@RequestMapping("/api/system/productCategory")
@Api(tags = "产品品类管理")
public class ProductCategoryController
{


    /**
     * 产品品类管理接口
     */
    @Autowired
    private ProductCategoryService productCategoryService;

    /**
     *
     * @Description 查询树
     * @return ResultInfo<List < ProductCategory>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月9日 16:24
     */
    @PostMapping("queryProductCategoryTree")
    @ResponseBody
    @ApiOperation("查询产品品类树")
//    @RequiresPermissions({})
    public ResultInfo<List<ProductCategory>> queryProductCategoryTree() throws Exception
    {
        return ResultInfo.success(productCategoryService.queryProductCategoryTree());
    }

}
