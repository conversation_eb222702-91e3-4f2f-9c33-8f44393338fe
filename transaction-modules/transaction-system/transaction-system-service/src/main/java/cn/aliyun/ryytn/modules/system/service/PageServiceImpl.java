package cn.aliyun.ryytn.modules.system.service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.aliyun.ryytn.common.entity.Page;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.PageService;
import cn.aliyun.ryytn.modules.system.dao.PageDao;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 页面管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午3:11:58
 */
@Slf4j
@Service
public class PageServiceImpl implements PageService
{
    @Autowired
    private PageDao pageDao;

    /**
     *
     * @Description 查询页面树
     * @param page
     * @return List<Page>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 11:20
     */
    @Override
    public List<Page> queryPageList(Page page) throws Exception
    {
        // 查询所有菜单列表
        List<Page> pageList = pageDao.queryAllPageList(page);
        return pageList;
    }

    /**
     *
     * @Description 查询页面树
     * @param page
     * @return List<Page>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 11:20
     */
    @Override
    public List<Page> queryPageTree(Page page) throws Exception
    {
        // 查询所有菜单列表
        List<Page> pageList = pageDao.queryAllPageList(page);

        // 遍历菜单，构造树形结构
        Iterator<Page> itor = pageList.iterator();
        while (itor.hasNext())
        {
            Page tempPage = itor.next();
            if (findParent(tempPage, pageList))
            {
                itor.remove();
            }
        }

        return pageList;
    }

    /**
     *
     * @Description 递归找父节点
     * @param page
     * @param menus
     * @return boolean
     * <AUTHOR>
     * @date 2023年10月08日 13:53
     */
    private boolean findParent(Page page, List<Page> menus)
    {
        for (Page m : menus)
        {
            if (CollectionUtils.isNotEmpty(m.getSubPageList()))
            {
                if (findParent(page, m.getSubPageList()))
                {
                    return true;
                }
            }
            if (StringUtils.equals(m.getId(), page.getParentId()))
            {
                if (null == m.getSubPageList())
                {
                    m.setSubPageList(new ArrayList<>());
                }
                m.getSubPageList().add(page);
                return true;
            }
        }
        return false;
    }
}
