package cn.aliyun.ryytn.modules.system.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSONArray;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.entity.Channel;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.ChannelService;
import cn.aliyun.ryytn.modules.system.entity.dto.ChannelDto;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 渠道管理接口
 * <AUTHOR>
 * @date 2023年10月09日 14:43
 */
@Slf4j
@Service
public class ChannelServiceImpl implements ChannelService
{

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 查询渠道树
     * @return List<ChannelNode>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月09日 14:43
     */
    @Override
    public List<Channel> queryChannelTree() throws Exception
    {
        //处理数据，构造树结构（现在只有四层）
        List<ChannelDto> channelDtoList = queryChannelList();
        List<Channel> channels = this.creatChannleTree(channelDtoList);

        return channels;
    }


    /**
     *
     * @Description 查询渠道列表
     * @return List<ChannelDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月23日 11:32
     */
    @Override
    public List<ChannelDto> queryChannelList() throws Exception
    {
        List<ChannelDto> result = Collections.EMPTY_LIST;

        // 接口是部门管理渠道关联关系表，由于部门与渠道是1对多关系，需要去重查询渠道
        Map<String, Object> param = new HashMap<>();
        param.put("group_by", "lv1_channel_code,lv1_channel_name,lv2_channel_code,lv2_channel_name,lv3_channel_code,lv3_channel_name");

        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BASEBUS_CHANNEL_LEVEL_LIST"));
        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, param, null);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            result = jsonArray.toJavaList(ChannelDto.class).stream().distinct().collect(Collectors.toList());
        }
        return result;
    }

    /**
     *
     * @Description 递归找父节点
     * @param Channel
     * @param channels
     * @return boolean
     * <AUTHOR>
     * @date 2023年10月09日 14:43
     */
    private boolean findParent(Channel Channel, List<Channel> channels)
    {
        for (Channel m : channels)
        {
            if (CollectionUtils.isNotEmpty(m.getSubList()))
            {
                if (findParent(Channel, m.getSubList()))
                {
                    return true;
                }
            }
            if (StringUtils.equals(m.getId(), Channel.getParentId()))
            {
                if (null == m.getSubList())
                {
                    m.setSubList(new ArrayList<>());
                }
                m.getSubList().add(Channel);
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @Description 构造Dataq的渠道树
     * @param channelDtoList
     * @return List<Channel>
     * <AUTHOR>
     * @date 2023年10月25日 16:45
     */
    private List<Channel> creatChannleTree(List<ChannelDto> channelDtoList)
    {

        List<Channel> Data = new ArrayList<>();

        //排序数据  Lv1ChannelCode+Lv2ChannelCode+Lv3ChannelCode，过滤脏数据
        channelDtoList = channelDtoList.stream()
            .filter(item -> StringUtils.isNotEmpty(item.getLv1ChannelCode()))
            .filter(item -> StringUtils.isNotEmpty(item.getLv2ChannelCode()))
            .filter(item -> StringUtils.isNotEmpty(item.getLv3ChannelCode()))
            .sorted(Comparator.comparing(ChannelDto::getLv1ChannelCode)
                .thenComparing((ChannelDto::getLv2ChannelCode))
                .thenComparing(ChannelDto::getLv3ChannelCode))
            .collect(Collectors.toList());

        List<String> first = channelDtoList.stream().map(item -> item.getLv1ChannelCode()).distinct().collect(Collectors.toList());
        for (String firstKey : first)
        {
            Channel channel = new Channel();
            List<ChannelDto> firstList = channelDtoList.stream().filter(item -> item.getLv1ChannelCode().equals(firstKey)).collect(Collectors.toList());
            ChannelDto channelDto = firstList.get(0);
            String lv1ChannelName = channelDto.getLv1ChannelName();
            channel.setName(lv1ChannelName);
            channel.setId(firstKey);
            channel.setSubList(new ArrayList<>());
            channel.setLevel(1);
            List<String> second = firstList.stream().map(item -> item.getLv2ChannelCode()).distinct().collect(Collectors.toList());

            for (String secondKey : second)
            {
                Channel channel2 = new Channel();
                List<ChannelDto> secondList = channelDtoList.stream()
                    .filter(item -> item.getLv1ChannelCode().equals(firstKey))
                    .filter(item -> item.getLv2ChannelCode().equals(secondKey)).collect(Collectors.toList());

                if (secondList.size() < 1)
                {
                    continue;
                }
                ChannelDto channelDto2 = secondList.get(0);
                String lv1ChannelName2 = channelDto2.getLv2ChannelName();
                channel2.setName(lv1ChannelName2);
                channel2.setId(secondKey);
                channel2.setSubList(new ArrayList<>());
                channel2.setLevel(2);
                List<String> third = channelDtoList.stream().map(item -> item.getLv3ChannelCode()).distinct().collect(Collectors.toList());

                for (String threeKey : third)
                {
                    Channel channel3 = new Channel();
                    List<ChannelDto> thirdList = channelDtoList.stream()
                        .filter(item -> item.getLv1ChannelCode().equals(firstKey))
                        .filter(item -> item.getLv2ChannelCode().equals(secondKey))
                        .filter(item -> item.getLv3ChannelCode().equals(threeKey)).collect(Collectors.toList());
                    if (thirdList.size() < 1)
                    {
                        continue;
                    }
                    ChannelDto channelDto3 = thirdList.get(0);

                    channel3.setId(threeKey);
                    channel3.setName(channelDto3.getLv3ChannelName());
                    channel3.setSubList(new ArrayList<>());
                    channel3.setLevel(3);

                    channel2.getSubList().add(channel3);
                }
                channel.getSubList().add(channel2);
            }
            Data.add(channel);
        }
        return Data;
    }


}
