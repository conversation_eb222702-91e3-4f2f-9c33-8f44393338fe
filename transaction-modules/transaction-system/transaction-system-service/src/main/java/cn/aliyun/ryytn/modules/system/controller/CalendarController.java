package cn.aliyun.ryytn.modules.system.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.DataqWeek;
import cn.aliyun.ryytn.common.entity.DataqYear;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.CalendarService;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryFsclListReqVo;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWeekListReqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 日历管理
 * <AUTHOR>
 * @date 2023/10/23 14:58
 */
@Slf4j
@RestController
@RequestMapping("/api/system/calendar")
@Api(tags = "日历管理")
public class CalendarController
{

    @Autowired
    private CalendarService calendarService;

    /**
     * @Description 查询财年列表
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/10/23 14:58
     */
    @PostMapping("queryFsclList")
    @ResponseBody
    @ApiOperation("查询财年列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<DataqYear>> queryFsclList(@RequestBody QueryFsclListReqVo queryFsclListReqVo) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(queryFsclListReqVo);
        List<DataqYear> result = calendarService.queryFsclList(queryFsclListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 查询周列表
     * @param queryWeekListReqVo
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月24日 14:30
     */
    @PostMapping("queryWeekList")
    @ResponseBody
    @ApiOperation("查询周列表")
    @RequiresPermissions(value = {})
    public ResultInfo<List<DataqWeek>> queryWeekList(@RequestBody QueryWeekListReqVo queryWeekListReqVo) throws Exception
    {
        List<DataqWeek> result = calendarService.queryWeekList(queryWeekListReqVo);
        return ResultInfo.success(result);
    }

    /**
     *
     * @Description 刷新日历周缓存
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月15日 15:32
     */
    @PostMapping("refreshCalendarWeekCache")
    @ResponseBody
    @ApiOperation("刷新日历周缓存")
    @RequiresPermissions(value = {})
    public ResultInfo<?> refreshCalendarWeekCache() throws Exception
    {
        calendarService.refreshCalendarWeekCache();
        return ResultInfo.success();
    }
}
