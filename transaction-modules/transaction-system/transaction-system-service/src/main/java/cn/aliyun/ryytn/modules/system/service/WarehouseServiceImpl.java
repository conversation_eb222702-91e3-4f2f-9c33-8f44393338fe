package cn.aliyun.ryytn.modules.system.service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSONArray;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.WarehouseService;
import cn.aliyun.ryytn.modules.system.entity.vo.PhysicWarehouseVo;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWarehouseListReqVo;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWarehouseListRspVo;
import cn.aliyun.ryytn.modules.system.entity.vo.WarehouseVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 仓库管理接口
 * <AUTHOR>
 * @date 2023年10月10日 10:42
 */
@Slf4j
@Service
public class WarehouseServiceImpl implements WarehouseService, ApplicationListener<ContextRefreshedEvent>
{

    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @Description 查询仓库分组
     * @return List<DepositoryVo>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:42
     */
    @Override
    public List<WarehouseVo> queryWarehouseGroup() throws Exception
    {
        List<WarehouseVo> returnData = new ArrayList<>();
        //查询dataq仓库数据
        QueryWarehouseListReqVo queryWarehouseListReqVo = new QueryWarehouseListReqVo();
        List<PhysicWarehouseVo> physicWarehouseList = this.queryPhysicWarehouseList(queryWarehouseListReqVo);

        //根据仓库类型编码分组
        Map<String, List<PhysicWarehouseVo>> collect =
            physicWarehouseList.stream()
                .sorted(Comparator.comparing(PhysicWarehouseVo::getLv1TypeCode)
                    .thenComparing(PhysicWarehouseVo::getWarehouseCode))
                .collect(Collectors.groupingBy(PhysicWarehouseVo::getLv1TypeCode));

        //构造返回数据
        Set<Map.Entry<String, List<PhysicWarehouseVo>>> entries = collect.entrySet();

        for (Map.Entry<String, List<PhysicWarehouseVo>> entry : entries)
        {
            WarehouseVo warehouseVo = new WarehouseVo();
            String key = entry.getKey();
            List<PhysicWarehouseVo> value = entry.getValue();
            warehouseVo.setLv1TypeCode(key);
            warehouseVo.setLv1TypeName(value.get(0).getLv1TypeName());
            warehouseVo.setWarehouseList(value);
            returnData.add(warehouseVo);
        }

        return returnData;
    }

    /**
     * @Description 查询仓库列表数据
     * @param warehouseListReqVo
     * @return List<QueryWarehouseListRspVo>
     * @throws Exception
     */
    @Override
    public List<QueryWarehouseListRspVo> queryWarehouseList(QueryWarehouseListReqVo warehouseListReqVo) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BASEBUS_WAREHOUSE_LIST"));
        JSONArray warehouseJson = (JSONArray) dataqService.invoke(HttpMethod.POST, path, null, null, warehouseListReqVo).getData();
        List<QueryWarehouseListRspVo> queryWarehouseListRspVos = JSONArray.parseArray(warehouseJson.toJSONString(), QueryWarehouseListRspVo.class);
        //过滤停用的仓库
        queryWarehouseListRspVos = queryWarehouseListRspVos.stream()
            .filter(item -> Objects.nonNull(item.getStatus()))
            .filter(item -> item.getStatus().equals("1")).collect(Collectors.toList());

        return queryWarehouseListRspVos;
    }

    /**
     * @Description 查询物理仓库列表数据
     * @param warehouseListReqVo
     * @return List<PhysicWarehouseVo>
     * @throws Exception
     */
    @Override
    public List<PhysicWarehouseVo> queryPhysicWarehouseList(QueryWarehouseListReqVo warehouseListReqVo) throws Exception
    {

        List<PhysicWarehouseVo> physicWarehouseVos = new ArrayList<>();

        Map<String, List<QueryWarehouseListRspVo>> physicWarehouseMap =
            this.queryWarehouseList(warehouseListReqVo).stream()
                .filter(item -> StringUtils.isNotEmpty(item.getBizWarehouseCode()))
                .collect(Collectors.groupingBy(QueryWarehouseListRspVo::getBizWarehouseCode));
        Set<Map.Entry<String, List<QueryWarehouseListRspVo>>> entries = physicWarehouseMap.entrySet();
        for (Map.Entry<String, List<QueryWarehouseListRspVo>> entry : entries)
        {
            String bizWarehouseCode = entry.getKey();
            if (StringUtils.isEmpty(bizWarehouseCode))
            {
                continue;
            }
            List<QueryWarehouseListRspVo> value = entry.getValue();
            if (Objects.nonNull(value) && value.size() > 0)
            {
                PhysicWarehouseVo warehouseVo = new PhysicWarehouseVo();
                String bizWarehouseName = value.get(0).getBizWarehouseName();
                String warehouseTypeCode = value.get(0).getBizWarehouseCode();
                warehouseVo.setLv1TypeCode(value.get(0).getLv1TypeCode());
                warehouseVo.setLv1TypeName(value.get(0).getLv1TypeName());
                warehouseVo.setWarehouseCode(bizWarehouseCode);
                warehouseVo.setWarehouseName(bizWarehouseName);
                warehouseVo.setWarehouseTypeCode(warehouseTypeCode);
                physicWarehouseVos.add(warehouseVo);

            }
        }
        return physicWarehouseVos;
    }

    /**
     *
     * @Description 监听启动事件
     * @param event
     * <AUTHOR>
     * @date 2023年12月27日 16:05
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event)
    {
        try
        {
            refreshWarehouseMappingCache();
        }
        catch (Exception e)
        {
            log.error("onApplicationEvent refreshWarehouseMappingCache has exceptoin:", e);
        }
    }

    /**
     *
     * @Description 刷新物理仓/逻辑仓映射关系缓存
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 16:07
     */
    @Override
    public void refreshWarehouseMappingCache() throws Exception
    {
        QueryWarehouseListReqVo queryWarehouseListReqVo = new QueryWarehouseListReqVo();
        List<QueryWarehouseListRspVo> warehouseList = queryWarehouseList(queryWarehouseListReqVo);
        if (CollectionUtils.isEmpty(warehouseList))
        {
            return;
        }
        Map<String, Object> logicToPhysicMap = warehouseList.stream().collect(Collectors.toMap(QueryWarehouseListRspVo::getWarehouseCode,
            Function.identity(), (key1, key2) -> key2));

        redisUtils.hmset(CommonConstants.WAREHOUSE_LOGIC_TO_PHYSIC_KEY, logicToPhysicMap);

        Map<String, List<QueryWarehouseListRspVo>> physicToLogicMap =
            warehouseList.stream().collect(Collectors.groupingBy(QueryWarehouseListRspVo::getBizWarehouseCode));
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, List<QueryWarehouseListRspVo>> entry : physicToLogicMap.entrySet())
        {
            map.put(entry.getKey(), entry.getValue());
        }

        redisUtils.hmset(CommonConstants.WAREHOUSE_PHYSIC_TO_LOGIC_KEY, map);
    }
}
