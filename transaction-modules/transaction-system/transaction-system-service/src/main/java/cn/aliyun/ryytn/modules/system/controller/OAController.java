package cn.aliyun.ryytn.modules.system.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.OADepartment;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.system.api.OAService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description OA数据同步接口
 * <AUTHOR>
 * @date 2023年9月19日 下午2:11:38
 */
@Slf4j
@RestController
@RequestMapping("/api/system/oa")
@Api(tags = "OA数据管理")
public class OAController
{
    /**
     * OA数据同步接口
     */
    @Autowired
    private OAService oaService;

    /**
     *
     * @Description 同步OA系统分部（分公司）数据
     * @throws Exception
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年09月28日 9:55
     */
    @PostMapping("syncOASubCompany")
    @ResponseBody
    @ApiOperation("同步OA系统分部数据")
    @RequiresPermissions({})
    public ResultInfo<?> syncOASubCompany() throws Exception
    {
        oaService.syncOASubCompany();
        return ResultInfo.success();
    }

    /**
     *
     * @Description 同步OA系统部门数据
     * @throws Exception
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年09月28日 9:55
     */
    @PostMapping("syncOADepartment")
    @ResponseBody
    @ApiOperation("同步OA系统部门数据")
    @RequiresPermissions({})
    public ResultInfo<?> syncOADepartment() throws Exception
    {
        oaService.syncOADepartment();
        return ResultInfo.success();
    }

    /**
     *
     * @Description 同步OA系统岗位数据
     * @throws Exception
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年09月28日 9:55
     */
    @PostMapping("syncOAJobTitle")
    @ResponseBody
    @ApiOperation("同步OA系统岗位数据")
    @RequiresPermissions({})
    public ResultInfo<?> syncOAJobTitle() throws Exception
    {
        oaService.syncOAJobTitle();
        return ResultInfo.success();
    }

    /**
     *
     * @Description 同步OA系统人员数据
     * @throws Exception
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年09月28日 9:55
     */
    @PostMapping("syncOAPerson")
    @ResponseBody
    @ApiOperation("同步OA系统人员数据")
    @RequiresPermissions({})
    public ResultInfo<?> syncOAPerson() throws Exception
    {
        oaService.syncOAPerson();
        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询部门树
     * @return ResultInfo<List < OADepartment>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月07日 17:19
     */
    @PostMapping("queryDepartmentTree")
    @ResponseBody
    @ApiOperation("查询部门树")
    @RequiresPermissions({})
    public ResultInfo<List<OADepartment>> queryDepartmentTree() throws Exception
    {
        List<OADepartment> oaDepartmentList = oaService.queryDepartmentTree();
        return ResultInfo.success(oaDepartmentList);
    }
}
