package cn.aliyun.ryytn.modules.system.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.entity.Session;
import cn.aliyun.ryytn.common.utils.mybatis.MybatisUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.SessionService;
import cn.aliyun.ryytn.modules.system.dao.SessionDao;
import cn.aliyun.ryytn.modules.system.entity.vo.LoginVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 会话管理接口
 * <AUTHOR>
 * @date 2023/10/10 10:31
 */
@Slf4j
@RestController
@RequestMapping("/api/system/session")
@Api(tags = "会话管理")
public class SessionController
{
    @Autowired
    private SessionService sessionService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SessionDao sessionDao;

    @Autowired
    private MybatisUtils mybatisUtils;

    /**
     *
     * @Description 登录
     * @param loginVo
     * @return ResultInfo<Session>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:49
     */
    @PostMapping("/login")
    @ResponseBody
    @ApiOperation("登录")
    public ResultInfo<Session> login(@RequestBody LoginVo loginVo) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(loginVo);
        ValidateUtil.checkIsNotEmpty(loginVo.getLoginId());
        ValidateUtil.checkIsNotEmpty(loginVo.getPassword());
        ValidateUtil.checkIsNotEmpty(loginVo.getCaptcha());

        // 登录
        Session session = sessionService.login(loginVo);

        return ResultInfo.success(session);
    }

    /**
     *
     * @Description 登出
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:50
     */
    @PostMapping("/logout")
    @ResponseBody
    @ApiOperation("登出")
    public ResultInfo<?> logout() throws Exception
    {
        // 登出
        sessionService.logout();
        return ResultInfo.success();
    }

    /**
     *
     * @Description 获取当前会话
     * @return ResultInfo<Session>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:50
     */
    @PostMapping("/getCurrentSession")
    @ResponseBody
    @ApiOperation("获取当前会话")
    @RequiresPermissions(value = {})
    public ResultInfo<Session> getCurrentSession() throws Exception
    {
        // 获取当前账号
        Session session = sessionService.getCurrentSession();
        return ResultInfo.success(session);
    }
}
