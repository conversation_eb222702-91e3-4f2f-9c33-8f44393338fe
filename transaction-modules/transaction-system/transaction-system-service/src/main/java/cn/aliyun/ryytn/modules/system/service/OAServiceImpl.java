package cn.aliyun.ryytn.modules.system.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.Account;
import cn.aliyun.ryytn.common.entity.OADepartment;
import cn.aliyun.ryytn.common.entity.OAJobTitle;
import cn.aliyun.ryytn.common.entity.OAPerson;
import cn.aliyun.ryytn.common.entity.OASubCompany;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.http.HttpUtils;
import cn.aliyun.ryytn.common.utils.http.LowResponse;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.OAService;
import cn.aliyun.ryytn.modules.system.dao.AccountDao;
import cn.aliyun.ryytn.modules.system.dao.OADao;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description OA系统数据同步任务处理类
 * <AUTHOR>
 * @date 2023/9/26 16:09
 */
@Slf4j
@Service
public class OAServiceImpl implements OAService
{
    /**
     * OA同步数据Dao
     */
    @Autowired
    private OADao oaDao;

    /**
     * 本地账号Dao
     */
    @Autowired
    private AccountDao accountDao;

    /**
     * 缓存工具
     */
    @Autowired
    private RedisUtils redisUtils;

    /**
     * OA系统同步分部（分公司）任务分布式锁的KEY
     */
    private static final String LOCK_KEY_SYNCOASUBCOMPANY = "SYNC_OA_SUBCOMPANY";

    /**
     * OA系统同步部门任务分布式锁的KEY
     */
    private static final String LOCK_KEY_SYNCOADEPARTMENT = "SYNC_OA_DEPARTMENT";

    /**
     * OA系统同步岗位任务分布式锁的KEY
     */
    private static final String LOCK_KEY_SYNCOAJOBTITLE = "SYNC_OA_JOBTITLE";

    /**
     * OA系统同步人员任务分布式锁的KEY
     */
    private static final String LOCK_KEY_SYNCOAPERSON = "SYNC_OA_PERSON";

    /**
     * OA系统数据同步锁失效时间默认值：锁定时间1800秒
     */
    private static final String OA_SYNC_LOCK_EXPIRE_TIME_DEFAULT = "1800";

    /**
     * OA系统数据同步页长默认值：一次获取1000条
     */
    private static final String OA_SYNC_PAGESIZE_DEFAULT = "1000";

    /**
     * OA系统数据清理时间默认值：30天
     */
    private static final String OA_SYNC_CLEAR_TIME_DEFAULT = "30";

    /**
     * OA系统分部（分公司）树和部门树根节点编号为0或者空
     */
    private static final String OA_TREE_ROOT_ID = "0";

    /**
     *
     * @Description 同步OA系统分部（分公司）数据
     * <AUTHOR>
     * @date 2023年09月27日 15:38
     */
    @Override
    public void syncOASubCompany() throws Exception
    {
        // 获取系统参数配置分布式锁失效时长
        long lockExpireTime = Long.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY,
            "OA_SYNC_LOCK_EXPIRE_TIME"), OA_SYNC_LOCK_EXPIRE_TIME_DEFAULT));

        // 申请分布式锁
        boolean isLock = redisUtils.lock(LOCK_KEY_SYNCOASUBCOMPANY, lockExpireTime);
        if (!isLock)
        {
            log.warn("syncOASubCompany has processing.");
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }

        try
        {
            // 获取系统参数配置OA系统查询分部信息接口地址
            String url = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_SUBCOMPANY_URL");

            // 封装请求参数
            int curPage = 1;
            int pageSize = Integer.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_SYNC_PAGESIZE"),
                OA_SYNC_PAGESIZE_DEFAULT));
            Map<String, Map<String, Integer>> reqMap = new HashMap<String, Map<String, Integer>>();
            Map<String, Integer> params = new HashMap<String, Integer>();
            params.put("curpage", curPage);
            params.put("pagesize", pageSize);
            reqMap.put("params", params);

            // http请求OA接口，与OA系统开发确认，通过在OA侧配置客户端白名单IP方式，暂时不对接认证接口获取令牌，否则此处调用HttpUtils方法需要传入header参数
            log.info("OA syncOASubCompany request:{}", reqMap);
            LowResponse reponse = HttpUtils.postJson(url, reqMap, null);
            log.info("OA syncOASubCompany response:{}", StringUtils.substring(reponse.toString(), 0, 1024));

            // 本次同步时间
            long syncTime = System.currentTimeMillis();

            // 有大的try，此处不需要对reponse判空或者code校验
            List<OASubCompany> firstPageList = reponse.getBodyJsonObj().getJSONObject("data").getJSONArray("dataList").toJavaList(OASubCompany.class);
            int total = reponse.getBodyJsonObj().getJSONObject("data").getIntValue("totalSize");
            // 分公司总数量不会非常大，考虑分公司还需要递归计算所有上级公司编号和名称，此处合并到一个集合中处理
            List<OASubCompany> allCompanyList = new ArrayList<OASubCompany>(total);
            if (CollectionUtils.isNotEmpty(firstPageList))
            {
                // 封装本次同步时间
                firstPageList.forEach(item -> {
                    item.setSyncTime(syncTime);
                });
                allCompanyList.addAll(firstPageList);
                firstPageList.clear();
            }

            for (++curPage; curPage <= Math.floorDiv(total, pageSize) + 1; curPage++)
            {
                // 封装请求参数
                reqMap.get("params").put("curpage", curPage);

                // http请求OA接口
                log.info("OA syncOASubCompany request:{}", reqMap);
                reponse = HttpUtils.postJson(url, reqMap, null);
                log.info("OA syncOASubCompany response:{}", StringUtils.substring(reponse.toString(), 0, 1024));

                // 由于是分页接口，此时查询的totalSize可能会发生变化
                // 当前仅用Set做去重，防止删除操作导致已查询到的数据后移，数据重复
                // 暂不考虑新增数据，T+1同步新增数据
                List<OASubCompany> oaSubCompanyList =
                    reponse.getBodyJsonObj().getJSONObject("data").getJSONArray("dataList").toJavaList(OASubCompany.class);

                if (CollectionUtils.isNotEmpty(oaSubCompanyList))
                {
                    // 封装本次同步时间
                    oaSubCompanyList.forEach(item -> {
                        item.setSyncTime(syncTime);
                    });
                    allCompanyList.addAll(oaSubCompanyList);
                    oaSubCompanyList.clear();
                }
            }

            // 递归封装所有父公司编号和层级字段
            for (OASubCompany oaSubCompany : allCompanyList)
            {
                String parentIds = getParentIds(oaSubCompany, allCompanyList);
                oaSubCompany.setSupSubComIds(parentIds);
                int level = StringUtils.countMatches(parentIds, StringUtils.COMMA_SEPARATOR) + 1;
                oaSubCompany.setLevel(level);
            }

            // 分公司总数量不会非常大，不考虑大集合操作撑爆JVM或数据库，批量提交入库
            oaDao.addOASubCompany(allCompanyList);

            // 失效同步时间，如果缓存没取到，默认30天
            int clearTime =
                Integer.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_SYNC_CLEAR_TIME"),
                    OA_SYNC_CLEAR_TIME_DEFAULT));
            long expireSyncTime = DateUtils.addDays(new Date(syncTime), -clearTime).getTime();

            // 清理连续N天未同步的数据，认为在OA系统已被删除
            oaDao.deleteOASubCompanyExpire(expireSyncTime);
        }
        catch (Exception e)
        {
            log.error("syncOASubCompany has exception:", e);
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            // 释放分布式锁
            redisUtils.unlock(LOCK_KEY_SYNCOASUBCOMPANY);
        }
    }

    /**
     *
     * @Description 同步OA系统部门数据
     * <AUTHOR>
     * @date 2023年09月27日 15:38
     */
    @Override
    public void syncOADepartment() throws Exception
    {
        // 获取系统参数配置分布式锁失效时长
        long lockExpireTime = Long.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY,
            "OA_SYNC_LOCK_EXPIRE_TIME"), OA_SYNC_LOCK_EXPIRE_TIME_DEFAULT));

        // 申请分布式锁
        boolean isLock = redisUtils.lock(LOCK_KEY_SYNCOADEPARTMENT, lockExpireTime);
        if (!isLock)
        {
            log.warn("syncOADepartment has processing in another node.");
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }

        try
        {
            // 获取系统参数配置OA系统查询部门信息接口地址
            String url = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_DEPARTMENT_URL");

            // 封装请求参数
            int curPage = 1;
            int pageSize = Integer.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_SYNC_PAGESIZE"),
                OA_SYNC_PAGESIZE_DEFAULT));
            Map<String, Map<String, Integer>> reqMap = new HashMap<String, Map<String, Integer>>();
            Map<String, Integer> params = new HashMap<String, Integer>();
            params.put("curpage", curPage);
            params.put("pagesize", pageSize);
            reqMap.put("params", params);

            // http请求OA接口，与OA系统开发确认，通过在OA侧配置客户端白名单IP方式，暂时不对接认证接口获取令牌，否则此处调用HttpUtils方法需要传入header参数
            log.info("OA syncOADepartment request:{}", reqMap);
            LowResponse reponse = HttpUtils.postJson(url, reqMap, null);
            log.info("OA syncOADepartment response:{}", StringUtils.substring(reponse.toString(), 0, 1024));

            // 本次同步时间
            long syncTime = System.currentTimeMillis();

            // 有大的try，此处不需要对reponse判空或者code校验
            List<OADepartment> firstPageList = reponse.getBodyJsonObj().getJSONObject("data").getJSONArray("dataList").toJavaList(OADepartment.class);
            int total = reponse.getBodyJsonObj().getJSONObject("data").getIntValue("totalSize");
            // 部门总数量不会非常大，考虑部门还需要递归计算所有上级部门编号和名称，此处合并到一个集合中处理
            List<OADepartment> allOADepartmentList = new ArrayList<OADepartment>(total);

            if (CollectionUtils.isNotEmpty(firstPageList))
            {
                // 封装本次同步时间
                firstPageList.forEach(item -> {
                    item.setSyncTime(syncTime);
                });
                allOADepartmentList.addAll(firstPageList);
                firstPageList.clear();
            }

            for (++curPage; curPage <= Math.floorDiv(total, pageSize) + 1; curPage++)
            {
                // 封装请求参数
                reqMap.get("params").put("curpage", curPage);

                // http请求OA接口
                log.info("OA syncOADepartment request:{}", reqMap);
                reponse = HttpUtils.postJson(url, reqMap, null);
                log.info("OA syncOADepartment response:{}", StringUtils.substring(reponse.toString(), 0, 1024));

                // 由于是分页接口，此时查询的totalSize可能会发生变化
                // 当前仅用Set做去重，防止删除操作导致已查询到的数据后移，数据重复
                // 暂不考虑新增数据，T+1同步新增数据
                List<OADepartment> oaDepartmentList =
                    reponse.getBodyJsonObj().getJSONObject("data").getJSONArray("dataList").toJavaList(OADepartment.class);

                if (CollectionUtils.isNotEmpty(oaDepartmentList))
                {
                    // 封装本次同步时间
                    oaDepartmentList.forEach(item -> {
                        item.setSyncTime(syncTime);
                    });
                    allOADepartmentList.addAll(oaDepartmentList);
                    oaDepartmentList.clear();
                }
            }

            // 递归封装所有父部门编号
            for (OADepartment oaDepartment : allOADepartmentList)
            {
                String parentIds = getParentIds(oaDepartment, allOADepartmentList);
                oaDepartment.setSupDepIds(parentIds);
                int level = StringUtils.countMatches(parentIds, StringUtils.COMMA_SEPARATOR) + 1;
                oaDepartment.setLevel(level);
            }

            // 部门总数量不会非常大，不考虑大集合操作撑爆JVM或数据库，批量提交入库
            oaDao.addOADepartment(allOADepartmentList);

            // 失效同步时间，如果缓存没取到，默认30天
            int clearTime =
                Integer.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_SYNC_CLEAR_TIME"),
                    OA_SYNC_CLEAR_TIME_DEFAULT));
            long expireSyncTime = DateUtils.addDays(new Date(syncTime), -clearTime).getTime();

            // 清理连续N天未同步的数据，认为在OA系统已被删除
            oaDao.deleteOADepartmentExpire(expireSyncTime);
        }
        catch (Exception e)
        {
            log.error("syncOADepartment has exception:", e);
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            // 释放分布式锁
            redisUtils.unlock(LOCK_KEY_SYNCOADEPARTMENT);
        }
    }

    /**
     *
     * @Description 同步OA系统岗位数据
     * <AUTHOR>
     * @date 2023年09月27日 15:38
     */
    @Override
    public void syncOAJobTitle() throws Exception
    {
        // 获取系统参数配置分布式锁失效时长
        long lockExpireTime = Long.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY,
            "OA_SYNC_LOCK_EXPIRE_TIME"), OA_SYNC_LOCK_EXPIRE_TIME_DEFAULT));

        // 申请分布式锁
        boolean isLock = redisUtils.lock(LOCK_KEY_SYNCOAJOBTITLE, lockExpireTime);
        if (!isLock)
        {
            log.warn("syncOAJobTitle has processing in another node.");
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }

        try
        {
            // 获取系统参数配置OA系统查询岗位信息接口地址
            String url = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_JOBTITLE_URL");

            // 封装请求参数
            int curPage = 1;
            int pageSize = Integer.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_SYNC_PAGESIZE"),
                OA_SYNC_PAGESIZE_DEFAULT));
            Map<String, Map<String, Integer>> reqMap = new HashMap<String, Map<String, Integer>>();
            Map<String, Integer> params = new HashMap<String, Integer>();
            params.put("curpage", curPage);
            params.put("pagesize", pageSize);
            reqMap.put("params", params);

            // http请求OA接口，与OA系统开发确认，通过在OA侧配置客户端白名单IP方式，暂时不对接认证接口获取令牌，否则此处调用HttpUtils方法需要传入header参数
            log.info("OA syncOAJobTitle request:{}", reqMap);
            LowResponse reponse = HttpUtils.postJson(url, reqMap, null);
            log.info("OA syncOAJobTitle response:{}", StringUtils.substring(reponse.toString(), 0, 1024));

            // 本次同步时间
            long syncTime = System.currentTimeMillis();

            // 有大的try，此处不需要对reponse判空或者code校验
            List<OAJobTitle> firstPageList = reponse.getBodyJsonObj().getJSONObject("data").getJSONArray("dataList").toJavaList(OAJobTitle.class);

            if (CollectionUtils.isNotEmpty(firstPageList))
            {
                // 封装本次同步时间
                firstPageList.forEach(item -> {
                    item.setSyncTime(syncTime);
                });

                // 防止大集合操作撑爆JVM或数据库，批量提交入库
                oaDao.addOAJobTitle(firstPageList);

                return;
            }

            long total = reponse.getBodyJsonObj().getJSONObject("data").getLong("totalSize");
            for (++curPage; curPage <= Math.floorDiv(total, pageSize) + 1; curPage++)
            {
                // 封装请求参数
                reqMap.get("params").put("curpage", curPage);

                // http请求OA接口
                log.info("OA syncOAJobTitle request:{}", reqMap);
                reponse = HttpUtils.postJson(url, reqMap, null);
                log.info("OA syncOAJobTitle response:{}", StringUtils.substring(reponse.toString(), 0, 1024));

                // 由于是分页接口，此时查询的totalSize可能会发生变化
                // 当前仅用Set做去重，防止删除操作导致已查询到的数据后移，数据重复
                // 暂不考虑新增数据，T+1同步新增数据
                List<OAJobTitle> oaJobTitleList =
                    reponse.getBodyJsonObj().getJSONObject("data").getJSONArray("dataList").toJavaList(OAJobTitle.class);

                if (CollectionUtils.isNotEmpty(oaJobTitleList))
                {
                    // 封装本次同步时间
                    oaJobTitleList.forEach(item -> {
                        item.setSyncTime(syncTime);
                    });

                    // 防止大集合操作撑爆JVM或数据库，批量提交入库
                    oaDao.addOAJobTitle(oaJobTitleList);
                }
            }

            // 失效同步时间，如果缓存没取到，默认30天
            int clearTime =
                Integer.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_SYNC_CLEAR_TIME"),
                    OA_SYNC_CLEAR_TIME_DEFAULT));
            long expireSyncTime = DateUtils.addDays(new Date(syncTime), -clearTime).getTime();

            // 清理连续N天未同步的数据，认为在OA系统已被删除
            oaDao.deleteOAJobTitleExpire(expireSyncTime);
        }
        catch (Exception e)
        {
            log.error("syncOAJobTitle has exception:", e);
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            // 释放分布式锁
            redisUtils.unlock(LOCK_KEY_SYNCOAJOBTITLE);
        }
    }


    /**
     *
     * @Description 同步OA系统人员数据
     * <AUTHOR>
     * @date 2023年09月27日 15:38
     */
    @Override
    public void syncOAPerson() throws Exception
    {
        // 获取系统参数配置分布式锁失效时长
        long lockExpireTime = Long.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY,
            "OA_SYNC_LOCK_EXPIRE_TIME"), OA_SYNC_LOCK_EXPIRE_TIME_DEFAULT));

        // 申请分布式锁
        boolean isLock = redisUtils.lock(LOCK_KEY_SYNCOAPERSON, lockExpireTime);
        if (!isLock)
        {
            log.warn("syncOAPerson has processing in another node.");
            throw new ServiceException(ErrorCodeConstants.FAIL_TASK_PROCESSING_ERROR);
        }

        try
        {
            // 获取系统参数配置OA系统查询岗位信息接口地址
            String url = (String) redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_PERSON_URL");

            // 封装请求参数
            int curPage = 1;
            int pageSize = Integer.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_SYNC_PAGESIZE"),
                OA_SYNC_PAGESIZE_DEFAULT));
            Map<String, Map<String, Integer>> reqMap = new HashMap<String, Map<String, Integer>>();
            Map<String, Integer> params = new HashMap<String, Integer>();
            params.put("curpage", curPage);
            params.put("pagesize", pageSize);
            reqMap.put("params", params);

            // http请求OA接口，与OA系统开发确认，通过在OA侧配置客户端白名单IP方式，暂时不对接认证接口获取令牌，否则此处调用HttpUtils方法需要传入header参数
            log.info("OA syncOAPerson request:{}", reqMap);
            LowResponse reponse = HttpUtils.postJson(url, reqMap, null);
            log.info("OA syncOAPerson response:{}", StringUtils.substring(reponse.toString(), 0, 1024));

            // 本次同步时间
            long syncTime = System.currentTimeMillis();

            // 有大的try，此处不需要对reponse判空或者code校验
            List<OAPerson> firstPageList = reponse.getBodyJsonObj().getJSONObject("data").getJSONArray("dataList").toJavaList(OAPerson.class);

            if (CollectionUtils.isNotEmpty(firstPageList))
            {
                // 封装本次同步时间
                firstPageList.forEach(item -> {
                    item.setSyncTime(syncTime);
                });

                // 防止大集合操作撑爆JVM或数据库，批量提交入库，事务控制
                addPersonAccount(firstPageList);
            }

            long total = reponse.getBodyJsonObj().getJSONObject("data").getLong("totalSize");
            for (++curPage; curPage <= Math.floorDiv(total, pageSize) + 1; curPage++)
            {
                // 封装请求参数
                reqMap.get("params").put("curpage", curPage);

                // http请求OA接口
                log.info("OA syncOAPerson request:{}", reqMap);
                reponse = HttpUtils.postJson(url, reqMap, null);
                log.info("OA syncOAPerson response:{}", StringUtils.substring(reponse.toString(), 0, 1024));

                // 由于是分页接口，此时查询的totalSize可能会发生变化
                // 当前仅用Set做去重，防止删除操作导致已查询到的数据后移，数据重复
                // 暂不考虑新增数据，T+1同步新增数据
                List<OAPerson> oaPersonList =
                    reponse.getBodyJsonObj().getJSONObject("data").getJSONArray("dataList").toJavaList(OAPerson.class);

                if (CollectionUtils.isNotEmpty(oaPersonList))
                {
                    // 封装本次同步时间
                    oaPersonList.forEach(item -> {
                        item.setSyncTime(syncTime);
                    });

                    // 防止大集合操作撑爆JVM或数据库，批量提交入库，事务控制
                    addPersonAccount(oaPersonList);
                }
            }

            // 清理N天未同步的账号
            clearExpireOAPerson(syncTime);
        }
        catch (Exception e)
        {
            log.error("syncOAPerson has exception:", e);
            throw new ServiceException(ErrorCodeConstants.FAIL_OTHER);
        }
        finally
        {
            // 释放分布式锁
            redisUtils.unlock(LOCK_KEY_SYNCOAPERSON);
        }
    }

    /**
     *
     * @Description 查询部门树
     * @return List<OADepartment>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月07日 17:16
     */
    @Override
    public List<OADepartment> queryDepartmentTree() throws Exception
    {
        // 查询所有部门数据
        List<OADepartment> oaDepartmentList = oaDao.queryAllOADepartmentList();

        if (CollectionUtils.isNotEmpty(oaDepartmentList))
        {
            // 遍历菜单，构造树形结构
            Iterator<OADepartment> itor = oaDepartmentList.iterator();
            while (itor.hasNext())
            {
                OADepartment oaDepartment = itor.next();
                if (findParent(oaDepartment, oaDepartmentList))
                {
                    itor.remove();
                }
            }
        }

        return oaDepartmentList;
    }

    /**
     *
     * @Description 级联新增OA人员数据和本地账号数据，仅在本方法内使用事务控制
     * @param oaPersonList
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 15:00
     */
    @Transactional(rollbackFor = Exception.class)
    public void addPersonAccount(List<OAPerson> oaPersonList) throws Exception
    {
        if (CollectionUtils.isEmpty(oaPersonList))
        {
            return;
        }

        // OA人员数据入库
        oaDao.addOAPerson(oaPersonList);

        // 新增的OA人员本地账号数据
        List<Account> addAccountList = oaDao.queryAddAccountList(oaPersonList);
        if (CollectionUtils.isNotEmpty(addAccountList))
        {
            // 封装唯一编号和默认状态
            addAccountList.forEach(item -> {
                item.setId(SeqUtils.getSequenceUid());
                item.setDataType(CommonConstants.DATA_TYPE_EXTERNAL);
                item.setStatus(CommonConstants.DATA_STATUS_ENABLE);
            });

            // 新增OA人员本地账号数据入库
            accountDao.batchAddAccount(addAccountList);
        }

        // 修改的的OA人员本地账号数据
        List<Account> updateAccountList = oaDao.queryUpdateAccountList(oaPersonList);
        if (CollectionUtils.isNotEmpty(updateAccountList))
        {
            // 修改OA人员本地账号数据入库
            accountDao.batchUpdateAccount(updateAccountList);
        }
    }

    /**
     *
     * @Description 清理连续N天未同步的数据，认为在OA系统已被删除
     * @throws Exception
     * <AUTHOR>
     * @date 2023年09月28日 15:45
     */
    @Transactional(rollbackFor = Exception.class)
    public void clearExpireOAPerson(long syncTime) throws Exception
    {
        // 失效同步时间，如果缓存没取到，默认30天
        int clearTime =
            Integer.valueOf(StringUtils.getDefaultValueIfNull(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "OA_SYNC_CLEAR_TIME"),
                OA_SYNC_CLEAR_TIME_DEFAULT));
        long expireSyncTime = DateUtils.addDays(new Date(syncTime), -clearTime).getTime();

        // 清理连续N天未同步的OA人员账号数据
        oaDao.deleteAccountExpire(expireSyncTime);

        // 清理连续N天未同步的OA人员数据
        oaDao.deleteOAPersonExpire(expireSyncTime);
    }

    /**
     *
     * @Description 递归生成部门树
     * @param oaDepartment
     * @param oaDepartmentList
     * @return boolean
     * <AUTHOR>
     * @date 2023年10月07日 17:06
     */
    private boolean findParent(OADepartment oaDepartment, List<OADepartment> oaDepartmentList)
    {
        for (OADepartment department : oaDepartmentList)
        {
            if (CollectionUtils.isNotEmpty(department.getSubDepartmentList()))
            {
                if (findParent(oaDepartment, department.getSubDepartmentList()))
                {
                    return true;
                }
            }
            if (StringUtils.equals(department.getId(), oaDepartment.getSupDepId()))
            {
                if (null == department.getSubDepartmentList())
                {
                    department.setSubDepartmentList(new ArrayList<>());
                }
                department.getSubDepartmentList().add(oaDepartment);
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @Description 封装所有父公司编号，英文逗号分隔
     * @param oaSubCompany
     * @param oaSubCompanyList
     * @return String
     * <AUTHOR>
     * @date 2023年10月07日 17:04
     */
    private String getParentIds(OASubCompany oaSubCompany, List<OASubCompany> oaSubCompanyList)
    {
        String parentId = oaSubCompany.getSupSubComId();
        if (OA_TREE_ROOT_ID.equals(parentId) || StringUtils.isEmpty(parentId))
        {
            return parentId;
        }

        OASubCompany parentOaSubCompany =
            oaSubCompanyList.stream().filter(item -> StringUtils.equals(parentId, item.getId())).collect(Collectors.toList()).get(0);

        return new StringBuilder(getParentIds(parentOaSubCompany, oaSubCompanyList)).append(StringUtils.COMMA_SEPARATOR).append(parentId).toString();
    }

    /**
     *
     * @Description 封装所有父部门编号，英文逗号分隔
     * @param oaDepartment
     * @param oaDepartmentList
     * @return String
     * <AUTHOR>
     * @date 2023年10月07日 17:04
     */
    private String getParentIds(OADepartment oaDepartment, List<OADepartment> oaDepartmentList)
    {
        String parentId = oaDepartment.getSupDepId();
        if (OA_TREE_ROOT_ID.equals(parentId) || StringUtils.isEmpty(parentId))
        {
            return parentId;
        }

        OADepartment parentOaDepartment =
            oaDepartmentList.stream().filter(item -> StringUtils.equals(parentId, item.getId())).collect(Collectors.toList()).get(0);

        return new StringBuilder(getParentIds(parentOaDepartment, oaDepartmentList)).append(StringUtils.COMMA_SEPARATOR).append(parentId).toString();
    }
}
