package cn.aliyun.ryytn.modules.system;//package cn.aliyun.ryytn.modules.system;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import cn.aliyun.ryytn.common.entity.ResultInfo;
//import cn.aliyun.ryytn.common.mq.MqFactory;
//import cn.aliyun.ryytn.common.utils.date.DateUtils;
//import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
//
///**
// * @Description 生产者样例代码
// * <AUTHOR>
// * @date 2023/10/12 17:16
// */
//@Controller
//public class DemoController
//{
//    @Autowired
//    private RedisUtils redisUtils;
//
//    @Value("${mq.type:1}")
//    private String mqType;
//
//    @Value("${mq.group.id:1}")
//    private String group;
//
//    @GetMapping("produce")
//    @ResponseBody
//    public ResultInfo<?> produce()
//    {
//        String topic = "demo";
//        MqFactory.newProducerService().produce(topic, DateUtils.getDate(DateUtils.YMDHMS_STD_MS));
//        return ResultInfo.success();
//    }
//}
