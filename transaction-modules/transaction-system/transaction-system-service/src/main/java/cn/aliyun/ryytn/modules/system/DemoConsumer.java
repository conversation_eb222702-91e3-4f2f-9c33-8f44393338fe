package cn.aliyun.ryytn.modules.system;//package cn.aliyun.ryytn.modules.system;
//
//import java.util.Observable;
//import java.util.Observer;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.event.ContextRefreshedEvent;
//import org.springframework.stereotype.Service;
//
//import cn.aliyun.ryytn.common.mq.MqFactory;
//import cn.aliyun.ryytn.common.mq.MqRecord;
//import cn.aliyun.ryytn.common.mq.api.ConsumerService;
//import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * @Description 消费者样例代码
// * <AUTHOR>
// * @date 2023/10/12 17:19
// */
//@Slf4j
//@Service
//public class DemoConsumer implements Observer, ApplicationListener<ContextRefreshedEvent>
//{
//    private ConsumerService consumerService;
//
//    @Autowired
//    private RedisUtils redisUtils;
//
//    @Override
//    public void onApplicationEvent(ContextRefreshedEvent event)
//    {
//        String topic = "demo";
//        consumerService = MqFactory.newConsumerService(topic);
//        consumerService.addObservers(this);
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public void update(Observable o, Object object)
//    {
//        MqRecord mqRecord = (MqRecord) object;
//        // 消息的key和value
//        String topic = mqRecord.getTopic();
//        String value = mqRecord.getValue();
//        log.info("{} consumer:{}", topic, value);
//
//        // 消息回执
//        consumerService.ack(mqRecord);
//    }
//}
