package cn.aliyun.ryytn.modules.system.controller;

import java.util.List;
import java.util.Objects;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.entity.SystemConfig;
import cn.aliyun.ryytn.common.entity.SystemConfigCategory;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.ConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 系统参数配置管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午2:11:38
 */
@Slf4j
@RestController
@RequestMapping("/api/system/config")
@Api(tags = "配置管理")
public class ConfigController
{
    /**
     *  系统参数配置管理接口
     */
    @Autowired
    private ConfigService configService;

    /**
     *
     * @Description 查询系统参数类型列表
     * @return ResultInfo<List < SystemConfigCategory>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:12
     */
    @PostMapping("querySystemConfigCategoryList")
    @ResponseBody
    @ApiOperation("查询系统参数类型列表")
    @RequiresPermissions(value = {"system:config:query"})
    public ResultInfo<List<SystemConfigCategory>> querySystemConfigCategoryList() throws Exception
    {
        List<SystemConfigCategory> list = configService.querySystemConfigCategoryList();
        return ResultInfo.success(list);
    }

    /**
     *
     * @Description 分页查询系统参数列表
     * @param condition
     * @return ResultInfo<PageInfo < SystemConfig>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:34
     */
    @PostMapping("querySystemConfigPage")
    @ResponseBody
    @ApiOperation("分页查询系统参数列表")
    @RequiresPermissions(value = {"system:config:query"})
    public ResultInfo<PageInfo<SystemConfig>> querySystemConfigPage(@RequestBody PageCondition<SystemConfig> condition) throws Exception
    {
        if (Objects.isNull(condition))
        {
            condition = new PageCondition<SystemConfig>();
        }
        PageInfo<SystemConfig> page = configService.querySystemConfigList(condition);
        return ResultInfo.success(page);
    }


    /**
     *
     * @Description 查询系统参数列表
     * @param SystemConfig
     * @return ResultInfo<List < SystemConfig>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:34
     */
    @PostMapping("querySystemConfigList")
    @ResponseBody
    @ApiOperation("不分页查询系统参数列表")
    @RequiresPermissions({})
    public ResultInfo<List<SystemConfig>> querySystemConfigList(@RequestBody SystemConfig SystemConfig) throws Exception
    {
        List<SystemConfig> list = configService.querySystemConfig(SystemConfig);
        return ResultInfo.success(list);
    }

    /**
     *
     * @Description 修改系统参数
     * @param systemConfig
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:18:57
     */
    @PostMapping("updateSystemConfig")
    @ResponseBody
    @ApiOperation("修改系统参数")
    @RequiresPermissions(value = {"system:config:update"})
    public ResultInfo<?> updateSystemConfig(@RequestBody SystemConfig systemConfig) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(systemConfig);

        // 修改系统参数
        configService.updateSystemConfig(systemConfig);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 批量修改系统参数
     * @param systemConfigList
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 上午11:19:12
     */
    @PostMapping("batchUpdateSystemConfig")
    @ResponseBody
    @ApiOperation("批量修改系统参数")
    @RequiresPermissions(value = {"system:config:update"})
    public ResultInfo<?> batchUpdateSystemConfig(@RequestBody List<SystemConfig> systemConfigList) throws Exception
    {
        // 参数校验
        ValidateUtil.checkIsNotEmpty(systemConfigList);

        // 修改系统参数
        configService.batchUpdateSystemConfig(systemConfigList);

        return ResultInfo.success();
    }

    /**
     *
     * @Description 刷新系统缓存
     * @throws Exception
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2022年8月22日 上午10:56:21
     */
    @PostMapping("refreshSystemConfig")
    @ResponseBody
    @ApiOperation("刷新系统参数缓存")
    @RequiresPermissions({})
    public ResultInfo<?> refreshSystemConfig() throws Exception
    {
        // 刷新系统参数
        configService.refreshSystemConfig();

        return ResultInfo.success();
    }
}
