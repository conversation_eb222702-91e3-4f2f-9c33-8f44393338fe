package cn.aliyun.ryytn.modules.system.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import com.alibaba.cosmo.remote.httpclient.HttpMethod;
import com.alibaba.fastjson.JSONArray;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.dataq.DataqResult;
import cn.aliyun.ryytn.common.dataq.api.DataqService;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.ProductService;
import cn.aliyun.ryytn.modules.system.dataqdao.DataqSkuDao;
import cn.aliyun.ryytn.modules.system.entity.dto.ProductionSkuDto;
import cn.aliyun.ryytn.modules.system.entity.dto.SkuDto;
import cn.aliyun.ryytn.modules.system.entity.vo.ProductionSkuConditionVo;
import cn.aliyun.ryytn.modules.system.entity.vo.SkuConditionVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 产品接口
 * <AUTHOR>
 * @date 2023/10/23 18:00
 */
@Slf4j
@Service
public class ProductServiceImpl implements ProductService, ApplicationListener<ContextRefreshedEvent>
{
    @Autowired
    private DataqService dataqService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private DataqSkuDao dataqSkuDao;

    /**
     *
     * @Description 启动刷新sku产品缓存
     * @param event
     * <AUTHOR>
     * @date 2023年11月29日 15:18
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event)
    {
        try
        {
            refreshSkuCache();
        }
        catch (Exception e)
        {
            log.error("onApplicationEvent refreshSkuCache has exceptoin:", e);
        }
    }

    /**
     * @Description 查询SKU列表
     * @param  skuConditionVo
     * @return List<SkuDto>
     * @throws Exception
     * @date 2023年10月23日 18:05
     */
    @Override
    public List<SkuDto> querySkuList(SkuConditionVo skuConditionVo) throws Exception
    {
        if (Objects.isNull(skuConditionVo))
        {
            skuConditionVo = new SkuConditionVo();
        }
        skuConditionVo.setStatusId(1);

        // dataq过滤器原因，导致浮点数字段（如净重、体积等字段无法获取），修改为直接查询数据库
        List<SkuDto> skuList = dataqSkuDao.querySkuList(skuConditionVo);
        List<ProductionSkuDto> productionSkuList = queryProductionSkuList(null);
        if (CollectionUtils.isNotEmpty(productionSkuList))
        {
            Map<String, ProductionSkuDto> map =
                productionSkuList.stream().collect(Collectors.toMap(ProductionSkuDto::getSkuCode, Function.identity(), (key1, key2) -> key1));
            productionSkuList.clear();
            for (SkuDto sku : skuList)
            {
                sku.setProductionSku(map.get(sku.getSkuCode()));
            }
        }

        return skuList;
    }

    /**
     *
     * @Description 查询生产SKU产品列表
     * @param productionSkuConditionVo
     * @return List<ProductionSkuDto>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月30日 18:10
     */
    @Override
    public List<ProductionSkuDto> queryProductionSkuList(ProductionSkuConditionVo productionSkuConditionVo) throws Exception
    {
        String path = String.valueOf(redisUtils.hget(CommonConstants.SYSTEMCONFIG_ALL_CACHE_KEY, "DATAQ_API_BASEBUS_SKU_PRODUCT_TABLE"));

        DataqResult<?> dataqResult = dataqService.invoke(HttpMethod.POST, path, null, null, productionSkuConditionVo);
        JSONArray jsonArray = (JSONArray) dataqResult.getData();
        List<ProductionSkuDto> productionSkuList = null;
        if (CollectionUtils.isNotEmpty(jsonArray))
        {
            productionSkuList = jsonArray.toJavaList(ProductionSkuDto.class);
        }

        return productionSkuList;
    }

    /**
     *
     * @Description 刷新sku产品到缓存
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月29日 15:38
     */
    @Override
    public void refreshSkuCache() throws Exception
    {
        List<SkuDto> skuList = querySkuList(null);
        if (CollectionUtils.isEmpty(skuList))
        {
            return;
        }
        Map<String, Object> map = skuList.stream().collect(Collectors.toMap(item -> {
            return StringUtils.format(CommonConstants.SKU_CACHE_KEY, item.getSkuCode());
        }, Function.identity(), (key1, key2) -> key1));

        redisUtils.multiSet(map);
    }
}
