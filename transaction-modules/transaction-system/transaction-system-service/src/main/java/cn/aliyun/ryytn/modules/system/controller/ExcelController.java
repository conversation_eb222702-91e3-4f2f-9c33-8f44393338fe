package cn.aliyun.ryytn.modules.system.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;

import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.excel.ExcelTask;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.ExcelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 电子表格管理接口
 * <AUTHOR>
 * @date 2023/10/17 11:18
 */
@Slf4j
@RestController
@RequestMapping("/api/system/excel")
@Api(tags = "电子表格管理")
public class ExcelController
{
    @Autowired
    private ExcelService excelService;

    @PostMapping("importExcel")
    @ResponseBody
    @ApiOperation("导入电子表格")
    @RequiresPermissions({})
    public ResultInfo<ExcelTask<?>> importExcel(@RequestPart("file") MultipartFile file, @RequestPart("condition") String condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(file);
        ExcelCondition excelCondition = JSON.parseObject(condition, ExcelCondition.class);

        ExcelTask<?> result = excelService.importExcel(file, excelCondition);

        return ResultInfo.success(result);
    }

    @PostMapping("exportExcel")
    @ResponseBody
    @ApiOperation("导出电子表格")
    @RequiresPermissions({})
    public ResultInfo<ExcelTask<?>> exportExcel(@RequestBody ExcelCondition condition) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(condition);

        ExcelTask<?> excelTask = excelService.exportExcel(condition);

        return ResultInfo.success(excelTask);
    }

    @PostMapping("queryExcelTask")
    @ResponseBody
    @ApiOperation("查询电子表格任务")
    @RequiresPermissions({})
    public ResultInfo<ExcelTask<?>> queryExcelTask(@RequestBody String id) throws Exception
    {
        ValidateUtil.checkIsNotEmpty(id);

        ExcelTask<?> result = excelService.queryExcelTask(id);

        return ResultInfo.success(result);
    }
}
