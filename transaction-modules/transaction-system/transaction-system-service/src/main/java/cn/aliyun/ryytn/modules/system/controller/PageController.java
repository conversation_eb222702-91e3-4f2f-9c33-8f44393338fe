package cn.aliyun.ryytn.modules.system.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.Page;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.system.api.PageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 页面管理接口
 * <AUTHOR>
 * @date 2023年9月19日 下午2:11:38
 */
@Slf4j
@RestController
@RequestMapping("/api/system/page")
@Api(tags = "菜单管理")
public class PageController
{
    /**
     * 页面管理接口
     */
    @Autowired
    private PageService pageService;

    /**
     *
     * @Description 查询菜单列表
     * @param name
     * @return ResultInfo<List < Page>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 13:56
     */
    @PostMapping("queryPageList")
    @ResponseBody
    @ApiOperation("查询菜单列表")
    @RequiresPermissions({})
    public ResultInfo<List<Page>> queryPageList(@RequestBody Page page) throws Exception
    {
        return ResultInfo.success(pageService.queryPageList(page));
    }

    /**
     *
     * @Description 查询菜单树
     * @param name
     * @return ResultInfo<List < Page>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月08日 13:56
     */
    @PostMapping("queryPageTree")
    @ResponseBody
    @ApiOperation("查询菜单树")
    @RequiresPermissions({})
    public ResultInfo<List<Page>> queryPageTree(@RequestBody Page page) throws Exception
    {
        return ResultInfo.success(pageService.queryPageTree(page));
    }
}
