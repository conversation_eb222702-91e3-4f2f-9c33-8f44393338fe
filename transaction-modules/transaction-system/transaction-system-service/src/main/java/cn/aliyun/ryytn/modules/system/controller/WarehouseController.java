package cn.aliyun.ryytn.modules.system.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.system.api.WarehouseService;
import cn.aliyun.ryytn.modules.system.entity.vo.PhysicWarehouseVo;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWarehouseListReqVo;
import cn.aliyun.ryytn.modules.system.entity.vo.QueryWarehouseListRspVo;
import cn.aliyun.ryytn.modules.system.entity.vo.WarehouseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 仓库管理接口
 * <AUTHOR>
 * @date 2023年10月10日 10:55
 */
@Slf4j
@RestController
@RequestMapping("/api/system/warehouse")
@Api(tags = "仓库管理")
public class WarehouseController
{
    /**
     * 仓库管理接口
     */
    @Autowired
    private WarehouseService warehouseService;

    /**
     *
     * @Description 查询仓库分组
     * @return ResultInfo<List < DepositoryVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月10日 10:55
     */
    @PostMapping("queryWarehouseGroup")
    @ResponseBody
    @ApiOperation("查询仓库分组")
    @RequiresPermissions({})
    public ResultInfo<List<WarehouseVo>> queryWarehouseGroup() throws Exception
    {

        List<WarehouseVo> warehouseVos = warehouseService.queryWarehouseGroup();

        return ResultInfo.success(warehouseVos);
    }

    /**
     *
     * @Description 查询仓库列表
     * @param warehouseListReqVo
     * @return ResultInfo<List < QueryWarehouseListRspVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月16日 21:10
     */
    @PostMapping("queryWarehouseList")
    @ResponseBody
    @ApiOperation("查询仓库列表")
    @RequiresPermissions({})
    public ResultInfo<List<QueryWarehouseListRspVo>> queryWarehouseList(@RequestBody QueryWarehouseListReqVo warehouseListReqVo) throws Exception
    {
        List<QueryWarehouseListRspVo> warehouseList = warehouseService.queryWarehouseList(warehouseListReqVo);
        return ResultInfo.success(warehouseList);
    }

    /**
     *
     * @Description 查询物理仓库列表
     * @param warehouseListReqVo
     * @return ResultInfo<List < PhysicWarehouseVo>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月14日 16:30
     */
    @PostMapping("queryPhysicWarehouseList")
    @ResponseBody
    @ApiOperation("查询物理仓库列表")
    @RequiresPermissions({})
    public ResultInfo<List<PhysicWarehouseVo>> queryPhysicWarehouseList(@RequestBody QueryWarehouseListReqVo warehouseListReqVo) throws Exception
    {
        List<PhysicWarehouseVo> physicWarehouseVos = warehouseService.queryPhysicWarehouseList(warehouseListReqVo);
        return ResultInfo.success(physicWarehouseVos);
    }

    /**
     *
     * @Description 刷新仓库逻辑仓/物理仓映射关系缓存
     * @return ResultInfo<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年12月27日 16:55
     */
    @PostMapping("refreshWarehouseMappingCache")
    @ResponseBody
    @ApiOperation("刷新仓库逻辑仓/物理仓映射关系缓存")
    @RequiresPermissions({})
    public ResultInfo<?> refreshWarehouseMappingCache() throws Exception
    {
        warehouseService.refreshWarehouseMappingCache();
        return ResultInfo.success();
    }
}
