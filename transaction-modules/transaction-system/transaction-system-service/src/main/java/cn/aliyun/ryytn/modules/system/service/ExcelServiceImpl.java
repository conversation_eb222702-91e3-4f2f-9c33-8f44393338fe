package cn.aliyun.ryytn.modules.system.service;

import java.io.File;
import java.util.Date;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;

import cn.aliyun.ryytn.common.constants.CommonConstants;
import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.excel.AbstractExportExcelHandler;
import cn.aliyun.ryytn.common.excel.AbstractImportExcelHandler;
import cn.aliyun.ryytn.common.excel.ExcelCondition;
import cn.aliyun.ryytn.common.excel.ExcelResult;
import cn.aliyun.ryytn.common.excel.ExcelTask;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.concurrent.ThreadPoolExecutorUtils;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.oss.OssUtils;
import cn.aliyun.ryytn.common.utils.redis.RedisUtils;
import cn.aliyun.ryytn.common.utils.resource.I18nUtils;
import cn.aliyun.ryytn.common.utils.spring.SpringUtil;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.common.utils.validate.ValidateUtil;
import cn.aliyun.ryytn.modules.system.api.ExcelService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 电子表格实现类
 * <AUTHOR>
 * @date 2023/10/19 12:54
 */
@Slf4j
@Service
public class ExcelServiceImpl implements ExcelService
{
    /**
     * 阿里云OSS工具类
     */
    @Autowired
    private OssUtils ossUtils;

    /**
     * Redis工具类
     */
    @Autowired
    private RedisUtils redisUtils;

    @Value("${tmp.file.location:/tmp/transaction/}")
    private String tmpFileLocation;

    private static final String BUCKET_NAME_EXCEL = "EXCEL";

    /**
     *
     * @Description 导入电子表格
     * @param file
     * @param condition
     * @return ExcelTask<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月19日 12:54
     */
    @Override
    public ExcelTask<?> importExcel(MultipartFile file, ExcelCondition condition) throws Exception
    {
        String key = condition.getKey();
        ValidateUtil.checkIsNotEmpty(key);

        AbstractImportExcelHandler handler = (AbstractImportExcelHandler) SpringUtil.getBean(key);
        if (Objects.isNull(handler))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }

        // 封装会话
        condition.setSession(ServiceContextUtils.currentSession());

        ExcelTask<ExcelResult> excelTask = new ExcelTask(key);
        redisUtils.set(StringUtils.format(CommonConstants.EXCELTASK_KEY, excelTask.getId()), excelTask, handler.timeout);

        // 防止异步springboot删除tomcat下临时文件，先复制一份文件
        File dir = new File(tmpFileLocation);
        if (!dir.exists())
        {
            dir.mkdirs();
        }
        File localFile = new File(dir.getAbsolutePath() + SeqUtils.getSequenceUid() +
            StringUtils.substringAfter(file.getOriginalFilename(), StringUtils.POINT_SEPARATOR));
        file.transferTo(localFile);

        ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.excelThread).submit(() -> {
            try
            {
                ServiceContextUtils.setSession(condition.getSession());
                ExcelResult<?> excelResult = handler.importExcel(localFile, condition);
                excelTask.setResult(excelResult);
                if (Objects.nonNull(excelResult) && excelResult.getSuccess() > 0)
                {
                    excelTask.setStatus(ExcelTask.ExcelTaskStatusEnum.SUCCESS);
                }
                else
                {
                    excelTask.setStatus(ExcelTask.ExcelTaskStatusEnum.FAILED);
                }
                if (Objects.nonNull(excelResult.getBytes()))
                {
                    // 上传文件
                    String fileId = new StringBuilder(SeqUtils.getSequenceUid()).append(ExcelTypeEnum.XLSX.getValue()).toString();
                    ObjectMetadata metadata = new ObjectMetadata();
                    // 过期时间1天
                    String expireTime = DateUtils.cst2utc(DateUtils.addDays(new Date(), 1), DateUtils.YMDHMS_STD_UTC);
                    metadata.setHeader(OSSHeaders.EXPIRES, expireTime);
                    // 文件ACL访问权限为私有
                    metadata.setObjectAcl(CannedAccessControlList.Private);
                    ossUtils.uploadFile(fileId, excelResult.getBytes(), metadata);

                    excelResult.setBytes(null);
                    excelTask.setFileId(fileId);
                }
            }
            catch (ServiceException se)
            {
                log.error("exportExcel has exception:", se);
                excelTask.setStatus(ExcelTask.ExcelTaskStatusEnum.FAILED);
                excelTask.setMsg(StringUtils.isNotBlank(se.getErrorMessage()) ? se.getErrorMessage() : I18nUtils.getValue(se.getErrorCode()));
            }
            catch (Exception e)
            {
                log.error("exportExcel has exception:", e);
                excelTask.setStatus(ExcelTask.ExcelTaskStatusEnum.FAILED);
                excelTask.setMsg("失败");
            }
            finally
            {
                ServiceContextUtils.remove();
                redisUtils.set(StringUtils.format(CommonConstants.EXCELTASK_KEY, excelTask.getId()), excelTask);
                localFile.delete();
            }
        });

        return excelTask;
    }

    /**
     *
     * @Description 导出电子表格
     * @return ExcelTask<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月19日 12:54
     */
    @Override
    public ExcelTask<?> exportExcel(ExcelCondition condition) throws Exception
    {
        String key = condition.getKey();
        ValidateUtil.checkIsNotEmpty(key);

        AbstractExportExcelHandler handler = (AbstractExportExcelHandler) SpringUtil.getBean(key);
        if (Objects.isNull(handler))
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_PARAM_INVALID);
        }

        // 封装会话
        condition.setSession(ServiceContextUtils.currentSession());

        ExcelTask<String> excelTask = new ExcelTask<String>(key);
        redisUtils.set(StringUtils.format(CommonConstants.EXCELTASK_KEY, excelTask.getId()), excelTask, handler.timeout);

        ThreadPoolExecutorUtils.newThreadPoolExecutor(ThreadPoolExecutorUtils.ThreadPoolNameEnum.excelThread).submit(() -> {
            try
            {
                ServiceContextUtils.setSession(condition.getSession());
                byte[] bytes = handler.export(condition);

                // 上传文件
                String fileId = new StringBuilder(SeqUtils.getSequenceUid()).append(ExcelTypeEnum.XLSX.getValue()).toString();
                ObjectMetadata metadata = new ObjectMetadata();
                // 过期时间1天
                String expireTime = DateUtils.cst2utc(DateUtils.addDays(new Date(), 1), DateUtils.YMDHMS_STD_UTC);
                metadata.setHeader(OSSHeaders.EXPIRES, expireTime);
                // 文件ACL访问权限为私有
                metadata.setObjectAcl(CannedAccessControlList.Private);
                ossUtils.uploadFile(fileId, bytes, metadata);

                excelTask.setFileId(fileId);
                excelTask.setStatus(ExcelTask.ExcelTaskStatusEnum.SUCCESS);
            }
            catch (ServiceException se)
            {
                log.error("exportExcel has exception:", se);
                excelTask.setStatus(ExcelTask.ExcelTaskStatusEnum.FAILED);
                excelTask.setMsg(se.getErrorMessage());
            }
            catch (Exception e)
            {
                log.error("exportExcel has exception:", e);
                excelTask.setStatus(ExcelTask.ExcelTaskStatusEnum.FAILED);
                excelTask.setMsg(null);
            }
            finally
            {
                ServiceContextUtils.remove();
                redisUtils.set(StringUtils.format(CommonConstants.EXCELTASK_KEY, excelTask.getId()), excelTask);
            }
        });

        return excelTask;
    }

    /**
     *
     * @Description 查询电子表格任务
     * @param id
     * @return ExcelTask<?>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月19日 13:58
     */
    @Override
    public ExcelTask<?> queryExcelTask(String id) throws Exception
    {
        ExcelTask<?> result = (ExcelTask<?>) redisUtils.get(StringUtils.format(CommonConstants.EXCELTASK_KEY, id));
        return result;
    }
}
