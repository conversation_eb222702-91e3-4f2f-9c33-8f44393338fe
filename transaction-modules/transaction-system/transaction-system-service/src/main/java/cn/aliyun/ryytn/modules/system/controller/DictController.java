package cn.aliyun.ryytn.modules.system.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import cn.aliyun.ryytn.common.entity.DictData;
import cn.aliyun.ryytn.common.entity.DictType;
import cn.aliyun.ryytn.common.entity.PageCondition;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.system.api.DictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 字典管理接口
 * <AUTHOR>
 * @date 2023/9/25 11:10
 */
@Slf4j
@RestController
@RequestMapping("/api/system/dict")
@Api(tags = "字典管理")
public class DictController
{
    @Autowired
    private DictService dictService;

    /**
     *
     * @Description 查询字典类型列表
     * @param dictType
     * @return ResultInfo<PageInfo < DictType>>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:28:40
     */
    @PostMapping("queryDictTypeList")
    @ResponseBody
    @ApiOperation("不分页查询字典类型列表")
    @RequiresPermissions(value = {"system:dict:query"})
    public ResultInfo<List<DictType>> queryDictTypeList(@RequestBody DictType dictType) throws Exception
    {
        List<DictType> list = dictService.queryDictTypeList(dictType);
        return ResultInfo.success(list);
    }

    /**
     *
     * @Description 分页查询字典类型列表
     * @param condition
     * @return ResultInfo<PageInfo < DictType>>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:28:40
     */
    @PostMapping("queryDictTypePage")
    @ResponseBody
    @ApiOperation("分页查询字典类型列表")
    @RequiresPermissions(value = {"system:dict:query"})
    public ResultInfo<PageInfo<DictType>> queryDictTypePage(@RequestBody PageCondition<DictType> condition) throws Exception
    {
        PageInfo<DictType> pageInfo = dictService.queryDictTypePage(condition);
        return ResultInfo.success(pageInfo);
    }

    /**
     *
     * @Description 查询字典类型详情
     * @param dictTypeId
     * @return ResultInfo<DictType>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:30:13
     */
    @PostMapping("queryDictTypeDetail")
    @ResponseBody
    @ApiOperation("查询字典类型详情")
    @RequiresPermissions(value = {"system:dict:query"})
    public ResultInfo<DictType> queryDictTypeDetail(@RequestBody String dictTypeId) throws Exception
    {
        DictType dictType = dictService.queryDictTypeDetail(dictTypeId);
        return ResultInfo.success(dictType);
    }

    /**
     *
     * @Description 新增字典类型
     * @param dictType
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:31:04
     */
    @PostMapping("addDictType")
    @ResponseBody
    @ApiOperation("新增字典类型")
    @RequiresPermissions(value = {"system:dict:add"})
    public ResultInfo<?> addDictType(@RequestBody DictType dictType) throws Exception
    {
        dictService.addDictType(dictType);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 修改字典类型
     * @param dictType
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:31:20
     */
    @PostMapping("updateDictType")
    @ResponseBody
    @ApiOperation("修改字典类型")
    @RequiresPermissions(value = {"system:dict:update"})
    public ResultInfo<?> updateDictType(@RequestBody DictType dictType) throws Exception
    {
        dictService.updateDictType(dictType);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 删除字典类型
     * @param dictTypeIdList
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:31:33
     */
    @PostMapping("deleteDictType")
    @ResponseBody
    @ApiOperation("删除字典类型")
    @RequiresPermissions(value = {"system:dict:delete"})
    public ResultInfo<?> deleteDictType(@RequestBody List<String> dictTypeIdList) throws Exception
    {
        dictService.deleteDictType(dictTypeIdList);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 校验字典类型唯一性
     * @param dictType
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:31:45
     */
    @PostMapping("checkDictTypeUnique")
    @ResponseBody
    @ApiOperation("校验字典类型唯一性")
    @RequiresPermissions(value = {"system:dict:add", "system:dict:update"}, logical = Logical.OR)
    public ResultInfo<?> checkDictTypeUnique(@RequestBody String dictType) throws Exception
    {
        dictService.checkDictTypeUnique(dictType);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 加载字典缓存
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:36:05
     */
    @PostMapping("loadDictCache")
    @ResponseBody
    @ApiOperation("加载字典缓存")
    @RequiresPermissions({})
    public ResultInfo<?> loadDictCache() throws Exception
    {
        dictService.loadDictCache();
        return ResultInfo.success();
    }

    /**
     *
     * @Description 分页查询字典数据列表
     * @param condition
     * @return ResultInfo<PageInfo < DictData>>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:47:43
     */
    @PostMapping("queryDictDataPage")
    @ResponseBody
    @ApiOperation("分页查询字典数据列表")
    @RequiresPermissions(value = {"system:dict:query"})
    public ResultInfo<PageInfo<DictData>> queryDictDataPage(@RequestBody PageCondition<DictData> condition) throws Exception
    {
        PageInfo<DictData> page = dictService.queryDictDataPage(condition);
        return ResultInfo.success(page);
    }

    /**
     *
     * @Description 查询字典数据列表
     * @param dictData
     * @return ResultInfo<List < DictData>>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:47:43
     */
    @PostMapping("queryDictDataList")
    @ResponseBody
    @ApiOperation("不分页查询字典数据列表")
    @RequiresPermissions({})
    public ResultInfo<List<DictData>> queryDictDataList(@RequestBody DictData dictData) throws Exception
    {
        List<DictData> list = dictService.queryDictDataList(dictData);
        return ResultInfo.success(list);
    }

    /**
     *
     * @Description 查询字典数据树
     * @param dictData
     * @return ResultInfo<List < DictData>>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:47:43
     */
    @PostMapping("queryDictDataTree")
    @ResponseBody
    @ApiOperation("查询字典数据树")
    @RequiresPermissions({})
    public ResultInfo<List<DictData>> queryDictDataTree(@RequestBody DictData dictData) throws Exception
    {
        List<DictData> tree = dictService.queryDictDataTree(dictData);
        return ResultInfo.success(tree);
    }

    /**
     *
     * @Description 查询字典数据详情
     * @param dictId
     * @return ResultInfo<DictData>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:49:54
     */
    @PostMapping("queryDictDataDetail")
    @ResponseBody
    @ApiOperation("查询字典数据详情")
    @RequiresPermissions(value = {"system:dict:query"})
    public ResultInfo<DictData> queryDictDataDetail(@RequestBody String dictId) throws Exception
    {
        DictData dictData = dictService.queryDictDataDetail(dictId);
        return ResultInfo.success(dictData);
    }

    /**
     *
     * @Description 新增字典数据
     * @param dictData
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月25日 下午10:07:09
     */
    @PostMapping("addDictData")
    @ResponseBody
    @ApiOperation("新增字典数据")
    @RequiresPermissions(value = {"system:dict:add"})
    public ResultInfo<?> addDictData(@RequestBody DictData dictData) throws Exception
    {
        dictService.addDictData(dictData);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 修改字典数据
     * @param dictData
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月25日 下午10:07:28
     */
    @PostMapping("updateDictData")
    @ResponseBody
    @ApiOperation("修改字典数据")
    @RequiresPermissions(value = {"system:dict:update"})
    public ResultInfo<?> updateDictData(@RequestBody DictData dictData) throws Exception
    {
        dictService.updateDictData(dictData);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 删除字典数据
     * @param dictIdList
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月25日 下午10:06:49
     */
    @PostMapping("deleteDictData")
    @ResponseBody
    @ApiOperation("删除字典数据")
    @RequiresPermissions(value = {"system:dict:delete"})
    public ResultInfo<?> deleteDictData(@RequestBody List<String> dictIdList) throws Exception
    {
        dictService.deleteDictData(dictIdList);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 校验字典数据唯一性
     * @param dictData
     * @return ResultInfo<?>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:31:45
     */
    @PostMapping("checkDictDataUnique")
    @ResponseBody
    @ApiOperation("校验字典数据唯一性")
    @RequiresPermissions(value = {"system:dict:add", "system:dict:update"}, logical = Logical.OR)
    public ResultInfo<?> checkDictDataUnique(@RequestBody DictData dictData) throws Exception
    {
        dictService.checkDictDataUnique(dictData);
        return ResultInfo.success();
    }

    /**
     *
     * @Description 查询全部数据字典(所有前端应用都需要获取)
     * @return ResultInfo<List < DictType>>
     * <AUTHOR>
     * @date 2023年9月25日 下午9:47:43
     */
    @PostMapping("queryAllDictList")
    @ResponseBody
    @ApiOperation("查询全部数据字典列表")
    @RequiresPermissions({})
    public ResultInfo<List<DictData>> queryAllDictList() throws Exception
    {
        List<DictData> list = dictService.queryAllDictList();
        return ResultInfo.success(list);
    }
}
