package cn.aliyun.ryytn.modules.system.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import cn.aliyun.ryytn.common.constants.ErrorCodeConstants;
import cn.aliyun.ryytn.common.entity.ServiceFile;
import cn.aliyun.ryytn.common.exception.ServiceException;
import cn.aliyun.ryytn.common.utils.date.DateUtils;
import cn.aliyun.ryytn.common.utils.file.FileUtils;
import cn.aliyun.ryytn.common.utils.oss.OssUtils;
import cn.aliyun.ryytn.common.utils.string.SeqUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.FileService;
import cn.aliyun.ryytn.modules.system.dao.FileDao;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 文件接口
 * <AUTHOR>
 * @date 2023年9月19日 下午3:11:58
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService
{
    /**
     * 文件数据库操作接口
     */
    @Autowired
    private FileDao fileDao;

    /**
     * 阿里云OSS工具类
     */
    @Autowired
    private OssUtils ossUtils;

    /**
     *
     * @Description 上传文件
     * @param multipartFile
     * @return String
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年9月22日 下午4:49:37
     */
    @Override
    public String uploadFile(MultipartFile multipartFile) throws Exception
    {
        // 文件编号，雪花算法拼接文件后缀，结构化存储服务上也有后缀，便于维护
        String fileName = multipartFile.getOriginalFilename();
        String suffix = FileUtils.getExtName(fileName);
        String fileId = new StringBuilder(SeqUtils.getSequenceUid()).append(FileUtils.FILENAME_SEPARATOR).append(suffix).toString();
        Date currentDate = new Date();

        // 上传文件
        ossUtils.uploadFile(fileId, multipartFile.getBytes());

        // 封装文件对象
        ServiceFile serviceFile = new ServiceFile();
        serviceFile.setFileId(fileId);
        serviceFile.setFileType(FileUtils.getFileType(fileName));
        serviceFile.setOFileName(fileName);
        serviceFile.setSuffix(suffix);
        serviceFile.setCreatedBy(StringUtils.EMPTY);
        serviceFile.setCreatedTime(currentDate);

        // 文件数据入库
        fileDao.addServiceFile(serviceFile);

        return fileId;
    }

    /**
     *
     * @Description 批量上传文件
     * @param multipartFileList
     * @return List<String>
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年9月22日 下午4:49:37
     */
    @Override
    public List<String> batchUploadFile(List<MultipartFile> multipartFileList) throws Exception
    {
        List<String> fileIdList = new ArrayList<String>();
        List<ServiceFile> serviceFileList = new ArrayList<ServiceFile>();
        Date currentDate = new Date();
        // 暂时循环处理，后续对接OSS实现批量上传后调整
        for (MultipartFile multipartFile : multipartFileList)
        {
            // 文件编号，雪花算法拼接文件后缀，结构化存储服务上也有后缀，便于维护
            String fileName = multipartFile.getOriginalFilename();
            String suffix = FileUtils.getExtName(fileName);
            String fileId = new StringBuilder(SeqUtils.getSequenceUid()).append(FileUtils.FILENAME_SEPARATOR).append(suffix).toString();
            fileIdList.add(fileId);

            // 上传文件
            ossUtils.uploadFile(fileId, multipartFile.getBytes());

            // 封装文件对象
            ServiceFile serviceFile = new ServiceFile();
            serviceFile.setFileId(fileId);
            serviceFile.setFileType(FileUtils.getFileType(fileName));
            serviceFile.setOFileName(fileName);
            serviceFile.setSuffix(suffix);
            serviceFile.setCreatedBy(StringUtils.EMPTY);
            serviceFile.setCreatedTime(currentDate);

            serviceFileList.add(serviceFile);
        }

        // 文件数据入库
        fileDao.addServiceFileList(serviceFileList);

        return fileIdList;
    }

    /**
     *
     * @Description 根据文件编号查询文件详情
     * @param fileId
     * @return byte[]
     * <AUTHOR>
     * @date 2023年9月22日 下午4:21:55
     */
    @Override
    public byte[] download(String fileId) throws Exception
    {
        // 下载文件
        byte[] content = ossUtils.downloadFile(fileId);

        return content;
    }

    /**
     *
     * @Description 删除文件
     * @param fileIdList
     * @throws ServiceException
     * <AUTHOR>
     * @date 2023年9月22日 下午4:48:28
     */
    @Override
    public void deleteFile(List<String> fileIdList) throws Exception
    {
        // 文件如果关联业务，不允许删除
        int exists = fileDao.queryServiceRefExists(fileIdList);
        if (exists > 0)
        {
            throw new ServiceException(ErrorCodeConstants.FAIL_DATA_REFERENCED);
        }

        // 删除文件
        ossUtils.deleteFile(fileIdList);

        // 删除文件数据
        fileDao.deleteServiceFile(fileIdList);
    }

    /**
     *
     * @Description 删除失效文件
     * edu_service_file表serviceType如果为空，则认为没有被业务使用
     * 默认删除30天以前的文件
     * @throws Exception
     * <AUTHOR>
     * @date 2023年9月22日 下午4:48:28
     */
    @Override
    public void deleteExpireFile() throws Exception
    {
        // 失效时间30天以前
        String expireTime = DateUtils.formatTime(DateUtils.addDays(new Date(), -30), DateUtils.YMDHMS_STD);

        // 查询失效的文件编号
        List<String> fileIdList = fileDao.queryExpireServiceFile(expireTime);

        // 删除失效文件
        ossUtils.deleteFile(fileIdList);

        // 删除失效文件数据
        fileDao.deleteServiceFile(fileIdList);
    }
}
