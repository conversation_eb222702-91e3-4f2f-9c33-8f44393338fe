package cn.aliyun.ryytn.modules.system.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.aliyun.ryytn.common.entity.Page;
import cn.aliyun.ryytn.common.entity.PageConfig;
import cn.aliyun.ryytn.common.utils.context.ServiceContextUtils;
import cn.aliyun.ryytn.common.utils.string.StringUtils;
import cn.aliyun.ryytn.modules.system.api.PageConfigService;
import cn.aliyun.ryytn.modules.system.dao.PageConfigDao;
import cn.aliyun.ryytn.modules.system.entity.vo.PageConfigVo;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 页面配置管理接口
 * <AUTHOR>
 * @date 2023年10月11日 10:50
 */
@Slf4j
@Service
public class PageConfigServiceImpl implements PageConfigService
{

    @Autowired
    private PageConfigDao pageConfigDao;

    /**
     * @Description 查询页面配置
     * @param pageConfig
     * @return List<PageConfig>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月11日 10:50
     */
    @Override
    public Map<String, Object> queryPageConfigList(PageConfig pageConfig) throws Exception
    {
        Map<String, Object> returnData = new HashMap<>();

        List<PageConfig> pageConfigs = pageConfigDao.queryPageConfigList(pageConfig);
        Integer pageSumFlag = pageConfigDao.getPageSumFlag(pageConfig.getPageId());
        returnData.put("sumFlag", pageSumFlag == 0 ? false : true);
        returnData.put("pageConfigList", pageConfigs);
        return returnData;
    }

    /**
     *
     * @Description 新增页面配置
     * @param pageConfigVo
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月11日 11:25
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddPageConfig(PageConfigVo pageConfigVo)
    {

        //是否合计
        Boolean sumFlag = pageConfigVo.getSumFlag();

        //页面配置列表
        List<PageConfig> pageConfigList = pageConfigVo.getPageConfigList();

        //更新页面的配置信息(先删后增)
        PageConfig pageConfig = pageConfigList.get(0);
        String pageId = pageConfig.getPageId();
        if (StringUtils.isNotEmpty(pageId))
        {
            pageConfigDao.deleteByCondition(pageId);
        }
        for (PageConfig config : pageConfigList)
        {
            //设置创建人
            String loginId = ServiceContextUtils.currentSession().getAccount().getLoginId();
            config.setCreatedBy(loginId);
        }
        pageConfigDao.batchAddPageConfig(pageConfigList);

        //修改页面表
        Page page = new Page();
        page.setSumFlag(sumFlag);
        page.setId(pageConfig.getPageId());
        pageConfigDao.updatePage(page);


    }

}
