package cn.aliyun.ryytn.modules.system.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.aliyun.ryytn.common.entity.Factory;
import cn.aliyun.ryytn.common.entity.ResultInfo;
import cn.aliyun.ryytn.modules.system.api.FactoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 工厂管理接口
 * <AUTHOR>
 * @date 2023年10月9日 17:38
 */
@Slf4j
@RestController
@RequestMapping("/api/system/factory")
@Api(tags = "工厂管理")
public class FactoryController
{

    /**
     * 渠道管理接口
     */
    @Autowired
    private FactoryService factoryService;

    /**
     *
     * @Description 查询所有工厂列表
     * @return ResultInfo<List < Factory>>
     * @throws Exception
     * <AUTHOR>
     * @date 2023年10月9日 14:24
     */
    @PostMapping("queryAllFactory")
    @ResponseBody
    @ApiOperation("查询所有工厂列表")
    public ResultInfo<List<Factory>> queryAllFactory() throws Exception
    {
        List<Factory> result = factoryService.queryAllFactory();
        return ResultInfo.success(result);
    }

}
