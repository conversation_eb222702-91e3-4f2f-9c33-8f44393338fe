package cn.aliyun.ryytn.modules.system.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.aliyun.ryytn.common.api.TaskService;
import cn.aliyun.ryytn.common.entity.SchedulerJob;
import cn.aliyun.ryytn.modules.system.api.OAService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 新增需求计划版本任务实现类
 * <AUTHOR>
 * @date 2023/11/7 14:15
 */
@Slf4j
@Service
public class SyncOAPersonTaskServiceImpl implements TaskService
{
    @Autowired
    private OAService oaService;

    /**
     *
     * @Description 执行任务，异常直接抛出给quartz，按照misfire策略执行
     * @param schedulerJob
     * @throws Exception
     * <AUTHOR>
     * @date 2023年11月08日 15:14
     */
    @Override
    public void process(SchedulerJob schedulerJob) throws Exception
    {
        oaService.syncOAPerson();
    }
}
