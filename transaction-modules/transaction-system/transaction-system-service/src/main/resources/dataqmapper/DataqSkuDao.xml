<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dataqdao.DataqSkuDao">
	<select id="querySkuList" parameterType="cn.aliyun.ryytn.modules.system.entity.vo.SkuConditionVo"
            resultType="cn.aliyun.ryytn.modules.system.entity.dto.SkuDto">
		select
			sku_code,
			code_69,
			sku_name,
			status_id,
			lifecycle,
			category_code,
			category_name,
			shelf_life,
			rought_weight,
			net_weight,
			weight_unit,
			volume,
			volume_unit,
			length,
			width,
			height,
			unit,
			plan_unit_cnt,
			lv1_category_code,
			lv1_category_name,
			lv2_category_code,
			lv2_category_name,
			lv3_category_code,
			lv3_category_name,
			fin_category_code,
			fin_category_name,
			atomic_unit_cnt,
			price,
			price_unit,
			gift,
			brand_code,
			brand_name,
			brand_group,
			brand_org,
			gmt_create,
			gmt_modified,
			ds
		from
			dim_bas_sku_baisc_info_df
		where 1=1
		<if test="statusId != null">
			AND status_id = #{statusId}
		</if>
		<if test="skuCodes != null and skuCodes != ''">
			AND sku_code = ANY(STRING_TO_ARRAY(#{skuCodes},','))
		</if>
		<if test="skuName != null and skuName != ''">
			AND sku_name LIKE '%#{skuName}%'
		</if>
		<if test="skuCode != null and skuCode != ''">
			AND sku_code = #{skuCode}
		</if>
		<if test="skuKey != null and skuKey != ''">
			AND (sku_code LIKE '%#{skuKey}%' OR sku_name LIKE '%#{skuName}%')
		</if>
	</select>

	<select id="querySaleSkuByProductionSku" parameterType="java.lang.String" resultType="java.lang.String">
		select sku_code from tdm_kcjh_rltn_product_sale_sku_df
		where production_code=any(string_to_array(#{productionCode},','))
	</select>
</mapper>