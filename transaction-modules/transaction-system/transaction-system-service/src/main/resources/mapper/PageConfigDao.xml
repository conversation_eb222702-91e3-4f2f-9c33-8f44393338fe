<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dao.PageConfigDao">
    <select id="queryPageConfigList" resultType="cn.aliyun.ryytn.common.entity.PageConfig"
            parameterType="cn.aliyun.ryytn.common.entity.PageConfig">
        SELECT pageconf.id,
               pageconf.page_id,
               pageconf.row_name,
               pageconf.row_field,
               pageconf.width,
               pageconf.sort_no,
               pageconf.freeze_flag,
               pageconf.show_flag,
               pageconf.gather_flag,
               pageconf.created_by,
               pageconf.created_time,
               pageconf.updated_by,
               pageconf.updated_time
        from t_ryytn_page_config pageconf
        <where>
            <if test="id!=null">
                pageconf.id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
            </if>
			<if test="pageId!=null">
                pageconf.page_id = #{pageId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
            </if>
            <if test="rowName!=null">
                strpos(pageconf.row_name,#{rowName}) &gt; 0
            </if>
        </where>
		order by pageconf.sort_no
    </select>

    <insert id="batchAddPageConfig" parameterType="java.util.List">
        INSERT INTO t_ryytn_page_config (
		  page_id,
		  row_name,
		  row_field,
		  width,
		  sort_no,
		  freeze_flag,
		  show_flag,
		  gather_flag,
		  created_by
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.pageId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.rowName},
			#{item.rowField},
			#{item.width},
			#{item.sortNo},
			#{item.freezeFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler},
			#{item.showFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler},
			#{item.gatherFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler},
			#{item.createdBy}
		  )
		</foreach>
    </insert>

	<delete id="deleteByCondition">
		delete
        from t_ryytn_page_config
        where page_id = #{pageId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<update id="updatePage" parameterType="cn.aliyun.ryytn.common.entity.Page">
		UPDATE
			t_ryytn_page
		SET
			sum_flag = #{sumFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler}
		WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<select id="getPageSumFlag" resultType="Integer">
		select sum_flag
        from t_ryytn_page
        where id = #{pageId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>
</mapper>