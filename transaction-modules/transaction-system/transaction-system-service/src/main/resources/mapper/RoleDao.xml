<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dao.RoleDao">
    <select id="queryRoleList" parameterType="cn.aliyun.ryytn.modules.system.entity.vo.RoleConditionVo" resultType="cn.aliyun.ryytn.common.entity.Role">
        SELECT
          id,
          name,
          default_flag,
          status,
          description,
          sort_no,
          data_type,
          created_by,
          created_time,
          updated_by,
          updated_time
        FROM
          t_ryytn_role
        WHERE 1=1
        <if test="name != null and name != ''">
            AND strpos(name, #{name}) &gt; 0
        </if>
        <if test="beginTime != null">
            AND created_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            AND created_time &lt;= #{endTime}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY id
    </select>

    <select id="queryRoleDetail" parameterType="java.lang.String" resultType="cn.aliyun.ryytn.common.entity.Role">
        SELECT
          id,
          name,
          default_flag,
          status,
          description,
          sort_no,
          data_type,
          created_by,
          created_time,
          updated_by,
          updated_time
        FROM
          t_ryytn_role
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <select id="queryRoleExists" parameterType="cn.aliyun.ryytn.common.entity.Role" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_ryytn_role
        WHERE name = #{name}
        <if test="id != null and id != ''">
            AND id &lt;&gt; #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </if>
    </select>

    <insert id="addRole" parameterType="cn.aliyun.ryytn.common.entity.Role">
        INSERT INTO t_ryytn_role (
          id,
          name,
          default_flag,
          status,
          description,
          sort_no,
          data_type,
          created_by
        )
        VALUES
          (
            #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{name},
            #{defaultFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler},
            #{status},
            #{description},
            #{sortNo},
            #{dataType},
            #{createdBy}
          )
    </insert>

    <update id="updateRole" parameterType="cn.aliyun.ryytn.common.entity.Role">
        UPDATE
          t_ryytn_role
        SET
          name = #{name},
          status = #{status},
          description = #{description},
          sort_no = #{sortNo},
          updated_by = #{updatedBy}
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </update>
    
    <delete id="deleteRole" parameterType="java.lang.String">
        DELETE FROM t_ryytn_role WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>
    
    <delete id="deleteAccountRoleByRoleId" parameterType="java.lang.String">
        DELETE FROM t_ryytn_account_role WHERE role_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <update id="updateRoleStatus" parameterType="cn.aliyun.ryytn.common.entity.Role">
        UPDATE
          t_ryytn_role
        SET
          status = #{status},
          updated_by = #{updatedBy}
        WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </update>

    <select id="queryUnbindAccountList" parameterType="cn.aliyun.ryytn.modules.system.entity.vo.AccountRoleVo"
            resultType="cn.aliyun.ryytn.common.entity.Account">
        SELECT
		  a.id,
		  a.name,
		  a.nick_name,
		  a.work_code,
		  a.login_id,
		  a.oa_id,
		  b.department_id,
          array_to_string(array_agg(e.department_name),'/') AS departmentName,
		  c.id AS jobTitleId,
		  c.job_title_mark AS jobTitleName
		FROM
		  t_ryytn_account a
		  LEFT JOIN t_ryytn_oa_person b
			ON a.oa_id = b.id
		  LEFT JOIN t_ryytn_oa_jobtitle c
			ON b.job_title_id = c.id
		  LEFT JOIN t_ryytn_oa_department d
			ON b.department_id = d.id
		  LEFT JOIN t_ryytn_oa_department e
			ON e.id = ANY(STRING_TO_ARRAY(d.sup_dep_ids,','))
			OR d.id = e.id
		WHERE NOT EXISTS (SELECT 'X' FROM t_ryytn_account_role f WHERE a.id = f.account_id AND f.role_id = #{roleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler})
        <if test="keyword != null and keyword != ''">
            AND (strpos(a.name, #{keyword}) &gt; 0 OR strpos(a.nick_name, #{keyword}) &gt; OR strpos(a.work_code, #{keyword}) &gt; OR strpos(a.login_id,
            #{keyword}) &gt; 0)
        </if>
		GROUP BY a.id,b.department_id,c.id
        ORDER BY a.id
    </select>

    <select id="queryBindedAccountList" parameterType="cn.aliyun.ryytn.modules.system.entity.vo.AccountRoleVo"
            resultType="cn.aliyun.ryytn.common.entity.Account">
        SELECT
		  a.id,
		  a.name,
		  a.nick_name,
		  a.work_code,
		  a.login_id,
		  a.oa_id,
		  b.department_id,
          array_to_string(array_agg(e.department_name),'/') AS departmentName,
		  c.id AS jobTitleId,
		  c.job_title_mark AS jobTitleName,
          array_to_string(array_agg(g.name),',') AS roleNames
		FROM
		  t_ryytn_account a
		  LEFT JOIN t_ryytn_oa_person b
			ON a.oa_id = b.id
		  LEFT JOIN t_ryytn_oa_jobtitle c
			ON b.job_title_id = c.id
		  LEFT JOIN t_ryytn_oa_department d
			ON b.department_id = d.id
		  LEFT JOIN t_ryytn_oa_department e
			ON e.id = ANY(STRING_TO_ARRAY(d.sup_dep_ids,','))
			OR d.id = e.id
		  LEFT JOIN t_ryytn_account_role f
			ON a.id = f.account_id
		  LEFT JOIN t_ryytn_role g
			ON f.role_id = g.id
		WHERE f.role_id = #{roleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        <if test="keyword != null and keyword != ''">
            AND (strpos(a.name, #{keyword}) &gt; 0 OR strpos(a.nick_name, #{keyword}) &gt; OR strpos(a.work_code, #{keyword}) &gt; OR strpos(a.login_id,
            #{keyword}) &gt; 0)
        </if>
		GROUP BY a.id,b.department_id,c.id
        ORDER BY a.id
    </select>

    <insert id="addAccountRole" parameterType="java.util.List">
        INSERT INTO t_ryytn_account_role (account_id, role_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
          (
            #{item.accountId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{item.roleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
            )
        </foreach>
    </insert>

    <delete id="deleteAccountRole" parameterType="cn.aliyun.ryytn.modules.system.entity.vo.AccountRoleVo">
        DELETE FROM t_ryytn_account_role
        WHERE role_id = #{roleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        AND account_id IN
        <foreach collection="accountIdList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
        </foreach>
    </delete>

    <insert id="addRolePage" parameterType="java.util.List">
        INSERT INTO t_ryytn_role_page (role_id, page_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.roleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{item.pageId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
            )
        </foreach>
    </insert>

    <delete id="deleteRolePageByRoleId" parameterType="java.lang.String">
        DELETE FROM t_ryytn_role_page WHERE role_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <insert id="addRoleButton" parameterType="java.util.List">
        INSERT INTO t_ryytn_role_button (role_id, button_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.roleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{item.buttonId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
            )
        </foreach>
    </insert>

    <delete id="deleteRoleButtonByRoleId" parameterType="java.lang.String">
        DELETE FROM t_ryytn_role_button WHERE role_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <insert id="addRoleChannel" parameterType="java.util.List">
        INSERT INTO t_ryytn_role_channel (role_id, channel_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.roleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{item.channelId}
            )
        </foreach>
    </insert>

    <delete id="deleteRoleChannelByRoleId" parameterType="java.lang.String">
        DELETE FROM t_ryytn_role_channel WHERE role_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <insert id="addRoleProductCategory" parameterType="java.util.List">
        INSERT INTO t_ryytn_role_productcategory (role_id, category_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.roleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{item.categoryId}
            )
        </foreach>
    </insert>

    <delete id="deleteRoleProductCategoryByRoleId" parameterType="java.lang.String">
        DELETE FROM t_ryytn_role_productcategory WHERE role_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <insert id="addRoleFactory" parameterType="java.util.List">
        INSERT INTO t_ryytn_role_factory (role_id, factory_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.roleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{item.factoryId}
            )
        </foreach>
    </insert>

    <delete id="deleteRoleFactoryByRoleId" parameterType="java.lang.String">
        DELETE FROM t_ryytn_role_factory WHERE role_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <insert id="addRoleDepository" parameterType="java.util.List">
        INSERT INTO t_ryytn_role_depository (role_id, depository_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.roleId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
            #{item.depositoryId}
            )
        </foreach>
    </insert>

    <delete id="deleteRoleDepositoryByRoleId" parameterType="java.lang.String">
        DELETE FROM t_ryytn_role_depository WHERE role_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </delete>

    <select id="queryPageIdListByRoleId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT page_id FROM t_ryytn_role_page WHERE role_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <select id="queryButtonIdListByRoleId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT button_id FROM t_ryytn_role_button WHERE role_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <select id="queryChannelIdListByRoleId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT channel_id FROM t_ryytn_role_channel WHERE role_id =
        #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <select id="queryProductCategoryIdListByRoleId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT category_id FROM t_ryytn_role_productcategory WHERE role_id =
        #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <select id="queryFactoryIdListByRoleId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT factory_id FROM t_ryytn_role_factory WHERE role_id =
        #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>

    <select id="queryDepositoryIdListByRoleId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT depository_id FROM t_ryytn_role_depository WHERE role_id =
        #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
    </select>
</mapper>