<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dao.ConfigDao">
	<select id="querySystemConfigCategoryList" resultType="cn.aliyun.ryytn.common.entity.SystemConfigCategory">
		SELECT 
		  category_id,
		  parent_id,
		  parent_ids,
		  name,
		  status,
		  sort_no
		FROM
		  t_ryytn_configcategory
	   WHERE status = 1
	ORDER BY sort_no
	</select>
	<select id="querySystemConfigList" parameterType="cn.aliyun.ryytn.common.entity.SystemConfig"
            resultType="cn.aliyun.ryytn.common.entity.SystemConfig">
		SELECT 
		  category_id,
		  config_id,
		  config_name,
		  config_type,
		  config_value,
		  status,
		  is_display,
		  validator,
		  description,
		  sort_no
		FROM
		  t_ryytn_config
	   WHERE status = 1
	     <if test="isDisplay!=null and isDisplay!=''">
	     	AND is_display=#{isDisplay}
	     </if>
	     <if test="categoryId!=null and categoryId!=''">
	     	AND category_id=#{categoryId}
	     </if>
	     <if test="configId!=null and configId!=''">
	     	AND strpos(config_id ,#{configId}) &gt; 0
	     </if>
	     <if test="configName!=null and configName!=''">
	     	AND strpos(config_name ,#{configName}) &gt; 0
	     </if>
	ORDER BY sort_no
	</select>
	<select id="querySystemConfigDetail" parameterType="cn.aliyun.ryytn.common.entity.SystemConfig"
            resultType="cn.aliyun.ryytn.common.entity.SystemConfig">
		SELECT 
		  category_id,
		  config_id,
		  config_name,
		  config_type,
		  config_value,
		  status,
		  is_display,
		  validator,
		  description,
		  sort_no,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_config
		WHERE config_id = #{configId}
	</select>
	<update id="updateSystemConfig" parameterType="cn.aliyun.ryytn.common.entity.SystemConfig">
		UPDATE 
		  t_ryytn_config
		SET
		  config_value = #{configValue},
		  updated_by = #{updatedBy},
		  updated_time = #{updatedTime}
		WHERE config_id = #{configId}
	</update>
	<update id="batchUpdateSystemConfig" parameterType="java.util.List">
		<foreach collection="list" item="item" open="" close="" separator=";">
            UPDATE t_ryytn_config
            SET
				config_value = #{item.configValue},
				updated_by = #{item.updatedBy},
				updated_time = #{item.updatedTime}
			WHERE config_id=#{item.configId}
        </foreach>
	</update>
	<select id="queryAllSystemConfigList" resultType="cn.aliyun.ryytn.common.entity.SystemConfig">
		SELECT 
		  config_id,
		  config_value
		FROM
		  t_ryytn_config
	</select>

	<select id="querySystemConfigDetailList" parameterType="cn.aliyun.ryytn.common.entity.SystemConfig"
            resultType="cn.aliyun.ryytn.common.entity.SystemConfig">
		SELECT 
		  category_id,
		  config_id,
		  config_name,
		  config_type,
		  config_value,
		  status,
		  is_display,
		  validator,
		  description,
		  sort_no,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_config
		WHERE config_id IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item.configId}
		</foreach>
	</select>
</mapper>