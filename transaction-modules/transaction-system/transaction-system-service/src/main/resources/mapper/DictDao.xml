<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dao.DictDao">
	<select id="queryDictTypeList" parameterType="cn.aliyun.ryytn.common.entity.DictType" resultType="cn.aliyun.ryytn.common.entity.DictType">
		SELECT
		  dict_type_id,
		  dict_type,
		  dict_name,
		  status,
		  delete_flag,
		  description,
		  data_type,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_dict_type
	   WHERE delete_flag=0
	     <if test="dictType!=null and dictType!=''">
	     	AND strpos(dict_type ,#{dictType}) &gt; 0
	     </if>
	     <if test="dictName!=null and dictName!=''">
	     	AND strpos(dict_name ,#{dictName}) &gt; 0
	     </if>
	     <if test="status!=null and status!=''">
	     	AND status = #{status}
	     </if>
	ORDER BY dict_name
	</select>

	<select id="queryDictTypeDetail" parameterType="cn.aliyun.ryytn.common.entity.DictType" resultType="cn.aliyun.ryytn.common.entity.DictType">
		SELECT
		  dict_type_id,
		  dict_type,
		  dict_name,
		  status,
		  delete_flag,
		  description,
		  data_type,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_dict_type
	   WHERE dict_type_id = #{dictTypeId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>

	<select id="queryDictTypeByDictType" parameterType="java.lang.String" resultType="cn.aliyun.ryytn.common.entity.DictType">
		SELECT
		  dict_type_id,
		  dict_type,
		  dict_name,
		  status,
		  delete_flag,
		  description,
		  data_type,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_dict_type
	   WHERE dict_type = #{dictType}
	</select>

	<select id="checkDictTypeUnique" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM t_ryytn_dict_type
		WHERE delete_flag=0
		  AND dict_type=#{dictType}
	</select>

	<insert id="addDictType" parameterType="cn.aliyun.ryytn.common.entity.DictType">
		INSERT INTO t_ryytn_dict_type (
		  dict_type,
		  dict_name,
		  status,
		  delete_flag,
		  description,
		  data_type,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		)
		VALUES
		  (
		    #{dictType},
		    #{dictName},
		    #{status},
		    #{deleteFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler},
		    #{description},
		    #{dataType},
		    #{createdBy},
		    #{createdTime},
		    #{updatedBy},
		    #{updatedTime}
		  )
	</insert>

	<update id="recoveryDictType" parameterType="cn.aliyun.ryytn.common.entity.DictType">
		UPDATE
		  t_ryytn_dict_type
		SET
		  dict_name = #{dictName},
		  delete_flag = #{deleteFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler},
		  description = #{description},
		  updated_by = #{updatedBy},
		  updated_time = #{updatedTime}
		WHERE dict_type_id = #{dictTypeId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<update id="updateDictType" parameterType="cn.aliyun.ryytn.common.entity.DictType">
		UPDATE
		  t_ryytn_dict_type
		SET
		  dict_name = #{dictName},
		  <if test="status!=null and status!=''">
		  	status = #{status},
		  </if>
		  description = #{description},
		  updated_by = #{updatedBy},
		  updated_time = #{updatedTime}
		WHERE dict_type_id = #{dictTypeId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<select id="queryInitDictTypeCount" parameterType="java.util.List" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM t_ryytn_dict_type
		WHERE data_type = 0
		  AND dict_type_id IN
		  <foreach collection="list" item="item" open="(" close=")" separator=",">
		  	#{item}
		  </foreach>
	</select>

	 <update id="deleteDictType">
		UPDATE t_ryytn_dict_type
		   SET delete_flag = 1,
		   updated_by = #{param1},
		   updated_time = NOW()
		 WHERE dict_type_id IN
		 <foreach collection="param2" item="item" open="(" close=")" separator=",">
		 	#{item}
		 </foreach>
	</update>

	<select id="queryAllDictDataList" resultType="cn.aliyun.ryytn.common.entity.DictData">
		SELECT
		  dict_id,
		  dict_type,
		  name,
		  code,
		  parent_id,
		  parent_ids,
		  level,
		  leaf_flag,
		  css_class,
		  list_class,
		  item_check,
		  sort_no,
		  status,
		  delete_flag,
		  description,
		  data_type,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_dict_data
	ORDER BY dict_type ASC,sort_no ASC,code ASC
	</select>

	<select id="queryDictDataList" parameterType="cn.aliyun.ryytn.common.entity.DictData" resultType="cn.aliyun.ryytn.common.entity.DictData">
		SELECT
		  dict_id,
		  dict_type,
		  name,
		  code,
		  parent_id,
		  parent_ids,
		  level,
		  leaf_flag,
		  css_class,
		  list_class,
		  item_check,
		  sort_no,
		  status,
		  delete_flag,
		  description,
		  data_type,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_dict_data
	   WHERE 1=1
	   <if test="dictType!=null and dictType!=''">
	   		AND dict_type = #{dictType}
	   </if>
	   <if test="code!=null and code!=''">
	   		AND code = #{code}
	   </if>
	   <if test="name!=null and name!=''">
	   		AND strpos(name ,#{name}) &gt; 0
	   </if>
	   <if test="parentId != null and parentId != ''">
	   		AND parent_id = #{parentId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	   </if>
	   AND delete_flag=0
	   ORDER BY sort_no
	</select>

	<select id="queryDictDataDetail" parameterType="java.lang.String" resultType="cn.aliyun.ryytn.common.entity.DictData">
		SELECT
		  dict_id,
		  dict_type,
		  name,
		  code,
		  parent_id,
		  parent_ids,
		  level,
		  leaf_flag,
		  css_class,
		  list_class,
		  item_check,
		  sort_no,
		  status,
		  delete_flag,
		  description,
		  data_type,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_dict_data
	   WHERE dict_id = #{dictId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>

	<select id="queryDictTypeExists" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM t_ryytn_dict_type
		WHERE dict_type=#{dictType}
	</select>

	<select id="queryDictDataByDictData" parameterType="cn.aliyun.ryytn.common.entity.DictData" resultType="cn.aliyun.ryytn.common.entity.DictData">
		SELECT
		  dict_id,
		  dict_type,
		  name,
		  code,
		  parent_id,
		  parent_ids,
		  level,
		  leaf_flag,
		  css_class,
		  list_class,
		  item_check,
		  sort_no,
		  status,
		  delete_flag,
		  description,
		  data_type,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_dict_data
	   WHERE dict_type = #{dictType}
	     AND code = #{code}
	</select>

	<select id="queryParentDictData" parameterType="java.lang.String" resultType="cn.aliyun.ryytn.common.entity.DictData">
		SELECT
		  dict_id,
		  dict_type,
		  name,
		  code,
		  parent_id,
		  parent_ids,
		  level,
		  leaf_flag,
		  css_class,
		  list_class,
		  item_check,
		  sort_no,
		  status,
		  delete_flag,
		  description,
		  data_type,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		FROM
		  t_ryytn_dict_data
	   WHERE parent_id = #{parentId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>

	<update id="recoveryDictData" parameterType="cn.aliyun.ryytn.common.entity.DictData">
		UPDATE
		  t_ryytn_dict_data
		SET
		  name = #{name},
		  css_class = #{cssClass},
		  list_class = #{listClass},
		  item_check = #{itemCheck},
		  sort_no = #{sortNo},
		  delete_flag = #{deleteFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler},
		  description = #{description},
		  updated_by = #{updatedBy},
		  updated_time = #{updatedTime}
		WHERE dict_id = #{dictId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<insert id="addDictData" parameterType="cn.aliyun.ryytn.common.entity.DictData">
		INSERT INTO t_ryytn_dict_data (
		  dict_type,
		  name,
		  code,
		  parent_id,
		  parent_ids,
		  level,
		  leaf_flag,
		  css_class,
		  list_class,
		  item_check,
		  sort_no,
		  status,
		  delete_flag,
		  description,
		  data_type,
		  created_by,
		  created_time,
		  updated_by,
		  updated_time
		)
		VALUES
		  (
		    #{dictType},
		    #{name},
		    #{code},
		    #{parentId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
		    #{parentIds},
		    #{level},
		    #{leafFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler},
		    #{cssClass},
		    #{listClass},
		    #{itemCheck},
		    #{sortNo},
		    #{status},
		    #{deleteFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler},
		    #{description},
		    #{dataType},
		    #{createdBy},
		    #{createdTime},
		    #{updatedBy},
		    #{updatedTime}
		  )
	</insert>

	<update id="updateDictData" parameterType="cn.aliyun.ryytn.common.entity.DictData">
		UPDATE
		  t_ryytn_dict_data
		SET
		  name = #{name},
		  css_class = #{cssClass},
		  list_class = #{listClass},
		  item_check = #{itemCheck},
		  sort_no = #{sortNo},
		  status = #{status},
		  description = #{description},
		  updated_by = #{updatedBy},
		  updated_time = #{updatedTime}
		WHERE dict_id = #{dictId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<select id="queryInitDictDataCount" parameterType="java.util.List" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM t_ryytn_dict_data
		WHERE data_type=0
		  AND dict_id IN
		  <foreach collection="list" item="item" open="(" close=")" separator=",">
		  	#{item}
		  </foreach>
	</select>

	<update id="deleteDictData">
		UPDATE t_ryytn_dict_data
		   SET delete_flag=1,
		   updated_by = #{param1},
		   updated_time = NOW()
		 WHERE dict_id IN
		 <foreach collection="param2" item="item" open="(" close=")" separator=",">
		 	#{item}
		 </foreach>
	</update>

	<select id="checkDictDataUnique" parameterType="cn.aliyun.ryytn.common.entity.DictData" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM t_ryytn_dict_data
		WHERE delete_flag=0
		  AND dict_type = #{dictType}
		  AND code = #{code}
	</select>

	<delete id="deleteDictDataByDictTypeCode" parameterType="cn.aliyun.ryytn.common.entity.DictData">
		DELETE FROM t_ryytn_dict_data
		WHERE delete_flag=1
		  AND dict_type = #{dictType}
		  AND code = #{code}
	</delete>

	<update id="updateDictDataLeafFlag" parameterType="cn.aliyun.ryytn.common.entity.DictData">
		UPDATE t_ryytn_dict_data
		   SET leaf_flag = #{leafFlag,jdbcType=SMALLINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.BooleanIntTypeHandler}
		 WHERE dict_id = #{dictId,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>
</mapper>