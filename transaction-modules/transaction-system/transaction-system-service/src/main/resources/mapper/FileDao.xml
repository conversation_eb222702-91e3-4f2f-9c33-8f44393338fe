<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dao.FileDao">
	<insert id="addServiceFile" parameterType="cn.aliyun.ryytn.common.entity.ServiceFile">
		INSERT INTO t_ryytn_file (
		  file_id,
		  file_type,
		  suffix,
		  oFile_name,
		  created_by
		)
		VALUES
		  (
		    #{fileId},
		    #{fileType},
		    #{suffix},
		    #{oFileName},
		    #{createdBy}
		  )
	</insert>

	<insert id="addServiceFileList" parameterType="java.util.List">
		INSERT INTO t_ryytn_file (
		  file_id,
		  file_type,
		  suffix,
		  oFile_name,
		  created_by
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
		    #{item.fileId},
		    #{item.fileType},
		    #{item.suffix},
		    #{item.oFileName},
		    #{item.createdBy}
		  )
		</foreach>
	</insert>

	<select id="queryServiceFile" parameterType="java.lang.String" resultType="cn.aliyun.ryytn.common.entity.ServiceFile">
		SELECT
		  file_id,
		  file_type,
		  suffix,
		  oFile_name,
		  created_by,
		  created_time
		FROM
		  t_ryytn_file
	   WHERE file_id = #{fileId}
	</select>

	<select id="queryExpireServiceFile" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT
		  a.file_id
		FROM
		  t_ryytn_file a
		WHERE NOT EXISTS (
		    SELECT 'X' FROM t_ryytn_file_ref b WHERE a.file_id = b.file_id
		  )
		  AND a.created_time &lt; #{expireTime}
	</select>

	<select id="queryServiceRefExists" parameterType="java.util.List" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM t_ryytn_file_ref WHERE file_id IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>

	<delete id="deleteServiceFile" parameterType="java.util.List">
		DELETE
		FROM
		  t_ryytn_file
		WHERE file_id IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</delete>

	<insert id="addServiceFileRef" parameterType="cn.aliyun.ryytn.common.entity.ServiceFileRef">
		INSERT INTO t_ryytn_file_ref (
		  file_id,
		  service_id,
		  service_type
		)
		VALUES
		  (
		    #{fileId},
		    #{serviceId},
		    #{serviceType}
		  )
	</insert>

	<insert id="addServiceFileRefList" parameterType="cn.aliyun.ryytn.common.entity.ServiceFileRef">
		INSERT INTO t_ryytn_file_ref (
		  file_id,
		  service_id,
		  service_type
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
		    #{item.fileId},
		    #{item.serviceId},
		    #{item.serviceType}
		  )
		</foreach>
	</insert>

	<delete id="deleteFileRefByService">
		DELETE FROM t_ryytn_file_ref WHERE service_id = #{param1} AND service_type = #{param2}
	</delete>

	<delete id="deleteFileRefByServiceList">
		DELETE FROM t_ryytn_file_ref
		 WHERE service_id IN
		 <foreach collection="param1" item="item" open="(" close=")" separator=",">
		 	#{item}
		 </foreach>
		 AND service_type = #{param2}
	</delete>

	<update id="updateServiceFileRef" parameterType="cn.aliyun.ryytn.common.entity.ServiceFileRef">
		UPDATE t_ryytn_file_ref SET file_id = #{fileId} WHERE service_id = #{serviceId} AND service_type = #{serviceType}
	</update>
</mapper>