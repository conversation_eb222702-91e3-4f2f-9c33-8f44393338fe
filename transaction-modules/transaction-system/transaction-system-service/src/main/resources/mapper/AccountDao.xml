<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dao.AccountDao">
	<insert id="batchAddAccount" parameterType="java.util.List">
		INSERT INTO t_ryytn_account (
		  id,
		  name,
		  nick_name,
		  work_code,
		  login_id,
		  password,
		  oa_id,
		  status,
		  description,
		  data_type,
		  created_time,
		  updated_time,
		  department_id
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
		  (
			#{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{item.name},
			#{item.nickName},
			#{item.workCode},
			#{item.loginId},
			#{item.password},
			#{item.oaId},
			#{item.status},
			#{item.description},
			#{item.dataType},
			#{item.createdTime},
			#{item.updatedTime},
			#{item.departmentId}
		  )
		</foreach>
	</insert>

	<update id="batchUpdateAccount" parameterType="java.util.List">
		<foreach collection="list" item="item" separator=";">
			UPDATE
			  t_ryytn_account
			SET
			  name = #{item.name},
			  nick_name = #{item.nickName},
			  work_code = #{item.workCode},
			  login_id = #{item.loginId},
			  password = #{item.password},
			  description = #{item.description},
			  updated_time = #{item.updatedTime},
			  department_id = #{item.departmentId}
			WHERE id = #{item.id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</foreach>
	</update>

	<resultMap id="accountResultMap" type="cn.aliyun.ryytn.common.entity.Account">
		<id column="id" property="id"/>
		<result column="name" property="name"/>
		<result column="nick_name" property="nickName"/>
		<result column="work_code" property="workCode"/>
		<result column="login_id" property="loginId"/>
		<result column="oa_id" property="oaId"/>
		<result column="department_id" property="departmentId"/>
		<result column="departmentName" property="departmentName"/>
		<result column="jobTitleId" property="jobTitleId"/>
		<result column="jobTitleName" property="jobTitleName"/>
		<result column="roleIds" property="roleIds"/>
		<result column="roleNames" property="roleNames"/>
		<result column="status" property="status"/>
		<result column="description" property="description"/>
		<result column="data_type" property="dataType"/>
		<result column="created_time" property="createdTime"/>
		<result column="updated_time" property="updatedTime"/>
	</resultMap>
	<select id="queryAccountList" parameterType="cn.aliyun.ryytn.common.entity.Account" resultMap="accountResultMap">
		SELECT
		  a.id,
		  a.name,
		  a.nick_name,
		  a.work_code,
		  a.login_id,
		  a.oa_id,
		  b.department_id,
		  array_to_string(array_agg(e.department_name order by e.level),'/') AS departmentName,
		  c.id AS jobTitleId,
		  c.job_title_mark AS jobTitleName,
		  array_to_string(array_agg(distinct g.id),',') as roleIds,
		  array_to_string(array_agg(distinct g.name order by g.name),',') AS roleNames,
		  a.status,
		  a.description,
		  a.data_type,
		  a.created_time,
		  a.updated_time
		FROM
		  t_ryytn_account a
		  LEFT JOIN t_ryytn_oa_person b
			ON a.oa_id = b.id
		  LEFT JOIN t_ryytn_oa_jobtitle c
			ON b.job_title_id = c.id
		  LEFT JOIN t_ryytn_oa_department d
			ON a.department_id = d.id
		  LEFT JOIN t_ryytn_oa_department e
			ON e.id = ANY(STRING_TO_ARRAY(d.sup_dep_ids,','))
			OR d.id = e.id
		  LEFT JOIN t_ryytn_account_role f
			ON a.id = f.account_id
		  LEFT JOIN t_ryytn_role g
			ON f.role_id = g.id
		WHERE 1=1
		AND (b.status is null OR b.status IN (0,1,2,3))
		<if test="name != null and name != ''">
			AND strpos(a.name,#{name}) &gt; 0
		</if>
		<if test="nickName != null and nickName != ''">
			AND strpos(a.nick_name,#{nickName}) &gt; 0
		</if>
		<if test="workCode != null">
			AND strpos(a.work_code,#{workCode}) &gt; 0
		</if>
		<if test="loginId != null and loginId != ''">
			AND strpos(a.login_id,#{loginId}) &gt; 0
		</if>
		<if test="status != null">
			AND a.status = #{status}
		</if>
		<if test="dataType != null">
			AND a.data_type = #{dateType}
		</if>
		<if test="departmentId != null and departmentId != ''">
			AND (#{departmentId} = ANY(STRING_TO_ARRAY(d.sup_dep_ids,',')) OR d.id = #{departmentId})
		</if>
		GROUP BY a.id,b.id,c.id
		ORDER BY a.id
	</select>

	<select id="queryAccountById" parameterType="java.lang.String" resultMap="accountResultMap">
		SELECT
		  a.id,
		  a.name,
		  a.nick_name,
		  a.work_code,
		  a.login_id,
		  a.oa_id,
		  b.department_id,
		  array_to_string(array_agg(e.department_name order by e.level),'/') AS departmentName,
		  c.id AS jobTitleId,
		  c.job_title_mark AS jobTitleName,
		  array_to_string(array_agg(g.id),',') as roleIds,
		  array_to_string(array_agg(g.name),',') AS roleNames,
		  a.status,
		  a.description,
		  a.data_type,
		  a.created_time,
		  a.updated_time
		FROM
		  t_ryytn_account a
		  LEFT JOIN t_ryytn_oa_person b
			ON a.oa_id = b.id
		  LEFT JOIN t_ryytn_oa_jobtitle c
			ON b.job_title_id = c.id
		  LEFT JOIN t_ryytn_oa_department d
			ON a.department_id = d.id
		  LEFT JOIN t_ryytn_oa_department e
			ON e.id = ANY(STRING_TO_ARRAY(d.sup_dep_ids,','))
			OR d.id = e.id
		  LEFT JOIN t_ryytn_account_role f
			ON a.id = f.account_id
		  LEFT JOIN t_ryytn_role g
			ON f.role_id = g.id
		WHERE a.id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		GROUP BY a.id,b.id,c.id
	</select>

	<select id="queryLoginIdRepeat" parameterType="cn.aliyun.ryytn.common.entity.Account" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM t_ryytn_account
		WHERE login_id = #{loginId}
		  AND id &lt;&gt; #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>

	<insert id="addAccount" parameterType="cn.aliyun.ryytn.common.entity.Account">
		INSERT INTO t_ryytn_account (
		  id,
		  name,
		  nick_name,
		  work_code,
		  login_id,
		  password,
		  oa_id,
		  status,
		  description,
		  data_type,
		  department_id
		)
		VALUES
		  (
			#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler},
			#{name},
		    #{nickName},
			#{workCode},
			#{loginId},
			#{password},
			#{oaId},
			#{status},
			#{description},
			#{dataType},
		    #{departmentId}
		  )
	</insert>

	<update id="updateAccount" parameterType="cn.aliyun.ryytn.common.entity.Account">
		UPDATE
		  t_ryytn_account
		SET
		  name = #{name},
		  nick_name = #{nickName},
		  work_code = #{workCode},
		  description = #{description}
		WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<update id="updateAccountStatus" parameterType="cn.aliyun.ryytn.common.entity.Account">
		UPDATE
		  t_ryytn_account
		SET
		  status = #{status}
		WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<update id="updateAccountPassword" parameterType="cn.aliyun.ryytn.common.entity.Account">
		UPDATE
		  t_ryytn_account
		SET
		  password = #{newPassword}
		WHERE login_id = #{loginId}
	</update>

	<update id="resetAccountPassword">
		UPDATE
		  t_ryytn_account
		SET
		  password = #{password}
		WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</update>

	<delete id="deleteAccount" parameterType="java.lang.String">
		DELETE FROM t_ryytn_account WHERE id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>

	<delete id="deleteAccountRole" parameterType="java.lang.String">
		DELETE FROM t_ryytn_account_role WHERE account_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</delete>
</mapper>