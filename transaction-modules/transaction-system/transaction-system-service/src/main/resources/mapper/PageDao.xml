<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dao.PageDao">
    <resultMap type="cn.aliyun.ryytn.common.entity.Page" id="queryAllPageListResult">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="alias" property="alias"/>
        <result column="permission" property="permission"/>
        <result column="parent_id" property="parentId"/>
        <result column="parent_ids" property="parentIds"/>
        <result column="dependency_ids" property="dependencyIds"/>
        <result column="type" property="type"/>
        <result column="path" property="path"/>
        <result column="config_path" property="configPath"/>
        <result column="component" property="component"/>
        <result column="icon" property="icon"/>
        <result column="moudel_id" property="moudelId"/>
        <result column="sort_no" property="sortNo"/>
        <result column="sum_flag" property="sumFlag"/>
        <result column="description" property="description"/>
        <collection property="subButtonList" ofType="cn.aliyun.ryytn.common.entity.Button">
            <id column="buttonId" property="id"/>
            <result column="buttonName" property="name"/>
            <result column="adpTitle" property="title"/>
            <result column="buttonAlias" property="alias"/>
            <result column="buttonPermission" property="permission"/>
            <result column="buttonDependencyIds" property="dependencyIds"/>
            <result column="buttonPageId" property="pageId"/>
            <result column="buttonSortNo" property="sortNo"/>
        </collection>
    </resultMap>
	<select id="queryAllPageList" parameterType="cn.aliyun.ryytn.common.entity.Page" resultMap="queryAllPageListResult">
		SELECT DISTINCT
		  page.id,
		  page.name,
		  page.alias,
		  page.permission,
		  page.parent_id,
		  page.parent_ids,
		  page.dependency_ids,
		  page.type,
		  page.path,
		  page.config_path,
		  page.component,
		  page.icon,
		  page.moudel_id,
		  page.sort_no,
		  page.sum_flag,
		  page.description,
		  button.id AS buttonId,
		  button.name AS buttonName,
		  button.alias AS buttonAlias,
		  button.permission AS buttonPermission,
		  button.dependency_ids AS buttonDependencyIds,
		  button.page_id AS buttonPageId,
		  button.sort_no AS buttonSortNo
		FROM
		  t_ryytn_page page
		  LEFT JOIN t_ryytn_button button
			ON page.id = button.page_id
		  LEFT JOIN t_ryytn_page subPage
		    ON cast(page.id as VARCHAR) = any(string_to_array(subPage.parent_ids,','))
		WHERE 1 = 1
		  AND page.type = '1'
		<if test="name != null and name != ''">
			AND (strpos(page.name, #{name}) &gt; 0 OR strpos(subPage.name, #{name}) &gt; 0)
		</if>
		ORDER BY page.sort_no,button.sort_no
	</select>
</mapper>