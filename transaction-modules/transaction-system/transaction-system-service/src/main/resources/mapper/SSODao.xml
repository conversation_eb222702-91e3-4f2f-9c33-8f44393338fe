<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dao.SSODao">
	<select id="queryThirdpartSystem" resultType="cn.aliyun.ryytn.modules.system.entity.dto.ThirdpartSystem">
		SELECT
		  id,
		  name,
		  auth_code,
		  url,
		  status,
		  description
		FROM
		  t_ryytn_thirdparty_system
		WHERE name = #{name}
		  AND auth_code = #{authCode}
		  AND status =1
	</select>
</mapper>