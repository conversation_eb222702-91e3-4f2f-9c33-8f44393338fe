<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dao.OADao">
	<insert id="addOASubCompany" parameterType="java.util.List">
		<foreach collection="list" item="item" separator=";">
		INSERT INTO t_ryytn_oa_subcompany (
		  id,
		  sub_company_name,
		  sub_company_desc,
		  sub_company_code,
		  sup_sub_com_id,
		  sup_sub_com_ids,
		  level,
		  canceled,
		  sort_no,
		  created_time,
		  updated_time,
		  sync_time
		)
		VALUES
		  (
			#{item.id},
			#{item.subCompanyName},
			#{item.subCompanyDesc},
			#{item.subCompanyCode},
			#{item.supSubComId},
			#{item.supSubComIds},
			#{item.level},
			#{item.canceled},
			#{item.sortNo},
			#{item.createdTime},
			#{item.updatedTime},
			#{item.syncTime}
		  )
		ON CONFLICT (id) DO UPDATE
		SET sub_company_name = excluded.sub_company_name,
			sub_company_desc = excluded.sub_company_desc,
			sub_company_code = excluded.sub_company_code,
		  	sup_sub_com_id = excluded.sup_sub_com_id,
		  	sup_sub_com_ids = excluded.sup_sub_com_ids,
		  	level = excluded.level,
		  	canceled = excluded.canceled,
		  	sort_no = excluded.sort_no,
		  	created_time = excluded.created_time,
		  	updated_time = excluded.updated_time,
		  	sync_time = excluded.sync_time
		</foreach>
	</insert>

	<delete id="deleteOASubCompanyExpire" parameterType="java.lang.Long">
		DELETE FROM t_ryytn_oa_subcompany WHERE sync_time &lt; #{expireSyncTime}
	</delete>

	<insert id="addOADepartment" parameterType="java.util.List">
		<foreach collection="list" item="item" separator=";">
		INSERT INTO t_ryytn_oa_department (
		  id,
		  department_mark,
		  department_name,
		  department_code,
		  sub_company_id,
		  sup_dep_id,
		  sup_dep_ids,
		  level,
		  canceled,
		  sort_no,
		  created_time,
		  updated_time,
		  sync_time
		)
		VALUES
		  (
			#{item.id},
			#{item.departmentMark},
			#{item.departmentName},
			#{item.departmentCode},
			#{item.subCompanyId},
			#{item.supDepId},
			#{item.supDepIds},
			#{item.level},
			#{item.canceled},
			#{item.sortNo},
			#{item.createdTime},
			#{item.updatedTime},
			#{item.syncTime}
		  )
		ON CONFLICT (id) DO UPDATE
		SET department_mark = excluded.department_mark,
		  	department_name = excluded.department_name,
		  	department_code = excluded.department_code,
		  	sub_company_id = excluded.sub_company_id,
		  	sup_dep_id = excluded.sup_dep_id,
		  	sup_dep_ids = excluded.sup_dep_ids,
		  	level = excluded.level,
		  	canceled = excluded.canceled,
		  	sort_no = excluded.sort_no,
		  	created_time = excluded.created_time,
		  	updated_time = excluded.updated_time,
		  	sync_time = excluded.sync_time
		</foreach>
	</insert>

	<delete id="deleteOADepartmentExpire" parameterType="java.lang.Long">
		DELETE FROM t_ryytn_oa_department WHERE level &lt;&gt; -1 and sync_time &lt; #{expireSyncTime}
	</delete>

	<insert id="addOAJobTitle" parameterType="java.util.List">
		<foreach collection="list" item="item" separator=";">
		INSERT INTO t_ryytn_oa_jobtitle (
		  id,
		  job_title_mark,
		  job_title_name,
		  job_doc,
		  job_department_id,
		  job_responsibility,
		  job_competency,
		  job_title_remark,
		  created_time,
		  updated_time,
		  sync_time
		)
		VALUES
		  (
			#{item.id},
			#{item.jobTitleMark},
			#{item.jobTitleName},
			#{item.jobDoc},
            #{item.jobDepartmentId},
            #{item.jobResponsibility},
			#{item.jobCompetency},
			#{item.jobTitleRemark},
			#{item.createdTime},
			#{item.updatedTime},
			#{item.syncTime}
		  )
		ON CONFLICT (id) DO UPDATE
		SET job_title_mark = excluded.job_title_mark,
		  	job_title_name = excluded.job_title_name,
		  	job_doc = excluded.job_doc,
		  	job_department_id = excluded.job_department_id,
		  	job_responsibility = excluded.job_responsibility,
		  	job_competency = excluded.job_competency,
		  	job_title_remark = excluded.job_title_remark,
		  	created_time = excluded.created_time,
		  	updated_time = excluded.updated_time,
		  	sync_time = excluded.sync_time
		</foreach>
	</insert>

	<delete id="deleteOAJobTitleExpire" parameterType="java.lang.Long">
		DELETE FROM t_ryytn_oa_jobtitle WHERE sync_time &lt; #{expireSyncTime}
	</delete>

	<insert id="addOAPerson" parameterType="java.util.List">
		<foreach collection="list" item="item" separator=";">
		INSERT INTO t_ryytn_oa_person (
		  id,
		  work_code,
		  last_name,
		  login_id,
		  account_type,
		  be_long_to,
		  department_id,
		  job_title_id,
		  location_id,
		  status,
		  language,
		  job_activity_desc,
		  job_level,
		  job_call,
		  manager_id,
		  assistant_id,
		  sex,
		  telephone,
		  mobile,
		  mobile_call,
		  email,
		  start_date,
		  end_date,
		  sec_level,
		  password,
		  certificate_num,
		  birthday,
		  height,
		  weight,
		  folk,
		  native_place,
		  health_info,
		  marital_status,
		  temp_resident_number,
		  resident_place,
		  regresident_place,
		  home_address,
		  policy,
		  be_member_date,
		  be_party_date,
		  degree,
		  education_level,
		  is_labouunion,
		  last_mod_date,
		  sort_no,
		  created_time,
		  updated_time,
		  sync_time
		)
		VALUES
		  (
			#{item.id},
			#{item.workCode},
			#{item.lastName},
			#{item.loginId},
			#{item.accountType},
			#{item.beLongTo},
			#{item.departmentId},
			#{item.jobTitleId},
			#{item.locationId},
			#{item.status},
			#{item.language},
			#{item.jobActivityDesc},
			#{item.jobLevel},
			#{item.jobCall},
			#{item.managerId},
			#{item.assistantId},
			#{item.sex},
			#{item.telephone},
			#{item.mobile},
			#{item.mobileCall},
			#{item.email},
			#{item.startDate},
			#{item.endDate},
			#{item.secLevel},
			#{item.password},
			#{item.certificateNum},
			#{item.birthday},
			#{item.height},
			#{item.weight},
			#{item.folk},
			#{item.nativePlace},
			#{item.healthInfo},
			#{item.maritalStatus},
			#{item.tempResidentNumber},
			#{item.residentPlace},
			#{item.regresidentPlace},
			#{item.homeAddress},
			#{item.policy},
			#{item.beMemberDate},
			#{item.bePartyDate},
			#{item.degree},
			#{item.educationLevel},
			#{item.isLabouunion},
			#{item.lastModDate},
			#{item.sortNo},
			#{item.createdTime},
			#{item.updatedTime},
			#{item.syncTime}
		  )
		ON CONFLICT (id) DO UPDATE
		SET work_code = excluded.work_code,
		  last_name = excluded.last_name,
		  login_id = excluded.login_id,
		  account_type = excluded.account_type,
		  be_long_to = excluded.be_long_to,
		  department_id = excluded.department_id,
		  job_title_id = excluded.job_title_id,
		  location_id = excluded.location_id,
		  status = excluded.status,
		  language = excluded.language,
		  job_activity_desc = excluded.job_activity_desc,
		  job_level = excluded.job_level,
		  job_call = excluded.job_call,
		  manager_id = excluded.manager_id,
		  assistant_id = excluded.assistant_id,
		  sex = excluded.sex,
		  telephone = excluded.telephone,
		  mobile = excluded.mobile,
		  mobile_call = excluded.mobile_call,
		  email = excluded.email,
		  start_date = excluded.start_date,
		  end_date = excluded.end_date,
		  sec_level = excluded.sec_level,
		  password = excluded.password,
		  certificate_num = excluded.certificate_num,
		  birthday = excluded.birthday,
		  height = excluded.height,
		  weight = excluded.weight,
		  folk = excluded.folk,
		  native_place = excluded.native_place,
		  health_info = excluded.health_info,
		  marital_status = excluded.marital_status,
		  temp_resident_number = excluded.temp_resident_number,
		  resident_place = excluded.resident_place,
		  regresident_place = excluded.regresident_place,
		  home_address = excluded.home_address,
		  policy = excluded.policy,
		  be_member_date = excluded.be_member_date,
		  be_party_date = excluded.be_party_date,
		  degree = excluded.degree,
		  education_level = excluded.education_level,
		  is_labouunion = excluded.is_labouunion,
		  last_mod_date = excluded.last_mod_date,
		  sort_no = excluded.sort_no,
		  created_time = excluded.created_time,
		  updated_time = excluded.updated_time,
		  sync_time = excluded.sync_time
		</foreach>
	</insert>

	<delete id="deleteAccountExpire" parameterType="java.lang.Long">
		DELETE FROM t_ryytn_account WHERE oa_id IN (SELECT id FROM t_ryytn_oa_person WHERE sync_time &lt; #{expireSyncTime})
	</delete>

	<delete id="deleteOAPersonExpire" parameterType="java.lang.Long">
		DELETE FROM t_ryytn_oa_person WHERE sync_time &lt; #{expireSyncTime}
	</delete>

	<select id="queryAddAccountList" parameterType="java.util.List" resultType="cn.aliyun.ryytn.common.entity.Account">
		SELECT
		  b.id,
		  a.last_name AS name,
		  a.last_name AS nickName,
		  a.work_code,
		  a.login_id,
		  a.password,
		  a.id AS oaId,
		  a.created_time,
		  a.updated_time,
		  a.department_id
		FROM
		  t_ryytn_oa_person a
		LEFT JOIN t_ryytn_account b
		  ON a.id=b.oa_id
		WHERE b.id IS NULL
		  AND a.id IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item.id}
		</foreach>
	</select>

	<select id="queryUpdateAccountList" parameterType="java.util.List" resultType="cn.aliyun.ryytn.common.entity.Account">
		SELECT
		  b.id,
		  a.last_name AS name,
		  a.last_name AS nickName,
		  a.work_code,
		  a.login_id,
		  a.password,
		  a.id AS oaId,
		  a.created_time,
		  a.updated_time,
		  a.department_id
		FROM
		  t_ryytn_oa_person a
		LEFT JOIN t_ryytn_account b
		  ON a.id=b.oa_id
		WHERE b.id IS NOT NULL
		  AND a.id IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item.id}
		</foreach>
	</select>

	<select id="queryAllOADepartmentList" resultType="cn.aliyun.ryytn.common.entity.OADepartment">
		SELECT
		  id,
		  department_mark,
		  department_name,
		  department_code,
		  sub_company_id,
		  sup_dep_id,
		  canceled,
		  sort_no,
		  created_time,
		  updated_time,
		  sync_time
		FROM
		  t_ryytn_oa_department
		order by sort_no
	</select>
</mapper>