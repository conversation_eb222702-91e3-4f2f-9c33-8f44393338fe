<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aliyun.ryytn.modules.system.dao.SessionDao">
	<resultMap id="accountResultMap" type="cn.aliyun.ryytn.common.entity.Account">
		<id property="id" column="id" javaType="java.lang.String" jdbcType="BIGINT" typeHandler="cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler"/>
	</resultMap>
    <select id="queryAccountByLoginId" parameterType="java.lang.String" resultMap="accountResultMap">
        SELECT
		  a.id,
		  a.name,
		  a.nick_name,
		  a.work_code,
		  a.login_id,
		  a.password,
		  a.oa_id,
		  b.department_id,
		  array_to_string(array_agg(e.department_name),'/') AS departmentName,
		  c.id AS jobTitleId,
		  c.job_title_mark AS jobTitleName,
		  array_to_string(array_agg(g.name),',') AS roleNames,
		  a.status,
		  a.description,
		  a.data_type,
		  a.created_time,
		  a.updated_time
		FROM
		  t_ryytn_account a
		  LEFT JOIN t_ryytn_oa_person b
			ON a.oa_id = b.id
		  LEFT JOIN t_ryytn_oa_jobtitle c
			ON b.job_title_id = c.id
		  LEFT JOIN t_ryytn_oa_department d
			ON b.department_id = d.id
		  LEFT JOIN t_ryytn_oa_department e
			ON e.id = ANY(STRING_TO_ARRAY(d.sup_dep_ids,','))
			OR d.id = e.id
		  LEFT JOIN t_ryytn_account_role f
			ON a.id = f.account_id
		  LEFT JOIN t_ryytn_role g
			ON f.role_id = g.id
		WHERE a.login_id = #{loginId}
		GROUP BY a.id,b.department_id,c.id,c.job_title_mark
    </select>

	<select id="queryRoleIdListByAccount" parameterType="cn.aliyun.ryytn.common.entity.Account" resultType="java.lang.String">
		SELECT role_id FROM t_ryytn_account_role WHERE account_id =
		#{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
	</select>

	<select id="queryPageListByAccount" parameterType="cn.aliyun.ryytn.common.entity.Account" resultType="cn.aliyun.ryytn.common.entity.Page">
		SELECT DISTINCT
		  p.id,
		  p.name,
		  p.alias,
		  p.permission,
		  p.parent_id,
		  p.permission,
		  p.parent_id,
		  p.parent_ids,
		  p.dependency_ids,
		  p.type,
		  p.path,
		  p.config_path,
		  p.component,
		  p.icon,
		  p.moudel_id,
		  p.sort_no,
		  p.sum_flag
		FROM
		  t_ryytn_page p
		LEFT JOIN t_ryytn_page sub_page
		  ON p.id::varchar = ANY(STRING_TO_ARRAY(sub_page.parent_ids,','))
		<if test="isAdmin != true">
		  LEFT JOIN t_ryytn_role_page rp
			ON p.id = rp.page_id
			OR sub_page.id = rp.page_id
		  LEFT JOIN t_ryytn_account_role ar
			ON rp.role_id = ar.role_id
		WHERE ar.account_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
			or p.type='99999'
		</if>
		ORDER BY p.sort_no
	</select>

	<select id="queryButtonListByAccount" parameterType="cn.aliyun.ryytn.common.entity.Account" resultType="cn.aliyun.ryytn.common.entity.Button">
		SELECT DISTINCT
		  b.id,
		  b.name,
		  b.alias,
		  b.permission,
		  b.page_id,
		  b.sort_no
		FROM
		  t_ryytn_button b
		<if test="isAdmin != true">
		  LEFT JOIN t_ryytn_role_button rb
			ON b.id = rb.button_id
		  LEFT JOIN t_ryytn_account_role ar
			ON rb.role_id = ar.role_id
		WHERE ar.account_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		   OR exists (select 'X' from t_ryytn_page page where b.page_id=page.id and page.type='99999')
		</if>
		ORDER BY b.sort_no
	</select>

	<select id="queryChannelIdListByAccount" parameterType="cn.aliyun.ryytn.common.entity.Account" resultType="java.lang.String">
		SELECT DISTINCT
		  rc.channel_id
		FROM
		  t_ryytn_role_channel rc
		  LEFT JOIN t_ryytn_account_role ar
			ON rc.role_id = ar.role_id
		WHERE 1 = 1
		<if test="isAdmin != true">
		  AND ar.account_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</if>
	</select>

	<select id="queryCategoryIdListByAccount" parameterType="cn.aliyun.ryytn.common.entity.Account" resultType="java.lang.String">
		SELECT DISTINCT
		  rp.category_id
		FROM
		  t_ryytn_role_productcategory rp
		  LEFT JOIN t_ryytn_account_role ar
			ON rp.role_id = ar.role_id
		WHERE 1 = 1
		<if test="isAdmin != true">
		  AND ar.account_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</if>
	</select>

	<select id="queryFactoryIdListByAccount" parameterType="cn.aliyun.ryytn.common.entity.Account" resultType="java.lang.String">
		SELECT DISTINCT
		  rf.factory_id
		FROM
		  t_ryytn_role_factory rf
		  LEFT JOIN t_ryytn_account_role ar
			ON rf.role_id = ar.role_id
		WHERE 1 = 1
		<if test="isAdmin != true">
		  AND ar.account_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</if>
	</select>

	<select id="queryDepositoryIdListByAccount" parameterType="cn.aliyun.ryytn.common.entity.Account" resultType="java.lang.String">
		SELECT DISTINCT
		  rd.depository_id
		FROM
		  t_ryytn_role_depository rd
		  LEFT JOIN t_ryytn_account_role ar
			ON rd.role_id = ar.role_id
		WHERE 1 = 1
		<if test="isAdmin != true">
		  AND ar.account_id = #{id,jdbcType=BIGINT,typeHandler=cn.aliyun.ryytn.common.mybatis.handler.PrimaryKeyTypeHandler}
		</if>
	</select>
</mapper>