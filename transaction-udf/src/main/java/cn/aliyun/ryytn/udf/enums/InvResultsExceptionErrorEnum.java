package cn.aliyun.ryytn.udf.enums;

import com.aliyun.odps.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/8 15:44
 * @description：
 */
@Getter
@AllArgsConstructor
public enum InvResultsExceptionErrorEnum {

    ALL("all", "全部", "", ""),
    NORMAL("normal", "正常", "#0066ff", "正常"),
    ABNORMAL("abnormal", "异常", "", "异常"),
    TOC_DAS_NUM_ZERO("TOC_DAS_NUM_ZERO", "日均销为0", "#ff0000", "日均销为0：请检查对应品仓算法产出的安全库存天数建议值"),
    TOC_SAFETY_ZERO("TOC_SAFETY_ZERO", "安全库存为0", "#ff0000", "安全库存数量（算法）在算法计算完未经业务修正时就是空或0"),
    TOC_PARAM_ZERO("TOC_PARAM_ZERO", "补货策略配置不完整", "#ff0000", "品仓的X1、X2、服务水平、提前期天数配置为0或空"),
    TOC_DIST_ERROR("TOC_DIST_ERROR", "补货策略配置异常", "#ff0000", "不符合 X1数量≤X2数量≤安全库存数量≤目标库存数量"),
    TOC_SHARED_SKU("TOC_SHARED_SKU", "共品", "#ff0000", "该SKU在多个工厂仓有库存"),
    ;
    private final String code;
    private final String name;
    private final String color;
    private final String tooltip;

    public static String getNameByCode(String code) {
        InvResultsExceptionErrorEnum dimensionEnum = getByCode(code);
        return dimensionEnum == null ? "" : dimensionEnum.name;
    }

    public static InvResultsExceptionErrorEnum getByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (InvResultsExceptionErrorEnum dimensionEnum : values()) {
            if (StringUtils.equals(dimensionEnum.code, code)) {
                return dimensionEnum;
            }
        }
        return null;
    }
}
