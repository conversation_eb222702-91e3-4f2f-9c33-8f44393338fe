package cn.aliyun.ryytn.udf.dto;

import lombok.Data;

@Data
public class DistTobSkuRdcDTO {
    private String skuCode;
    private String skuName;
    private String rdcCode;
    private String rdcName;
    private Integer priority;
    private Integer atpInvQty;
    private Integer atpInvDays;
    private Integer dasNum;
    private Integer oosPointQty;
    private Integer oosPointDays;
    private Integer limitWaterLevelQty;
    private Integer limitWaterLevelDays;
    private Integer safetyQty;
    private Integer targetQty;
    private Integer oosPointQtyGapQty;
    private Integer limitWaterLevelQtyGapQty;
    private Integer safetyGapQty;
    private Integer targetGapQty;
    private Integer safetyDays;
    private Integer targetDays;
}
