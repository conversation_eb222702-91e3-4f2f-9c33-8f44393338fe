package cn.aliyun.ryytn.udf.dto;

import lombok.Data;

@Data
public class DistTocResultDTO {
    private String skuCode;
    private String skuName;
    private String rdcCode;
    private String rdcName;
    private Integer dasNum;
    private Integer safetyQty;
    private Integer targetQty;
    private Integer safetyDays;
    private Integer targetDays;
    private String errMsg;
    private String route;
    private Integer isKeepSysValue;
    private Integer atpInvQty;
    private Integer atpInvDays;
    private Double atpInvDaysExact;
    private Integer sysSafetyQty;
    private Integer sysTargetQty;
    private Integer sysSafetyDays;
    private Integer sysTargetDays;
    private Integer atpSupplyDays;
    private Integer fullSupplyDays;
    private Integer x1Qty;
    private Integer x2Qty;
    /**
     * sku级 cdc的可用库存总量
     */
    private Integer skuCdcAtpInvQty;

    /**
     * sku级 cdc的总供应总量
     */
    private Integer skuCdcAtpSupplyQty;

    /**
     * sku级 cdc的可供应总量，考虑了爆仓
     */
    private Integer skuCdcFullSupplyQty;

    /**
     * sku级的全国平均库存天数 在超安全库存的场景会出现
     */
    private Double skuAvgInvDays;

    /**
     * 全国平均库存天数场景，补货的天数
     */
    private Double replnInvDays;

    /**
     * 全国平均库存天数场景，补货的件数
     */
    private Integer replnInvQty;

    /**
     * 是否已满足当前路由下的最大水位 1是 0否
     */
    private Integer isMatchLevelMax;

    /**
     * 补货优先级
     */
    private Integer priority;
}
