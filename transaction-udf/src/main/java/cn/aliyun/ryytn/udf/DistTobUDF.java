package cn.aliyun.ryytn.udf;

import cn.aliyun.ryytn.udf.dto.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.aliyun.odps.udf.UDF;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 17:31
 * @description：
 */
public class DistTobUDF extends UDF {

    public String evaluate(String input) {
        try {
            DistTobInputDTO inputDTO = JSON.parseObject(input, DistTobInputDTO.class, JSONReader.Feature.SupportSmartMatch);
            DistTobSkuDTO skuDTO = inputDTO.getSku();
            List<DistTobSkuRdcDTO> skuRdcList = inputDTO.getSkuRdcList();
            List<DistTobSkuCdcDTO> skuCdcList = inputDTO.getSkuCdcList();
            List<DistTobResultDTO> distResult = distToc(skuDTO, skuRdcList, skuCdcList);
            return JSON.toJSONString(distResult);
        } catch (Exception e) {
            return e.toString();
        }
    }

    private List<DistTobResultDTO> distToc(DistTobSkuDTO skuDTO, List<DistTobSkuRdcDTO> skuRdcList, List<DistTobSkuCdcDTO> skuCdcList) {
        if (skuDTO == null || skuRdcList == null || skuRdcList.isEmpty() || skuCdcList == null || skuCdcList.isEmpty()) {
            return new ArrayList<>();
        }
        Integer totalAtpSupplyQty = skuDTO.getCdcAtpSupplyQty();
        if (skuDTO.getSafetyGapQty() == null || skuDTO.getTargetGapQty() == null || totalAtpSupplyQty == null) {
            return new ArrayList<>();
        }
        Integer skuAvgInvDays;
        if (skuDTO.getCdcFullSupplyCnt() != null && skuDTO.getCdcFullSupplyCnt() > 0) {
            skuAvgInvDays = genSkuAvgInvDaysAfterRepln(skuDTO.getCdcFullSupplyQty(), skuRdcList);
        } else {
            skuAvgInvDays = genSkuAvgInvDaysAfterRepln(totalAtpSupplyQty, skuRdcList);
        }
        List<DistTobResultDTO> ret = new ArrayList<>();
        //全配置不爆仓 再订货点模式
        if (skuDTO.getCdcFullSupplyCnt() == 0) {
            ret.addAll(allNotFull(skuDTO, skuRdcList, skuAvgInvDays));
        } else if (skuDTO.getCdcFullSupplyCnt() == skuDTO.getCdcCnt()) {
            //全配置爆仓
            ret.addAll(allFull(skuDTO, skuRdcList, skuAvgInvDays));
        } else {
            //部分爆仓
            ret.addAll(partFull(skuDTO, skuRdcList, skuAvgInvDays));
        }
        return ret;
    }

    private List<DistTobResultDTO> allNotFull(DistTobSkuDTO skuDTO, List<DistTobSkuRdcDTO> skuRdcList, Integer skuAvgInvDays) {
        List<DistTobResultDTO> ret = new ArrayList<>();
        for (DistTobSkuRdcDTO rdcDto : skuRdcList) {
            //安全库存
            Integer safetyQty = null;
            //目标库存
            Integer targetQty = null;
            //补货量
            Integer replenQty = null;
            String errMsg = "";
            String route = null;
            //下游库存水位高于C点
            if (rdcDto.getAtpInvDays() > rdcDto.getSafetyDays()) {
                safetyQty = 0;
                targetQty = 0;
                route = "全不爆仓；下游库存水位高于C点；不补货";
            } else {
                //下游库存水位低于C点
                //下游缺口 > 上游供应
                if (skuDTO.getTargetGapQty() > skuDTO.getCdcAtpSupplyQty()) {
                    //当前仓库水位 <= 全国平均库存水位
                    if (rdcDto.getAtpInvDays() <= skuAvgInvDays) {
                        safetyQty = skuAvgInvDays * rdcDto.getDasNum();
                        targetQty = 0;
                        replenQty = skuAvgInvDays * rdcDto.getDasNum() - rdcDto.getAtpInvQty();
                        route = "全不爆仓；下游缺口>上游供应；当前仓库水位<=全国平均库存水位；补货至全国平均";
                        if (skuAvgInvDays < rdcDto.getOosPointDays()) {
                            errMsg = "补货后水位低于缺货报警点";
                        }
                    } else {
                        safetyQty = 0;
                        targetQty = 0;
                        route = "全不爆仓；下游缺口>上游供应；当前仓库水位>全国平均库存水位；不补货";
                        if (skuAvgInvDays < rdcDto.getOosPointDays()) {
                            errMsg = "补货后水位低于缺货报警点";
                        }
                    }
                } else {
                    //下游缺口 <= 上游供应
                    safetyQty = rdcDto.getSafetyQty();
                    targetQty = rdcDto.getTargetQty();
                    replenQty = rdcDto.getTargetQty() - rdcDto.getAtpInvQty();
                    route = "全不爆仓；下游缺口<=上游供应；补货至目标库存";
                }
            }
            DistTobResultDTO dto = setResultDTO(rdcDto, skuDTO);
            dto.setSkuAvgInvDays(skuAvgInvDays);
            dto.setSafetyQty(safetyQty);
            dto.setSafetyDays(qtyToDays(safetyQty, rdcDto.getDasNum()));
            dto.setTargetQty(targetQty);
            dto.setTargetDays(qtyToDays(targetQty, rdcDto.getDasNum()));
            dto.setErrMsg(errMsg);
            dto.setRoute(route);
            dto.setReplenQty(replenQty);
            ret.add(dto);
        }
        return ret;
    }

    private List<DistTobResultDTO> allFull(DistTobSkuDTO skuDTO, List<DistTobSkuRdcDTO> skuRdcList, Integer skuAvgInvDays) {
        List<DistTobResultDTO> ret = new ArrayList<>();
        for (DistTobSkuRdcDTO rdcDto : skuRdcList) {
            //安全库存
            Integer safetyQty = null;
            //目标库存
            Integer targetQty = null;
            //补货量
            Integer replenQty = null;
            String errMsg = null;
            String route = null;
            //下游缺口>上游供应
            if (skuDTO.getTargetGapQty() > skuDTO.getCdcAtpSupplyQty()) {
                //当前仓库水位 <= 全国平均库存水位
                if (rdcDto.getAtpInvDays() <= skuAvgInvDays) {
                    safetyQty = skuAvgInvDays * rdcDto.getDasNum();
                    targetQty = 0;
                    replenQty = skuAvgInvDays * rdcDto.getDasNum() - rdcDto.getAtpInvQty();
                    route = "全爆仓；下游缺口>上游供应；当前仓库水位<=全国平均库存水位；补货至全国平均";
                    if (skuAvgInvDays < rdcDto.getOosPointDays()) {
                        errMsg = "补货后水位低于缺货报警点";
                    }
                } else {
                    safetyQty = 0;
                    targetQty = 0;
                    route = "全爆仓；下游缺口>上游供应；当前仓库水位>全国平均库存水位；不补货";
                    if (skuAvgInvDays < rdcDto.getOosPointDays()) {
                        errMsg = "补货后水位低于缺货报警点";
                    }
                }
            } else if (skuDTO.getTargetGapQty() == skuDTO.getCdcAtpSupplyQty()) {
                //下游缺口 = 上游供应
                safetyQty = rdcDto.getSafetyQty();
                targetQty = rdcDto.getTargetQty();
                Integer replen = rdcDto.getTargetQty() - rdcDto.getAtpInvQty();
                if (replen > 0) {
                    replenQty = replen;
                }
                route = "全爆仓；下游缺口=上游供应；补货至目标库存";
            } else {
                //下游缺口 < 上游供应
                //当前仓库水位 >= 全国平均库存水位
                if (rdcDto.getAtpInvDays() >= skuAvgInvDays) {
                    safetyQty = 0;
                    targetQty = 0;
                    route = "全爆仓；下游缺口<上游供应；自身库存水位>=全国平均库存；不补货";
                } else {
                    //当前仓库水位 < 全国平均库存水位
                    //全国平均库存 < A
                    if (skuAvgInvDays < rdcDto.getLimitWaterLevelDays()) {
                        safetyQty = rdcDto.getSafetyQty();
                        targetQty = skuAvgInvDays * rdcDto.getDasNum();
                        replenQty = skuAvgInvDays * rdcDto.getDasNum() - rdcDto.getAtpInvQty();
                        route = "全爆仓；下游缺口<上游供应；自身库存水位<全国平均库存；全国平均水平<A；补货至全国平均";
                    } else {
                        //全国平均库存 >= A
                        safetyQty = rdcDto.getSafetyQty();
                        targetQty = rdcDto.getLimitWaterLevelQty();
                        replenQty = rdcDto.getLimitWaterLevelQty() - rdcDto.getAtpInvQty();
                        route = "全爆仓；下游缺口<上游供应；自身库存水位<全国平均库存；全国平均水平>=A；补货至A";
                    }
                }
            }
            DistTobResultDTO dto = setResultDTO(rdcDto, skuDTO);
            dto.setSkuAvgInvDays(skuAvgInvDays);
            dto.setSafetyQty(safetyQty);
            dto.setSafetyDays(qtyToDays(safetyQty, rdcDto.getDasNum()));
            dto.setTargetQty(targetQty);
            dto.setTargetDays(qtyToDays(targetQty, rdcDto.getDasNum()));
            dto.setErrMsg(errMsg);
            dto.setRoute(route);
            dto.setReplenQty(replenQty);
            ret.add(dto);
        }
        return ret;
    }

    private List<DistTobResultDTO> partFull(DistTobSkuDTO skuDTO, List<DistTobSkuRdcDTO> skuRdcList, Integer skuAvgInvDays) {
        List<DistTobResultDTO> ret = new ArrayList<>();
        for (DistTobSkuRdcDTO rdcDto : skuRdcList) {
            //安全库存
            Integer safetyQty = null;
            //目标库存
            Integer targetQty = null;
            //补货量
            Integer replenQty = null;
            String errMsg = "";
            String route = null;
            //下游库存水位高于C点
            if (rdcDto.getAtpInvDays() >= rdcDto.getSafetyDays()) {
                route = "部分爆仓；下游库存水位高于C点；不补货";
            } else {
                //下游库存水位低于C点
                //下游缺口 > 上游供应
                if (skuDTO.getTargetGapQty() > skuDTO.getCdcAtpSupplyQty()) {
                    //当前仓库水位 <= 全国平均库存水位
                    if (rdcDto.getAtpInvDays() <= skuAvgInvDays) {
                        safetyQty = skuAvgInvDays * rdcDto.getDasNum();
                        targetQty = 0;
                        replenQty = skuAvgInvDays * rdcDto.getDasNum() - rdcDto.getAtpInvQty();
                        route = "部分爆仓；下游库存水位低于C点；下游缺口>上游供应；当前仓库水位<=全国平均库存水位；补货至全国平均";
                        if (skuAvgInvDays < rdcDto.getOosPointDays()) {
                            errMsg = "补货后水位低于缺货报警点";
                        }
                    } else {
                        safetyQty = 0;
                        targetQty = 0;
                        route = "部分爆仓；下游库存水位低于C点；下游缺口>上游供应；当前仓库水位>全国平均库存水位；不补货";
                        if (skuAvgInvDays < rdcDto.getOosPointDays()) {
                            errMsg = "补货后水位低于缺货报警点";
                        }
                    }
                } else {
                    //下游缺口 <= 上游供应
                    safetyQty = rdcDto.getSafetyQty();
                    targetQty = rdcDto.getTargetQty();
                    replenQty = rdcDto.getTargetQty() - rdcDto.getAtpInvQty();
                    route = "部分爆仓；下游库存水位低于C点；下游缺口<=上游供应；补货至B";
                }
            }
            DistTobResultDTO dto = setResultDTO(rdcDto, skuDTO);
            dto.setSkuAvgInvDays(skuAvgInvDays);
            dto.setSafetyQty(safetyQty);
            dto.setSafetyDays(qtyToDays(safetyQty, rdcDto.getDasNum()));
            dto.setTargetQty(targetQty);
            dto.setTargetDays(qtyToDays(targetQty, rdcDto.getDasNum()));
            dto.setErrMsg(errMsg);
            dto.setRoute(route);
            dto.setReplenQty(replenQty);
            ret.add(dto);
        }
        return ret;
    }

    private DistTobResultDTO setResultDTO(DistTobSkuRdcDTO rdcDto, DistTobSkuDTO skuDTO) {
        DistTobResultDTO dto = new DistTobResultDTO();
        dto.setSkuCode(rdcDto.getSkuCode());
        dto.setSkuName(rdcDto.getSkuName());
        dto.setRdcCode(rdcDto.getRdcCode());
        dto.setRdcName(rdcDto.getRdcName());
        dto.setDasNum(rdcDto.getDasNum());
        dto.setAtpInvQty(rdcDto.getAtpInvQty());
        dto.setAtpInvDays(rdcDto.getAtpInvDays());
        dto.setSysSafetyQty(rdcDto.getSafetyQty());
        dto.setSysTargetQty(rdcDto.getTargetQty());
        dto.setSysSafetyDays(rdcDto.getSafetyDays());
        dto.setSysTargetDays(rdcDto.getTargetDays());
        dto.setPriority(rdcDto.getPriority());
        dto.setSkuCdcAtpInvQty(skuDTO.getCdcAtpInvQty());
        dto.setSkuCdcAtpSupplyQty(skuDTO.getCdcAtpSupplyQty());
        dto.setSkuCdcFullSupplyQty(skuDTO.getCdcFullSupplyQty());
        return dto;
    }

    /**
     * 生成补货后的全国平均库存水位天数
     *
     * @return
     */
    private int genSkuAvgInvDaysAfterRepln(Integer supplyQty, List<DistTobSkuRdcDTO> skuRdcList) {
        Integer minInvDays = Integer.MAX_VALUE;
        for (DistTobSkuRdcDTO item : skuRdcList) {
            if (item.getAtpInvDays() != null && item.getAtpInvDays() < minInvDays) {
                minInvDays = item.getAtpInvDays();
            }
        }

        Integer remainSupplyQty = supplyQty;
        if (supplyQty != null) {
            while (remainSupplyQty > 0 && minInvDays < 99) {
                int nextLevelGap = getNextLevelGap(skuRdcList, minInvDays);
                if (remainSupplyQty > nextLevelGap) {
                    minInvDays++;
                }
                remainSupplyQty = remainSupplyQty - nextLevelGap;
            }
        }
        return minInvDays;
    }

    private int getNextLevelGap(List<DistTobSkuRdcDTO> skuRdcList, Integer minInvDays) {
        int nextLevelGap = 0;
        for (DistTobSkuRdcDTO item : skuRdcList) {
            if (item.getDasNum() != null && item.getAtpInvDays() != null && item.getAtpInvQty() != null) {
                if (item.getAtpInvDays() < minInvDays) {
                    nextLevelGap += item.getDasNum();
                } else if (minInvDays.equals(item.getAtpInvDays())) {
                    int gap = (item.getAtpInvDays() + 1) * item.getDasNum() - item.getAtpInvQty();
                    if (gap > item.getDasNum()) {
                        gap = item.getDasNum();
                    }
                    nextLevelGap += gap;
                } else {
                    nextLevelGap += 0;
                }
            }
        }
        return nextLevelGap;
    }

    public Integer qtyToDays(Integer qty, Integer dasNum) {
        if (qty == null) {
            return null;
        } else if (qty == 0) {
            return 0;
        } else if (dasNum == null || dasNum == 0) {
            return null;
        } else {
            return (int) Math.ceil(qty * 1.0 / dasNum);
        }
    }
}
