package cn.aliyun.ryytn.udf;

import cn.aliyun.ryytn.udf.dto.*;
import cn.aliyun.ryytn.udf.enums.InvResultsExceptionErrorEnum;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.aliyun.odps.udf.UDF;
import com.aliyun.odps.utils.StringUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class DistTocUDF extends UDF {

    public String evaluate(String input) {
        try {
            DistTocInputDTO inputDTO = JSON.parseObject(input, DistTocInputDTO.class, JSONReader.Feature.SupportSmartMatch);
            DistTocSkuDTO skuDTO = inputDTO.getSku();
            List<DistTocSkuRdcDTO> skuRdcList = inputDTO.getSkuRdcList();
            List<DistTocSkuCdcDTO> skuCdcList = inputDTO.getSkuCdcList();
            List<DistTocResultDTO> distResult = distToc(skuDTO, skuRdcList, skuCdcList);
            return JSON.toJSONString(distResult);
        } catch (Exception e) {
            e.printStackTrace();
            return e.toString();
        }
    }

    protected void genAtpInvDaysExact(List<DistTocSkuRdcDTO> skuRdcList) {
        if (skuRdcList != null) {
            for (DistTocSkuRdcDTO rdcDTO : skuRdcList) {
                if (rdcDTO.getDasNum() == null) {
                    rdcDTO.setDasNum(0);
                }
                if (rdcDTO.getDasNum() > 0 && rdcDTO.getAtpInvQty() != null) {
                    rdcDTO.setAtpInvDaysExact(rdcDTO.getAtpInvQty() / (rdcDTO.getDasNum() * 1.0));
                } else {
                    rdcDTO.setAtpInvDaysExact(0.0);
                }
                rdcDTO.setAtpInvDaysCurrent(rdcDTO.getAtpInvDaysExact());
            }
        }
    }

    private List<DistTocResultDTO> distToc(DistTocSkuDTO skuDTO, List<DistTocSkuRdcDTO> skuRdcList, List<DistTocSkuCdcDTO> skuCdcList) {
        if (skuDTO == null || skuRdcList == null || skuRdcList.isEmpty() || skuCdcList == null || skuCdcList.isEmpty()) {
            return new ArrayList<>();
        }

        Integer totalX1GapQty = skuDTO.getX1GapQty();
        Integer totalX2GapQty = skuDTO.getX2GapQty();
        Integer totalSafetyGapQty = skuDTO.getSafetyGapQty();
        Integer totalTargetGapQty = skuDTO.getTargetGapQty();
        Integer totalAtpSupplyQty = skuDTO.getCdcAtpSupplyQty();
        if (totalX1GapQty == null || totalX2GapQty == null || totalSafetyGapQty == null || totalTargetGapQty == null || totalAtpSupplyQty == null) {
            return new ArrayList<>();
        }

        genAtpInvDaysExact(skuRdcList);
        List<DistTocResultDTO> ret = new ArrayList<>();
        List<DistTocSkuRdcDTO> sortedSkuRdcList = skuRdcList.stream().sorted(Comparator.comparingInt(DistTocSkuRdcDTO::getPriority)).collect(Collectors.toList());
        Integer remainSupply = getRemainSupplyQty(skuDTO);

        Double skuAvgInvDays;
        if (totalAtpSupplyQty > totalTargetGapQty && skuDTO.getCdcFullSupplyCnt() != null && skuDTO.getCdcFullSupplyCnt() > 0) {
            skuAvgInvDays = genSkuAvgInvDaysAfterRepln(skuDTO.getCdcFullSupplyQty(), skuRdcList);
        } else {
            skuAvgInvDays = genSkuAvgInvDaysAfterRepln(skuDTO.getCdcAtpSupplyQty(), skuRdcList);
        }
        for (DistTocSkuRdcDTO rdcDTO : sortedSkuRdcList) {
            Integer safetyQty;
            Integer targetQty;
            String route;
            boolean isKeepSysValue = false;
            boolean isMatchLevelMax;
            if (totalAtpSupplyQty < totalX1GapQty) {
                // 总供应 < 总X1缺口
                targetQty = 0;
                LoopSafetyResult loopSafetyResult = genSafetyQty(remainSupply, 0, rdcDTO.getX1Qty(), rdcDTO.getAtpInvQty());
                if (loopSafetyResult.getSuccess()) {
                    safetyQty = loopSafetyResult.getSafetyQty();
                    remainSupply = loopSafetyResult.getRemainSupply();
                    isMatchLevelMax = loopSafetyResult.getIsMatchDemand();
                } else {
                    isMatchLevelMax = false;
                    safetyQty = null;
                }
                route = "~x1 " + loopSafetyResult.getRoute();
            } else if (totalAtpSupplyQty < totalX2GapQty) {
                // 总供应 >= 总X1缺口 && 总供应 < 总X2缺口
                targetQty = 0;
                LoopSafetyResult loopSafetyResult = genSafetyQty(remainSupply, rdcDTO.getX1Qty(), rdcDTO.getX2Qty(), rdcDTO.getAtpInvQty());
                if (loopSafetyResult.getSuccess()) {
                    safetyQty = loopSafetyResult.getSafetyQty();
                    remainSupply = loopSafetyResult.getRemainSupply();
                    isMatchLevelMax = loopSafetyResult.getIsMatchDemand();
                } else {
                    isMatchLevelMax = false;
                    safetyQty = null;
                }
                route = "x1~x2 " + loopSafetyResult.getRoute();
            } else if (totalAtpSupplyQty < totalSafetyGapQty) {
                // 总供应 >= 总X2缺口 && 总供应 < 总安全缺口
                targetQty = 0;
                LoopSafetyResult loopSafetyResult = genSafetyQty(remainSupply, rdcDTO.getX2Qty(), rdcDTO.getSafetyQty(), rdcDTO.getAtpInvQty());
                if (loopSafetyResult.getSuccess()) {
                    safetyQty = loopSafetyResult.getSafetyQty();
                    remainSupply = loopSafetyResult.getRemainSupply();
                    isMatchLevelMax = loopSafetyResult.getIsMatchDemand();
                } else {
                    isMatchLevelMax = false;
                    safetyQty = null;
                }
                route = "x2~safety " + loopSafetyResult.getRoute();
            } else if (totalAtpSupplyQty < totalTargetGapQty) {
                // 总供应 >= 总安全缺口 && 总供应 < 总目标缺口
                if (rdcDTO.getAtpInvDaysExact() > skuAvgInvDays) {
                    // RDC库存>全国平均库存水位
                    safetyQty = 0;
                    targetQty = 0;
                    route = "safety~target >line";
                    isMatchLevelMax = true;
                } else {
                    // RDC库存<=全国平均库存水位, 补到全国平均库存水位
                    safetyQty = 0;
                    targetQty = bigger((int) Math.round(skuAvgInvDays * rdcDTO.getDasNum()), rdcDTO.getAtpInvQty());
                    route = "safety~target <=line";
                    isMatchLevelMax = false;
                }
            } else if (totalAtpSupplyQty.equals(totalTargetGapQty)) {
                route = "safety==target";
                isKeepSysValue = true;
                safetyQty = rdcDTO.getSafetyQty();
                targetQty = rdcDTO.getTargetQty();
                isMatchLevelMax = true;
            } else {
                // totalAtpSupplyQty > totalTargetGapQty
                isMatchLevelMax = true;
                if (skuDTO.getCdcFullSupplyCnt() == 0 || skuDTO.getCdcFullSupplyQty() == null || skuDTO.getCdcFullSupplyQty() <= 0) {
                    // 没有爆仓
                    route = "~target&noFullSupply";
                    isKeepSysValue = true;
                    safetyQty = rdcDTO.getSafetyQty();
                    targetQty = rdcDTO.getTargetQty();
                } else {
                    // 爆仓
                    if (rdcDTO.getAtpInvDaysExact() > skuAvgInvDays) {
                        // RDC库存>全国平均库存水位
                        safetyQty = 0;
                        targetQty = 0;
                        route = "~target&fullSupply >line";
                    } else {
                        // RDC库存<=全国平均库存水位
                        safetyQty = rdcDTO.getSafetyQty();
                        double qtyAfter = skuAvgInvDays * rdcDTO.getDasNum();
                        Integer targetQtyTmp = rdcDTO.getAtpInvQty();
                        targetQty = bigger((int) Math.round(qtyAfter), rdcDTO.getAtpInvQty());
                        route = "~target&fullSupply <=line";
                    }
                }
            }
            DistTocResultDTO dto = new DistTocResultDTO();
            dto.setSkuCode(rdcDTO.getSkuCode());
            dto.setSkuName(rdcDTO.getSkuName());
            dto.setRdcCode(rdcDTO.getRdcCode());
            dto.setRdcName(rdcDTO.getRdcName());
            dto.setDasNum(rdcDTO.getDasNum());
            dto.setAtpInvQty(rdcDTO.getAtpInvQty());
            dto.setAtpInvDays(rdcDTO.getAtpInvDays());
            dto.setAtpInvDaysExact(rdcDTO.getAtpInvDaysExact());
            dto.setIsKeepSysValue(isKeepSysValue ? 1 : 0);
            dto.setSysSafetyQty(rdcDTO.getSafetyQty());
            dto.setSysTargetQty(rdcDTO.getTargetQty());
            dto.setSysSafetyDays(rdcDTO.getSafetyDays());
            dto.setSysTargetDays(rdcDTO.getTargetDays());
            dto.setPriority(rdcDTO.getPriority());
            dto.setIsMatchLevelMax(isMatchLevelMax ? 1 : 0);
            dto.setX1Qty(rdcDTO.getX1Qty());
            dto.setX2Qty(rdcDTO.getX2Qty());
            dto.setSkuAvgInvDays(skuAvgInvDays);
            dto.setSkuCdcAtpInvQty(skuDTO.getCdcAtpInvQty());
            dto.setSkuCdcAtpSupplyQty(skuDTO.getCdcAtpSupplyQty());
            dto.setSkuCdcFullSupplyQty(skuDTO.getCdcFullSupplyQty());

            if (!isKeepSysValue) {
                dto.setSafetyQty(safetyQty);
                dto.setSafetyDays(qtyToDays(safetyQty, rdcDTO.getDasNum()));
                dto.setTargetQty(targetQty);
                dto.setTargetDays(qtyToDays(targetQty, rdcDTO.getDasNum()));
            } else {
                dto.setSafetyQty(rdcDTO.getSafetyQty());
                dto.setSafetyDays(rdcDTO.getSafetyDays());
                dto.setTargetQty(rdcDTO.getTargetQty());
                dto.setTargetDays(rdcDTO.getTargetDays());
            }
            if (dto.getDasNum() != null && dto.getDasNum() > 0) {
                if (dto.getAtpInvDaysExact() != null) {
                    dto.setReplnInvDays(skuAvgInvDays > dto.getAtpInvDaysExact() ? skuAvgInvDays - dto.getAtpInvDaysExact() : 0);
                }
                if (dto.getTargetQty() != null && dto.getAtpInvQty() != null) {
                    dto.setReplnInvQty(dto.getTargetQty() > dto.getAtpInvQty() ? dto.getTargetQty() - dto.getAtpInvQty() : 0);
                }
            } else {
                dto.setReplnInvDays(0.0);
                dto.setReplnInvQty(0);
            }
            StringBuilder errMsg = new StringBuilder();
            if (rdcDTO.getDasNum() == null || rdcDTO.getDasNum() == 0) {
                appendWithSeparator(errMsg, InvResultsExceptionErrorEnum.TOC_DAS_NUM_ZERO.getCode());
            }
            if (rdcDTO.getOriginAlgoSafetyDays() == null || rdcDTO.getOriginAlgoSafetyDays() == 0) {
                appendWithSeparator(errMsg, InvResultsExceptionErrorEnum.TOC_SAFETY_ZERO.getCode());
            }
            if (rdcDTO.getX1Qty() == null || rdcDTO.getX2Qty() == null || rdcDTO.getServiceRatio() == null || rdcDTO.getLeadDays() == null
                    || rdcDTO.getX1Qty() == 0 || rdcDTO.getX2Qty() == 0 || rdcDTO.getServiceRatio() == 0 || rdcDTO.getLeadDays() == 0) {
                appendWithSeparator(errMsg, InvResultsExceptionErrorEnum.TOC_PARAM_ZERO.getCode());
            }
            if (rdcDTO.getSafetyQty() != null && rdcDTO.getTargetQty() != null && (rdcDTO.getX1Qty() >= rdcDTO.getX2Qty() || rdcDTO.getX2Qty() >= rdcDTO.getSafetyQty() || rdcDTO.getSafetyQty() >= rdcDTO.getTargetQty())) {
                appendWithSeparator(errMsg, InvResultsExceptionErrorEnum.TOC_DIST_ERROR.getCode());
            }
            if (skuDTO.getSupplyCdcCnt() != null && skuDTO.getSupplyCdcCnt() > 1) {
                appendWithSeparator(errMsg, InvResultsExceptionErrorEnum.TOC_SHARED_SKU.getCode());
            }
            dto.setErrMsg(errMsg.toString());
            dto.setRoute(route);
            ret.add(dto);
        }
        return ret;
    }

    public void appendWithSeparator(StringBuilder sb, String element) {
        if (sb.length() > 0) {
            sb.append(",");
        }
        sb.append(element);
    }

    private int getRemainSupplyQty(DistTocSkuDTO skuDTO) {
        Integer totalX1GapQty = skuDTO.getX1GapQty();
        Integer totalX2GapQty = skuDTO.getX2GapQty();
        Integer totalSafetyGapQty = skuDTO.getSafetyGapQty();
        Integer totalTargetGapQty = skuDTO.getTargetGapQty();
        Integer totalAtpSupplyQty = skuDTO.getCdcAtpSupplyQty();
        if (totalAtpSupplyQty < totalX1GapQty) {
            return skuDTO.getCdcAtpSupplyQty();
        } else if (totalAtpSupplyQty < totalX2GapQty) {
            return skuDTO.getCdcRemainInvQtyAfterX1();
        } else if (totalAtpSupplyQty < totalSafetyGapQty) {
            return skuDTO.getCdcRemainInvQtyAfterX2();
        } else if (totalAtpSupplyQty < totalTargetGapQty) {
            return skuDTO.getCdcRemainInvQtyAfterSafety();
        } else {
            return skuDTO.getCdcRemainInvQtyAfterTarget();
        }
    }

    /**
     * 生成补货后的全国平均库存水位天数
     * 连通器法则，从最低水位的仓库，向上慢慢补齐。越接近补完，步长就越小
     *
     * @return 全国平均库存水位，精确到小数n位
     */
    private double genSkuAvgInvDaysAfterRepln(Integer supplyQty, List<DistTocSkuRdcDTO> skuRdcList) {
        Double minInvDays = Integer.MAX_VALUE * 1.0;

        // 先取所有仓最小的库存可用天数，作为全国平均库存水位的基准
        int totalDasNum = 0;
        for (DistTocSkuRdcDTO item : skuRdcList) {
            if (item.getAtpInvDaysExact() != null && item.getAtpInvDaysExact() < minInvDays) {
                minInvDays = item.getAtpInvDaysExact();
            }
            if (item.getDasNum() != null) {
                totalDasNum += item.getDasNum();
            }
        }


        if (supplyQty != null && totalDasNum > 0) {
            // 剩余供应量
            double remainSupplyQty = supplyQty.doubleValue();
            while (remainSupplyQty > 0 && minInvDays < 999) {
                // 生成动态步长。除非是最小步长单位，否则单次步长不会超过remainSupplyQty剩余供应量
                double step = genStepLength(totalDasNum, remainSupplyQty);
                // 补到步长增量水位时，需要补的量
                double nextLevelGap = getNextLevelGap(skuRdcList, minInvDays, step);
                if (remainSupplyQty > nextLevelGap) {
                    minInvDays += step;
                    remainSupplyQty = remainSupplyQty - nextLevelGap;
                } else {
                    break;
                }
            }
        }
        return minInvDays;
    }

    /**
     * 生成步长，余量越小，步长约短
     *
     * @return
     */
    private double genStepLength(int dasNum, double remainSupplyQty) {
        if (remainSupplyQty > dasNum) {
            return 1;
        } else if (remainSupplyQty * 10 > dasNum) {
            return 0.1;
        } else if (remainSupplyQty * 100 > dasNum) {
            return 0.01;
        } else if (remainSupplyQty * 1000 > dasNum) {
            return 0.001;
        } else if (remainSupplyQty * 10000 > dasNum) {
            return 0.0001;
        } else if (remainSupplyQty * 100000 > dasNum) {
            return 0.00001;
        } else if (remainSupplyQty * 1000000 > dasNum) {
            return 0.000001;
        } else {
            return 0.0000001;
        }
    }

    /**
     * @param skuRdcList
     * @param minInvDays
     * @param step       水位上升的步长
     * @return
     */
    private double getNextLevelGap(List<DistTocSkuRdcDTO> skuRdcList, Double minInvDays, Double step) {
        double nextLevelGap = 0;
        for (DistTocSkuRdcDTO item : skuRdcList) {
            if (item.getDasNum() != null && item.getAtpInvDays() != null && item.getAtpInvQty() != null) {
                double gap = 0.0;
                if (item.getAtpInvDaysCurrent() <= (minInvDays + step)) {
                    gap = item.getDasNum() * (minInvDays + step - item.getAtpInvDaysCurrent());
                    item.setAtpInvDaysCurrent(minInvDays + step);
                }
                nextLevelGap += gap;
            }
        }
        return nextLevelGap;
    }


    @Data
    class LoopSafetyResult {
        private Integer safetyQty;
        private Integer remainSupply;
        private String route;
        private Boolean success;

        /**
         * 是否满足了需求
         */
        private Boolean isMatchDemand;

        public LoopSafetyResult(Integer safetyQty, Integer remainSupply, String route, Boolean isMatchDemand, Boolean success) {
            this.safetyQty = safetyQty;
            this.remainSupply = remainSupply;
            this.route = route;
            this.isMatchDemand = isMatchDemand;
            this.success = success;
        }
    }

    private LoopSafetyResult genSafetyQty(Integer remainSupply, Integer lv1Qty, Integer lv2Qty, Integer aptInvQty) {
        if (remainSupply == null || lv1Qty == null || lv2Qty == null || aptInvQty == null) {
            return new LoopSafetyResult(null, null, "null param", false, false);
        } else if (aptInvQty >= lv2Qty) {
            // 可用库存太多了
            return new LoopSafetyResult(0, remainSupply, ">=line", true, true);
        } else {
            int delta = smaller(lv2Qty - aptInvQty, lv2Qty - lv1Qty);
            int safetyQty;
            boolean isMatchDemand;
            if (remainSupply >= delta) {
                // 剩余量满足缺口
                safetyQty = lv2Qty;
                isMatchDemand = true;
            } else {
                delta = remainSupply;
                safetyQty = bigger(lv1Qty, aptInvQty) + delta;
                isMatchDemand = false;
            }
            remainSupply = remainSupply - delta;
            return new LoopSafetyResult(safetyQty, remainSupply, "<line", isMatchDemand, true);
        }
    }

    protected Integer bigger(Integer a, Integer b) {
        if (a == null) return b;
        if (b == null) return a;
        return a.compareTo(b) >= 0 ? a : b;
    }

    protected Integer smaller(Integer a, Integer b) {
        if (a == null) return b;
        if (b == null) return a;
        return a.compareTo(b) <= 0 ? a : b;
    }

    public Integer qtyToDays(Integer qty, Integer dasNum) {
        if (qty == null) {
            return null;
        } else if (qty == 0) {
            return 0;
        } else if (dasNum == null || dasNum == 0) {
            return null;
        } else {
            return (int) Math.ceil(qty * 1.0 / dasNum);
        }
    }
}
