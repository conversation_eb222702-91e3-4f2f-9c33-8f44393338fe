package cn.aliyun.ryytn.udf.dto;

import lombok.Data;

@Data
public class DistTocSkuDTO {
    private String skuCode;
    private String skuName;
    private Integer x1GapQty;
    private Integer x2GapQty;
    private Integer safetyGapQty;
    private Integer targetGapQty;
    private Integer rdcAtpInvQty;
    private Integer cdcAtpInvQty;
    private Integer cdcAtpSupplyQty;
    private Integer cdcRemainInvQtyAfterX1;
    private Integer cdcRemainInvQtyAfterX2;
    private Integer cdcRemainInvQtyAfterSafety;
    private Integer cdcRemainInvQtyAfterTarget;
    private Integer cdcFullSupplyCnt;
    private Integer cdcFullSupplyQty;

    /**
     * 有供应量的cdc数量
     */
    private Integer supplyCdcCnt;

}
