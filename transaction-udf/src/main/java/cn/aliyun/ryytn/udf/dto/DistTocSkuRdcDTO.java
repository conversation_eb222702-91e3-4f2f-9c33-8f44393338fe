package cn.aliyun.ryytn.udf.dto;

import lombok.Data;

@Data
public class DistTocSkuRdcDTO {
    private String skuCode;
    private String skuName;
    private String rdcCode;
    private String rdcName;
    private Integer priority;
    private Integer atpInvQty;
    private Integer atpInvDays;
    /**
     * 精确的库存天数
     */
    private Double atpInvDaysExact;
    /**
     * 全国平均水位补货后的库存天数
     */
    private Double atpInvDaysCurrent;
    private Integer dasNum;
    private Integer x1Qty;
    private Integer x2Qty;
    private Integer safetyQty;
    private Integer targetQty;
    private Integer x1GapQty;
    private Integer x2GapQty;
    private Integer safetyGapQty;
    private Integer targetGapQty;
    private Integer safetyDays;
    private Integer targetDays;

    private Integer leadDays;
    private Double serviceRatio;
    private Integer originAlgoSafetyDays;
}
