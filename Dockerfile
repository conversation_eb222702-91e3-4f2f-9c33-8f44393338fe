FROM registry-enterprise-registry.cn-hangzhou.cr.aliyuncs.com/tech/openjdk:8u351
ADD transaction-starter/target/transaction.jar transaction.jar

ENV TZ=Asia/Shanghai 
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
ENV APP_NAME=transaction

EXPOSE 7001

ENV JAVA_OPTS="-Dspring.profiles.active=test -Xms2000m -Xmx4000m -XX:+UseParallelGC -XX:MaxGCPauseMillis=100 -XX:PermSize=60M -XX:MaxPermSize=1000M -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./logs/"

ENTRYPOINT [ "sh", "-c", "java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar /${APP_NAME}.jar" ]