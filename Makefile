PKG_NAME = $(shell cat package.json | awk -F '"' '/name" *: *"/{print $$4}')
PKG_VERSION = $(shell cat package.json | awk -F '"' '/version" *: *"/{print $$4}')
PKG_BUILD = $(shell cat package.json | awk -F '"' '/build" *: *"/{print $$4}')

package:
	@mvn clean package -Dmaven.test.skip=true -P pro
	@rm -rf ./out && mkdir ./out
	@mkdir ./out/$(PKG_NAME)_$(PKG_VERSION)_$(PKG_BUILD)
	@unzip -o -qq ./transaction-starter/target/${PKG_NAME}.jar -d ./out/$(PKG_NAME)_$(PKG_VERSION)_$(PKG_BUILD)/${PKG_NAME} || exit 0
	@cp ./package.json ./out/$(PKG_NAME)_$(PKG_VERSION)_$(PKG_BUILD)
	@cd out && tar zcf $(PKG_NAME)_$(PKG_VERSION)_$(PKG_BUILD).tgz $(PKG_NAME)_$(PKG_VERSION)_$(PKG_BUILD)

release:
	@mvn clean package -Dmaven.test.skip=true -P pro
	@rm  -rf ./out && mkdir -p out/release
	@unzip -o -qq ./transaction-starter/target/${PKG_NAME}.jar -d ./out/release/${PKG_NAME} || exit 0
	@cp ./package.json ./out/release

.PHONY:  package release